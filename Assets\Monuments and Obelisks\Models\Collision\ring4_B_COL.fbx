; FBX 7.4.0 project file
; Copyright (C) 1997-2015 Autodesk Inc. and/or its licensors.
; All rights reserved.
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1003
	FBXVersion: 7400
	CreationTimeStamp:  {
		Version: 1000
		Year: 2018
		Month: 1
		Day: 25
		Hour: 17
		Minute: 9
		Second: 51
		Millisecond: 783
	}
	Creator: "FBX SDK/FBX Plugins version 2017.1"
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "D:\Docs\3D\objectSets\monuments\unity\ring4_B_COL.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "D:\Docs\3D\objectSets\monuments\unity\ring4_B_COL.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", "Autodesk"
			P: "Original|ApplicationName", "KString", "", "", "Maya"
			P: "Original|ApplicationVersion", "KString", "", "", "2017"
			P: "Original|DateTime_GMT", "DateTime", "", "", "26/01/2018 00:09:51.782"
			P: "Original|FileName", "KString", "", "", "D:\Docs\3D\objectSets\monuments\unity\ring4_B_COL.fbx"
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", "Autodesk"
			P: "LastSaved|ApplicationName", "KString", "", "", "Maya"
			P: "LastSaved|ApplicationVersion", "KString", "", "", "2017"
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", "26/01/2018 00:09:51.782"
			P: "Original|ApplicationActiveProject", "KString", "", "", "D:\Docs\3D\objectSets\monuments\unity"
			P: "Original|ApplicationNativeFile", "KString", "", "", "D:\Docs\3D\objectSets\monuments\monumentSetUnity.ma"
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",1
		P: "OriginalUnitScaleFactor", "double", "Number", "",1
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",6
		P: "TimeProtocol", "enum", "", "",2
		P: "SnapOnFrameMode", "enum", "", "",0
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",46186158000
		P: "CustomFrameRate", "double", "Number", "",-1
		P: "TimeMarker", "Compound", "", ""
		P: "CurrentTimeMarker", "int", "Integer", "",-1
	}
}

; Documents Description
;------------------------------------------------------------------

Documents:  {
	Count: 1
	Document: 2231794153872, "", "Scene" {
		Properties70:  {
			P: "SourceObject", "object", "", ""
			P: "ActiveAnimStackName", "KString", "", "", "Take 001"
		}
		RootNode: 0
	}
}

; Document References
;------------------------------------------------------------------

References:  {
}

; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 8
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "AnimationStack" {
		Count: 1
		PropertyTemplate: "FbxAnimStack" {
			Properties70:  {
				P: "Description", "KString", "", "", ""
				P: "LocalStart", "KTime", "Time", "",0
				P: "LocalStop", "KTime", "Time", "",0
				P: "ReferenceStart", "KTime", "Time", "",0
				P: "ReferenceStop", "KTime", "Time", "",0
			}
		}
	}
	ObjectType: "AnimationLayer" {
		Count: 1
		PropertyTemplate: "FbxAnimLayer" {
			Properties70:  {
				P: "Weight", "Number", "", "A",100
				P: "Mute", "bool", "", "",0
				P: "Solo", "bool", "", "",0
				P: "Lock", "bool", "", "",0
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BlendMode", "enum", "", "",0
				P: "RotationAccumulationMode", "enum", "", "",0
				P: "ScaleAccumulationMode", "enum", "", "",0
				P: "BlendModeBypass", "ULongLong", "", "",0
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfaceLambert" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Lambert"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 1
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Video" {
		Count: 1
		PropertyTemplate: "FbxVideo" {
			Properties70:  {
				P: "ImageSequence", "bool", "", "",0
				P: "ImageSequenceOffset", "int", "Integer", "",0
				P: "FrameRate", "double", "Number", "",0
				P: "LastFrame", "int", "Integer", "",0
				P: "Width", "int", "Integer", "",0
				P: "Height", "int", "Integer", "",0
				P: "Path", "KString", "XRefUrl", "", ""
				P: "StartFrame", "int", "Integer", "",0
				P: "StopFrame", "int", "Integer", "",0
				P: "PlaySpeed", "double", "Number", "",0
				P: "Offset", "KTime", "Time", "",0
				P: "InterlaceMode", "enum", "", "",0
				P: "FreeRunning", "bool", "", "",0
				P: "Loop", "bool", "", "",0
				P: "AccessMode", "enum", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Geometry: 2231866916464, "Geometry::", "Mesh" {
		Vertices: *192 {
			a: 50,186.437774658203,-77.2249221801758,50,142.693328857422,-142.693161010742,50,77.2251129150391,-186.43766784668,50,0.000102239209809341,-201.798690795898,50,-77.2249450683594,-186.437759399414,50,-142.69319152832,-142.693313598633,50,-186.437698364258,-77.2250900268555,50,-201.798706054688,-7.21688338671811e-005,50,-186.437744140625,77.2249755859375,50,-142.693283081055,142.693222045898,50,-77.2250671386719,186.437713623047,50,-3.00703541142866e-005,201.798706054688,50,77.2250061035156,186.437728881836,50,142.693237304688,142.693267822266,50,186.437713623047,77.2250366210938,50,201.798706054688,0,-50,186.437774658203,-77.2249221801758,-50,142.693328857422,-142.693161010742,-50,77.2251129150391,-186.43766784668,-50,0.000102239209809341,-201.798690795898,-50,-77.2249450683594,-186.437759399414,-50,-142.69319152832,-142.693313598633,-50,-186.437698364258,-77.2250900268555,-50,-201.798706054688,-7.21688338671811e-005,-50,-186.437744140625,77.2249755859375,-50,-142.693283081055,142.693222045898,-50,-77.2250671386719,186.437713623047,-50,-3.00703541142866e-005,201.798706054688,-50,77.2250061035156,186.437728881836,-50,142.693237304688,142.693267822266,-50,186.437713623047,77.2250366210938,-50,201.798706054688,0,50,147.712173461914,-61.1842727661133,50,113.054016113281,-113.053894042969,50,61.1844329833984,-147.712051391602,50,8.10027559055015e-005,-159.882431030273,50,-61.1842956542969,-147.712158203125,50,-113.053924560547,-113.054000854492,50,-147.712097167969,-61.1844024658203,50,-159.882446289063,-5.55879232706502e-005,50,-147.712142944336,61.184326171875,50,-113.053970336914,113.053939819336,50,-61.1843872070313,147.712112426758,50,-2.38243428611895e-005,159.882446289063,50,61.1843490600586,147.712127685547,50,113.053939819336,113.053970336914,50,147.712112426758,61.1843719482422,50,159.882446289063,1.59048659043037e-006,-50,147.712173461914,-61.1842727661133,-50,113.054016113281,-113.053894042969,-50,61.1844329833984,-147.712051391602,-50,8.10027559055015e-005,-159.882431030273,-50,-61.1842956542969,-147.712158203125,
-50,-113.053924560547,-113.054000854492,-50,-147.712097167969,-61.1844024658203,-50,-159.882446289063,-5.55879232706502e-005,-50,-147.712142944336,61.184326171875,-50,-113.053970336914,113.053939819336,-50,-61.1843872070313,147.712112426758,-50,-2.38243428611895e-005,159.882446289063,-50,61.1843490600586,147.712127685547,-50,113.053939819336,113.053970336914,-50,147.712112426758,61.1843719482422,-50,159.882446289063,1.59048659043037e-006
		} 
		PolygonVertexIndex: *256 {
			a: 0,1,17,-17,1,2,18,-18,2,3,19,-19,3,4,20,-20,4,5,21,-21,5,6,22,-22,6,7,23,-23,7,8,24,-24,8,9,25,-25,9,10,26,-26,10,11,27,-27,11,12,28,-28,12,13,29,-29,13,14,30,-30,14,15,31,-31,15,0,16,-32,1,0,32,-34,2,1,33,-35,3,2,34,-36,4,3,35,-37,5,4,36,-38,6,5,37,-39,7,6,38,-40,8,7,39,-41,9,8,40,-42,10,9,41,-43,11,10,42,-44,12,11,43,-45,13,12,44,-46,14,13,45,-47,15,14,46,-48,0,15,47,-33,16,17,49,-49,17,18,50,-50,18,19,51,-51,19,20,52,-52,20,21,53,-53,21,22,54,-54,22,23,55,-55,23,24,56,-56,24,25,57,-57,25,26,58,-58,26,27,59,-59,27,28,60,-60,28,29,61,-61,29,30,62,-62,30,31,63,-63,31,16,48,-64,33,32,48,-50,34,33,49,-51,35,34,50,-52,36,35,51,-53,37,36,52,-54,38,37,53,-55,39,38,54,-56,40,39,55,-57,41,40,56,-58,42,41,57,-59,43,42,58,-60,44,43,59,-61,45,44,60,-62,46,45,61,-63,47,46,62,-64,32,47,63,-49
		} 
		Edges: *128 {
			a: 0,4,8,12,16,20,24,28,32,36,40,44,48,52,56,60,2,6,10,14,18,22,26,30,34,38,42,46,50,54,58,62,3,1,5,9,13,17,21,25,29,33,37,41,45,49,53,57,65,67,66,71,70,75,74,79,78,83,82,87,86,91,90,95,94,99,98,103,102,107,106,111,110,115,114,119,118,123,122,126,131,129,130,133,134,137,138,141,142,145,146,149,150,153,154,157,158,161,162,165,166,169,170,173,174,177,178,181,182,185,186,190,193,195,199,203,207,211,215,219,223,227,231,235,239,243,247,251
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 102
			Name: ""
			MappingInformationType: "ByVertice"
			ReferenceInformationType: "Direct"
			Normals: *192 {
				a: 0.312243074178696,0.877687633037567,-0.363549739122391,0.312243193387985,0.671753168106079,-0.671752750873566,0.312243193387985,0.363550424575806,-0.877687394618988,0.312243103981018,5.64038430184155e-007,-0.950002074241638,0.312242925167084,-0.363549441099167,-0.877687811851501,0.312243163585663,-0.671752750873566,-0.671753168106079,0.312243074178696,-0.877687454223633,-0.363550305366516,0.31224313378334,-0.950002133846283,-2.29439365284634e-007,0.312243074178696,-0.877687692642212,0.363549739122391,0.312243103981018,-0.671753168106079,0.671752750873566,0.312242984771729,-0.363550066947937,0.877687513828278,0.312243163585663,-1.05159678298605e-007,0.950002133846283,0.312243103981018,0.363549828529358,0.877687692642212,0.312243044376373,0.671752870082855,0.67175304889679,0.312243044376373,0.877687573432922,0.363549947738647,0.312243103981018,0.950002133846283,3.53719201484637e-007,-0.312243074178696,0.877687633037567,-0.363549739122391,-0.312243193387985,0.671753168106079,-0.671752750873566,-0.312243193387985,0.363550424575806,-0.877687394618988,-0.312243103981018,5.64038430184155e-007,-0.950002074241638,-0.312242895364761,-0.363549441099167,-0.877687811851501,-0.312243163585663,-0.671752750873566,-0.671753168106079,-0.312243074178696,-0.877687454223633,-0.363550305366516,-0.31224313378334,-0.950002133846283,-2.29439365284634e-007,-0.312243044376373,-0.877687692642212,0.363549739122391,-0.31224313378334,-0.671753168106079,0.671752750873566,-0.312243014574051,-0.363550066947937,0.877687513828278,-0.312243193387985,-1.11729512752845e-007,0.950002133846283,-0.312243103981018,0.363549828529358,0.877687692642212,-0.312243014574051,0.671752870082855,0.67175304889679,-0.312243044376373,0.877687573432922,0.363549947738647,-0.31224313378334,0.950002133846283,3.53719201484637e-007,0.470615029335022,-0.815174698829651,0.337656259536743,0.470615118741989,-0.623907744884491,0.623907566070557,0.4706149995327,-0.337656825780869,0.815174460411072,0.470614820718765,-7.67671906487521e-007,0.88233882188797,0.47061476111412,0.337655931711197,0.815174877643585,
0.470614969730377,0.623907506465912,0.623907864093781,0.470614850521088,0.815174520015717,0.337656587362289,0.470614790916443,0.882338643074036,2.91379052441698e-007,0.470614969730377,0.815174698829651,-0.337656199932098,0.470614939928055,0.623907744884491,-0.623907566070557,0.470614969730377,0.337656557559967,-0.815174579620361,0.470614969730377,1.06465428473257e-007,-0.882338643074036,0.470614939928055,-0.337656319141388,-0.815174639225006,0.470614850521088,-0.623907685279846,-0.623907625675201,0.470614939928055,-0.815174639225006,-0.337656527757645,0.470614850521088,-0.882338762283325,-4.93103470944334e-007,-0.470615029335022,-0.815174698829651,0.337656259536743,-0.470615029335022,-0.623907744884491,0.623907566070557,-0.4706149995327,-0.337656825780869,0.815174460411072,-0.470614820718765,-7.67671906487521e-007,0.88233882188797,-0.47061476111412,0.337655931711197,0.815174877643585,-0.470614910125732,0.623907446861267,0.623907804489136,-0.470614850521088,0.815174520015717,0.337656587362289,-0.470614850521088,0.882338762283325,2.91379137706826e-007,-0.470615029335022,0.815174698829651,-0.337656229734421,-0.470614939928055,0.623907744884491,-0.623907566070557,-0.4706149995327,0.337656557559967,-0.815174579620361,-0.470614969730377,9.52585423874552e-008,-0.882338643074036,-0.470614939928055,-0.337656319141388,-0.815174639225006,-0.47061488032341,-0.623907744884491,-0.623907685279846,-0.470614939928055,-0.815174639225006,-0.337656527757645,-0.470614850521088,-0.88233870267868,-4.93103470944334e-007
			} 
			NormalsW: *64 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "map1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *228 {
				a: 0.644356191158295,0.0964557975530624,0.61048549413681,0.0457646325230598,0.559794366359711,0.0118938684463501,0.500000059604645,1.49011611938477e-008,0.440205782651901,0.0118937939405441,0.389514625072479,0.0457645207643509,0.355643838644028,0.0964556634426117,0.34375,0.156249940395355,0.355643808841705,0.216044247150421,0.389514535665512,0.266735404729843,0.440205693244934,0.300606161355972,0.499999970197678,0.3125,0.559794247150421,0.300606191158295,0.610485434532166,0.266735434532166,0.644356191158295,0.216044291853905,0.65625,0.15625,0.375,0.3125,0.390625,0.3125,0.40625,0.3125,0.421875,0.3125,0.4375,0.3125,0.453125,0.3125,0.46875,0.3125,0.484375,0.3125,0.5,0.3125,0.515625,0.3125,0.53125,0.3125,0.546875,0.3125,0.5625,0.3125,0.578125,0.3125,0.59375,0.3125,0.609375,0.3125,0.625,0.3125,0.375,0.688439846038818,0.390625,0.688439846038818,0.40625,0.688439846038818,0.421875,0.688439846038818,0.4375,0.688439846038818,0.453125,0.688439846038818,0.46875,0.688439846038818,0.484375,0.688439846038818,0.5,0.688439846038818,0.515625,0.688439846038818,0.53125,0.688439846038818,0.546875,0.688439846038818,0.5625,0.688439846038818,0.578125,0.688439846038818,0.59375,0.688439846038818,0.609375,0.688439846038818,0.625,0.688439846038818,0.644356191158295,0.783955812454224,0.61048549413681,0.733264625072479,0.559794366359711,0.69939386844635,0.500000059604645,0.6875,0.440205782651901,0.699393808841705,0.389514625072479,0.73326450586319,0.355643838644028,0.783955693244934,0.34375,0.843749940395355,0.355643808841705,0.903544247150421,0.389514535665512,0.954235434532166,0.440205693244934,0.98810613155365,0.499999970197678,1,0.559794247150421,0.988106191158295,0.610485434532166,0.954235434532166,0.644356191158295,0.903544306755066,0.65625,0.84375,0.61048549413681,0.0457646325230598,0.644356191158295,0.0964557975530624,0.559794366359711,0.0118938684463501,0.500000059604645,1.49011611938477e-008,0.440205782651901,0.0118937939405441,0.389514625072479,0.0457645207643509,0.355643838644028,0.0964556634426117,0.34375,0.156249940395355,
0.355643808841705,0.216044247150421,0.389514535665512,0.266735404729843,0.440205693244934,0.300606161355972,0.499999970197678,0.3125,0.559794247150421,0.300606191158295,0.610485434532166,0.266735434532166,0.644356191158295,0.216044291853905,0.65625,0.15625,0.644356191158295,0.903544306755066,0.610485434532166,0.954235434532166,0.559794247150421,0.988106191158295,0.499999970197678,1,0.440205693244934,0.98810613155365,0.389514535665512,0.954235434532166,0.355643808841705,0.903544247150421,0.34375,0.843749940395355,0.355643838644028,0.783955693244934,0.389514625072479,0.73326450586319,0.440205782651901,0.699393808841705,0.500000059604645,0.6875,0.559794366359711,0.69939386844635,0.61048549413681,0.733264625072479,0.644356191158295,0.783955812454224,0.65625,0.84375,0.61048549413681,0.0457646325230598,0.644356191158295,0.0964557975530624,0.559794366359711,0.0118938684463501,0.500000059604645,1.49011611938477e-008,0.440205782651901,0.0118937939405441,0.389514625072479,0.0457645207643509,0.355643838644028,0.0964556634426117,0.34375,0.156249940395355,0.355643808841705,0.216044247150421,0.389514535665512,0.266735404729843,0.440205693244934,0.300606161355972,0.499999970197678,0.3125,0.559794247150421,0.300606191158295,0.610485434532166,0.266735434532166,0.644356191158295,0.216044291853905,0.65625,0.15625
			} 
			UVIndex: *256 {
				a: 16,17,34,33,17,18,35,34,18,19,36,35,19,20,37,36,20,21,38,37,21,22,39,38,22,23,40,39,23,24,41,40,24,25,42,41,25,26,43,42,26,27,44,43,27,28,45,44,28,29,46,45,29,30,47,46,30,31,48,47,31,32,49,48,1,0,67,66,2,1,66,68,3,2,68,69,4,3,69,70,5,4,70,71,6,5,71,72,7,6,72,73,8,7,73,74,9,8,74,75,10,9,75,76,11,10,76,77,12,11,77,78,13,12,78,79,14,13,79,80,15,14,80,81,0,15,81,67,64,63,83,82,63,62,84,83,62,61,85,84,61,60,86,85,60,59,87,86,59,58,88,87,58,57,89,88,57,56,90,89,56,55,91,90,55,54,92,91,54,53,93,92,53,52,94,93,52,51,95,94,51,50,96,95,50,65,97,96,65,64,82,97,66,67,99,98,68,66,98,100,69,68,100,101,70,69,101,102,71,70,102,103,72,71,103,104,73,72,104,105,74,73,105,106,75,74,106,107,76,75,107,108,77,76,108,109,78,77,109,110,79,78,110,111,80,79,111,112,81,80,112,113,67,81,113,99
			} 
		}
		LayerElementSmoothing: 0 {
			Version: 102
			Name: ""
			MappingInformationType: "ByEdge"
			ReferenceInformationType: "Direct"
			Smoothing: *128 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "AllSame"
			ReferenceInformationType: "IndexToDirect"
			Materials: *1 {
				a: 0
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementSmoothing"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}
	Model: 2231402308336, "Model::ring4_B_COL", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "currentUVSet", "KString", "", "U", "map1"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Material: 2231456702480, "Material::lambert1", "" {
		Version: 102
		ShadingModel: "lambert"
		MultiLayer: 0
		Properties70:  {
			P: "AmbientColor", "Color", "", "A",0,0,0
			P: "DiffuseColor", "Color", "", "A",1,1,1
			P: "DiffuseFactor", "Number", "", "A",0.800000011920929
			P: "TransparencyFactor", "Number", "", "A",1
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "Ambient", "Vector3D", "Vector", "",0,0,0
			P: "Diffuse", "Vector3D", "Vector", "",0.800000011920929,0.800000011920929,0.800000011920929
			P: "Opacity", "double", "Number", "",1
		}
	}
	Video: 2231456713280, "Video::file1", "Clip" {
		Type: "Clip"
		Properties70:  {
			P: "Path", "KString", "XRefUrl", "", "D:/Docs/3D/UV_Mapping_Grid.jpg"
		}
		UseMipMap: 0
		Filename: "D:/Docs/3D/UV_Mapping_Grid.jpg"
		RelativeFilename: "..\..\..\UV_Mapping_Grid.jpg"
	}
	Texture: 2231971842240, "Texture::file1", "" {
		Type: "TextureVideoClip"
		Version: 202
		TextureName: "Texture::file1"
		Properties70:  {
			P: "Scaling", "Vector", "", "A",8,8,1
			P: "CurrentTextureBlendMode", "enum", "", "",0
			P: "UVSet", "KString", "", "", "map1"
			P: "UseMaterial", "bool", "", "",1
		}
		Media: "Video::file1"
		FileName: "D:/Docs/3D/UV_Mapping_Grid.jpg"
		RelativeFilename: "..\..\..\UV_Mapping_Grid.jpg"
		ModelUVTranslation: 0,0
		ModelUVScaling: 1,1
		Texture_Alpha_Source: "None"
		Cropping: 0,0,0,0
	}
}

; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::ring4_B_COL, Model::RootNode
	C: "OO",2231402308336,0
	
	;AnimLayer::BaseLayer, AnimStack::Take 001
	C: "OO",2232026297568,2232002753504
	
	;Texture::file1, Material::lambert1
	C: "OP",2231971842240,2231456702480, "DiffuseColor"
	
	;Video::file1, Texture::file1
	C: "OO",2231456713280,2231971842240
	
	;Geometry::, Model::ring4_B_COL
	C: "OO",2231866916464,2231402308336
	
	;Material::lambert1, Model::ring4_B_COL
	C: "OO",2231456702480,2231402308336
}
