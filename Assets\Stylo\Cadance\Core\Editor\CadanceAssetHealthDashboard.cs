using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.Linq;

namespace Stylo.Cadance.Editor
{
    /// <summary>
    /// Comprehensive asset health monitoring dashboard for Cadance assets.
    /// Provides real-time validation, batch operations, and project-wide asset management.
    /// </summary>
    public class CadanceAssetHealthDashboard : EditorWindow
    {
        private CadanceAsset targetAsset;
        private Vector2 scrollPosition;
        private bool showProjectWideValidation = false;
        private ProjectAudioValidationSummary projectValidation;
        private List<CadanceAssetInfo> cadanceAssets = new List<CadanceAssetInfo>();
        private bool isScanning = false;
        private float scanProgress = 0f;

        [System.Serializable]
        public class CadanceAssetInfo
        {
            public CadanceAsset asset;
            public string path;
            public bool hasAudioClip;
            public bool audioClipValid;
            public AudioClipValidationResult validationResult;
            public List<string> issues = new List<string>();
        }

        /// <summary>
        /// Shows the asset health dashboard window.
        /// </summary>
        /// <param name="cadanceAsset">Optional specific asset to focus on</param>
        public static void ShowWindow(CadanceAsset cadanceAsset = null)
        {
            var window = GetWindow<CadanceAssetHealthDashboard>("Asset Health Dashboard");
            window.targetAsset = cadanceAsset;
            window.minSize = new Vector2(600, 400);
            window.RefreshAssetData();
        }

        private void OnGUI()
        {
            DrawHeader();

            if (targetAsset != null)
            {
                DrawSingleAssetHealth();
                EditorGUILayout.Space();
            }

            DrawProjectWideSection();

            if (isScanning)
            {
                DrawScanProgress();
            }
        }

        private void DrawHeader()
        {
            EditorGUILayout.LabelField("Cadance Asset Health Dashboard", EditorStyles.boldLabel);
            EditorGUILayout.HelpBox(
                "Monitor and manage the health of your Cadance assets. " +
                "Validate AudioClip references, fix import settings, and ensure optimal performance.",
                MessageType.Info);
            EditorGUILayout.Space();
        }

        private void DrawSingleAssetHealth()
        {
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("Current Asset Health", EditorStyles.boldLabel);

            if (targetAsset == null)
            {
                EditorGUILayout.LabelField("No asset selected", EditorStyles.centeredGreyMiniLabel);
                EditorGUILayout.EndVertical();
                return;
            }

            // Asset name and path
            EditorGUILayout.LabelField($"Asset: {targetAsset.name}");
            string assetPath = AssetDatabase.GetAssetPath(targetAsset);
            EditorGUILayout.LabelField($"Path: {assetPath}", EditorStyles.miniLabel);

            EditorGUILayout.Space();

            // AudioClip status
            bool hasAudioClip = targetAsset.SourceClip != null;
            EditorGUILayout.BeginHorizontal();

            if (hasAudioClip)
            {
                var validationResult = CadanceAudioValidator.ValidateAudioClipDetailed(targetAsset.SourceClip);

                if (validationResult.IsValid)
                {
                    GUI.color = Color.green;
                    EditorGUILayout.LabelField("✓ AudioClip Valid", EditorStyles.boldLabel);
                }
                else
                {
                    GUI.color = Color.red;
                    EditorGUILayout.LabelField("✗ AudioClip Issues", EditorStyles.boldLabel);
                }
                GUI.color = Color.white;

                if (GUILayout.Button("Validate", GUILayout.Width(80)))
                {
                    ValidateSingleAsset();
                }

                if (!validationResult.IsValid && validationResult.CanAutoFix)
                {
                    if (GUILayout.Button("Auto-Fix", GUILayout.Width(80)))
                    {
                        CadanceAudioValidator.AttemptToFixAudioClip(targetAsset.SourceClip);
                        ValidateSingleAsset();
                    }
                }
            }
            else
            {
                GUI.color = Color.yellow;
                EditorGUILayout.LabelField("⚠ AudioClip Missing", EditorStyles.boldLabel);
                GUI.color = Color.white;

                if (GUILayout.Button("Auto-Discover", GUILayout.Width(100)))
                {
                    TryAutoDiscoverForAsset(targetAsset);
                }
            }

            EditorGUILayout.EndHorizontal();

            // Track information
            EditorGUILayout.Space();
            EditorGUILayout.LabelField($"Tracks: {targetAsset.Tracks.Count}");

            int totalEvents = 0;
            foreach (var track in targetAsset.Tracks)
            {
                if (track != null)
                    totalEvents += track.EventCount;
            }
            EditorGUILayout.LabelField($"Total Events: {totalEvents}");

            EditorGUILayout.EndVertical();
        }

        private void DrawProjectWideSection()
        {
            EditorGUILayout.BeginVertical("box");

            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("Project-Wide Validation", EditorStyles.boldLabel);
            GUILayout.FlexibleSpace();

            if (GUILayout.Button("Scan Project", GUILayout.Width(100)))
            {
                ScanProjectAssets();
            }

            if (GUILayout.Button("Refresh", GUILayout.Width(80)))
            {
                RefreshAssetData();
            }

            if (GUILayout.Button("Batch Discovery", GUILayout.Width(120)))
            {
                AudioClipDiscoveryTool.ShowWindow();
            }

            if (GUILayout.Button("Auto-Fix All", GUILayout.Width(100)))
            {
                BatchAutoDiscovery();
            }
            EditorGUILayout.EndHorizontal();

            if (cadanceAssets.Count > 0)
            {
                DrawAssetSummary();
                EditorGUILayout.Space();
                DrawAssetList();
            }
            else
            {
                EditorGUILayout.LabelField("No Cadance assets found in project", EditorStyles.centeredGreyMiniLabel);
            }

            EditorGUILayout.EndVertical();
        }

        private void DrawAssetSummary()
        {
            int totalAssets = cadanceAssets.Count;
            int assetsWithAudio = cadanceAssets.Count(a => a.hasAudioClip);
            int validAssets = cadanceAssets.Count(a => a.hasAudioClip && a.audioClipValid);
            int assetsWithIssues = cadanceAssets.Count(a => a.issues.Count > 0);

            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField($"Total Assets: {totalAssets}", GUILayout.Width(120));
            EditorGUILayout.LabelField($"With Audio: {assetsWithAudio}", GUILayout.Width(120));
            EditorGUILayout.LabelField($"Valid: {validAssets}", GUILayout.Width(120));
            EditorGUILayout.LabelField($"Issues: {assetsWithIssues}", GUILayout.Width(120));
            EditorGUILayout.EndHorizontal();

            // Progress bar
            float healthPercentage = totalAssets > 0 ? (float)validAssets / totalAssets : 0f;
            Rect progressRect = GUILayoutUtility.GetRect(0, 20, GUILayout.ExpandWidth(true));
            EditorGUI.ProgressBar(progressRect, healthPercentage, $"Project Health: {healthPercentage:P0}");
        }

        private void DrawAssetList()
        {
            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);

            foreach (var assetInfo in cadanceAssets)
            {
                DrawAssetListItem(assetInfo);
            }

            EditorGUILayout.EndScrollView();
        }

        private void DrawAssetListItem(CadanceAssetInfo assetInfo)
        {
            EditorGUILayout.BeginHorizontal("box");

            // Status icon
            if (assetInfo.hasAudioClip && assetInfo.audioClipValid)
            {
                GUI.color = Color.green;
                EditorGUILayout.LabelField("✓", GUILayout.Width(20));
            }
            else if (assetInfo.hasAudioClip)
            {
                GUI.color = Color.yellow;
                EditorGUILayout.LabelField("⚠", GUILayout.Width(20));
            }
            else
            {
                GUI.color = Color.red;
                EditorGUILayout.LabelField("✗", GUILayout.Width(20));
            }
            GUI.color = Color.white;

            // Asset name
            EditorGUILayout.LabelField(assetInfo.asset.name, GUILayout.Width(200));

            // Issues summary
            if (assetInfo.issues.Count > 0)
            {
                EditorGUILayout.LabelField($"{assetInfo.issues.Count} issues", EditorStyles.miniLabel, GUILayout.Width(80));
            }
            else
            {
                EditorGUILayout.LabelField("OK", EditorStyles.miniLabel, GUILayout.Width(80));
            }

            GUILayout.FlexibleSpace();

            // Action buttons
            if (GUILayout.Button("Select", GUILayout.Width(60)))
            {
                Selection.activeObject = assetInfo.asset;
                EditorGUIUtility.PingObject(assetInfo.asset);
            }

            if (GUILayout.Button("Edit", GUILayout.Width(60)))
            {
                CadanceEditorWindow.OpenCadance(assetInfo.asset);
            }

            EditorGUILayout.EndHorizontal();
        }

        private void DrawScanProgress()
        {
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Scanning project assets...", EditorStyles.boldLabel);

            Rect progressRect = GUILayoutUtility.GetRect(0, 20, GUILayout.ExpandWidth(true));
            EditorGUI.ProgressBar(progressRect, scanProgress, $"Progress: {scanProgress:P0}");
        }

        private void ValidateSingleAsset()
        {
            if (targetAsset == null) return;

            // Force refresh of the current asset validation
            Repaint();
        }

        private void TryAutoDiscoverForAsset(CadanceAsset asset)
        {
            if (asset == null || string.IsNullOrEmpty(asset.SourceClipName)) return;

            // Search for AudioClip by name
            string[] audioClipGuids = AssetDatabase.FindAssets($"{asset.SourceClipName} t:AudioClip");

            if (audioClipGuids.Length > 0)
            {
                string path = AssetDatabase.GUIDToAssetPath(audioClipGuids[0]);
                var audioClip = AssetDatabase.LoadAssetAtPath<AudioClip>(path);

                if (audioClip != null)
                {
                    asset.SourceClip = audioClip;
                    EditorUtility.SetDirty(asset);
                    Debug.Log($"[Asset Health] Auto-discovered AudioClip for {asset.name}: {audioClip.name}");
                    RefreshAssetData();
                }
            }
            else
            {
                Debug.LogWarning($"[Asset Health] No AudioClip found for {asset.name} (looking for: {asset.SourceClipName})");
            }
        }

        private void RefreshAssetData()
        {
            cadanceAssets.Clear();

            string[] cadanceGuids = AssetDatabase.FindAssets("t:CadanceAsset");

            foreach (string guid in cadanceGuids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                var asset = AssetDatabase.LoadAssetAtPath<CadanceAsset>(path);

                if (asset != null)
                {
                    var assetInfo = new CadanceAssetInfo
                    {
                        asset = asset,
                        path = path,
                        hasAudioClip = asset.SourceClip != null
                    };

                    if (assetInfo.hasAudioClip)
                    {
                        assetInfo.validationResult = CadanceAudioValidator.ValidateAudioClipDetailed(asset.SourceClip);
                        assetInfo.audioClipValid = assetInfo.validationResult.IsValid;
                        assetInfo.issues.AddRange(assetInfo.validationResult.Issues);
                    }
                    else if (!string.IsNullOrEmpty(asset.SourceClipName))
                    {
                        assetInfo.issues.Add("AudioClip reference missing");
                    }

                    cadanceAssets.Add(assetInfo);
                }
            }

            Repaint();
        }

        private void BatchAutoDiscovery()
        {
            if (EditorUtility.DisplayDialog("Batch Auto-Discovery",
                "Attempt to automatically discover and assign AudioClips for all Cadance assets with missing references?",
                "Yes", "Cancel"))
            {
                int fixedCount = 0;
                int totalAttempts = 0;

                foreach (var assetInfo in cadanceAssets)
                {
                    if (!assetInfo.hasAudioClip && !string.IsNullOrEmpty(assetInfo.asset.SourceClipName))
                    {
                        totalAttempts++;

                        // Try to find matching AudioClip
                        string[] audioClipGuids = AssetDatabase.FindAssets($"{assetInfo.asset.SourceClipName} t:AudioClip");

                        if (audioClipGuids.Length > 0)
                        {
                            string path = AssetDatabase.GUIDToAssetPath(audioClipGuids[0]);
                            var audioClip = AssetDatabase.LoadAssetAtPath<AudioClip>(path);

                            if (audioClip != null && CadanceAudioValidator.IsAudioClipValid(audioClip))
                            {
                                assetInfo.asset.SourceClip = audioClip;
                                EditorUtility.SetDirty(assetInfo.asset);
                                fixedCount++;
                                Debug.Log($"[Batch Discovery] Fixed {assetInfo.asset.name} -> {audioClip.name}");
                            }
                        }
                    }
                }

                AssetDatabase.SaveAssets();
                RefreshAssetData();

                EditorUtility.DisplayDialog("Batch Auto-Discovery Complete",
                    $"Successfully resolved {fixedCount} out of {totalAttempts} missing AudioClip references.",
                    "OK");
            }
        }

        private void ScanProjectAssets()
        {
            isScanning = true;
            scanProgress = 0f;

            // Start async scan
            EditorApplication.delayCall += () =>
            {
                projectValidation = CadanceAudioValidator.ValidateProjectAudioClips();
                RefreshAssetData();
                isScanning = false;
                scanProgress = 1f;
                Repaint();
            };
        }
    }
}
