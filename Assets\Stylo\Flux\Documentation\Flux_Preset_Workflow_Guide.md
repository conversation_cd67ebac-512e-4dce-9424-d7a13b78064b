# Flux Preset Workflow Guide

## Overview

The Flux Preset system provides a **direct, asset-based workflow** for managing datamoshing effect configurations. Presets are ScriptableObject assets that can be **dragged directly onto Volume Profiles and Flux Effect components** for instant parameter application.

---

## 🎯 **Core Workflow**

### **1. Direct Asset Application**

- **Drag & Drop**: Drag FluxPreset assets directly onto Volume Profiles or Flux Effect components
- **Instant Application**: Parameters are applied immediately when preset is assigned
- **Asset-Based**: Presets are project assets that can be shared, versioned, and organized

### **2. Native Unity Integration**

- **Project Browser**: Presets appear as normal assets in the Project window
- **Inspector Integration**: Preset fields with "Apply" buttons in component inspectors
- **Asset Management**: Full Unity asset workflow (rename, move, duplicate, etc.)

---

## 📁 **Creating Presets**

### **Method 1: From Volume Profile**

1. Configure your Flux Effect parameters in a Volume Profile
2. Right-click the Volume Profile in Project window
3. Select **"Create Flux Preset from Selection"**
4. Preset asset is created next to the Volume Profile

### **Method 2: From Flux Effect Component**

1. Configure your Flux Effect component parameters
2. Right-click the Flux Effect component asset (if applicable)
3. Select **"Create Flux Preset from Selection"**
4. Preset asset is created with current settings

### **Method 3: Manual Creation**

1. Right-click in Project window
2. Select **"Create → Stylo → Flux Preset"**
3. Configure parameters manually in the preset inspector
4. Save the asset

### **Method 4: From Volume Profile Inspector**

1. Select a Volume Profile with Flux Effect
2. In the inspector, click **"Create Preset from Current Settings"**
3. Choose save location and name
4. Preset asset is created and selected

---

## 🚀 **Applying Presets**

### **Method 1: Direct Drag & Drop**

- **To Volume Profile**: Drag preset onto Volume Profile asset in Project window
- **To Volume Component**: Drag preset onto Volume component in Scene
- **To Flux Effect**: Drag preset onto Flux Effect component

### **Method 2: Inspector Application**

- **Volume Profiles**: Use the "Flux Preset Application" section in Volume Profile inspector
- **Flux Effects**: Use the "Preset Application" section in Flux Effect inspector
- **Property Drawers**: Use "Apply" buttons next to preset fields

### **Method 3: Programmatic Application**

- **Runtime Code**: Use `preset.ApplyToFluxComponent(fluxEffect)` in scripts
- **Editor Scripts**: Use `preset.ApplyToVolumeProfile(volumeProfile)` for automation

---

## 🎨 **Default Presets**

### **VHS Datamosh**

```
Category: VHS
Effect Intensity: 0.7
Motion Vector Corruption: 1.2
Error Accumulation: 0.6
Chroma Corruption: 0.4
Multi-Scale Corruption: 0.3
```

### **Digital Glitch**

```
Category: Digital
Effect Intensity: 0.8
Feedback Intensity: 0.6
Multi-Scale Corruption: 0.8
DCT Corruption: 0.5
Glitch Transition: 0.7
```

### **Subtle Compression**

```
Category: Subtle
Effect Intensity: 0.3
DCT Corruption: 0.2
Chroma Corruption: 0.1
Multi-Scale Corruption: 0.2
```

### **Satellite Feed Loss**

```
Category: Cinematic
Effect Intensity: 0.9
Motion Vector Corruption: 2.0
Error Accumulation: 0.8
Multi-Scale Corruption: 0.6
```

---

## 🔧 **Advanced Usage**

### **Preset Organization**

- **Categories**: Use the `category` field to organize presets
- **Folders**: Organize preset assets in project folders by type/use case
- **Naming**: Use descriptive names that indicate intensity and style

### **Team Workflow**

- **Shared Library**: Create a "Flux Presets" folder for team-shared presets
- **Version Control**: Presets are version-control friendly ScriptableObject assets
- **Project Templates**: Include standard presets in project templates

### **Performance Considerations**

- **Mobile Variants**: Create mobile-optimized versions of presets
- **Platform Specific**: Different presets for different target platforms
- **Quality Levels**: Multiple intensity levels for the same style

---

## 🎛️ **Preset Asset Structure**

### **Metadata**

- **Preset Name**: Display name for UI
- **Category**: Organization category (VHS, Digital, Cinematic, etc.)
- **Description**: Detailed description of the effect

### **Core Parameters**

- Effect Intensity, Color Crunch, Downscaling, Block Size
- Oversharpening, Don't Crunch Skybox, Only Stenciled

### **Datamoshing Parameters**

- Base Noise, Base Reroll Speed, Length Influence
- Keyframe Reset Rate, Motion Vector Corruption
- Error Accumulation, DCT Corruption

### **Enhanced Corruption**

- Corruption Mask, Chroma Corruption, Glitch Transition
- Feedback Intensity, Multi-Scale Corruption

---

## 🔄 **Integration Examples**

### **Scene Setup Workflow**

1. Create Volume in scene
2. Create or assign Volume Profile
3. Drag desired Flux preset onto Volume Profile
4. Flux Effect is automatically added and configured

### **Animation Workflow**

1. Create multiple presets for different intensities
2. Use Timeline or Animation system to blend between presets
3. Animate the `PresetToApply` parameter for smooth transitions

### **Runtime Switching**

```csharp
// Switch presets at runtime (URP)
public FluxPreset[] presets;
public Volume volume;

void SwitchToPreset(int index)
{
    if (volume.profile.TryGet<Universal.FluxEffect>(out var flux))
    {
        presets[index].ApplyToURP(flux);
    }
}
```

---

## 🛠️ **Menu Commands**

### **Asset Creation**

- `Assets → Create → Stylo → Flux Preset`
- `Assets → Create Flux Preset from Selection` (context-sensitive)

### **Quick Actions**

- Right-click Volume Profile → "Create Flux Preset from Selection"
- Right-click Flux Effect → "Create Flux Preset from Selection"

---

## 🎯 **Best Practices**

### **Naming Conventions**

- **Style_Intensity**: "VHS_Heavy", "Digital_Subtle", "Cinematic_Impact"
- **Use Case**: "UI_Safe_Glitch", "Background_Corruption", "Transition_Effect"
- **Platform**: "Mobile_VHS", "Console_Digital", "PC_Ultra"

### **Organization**

```
Flux Presets/
├── VHS/
│   ├── VHS_Light.asset
│   ├── VHS_Medium.asset
│   └── VHS_Heavy.asset
├── Digital/
│   ├── Digital_Glitch_Art.asset
│   └── Digital_Cyberpunk.asset
└── Cinematic/
    ├── Satellite_Loss.asset
    └── Signal_Interference.asset
```

### **Version Control**

- Include preset assets in version control
- Use meaningful commit messages when updating presets
- Consider preset changes as part of visual design iterations

---

## 🚀 **Benefits**

### **Artist-Friendly**

- **Visual Asset Management**: Presets are visible assets in Project window
- **Drag & Drop Workflow**: Intuitive application method
- **Immediate Feedback**: Instant parameter application

### **Production-Ready**

- **Asset-Based**: Full Unity asset workflow support
- **Team Collaboration**: Easy sharing and version control
- **Performance Optimized**: No runtime overhead for preset system

### **Flexible**

- **Multiple Application Methods**: Choose the workflow that fits your needs
- **Cross-Pipeline**: Works with both Built-in RP and URP
- **Extensible**: Easy to add new parameters or features

---

**The Flux Preset system transforms parameter management from manual tweaking to asset-based workflow, enabling rapid iteration and consistent quality across your project.**
