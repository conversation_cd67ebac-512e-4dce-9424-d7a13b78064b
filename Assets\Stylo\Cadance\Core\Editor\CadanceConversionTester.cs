using UnityEngine;
using UnityEditor;
using System.IO;
using System.Linq;
using SonicBloom.Koreo;
using Stylo.Cadance;

namespace Stylo.Cadance.Editor
{
    /// <summary>
    /// Tool to test Koreography to Cadance conversion with a single asset to verify the process works correctly.
    /// </summary>
    public class CadanceConversionTester : EditorWindow
    {
        private Koreography testKoreography;
        private CadanceAsset resultCadanceAsset;
        private string testOutputPath = "Assets/_Scriptable Objects/CadanceConverts/Test";
        private Vector2 scrollPosition;
        private bool testComplete = false;
        private string testResults = "";

        [MenuItem("Stylo/Cadance/Tools/Conversion Tester", false, 101)]
        public static void ShowWindow()
        {
            GetWindow<CadanceConversionTester>("Cadance Conversion Tester");
        }

        private void OnGUI()
        {
            EditorGUILayout.LabelField("Cadance Conversion Tester", EditorStyles.boldLabel);
            EditorGUILayout.HelpBox("This tool tests the conversion process with a single Koreography asset to verify data preservation.", MessageType.Info);

            EditorGUILayout.Space();

            // Test asset selection
            EditorGUILayout.LabelField("Test Configuration", EditorStyles.boldLabel);
            testKoreography = (Koreography)EditorGUILayout.ObjectField("Test Koreography", testKoreography, typeof(Koreography), false);
            testOutputPath = EditorGUILayout.TextField("Output Path", testOutputPath);

            EditorGUILayout.Space();

            EditorGUI.BeginDisabledGroup(testKoreography == null);
            if (GUILayout.Button("Run Conversion Test"))
            {
                RunConversionTest();
            }
            EditorGUI.EndDisabledGroup();

            EditorGUILayout.Space();

            if (testComplete)
            {
                EditorGUILayout.LabelField("Test Results", EditorStyles.boldLabel);

                scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);
                EditorGUILayout.TextArea(testResults, GUILayout.ExpandHeight(true));
                EditorGUILayout.EndScrollView();

                EditorGUILayout.Space();

                if (resultCadanceAsset != null)
                {
                    EditorGUILayout.LabelField("Result Asset", EditorStyles.boldLabel);
                    EditorGUI.BeginDisabledGroup(true);
                    EditorGUILayout.ObjectField("Converted CadanceAsset", resultCadanceAsset, typeof(CadanceAsset), false);
                    EditorGUI.EndDisabledGroup();

                    if (GUILayout.Button("Select Result Asset"))
                    {
                        Selection.activeObject = resultCadanceAsset;
                        EditorGUIUtility.PingObject(resultCadanceAsset);
                    }
                }
            }
        }

        private void RunConversionTest()
        {
            testResults = "";
            testComplete = false;
            resultCadanceAsset = null;

            try
            {
                LogResult("=== CADANCE CONVERSION TEST ===");
                LogResult($"Testing conversion of: {testKoreography.name}");
                LogResult($"Original path: {AssetDatabase.GetAssetPath(testKoreography)}");
                LogResult("");

                // Analyze original asset
                AnalyzeOriginalKoreography();

                // Perform conversion
                LogResult("--- CONVERSION PROCESS ---");
                var convertedAsset = Cadance.Instance.ConvertKoreographyToCadance(testKoreography);

                if (convertedAsset == null)
                {
                    LogResult("❌ CONVERSION FAILED: ConvertKoreographyToCadance returned null");
                    testComplete = true;
                    return;
                }

                LogResult($"✅ Conversion successful, created CadanceAsset: {convertedAsset.name}");

                // Save the converted asset
                SaveConvertedAsset(convertedAsset);

                // Analyze converted asset
                AnalyzeConvertedAsset(convertedAsset);

                // Compare data preservation
                CompareDataPreservation();

                LogResult("");
                LogResult("=== TEST COMPLETE ===");

            }
            catch (System.Exception ex)
            {
                LogResult($"❌ TEST FAILED WITH EXCEPTION: {ex.Message}");
                LogResult($"Stack trace: {ex.StackTrace}");
            }

            testComplete = true;
        }

        private void AnalyzeOriginalKoreography()
        {
            LogResult("--- ORIGINAL KOREOGRAPHY ANALYSIS ---");
            LogResult($"Name: {testKoreography.name}");
            LogResult($"Source Clip: {testKoreography.SourceClip?.name ?? "None"}");
            LogResult($"Source Clip Name: {testKoreography.SourceClipName}");
            LogResult($"Sample Rate: {testKoreography.SampleRate}");

            string[] eventIDs = testKoreography.GetEventIDs();
            LogResult($"Track Count: {eventIDs.Length}");

            foreach (string eventID in eventIDs)
            {
                var track = testKoreography.GetTrackByID(eventID);
                if (track != null)
                {
                    var events = track.GetAllEvents();
                    LogResult($"  Track '{eventID}': {events.Count} events");

                    if (events.Count > 0)
                    {
                        var firstEvent = events[0];
                        var lastEvent = events[events.Count - 1];
                        LogResult($"    First event: sample {firstEvent.StartSample}");
                        LogResult($"    Last event: sample {lastEvent.StartSample}");

                        // Check for payload types
                        int payloadCount = 0;
                        foreach (var evt in events)
                        {
                            if (evt.Payload != null) payloadCount++;
                        }
                        LogResult($"    Events with payloads: {payloadCount}");
                    }
                }
                else
                {
                    LogResult($"  Track '{eventID}': ❌ NULL TRACK");
                }
            }
            LogResult("");
        }

        private void SaveConvertedAsset(CadanceAsset convertedAsset)
        {
            LogResult("--- SAVING CONVERTED ASSET ---");

            // Ensure output directory exists
            if (!Directory.Exists(testOutputPath))
            {
                Directory.CreateDirectory(testOutputPath);
                AssetDatabase.Refresh();
            }

            // Create unique filename
            string fileName = $"{testKoreography.name}_TestConversion.asset";
            string fullPath = Path.Combine(testOutputPath, fileName).Replace('\\', '/');

            // Ensure unique filename
            int counter = 1;
            while (File.Exists(fullPath))
            {
                fileName = $"{testKoreography.name}_TestConversion_{counter}.asset";
                fullPath = Path.Combine(testOutputPath, fileName).Replace('\\', '/');
                counter++;
            }

            // Save the main asset
            AssetDatabase.CreateAsset(convertedAsset, fullPath);
            LogResult($"Created main asset: {fullPath}");

            // Add tracks as sub-assets
            int savedTracks = 0;
            foreach (var track in convertedAsset.Tracks)
            {
                if (track != null)
                {
                    AssetDatabase.AddObjectToAsset(track, convertedAsset);
                    savedTracks++;
                    LogResult($"  Added track '{track.EventID}' as sub-asset");
                }
                else
                {
                    LogResult($"  ❌ Skipped null track");
                }
            }

            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            LogResult($"✅ Saved {savedTracks} tracks as sub-assets");

            // Load the saved asset to verify
            resultCadanceAsset = AssetDatabase.LoadAssetAtPath<CadanceAsset>(fullPath);
            if (resultCadanceAsset != null)
            {
                LogResult($"✅ Successfully loaded saved asset");
            }
            else
            {
                LogResult($"❌ Failed to load saved asset");
            }

            LogResult("");
        }

        private void AnalyzeConvertedAsset(CadanceAsset convertedAsset)
        {
            LogResult("--- CONVERTED CADANCE ASSET ANALYSIS ---");
            LogResult($"Name: {convertedAsset.name}");
            LogResult($"Source Clip: {convertedAsset.SourceClip?.name ?? "None"}");
            LogResult($"Source Clip Name: {convertedAsset.SourceClipName}");
            LogResult($"Sample Rate: {convertedAsset.SampleRate}");
            LogResult($"Track Count: {convertedAsset.Tracks.Count}");

            int nullTracks = 0;
            foreach (var track in convertedAsset.Tracks)
            {
                if (track == null)
                {
                    nullTracks++;
                    LogResult($"  ❌ NULL TRACK");
                }
                else
                {
                    LogResult($"  Track '{track.EventID}': {track.EventCount} events");

                    if (track.EventCount > 0)
                    {
                        var firstEvent = track.GetEventAtIndex(0);
                        var lastEvent = track.GetEventAtIndex(track.EventCount - 1);
                        LogResult($"    First event: sample {firstEvent.StartSample}");
                        LogResult($"    Last event: sample {lastEvent.StartSample}");

                        // Check for payload preservation
                        int payloadCount = 0;
                        for (int i = 0; i < track.EventCount; i++)
                        {
                            var evt = track.GetEventAtIndex(i);
                            if (evt.Payload != null) payloadCount++;
                        }
                        LogResult($"    Events with payloads: {payloadCount}");
                    }
                }
            }

            if (nullTracks > 0)
            {
                LogResult($"❌ Found {nullTracks} null tracks");
            }

            LogResult("");
        }

        private void CompareDataPreservation()
        {
            LogResult("--- DATA PRESERVATION COMPARISON ---");

            if (resultCadanceAsset == null)
            {
                LogResult("❌ Cannot compare: no result asset loaded");
                return;
            }

            // Compare basic properties
            bool sourceClipNameMatch = testKoreography.SourceClipName == resultCadanceAsset.SourceClipName;
            bool sampleRateMatch = testKoreography.SampleRate == resultCadanceAsset.SampleRate;

            LogResult($"Source Clip Name: {(sourceClipNameMatch ? "✅" : "❌")} {testKoreography.SourceClipName} vs {resultCadanceAsset.SourceClipName}");
            LogResult($"Sample Rate: {(sampleRateMatch ? "✅" : "❌")} {testKoreography.SampleRate} vs {resultCadanceAsset.SampleRate}");

            // Compare tracks
            string[] originalEventIDs = testKoreography.GetEventIDs();
            bool trackCountMatch = originalEventIDs.Length == resultCadanceAsset.Tracks.Count;
            LogResult($"Track Count: {(trackCountMatch ? "✅" : "❌")} {originalEventIDs.Length} vs {resultCadanceAsset.Tracks.Count}");

            // Check each track
            foreach (string eventID in originalEventIDs)
            {
                var originalTrack = testKoreography.GetTrackByID(eventID);
                var convertedTrack = resultCadanceAsset.Tracks.FirstOrDefault(t => t != null && t.EventID == eventID);

                if (convertedTrack == null)
                {
                    LogResult($"❌ Track '{eventID}': not found in converted asset");
                }
                else
                {
                    var originalEvents = originalTrack.GetAllEvents();
                    bool eventCountMatch = originalEvents.Count == convertedTrack.EventCount;
                    LogResult($"{(eventCountMatch ? "✅" : "❌")} Track '{eventID}': {originalEvents.Count} vs {convertedTrack.EventCount} events");
                }
            }

            LogResult("");
        }

        private void LogResult(string message)
        {
            testResults += message + "\n";
            Debug.Log($"[ConversionTest] {message}");
        }
    }
}
