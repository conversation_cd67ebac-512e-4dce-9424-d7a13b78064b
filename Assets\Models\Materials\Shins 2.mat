%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!28 &-4911530017942088168
Texture2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: z__GradientRampTex{"mode":0,"colorKeys":[{"color":{"r":0.46226418018341067,"g":0.0,"b":0.0,"a":1.0},"time":0.2500038146972656},{"color":{"r":1.0,"g":1.0,"b":1.0,"a":1.0},"time":1.0}],"alphaKeys":[{"alpha":1.0,"time":0.0},{"alpha":1.0,"time":1.0}]}
  m_ImageContentsHash:
    serializedVersion: 2
    Hash: 00000000000000000000000000000000
  m_IsAlphaChannelOptional: 0
  serializedVersion: 4
  m_Width: 256
  m_Height: 1
  m_CompleteImageSize: 1024
  m_MipsStripped: 0
  m_TextureFormat: 5
  m_MipCount: 1
  m_IsReadable: 1
  m_IsPreProcessed: 0
  m_IgnoreMipmapLimit: 0
  m_MipmapLimitGroupName: 
  m_StreamingMipmaps: 0
  m_StreamingMipmapsPriority: 0
  m_VTOnly: 0
  m_AlphaIsTransparency: 0
  m_ImageCount: 1
  m_TextureDimension: 2
  m_TextureSettings:
    serializedVersion: 2
    m_FilterMode: 1
    m_Aniso: 1
    m_MipBias: 0
    m_WrapU: 1
    m_WrapV: 1
    m_WrapW: 1
  m_LightmapFormat: 0
  m_ColorSpace: 1
  m_PlatformBlob: 
  image data: 1024
  _typelessdata: ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff770202ff770303ff780404ff790606ff7a0707ff7a0808ff7b0a0aff7c0b0bff7d0c0cff7d0e0eff7e0f0fff7f1010ff7f1212ff801313ff811414ff821616ff821717ff831818ff841a1aff841b1bff851c1cff861e1eff871f1fff872020ff882222ff892323ff892424ff8a2626ff8b2727ff8c2828ff8c2a2aff8d2b2bff8e2c2cff8e2e2eff8f2f2fff903030ff913232ff913333ff923434ff933636ff933737ff943838ff953a3aff963b3bff963c3cff973e3eff983f3fff984040ff994242ff9a4343ff9b4444ff9b4646ff9c4747ff9d4848ff9d4a4aff9e4b4bff9f4c4cffa04e4effa04f4fffa15050ffa25252ffa35353ffa35454ffa45656ffa55757ffa55858ffa65a5affa75b5bffa85c5cffa85e5effa95f5fffaa6060ffaa6262ffab6363ffac6464ffad6666ffad6767ffae6868ffaf6a6affaf6b6bffb06c6cffb16e6effb26f6fffb27070ffb37272ffb47373ffb47474ffb57676ffb67777ffb77878ffb77a7affb87b7bffb97c7cffb97e7effba7f7fffbb8080ffbc8282ffbc8383ffbd8484ffbe8686ffbe8787ffbf8888ffc08a8affc18b8bffc18c8cffc28e8effc38f8fffc39090ffc49292ffc59393ffc69494ffc69696ffc79797ffc89898ffc99a9affc99b9bffca9c9cffcb9e9effcb9f9fffcca0a0ffcda2a2ffcea3a3ffcea4a4ffcfa6a6ffd0a7a7ffd0a8a8ffd1aaaaffd2ababffd3acacffd3aeaeffd4afafffd5b0b0ffd5b2b2ffd6b3b3ffd7b4b4ffd8b6b6ffd8b7b7ffd9b8b8ffdababaffdabbbbffdbbcbcffdcbebeffddbfbfffddc0c0ffdec2c2ffdfc3c3ffdfc4c4ffe0c6c6ffe1c7c7ffe2c8c8ffe2cacaffe3cbcbffe4ccccffe4ceceffe5cfcfffe6d0d0ffe7d2d2ffe7d3d3ffe8d4d4ffe9d6d6ffe9d7d7ffead8d8ffebdadaffecdbdbffecdcdcffeddedeffeedfdfffefe0e0ffefe2e2fff0e3e3fff1e4e4fff1e6e6fff2e7e7fff3e8e8fff4eaeafff4ebebfff5ececfff6eeeefff6efeffff7f0f0fff8f2f2fff9f3f3fff9f4f4fffaf6f6fffbf7f7fffbf8f8fffcfafafffdfbfbfffefcfcfffefefeffffffff
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Shins 2
  m_Shader: {fileID: 4800000, guid: 6d7dbbdf9194b6745ae79aaf2f3c053c, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords:
  - _ExcludeFromJPG
  m_InvalidKeywords:
  - DR_SPECULAR_ON
  - _DETAILMAPBLENDINGMODE_MULTIPLY
  - _EXCLUDE_FROM_JPG
  - _ReceiveFogEnabled
  - _ReceiveShadowsEnabled
  - _SubsurfaceEnabled
  - _TEXTUREBLENDINGMODE_MULTIPLY
  - _UNITYSHADOW_OCCLUSION
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: 2000
  stringTagMap:
    RenderType: Opaque
  disabledShaderPasses:
  - SRPDEFAULTUNLIT
  - MOTIONVECTORS
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BakedGIRamp:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BaseMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Emission_Map:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _GradientRamp:
        m_Texture: {fileID: -4911530017942088168}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _HeightMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _HeightMap2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetalnessMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetalnessMap2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalMap2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _RoughnessMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _RoughnessMap2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecularDabTexture:
        m_Texture: {fileID: 2800000, guid: 442e6933888925c48b05d56bcfba9809, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SubsurfaceThicknessMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AOEnabled: 1
    - _AOStrength: 0.2
    - _AOToonShadingEnabled: 1
    - _AddPrecomputedVelocity: 1
    - _AdditionalLightToonShadingEnabled: 1
    - _AdditionalLightToonShadingThreshold: 0.1
    - _AlphaClip: 0
    - _AlphaClipEnabled: 0
    - _AlphaClipThreshold: 0
    - _AlphaCutoff: 0.5
    - _AlphaCutoffEnable: 0
    - _AlphaDstBlend: 0
    - _AlphaSrcBlend: 1
    - _AlphaToMask: 0
    - _AmbientLightStrength: 1
    - _Blend: 0
    - _BlendMode: 0
    - _BlendOp: 0
    - _BumpScale: 1
    - _CameraDistanceImpact: 0
    - _CameraFadingEnabled: 0
    - _CameraFarFadeDistance: 2
    - _CameraNearFadeDistance: 1
    - _CastShadows: 1
    - _ClearCoatMask: 0
    - _ClearCoatSmoothness: 0
    - _ColorMode: 0
    - _Cull: 2
    - _CullMode: 2
    - _CullModeForward: 2
    - _Culling: 2
    - _Cutoff: 0.5
    - _DetailAlbedoMapScale: 1
    - _DetailMapBlendingMode: 0
    - _DetailMapImpact: 0
    - _DetailNormalMapScale: 1
    - _DistortionBlend: 0.5
    - _DistortionEnabled: 0
    - _DistortionStrength: 1
    - _DistortionStrengthScaled: 0.1
    - _DoubleSidedEnable: 0
    - _DoubleSidedNormalMode: 2
    - _DstBlend: 0
    - _Emissive: 0
    - _EnableFogOnTransparent: 1
    - _EnvironmentReflections: 1
    - _ExcludeFromJPG: 1
    - _FlatRimEdgeSmoothness: 0.5
    - _FlatRimLightAlign: 0
    - _FlatRimSize: 0.5
    - _FlatSpecularEdgeSmoothness: 0.098
    - _FlatSpecularSize: 0.073
    - _FlipbookBlending: 0
    - _FlipbookMode: 0
    - _FogEnabled: 1
    - _GlossMapScale: 1
    - _Glossiness: 0
    - _GlossyReflections: 1
    - _GradientAngle: 0
    - _GradientCenterX: 0
    - _GradientCenterY: 0
    - _GradientEnabled: 0
    - _GradientSize: 10
    - _GridSizeX: 24
    - _GridSizeY: -2.2
    - _GridSizeZ: 5.75
    - _HasEmissionMap: 0
    - _HasEmissionMap2: 0
    - _HasHeightMap: 0
    - _HasHeightMap2: 0
    - _HasMetalnessMap: 0
    - _HasMetalnessMap2: 0
    - _HasNormalMap: 0
    - _HasNormalMap2: 0
    - _HasRoughnessMap: 0
    - _HasRoughnessMap2: 0
    - _HasSubsurfaceMap: 0
    - _HeightStrength: 0.02
    - _HeightStrength2: 0.02
    - _LightContribution: 0.527
    - _LightingMode: 1
    - _LightmapDirectionPitch: 0
    - _LightmapDirectionYaw: 0
    - _Metallic: 0
    - _Metalness: 0
    - _Metalness2: 0
    - _MetalnessMapExposure: 0
    - _MetalnessMapExposure2: 0
    - _Midpoint: 0.3
    - _MidpointStrength: 0.5
    - _Mode: 0
    - _NormalStrength: 1
    - _NormalStrength2: 1
    - _OcclusionStrength: 1
    - _OcclusionStrength2: 1
    - _OutlineDepthOffset: 0
    - _OutlineEnabled: 0
    - _OutlineScale: 1
    - _OutlineWidth: 11.44
    - _OverrideBakedGi: 0
    - _OverrideLightAttenuation: 0
    - _OverrideLightmapDir: 0
    - _Parallax: 0.02
    - _QueueControl: 0
    - _QueueOffset: 0
    - _ReceiveAdditionalLights: 1
    - _ReceiveFogEnabled: 1
    - _ReceiveShadows: 1
    - _ReceiveShadowsEnabled: 1
    - _ReceivesSSR: 1
    - _RenderQueueType: 1
    - _RequireSplitLighting: 0
    - _RimEnabled: 0
    - _RimLightingEnabled: 1
    - _RimThreshold: 0.85
    - _Roughness: 0.5
    - _Roughness2: 0.5
    - _RoughnessAmount: 0.25
    - _RoughnessMapExposure: 0
    - _RoughnessMapExposure2: 0
    - _SelfShadingSize: 0.558
    - _Smoothness: 0.5
    - _SmoothnessTextureChannel: 0
    - _SoftParticlesEnabled: 0
    - _SoftParticlesFarFadeDistance: 1
    - _SoftParticlesNearFadeDistance: 0
    - _SortPriority: 0
    - _SpecularColorAmount: 0
    - _SpecularDabRotation: 0
    - _SpecularDabScale: 20
    - _SpecularEnabled: 1
    - _SpecularHighlights: 1
    - _SpecularHighlightsEnabled: 1
    - _Specularity: 0.5
    - _Specularity2: 0.5
    - _SrcBlend: 1
    - _StencilRef: 0
    - _StencilRefDepth: 8
    - _StencilRefDistortionVec: 4
    - _StencilRefGBuffer: 10
    - _StencilRefMV: 40
    - _StencilWriteMask: 6
    - _StencilWriteMaskDepth: 8
    - _StencilWriteMaskDistortionVec: 4
    - _StencilWriteMaskGBuffer: 14
    - _StencilWriteMaskMV: 40
    - _SubsurfaceAmbient: 0.2
    - _SubsurfaceDistortion: 0.3
    - _SubsurfaceEnabled: 1
    - _SubsurfaceFalloff: 2
    - _SubsurfaceThickness: 0.8
    - _Surface: 0
    - _SurfaceType: 0
    - _TextureBlendingMode: 0
    - _TextureImpact: 0
    - _TransparentBackfaceEnable: 0
    - _TransparentCullMode: 2
    - _TransparentSortPriority: 0
    - _TransparentZWrite: 1
    - _UVSec: 0
    - _UnityShadowOcclusion: 1
    - _UseShadowThreshold: 0
    - _UseVertexColors: 0
    - _VertexColorsEnabled: 0
    - _ViewState: 0
    - _WorkflowMode: 1
    - _ZTest: 4
    - _ZTestDepthEqualForOpaque: 4
    - _ZTestGBuffer: 4
    - _ZTestTransparent: 4
    - _ZWrite: 1
    - _ZWriteControl: 0
    m_Colors:
    - _BaseColor: {r: 0.8867924, g: 0.012548926, b: 0.22005603, a: 1}
    - _BaseColor2: {r: 0.5, g: 0.5, b: 0.5, a: 1}
    - _BaseColorAddSubDiff: {r: 0, g: 0, b: 0, a: 0}
    - _CameraFadeParams: {r: 0, g: 0, b: 0, a: 0}
    - _Color: {r: 0.5, g: 0, b: 0.2580311, a: 1}
    - _Color0: {r: 1, g: 0.22169808, b: 0.22169808, a: 1}
    - _ColorGradient: {r: 0.85023, g: 0.85034, b: 0.8504499, a: 0.85056}
    - _Color_Lit: {r: 1, g: 1, b: 1, a: 0}
    - _DetailMapColor: {r: 1, g: 1, b: 1, a: 1}
    - _DoubleSidedConstants: {r: 1, g: 1, b: -1, a: 0}
    - _Emission: {r: 0, g: 0, b: 0, a: 1}
    - _Emission2: {r: 0, g: 0, b: 0, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _Emission_Color: {r: 0, g: 0, b: 0, a: 0}
    - _FlatRimColor: {r: 0.85023, g: 0.85034, b: 0.85045, a: 0.85056}
    - _FlatSpecularColor: {r: 1, g: 0, b: 0, a: 0.85056}
    - _LightAttenuation: {r: 0, g: 1, b: 0, a: 0}
    - _LightmapDirection: {r: 0, g: 1, b: 0, a: 0}
    - _MainColor: {r: 191.74905, g: 0, b: 49.954643, a: 1}
    - _OffsetMatrix: {r: 9993.62, g: 0, b: 0, a: 0}
    - _OutlineColor: {r: 1, g: 1, b: 1, a: 1}
    - _RimColor: {r: 1, g: 1, b: 1, a: 0}
    - _ShadowColor: {r: 0, g: 0, b: 0, a: 1}
    - _ShadowTint: {r: 0, g: 0, b: 0, a: 0}
    - _SoftParticleFadeParams: {r: 0, g: 0, b: 0, a: 0}
    - _SpecColor: {r: 0.2, g: 0.2, b: 0.2, a: 1}
    - _SpecularColor: {r: 1, g: 1, b: 1, a: 0}
    - _SubsurfaceColor: {r: 25.992073, g: 25.992073, b: 25.992073, a: 1}
  m_BuildTextureStacks: []
  m_AllowLocking: 1
--- !u!114 &824577688177918318
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0353a89b1f911e48b9e16bdc9f2e058, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 9
