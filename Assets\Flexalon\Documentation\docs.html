<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<link rel="icon" href="./images/favicon.svg" />
		<link rel="icon" href="./images/favicon.ico" />
		<meta name="viewport" content="width=device-width, height=device-height, initial-scale=1">
		<meta property="og:type" content="website">
		<meta http-equiv="content-security-policy" content="">
		<link href="./_app/immutable/assets/_layout.21aea584.css" rel="stylesheet">
		<link href="./_app/immutable/assets/_layout.0154a58a.css" rel="stylesheet">
		<link href="./_app/immutable/assets/_page.7eb0c3ee.css" rel="stylesheet">
		
		
		
		
		
		
		
		
		
		
		
		
		<title>Installation</title><!-- HEAD_svelte-i5aht6_START --><meta property="og:title" content="Installation"><meta name="description" property="og:description" content="Flexalon lets Unity developers arrange objects in 3D layouts that are precise and easy to adjust. Use built-in interactions and animations to create powerful experiences."><meta name="image" property="og:image" content="./images/flexalon-1200x630.png"><meta name="twitter:card" content="summary_large_image"><meta name="twitter:title" content="Installation"><meta name="twitter:site" content="@alon_farchy"><meta name="twitter:description" content="Flexalon lets Unity developers arrange objects in 3D layouts that are precise and easy to adjust. Use built-in interactions and animations to create powerful experiences."><meta name="twitter:image" content="./images/flexalon-1200x630.png"><!-- HEAD_svelte-i5aht6_END -->
	</head>
	<body>
		<div>


<div class="text-white font-body p-4 sticky top-0 bg-black flex gap-5 w-full text-gray-200 z-20 border-b-2 border-[#21a6f0]"><button class="lg:hidden"><svg width="24" height="24"><path d="M5 6h14M5 12h14M5 18h14" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"></path></svg></button>
    <a href="https://www.flexalon.com/" class="font-logo upper text-lg text-[#21a6f0] lg:pl-5 svelte-194nj6y">Flexalon Layouts</a></div>

<div class="w-full max-w-[1600px] lg:pl-10"><div class="flex w-full min-h-full text-zinc-400 relative"><div class="h-full fixed bg-black z-10 max-lg:hidden"><div class="flex flex-col w-[300px] gap-2 border-[#21a6f0] border-r-2 bg-black min-h-0 h-full pt-10 pb-36 overflow-auto max-lg:pl-10"><b class="text-zinc-200">Getting Started</b>
                <a href="./docs.html" class="svelte-194nj6y">Installation</a>
                <a href="./docs/coreConcepts.html" class="svelte-194nj6y">Core Concepts</a>
                <a href="./docs/ui.html" class="svelte-194nj6y">Flexalon UI <span class="ml-2 text-xs font-bold text-pink-300">(New in v4.0!)</span></a>
                <a href="./docs/templates.html" class="svelte-194nj6y">Template Pack</a>
                <a href="./docs/challenges.html" class="svelte-194nj6y">Challenges!</a>

                <a class="text-zinc-200 mt-5 font-bold svelte-194nj6y" href="./docs/flexalonObject.html">Flexalon Objects</a>
                <a href="./docs/flexalonObject.html#size" class="svelte-194nj6y">Width, Height, and Depth</a>
                <a href="./docs/flexalonObject.html#minmax" class="svelte-194nj6y">Min / Max Size<span class="ml-2 text-xs font-bold text-purple-400">(v4.1)</span></a>
                <a href="./docs/flexalonObject.html#margins" class="svelte-194nj6y">Margins and Padding</a>
                <a href="./docs/flexalonObject.html#offset" class="svelte-194nj6y">Offset, Scale, and Rotation</a>

                <b class="text-zinc-200 mt-5">Layouts</b>
                <a href="./docs/flexibleLayout.html" class="svelte-194nj6y">Flexible Layout</a>
                <a href="./docs/gridLayout.html" class="svelte-194nj6y">Grid Layout</a>
                <a href="./docs/circleLayout.html" class="svelte-194nj6y">Circle / Spiral Layout</a>
                <a href="./docs/curveLayout.html" class="svelte-194nj6y">Curve Layout</a>
                <a href="./docs/randomLayout.html" class="svelte-194nj6y">Random Layout</a>
                <a href="./docs/alignLayout.html" class="svelte-194nj6y">Align Layout</a>
                <a href="./docs/shapeLayout.html" class="svelte-194nj6y">Shape Layout</a>
                <a href="./docs/constraints.html" class="svelte-194nj6y">Constraints</a>
                <a href="./docs/randomModifier.html" class="svelte-194nj6y">Random Modifier</a>

                <a class="text-zinc-200 mt-5 font-bold svelte-194nj6y" href="./docs/animators.html">Animators</a>
                <a href="./docs/animators.html#curve" class="svelte-194nj6y">Curve Animator</a>
                <a href="./docs/animators.html#lerp" class="svelte-194nj6y">Lerp Animator</a>
                <a href="./docs/animators.html#rb" class="svelte-194nj6y">Rigid Body Animator</a>
                <a href="./docs/animators.html#layouts" class="svelte-194nj6y">Animating Layouts</a>

                <b class="text-zinc-200 mt-5">Click &amp; Drag</b>
                <a href="./docs/interactable.html" class="svelte-194nj6y">Interactable</a>
                <a href="./docs/interactable.html#dragTarget" class="svelte-194nj6y">Drag Target</a>
                <a href="./docs/xr.html" class="svelte-194nj6y">XR Interactions <span class="ml-2 text-xs font-bold text-blue-300">(v3.2)</span></a>
                <a href="./docs/interactable.html#customInputProvider" class="svelte-194nj6y">Custom Input Provider</a>

                <b class="text-zinc-200 mt-5">Data Binding</b>
                <a href="./docs/cloner.html" class="svelte-194nj6y">Cloner</a>
                <a href="./docs/cloner.html#databinding" class="svelte-194nj6y">Data Binding</a>

                <b class="text-zinc-200 mt-5">Advanced</b>
                <a href="./docs/pipeline.html" class="svelte-194nj6y">Flexalon Pipeline</a>
                <a href="./docs/customLayout.html" class="svelte-194nj6y">Custom Layouts</a>
                <a href="./docs/customAnimators.html" class="svelte-194nj6y">Custom Animators</a>
                <a href="./docs/adapters.html" class="svelte-194nj6y">Adapters</a>

                <a class="text-zinc-200 mt-5 font-bold svelte-194nj6y" href="./docs/api/Flexalon.html">Scripting API</a>
                <div class="mb-20"></div></div></div>
        
        <div class="flex flex-col w-full p-10 lg:ml-[300px]"><div class="doc svelte-194nj6y">

<div class="text-white text-2xl mb-10">Installation</div>

<p class="text-xl svelte-dv8srb">To install or upgrade Flexalon:</p>
<ul class="list-decimal"><li>If upgrading from an older version, delete the &quot;Flexalon&quot; directory in your project.</li>
    <li>Import the Flexalon package into in your Unity project. The &quot;Documentation&quot; and &quot;Samples&quot; directories are optional.</li>
    <li>Create an empty GameObject and add the &quot;Flexalon&quot; singleton component to it. Alternatively, this component will be automatically created when you use any Flexalon Component.</li></ul>
<p class="svelte-dv8srb">Well, that was easy! Head over to <a href="./docs/coreConcepts.html" class="svelte-dv8srb">Core Concepts</a> to learn what Flexalon can do!</p>
<p class="svelte-dv8srb">Download the new <a href="./docs/templates.html" class="svelte-dv8srb">Flexalon Template Pack</a> for complete examples you can copy into your scene.</p>
<p class="svelte-dv8srb">If you prefer to learn by doing, check out the <a href="./docs/coreConcepts.html" class="svelte-dv8srb">Challenges</a>.</p>
<p class="svelte-dv8srb">Or, if you prefer to watch and listen, here&#39;s a video tutorial:</p>
<div class="w-full flex justify-center"><div class="w-4/5 h-0 pb-[56.25%] relative"><iframe width="100%" height="100%" class="absolute" src="https://www.youtube-nocookie.com/embed/-8tuEbq4Wf4" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe></div>
</div></div></div></div>
</div>


			
			
		</div>
	</body>
</html>
