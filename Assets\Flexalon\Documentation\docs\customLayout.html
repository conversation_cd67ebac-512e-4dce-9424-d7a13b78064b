<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<link rel="icon" href="../images/favicon.svg" />
		<link rel="icon" href="../images/favicon.ico" />
		<meta name="viewport" content="width=device-width, height=device-height, initial-scale=1">
		<meta property="og:type" content="website">
		<meta http-equiv="content-security-policy" content="">
		<link href="../_app/immutable/assets/_layout.21aea584.css" rel="stylesheet">
		<link href="../_app/immutable/assets/_layout.0154a58a.css" rel="stylesheet">
		
		
		
		
		
		
		
		
		
		
		
		
		<title>Custom Layout</title><!-- HEAD_svelte-i5aht6_START --><meta property="og:title" content="Custom Layout"><meta name="description" property="og:description" content="Flexalon lets Unity developers arrange objects in 3D layouts that are precise and easy to adjust. Use built-in interactions and animations to create powerful experiences."><meta name="image" property="og:image" content="../images/flexalon-1200x630.png"><meta name="twitter:card" content="summary_large_image"><meta name="twitter:title" content="Custom Layout"><meta name="twitter:site" content="@alon_farchy"><meta name="twitter:description" content="Flexalon lets Unity developers arrange objects in 3D layouts that are precise and easy to adjust. Use built-in interactions and animations to create powerful experiences."><meta name="twitter:image" content="../images/flexalon-1200x630.png"><!-- HEAD_svelte-i5aht6_END -->
	</head>
	<body>
		<div>


<div class="text-white font-body p-4 sticky top-0 bg-black flex gap-5 w-full text-gray-200 z-20 border-b-2 border-[#21a6f0]"><button class="lg:hidden"><svg width="24" height="24"><path d="M5 6h14M5 12h14M5 18h14" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"></path></svg></button>
    <a href="https://www.flexalon.com/" class="font-logo upper text-lg text-[#21a6f0] lg:pl-5 svelte-194nj6y">Flexalon Layouts</a></div>

<div class="w-full max-w-[1600px] lg:pl-10"><div class="flex w-full min-h-full text-zinc-400 relative"><div class="h-full fixed bg-black z-10 max-lg:hidden"><div class="flex flex-col w-[300px] gap-2 border-[#21a6f0] border-r-2 bg-black min-h-0 h-full pt-10 pb-36 overflow-auto max-lg:pl-10"><b class="text-zinc-200">Getting Started</b>
                <a href="../docs.html" class="svelte-194nj6y">Installation</a>
                <a href="../docs/coreConcepts.html" class="svelte-194nj6y">Core Concepts</a>
                <a href="../docs/ui.html" class="svelte-194nj6y">Flexalon UI <span class="ml-2 text-xs font-bold text-pink-300">(New in v4.0!)</span></a>
                <a href="../docs/templates.html" class="svelte-194nj6y">Template Pack</a>
                <a href="../docs/challenges.html" class="svelte-194nj6y">Challenges!</a>

                <a class="text-zinc-200 mt-5 font-bold svelte-194nj6y" href="../docs/flexalonObject.html">Flexalon Objects</a>
                <a href="../docs/flexalonObject.html#size" class="svelte-194nj6y">Width, Height, and Depth</a>
                <a href="../docs/flexalonObject.html#minmax" class="svelte-194nj6y">Min / Max Size<span class="ml-2 text-xs font-bold text-purple-400">(v4.1)</span></a>
                <a href="../docs/flexalonObject.html#margins" class="svelte-194nj6y">Margins and Padding</a>
                <a href="../docs/flexalonObject.html#offset" class="svelte-194nj6y">Offset, Scale, and Rotation</a>

                <b class="text-zinc-200 mt-5">Layouts</b>
                <a href="../docs/flexibleLayout.html" class="svelte-194nj6y">Flexible Layout</a>
                <a href="../docs/gridLayout.html" class="svelte-194nj6y">Grid Layout</a>
                <a href="../docs/circleLayout.html" class="svelte-194nj6y">Circle / Spiral Layout</a>
                <a href="../docs/curveLayout.html" class="svelte-194nj6y">Curve Layout</a>
                <a href="../docs/randomLayout.html" class="svelte-194nj6y">Random Layout</a>
                <a href="../docs/alignLayout.html" class="svelte-194nj6y">Align Layout</a>
                <a href="../docs/shapeLayout.html" class="svelte-194nj6y">Shape Layout</a>
                <a href="../docs/constraints.html" class="svelte-194nj6y">Constraints</a>
                <a href="../docs/randomModifier.html" class="svelte-194nj6y">Random Modifier</a>

                <a class="text-zinc-200 mt-5 font-bold svelte-194nj6y" href="../docs/animators.html">Animators</a>
                <a href="../docs/animators.html#curve" class="svelte-194nj6y">Curve Animator</a>
                <a href="../docs/animators.html#lerp" class="svelte-194nj6y">Lerp Animator</a>
                <a href="../docs/animators.html#rb" class="svelte-194nj6y">Rigid Body Animator</a>
                <a href="../docs/animators.html#layouts" class="svelte-194nj6y">Animating Layouts</a>

                <b class="text-zinc-200 mt-5">Click &amp; Drag</b>
                <a href="../docs/interactable.html" class="svelte-194nj6y">Interactable</a>
                <a href="../docs/interactable.html#dragTarget" class="svelte-194nj6y">Drag Target</a>
                <a href="../docs/xr.html" class="svelte-194nj6y">XR Interactions <span class="ml-2 text-xs font-bold text-blue-300">(v3.2)</span></a>
                <a href="../docs/interactable.html#customInputProvider" class="svelte-194nj6y">Custom Input Provider</a>

                <b class="text-zinc-200 mt-5">Data Binding</b>
                <a href="../docs/cloner.html" class="svelte-194nj6y">Cloner</a>
                <a href="../docs/cloner.html#databinding" class="svelte-194nj6y">Data Binding</a>

                <b class="text-zinc-200 mt-5">Advanced</b>
                <a href="../docs/pipeline.html" class="svelte-194nj6y">Flexalon Pipeline</a>
                <a href="../docs/customLayout.html" class="svelte-194nj6y">Custom Layouts</a>
                <a href="../docs/customAnimators.html" class="svelte-194nj6y">Custom Animators</a>
                <a href="../docs/adapters.html" class="svelte-194nj6y">Adapters</a>

                <a class="text-zinc-200 mt-5 font-bold svelte-194nj6y" href="../docs/api/Flexalon.html">Scripting API</a>
                <div class="mb-20"></div></div></div>
        
        <div class="flex flex-col w-full p-10 lg:ml-[300px]"><div class="doc svelte-194nj6y">

<div class="text-white text-2xl mb-10">Custom Layout</div>

<p>You can create your own layouts by implementing Flexalon.Layout.
    To make this easier, we provide the FlexalonLayoutBase MonoBehaviour that you can extend.
    This component will handle child management and working correctly in edit mode.
</p>

<p>To implement a layout, you need to override two methods: <b>Measure</b> and <b>Arrange</b>.</p>

<p><b>HINT:</b> An example <b>CustomLayout</b> script can be found in the Samples/Scripts directory.</p>

<p class="text-xl mt-10">FlexalonNode</p>
<p>A FlexalonNode is automatically created for each gameObject managed by Flexalon.
    A node holds layout state which is shared between different components and stores the
    layout results. In implementing Measure and Arrange, you will need to become familiar with this class.
</p>

<p class="text-xl mt-10">Layout Space</p>
<p>In implementing your layout&#39;s <b>Measure</b> and <b>Arrange</b> methods,
    you are measuring and arranging your children in <b>Layout Space</b>.
    This is different from both Unity world space and local space.
</p>
<p>In Layout Space, you can assume that:</p>
<p class="ml-10">1. Your layout node&#39;s center is at (0, 0, 0).</p>
<p class="ml-10">2. Each child pivots at the center of its size.</p>
<p class="ml-10">3. All sizes are axis aligned.</p>
<p class="ml-10">4. You can also ignore margin and padding, as they are handled exernally.</p>

<p>Essentially, your task is to measure and arrange a set of simple boxes. </p>

<p class="text-xl mt-10">Measure</p>
<p style="font-family:'Courier New', Courier, monospace">Bounds <b>Measure</b>(FlexalonNode node, Vector3 size, Vector3 min, Vector3 max)
</p>
<table><tr><td>node</td>
        <td>The node representing the gameObject to be measured.</td></tr>
    <tr><td>size</td>
        <td>The size of the node, determined by the Flexalon Object Component.
            You need to determine the size of any axis set to SizeType.Layout.
        </td></tr>
    <tr><td>min</td>
        <td>The minimum size of the node, determined by the Flexalon Object Component.
            Ensure the returned bounds fit in min/max.
        </td></tr>
    <tr><td>max</td>
        <td>The maximum size of the node, determined by the Flexalon Object Component.
            Ensure the returned bounds fit in min/max.
        </td></tr>
    <tr><td>Return Value</td>
        <td>The bounding volume for this layout.
        </td></tr></table>
<p class="mt-10">In the Measure step, Flexalon needs you to determine two things: the fill size for children using SizeType.Fill and the total
    size of the layout. Access node.Children and set each child&#39;s fill/shrink size by calling child.SetShrinkFillSize. You
    can access each child&#39;s size by calling child.GetMeasureSize and its and size type by calling child.GetSizeType.
</p>
<p>Measure may be called multiple times with different sizes during a single layout update. This is because
    children using SizeType.Fill, may change their size based on the layout or adapter attached to them. For example,
    consider a text object with width set to SizeType.Fill and height set to SizeType.Component.
    Once the text gets a fill size, it may choose to wrap its text, which will change its height,
    which may change how you measure your layout.
</p>

<p class="text-xl mt-10">Arrange</p>
<p style="font-family:'Courier New', Courier, monospace">void <b>Arrange</b>(FlexalonNode node, Vector3 layoutSize)
</p>
<table><tr><td>node</td>
        <td>The node representing the gameObject whose children are to be arranged.</td></tr>
    <tr><td>layoutSize</td>
        <td>The size of this layout computed in the Measure step, possibly adjusted by an adapter.
        </td></tr></table>
<p class="mt-10">In the Arrange step, Flexalon is asking you to position and rotate each child. Access node.Children
    and set each child&#39;s position with child.SetPositionResult and rotation with child.SetRotationResult. You can access
    each child&#39;s size by calling child.GetArrangeSize. Arrange will only be called once in a layout update.
</p></div></div></div>
</div>


			
			
		</div>
	</body>
</html>
