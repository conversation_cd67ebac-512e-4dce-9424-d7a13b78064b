%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: GSF Implicit
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor v1.9.4.4\n// Available at the
    Unity Asset Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=19404\nNode;AmplifyShaderEditor.CommentaryNode;38;-2432,1920;Inherit;False;708.8215;357.6577;Math;4;214;62;337;338;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;267;-3513.461,1920;Inherit;False;1021.198;557.3833;Dot
    Products;9;304;270;328;238;271;273;272;342;343;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;338;-2384,2112;Inherit;False;192;132;NdotL;1;93;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;337;-2384,1968;Inherit;False;191;137;NdotV;1;81;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;232;-2432,1600;Inherit;False;711.8147;285.3875;GSF
    Implicit Node;1;43;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;43;-2112,1664;Inherit;False;342.896;152.4491;;;0,0,0,1;By
    multiplying the dot product of the normal and light by the dot product of the
    normal and our viewpoint we get an accurate portrayal of how light affects the
    surface of an object based on our view -Jorden Stevens;0;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;62;-2144,2016;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;93;-2368,2160;Inherit;False;238;NdotL;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;81;-2368,2016;Inherit;False;328;NdotV;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.StickyNoteNode;339;-2432,1408;Inherit;False;548.9943;153.3348;Credits;;0,0,0,0.5019608;Method
    by Jordan Stevens$Link: https://www.jordanstevenstechart.com/physically-based-rendering$$Node
    Made by Tiber Legacy$Special Thanks to Dawie;0;0\nNode;AmplifyShaderEditor.FunctionInput;272;-3200,2080;Inherit;False;NdotV;1;2;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;273;-3200,2304;Inherit;False;NdotL;1;1;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;238;-2784,2224;Inherit;True;NdotL;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;328;-2784,2000;Inherit;True;NdotV;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;304;-3456,2000;Inherit;False;Normal;3;0;False;1;0;FLOAT3;0,0,1;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitch;270;-3040,2000;Inherit;False;Custom
    Dot Products;True;0;2;1;In 0;In 1;Object;-1;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;271;-3040,2224;Inherit;False;Option;False;0;2;1;In
    0;In 1;Instance;270;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionNode;342;-3264,2000;Inherit;False;NdotV;-1;;3;65be4ec269a534f4e8867af2f999aaa9;4,27,1,377,0,452,0,451,0;3;230;FLOAT3;0,0,1;False;443;FLOAT3;0,0,0;False;415;FLOAT;1E-37;False;1;FLOAT;23\nNode;AmplifyShaderEditor.FunctionNode;343;-3264,2224;Inherit;False;NdotL;-1;;4;6d7ee1aa8c79587448475fa2b37affc1;5,27,1,381,0,460,0,461,0,463,0;4;230;FLOAT3;0,0,1;False;462;FLOAT3;0,0,0;False;454;FLOAT3;0,0,0;False;416;FLOAT;1E-37;False;1;FLOAT;111\nNode;AmplifyShaderEditor.FunctionOutput;214;-1968,2016;Inherit;True;True;-1;Out;0;False;1;0;FLOAT;0;False;1;FLOAT;0\nWireConnection;62;0;81;0\nWireConnection;62;1;93;0\nWireConnection;238;0;271;0\nWireConnection;328;0;270;0\nWireConnection;270;0;342;23\nWireConnection;270;1;272;0\nWireConnection;271;0;343;111\nWireConnection;271;1;273;0\nWireConnection;342;230;304;0\nWireConnection;343;230;304;0\nWireConnection;214;0;62;0\nASEEND*/\n//CHKSM=B7EB044B72520B31CF0F32309CF851DDBFB1F2FB"
  m_functionName: 
  m_description: 'GSF Implicit Node

'
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 0
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_customEdited: 0
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 0
  m_headerStyle: 2
  m_headerColor: {r: 0.7529412, g: 0.6784314, b: 0, a: 1}
  m_customNodeCategory: Amplify Shader Pack
  m_previewPosition: 0
  m_hidden: 0
  m_url: 
