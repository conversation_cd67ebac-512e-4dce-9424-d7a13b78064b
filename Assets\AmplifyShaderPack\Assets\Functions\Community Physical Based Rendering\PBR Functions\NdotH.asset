%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: NdotH
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor v1.9.4.4\n// Available at the
    Unity Asset Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=19404\nNode;AmplifyShaderEditor.CommentaryNode;370;-256,-768;Inherit;False;1505.621;445.9705;NdotH;13;466;465;464;109;373;188;367;331;417;437;178;283;290;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;119;-256,-1232;Inherit;False;925.3423;421.7822;NdotH
    Node;3;438;35;442;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;138;-1184,-368;Inherit;False;197.7;371;Normalized;2;211;114;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;35;224,-1024;Inherit;False;418.6689;173.682;;;0,0,0,1;Common
    Dot Pruducts used in lighting calculations$$Naming Conventions:$'A-Vector' dot
    'B-Vector'$Example: dot(Light Dir, View Dir) = LdotV$$NdotH - dot( Normal, Half
    Vector )$;0;0\nNode;AmplifyShaderEditor.GetLocalVarNode;290;-160,-624;Inherit;False;288;Half
    Vector;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;283;-192,-704;Inherit;False;282;Normal
    Space;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.DotProductOpNode;178;64,-704;Inherit;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.StickyNoteNode;437;656,-512;Inherit;False;449.235;154.571;Light
    Wrapping;;0,0,0,0.5019608;Allows the side of the Obj that is in 'Shadow'/darkness
    to still be seen or while in a low light environment$$for more details see this
    Link:$https://www.jordanstevenstechart.com/lighting-models;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;438;224,-1168;Inherit;False;414.9685;134.1714;;;0,0,0,1;Common
    Normal Calculations typically used in PBR Shaders, expand as necessary or if
    needed. Source: \"Physically Based Rendering Algorithms:$A Comprehensive Study
    In Unity3D\" By Jordan Steve;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;442;-240,-928;Inherit;False;208;100;Inputs;;1,0,0,1;all
    Inputs should be pre normalized prior to input;0;0\nNode;AmplifyShaderEditor.WorldNormalVector;114;-1168,-320;Inherit;False;True;1;0;FLOAT3;0,0,1;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.WorldNormalVector;211;-1168,-160;Inherit;False;True;1;0;FLOAT3;0,0,1;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.FunctionSwitch;27;-928,-320;Inherit;False;Normal
    Space;False;1;2;0;World;Tangent;Object;-1;10;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionInput;417;192,-480;Inherit;False;Light
    Wrapping;1;5;False;1;0;FLOAT;1E-37;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;230;-1360,0;Inherit;False;Normal;3;0;False;1;0;FLOAT3;0,0,1;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RangedFloatNode;331;224,-560;Inherit;False;Constant;_NearZero;Near
    Zero;0;0;Create;True;0;0;0;False;0;False;1E-37;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;367;224,-640;Inherit;False;Constant;_Zero;Zero;0;0;Create;True;0;0;0;False;0;False;0;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;288;-592,-720;Inherit;False;Half
    Vector;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionInput;453;-1040,-640;Inherit;False;Half
    Vector;3;4;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitch;463;-704,-32;Inherit;False;Custom
    Normal;True;0;2;3;In 0;In 1;Object;-1;10;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;282;-480,-32;Inherit;False;Normal
    Space;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitch;452;-848,-720;Inherit;False;Custom
    Half Vector;True;0;2;6;In 0;In 1;Object;-1;10;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMaxOpNode;188;912,-704;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;373;656,-640;Inherit;False;Light
    Wrapping;False;0;3;1;Zero;Near Zero;Custom;Object;-1;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSubtitle;464;416,-640;Inherit;False;Light
    Wrapping Zero;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSubtitle;465;384,-560;Inherit;False;Light
    Wrapping Near Zero;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSubtitle;466;384,-480;Inherit;False;Light
    Wrapping Custom;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.NormalizeNode;447;-1008,-720;Inherit;False;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;448;-1184,-720;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitch;451;-1472,-720;Inherit;False;Custom
    View Dir;True;0;2;2;In 0;In 1;Object;-1;10;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitch;460;-1472,-560;Inherit;False;Custom
    Light Dir;True;0;2;4;In 0;In 1;Object;-1;10;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionInput;443;-1664,-640;Inherit;False;View
    Dir;3;1;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitch;461;-1728,-400;Inherit;False;Fake
    Light Dir;True;0;2;5;In 0;In 1;Object;-1;10;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionInput;462;-1920,-400;Inherit;False;Light
    Dir;3;2;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.NormalizeNode;450;-1920,-320;Inherit;False;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;445;-2048,-320;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionInput;454;-2240,-320;Inherit;False;Fake
    Light Dir;3;3;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.ViewDirInputsCoordNode;446;-2240,-240;Float;False;World;False;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.ViewDirInputsCoordNode;444;-1872,-720;Inherit;False;World;True;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.WorldSpaceLightDirHlpNode;449;-1728,-560;Inherit;False;True;1;0;FLOAT;0;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.FunctionOutput;109;1056,-704;Inherit;True;True;-1;NdotH;0;True;1;0;FLOAT;0;False;1;FLOAT;0\nWireConnection;178;0;283;0\nWireConnection;178;1;290;0\nWireConnection;211;0;230;0\nWireConnection;27;0;114;0\nWireConnection;27;1;211;0\nWireConnection;288;0;452;0\nWireConnection;463;0;27;0\nWireConnection;463;1;230;0\nWireConnection;282;0;463;0\nWireConnection;452;0;447;0\nWireConnection;452;1;453;0\nWireConnection;188;0;178;0\nWireConnection;188;1;373;0\nWireConnection;373;0;464;0\nWireConnection;373;1;465;0\nWireConnection;373;2;466;0\nWireConnection;464;0;367;0\nWireConnection;465;0;331;0\nWireConnection;466;0;417;0\nWireConnection;447;0;448;0\nWireConnection;448;0;451;0\nWireConnection;448;1;460;0\nWireConnection;451;0;444;0\nWireConnection;451;1;443;0\nWireConnection;460;0;449;0\nWireConnection;460;1;461;0\nWireConnection;461;0;462;0\nWireConnection;461;1;450;0\nWireConnection;450;0;445;0\nWireConnection;445;0;454;0\nWireConnection;445;1;446;0\nWireConnection;109;0;188;0\nASEEND*/\n//CHKSM=1EB5C911CE5DF0134E75C06D9802189FD65FA85D"
  m_functionName: 
  m_description: 'NdotH Node

'
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 0
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_customEdited: 0
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 0
  m_headerStyle: 2
  m_headerColor: {r: 0.7529412, g: 0.6784314, b: 0, a: 1}
  m_customNodeCategory: Amplify Shader Pack
  m_previewPosition: 0
  m_hidden: 0
  m_url: 
