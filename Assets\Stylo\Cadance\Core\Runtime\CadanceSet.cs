using System;
using System.Collections.Generic;
using UnityEngine;

namespace Stylo.Cadance
{
    /// <summary>
    /// A collection of CadanceAsset files, equivalent to FMODKoreographySet.
    /// Provides organized management of multiple Cadance assets.
    /// </summary>
    [CreateAssetMenu(fileName = "CadanceSet", menuName = "Stylo/Cadance/Cadance Set")]
    public class CadanceSet : ScriptableObject
    {
        [SerializeField] private List<CadanceEntry> cadances = new List<CadanceEntry>();
        [SerializeField] private string description = "";

        /// <summary>
        /// Gets the list of Cadance entries in this set.
        /// </summary>
        public List<CadanceEntry> Cadances => cadances;

        /// <summary>
        /// Gets or sets the description of this Cadance set.
        /// </summary>
        public string Description
        {
            get => description;
            set => description = value;
        }

        /// <summary>
        /// Gets the number of Cadance assets in this set.
        /// </summary>
        public int Count => cadances.Count;

        /// <summary>
        /// Adds a CadanceAsset to this set.
        /// </summary>
        /// <param name="cadance">The CadanceAsset to add</param>
        /// <param name="utf8Name">Optional UTF8 name for compatibility</param>
        public void AddCadance(CadanceAsset cadance, string utf8Name = "")
        {
            if (cadance == null)
            {
                Debug.LogWarning("[CadanceSet] Cannot add null CadanceAsset");
                return;
            }

            // Check if already exists
            if (cadances.Exists(entry => entry.cadance == cadance))
            {
                Debug.LogWarning($"[CadanceSet] CadanceAsset '{cadance.name}' already exists in set");
                return;
            }

            var entry = new CadanceEntry
            {
                cadance = cadance,
                utf8Name = string.IsNullOrEmpty(utf8Name) ? cadance.name : utf8Name
            };

            cadances.Add(entry);
        }

        /// <summary>
        /// Adds a CadanceAsset to this set with FMOD integration.
        /// </summary>
        /// <param name="cadance">The CadanceAsset to add</param>
        /// <param name="fmodEvent">The FMOD event reference to associate</param>
        /// <param name="utf8Name">Optional UTF8 name for compatibility</param>
        public void AddCadance(CadanceAsset cadance, FMODUnity.EventReference fmodEvent, string utf8Name = "")
        {
            if (cadance == null)
            {
                Debug.LogWarning("[CadanceSet] Cannot add null CadanceAsset");
                return;
            }

            // Check if already exists
            if (cadances.Exists(entry => entry.cadance == cadance))
            {
                Debug.LogWarning($"[CadanceSet] CadanceAsset '{cadance.name}' already exists in set");
                return;
            }

            var entry = new CadanceEntry(cadance, fmodEvent, utf8Name);
            cadances.Add(entry);
        }

        /// <summary>
        /// Removes a CadanceAsset from this set.
        /// </summary>
        /// <param name="cadance">The CadanceAsset to remove</param>
        /// <returns>True if the asset was removed</returns>
        public bool RemoveCadance(CadanceAsset cadance)
        {
            if (cadance == null) return false;

            int index = cadances.FindIndex(entry => entry.cadance == cadance);
            if (index >= 0)
            {
                cadances.RemoveAt(index);
                return true;
            }

            return false;
        }

        /// <summary>
        /// Gets a CadanceAsset by name.
        /// </summary>
        /// <param name="name">The name to search for</param>
        /// <returns>The CadanceAsset if found, null otherwise</returns>
        public CadanceAsset GetCadanceByName(string name)
        {
            if (string.IsNullOrEmpty(name)) return null;

            var entry = cadances.Find(e => e.cadance != null &&
                (e.cadance.name == name || e.utf8Name == name));

            return entry?.cadance;
        }

        /// <summary>
        /// Gets a CadanceAsset by index.
        /// </summary>
        /// <param name="index">The index</param>
        /// <returns>The CadanceAsset if found, null otherwise</returns>
        public CadanceAsset GetCadanceAtIndex(int index)
        {
            if (index >= 0 && index < cadances.Count)
                return cadances[index].cadance;

            return null;
        }

        /// <summary>
        /// Checks if this set contains the specified CadanceAsset.
        /// </summary>
        /// <param name="cadance">The CadanceAsset to check for</param>
        /// <returns>True if the asset is in this set</returns>
        public bool ContainsCadance(CadanceAsset cadance)
        {
            if (cadance == null) return false;
            return cadances.Exists(entry => entry.cadance == cadance);
        }

        /// <summary>
        /// Clears all Cadance assets from this set.
        /// </summary>
        public void Clear()
        {
            cadances.Clear();
        }

        /// <summary>
        /// Loads all Cadance assets in this set into the Cadance system.
        /// </summary>
        public void LoadAll()
        {
            if (Cadance.Instance == null)
            {
                Debug.LogError("[CadanceSet] Cadance system not initialized");
                return;
            }

            int loadedCount = 0;
            foreach (var entry in cadances)
            {
                if (entry.cadance != null)
                {
                    Cadance.Instance.LoadCadance(entry.cadance);
                    loadedCount++;
                }
            }

            Debug.Log($"[CadanceSet] Loaded {loadedCount} Cadance assets from set '{name}'");
        }

        /// <summary>
        /// Unloads all Cadance assets in this set from the Cadance system.
        /// </summary>
        public void UnloadAll()
        {
            if (Cadance.Instance == null)
            {
                Debug.LogError("[CadanceSet] Cadance system not initialized");
                return;
            }

            int unloadedCount = 0;
            foreach (var entry in cadances)
            {
                if (entry.cadance != null)
                {
                    Cadance.Instance.UnloadCadance(entry.cadance);
                    unloadedCount++;
                }
            }

            Debug.Log($"[CadanceSet] Unloaded {unloadedCount} Cadance assets from set '{name}'");
        }

        /// <summary>
        /// Checks if all Cadance assets in this set are loaded.
        /// </summary>
        /// <returns>True if all assets are loaded</returns>
        public bool IsAllLoaded()
        {
            if (Cadance.Instance == null) return false;

            foreach (var entry in cadances)
            {
                if (entry.cadance != null && !Cadance.Instance.IsCadanceLoaded(entry.cadance))
                {
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// Gets all loaded Cadance assets from this set.
        /// </summary>
        /// <returns>List of loaded CadanceAsset objects</returns>
        public List<CadanceAsset> GetLoadedCadances()
        {
            var loadedCadances = new List<CadanceAsset>();

            if (Cadance.Instance != null)
            {
                foreach (var entry in cadances)
                {
                    if (entry.cadance != null && Cadance.Instance.IsCadanceLoaded(entry.cadance))
                    {
                        loadedCadances.Add(entry.cadance);
                    }
                }
            }

            return loadedCadances;
        }

        /// <summary>
        /// Validates all Cadance assets in this set.
        /// </summary>
        /// <returns>True if all assets are valid</returns>
        public bool ValidateAll()
        {
            bool allValid = true;

            for (int i = cadances.Count - 1; i >= 0; i--)
            {
                var entry = cadances[i];
                if (entry.cadance == null)
                {
                    Debug.LogWarning($"[CadanceSet] Removing null CadanceAsset entry at index {i}");
                    cadances.RemoveAt(i);
                    allValid = false;
                }
            }

            return allValid;
        }

        /// <summary>
        /// Creates a CadanceSet from an FMODKoreographySet for migration purposes.
        /// </summary>
        /// <param name="fmodSet">The FMODKoreographySet to convert</param>
        /// <returns>A new CadanceSet with equivalent data</returns>
        public static CadanceSet CreateFromFMODKoreographySet(SonicBloom.Koreo.Players.FMODStudio.FMODKoreographySet fmodSet)
        {
            if (fmodSet == null) return null;

            var cadanceSet = CreateInstance<CadanceSet>();
            cadanceSet.name = fmodSet.name + "_CadanceSet";
            cadanceSet.description = $"Converted from FMODKoreographySet: {fmodSet.name}";

            foreach (var koreoEntry in fmodSet.koreographies)
            {
                if (koreoEntry.koreo != null)
                {
                    var convertedCadance = Cadance.Instance?.ConvertKoreographyToCadance(koreoEntry.koreo);
                    if (convertedCadance != null)
                    {
                        // Since utf8Name is private, use the Koreography's SourceClipName instead
                        string utf8Name = koreoEntry.koreo.SourceClipName ?? koreoEntry.koreo.name;
                        cadanceSet.AddCadance(convertedCadance, utf8Name);
                    }
                }
            }

            return cadanceSet;
        }
    }

    /// <summary>
    /// Entry in a CadanceSet, equivalent to FMODKoreographySet.KoreographyEntry.
    /// Enhanced with FMOD integration and additional metadata.
    /// </summary>
    [System.Serializable]
    public class CadanceEntry
    {
        [SerializeField] public CadanceAsset cadance;
        [SerializeField] public string utf8Name;

        [Header("FMOD Integration")]
        [SerializeField] public FMODUnity.EventReference fmodEvent;
        [SerializeField] public bool enableFMODIntegration = false;

        [Header("Entry Configuration")]
        [SerializeField] public bool isEnabled = true;
        [SerializeField] public int priority = 0;
        [SerializeField] public float volume = 1.0f;
        [SerializeField] public string tags = "";
        [SerializeField] public string description = "";

        /// <summary>
        /// Gets the display name for this entry.
        /// </summary>
        public string DisplayName => !string.IsNullOrEmpty(utf8Name) ? utf8Name :
            (cadance != null ? cadance.name : "Unknown");

        /// <summary>
        /// Gets whether this entry has a valid FMOD event reference.
        /// </summary>
        public bool HasValidFMODEvent => enableFMODIntegration && !fmodEvent.IsNull;

        /// <summary>
        /// Gets whether this entry is valid and ready to use.
        /// </summary>
        public bool IsValid => cadance != null && isEnabled;

        /// <summary>
        /// Constructor for basic Cadance entry.
        /// </summary>
        public CadanceEntry()
        {
            isEnabled = true;
            priority = 0;
            volume = 1.0f;
            tags = "";
            description = "";
        }

        /// <summary>
        /// Constructor with CadanceAsset.
        /// </summary>
        public CadanceEntry(CadanceAsset cadanceAsset, string name = "")
        {
            cadance = cadanceAsset;
            utf8Name = string.IsNullOrEmpty(name) ? (cadanceAsset?.name ?? "") : name;
            isEnabled = true;
            priority = 0;
            volume = 1.0f;
            tags = "";
            description = "";
        }

        /// <summary>
        /// Constructor with FMOD integration.
        /// </summary>
        public CadanceEntry(CadanceAsset cadanceAsset, FMODUnity.EventReference eventRef, string name = "")
        {
            cadance = cadanceAsset;
            fmodEvent = eventRef;
            utf8Name = string.IsNullOrEmpty(name) ? (cadanceAsset?.name ?? "") : name;
            enableFMODIntegration = !eventRef.IsNull;
            isEnabled = true;
            priority = 0;
            volume = 1.0f;
            tags = "";
            description = "";
        }
    }
}
