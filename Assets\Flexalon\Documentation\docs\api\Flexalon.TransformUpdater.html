﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Interface TransformUpdater
 | Flexalon 3D Layouts </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Interface TransformUpdater
 | Flexalon 3D Layouts ">
    <meta name="generator" content="docfx ">
  
    <link rel="shortcut icon" href="images/favicon.svg">
    <link rel="stylesheet" href="styles/docfx.vendor.css">
    <link rel="stylesheet" href="styles/docfx.css">
    <link rel="stylesheet" href="styles/main.css">
    <meta property="docfx:navrel" content="tochead.html">
    <meta property="docfx:tocrel" content="toc.html">
  
  <meta property="docfx:rel" content="">
  
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="https://www.flexalon.com/">
                <img id="logo" class="svg" src="images/logo_32x32.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first="First" data-prev="Previous" data-next="Next" data-last="Last"></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">

        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="Flexalon.TransformUpdater">


  <h1 id="Flexalon_TransformUpdater" data-uid="Flexalon.TransformUpdater" class="text-break">Interface TransformUpdater
</h1>
  <div class="markdown level0 summary"><p>A transform updater determines how an object
gets from its current position to the computed layout position.</p>
</div>
  <div class="markdown level0 conceptual"></div>
  <h5 id="Flexalon_TransformUpdater_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public interface TransformUpdater</code></pre>
  </div>
  <h3 id="methods">Methods
</h3>
  <a id="Flexalon_TransformUpdater_PreUpdate_" data-uid="Flexalon.TransformUpdater.PreUpdate*"></a>
  <h4 id="Flexalon_TransformUpdater_PreUpdate_Flexalon_FlexalonNode_" data-uid="Flexalon.TransformUpdater.PreUpdate(Flexalon.FlexalonNode)">PreUpdate(FlexalonNode)</h4>
  <div class="markdown level1 summary"><p>Called before the layout system starts updating any transforms.
Use this to capture the transform position.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">void PreUpdate(FlexalonNode node)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.FlexalonNode.html">FlexalonNode</a></td>
        <td><span class="parametername">node</span></td>
        <td><p>The node being updated.</p>
</td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_TransformUpdater_UpdatePosition_" data-uid="Flexalon.TransformUpdater.UpdatePosition*"></a>
  <h4 id="Flexalon_TransformUpdater_UpdatePosition_Flexalon_FlexalonNode_UnityEngine_Vector3_" data-uid="Flexalon.TransformUpdater.UpdatePosition(Flexalon.FlexalonNode,UnityEngine.Vector3)">UpdatePosition(FlexalonNode, Vector3)</h4>
  <div class="markdown level1 summary"><p>Called to update the position of the object.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool UpdatePosition(FlexalonNode node, Vector3 position)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.FlexalonNode.html">FlexalonNode</a></td>
        <td><span class="parametername">node</span></td>
        <td><p>The node being updated.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><span class="parametername">position</span></td>
        <td><p>The computed local position of the object.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">bool</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_TransformUpdater_UpdateRectSize_" data-uid="Flexalon.TransformUpdater.UpdateRectSize*"></a>
  <h4 id="Flexalon_TransformUpdater_UpdateRectSize_Flexalon_FlexalonNode_UnityEngine_Vector2_" data-uid="Flexalon.TransformUpdater.UpdateRectSize(Flexalon.FlexalonNode,UnityEngine.Vector2)">UpdateRectSize(FlexalonNode, Vector2)</h4>
  <div class="markdown level1 summary"><p>Called to update the rect of the object.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool UpdateRectSize(FlexalonNode node, Vector2 rect)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.FlexalonNode.html">FlexalonNode</a></td>
        <td><span class="parametername">node</span></td>
        <td><p>The node being updated.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">UnityEngine.Vector2</span></td>
        <td><span class="parametername">rect</span></td>
        <td><p>The computed rect of the object.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">bool</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_TransformUpdater_UpdateRotation_" data-uid="Flexalon.TransformUpdater.UpdateRotation*"></a>
  <h4 id="Flexalon_TransformUpdater_UpdateRotation_Flexalon_FlexalonNode_UnityEngine_Quaternion_" data-uid="Flexalon.TransformUpdater.UpdateRotation(Flexalon.FlexalonNode,UnityEngine.Quaternion)">UpdateRotation(FlexalonNode, Quaternion)</h4>
  <div class="markdown level1 summary"><p>Called to update the rotation of the object.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool UpdateRotation(FlexalonNode node, Quaternion rotation)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.FlexalonNode.html">FlexalonNode</a></td>
        <td><span class="parametername">node</span></td>
        <td><p>The node being updated.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">UnityEngine.Quaternion</span></td>
        <td><span class="parametername">rotation</span></td>
        <td><p>The computed local rotation of the object.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">bool</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_TransformUpdater_UpdateScale_" data-uid="Flexalon.TransformUpdater.UpdateScale*"></a>
  <h4 id="Flexalon_TransformUpdater_UpdateScale_Flexalon_FlexalonNode_UnityEngine_Vector3_" data-uid="Flexalon.TransformUpdater.UpdateScale(Flexalon.FlexalonNode,UnityEngine.Vector3)">UpdateScale(FlexalonNode, Vector3)</h4>
  <div class="markdown level1 summary"><p>Called to update the scale of the object.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">bool UpdateScale(FlexalonNode node, Vector3 scale)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.FlexalonNode.html">FlexalonNode</a></td>
        <td><span class="parametername">node</span></td>
        <td><p>The node being updated.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><span class="parametername">scale</span></td>
        <td><p>The computed local scale of the object.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">bool</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In This Article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      Flexalon 3D Layouts
      
          </div>
        </div>
      </footer>
    </div>

    
    
    
  </body>
</html>
