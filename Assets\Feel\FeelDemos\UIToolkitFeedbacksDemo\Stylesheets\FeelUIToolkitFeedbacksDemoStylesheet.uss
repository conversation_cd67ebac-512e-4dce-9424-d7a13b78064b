.button {
    width: 160px;
    height: 60px;
    border-left-width: 0;
    border-right-width: 0;
    border-top-width: 0;
    border-bottom-width: 0;
    background-color: rgb(255, 196, 0);
    color: rgb(0, 0, 0);
    font-size: 14px;
    -unity-font-style: normal;
    -unity-font: url("../Fonts/UIToolkitDemo_Lato.ttf?fileID=12800000&guid=6f8c7831e01d1074488720a8bac8a393&type=3#UIToolkitDemo_Lato");
    margin-left: 10px;
    margin-right: 10px;
    margin-top: 10px;
    margin-bottom: 10px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    padding-left: 5px;
    padding-right: 5px;
    padding-top: 5px;
    padding-bottom: 5px;
    justify-content: flex-start;
    -unity-text-align: middle-center;
    white-space: normal;
    transition-duration: 0.2s;
    transition-timing-function: ease-out-cubic;
}

.button:hover {
    background-color: rgb(255, 255, 255);
    color: rgb(0, 0, 0);
}

.button:active {
    background-color: rgb(255, 196, 0);
    color: rgb(0, 0, 0);
}

.MainText {
    flex-basis: auto;
    justify-content: center;
    -unity-text-align: middle-center;
    -unity-font: url("../Fonts/UIToolkitDemo_Lato.ttf?fileID=12800000&guid=6f8c7831e01d1074488720a8bac8a393&type=3#UIToolkitDemo_Lato");
    -unity-font-style: normal;
    font-size: 16px;
    -unity-font-definition: initial;
    background-color: rgb(53, 53, 53);
    color: rgb(255, 196, 0);
    flex-wrap: nowrap;
    flex-grow: 0;
    flex-shrink: 0;
    overflow: visible;
    position: relative;
    flex-direction: column;
    align-items: center;
    white-space: normal;
    width: 50%;
    height: auto;
    max-height: none;
    padding-left: 20px;
    padding-right: 20px;
    padding-top: 20px;
    padding-bottom: 20px;
    border-left-color: rgb(255, 196, 0);
    border-right-color: rgb(255, 196, 0);
    border-top-color: rgb(255, 196, 0);
    border-bottom-color: rgb(255, 196, 0);
    border-left-width: 2px;
    border-right-width: 2px;
    border-top-width: 2px;
    border-bottom-width: 2px;
    border-top-left-radius: 20px;
    border-bottom-left-radius: 20px;
    border-top-right-radius: 20px;
    border-bottom-right-radius: 20px;
    margin-left: 20px;
    margin-right: 20px;
    margin-top: 20px;
    margin-bottom: 20px;
    transition-duration: 0.2s;
    transition-timing-function: ease-out-cubic;
}

.MainTextAlt {
    flex-basis: auto;
    justify-content: center;
    -unity-text-align: middle-center;
    -unity-font: url("../Fonts/UIToolkitDemo_Lato.ttf?fileID=12800000&guid=6f8c7831e01d1074488720a8bac8a393&type=3#UIToolkitDemo_Lato");
    -unity-font-style: normal;
    font-size: 14px;
    -unity-font-definition: initial;
    background-color: rgb(255, 196, 0);
    color: rgb(55, 55, 55);
    flex-wrap: nowrap;
    flex-grow: 0;
    flex-shrink: 0;
    overflow: visible;
    position: relative;
    flex-direction: column;
    align-items: center;
    white-space: normal;
    width: 50%;
    height: auto;
    max-height: none;
    padding-left: 20px;
    padding-right: 20px;
    padding-top: 20px;
    padding-bottom: 20px;
    border-left-color: rgb(255, 196, 0);
    border-right-color: rgb(255, 196, 0);
    border-top-color: rgb(255, 196, 0);
    border-bottom-color: rgb(255, 196, 0);
    border-left-width: 2px;
    border-right-width: 2px;
    border-top-width: 2px;
    border-bottom-width: 2px;
    border-top-left-radius: 20px;
    border-bottom-left-radius: 20px;
    border-top-right-radius: 20px;
    border-bottom-right-radius: 20px;
    margin-left: 20px;
    margin-right: 20px;
    margin-top: 20px;
    margin-bottom: 20px;
}

.MainContainer {
    background-color: rgb(31, 31, 31);
    height: 883px;
}
