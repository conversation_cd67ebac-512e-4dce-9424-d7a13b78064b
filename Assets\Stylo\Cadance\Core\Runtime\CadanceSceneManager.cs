using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using Stylo.Cadance.FMOD;

namespace Stylo.Cadance
{
    /// <summary>
    /// Scene manager that coordinates Cadance components and ensures proper initialization sequence.
    /// </summary>
    public class CadanceSceneManager : MonoBehaviour
    {
        [Header("Configuration")]
        [SerializeField] private bool autoInitialize = true;
        [SerializeField] private bool persistAcrossSceneLoads = true;

        [Header("Components")]
        [SerializeField] private FMODCadanceManager fmodManager;

        [Header("Debug")]
        [SerializeField] private bool verboseLogging = false;

        private static CadanceSceneManager _instance;

        /// <summary>
        /// Singleton instance of the CadanceSceneManager.
        /// </summary>
        public static CadanceSceneManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    // Look for existing instance
                    _instance = FindFirstObjectByType<CadanceSceneManager>();

                    // Create new instance if none found
                    if (_instance == null)
                    {
                        GameObject go = new GameObject("CadanceSceneManager");
                        _instance = go.AddComponent<CadanceSceneManager>();
                    }
                }

                return _instance;
            }
        }

        private void Awake()
        {
            // Ensure singleton behavior
            if (_instance != null && _instance != this)
            {
                Destroy(gameObject);
                return;
            }

            _instance = this;

            // Configure persistence
            if (persistAcrossSceneLoads)
            {
                DontDestroyOnLoad(gameObject);
            }

            // Auto-initialize if specified
            if (autoInitialize)
            {
                InitializeCadance();
            }
        }

        private void OnDestroy()
        {
            if (_instance == this)
            {
                _instance = null;
            }
        }

        /// <summary>
        /// Initialize the Cadance system and all required components.
        /// </summary>
        public void InitializeCadance()
        {
            if (verboseLogging)
            {
                Debug.Log("[CadanceSceneManager] Initializing Cadance system");
            }

            // Ensure Cadance instance exists
            if (Cadance.Instance == null)
            {
                if (verboseLogging)
                {
                    Debug.Log("[CadanceSceneManager] Creating Cadance instance");
                }

                GameObject cadanceObj = new GameObject("Cadance");
                cadanceObj.AddComponent<Cadance>();

                // Make Cadance persistent if this manager is persistent
                if (persistAcrossSceneLoads)
                {
                    DontDestroyOnLoad(cadanceObj);
                }
            }

            // Ensure FMOD Cadance Manager exists
            if (fmodManager == null)
            {
                fmodManager = GetComponent<FMODCadanceManager>();

                if (fmodManager == null)
                {
                    if (verboseLogging)
                    {
                        Debug.Log("[CadanceSceneManager] Adding FMODCadanceManager component");
                    }

                    fmodManager = gameObject.AddComponent<FMODCadanceManager>();
                }
            }

            // Apply runtime configuration settings if available
            ApplyRuntimeConfiguration();

            // Register all FMOD events in the scene if auto-detection is enabled
            if (CadanceRuntimeConfig.Instance.AutoDetectFMODEvents)
            {
                AutoDetectFMODEvents();
            }

            if (verboseLogging)
            {
                Debug.Log("[CadanceSceneManager] Cadance system initialization complete");
            }
        }

        /// <summary>
        /// Apply settings from the CadanceRuntimeConfig.
        /// </summary>
        private void ApplyRuntimeConfiguration()
        {
            var config = CadanceRuntimeConfig.Instance;

            // Apply logging settings
            verboseLogging = config.EnableDebugLogging;

            if (verboseLogging)
            {
                Debug.Log("[CadanceSceneManager] Applying runtime configuration settings");
            }
        }

        /// <summary>
        /// Automatically detect and register FMOD events in the scene.
        /// </summary>
        public void AutoDetectFMODEvents()
        {
            if (fmodManager == null)
            {
                Debug.LogWarning("[CadanceSceneManager] Cannot auto-detect FMOD events: FMODCadanceManager not found");
                return;
            }

            if (verboseLogging)
            {
                Debug.Log("[CadanceSceneManager] Auto-detecting FMOD events in scene");
            }

            // Find all FMOD CadancedEventEmitter components in the scene
            Stylo.Cadance.FMOD.CadancedEventEmitter[] emitters = FindObjectsByType<Stylo.Cadance.FMOD.CadancedEventEmitter>(FindObjectsSortMode.None);

            HashSet<string> registeredEvents = new HashSet<string>();

            foreach (var emitter in emitters)
            {
                // Check all event mappings in this emitter
                if (emitter.EventMappings == null || emitter.EventMappings.Count == 0)
                {
                    continue;
                }

                foreach (var mapping in emitter.EventMappings)
                {
                    string eventPath = mapping.FMODEventPath;

                    // Skip empty event paths or already registered events
                    if (string.IsNullOrEmpty(eventPath) || registeredEvents.Contains(eventPath))
                    {
                        continue;
                    }

                    // Register FMOD event if not already registered
                    if (!fmodManager.HasEventRegistered(eventPath))
                    {
                        string eventName = System.IO.Path.GetFileNameWithoutExtension(eventPath);
                        fmodManager.RegisterFMODEvent(eventName, eventPath);

                        registeredEvents.Add(eventPath);

                        if (verboseLogging)
                        {
                            UnityEngine.Debug.Log($"[Cadance] [CadanceSceneManager] Auto-registered FMOD event: {eventName} => {eventPath}");
                        }
                    }
                }
            }

            if (verboseLogging)
            {
                UnityEngine.Debug.Log($"[Cadance] [CadanceSceneManager] Auto-detected and registered {registeredEvents.Count} FMOD events");
            }
        }

        /// <summary>
        /// Play an FMOD event through the FMODCadanceManager.
        /// </summary>
        /// <param name="eventPath">Path to the FMOD event</param>
        /// <returns>True if successfully played, false otherwise</returns>
        public bool PlayFMODEvent(string eventPath)
        {
            if (fmodManager == null)
            {
                Debug.LogWarning("[CadanceSceneManager] Cannot play FMOD event: FMODCadanceManager not found");
                return false;
            }

            // Register the event if not already registered
            if (!fmodManager.HasEventRegistered(eventPath))
            {
                string eventName = System.IO.Path.GetFileNameWithoutExtension(eventPath);
                fmodManager.RegisterFMODEvent(eventName, eventPath);

                if (verboseLogging)
                {
                    Debug.Log($"[CadanceSceneManager] Auto-registered FMOD event before playing: {eventName} => {eventPath}");
                }
            }

            // Play the event
            return fmodManager.PlayEvent(eventPath);
        }

        /// <summary>
        /// Stop an FMOD event through the FMODCadanceManager.
        /// </summary>
        /// <param name="eventPath">Path to the FMOD event</param>
        /// <returns>True if successfully stopped, false otherwise</returns>
        public bool StopFMODEvent(string eventPath)
        {
            if (fmodManager == null)
            {
                Debug.LogWarning("[CadanceSceneManager] Cannot stop FMOD event: FMODCadanceManager not found");
                return false;
            }

            return fmodManager.StopEvent(eventPath);
        }

        /// <summary>
        /// Get the FMOD event instance tracker for a specific event path.
        /// </summary>
        /// <param name="eventPath">Path to the FMOD event</param>
        /// <returns>The event instance tracker, or null if not found</returns>
        public FMODEventInstanceTracker GetEventTracker(string eventPath)
        {
            if (fmodManager == null)
            {
                Debug.LogWarning("[CadanceSceneManager] Cannot get event tracker: FMODCadanceManager not found");
                return null;
            }

            return fmodManager.GetEventTracker(eventPath);
        }
    }
}
