#if UNITY_EDITOR && URP_INSTALLED
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using UnityEditor;
using Stylo.BFI.Universal;

namespace Stylo.BFI.Editor
{
    public class BFIValidator : EditorWindow
    {
        private Vector2 scrollPosition;
        private bool autoRefresh = true;
        private float lastRefreshTime;
        private const float REFRESH_INTERVAL = 1f;

        [MenuItem("Stylo/BFI/Validation Tool")]
        public static void ShowWindow()
        {
            GetWindow<BFIValidator>("BFI Validator");
        }

        private void OnEnable()
        {
            lastRefreshTime = Time.realtimeSinceStartup;
        }

        private void OnGUI()
        {
            EditorGUILayout.LabelField("BFI System Validation", EditorStyles.boldLabel);
            EditorGUILayout.Space();

            // Auto-refresh toggle
            autoRefresh = EditorGUILayout.Toggle("Auto Refresh", autoRefresh);

            if (GUILayout.Button("Manual Refresh") || (autoRefresh && Time.realtimeSinceStartup - lastRefreshTime > REFRESH_INTERVAL))
            {
                Repaint();
                lastRefreshTime = Time.realtimeSinceStartup;
            }

            EditorGUILayout.Space();
            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);

            // Validate URP Setup
            ValidateURPSetup();
            EditorGUILayout.Space();

            // Validate BFI Components
            ValidateBFIComponents();
            EditorGUILayout.Space();

            // Validate Renderer Features
            ValidateRendererFeatures();
            EditorGUILayout.Space();

            // Validate Volume Setup
            ValidateVolumeSetup();
            EditorGUILayout.Space();

            // Performance Analysis
            PerformanceAnalysis();
            EditorGUILayout.Space();

            // Quick Actions
            QuickActions();

            EditorGUILayout.EndScrollView();
        }

        private void ValidateURPSetup()
        {
            EditorGUILayout.LabelField("URP Configuration", EditorStyles.boldLabel);

            var urpAsset = GraphicsSettings.currentRenderPipeline as UniversalRenderPipelineAsset;
            if (urpAsset == null)
            {
                EditorGUILayout.HelpBox("No URP asset found in Graphics Settings. BFI requires URP.", MessageType.Error);
                return;
            }

            DrawValidationItem("URP Asset Found", true, urpAsset.name);

            // Check Unity version
            bool isUnity6 = Application.unityVersion.StartsWith("6.");
            DrawValidationItem("Unity 6 Compatible", isUnity6, $"Unity {Application.unityVersion}");

            // Check render graph support
            bool renderGraphSupported = isUnity6;
            DrawValidationItem("Render Graph Support", renderGraphSupported, renderGraphSupported ? "Available" : "Requires Unity 6+");
        }

        private void ValidateBFIComponents()
        {
            EditorGUILayout.LabelField("BFI Components", EditorStyles.boldLabel);

            // Check BFI shader
            var bfiShader = Shader.Find("Hidden/Universal Render Pipeline/BFI");
            bool shaderExists = bfiShader != null;
            DrawValidationItem("BFI Shader", shaderExists, shaderExists ? "Found" : "Missing - Check shader compilation");

            // Check BFI scripts
            var bfiEffectType = System.Type.GetType("Stylo.BFI.Universal.BFIEffect");
            var bfiRendererType = System.Type.GetType("Stylo.BFI.Universal.BFIRendererFeature");

            DrawValidationItem("BFI Effect Script", bfiEffectType != null, bfiEffectType?.Name ?? "Missing");
            DrawValidationItem("BFI Renderer Feature Script", bfiRendererType != null, bfiRendererType?.Name ?? "Missing");

            // Check assembly definitions
            var assemblies = System.AppDomain.CurrentDomain.GetAssemblies();
            bool bfiAssemblyLoaded = assemblies.Any(a => a.GetName().Name == "Stylo.BFI.Universal.Runtime");
            DrawValidationItem("BFI Assembly Loaded", bfiAssemblyLoaded, bfiAssemblyLoaded ? "Loaded" : "Not found");
        }

        private void ValidateRendererFeatures()
        {
            EditorGUILayout.LabelField("Renderer Features", EditorStyles.boldLabel);

            var urpAsset = GraphicsSettings.currentRenderPipeline as UniversalRenderPipelineAsset;
            if (urpAsset == null) return;

            // Get renderer data using reflection
            var rendererDataListField = typeof(UniversalRenderPipelineAsset).GetField("m_RendererDataList",
                BindingFlags.NonPublic | BindingFlags.Instance);

            if (rendererDataListField == null)
            {
                EditorGUILayout.HelpBox("Could not access URP renderer data list.", MessageType.Warning);
                return;
            }

            var rendererDataArray = rendererDataListField.GetValue(urpAsset) as ScriptableRendererData[];
            if (rendererDataArray == null || rendererDataArray.Length == 0)
            {
                EditorGUILayout.HelpBox("No URP Renderer Data found.", MessageType.Warning);
                return;
            }

            var rendererData = rendererDataArray[0] as UniversalRendererData;
            if (rendererData == null)
            {
                EditorGUILayout.HelpBox("First renderer is not UniversalRendererData.", MessageType.Warning);
                return;
            }

            DrawValidationItem("Renderer Data Found", true, rendererData.name);

            // Check for BFI renderer feature
            bool bfiFeatureFound = false;
            bool bfiFeatureActive = false;

            foreach (var feature in rendererData.rendererFeatures)
            {
                if (feature != null && feature.GetType().Name == "BFIRendererFeature")
                {
                    bfiFeatureFound = true;
                    bfiFeatureActive = feature.isActive;
                    break;
                }
            }

            DrawValidationItem("BFI Renderer Feature Added", bfiFeatureFound, bfiFeatureFound ? "Found" : "Not added to renderer");
            if (bfiFeatureFound)
            {
                DrawValidationItem("BFI Feature Active", bfiFeatureActive, bfiFeatureActive ? "Active" : "Inactive");
            }

            // List all renderer features
            EditorGUILayout.LabelField("All Renderer Features:", EditorStyles.miniBoldLabel);
            foreach (var feature in rendererData.rendererFeatures)
            {
                if (feature != null)
                {
                    string status = feature.isActive ? "✓" : "✗";
                    EditorGUILayout.LabelField($"  {status} {feature.name} ({feature.GetType().Name})");
                }
            }
        }

        private void ValidateVolumeSetup()
        {
            EditorGUILayout.LabelField("Volume Configuration", EditorStyles.boldLabel);

            // Check for Volume Manager
            bool volumeManagerExists = VolumeManager.instance != null;
            DrawValidationItem("Volume Manager", volumeManagerExists, volumeManagerExists ? "Active" : "Not found");

            if (!volumeManagerExists) return;

            // Check for BFI effect in stack
            var stack = VolumeManager.instance.stack;
            var bfiEffect = stack?.GetComponent<BFIEffect>();
            bool bfiInStack = bfiEffect != null;

            DrawValidationItem("BFI Effect in Volume Stack", bfiInStack, bfiInStack ? "Found" : "No BFI Volume Profile active");

            if (bfiInStack)
            {
                DrawValidationItem("BFI Effect Active", bfiEffect.IsActive(), $"Mode: {bfiEffect.Mode.value}, Intensity: {bfiEffect.Intensity.value:F2}");
            }

            // Find Volume GameObjects in scene
            var volumes = FindObjectsOfType<Volume>();
            EditorGUILayout.LabelField($"Volume GameObjects in Scene: {volumes.Length}", EditorStyles.miniBoldLabel);

            foreach (var volume in volumes)
            {
                var profile = volume.profile;
                bool hasBFI = profile != null && profile.Has<BFIEffect>();
                string status = hasBFI ? "✓ BFI" : "✗ No BFI";
                EditorGUILayout.LabelField($"  {volume.name} ({status})");
            }
        }

        private void PerformanceAnalysis()
        {
            EditorGUILayout.LabelField("Performance Analysis", EditorStyles.boldLabel);

            // Display refresh rate info
            int refreshRate = Screen.currentResolution.refreshRate;
            DrawValidationItem("Display Refresh Rate", true, $"{refreshRate}Hz");

            // Recommend BFI mode based on refresh rate
            string recommendedMode = "Off";
            if (refreshRate >= 240) recommendedMode = "4x Frame Rate (240Hz)";
            else if (refreshRate >= 180) recommendedMode = "3x Frame Rate (180Hz)";
            else if (refreshRate >= 120) recommendedMode = "2x Frame Rate (120Hz)";
            else recommendedMode = "Limited benefit at 60Hz";

            EditorGUILayout.LabelField($"Recommended BFI Mode: {recommendedMode}");

            // Frame timing info
            if (Application.isPlaying)
            {
                float frameTime = Time.unscaledDeltaTime * 1000f;
                float fps = 1f / Time.unscaledDeltaTime;
                EditorGUILayout.LabelField($"Current FPS: {fps:F1} ({frameTime:F2}ms)");

                bool stableFrameTime = frameTime < 20f; // Under 20ms for 50+ FPS
                DrawValidationItem("Stable Frame Timing", stableFrameTime, stableFrameTime ? "Good" : "Unstable - may affect BFI");
            }
            else
            {
                EditorGUILayout.LabelField("Enter Play Mode for runtime performance data");
            }
        }

        private void QuickActions()
        {
            EditorGUILayout.LabelField("Quick Actions", EditorStyles.boldLabel);

            EditorGUILayout.BeginHorizontal();

            if (GUILayout.Button("Create Default Presets"))
            {
                BFIPresetManager.CreateDefaultPresets();
                EditorGUILayout.HelpBox("Default BFI presets created in Assets/Stylo/BFI/Presets/", MessageType.Info);
            }

            if (GUILayout.Button("Refresh Preset Cache"))
            {
                BFIPresetManager.RefreshPresetCache();
            }

            EditorGUILayout.EndHorizontal();

            EditorGUILayout.BeginHorizontal();

            if (GUILayout.Button("Open Integration Guide"))
            {
                var guidePath = "Assets/Stylo/BFI/Documentation/BFI_Integration_Guide.md";
                var guideAsset = AssetDatabase.LoadAssetAtPath<TextAsset>(guidePath);
                if (guideAsset != null)
                {
                    Selection.activeObject = guideAsset;
                    EditorGUIUtility.PingObject(guideAsset);
                }
            }

            if (GUILayout.Button("Open BFI Folder"))
            {
                EditorUtility.RevealInFinder("Assets/Stylo/BFI");
            }

            EditorGUILayout.EndHorizontal();
        }

        private void DrawValidationItem(string label, bool isValid, string details = "")
        {
            EditorGUILayout.BeginHorizontal();

            // Status icon
            string icon = isValid ? "✓" : "✗";
            var color = isValid ? Color.green : Color.red;

            var oldColor = GUI.color;
            GUI.color = color;
            EditorGUILayout.LabelField(icon, GUILayout.Width(20));
            GUI.color = oldColor;

            // Label and details
            EditorGUILayout.LabelField(label, GUILayout.Width(200));
            if (!string.IsNullOrEmpty(details))
            {
                EditorGUILayout.LabelField(details, EditorStyles.miniLabel);
            }

            EditorGUILayout.EndHorizontal();
        }
    }
}
#endif
