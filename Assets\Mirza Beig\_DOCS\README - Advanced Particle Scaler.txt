
- PARTICLE SCALER:

Tool:

- Window -> <PERSON> -> Particle Scaler

Easy-to-use tool for scaling individual particle-component game objects, or even
complex hierarchies of particles. 

Use either the quick-scale buttons or set a custom scale using the slider and hit apply.
You can also scale the particles in real-time by enabling real-time scaling and dragging
the scale slider after that. Once clicked away from, real-time scaling is automatically
disabled for convenience.

Additionally, you can playback and preview the entire particle system(s) animation through the 
editor window. 

** MAKE SURE TO READ THE TOOLTIPS!

XX -- CHANGE LOG -- XX

v1.2: 

- Updated for Unity 5.5.

v1.1: 

- Updated for Unity 5.x.
- Backwards compatible to Unity 4.6.
- Bug fixes and additional features.

v1.0: 

- Initial release.
