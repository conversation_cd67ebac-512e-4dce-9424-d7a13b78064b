# Datamosh Visual Effect - Unity 6 Render Graph Implementation Guide

## Overview

This document provides step-by-step instructions for recreating a datamosh-style visual effect using Unity 6's Render Graph system with Custom Render Textures and blit render passes.

## Effect Description

- **Purpose**: Creates datamoshing effects with motion-based trailing and pixelated compression artifacts
- **Implementation**: Custom Render Texture + Fullscreen blit passes in Unity 6 Render Graph
- **Key Features**: Motion vector sampling, coordinate transformation, pixelated noise generation, color blending
- **Output**: Custom Render Texture that can be used in fullscreen post-processing

## Architecture Overview

This effect requires a **three-part system**:

1. **Custom Blit Renderer Features** - Captures color/motion data into global shader variables
2. **Custom Render Texture Shader Graph** - Processes the datamosh effect (the analyzed shader graph)
3. **Fullscreen Blit Pass** - Applies the Custom RT result as fullscreen post-processing

## Required Components

- Unity 6 (or Unity 2023.2.3f1+) with URP Render Graph
- Custom Render Texture support
- Fullscreen Shader Graph capability
- Custom blit renderer features for color/motion capture

## Critical Implementation Notes

- **Cannot use URP Sample Buffer** in Custom Render Texture graphs
- **Global shader variables** are used instead of direct texture properties
- **Non-trivial setup** requiring understanding of Unity's official blit examples
- **Render Graph blit passes** are essential for proper data flow

## Implementation Phases

### Phase 1: Render Graph Setup and Blit Passes

#### Step 1: Study Unity Official Examples
**CRITICAL**: Before implementation, review these Unity examples:
- "Perform a fullscreen blit in URP" 
- "Blit Camera color texture to RTHandle"
- Custom Render Texture documentation

#### Step 2: Create Custom Blit Renderer Features
1. **Color Capture Blit**:
   - Create renderer feature to capture camera color texture
   - Record into global shader variable (e.g., `_CameraColorTexture`)
   
2. **Motion Vector Capture Blit**:
   - Create renderer feature to capture motion vectors
   - Record into global shader variable (e.g., `_MotionVectorTexture`)

#### Step 3: Setup Custom Render Texture Asset
1. Right-click in Project → Create → Custom Render Texture
2. Name: "DatamoshRenderTexture"
3. Configure properties:
   - **Resolution**: Match screen resolution or desired effect resolution
   - **Format**: RGBA32 or appropriate format
   - **Update Mode**: Realtime or OnDemand based on needs

### Phase 2: Custom Render Texture Shader Graph

#### Step 4: Create Custom Render Texture Shader Graph
1. Right-click in Project → Create → Shader Graph → **Custom Render Texture**
2. Name: "DatamoshCustomRT"
3. **IMPORTANT**: This is NOT a standard material shader - it's specifically Custom Render Texture type

#### Step 5: Global Texture Access (No URP Sample Buffer)
**Note**: Cannot use "URP Sample Buffer" in Custom Render Texture graphs

1. **Access Color Texture**:
   - Add **Texture 2D** node (not Sample Texture 2D)
   - Reference global shader variable from blit pass
   - Use custom UV sampling

2. **Access Motion Vectors**:
   - Add **Texture 2D** node for motion vector global variable
   - Sample using UV coordinates

#### Step 6: Add Shader Graph Properties
1. **Effect Intensity**:
   - Add Float property
   - Name: "EffectIntensity"
   - Default: 1.0
   - Range: 0-2

2. **Pixelation Scale**:
   - Add Float property
   - Name: "PixelationScale"
   - Default: 100.0
   - Range: 10-500

3. **Blend Factor**:
   - Add Float property
   - Name: "BlendFactor"
   - Default: 0.5
   - Range: 0-1

### Phase 3: Motion Vector System (Custom RT Graph)

#### Step 7: Motion Vector Sampling
1. Add **Sample Texture 2D** node
2. Connect global motion vector texture (from blit pass)
3. Connect **UV** node to UV input
4. Label: "Motion Vector Sample"

#### Step 8: Motion Vector Processing
1. Add **Split** node, connect Motion Vector Sample output
2. Extract R and G channels (X and Y motion components)
3. Add **Multiply** nodes for each channel
4. Connect **EffectIntensity** property to multiply intensity

#### Step 9: Coordinate Transformation
1. Add **Add** node
2. Connect base **UV** to A input
3. Connect processed motion vectors to B input
4. Result: Motion-offset UV coordinates

### Phase 4: Color Processing Pipeline

#### Step 10: Base Color Sampling
1. Add **Sample Texture 2D** node
2. Connect global color texture (from blit pass)
3. Connect base **UV** to UV input
4. Label: "Base Color Sample"

#### Step 11: Motion-Offset Color Sampling
1. Add second **Sample Texture 2D** node
2. Connect global color texture (same as above)
3. Connect motion-offset UV coordinates to UV input
4. Label: "Motion Offset Sample"

#### Step 12: Color Blending
1. Add **Lerp** node
2. Connect Base Color Sample to A input
3. Connect Motion Offset Sample to B input
4. Connect **BlendFactor** property to T input

### Phase 5: Pixelated Noise System

#### Step 13: Noise Generation
1. Add **Simple Noise** node
2. Connect motion-offset UV to UV input
3. Add **Multiply** node to scale noise
4. Add **Float** property "NoiseScale" (default: 10)

#### Step 14: Pixelation Effect
1. Add **Multiply** node: UV × **PixelationScale**
2. Add **Floor** node to create stepped/pixelated coordinates
3. Add **Divide** node: Floor result ÷ **PixelationScale**
4. Result: Pixelated UV coordinates

#### Step 15: Pixelated Noise Blend
1. Use pixelated UV coordinates with **Simple Noise**
2. Add **Lerp** node to blend with color result
3. Connect noise as blend factor
4. Add intensity control with **Multiply** node

### Phase 6: Custom Render Texture Output

#### Step 16: Final Color Combination
1. Add final **Lerp** or **Add** node
2. Combine color blend result with pixelated noise
3. Add overall intensity control
4. **IMPORTANT**: Connect to Custom Render Texture **Color** output (not Fragment)

#### Step 17: Custom RT Output Connections
1. Connect final color result to Custom Render Texture **Color** output
2. Custom Render Texture graphs have different outputs than standard shaders
3. No Vertex processing needed for Custom RT

### Phase 7: Fullscreen Blit Integration

#### Step 18: Create Fullscreen Shader Graph
1. Create **Fullscreen Shader Graph** (separate from Custom RT)
2. Name: "DatamoshFullscreenBlit"
3. This will apply the Custom RT result as fullscreen effect

#### Step 19: Fullscreen Blit Setup
1. Add **Texture 2D** property for Custom Render Texture
2. Connect Custom RT as input texture
3. Add **Sample Texture 2D** with screen UV
4. Connect to **Base Color** output

#### Step 20: Render Graph Integration
1. Setup blit render pass in Render Graph
2. Use Custom Render Texture as source
3. Apply fullscreen blit to camera target
4. Configure execution order in URP settings

## Implementation Summary

### Required Assets:
1. **Custom Blit Renderer Features** (C# scripts)
2. **Custom Render Texture Asset** (.asset file)
3. **Custom Render Texture Shader Graph** (.shadergraph - the analyzed graph)
4. **Fullscreen Shader Graph** (.shadergraph - for final blit)

### Node Summary by Section (Custom RT Graph):

**Motion Vector Section (7 nodes):**
- Sample Texture 2D (Global Motion Vectors)
- Split (R/G channels)
- Multiply × 2 (intensity scaling)
- Add (UV offset)
- UV input node

**Color Processing Section (6 nodes):**
- Sample Texture 2D × 2 (global color texture, base + offset)
- Lerp (color blending)
- Float properties × 2 (blend factor, intensity)

**Pixelated Noise Section (8 nodes):**
- Simple Noise × 2 (regular + pixelated)
- Multiply × 3 (scaling operations)
- Floor (pixelation)
- Divide (coordinate normalization)
- Lerp (noise blending)

**Output Section (2 nodes):**
- Final combination node (Lerp/Add)
- Custom Render Texture Color output

## Testing and Validation

### Test Parameters:
- **EffectIntensity**: 0.5-1.5 for visible motion effects
- **PixelationScale**: 50-200 for noticeable pixelation
- **BlendFactor**: 0.3-0.7 for balanced blending
- **NoiseScale**: 5-20 for appropriate noise frequency

### Expected Results:
- Motion-based trailing/smearing effects
- Pixelated compression-like artifacts
- Dynamic response to camera/object movement
- Authentic datamosh visual appearance

## Troubleshooting

### Unity 6 Render Graph Specific Issues:
1. **Global variables not accessible**: Verify blit renderer features are properly recording data
2. **Custom RT not updating**: Check Custom Render Texture update mode and triggers
3. **Fullscreen blit not applying**: Verify Render Graph execution order
4. **Performance issues**: Optimize Custom RT resolution and update frequency

### Common Shader Graph Issues:
1. **No motion effects**: Check global motion vector variable names match blit pass
2. **Excessive pixelation**: Reduce PixelationScale value
3. **No trailing**: Increase EffectIntensity and verify motion vector data flow
4. **Black output**: Verify Custom RT Color output connections

### Optimization Tips:
- Use appropriate Custom RT resolution (don't always match screen resolution)
- Consider update frequency for Custom RT (OnDemand vs Realtime)
- Implement conditional compilation for mobile platforms
- Cache frequently used calculations in Custom RT

## Integration Notes
- **Unity Version**: Requires Unity 2023.2.3f1+ or Unity 6
- **URP Compatibility**: Designed for URP Render Graph systems
- **Motion Vector Source**: Works with Unity's built-in motion vector generation
- **Application**: Post-process effect via fullscreen blit, not material shader
- **Real-time**: Supports real-time parameter adjustment for dynamic effects
- **Performance**: Custom RT approach allows for optimized update patterns
