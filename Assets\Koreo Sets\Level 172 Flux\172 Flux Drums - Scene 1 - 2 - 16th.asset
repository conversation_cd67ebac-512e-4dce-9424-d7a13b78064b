%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -1223223985, guid: aa81bbec85a3f423193272a7c6154986, type: 3}
  m_Name: 172 Flux Drums - Scene 1 - 2 - 16th
  m_EditorClassIdentifier: 
  mEventID: 16th
  mEventList:
  - mStartSample: 0
    mEndSample: 0
  - mStartSample: 7691
    mEndSample: 7691
  - mStartSample: 15383
    mEndSample: 15383
  - mStartSample: 23074
    mEndSample: 23074
  - mStartSample: 30767
    mEndSample: 30767
  - mStartSample: 38458
    mEndSample: 38458
  - mStartSample: 46151
    mEndSample: 46151
  - mStartSample: 53842
    mEndSample: 53842
  - mStartSample: 61534
    mEndSample: 61534
  - mStartSample: 69225
    mEndSample: 69225
  - mStartSample: 76918
    mEndSample: 76918
  - mStartSample: 84609
    mEndSample: 84609
  - mStartSample: 92302
    mEndSample: 92302
  - mStartSample: 99993
    mEndSample: 99993
  - mStartSample: 107686
    mEndSample: 107686
  - mStartSample: 115377
    mEndSample: 115377
  - mStartSample: 123069
    mEndSample: 123069
  - mStartSample: 130760
    mEndSample: 130760
  - mStartSample: 138452
    mEndSample: 138452
  - mStartSample: 146143
    mEndSample: 146143
  - mStartSample: 153836
    mEndSample: 153836
  - mStartSample: 161527
    mEndSample: 161527
  - mStartSample: 169220
    mEndSample: 169220
  - mStartSample: 176911
    mEndSample: 176911
  - mStartSample: 184603
    mEndSample: 184603
  - mStartSample: 192294
    mEndSample: 192294
  - mStartSample: 199987
    mEndSample: 199987
  - mStartSample: 207678
    mEndSample: 207678
  - mStartSample: 215371
    mEndSample: 215371
  - mStartSample: 223062
    mEndSample: 223062
  - mStartSample: 230755
    mEndSample: 230755
  - mStartSample: 238446
    mEndSample: 238446
  - mStartSample: 246139
    mEndSample: 246139
  - mStartSample: 253830
    mEndSample: 253830
  - mStartSample: 261522
    mEndSample: 261522
  - mStartSample: 269213
    mEndSample: 269213
  - mStartSample: 276906
    mEndSample: 276906
  - mStartSample: 284597
    mEndSample: 284597
  - mStartSample: 292290
    mEndSample: 292290
  - mStartSample: 299981
    mEndSample: 299981
  - mStartSample: 307673
    mEndSample: 307673
  - mStartSample: 315364
    mEndSample: 315364
  - mStartSample: 323057
    mEndSample: 323057
  - mStartSample: 330748
    mEndSample: 330748
  - mStartSample: 338441
    mEndSample: 338441
  - mStartSample: 346132
    mEndSample: 346132
  - mStartSample: 353825
    mEndSample: 353825
  - mStartSample: 361516
    mEndSample: 361516
  - mStartSample: 369209
    mEndSample: 369209
  - mStartSample: 376900
    mEndSample: 376900
  - mStartSample: 384592
    mEndSample: 384592
  - mStartSample: 392283
    mEndSample: 392283
  - mStartSample: 399976
    mEndSample: 399976
  - mStartSample: 407667
    mEndSample: 407667
  - mStartSample: 415360
    mEndSample: 415360
  - mStartSample: 423051
    mEndSample: 423051
  - mStartSample: 430743
    mEndSample: 430743
  - mStartSample: 438434
    mEndSample: 438434
  - mStartSample: 446127
    mEndSample: 446127
  - mStartSample: 453818
    mEndSample: 453818
  - mStartSample: 461511
    mEndSample: 461511
  - mStartSample: 469202
    mEndSample: 469202
  - mStartSample: 476895
    mEndSample: 476895
  - mStartSample: 484586
    mEndSample: 484586
  _SerializedPayloadTypes: []
  _AssetPayloads: []
  _AssetPayloadIdxs: 
  _ColorPayloads: []
  _ColorPayloadIdxs: 
  _CurvePayloads: []
  _CurvePayloadIdxs: 
  _FloatPayloads: []
  _FloatPayloadIdxs: 
  _GradientPayloads: []
  _GradientPayloadIdxs: 
  _IntPayloads: []
  _IntPayloadIdxs: 
  _SpectrumPayloads: []
  _SpectrumPayloadIdxs: 
  _TextPayloads: []
  _TextPayloadIdxs: 
