%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-4508422724554390966
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8a6f998358657e440912b5b48d462e96, type: 3}
  m_Name: FluxEffect
  m_EditorClassIdentifier: 
  active: 1
  EffectIntensity:
    m_OverrideState: 1
    m_Value: 0.152
  OnlyStenciled:
    m_OverrideState: 1
    m_Value: 0
  ColorCrunch:
    m_OverrideState: 1
    m_Value: 0.524
  Downscaling:
    m_OverrideState: 1
    m_Value: 1
  BlockSize:
    m_OverrideState: 1
    m_Value: 1
  Oversharpening:
    m_OverrideState: 1
    m_Value: 0.576
  DontCrunchSkybox:
    m_OverrideState: 1
    m_Value: 0
  ReprojectBaseNoise:
    m_OverrideState: 1
    m_Value: 0.895
  ReprojectBaseRerollSpeed:
    m_OverrideState: 1
    m_Value: 15.14
  ReprojectLengthInfluence:
    m_OverrideState: 1
    m_Value: 4.33
  KeyframeResetRate:
    m_OverrideState: 0
    m_Value: 0.175
  MotionVectorCorruption:
    m_OverrideState: 0
    m_Value: 0.828
  ErrorAccumulation:
    m_OverrideState: 0
    m_Value: 0.8
  DCTCorruption:
    m_OverrideState: 0
    m_Value: 0
  CorruptionMask:
    m_OverrideState: 0
    m_Value: {fileID: 0}
  ChromaCorruption:
    m_OverrideState: 0
    m_Value: 0.245
  GlitchTransition:
    m_OverrideState: 0
    m_Value: 0.143
  FeedbackIntensity:
    m_OverrideState: 0
    m_Value: 0.152
  MultiScaleCorruption:
    m_OverrideState: 0
    m_Value: 0.325
  VisualizeMotionVectors:
    m_OverrideState: 0
    m_Value: 0
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d7fd9488000d3734a9e00ee676215985, type: 3}
  m_Name: DemoVolume_URP
  m_EditorClassIdentifier: 
  components:
  - {fileID: -4508422724554390966}
--- !u!114 &277022162085034876
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d468b943de483f641a8eb80fcd52e584, type: 3}
  m_Name: JPG
  m_EditorClassIdentifier: 
  active: 1
  EffectIntensity:
    m_OverrideState: 1
    m_Value: 0.359
  OnlyStenciled:
    m_OverrideState: 1
    m_Value: 0
  ColorCrunch:
    m_OverrideState: 1
    m_Value: 0.384
  Downscaling:
    m_OverrideState: 1
    m_Value: 2
  BlockSize:
    m_OverrideState: 1
    m_Value: 0
  Oversharpening:
    m_OverrideState: 1
    m_Value: 0
  DontCrunchSkybox:
    m_OverrideState: 1
    m_Value: 1
  ReprojectBaseNoise:
    m_OverrideState: 1
    m_Value: 1
  ReprojectBaseRerollSpeed:
    m_OverrideState: 1
    m_Value: 16.46
  ReprojectLengthInfluence:
    m_OverrideState: 1
    m_Value: 1.12
  VisualizeMotionVectors:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &1024402090557769300
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 97c23e3b12dc18c42a140437e53d3951, type: 3}
  m_Name: Tonemapping
  m_EditorClassIdentifier: 
  active: 0
  mode:
    m_OverrideState: 1
    m_Value: 2
  neutralHDRRangeReductionMode:
    m_OverrideState: 0
    m_Value: 2
  acesPreset:
    m_OverrideState: 0
    m_Value: 3
  hueShiftAmount:
    m_OverrideState: 0
    m_Value: 0
  detectPaperWhite:
    m_OverrideState: 0
    m_Value: 0
  paperWhite:
    m_OverrideState: 0
    m_Value: 300
  detectBrightnessLimits:
    m_OverrideState: 0
    m_Value: 1
  minNits:
    m_OverrideState: 0
    m_Value: 0.005
  maxNits:
    m_OverrideState: 0
    m_Value: 1000
--- !u!114 &5633232736217173614
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0b2db86121404754db890f4c8dfe81b2, type: 3}
  m_Name: Bloom
  m_EditorClassIdentifier: 
  active: 0
  skipIterations:
    m_OverrideState: 0
    m_Value: 1
  threshold:
    m_OverrideState: 1
    m_Value: 0.19
  intensity:
    m_OverrideState: 1
    m_Value: 1
  scatter:
    m_OverrideState: 0
    m_Value: 0.7
  clamp:
    m_OverrideState: 0
    m_Value: 65472
  tint:
    m_OverrideState: 0
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  highQualityFiltering:
    m_OverrideState: 0
    m_Value: 0
  downscale:
    m_OverrideState: 0
    m_Value: 0
  maxIterations:
    m_OverrideState: 0
    m_Value: 6
  dirtTexture:
    m_OverrideState: 0
    m_Value: {fileID: 0}
    dimension: 1
  dirtIntensity:
    m_OverrideState: 0
    m_Value: 0
--- !u!114 &7678543493164762238
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 899c54efeace73346a0a16faa3afe726, type: 3}
  m_Name: Vignette
  m_EditorClassIdentifier: 
  active: 0
  color:
    m_OverrideState: 0
    m_Value: {r: 0, g: 0, b: 0, a: 1}
  center:
    m_OverrideState: 0
    m_Value: {x: 0.5, y: 0.5}
  intensity:
    m_OverrideState: 1
    m_Value: 0.48
  smoothness:
    m_OverrideState: 0
    m_Value: 0.2
  rounded:
    m_OverrideState: 0
    m_Value: 0
