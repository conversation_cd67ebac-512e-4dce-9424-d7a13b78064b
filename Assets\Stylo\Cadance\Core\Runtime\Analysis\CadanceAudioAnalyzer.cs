using System;
using UnityEngine;

namespace Stylo.Cadance
{
    /// <summary>
    /// Real-time audio analysis system for Cadance.
    /// Provides FFT spectrum analysis and RMS energy analysis.
    /// </summary>
    [AddComponentMenu("Stylo/Cadance/Audio Analyzer")]
    public class CadanceAudioAnalyzer : MonoBehaviour
    {
        [Header("Analysis Configuration")]
        [SerializeField] private bool enableAnalysis = true;
        [SerializeField] private AudioSource targetAudioSource;
        [SerializeField] private int fftSize = 1024;
        [SerializeField] private FFTWindow windowFunction = FFTWindow.BlackmanHarris;
        [SerializeField] private float analysisInterval = 0.1f; // Analysis every 100ms

        [Header("RMS Analysis")]
        [SerializeField] private bool enableRMSAnalysis = true;
        [SerializeField] private int rmsWindowSize = 1024;

        [Header("Spectrum Analysis")]
        [SerializeField] private bool enableSpectrumAnalysis = true;
        [SerializeField] private int spectrumBins = 512;

        [Header("Event Generation")]
        [SerializeField] private bool generateAnalysisEvents = false;
        [SerializeField] private string rmsEventID = "RMS_Analysis";
        [SerializeField] private string spectrumEventID = "Spectrum_Analysis";
        [SerializeField] private float eventThreshold = 0.1f; // Minimum RMS to generate events

        // Analysis data
        private float[] spectrumData;
        private float[] audioSamples;
        private float lastAnalysisTime;
        private float currentRMS;
        private float currentPeak;
        private float currentAverage;

        // Events
        public event System.Action<SpectrumPayload> OnSpectrumAnalyzed;
        public event System.Action<RMSPayload> OnRMSAnalyzed;

        /// <summary>
        /// Gets the current RMS value.
        /// </summary>
        public float CurrentRMS => currentRMS;

        /// <summary>
        /// Gets the current peak value.
        /// </summary>
        public float CurrentPeak => currentPeak;

        /// <summary>
        /// Gets the current spectrum data.
        /// </summary>
        public float[] CurrentSpectrumData => spectrumData;

        /// <summary>
        /// Gets whether analysis is currently enabled.
        /// </summary>
        public bool IsAnalysisEnabled => enableAnalysis && targetAudioSource != null && targetAudioSource.isPlaying;

        private void Start()
        {
            InitializeAnalysis();
        }

        private void Update()
        {
            if (!IsAnalysisEnabled) return;

            // Check if it's time for analysis
            if (Time.time - lastAnalysisTime >= analysisInterval)
            {
                PerformAnalysis();
                lastAnalysisTime = Time.time;
            }
        }

        /// <summary>
        /// Initializes the audio analysis system.
        /// </summary>
        private void InitializeAnalysis()
        {
            if (targetAudioSource == null)
                targetAudioSource = GetComponent<AudioSource>();

            if (targetAudioSource == null)
            {
                Debug.LogWarning("[CadanceAudioAnalyzer] No AudioSource found. Analysis disabled.");
                enableAnalysis = false;
                return;
            }

            // Initialize arrays
            spectrumData = new float[spectrumBins];
            audioSamples = new float[rmsWindowSize];

            Debug.Log($"[CadanceAudioAnalyzer] Initialized with FFT size: {fftSize}, RMS window: {rmsWindowSize}");
        }

        /// <summary>
        /// Performs real-time audio analysis.
        /// </summary>
        private void PerformAnalysis()
        {
            if (enableSpectrumAnalysis)
            {
                AnalyzeSpectrum();
            }

            if (enableRMSAnalysis)
            {
                AnalyzeRMS();
            }

            // Generate events if enabled
            if (generateAnalysisEvents && currentRMS >= eventThreshold)
            {
                GenerateAnalysisEvents();
            }
        }

        /// <summary>
        /// Analyzes the frequency spectrum using FFT.
        /// </summary>
        private void AnalyzeSpectrum()
        {
            // Get spectrum data from AudioSource
            targetAudioSource.GetSpectrumData(spectrumData, 0, windowFunction);

            // Create spectrum payload
            var spectrumInfo = new SpectrumInfo(
                AudioSettings.outputSampleRate,
                fftSize,
                windowFunction
            );

            var spectrumPayload = new SpectrumPayload(spectrumData, spectrumInfo);

            // Trigger event
            OnSpectrumAnalyzed?.Invoke(spectrumPayload);
        }

        /// <summary>
        /// Analyzes RMS energy levels.
        /// </summary>
        private void AnalyzeRMS()
        {
            // Get audio samples
            targetAudioSource.GetOutputData(audioSamples, 0);

            // Calculate RMS, peak, and average
            float sum = 0f;
            float peak = 0f;

            for (int i = 0; i < audioSamples.Length; i++)
            {
                float sample = Mathf.Abs(audioSamples[i]);
                sum += sample * sample;

                if (sample > peak)
                    peak = sample;
            }

            currentRMS = Mathf.Sqrt(sum / audioSamples.Length);
            currentPeak = peak;
            currentAverage = sum / audioSamples.Length;

            // Create RMS payload
            var rmsInfo = new RMSInfo(
                AudioSettings.outputSampleRate,
                rmsWindowSize,
                0.5f, // window overlap
                RMSAnalysisMethod.Standard
            );

            var rmsPayload = new RMSPayload(currentRMS, currentPeak, rmsInfo);

            // Trigger event
            OnRMSAnalyzed?.Invoke(rmsPayload);
        }

        /// <summary>
        /// Generates Cadance events with analysis data.
        /// </summary>
        private void GenerateAnalysisEvents()
        {
            if (Cadance.Instance == null) return;

            int currentSample = Cadance.Instance.GetSampleTimeForClip(targetAudioSource.clip?.name);

            // Generate RMS event
            if (enableRMSAnalysis && !string.IsNullOrEmpty(rmsEventID))
            {
                var rmsInfo = new RMSInfo(AudioSettings.outputSampleRate, rmsWindowSize);
                var rmsPayload = new RMSPayload(currentRMS, currentPeak, rmsInfo);
                var rmsEvent = new CadanceEvent(rmsEventID, currentSample, rmsPayload);

                // Trigger the event directly
                Cadance.Instance.TriggerEvent(rmsEvent);
            }

            // Generate Spectrum event
            if (enableSpectrumAnalysis && !string.IsNullOrEmpty(spectrumEventID))
            {
                var spectrumInfo = new SpectrumInfo(AudioSettings.outputSampleRate, fftSize, windowFunction);
                var spectrumPayload = new SpectrumPayload(spectrumData, spectrumInfo);
                var spectrumEvent = new CadanceEvent(spectrumEventID, currentSample, spectrumPayload);

                // Trigger the event directly
                Cadance.Instance.TriggerEvent(spectrumEvent);
            }
        }

        /// <summary>
        /// Gets the current spectrum data with specified bin count.
        /// </summary>
        /// <param name="binCount">Number of bins to return</param>
        /// <returns>Spectrum data array</returns>
        public float[] GetSpectrumData(int binCount)
        {
            if (spectrumData == null) return new float[binCount];

            float[] result = new float[binCount];

            if (binCount == spectrumData.Length)
            {
                Array.Copy(spectrumData, result, binCount);
            }
            else
            {
                // Resample to requested bin count
                for (int i = 0; i < binCount; i++)
                {
                    float sourceIndex = (float)i * (spectrumData.Length - 1) / (binCount - 1);
                    int lowerIndex = Mathf.FloorToInt(sourceIndex);
                    int upperIndex = Mathf.Min(lowerIndex + 1, spectrumData.Length - 1);
                    float t = sourceIndex - lowerIndex;

                    result[i] = Mathf.Lerp(spectrumData[lowerIndex], spectrumData[upperIndex], t);
                }
            }

            return result;
        }

        /// <summary>
        /// Gets the magnitude in a specific frequency range.
        /// </summary>
        /// <param name="lowFreq">Low frequency in Hz</param>
        /// <param name="highFreq">High frequency in Hz</param>
        /// <returns>Average magnitude in the frequency range</returns>
        public float GetMagnitudeInRange(float lowFreq, float highFreq)
        {
            if (spectrumData == null || spectrumData.Length == 0) return 0f;

            float nyquist = AudioSettings.outputSampleRate * 0.5f;
            int lowBin = Mathf.FloorToInt((lowFreq / nyquist) * spectrumData.Length);
            int highBin = Mathf.CeilToInt((highFreq / nyquist) * spectrumData.Length);

            lowBin = Mathf.Clamp(lowBin, 0, spectrumData.Length - 1);
            highBin = Mathf.Clamp(highBin, 0, spectrumData.Length - 1);

            if (lowBin >= highBin) return spectrumData[lowBin];

            float sum = 0f;
            int count = 0;

            for (int i = lowBin; i <= highBin; i++)
            {
                sum += spectrumData[i];
                count++;
            }

            return count > 0 ? sum / count : 0f;
        }

        /// <summary>
        /// Sets the target AudioSource for analysis.
        /// </summary>
        /// <param name="audioSource">The AudioSource to analyze</param>
        public void SetTargetAudioSource(AudioSource audioSource)
        {
            targetAudioSource = audioSource;
            InitializeAnalysis();
        }

        /// <summary>
        /// Enables or disables analysis.
        /// </summary>
        /// <param name="enabled">Whether to enable analysis</param>
        public void SetAnalysisEnabled(bool enabled)
        {
            enableAnalysis = enabled;
        }
    }
}
