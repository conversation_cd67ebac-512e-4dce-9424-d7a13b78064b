using UnityEngine;
using UnityEditor;

namespace Stylo.Cadance.Editor
{
    /// <summary>
    /// Debug utility for testing and diagnosing spectrum analyzer issues.
    /// Provides tools to test both FMOD and Unity spectrum analysis.
    /// </summary>
    public class CadanceSpectrumAnalyzerDebugger : EditorWindow
    {
        private CadanceRealtimeSpectrumAnalyzer testAnalyzer;
        private bool isTestRunning = false;
        private string debugLog = "";
        private Vector2 scrollPosition;

        [MenuItem("Stylo/Cadance/Debug Spectrum Analyzer")]
        public static void ShowWindow()
        {
            GetWindow<CadanceSpectrumAnalyzerDebugger>("Spectrum Analyzer Debugger");
        }

        private void OnEnable()
        {
            testAnalyzer = new CadanceRealtimeSpectrumAnalyzer();
            debugLog = "Spectrum Analyzer Debugger initialized.\n";
        }

        private void OnGUI()
        {
            EditorGUILayout.LabelField("Spectrum Analyzer Debugger", EditorStyles.boldLabel);
            EditorGUILayout.Space();

            // Test controls
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("Test FMOD Spectrum"))
            {
                TestFMODSpectrum();
            }
            if (GUILayout.Button("Test Unity Spectrum"))
            {
                TestUnitySpectrum();
            }
            if (GUILayout.Button("Clear Log"))
            {
                debugLog = "";
            }
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.Space();

            // Status display
            if (testAnalyzer != null)
            {
                EditorGUILayout.LabelField("Current Status:", EditorStyles.boldLabel);
                EditorGUILayout.LabelField($"Analyzing: {testAnalyzer.IsAnalyzing()}");
                EditorGUILayout.LabelField($"Spectrum Source: {testAnalyzer.GetSpectrumSource()}");
                EditorGUILayout.LabelField($"FMOD System: {(FMODEditorAudioSystem.IsSpectrumAnalysisEnabled() ? "Available" : "Not Available")}");
                
                // Show current spectrum data
                var spectrum = testAnalyzer.GetCurrentSpectrum();
                if (spectrum != null)
                {
                    EditorGUILayout.LabelField($"Spectrum Data: {spectrum.Length} samples");
                    
                    // Show first few values
                    string sampleValues = "First 10 values: ";
                    for (int i = 0; i < Mathf.Min(10, spectrum.Length); i++)
                    {
                        sampleValues += $"{spectrum[i]:F4} ";
                    }
                    EditorGUILayout.LabelField(sampleValues, EditorStyles.miniLabel);
                }
                else
                {
                    EditorGUILayout.LabelField("Spectrum Data: None");
                }
            }

            EditorGUILayout.Space();

            // Debug log
            EditorGUILayout.LabelField("Debug Log:", EditorStyles.boldLabel);
            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition, GUILayout.Height(200));
            EditorGUILayout.TextArea(debugLog, GUILayout.ExpandHeight(true));
            EditorGUILayout.EndScrollView();

            // Auto-refresh
            if (isTestRunning)
            {
                Repaint();
            }
        }

        private void TestFMODSpectrum()
        {
            debugLog += "\n=== Testing FMOD Spectrum Analysis ===\n";
            
            // Check FMOD initialization
            bool fmodInitialized = FMODEditorAudioSystem.Initialize();
            debugLog += $"FMOD Initialized: {fmodInitialized}\n";
            
            if (!fmodInitialized)
            {
                debugLog += "ERROR: FMOD system not initialized. Cannot test FMOD spectrum.\n";
                return;
            }

            // Check if audio is playing
            bool isPlaying = FMODEditorAudioSystem.IsPlaying();
            debugLog += $"FMOD Audio Playing: {isPlaying}\n";
            
            if (!isPlaying)
            {
                debugLog += "WARNING: No FMOD audio playing. Start audio playback first.\n";
                debugLog += "Try opening Cadance Editor and playing an audio file.\n";
                return;
            }

            // Test spectrum analysis
            bool spectrumEnabled = FMODEditorAudioSystem.EnableSpectrumAnalysis(1024);
            debugLog += $"FMOD Spectrum Enabled: {spectrumEnabled}\n";
            
            if (spectrumEnabled)
            {
                // Test getting spectrum data
                var spectrum = FMODEditorAudioSystem.GetSpectrumData();
                if (spectrum != null)
                {
                    debugLog += $"SUCCESS: Got FMOD spectrum data with {spectrum.Length} samples\n";
                    
                    // Calculate some basic stats
                    float max = 0f, avg = 0f;
                    for (int i = 0; i < spectrum.Length; i++)
                    {
                        avg += spectrum[i];
                        if (spectrum[i] > max) max = spectrum[i];
                    }
                    avg /= spectrum.Length;
                    
                    debugLog += $"Spectrum Stats - Max: {max:F4}, Avg: {avg:F4}\n";
                }
                else
                {
                    debugLog += "ERROR: FMOD spectrum data is null\n";
                }
            }
            else
            {
                debugLog += "ERROR: Failed to enable FMOD spectrum analysis\n";
            }
        }

        private void TestUnitySpectrum()
        {
            debugLog += "\n=== Testing Unity Spectrum Analysis ===\n";
            
            // Find Unity AudioSources
            AudioSource[] audioSources = FindObjectsOfType<AudioSource>();
            debugLog += $"Found {audioSources.Length} AudioSource(s) in scene\n";
            
            AudioSource playingSource = null;
            foreach (var source in audioSources)
            {
                debugLog += $"AudioSource '{source.name}': Playing={source.isPlaying}, Clip={source.clip?.name ?? "None"}\n";
                if (source.isPlaying && source.clip != null)
                {
                    playingSource = source;
                }
            }
            
            if (playingSource == null)
            {
                debugLog += "WARNING: No playing AudioSource found. Unity spectrum analysis not available.\n";
                debugLog += "Try playing audio through Unity AudioSource (not FMOD).\n";
                return;
            }
            
            debugLog += $"Using AudioSource: {playingSource.name}\n";
            
            // Test getting spectrum data
            try
            {
                float[] spectrum = new float[512];
                playingSource.GetSpectrumData(spectrum, 0, FFTWindow.BlackmanHarris);
                
                debugLog += $"SUCCESS: Got Unity spectrum data with {spectrum.Length} samples\n";
                
                // Calculate some basic stats
                float max = 0f, avg = 0f;
                for (int i = 0; i < spectrum.Length; i++)
                {
                    avg += spectrum[i];
                    if (spectrum[i] > max) max = spectrum[i];
                }
                avg /= spectrum.Length;
                
                debugLog += $"Spectrum Stats - Max: {max:F4}, Avg: {avg:F4}\n";
                
                // Test the analyzer with this source
                if (testAnalyzer != null)
                {
                    debugLog += "Testing spectrum analyzer with Unity fallback...\n";
                    bool started = testAnalyzer.StartAnalysis();
                    debugLog += $"Analyzer started: {started}\n";
                    debugLog += $"Spectrum source: {testAnalyzer.GetSpectrumSource()}\n";
                    
                    isTestRunning = true;
                    EditorApplication.delayCall += () => {
                        isTestRunning = false;
                    };
                }
            }
            catch (System.Exception ex)
            {
                debugLog += $"ERROR: Failed to get Unity spectrum data: {ex.Message}\n";
            }
        }

        private void OnDisable()
        {
            if (testAnalyzer != null)
            {
                testAnalyzer.StopAnalysis();
            }
            isTestRunning = false;
        }
    }

    /// <summary>
    /// Menu items for quick spectrum analyzer testing.
    /// </summary>
    public static class SpectrumAnalyzerQuickTests
    {
        [MenuItem("Stylo/Cadance/Quick Test FMOD Spectrum")]
        public static void QuickTestFMOD()
        {
            bool enabled = FMODEditorAudioSystem.EnableSpectrumAnalysis(1024);
            if (enabled)
            {
                var spectrum = FMODEditorAudioSystem.GetSpectrumData();
                if (spectrum != null)
                {
                    Debug.Log($"[Quick Test] FMOD spectrum working: {spectrum.Length} samples");
                }
                else
                {
                    Debug.LogWarning("[Quick Test] FMOD spectrum enabled but no data");
                }
            }
            else
            {
                Debug.LogWarning("[Quick Test] FMOD spectrum failed to enable");
            }
        }

        [MenuItem("Stylo/Cadance/Quick Test Unity Spectrum")]
        public static void QuickTestUnity()
        {
            AudioSource[] sources = Object.FindObjectsOfType<AudioSource>();
            AudioSource playing = null;
            
            foreach (var source in sources)
            {
                if (source.isPlaying && source.clip != null)
                {
                    playing = source;
                    break;
                }
            }
            
            if (playing != null)
            {
                float[] spectrum = new float[512];
                playing.GetSpectrumData(spectrum, 0, FFTWindow.BlackmanHarris);
                Debug.Log($"[Quick Test] Unity spectrum working: {spectrum.Length} samples from '{playing.name}'");
            }
            else
            {
                Debug.LogWarning("[Quick Test] No playing Unity AudioSource found");
            }
        }
    }
}
