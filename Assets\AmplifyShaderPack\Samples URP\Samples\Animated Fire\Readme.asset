%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e78934177bebe0545ab566bb787b0282, type: 3}
  m_Name: Readme
  m_EditorClassIdentifier: 
  Icon: {fileID: 2800000, guid: 02303dad5cc1b4c4c9322602ab6d0192, type: 3}
  Title: Animated Fire
  Description:
    Heading: 
    Text: This sample generates an animated fire effect using both a fire texture
      and mask texture which delimites the burning area.
    LinkText: 
    Url: 
  PropertiesHeader:
    Heading: Properties
    Text: 
    LinkText: 
    Url: 
  Properties:
  - Heading: Fire Texture
    Text: Texture value ( along with tile and offset ) for emission fire values.
    LinkText: 
    Url: 
  - Heading: Burn Mask
    Text: Texture value ( along with tile and offset ) to control burnt areas.
    LinkText: 
    Url: 
  - Heading: Fire Intensity
    Text: Intensity value for fire effect.
    LinkText: 
    Url: 
  - Heading: Albedo
    Text: Texture value ( along with tile and offset ) for albedo color.
    LinkText: 
    Url: 
  - Heading: Normals
    Text: Texture value ( along with tile and offset ) for normals value
    LinkText: 
    Url: 
  - Heading: Specular
    Text: Texture value ( along with tile and offset ) for specular color.
    LinkText: 
    Url: 
  - Heading: Smoothness
    Text: Value to control overall smoothness.
    LinkText: 
    Url: 
  AdditionalProperties: []
  AdditionalScripts: []
  LoadedLayout: 0
  RPType: 3
