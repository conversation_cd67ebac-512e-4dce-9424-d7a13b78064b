
- PARTICLE FORCE FIELDS (v1.0.0)

DOCUMENTATION: http://www.mirzabeig.com/products/particle-force-fields/

Demo scene and scripts can be found under...

- "Mirza Beig/Scripting/Effects/Particle Force Fields/"

There are three main force fields included with PFF:

Attraction: Attract and repel particles.
- Component name: AttractionParticleForceField

Vortex: Apply a rotational tornado/cyclone-like force to particles.
- Component name: VortexParticleForceField

Turbulence: Add organic turbulent forces to particles using different noise types.
- Component name: TurbulenceParticleForceField

Add them like any other component by dragging the script onto a game object
or using the Add Component button.

You can also add them from the add component menu:

Components -> Effects -> Particle Force Fields

Hidden controls:

- Use the horizontal/vertical axis inputs to rotate the scene (arrow keys, WASD).
- Press "R" while running the demo scene to reset the camera rotation.

XX -- CHANGE LOG -- XX

v1.0.0

- Initial release.