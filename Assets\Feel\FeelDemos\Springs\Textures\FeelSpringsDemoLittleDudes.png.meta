fileFormatVersion: 2
guid: 6582f34ddd5f686408c019550cd94c9d
TextureImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 12
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 0
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 140
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: FeelSpringsDemoLittleDudes_0
      rect:
        serializedVersion: 2
        x: 0
        y: 1024
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ffdf501741c79f04d95051f92ffefed5
      internalID: 1978829596
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: FeelSpringsDemoLittleDudes_1
      rect:
        serializedVersion: 2
        x: 256
        y: 1024
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 53b4853715866d042bb204fc315a0c81
      internalID: -918889415
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: FeelSpringsDemoLittleDudes_2
      rect:
        serializedVersion: 2
        x: 512
        y: 1024
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 3d0846a21421b5e4883ad77bcc815729
      internalID: 575148594
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: FeelSpringsDemoLittleDudes_3
      rect:
        serializedVersion: 2
        x: 768
        y: 1024
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: bac60d4531694054c8f5c4883d49c7bd
      internalID: 184926547
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: FeelSpringsDemoLittleDudes_4
      rect:
        serializedVersion: 2
        x: 1024
        y: 1024
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 3ba42bfb76336b142b4d882242d611ae
      internalID: 1138137278
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: FeelSpringsDemoLittleDudes_5
      rect:
        serializedVersion: 2
        x: 0
        y: 768
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: fe9e995ef13a1d74a8d78db1fe6e5fad
      internalID: 124857761
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: FeelSpringsDemoLittleDudes_6
      rect:
        serializedVersion: 2
        x: 256
        y: 768
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 4f1370e36b388fa4b88aba1ae223e147
      internalID: 1178595843
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: FeelSpringsDemoLittleDudes_7
      rect:
        serializedVersion: 2
        x: 512
        y: 768
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: aeb50f3bdf34b934198cc00ec4c69805
      internalID: 1396070358
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: FeelSpringsDemoLittleDudes_8
      rect:
        serializedVersion: 2
        x: 768
        y: 768
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 41a2471c308276146ab3057ebdbf0005
      internalID: -1994478078
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: FeelSpringsDemoLittleDudes_9
      rect:
        serializedVersion: 2
        x: 1024
        y: 768
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 181f798466733e145827ea6af0aed0ed
      internalID: 672839533
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: FeelSpringsDemoLittleDudes_10
      rect:
        serializedVersion: 2
        x: 0
        y: 512
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e67fd2959a9b17648ad324b7c2246cd1
      internalID: 496806899
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: FeelSpringsDemoLittleDudes_11
      rect:
        serializedVersion: 2
        x: 256
        y: 512
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 3ef5449c8212f4b4bafd6930862ceb4a
      internalID: -2027504960
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: FeelSpringsDemoLittleDudes_12
      rect:
        serializedVersion: 2
        x: 512
        y: 512
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: df25e3e987894f045bec8370d7ec6335
      internalID: -1331788603
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: FeelSpringsDemoLittleDudes_13
      rect:
        serializedVersion: 2
        x: 768
        y: 512
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 034dbc0a9fca480448cd6cc2f8084377
      internalID: 1705615154
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: FeelSpringsDemoLittleDudes_14
      rect:
        serializedVersion: 2
        x: 1024
        y: 512
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 10d212c40741bc04d876872f4363d447
      internalID: 2128112624
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: FeelSpringsDemoLittleDudes_15
      rect:
        serializedVersion: 2
        x: 0
        y: 256
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9c1e0d1b117822149bbd765346058640
      internalID: 1463657965
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: FeelSpringsDemoLittleDudes_16
      rect:
        serializedVersion: 2
        x: 256
        y: 256
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2a4edc251809d6044b592f32b07ba25e
      internalID: -1565944252
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: FeelSpringsDemoLittleDudes_17
      rect:
        serializedVersion: 2
        x: 512
        y: 256
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f9bb30885fc6d9347844336de20cf76a
      internalID: 1633796411
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: FeelSpringsDemoLittleDudes_18
      rect:
        serializedVersion: 2
        x: 768
        y: 256
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a11c3411fa7d8d341b0f3d5bea4001e3
      internalID: -607652506
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: FeelSpringsDemoLittleDudes_19
      rect:
        serializedVersion: 2
        x: 1024
        y: 256
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1cd05c60750705a45adf0c6fecd3aa89
      internalID: -1595002427
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: 5b32bf1ed8eee4a47ae197f1b6c0f4d8
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      FeelSpringsDemoLittleDudes_0: 1978829596
      FeelSpringsDemoLittleDudes_1: -918889415
      FeelSpringsDemoLittleDudes_10: 496806899
      FeelSpringsDemoLittleDudes_11: -2027504960
      FeelSpringsDemoLittleDudes_12: -1331788603
      FeelSpringsDemoLittleDudes_13: 1705615154
      FeelSpringsDemoLittleDudes_14: 2128112624
      FeelSpringsDemoLittleDudes_15: 1463657965
      FeelSpringsDemoLittleDudes_16: -1565944252
      FeelSpringsDemoLittleDudes_17: 1633796411
      FeelSpringsDemoLittleDudes_18: -607652506
      FeelSpringsDemoLittleDudes_19: -1595002427
      FeelSpringsDemoLittleDudes_2: 575148594
      FeelSpringsDemoLittleDudes_3: 184926547
      FeelSpringsDemoLittleDudes_4: 1138137278
      FeelSpringsDemoLittleDudes_5: 124857761
      FeelSpringsDemoLittleDudes_6: 1178595843
      FeelSpringsDemoLittleDudes_7: 1396070358
      FeelSpringsDemoLittleDudes_8: -1994478078
      FeelSpringsDemoLittleDudes_9: 672839533
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
AssetOrigin:
  serializedVersion: 1
  productId: 183370
  packageName: Feel
  packageVersion: 5.5
  assetPath: Assets/Feel/FeelDemos/Springs/Textures/FeelSpringsDemoLittleDudes.png
  uploadId: 759320
