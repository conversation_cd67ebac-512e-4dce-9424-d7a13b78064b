%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: tilingEmissiveSimple_mat
  m_Shader: {fileID: 4800000, guid: 7b4df804bfdc6ec429d0ce9540fdfdf7, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords: []
  m_InvalidKeywords:
  - _CastShadowsEnabled
  - _DUPLICATEDETAILS_ON
  - _EMISSION
  - _FadingEnabled
  - _MAPCONTRIBUTION_NONE
  - _NORMALMAP
  - _PreferQuadsEnabled
  - _ReceiveAmbientLightingEnabled
  - _ReceiveDirectLightingEnabled
  - _ReceiveFogEnabled
  - _ReceiveShadowsEnabled
  - _SURFACE_TYPE_TRANSPARENT
  - _UVSCREENPROJECTION_UVPROJECTION
  m_LightmapFlags: 1
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses:
  - DepthOnly
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BaseMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 2800000, guid: 9d388a7b96dba1048b2896f78db8c750, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMap:
        m_Texture: {fileID: 2800000, guid: 8c38d2e72ca280f4783343ec9c4418f7, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _HeightMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _HeightMap2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetalnessMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetalnessMap2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalMap2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _RoughnessMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _RoughnessMap2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SubsurfaceThicknessMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AlphaClip: 0
    - _AlphaClipEnabled: 0
    - _AlphaCutoff: 0.5
    - _AlphaToMask: 0
    - _AmplitudeSpeed: 1
    - _Blend: 0
    - _BlendDst: 10
    - _BlendModePreserveSpecular: 1
    - _BlendSrc: 5
    - _BumpScale: 1
    - _CastShadowsEnabled: 1
    - _ClearCoatMask: 0
    - _ClearCoatSmoothness: 0
    - _ContrastDetailMap: 1
    - _Cull: 2
    - _Culling: 0
    - _Cutoff: 0.5
    - _DesaturateBackground: 0
    - _DetailAlbedoMapScale: 1
    - _DetailNormalMapScale: 1
    - _DetailScale: 1
    - _DstBlend: 10
    - _DstBlendAlpha: 10
    - _DuplicateDetails: 1
    - _EnableSpecularLight: 0
    - _EnvironmentReflections: 1
    - _FadingEnabled: 1
    - _FresnelBias: 0
    - _FresnelIntensity: 4
    - _FresnelPower: 4
    - _GlossMapScale: 1
    - _Glossiness: 0.347
    - _GlossyReflections: 1
    - _HasEmissionMap: 0
    - _HasEmissionMap2: 0
    - _HasHeightMap: 0
    - _HasHeightMap2: 0
    - _HasMetalnessMap: 0
    - _HasMetalnessMap2: 0
    - _HasNormalMap: 0
    - _HasNormalMap2: 0
    - _HasRoughnessMap: 0
    - _HasRoughnessMap2: 0
    - _HasSubsurfaceMap: 0
    - _HeightStrength: 0.02
    - _HeightStrength2: 0.02
    - _Invert: 0
    - _LightingMode: 1
    - _MapContribution: 0
    - _MaxValueAmplitude: 2
    - _Metallic: 0.736
    - _Metalness: 0
    - _Metalness2: 0
    - _MetalnessMapExposure: 0
    - _MetalnessMapExposure2: 0
    - _MinValueAmplitude: 1
    - _Mode: 0
    - _NormalStrength: 1
    - _NormalStrength2: 1
    - _OcclusionStrength: 1
    - _OcclusionStrength2: 1
    - _Opacity: 1
    - _Parallax: 0.02
    - _PreferQuadsEnabled: 1
    - _QueueOffset: 0
    - _ReceiveAmbientLightingEnabled: 1
    - _ReceiveDirectLightingEnabled: 1
    - _ReceiveFogEnabled: 1
    - _ReceiveShadows: 1
    - _ReceiveShadowsEnabled: 1
    - _Refraction: 1.1
    - _RotationAngle: 0
    - _RotationSpeed: 0
    - _Roughness: 0.5
    - _Roughness2: 0.5
    - _RoughnessMapExposure: 0
    - _RoughnessMapExposure2: 0
    - _SelfIllumination: 1
    - _ShadowOpacity: 0
    - _Smoothness: 0.245
    - _SmoothnessTextureChannel: 0
    - _SortPriority: 0
    - _SpecularHighlights: 1
    - _Specularity: 0.5
    - _Specularity2: 0.5
    - _SpreadDetailMap: 0
    - _SrcBlend: 5
    - _SrcBlendAlpha: 1
    - _SubsurfaceAmbient: 0.2
    - _SubsurfaceDistortion: 0.3
    - _SubsurfaceEnabled: 0
    - _SubsurfaceFalloff: 2
    - _SubsurfaceThickness: 0.8
    - _Surface: 1
    - _TexturesScale: 1
    - _TranslationSpeed: 0
    - _UVScreenProjection: 0
    - _UVSec: 0
    - _UseVertexColors: 0
    - _WireframeSize: 1
    - _WorkflowMode: 1
    - _XRayBias: 0.04
    - _XRayFresnelIntensity: 1
    - _XRayFresnelPower: 1
    - _ZTest: 4
    - _ZWrite: 0
    m_Colors:
    - _BaseColor: {r: 1, g: 1, b: 1, a: 0.4392157}
    - _BaseColor2: {r: 0.5, g: 0.5, b: 0.5, a: 1}
    - _Color: {r: 1, g: 0.07075471, b: 0.3076211, a: 0.4392157}
    - _Emission: {r: 0, g: 0, b: 0, a: 1}
    - _Emission2: {r: 0, g: 0, b: 0, a: 1}
    - _EmissionColor: {r: 0.26571107, g: 0.45637712, b: 1.7207952, a: 1}
    - _FadeMinMaxDistance: {r: 10, g: 20, b: 0, a: 0}
    - _FillColorBackground: {r: 0, g: 0, b: 0, a: 0}
    - _FresnelColor: {r: 0.6933962, g: 1, b: 0.98143524, a: 1}
    - _SpecColor: {r: 0.19999996, g: 0.19999996, b: 0.19999996, a: 1}
    - _SubsurfaceColor: {r: 1, g: 1, b: 1, a: 1}
    - _XRayColor: {r: 0.9433962, g: 0, b: 0.25469717, a: 0.6313726}
  m_BuildTextureStacks: []
--- !u!114 &6353853051910003056
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0353a89b1f911e48b9e16bdc9f2e058, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 7
