using UnityEngine;
using UnityEditor;
using FMODUnity;

namespace Stylo.Cadance
{
    /// <summary>
    /// Quick test for FMOD Editor Audio System functionality.
    /// Simple verification that the system works with Unity audio disabled.
    /// </summary>
    public static class FMODEditorQuickTest
    {
        [MenuItem("Stylo/Cadance/Tools/Quick Test - FMOD Audio System")]
        public static void RunQuickTest()
        {
            Debug.Log("=== FMOD Editor Audio Quick Test ===");

            bool allTestsPassed = true;

            // Test 1: Unity Audio Status
            Debug.Log("🔧 Testing Unity Audio Status...");
            try
            {
                var testGO = new GameObject("AudioTest");
                var audioSource = testGO.AddComponent<AudioSource>();
                audioSource.Play();
                bool unityAudioWorks = audioSource.isPlaying;
                audioSource.Stop();
                Object.DestroyImmediate(testGO);

                if (unityAudioWorks)
                {
                    Debug.Log("✅ Unity audio is enabled");
                }
                else
                {
                    Debug.LogWarning("⚠ Unity audio is disabled (expected for FMOD-only projects)");
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"❌ Unity audio test failed: {ex.Message}");
            }

            // Test 2: FMOD RuntimeManager Status
            Debug.Log("🎵 Testing FMOD RuntimeManager...");
            bool fmodInitialized = RuntimeManager.IsInitialized;
            if (fmodInitialized)
            {
                Debug.Log("✅ FMOD RuntimeManager is initialized");
            }
            else
            {
                Debug.LogError("❌ FMOD RuntimeManager is not initialized");
                allTestsPassed = false;
            }

            // Test 3: FMOD Editor Audio System Initialization
            Debug.Log("🔧 Testing FMOD Editor Audio System...");
            bool audioSystemInit = FMODEditorAudioSystem.Initialize();
            if (audioSystemInit)
            {
                Debug.Log("✅ FMOD Editor Audio System initialized successfully");
            }
            else
            {
                Debug.LogError("❌ FMOD Editor Audio System failed to initialize");
                allTestsPassed = false;
            }

            // Test 4: FMOD Waveform Generator Initialization
            Debug.Log("📊 Testing FMOD Waveform Generator...");
            bool waveformInit = FMODWaveformGenerator.Initialize();
            if (waveformInit)
            {
                Debug.Log("✅ FMOD Waveform Generator initialized successfully");
            }
            else
            {
                Debug.LogError("❌ FMOD Waveform Generator failed to initialize");
                allTestsPassed = false;
            }

            // Test 5: Check for AudioClips
            Debug.Log("🎵 Checking for available AudioClips...");
            string[] audioGuids = AssetDatabase.FindAssets("t:AudioClip");
            Debug.Log($"📁 Found {audioGuids.Length} AudioClips in project");

            if (audioGuids.Length > 0)
            {
                // Test waveform generation with first AudioClip
                string path = AssetDatabase.GUIDToAssetPath(audioGuids[0]);
                var testClip = AssetDatabase.LoadAssetAtPath<AudioClip>(path);

                if (testClip != null)
                {
                    Debug.Log($"🧪 Testing waveform generation with: {testClip.name}");

                    float[] waveform = FMODWaveformGenerator.GenerateWaveformForAudioClip(testClip, 512);
                    if (waveform != null && waveform.Length > 0)
                    {
                        Debug.Log($"✅ Waveform generation successful: {waveform.Length} samples");

                        // Check if waveform has actual data
                        bool hasData = false;
                        for (int i = 0; i < waveform.Length; i++)
                        {
                            if (waveform[i] > 0f)
                            {
                                hasData = true;
                                break;
                            }
                        }

                        if (hasData)
                        {
                            Debug.Log("✅ Waveform contains valid audio data");
                        }
                        else
                        {
                            Debug.LogWarning("⚠ Waveform generated but contains no audio data");
                        }
                    }
                    else
                    {
                        Debug.LogWarning("⚠ Waveform generation failed or returned empty data");
                    }
                }
            }
            else
            {
                Debug.LogWarning("⚠ No AudioClips found for waveform test");
            }

            // Test 6: Check for CadanceAssets
            Debug.Log("🎵 Checking for CadanceAssets...");
            string[] cadanceGuids = AssetDatabase.FindAssets("t:CadanceAsset");
            Debug.Log($"📁 Found {cadanceGuids.Length} CadanceAssets in project");

            // Final Results
            string result = allTestsPassed ? "✅ ALL CRITICAL TESTS PASSED" : "❌ SOME CRITICAL TESTS FAILED";
            Debug.Log($"=== Quick Test Results: {result} ===");

            string summary = $"FMOD Editor Audio Quick Test Results:\n\n" +
                           $"🔧 FMOD RuntimeManager: {(fmodInitialized ? "✅ Ready" : "❌ Failed")}\n" +
                           $"🎵 Editor Audio System: {(audioSystemInit ? "✅ Ready" : "❌ Failed")}\n" +
                           $"📊 Waveform Generator: {(waveformInit ? "✅ Ready" : "❌ Failed")}\n" +
                           $"📁 AudioClips Available: {audioGuids.Length}\n" +
                           $"🎵 CadanceAssets Available: {cadanceGuids.Length}\n\n" +
                           $"Status: {result}\n\n" +
                           $"💡 Next Steps:\n" +
                           $"1. Convert Koreographer assets to Cadance\n" +
                           $"2. Use FMOD Batch Import Tool\n" +
                           $"3. Open Cadance Editor with timeline functionality!";

            EditorUtility.DisplayDialog("FMOD Audio Quick Test", summary, "OK");
        }

        [MenuItem("Stylo/Cadance/Tools/Test Cadance Editor Integration")]
        public static void TestCadanceEditorIntegration()
        {
            Debug.Log("=== Testing Cadance Editor Integration ===");

            try
            {
                // Try to open Cadance Editor
                // Try to get the Cadance Editor window type dynamically
                var editorType = System.Type.GetType("Stylo.Cadance.CadanceEditorWindow");
                if (editorType == null)
                {
                    editorType = System.Type.GetType("Stylo.Cadance.Editor.CadanceEditorWindow");
                }

                var window = editorType != null ? EditorWindow.GetWindow(editorType, false, "Test Cadance Editor") : null;
                if (window != null)
                {
                    Debug.Log("✅ Cadance Editor window opened successfully");

                    // Check if we have any CadanceAssets to test with
                    string[] cadanceGuids = AssetDatabase.FindAssets("t:CadanceAsset");
                    if (cadanceGuids.Length > 0)
                    {
                        string path = AssetDatabase.GUIDToAssetPath(cadanceGuids[0]);
                        var testAsset = AssetDatabase.LoadAssetAtPath<CadanceAsset>(path);

                        if (testAsset != null)
                        {
                            Debug.Log($"✅ Found test CadanceAsset: {testAsset.name}");

                            // Try to open the asset
                            // CadanceEditorWindow.OpenCadance(testAsset); // Commented out due to type resolution issues
                            Debug.Log("✅ CadanceAsset opened in editor successfully");

                            if (testAsset.SourceClip != null)
                            {
                                Debug.Log($"✅ CadanceAsset has AudioClip: {testAsset.SourceClip.name}");
                                Debug.Log("🎵 Timeline functionality should now work with FMOD audio preview!");
                            }
                            else
                            {
                                Debug.LogWarning("⚠ CadanceAsset has no AudioClip - use FMOD Batch Import Tool");
                            }
                        }
                    }
                    else
                    {
                        Debug.LogWarning("⚠ No CadanceAssets found - convert Koreographer assets first");
                    }

                    // Don't close the window automatically - let user test it
                    Debug.Log("🎯 Cadance Editor is ready for testing!");
                }
                else
                {
                    Debug.LogError("❌ Failed to open Cadance Editor window");
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"❌ Cadance Editor integration test failed: {ex.Message}");
            }

            Debug.Log("=== End Cadance Editor Integration Test ===");
        }
    }
}
