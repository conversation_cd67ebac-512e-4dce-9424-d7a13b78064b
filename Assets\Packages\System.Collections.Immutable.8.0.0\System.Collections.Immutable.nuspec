﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2013/05/nuspec.xsd">
  <metadata>
    <id>System.Collections.Immutable</id>
    <version>8.0.0</version>
    <authors>Microsoft</authors>
    <license type="expression">MIT</license>
    <licenseUrl>https://licenses.nuget.org/MIT</licenseUrl>
    <icon>Icon.png</icon>
    <readme>PACKAGE.md</readme>
    <projectUrl>https://dot.net/</projectUrl>
    <description>This package provides collections that are thread safe and guaranteed to never change their contents, also known as immutable collections. Like strings, any methods that perform modifications will not change the existing instance but instead return a new instance. For efficiency reasons, the implementation uses a sharing mechanism to ensure that newly created instances share as much data as possible with the previous instance while ensuring that operations have a predictable time complexity.

The System.Collections.Immutable library is built-in as part of the shared framework in .NET Runtime. The package can be installed when you need to use it in other target frameworks.</description>
    <releaseNotes>https://go.microsoft.com/fwlink/?LinkID=799421</releaseNotes>
    <copyright>© Microsoft Corporation. All rights reserved.</copyright>
    <serviceable>true</serviceable>
    <repository type="git" url="https://github.com/dotnet/runtime" commit="5535e31a712343a63f5d7d796cd874e563e5ac14" />
    <dependencies>
      <group targetFramework=".NETFramework4.6.2">
        <dependency id="System.Memory" version="4.5.5" exclude="Build,Analyzers" />
        <dependency id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" exclude="Build,Analyzers" />
      </group>
      <group targetFramework="net6.0">
        <dependency id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" exclude="Build,Analyzers" />
      </group>
      <group targetFramework="net7.0" />
      <group targetFramework="net8.0" />
      <group targetFramework=".NETStandard2.0">
        <dependency id="System.Memory" version="4.5.5" exclude="Build,Analyzers" />
        <dependency id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
</package>