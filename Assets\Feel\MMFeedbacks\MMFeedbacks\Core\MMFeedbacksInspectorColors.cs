﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using MoreMountains.Feedbacks;

namespace MoreMountains.Feedbacks
{
	public class MMFeedbacksInspectorColors : MonoBehaviour
	{
		public static Color32 GameObjectColor = new Color32(76, 174, 80, 255);
		public static Color32 PostProcessColor = new Color32(254, 234, 59, 255);
		public static Color32 RendererColor = new Color32(254, 151, 0, 255);
		public static Color32 TransformColor = new Color32(134, 209, 243, 255);
		public static Color32 CameraColor = new Color32(237, 0, 0, 255);
		public static Color32 SoundsColor = new Color32(155, 39, 175, 255);
		public static Color32 EventsColor = new Color32(232, 30, 99, 255);
		public static Color32 SceneColor = new Color32(232, 30, 99, 255);
		public static Color32 TimeColor = new Color32(240, 172, 172, 255);
		public static Color32 LightColor = new Color32(254, 192, 7, 255);
		public static Color32 ParticlesColor = new Color32(0, 149, 135, 255);
		public static Color32 UIColor = new Color32(225, 2, 65, 255);
		public static Color32 TMPColor = new Color32(135, 206, 250, 255);
		public static Color32 HapticsColor = new Color32(61, 206, 250, 255);
		public static Color32 FeedbacksColor = new Color32(105, 32, 133, 255);
		public static Color32 AnimationColor = new Color32(200, 48, 128, 255);
		public static Color32 SpringColor = new Color32(221, 230, 128, 255);

		public static Color32 PauseColor = new Color32(98, 115, 0, 255);
		public static Color32 HoldingPauseColor = new Color32(0, 114, 61, 255);
		public static Color32 LooperColor = new Color32(12, 100, 128, 255);
		public static Color32 DebugColor = new Color32(255, 0, 0, 255);
		public static Color32 LooperStartColor = new Color32(166, 75, 5, 255); 
	}
}