%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -1223223985, guid: aa81bbec85a3f423193272a7c6154986, type: 3}
  m_Name: 172 Flux Drums - Scene 1 - 1 - 16th
  m_EditorClassIdentifier: 
  mEventID: 16th
  mEventList:
  - mStartSample: 0
    mEndSample: 0
  - mStartSample: 7691
    mEndSample: 7691
  - mStartSample: 15383
    mEndSample: 15383
  - mStartSample: 23075
    mEndSample: 23075
  - mStartSample: 30767
    mEndSample: 30767
  - mStartSample: 38459
    mEndSample: 38459
  - mStartSample: 46151
    mEndSample: 46151
  - mStartSample: 53843
    mEndSample: 53843
  - mStartSample: 61534
    mEndSample: 61534
  - mStartSample: 69225
    mEndSample: 69225
  - mStartSample: 76917
    mEndSample: 76917
  - mStartSample: 84609
    mEndSample: 84609
  - mStartSample: 92301
    mEndSample: 92301
  - mStartSample: 99993
    mEndSample: 99993
  - mStartSample: 107685
    mEndSample: 107685
  - mStartSample: 115377
    mEndSample: 115377
  - mStartSample: 123069
    mEndSample: 123069
  - mStartSample: 130760
    mEndSample: 130760
  - mStartSample: 138452
    mEndSample: 138452
  - mStartSample: 146144
    mEndSample: 146144
  - mStartSample: 153836
    mEndSample: 153836
  - mStartSample: 161528
    mEndSample: 161528
  - mStartSample: 169220
    mEndSample: 169220
  - mStartSample: 176912
    mEndSample: 176912
  - mStartSample: 184604
    mEndSample: 184604
  - mStartSample: 192295
    mEndSample: 192295
  - mStartSample: 199987
    mEndSample: 199987
  - mStartSample: 207679
    mEndSample: 207679
  - mStartSample: 215371
    mEndSample: 215371
  - mStartSample: 223063
    mEndSample: 223063
  - mStartSample: 230755
    mEndSample: 230755
  - mStartSample: 238447
    mEndSample: 238447
  _SerializedPayloadTypes: []
  _AssetPayloads: []
  _AssetPayloadIdxs: 
  _ColorPayloads: []
  _ColorPayloadIdxs: 
  _CurvePayloads: []
  _CurvePayloadIdxs: 
  _FloatPayloads: []
  _FloatPayloadIdxs: 
  _GradientPayloads: []
  _GradientPayloadIdxs: 
  _IntPayloads: []
  _IntPayloadIdxs: 
  _SpectrumPayloads: []
  _SpectrumPayloadIdxs: 
  _TextPayloads: []
  _TextPayloadIdxs: 
