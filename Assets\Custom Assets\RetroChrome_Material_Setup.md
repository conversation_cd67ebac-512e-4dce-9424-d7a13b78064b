# RetroChrome Material Setup Guide
*February 2024*

## Prerequisites
1. URP Project Setup:
   - Universal Render Pipeline installed
   - HDR enabled in URP Asset
   - Post-processing enabled in URP Asset
   - Forward rendering path

2. Required Assets:
   - RetroChrome.shader imported
   - Environment reflection probe set up
   - Post-processing volume in scene

## Material Creation Steps

### 1. Basic Setup
1. Create new material:
   - Right-click in Project window
   - Create → Material
   - Name it "RetroChrome_Mat"

2. Assign shader:
   - Select the material
   - In Inspector, click Shader dropdown
   - Select Custom → RetroChrome

### 2. Core Properties Configuration

#### Base Settings
```
Base Color: (0.8, 0.8, 0.8, 1.0)
Metallic: 1.0
Smoothness: 0.946
```

#### Edge and Reflection
```
Edge Sharpness: 10.0
Reflection Intensity: 1.5
Fresnel Power: 5.0
Fresnel Intensity: 2.0
```

#### Glow Settings
```
Glow Strength: 1.0
Glow Threshold: 0.5
Streak Intensity: 2.0
Emission Color: (0, 0.5, 1.0, 1.0) HDR
```

#### Effect Settings
```
Chromatic Aberration: 0.02
Noise Scale: 50.0
Noise Strength: 0.15
```

## Environment Setup

### 1. Lighting Setup
1. Scene lighting:
   - Create 2-3 area lights
   - Position as thin strips around object
   - Set Intensity: 2-3 (HDR)
   - Color Temperature: Cool (6500K-7500K)

2. Environment:
   - Set skybox to dark gradient
   - Ambient light intensity: Low (0.2-0.3)
   - Ambient source: Gradient
   - Ground color: Deep blue/black

### 2. Reflection Probe Setup
1. Add reflection probe:
   - GameObject → Light → Reflection Probe
   - Position to encompass chrome object
   - Type: Realtime
   - Resolution: 256x256 (minimum)
   - Box Projection: Enabled

2. Probe settings:
   ```
   Intensity Multiplier: 1.0
   Update Mode: On Awake
   Box Size: Adjust to fit scene
   ```

## Post-Processing Setup

### 1. Volume Profile
1. Create post-process volume:
   - Add Volume component
   - Create new profile
   - Set Is Global: true

### 2. Bloom Settings
```
Threshold: 1.0
Intensity: 1.2
Scatter: 0.7
Tint: Slight blue (Optional)
High Quality: Enabled
```

### 3. Additional Effects
1. Vignette (Optional):
   ```
   Intensity: 0.3
   Smoothness: 0.2
   Color: Deep blue
   ```

2. Color Grading:
   ```
   Temperature: -5 (cooler)
   Contrast: +10
   Post Exposure: 0.5
   ```

## Fine-Tuning Guide

### 1. Reflection Adjustments
If reflections are too strong:
1. Reduce `Reflection Intensity`
2. Lower `Fresnel Intensity`
3. Adjust reflection probe intensity

If reflections are too weak:
1. Increase `Edge Sharpness`
2. Raise `Metallic` to 1.0
3. Ensure HDR is enabled

### 2. Retro Effect Tuning
For stronger retro look:
1. Increase `Chromatic Aberration`
2. Raise `Noise Strength`
3. Adjust bloom intensity
4. Enhance `Streak Intensity`

For subtler retro look:
1. Reduce `Noise Strength`
2. Lower `Chromatic Aberration`
3. Decrease bloom threshold

### 3. Performance Optimization
If experiencing performance issues:
1. Lower reflection probe resolution
2. Reduce `Noise Scale`
3. Disable high-quality bloom
4. Adjust update frequency of reflection probe

## Common Issues

### 1. Material Appears Black
- Check environment lighting setup
- Verify HDR is enabled
- Ensure reflection probe is properly positioned
- Check `Base Color` isn't set too dark

### 2. Missing Reflections
- Verify reflection probe is set up
- Check URP settings for reflections
- Ensure `Metallic` is set to 1.0
- Verify environment lighting exists

### 3. Weak Retro Effect
- Increase post-processing bloom
- Adjust `Chromatic Aberration`
- Enhance `Noise Strength`
- Check `Emission Color` intensity

### 4. Flickering or Artifacts
- Adjust `Noise Scale`
- Check reflection probe settings
- Verify URP quality settings
- Adjust bloom threshold

## Final Notes
- Always test material under different lighting conditions
- Consider baking reflection probes for better performance
- Use HDR color picker for emission colors
- Monitor performance impact of post-processing stack 