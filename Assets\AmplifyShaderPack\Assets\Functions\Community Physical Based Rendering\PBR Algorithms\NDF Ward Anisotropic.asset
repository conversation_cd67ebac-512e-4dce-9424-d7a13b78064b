%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: NDF Ward Anisotropic
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor v1.9.5.1\n// Available at the
    Unity Asset Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=19501\nNode;AmplifyShaderEditor.CommentaryNode;261;-3072,-1408;Inherit;False;2724;1235;Math;52;38;55;111;116;117;118;122;124;126;86;87;103;104;92;93;110;89;257;77;79;81;82;83;258;106;94;95;259;260;0;43;46;47;49;44;48;101;102;99;39;78;80;40;98;45;50;52;51;54;53;277;276;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;127;-4191.19,-1408;Inherit;False;1061.316;1277.201;Dot
    Products;23;250;249;243;242;224;236;237;256;235;241;251;176;173;172;166;165;269;270;271;272;273;274;275;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;263;-3073,-1792;Inherit;False;814.6704;315.7311;NDF
    Ward Anisotropic Node;1;262;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;116;-2176,-1344;Inherit;False;204.1918;132.6709;NdotV;1;119;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;117;-2176,-1200;Inherit;False;202.2413;129.1683;NdotL;1;120;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;118;-1872,-992;Inherit;False;201.5715;133.0102;NdotH;1;121;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;122;-1984,-848;Inherit;False;201.5715;133.0102;HdotX;1;123;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;124;-1984,-704;Inherit;False;201.5715;133.0102;HdotY;1;125;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;27;-3072,-1984;Inherit;False;540;156.4001;Credits;;0,0,0,0.5019608;Method
    by Jordan Stevens$Link: https://www.jordanstevenstechart.com/physically-based-rendering$$Node
    Made by Tiber Legacy$Special Thanks to Dawie;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;38;-3056,-496;Inherit;False;255;128;Anisotropic
    Value Range;;0,0,0,0.5019608;in Jordan Stevens's article, he set his own Ansitropic
    Porperty to a Range of -20 to 1, so try this when creating yours!;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;111;-672,-944;Inherit;False;270.7786;123.9444;Weird
    Output;;0,0,0,0.5019608;even tho that the output looks incorrect, in scene and
    on a correctly UV-ed Object is used, it will look and function as normal & intended;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;126;-2784,-640;Inherit;False;194.8724;102.4255;Smoothness
    ;;0,0,0,1;;0;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;55;-1632,-944;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;121;-1840,-944;Inherit;False;251;NdotH;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;103;-1456,-1216;Inherit;False;101;X;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;104;-1456,-1152;Inherit;False;102;Y;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.ReciprocalOpNode;110;-944,-1296;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;89;-1232,-1296;Inherit;False;5;5;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;257;-1136,-896;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;81;-1392,-800;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.NegateNode;82;-1280,-800;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleDivideOpNode;83;-1072,-800;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;258;-1136,-784;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;106;-1152,-912;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;95;-752,-800;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;259;-800,-1216;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;260;-800,-800;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;43;-2208,-432;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;46;-2576,-528;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;47;-2400,-528;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SqrtOpNode;49;-2368,-432;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleDivideOpNode;44;-2208,-528;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;101;-1712,-528;Inherit;False;X;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;102;-1712,-432;Inherit;False;Y;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.ScaleNode;99;-1904,-432;Inherit;False;5;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.ScaleNode;39;-1904,-528;Inherit;False;5;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMaxOpNode;40;-2032,-528;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0.001;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMaxOpNode;98;-2032,-432;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0.001;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;45;-2240,-320;Inherit;False;Constant;_NonZero;Non
    Zero;0;0;Create;True;0;0;0;False;0;False;0.001;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;50;-2576,-432;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;52;-2736,-352;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;51;-2736,-432;Half;False;Constant;_1h;1h;0;0;Create;True;0;0;0;False;0;False;1;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;53;-2896,-272;Half;False;Constant;_09h;0.9h;0;0;Create;True;0;0;0;False;0;False;0.9;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;123;-1952,-800;Inherit;False;236;HdotX;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;125;-1952,-656;Inherit;False;237;HdotY;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.StickyNoteNode;262;-2720,-1744;Inherit;False;415.0906;154.3793;;;0,0,0,1;The
    Ward approach to Anisotropic BRDF produces drastically different results than
    the Trowbridge-Reitz method. The Specular highlight is much softer, and dissapates
    much faster as the surface proceeds in smoothness - Jordans Stevens;0;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;165;-3392,-1088;Inherit;True;NdotV;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;166;-3392,-864;Inherit;True;NdotL;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;251;-3392,-1312;Inherit;True;NdotH;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;241;-3680,-1312;Inherit;False;Custom
    Dot Products;True;0;2;1;In 0;In 1;Object;-1;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;235;-3840,-560;Inherit;False;HdotX;1;4;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;256;-3840,-320;Inherit;False;HdotY;1;5;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;237;-3392,-400;Inherit;True;HdotY;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;236;-3392,-640;Inherit;True;HdotX;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;242;-3680,-1088;Inherit;False;Option;False;0;2;1;In
    0;In 1;Instance;241;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;243;-3680,-864;Inherit;False;Option;False;0;2;1;In
    0;In 1;Instance;241;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;249;-3680,-640;Inherit;False;Option;False;0;2;1;In
    0;In 1;Instance;241;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;250;-3680,-400;Inherit;False;Option;False;0;2;1;In
    0;In 1;Instance;241;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;93;-1424,-1008;Inherit;False;Constant;_Four;Four;0;0;Create;True;0;0;0;False;0;False;4;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.PiNode;92;-1456,-1088;Inherit;False;1;0;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleDivideOpNode;78;-1728,-656;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleDivideOpNode;77;-1728,-800;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;79;-1568,-800;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;80;-1568,-656;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.ExpOpNode;94;-912,-800;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;176;-3840,-1200;Inherit;False;NdotH;1;2;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;224;-4144,-1312;Inherit;False;Normal;3;0;False;1;0;FLOAT3;0,0,1;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionInput;172;-3840,-992;Inherit;False;NdotV;1;3;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;173;-3840,-768;Inherit;False;NdotL;1;1;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;54;-2912,-352;Inherit;False;Anistropic;1;6;False;1;0;FLOAT;5;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;270;-3984,-880;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionInput;48;-2752,-528;Inherit;False;Smoothness
    ;1;7;False;1;0;FLOAT;0.5;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;269;-4128,-1072;Inherit;False;Constant;_Max;Max;0;0;Create;True;0;0;0;False;0;False;0.1;0.1;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionNode;271;-3904,-1312;Inherit;False;NdotH;-1;;9;fadb6690f179c1448859861f76ebb061;7,27,1,373,0,451,0,463,0,460,0,461,0,452,0;6;230;FLOAT3;0,0,1;False;443;FLOAT3;0,0,0;False;462;FLOAT3;0,0,0;False;454;FLOAT3;0,0,0;False;453;FLOAT3;0,0,0;False;417;FLOAT;1E-37;False;1;FLOAT;109\nNode;AmplifyShaderEditor.FunctionNode;272;-3936,-1088;Inherit;False;NdotV;-1;;10;65be4ec269a534f4e8867af2f999aaa9;4,27,1,377,2,452,0,451,0;3;230;FLOAT3;0,0,1;False;443;FLOAT3;0,0,0;False;415;FLOAT;1E-37;False;1;FLOAT;23\nNode;AmplifyShaderEditor.FunctionNode;273;-3904,-864;Inherit;False;NdotL;-1;;11;6d7ee1aa8c79587448475fa2b37affc1;5,27,1,381,0,460,0,461,0,463,0;4;230;FLOAT3;0,0,1;False;462;FLOAT3;0,0,0;False;454;FLOAT3;0,0,0;False;416;FLOAT;1E-37;False;1;FLOAT;111\nNode;AmplifyShaderEditor.FunctionNode;274;-3840,-640;Inherit;False;HdotX;-1;;12;e4a7c169e85b67f408600ac15b2fd149;5,443,0,444,0,121,0,445,0,446,0;5;440;FLOAT3;0,0,0;False;439;FLOAT3;0,0,0;False;130;FLOAT3;0,0,0;False;441;FLOAT3;0,0,0;False;447;FLOAT3;0,0,0;False;1;FLOAT;299\nNode;AmplifyShaderEditor.FunctionNode;275;-3840,-400;Inherit;False;HdotY;-1;;13;1c82aeabcbdbf7041b87a9a5449f37a1;5,453,0,462,0,463,0,454,0,465,0;5;445;FLOAT3;0,0,0;False;464;FLOAT3;0,0,0;False;456;FLOAT3;0,0,0;False;455;FLOAT3;0,0,0;False;466;FLOAT3;0,0,0;False;1;FLOAT;300\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;86;-1920,-1296;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;120;-2144,-1152;Inherit;False;166;NdotL;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SqrtOpNode;87;-1760,-1296;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.StickyNoteNode;276;-1696,-1200;Inherit;False;190.6133;102;Max
    .5;;0,0,0,0.5019608;This fixes the issue of the \"weird\" output and NaN Values;0;0\nNode;AmplifyShaderEditor.GetLocalVarNode;119;-2144,-1296;Inherit;False;165;NdotV;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMaxOpNode;277;-1600,-1296;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0.5;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;0;-592,-800;Inherit;True;True;-1;Out;0;True;1;0;FLOAT;0;False;1;FLOAT;0\nWireConnection;55;0;121;0\nWireConnection;55;1;121;0\nWireConnection;110;0;89;0\nWireConnection;89;0;277;0\nWireConnection;89;1;103;0\nWireConnection;89;2;104;0\nWireConnection;89;3;92;0\nWireConnection;89;4;93;0\nWireConnection;257;0;106;0\nWireConnection;81;0;79;0\nWireConnection;81;1;80;0\nWireConnection;82;0;81;0\nWireConnection;83;0;82;0\nWireConnection;83;1;258;0\nWireConnection;258;0;257;0\nWireConnection;106;0;55;0\nWireConnection;95;0;94;0\nWireConnection;95;1;260;0\nWireConnection;259;0;110;0\nWireConnection;260;0;259;0\nWireConnection;43;0;49;0\nWireConnection;43;1;47;0\nWireConnection;46;0;48;0\nWireConnection;47;0;46;0\nWireConnection;47;1;46;0\nWireConnection;49;0;50;0\nWireConnection;44;0;47;0\nWireConnection;44;1;49;0\nWireConnection;101;0;39;0\nWireConnection;102;0;99;0\nWireConnection;99;0;98;0\nWireConnection;39;0;40;0\nWireConnection;40;0;44;0\nWireConnection;40;1;45;0\nWireConnection;98;0;43;0\nWireConnection;98;1;45;0\nWireConnection;50;0;51;0\nWireConnection;50;1;52;0\nWireConnection;52;0;54;0\nWireConnection;52;1;53;0\nWireConnection;165;0;242;0\nWireConnection;166;0;243;0\nWireConnection;251;0;241;0\nWireConnection;241;0;271;109\nWireConnection;241;1;176;0\nWireConnection;237;0;250;0\nWireConnection;236;0;249;0\nWireConnection;242;0;272;23\nWireConnection;242;1;172;0\nWireConnection;243;0;273;111\nWireConnection;243;1;173;0\nWireConnection;249;0;274;299\nWireConnection;249;1;235;0\nWireConnection;250;0;275;300\nWireConnection;250;1;256;0\nWireConnection;78;0;125;0\nWireConnection;78;1;99;0\nWireConnection;77;0;123;0\nWireConnection;77;1;39;0\nWireConnection;79;0;77;0\nWireConnection;79;1;77;0\nWireConnection;80;0;78;0\nWireConnection;80;1;78;0\nWireConnection;94;0;83;0\nWireConnection;270;0;224;0\nWireConnection;271;230;224;0\nWireConnection;272;230;224;0\nWireConnection;272;415;269;0\nWireConnection;273;230;270;0\nWireConnection;86;0;119;0\nWireConnection;86;1;120;0\nWireConnection;87;0;86;0\nWireConnection;277;0;87;0\nWireConnection;0;0;95;0\nASEEND*/\n//CHKSM=98DD836997CB8F9D2DE3D449BE7FFC55F55CED6F"
  m_functionName: 
  m_description: 'NDF Ward Anisotropic Node


    NdotV set to custom 0.1

'
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 0
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_customEdited: 0
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 0
  m_headerStyle: 2
  m_headerColor: {r: 0.7529412, g: 0.6784314, b: 0, a: 1}
  m_customNodeCategory: Amplify Shader Pack
  m_previewPosition: 0
  m_hidden: 0
  m_url: 
