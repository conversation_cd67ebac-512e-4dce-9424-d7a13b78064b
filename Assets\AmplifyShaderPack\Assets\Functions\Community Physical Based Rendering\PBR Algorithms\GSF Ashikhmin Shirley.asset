%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: GSF Ashikhmin Shirley
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor v1.9.4.4\n// Available at the
    Unity Asset Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=19404\nNode;AmplifyShaderEditor.CommentaryNode;37;-2944,1920;Inherit;False;1021.824;539.6898;Math;10;215;341;340;69;68;74;67;337;338;339;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;267;-4099.164,1920;Inherit;False;1094.901;778.4443;Dot
    Products;13;304;315;272;273;313;271;330;261;328;270;347;348;350;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;339;-2896,2144;Inherit;False;193;134;NdotL;1;97;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;338;-2896,1984;Inherit;False;197;136;NdotV;1;82;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;337;-2736,2288;Inherit;False;195.3479;135.4431;LdotH;1;80;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;266;-2944,1600;Inherit;False;673.0911;245.3663;GSF
    Ashikhmin Shirley Node;1;44;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;44;-2624,1664;Inherit;False;317.573;144.4634;;;0,0,0,1;Designed
    for use with Anisotropic Normal Distribution Functions, the Ashikhmin-Shirley
    GSF provides a good foundation for Anisotropic effects -Jorden Stevens;0;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;67;-2640,2032;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMaxOpNode;74;-2640,2192;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;68;-2496,2336;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleDivideOpNode;69;-2288,2032;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;340;-2336,2128;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;341;-2336,2352;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.StickyNoteNode;342;-2944,1408;Inherit;False;575.7085;152.1831;Credits;;0,0,0,0.5019608;Method
    by Jordan Stevens$Link: https://www.jordanstevenstechart.com/physically-based-rendering$$Node
    Made by Tiber Legacy$Special Thanks to Dawie;0;0\nNode;AmplifyShaderEditor.GetLocalVarNode;82;-2880,2032;Inherit;False;328;NdotV;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;97;-2880,2192;Inherit;False;261;NdotL;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;80;-2720,2336;Inherit;False;330;LdotH;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;270;-3568,2016;Inherit;False;Custom
    Dot Products;True;0;2;1;In 0;In 1;Object;-1;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;328;-3296,2016;Inherit;True;NdotV;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;261;-3296,2240;Inherit;True;NdotL;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;330;-3296,2464;Inherit;True;LdotH;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;313;-3568,2464;Inherit;False;Option;False;0;2;1;In
    0;In 1;Instance;270;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;273;-3744,2320;Inherit;False;NdotL;1;1;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;272;-3744,2096;Inherit;False;NdotV;1;2;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;315;-3744,2544;Inherit;False;LdotH;1;3;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;304;-4000,2016;Inherit;False;Normal;3;0;False;1;0;FLOAT3;0,0,1;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionNode;347;-3808,2016;Inherit;False;NdotV;-1;;5;65be4ec269a534f4e8867af2f999aaa9;4,27,1,377,0,452,0,451,0;3;230;FLOAT3;0,0,1;False;443;FLOAT3;0,0,0;False;415;FLOAT;1E-37;False;1;FLOAT;23\nNode;AmplifyShaderEditor.FunctionSwitch;271;-3568,2240;Inherit;False;Option;False;0;2;1;In
    0;In 1;Instance;270;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionNode;348;-3808,2240;Inherit;False;NdotL;-1;;6;6d7ee1aa8c79587448475fa2b37affc1;5,27,1,381,0,460,0,461,0,463,0;4;230;FLOAT3;0,0,1;False;462;FLOAT3;0,0,0;False;454;FLOAT3;0,0,0;False;416;FLOAT;1E-37;False;1;FLOAT;111\nNode;AmplifyShaderEditor.FunctionNode;350;-3808,2464;Inherit;False;LdotH;-1;;8;0364555185933b049a6a96d60ccefd1a;4,385,0,460,0,451,0,461,0;4;443;FLOAT3;0,0,0;False;462;FLOAT3;0,0,0;False;454;FLOAT3;0,0,0;False;419;FLOAT;1E-37;False;1;FLOAT;112\nNode;AmplifyShaderEditor.FunctionOutput;215;-2128,2032;Inherit;True;True;-1;Out;0;False;1;0;FLOAT;0;False;1;FLOAT;0\nWireConnection;67;0;82;0\nWireConnection;67;1;97;0\nWireConnection;74;0;97;0\nWireConnection;74;1;82;0\nWireConnection;68;0;80;0\nWireConnection;68;1;74;0\nWireConnection;69;0;67;0\nWireConnection;69;1;340;0\nWireConnection;340;0;341;0\nWireConnection;341;0;68;0\nWireConnection;270;0;347;23\nWireConnection;270;1;272;0\nWireConnection;328;0;270;0\nWireConnection;261;0;271;0\nWireConnection;330;0;313;0\nWireConnection;313;0;350;112\nWireConnection;313;1;315;0\nWireConnection;347;230;304;0\nWireConnection;271;0;348;111\nWireConnection;271;1;273;0\nWireConnection;348;230;304;0\nWireConnection;215;0;69;0\nASEEND*/\n//CHKSM=2D6B93D5D175671E3F5ED59B78E8932B56E15D9B"
  m_functionName: 
  m_description: 'GSF Ashikhmin Shirley Node

'
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 0
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_customEdited: 0
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 0
  m_headerStyle: 2
  m_headerColor: {r: 0.7529412, g: 0.6784314, b: 0, a: 1}
  m_customNodeCategory: Amplify Shader Pack
  m_previewPosition: 0
  m_hidden: 0
  m_url: 
