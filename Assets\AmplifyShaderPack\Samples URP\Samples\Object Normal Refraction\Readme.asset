%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e78934177bebe0545ab566bb787b0282, type: 3}
  m_Name: Readme
  m_EditorClassIdentifier: 
  Icon: {fileID: 2800000, guid: 01a84204b7cd46e4f98dc25cbff67e3a, type: 3}
  Title: Object Normal Refraction
  Description:
    Heading: 
    Text: Translucent effect with Normal Refraction.
    LinkText: 
    Url: 
  PropertiesHeader:
    Heading: Properties
    Text: 
    LinkText: 
    Url: 
  Properties:
  - Heading: Chromatic Aberration
    Text: Chromatic aberration intensity.
    LinkText: 
    Url: 
  - Heading: Normal Map
    Text: Normals texture used to influence refraction effect.
    LinkText: 
    Url: 
  - Heading: Normal Scale
    Text: Refraction normal texture intensity.
    LinkText: 
    Url: 
  - Heading: Cube Map
    Text: Cubemap texture used.
    LinkText: 
    Url: 
  - Heading: Opacity
    Text: Base opacity value.
    LinkText: 
    Url: 
  - Heading: Index of Refraction
    Text: Index of Refraction value.
    LinkText: 
    Url: 
  AdditionalProperties: []
  AdditionalScripts: []
  LoadedLayout: 0
  RPType: 3
