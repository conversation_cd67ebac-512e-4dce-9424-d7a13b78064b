%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-8354371464813122523
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ff6f7b9dd354e3c44b2f19d9f06a566a, type: 3}
  m_Name: FSRRenderFeature
  m_EditorClassIdentifier: 
  m_Active: 1
  IsEnabled: 0
--- !u!114 &-7608110745845637236
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f7c2c2c3c19808149a4d3454e1019255, type: 3}
  m_Name: HBAO
  m_EditorClassIdentifier: 
  m_Active: 1
  shader: {fileID: 4800000, guid: bf610497676b34e4dbe0f14fe3fe311c, type: 3}
--- !u!114 &-7029767673589858356
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6b3d386ba5cd94485973aee1479b272e, type: 3}
  m_Name: Normal Reticle
  m_EditorClassIdentifier: 
  m_Active: 1
  settings:
    passTag: Normal Reticle
    Event: 550
    filterSettings:
      RenderQueueType: 1
      LayerMask:
        serializedVersion: 2
        m_Bits: 512
      PassNames: []
    overrideMaterial: {fileID: 0}
    overrideMaterialPassIndex: 0
    overrideShader: {fileID: 0}
    overrideShaderPassIndex: 0
    overrideMode: 1
    overrideDepthState: 1
    depthCompareFunction: 4
    enableWrite: 1
    stencilSettings:
      overrideStencilState: 0
      stencilReference: 0
      stencilCompareFunction: 8
      passOperation: 0
      failOperation: 0
      zFailOperation: 0
    cameraSettings:
      overrideCamera: 0
      restoreCamera: 1
      offset: {x: 0, y: 0, z: 0, w: 0}
      cameraFieldOfView: 60
--- !u!114 &-5605018202843266062
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6b3d386ba5cd94485973aee1479b272e, type: 3}
  m_Name: Normal Opaque
  m_EditorClassIdentifier: 
  m_Active: 1
  settings:
    passTag: Normal Opaque
    Event: 550
    filterSettings:
      RenderQueueType: 0
      LayerMask:
        serializedVersion: 2
        m_Bits: 128
      PassNames: []
    overrideMaterial: {fileID: 0}
    overrideMaterialPassIndex: 0
    overrideShader: {fileID: 0}
    overrideShaderPassIndex: 0
    overrideMode: 0
    overrideDepthState: 1
    depthCompareFunction: 4
    enableWrite: 1
    stencilSettings:
      overrideStencilState: 0
      stencilReference: 0
      stencilCompareFunction: 8
      passOperation: 0
      failOperation: 0
      zFailOperation: 0
    cameraSettings:
      overrideCamera: 0
      restoreCamera: 1
      offset: {x: 0, y: 0, z: 0, w: 0}
      cameraFieldOfView: 60
--- !u!114 &-4975315007987531210
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6b3d386ba5cd94485973aee1479b272e, type: 3}
  m_Name: Normal Enemy
  m_EditorClassIdentifier: 
  m_Active: 1
  settings:
    passTag: Normal Enemy
    Event: 500
    filterSettings:
      RenderQueueType: 1
      LayerMask:
        serializedVersion: 2
        m_Bits: 2112
      PassNames: []
    overrideMaterial: {fileID: 2100000, guid: 48569cbfe814f734f823ddaf08faa5f3, type: 2}
    overrideMaterialPassIndex: 0
    overrideShader: {fileID: 0}
    overrideShaderPassIndex: 0
    overrideMode: 1
    overrideDepthState: 1
    depthCompareFunction: 4
    enableWrite: 1
    stencilSettings:
      overrideStencilState: 0
      stencilReference: 0
      stencilCompareFunction: 8
      passOperation: 0
      failOperation: 0
      zFailOperation: 0
    cameraSettings:
      overrideCamera: 0
      restoreCamera: 1
      offset: {x: 0, y: 0, z: 0, w: 0}
      cameraFieldOfView: 60
--- !u!114 &-4729688654197147598
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 579ed0c7e645ec2469610460c45b7ca1, type: 3}
  m_Name: FlowFx
  m_EditorClassIdentifier: 
  m_Active: 1
  _event: 600
  _pass: {fileID: 8166970638701813335}
  _blitShader: {fileID: 4800000, guid: c17132b1f77d20942aa75f8429c0f8bc, type: 3}
--- !u!114 &-4435671964996340615
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cde85230acf669d43a012f11b632f852, type: 3}
  m_Name: FSRScriptableRenderFeature
  m_EditorClassIdentifier: 
  m_Active: 1
  IsEnabled: 0
--- !u!114 &-4308113831967461310
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cde85230acf669d43a012f11b632f852, type: 3}
  m_Name: FSRScriptableRenderFeature
  m_EditorClassIdentifier: 
  m_Active: 1
  IsEnabled: 0
--- !u!114 &-3478454353841413397
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6b3d386ba5cd94485973aee1479b272e, type: 3}
  m_Name: Highlight Transparent
  m_EditorClassIdentifier: 
  m_Active: 1
  settings:
    passTag: Highlight Transparent
    Event: 450
    filterSettings:
      RenderQueueType: 1
      LayerMask:
        serializedVersion: 2
        m_Bits: 128
      PassNames: []
    overrideMaterial: {fileID: 2100000, guid: 48569cbfe814f734f823ddaf08faa5f3, type: 2}
    overrideMaterialPassIndex: 0
    overrideShader: {fileID: 0}
    overrideShaderPassIndex: 0
    overrideMode: 1
    overrideDepthState: 1
    depthCompareFunction: 4
    enableWrite: 0
    stencilSettings:
      overrideStencilState: 0
      stencilReference: 0
      stencilCompareFunction: 8
      passOperation: 0
      failOperation: 0
      zFailOperation: 0
    cameraSettings:
      overrideCamera: 0
      restoreCamera: 1
      offset: {x: 0, y: 0, z: 0, w: 0}
      cameraFieldOfView: 60
--- !u!114 &-3082839466147879067
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 579ed0c7e645ec2469610460c45b7ca1, type: 3}
  m_Name: FlowFx
  m_EditorClassIdentifier: 
  m_Active: 1
  _event: 550
  _pass: {fileID: 6829741525464651231}
  _blitShader: {fileID: 4800000, guid: c17132b1f77d20942aa75f8429c0f8bc, type: 3}
--- !u!114 &-1116602868552212492
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cf55d7f5ccca49c40b80bbf154889a58, type: 3}
  m_Name: LightScatteringRenderFeature
  m_EditorClassIdentifier: 
  m_Active: 1
  settings:
    renderPassEvent: 550
--- !u!114 &-978749116799918553
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 579ed0c7e645ec2469610460c45b7ca1, type: 3}
  m_Name: FlowFx
  m_EditorClassIdentifier: 
  m_Active: 1
  _event: 600
  _pass: {fileID: 2290093991100166837}
  _blitShader: {fileID: 4800000, guid: c17132b1f77d20942aa75f8429c0f8bc, type: 3}
--- !u!114 &-932722753685603009
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ce2e666a0193a442ab1551414ba5f7c3, type: 3}
  m_Name: QuibliPostProcess
  m_EditorClassIdentifier: 
  m_Active: 1
  settings:
    renderersAfterOpaqueAndSky: []
    renderersBeforePostProcess:
    - CompoundRendererFeature.PostProcess.ColorGradingRenderer, Assembly-CSharp,
      Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
    - CompoundRendererFeature.PostProcess.StylizedDetailRenderer, Assembly-CSharp,
      Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
    renderersAfterPostProcess: []
--- !u!114 &-619502560364433431
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 729a2e952309e2547a3747b2cdd9a36f, type: 3}
  m_Name: ShapesRenderFeature
  m_EditorClassIdentifier: 
  m_Active: 1
--- !u!114 &-599560932538769442
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 70e5811d9aeb3e04fb48321dce46f24d, type: 3}
  m_Name: HighlightsManagerURP
  m_EditorClassIdentifier: 
  m_Active: 1
  DepthLayerMask:
    serializedVersion: 2
    m_Bits: 0
  renderingEvent: 1
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: de640fe3d0db1804a85f9fc8f5cadab6, type: 3}
  m_Name: Beat Renderer
  m_EditorClassIdentifier: 
  debugShaders:
    debugReplacementPS: {fileID: 4800000, guid: cf852408f2e174538bcd9b7fda1c5ae7, type: 3}
    hdrDebugViewPS: {fileID: 4800000, guid: 573620ae32aec764abd4d728906d2587, type: 3}
    probeVolumeSamplingDebugComputeShader: {fileID: 7200000, guid: 53626a513ea68ce47b59dc1299fe3959, type: 3}
  probeVolumeResources:
    probeVolumeDebugShader: {fileID: 0}
    probeVolumeFragmentationDebugShader: {fileID: 0}
    probeVolumeOffsetDebugShader: {fileID: 0}
    probeVolumeSamplingDebugShader: {fileID: 0}
    probeSamplingDebugMesh: {fileID: 0}
    probeSamplingDebugTexture: {fileID: 0}
    probeVolumeBlendStatesCS: {fileID: 0}
  m_RendererFeatures:
  - {fileID: -4975315007987531210}
  - {fileID: 2888265625598883128}
  - {fileID: -3478454353841413397}
  - {fileID: 6755989004430836863}
  - {fileID: -1116602868552212492}
  - {fileID: 4448173028715358177}
  - {fileID: -7029767673589858356}
  - {fileID: -5605018202843266062}
  - {fileID: 8237595999034103404}
  - {fileID: -619502560364433431}
  - {fileID: -8354371464813122523}
  - {fileID: 1234567890123456789}
  m_RendererFeatureMap: 36c23b4f5c21f4ba38f5caa6482e1528eb22dfdf310cbacf7f848b6b3418c25df45fc3689b0781f0e1670d5ccd15bb3dccff26d28b3f719ef2c3ed23a4f936b26c569b9dead15172e96f0820b41567f7256c27866a4f0f8c629a8b5c32be1b3498dbbc2cb9f6f46d
  m_UseNativeRenderPass: 1
  xrSystemData: {fileID: 11400000, guid: 60e1133243b97e347b653163a8c01b64, type: 2}
  postProcessData: {fileID: 11400000, guid: 41439944d30ece34e96484bdb6645b55, type: 2}
  m_AssetVersion: 2
  m_OpaqueLayerMask:
    serializedVersion: 2
    m_Bits: 16767
  m_TransparentLayerMask:
    serializedVersion: 2
    m_Bits: 16767
  m_DefaultStencilState:
    overrideStencilState: 0
    stencilReference: 0
    stencilCompareFunction: 8
    passOperation: 2
    failOperation: 0
    zFailOperation: 0
  m_ShadowTransparentReceive: 1
  m_RenderingMode: 2
  m_DepthPrimingMode: 1
  m_CopyDepthMode: 1
  m_DepthAttachmentFormat: 0
  m_DepthTextureFormat: 0
  m_AccurateGbufferNormals: 0
  m_IntermediateTextureMode: 0
--- !u!114 &864329678921258455
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 96dab975e69883249a56554e791c4fd0, type: 3}
  m_Name: ButoRenderFeature
  m_EditorClassIdentifier: 
  m_Active: 1
  settings:
    renderPassEvent: 450
--- !u!114 &1234567890123456789
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 629a8b5c32be1b3498dbbc2cb9f6f46d, type: 3}
  m_Name: Flux
  m_EditorClassIdentifier: 
  m_Active: 1
  shader: {fileID: 4800000, guid: 62449f654265cf94e81275e55a4b1823, type: 3}
--- !u!114 &2290093991100166837
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0a4a3c9fc53e47eeb28b368324d7020f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _active: 1
  _shader: {fileID: 4800000, guid: 3fc4e2bbb04b44788d407966b1b80a45, type: 3}
  _fps: 60
  _scale: 0.5
  _hdr: 0
--- !u!114 &2888265625598883128
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6b3d386ba5cd94485973aee1479b272e, type: 3}
  m_Name: Normal Transparent
  m_EditorClassIdentifier: 
  m_Active: 1
  settings:
    passTag: Normal Transparent
    Event: 500
    filterSettings:
      RenderQueueType: 1
      LayerMask:
        serializedVersion: 2
        m_Bits: 128
      PassNames: []
    overrideMaterial: {fileID: 0}
    overrideMaterialPassIndex: 0
    overrideShader: {fileID: 0}
    overrideShaderPassIndex: 0
    overrideMode: 0
    overrideDepthState: 1
    depthCompareFunction: 4
    enableWrite: 1
    stencilSettings:
      overrideStencilState: 0
      stencilReference: 0
      stencilCompareFunction: 8
      passOperation: 0
      failOperation: 0
      zFailOperation: 0
    cameraSettings:
      overrideCamera: 0
      restoreCamera: 1
      offset: {x: 0, y: 0, z: 0, w: 0}
      cameraFieldOfView: 60
--- !u!114 &4448173028715358177
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 96dab975e69883249a56554e791c4fd0, type: 3}
  m_Name: ButoRenderFeature
  m_EditorClassIdentifier: 
  m_Active: 1
  settings:
    renderPassEvent: 500
--- !u!114 &5057699727715101957
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 55e6184d2ea958044a6434dca151c688, type: 3}
  m_Name: JPG
  m_EditorClassIdentifier: 
  m_Active: 1
  shader: {fileID: 4800000, guid: 62449f654265cf94e81275e55a4b1823, type: 3}
  renderPassEvent: 500
--- !u!114 &6665530316897770538
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cde85230acf669d43a012f11b632f852, type: 3}
  m_Name: FSRScriptableRenderFeature
  m_EditorClassIdentifier: 
  m_Active: 1
  IsEnabled: 0
--- !u!114 &6755989004430836863
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6b3d386ba5cd94485973aee1479b272e, type: 3}
  m_Name: Normal Projectile
  m_EditorClassIdentifier: 
  m_Active: 1
  settings:
    passTag: Normal Projectile
    Event: 500
    filterSettings:
      RenderQueueType: 1
      LayerMask:
        serializedVersion: 2
        m_Bits: 1024
      PassNames: []
    overrideMaterial: {fileID: 0}
    overrideMaterialPassIndex: 0
    overrideShader: {fileID: 0}
    overrideShaderPassIndex: 0
    overrideMode: 1
    overrideDepthState: 1
    depthCompareFunction: 4
    enableWrite: 1
    stencilSettings:
      overrideStencilState: 0
      stencilReference: 0
      stencilCompareFunction: 8
      passOperation: 0
      failOperation: 0
      zFailOperation: 0
    cameraSettings:
      overrideCamera: 0
      restoreCamera: 1
      offset: {x: 0, y: 0, z: 0, w: 0}
      cameraFieldOfView: 60
--- !u!114 &6829741525464651231
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0a4a3c9fc53e47eeb28b368324d7020f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _active: 1
  _shader: {fileID: 4800000, guid: 3fc4e2bbb04b44788d407966b1b80a45, type: 3}
  _fps: 60
  _scale: 0.5
  _hdr: 0
--- !u!114 &7191800282698399116
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ce2e666a0193a442ab1551414ba5f7c3, type: 3}
  m_Name: QuibliPostProcess
  m_EditorClassIdentifier: 
  m_Active: 1
  settings:
    renderersAfterOpaqueAndSky: []
    renderersBeforePostProcess:
    - CompoundRendererFeature.PostProcess.StylizedDetailRenderer, Assembly-CSharp,
      Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
    - CompoundRendererFeature.PostProcess.ColorGradingRenderer, Assembly-CSharp,
      Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
    renderersAfterPostProcess: []
--- !u!114 &8166970638701813335
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0a4a3c9fc53e47eeb28b368324d7020f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _active: 1
  _shader: {fileID: 4800000, guid: 3fc4e2bbb04b44788d407966b1b80a45, type: 3}
  _fps: 10
  _scale: 0.5
  _hdr: 0
--- !u!114 &8237595999034103404
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 473a86c9e274347dfbdde619584cebe9, type: 3}
  m_Name: HighlightPlusRenderPassFeature
  m_EditorClassIdentifier: 
  m_Active: 1
  renderPassEvent: 550
  clearStencil: 0
  previewInEditMode: 1
  showInPreviewCamera: 1
