%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: MAT_<PERSON>_Hair_MatStandard
  m_Shader: {fileID: 46, guid: 0000000000000000f000000000000000, type: 0}
  m_ValidKeywords:
  - _NORMALMAP
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _AnisoTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _AnisotropyMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 2800000, guid: 5f62fee607bd378448eaf1defd926de9, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: 721a18bd97fd18b428dadc9d3cfb8940, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 2800000, guid: c3f4a5d645c2b5c4390c8892609a8093, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicSmooth:
        m_Texture: {fileID: 2800000, guid: 6879b0e041621c34ea9ce4c3507ed376, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecularTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TangentMap:
        m_Texture: {fileID: 2800000, guid: 66e52c3d6beda4989a507a521ac66048, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AnisoOffset: 0.04
    - _AnisoOffset2: -0.19
    - _Anisotropy: 1
    - _BumpScale: 1
    - _Cutoff: 0.404
    - _DetailNormalMapScale: 1
    - _DstBlend: 0
    - _Gloss: 0.75
    - _Gloss2: 0.5
    - _GlossMapScale: 0.75
    - _Glossiness: 0.364
    - _GlossyReflections: 1
    - _Metallic: 0
    - _Mode: 0
    - _OcclusionStrength: 1
    - _Parallax: 0.02
    - _Reflection: 0.094
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _Specularity: 0.36
    - _Specularity2: 0.13
    - _SrcBlend: 1
    - _UVSec: 0
    - _ZWrite: 1
    - _numSteps: 4
    - _value: 0
    m_Colors:
    - _Color: {r: 0.5529412, g: 0.13333334, b: 0.13333334, a: 1}
    - _Color2: {r: 1, g: 0.45588237, b: 0.45588237, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _HighlightColor: {r: 0.8018868, g: 0.094562106, b: 0.21537162, a: 1}
    - _HighlightColor2: {r: 0.9150943, g: 0.15970986, b: 0.29221818, a: 1}
  m_BuildTextureStacks: []
