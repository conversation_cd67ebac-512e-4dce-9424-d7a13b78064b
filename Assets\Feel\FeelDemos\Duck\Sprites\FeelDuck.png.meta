fileFormatVersion: 2
guid: 8a1f96c93a153e34a98b5f6dd6bbfebd
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 4153240674832713612
    second: FeelDuck_0
  - first:
      213: -3763894057934510291
    second: FeelDuck_1
  - first:
      213: -7272332370791766393
    second: FeelDuck_2
  - first:
      213: 8802554064341720584
    second: FeelDuck_3
  - first:
      213: 7807161731283161696
    second: FeelDuck_4
  - first:
      213: 1310270320375162423
    second: FeelDuck_5
  - first:
      213: -8176256291387964677
    second: FeelDuck_6
  - first:
      213: -5848280782949743053
    second: FeelDuck_7
  - first:
      213: 5657178389051071312
    second: FeelDuck_8
  - first:
      213: 4326485097364342716
    second: FeelDuck_9
  - first:
      213: -1128652821404161721
    second: FeelDuck_10
  - first:
      213: 3784876598984905458
    second: FeelDuck_11
  - first:
      213: -4447904424348394206
    second: FeelDuck_12
  - first:
      213: 8189371831780941328
    second: FeelDuck_13
  - first:
      213: -1522864199519269440
    second: FeelDuck_14
  - first:
      213: 2950031539233405113
    second: FeelDuck_15
  - first:
      213: 889054522252835898
    second: FeelDuck_12
  - first:
      213: -2962084026128317011
    second: FeelDuck_13
  - first:
      213: -9008161957514740383
    second: FeelDuck_14
  - first:
      213: 2334874930736024156
    second: FeelDuck_15
  - first:
      213: -2520759210819888809
    second: FeelDuck_16
  externalObjects: {}
  serializedVersion: 12
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMasterTextureLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 0
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  cookieLightType: 1
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: iPhone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: FeelDuck_0
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c878b12936643a930800000000000000
      internalID: 4153240674832713612
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: FeelDuck_1
      rect:
        serializedVersion: 2
        x: 66
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d272c98db46f3cbc0800000000000000
      internalID: -3763894057934510291
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: FeelDuck_2
      rect:
        serializedVersion: 2
        x: 132
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 786e16e3d3c731b90800000000000000
      internalID: -7272332370791766393
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: FeelDuck_3
      rect:
        serializedVersion: 2
        x: 198
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 80e52499844f82a70800000000000000
      internalID: 8802554064341720584
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: FeelDuck_4
      rect:
        serializedVersion: 2
        x: 264
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 06218210b3c985c60800000000000000
      internalID: 7807161731283161696
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: FeelDuck_5
      rect:
        serializedVersion: 2
        x: 330
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 736e2bd0ae30f2210800000000000000
      internalID: 1310270320375162423
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: FeelDuck_6
      rect:
        serializedVersion: 2
        x: 396
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: bf2844ff83a188e80800000000000000
      internalID: -8176256291387964677
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: FeelDuck_7
      rect:
        serializedVersion: 2
        x: 462
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 33689890d9bb6dea0800000000000000
      internalID: -5848280782949743053
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: FeelDuck_8
      rect:
        serializedVersion: 2
        x: 528
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 05bd76695c5528e40800000000000000
      internalID: 5657178389051071312
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: FeelDuck_9
      rect:
        serializedVersion: 2
        x: 594
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: cb74c6af543ca0c30800000000000000
      internalID: 4326485097364342716
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: FeelDuck_10
      rect:
        serializedVersion: 2
        x: 660
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 74966712d383650f0800000000000000
      internalID: -1128652821404161721
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: FeelDuck_11
      rect:
        serializedVersion: 2
        x: 726
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2f21f21b635968430800000000000000
      internalID: 3784876598984905458
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: FeelDuck_12
      rect:
        serializedVersion: 2
        x: 792
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a3cb5d8a06e865c00800000000000000
      internalID: -4447904424348394206
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: FeelDuck_13
      rect:
        serializedVersion: 2
        x: 858
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: da543c3a54094e6d0800000000000000
      internalID: 8189371831780941328
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: FeelDuck_14
      rect:
        serializedVersion: 2
        x: 924
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 16d752b4d649cf280800000000000000
      internalID: -1522864199519269440
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: FeelDuck_15
      rect:
        serializedVersion: 2
        x: 990
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c5a4c79f064276020800000000000000
      internalID: 2950031539233405113
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: FeelDuck_16
      rect:
        serializedVersion: 2
        x: 1056
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 75959c27ad6740dd0800000000000000
      internalID: -2520759210819888809
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: 5e97eb03825dee720800000000000000
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      FeelDuck_0: 4153240674832713612
      FeelDuck_1: -3763894057934510291
      FeelDuck_10: -1128652821404161721
      FeelDuck_11: 3784876598984905458
      FeelDuck_12: -4447904424348394206
      FeelDuck_13: 8189371831780941328
      FeelDuck_14: -1522864199519269440
      FeelDuck_15: 2950031539233405113
      FeelDuck_16: -2520759210819888809
      FeelDuck_2: -7272332370791766393
      FeelDuck_3: 8802554064341720584
      FeelDuck_4: 7807161731283161696
      FeelDuck_5: 1310270320375162423
      FeelDuck_6: -8176256291387964677
      FeelDuck_7: -5848280782949743053
      FeelDuck_8: 5657178389051071312
      FeelDuck_9: 4326485097364342716
  spritePackingTag: 
  pSDRemoveMatte: 0
  pSDShowRemoveMatteOption: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
AssetOrigin:
  serializedVersion: 1
  productId: 183370
  packageName: Feel
  packageVersion: 5.5
  assetPath: Assets/Feel/FeelDemos/Duck/Sprites/FeelDuck.png
  uploadId: 759320
