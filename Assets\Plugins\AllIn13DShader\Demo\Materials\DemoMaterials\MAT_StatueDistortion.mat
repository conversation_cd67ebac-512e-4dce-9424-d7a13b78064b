%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: MAT_StatueDistortion
  m_Shader: {fileID: 4800000, guid: 0dde7f0097aeb2541aea684ca0ad0e3c, type: 3}
  m_ValidKeywords:
  - _ALBEDOVERTEXCOLORMODE_REPLACE
  - _ALPHA_CUTOFF_ON
  - _AOMAP_ON
  - _CAST_SHADOWS_ON
  - _COLORRAMPLIGHTINGSTAGE_BEFORELIGHTING
  - _EMISSION_ON
  - _FADE_BURN_ON
  - _FADE_ON
  - _GREYSCALESTAGE_BEFORELIGHTING
  - _HEIGHTGRADIENTPOSITIONSPACE_LOCAL
  - _HIGHLIGHTS_ON
  - _LIGHTMODEL_CLASSIC
  - _MATCAPBLENDMODE_MULTIPLY
  - _NORMAL_MAP_ON
  - _OUTLINETYPE_NONE
  - _RECEIVE_SHADOWS_ON
  - _REFLECTIONS_CLASSIC
  - _RIMLIGHTINGSTAGE_BEFORELIGHTING
  - _RIM_LIGHTING_ON
  - _SHADINGMODEL_PBR
  - _SPECULARMODEL_CLASSIC
  - _TEXTUREBLENDINGMODE_RGB
  - _TEXTUREBLENDINGSOURCE_TEXTURE
  - _TEXTURE_BLENDING_ON
  - _TRIPLANARNORMALSPACE_WORLD
  - _USE_WIND_VERTICAL_MASK
  - _VERTEX_DISTORTION_ON
  m_InvalidKeywords:
  - _GLITCHWORLDSPACE_ON
  - _NORMALMAP
  - _USE_MASK_ON
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 1
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _AOMap:
        m_Texture: {fileID: 2800000, guid: e0d38f87a22964443b62e4a3072e108f, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BlendingNormalMapB:
        m_Texture: {fileID: 2800000, guid: 1ada15fc422d29f4caaa21ac64d0adb8, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BlendingNormalMapG:
        m_Texture: {fileID: 2800000, guid: 59bfabcb50ebb2144ba13c59cc13aa8b, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BlendingNormalMapWhite:
        m_Texture: {fileID: 2800000, guid: 8175cba73bf87af41aeba7bbd2f3c4cd, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BlendingTextureB:
        m_Texture: {fileID: 2800000, guid: 39a78d41cf720cc44b021d49ad333395, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BlendingTextureG:
        m_Texture: {fileID: 2800000, guid: fd438d519a346014b922f761894ea671, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BlendingTextureWhite:
        m_Texture: {fileID: 2800000, guid: 2af6ba1567607124eb61349fa044375b, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 2800000, guid: 6e813470a48701346b15d112bc3d29cb, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ColorRampTex:
        m_Texture: {fileID: 2800000, guid: 99b6592afee2790429156fef1dc9ba44, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailBump:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DistortTex:
        m_Texture: {fileID: 2800000, guid: fcb69fdadf254834880fe3c443965278, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Emission:
        m_Texture: {fileID: 2800000, guid: 595b8ad4bbd0a7942b3f0452420a3d0c, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 2800000, guid: 1487ed188ce061f4bb9254b3041e70b8, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _FadeTex:
        m_Texture: {fileID: 2800000, guid: fcb69fdadf254834880fe3c443965278, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _GlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: 39a78d41cf720cc44b021d49ad333395, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MatcapTex:
        m_Texture: {fileID: 2800000, guid: cf3a2315564d4de4eb8601b697dea358, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicRough:
        m_Texture: {fileID: 2800000, guid: ae053b2157d1a8c479b1e8554de84a0b, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalMap:
        m_Texture: {fileID: 2800000, guid: 1ada15fc422d29f4caaa21ac64d0adb8, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 2800000, guid: 7d22527fe46049f40b9d9d9625b0b55f, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SSSMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecularMap:
        m_Texture: {fileID: 2800000, guid: 21303f82b94a4e14a9758e491842b087, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TexBlendingMask:
        m_Texture: {fileID: 2800000, guid: 5a775794f8704ba4385b9c92cf9f1224, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0.24, y: 0}
    - _ToonRamp:
        m_Texture: {fileID: 2800000, guid: 339398f75cfa2714d93579662d08ec9a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TopAlbedo:
        m_Texture: {fileID: 2800000, guid: fd467bbf94d53bb499d3c3075cb021c9, type: 3}
        m_Scale: {x: 0.75, y: 0.75}
        m_Offset: {x: 0, y: 0}
    - _TopMetallicRough:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TopNormal:
        m_Texture: {fileID: 2800000, guid: 8d24d1cae6ab73546a92f7aeae5c2921, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TopNormal2:
        m_Texture: {fileID: 2800000, guid: b5e52e512494f434c88a1675f0e9361d, type: 3}
        m_Scale: {x: 3, y: 3}
        m_Offset: {x: 0, y: 0}
    - _TriplanarTopNormalMap:
        m_Texture: {fileID: 2800000, guid: 8175cba73bf87af41aeba7bbd2f3c4cd, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TriplanarTopTex:
        m_Texture: {fileID: 2800000, guid: 2af6ba1567607124eb61349fa044375b, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _VertexDistortionNoiseTex:
        m_Texture: {fileID: 2800000, guid: fcb69fdadf254834880fe3c443965278, type: 3}
        m_Scale: {x: 0.2, y: 0.2}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AOContrast: 1
    - _AOMapEnabled: 1
    - _AOMapStrength: 1
    - _AdvancedConfigurationEnabled: 0
    - _AffectedByLightmaps: 0
    - _AlbedoVertexColorMode: 1
    - _AlbedoVertexColorOn: 0
    - _AlphaCutoffOn: 1
    - _AlphaCutoffValue: 0.25
    - _AlphaRoundOn: 0
    - _AnisoShininess: 0.85
    - _Anisotropy: 0.45
    - _BlendDst: 0
    - _BlendSrc: 1
    - _BlendingMaskCutoffB: 0.1
    - _BlendingMaskCutoffG: 0
    - _BlendingMaskCutoffWhite: 0.15
    - _BlendingMaskSmoothnessB: 0.4
    - _BlendingMaskSmoothnessG: 0.175
    - _BlendingMaskSmoothnessWhite: 0.4
    - _Brightness: 0
    - _BrightnessLM: 0
    - _BumpScale: 1
    - _CastShadowsOn: 1
    - _ColorMask: 15
    - _ColorRampBlend: 1
    - _ColorRampLightingStage: 0
    - _ColorRampLuminosity: 0
    - _ColorRampOn: 0
    - _ColorRampScrollSpeed: 0
    - _ColorRampTiling: 1
    - _Contrast: 1
    - _ContrastBrightnessOn: 0
    - _ContrastLM: 1
    - _CullingMode: 2
    - _CustomAmbientLightOn: 0
    - _CustomShadowColorOn: 0
    - _Cutoff: 0.5
    - _DepthColoringOn: 0
    - _DepthGlowColorIntensity: 25
    - _DepthGlowDist: 0.2
    - _DepthGlowGlobalIntensity: 2
    - _DepthGlowPower: 25
    - _DetailNormalMapScale: 1
    - _DetailScale: 1
    - _DistortAmount: 0.3
    - _DistortTexXSpeed: 2
    - _DistortTexYSpeed: 2
    - _DitherOn: 0
    - _DitherScale: 1
    - _DstBlend: 0
    - _EmissionEnabled: 1
    - _EmissionSelfGlow: 1
    - _FaceDownCutoff: 0.25
    - _FadeAmount: 0
    - _FadeBurnOn: 1
    - _FadeBurnWidth: 0.015
    - _FadeByCamDistanceOn: 0
    - _FadeOn: 1
    - _FadePower: 1
    - _FadeTransition: 0.052
    - _FogOn: 0
    - _GeneralAlpha: 1
    - _Glitch: 0
    - _GlitchAmount: 0.5
    - _GlitchSpeed: 2.5
    - _GlitchTiling: 5
    - _GlitchWorldSpace: 1
    - _GlossMapOn: 0
    - _GlossMapScale: 1
    - _Glossiness: 1
    - _GlossyReflections: 1
    - _GreyScaleStage: 0
    - _Greyscale: 0
    - _GreyscaleBlend: 1
    - _GreyscaleLuminosity: 0
    - _HalfLambertWrap: 1
    - _HandDrawn: 0
    - _HandDrawnAmount: 10
    - _HandDrawnSpeed: 5
    - _HardnessFakeGI: 0.75
    - _HeightGradientOn: 0
    - _HeightGradientPositionSpace: 0
    - _HighlightCutoff: 0.236
    - _HighlightSmoothness: 0.5
    - _Highlights: 1
    - _HighlightsStrength: 0.7
    - _Hit: 0
    - _HitBlend: 1
    - _HitGlow: 5
    - _Hologram: 0
    - _HologramAccentAlpha: 0.5
    - _HologramAccentFrequency: 2
    - _HologramAccentSpeed: 1
    - _HologramAlpha: 1
    - _HologramBaseAlpha: 0.1
    - _HologramFrequency: 20
    - _HologramLineCenter: 0.5
    - _HologramLineSmoothness: 2
    - _HologramLineSpacing: 2
    - _HologramScrollSpeed: 2
    - _HueBrightness: 1
    - _HueBrightnessLM: 1
    - _HueSaturation: 1
    - _HueSaturationLM: 1
    - _HueShift: 0
    - _HueShiftEnabled: 0
    - _HueShiftLM: 0
    - _InflateBlend: 0
    - _IntersectionFadeFactor: 1
    - _IntersectionFadeOn: 0
    - _IntersectionGlowOn: 0
    - _LightModel: 1
    - _LightmapColorCorrection: 0
    - _MaskNormalAmount: 0.462
    - _Matcap: 0
    - _MatcapBlend: 1
    - _MatcapBlendMode: 0
    - _MatcapIntensity: 1
    - _MaxDistanceToFade: 100
    - _MaxGradientHeight: 0.75
    - _MaxInflate: 0.2
    - _MaxRim: 1
    - _Metallic: 0
    - _MetallicMapOn: 0
    - _MinDistanceToFade: 0
    - _MinGradientHeight: 0
    - _MinInflate: 0
    - _MinRim: 0.762
    - _Mode: 0
    - _NearFade: 0
    - _NormalInfluence: 0.5
    - _NormalMapEnabled: 1
    - _NormalStrength: 1.5
    - _OcclusionStrength: 1
    - _OutlineType: 0
    - _Parallax: 0.02
    - _Pixelate: 0
    - _PixelateSize: 132
    - _Posterize: 0
    - _PosterizeGamma: 0.75
    - _PosterizeNumColors: 8
    - _RecalculateNormals: 0
    - _ReceiveShadowsOn: 1
    - _Reflections: 1
    - _ReflectionsAtten: 0.4
    - _RenderPreset: 1
    - _RimAttenuation: 0.1
    - _RimLighting: 1
    - _RimLightingStage: 0
    - _SSSAtten: 1
    - _SSSFrontAtten: 0.3
    - _SSSFrontPower: 3
    - _SSSPower: 1
    - _ScaleWithCameraDistance: 0
    - _ScreenSpaceUVOn: 0
    - _ScrollTextureOn: 0
    - _ScrollTextureX: 1
    - _ScrollTextureY: 1
    - _ShadingModel: 1
    - _ShakeBlend: 1
    - _ShakeSpeedMult: 1
    - _Shininess: 16
    - _Smoothness: 0.4
    - _SmoothnessTextureChannel: 0
    - _SpecularAtten: 0.644
    - _SpecularHighlights: 1
    - _SpecularModel: 1
    - _SpecularToonCutoff: 0.35
    - _SpecularToonSmoothness: 0
    - _SpherizeNormals: 0
    - _SrcBlend: 1
    - _StochasticSampling: 0
    - _StochasticScale: 3.464
    - _StochasticSkew: 0.57735026
    - _SubsurfaceScattering: 0
    - _TextureBlending: 1
    - _TextureBlendingMode: 0
    - _TextureBlendingSource: 1
    - _TimingSeed: 0
    - _ToonCutoff: 0.5
    - _ToonFactor: 0
    - _ToonSmoothness: 0.5
    - _TopGlossiness: 0
    - _TopMetallic: 0
    - _TopNormalStrength: 1
    - _TriplanarMappingOn: 0
    - _TriplanarNormalSpace: 1
    - _TriplanarSharpness: 15
    - _UVDistortion: 0
    - _UVSec: 0
    - _UseCustomTime: 0
    - _UseVerticalMask: 1
    - _Use_Mask: 1
    - _VertexColorBlending: 1
    - _VertexDistortionAmount: 0.24
    - _VertexDistortionOn: 1
    - _VertexInflate: 0
    - _VertexShakeOn: 0
    - _VoxelBlend: 1
    - _VoxelSize: 2
    - _Voxelize: 0
    - _WaveAmount: 7
    - _WaveSpeed: 10
    - _WaveStrength: 7.5
    - _WaveUVOn: 0
    - _WaveX: 0
    - _WaveY: 0.5
    - _WindAttenuation: 1
    - _WindOn: 0
    - _WindVerticalMaskMaxY: 1
    - _WindVerticalMaskMinY: -1
    - _ZTestMode: 4
    - _ZWrite: 1
    m_Colors:
    - _AOColor: {r: 0, g: 0, b: 0, a: 0}
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _CustomAmbientColor: {r: 0.65, g: 0.65, b: 0.65, a: 1}
    - _DepthGlowColor: {r: 1, g: 0.9869999, b: 0.6, a: 1}
    - _EmissionColor: {r: 0, g: 5.678695, b: 14.928529, a: 1}
    - _EmissiveColor: {r: 0.10980392, g: 1.1294118, b: 4, a: 1}
    - _FadeBurnColor: {r: 0, g: 7.9749274, b: 20.865906, a: 1}
    - _GlitchOffset: {r: -0.5, g: 0, b: 0, a: 0}
    - _GradientHeightColor01: {r: 0.2, g: 0.2, b: 0.2, a: 1}
    - _GradientHeightColor02: {r: 1, g: 1, b: 1, a: 1}
    - _GreyscaleTintColor: {r: 1, g: 1, b: 1, a: 1}
    - _HighlightOffset: {r: 0, g: 0.3, b: 0, a: 0}
    - _HighlightsColor: {r: 2.9960785, g: 2.9960785, b: 2.9960785, a: 1}
    - _HitColor: {r: 1, g: 1, b: 1, a: 1}
    - _HologramColor: {r: 0.35, g: 0.8, b: 2, a: 1}
    - _HologramLineDirection: {r: 0, g: 1, b: 0, a: 0}
    - _RimColor: {r: 1, g: 1, b: 1, a: 1}
    - _RimOffset: {r: 0, g: 0, b: 0, a: 0}
    - _SSSColor: {r: 1, g: 1, b: 1, a: 1}
    - _ShakeMaxDisplacement: {r: 0.1, g: 0.1, b: 0.1, a: 0}
    - _ShakeSpeed: {r: 41, g: 49, b: 45, a: 0}
    - _VertexDistortionNoiseSpeed: {r: 4, g: 4, b: 0, a: 0}
  m_BuildTextureStacks: []
