# Flux Motion Vector Debug Tools - Usage Guide

## 🔧 **Available Debug Tools**

I've created multiple diagnostic tools to identify why motion vectors aren't working:

### **1. Enhanced Motion Vector Debug Tool**
- **Location**: `Tools → Flux → Motion Vector Debug Tool`
- **Features**: 
  - Opens a window with diagnostic results
  - Shows results both in window and console
  - Buttons for enabling/disabling motion vector visualization
  - Force enable reprojection functionality

### **2. Quick Test Menu Items**
- **Location**: `Tools → Flux → Quick Test - [Option]`
- **Options**:
  - **Check Motion Vector System**: Fast diagnostic in console
  - **Force Enable Motion Vectors**: Sets Base Noise = 0.1 + enables visualization
  - **Load Enhanced Preset**: Applies the Enhanced Pixel Trailing Demo preset

## 🧪 **Testing Protocol**

### **Step 1: Quick Diagnostic**
```
Tools → Flux → Quick Test - Check Motion Vector System
```
This will immediately output to console:
- URP setup status
- FluxEffect detection
- DoReprojection calculation
- Camera analysis
- Play Mode status

### **Step 2: Force Enable Motion Vectors**
If Step 1 shows `DoReprojection = false`:
```
Tools → Flux → Quick Test - Force Enable Motion Vectors
```
This will:
- Set Base Noise = 0.1
- Enable Motion Vector Visualization
- Force motion vector generation

### **Step 3: Test in Game View**
1. **Enter Play Mode**
2. **Switch to Game View** (not Scene View)
3. **Move camera with WASD + Mouse**
4. **Expected**: Colored motion vector overlay

### **Step 4: Load Enhanced Preset**
```
Tools → Flux → Quick Test - Load Enhanced Preset
```
This applies the Enhanced Pixel Trailing Demo preset with all enhanced parameters.

## 🎯 **Expected Console Output**

### **Successful Detection**
```
=== FLUX QUICK TEST ===
✅ URP Asset: [AssetName]
✅ Found FluxEffect in Volume: [VolumeName]
📊 Key Values:
   Effect Intensity: 1
   Base Noise: 0.1
   Length Influence: 8
   Camera Motion Amplification: 10
   Visualize Motion Vectors: True
📊 DoReprojection = True
📊 Found 2 cameras:
   Main Camera: Game ✅ OK
   SceneCamera: SceneView ❌ EXCLUDED
📊 Play Mode: True
=== QUICK TEST COMPLETE ===
```

### **Problem Detection**
```
=== FLUX QUICK TEST ===
❌ No URP Render Pipeline Asset! Go to Edit → Project Settings → Graphics
```
OR
```
❌ No FluxEffect found! Create a Volume with Flux Effect component.
```
OR
```
❌ Motion vectors DISABLED! Need at least one parameter > 0
```

## 🚨 **Common Issues and Solutions**

### **Issue 1: "No console output when clicking buttons"**
- **Solution**: Open Console window manually: `Window → General → Console`
- **Alternative**: Use the "Open Console Window" button in the debug tool

### **Issue 2: "DoReprojection = false"**
- **Solution**: Click "Force Enable Motion Vectors" quick test
- **Manual**: Set any of these > 0: Base Noise, Length Influence, Camera Motion Amplification

### **Issue 3: "No FluxEffect found"**
- **Solution**: Create a Volume GameObject with a Volume Profile containing a Flux Effect component
- **Check**: Ensure the Volume Profile is assigned and contains the Flux Effect

### **Issue 4: "Testing in Scene View"**
- **Problem**: Scene View cameras are excluded from motion vector generation
- **Solution**: Test in Game View during Play Mode only

### **Issue 5: "No URP Render Pipeline Asset"**
- **Solution**: Go to `Edit → Project Settings → Graphics` and assign a URP asset

## ✅ **Success Criteria**

After using the debug tools, you should see:

1. **✅ URP Asset detected**
2. **✅ FluxEffect found in Volume**
3. **✅ DoReprojection = True**
4. **✅ Game View camera detected**
5. **✅ Play Mode active**

Once all criteria are met:
- **Motion vector visualization** should show colored overlay in Game View
- **Enhanced trailing parameters** should produce visible effects
- **Camera movement** should create dramatic trailing

## 🔄 **Troubleshooting Workflow**

1. **Run Quick Test** → Identifies specific issues
2. **Fix identified issues** → Use Force Enable or manual fixes
3. **Test in Game View** → Verify motion vectors work
4. **Load Enhanced Preset** → Test enhanced trailing effects
5. **Verify results** → Should see smooth, fluid trailing

The debug tools will pinpoint exactly what's preventing motion vectors from working and provide specific solutions for each issue.
