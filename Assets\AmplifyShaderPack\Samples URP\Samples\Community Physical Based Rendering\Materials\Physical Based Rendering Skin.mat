%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Physical Based Rendering Skin
  m_Shader: {fileID: 4800000, guid: b9202a598bafa0c458e5d4738440b017, type: 3}
  m_ValidKeywords: []
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BumpMap:
        m_Texture: {fileID: 2800000, guid: c42b4b6e8fc553e43b916e18fde5a2ba, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MSOMROMap:
        m_Texture: {fileID: 2800000, guid: 2f6e6e9e8a2ccff408fb503d6741118e, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MSOMap:
        m_Texture: {fileID: 2800000, guid: 6de330a74472496419311e76a5b38da2, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainMaskMap:
        m_Texture: {fileID: 2800000, guid: 2f6e6e9e8a2ccff408fb503d6741118e, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: 32098fe4c0dbec04396928d03cf9bfa5, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecularMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _texcoord:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AlphaCutoff: 0.5
    - _AlphaCutoffBias: 1
    - _AlphaCutoffBiasShadow: 0.5
    - _Brightness: 1
    - _BumpScale: 1
    - _ConvertMaptoMRO: 1
    - _Cull: 0
    - _Cutoff: 0.5
    - _DetailNormalMapScale: 1
    - _DstBlend: 0
    - _EnvironmentReflections: 1
    - _Float2: 1
    - _GlancingClipMode: 0
    - _GlossMapScale: 1
    - _Glossiness: 0.5
    - _GlossyReflections: 1
    - _IndirectDiffuse: 0.659
    - _IndirectSpecular: 0.426
    - _IndirectSpecularSmoothness: 0.253
    - _MainMaskType: 1
    - _MapType: 1
    - _MaskClipValue2: 0.5
    - _MaxA: 0.1
    - _MaxB: 0
    - _MaxC: 0
    - _Metallic: 0
    - _MetallicStrength: 0.449
    - _Mode: 0
    - _NDFAnistropic: -2
    - _NDFRoughness: 2.193
    - _NDFSmoothness: 0.694
    - _NormalStrength: 1
    - _OcclusionStrength: 1
    - _OcclusionStrengthAO: 0.11
    - _Parallax: 0.02
    - _QueueControl: 0
    - _QueueOffset: 0
    - _ReceiveShadows: 1
    - _Saturation: 0
    - _ShadowColorEnable: 0
    - _ShadowFalloff: 1
    - _ShadowOffset: -0.826
    - _ShadowSharpness: 0.824
    - _ShadowStrength: 0.307
    - _SmoothnessStrength: 0.879
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _UVSec: 0
    - _ZWrite: 1
    - __dirty: 1
    - _fresnelIOR: 1.5
    m_Colors:
    - _BaseColor: {r: 1, g: 1, b: 1, a: 0}
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _IndirectSpecularColor: {r: 1, g: 0.9568626, b: 0.8392157, a: 0}
    - _MainUVs: {r: 1, g: 1, b: 0, a: 0}
    - _ShadowColor: {r: 0.3113208, g: 0.3113208, b: 0.3113208, a: 0}
  m_BuildTextureStacks: []
