#if HLSL
    #define SAMPLE(texture, sampler, uv) SAMPLE_TEXTURE2D_X(texture, sampler, uv)
#else
    #define SAMPLE(texture, sampler, uv) UNITY_SAMPLE_SCREENSPACE_TEXTURE(texture, uv)
#endif

#ifdef BLOCK_SIZE_2
    #define BLOCK_SIZE 2
#endif
#ifdef BLOCK_SIZE_4
    #define BLOCK_SIZE 4
#endif
#ifdef BLOCK_SIZE_8
    #define BLOCK_SIZE 8
#endif
#ifdef BLOCK_SIZE_16
    #define BLOCK_SIZE 16
#endif
#ifdef BLOCK_SIZE_32
    #define BLOCK_SIZE 32
#endif
#ifndef BLOCK_SIZE
    #define BLOCK_SIZE 4
#endif

// Hash function used throughout the shader
float hash1(uint n)
{
    n++;
    n = (n << 13U) ^ n;
    n = n * (n * n * 15731U + 789221U) + 1376312589U;
    return float(n & uint(0x7fffffffU))/float(0x7fffffff);
}

// Luminance calculation for brightness-based masking
float CalculateLuminance(float3 color)
{
    return dot(color, float3(0.2126, 0.7152, 0.0722));
}

// Brightness-based noise masking function
float CalculateBrightnessMask(float3 color, float brightnessThreshold, float maskingStrength)
{
    float luminance = CalculateLuminance(color);
    float brightnessFactor = saturate((luminance - brightnessThreshold) / (1.0 - brightnessThreshold));
    return lerp(1.0, 1.0 - maskingStrength, brightnessFactor);
}

// Enhanced Motion Vector Processing
// Implements coordinate transformation and pixelated noise generation
float2 ProcessMotionVectorCoordinates(float2 baseUV, float2 motionVector, float intensity)
{
    // Guide's coordinate transformation approach
    // Add motion vector offset to base UV coordinates
    float2 processedMotion = motionVector * intensity;
    return baseUV + processedMotion;
}

float4 GeneratePixelatedNoise(float2 uv, float pixelationScale, float noiseScale)
{
    // Guide's pixelated noise system implementation
    // Create stepped/pixelated coordinates
    float2 pixelatedUV = floor(uv * pixelationScale) / pixelationScale;

    // Generate noise using pixelated coordinates
    float noise = hash1(uint(pixelatedUV.x * 1000.0 + pixelatedUV.y * 2000.0 + _Time.y * noiseScale));
    return float4(noise, noise, noise, 1.0);
}

float2 EnhancedMotionVectorSampling(float2 uv, float2 motionVector, float blendFactor)
{
    // Guide's motion vector blending approach
    // Blend between base UV and motion-offset UV
    float2 motionOffsetUV = ProcessMotionVectorCoordinates(uv, motionVector, 1.0);
    return lerp(uv, motionOffsetUV, blendFactor);
}

//
float basis1D(float k, float i)
{
    float4 _G = float4(2, 1, 2, 2);
    float _Contrast = 0.0;
    return k == 0 ? sqrt(1. / float(BLOCK_SIZE)) : sqrt((_G.w + _Contrast) / float(BLOCK_SIZE)) * cos(float((_G.x * float(i) + _G.y) * k) * 3.14159265358 / (_G.z * float(BLOCK_SIZE)));
}
float basis2D(float2 jk, float2 xy)
{
    return basis1D(jk.x, xy.x) * basis1D(jk.y, xy.y);
}
float4 jpg(float2 uv, int m)
{
    // JPEG quality calculation - use user-controlled quality with JPG Bitcrunch fallback
    float _Quality = (_JPEGQuality > 0.001) ? (_JPEGQuality / 25.0) : 4.0; // Default to JPG Bitcrunch value if not set
    float quality = length(float2(_Quality, _Quality)); // Exact same calculation as JPG Bitcrunch
    float4 outColor = float4(0, 0, 0, 1);

    float2 textureSize = _Downscaled_TexelSize.zw;
    textureSize = floor(textureSize / 2.0) * 2.0;

    float2 coords = int2(textureSize * uv);
    float2 inBlock = coords % BLOCK_SIZE - m * 0.5;
    float2 block = coords - inBlock;

    // EXACT DCT implementation like JPG Bitcrunch - no complex features
    [loop]
    for (int2 xy = 0; xy.x < BLOCK_SIZE; xy.x++)
    {
        [loop]
        for (xy.y = 0; xy.y < BLOCK_SIZE; xy.y++)
        {
            outColor += SAMPLE(_Input, sampler_LinearClamp, float2(block + xy) / textureSize)
                            * basis2D(lerp(inBlock, xy, m), lerp(inBlock, xy, 1.0 - m));
        }
    }

    // Much more aggressive frequency filtering like JPG Bitcrunch
    outColor *= lerp(step(length(float2(inBlock)), quality), 1.0, m);
    return outColor;
}
//


float4 Downscale_Frag(Varyings input) : SV_Target
{
    return SAMPLE(_Input, sampler_LinearClamp, input.uv);
}

float4 Encode_Frag(Varyings input) : SV_Target
{
    float4 col = jpg(input.uv, 0);

    // 5. Multi-Scale Corruption - Different corruption at multiple scales
    if (_MultiScaleCorruption > 0.001)
    {
        #ifdef MOBILE_OPTIMIZED
            // Mobile optimization: Only use 2 scales instead of 3
            float2 mediumBlockPos = floor(input.uv * _Downscaled_TexelSize.zw / 8.0);
            float mediumCorruption = hash1(uint(mediumBlockPos.x * 333 + mediumBlockPos.y * 444 + _Time.y * 4.0));

            float2 smallBlockPos = floor(input.uv * _Downscaled_TexelSize.zw / 2.0);
            float smallCorruption = hash1(uint(smallBlockPos.x * 555 + smallBlockPos.y * 666 + _Time.y * 5.0));

            if (mediumCorruption < _MultiScaleCorruption * 0.6)
            {
                float brightnessCorruption = (hash1(uint(mediumBlockPos.x * 999 + mediumBlockPos.y * 111)) - 0.5) * _MultiScaleCorruption * 0.2;
                col.rgb *= (1.0 + brightnessCorruption);
            }

            if (smallCorruption < _MultiScaleCorruption * 0.8)
            {
                float3 noise = float3(
                    hash1(uint(smallBlockPos.x * 123 + smallBlockPos.y * 456)),
                    hash1(uint(smallBlockPos.x * 456 + smallBlockPos.y * 789)),
                    hash1(uint(smallBlockPos.x * 789 + smallBlockPos.y * 123))
                ) - 0.5;
                col.rgb += noise * _MultiScaleCorruption * 0.05;
            }
        #else
            // Desktop: Full 3-scale corruption
            float2 largeBlockPos = floor(input.uv * _Downscaled_TexelSize.zw / 32.0);
            float largeCorruption = hash1(uint(largeBlockPos.x * 111 + largeBlockPos.y * 222 + _Time.y * 3.0));

            float2 mediumBlockPos = floor(input.uv * _Downscaled_TexelSize.zw / 8.0);
            float mediumCorruption = hash1(uint(mediumBlockPos.x * 333 + mediumBlockPos.y * 444 + _Time.y * 4.0));

            float2 smallBlockPos = floor(input.uv * _Downscaled_TexelSize.zw / 2.0);
            float smallCorruption = hash1(uint(smallBlockPos.x * 555 + smallBlockPos.y * 666 + _Time.y * 5.0));

            if (largeCorruption < _MultiScaleCorruption * 0.3)
            {
                col.rgb += (hash1(uint(largeBlockPos.x * 777 + largeBlockPos.y * 888)) - 0.5) * _MultiScaleCorruption * 0.2;
            }

            if (mediumCorruption < _MultiScaleCorruption * 0.5)
            {
                float brightnessCorruption = (hash1(uint(mediumBlockPos.x * 999 + mediumBlockPos.y * 111)) - 0.5) * _MultiScaleCorruption * 0.3;
                col.rgb *= (1.0 + brightnessCorruption);
            }

            if (smallCorruption < _MultiScaleCorruption * 0.7)
            {
                float3 noise = float3(
                    hash1(uint(smallBlockPos.x * 123 + smallBlockPos.y * 456)),
                    hash1(uint(smallBlockPos.x * 456 + smallBlockPos.y * 789)),
                    hash1(uint(smallBlockPos.x * 789 + smallBlockPos.y * 123))
                ) - 0.5;
                col.rgb += noise * _MultiScaleCorruption * 0.1;
            }
        #endif
    }

    // DCT Corruption - only when enabled (> 0) with brightness control
    if (_DCTCorruption > 0.001)
    {
        float2 blockPos = floor(input.uv * _Downscaled_TexelSize.zw / BLOCK_SIZE);
        float corruption = hash1(uint(blockPos.x * 123 + blockPos.y * 456 + _Time.y * 5.0));

        if (corruption < _DCTCorruption)
        {
            // Corrupt specific frequency components
            float2 freqPos = fmod(input.uv * _Downscaled_TexelSize.zw, BLOCK_SIZE);
            float freqWeight = length(freqPos - BLOCK_SIZE * 0.5) / (BLOCK_SIZE * 0.5);

            // Apply brightness-based masking to DCT corruption
            float brightnessMask = CalculateBrightnessMask(col.rgb, _BrightnessThreshold, _BrightAreaMasking);

            // Higher frequencies get more corruption (typical of compression artifacts)
            float corruptionAmount = freqWeight * _DCTCorruption * brightnessMask;
            float3 corruptionNoise = (hash1(uint(blockPos.x * 789 + blockPos.y * 123 + _Time.y * 7.0)) - 0.5) * corruptionAmount * 0.5;

            // Clamp corruption to prevent peak brightness
            corruptionNoise = min(corruptionNoise, _MaxNoiseBrightness - CalculateLuminance(col.rgb));
            col.rgb += corruptionNoise;
        }
    }

    // EXACT JPG Bitcrunch color crunching - this is the key visual effect!
    if(_ColorCrunch == 0.0) return col;
    #ifndef COLOR_CRUNCH_SKYBOX
        float depth = SAMPLE(_CameraDepthTexture, sampler_LinearClamp, input.uv).x;
        if (depth == 0.0 || depth == 1.0) return col;
    #endif

    // EXACT same truncation calculation as JPG Bitcrunch
    float truncation = log10(lerp(1.0, 0.0001, _ColorCrunch));
    col.rgb = round(col.rgb / truncation) * truncation;

    // Enhanced Feature: Additional JPEG quantization features
    if (_LuminanceQuantization > 0.0 || _ChrominanceQuantization > 0.0)
    {
        // Convert to YUV for separate quantization
        float3 yuv;
        yuv.x = dot(col.rgb, float3(0.299, 0.587, 0.114)); // Luminance
        yuv.y = dot(col.rgb, float3(-0.14713, -0.28886, 0.436)); // U (Cb)
        yuv.z = dot(col.rgb, float3(0.615, -0.51499, -0.10001)); // V (Cr)

        // Apply additional quantization
        if (_LuminanceQuantization > 0.0)
        {
            float lumaQuantization = log10(lerp(1.0, 0.001, _LuminanceQuantization));
            yuv.x = round(yuv.x / lumaQuantization) * lumaQuantization;
        }

        if (_ChrominanceQuantization > 0.0)
        {
            float chromaQuantization = log10(lerp(1.0, 0.001, _ChrominanceQuantization));
            yuv.yz = round(yuv.yz / chromaQuantization) * chromaQuantization;
        }

        // Convert back to RGB
        col.r = yuv.x + 1.13983 * yuv.z;
        col.g = yuv.x - 0.39465 * yuv.y - 0.58060 * yuv.z;
        col.b = yuv.x + 2.03211 * yuv.y;
        col.rgb = saturate(col.rgb);
    }

    return col;
}

float4 Decode_Frag(Varyings input) : SV_Target
{
    float4 col = jpg(input.uv, 1);
    col.a = 1.0;
    return col;
}


float4 Upscale_Pull_Frag(Varyings input) : SV_Target
{
    float2 uv = input.uv;
    
    float3 center = SAMPLE(_Input, sampler_LinearClamp, uv + _Downscaled_TexelSize.xy * float2(0, 0)).rgb;
    float3 col;
    if (_Sharpening > 0.0)
    {
        float3 up = SAMPLE(_Input, sampler_LinearClamp, uv + _Downscaled_TexelSize.xy * float2(0, 1)).rgb;
        float3 left = SAMPLE(_Input, sampler_LinearClamp, uv + _Downscaled_TexelSize.xy * float2(-1, 0)).rgb;
        float3 right = SAMPLE(_Input, sampler_LinearClamp, uv + _Downscaled_TexelSize.xy * float2(1, 0)).rgb;
        float3 down = SAMPLE(_Input, sampler_LinearClamp, uv + _Downscaled_TexelSize.xy * float2(0, -1)).rgb;
        _Sharpening *= 2.0;
        col = (1.0 + 4.0 * _Sharpening) * center - _Sharpening * (up + left + right + down);
    }
    else
    {
        col = center;
    }

    // MOTION VECTOR VISUALIZATION - Enable this to see motion vectors
    // #define VIZ_MOTION_VECTORS
    #ifdef VIZ_MOTION_VECTORS
        float2 mv = SAMPLE(_MotionVectorTexture, sampler_LinearClamp, uv).xy;
        // Scale motion vectors for visibility and show as color
        return 0.5 + float4(mv * 50.0, 0.0, 1.0);
    #endif

    #ifdef REPROJECTION
        // Motion Sampling: Different behavior for Pure Datamosh vs Enhanced Mode
        float2 snappedUV = (floor(uv / (_Downscaled_TexelSize.xy * BLOCK_SIZE)) + 0.5) * (_Downscaled_TexelSize.xy * BLOCK_SIZE);

        // Core Datamosh: Always use block-based sampling as base
        float2 motionSampleUV = snappedUV;
        int2 blockID = floor(uv / (_Downscaled_TexelSize.xy * BLOCK_SIZE));

        // Enhanced Mode: Optionally blend with per-pixel sampling for smooth trails
        if (_TrailSmoothness > 0.001)
        {
            float2 pixelUV = uv; // Per-pixel sampling for smooth trails
            float smoothnessFactor = _TrailSmoothness;
            motionSampleUV = lerp(snappedUV, pixelUV, smoothnessFactor);
            // Also apply smoothness to block ID calculation for smoother transitions
            blockID = floor(lerp(snappedUV, pixelUV, smoothnessFactor * 0.5) / (_Downscaled_TexelSize.xy * BLOCK_SIZE));
        }

        // Core Datamosh: Basic corruption control (always active)
        float corruptionMask = 1.0;
        float glitchTransition = 1.0;

        // Enhanced Feature: Corruption Mask - Control where corruption occurs
        #ifdef _CORRUPTIONMASK
            #if defined(SHADER_API_D3D11) || defined(SHADER_API_D3D12) || defined(SHADER_API_VULKAN) || defined(SHADER_API_METAL)
                // URP/HDRP style
                corruptionMask = SAMPLE_TEXTURE2D(_CorruptionMask, sampler_CorruptionMask, uv).r;
            #else
                // Built-in style
                corruptionMask = tex2D(_CorruptionMask, uv).r;
            #endif
            if (corruptionMask < 0.1) return float4(col, 1.0); // No corruption in masked areas
        #endif

        // Enhanced Feature: Glitch Transition - Animated corruption spread
        if (_GlitchTransition > 0.001)
        {
            float2 transitionCenter = float2(0.5, 0.5); // Could be animated
            float distanceFromCenter = length(uv - transitionCenter);
            float transitionRadius = _GlitchTransition * 2.0;
            glitchTransition = smoothstep(transitionRadius - 0.2, transitionRadius + 0.2, distanceFromCenter);
            if (glitchTransition < 0.1) return float4(col, 1.0); // Clean areas during transition
        }

        // Enhanced Feature: Keyframe Reset (I-Frame simulation) - only when enabled
        if (_KeyframeResetRate > 0.001)
        {
            float keyframeReset = hash1(uint(blockID.x * 789 + blockID.y * 456 + floor(_Time.y * 60.0)));
            if (keyframeReset < _KeyframeResetRate)
                return float4(col, 1.0); // Reset to clean frame
        }

        // ENHANCED Motion Vector Sampling
        // Declare motion vector variables at higher scope to avoid undeclared identifier errors
        float2 motionVector = float2(0, 0);
        float2 enhancedMotionVector = float2(0, 0);

        // Core Datamosh: Always sample motion vectors (JPG Bitcrunch base behavior)
        float2 baseMotionVector = SAMPLE(_MotionVectorTexture, sampler_LinearClamp, motionSampleUV).xy;
        motionVector = baseMotionVector;
        enhancedMotionVector = baseMotionVector;

        // Enhanced Feature: Coordinate transformation for enhanced motion processing
        if (_MotionAmplification > 0.001)
        {
            float transformIntensity = _MotionAmplification * 0.1; // Scale down for coordinate transform
            #ifdef CUSTOM_RT_ENHANCED
                // Custom RT Enhanced Mode: Double the transformation intensity
                transformIntensity *= 2.0;
            #endif

            float2 transformedUV = ProcessMotionVectorCoordinates(motionSampleUV, baseMotionVector, transformIntensity);
            float2 enhancedSample = SAMPLE(_MotionVectorTexture, sampler_LinearClamp, transformedUV).xy;

            // Blend enhanced sample with base motion vector
            if (_TrailSmoothness > 0.001)
            {
                motionVector = lerp(baseMotionVector, enhancedSample, _TrailSmoothness);
                enhancedMotionVector = motionVector;
            }
        }


        // Calculate camera motion magnitude using actual screen resolution
        float2 screenResolution = _Screen_TexelSize.zw;
        float motionMagnitude = length(motionVector * screenResolution);

        // Motion Processing System - Core + Enhanced Features
        float totalMotionAmplification = 1.0;

        // CONSOLIDATED MOTION PROCESSING SYSTEM
        // Single unified system that replaces all previous overlapping motion parameters
        if (motionMagnitude > _MotionThreshold)
        {
            // Master motion amplification with much stronger base multiplier
            float masterAmplification = _MotionAmplification * motionMagnitude * 15.0; // Increased from 10.0
            
            // Trail intensity directly affects motion amplification
            float trailAmplification = _TrailIntensity * motionMagnitude * 8.0;
            
            // Camera vs Object motion balance - unified approach
            // This replaces separate camera motion parameters with a single balance control
            float cameraMotionFactor = _CameraObjectMotionBalance;
            float objectMotionFactor = 1.0 - _CameraObjectMotionBalance;
            
            // Apply balanced motion scaling with threshold
            float balancedAmplification = (cameraMotionFactor * 2.0 + objectMotionFactor) * 
                                        smoothstep(_MotionThreshold, _MotionThreshold * 2.0, motionMagnitude) * 6.0;
            
            // Apply motion smoothing to prevent jittery effects
            float smoothingFactor = lerp(1.0, 0.3, _MotionSmoothing);
            
            // Combine all amplifications with smoothing
            totalMotionAmplification = 1.0 + (masterAmplification + trailAmplification + balancedAmplification) * smoothingFactor;
        }

        // Consolidated Motion Processing with Flow Spread
        if (_FlowSpread > 0.001 && motionMagnitude > 0.001)
        {
            // Calculate flow patterns that spread outward from motion centers
            float2 motionDirection = normalize(motionVector);
            float2 uvOffset = uv - 0.5;
            float motionAlignment = dot(normalize(uvOffset), motionDirection);

            // Enhanced flow spread calculation with stronger multipliers
            float spreadInfluence = _FlowSpread * motionMagnitude * (1.0 + motionAlignment) * 5.0; // Increased from 2.0
            totalMotionAmplification += spreadInfluence;
        }

        // Apply total amplification
        enhancedMotionVector *= totalMotionAmplification;

        // Enhanced Motion Vector Corruption with Pixelated Noise (works in all modes)
        if (_MotionVectorCorruption > 0.001)
        {
            // Original corruption method
            float2 corruption = float2(
                hash1(uint(blockID.x * 123 + blockID.y * 456 + _Time.y * 10.0)) - 0.5,
                hash1(uint(blockID.x * 456 + blockID.y * 789 + _Time.y * 10.0)) - 0.5
            ) * _MotionVectorCorruption * 0.1;

            // Pixelated noise enhancement with motion-based scale
            float pixelationScale = _MotionVectorCorruption * 100.0 + 10.0; // Motion-based scale with base value
            float4 pixelatedNoise = GeneratePixelatedNoise(uv, pixelationScale, 5.0);

            // Combine traditional corruption with pixelated noise using brightness controls
            float baseNoiseIntensity = _NoiseTransparency; // Use user-controlled transparency
            #ifdef PURE_DATAMOSH_MODE
                baseNoiseIntensity *= 0.5; // Reduced intensity in Pure mode
            #endif

            // Apply brightness-based masking to noise intensity
            float3 currentColor = SAMPLE(_Input, sampler_LinearClamp, uv).rgb;
            float brightnessMask = CalculateBrightnessMask(currentColor, _BrightnessThreshold, _BrightAreaMasking);
            float effectiveNoiseIntensity = baseNoiseIntensity * brightnessMask;

            // Clamp pixelated noise to prevent peak brightness
            float4 clampedPixelatedNoise = float4(min(pixelatedNoise.xyz, _MaxNoiseBrightness), pixelatedNoise.w);

            float2 enhancedCorruption = corruption + (clampedPixelatedNoise.xy - 0.5) * _MotionVectorCorruption * effectiveNoiseIntensity;
            enhancedMotionVector += enhancedCorruption;
        }

        // Use appropriate motion vector based on mode
        #ifndef PURE_DATAMOSH_MODE
            // Enhanced Mode: Use enhanced motion vector for additional features
            motionVector = enhancedMotionVector;
        #endif
        // Pure Datamosh Mode: Keep original motion vector unchanged

        #ifdef COMPRESSION_ARTIFACTS
            // Enhanced Compression Artifacts: Ringing and Mosquito Noise
            if (_RingingArtifacts > 0.001 || _MosquitoNoise > 0.001)
            {
                // Debug mode: Show compression artifacts in bright colors
                if (_DebugCompressionArtifacts > 0.5)
                {
                    // Bright red overlay to show where compression artifacts are active
                    col.rgb = lerp(col.rgb, float3(1.0, 0.0, 0.0), 0.3);
                }
                // Improved edge detection for artifact placement
                float3 up = SAMPLE(_Input, sampler_LinearClamp, uv + _Downscaled_TexelSize.xy * float2(0, 1)).rgb;
                float3 left = SAMPLE(_Input, sampler_LinearClamp, uv + _Downscaled_TexelSize.xy * float2(-1, 0)).rgb;
                float3 right = SAMPLE(_Input, sampler_LinearClamp, uv + _Downscaled_TexelSize.xy * float2(1, 0)).rgb;
                float3 down = SAMPLE(_Input, sampler_LinearClamp, uv + _Downscaled_TexelSize.xy * float2(0, -1)).rgb;

                // Enhanced edge detection with better sensitivity
                float edgeDetection = length(col - up) + length(col - left) + length(col - right) + length(col - down);
                edgeDetection *= _EdgeSensitivity;

                // Lower threshold for more visible artifacts and add base artifact level
                float artifactThreshold = 0.05; // Reduced from 0.1 for more sensitivity
                float baseArtifactLevel = 0.1; // Always apply some artifacts even without strong edges

                if (edgeDetection > artifactThreshold || baseArtifactLevel > 0.0)
                {
                    float effectiveEdgeStrength = max(edgeDetection, baseArtifactLevel);

                    // Enhanced Ringing artifacts: Overshoot around edges with brightness control
                    if (_RingingArtifacts > 0.001)
                    {
                        float ringingFrequency = 12.0; // Increased frequency for more visible ringing
                        float2 ringingPattern = sin(uv * ringingFrequency * 3.14159);

                        // Apply brightness-based masking to ringing artifacts
                        float brightnessMask = CalculateBrightnessMask(col.rgb, _BrightnessThreshold, _BrightAreaMasking);

                        // Multi-directional ringing for more authentic compression artifacts
                        float ringingIntensity = _RingingArtifacts * effectiveEdgeStrength * brightnessMask;
                        float3 ringingEffect = float3(
                            ringingPattern.x * ringingIntensity * 0.3,
                            ringingPattern.y * ringingIntensity * 0.2,
                            (ringingPattern.x + ringingPattern.y) * ringingIntensity * 0.1
                        );

                        // Clamp ringing effect to prevent peak brightness
                        ringingEffect = min(ringingEffect, _MaxNoiseBrightness - CalculateLuminance(col.rgb));
                        col.rgb += ringingEffect;
                    }

                    // Enhanced Mosquito noise: High-frequency artifacts with brightness control
                    if (_MosquitoNoise > 0.001)
                    {
                        float mosquitoFrequency = 20.0; // Higher frequency for more visible mosquito noise

                        // Multi-layer mosquito noise for more authentic effect
                        float mosquitoNoise1 = sin(uv.x * mosquitoFrequency + _Time.y * 15.0) *
                                             sin(uv.y * mosquitoFrequency + _Time.y * 18.0);
                        float mosquitoNoise2 = sin(uv.x * mosquitoFrequency * 1.3 + _Time.y * 12.0) *
                                             sin(uv.y * mosquitoFrequency * 0.7 + _Time.y * 22.0);

                        float combinedMosquitoNoise = (mosquitoNoise1 + mosquitoNoise2 * 0.5) / 1.5;

                        // Apply brightness-based masking to mosquito noise
                        float brightnessMask = CalculateBrightnessMask(col.rgb, _BrightnessThreshold, _BrightAreaMasking);

                        // Enhanced intensity calculation - now includes motion bonus but doesn't require it
                        float mosquitoIntensity = _MosquitoNoise * effectiveEdgeStrength * brightnessMask;
                        if (motionMagnitude > 0.001)
                        {
                            mosquitoIntensity *= (1.0 + motionMagnitude * 2.0); // Motion bonus
                        }

                        // Apply mosquito noise with brightness clamping
                        float3 mosquitoEffect = combinedMosquitoNoise * mosquitoIntensity * 0.15;
                        mosquitoEffect = min(mosquitoEffect, _MaxNoiseBrightness - CalculateLuminance(col.rgb));
                        col.rgb += mosquitoEffect;
                    }
                }
            }
        #endif

        // Enhanced Previous Frame Sampling (works in all modes)
        float2 baseOffsetUV = uv - enhancedMotionVector;



        // Declare pull variable at proper scope
        float3 pull;

        // Apply enhanced motion vector sampling
        if (_TrailSmoothness > 0.001 && _MotionAmplification > 0.001)
        {
            float2 enhancedOffsetUV = EnhancedMotionVectorSampling(uv, enhancedMotionVector, _TrailSmoothness);

            // Blend between base and enhanced sampling using coordinate transform intensity
            float blendStrength = _MotionAmplification * 0.02; // Scale down for blending
            #ifdef PURE_DATAMOSH_MODE
                blendStrength *= 0.5; // Reduced strength in Pure mode
            #endif

            float2 finalSampleUV = lerp(baseOffsetUV, enhancedOffsetUV, blendStrength);
            pull = SAMPLE(_PrevScreen, sampler_LinearClamp, finalSampleUV).rgb;
        }
        else
        {
            // Standard sampling when enhanced features are disabled
            pull = SAMPLE(_PrevScreen, sampler_LinearClamp, baseOffsetUV).rgb;
        }

        // Safety check for uninitialized previous frame data (like JPG Bitcrunch)
        if (all(pull == 0.0)) return float4(col, 1.0);

        // Debug: Uncomment to visualize when previous frame data is available
        // if (any(pull > 0.0)) return float4(1, 0, 1, 1); // Magenta = previous frame data available

        // Debug: Motion Vector Visualization
        // return 0.5 + float4(motionVector * 50.0, 0.0, 1.0); // Visualize motion vectors

        // Debug: Reprojection Threshold Visualization
        // float debugThreshold = _ReprojectPercent + min(length(motionVector * float2(1920, 1080)) * _ReprojectLengthInfluence, 0.7);
        // if (debugThreshold > 0.1) return float4(0, 1, 0, 1); // Green = high reprojection chance

        #ifndef PURE_DATAMOSH_MODE
            // Trail Persistence: Enhanced temporal trail effects with stronger multipliers
            if (_TrailPersistence > 0.001 && motionMagnitude > 0.001)
            {
                // Sample multiple points along the motion vector for persistence with increased intensity
                float3 persistentSample1 = SAMPLE(_PrevScreen, sampler_LinearClamp, uv - enhancedMotionVector * 0.3).rgb;
                float3 persistentSample2 = SAMPLE(_PrevScreen, sampler_LinearClamp, uv - enhancedMotionVector * 0.7).rgb;
                float3 persistentSample3 = SAMPLE(_PrevScreen, sampler_LinearClamp, uv - enhancedMotionVector * 1.2).rgb;
                float3 persistentSample4 = SAMPLE(_PrevScreen, sampler_LinearClamp, uv - enhancedMotionVector * 1.8).rgb;
                float3 persistentSample5 = SAMPLE(_PrevScreen, sampler_LinearClamp, uv - enhancedMotionVector * 2.5).rgb;
                float3 persistentSample6 = SAMPLE(_PrevScreen, sampler_LinearClamp, uv - enhancedMotionVector * 3.2).rgb;

                // Enhanced persistence strength with much stronger motion response
                float persistenceStrength = _TrailPersistence * (2.0 + motionMagnitude * 200.0); // Doubled base + stronger motion scaling
                float3 persistentAverage = (persistentSample1 + persistentSample2 + persistentSample3 + persistentSample4 + persistentSample5 + persistentSample6) / 6.0;

                // Enhanced blending with stronger motion-based influence
                pull = lerp(pull, persistentAverage, saturate(persistenceStrength));
            }

            // Note: Temporal accumulation now integrated into Trail Persistence above for consolidated control
        #endif

        #ifndef PURE_DATAMOSH_MODE
            // Enhanced corruption features only in non-pure mode

            // 3. Chroma Corruption - Separate RGB channel corruption with brightness control
            if (_ChromaCorruption > 0.001)
            {
                // Apply brightness-based masking to chroma corruption
                float3 currentColor = SAMPLE(_Input, sampler_LinearClamp, uv).rgb;
                float brightnessMask = CalculateBrightnessMask(currentColor, _BrightnessThreshold, _BrightAreaMasking);

                float effectiveChromaCorruption = _ChromaCorruption * brightnessMask;

                float2 chromaOffset1 = float2(
                    hash1(uint(blockID.x * 234 + blockID.y * 567 + _Time.y * 8.0)) - 0.5,
                    hash1(uint(blockID.x * 567 + blockID.y * 890 + _Time.y * 8.0)) - 0.5
                ) * effectiveChromaCorruption * 0.02;

                float2 chromaOffset2 = float2(
                    hash1(uint(blockID.x * 345 + blockID.y * 678 + _Time.y * 9.0)) - 0.5,
                    hash1(uint(blockID.x * 678 + blockID.y * 901 + _Time.y * 9.0)) - 0.5
                ) * effectiveChromaCorruption * 0.02;

                // Sample RGB channels with different offsets
                float3 chromaCorruptedSample = float3(
                    SAMPLE(_PrevScreen, sampler_LinearClamp, uv - motionVector + chromaOffset1).r,
                    SAMPLE(_PrevScreen, sampler_LinearClamp, uv - motionVector).g,
                    SAMPLE(_PrevScreen, sampler_LinearClamp, uv - motionVector + chromaOffset2).b
                );

                // Clamp chroma corrupted result to prevent peak brightness
                chromaCorruptedSample = min(chromaCorruptedSample, _MaxNoiseBrightness);
                pull = chromaCorruptedSample;
            }

            // 4. Feedback Loops - Route output back as input
            if (_FeedbackIntensity > 0.001)
            {
                float feedbackNoise = hash1(uint(blockID.x * 456 + blockID.y * 789 + _Time.y * 12.0));
                if (feedbackNoise < _FeedbackIntensity)
                {
                    // Create feedback by sampling from a slightly offset position
                    float2 feedbackOffset = float2(
                        hash1(uint(blockID.x * 111 + blockID.y * 222 + _Time.y * 6.0)) - 0.5,
                        hash1(uint(blockID.x * 222 + blockID.y * 333 + _Time.y * 6.0)) - 0.5
                    ) * 0.05;

                    float3 feedbackSample = SAMPLE(_PrevScreen, sampler_LinearClamp, uv + feedbackOffset).rgb;
                    pull = lerp(pull, feedbackSample, _FeedbackIntensity * 0.5);
                }
            }
        #endif

        #ifdef PURE_DATAMOSH_MODE
            // Pure Datamosh Mode: Simple reprojection calculation like JPG Bitcrunch
            // No corruption mask or transition effects - just pure datamoshing
            float reprojectChance = _ReprojectPercent + min(length(motionVector * float2(1920, 1080)) * _ReprojectLengthInfluence, 0.7);
        #else
            // Enhanced Mode: Complex reprojection with all features

            // Apply corruption mask and transition effects
            float finalCorruptionStrength = corruptionMask * glitchTransition;

            // MASSIVE Motion-Driven Reprojection Logic
            // Use actual screen resolution with MASSIVE motion scaling
            float motionInfluence = length(enhancedMotionVector * screenResolution) * _ReprojectLengthInfluence * 10.0; // 10x stronger

            // Remove limits - let motion drive everything
            motionInfluence = min(motionInfluence, 50.0); // MASSIVE increase from 2.0

            // CONSOLIDATED reprojection chance using unified motion system
            float consolidatedMotionContribution = 0.0;
            if (motionMagnitude > _MotionThreshold)
            {
                // Use the same unified motion processing for reprojection
                float motionAboveThreshold = motionMagnitude - _MotionThreshold;
                
                // Apply motion amplification with trail intensity
                consolidatedMotionContribution = motionAboveThreshold * _MotionAmplification * _TrailIntensity * 25.0;
                
                // Apply camera/object balance to reprojection
                float balanceMultiplier = lerp(1.0, 2.5, _CameraObjectMotionBalance); // Camera motion gets stronger influence
                consolidatedMotionContribution *= balanceMultiplier;
                
                // Cap at reasonable maximum
                consolidatedMotionContribution = min(consolidatedMotionContribution, 40.0);
            }

            // Combine all influences with enhanced motion response
            float reprojectChance = (_ReprojectPercent + motionInfluence + consolidatedMotionContribution) * finalCorruptionStrength;
        #endif

        // FIXED: Trail Smoothness now has dramatic impact on blending behavior
        float blendFactor = reprojectChance;

        #ifdef PURE_DATAMOSH_MODE
            // Pure Datamosh Mode: EXACT JPG Bitcrunch behavior - simple, direct, aggressive
            // Use the exact same logic as JPG Bitcrunch for authentic datamoshing
            float reprojectThreshold = _ReprojectPercent + min(length(motionVector * float2(1920, 1080)) * _ReprojectLengthInfluence, 0.7);

            // CRITICAL FIX: Remove explicit uint() cast to match JPG Bitcrunch exactly
            if (hash1((123 + blockID.x) * (456 + blockID.y) + (_Time.y * _ReprojectSpeed)) < reprojectThreshold)
            {
                // Direct reprojection like JPG Bitcrunch - no blending, no accumulation
                return float4(pull, 1.0);
            }
        #else
            // Enhanced Mode: Trail smoothness and enhanced blending
            // FIXED: Lower threshold for smooth mode and stronger blending
            if (_TrailSmoothness > 0.1) // Much lower threshold
            {
                // FIXED: Smooth mode with much stronger blending
                float smoothBlend = saturate(blendFactor * (1.0 + _TrailSmoothness * 3.0)); // Much stronger

                // FIXED: Trail Smoothness affects the blending strength directly
                float trailStrength = _TrailSmoothness * (1.0 + motionMagnitude);

                // Enhanced blending with temporal accumulation
                if (_ErrorAccumulation > 0.001)
                {
                    float3 accumulated = lerp(col, pull, _ErrorAccumulation * smoothBlend * (1.0 + trailStrength));
                    return float4(accumulated, 1.0);
                }
                else
                {
                    // FIXED: Much stronger smooth reprojection blending
                    float3 smoothResult = lerp(col, pull, smoothBlend * (0.5 + trailStrength));
                    return float4(smoothResult, 1.0);
                }
            }
            else
            {
                // Block mode: Use original random threshold approach
                if (hash1(uint((123 + blockID.x) * (456 + blockID.y) + (_Time.y * _ReprojectSpeed))) < reprojectChance)
                {
                    // Error Accumulation - only when enabled, otherwise use original behavior
                    if (_ErrorAccumulation > 0.001)
                    {
                        float3 accumulated = lerp(col, pull, _ErrorAccumulation);
                        return float4(accumulated, 1.0);
                    }
                    else
                    {
                        // Original behavior - direct reprojection
                        return float4(pull, 1.0);
                    }
                }
            }
        #endif
    #endif
    
    return float4(col, 1.0);
}

float4 CopyToPrev_Frag(Varyings input) : SV_Target
{
    return SAMPLE(_Input, sampler_LinearClamp, input.uv);
}
