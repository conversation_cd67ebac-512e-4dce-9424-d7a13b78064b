%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7be522ae007ba5a4f87ffaaf2e793f4c, type: 3}
  m_Name: VHS Datamosh
  m_EditorClassIdentifier: 
  presetName: VHS Datamosh
  description: Classic analog VHS-style datamoshing with temporal trails, chroma corruption, and vintage video artifacts. Recreates the look of corrupted VHS tapes.
  category: Analog
  effectIntensity: 0.7
  colorCrunch: 0.6
  downscaling: 8
  blockSize: 2
  oversharpening: 0
  dontCrunchSkybox: 1
  onlyStenciled: 0
  reprojectBaseNoise: 0.25
  reprojectBaseRerollSpeed: 3
  reprojectLengthInfluence: 2.5
  cameraMotionAmplification: 1
  cameraMotionThreshold: 0.001
  cameraMotionInfluence: 2
  cameraMotionSmoothing: 0.1
  pixelFlowIntensity: 1
  trailSmoothness: 0.5
  motionPersistence: 0.3
  flowGradient: 1
  temporalAccumulation: 0.2
  keyframeResetRate: 0.05
  motionVectorCorruption: 1.2
  errorAccumulation: 0.6
  dctCorruption: 0
  corruptionMask: {fileID: 0}
  chromaCorruption: 0.4
  glitchTransition: 0
  feedbackIntensity: 0
  multiScaleCorruption: 0.3
  visualizeMotionVectors: 0
