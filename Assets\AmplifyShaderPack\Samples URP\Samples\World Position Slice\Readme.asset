%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e78934177bebe0545ab566bb787b0282, type: 3}
  m_Name: Readme
  m_EditorClassIdentifier: 
  Icon: {fileID: 2800000, guid: 465885576ce03e34fb9ecc147ff88eb0, type: 3}
  Title: World Position Slice
  Description:
    Heading: 
    Text: This sample performs alpha clip on specific intervals depending on world
      position.
    LinkText: 
    Url: 
  PropertiesHeader:
    Heading: Properties
    Text: 
    LinkText: 
    Url: 
  Properties:
  - Heading: Mask Clip Value
    Text: Reference value to which perform alpha clip.
    LinkText: 
    Url: 
  - Heading: Albedo
    Text: Albedo texture applied to surface.
    LinkText: 
    Url: 
  - Heading: Thickness
    Text: Size value for clipped areas.
    LinkText: 
    Url: 
  AdditionalProperties: []
  AdditionalScripts: []
  LoadedLayout: 0
  RPType: 3
