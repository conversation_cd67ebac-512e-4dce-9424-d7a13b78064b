%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7be522ae007ba5a4f87ffaaf2e793f4c, type: 3}
  m_Name: Strong Datamosh
  m_EditorClassIdentifier: 
  presetName: Strong Datamosh
  description: Strong JPG Bitcrunch-style datamoshing with prominent trailing and smearing effects. Designed to showcase authentic datamosh behavior with high motion sensitivity.
  category: True Datamosh
  effectIntensity: 1
  colorCrunch: 0.4
  downscaling: 6
  blockSize: 1
  oversharpening: 0.5
  dontCrunchSkybox: 0
  onlyStenciled: 0
  reprojectBaseNoise: 0.35
  reprojectBaseRerollSpeed: 5
  reprojectLengthInfluence: 3.5
  cameraMotionAmplification: 0
  cameraMotionThreshold: 0.001
  cameraMotionInfluence: 0
  cameraMotionSmoothing: 0.1
  pixelFlowIntensity: 0
  trailSmoothness: 0
  motionPersistence: 0
  flowGradient: 0
  temporalAccumulation: 0
  keyframeResetRate: 0
  motionVectorCorruption: 0
  errorAccumulation: 0
  dctCorruption: 0
  corruptionMask: {fileID: 0}
  chromaCorruption: 0
  glitchTransition: 0
  feedbackIntensity: 0
  multiScaleCorruption: 0
  jpegQuality: 100
  luminanceQuantization: 0
  chrominanceQuantization: 0
  chromaSubsampling: 0
  ringingArtifacts: 0
  mosquitoNoise: 0
  edgeSensitivity: 0.5

  visualizeMotionVectors: 0
