Shader "Stylo/Flux/CustomRT"
{
    Properties
    {
        // Unity6 Guide Properties
        _EffectIntensity ("Effect Intensity", Range(0, 2)) = 1.0
        _PixelationScale ("Pixelation Scale", Range(10, 500)) = 100.0
        _BlendFactor ("Blend Factor", Range(0, 1)) = 0.5
        _NoiseScale ("Noise Scale", Range(1, 50)) = 10.0
        _MotionAmplification ("Motion Amplification", Range(0, 10)) = 2.0
        
        // Global textures set by Flux
        _GlobalColorTexture ("Global Color Texture", 2D) = "white" {}
        _GlobalMotionTexture ("Global Motion Texture", 2D) = "black" {}
    }

    SubShader
    {
        Tags { "RenderType"="Opaque" "RenderPipeline"="UniversalPipeline" }
        
        Pass
        {
            Name "FluxCustomRT"
            
            HLSLPROGRAM
            #pragma vertex CustomRenderTextureVertexShader
            #pragma fragment frag
            #pragma target 3.0

            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #include "Packages/com.unity.render-pipelines.core/ShaderLibrary/CustomRenderTexture.hlsl"

            // Unity6 Guide Properties
            float _EffectIntensity;
            float _PixelationScale;
            float _BlendFactor;
            float _NoiseScale;
            float _MotionAmplification;

            // Brightness Control parameters
            float _NoiseTransparency;
            float _MaxNoiseBrightness;
            float _BrightnessThreshold;
            float _BrightAreaMasking;

            // Global textures from Flux
            TEXTURE2D(_GlobalColorTexture);
            SAMPLER(sampler_GlobalColorTexture);
            TEXTURE2D(_GlobalMotionTexture);
            SAMPLER(sampler_GlobalMotionTexture);

            // Unity6 Guide: Simple noise function
            float SimpleNoise(float2 uv)
            {
                return frac(sin(dot(uv, float2(12.9898, 78.233))) * 43758.5453);
            }

            // Luminance calculation for brightness-based masking
            float CalculateLuminance(float3 color)
            {
                return dot(color, float3(0.2126, 0.7152, 0.0722));
            }

            // Brightness-based noise masking function
            float CalculateBrightnessMask(float3 color, float brightnessThreshold, float maskingStrength)
            {
                float luminance = CalculateLuminance(color);
                float brightnessFactor = saturate((luminance - brightnessThreshold) / (1.0 - brightnessThreshold));
                return lerp(1.0, 1.0 - maskingStrength, brightnessFactor);
            }

            // Unity6 Guide: Pixelated UV generation
            float2 GetPixelatedUV(float2 uv, float pixelationScale)
            {
                float2 pixelated = uv * pixelationScale;
                pixelated = floor(pixelated);
                return pixelated / pixelationScale;
            }

            float4 frag(v2f_customrendertexture i) : SV_Target
            {
                float2 uv = i.localTexcoord.xy;
                
                // Unity6 Guide Step 1: Base Color Sampling
                float4 baseColor = SAMPLE_TEXTURE2D(_GlobalColorTexture, sampler_GlobalColorTexture, uv);
                
                // Unity6 Guide Step 2: Motion Vector Sampling
                float4 motionVector = SAMPLE_TEXTURE2D(_GlobalMotionTexture, sampler_GlobalMotionTexture, uv);
                
                // Unity6 Guide Step 3: Motion Vector Processing
                float2 processedMotion = motionVector.xy * _EffectIntensity * _MotionAmplification;
                
                // Unity6 Guide Step 4: Coordinate Transformation
                float2 motionOffsetUV = uv + processedMotion;
                
                // Unity6 Guide Step 5: Motion-Offset Color Sampling
                float4 motionOffsetColor = SAMPLE_TEXTURE2D(_GlobalColorTexture, sampler_GlobalColorTexture, motionOffsetUV);
                
                // Unity6 Guide Step 6: Color Blending
                float4 blendedColor = lerp(baseColor, motionOffsetColor, _BlendFactor);
                
                // Unity6 Guide Step 7: Pixelated Noise Generation
                float2 pixelatedUV = GetPixelatedUV(motionOffsetUV, _PixelationScale);
                float pixelatedNoise = SimpleNoise(pixelatedUV * _NoiseScale);

                // Unity6 Guide Step 8: Enhanced Noise Blend with Brightness Control
                // Calculate brightness mask to reduce noise in bright areas
                float brightnessMask = CalculateBrightnessMask(blendedColor.rgb, _BrightnessThreshold, _BrightAreaMasking);

                // Apply user-controlled noise transparency and brightness masking
                float effectiveNoiseIntensity = _NoiseTransparency * brightnessMask;
                float3 noiseColor = float3(pixelatedNoise, pixelatedNoise, pixelatedNoise);

                // Clamp noise to maximum brightness to prevent peak white
                noiseColor = min(noiseColor, _MaxNoiseBrightness);

                // Final blend with controlled intensity
                float4 finalColor = lerp(blendedColor, float4(noiseColor, 1.0), pixelatedNoise * effectiveNoiseIntensity);

                // Ensure final result doesn't exceed max brightness
                finalColor.rgb = min(finalColor.rgb, _MaxNoiseBrightness);

                // Unity6 Guide Step 9: Ensure proper alpha
                finalColor.a = 1.0;
                
                return finalColor;
            }
            ENDHLSL
        }
    }
}
