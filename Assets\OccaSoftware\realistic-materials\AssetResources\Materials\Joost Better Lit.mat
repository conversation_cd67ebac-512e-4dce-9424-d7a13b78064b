%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Joost Better Lit
  m_Shader: {fileID: 4800000, guid: 6d7dbbdf9194b6745ae79aaf2f3c053c, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords:
  - _ExcludeFromJPG
  m_InvalidKeywords:
  - _EMISSION
  - _EXCLUDE_FROM_JPG
  - _METALLICSPECGLOSSMAP
  - _NORMALMAP
  - _OCCLUSIONMAP
  - _PARALLAXMAP
  - _ReceiveFogEnabled
  - _ReceiveShadowsEnabled
  - _SubsurfaceEnabled
  m_LightmapFlags: 1
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: 2000
  stringTagMap:
    RenderType: Opaque
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BaseMap:
        m_Texture: {fileID: 2800000, guid: 3f6560971d9577f42ad69efb42abf347, type: 3}
        m_Scale: {x: 8, y: 4}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 2800000, guid: 223cbb5f4e77198418690a06f810f0f1, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _HeightMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _HeightMap2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 8, y: 4}
        m_Offset: {x: 0, y: 0}
    - _MainTex2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 2800000, guid: 4f6009f964eb7ab44a1c03e1adf81c92, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetalnessMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetalnessMap2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalMap2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 2800000, guid: f2f6b00311c54b1489f584497bdfa8ee, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 2800000, guid: 3525acc298d230e4b872c651a00f90db, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _RoughnessMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _RoughnessMap2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SubsurfaceThicknessMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AlphaClip: 0
    - _AlphaClipEnabled: 0
    - _AlphaToMask: 0
    - _Blend: 0
    - _BlendModePreserveSpecular: 1
    - _BumpScale: 1
    - _ClearCoatMask: 0
    - _ClearCoatSmoothness: 0
    - _Cull: 2
    - _Culling: 2
    - _Cutoff: 0.5
    - _DetailAlbedoMapScale: 1
    - _DetailNormalMapScale: 1
    - _DstBlend: 0
    - _DstBlendAlpha: 0
    - _EnvironmentReflections: 1
    - _ExcludeFromJPG: 1
    - _GlossMapScale: 0
    - _Glossiness: 0
    - _GlossyReflections: 0
    - _HasEmissionMap: 0
    - _HasEmissionMap2: 0
    - _HasHeightMap: 0
    - _HasHeightMap2: 0
    - _HasMetalnessMap: 0
    - _HasMetalnessMap2: 0
    - _HasNormalMap: 0
    - _HasNormalMap2: 0
    - _HasRoughnessMap: 0
    - _HasRoughnessMap2: 0
    - _HasSubsurfaceMap: 0
    - _HeightStrength: 0.02
    - _HeightStrength2: 0.02
    - _Metallic: 0
    - _Metalness: 0.183
    - _Metalness2: 0
    - _MetalnessMapExposure: 0
    - _MetalnessMapExposure2: 0
    - _NormalStrength: 1
    - _NormalStrength2: 1
    - _OcclusionStrength: 1
    - _OcclusionStrength2: 1
    - _Parallax: 0.005
    - _QueueOffset: 0
    - _ReceiveFogEnabled: 1
    - _ReceiveShadows: 1
    - _ReceiveShadowsEnabled: 1
    - _Roughness: 1
    - _Roughness2: 0.5
    - _RoughnessMapExposure: 0
    - _RoughnessMapExposure2: 0
    - _Smoothness: 1
    - _SmoothnessTextureChannel: 0
    - _SortPriority: 0
    - _SpecularHighlights: 1
    - _Specularity: 0.167
    - _Specularity2: 0.5
    - _SrcBlend: 1
    - _SrcBlendAlpha: 1
    - _SubsurfaceAmbient: 0.056
    - _SubsurfaceDistortion: 0.837
    - _SubsurfaceEnabled: 1
    - _SubsurfaceFalloff: 2.56
    - _SubsurfaceThickness: 0.815
    - _Surface: 0
    - _UseVertexColors: 0
    - _WorkflowMode: 1
    - _ZTest: 4
    - _ZWrite: 1
    m_Colors:
    - _BaseColor: {r: 1, g: 0.06387081, b: 0, a: 1}
    - _BaseColor2: {r: 0.5, g: 0.5, b: 0.5, a: 1}
    - _Color: {r: 1, g: 0.06387079, b: 0, a: 1}
    - _Emission: {r: 0, g: 0, b: 0, a: 1}
    - _Emission2: {r: 1.4980391, g: 0, b: 0, a: 1}
    - _EmissionColor: {r: 0.75557226, g: 0, b: 0.28516302, a: 1}
    - _SpecColor: {r: 0.19999996, g: 0.19999996, b: 0.19999996, a: 1}
    - _SubsurfaceColor: {r: 16.94838, g: 16.94838, b: 16.94838, a: 1}
  m_BuildTextureStacks: []
  m_AllowLocking: 1
--- !u!114 &7486984406140925110
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0353a89b1f911e48b9e16bdc9f2e058, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 7
