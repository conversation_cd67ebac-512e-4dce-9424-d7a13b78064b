%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: Terrain 4 Layer
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor v1.9.3.4\n// Available at the
    Unity Asset Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=19304\nNode;AmplifyShaderEditor.CommentaryNode;973;-5696,-3888;Inherit;False;3623;3418;Snow
    ;3;968;971;972;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;1032;608,1440;Inherit;False;1364;835;Snow
    Normals;36;978;979;982;990;989;992;975;986;976;1005;1004;1017;1016;669;1007;1018;1019;1008;985;984;1021;1020;1023;1022;1025;1024;1026;1027;1028;1029;1030;1031;1015;988;987;991;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;974;560,-128;Inherit;False;1732;1123;Snow
    BaseColor;62;894;892;896;925;906;900;901;902;905;904;897;903;899;924;668;942;941;944;943;946;945;948;947;951;952;953;954;955;949;950;956;957;958;940;910;912;917;916;898;918;959;934;933;937;936;960;961;963;962;965;964;967;966;926;927;928;929;938;935;932;1038;1039;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;972;-5088,-1232;Inherit;False;2932;709;Snow
    Textures and Color;17;844;855;863;850;853;851;845;846;852;847;849;848;862;854;842;1078;839;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;971;-5648,-3808;Inherit;False;1412;723;Snow
    Splat;18;718;720;721;722;723;724;725;726;727;717;731;730;719;728;732;733;734;735;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;968;-4192,-3824;Inherit;False;2034.429;2532.228;Snow
    Splat Mask;80;711;729;747;748;749;750;751;752;753;754;756;757;762;763;764;779;780;781;782;783;784;785;786;787;801;802;803;804;805;806;807;808;809;788;810;794;817;834;793;833;790;791;789;836;835;772;773;774;776;771;813;815;824;814;823;778;744;741;737;796;831;832;797;795;770;760;759;758;761;755;767;715;818;800;799;777;969;812;970;811;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;697;-1984,-848;Inherit;False;965.0878;383.1604;Vertex
    Tangents;5;701;704;703;702;699;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;698;-1984,-1248;Inherit;False;958.1046;353.6467;Vertex
    Tangents;5;705;709;710;707;706;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;285;-4176,320;Inherit;False;451.0647;123.0272;Terrain
    Offset **for terrain control mapping;;0,0,0,1;push towards camera a bit, so that
    coord mismatch due to dynamic batching is not affecting us$Offset for CameraX
    0.001$Offset for CameraY 0.0001$$;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;302;-3314.626,206.4356;Inherit;False;244;105;weight;;0,0,0,1;weight
    = dot(splat_control, half4(1,1,1,1))@;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;303;-2892.198,243.0062;Inherit;False;277;100;Normalize
    weights;;0,0,0,1;splat_control /= (weight + 1e-3f)@;0;0\nNode;AmplifyShaderEditor.SamplerNode;414;-4326.265,618.3862;Inherit;True;Property;_TextureSample0;Texture
    Sample 0;35;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.WireNode;430;-4396.478,888.6406;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;431;-4395.478,677.6406;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;432;-4391.478,1077.641;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.SamplerNode;420;-4317.574,1353.878;Inherit;True;Property;_TextureSample6;Texture
    Sample 6;40;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.TextureCoordinatesNode;428;-4587.776,1276.831;Inherit;False;0;-1;2;3;2;SAMPLER2D;;False;0;FLOAT2;1,1;False;1;FLOAT2;0,0;False;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.GetLocalVarNode;365;-4542.978,1625.716;Inherit;False;327;SSNormal0;1;0;OBJECT;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.GetLocalVarNode;366;-4535.21,1838.577;Inherit;False;333;SSMask0;1;0;OBJECT;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.GetLocalVarNode;364;-4532.875,1415.613;Inherit;False;325;SSSplat0;1;0;OBJECT;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.SamplerNode;417;-4326.123,2094.626;Inherit;True;Property;_TextureSample3;Texture
    Sample 3;37;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.TextureCoordinatesNode;427;-4581.301,2018.732;Inherit;False;0;-1;2;3;2;SAMPLER2D;;False;0;FLOAT2;1,1;False;1;FLOAT2;0,0;False;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SamplerNode;419;-4326.124,2488.372;Inherit;True;Property;_TextureSample5;Texture
    Sample 5;39;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.GetLocalVarNode;349;-4538.876,2553.519;Inherit;False;333;SSMask0;1;0;OBJECT;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.GetLocalVarNode;347;-4535.688,2159.768;Inherit;False;325;SSSplat0;1;0;OBJECT;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.TextureCoordinatesNode;429;-4576.688,2735.466;Inherit;False;0;-1;2;3;2;SAMPLER2D;;False;0;FLOAT2;1,1;False;1;FLOAT2;0,0;False;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SamplerNode;425;-4327.032,3204.633;Inherit;True;Property;_TextureSample11;Texture
    Sample 11;45;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SamplerNode;424;-4328.032,3008.633;Inherit;True;Property;_TextureSample10;Texture
    Sample 10;44;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SamplerNode;423;-4330.032,2803.633;Inherit;True;Property;_TextureSample9;Texture
    Sample 9;43;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.GetLocalVarNode;383;-4535.587,2865.616;Inherit;False;325;SSSplat0;1;0;OBJECT;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.GetLocalVarNode;387;-4537.853,3270.219;Inherit;False;333;SSMask0;1;0;OBJECT;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.GetLocalVarNode;385;-4551.587,3076.616;Inherit;False;327;SSNormal0;1;0;OBJECT;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.TexturePropertyNode;318;-4819.397,1352.214;Inherit;True;Property;_Splat1;Splat1;9;1;[HideInInspector];Create;False;1;;0;0;False;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.TexturePropertyNode;314;-4825.497,2801.204;Inherit;True;Property;_Splat3;Splat3;21;1;[HideInInspector];Create;False;1;;0;0;False;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.TexturePropertyNode;310;-4822.514,3005.567;Inherit;True;Property;_Normal3;Normal3;22;1;[HideInInspector];Create;False;0;0;0;False;0;False;None;None;False;bump;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.TexturePropertyNode;324;-4813.986,618.611;Inherit;True;Property;_Splat0;Splat0;3;1;[HideInInspector];Create;False;0;0;0;False;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.RegisterLocalVarNode;325;-4567.715,710.0908;Inherit;False;SSSplat0;-1;True;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;327;-4571.883,917.6796;Inherit;False;SSNormal0;-1;True;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.CustomExpressionNode;276;-3017.645,106.7095;Float;False;#if
    !defined(SHADER_API_MOBILE) && defined(TERRAIN_SPLAT_ADDPASS)$\tclip(SplatWeight
    == 0.0f ? -1 : 1)@$#endif;1;Call;1;True;SplatWeight;FLOAT;0;In;;Float;False;SplatClip;False;False;0;;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.DotProductOpNode;278;-3177.738,116.3414;Inherit;False;2;0;COLOR;0,0,0,0;False;1;FLOAT4;0,0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;281;-2585.631,106.1094;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;295;-4326.669,25.06716;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.TexCoordVertexDataNode;296;-4559.991,26.89406;Inherit;False;0;2;0;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.TextureTransformNode;289;-4572.729,144.0501;Inherit;False;-1;False;1;0;SAMPLER2D;;False;2;FLOAT2;0;FLOAT2;1\nNode;AmplifyShaderEditor.BreakToComponentsNode;291;-4324.448,171.1747;Inherit;False;FLOAT2;1;0;FLOAT2;0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.RangedFloatNode;292;-4386.997,261.5105;Inherit;False;Constant;_OffsetCameraX;Offset
    CameraX;0;0;Create;False;0;0;0;False;0;False;0.001;-0.02;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;297;-4389.458,334.0909;Inherit;False;Constant;_OffsetCameraY;Offset
    CameraY;0;0;Create;False;0;0;0;False;0;False;0.0001;-0.02;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;290;-4168.201,123.1431;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;288;-4168.676,212.3935;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.DynamicAppendNode;293;-4026.405,123.6794;Inherit;False;FLOAT2;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;294;-3883.17,29.78156;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.VertexToFragmentNode;286;-3760.342,30.19623;Inherit;False;False;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.RangedFloatNode;275;-2795.199,170.2677;Float;False;Constant;_Normalizeweights;Normalize
    weights;4;0;Create;True;1;;0;0;False;0;False;0.001;0.001;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;448;-3574.511,-93.00976;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.StickyNoteNode;450;80,112;Inherit;False;376.1325;121;#ifdef
    _ALPHATEST_ON;;0,0,0,1;float hole = tex2D(_TerrainHolesTexture, uv).r@$clip(hole
    == 0.0f ? -1 : 1)@;0;0\nNode;AmplifyShaderEditor.SwizzleNode;470;-3966.949,1352.082;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;436;-3970.939,1445.235;Inherit;False;Splat1A;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;370;-3973.006,1700.472;Inherit;False;Mask1R;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;371;-3973.223,1773.129;Inherit;False;Mask1G;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;379;-3804.628,1351.338;Inherit;False;Splat1;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;412;-3974.223,1847.132;Inherit;False;Mask1B;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.StickyNoteNode;499;-832,1664;Inherit;False;225.5509;141.1876;Normal
    Strength;;0,0,0,1;_NormalScale0$_NormalScale1$_NormalScale2$_NormalScale3$;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;505;-96,1696;Inherit;False;608.4122;108.9153;Enable
    PerPixel Normal;;0,0,0,1;#pragma shader_feature_local _TERRAIN_INSTANCED_PERPIXEL_NORMAL;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;512;-544,1696;Inherit;False;244.7206;103.6938;to
    avoid nan after normalizing;;0,0,0,1;mixedNormal.z += 1e-5f@;0;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;341;-3964.502,827.8027;Inherit;False;Normal0;-1;True;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;334;-3967.711,948.2559;Inherit;False;Mask0R;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SwizzleNode;469;-3964.349,620.582;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;359;-3970.348,2412.838;Inherit;False;Mask2R;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;358;-3971.168,2485.594;Inherit;False;Mask2G;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;411;-3968.836,2559.897;Inherit;False;Mask2B;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SwizzleNode;472;-3971.193,2803.082;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;390;-3807.439,2802.311;Inherit;False;Splat3;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;440;-3965.336,2895.318;Inherit;False;Splat3A;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;398;-3967.718,3010.222;Inherit;False;Normal3;-1;True;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;388;-3966.236,3130.126;Inherit;False;Mask3R;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;389;-3967.592,3201.97;Inherit;False;Mask3G;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;413;-3967.784,3276.611;Inherit;False;Mask3B;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.UnpackScaleNormalNode;491;-1219.263,2048.274;Inherit;False;Tangent;2;0;FLOAT4;0,0,0,0;False;1;FLOAT;1;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.GetLocalVarNode;481;-1425.166,1500.039;Inherit;False;341;Normal0;1;0;OBJECT;;False;1;COLOR;0\nNode;AmplifyShaderEditor.GetLocalVarNode;477;-1429.288,2044.832;Inherit;False;398;Normal3;1;0;OBJECT;;False;1;COLOR;0\nNode;AmplifyShaderEditor.SummedBlendNode;473;-797.3369,1482.266;Inherit;False;5;0;COLOR;0,0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;3;FLOAT3;0,0,0;False;4;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;500;-983.2628,1613.247;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;501;-961.2628,1640.247;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;502;-939.2629,1672.247;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;474;-982.0718,1454.325;Inherit;False;26;Control;1;0;OBJECT;;False;1;COLOR;0\nNode;AmplifyShaderEditor.StickyNoteNode;583;-336,3184;Inherit;False;209.4512;189.7598;Smoothness;;0,0,0,1;_Smoothness0$_Smoothness1$_Smoothness2$_Smoothness3;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;584;-688,2464;Inherit;False;209.4512;189.7598;Metallic;;0,0,0,1;_Metallic0$_Metallic1$_Metallic2$_Metallic3;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;603;-365.2152,4050.987;Inherit;False;209.4512;189.7598;Occlusion;;0,0,0,1;;0;0\nNode;AmplifyShaderEditor.GetLocalVarNode;596;-548.3896,3873.027;Inherit;False;26;Control;1;0;OBJECT;;False;1;COLOR;0\nNode;AmplifyShaderEditor.WireNode;630;-537.1144,4023.654;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;631;-506.0751,4047.797;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;632;-480.2094,4078.837;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;482;-1427.365,1575.091;Half;False;Property;_NormalScale0;NormalScale0;5;1;[HideInInspector];Create;False;1;;0;0;False;0;False;1;1;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;483;-1428.751,1759.382;Half;False;Property;_NormalScale1;NormalScale1;11;1;[HideInInspector];Create;False;1;;0;0;False;0;False;1;1;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;484;-1430.585,1941.313;Half;False;Property;_NormalScale2;NormalScale2;17;1;[HideInInspector];Create;False;1;;0;0;False;0;False;1;1;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;485;-1436.374,2120.879;Half;False;Property;_NormalScale3;_NormalScale3;23;1;[HideInInspector];Create;False;1;;0;0;False;0;False;1;1;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.StickyNoteNode;633;-126.2138,3967.085;Inherit;False;301;101;Saturate;;0,0,0,1;Saturate
    final Occlusion to avoid possible nan in unity lighting ;0;0\nNode;AmplifyShaderEditor.SaturateNode;634;-128.5524,3895.759;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SamplerNode;415;-4326.265,826.3862;Inherit;True;Property;_TextureSample1;Texture
    Sample 1;35;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.TexturePropertyNode;326;-4816.021,824.8513;Inherit;True;Property;_Normal0;Normal0;4;1;[HideInInspector];Create;False;1;;0;0;False;0;False;None;None;False;bump;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.StickyNoteNode;635;-4816,32;Inherit;False;150;100;;;0,0,0,1;_Control
    ;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;636;-4324.75,507.3965;Inherit;False;149.3613;101.6721;;;0,0,0,1;_Splat0$_Normal0$_Mask0;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;637;-4324.121,1982.694;Inherit;False;149.3613;101.6721;;;0,0,0,1;_Splat2$_Normal2$_Mask2;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;638;-4311.699,1239.669;Inherit;False;149.3613;101.6721;;;0,0,0,1;_Splat1$_Normal1$_Mask1;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;639;-4323.158,2694.31;Inherit;False;149.3613;101.6721;;;0,0,0,1;_Splat3$_Normal3$_Mask3;0;0\nNode;AmplifyShaderEditor.CrossProductOpNode;703;-1680,-784;Inherit;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.DynamicAppendNode;704;-1520,-784;Inherit;False;FLOAT4;4;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;-1;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.CustomExpressionNode;706;-1920,-1168;Float;False;v.tangent.xyz
    = cross ( v.normal, float3( 0, 0, 1 ) )@$v.tangent.w = -1@;1;Call;0;CalculateTangentsStandard;True;False;0;;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;707;-1664,-1168;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitchByPipeline;710;-1504,-1152;Inherit;False;4;0;FLOAT3;0,0,0;False;3;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.StickyNoteNode;969;-3952,-3696;Inherit;False;394;151;WorldPosition;;0,0,0,1;used
    for the Altitude Mask and creates a grayscale (float) mask based on the height
    or altitude in the world.  ;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;970;-2816,-3776;Inherit;False;406.2698;157.4146;Min
    Max;;0,0,0,1;The gradient begins with an output value of zero at the Minimum
    height and ends with an output value of one at the Maximum height. $$Heights
    less than Minimum will always return zero and heights greater than Maximum will
    always return one.;0;0\nNode;AmplifyShaderEditor.TFHCRemapNode;711;-3184,-1504;Inherit;False;5;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;1;False;3;FLOAT;-1;False;4;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.PowerNode;729;-3152,-1712;Inherit;False;True;2;0;FLOAT;0;False;1;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.LerpOp;747;-2960,-1712;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;748;-3312,-1616;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;749;-3600,-1616;Half;False;Property;_SnowSplatABlendFalloff;Blend
    Falloff;62;0;Create;False;1;;0;0;False;0;False;0;0.01;0;10;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMaxOpNode;750;-3312,-1712;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;751;-3600,-1712;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;752;-3728,-1632;Inherit;False;2;2;0;FLOAT;1;False;1;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;756;-3312,-2192;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;757;-3616,-2192;Half;False;Property;_SnowSplatBBlendFalloff;Blend
    Falloff;55;0;Create;False;1;;0;0;False;0;False;0;0.01;0;10;0;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;762;-3824,-2288;Inherit;False;734;SnowSplatB;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;763;-3760,-2208;Inherit;False;2;2;0;FLOAT;1;False;1;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;779;-3312,-2752;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;780;-3600,-2752;Half;False;Property;_SnowSplatGBlendFalloff;Blend
    Falloff;48;0;Create;False;1;;0;0;False;0;False;0;0.01;0;10;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMaxOpNode;781;-3312,-2848;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.PowerNode;782;-3152,-2848;Inherit;False;True;2;0;FLOAT;0;False;1;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.LerpOp;783;-2960,-2848;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;784;-3600,-2848;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;785;-3824,-2848;Inherit;False;733;SnowSplatG;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;786;-3760,-2768;Inherit;False;2;2;0;FLOAT;1;False;1;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMaxOpNode;801;-3376,-3488;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.PowerNode;802;-3152,-3488;Inherit;False;True;2;0;FLOAT;0;False;1;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;803;-3408,-3376;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.LerpOp;804;-2976,-3488;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;805;-3696,-3376;Half;False;Property;_SnowSplatRBlendFalloff;Blend
    Falloff;41;0;Create;False;1;;0;0;False;0;False;0;0.01;0;10;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;806;-3680,-3488;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;807;-3888,-3488;Inherit;False;732;SnowSplatR;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;808;-3824,-3408;Inherit;False;2;2;0;FLOAT;1;False;1;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SaturateNode;788;-2784,-2848;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SaturateNode;810;-2784,-3488;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.LerpOp;794;-2592,-2880;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SmoothstepOpNode;834;-2816,-2432;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.LerpOp;793;-2592,-2336;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.BreakToComponentsNode;833;-2960,-2432;Inherit;False;FLOAT3;1;0;FLOAT3;0,0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.RangedFloatNode;790;-2992,-2512;Half;False;Property;_SnowSplatBMax;Max;53;0;Create;False;1;;0;0;False;0;False;1;0.5;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;791;-2992,-2592;Half;False;Property;_SnowSplatBMin;Min;52;0;Create;False;1;;0;0;False;0;False;-0.5;0.5;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;789;-3200,-2432;Inherit;False;812;WorldPosition;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SmoothstepOpNode;836;-2816,-1856;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.BreakToComponentsNode;835;-2960,-1856;Inherit;False;FLOAT3;1;0;FLOAT3;0,0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.GetLocalVarNode;772;-3200,-1856;Inherit;False;812;WorldPosition;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RangedFloatNode;773;-2992,-1936;Half;False;Property;_SnowSplatAMax;Max;60;0;Create;False;1;;0;0;False;0;False;1;0.5;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;774;-2992,-2016;Half;False;Property;_SnowSplatAMin;Min;59;0;Create;False;1;;0;0;False;0;False;-0.5;0.5;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SaturateNode;771;-2784,-1712;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;813;-3184,-3616;Inherit;False;812;WorldPosition;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RangedFloatNode;815;-2992,-3776;Half;False;Property;_SnowSplatRMin;Min;38;0;Create;False;1;;0;0;False;0;False;-0.5;0.5;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.BreakToComponentsNode;824;-2960,-3616;Inherit;False;FLOAT3;1;0;FLOAT3;0,0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.RangedFloatNode;814;-2992,-3696;Half;False;Property;_SnowSplatRMax;Max;39;0;Create;False;1;;0;0;False;0;False;1;0.5;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SmoothstepOpNode;823;-2816,-3616;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.TFHCRemapNode;778;-3200,-2656;Inherit;False;5;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;1;False;3;FLOAT;-1;False;4;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;744;-3552,-2656;Half;False;Property;_SnowSplatGBlendStrength;Blend
    Strength;47;0;Create;False;0;0;0;False;0;False;0;0;0;5;0;1;FLOAT;0\nNode;AmplifyShaderEditor.TFHCRemapNode;741;-3200,-3264;Inherit;False;5;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;1;False;3;FLOAT;-1;False;4;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;737;-3552,-3264;Half;False;Property;_SnowSplatRBlendStrength;Blend
    Strength;40;0;Create;False;0;0;0;False;0;False;0;0;0;5;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;796;-2992,-3056;Half;False;Property;_SnowSplatGMax;Max;46;0;Create;False;1;;0;0;False;0;False;1;0.5;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.BreakToComponentsNode;831;-2960,-2976;Inherit;False;FLOAT3;1;0;FLOAT3;0,0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.SmoothstepOpNode;832;-2816,-2976;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;797;-2992,-3136;Half;False;Property;_SnowSplatGMin;Min;45;0;Create;False;1;;0;0;False;0;False;-0.5;0.5;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;795;-3200,-2976;Inherit;False;812;WorldPosition;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SaturateNode;770;-2784,-2304;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.LerpOp;760;-2976,-2304;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.PowerNode;759;-3168,-2304;Inherit;False;True;2;0;FLOAT;0;False;1;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMaxOpNode;758;-3328,-2304;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;761;-3632,-2304;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.TFHCRemapNode;755;-3200,-2112;Inherit;False;5;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;1;False;3;FLOAT;-1;False;4;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;767;-3552,-2112;Half;False;Property;_SnowSplatBBlendStrength;Blend
    Strength;54;0;Create;False;0;0;0;False;0;False;0;0;0;5;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;715;-3552,-1504;Half;False;Property;_SnowSplatABlendStrength;Blend
    Strength;61;0;Create;False;0;0;0;False;0;False;0;0;0;5;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;818;-2400,-3504;Inherit;False;SnowSplatRMask;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;800;-2400,-2880;Inherit;False;SnowSplatGMask;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;799;-2400,-2336;Inherit;False;SnowSplatBMask;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;777;-2400,-1728;Inherit;False;SnowSplatAMask;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;718;-4992,-3520;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.DynamicAppendNode;723;-4816,-3616;Inherit;False;FLOAT4;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;724;-4992,-3616;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;725;-4992,-3424;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;726;-4992,-3328;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SamplerNode;717;-5312,-3632;Inherit;True;Property;_TextureSample15;Texture
    Sample 0;35;1;[SingleLineTexture];Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.RegisterLocalVarNode;733;-4480,-3552;Inherit;False;SnowSplatG;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;857;-3600,-672;Half;False;Property;_SnowNormalStrength;Normal
    Strength;35;0;Create;False;1;;0;0;False;0;False;2;1;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;850;-4144,-1152;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.RangedFloatNode;853;-4320,-1104;Inherit;False;Constant;_Float4;Float
    3;13;0;Create;True;0;0;0;False;0;False;100;300;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.VertexToFragmentNode;851;-4384,-1184;Inherit;False;False;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;845;-4512,-1184;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;846;-4688,-1184;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SwizzleNode;847;-4848,-1056;Inherit;False;FLOAT2;0;1;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SwizzleNode;849;-4688,-1008;Inherit;False;FLOAT2;2;3;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;858;-3072,-752;Inherit;False;SnowNormal;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;978;992,1856;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;979;992,1984;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;982;992,2112;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;990;1152,1872;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;989;1232,1856;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;992;1216,2112;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SummedBlendNode;975;1344,1712;Inherit;False;5;0;FLOAT4;0,0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;3;FLOAT3;0,0,0;False;4;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;976;992,1728;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;1005;1568,1584;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1004;1344,1888;Inherit;False;932;SnowEnable;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1017;1504,1568;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1018;880,1504;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1019;912,1536;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1021;912,1536;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1020;880,1504;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1023;912,1536;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1022;880,1504;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1025;912,1536;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1024;880,1504;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1026;928,2144;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1027;928,2112;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1028;928,2016;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1029;928,1984;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1030;928,1888;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1031;928,1872;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;988;1184,1808;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;987;1200,1840;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;991;1184,1984;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.TextureCoordinatesNode;730;-5600,-3760;Inherit;False;0;731;2;3;2;SAMPLER2D;;False;0;FLOAT2;1,1;False;1;FLOAT2;0,0;False;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.TexturePropertyNode;731;-5600,-3632;Inherit;True;Property;_SnowMapSplat;Splat
    Mask;28;1;[SingleLineTexture];Create;False;1;;0;0;False;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.RangedFloatNode;719;-5184,-3440;Half;False;Property;_SnowSplatRSplatBias;Splat
    Bias;37;0;Create;False;0;0;0;False;0;False;1;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;720;-5184,-3360;Half;False;Property;_SnowSplatGSplatBias;Splat
    Bias;44;0;Create;False;0;0;0;False;0;False;1;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;721;-5184,-3280;Half;False;Property;_SnowSplatBSplatBias;Splat
    Bias;51;0;Create;False;0;0;0;False;0;False;1;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;722;-5184,-3200;Half;False;Property;_SnowSplatASplatBias;Splat
    Bias;58;0;Create;False;0;0;0;False;0;False;1;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;809;-4128,-3376;Half;False;Property;_SnowSplatRBlendFactor;Blend
    Factor;42;0;Create;False;1;;0;0;False;0;False;0;0.01;0;10;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;787;-4080,-2736;Half;False;Property;_SnowSplatGBlendFactor;Blend
    Factor;49;0;Create;False;1;;0;0;False;0;False;0;0.01;0;10;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;764;-4064,-2176;Half;False;Property;_SnowSplatBBlendFactor;Blend
    Factor;56;0;Create;False;1;;0;0;False;0;False;0;0.01;0;10;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;754;-4032,-1600;Half;False;Property;_SnowSplatABlendFactor;Blend
    Factor;63;0;Create;False;1;;0;0;False;0;False;0;0.01;0;10;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SamplerNode;844;-3968,-1008;Inherit;True;Property;_TextureSample17;Texture
    Sample 2;6;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.RegisterLocalVarNode;863;-3968,-1088;Inherit;False;SSSnowMapBaseColor;-1;True;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.SamplerNode;855;-3968,-752;Inherit;True;Property;_TextureSample18;Texture
    Sample 4;60;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SwizzleNode;837;-3664,-1008;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.ColorNode;841;-2912,-1184;Half;False;Property;_SnowColor;Tint;29;0;Create;False;1;;0;0;False;0;False;1,1,1,0;0.3647059,0.5372549,0.3411765,0;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SwizzleNode;840;-2688,-1184;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1016;1472,1504;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1007;1120,1664;Inherit;False;728;SnowSplatRGBA;1;0;OBJECT;;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.GetLocalVarNode;985;672,1904;Inherit;False;896;SnowEnableGChannel;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1008;672,1776;Inherit;False;925;SnowEnableRChannel;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1015;736,1696;Inherit;False;858;SnowNormal;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;984;672,2032;Inherit;False;897;SnowEnableBChannel;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;986;672,2160;Inherit;False;899;SnowEnableAChannel;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;728;-4640,-3712;Inherit;False;SnowSplatRGBA;-1;True;1;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.BreakToComponentsNode;727;-4640,-3616;Inherit;False;FLOAT4;1;0;FLOAT4;0,0,0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.RegisterLocalVarNode;732;-4480,-3632;Inherit;False;SnowSplatR;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;734;-4480,-3472;Inherit;False;SnowSplatB;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;735;-4480,-3392;Inherit;False;SnowSplatA;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WorldPosInputsNode;811;-4160,-3776;Inherit;False;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.RegisterLocalVarNode;812;-3952,-3776;Inherit;False;WorldPosition;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;817;-2592,-3504;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;753;-3792,-1712;Inherit;False;735;SnowSplatA;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.LerpOp;776;-2592,-1728;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.UnpackScaleNormalNode;856;-3344,-752;Inherit;False;Tangent;2;0;FLOAT4;0,0,0,0;False;1;FLOAT;1;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.TexturePropertyNode;862;-4288,-1008;Inherit;True;Property;_SnowMapBaseColor;BaseColor;32;1;[SingleLineTexture];Create;False;0;0;0;False;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.TexCoordVertexDataNode;852;-5056,-1184;Inherit;False;0;2;0;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.Vector4Node;848;-5040,-1008;Inherit;False;Property;_SnowMainUVs;Main
    UVs;33;0;Create;False;0;0;0;False;0;False;0.002,0.002,0,0;0,0,0,0;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.RangedFloatNode;861;-3664,-880;Half;False;Property;_SnowSaturation;Saturation;31;0;Create;False;0;0;0;False;1;Space(5);False;0;0.65;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;859;-3376,-880;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;843;-2864,-928;Half;False;Property;_SnowBrightness;Brightness;30;0;Create;False;1;;0;0;False;0;False;1;1;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;839;-2544,-1040;Inherit;False;3;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionNode;1078;-3200,-1008;Inherit;False;Saturation;-1;;1;4f383aa3b2a7ef640be83276d286e709;0;2;12;FLOAT3;0,0,0;False;21;FLOAT;0.5;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.TextureCoordinatesNode;426;-4575.231,541.7663;Inherit;False;0;-1;2;3;2;SAMPLER2D;;False;0;FLOAT2;1,1;False;1;FLOAT2;0,0;False;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SummedBlendNode;602;-363.3203,3893.459;Inherit;False;5;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;333;-4569.231,1106.87;Inherit;False;SSMask0;-1;True;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;435;-3965.583,712.6168;Inherit;False;Splat0A;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.TexturePropertyNode;316;-4820.527,2093.342;Inherit;True;Property;_Splat2;Splat2;15;1;[HideInInspector];Create;False;1;;0;0;False;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.GetLocalVarNode;538;-1216,2400;Inherit;False;334;Mask0R;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;539;-1216,2560;Inherit;False;370;Mask1R;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;540;-1216,2720;Inherit;False;359;Mask2R;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;541;-1216,2880;Inherit;False;388;Mask3R;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;537;-864,2272;Inherit;False;26;Control;1;0;OBJECT;;False;1;COLOR;0\nNode;AmplifyShaderEditor.SummedBlendNode;536;-672,2288;Inherit;False;5;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;476;-1426.607,1684.735;Inherit;False;378;Normal1;1;0;OBJECT;;False;1;COLOR;0\nNode;AmplifyShaderEditor.TexturePropertyNode;317;-4816.875,1560.946;Inherit;True;Property;_Normal1;Normal1;10;1;[HideInInspector];Create;False;1;;0;0;False;0;False;None;None;False;bump;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.SamplerNode;421;-4320,1568;Inherit;True;Property;_TextureSample7;Texture
    Sample 7;41;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.RegisterLocalVarNode;378;-3968,1568;Inherit;False;Normal1;-1;True;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.NormalVertexDataNode;705;-1872,-1088;Inherit;False;0;5;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.NormalVertexDataNode;702;-1904,-784;Inherit;False;0;5;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.Vector3Node;699;-1904,-640;Inherit;False;Constant;_Vector7;Vector
    7;234;0;Create;True;0;0;0;False;0;False;0,0,1;0,0,1;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.RegisterLocalVarNode;842;-2384,-1040;Inherit;False;SnowBaseColor;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;342;-3808,624;Inherit;False;Splat0;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMaxOpNode;1110;-992,2320;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMaxOpNode;1111;-992,2480;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMaxOpNode;1112;-992,2640;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMaxOpNode;1113;-992,2800;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;409;-3968.333,1020.522;Inherit;False;Mask0G;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;410;-3967.698,1093.529;Inherit;False;Mask0B;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SwizzleNode;471;-3968.949,2094.082;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;357;-3802.056,2092.957;Inherit;False;Splat2;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;438;-3969.931,2184.846;Inherit;False;Splat2A;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SamplerNode;416;-4320,1024;Inherit;True;Property;_TextureSample2;Texture
    Sample 2;36;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SamplerNode;422;-4326.506,1770.675;Inherit;True;Property;_TextureSample8;Texture
    Sample 8;42;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.TexturePropertyNode;332;-4816,1024;Inherit;True;Property;_Mask0;Mask0;6;1;[HideInInspector];Create;False;1;;0;0;False;0;False;None;None;False;gray;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.TexturePropertyNode;307;-4816,1776;Inherit;True;Property;_Mask1;Mask1;12;1;[HideInInspector];Create;False;1;;0;0;False;0;False;None;None;False;gray;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.TexturePropertyNode;311;-4816,2480;Inherit;True;Property;_Mask2;Mask2;18;1;[HideInInspector];Create;False;1;;0;0;False;0;False;None;None;False;gray;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.TexturePropertyNode;309;-4819.546,3203.908;Inherit;True;Property;_Mask3;Mask3;24;1;[HideInInspector];Create;False;0;0;0;False;0;False;None;None;False;gray;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.UnpackScaleNormalNode;490;-1217.233,1504.807;Inherit;False;Tangent;2;0;FLOAT4;0,0,0,0;False;1;FLOAT;1;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.UnpackScaleNormalNode;496;-1217.833,1688.274;Inherit;False;Tangent;2;0;FLOAT4;0,0,0,0;False;1;FLOAT;1;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.UnpackScaleNormalNode;494;-1221.295,1869.847;Inherit;False;Tangent;2;0;FLOAT4;0,0,0,0;False;1;FLOAT;1;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.GetLocalVarNode;480;-1427.343,1864.297;Inherit;False;356;Normal2;1;0;OBJECT;;False;1;COLOR;0\nNode;AmplifyShaderEditor.SamplerNode;418;-4326.769,2295.66;Inherit;True;Property;_TextureSample4;Texture
    Sample 4;38;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.GetLocalVarNode;348;-4545.819,2365.147;Inherit;False;327;SSNormal0;1;0;OBJECT;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.TexturePropertyNode;308;-4817.57,2291.421;Inherit;True;Property;_Normal2;Normal2;16;1;[HideInInspector];Create;False;1;;0;0;False;0;False;None;None;False;bump;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.RegisterLocalVarNode;356;-3967.32,2297.206;Inherit;False;Normal2;-1;True;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.FunctionSwitch;669;1728,1472;Inherit;False;Option;False;0;2;2;In
    0;In 1;Instance;668;10;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.TexturePropertyNode;854;-4288,-752;Inherit;True;Property;_SnowMapNormal;Normal
    Map;34;2;[Normal];[SingleLineTexture];Create;False;0;0;0;False;0;False;None;None;False;bump;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.StaticSwitch;503;-80,1568;Inherit;False;Property;_EnableInstancedPerPixelNormal;Enable
    Instanced Per-Pixel Normal;0;0;Create;False;0;0;0;False;0;False;0;0;0;True;_TERRAIN_INSTANCED_PERPIXEL_NORMAL;Toggle;2;Key0;Key1;Create;True;False;All;9;1;FLOAT3;0,0,0;False;0;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;3;FLOAT3;0,0,0;False;4;FLOAT3;0,0,0;False;5;FLOAT3;0,0,0;False;6;FLOAT3;0,0,0;False;7;FLOAT3;0,0,0;False;8;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.BreakToComponentsNode;513;-512,1488;Inherit;False;FLOAT3;1;0;FLOAT3;0,0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.SimpleAddOpNode;515;-384,1568;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.DynamicAppendNode;514;-256,1488;Inherit;False;FLOAT3;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RangedFloatNode;516;-576,1616;Float;False;Constant;_Float7;Float
    7;9;0;Create;True;0;0;0;False;0;False;0.001;0.001;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;504;336,1488;Inherit;False;Enable
    PerPixel Normals;True;1;2;0;In 0;In 1;Object;-1;10;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RangedFloatNode;48;-1312,2320;Float;False;Property;_Metallic0;Metallic0;7;1;[HideInInspector];Create;True;0;0;0;False;0;False;0;0;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;49;-1312,2480;Float;False;Property;_Metallic1;Metallic1;13;1;[HideInInspector];Create;True;0;0;0;False;0;False;0;0;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;50;-1312,2640;Float;False;Property;_Metallic2;Metallic2;19;1;[HideInInspector];Create;True;0;0;0;False;0;False;0;0;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;51;-1312,2800;Float;False;Property;_Metallic3;Metallic3;25;1;[HideInInspector];Create;True;0;0;0;False;0;False;0;0;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1175;-848,2416;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1176;-816,2448;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1177;-784,2480;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1179;-832,2640;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1178;-800,2784;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;579;-1456,3072;Float;False;Property;_Smoothness0;Smoothness
    0;8;1;[HideInInspector];Create;False;0;0;0;False;0;False;0;0.077;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;580;-1456,3152;Float;False;Property;_Smoothness1;Smoothness1;14;1;[HideInInspector];Create;False;0;0;0;False;0;False;0;0.077;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;581;-1456,3232;Float;False;Property;_Smoothness2;Smoothness2;20;1;[HideInInspector];Create;False;0;0;0;False;0;False;0;0.077;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;582;-1456,3312;Float;False;Property;_Smoothness3;Smoothness3;26;1;[HideInInspector];Create;False;0;0;0;False;0;False;0;0.077;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.DynamicAppendNode;1168;-1088,3072;Inherit;False;FLOAT4;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.WireNode;1170;-1168,3152;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1171;-1152,3184;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1172;-1136,3200;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1132;-1216,3392;Inherit;False;435;Splat0A;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.DynamicAppendNode;1169;-960,3392;Inherit;False;FLOAT4;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.WireNode;1181;-784,3392;Inherit;False;1;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.WireNode;1180;-784,3152;Inherit;False;1;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.GetLocalVarNode;561;-544,3152;Inherit;False;26;Control;1;0;OBJECT;;False;1;COLOR;0\nNode;AmplifyShaderEditor.DotProductOpNode;1166;-336,3072;Inherit;False;2;0;FLOAT4;0,0,0,0;False;1;COLOR;0,0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;549;-1472,3392;Inherit;False;335;Mask0A;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1133;-1472,3472;Inherit;False;436;Splat1A;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1134;-1472,3552;Inherit;False;438;Splat2A;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1135;-1472,3632;Inherit;False;440;Splat3A;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;553;-1216,3472;Inherit;False;369;Mask1A;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;556;-1216,3552;Inherit;False;360;Mask2A;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;559;-1216,3632;Inherit;False;391;Mask3A;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;1162;-704,3072;Inherit;False;2;2;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;525;-832,3920;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;521;-1024,3920;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;522;-1024,4016;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;527;-1200,3920;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0.5;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;608;-1232,4016;Inherit;False;Constant;_Float0;Float
    0;27;0;Create;True;0;0;0;False;0;False;0.25;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;597;-1408,3920;Inherit;False;409;Mask0G;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;609;-1024,4112;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;613;-1200,4112;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0.5;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;612;-832,4112;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;610;-1024,4208;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;614;-1232,4208;Inherit;False;Constant;_Float1;Float
    0;27;0;Create;True;0;0;0;False;0;False;0.25;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;598;-1424,4112;Inherit;False;371;Mask1G;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;616;-1024,4304;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;620;-1200,4304;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0.5;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;619;-832,4304;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;617;-1024,4400;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;599;-1408,4304;Inherit;False;358;Mask2G;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;621;-1232,4400;Inherit;False;Constant;_Float2;Float
    0;27;0;Create;True;0;0;0;False;0;False;0.25;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SaturateNode;622;-704,4304;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SaturateNode;615;-704,4112;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SaturateNode;526;-704,3920;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;623;-1024,4480;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;627;-1200,4480;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0.5;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;600;-1408,4480;Inherit;False;389;Mask3G;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;626;-832,4464;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;624;-1024,4576;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;628;-1232,4576;Inherit;False;Constant;_Float3;Float
    0;27;0;Create;True;0;0;0;False;0;False;0.25;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SaturateNode;629;-704,4464;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.Vector4Node;280;-3488,144;Half;False;Constant;_weight;weight;3;0;Create;True;1;;0;0;False;0;False;1,1,1,1;1,1,1,1;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.RegisterLocalVarNode;369;-3584,1872;Inherit;True;Mask1A;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;335;-3584,1120;Inherit;True;Mask0A;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;360;-3584,2592;Inherit;True;Mask2A;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;391;-3600,3296;Inherit;True;Mask3A;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;26;-2272,-160;Float;False;Control;-1;True;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.SimpleDivideOpNode;304;-2432,-160;Inherit;False;2;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;1;COLOR;0\nNode;AmplifyShaderEditor.SamplerNode;283;-3504,-160;Inherit;True;Property;_TextureSample16;Texture
    Sample 16;12;1;[NoScaleOffset];Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.TexturePropertyNode;284;-4816,-160;Inherit;True;Property;_Control;Control;2;1;[HideInInspector];Create;False;0;0;0;False;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.StickyNoteNode;1200;-1664,-176;Inherit;False;413.2189;213.4898;URP
    - Color Tint ;;0.04402506,0.04402506,0.04402506,1;To Set Color Tint custom Inspector
    must be set:$$UnityEditor.Rendering.Universal.TerrainLitShaderGUI$$Note:$ Unity
    terrain must be reloaded for it to take effect over the terrain custom inspect;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;1218;-1664,64;Inherit;False;200.6666;110;;;0,0,0,1;_DiffuseRemapScale0;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;1219;-1664,352;Inherit;False;200.6666;110;;;0,0,0,1;_DiffuseRemapScale1;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;1220;-1664,672;Inherit;False;200.6666;110;;;0,0,0,1;_DiffuseRemapScale2;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;1221;-1664,960;Inherit;False;200.6666;110;;;0,0,0,1;_DiffuseRemapScale3;0;0\nNode;AmplifyShaderEditor.SamplerNode;451;-240,128;Inherit;True;Property;_TextureSample12;Texture
    Sample 4;284;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.LerpOp;894;1360,112;Inherit;False;3;0;FLOAT3;1,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;892;1360,320;Inherit;False;3;0;FLOAT3;1,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;906;1584,208;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SummedBlendNode;900;1696,96;Inherit;False;5;0;FLOAT4;0,0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;3;FLOAT3;0,0,0;False;4;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;901;1616,224;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;902;1648,240;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;905;1584,320;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;904;1600,512;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;903;1616,704;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;924;1904,0;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitch;668;2064,-80;Inherit;False;Enable
    Snow;True;0;2;2;In 0;In 1;Object;-1;10;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;942;848,-16;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;944;848,-16;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;946;848,-16;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;948;848,-16;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;952;864,576;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;954;864,784;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;949;864,400;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;956;848,208;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;957;848,208;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;958;848,208;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;940;624,160;Inherit;False;842;SnowBaseColor;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;910;944,352;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;912;944,128;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;917;944,544;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;916;944,752;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;898;1360,736;Inherit;False;3;0;FLOAT3;1,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;918;1360,528;Inherit;False;3;0;FLOAT3;1,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;959;1296,752;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;960;1248,-48;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;961;1296,-16;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;963;1296,-16;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;962;1248,-48;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;965;1296,-16;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;964;1248,-48;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;967;1824,-16;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;966;1824,-48;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;932;1888,272;Inherit;False;SnowEnable;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;925;1360,240;Inherit;False;SnowEnableRChannel;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;896;1360,448;Inherit;False;SnowEnableGChannel;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;897;1360,656;Inherit;False;SnowEnableBChannel;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;899;1360,864;Inherit;False;SnowEnableAChannel;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;927;624,400;Inherit;False;800;SnowSplatGMask;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;928;624,592;Inherit;False;799;SnowSplatBMask;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;929;624,800;Inherit;False;777;SnowSplatAMask;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;935;1728,272;Half;False;Property;_SnowEnable;ENABLE;27;2;[Header];[ToggleUI];Create;False;1;SNOW;0;0;False;0;False;0;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;938;1456,32;Inherit;False;728;SnowSplatRGBA;1;0;OBJECT;;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.WireNode;951;848,128;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;950;864,352;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;953;864,560;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;955;864,752;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1038;1296,544;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1040;1296,-16;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RangedFloatNode;934;1056,672;Half;False;Property;_SnowSplatBEnable;ENABLE
    CHANNEL BLUE;50;1;[ToggleUI];Create;False;0;0;0;False;1;Space(10);False;0;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;936;1040,256;Half;False;Property;_SnowSplatREnable;ENABLE
    CHANNEL RED;36;1;[ToggleUI];Create;False;0;0;0;False;1;Space(10);False;0;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;937;1024,464;Half;False;Property;_SnowSplatGEnable;ENABLE
    CHANNEL GREEN;43;1;[ToggleUI];Create;False;0;0;0;False;1;Space(10);False;0;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;933;1040,880;Half;False;Property;_SnowSplatAEnable;ENABLE
    CHANNEL ALPHA;57;1;[ToggleUI];Create;False;0;0;0;False;1;Space(10);False;0;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.TexturePropertyNode;452;-496,128;Inherit;True;Property;_TerrainHolesTexture;_TerrainHolesTexture;1;1;[HideInInspector];Create;False;0;0;0;False;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.FunctionSwitch;102;288,-64;Inherit;False;Enable
    Holes;True;1;2;1;In 0;In 1;Object;-1;10;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.CustomExpressionNode;453;80,16;Inherit;False;#ifdef
    _ALPHATEST_ON$\tclip(Hole == 0.0f ? -1 : 1)@$#endif;1;Call;1;True;Hole;FLOAT;0;In;;Inherit;False;ClipHoles;False;False;0;;False;2;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1079;16,-16;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;468;0,-32;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;926;624,256;Inherit;False;818;SnowSplatRMask;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SummedBlendNode;9;-480,-64;Inherit;False;5;0;COLOR;0,0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;3;FLOAT3;0,0,0;False;4;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;433;-1216,-48;Inherit;False;342;Splat0;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitchByPipeline;1201;-832,-48;Inherit;False;4;0;FLOAT3;0,0,0;False;3;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;1191;-1008,64;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SwizzleNode;1184;-1184,64;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;1190;-992,352;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;445;-1216,256;Inherit;False;379;Splat1;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SwizzleNode;1186;-1184,352;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.ColorNode;1185;-1440,352;Inherit;False;Global;_DiffuseRemapScale1;_DiffuseRemapScale1;23;0;Create;True;0;0;0;False;0;False;1,1,1,1;0,0,0,0;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.FunctionSwitchByPipeline;1202;-832,256;Inherit;False;4;0;FLOAT3;0,0,0;False;3;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;446;-1216,576;Inherit;False;357;Splat2;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;1189;-992,656;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SwizzleNode;1188;-1168,672;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.ColorNode;1187;-1424,672;Inherit;False;Global;_DiffuseRemapScale2;_DiffuseRemapScale2;23;0;Create;True;0;0;0;False;0;False;1,1,1,1;0,0,0,0;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.FunctionSwitchByPipeline;1205;-832,576;Inherit;False;4;0;FLOAT3;0,0,0;False;3;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;447;-1200,864;Inherit;False;390;Splat3;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitchByPipeline;1206;-832,864;Inherit;False;4;0;FLOAT3;0,0,0;False;3;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1209;-608,64;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1210;-576,80;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1211;-544,96;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1212;-560,848;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1213;-592,576;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;449;-704,-144;Inherit;False;26;Control;1;0;OBJECT;;False;1;COLOR;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;1194;-992,960;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SwizzleNode;1192;-1168,960;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.ColorNode;1193;-1440,960;Inherit;False;Global;_DiffuseRemapScale3;_DiffuseRemapScale3;23;0;Create;True;0;0;0;False;0;False;1,1,1,1;0,0,0,0;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.WireNode;1214;-896,288;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1215;-880,608;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1216;-880,896;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1217;-912,-16;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.ColorNode;1183;-1440,64;Inherit;False;Global;_DiffuseRemapScale0;_DiffuseRemapScale0;23;0;Create;True;0;0;0;False;0;False;1,1,1,1;0,0,0,0;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.WireNode;947;832,-32;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;945;832,-32;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;943;832,-32;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;941;832,-32;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1039;1248,-48;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionOutput;701;-1280,-784;Inherit;False;False;-1;Vertex
    Tangents;7;False;1;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.FunctionOutput;709;-1280,-1152;Inherit;False;False;-1;Vertex
    Normals;6;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionOutput;56;-480,2288;Inherit;False;False;-1;Metallic;2;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;200;32,3888;Inherit;False;False;-1;Occlusion;4;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;14;2000,1472;Inherit;False;False;-1;Normal;1;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionOutput;45;-192,3072;Inherit;False;False;-1;Smoothness;3;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;282;-3024,224;Inherit;False;False;-1;Alpha;5;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;0;2336,-80;Inherit;False;True;-1;BaseColor;0;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nWireConnection;414;0;324;0\nWireConnection;414;1;426;0\nWireConnection;414;7;431;0\nWireConnection;430;0;326;1\nWireConnection;431;0;324;1\nWireConnection;432;0;332;1\nWireConnection;420;0;318;0\nWireConnection;420;1;428;0\nWireConnection;420;7;364;0\nWireConnection;428;2;318;0\nWireConnection;417;0;316;0\nWireConnection;417;1;427;0\nWireConnection;417;7;347;0\nWireConnection;427;2;316;0\nWireConnection;419;0;311;0\nWireConnection;419;1;427;0\nWireConnection;419;7;349;0\nWireConnection;429;2;314;0\nWireConnection;425;0;309;0\nWireConnection;425;1;429;0\nWireConnection;425;7;387;0\nWireConnection;424;0;310;0\nWireConnection;424;1;429;0\nWireConnection;424;7;385;0\nWireConnection;423;0;314;0\nWireConnection;423;1;429;0\nWireConnection;423;7;383;0\nWireConnection;325;0;324;1\nWireConnection;327;0;326;1\nWireConnection;276;0;278;0\nWireConnection;276;1;278;0\nWireConnection;278;0;283;0\nWireConnection;278;1;280;0\nWireConnection;281;0;276;0\nWireConnection;281;1;275;0\nWireConnection;295;0;296;0\nWireConnection;295;1;289;0\nWireConnection;289;0;284;0\nWireConnection;291;0;289;1\nWireConnection;290;0;291;0\nWireConnection;290;1;292;0\nWireConnection;288;0;291;1\nWireConnection;288;1;297;0\nWireConnection;293;0;290;0\nWireConnection;293;1;288;0\nWireConnection;294;0;295;0\nWireConnection;294;1;293;0\nWireConnection;286;0;294;0\nWireConnection;448;0;284;1\nWireConnection;470;0;420;0\nWireConnection;436;0;420;4\nWireConnection;370;0;422;1\nWireConnection;371;0;422;2\nWireConnection;379;0;470;0\nWireConnection;412;0;422;3\nWireConnection;341;0;415;0\nWireConnection;334;0;416;1\nWireConnection;469;0;414;0\nWireConnection;359;0;419;1\nWireConnection;358;0;419;2\nWireConnection;411;0;419;3\nWireConnection;472;0;423;0\nWireConnection;390;0;472;0\nWireConnection;440;0;423;4\nWireConnection;398;0;424;0\nWireConnection;388;0;425;1\nWireConnection;389;0;425;2\nWireConnection;413;0;425;3\nWireConnection;491;0;477;0\nWireConnection;491;1;485;0\nWireConnection;473;0;474;0\nWireConnection;473;1;490;0\nWireConnection;473;2;500;0\nWireConnection;473;3;501;0\nWireConnection;473;4;502;0\nWireConnection;500;0;496;0\nWireConnection;501;0;494;0\nWireConnection;502;0;491;0\nWireConnection;630;0;615;0\nWireConnection;631;0;622;0\nWireConnection;632;0;629;0\nWireConnection;634;0;602;0\nWireConnection;415;0;326;0\nWireConnection;415;1;426;0\nWireConnection;415;7;430;0\nWireConnection;703;0;702;0\nWireConnection;703;1;699;0\nWireConnection;704;0;703;0\nWireConnection;707;0;706;0\nWireConnection;707;1;705;0\nWireConnection;710;0;707;0\nWireConnection;710;3;707;0\nWireConnection;710;1;705;0\nWireConnection;710;2;705;0\nWireConnection;711;0;715;0\nWireConnection;729;0;750;0\nWireConnection;729;1;748;0\nWireConnection;747;1;729;0\nWireConnection;747;2;711;0\nWireConnection;748;0;749;0\nWireConnection;750;0;751;0\nWireConnection;751;0;753;0\nWireConnection;751;1;752;0\nWireConnection;752;1;754;0\nWireConnection;756;0;757;0\nWireConnection;763;1;764;0\nWireConnection;779;0;780;0\nWireConnection;781;0;784;0\nWireConnection;782;0;781;0\nWireConnection;782;1;779;0\nWireConnection;783;1;782;0\nWireConnection;783;2;778;0\nWireConnection;784;0;785;0\nWireConnection;784;1;786;0\nWireConnection;786;1;787;0\nWireConnection;801;0;806;0\nWireConnection;802;0;801;0\nWireConnection;802;1;803;0\nWireConnection;803;0;805;0\nWireConnection;804;1;802;0\nWireConnection;804;2;741;0\nWireConnection;806;0;807;0\nWireConnection;806;1;808;0\nWireConnection;808;1;809;0\nWireConnection;788;0;783;0\nWireConnection;810;0;804;0\nWireConnection;794;1;788;0\nWireConnection;794;2;832;0\nWireConnection;834;0;833;1\nWireConnection;834;1;791;0\nWireConnection;834;2;790;0\nWireConnection;793;1;770;0\nWireConnection;793;2;834;0\nWireConnection;833;0;789;0\nWireConnection;836;0;835;1\nWireConnection;836;1;774;0\nWireConnection;836;2;773;0\nWireConnection;835;0;772;0\nWireConnection;771;0;747;0\nWireConnection;824;0;813;0\nWireConnection;823;0;824;1\nWireConnection;823;1;815;0\nWireConnection;823;2;814;0\nWireConnection;778;0;744;0\nWireConnection;741;0;737;0\nWireConnection;831;0;795;0\nWireConnection;832;0;831;1\nWireConnection;832;1;797;0\nWireConnection;832;2;796;0\nWireConnection;770;0;760;0\nWireConnection;760;1;759;0\nWireConnection;760;2;755;0\nWireConnection;759;0;758;0\nWireConnection;759;1;756;0\nWireConnection;758;0;761;0\nWireConnection;761;0;762;0\nWireConnection;761;1;763;0\nWireConnection;755;0;767;0\nWireConnection;818;0;817;0\nWireConnection;800;0;794;0\nWireConnection;799;0;793;0\nWireConnection;777;0;776;0\nWireConnection;718;0;717;2\nWireConnection;718;1;720;0\nWireConnection;723;0;724;0\nWireConnection;723;1;718;0\nWireConnection;723;2;725;0\nWireConnection;723;3;726;0\nWireConnection;724;0;717;1\nWireConnection;724;1;719;0\nWireConnection;725;0;717;3\nWireConnection;725;1;721;0\nWireConnection;726;0;717;4\nWireConnection;726;1;722;0\nWireConnection;717;0;731;0\nWireConnection;717;1;730;0\nWireConnection;717;7;731;1\nWireConnection;733;0;727;1\nWireConnection;850;0;851;0\nWireConnection;850;1;853;0\nWireConnection;851;0;845;0\nWireConnection;845;0;846;0\nWireConnection;845;1;849;0\nWireConnection;846;0;852;0\nWireConnection;846;1;847;0\nWireConnection;847;0;848;0\nWireConnection;849;0;848;0\nWireConnection;858;0;856;0\nWireConnection;978;0;1031;0\nWireConnection;978;1;1030;0\nWireConnection;978;2;985;0\nWireConnection;979;0;1029;0\nWireConnection;979;1;1028;0\nWireConnection;979;2;984;0\nWireConnection;982;0;1027;0\nWireConnection;982;1;1026;0\nWireConnection;982;2;986;0\nWireConnection;990;0;978;0\nWireConnection;989;0;992;0\nWireConnection;992;0;982;0\nWireConnection;975;0;1007;0\nWireConnection;975;1;976;0\nWireConnection;975;2;988;0\nWireConnection;975;3;987;0\nWireConnection;975;4;989;0\nWireConnection;976;0;1019;0\nWireConnection;976;1;1015;0\nWireConnection;976;2;1008;0\nWireConnection;1005;0;1017;0\nWireConnection;1005;1;975;0\nWireConnection;1005;2;1004;0\nWireConnection;1017;0;1016;0\nWireConnection;1018;0;504;0\nWireConnection;1019;0;1018;0\nWireConnection;1021;0;1020;0\nWireConnection;1020;0;504;0\nWireConnection;1023;0;1022;0\nWireConnection;1022;0;504;0\nWireConnection;1025;0;1024;0\nWireConnection;1024;0;504;0\nWireConnection;1026;0;1015;0\nWireConnection;1027;0;1025;0\nWireConnection;1028;0;1015;0\nWireConnection;1029;0;1023;0\nWireConnection;1030;0;1015;0\nWireConnection;1031;0;1021;0\nWireConnection;988;0;990;0\nWireConnection;987;0;991;0\nWireConnection;991;0;979;0\nWireConnection;844;0;862;0\nWireConnection;844;1;850;0\nWireConnection;844;7;862;1\nWireConnection;863;0;862;1\nWireConnection;855;0;854;0\nWireConnection;855;1;850;0\nWireConnection;855;7;854;1\nWireConnection;837;0;844;0\nWireConnection;840;0;841;0\nWireConnection;1016;0;504;0\nWireConnection;728;0;723;0\nWireConnection;727;0;723;0\nWireConnection;732;0;727;0\nWireConnection;734;0;727;2\nWireConnection;735;0;727;3\nWireConnection;812;0;811;0\nWireConnection;817;1;810;0\nWireConnection;817;2;823;0\nWireConnection;776;1;771;0\nWireConnection;776;2;836;0\nWireConnection;856;0;855;0\nWireConnection;856;1;857;0\nWireConnection;859;0;861;0\nWireConnection;839;0;840;0\nWireConnection;839;1;1078;0\nWireConnection;839;2;843;0\nWireConnection;1078;12;837;0\nWireConnection;1078;21;859;0\nWireConnection;426;2;324;0\nWireConnection;602;0;596;0\nWireConnection;602;1;526;0\nWireConnection;602;2;630;0\nWireConnection;602;3;631;0\nWireConnection;602;4;632;0\nWireConnection;333;0;332;1\nWireConnection;435;0;414;4\nWireConnection;536;0;537;0\nWireConnection;536;1;1110;0\nWireConnection;536;2;1175;0\nWireConnection;536;3;1176;0\nWireConnection;536;4;1177;0\nWireConnection;421;0;317;0\nWireConnection;421;1;428;0\nWireConnection;421;7;365;0\nWireConnection;378;0;421;0\nWireConnection;842;0;839;0\nWireConnection;342;0;469;0\nWireConnection;1110;0;48;0\nWireConnection;1110;1;538;0\nWireConnection;1111;0;49;0\nWireConnection;1111;1;539;0\nWireConnection;1112;0;50;0\nWireConnection;1112;1;540;0\nWireConnection;1113;0;51;0\nWireConnection;1113;1;541;0\nWireConnection;409;0;416;2\nWireConnection;410;0;416;3\nWireConnection;471;0;417;0\nWireConnection;357;0;471;0\nWireConnection;438;0;417;4\nWireConnection;416;0;332;0\nWireConnection;416;1;426;0\nWireConnection;416;7;432;0\nWireConnection;422;0;307;0\nWireConnection;422;1;428;0\nWireConnection;422;7;366;0\nWireConnection;490;0;481;0\nWireConnection;490;1;482;0\nWireConnection;496;0;476;0\nWireConnection;496;1;483;0\nWireConnection;494;0;480;0\nWireConnection;494;1;484;0\nWireConnection;418;0;308;0\nWireConnection;418;1;427;0\nWireConnection;418;7;348;0\nWireConnection;356;0;418;0\nWireConnection;669;0;504;0\nWireConnection;669;1;1005;0\nWireConnection;503;1;514;0\nWireConnection;503;0;514;0\nWireConnection;513;0;473;0\nWireConnection;515;0;513;2\nWireConnection;515;1;516;0\nWireConnection;514;0;513;0\nWireConnection;514;1;513;1\nWireConnection;514;2;515;0\nWireConnection;504;0;514;0\nWireConnection;504;1;503;0\nWireConnection;1175;0;1111;0\nWireConnection;1176;0;1179;0\nWireConnection;1177;0;1178;0\nWireConnection;1179;0;1112;0\nWireConnection;1178;0;1113;0\nWireConnection;1168;0;579;0\nWireConnection;1168;1;1170;0\nWireConnection;1168;2;1171;0\nWireConnection;1168;3;1172;0\nWireConnection;1170;0;580;0\nWireConnection;1171;0;581;0\nWireConnection;1172;0;582;0\nWireConnection;1169;0;1132;0\nWireConnection;1169;1;553;0\nWireConnection;1169;2;556;0\nWireConnection;1169;3;559;0\nWireConnection;1181;0;1169;0\nWireConnection;1180;0;1181;0\nWireConnection;1166;0;1162;0\nWireConnection;1166;1;561;0\nWireConnection;1162;0;1168;0\nWireConnection;1162;1;1180;0\nWireConnection;525;0;521;0\nWireConnection;525;1;522;0\nWireConnection;521;0;527;0\nWireConnection;521;1;608;0\nWireConnection;522;0;608;0\nWireConnection;527;0;597;0\nWireConnection;609;0;613;0\nWireConnection;609;1;614;0\nWireConnection;613;0;598;0\nWireConnection;612;0;609;0\nWireConnection;612;1;610;0\nWireConnection;610;0;614;0\nWireConnection;616;0;620;0\nWireConnection;616;1;621;0\nWireConnection;620;0;599;0\nWireConnection;619;0;616;0\nWireConnection;619;1;617;0\nWireConnection;617;0;621;0\nWireConnection;622;0;619;0\nWireConnection;615;0;612;0\nWireConnection;526;0;525;0\nWireConnection;623;0;627;0\nWireConnection;623;1;628;0\nWireConnection;627;0;600;0\nWireConnection;626;0;623;0\nWireConnection;626;1;624;0\nWireConnection;624;0;628;0\nWireConnection;629;0;626;0\nWireConnection;369;0;422;4\nWireConnection;335;0;416;4\nWireConnection;360;0;419;4\nWireConnection;391;0;425;4\nWireConnection;26;0;304;0\nWireConnection;304;0;283;0\nWireConnection;304;1;281;0\nWireConnection;283;0;284;0\nWireConnection;283;1;286;0\nWireConnection;283;7;448;0\nWireConnection;451;0;452;0\nWireConnection;451;7;452;1\nWireConnection;894;0;963;0\nWireConnection;894;1;912;0\nWireConnection;894;2;936;0\nWireConnection;892;0;965;0\nWireConnection;892;1;910;0\nWireConnection;892;2;937;0\nWireConnection;906;0;905;0\nWireConnection;900;0;938;0\nWireConnection;900;1;894;0\nWireConnection;900;2;906;0\nWireConnection;900;3;901;0\nWireConnection;900;4;902;0\nWireConnection;901;0;904;0\nWireConnection;902;0;903;0\nWireConnection;905;0;892;0\nWireConnection;904;0;918;0\nWireConnection;903;0;898;0\nWireConnection;924;0;967;0\nWireConnection;924;1;900;0\nWireConnection;924;2;935;0\nWireConnection;668;0;102;0\nWireConnection;668;1;924;0\nWireConnection;942;0;941;0\nWireConnection;944;0;943;0\nWireConnection;946;0;945;0\nWireConnection;948;0;947;0\nWireConnection;952;0;957;0\nWireConnection;954;0;958;0\nWireConnection;949;0;956;0\nWireConnection;956;0;940;0\nWireConnection;957;0;940;0\nWireConnection;958;0;940;0\nWireConnection;910;0;950;0\nWireConnection;910;1;949;0\nWireConnection;910;2;927;0\nWireConnection;912;0;951;0\nWireConnection;912;1;940;0\nWireConnection;912;2;926;0\nWireConnection;917;0;953;0\nWireConnection;917;1;952;0\nWireConnection;917;2;928;0\nWireConnection;916;0;955;0\nWireConnection;916;1;954;0\nWireConnection;916;2;929;0\nWireConnection;898;0;959;0\nWireConnection;898;1;916;0\nWireConnection;898;2;933;0\nWireConnection;918;0;1038;0\nWireConnection;918;1;917;0\nWireConnection;918;2;934;0\nWireConnection;959;0;961;0\nWireConnection;960;0;102;0\nWireConnection;961;0;960;0\nWireConnection;963;0;962;0\nWireConnection;962;0;102;0\nWireConnection;965;0;964;0\nWireConnection;964;0;102;0\nWireConnection;967;0;966;0\nWireConnection;966;0;102;0\nWireConnection;932;0;935;0\nWireConnection;925;0;936;0\nWireConnection;896;0;937;0\nWireConnection;897;0;934;0\nWireConnection;899;0;933;0\nWireConnection;951;0;942;0\nWireConnection;950;0;944;0\nWireConnection;953;0;946;0\nWireConnection;955;0;948;0\nWireConnection;1038;0;1040;0\nWireConnection;1040;0;1039;0\nWireConnection;102;0;9;0\nWireConnection;102;1;453;0\nWireConnection;453;0;1079;0\nWireConnection;453;1;451;1\nWireConnection;1079;0;468;0\nWireConnection;468;0;9;0\nWireConnection;9;0;449;0\nWireConnection;9;1;1201;0\nWireConnection;9;2;1209;0\nWireConnection;9;3;1210;0\nWireConnection;9;4;1211;0\nWireConnection;1201;0;433;0\nWireConnection;1201;3;1217;0\nWireConnection;1201;1;1191;0\nWireConnection;1191;0;433;0\nWireConnection;1191;1;1184;0\nWireConnection;1184;0;1183;0\nWireConnection;1190;0;445;0\nWireConnection;1190;1;1186;0\nWireConnection;1186;0;1185;0\nWireConnection;1202;0;445;0\nWireConnection;1202;3;1214;0\nWireConnection;1202;1;1190;0\nWireConnection;1189;0;446;0\nWireConnection;1189;1;1188;0\nWireConnection;1188;0;1187;0\nWireConnection;1205;0;446;0\nWireConnection;1205;3;1215;0\nWireConnection;1205;1;1189;0\nWireConnection;1206;0;447;0\nWireConnection;1206;3;1216;0\nWireConnection;1206;1;1194;0\nWireConnection;1209;0;1202;0\nWireConnection;1210;0;1213;0\nWireConnection;1211;0;1212;0\nWireConnection;1212;0;1206;0\nWireConnection;1213;0;1205;0\nWireConnection;1194;0;447;0\nWireConnection;1194;1;1192;0\nWireConnection;1192;0;1193;0\nWireConnection;1214;0;445;0\nWireConnection;1215;0;446;0\nWireConnection;1216;0;447;0\nWireConnection;1217;0;433;0\nWireConnection;947;0;102;0\nWireConnection;945;0;102;0\nWireConnection;943;0;102;0\nWireConnection;941;0;102;0\nWireConnection;1039;0;102;0\nWireConnection;701;0;704;0\nWireConnection;709;0;710;0\nWireConnection;56;0;536;0\nWireConnection;200;0;634;0\nWireConnection;14;0;669;0\nWireConnection;45;0;1166;0\nWireConnection;282;0;278;0\nWireConnection;0;0;668;0\nASEEND*/\n//CHKSM=C012458FC30FEB779249921EF39989861BD8B54E"
  m_functionName: 
  m_description: 'Amplify Shader Pack Example

    *BIRP

    *URP'
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 1
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_customEdited: 0
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 0
  m_headerStyle: 2
  m_headerColor: {r: 0.7647059, g: 0.30588236, b: 0, a: 1}
  m_customNodeCategory: Amplify Shader Pack
  m_previewPosition: 0
  m_hidden: 0
  m_url: 
