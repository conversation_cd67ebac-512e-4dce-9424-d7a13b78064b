using UnityEngine;
using UnityEditor;
using System.IO;
using System.Text;
using System.Linq;
using System.Collections.Generic;
using ScriptAnalysis.Analyzers;

namespace ScriptAnalysis.Exporters
{
    public abstract class Exporter
    {
        public abstract void Export(string path, DependencyAnalyzer analyzer);
    }

    public class DotExporter : Exporter
    {
        public override void Export(string path, DependencyAnalyzer analyzer)
        {
            var scriptMetadata = analyzer.ScriptMetadata;

            StringBuilder dot = new StringBuilder();
            dot.AppendLine("digraph G {");
            dot.AppendLine("    rankdir=LR;");

            // Define node styles
            dot.AppendLine("    node [shape=box, style=filled, color=lightgrey];");

            // Define nodes
            foreach (var script in scriptMetadata.Values)
            {
                string color = script.IsMonoBehaviour ? "lightblue" :
                               script.IsScriptableObject ? "lightgreen" : "white";
                dot.AppendLine($"    \"{script.Name}\" [fillcolor={color}];");
            }

            // Define edges
            foreach (var script in scriptMetadata.Values)
            {
                foreach (var reference in script.References.Keys)
                {
                    dot.AppendLine($"    \"{script.Name}\" -> \"{reference}\";");
                }
            }

            dot.AppendLine("}");

            File.WriteAllText(path, dot.ToString());
        }
    }

    public class SvgExporter : Exporter
    {
        public override void Export(string path, DependencyAnalyzer analyzer)
        {
            // Implement SVG export logic
            EditorUtility.DisplayDialog("SVG Export", "SVG export not implemented yet.", "OK");
        }
    }

    public class HtmlExporter : Exporter
    {
        public override void Export(string path, DependencyAnalyzer analyzer)
        {
            // Implement HTML export logic
            EditorUtility.DisplayDialog("HTML Export", "HTML export not implemented yet.", "OK");
        }
    }

    public class MarkdownExporter : Exporter
    {
        public override void Export(string path, DependencyAnalyzer analyzer)
        {
            StringBuilder md = new StringBuilder();
            md.AppendLine("# Script Dependency Analysis\n");
            
            md.AppendLine("## Scripts Overview\n");
            foreach (var script in analyzer.ScriptMetadata.Values)
            {
                md.AppendLine($"### {script.Name} ({GetScriptType(script)})");
                
                md.AppendLine("\nDependencies:");
                foreach (var dep in script.References.Keys)
                {
                    md.AppendLine($"- {dep}");
                }
                
                md.AppendLine("\nReferenced By:");
                foreach (var refScript in GetReferences(script.Name, analyzer))
                {
                    md.AppendLine($"- {refScript}");
                }
                md.AppendLine();
            }
            
            File.WriteAllText(path, md.ToString());
        }
        
        private string GetScriptType(ScriptMetadata script)
        {
            if (script.IsMonoBehaviour) return "MonoBehaviour";
            if (script.IsScriptableObject) return "ScriptableObject";
            return "Script";
        }
        
        private IEnumerable<string> GetReferences(string scriptName, DependencyAnalyzer analyzer)
        {
            return analyzer.ScriptMetadata.Values
                .Where(s => s.References.ContainsKey(scriptName))
                .Select(s => s.Name);
        }
    }
}