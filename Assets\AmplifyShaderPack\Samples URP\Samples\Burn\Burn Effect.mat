%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Burn Effect
  m_Shader: {fileID: 4800000, guid: 1be68f5305ac47a28780e2c133c7b88d, type: 3}
  m_ValidKeywords: []
  m_InvalidKeywords:
  - _EMISSION
  m_LightmapFlags: 1
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - Albedo:
        m_Texture: {fileID: 2800000, guid: 85e3723e62d44f758723754190c67911, type: 3}
        m_Scale: {x: 0.5, y: 0.5}
        m_Offset: {x: -0.36, y: 0}
    - BurntTileNormals:
        m_Texture: {fileID: 2800000, guid: 2fb1b14b4e2147e4a580f44624c73725, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - Masks:
        m_Texture: {fileID: 2800000, guid: 21b9f4e0af3140a99ef0bc5a43d58a97, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - Normals:
        m_Texture: {fileID: 2800000, guid: abfd39fa1d6a42ba9b322e4301333932, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - SamplerNode80:
        m_Texture: {fileID: 2800000, guid: f0e651d26c455794d87206a2e7d7388c, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Albedo:
        m_Texture: {fileID: 2800000, guid: 85e3723e62d44f758723754190c67911, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BurntTileNormals:
        m_Texture: {fileID: 2800000, guid: 2fb1b14b4e2147e4a580f44624c73725, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Masks:
        m_Texture: {fileID: 2800000, guid: 21b9f4e0af3140a99ef0bc5a43d58a97, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Metallic:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Normals:
        m_Texture: {fileID: 2800000, guid: abfd39fa1d6a42ba9b322e4301333932, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Occlusion:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Texture0:
        m_Texture: {fileID: 2800000, guid: f0e651d26c455794d87206a2e7d7388c, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Texture1:
        m_Texture: {fileID: 2800000, guid: 7d65a1d73c4d4054f8ff447189a4a0c0, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Texture2:
        m_Texture: {fileID: 2800000, guid: 62b8b522688b14244816c5606dbbe2d0, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Texture3:
        m_Texture: {fileID: 2800000, guid: 26b3e85120468a64396a93b6019cf05f, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _texcoord:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - null:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - BaseEmber: 0.371
    - GlowBaseFrequency: 2.35
    - GlowBaseFrequency159: 1.1
    - GlowBaseFrequency89: 4.08
    - GlowColorIntensity: 3.66
    - GlowDuration: 20
    - GlowEmissionMultiplier: 21.9
    - GlowOverride: 1.07
    - R2: 1
    - RangedFloatNode156: 0.88
    - RangedFloatNode192: 10
    - RangedFloatNode95: 1
    - SuperBurn: 1
    - _AlbedoMix: 0.356
    - _AlphaCutoff: 0.5
    - _BaseDirtMud: 1
    - _BaseEmber: 0.371
    - _BurnOffset: 0.22
    - _BurnTilling: 0.484
    - _CharcoalMix: 0.713
    - _CharcoalNormalTile: 2
    - _Distribution: 1
    - _EnvironmentReflections: 1
    - _GlowBaseFrequency: 2.35
    - _GlowBaseFrequency76: 1.137
    - _GlowColorIntensity: 3.66
    - _GlowColorIntensity77: 4.03
    - _GlowEmissionMultiplier: 21.9
    - _GlowOverride: 1.07
    - _MudDirtMix: 0
    - _QueueControl: 0
    - _QueueOffset: 0
    - _ReceiveShadows: 1
    - _RimPower: 0
    - _Smoothness: 0.13
    - _SmoothnessFactor: 0
    - _SpecularHighlights: 1
    - _StartPoint: 0.75
    - _TestBaseIntensityVariation78: 0
    - _Thickness: 0.5
    - _UnderwaterInfluence: 0
    - __dirty: 1
    - temp3: 1
    m_Colors:
    - EmberColorTint: {r: 0.966, g: 0.1062518, b: 0.004325263, a: 1}
    - _EmberColorTint: {r: 0.966, g: 0.106251724, b: 0.004325263, a: 1}
    - _EmberColorTint1551673: {r: 0.6901961, g: 0.2588228, b: 0.06274459, a: 1}
    - _EmissionColor: {r: 1, g: 1, b: 1, a: 1}
    - _MudDirt: {r: 0.58823526, g: 0, b: 0, a: 0}
    - _RimColor: {r: 0, g: 0, b: 0, a: 0}
    - _Tint: {r: 0.5294118, g: 0.42642888, b: 0, a: 0}
    - temp: {r: 0.6901961, g: 0.2588235, b: 0.0627451, a: 1}
  m_BuildTextureStacks: []
