%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Vector Displacement
  m_Shader: {fileID: 4800000, guid: e7790db9f86141eb8f181b48002710d8, type: 3}
  m_ValidKeywords: []
  m_InvalidKeywords:
  - _ADD_PRECOMPUTED_VELOCITY
  m_LightmapFlags: 0
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap:
    MotionVector: User
  disabledShaderPasses:
  - TransparentBackface
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _Albedo:
        m_Texture: {fileID: 2800000, guid: 47a4fa154e4d444982cb3993f6da2795, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _LeftHandVDM:
        m_Texture: {fileID: 2800000, guid: 07775227b94f4c0193241bd40841f81c, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Masks:
        m_Texture: {fileID: 2800000, guid: 2ce18361c9dd445ab8b2bdef593443ba, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NoiseFlipbook:
        m_Texture: {fileID: 2800000, guid: 417025cc229b4cd38638ba97d15d9fd2, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Normal:
        m_Texture: {fileID: 2800000, guid: 21786817dce64c4a9f09e9d564a7ef61, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalHands:
        m_Texture: {fileID: 2800000, guid: 7213c3e97f724fa5adb6e613340bafb0, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalTopSkull:
        m_Texture: {fileID: 2800000, guid: 25ed1d4b8ff342dba6a9335134a9f686, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalsLeftHand:
        m_Texture: {fileID: 2800000, guid: 37097ef2806343569563472cbdc7f6bf, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ScreenHandsVDM:
        m_Texture: {fileID: 2800000, guid: eed7ad983901406c86b628d1d8b8b68c, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TV_MetallicSmoothness:
        m_Texture: {fileID: 2800000, guid: 0adb73023f2a4566bc08f553cd0b2f2c, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TopSkullVDM:
        m_Texture: {fileID: 2800000, guid: 4cd521ef1b5b4ffabe5a48acfe011ab2, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _texcoord:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AddPrecomputedVelocity: 1
    - _AlphaCutoff: 0.5
    - _AlphaCutoffEnable: 0
    - _AlphaDstBlend: 0
    - _AlphaSrcBlend: 1
    - _BaseSmoothness: 0.55
    - _BlendMode: 0
    - _CullMode: 2
    - _CullModeForward: 2
    - _DisplacementMultiplier: 1
    - _DoubleSidedEnable: 0
    - _DoubleSidedNormalMode: 2
    - _DstBlend: 0
    - _EdgeLength: 15
    - _EmissionIntensity2: 1
    - _EmissionWeight2: 1
    - _EnableFogOnTransparent: 1
    - _EnvironmentReflections: 1
    - _GlowIntensity: 0.21
    - _HandIntensity: 0.3
    - _NoiseTiling: 1
    - _QueueControl: 0
    - _QueueOffset: 0
    - _ReceiveShadows: 1
    - _ReceivesSSR: 1
    - _RenderQueueType: 1
    - _RequireSplitLighting: 0
    - _ScreenColorTintBlend: 0.28
    - _ScreenToggle: 1
    - _SideHandIntensity: 0.34
    - _SkullIntensity: 0.06
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _StencilRef: 0
    - _StencilRefDepth: 8
    - _StencilRefDistortionVec: 4
    - _StencilRefGBuffer: 10
    - _StencilRefMV: 40
    - _StencilWriteMask: 6
    - _StencilWriteMaskDepth: 8
    - _StencilWriteMaskDistortionVec: 4
    - _StencilWriteMaskGBuffer: 14
    - _StencilWriteMaskMV: 40
    - _SurfaceType: 0
    - _TessMax: 25
    - _TessMin: 10
    - _TessValue: 10.2
    - _TilingGlow: 2
    - _TransparentBackfaceEnable: 0
    - _TransparentCullMode: 2
    - _TransparentSortPriority: 0
    - _TransparentZWrite: 1
    - _UseShadowThreshold: 0
    - _ZTestDepthEqualForOpaque: 3
    - _ZTestGBuffer: 4
    - _ZTestTransparent: 4
    - _ZWrite: 1
    - __dirty: 0
    m_Colors:
    - _DoubleSidedConstants: {r: 1, g: 1, b: -1, a: 0}
    - _EmissionColor: {r: 1, g: 1, b: 1, a: 1}
    - _NoiseTint: {r: 77.24828, g: 77.24828, b: 77.24828, a: 0}
    - _SideHandTint: {r: 0, g: 0, b: 0, a: 0}
    - _TVHandsTint: {r: 0.92136884, g: 0.9365109, b: 0.95283014, a: 0}
    - _TopSkullTint: {r: 0, g: 0, b: 0, a: 0}
  m_BuildTextureStacks: []
