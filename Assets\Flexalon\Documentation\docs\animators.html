<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<link rel="icon" href="../images/favicon.svg" />
		<link rel="icon" href="../images/favicon.ico" />
		<meta name="viewport" content="width=device-width, height=device-height, initial-scale=1">
		<meta property="og:type" content="website">
		<meta http-equiv="content-security-policy" content="">
		<link href="../_app/immutable/assets/_layout.21aea584.css" rel="stylesheet">
		<link href="../_app/immutable/assets/_layout.0154a58a.css" rel="stylesheet">
		
		
		
		
		
		
		
		
		
		
		
		
		<title>Animators</title><!-- HEAD_svelte-i5aht6_START --><meta property="og:title" content="Animators"><meta name="description" property="og:description" content="Flexalon lets Unity developers arrange objects in 3D layouts that are precise and easy to adjust. Use built-in interactions and animations to create powerful experiences."><meta name="image" property="og:image" content="../images/flexalon-1200x630.png"><meta name="twitter:card" content="summary_large_image"><meta name="twitter:title" content="Animators"><meta name="twitter:site" content="@alon_farchy"><meta name="twitter:description" content="Flexalon lets Unity developers arrange objects in 3D layouts that are precise and easy to adjust. Use built-in interactions and animations to create powerful experiences."><meta name="twitter:image" content="../images/flexalon-1200x630.png"><!-- HEAD_svelte-i5aht6_END -->
	</head>
	<body>
		<div>


<div class="text-white font-body p-4 sticky top-0 bg-black flex gap-5 w-full text-gray-200 z-20 border-b-2 border-[#21a6f0]"><button class="lg:hidden"><svg width="24" height="24"><path d="M5 6h14M5 12h14M5 18h14" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"></path></svg></button>
    <a href="https://www.flexalon.com/" class="font-logo upper text-lg text-[#21a6f0] lg:pl-5 svelte-194nj6y">Flexalon Layouts</a></div>

<div class="w-full max-w-[1600px] lg:pl-10"><div class="flex w-full min-h-full text-zinc-400 relative"><div class="h-full fixed bg-black z-10 max-lg:hidden"><div class="flex flex-col w-[300px] gap-2 border-[#21a6f0] border-r-2 bg-black min-h-0 h-full pt-10 pb-36 overflow-auto max-lg:pl-10"><b class="text-zinc-200">Getting Started</b>
                <a href="../docs.html" class="svelte-194nj6y">Installation</a>
                <a href="../docs/coreConcepts.html" class="svelte-194nj6y">Core Concepts</a>
                <a href="../docs/ui.html" class="svelte-194nj6y">Flexalon UI <span class="ml-2 text-xs font-bold text-pink-300">(New in v4.0!)</span></a>
                <a href="../docs/templates.html" class="svelte-194nj6y">Template Pack</a>
                <a href="../docs/challenges.html" class="svelte-194nj6y">Challenges!</a>

                <a class="text-zinc-200 mt-5 font-bold svelte-194nj6y" href="../docs/flexalonObject.html">Flexalon Objects</a>
                <a href="../docs/flexalonObject.html#size" class="svelte-194nj6y">Width, Height, and Depth</a>
                <a href="../docs/flexalonObject.html#minmax" class="svelte-194nj6y">Min / Max Size<span class="ml-2 text-xs font-bold text-purple-400">(v4.1)</span></a>
                <a href="../docs/flexalonObject.html#margins" class="svelte-194nj6y">Margins and Padding</a>
                <a href="../docs/flexalonObject.html#offset" class="svelte-194nj6y">Offset, Scale, and Rotation</a>

                <b class="text-zinc-200 mt-5">Layouts</b>
                <a href="../docs/flexibleLayout.html" class="svelte-194nj6y">Flexible Layout</a>
                <a href="../docs/gridLayout.html" class="svelte-194nj6y">Grid Layout</a>
                <a href="../docs/circleLayout.html" class="svelte-194nj6y">Circle / Spiral Layout</a>
                <a href="../docs/curveLayout.html" class="svelte-194nj6y">Curve Layout</a>
                <a href="../docs/randomLayout.html" class="svelte-194nj6y">Random Layout</a>
                <a href="../docs/alignLayout.html" class="svelte-194nj6y">Align Layout</a>
                <a href="../docs/shapeLayout.html" class="svelte-194nj6y">Shape Layout</a>
                <a href="../docs/constraints.html" class="svelte-194nj6y">Constraints</a>
                <a href="../docs/randomModifier.html" class="svelte-194nj6y">Random Modifier</a>

                <a class="text-zinc-200 mt-5 font-bold svelte-194nj6y" href="../docs/animators.html">Animators</a>
                <a href="../docs/animators.html#curve" class="svelte-194nj6y">Curve Animator</a>
                <a href="../docs/animators.html#lerp" class="svelte-194nj6y">Lerp Animator</a>
                <a href="../docs/animators.html#rb" class="svelte-194nj6y">Rigid Body Animator</a>
                <a href="../docs/animators.html#layouts" class="svelte-194nj6y">Animating Layouts</a>

                <b class="text-zinc-200 mt-5">Click &amp; Drag</b>
                <a href="../docs/interactable.html" class="svelte-194nj6y">Interactable</a>
                <a href="../docs/interactable.html#dragTarget" class="svelte-194nj6y">Drag Target</a>
                <a href="../docs/xr.html" class="svelte-194nj6y">XR Interactions <span class="ml-2 text-xs font-bold text-blue-300">(v3.2)</span></a>
                <a href="../docs/interactable.html#customInputProvider" class="svelte-194nj6y">Custom Input Provider</a>

                <b class="text-zinc-200 mt-5">Data Binding</b>
                <a href="../docs/cloner.html" class="svelte-194nj6y">Cloner</a>
                <a href="../docs/cloner.html#databinding" class="svelte-194nj6y">Data Binding</a>

                <b class="text-zinc-200 mt-5">Advanced</b>
                <a href="../docs/pipeline.html" class="svelte-194nj6y">Flexalon Pipeline</a>
                <a href="../docs/customLayout.html" class="svelte-194nj6y">Custom Layouts</a>
                <a href="../docs/customAnimators.html" class="svelte-194nj6y">Custom Animators</a>
                <a href="../docs/adapters.html" class="svelte-194nj6y">Adapters</a>

                <a class="text-zinc-200 mt-5 font-bold svelte-194nj6y" href="../docs/api/Flexalon.html">Scripting API</a>
                <div class="mb-20"></div></div></div>
        
        <div class="flex flex-col w-full p-10 lg:ml-[300px]"><div class="doc svelte-194nj6y">

<div class="text-white text-2xl mb-10">Animators</div>

<p>You can create interesting effects by allowing objects to animate between layout positions.
    Flexalon provides three components to do animations, and you can create your own by implementing the FlexalonTransformUpdater interface.
</p>

<p id="curve" class="text-xl mt-10">Curve Animator</p>
<p>The Curve Animator animates objects along specified curve.
    The curve is restarted each time the layout results changes.
    This is ideal for scenarios in which the layout results do not change often.
</p>
<div class="flex m-5 mb-10"><video muted autoplay loop playsinline><source src="https://www.flexalon.com/videos/doc-reorder.mp4"></video></div>
<p>In this drag and drop example, the squares are positioned in a flexible layout. A
    drag-drop script (included in the samples) handles mouse input to drag the selected object and swap its position in the layout.
    Each square has a Curve Animator, which ensures the swapping happens smoothly.
</p>
<div class="flex ml-10 m-5 mb-10"><img src="../images/doc-curve-animator.png" alt="Curve Animator Options"></div>

<table><tr><td>Animate In World Space</td>
        <td>Determines if the object should be animated in world space or local space. Use local space if the parent is moving or animating and you want the parent and child to stick together.</td></tr>
    <tr><td>Curve</td>
        <td>The curve to apply. Should begin at 0 and end at 1.</td></tr>
    <tr><td>Animate Position</td>
        <td>Determines if the position should be animated.</td></tr>
    <tr><td>Animate Rotation</td>
        <td>Determines if the rotation should be animated.</td></tr>
    <tr><td>Animate Scale</td>
        <td>Determines if the scale should be animated.</td></tr></table>

<p id="lerp" class="text-xl mt-10">Lerp Animator</p>
<p>The lerp animator constantly performs a linear interpolation between the object&#39;s current position and its layout position.
    This is useful if the layout position is continuously changing.
</p>
<div class="flex m-5 mb-10"><video muted autoplay loop playsinline><source src="https://www.flexalon.com/videos/doc-snake.mp4"></video></div>
<p>In this example, each block uses a Flexalon Constraint to position itself on top
    of the one beneath it. When the user drags the white block, it cause the one above to follow, which causes
    the one above that to follow, and so on.
</p>
<p>Since we are constantly changing the position of the white block,
    a Curve Animator would not work well, as the curve would have to restart on each frame. Instead, each block
    has a Lerp Animator, which animates the blocks smoothly, increasing their speed as needed when the blocks get farther away.
</p>
<div class="flex ml-10 m-5 mb-10"><img src="../images/doc-lerp-animator.png" alt="Lerp Animator Options"></div>

<table><tr><td>Animate In World Space</td>
        <td>Determines if the object should be animated in world space or local space. Use local space if the parent is moving or animating and you want the parent and child to stick together.</td></tr>
    <tr><td>Interpolation Speed</td>
        <td>Amount the object should be interpolated towards the target at each frame. This value is multiplied by Time.deltaTime.</td></tr>
    <tr><td>Animate Position</td>
        <td>Determines if the position should be animated.</td></tr>
    <tr><td>Animate Rotation</td>
        <td>Determines if the rotation should be animated.</td></tr>
    <tr><td>Animate Scale</td>
        <td>Determines if the scale should be animated.</td></tr></table>

<p id="rb" class="text-xl mt-10">Rigid Body Animator</p>
<p>If you add a Rigid Body to an object which is managed by Flexalon, then the physics system will compete with Flexalon for control over the object&#39;s position and rotation.
    Adding a Rigid Body animator will resolve this by applying forces to the rigid body component instead of changing the transform directly.
</p>
<div class="flex m-5 mb-10"><video muted autoplay loop playsinline><source src="https://www.flexalon.com/videos/doc-chess-physics.mp4"></video></div>
<p>In this example, the tiles and chess pieces are all positioned with grid layouts and all have Rigid Body Animators.
    When the bowling ball disturbes their positions, the animators use forces to push the objects back into their layout positions.
</p>
<div class="flex ml-10 m-5 mb-10"><img src="../images/doc-rb-animator.png" alt="Rigid Body Animator Options"></div>

<table><tr><td>Position Force</td>
        <td>How much force should be applied each frame to move the object to the layout position.</td></tr>
    <tr><td>Rotation Force</td>
        <td>How much force should be applied each frame to rotation the object to the layout rotation.</td></tr>
    <tr><td>Scale Interpolation Speed</td>
        <td>Amount the object&#39;s scale should be interpolated towards the layout size at each frame. This value is multiplied by Time.deltaTime.</td></tr></table>

<p id="layouts" class="text-xl mt-10">Animating Layouts</p>
<p>You can also create interesting motions by animating different layout properties using standard Unity animators. For example,
    you can animate the Start At property of the curve layout to move objects along the curve.
    <a href="https://docs.unity3d.com/Manual/AnimationOverview.html">Learn more about using standard Unity animators.</a></p>

<div class="flex m-5 mb-10"><video muted autoplay loop playsinline><source src="https://www.flexalon.com/videos/doc-flying-text.mp4"></video></div>

<p>In this example, we have a separate object for each letter positioned aloing a Curve Layout.
    A Unity Animator is configured to modify the curve layout&#39;s &#39;Start At&#39; property over time.
</p></div></div></div>
</div>


			
			
		</div>
	</body>
</html>
