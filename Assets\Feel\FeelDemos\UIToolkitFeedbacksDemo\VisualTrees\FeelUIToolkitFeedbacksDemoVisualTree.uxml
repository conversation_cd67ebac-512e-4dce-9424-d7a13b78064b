<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns:uie="UnityEditor.UIElements" editor-extension-mode="False">
    <Style src="../Stylesheets/FeelUIToolkitFeedbacksDemoStylesheet.uss?fileID=7433441132597879392&amp;guid=4b112bc2d5df4c247864fd3499a53d9e&amp;type=3#FeelUIToolkitFeedbacksDemoStylesheet" />
    <ui:VisualElement name="MainContainer" class="MainContainer">
        <ui:Label text="FEEL UI Toolkit Demo" display-tooltip-when-elided="true" name="Title" style="flex-basis: 10%; justify-content: center; -unity-text-align: middle-center; -unity-font: url(&quot;../Fonts/UIToolkitDemo_BigJohn.otf?fileID=12800000&amp;guid=3258cbeb41f23b545a323883d7b047b9&amp;type=3#UIToolkitDemo_BigJohn&quot;); -unity-font-style: normal; font-size: 40px; -unity-font-definition: initial; background-color: rgb(255, 196, 0); color: rgb(0, 0, 0); width: 100%;" />
        <ui:VisualElement name="Container" style="flex-grow: 1; flex-basis: 50%; -unity-background-image-tint-color: rgb(31, 31, 31); flex-direction: row; align-items: center; background-color: rgba(31, 31, 31, 0); justify-content: center;">
            <ui:VisualElement name="DemoFace" style="background-image: none; width: 128px; height: 128px;" />
            <ui:Label text="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum." display-tooltip-when-elided="true" name="DemoText" class="MainText" style="-unity-font: url(&quot;../Fonts/UIToolkitDemo_Lato.ttf?fileID=12800000&amp;guid=6f8c7831e01d1074488720a8bac8a393&amp;type=3#UIToolkitDemo_Lato&quot;);" />
        </ui:VisualElement>
        <ui:VisualElement name="Buttons" style="background-color: rgba(31, 31, 31, 0); display: flex; flex-grow: 0; flex-basis: 40%; justify-content: center; flex-direction: row; height: 312px; position: relative; left: 0; top: 0; right: 0; bottom: 0; flex-wrap: wrap; flex-shrink: 1; align-items: flex-end; padding-left: 20px; padding-right: 20px; padding-top: 20px; padding-bottom: 20px; margin-left: 0; margin-right: 0; margin-top: 0; margin-bottom: 0; -unity-background-image-tint-color: rgb(31, 31, 31);">
            <ui:Button text="Background Color" display-tooltip-when-elided="true" name="BtnBackgroundColor" class="button nop" />
            <ui:Button text="Border Color" display-tooltip-when-elided="true" name="BtnBorderColor" enable-rich-text="false" class="button FeelBackground" />
            <ui:Button text="Border Radius" display-tooltip-when-elided="true" name="BtnBorderRadius" class="button" />
            <ui:Button text="Border Width" display-tooltip-when-elided="true" name="BtnBorderWidth" class="button" />
            <ui:Button text="Class" display-tooltip-when-elided="true" name="BtnClass" class="button" />
            <ui:Button text="Font Size" display-tooltip-when-elided="true" name="BtnFontSize" class="button" />
            <ui:Button text="Image Tint" display-tooltip-when-elided="true" name="BtnTint" class="button" />
            <ui:Button text="Opacity" display-tooltip-when-elided="true" name="BtnOpacity" class="button" />
            <ui:Button text="Rotate" display-tooltip-when-elided="true" name="BtnRotate" class="button" />
            <ui:Button text="Scale" display-tooltip-when-elided="true" name="BtnScale" class="button" />
            <ui:Button text="Size" display-tooltip-when-elided="true" name="BtnSize" class="button" />
            <ui:Button text="Stylesheet" display-tooltip-when-elided="true" name="BtnStylesheet" class="button" />
            <ui:Button text="Text" display-tooltip-when-elided="true" name="BtnText" class="button" />
            <ui:Button text="Text Color" display-tooltip-when-elided="true" name="BtnTextColor" class="button" />
            <ui:Button text="Translate" display-tooltip-when-elided="true" name="BtnTranslate" class="button" />
            <ui:Button text="Visible" display-tooltip-when-elided="true" name="BtnVisible" class="button" style="flex-wrap: nowrap;" />
        </ui:VisualElement>
    </ui:VisualElement>
</ui:UXML>
