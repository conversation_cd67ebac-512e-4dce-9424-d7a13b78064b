using System.Collections.Generic;
using UnityEngine;

namespace ExtendedImportSettings
{
    [System.Serializable]
    public class UVScaleOffsetData : PerMeshDataBase
    {
        public Vector2 Scale = Vector2.one;
        public Vector2 Offset = Vector2.zero;
        public UVChannel UVChannel;
    }
    
    [System.Serializable]
    public class UVScaleOffsetModule : PerMeshModuleBase<UVScaleOffsetData>
    {
        protected override void OnPostprocessModelPerMesh(Mesh mesh, UVScaleOffsetData data,
            GameObject meshGameObject, GameObject rootGameObject)
        {
            List<Vector2> uvs = new ();
            var uvIndexes = data.UVChannel.ToIndexes();
            foreach (var uvIndex in uvIndexes)
            {
                uvs.Clear();
                mesh.GetUVs(uvIndex, uvs);

                for (var i = 0; i < uvs.Count; i++)
                    uvs[i] = (uvs[i] + data.Offset) * data.Scale;
                
                mesh.SetUVs(uvIndex, uvs);
            }
        }
    }
}