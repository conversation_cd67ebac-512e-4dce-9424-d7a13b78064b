%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 10
  m_Fog: 1
  m_FogColor: {r: 0.18431373, g: 0.18431373, b: 0.18431373, a: 0}
  m_FogMode: 1
  m_FogDensity: 0.005
  m_LinearFogStart: 0
  m_LinearFogEnd: 1000
  m_AmbientSkyColor: {r: 0.20588237, g: 0.20588237, b: 0.20588237, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 3
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 10304, guid: 0000000000000000f000000000000000, type: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &3
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 13
  m_BakeOnSceneLoad: 0
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 0
    m_EnableRealtimeLightmaps: 0
  m_LightmapEditorSettings:
    serializedVersion: 12
    m_Resolution: 2
    m_BakeResolution: 40
    m_AtlasSize: 1024
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_ExtractAmbientOcclusion: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_ReflectionCompression: 2
    m_MixedBakeMode: 2
    m_BakeBackend: 0
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 500
    m_PVRBounces: 2
    m_PVREnvironmentSampleCount: 500
    m_PVREnvironmentReferencePointCount: 2048
    m_PVRFilteringMode: 2
    m_PVRDenoiserTypeDirect: 0
    m_PVRDenoiserTypeIndirect: 0
    m_PVRDenoiserTypeAO: 0
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVREnvironmentMIS: 0
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ExportTrainingData: 0
    m_TrainingDataDestination: TrainingData
    m_LightProbeSampleCountMultiplier: 4
  m_LightingDataAsset: {fileID: 0}
  m_LightingSettings: {fileID: 4890085278179872738, guid: 56ccb14b663241c4ea4b7574a38ccce8, type: 2}
--- !u!196 &4
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 3
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    buildHeightMesh: 0
    maxJobWorkers: 0
    preserveTilesOutsideBounds: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!1 &67210247
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 67210250}
  - component: {fileID: 67210249}
  - component: {fileID: 67210248}
  m_Layer: 0
  m_Name: EventSystem
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &67210248
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 67210247}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4f231c4fb786f3946a6b90b886c48677, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_SendPointerHoverToParent: 1
  m_HorizontalAxis: Horizontal
  m_VerticalAxis: Vertical
  m_SubmitButton: Submit
  m_CancelButton: Cancel
  m_InputActionsPerSecond: 10
  m_RepeatDelay: 0.5
  m_ForceModuleActive: 0
--- !u!114 &67210249
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 67210247}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 76c392e42b5098c458856cdf6ecaaaa1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_FirstSelected: {fileID: 0}
  m_sendNavigationEvents: 1
  m_DragThreshold: 5
--- !u!4 &67210250
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 67210247}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &115526031
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 115526033}
  - component: {fileID: 115526032}
  m_Layer: 0
  m_Name: Generated Optimizers Manager
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &115526032
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 115526031}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 14abe8e040d5f4d42bf57a7642a6fae9, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  ExistThroughScenes: 0
  GlobalMaxDistanceMultiplier: 1
  SingleContainerCapacity: 300
  DrawHumanSizeRefIcon: 0
  UpdateOptimizersSystem: 1
  TargetCamera: {fileID: 656210485}
  GetCameraAfter: 0
  DebugProgressiveCasting: 0
  DebugProgrFreq: 1
  DebugProgrAllAlpha: 0
  notContainedStaticOptimizers: []
  notContainedDynamicOptimizers: []
  notContainedEffectiveOptimizers: []
  notContainedTriggerOptimizers: []
  Advanced: 0
  Debugging: 0
  DetectCameraFreeze: 0
  WorldScale: 1.73
  MoveTreshold: 0.011533334
  UpdateBoost: 0
  Distances:
  - 103.8
  - 402.225
  - 700.65
  - 999.075
  _editor_drawSetup: 1
  _editor_drawDynamicSetup: 0
  _editor_drawTools: 0
  _editor_drawDOTS: 0
  GizmosAlpha: 0.75
  HumanScaleGuide: {fileID: 2800000, guid: 0b4f6f393aad1744ca4d960ba4a6c509, type: 3}
  _editorDrawSphere1: 0
  _editorSphere1Color: {r: 1, g: 0.4, b: 0.1, a: 0.6}
  _editorDrawSphere2: 0
  _editorSphere2Color: {r: 0.9, g: 0.75, b: 0, a: 0.6}
  _editorDrawSphere3Origin: {x: 0, y: 0, z: 0}
  _editorDrawSphere3: 0
  _editorSphere3Color: {r: 0.2, g: 0.2, b: 1, a: 0.5}
  _editorDrawSphere4Origin: {x: 0, y: 0, z: 0}
  _editorDrawSphere4: 0
  _editorSphere4Color: {r: 0.8, g: 0.1, b: 0.1, a: 0.75}
--- !u!4 &115526033
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 115526031}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &118799962
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 118799966}
  - component: {fileID: 118799965}
  - component: {fileID: 118799964}
  - component: {fileID: 118799963}
  - component: {fileID: 118799967}
  m_Layer: 0
  m_Name: Ground
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!23 &118799963
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 118799962}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 1476578177}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 1
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &118799964
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 118799962}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!33 &118799965
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 118799962}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &118799966
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 118799962}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -27, y: -3, z: 372}
  m_LocalScale: {x: 99999, y: 0.3, z: 99999}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &118799967
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 118799962}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6604dc3407fb5f64f8fed44b2a0b3b9a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  TextureProperty: _MainTex
  ScaleValues: {x: 1, y: 1}
  EqualDimensions: 1
--- !u!1 &166772413
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 166772414}
  - component: {fileID: 166772417}
  - component: {fileID: 166772416}
  - component: {fileID: 166772415}
  m_Layer: 5
  m_Name: Text (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &166772414
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 166772413}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1315008426}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 1, y: 0}
  m_AnchorMax: {x: 1, y: 0}
  m_AnchoredPosition: {x: -5, y: 5}
  m_SizeDelta: {x: 491, y: 450}
  m_Pivot: {x: 1, y: 0}
--- !u!114 &166772415
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 166772413}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e19747de3f5aca642ab2be37e372fb86, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_EffectColor: {r: 0, g: 0, b: 0, a: 0.5}
  m_EffectDistance: {x: 1, y: -1}
  m_UseGraphicAlpha: 1
--- !u!114 &166772416
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 166772413}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5f7201a12d95ffc409449d95f23cf332, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_FontData:
    m_Font: {fileID: 10102, guid: 0000000000000000e000000000000000, type: 0}
    m_FontSize: 14
    m_FontStyle: 1
    m_BestFit: 0
    m_MinSize: 10
    m_MaxSize: 40
    m_Alignment: 8
    m_AlignByGeometry: 0
    m_RichText: 1
    m_HorizontalOverflow: 0
    m_VerticalOverflow: 0
    m_LineSpacing: 1
  m_Text: There is static optimizing method on optimizer in this scene. If you want
    dynamic detection, change optimizing method.
--- !u!222 &166772417
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 166772413}
  m_CullTransparentMesh: 1
--- !u!1 &186419946 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 100000, guid: 1a99dc8e1c305a54e8409341de7df888, type: 3}
  m_PrefabInstance: {fileID: 273917287}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &186419947
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 186419946}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 488b95f6070988f45b11ba45372d0c65, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  AddToContainer: 1
  _editor_DrawSetup: 1
  _editor_DrawOptimizeList: 0
  _editor_DrawAddFeatures: 0
  _editor_DrawLODLevelsSetup: 1
  _editor_DrawExtra: 0
  _editor_horizontal: 1
  _editor_category: 0
  Editor_InIsolatedScene: 0
  Editor_JustCreated: 1
  UseDOTS: 0
  DOTSObstacleType: 2
  DOTSDetection: 0
  DOTSMeshData: []
  DOTSOffset: {x: 0, y: 0, z: 0}
  DOTSSize: {x: 1, y: 1, z: 1}
  DOTSRadius: 1
  UseMultiShape: 0
  AutoPrecision: 0.25
  AutoReferenceMesh: {fileID: 0}
  DrawPositionHandles: 1
  ScalingHandles: 1
  Shapes: []
  ShapePos: []
  ShapeRadius: []
  UseObstacleDetection: 0
  CoveragePrecision: 1
  CoverageScale: 1
  CoverageMask:
    serializedVersion: 2
    m_Bits: 1
  CustomCoveragePoints: 0
  CoverageOffsets: []
  ignoredObstacleColliders: []
  OnlyCamCollLayer:
    serializedVersion: 2
    m_Bits: 0
  LODLevels: 5
  preLODLevels: 5
  MaxDistance: 150
  OptimizingMethod: 2
  CullIfNotSee: 1
  DetectionRadius: 5.4711537
  DetectionBounds: {x: 6.3175435, y: 6.317546, z: 6.3175454}
  Hideable: 0
  DetectionOffset: {x: 0, y: -0.000000059604645, z: 0}
  GizmosAlpha: 1
  DrawGizmos: 1
  FadeDuration: 1
  FadeViewVisibility: 0
  DeactivateObject: 0
  LODPercent:
  - 0.06994595
  - 0.19494595
  - 0.374
  - 0.5800811
  - 1
  AutoDistance: 0
  AutoDistanceFactor: 0
  DrawAutoDistanceToggle: 1
  HiddenCullAt: -1
  LimitLODLevels: 0
  UnlockFirstLOD: 0
  DrawGeneratedPrefabInfo: 0
  DrawDeactivateToggle: 1
  OptimizationTypes: {fileID: 0}
  ToOptimize:
  - constructed: 1
    ToOptimizeIndex: 0
    optimizer: {fileID: 186419947}
    Component: {fileID: 186419948}
    Version: 0
    lockFirstLOD: 1
    editorHeader: Optim
    drawProperties: 1
    LODs_Particle: []
    LODs_Light: []
    LODs_Mono: []
    LODs_Renderer: []
    LODs_NavMesh: []
    LODs_Audio: []
    LODs_Rigidbody: []
    LODs_LODGroup:
    - SetDisabled: 0
      _version: 2
      _Locked: 0
      cmp: {fileID: 186419948}
      UnityLODLevel: 0
      shaderParam: _Cutoff
      ColorParameter: 0
      shaderVisibleValue: 0
      shaderInvisibleValue: 1
      crossfadeHelper: 0
    - SetDisabled: 0
      _version: 2
      _Locked: 0
      cmp: {fileID: 186419948}
      UnityLODLevel: 1
      shaderParam: _Cutoff
      ColorParameter: 0
      shaderVisibleValue: 0
      shaderInvisibleValue: 1
      crossfadeHelper: 0
    - SetDisabled: 0
      _version: 2
      _Locked: 0
      cmp: {fileID: 186419948}
      UnityLODLevel: 2
      shaderParam: _Cutoff
      ColorParameter: 0
      shaderVisibleValue: 0
      shaderInvisibleValue: 1
      crossfadeHelper: 0
    - SetDisabled: 0
      _version: 2
      _Locked: 0
      cmp: {fileID: 186419948}
      UnityLODLevel: 3
      shaderParam: _Cutoff
      ColorParameter: 0
      shaderVisibleValue: 0
      shaderInvisibleValue: 1
      crossfadeHelper: 0
    - SetDisabled: 0
      _version: 2
      _Locked: 0
      cmp: {fileID: 186419948}
      UnityLODLevel: 4
      shaderParam: _Cutoff
      ColorParameter: 0
      shaderVisibleValue: 0
      shaderInvisibleValue: 1
      crossfadeHelper: 0
    - SetDisabled: 1
      _version: 2
      _Locked: 0
      cmp: {fileID: 186419948}
      UnityLODLevel: 5
      shaderParam: _Cutoff
      ColorParameter: 0
      shaderVisibleValue: 0
      shaderInvisibleValue: 1
      crossfadeHelper: 0
    - SetDisabled: 1
      _version: 2
      _Locked: 0
      cmp: {fileID: 186419948}
      UnityLODLevel: 5
      shaderParam: _Cutoff
      ColorParameter: 0
      shaderVisibleValue: 0
      shaderInvisibleValue: 1
      crossfadeHelper: 0
    Ini_Particle:
      SetDisabled: 0
      QLowerer: 1
      _Locked: 0
      cmp: {fileID: 0}
      EmmissionAmount: 1
      BurstsAmount: 1
      ParticleSizeMul: 1
      MaxParticlAmount: 1
      OverDistanceMul: 1
      LifetimeAlpha: 1
      ChangeBursts: 1
      ChangeGradients: 1
      ColorOverLifetime:
        m_Mode: 0
        m_GradientMin:
          serializedVersion: 2
          key0: {r: 1, g: 1, b: 1, a: 1}
          key1: {r: 1, g: 1, b: 1, a: 1}
          key2: {r: 0, g: 0, b: 0, a: 0}
          key3: {r: 0, g: 0, b: 0, a: 0}
          key4: {r: 0, g: 0, b: 0, a: 0}
          key5: {r: 0, g: 0, b: 0, a: 0}
          key6: {r: 0, g: 0, b: 0, a: 0}
          key7: {r: 0, g: 0, b: 0, a: 0}
          ctime0: 0
          ctime1: 65535
          ctime2: 0
          ctime3: 0
          ctime4: 0
          ctime5: 0
          ctime6: 0
          ctime7: 0
          atime0: 0
          atime1: 65535
          atime2: 0
          atime3: 0
          atime4: 0
          atime5: 0
          atime6: 0
          atime7: 0
          m_Mode: 0
          m_ColorSpace: -1
          m_NumColorKeys: 2
          m_NumAlphaKeys: 2
        m_GradientMax:
          serializedVersion: 2
          key0: {r: 1, g: 1, b: 1, a: 1}
          key1: {r: 1, g: 1, b: 1, a: 1}
          key2: {r: 0, g: 0, b: 0, a: 0}
          key3: {r: 0, g: 0, b: 0, a: 0}
          key4: {r: 0, g: 0, b: 0, a: 0}
          key5: {r: 0, g: 0, b: 0, a: 0}
          key6: {r: 0, g: 0, b: 0, a: 0}
          key7: {r: 0, g: 0, b: 0, a: 0}
          ctime0: 0
          ctime1: 65535
          ctime2: 0
          ctime3: 0
          ctime4: 0
          ctime5: 0
          ctime6: 0
          ctime7: 0
          atime0: 0
          atime1: 65535
          atime2: 0
          atime3: 0
          atime4: 0
          atime5: 0
          atime6: 0
          atime7: 0
          m_Mode: 0
          m_ColorSpace: -1
          m_NumColorKeys: 2
          m_NumAlphaKeys: 2
        m_ColorMin: {r: 0, g: 0, b: 0, a: 0}
        m_ColorMax: {r: 0, g: 0, b: 0, a: 0}
    Ini_Light:
      SetDisabled: 0
      _Locked: 0
      cmp: {fileID: 0}
      IntensityMul: 1
      RangeMul: 1
      ShadowsMode: 2
      ShadowsStrength: 1
      RenderMode: 0
      ChangeIntensity: 1
    Ini_Mono:
      SetDisabled: 0
      ver: 0
      _Locked: 0
      cmp: {fileID: 0}
      BaseLOD: 0
      Event:
        m_PersistentCalls:
          m_Calls: []
      Parameters: []
      NotSupported: []
    Ini_Rend:
      SetDisabled: 0
      _version: 1
      _Locked: 0
      cmp: {fileID: 0}
      UseShadows: 1
      MotionVectors: 1
      SkinnedQuality: 0
      shaderParam: _Transparency
      ColorParameter: 0
      targetParamValue: 1
    Ini_Nav:
      SetDisabled: 0
      _Locked: 0
      cmp: {fileID: 0}
      Priority: 1
      Quality: 4
    Ini_Audio:
      SetDisabled: 0
      _Locked: 0
      cmp: {fileID: 0}
      PriorityFactor: 1
      Volume: 1
    Ini_Rigidbody:
      SetDisabled: 0
      _Locked: 0
      cmp: {fileID: 0}
      DetectCollisions: 0
      IsKinematic: 0
      Interpolation: 0
      CollisionMode: 0
      TryTriggerSleep: 0
      TriggerWakeup: 0
      ChangeIsKinematic: 1
    Ini_LODGroup:
      SetDisabled: 0
      _version: 2
      _Locked: 0
      cmp: {fileID: 0}
      UnityLODLevel: 0
      shaderParam: _Transparency
      ColorParameter: 0
      shaderVisibleValue: 1
      shaderInvisibleValue: 0
      crossfadeHelper: 0
    eOptimizer: {fileID: 186419947}
    ControlerType: 8
--- !u!205 &186419948 stripped
LODGroup:
  m_CorrespondingSourceObject: {fileID: 20500000, guid: 1a99dc8e1c305a54e8409341de7df888, type: 3}
  m_PrefabInstance: {fileID: 273917287}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &236902162
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 236902163}
  - component: {fileID: 236902166}
  - component: {fileID: 236902165}
  - component: {fileID: 236902164}
  m_Layer: 5
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &236902163
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 236902162}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1315008426}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 10, y: -10}
  m_SizeDelta: {x: 491, y: 450}
  m_Pivot: {x: 0, y: 1}
--- !u!114 &236902164
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 236902162}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e19747de3f5aca642ab2be37e372fb86, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_EffectColor: {r: 0, g: 0, b: 0, a: 0.5}
  m_EffectDistance: {x: 1, y: -1}
  m_UseGraphicAlpha: 1
--- !u!114 &236902165
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 236902162}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5f7201a12d95ffc409449d95f23cf332, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_FontData:
    m_Font: {fileID: 10102, guid: 0000000000000000e000000000000000, type: 0}
    m_FontSize: 12
    m_FontStyle: 1
    m_BestFit: 0
    m_MinSize: 1
    m_MaxSize: 40
    m_Alignment: 0
    m_AlignByGeometry: 0
    m_RichText: 1
    m_HorizontalOverflow: 0
    m_VerticalOverflow: 0
    m_LineSpacing: 1
  m_Text: "Changing LOD level parameters for components\ncan be done smoothly with
    transitions.\nTransitions are requiring existence of FOptimizers_Manager on the
    scene. (added automatically if you not do this)\n\nMove camera nearer / further
    from object to notice smooth transition.\n\nSelect \"ParticleAndLight\" GameObject
    and follow LOD bar inside Optimizer's inspector window.\n\nOpen \"Stats\" tab
    on the right top of \"Game\" view to check how vertices are dropping when going
    further away. \n(disable canvas to remove unnecessary tris. count)\n\nIn additional
    example of crossfading LOD Group and fading out mesh renderer with Cutoff shader
    parameter."
--- !u!222 &236902166
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 236902162}
  m_CullTransparentMesh: 1
--- !u!1001 &273917287
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 100000, guid: 1a99dc8e1c305a54e8409341de7df888, type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 400000, guid: 1a99dc8e1c305a54e8409341de7df888, type: 3}
      propertyPath: m_RootOrder
      value: 11
      objectReference: {fileID: 0}
    - target: {fileID: 400000, guid: 1a99dc8e1c305a54e8409341de7df888, type: 3}
      propertyPath: m_LocalPosition.x
      value: -12
      objectReference: {fileID: 0}
    - target: {fileID: 400000, guid: 1a99dc8e1c305a54e8409341de7df888, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.55
      objectReference: {fileID: 0}
    - target: {fileID: 400000, guid: 1a99dc8e1c305a54e8409341de7df888, type: 3}
      propertyPath: m_LocalPosition.z
      value: -15.3
      objectReference: {fileID: 0}
    - target: {fileID: 400000, guid: 1a99dc8e1c305a54e8409341de7df888, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.50090677
      objectReference: {fileID: 0}
    - target: {fileID: 400000, guid: 1a99dc8e1c305a54e8409341de7df888, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 400000, guid: 1a99dc8e1c305a54e8409341de7df888, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.8655013
      objectReference: {fileID: 0}
    - target: {fileID: 400000, guid: 1a99dc8e1c305a54e8409341de7df888, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 400000, guid: 1a99dc8e1c305a54e8409341de7df888, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 119.88
      objectReference: {fileID: 0}
    - target: {fileID: 2300000, guid: 1a99dc8e1c305a54e8409341de7df888, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: eb1f1972573e7664ea8d2c09971ebab5, type: 2}
    - target: {fileID: 2300002, guid: 1a99dc8e1c305a54e8409341de7df888, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: eb1f1972573e7664ea8d2c09971ebab5, type: 2}
    - target: {fileID: 2300004, guid: 1a99dc8e1c305a54e8409341de7df888, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: eb1f1972573e7664ea8d2c09971ebab5, type: 2}
    - target: {fileID: 2300006, guid: 1a99dc8e1c305a54e8409341de7df888, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: eb1f1972573e7664ea8d2c09971ebab5, type: 2}
    - target: {fileID: 2300008, guid: 1a99dc8e1c305a54e8409341de7df888, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: eb1f1972573e7664ea8d2c09971ebab5, type: 2}
    - target: {fileID: 20500000, guid: 1a99dc8e1c305a54e8409341de7df888, type: 3}
      propertyPath: m_LODs.Array.data[0].screenRelativeHeight
      value: 0.37569717
      objectReference: {fileID: 0}
    - target: {fileID: 20500000, guid: 1a99dc8e1c305a54e8409341de7df888, type: 3}
      propertyPath: m_LODs.Array.data[1].screenRelativeHeight
      value: 0.1804129
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 100000, guid: 1a99dc8e1c305a54e8409341de7df888, type: 3}
      insertIndex: -1
      addedObject: {fileID: 186419947}
  m_SourcePrefab: {fileID: 100100000, guid: 1a99dc8e1c305a54e8409341de7df888, type: 3}
--- !u!1001 &410564920
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 4565500318353682, guid: 1a5086968a6f9884493fff8f38d9d0ea, type: 3}
      propertyPath: m_RootOrder
      value: 10
      objectReference: {fileID: 0}
    - target: {fileID: 4565500318353682, guid: 1a5086968a6f9884493fff8f38d9d0ea, type: 3}
      propertyPath: m_LocalPosition.x
      value: -23.17454
      objectReference: {fileID: 0}
    - target: {fileID: 4565500318353682, guid: 1a5086968a6f9884493fff8f38d9d0ea, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.94698954
      objectReference: {fileID: 0}
    - target: {fileID: 4565500318353682, guid: 1a5086968a6f9884493fff8f38d9d0ea, type: 3}
      propertyPath: m_LocalPosition.z
      value: -13.548706
      objectReference: {fileID: 0}
    - target: {fileID: 4565500318353682, guid: 1a5086968a6f9884493fff8f38d9d0ea, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 4565500318353682, guid: 1a5086968a6f9884493fff8f38d9d0ea, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0.70710677
      objectReference: {fileID: 0}
    - target: {fileID: 4565500318353682, guid: 1a5086968a6f9884493fff8f38d9d0ea, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4565500318353682, guid: 1a5086968a6f9884493fff8f38d9d0ea, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0.0000000074505815
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 1a5086968a6f9884493fff8f38d9d0ea, type: 3}
--- !u!1 &469010370
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 469010372}
  - component: {fileID: 469010371}
  m_Layer: 0
  m_Name: Lod Text Writer
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &469010371
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 469010370}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 30986c01a4084f840b99ab499c01db8b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  optimizer: {fileID: 941187507}
  TextToWriteOn: {fileID: 1240395487}
--- !u!4 &469010372
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 469010370}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -98.58554, y: 118.28014, z: -52.170475}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &541863766
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 541863767}
  m_Layer: 0
  m_Name: -----------------------
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &541863767
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 541863766}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 101.85121, y: 19.63639, z: -250.3336}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &656210482
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 656210486}
  - component: {fileID: 656210485}
  - component: {fileID: 656210484}
  - component: {fileID: 656210483}
  - component: {fileID: 656210488}
  m_Layer: 0
  m_Name: Main Camera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!81 &656210483
AudioListener:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 656210482}
  m_Enabled: 1
--- !u!124 &656210484
Behaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 656210482}
  m_Enabled: 1
--- !u!20 &656210485
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 656210482}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 2
  m_BackGroundColor: {r: 0.18382353, g: 0.18382353, b: 0.18382353, a: 0}
  m_projectionMatrixMode: 1
  m_GateFitMode: 2
  m_FOVAxisMode: 0
  m_Iso: 200
  m_ShutterSpeed: 0.005
  m_Aperture: 16
  m_FocusDistance: 10
  m_FocalLength: 50
  m_BladeCount: 5
  m_Curvature: {x: 2, y: 11}
  m_BarrelClipping: 0.25
  m_Anamorphism: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.3
  far clip plane: 900
  field of view: 60
  orthographic: 0
  orthographic size: 5
  m_Depth: -1
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 1
  m_AllowMSAA: 0
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 0
  m_OcclusionCulling: 1
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!4 &656210486
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 656210482}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0008576903, y: 0.18463749, z: -0.00016113224, w: 0.9828063}
  m_LocalPosition: {x: -26.43454, y: 3.81, z: -20.737366}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0.1, y: 21.28, z: 0}
--- !u!114 &656210488
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 656210482}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2e401366475335a4c85c5431e4624158, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  SpeedMultiplier: 44
  AccelerationSmothnessValue: 10
  RotationSmothnessValue: 10
  MouseSensitivity: 5
  NeedRMB: 1
--- !u!1 &671842682
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 671842683}
  m_Layer: 0
  m_Name: -----------------------
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &671842683
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 671842682}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 101.85121, y: 19.63639, z: -250.3336}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &835911891
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 835911892}
  m_Layer: 0
  m_Name: -----------------------
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &835911892
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 835911891}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 101.85121, y: 19.63639, z: -250.3336}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &941187507 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 114950214877345748, guid: 1a5086968a6f9884493fff8f38d9d0ea, type: 3}
  m_PrefabInstance: {fileID: 410564920}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 488b95f6070988f45b11ba45372d0c65, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &1194901734
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1194901736}
  - component: {fileID: 1194901735}
  m_Layer: 0
  m_Name: Directional Light
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!108 &1194901735
Light:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1194901734}
  m_Enabled: 1
  serializedVersion: 11
  m_Type: 1
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_Intensity: 0.18
  m_Range: 10
  m_SpotAngle: 30
  m_InnerSpotAngle: 21.80208
  m_CookieSize: 10
  m_Shadows:
    m_Type: 2
    m_Resolution: -1
    m_CustomResolution: -1
    m_Strength: 1
    m_Bias: 0.05
    m_NormalBias: 0.4
    m_NearPlane: 0.2
    m_CullingMatrixOverride:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    m_UseCullingMatrixOverride: 0
  m_Cookie: {fileID: 0}
  m_DrawHalo: 0
  m_Flare: {fileID: 0}
  m_RenderMode: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingLayerMask: 1
  m_Lightmapping: 4
  m_LightShadowCasterMode: 0
  m_AreaSize: {x: 1, y: 1}
  m_BounceIntensity: 1
  m_ColorTemperature: 6570
  m_UseColorTemperature: 0
  m_BoundingSphereOverride: {x: 0, y: 0, z: 0, w: 0}
  m_UseBoundingSphereOverride: 0
  m_UseViewFrustumForShadowCasterCull: 1
  m_ForceVisible: 0
  m_ShadowRadius: 0
  m_ShadowAngle: 0
  m_LightUnit: 1
  m_LuxAtDistance: 1
  m_EnableSpotReflector: 1
--- !u!4 &1194901736
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1194901734}
  serializedVersion: 2
  m_LocalRotation: {x: 0.40821788, y: -0.23456968, z: 0.10938163, w: 0.8754261}
  m_LocalPosition: {x: 0.34, y: 4, z: -0.98}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 50, y: -30, z: 0}
--- !u!1 &1240395484
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1240395485}
  - component: {fileID: 1240395488}
  - component: {fileID: 1240395487}
  - component: {fileID: 1240395486}
  m_Layer: 5
  m_Name: Text (3)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1240395485
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1240395484}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1315008426}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 1}
  m_AnchorMax: {x: 0.5, y: 1}
  m_AnchoredPosition: {x: 0, y: -8}
  m_SizeDelta: {x: 495.14, y: 139.8}
  m_Pivot: {x: 0.5, y: 1}
--- !u!114 &1240395486
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1240395484}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e19747de3f5aca642ab2be37e372fb86, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_EffectColor: {r: 0, g: 0, b: 0, a: 0.5}
  m_EffectDistance: {x: 1, y: -1}
  m_UseGraphicAlpha: 1
--- !u!114 &1240395487
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1240395484}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5f7201a12d95ffc409449d95f23cf332, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.74643165, g: 0.8602941, b: 0.80846703, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_FontData:
    m_Font: {fileID: 10102, guid: 0000000000000000e000000000000000, type: 0}
    m_FontSize: 14
    m_FontStyle: 1
    m_BestFit: 0
    m_MinSize: 10
    m_MaxSize: 40
    m_Alignment: 1
    m_AlignByGeometry: 0
    m_RichText: 1
    m_HorizontalOverflow: 0
    m_VerticalOverflow: 0
    m_LineSpacing: 1
  m_Text: 'Current LOD:'
--- !u!222 &1240395488
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1240395484}
  m_CullTransparentMesh: 1
--- !u!1 &1315008422
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1315008426}
  - component: {fileID: 1315008425}
  - component: {fileID: 1315008424}
  - component: {fileID: 1315008423}
  m_Layer: 5
  m_Name: Canvas
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1315008423
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1315008422}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc42784cf147c0c48a680349fa168899, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 4294967295
--- !u!114 &1315008424
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1315008422}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0cd44c1031e13a943bb63640046fad76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UiScaleMode: 0
  m_ReferencePixelsPerUnit: 100
  m_ScaleFactor: 1
  m_ReferenceResolution: {x: 800, y: 600}
  m_ScreenMatchMode: 0
  m_MatchWidthOrHeight: 0
  m_PhysicalUnit: 3
  m_FallbackScreenDPI: 96
  m_DefaultSpriteDPI: 96
  m_DynamicPixelsPerUnit: 1
  m_PresetInfoIsWorld: 0
--- !u!223 &1315008425
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1315008422}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 0
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_VertexColorAlwaysGammaSpace: 0
  m_AdditionalShaderChannelsFlag: 0
  m_UpdateRectTransformForStandalone: 0
  m_SortingLayerID: 0
  m_SortingOrder: 0
  m_TargetDisplay: 0
--- !u!224 &1315008426
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1315008422}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0, y: 0, z: 0}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 236902163}
  - {fileID: 166772414}
  - {fileID: 1690978157}
  - {fileID: 1240395485}
  - {fileID: 1706786892}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0, y: 0}
--- !u!21 &1476578177
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: MAT_FBasic_Floor
  m_Shader: {fileID: 46, guid: 0000000000000000f000000000000000, type: 0}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords: []
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 10305, guid: 0000000000000000f000000000000000, type: 0}
        m_Scale: {x: 99999, y: 99999}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _BumpScale: 1
    - _Cutoff: 0.5
    - _DetailNormalMapScale: 1
    - _DstBlend: 0
    - _GlossMapScale: 1
    - _Glossiness: 0
    - _GlossyReflections: 1
    - _Metallic: 0
    - _Mode: 0
    - _OcclusionStrength: 1
    - _Parallax: 0.02
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _UVSec: 0
    - _ZWrite: 1
    m_Colors:
    - _Color: {r: 0.7647059, g: 0.7647059, b: 0.7647059, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
  m_BuildTextureStacks: []
  m_AllowLocking: 1
--- !u!1 &1522538435
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1522538436}
  m_Layer: 0
  m_Name: -----------------------
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1522538436
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1522538435}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 101.85121, y: 19.63639, z: -250.3336}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &1525156584
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 4291908794094286, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: m_RootOrder
      value: 12
      objectReference: {fileID: 0}
    - target: {fileID: 4291908794094286, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: m_LocalPosition.x
      value: -30.4
      objectReference: {fileID: 0}
    - target: {fileID: 4291908794094286, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: m_LocalPosition.y
      value: -2.85
      objectReference: {fileID: 0}
    - target: {fileID: 4291908794094286, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: m_LocalPosition.z
      value: -0.8
      objectReference: {fileID: 0}
    - target: {fileID: 4291908794094286, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.9721658
      objectReference: {fileID: 0}
    - target: {fileID: 4291908794094286, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4291908794094286, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.23429382
      objectReference: {fileID: 0}
    - target: {fileID: 4291908794094286, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4291908794094286, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 27.1
      objectReference: {fileID: 0}
    - target: {fileID: 114043524403747050, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: LODLevels
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114043524403747050, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: MaxDistance
      value: 25
      objectReference: {fileID: 0}
    - target: {fileID: 114043524403747050, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: FadeDuration
      value: 1.5
      objectReference: {fileID: 0}
    - target: {fileID: 114043524403747050, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: preLODLevels
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114043524403747050, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: DetectionRadius
      value: 9.4
      objectReference: {fileID: 0}
    - target: {fileID: 114043524403747050, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: DetectionBounds.x
      value: 14.376642
      objectReference: {fileID: 0}
    - target: {fileID: 114043524403747050, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: DetectionBounds.y
      value: 15.239832
      objectReference: {fileID: 0}
    - target: {fileID: 114043524403747050, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: DetectionBounds.z
      value: 9.571362
      objectReference: {fileID: 0}
    - target: {fileID: 114043524403747050, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: DetectionOffset.x
      value: 0.037692547
      objectReference: {fileID: 0}
    - target: {fileID: 114043524403747050, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: DetectionOffset.z
      value: -7.063671
      objectReference: {fileID: 0}
    - target: {fileID: 114043524403747050, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: LODPercent.Array.size
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114043524403747050, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: 'LODPercent.Array.data[0]'
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114043524403747050, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: 'LODPercent.Array.data[1]'
      value: 0.48027572
      objectReference: {fileID: 0}
    - target: {fileID: 114043524403747050, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: 'LODPercent.Array.data[2]'
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114043524403747050, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: 'LODPercent.Array.data[3]'
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114043524403747050, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: _editor_DrawOptimizeList
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 114043524403747050, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: ToOptimize.Array.data[0].Ini_Rend.cmp
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 114043524403747050, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: ToOptimize.Array.data[0].Ini_Rend._version
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 114043524403747050, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: ToOptimize.Array.data[0].LODs_Renderer.Array.size
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 114043524403747050, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: ToOptimize.Array.data[0].LODs_Renderer.Array.data[0]._version
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 114043524403747050, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: ToOptimize.Array.data[0].LODs_Renderer.Array.data[1]._version
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 114043524403747050, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: ToOptimize.Array.data[0].LODs_Renderer.Array.data[2]._version
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 114043524403747050, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: ToOptimize.Array.data[0].LODs_Renderer.Array.data[3]._version
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 114043524403747050, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: ToOptimize.Array.data[0].LODs_Renderer.Array.data[4]._version
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 114043524403747050, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: ToOptimize.Array.data[0].LODs_Renderer.Array.data[1].UseShadows
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114043524403747050, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: ToOptimize.Array.data[0].LODs_Renderer.Array.data[0].shaderParam
      value: _Cutoff
      objectReference: {fileID: 0}
    - target: {fileID: 114043524403747050, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: ToOptimize.Array.data[0].LODs_Renderer.Array.data[1].SetDisabled
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114043524403747050, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: ToOptimize.Array.data[0].LODs_Renderer.Array.data[1].shaderParam
      value: _Cutoff
      objectReference: {fileID: 0}
    - target: {fileID: 114043524403747050, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: ToOptimize.Array.data[0].LODs_Renderer.Array.data[2].SetDisabled
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114043524403747050, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: ToOptimize.Array.data[0].LODs_Renderer.Array.data[2].shaderParam
      value: _Cutoff
      objectReference: {fileID: 0}
    - target: {fileID: 114043524403747050, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: ToOptimize.Array.data[0].LODs_Renderer.Array.data[3].SetDisabled
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114043524403747050, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: ToOptimize.Array.data[0].LODs_Renderer.Array.data[3].shaderParam
      value: _Transparency
      objectReference: {fileID: 0}
    - target: {fileID: 114043524403747050, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: ToOptimize.Array.data[0].LODs_Renderer.Array.data[4].SetDisabled
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114043524403747050, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: ToOptimize.Array.data[0].LODs_Renderer.Array.data[4].shaderParam
      value: _Transparency
      objectReference: {fileID: 0}
    - target: {fileID: 114043524403747050, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: ToOptimize.Array.data[0].LODs_Renderer.Array.data[5].SetDisabled
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114043524403747050, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: ToOptimize.Array.data[0].LODs_Renderer.Array.data[5].shaderParam
      value: _Transparency
      objectReference: {fileID: 0}
    - target: {fileID: 114043524403747050, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: ToOptimize.Array.data[0].LODs_Renderer.Array.data[1].MotionVectors
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 114043524403747050, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: ToOptimize.Array.data[0].LODs_Renderer.Array.data[2].MotionVectors
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 114043524403747050, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: ToOptimize.Array.data[0].LODs_Renderer.Array.data[3].MotionVectors
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 114043524403747050, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: ToOptimize.Array.data[0].LODs_Renderer.Array.data[4].MotionVectors
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 114043524403747050, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: ToOptimize.Array.data[0].LODs_Renderer.Array.data[5].MotionVectors
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 114043524403747050, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: ToOptimize.Array.data[0].LODs_Renderer.Array.data[1].SkinnedQuality
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114043524403747050, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: ToOptimize.Array.data[0].LODs_Renderer.Array.data[2].SkinnedQuality
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114043524403747050, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: ToOptimize.Array.data[0].LODs_Renderer.Array.data[3].SkinnedQuality
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114043524403747050, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: ToOptimize.Array.data[0].LODs_Renderer.Array.data[4].SkinnedQuality
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114043524403747050, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: ToOptimize.Array.data[0].LODs_Renderer.Array.data[5].SkinnedQuality
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114043524403747050, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: ToOptimize.Array.data[0].LODs_Renderer.Array.data[0].targetParamValue
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 114043524403747050, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: ToOptimize.Array.data[0].LODs_Renderer.Array.data[1].targetParamValue
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114043524403747050, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: ToOptimize.Array.data[0].LODs_Renderer.Array.data[2].targetParamValue
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 114043524403747050, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
      propertyPath: ToOptimize.Array.data[0].LODs_Renderer.Array.data[3].targetParamValue
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: cbe44524c4ac4384bbd8b209fa091ca7, type: 3}
--- !u!1 &1690978156
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1690978157}
  - component: {fileID: 1690978159}
  - component: {fileID: 1690978158}
  m_Layer: 5
  m_Name: Text (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1690978157
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1690978156}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1315008426}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0.5}
  m_AnchorMax: {x: 0, y: 0.5}
  m_AnchoredPosition: {x: 5, y: -84.1}
  m_SizeDelta: {x: 700, y: 250}
  m_Pivot: {x: 0, y: 0.5}
--- !u!114 &1690978158
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1690978156}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5f7201a12d95ffc409449d95f23cf332, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 0.472}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_FontData:
    m_Font: {fileID: 10102, guid: 0000000000000000e000000000000000, type: 0}
    m_FontSize: 14
    m_FontStyle: 1
    m_BestFit: 0
    m_MinSize: 10
    m_MaxSize: 40
    m_Alignment: 3
    m_AlignByGeometry: 0
    m_RichText: 1
    m_HorizontalOverflow: 0
    m_VerticalOverflow: 0
    m_LineSpacing: 1
  m_Text: 'Fly with camera using ''WSAD'' keys

    Hold right mouse button and move
    mouse to rotate camera

    Hold shift to fly faster

'
--- !u!222 &1690978159
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1690978156}
  m_CullTransparentMesh: 1
--- !u!1 &1706786891
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1706786892}
  - component: {fileID: 1706786895}
  - component: {fileID: 1706786894}
  - component: {fileID: 1706786893}
  m_Layer: 5
  m_Name: Text (4)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1706786892
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1706786891}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1315008426}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 5, y: 5}
  m_SizeDelta: {x: 495.14, y: 139.79999}
  m_Pivot: {x: 0, y: 0}
--- !u!114 &1706786893
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1706786891}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e19747de3f5aca642ab2be37e372fb86, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_EffectColor: {r: 0, g: 0, b: 0, a: 0.5}
  m_EffectDistance: {x: 1, y: -1}
  m_UseGraphicAlpha: 1
--- !u!114 &1706786894
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1706786891}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5f7201a12d95ffc409449d95f23cf332, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.74643165, g: 0.8602941, b: 0.80846703, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_FontData:
    m_Font: {fileID: 10102, guid: 0000000000000000e000000000000000, type: 0}
    m_FontSize: 8
    m_FontStyle: 1
    m_BestFit: 0
    m_MinSize: 8
    m_MaxSize: 40
    m_Alignment: 6
    m_AlignByGeometry: 0
    m_RichText: 1
    m_HorizontalOverflow: 0
    m_VerticalOverflow: 0
    m_LineSpacing: 1
  m_Text: 'When transitioning to culling (last LOD) transition will take longer (~500%)

    because
    of waiting additional time (particle lifetime) until last particle is dead.'
--- !u!222 &1706786895
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1706786891}
  m_CullTransparentMesh: 1
--- !u!1660057539 &9223372036854775807
SceneRoots:
  m_ObjectHideFlags: 0
  m_Roots:
  - {fileID: 115526033}
  - {fileID: 656210486}
  - {fileID: 1194901736}
  - {fileID: 835911892}
  - {fileID: 67210250}
  - {fileID: 1315008426}
  - {fileID: 469010372}
  - {fileID: 1522538436}
  - {fileID: 118799966}
  - {fileID: 671842683}
  - {fileID: 410564920}
  - {fileID: 273917287}
  - {fileID: 1525156584}
  - {fileID: 541863767}
