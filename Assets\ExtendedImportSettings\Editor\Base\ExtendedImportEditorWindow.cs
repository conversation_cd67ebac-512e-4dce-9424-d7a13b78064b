using System;
using System.Collections.Generic;
using System.Reflection;
using Newtonsoft.Json;
using UnityEditor;
using UnityEngine;

namespace ExtendedImportSettings
{
    public class ExtendedImportEditorWindow : EditorWindow
    {
        private const string ASSET_NAME = "Extended Import Settings";
        
        private ExtendedImportModuleList _temp;
        private SerializedObject _tempSerializedObject;
        private FieldInfo[] _moduleInfos;
        private int[] _dataIDs;
        private bool[] _isExpanded;
        private AssetImporter _target;
        private UnityEngine.Object _targetAsset;
        private Vector2 _scrollPosition;

        [MenuItem("Tools/" + ASSET_NAME)]
        public static void ShowWindow()
        {
            GetWindow<ExtendedImportEditorWindow>(ASSET_NAME);
        }

        void OnEnable()
        {
            Selection.selectionChanged += OnSelectionChanged;
            Initialize();
            PrepareData();
        }

        void OnDisable()
        {
            Selection.selectionChanged -= OnSelectionChanged;
            
            if (_target != null && _moduleInfos != null && _temp != null)
            {
                for (var i = 0; i < _moduleInfos.Length; i++)
                {
                    var module = (ExtendedImportModule)_moduleInfos[i].GetValue(_temp);
                    module.OnDisableGUI(_target);
                }
            }

            if (_temp != null)
            {
                DestroyImmediate(_temp);
                _temp = null;
            }

            if (_tempSerializedObject != null)
            {
                _tempSerializedObject.Dispose();
                _tempSerializedObject = null;
            }

            _moduleInfos = null;
            _dataIDs = null;
            _isExpanded = null;
        }

        private void OnSelectionChanged()
        {
            OnDisable();
            OnEnable();
        }

        private void PrepareData()
        {
            GetSelectedAssetImporter();
            if (_target == null)
            {
                Repaint();
                return;
            }

            for (var i = 0; i < _moduleInfos.Length; i++)
            {
                var module = (ExtendedImportModule)_moduleInfos[i].GetValue(_temp);
                module.OnEnableGUI(_target);
                
                LoadData(_moduleInfos[i], i);
            }
            Repaint();
        }

        private void Initialize()
        {
            _temp = CreateInstance<ExtendedImportModuleList>();
            _tempSerializedObject = new SerializedObject(_temp);

            _moduleInfos = ExtendedImportUtilities.GetFields(_temp.GetType());
            _dataIDs = new int[_moduleInfos.Length];
            _isExpanded = new bool[_moduleInfos.Length];
            for (var i = 0; i < _moduleInfos.Length; i++)
                _isExpanded[i] = true;
        }

        void OnGUI()
        {
            if (_target == null)
            {
                HorizontalArea(() =>
                {
                    var style = new GUIStyle(EditorStyles.largeLabel)
                    {
                        alignment = TextAnchor.MiddleCenter,
                        wordWrap = true
                    };
                    EditorGUILayout.Space();
                    if(Selection.objects.Length > 1)
                        EditorGUILayout.LabelField("Multiple object selection is not supported", style);
                    else
                        EditorGUILayout.LabelField("Please Select Asset", style);
                    EditorGUILayout.Space();
                }, 4, 4, 4, 4, GUILayout.Height(58));
                DrawHorizontalGUILine(0);
                return;
            }
            
            HorizontalArea(() =>
            {
                GUILayout.Space(58);
                var preview = AssetPreview.GetAssetPreview(_targetAsset);
                var previewRect = new Rect(0, 0, 58, 58);
                previewRect.height = previewRect.width;
                if(preview != null)
                    EditorGUI.DrawPreviewTexture(previewRect, preview);
                
                GUILayout.Space(4);

                var style = new GUIStyle(EditorStyles.largeLabel)
                {
                    wordWrap = true
                };
                EditorGUILayout.LabelField(_targetAsset.name, style);
                EditorGUILayout.Space();

                if (GUILayout.Button("Edit Modules"))
                {
                    ExtendedImportUtilities.OpenScript(nameof(ExtendedImportModuleList));
                }
            }, 4, 4, 4, 4, GUILayout.Height(58));
            DrawHorizontalGUILine(0);
            
            _scrollPosition = EditorGUILayout.BeginScrollView(_scrollPosition);
            for (var i = 0; i < _moduleInfos.Length; i++)
            {
                var index = i;
                var moduleInfo = _moduleInfos[i];

                EditorGUI.BeginChangeCheck();
                
                Undo.RecordObject(_temp, ASSET_NAME);
                var module = (ExtendedImportModule)moduleInfo.GetValue(_temp);
                HorizontalArea(() =>
                {
                    module.IsActive = GUILayout.Toggle(module.IsActive, "", GUILayout.Width(15));
                    if (EditorPrefs.HasKey(moduleInfo.Name))
                        _isExpanded[index] = EditorPrefs.GetBool(moduleInfo.Name);
                    _isExpanded[index] = GUILayout.Toggle(_isExpanded[index],
                        ExtendedImportUtilities.InsertSpaces(moduleInfo.Name), 
                        new GUIStyle(EditorStyles.foldoutHeader)
                    );
                    EditorPrefs.SetBool(moduleInfo.Name, _isExpanded[index]);
                    _tempSerializedObject.Update();
                });
                DrawHorizontalGUILine(0, new Color32(48, 48, 48, 255));

                if (_isExpanded[index])
                {
                    GUI.enabled = module.IsActive;
                    VerticalArea(() =>
                    {
                        SerializedProperty moduleSerializedProperty =
                            _tempSerializedObject.FindProperty(moduleInfo.Name);
                        module.OnInspectorGUI(moduleSerializedProperty, _target);
                    }, 18);
                    GUI.enabled = true;
                }

                if(i < _moduleInfos.Length - 1)
                    DrawHorizontalGUILine(0);

                if (EditorGUI.EndChangeCheck())
                {
                    _tempSerializedObject.ApplyModifiedProperties();
                }
            }
            
            EditorGUILayout.EndScrollView();

            DrawHorizontalGUILine(0);
            var isNeedSaveAndReimport = false;
            VerticalArea(() =>
            {
                if (GUILayout.Button("Save And Reimport"))
                    isNeedSaveAndReimport = true;
            });
            
            if (isNeedSaveAndReimport)
            {
                for (var i = 0; i < _moduleInfos.Length; i++)
                    SaveData(_moduleInfos[i], i);

                _target.SaveAndReimport();
                OnSelectionChanged();
            }
        }

        private void VerticalArea(Action action, float left = 4, float right = 4,
            float up = 4, float down = 4, params GUILayoutOption[] options)
        {
            EditorGUILayout.BeginHorizontal(options);
            GUILayout.Space(left);
            EditorGUILayout.BeginVertical();
            GUILayout.Space(up);
            action?.Invoke();
            GUILayout.Space(down);
            EditorGUILayout.EndVertical();
            GUILayout.Space(right);
            EditorGUILayout.EndHorizontal();
        }
        
        private void HorizontalArea(Action action, float left = 4, float right = 4,
            float up = 4, float down = 4, params GUILayoutOption[] options)
        {
            EditorGUILayout.BeginVertical(options);
            GUILayout.Space(up);
            EditorGUILayout.BeginHorizontal();
            GUILayout.Space(left);
            action?.Invoke();
            GUILayout.Space(right);
            EditorGUILayout.EndHorizontal();
            GUILayout.Space(down);
            EditorGUILayout.EndVertical();
        }

        private static void DrawHorizontalGUILine(int space = 4, Color color = default, int height = 1) {
            if(space > 0)
                GUILayout.Space(space);

            Rect rect = GUILayoutUtility.GetRect(10, height, GUILayout.ExpandWidth(true));
            rect.height = height;
            rect.xMin = 0;
            rect.xMax = EditorGUIUtility.currentViewWidth;

            Color lineColor = new Color32(26, 26, 26, 255);
            if (color != default)
                lineColor = color;
            EditorGUI.DrawRect(rect, lineColor);
            if(space > 0)
                GUILayout.Space(space);
        }

        private void GetSelectedAssetImporter()
        {
            if (Selection.objects.Length > 1)
            {
                _target = null;
                _targetAsset = null;
                return;
            }
            
            UnityEngine.Object selectedAsset = Selection.activeObject;
            if (selectedAsset != null)
            {
                _targetAsset = selectedAsset;
                string assetPath = AssetDatabase.GetAssetPath(selectedAsset);
                AssetImporter importer = AssetImporter.GetAtPath(assetPath);

                if (importer != null && importer is ModelImporter)
                {
                    _target = importer;
                    return;
                }
            }
            
            _target = null;
        }

        private void LoadData(FieldInfo moduleInfo, int moduleID)
        {
            var moduleName = moduleInfo.FieldType.Name;
            _dataIDs[moduleID] = ExtendedImportUtilities.GetExtraUserProperty(_target, moduleName, out string json);
            var existed = _dataIDs[moduleID] > -1;

            if (existed)
            {
                json = json.Replace($"\"{moduleName}\":", "");
                var originalData = moduleInfo.GetValue(_temp);
                //var loadedData = JsonUtility.FromJson(json, moduleInfo.FieldType);
                var loadedData = JsonConvert.DeserializeObject(json, moduleInfo.FieldType);
                CopyPublicData(moduleInfo.FieldType, loadedData, originalData);
            }
        }

        private void CopyPublicData(Type type, object from, object to)
        {
            var fieldInfos = ExtendedImportUtilities.GetFields(type);
            for (var i = 0; i < fieldInfos.Length; i++)
                fieldInfos[i].SetValue(to, fieldInfos[i].GetValue(from));
        }

        private void SaveData(FieldInfo moduleInfo, int moduleID)
        {
            var moduleName = moduleInfo.FieldType.Name;
            //var editedData = JsonUtility.ToJson(moduleInfo.GetValue(_temp));
            var editedData = JsonConvert.SerializeObject(moduleInfo.GetValue(_temp), ExtendedImportUtilities.JsonSerializerSettings);
            editedData = $"\"{moduleName}\":{editedData}";
            var extraUserProperties = ((ModelImporter)_target).extraUserProperties;
            var existed = _dataIDs[moduleID] >= 0;

            if (existed)
            {
                List<string> extraUserPropertiesList = new List<string>(extraUserProperties);
                extraUserPropertiesList[_dataIDs[moduleID]] = editedData;
                ((ModelImporter)_target).extraUserProperties = extraUserPropertiesList.ToArray();
            }
            else
            {
                var module = (ExtendedImportModule)moduleInfo.GetValue(_temp);
                if (module.IsActive)
                {
                    List<string> extraUserPropertiesList = new List<string>(extraUserProperties);
                    extraUserPropertiesList.Add(editedData);
                    ((ModelImporter)_target).extraUserProperties = extraUserPropertiesList.ToArray();
                }
            }
        }
    }
}