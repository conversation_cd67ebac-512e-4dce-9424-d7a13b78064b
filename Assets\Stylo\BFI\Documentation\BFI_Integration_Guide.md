# BFI Integration Guide

## Overview

This guide explains how to integrate the Stylo BFI (Black Frame Insertion) effect into your Unity project's render pipeline.

## Prerequisites

- Unity 6.0 or later
- Universal Render Pipeline (URP) installed
- Stylo BFI package imported into your project

## Step 1: Add BFI Renderer Feature

### Method 1: Using Unity Inspector (Recommended)

1. **Open your URP Renderer Data asset**:
   - Navigate to `Assets/BT Render Pipeline/BT Render 1.asset`
   - Select the asset in the Project window

2. **Add BFI Renderer Feature**:
   - In the Inspector, scroll down to "Renderer Features"
   - Click the "+" button to add a new renderer feature
   - Select "BFI Renderer Feature" from the dropdown

3. **Configure BFI Renderer Feature**:
   - Ensure the feature is set to "Active"
   - The shader should automatically be assigned to "Hidden/Universal Render Pipeline/BFI"

### Method 2: Programmatic Integration

If you need to add the BFI feature programmatically, use this script:

```csharp
#if UNITY_EDITOR
using UnityEngine;
using UnityEngine.Rendering.Universal;
using UnityEditor;
using Stylo.BFI.Universal;

public static class BFIIntegration
{
    [MenuItem("Stylo/BFI/Add to Current Renderer")]
    public static void AddBFIToCurrentRenderer()
    {
        // Get the current URP asset
        var urpAsset = GraphicsSettings.currentRenderPipeline as UniversalRenderPipelineAsset;
        if (urpAsset == null)
        {
            Debug.LogError("No URP asset found in Graphics Settings");
            return;
        }

        // Get the default renderer data
        var rendererData = urpAsset.GetRenderer(0) as UniversalRendererData;
        if (rendererData == null)
        {
            Debug.LogError("No URP Renderer Data found");
            return;
        }

        // Check if BFI feature already exists
        foreach (var feature in rendererData.rendererFeatures)
        {
            if (feature is BFIRendererFeature)
            {
                Debug.Log("BFI Renderer Feature already exists");
                return;
            }
        }

        // Create and add BFI feature
        var bfiFeature = ScriptableObject.CreateInstance<BFIRendererFeature>();
        bfiFeature.name = "BFI Renderer Feature";
        
        // Add to renderer data
        rendererData.rendererFeatures.Add(bfiFeature);
        
        // Mark dirty and save
        EditorUtility.SetDirty(rendererData);
        AssetDatabase.SaveAssets();
        
        Debug.Log("BFI Renderer Feature added successfully");
    }
}
#endif
```

## Step 2: Configure Volume Profile

### Create BFI Volume Profile

1. **Create a new Volume Profile**:
   - Right-click in Project window
   - Create → Rendering → Volume Profile
   - Name it "BFI_VolumeProfile"

2. **Add BFI Effect**:
   - Select the Volume Profile
   - Click "Add Override"
   - Navigate to "Stylo - BFI Effect"
   - Click to add the effect

3. **Configure BFI Settings**:
   - Set "Mode" to your desired BFI mode (start with "2x Frame Rate")
   - Adjust "Intensity" to 1.0
   - Configure "Brightness Compensation" as needed (start with 1.5)

### Apply Volume Profile to Scene

1. **Create Global Volume**:
   - GameObject → Volume → Global Volume
   - Assign your BFI Volume Profile to the "Profile" field

2. **Or use existing Volume**:
   - Select existing Volume GameObject
   - Add BFI Effect override to existing profile

## Step 3: Verify Integration

### Check Shader Compilation

1. **Verify BFI Shader**:
   - Navigate to `Assets/Stylo/BFI/Shaders/URP_BFI.shader`
   - Check that it compiles without errors
   - Look for "Hidden/Universal Render Pipeline/BFI" in shader dropdown

### Test BFI Effect

1. **Enable Debug Visualization**:
   - In BFI Effect settings, enable "Debug Visualization"
   - Enter Play Mode
   - You should see BFI timing visualization

2. **Test Different Modes**:
   - Try different BFI modes (2x, 3x, 4x)
   - Observe motion clarity improvements
   - Adjust brightness compensation as needed

## Step 4: Optimize Settings

### Display Compatibility

- **120Hz Display**: Use "2x Frame Rate" mode
- **180Hz Display**: Use "3x Frame Rate" mode  
- **240Hz Display**: Use "4x Frame Rate" mode
- **60Hz Display**: BFI will work but benefits are limited

### Performance Optimization

1. **For Better Performance**:
   - Use lower BFI modes (2x instead of 4x)
   - Disable Motion Adaptive features
   - Reduce Subpixel Precision

2. **For Better Quality**:
   - Enable Motion Adaptive BFI
   - Use High Flicker Reduction
   - Increase Temporal Smoothing

### Common Settings

```
Gaming Setup:
- Mode: 2x Frame Rate
- Brightness Compensation: 1.8
- Flicker Reduction: Medium
- Motion Adaptive: Enabled

Cinema Setup:
- Mode: 3x Frame Rate  
- Brightness Compensation: 2.2
- Flicker Reduction: High
- Temporal Smoothing: 0.4
```

## Troubleshooting

### BFI Not Visible

1. Check that BFI Renderer Feature is active
2. Verify BFI Effect intensity > 0
3. Ensure BFI Mode is not set to "Off"
4. Check that Volume Profile is applied to scene

### Performance Issues

1. Lower BFI Mode (4x → 2x)
2. Disable Motion Adaptive features
3. Reduce Flicker Reduction settings
4. Check frame timing stability

### Too Much Flicker

1. Increase Flicker Reduction Mode
2. Adjust Temporal Smoothing
3. Enable Edge Preservation
4. Consider lower BFI mode

### Brightness Issues

1. Adjust Brightness Compensation
2. Modify Manual Brightness
3. Change Gamma Correction
4. Check display settings

## Advanced Configuration

### Custom Presets

Use the BFI Preset system to create custom configurations:

```csharp
// Create custom preset
var preset = BFIPreset.CreateDefaultPreset(BFIEffect.BFIMode.DoubleFrameRate);
preset.presetName = "My Custom BFI";
preset.brightnessCompensation = 2.0f;
preset.motionAdaptive = true;

// Apply to current volume stack
BFIPresetManager.ApplyPresetToCurrentStack(preset);
```

### Runtime Control

Control BFI settings at runtime:

```csharp
// Get BFI effect from volume stack
var stack = VolumeManager.instance.stack;
var bfiEffect = stack.GetComponent<BFIEffect>();

// Modify settings
bfiEffect.Mode.value = BFIEffect.BFIMode.TripleFrameRate;
bfiEffect.Intensity.value = 0.8f;
bfiEffect.BrightnessCompensation.value = 2.2f;
```

## Integration Complete

Your BFI system should now be fully integrated and functional. Test with different content types and adjust settings based on your specific needs and display capabilities.
