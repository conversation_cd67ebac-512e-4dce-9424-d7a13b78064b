using UnityEngine;
using UnityEditor;
using FMODUnity;

namespace Stylo.Cadance
{
    /// <summary>
    /// Simple test to verify FMOD Editor Audio System is working.
    /// This is the main test you should run first.
    /// </summary>
    public static class SimpleFMODEditorTest
    {
        [MenuItem("Stylo/Cadance/Tools/🎵 Simple FMOD Test (START HERE)")]
        public static void RunSimpleTest()
        {
            Debug.Log("=== 🎵 SIMPLE FMOD EDITOR TEST ===");
            
            bool allGood = true;
            
            // Test 1: Unity Audio Status
            Debug.Log("1️⃣ Testing Unity Audio...");
            try
            {
                var testGO = new GameObject("AudioTest");
                var audioSource = testGO.AddComponent<AudioSource>();
                audioSource.Play();
                bool unityWorks = audioSource.isPlaying;
                audioSource.Stop();
                Object.DestroyImmediate(testGO);
                
                if (unityWorks)
                {
                    Debug.Log("   ✅ Unity audio is enabled");
                }
                else
                {
                    Debug.Log("   ⚠️ Unity audio is disabled (expected for FMOD projects)");
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"   ❌ Unity audio test error: {ex.Message}");
            }
            
            // Test 2: FMOD RuntimeManager
            Debug.Log("2️⃣ Testing FMOD RuntimeManager...");
            if (RuntimeManager.IsInitialized)
            {
                Debug.Log("   ✅ FMOD RuntimeManager is working");
            }
            else
            {
                Debug.LogError("   ❌ FMOD RuntimeManager not initialized");
                allGood = false;
            }
            
            // Test 3: FMOD Editor Audio System
            Debug.Log("3️⃣ Testing FMOD Editor Audio System...");
            if (FMODEditorAudioSystem.Initialize())
            {
                Debug.Log("   ✅ FMOD Editor Audio System ready");
            }
            else
            {
                Debug.LogError("   ❌ FMOD Editor Audio System failed");
                allGood = false;
            }
            
            // Test 4: FMOD Waveform Generator
            Debug.Log("4️⃣ Testing FMOD Waveform Generator...");
            if (FMODWaveformGenerator.Initialize())
            {
                Debug.Log("   ✅ FMOD Waveform Generator ready");
            }
            else
            {
                Debug.LogError("   ❌ FMOD Waveform Generator failed");
                allGood = false;
            }
            
            // Test 5: Check Assets
            Debug.Log("5️⃣ Checking project assets...");
            string[] audioGuids = AssetDatabase.FindAssets("t:AudioClip");
            string[] cadanceGuids = AssetDatabase.FindAssets("t:CadanceAsset");
            
            Debug.Log($"   📁 Found {audioGuids.Length} AudioClips");
            Debug.Log($"   🎵 Found {cadanceGuids.Length} CadanceAssets");
            
            // Test 6: Simple Waveform Test
            if (audioGuids.Length > 0)
            {
                Debug.Log("6️⃣ Testing waveform generation...");
                try
                {
                    string path = AssetDatabase.GUIDToAssetPath(audioGuids[0]);
                    var testClip = AssetDatabase.LoadAssetAtPath<AudioClip>(path);
                    
                    if (testClip != null)
                    {
                        float[] waveform = FMODWaveformGenerator.GenerateWaveformForAudioClip(testClip, 256);
                        if (waveform != null && waveform.Length > 0)
                        {
                            Debug.Log($"   ✅ Waveform generated: {waveform.Length} samples");
                        }
                        else
                        {
                            Debug.Log("   ⚠️ Waveform generation returned empty (may need Unity fallback)");
                        }
                    }
                }
                catch (System.Exception ex)
                {
                    Debug.LogWarning($"   ⚠️ Waveform test error: {ex.Message}");
                }
            }
            else
            {
                Debug.Log("6️⃣ No AudioClips found for waveform test");
            }
            
            // Final Result
            Debug.Log("=== 🎯 TEST RESULTS ===");
            if (allGood)
            {
                Debug.Log("✅ ALL CRITICAL SYSTEMS WORKING!");
                Debug.Log("🚀 Ready for Koreographer → Cadance conversion!");
                Debug.Log("");
                Debug.Log("📋 NEXT STEPS:");
                Debug.Log("1. Run: Stylo/Cadance/Tools/Convert Koreographer to Cadance");
                Debug.Log("2. Run: Stylo/Cadance/Tools/FMOD Batch Import Tool");
                Debug.Log("3. Open any CadanceAsset in Cadance Editor");
                Debug.Log("4. Enjoy full timeline functionality! 🎵");
            }
            else
            {
                Debug.LogError("❌ SOME CRITICAL SYSTEMS FAILED");
                Debug.LogError("Please check the errors above and fix them first.");
            }
            
            // Show dialog
            string dialogMessage = allGood ? 
                "✅ ALL SYSTEMS WORKING!\n\nReady for Koreographer conversion!\n\nNext: Convert your assets and enjoy full timeline functionality." :
                "❌ SOME SYSTEMS FAILED\n\nCheck console for details.\n\nFix errors before proceeding.";
                
            EditorUtility.DisplayDialog("FMOD Editor Test Results", dialogMessage, "OK");
        }

        [MenuItem("Stylo/Cadance/Tools/📊 Show System Status")]
        public static void ShowSystemStatus()
        {
            Debug.Log("=== 📊 FMOD EDITOR SYSTEM STATUS ===");
            
            // Unity Audio
            bool unityAudio = false;
            try
            {
                var testGO = new GameObject("AudioTest");
                var audioSource = testGO.AddComponent<AudioSource>();
                audioSource.Play();
                unityAudio = audioSource.isPlaying;
                audioSource.Stop();
                Object.DestroyImmediate(testGO);
            }
            catch { }
            
            // FMOD Status
            bool fmodReady = RuntimeManager.IsInitialized;
            bool editorAudioReady = FMODEditorAudioSystem.Initialize();
            bool waveformReady = FMODWaveformGenerator.Initialize();
            
            // Asset Counts
            int audioClips = AssetDatabase.FindAssets("t:AudioClip").Length;
            int cadanceAssets = AssetDatabase.FindAssets("t:CadanceAsset").Length;
            
            string status = "FMOD Editor System Status:\n\n" +
                           $"🔊 Unity Audio: {(unityAudio ? "✅ Enabled" : "⚠️ Disabled")}\n" +
                           $"🎵 FMOD RuntimeManager: {(fmodReady ? "✅ Ready" : "❌ Failed")}\n" +
                           $"🎧 Editor Audio System: {(editorAudioReady ? "✅ Ready" : "❌ Failed")}\n" +
                           $"📊 Waveform Generator: {(waveformReady ? "✅ Ready" : "❌ Failed")}\n\n" +
                           $"📁 AudioClips: {audioClips}\n" +
                           $"🎵 CadanceAssets: {cadanceAssets}\n\n" +
                           $"Overall Status: {(fmodReady && editorAudioReady && waveformReady ? "✅ READY" : "❌ NEEDS ATTENTION")}";
            
            Debug.Log(status);
            EditorUtility.DisplayDialog("System Status", status, "OK");
        }

        [MenuItem("Stylo/Cadance/Tools/🔧 Test Cadance Editor")]
        public static void TestCadanceEditor()
        {
            Debug.Log("=== 🔧 TESTING CADANCE EDITOR ===");
            
            try
            {
                // Try to get the Cadance Editor window type
                var editorType = System.Type.GetType("Stylo.Cadance.CadanceEditorWindow");
                if (editorType != null)
                {
                    var window = EditorWindow.GetWindow(editorType, false, "Cadance Editor Test");
                    if (window != null)
                    {
                        Debug.Log("✅ Cadance Editor window opened successfully");
                        
                        // Check for test assets
                        string[] cadanceGuids = AssetDatabase.FindAssets("t:CadanceAsset");
                        if (cadanceGuids.Length > 0)
                        {
                            Debug.Log($"✅ Found {cadanceGuids.Length} CadanceAssets for testing");
                            Debug.Log("🎯 Try opening a CadanceAsset to test timeline functionality!");
                        }
                        else
                        {
                            Debug.Log("⚠️ No CadanceAssets found. Convert Koreographer assets first.");
                        }
                    }
                    else
                    {
                        Debug.LogError("❌ Failed to create Cadance Editor window");
                    }
                }
                else
                {
                    Debug.LogError("❌ CadanceEditorWindow type not found");
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"❌ Cadance Editor test failed: {ex.Message}");
            }
        }
    }
}
