fileFormatVersion: 2
guid: 3da05e9f66c9f254c8875335e7e9179e
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 4279260276780606416
    second: NVPresetsIcons_0
  - first:
      213: -2389462326047053563
    second: NVPresetsIcons_1
  - first:
      213: 6684065222803499413
    second: NVPresetsIcons_2
  - first:
      213: -8308906585651362545
    second: NVPresetsIcons_3
  - first:
      213: -7933564687622153330
    second: NVPresetsIcons_4
  - first:
      213: 2657873270443967794
    second: NVPresetsIcons_5
  - first:
      213: -6415259062133742075
    second: NVPresetsIcons_6
  - first:
      213: -3820048696943746370
    second: NVPresetsIcons_7
  - first:
      213: -312768529563260625
    second: NVPresetsIcons_8
  - first:
      213: -8918885832014658072
    second: NVPresetsIcons_9
  externalObjects: {}
  serializedVersion: 12
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMasterTextureLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 0
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 1
  cookieLightType: 1
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: iPhone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: NVPresetsIcons_0
      rect:
        serializedVersion: 2
        x: 0
        y: 768
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9f03a35fbb19c054baa115a0e145db89
      internalID: 4279260276780606416
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: NVPresetsIcons_1
      rect:
        serializedVersion: 2
        x: 256
        y: 768
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c998f8a5e281a9340b13d94ff576eada
      internalID: -2389462326047053563
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: NVPresetsIcons_2
      rect:
        serializedVersion: 2
        x: 512
        y: 768
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b552659543dbebb4194ce28bb88910e0
      internalID: 6684065222803499413
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: NVPresetsIcons_3
      rect:
        serializedVersion: 2
        x: 768
        y: 768
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: eca1cdb25fce6994f9b3dc71a8cb227b
      internalID: -8308906585651362545
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: NVPresetsIcons_4
      rect:
        serializedVersion: 2
        x: 0
        y: 512
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ac399416a8fc4554b8911fa991e10e21
      internalID: -7933564687622153330
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: NVPresetsIcons_5
      rect:
        serializedVersion: 2
        x: 256
        y: 512
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 0009bca4c51e44d428bcb541f9ae39e3
      internalID: 2657873270443967794
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: NVPresetsIcons_6
      rect:
        serializedVersion: 2
        x: 512
        y: 512
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: cef485081e26319448ed921f2afc608d
      internalID: -6415259062133742075
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: NVPresetsIcons_7
      rect:
        serializedVersion: 2
        x: 768
        y: 512
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d86d779fa8b4d3b418c43bee02b850f5
      internalID: -3820048696943746370
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: NVPresetsIcons_8
      rect:
        serializedVersion: 2
        x: 0
        y: 256
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 727e5f77f6c15604f999621ebdd89b01
      internalID: -312768529563260625
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: NVPresetsIcons_9
      rect:
        serializedVersion: 2
        x: 256
        y: 256
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 24457b6072252344e92fa41e033711a3
      internalID: -8918885832014658072
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: 3ef685cd4dc3bb34aa4c1d017b4d62c0
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      NVPresetsIcons_0: 4279260276780606416
      NVPresetsIcons_1: -2389462326047053563
      NVPresetsIcons_2: 6684065222803499413
      NVPresetsIcons_3: -8308906585651362545
      NVPresetsIcons_4: -7933564687622153330
      NVPresetsIcons_5: 2657873270443967794
      NVPresetsIcons_6: -6415259062133742075
      NVPresetsIcons_7: -3820048696943746370
      NVPresetsIcons_8: -312768529563260625
      NVPresetsIcons_9: -8918885832014658072
  spritePackingTag: 
  pSDRemoveMatte: 0
  pSDShowRemoveMatteOption: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
AssetOrigin:
  serializedVersion: 1
  productId: 183370
  packageName: Feel
  packageVersion: 5.5
  assetPath: Assets/Feel/NiceVibrations/Demo/DemoAssets/HapticClipsDemo/Sprites/NVPresetsIcons.png
  uploadId: 759320
