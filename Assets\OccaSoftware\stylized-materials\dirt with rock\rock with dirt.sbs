<?xml version="1.0" encoding="UTF-8"?><package><identifier v="Unsaved Package"/><formatVersion v="1.1.0.202302"/><updaterVersion v="1.1.0.202302"/><fileUID v="{64dfaf85-3a74-441d-8805-d5b3c2782cc9}"/><versionUID v="0"/><dependencies><dependency><filename v="sbs://hbao_2.sbs"/><uid v="1297162667"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://pattern_tile_sampler.sbs"/><uid v="1449399972"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://flood_fill_2.sbs"/><uid v="1323881949"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://edge_detect.sbs"/><uid v="1407202483"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://histogram_scan.sbs"/><uid v="1449404462"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://histogram_select.sbs"/><uid v="1407203168"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://bevel.sbs"/><uid v="1449405586"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://auto_levels.sbs"/><uid v="1449400845"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_crystal_1.sbs"/><uid v="1407271237"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://blur_hq.sbs"/><uid v="1299236171"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://waveform_1.sbs"/><uid v="1446771957"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://slope_blur.sbs"/><uid v="1449402040"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_fractal_sum_base.sbs"/><uid v="1449402686"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_perlin_noise.sbs"/><uid v="1407332030"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://vector_warp.sbs"/><uid v="1407332989"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://non_uniform_blur.sbs"/><uid v="1290776959"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://pattern_shape.sbs"/><uid v="1311189026"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://skew.sbs"/><uid v="1407476587"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://mirror.sbs"/><uid v="1407476641"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://invert.sbs"/><uid v="1368115694"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://height_blend.sbs"/><uid v="1449623608"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://rgba_split.sbs"/><uid v="1147283954"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://highpass.sbs"/><uid v="1449753752"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://quantize.sbs"/><uid v="1407503933"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://curvature_smooth.sbs"/><uid v="1449590280"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency></dependencies><content><graph><identifier v="dirt_with_rock"/><uid v="1407184709"/><graphOutputs><graphoutput><identifier v="basecolor_1"/><uid v="1407198767"/><attributes><label v="Base Color"/></attributes><usages><usage><components v="RGBA"/><name v="baseColor"/></usage></usages><group v="Material"/></graphoutput><graphoutput><identifier v="normal_1"/><uid v="1407198769"/><attributes><label v="Normal"/></attributes><usages><usage><components v="RGBA"/><name v="normal"/></usage></usages><group v="Material"/></graphoutput><graphoutput><identifier v="roughness_1"/><uid v="1407198771"/><attributes><label v="Roughness"/></attributes><usages><usage><components v="RGBA"/><name v="roughness"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput><graphoutput><identifier v="metallic_1"/><uid v="1407198773"/><attributes><label v="Metallic"/></attributes><usages><usage><components v="RGBA"/><name v="metallic"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput><graphoutput><identifier v="height_1"/><uid v="1407198775"/><attributes><label v="Height"/></attributes><usages><usage><components v="RGBA"/><name v="height"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput><graphoutput><identifier v="AO"/><uid v="1407198777"/><usages><usage><components v="RGBA"/><name v="ambientOcclusion"/></usage></usages><channels v="2"/></graphoutput></graphOutputs><compNodes><compNode><uid v="1407198759"/><GUILayout><gpos v="9445.33496 314.666382 0"/></GUILayout><compOutputs><compOutput><uid v="1355432809"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="uniform"/><parameters><parameter><name v="colorswitch"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407198760"/><GUILayout><gpos v="9445.33496 -325.33374 0"/></GUILayout><compOutputs><compOutput><uid v="1355432809"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="uniform"/><parameters><parameter><name v="colorswitch"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="outputcolor"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.552941203 0.552941203 0.552941203 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407198761"/><connections><connection><identifier v="input"/><connRef v="1407198764"/><connRefOutput v="1355424890"/></connection></connections><GUILayout><gpos v="9456 -161.878403 0"/></GUILayout><compOutputs><compOutput><uid v="1355424961"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///hbao?dependency=1297162667"/><parameters><parameter><name v="height_depth"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.370000005"/></paramValue></parameter><parameter><name v="samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="16"/></paramValue></parameter><parameter><name v="surface_size"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="268.820007"/></paramValue></parameter><parameter><name v="height_depth_cm"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="93.1899948"/></paramValue></parameter><parameter><name v="radius"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.579999983"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1355424961"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407198762"/><GUILayout><gpos v="9445.33496 -485.333618 0"/></GUILayout><compOutputs><compOutput><uid v="1355432809"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="uniform"/><parameters><parameter><name v="colorswitch"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="outputcolor"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.420000017 0.420000017 0.420000017 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407198763"/><connections><connection><identifier v="input1"/><connRef v="1407198764"/><connRefOutput v="1355424890"/></connection></connections><GUILayout><gpos v="9445.33496 -5.33361816 0"/></GUILayout><compOutputs><compOutput><uid v="1355431358"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="normal"/><parameters><parameter><name v="input2alpha"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="inversedy"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="10"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407198764"/><connections><connection><identifier v="destination"/><connRef v="1407420471"/><connRefOutput v="1407420472"/></connection></connections><GUILayout><gpos v="9221.33496 154.666382 0"/></GUILayout><compOutputs><compOutput><uid v="1355424890"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407198766"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1407516236"/><connRefOutput v="1407516237"/></connection></connections><GUILayout><gpos v="9669.33496 -485.333618 0"/></GUILayout><compImplementation><compOutputBridge><output v="1407198767"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1407198768"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1407198763"/><connRefOutput v="1355431358"/></connection></connections><GUILayout><gpos v="9669.33496 -5.33361816 0"/></GUILayout><compImplementation><compOutputBridge><output v="1407198769"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1407198770"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1407527732"/><connRefOutput v="1407527733"/></connection></connections><GUILayout><gpos v="9680 -336 0"/></GUILayout><compImplementation><compOutputBridge><output v="1407198771"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1407198772"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1407198759"/><connRefOutput v="1355432809"/></connection></connections><GUILayout><gpos v="9669.33496 314.666382 0"/></GUILayout><compImplementation><compOutputBridge><output v="1407198773"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1407198774"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1407198764"/><connRefOutput v="1355424890"/></connection></connections><GUILayout><gpos v="9669.33496 154.666382 0"/></GUILayout><compImplementation><compOutputBridge><output v="1407198775"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1407198776"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1407198761"/><connRefOutput v="1355424961"/></connection></connections><GUILayout><gpos v="9669.33496 -165.33374 0"/></GUILayout><compImplementation><compOutputBridge><output v="1407198777"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1407201466"/><GUILayout><gpos v="-6128 208 0"/></GUILayout><compOutputs><compOutput><uid v="1407201467"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1449399972"/><parameters><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="11"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.229999989"/></paramValue></parameter><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="2.3599999"/></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.459999979"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.649999976"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407201467"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407202195"/><connections><connection><identifier v="mask"/><connRef v="1407201466"/><connRefOutput v="1407201467"/></connection></connections><GUILayout><gpos v="-5968 208 0"/></GUILayout><compOutputs><compOutput><uid v="1407202196"/><comptype v="1"/></compOutput><compOutput><uid v="1407202197"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill?dependency=1323881949"/><parameters/><outputBridgings><outputBridging><uid v="1407202196"/><identifier v="output"/></outputBridging><outputBridging><uid v="1407202197"/><identifier v="referenceMap"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407202279"/><connections><connection><identifier v="source"/><connRef v="1407202317"/><connRefOutput v="1407202318"/></connection><connection><identifier v="mask"/><connRef v="1407201466"/><connRefOutput v="1407201467"/></connection></connections><GUILayout><gpos v="-5648 208 0"/></GUILayout><compOutputs><compOutput><uid v="1407202280"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters><parameter><name v="combinedistance"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="256"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407202317"/><connections><connection><identifier v="input"/><connRef v="1407202195"/><connRefOutput v="1407202196"/></connection></connections><GUILayout><gpos v="-5808 208 0"/></GUILayout><compOutputs><compOutput><uid v="1407202318"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_random_grayscale?dependency=1323881949"/><parameters/><outputBridgings><outputBridging><uid v="1407202318"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407202524"/><connections><connection><identifier v="input"/><connRef v="1407202279"/><connRefOutput v="1407202280"/></connection></connections><GUILayout><gpos v="-5488 208 0"/></GUILayout><compOutputs><compOutput><uid v="1407202525"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///edge_detect?dependency=1407202483"/><parameters><parameter><name v="edge_roundness"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="12.3299999"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407202525"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407202745"/><connections><connection><identifier v="Input_1"/><connRef v="1407202279"/><connRefOutput v="1407202280"/></connection></connections><GUILayout><gpos v="-5488 80 0"/></GUILayout><compOutputs><compOutput><uid v="1407202746"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1449404462"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.349999994"/></paramValue></parameter><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407202746"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407202845"/><connections><connection><identifier v="input"/><connRef v="1407202279"/><connRefOutput v="1407202280"/></connection></connections><GUILayout><gpos v="-5488 336 0"/></GUILayout><compOutputs><compOutput><uid v="1407202525"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///edge_detect?dependency=1407202483"/><parameters><parameter><name v="edge_roundness"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="4.46999979"/></paramValue></parameter><parameter><name v="edge_width"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="3.41999984"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407202525"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407202867"/><connections><connection><identifier v="source"/><connRef v="1407202524"/><connRefOutput v="1407202525"/></connection><connection><identifier v="destination"/><connRef v="1407202845"/><connRefOutput v="1407202525"/></connection><connection><identifier v="opacity"/><connRef v="1407202745"/><connRefOutput v="1407202746"/></connection></connections><GUILayout><gpos v="-5328 336 0"/></GUILayout><compOutputs><compOutput><uid v="1407202868"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1407203017"/><connections><connection><identifier v="input"/><connRef v="1407202279"/><connRefOutput v="1407202280"/></connection></connections><GUILayout><gpos v="-5488 -48 0"/></GUILayout><compOutputs><compOutput><uid v="1407202525"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///edge_detect?dependency=1407202483"/><parameters><parameter><name v="edge_roundness"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="12.0999994"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407202525"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407203207"/><connections><connection><identifier v="input"/><connRef v="1407202279"/><connRefOutput v="1407202280"/></connection></connections><GUILayout><gpos v="-5488 -176 0"/></GUILayout><compOutputs><compOutput><uid v="1407203208"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_select?dependency=1407203168"/><parameters><parameter><name v="constrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.199999988"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407203208"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407203294"/><connections><connection><identifier v="destination"/><connRef v="1407202867"/><connRefOutput v="1407202868"/></connection><connection><identifier v="source"/><connRef v="1407203017"/><connRefOutput v="1407202525"/></connection><connection><identifier v="opacity"/><connRef v="1407203207"/><connRefOutput v="1407203208"/></connection></connections><GUILayout><gpos v="-5168 336 0"/></GUILayout><compOutputs><compOutput><uid v="1407203295"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1407203431"/><connections><connection><identifier v="input"/><connRef v="1407202279"/><connRefOutput v="1407202280"/></connection></connections><GUILayout><gpos v="-5488 -464 0"/></GUILayout><compOutputs><compOutput><uid v="1407203208"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_select?dependency=1407203168"/><parameters><parameter><name v="constrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.359999985"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407203208"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407203469"/><connections><connection><identifier v="input"/><connRef v="1407202279"/><connRefOutput v="1407202280"/></connection></connections><GUILayout><gpos v="-5488 -336 0"/></GUILayout><compOutputs><compOutput><uid v="1407202525"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///edge_detect?dependency=1407202483"/><parameters><parameter><name v="edge_roundness"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="15.4299994"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407202525"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407203524"/><connections><connection><identifier v="destination"/><connRef v="1407203294"/><connRefOutput v="1407203295"/></connection><connection><identifier v="opacity"/><connRef v="1407203431"/><connRefOutput v="1407203208"/></connection><connection><identifier v="source"/><connRef v="1407203469"/><connRefOutput v="1407202525"/></connection></connections><GUILayout><gpos v="-4976 336 0"/></GUILayout><compOutputs><compOutput><uid v="1407203295"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1407205796"/><connections><connection><identifier v="input"/><connRef v="1407203524"/><connRefOutput v="1407203295"/></connection></connections><GUILayout><gpos v="-3824 464 0"/></GUILayout><compOutputs><compOutput><uid v="1407205797"/><comptype v="2"/></compOutput><compOutput><uid v="1407205798"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///bevel?dependency=1449405586"/><parameters><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0599999987"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407205797"/><identifier v="height"/></outputBridging><outputBridging><uid v="1407205798"/><identifier v="normal"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407212376"/><connections><connection><identifier v="mask"/><connRef v="1407203524"/><connRefOutput v="1407203295"/></connection></connections><GUILayout><gpos v="-3808.72217 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407212377"/><comptype v="1"/></compOutput><compOutput><uid v="1407212378"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill?dependency=1323881949"/><parameters/><outputBridgings><outputBridging><uid v="1407212377"/><identifier v="output"/></outputBridging><outputBridging><uid v="1407212378"/><identifier v="referenceMap"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407212731"/><connections><connection><identifier v="input"/><connRef v="1407212376"/><connRefOutput v="1407212377"/></connection></connections><GUILayout><gpos v="-3648.72217 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407212732"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_random_grayscale?dependency=1323881949"/><parameters/><outputBridgings><outputBridging><uid v="1407212732"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407213312"/><connections><connection><identifier v="input"/><connRef v="1407212376"/><connRefOutput v="1407212377"/></connection></connections><GUILayout><gpos v="-3632 144 0"/></GUILayout><compOutputs><compOutput><uid v="1407213313"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.75999999"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407213313"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407213377"/><connections><connection><identifier v="input1"/><connRef v="1407213312"/><connRefOutput v="1407213313"/></connection></connections><GUILayout><gpos v="-3504 144 0"/></GUILayout><compOutputs><compOutput><uid v="1407213378"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.292048931 0.292048931 0.292048931 1"/></paramValue></parameter><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.383792043 0.383792043 0.383792043 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407213680"/><connections><connection><identifier v="destination"/><connRef v="1407282150"/><connRefOutput v="1407282151"/></connection><connection><identifier v="source"/><connRef v="1407431746"/><connRefOutput v="1407431747"/></connection></connections><GUILayout><gpos v="-2672 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407213681"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407213687"/><connections><connection><identifier v="source"/><connRef v="1407213377"/><connRefOutput v="1407213378"/></connection><connection><identifier v="mask"/><connRef v="1407203524"/><connRefOutput v="1407203295"/></connection></connections><GUILayout><gpos v="-3376 144 0"/></GUILayout><compOutputs><compOutput><uid v="1407213688"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters><parameter><name v="combinedistance"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="256"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407214062"/><connections><connection><identifier v="destination"/><connRef v="1407213680"/><connRefOutput v="1407213681"/></connection><connection><identifier v="source"/><connRef v="1407312563"/><connRefOutput v="1407287588"/></connection></connections><GUILayout><gpos v="-2512 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407214063"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407220796"/><connections><connection><identifier v="input1"/><connRef v="1407220797"/><connRefOutput v="1407213313"/></connection></connections><GUILayout><gpos v="-3504 16 0"/></GUILayout><compOutputs><compOutput><uid v="1407213378"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.246177375 0.246177375 0.246177375 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407220797"/><connections><connection><identifier v="input"/><connRef v="1407212376"/><connRefOutput v="1407212377"/></connection></connections><GUILayout><gpos v="-3632 16 0"/></GUILayout><compOutputs><compOutput><uid v="1407213313"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.75999999"/></paramValue></parameter><parameter><name v="angle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.43599999"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407213313"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407220798"/><connections><connection><identifier v="source"/><connRef v="1407220796"/><connRefOutput v="1407213378"/></connection><connection><identifier v="mask"/><connRef v="1407203524"/><connRefOutput v="1407203295"/></connection></connections><GUILayout><gpos v="-3376 16 0"/></GUILayout><compOutputs><compOutput><uid v="1407213688"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters><parameter><name v="combinedistance"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="256"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407222068"/><connections><connection><identifier v="input1"/><connRef v="1407205796"/><connRefOutput v="1407205797"/></connection></connections><GUILayout><gpos v="-3664 464 0"/></GUILayout><compOutputs><compOutput><uid v="1407222069"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1407225785"/><connections><connection><identifier v="input1"/><connRef v="1407225786"/><connRefOutput v="1407213313"/></connection></connections><GUILayout><gpos v="-3504 -112 0"/></GUILayout><compOutputs><compOutput><uid v="1407213378"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.16055046 0.16055046 0.16055046 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407225786"/><connections><connection><identifier v="input"/><connRef v="1407212376"/><connRefOutput v="1407212377"/></connection></connections><GUILayout><gpos v="-3632 -112 0"/></GUILayout><compOutputs><compOutput><uid v="1407213313"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.909999967"/></paramValue></parameter><parameter><name v="angle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.133083329"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407213313"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407225787"/><connections><connection><identifier v="source"/><connRef v="1407225785"/><connRefOutput v="1407213378"/></connection><connection><identifier v="mask"/><connRef v="1407203524"/><connRefOutput v="1407203295"/></connection></connections><GUILayout><gpos v="-3376 -112 0"/></GUILayout><compOutputs><compOutput><uid v="1407213688"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters><parameter><name v="combinedistance"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="256"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407225796"/><connections><connection><identifier v="destination"/><connRef v="1407214062"/><connRefOutput v="1407214063"/></connection><connection><identifier v="source"/><connRef v="1407341920"/><connRefOutput v="1407287588"/></connection></connections><GUILayout><gpos v="-2352 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407214063"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407229211"/><connections><connection><identifier v="source"/><connRef v="1407229215"/><connRefOutput v="1407213378"/></connection><connection><identifier v="mask"/><connRef v="1407203524"/><connRefOutput v="1407203295"/></connection></connections><GUILayout><gpos v="-3376 -368 0"/></GUILayout><compOutputs><compOutput><uid v="1407213688"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters><parameter><name v="combinedistance"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="256"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407229212"/><connections><connection><identifier v="source"/><connRef v="1407229219"/><connRefOutput v="1407213378"/></connection><connection><identifier v="mask"/><connRef v="1407203524"/><connRefOutput v="1407203295"/></connection></connections><GUILayout><gpos v="-3376 -496 0"/></GUILayout><compOutputs><compOutput><uid v="1407213688"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters><parameter><name v="combinedistance"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="256"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407229213"/><connections><connection><identifier v="input1"/><connRef v="1407229218"/><connRefOutput v="1407213313"/></connection></connections><GUILayout><gpos v="-3504 -240 0"/></GUILayout><compOutputs><compOutput><uid v="1407213378"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.288990825 0.288990825 0.288990825 1"/></paramValue></parameter><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.270642191 0.270642191 0.270642191 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407229214"/><connections><connection><identifier v="input"/><connRef v="1407212376"/><connRefOutput v="1407212377"/></connection></connections><GUILayout><gpos v="-3632 -496 0"/></GUILayout><compOutputs><compOutput><uid v="1407213313"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.909999967"/></paramValue></parameter><parameter><name v="angle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.366277784"/></paramValue></parameter><parameter><name v="randomseed"/><relativeTo v="2"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407213313"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407229215"/><connections><connection><identifier v="input1"/><connRef v="1407229216"/><connRefOutput v="1407213313"/></connection></connections><GUILayout><gpos v="-3504 -368 0"/></GUILayout><compOutputs><compOutput><uid v="1407213378"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.246177375 0.246177375 0.246177375 1"/></paramValue></parameter><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.172782868 0.172782868 0.172782868 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407229216"/><connections><connection><identifier v="input"/><connRef v="1407212376"/><connRefOutput v="1407212377"/></connection></connections><GUILayout><gpos v="-3632 -368 0"/></GUILayout><compOutputs><compOutput><uid v="1407213313"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.75999999"/></paramValue></parameter><parameter><name v="angle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="-0.0278611109"/></paramValue></parameter><parameter><name v="randomseed"/><relativeTo v="2"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407213313"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407229217"/><connections><connection><identifier v="source"/><connRef v="1407229213"/><connRefOutput v="1407213378"/></connection><connection><identifier v="mask"/><connRef v="1407203524"/><connRefOutput v="1407203295"/></connection></connections><GUILayout><gpos v="-3376 -240 0"/></GUILayout><compOutputs><compOutput><uid v="1407213688"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters><parameter><name v="combinedistance"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="256"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407229218"/><connections><connection><identifier v="input"/><connRef v="1407212376"/><connRefOutput v="1407212377"/></connection></connections><GUILayout><gpos v="-3632 -240 0"/></GUILayout><compOutputs><compOutput><uid v="1407213313"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.479999989"/></paramValue></parameter><parameter><name v="angle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="-0.266583323"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407213313"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407229219"/><connections><connection><identifier v="input1"/><connRef v="1407229214"/><connRefOutput v="1407213313"/></connection></connections><GUILayout><gpos v="-3504 -496 0"/></GUILayout><compOutputs><compOutput><uid v="1407213378"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.194189608 0.194189608 0.194189608 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407229236"/><connections><connection><identifier v="destination"/><connRef v="1407229237"/><connRefOutput v="1407213681"/></connection><connection><identifier v="source"/><connRef v="1407450726"/><connRefOutput v="1407431747"/></connection></connections><GUILayout><gpos v="-1984.72217 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407214063"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407229237"/><connections><connection><identifier v="destination"/><connRef v="1407225796"/><connRefOutput v="1407214063"/></connection><connection><identifier v="source"/><connRef v="1407438084"/><connRefOutput v="1407431747"/></connection></connections><GUILayout><gpos v="-2192 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407213681"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407229238"/><connections><connection><identifier v="destination"/><connRef v="1407229236"/><connRefOutput v="1407214063"/></connection><connection><identifier v="source"/><connRef v="1407229212"/><connRefOutput v="1407213688"/></connection></connections><GUILayout><gpos v="-1824.72217 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407214063"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407237835"/><connections><connection><identifier v="source"/><connRef v="1407237837"/><connRefOutput v="1407213378"/></connection><connection><identifier v="mask"/><connRef v="1407203524"/><connRefOutput v="1407203295"/></connection></connections><GUILayout><gpos v="-3376 -624 0"/></GUILayout><compOutputs><compOutput><uid v="1407213688"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters><parameter><name v="combinedistance"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="256"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407237836"/><connections><connection><identifier v="input"/><connRef v="1407212376"/><connRefOutput v="1407212377"/></connection></connections><GUILayout><gpos v="-3632 -624 0"/></GUILayout><compOutputs><compOutput><uid v="1407213313"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.909999967"/></paramValue></parameter><parameter><name v="angle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.541166663"/></paramValue></parameter><parameter><name v="randomseed"/><relativeTo v="2"/><paramValue><constantValueInt32 v="9"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407213313"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407237837"/><connections><connection><identifier v="input1"/><connRef v="1407237836"/><connRefOutput v="1407213313"/></connection></connections><GUILayout><gpos v="-3504 -624 0"/></GUILayout><compOutputs><compOutput><uid v="1407213378"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.408256888 0.408256888 0.408256888 1"/></paramValue></parameter><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.659021378 0.659021378 0.659021378 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407242426"/><connections><connection><identifier v="destination"/><connRef v="1407229238"/><connRefOutput v="1407214063"/></connection><connection><identifier v="source"/><connRef v="1407433074"/><connRefOutput v="1407431747"/></connection></connections><GUILayout><gpos v="-1680 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407214063"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407246477"/><connections><connection><identifier v="source"/><connRef v="1407246479"/><connRefOutput v="1407213378"/></connection><connection><identifier v="mask"/><connRef v="1407203524"/><connRefOutput v="1407203295"/></connection></connections><GUILayout><gpos v="-3373.72095 -752 0"/></GUILayout><compOutputs><compOutput><uid v="1407213688"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters><parameter><name v="combinedistance"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="256"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407246478"/><connections><connection><identifier v="input"/><connRef v="1407212376"/><connRefOutput v="1407212377"/></connection></connections><GUILayout><gpos v="-3629.72095 -752 0"/></GUILayout><compOutputs><compOutput><uid v="1407213313"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.909999967"/></paramValue></parameter><parameter><name v="angle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.88794446"/></paramValue></parameter><parameter><name v="randomseed"/><relativeTo v="2"/><paramValue><constantValueInt32 v="20"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407213313"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407246479"/><connections><connection><identifier v="input1"/><connRef v="1407246478"/><connRefOutput v="1407213313"/></connection></connections><GUILayout><gpos v="-3501.72095 -752 0"/></GUILayout><compOutputs><compOutput><uid v="1407213378"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.200305805 0.200305805 0.200305805 1"/></paramValue></parameter><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.659021378 0.659021378 0.659021378 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407246626"/><connections><connection><identifier v="destination"/><connRef v="1407242426"/><connRefOutput v="1407214063"/></connection><connection><identifier v="source"/><connRef v="1407309663"/><connRefOutput v="1407287588"/></connection></connections><GUILayout><gpos v="-1520 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407214063"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407249360"/><connections><connection><identifier v="source"/><connRef v="1407249365"/><connRefOutput v="1407213378"/></connection><connection><identifier v="mask"/><connRef v="1407203524"/><connRefOutput v="1407203295"/></connection></connections><GUILayout><gpos v="-3376 -880 0"/></GUILayout><compOutputs><compOutput><uid v="1407213688"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters><parameter><name v="combinedistance"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="256"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407249361"/><connections><connection><identifier v="input1"/><connRef v="1407249364"/><connRefOutput v="1407213313"/></connection></connections><GUILayout><gpos v="-3501.7207 -1008 0"/></GUILayout><compOutputs><compOutput><uid v="1407213378"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.389908254 0.389908254 0.389908254 1"/></paramValue></parameter><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.536697268 0.536697268 0.536697268 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407249362"/><connections><connection><identifier v="source"/><connRef v="1407249361"/><connRefOutput v="1407213378"/></connection><connection><identifier v="mask"/><connRef v="1407203524"/><connRefOutput v="1407203295"/></connection></connections><GUILayout><gpos v="-3373.7207 -1008 0"/></GUILayout><compOutputs><compOutput><uid v="1407213688"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters><parameter><name v="combinedistance"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="256"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407249363"/><connections><connection><identifier v="input"/><connRef v="1407212376"/><connRefOutput v="1407212377"/></connection></connections><GUILayout><gpos v="-3632 -880 0"/></GUILayout><compOutputs><compOutput><uid v="1407213313"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.709999979"/></paramValue></parameter><parameter><name v="angle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.541166663"/></paramValue></parameter><parameter><name v="randomseed"/><relativeTo v="2"/><paramValue><constantValueInt32 v="12"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407213313"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407249364"/><connections><connection><identifier v="input"/><connRef v="1407212376"/><connRefOutput v="1407212377"/></connection></connections><GUILayout><gpos v="-3629.7207 -1008 0"/></GUILayout><compOutputs><compOutput><uid v="1407213313"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.370000005"/></paramValue></parameter><parameter><name v="angle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.0070833"/></paramValue></parameter><parameter><name v="randomseed"/><relativeTo v="2"/><paramValue><constantValueInt32 v="26"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407213313"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407249365"/><connections><connection><identifier v="input1"/><connRef v="1407249363"/><connRefOutput v="1407213313"/></connection></connections><GUILayout><gpos v="-3504 -880 0"/></GUILayout><compOutputs><compOutput><uid v="1407213378"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.282874614 0.282874614 0.282874614 1"/></paramValue></parameter><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.438837916 0.438837916 0.438837916 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407249395"/><connections><connection><identifier v="destination"/><connRef v="1407249396"/><connRefOutput v="1407214063"/></connection><connection><identifier v="source"/><connRef v="1407435567"/><connRefOutput v="1407431747"/></connection></connections><GUILayout><gpos v="-1232 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407214063"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407249396"/><connections><connection><identifier v="destination"/><connRef v="1407246626"/><connRefOutput v="1407214063"/></connection><connection><identifier v="source"/><connRef v="1407434323"/><connRefOutput v="1407431747"/></connection></connections><GUILayout><gpos v="-1392 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407214063"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407253754"/><connections><connection><identifier v="input1"/><connRef v="1407253760"/><connRefOutput v="1407213313"/></connection></connections><GUILayout><gpos v="-3504 -1392 0"/></GUILayout><compOutputs><compOutput><uid v="1407213378"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.282874614 0.282874614 0.282874614 1"/></paramValue></parameter><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.438837916 0.438837916 0.438837916 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407253755"/><connections><connection><identifier v="input1"/><connRef v="1407253758"/><connRefOutput v="1407213313"/></connection></connections><GUILayout><gpos v="-3504 -1136 0"/></GUILayout><compOutputs><compOutput><uid v="1407213378"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.258409798 0.258409798 0.258409798 1"/></paramValue></parameter><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.659021378 0.659021378 0.659021378 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407253756"/><connections><connection><identifier v="source"/><connRef v="1407253761"/><connRefOutput v="1407213378"/></connection><connection><identifier v="mask"/><connRef v="1407203524"/><connRefOutput v="1407203295"/></connection></connections><GUILayout><gpos v="-3376 -1520 0"/></GUILayout><compOutputs><compOutput><uid v="1407213688"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters><parameter><name v="combinedistance"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="256"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407253757"/><connections><connection><identifier v="input1"/><connRef v="1407253759"/><connRefOutput v="1407213313"/></connection></connections><GUILayout><gpos v="-3501.72095 -1264 0"/></GUILayout><compOutputs><compOutput><uid v="1407213378"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.200305805 0.200305805 0.200305805 1"/></paramValue></parameter><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.659021378 0.659021378 0.659021378 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407253758"/><connections><connection><identifier v="input"/><connRef v="1407212376"/><connRefOutput v="1407212377"/></connection></connections><GUILayout><gpos v="-3632 -1136 0"/></GUILayout><compOutputs><compOutput><uid v="1407213313"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.909999967"/></paramValue></parameter><parameter><name v="angle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.541166663"/></paramValue></parameter><parameter><name v="randomseed"/><relativeTo v="2"/><paramValue><constantValueInt32 v="10"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407213313"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407253759"/><connections><connection><identifier v="input"/><connRef v="1407212376"/><connRefOutput v="1407212377"/></connection></connections><GUILayout><gpos v="-3629.72095 -1264 0"/></GUILayout><compOutputs><compOutput><uid v="1407213313"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.909999967"/></paramValue></parameter><parameter><name v="angle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.00711107"/></paramValue></parameter><parameter><name v="randomseed"/><relativeTo v="2"/><paramValue><constantValueInt32 v="35"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407213313"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407253760"/><connections><connection><identifier v="input"/><connRef v="1407212376"/><connRefOutput v="1407212377"/></connection></connections><GUILayout><gpos v="-3632 -1392 0"/></GUILayout><compOutputs><compOutput><uid v="1407213313"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.709999979"/></paramValue></parameter><parameter><name v="angle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.541166663"/></paramValue></parameter><parameter><name v="randomseed"/><relativeTo v="2"/><paramValue><constantValueInt32 v="22"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407213313"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407253761"/><connections><connection><identifier v="input1"/><connRef v="1407253762"/><connRefOutput v="1407213313"/></connection></connections><GUILayout><gpos v="-3504 -1520 0"/></GUILayout><compOutputs><compOutput><uid v="1407213378"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.37155962 0.37155962 0.37155962 1"/></paramValue></parameter><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.536697268 0.536697268 0.536697268 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407253762"/><connections><connection><identifier v="input"/><connRef v="1407212376"/><connRefOutput v="1407212377"/></connection></connections><GUILayout><gpos v="-3632 -1520 0"/></GUILayout><compOutputs><compOutput><uid v="1407213313"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.370000005"/></paramValue></parameter><parameter><name v="angle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.0070833"/></paramValue></parameter><parameter><name v="randomseed"/><relativeTo v="2"/><paramValue><constantValueInt32 v="26"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407213313"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407253763"/><connections><connection><identifier v="source"/><connRef v="1407253754"/><connRefOutput v="1407213378"/></connection><connection><identifier v="mask"/><connRef v="1407203524"/><connRefOutput v="1407203295"/></connection></connections><GUILayout><gpos v="-3376 -1392 0"/></GUILayout><compOutputs><compOutput><uid v="1407213688"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters><parameter><name v="combinedistance"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="256"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407253764"/><connections><connection><identifier v="source"/><connRef v="1407253755"/><connRefOutput v="1407213378"/></connection><connection><identifier v="mask"/><connRef v="1407203524"/><connRefOutput v="1407203295"/></connection></connections><GUILayout><gpos v="-3376 -1136 0"/></GUILayout><compOutputs><compOutput><uid v="1407213688"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters><parameter><name v="combinedistance"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="256"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407253765"/><connections><connection><identifier v="source"/><connRef v="1407253757"/><connRefOutput v="1407213378"/></connection><connection><identifier v="mask"/><connRef v="1407203524"/><connRefOutput v="1407203295"/></connection></connections><GUILayout><gpos v="-3373.72095 -1264 0"/></GUILayout><compOutputs><compOutput><uid v="1407213688"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters><parameter><name v="combinedistance"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="256"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407253788"/><connections><connection><identifier v="destination"/><connRef v="1407253789"/><connRefOutput v="1407214063"/></connection><connection><identifier v="source"/><connRef v="1407253765"/><connRefOutput v="1407213688"/></connection></connections><GUILayout><gpos v="-464 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407214063"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407253789"/><connections><connection><identifier v="source"/><connRef v="1407253764"/><connRefOutput v="1407213688"/></connection><connection><identifier v="destination"/><connRef v="1407464089"/><connRefOutput v="1407464090"/></connection></connections><GUILayout><gpos v="-624 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407214063"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407253790"/><connections><connection><identifier v="destination"/><connRef v="1407253791"/><connRefOutput v="1407214063"/></connection><connection><identifier v="source"/><connRef v="1407253756"/><connRefOutput v="1407213688"/></connection></connections><GUILayout><gpos v="-63.277832 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407214063"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407253791"/><connections><connection><identifier v="destination"/><connRef v="1407253788"/><connRefOutput v="1407214063"/></connection><connection><identifier v="source"/><connRef v="1407253763"/><connRefOutput v="1407213688"/></connection></connections><GUILayout><gpos v="-272 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407214063"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407263529"/><GUILayout><gpos v="-4208 879.05249 0"/></GUILayout><compOutputs><compOutput><uid v="1407263530"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1449399972"/><parameters><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="20"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="20"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="8.0199995"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.589999974"/></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="5.58999968"/></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.469999999"/></paramValue></parameter><parameter><name v="rotation_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.319999993"/></paramValue></parameter><parameter><name v="mask_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.159999996"/></paramValue></parameter><parameter><name v="color_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.75"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407263530"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407264172"/><connections><connection><identifier v="Input"/><connRef v="1407263529"/><connRefOutput v="1407263530"/></connection></connections><GUILayout><gpos v="-4048 879.05249 0"/></GUILayout><compOutputs><compOutput><uid v="1407264173"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1449400845"/><parameters/><outputBridgings><outputBridging><uid v="1407264173"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407264204"/><connections><connection><identifier v="input1"/><connRef v="1407264172"/><connRefOutput v="1407264173"/></connection></connections><GUILayout><gpos v="-3888 879.05249 0"/></GUILayout><compOutputs><compOutput><uid v="1407264205"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelouthigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.451070338 0.451070338 0.451070338 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407264388"/><connections><connection><identifier v="destination"/><connRef v="1407222068"/><connRefOutput v="1407222069"/></connection><connection><identifier v="source"/><connRef v="1407270305"/><connRefOutput v="1407270306"/></connection></connections><GUILayout><gpos v="-3519.27783 464 0"/></GUILayout><compOutputs><compOutput><uid v="1407264389"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.370000005"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407270305"/><connections><connection><identifier v="inputintensity"/><connRef v="1407271662"/><connRefOutput v="1407271663"/></connection><connection><identifier v="input1"/><connRef v="1407264204"/><connRefOutput v="1407264205"/></connection></connections><GUILayout><gpos v="-3696 880 0"/></GUILayout><compOutputs><compOutput><uid v="1407270306"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="directionalwarp"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="5.33999968"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407271606"/><GUILayout><gpos v="-3903.04932 1088.02344 0"/></GUILayout><compOutputs><compOutput><uid v="1407271607"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///crystal_1?dependency=1407271237"/><parameters><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueInt32 v="14"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407271607"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407271662"/><connections><connection><identifier v="Source"/><connRef v="1407271606"/><connRefOutput v="1407271607"/></connection></connections><GUILayout><gpos v="-3743.04932 1088.02344 0"/></GUILayout><compOutputs><compOutput><uid v="1407271663"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1299236171"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.25999999"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407271663"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407278315"/><connections><connection><identifier v="inputintensity"/><connRef v="1407271662"/><connRefOutput v="1407271663"/></connection><connection><identifier v="input1"/><connRef v="1407292258"/><connRefOutput v="1407214063"/></connection></connections><GUILayout><gpos v="1216.80273 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407270306"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="directionalwarp"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.64999998"/></paramValue></parameter><parameter><name v="warpangle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.116666667"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407280227"/><connections><connection><identifier v="inputintensity"/><connRef v="1407271662"/><connRefOutput v="1407271663"/></connection><connection><identifier v="input1"/><connRef v="1407278315"/><connRefOutput v="1407270306"/></connection></connections><GUILayout><gpos v="1376.80273 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407270306"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="directionalwarp"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="3.25999999"/></paramValue></parameter><parameter><name v="warpangle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.417861104"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407282150"/><connections><connection><identifier v="destination"/><connRef v="1407264388"/><connRefOutput v="1407264389"/></connection><connection><identifier v="source"/><connRef v="1407455314"/><connRefOutput v="1407455315"/></connection></connections><GUILayout><gpos v="-3376 464 0"/></GUILayout><compOutputs><compOutput><uid v="1407282151"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.370000005"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407282167"/><connections><connection><identifier v="mask"/><connRef v="1407203524"/><connRefOutput v="1407203295"/></connection><connection><identifier v="source"/><connRef v="1407212731"/><connRefOutput v="1407212732"/></connection></connections><GUILayout><gpos v="-3504 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407213688"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters><parameter><name v="combinedistance"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="256"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407283127"/><connections><connection><identifier v="Input"/><connRef v="1407280227"/><connRefOutput v="1407270306"/></connection></connections><GUILayout><gpos v="1536.80273 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407283128"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1449400845"/><parameters/><outputBridgings><outputBridging><uid v="1407283128"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407285009"/><connections><connection><identifier v="input"/><connRef v="1407212376"/><connRefOutput v="1407212377"/></connection></connections><GUILayout><gpos v="-3632 -1648 0"/></GUILayout><compOutputs><compOutput><uid v="1407213313"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.370000005"/></paramValue></parameter><parameter><name v="angle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.36494446"/></paramValue></parameter><parameter><name v="randomseed"/><relativeTo v="2"/><paramValue><constantValueInt32 v="26"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407213313"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407285010"/><connections><connection><identifier v="source"/><connRef v="1407285011"/><connRefOutput v="1407213378"/></connection><connection><identifier v="mask"/><connRef v="1407203524"/><connRefOutput v="1407203295"/></connection></connections><GUILayout><gpos v="-3376 -1648 0"/></GUILayout><compOutputs><compOutput><uid v="1407213688"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters><parameter><name v="combinedistance"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="256"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407285011"/><connections><connection><identifier v="input1"/><connRef v="1407285009"/><connRefOutput v="1407213313"/></connection></connections><GUILayout><gpos v="-3504 -1648 0"/></GUILayout><compOutputs><compOutput><uid v="1407213378"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.45718655 0.45718655 0.45718655 1"/></paramValue></parameter><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.426605493 0.426605493 0.426605493 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407285025"/><connections><connection><identifier v="destination"/><connRef v="1407253790"/><connRefOutput v="1407214063"/></connection><connection><identifier v="source"/><connRef v="1407285010"/><connRefOutput v="1407213688"/></connection></connections><GUILayout><gpos v="157.193848 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407214063"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407288418"/><connections><connection><identifier v="input"/><connRef v="1407212376"/><connRefOutput v="1407212377"/></connection></connections><GUILayout><gpos v="-3632 -1776 0"/></GUILayout><compOutputs><compOutput><uid v="1407213313"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.370000005"/></paramValue></parameter><parameter><name v="angle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.36494446"/></paramValue></parameter><parameter><name v="randomseed"/><relativeTo v="2"/><paramValue><constantValueInt32 v="46"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407213313"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407288419"/><connections><connection><identifier v="input1"/><connRef v="1407288418"/><connRefOutput v="1407213313"/></connection></connections><GUILayout><gpos v="-3504 -1776 0"/></GUILayout><compOutputs><compOutput><uid v="1407213378"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.45718655 0.45718655 0.45718655 1"/></paramValue></parameter><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.426605493 0.426605493 0.426605493 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407288420"/><connections><connection><identifier v="source"/><connRef v="1407288419"/><connRefOutput v="1407213378"/></connection><connection><identifier v="mask"/><connRef v="1407203524"/><connRefOutput v="1407203295"/></connection></connections><GUILayout><gpos v="-3376 -1776 0"/></GUILayout><compOutputs><compOutput><uid v="1407213688"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters><parameter><name v="combinedistance"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="256"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407288433"/><connections><connection><identifier v="destination"/><connRef v="1407285025"/><connRefOutput v="1407214063"/></connection><connection><identifier v="source"/><connRef v="1407288420"/><connRefOutput v="1407213688"/></connection></connections><GUILayout><gpos v="400 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407214063"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407292116"/><connections><connection><identifier v="input"/><connRef v="1407212376"/><connRefOutput v="1407212377"/></connection></connections><GUILayout><gpos v="-4890.6665 2586.66675 0"/></GUILayout><compOutputs><compOutput><uid v="1407292117"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_bbox_size?dependency=1323881949"/><parameters/><outputBridgings><outputBridging><uid v="1407292117"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407292129"/><connections><connection><identifier v="Input"/><connRef v="1407292116"/><connRefOutput v="1407292117"/></connection></connections><GUILayout><gpos v="-4730.6665 2586.66675 0"/></GUILayout><compOutputs><compOutput><uid v="1407292130"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1449400845"/><parameters/><outputBridgings><outputBridging><uid v="1407292130"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407292136"/><connections><connection><identifier v="Input_1"/><connRef v="1407292203"/><connRefOutput v="1407213688"/></connection></connections><GUILayout><gpos v="193.832199 592 0"/></GUILayout><compOutputs><compOutput><uid v="1407292137"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1449404462"/><parameters><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.289999992"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407292137"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407292203"/><connections><connection><identifier v="mask"/><connRef v="1407203524"/><connRefOutput v="1407203295"/></connection><connection><identifier v="source"/><connRef v="1407292129"/><connRefOutput v="1407292130"/></connection></connections><GUILayout><gpos v="-4602.6665 2586.66675 0"/></GUILayout><compOutputs><compOutput><uid v="1407213688"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters><parameter><name v="combinedistance"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="256"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407292229"/><connections><connection><identifier v="source"/><connRef v="1407292231"/><connRefOutput v="1407213378"/></connection><connection><identifier v="mask"/><connRef v="1407203524"/><connRefOutput v="1407203295"/></connection></connections><GUILayout><gpos v="-3376 -2160 0"/></GUILayout><compOutputs><compOutput><uid v="1407213688"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters><parameter><name v="combinedistance"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1000"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407292230"/><connections><connection><identifier v="source"/><connRef v="1407292232"/><connRefOutput v="1407213378"/></connection><connection><identifier v="mask"/><connRef v="1407203524"/><connRefOutput v="1407203295"/></connection></connections><GUILayout><gpos v="-3376 -2032 0"/></GUILayout><compOutputs><compOutput><uid v="1407213688"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters><parameter><name v="combinedistance"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="256"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407292231"/><connections><connection><identifier v="input1"/><connRef v="1407292233"/><connRefOutput v="1407213313"/></connection></connections><GUILayout><gpos v="-3504 -2160 0"/></GUILayout><compOutputs><compOutput><uid v="1407213378"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.576452613 0.576452613 0.576452613 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407292232"/><connections><connection><identifier v="input1"/><connRef v="1407292237"/><connRefOutput v="1407213313"/></connection></connections><GUILayout><gpos v="-3504 -2032 0"/></GUILayout><compOutputs><compOutput><uid v="1407213378"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.481651366 0.481651366 0.481651366 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407292233"/><connections><connection><identifier v="input"/><connRef v="1407212376"/><connRefOutput v="1407212377"/></connection></connections><GUILayout><gpos v="-3632 -2160 0"/></GUILayout><compOutputs><compOutput><uid v="1407213313"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.370000005"/></paramValue></parameter><parameter><name v="angle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="2.34925008"/></paramValue></parameter><parameter><name v="randomseed"/><relativeTo v="2"/><paramValue><constantValueInt32 v="46"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407213313"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407292234"/><connections><connection><identifier v="input1"/><connRef v="1407292236"/><connRefOutput v="1407213313"/></connection></connections><GUILayout><gpos v="-3504 -1904 0"/></GUILayout><compOutputs><compOutput><uid v="1407213378"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.506116211 0.506116211 0.506116211 1"/></paramValue></parameter><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.20336391 0.20336391 0.20336391 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407292235"/><connections><connection><identifier v="source"/><connRef v="1407292234"/><connRefOutput v="1407213378"/></connection><connection><identifier v="mask"/><connRef v="1407203524"/><connRefOutput v="1407203295"/></connection></connections><GUILayout><gpos v="-3376 -1904 0"/></GUILayout><compOutputs><compOutput><uid v="1407213688"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters><parameter><name v="combinedistance"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="256"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407292236"/><connections><connection><identifier v="input"/><connRef v="1407212376"/><connRefOutput v="1407212377"/></connection></connections><GUILayout><gpos v="-3632 -1904 0"/></GUILayout><compOutputs><compOutput><uid v="1407213313"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.370000005"/></paramValue></parameter><parameter><name v="angle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.50352776"/></paramValue></parameter><parameter><name v="randomseed"/><relativeTo v="2"/><paramValue><constantValueInt32 v="36"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407213313"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407292237"/><connections><connection><identifier v="input"/><connRef v="1407212376"/><connRefOutput v="1407212377"/></connection></connections><GUILayout><gpos v="-3632 -2032 0"/></GUILayout><compOutputs><compOutput><uid v="1407213313"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.50999999"/></paramValue></parameter><parameter><name v="angle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="2.11558342"/></paramValue></parameter><parameter><name v="randomseed"/><relativeTo v="2"/><paramValue><constantValueInt32 v="36"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407213313"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407292257"/><connections><connection><identifier v="destination"/><connRef v="1407292259"/><connRefOutput v="1407214063"/></connection><connection><identifier v="source"/><connRef v="1407292230"/><connRefOutput v="1407213688"/></connection><connection><identifier v="opacity"/><connRef v="1407292136"/><connRefOutput v="1407292137"/></connection></connections><GUILayout><gpos v="816 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407214063"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407292258"/><connections><connection><identifier v="destination"/><connRef v="1407292257"/><connRefOutput v="1407214063"/></connection><connection><identifier v="opacity"/><connRef v="1407292136"/><connRefOutput v="1407292137"/></connection><connection><identifier v="source"/><connRef v="1407325955"/><connRefOutput v="1407287588"/></connection></connections><GUILayout><gpos v="1008 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407214063"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407292259"/><connections><connection><identifier v="destination"/><connRef v="1407288433"/><connRefOutput v="1407214063"/></connection><connection><identifier v="opacity"/><connRef v="1407292136"/><connRefOutput v="1407292137"/></connection><connection><identifier v="source"/><connRef v="1407292235"/><connRefOutput v="1407213688"/></connection></connections><GUILayout><gpos v="624 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407214063"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407322618"/><connections><connection><identifier v="Input"/><connRef v="1407282167"/><connRefOutput v="1407213688"/></connection></connections><GUILayout><gpos v="5482.06396 -1633.66919 0"/></GUILayout><compOutputs><compOutput><uid v="1407322619"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1449400845"/><parameters/><outputBridgings><outputBridging><uid v="1407322619"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407322631"/><connections><connection><identifier v="input1"/><connRef v="1407322618"/><connRefOutput v="1407322619"/></connection></connections><GUILayout><gpos v="5644.78955 -1633.66919 0"/></GUILayout><compOutputs><compOutput><uid v="1407322632"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1407322639"/><paramsArrayCells><paramsArrayCell><uid v="1489905181"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.665289283 0.55899626 0.46490097 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1407324757"/><connections><connection><identifier v="pattern_input"/><connRef v="1407325385"/><connRefOutput v="1407325386"/></connection><connection><identifier v="color_map_input"/><connRef v="1407332759"/><connRefOutput v="1407332760"/></connection><connection><identifier v="mask_map_input"/><connRef v="1407332759"/><connRefOutput v="1407332760"/></connection></connections><GUILayout><gpos v="-5306.6665 2746.66675 0"/></GUILayout><compOutputs><compOutput><uid v="1407324758"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1449399972"/><parameters><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="100"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="100"/></paramValue></parameter><parameter><name v="size"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="2.91999984 0.829999983"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="16.789999"/></paramValue></parameter><parameter><name v="mask_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.629999995"/></paramValue></parameter><parameter><name v="color_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.269999981"/></paramValue></parameter><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.50999999"/></paramValue></parameter><parameter><name v="color_parametrization_multiplier"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.620000005"/></paramValue></parameter><parameter><name v="mask_map_threshold"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0799999982"/></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.299999982"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.48999998"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407324758"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407324885"/><connections><connection><identifier v="color_map_input"/><connRef v="1407332759"/><connRefOutput v="1407332760"/></connection><connection><identifier v="mask_map_input"/><connRef v="1407332759"/><connRefOutput v="1407332760"/></connection></connections><GUILayout><gpos v="-5434.6665 3002.66675 0"/></GUILayout><compOutputs><compOutput><uid v="1407324758"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1449399972"/><parameters><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="64"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="64"/></paramValue></parameter><parameter><name v="size"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="2 0.109999999"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="10"/></paramValue></parameter><parameter><name v="rotation_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0199999996"/></paramValue></parameter><parameter><name v="mask_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.429999977"/></paramValue></parameter><parameter><name v="color_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.409999996"/></paramValue></parameter><parameter><name v="randomseed"/><relativeTo v="2"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter><parameter><name v="color_parametrization_multiplier"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407324758"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407324915"/><connections><connection><identifier v="input1"/><connRef v="1407324921"/><connRefOutput v="1407324922"/></connection><connection><identifier v="inputintensity"/><connRef v="1407324943"/><connRefOutput v="1407324944"/></connection></connections><GUILayout><gpos v="-4858.6665 2746.66675 0"/></GUILayout><compOutputs><compOutput><uid v="1407324916"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="directionalwarp"/><parameters><parameter><name v="warpangle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.055277776"/></paramValue></parameter><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="5.94999981"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407324921"/><connections><connection><identifier v="Input"/><connRef v="1407324757"/><connRefOutput v="1407324758"/></connection></connections><GUILayout><gpos v="-5114.6665 2746.66675 0"/></GUILayout><compOutputs><compOutput><uid v="1407324922"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1449400845"/><parameters/><outputBridgings><outputBridging><uid v="1407324922"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407324930"/><connections><connection><identifier v="Input"/><connRef v="1407324885"/><connRefOutput v="1407324758"/></connection></connections><GUILayout><gpos v="-5274.6665 3002.66675 0"/></GUILayout><compOutputs><compOutput><uid v="1407324931"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1449400845"/><parameters/><outputBridgings><outputBridging><uid v="1407324931"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407324943"/><connections><connection><identifier v="Source"/><connRef v="1407324930"/><connRefOutput v="1407324931"/></connection></connections><GUILayout><gpos v="-5114.6665 3002.66675 0"/></GUILayout><compOutputs><compOutput><uid v="1407324944"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1299236171"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.5"/></paramValue></parameter><parameter><name v="Quality"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407324944"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407325341"/><GUILayout><gpos v="-5946.6665 2746.66675 0"/></GUILayout><compOutputs><compOutput><uid v="1407325342"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///waveform_1?dependency=1446771957"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1024"/></paramValue></parameter><parameter><name v="SizeMax"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.149999991"/></paramValue></parameter><parameter><name v="WaveNumber"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="Noise"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="Pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="9"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407325342"/><identifier v="Waveform"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407325385"/><connections><connection><identifier v="input"/><connRef v="1407325341"/><connRefOutput v="1407325342"/></connection></connections><GUILayout><gpos v="-5786.6665 2746.66675 0"/></GUILayout><compOutputs><compOutput><uid v="1407325386"/><comptype v="2"/></compOutput><compOutput><uid v="1407325387"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///bevel?dependency=1449405586"/><parameters><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="-0.099999994"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407325386"/><identifier v="height"/></outputBridging><outputBridging><uid v="1407325387"/><identifier v="normal"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407330056"/><connections><connection><identifier v="Source"/><connRef v="1407324915"/><connRefOutput v="1407324916"/></connection><connection><identifier v="Effect"/><connRef v="1407331781"/><connRefOutput v="1407331782"/></connection></connections><GUILayout><gpos v="-4602.6665 2746.66675 0"/></GUILayout><compOutputs><compOutput><uid v="1407330057"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///slope_blur_grayscale_2?dependency=1449402040"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="32"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="2.57999992"/></paramValue></parameter><parameter><name v="mode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407330057"/><identifier v="Slope_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407331781"/><GUILayout><gpos v="-4858.6665 2906.66675 0"/></GUILayout><compOutputs><compOutput><uid v="1407331782"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///fractal_sum_base_2?dependency=1449402686"/><parameters><parameter><name v="Roughness"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.149999991"/></paramValue></parameter><parameter><name v="MinLevel"/><relativeTo v="0"/><paramValue><constantValueInt32 v="5"/></paramValue></parameter><parameter><name v="MaxLevel"/><relativeTo v="0"/><paramValue><constantValueInt32 v="9"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407331782"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407332514"/><GUILayout><gpos v="-5896.09131 3036.18628 0"/></GUILayout><compOutputs><compOutput><uid v="1407332515"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///perlin_noise?dependency=1407332030"/><parameters><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueInt32 v="9"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407332515"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407332759"/><connections><connection><identifier v="Input"/><connRef v="1407332514"/><connRefOutput v="1407332515"/></connection></connections><GUILayout><gpos v="-5754.6665 3034.66675 0"/></GUILayout><compOutputs><compOutput><uid v="1407332760"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1449400845"/><parameters/><outputBridgings><outputBridging><uid v="1407332760"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407333064"/><connections><connection><identifier v="input"/><connRef v="1407330056"/><connRefOutput v="1407330057"/></connection><connection><identifier v="vector_map"/><connRef v="1407333089"/><connRefOutput v="1407333090"/></connection></connections><GUILayout><gpos v="-4090.6665 2746.66675 0"/></GUILayout><compOutputs><compOutput><uid v="1407333065"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///vector_warp_grayscale?dependency=1407332989"/><parameters><parameter><name v="vector_format"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="2"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407333065"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407333089"/><connections><connection><identifier v="input1"/><connRef v="1407292203"/><connRefOutput v="1407213688"/></connection></connections><GUILayout><gpos v="-318 1786.66663 -1"/><docked v="1"/><dockDistance v="448 384"/></GUILayout><compOutputs><compOutput><uid v="1407333090"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1407335953"/><connections><connection><identifier v="Input_1"/><connRef v="1407400443"/><connRefOutput v="1407400444"/></connection></connections><GUILayout><gpos v="-2810.6665 3002.66675 0"/></GUILayout><compOutputs><compOutput><uid v="1407335954"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1449404462"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.429999977"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407335954"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407398133"/><connections><connection><identifier v="input1"/><connRef v="1407292203"/><connRefOutput v="1407213688"/></connection></connections><GUILayout><gpos v="-318 2170.66675 502"/><docked v="1"/><dockDistance v="448 384"/></GUILayout><compOutputs><compOutput><uid v="1407333090"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1407398134"/><connections><connection><identifier v="input1"/><connRef v="1407330056"/><connRefOutput v="1407330057"/></connection></connections><GUILayout><gpos v="-4378.6665 3130.66675 0"/></GUILayout><compOutputs><compOutput><uid v="1407398077"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="transformation"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1407398135"/><connections><connection><identifier v="vector_map"/><connRef v="1407398133"/><connRefOutput v="1407333090"/></connection><connection><identifier v="input"/><connRef v="1407398134"/><connRefOutput v="1407398077"/></connection></connections><GUILayout><gpos v="-4090.6665 3130.66675 501"/></GUILayout><compOutputs><compOutput><uid v="1407333065"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///vector_warp_grayscale?dependency=1407332989"/><parameters><parameter><name v="vector_format"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="2"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407333065"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407398149"/><connections><connection><identifier v="input1"/><connRef v="1407292203"/><connRefOutput v="1407213688"/></connection></connections><GUILayout><gpos v="-318 2362.66675 502"/><docked v="1"/><dockDistance v="448 384"/></GUILayout><compOutputs><compOutput><uid v="1407333090"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1407398150"/><connections><connection><identifier v="input1"/><connRef v="1407330056"/><connRefOutput v="1407330057"/></connection></connections><GUILayout><gpos v="-4378.6665 3322.66675 0"/></GUILayout><compOutputs><compOutput><uid v="1407398077"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="transformation"/><parameters><parameter><name v="matrix22"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0 1 -1 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407398151"/><connections><connection><identifier v="vector_map"/><connRef v="1407398149"/><connRefOutput v="1407333090"/></connection><connection><identifier v="input"/><connRef v="1407398150"/><connRefOutput v="1407398077"/></connection></connections><GUILayout><gpos v="-4090.6665 3322.66675 501"/></GUILayout><compOutputs><compOutput><uid v="1407333065"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///vector_warp_grayscale?dependency=1407332989"/><parameters><parameter><name v="vector_format"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="2"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407333065"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407398169"/><connections><connection><identifier v="input1"/><connRef v="1407292203"/><connRefOutput v="1407213688"/></connection></connections><GUILayout><gpos v="-318 2522.66675 504"/><docked v="1"/><dockDistance v="448 384"/></GUILayout><compOutputs><compOutput><uid v="1407333090"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1407398170"/><connections><connection><identifier v="vector_map"/><connRef v="1407398169"/><connRefOutput v="1407333090"/></connection><connection><identifier v="input"/><connRef v="1407398171"/><connRefOutput v="1407398077"/></connection></connections><GUILayout><gpos v="-4090.6665 3482.66675 501"/></GUILayout><compOutputs><compOutput><uid v="1407333065"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///vector_warp_grayscale?dependency=1407332989"/><parameters><parameter><name v="vector_format"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="2"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407333065"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407398171"/><connections><connection><identifier v="input1"/><connRef v="1407330056"/><connRefOutput v="1407330057"/></connection></connections><GUILayout><gpos v="-4378.6665 3482.66675 0"/></GUILayout><compOutputs><compOutput><uid v="1407398077"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="transformation"/><parameters><parameter><name v="matrix22"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="-0.782500029 -0.622699976 0.622699976 -0.782500029"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407398267"/><connections><connection><identifier v="destination"/><connRef v="1407333064"/><connRefOutput v="1407333065"/></connection><connection><identifier v="source"/><connRef v="1407398135"/><connRefOutput v="1407333065"/></connection><connection><identifier v="opacity"/><connRef v="1407398289"/><connRefOutput v="1407398290"/></connection></connections><GUILayout><gpos v="-3834.6665 3002.66675 0"/></GUILayout><compOutputs><compOutput><uid v="1407398268"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1407398289"/><connections><connection><identifier v="Input_1"/><connRef v="1407292203"/><connRefOutput v="1407213688"/></connection></connections><GUILayout><gpos v="-4090.6665 3002.66675 0"/></GUILayout><compOutputs><compOutput><uid v="1407398290"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1449404462"/><parameters><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.179999992"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407398290"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407398326"/><connections><connection><identifier v="source"/><connRef v="1407398151"/><connRefOutput v="1407333065"/></connection><connection><identifier v="destination"/><connRef v="1407398267"/><connRefOutput v="1407398268"/></connection><connection><identifier v="opacity"/><connRef v="1407398368"/><connRefOutput v="1407398369"/></connection></connections><GUILayout><gpos v="-3610.6665 3002.66675 0"/></GUILayout><compOutputs><compOutput><uid v="1407398268"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1407398327"/><connections><connection><identifier v="Input_1"/><connRef v="1407292203"/><connRefOutput v="1407213688"/></connection></connections><GUILayout><gpos v="-62 2160 -1"/><docked v="1"/><dockDistance v="160 0"/></GUILayout><compOutputs><compOutput><uid v="1407398290"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1449404462"/><parameters><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.289999992"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407398290"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407398368"/><connections><connection><identifier v="destination"/><connRef v="1407398327"/><connRefOutput v="1407398290"/></connection><connection><identifier v="source"/><connRef v="1407398289"/><connRefOutput v="1407398290"/></connection></connections><GUILayout><gpos v="-3834.6665 3130.66675 501"/></GUILayout><compOutputs><compOutput><uid v="1407398369"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407400005"/><connections><connection><identifier v="input"/><connRef v="1407292203"/><connRefOutput v="1407213688"/></connection></connections><GUILayout><gpos v="354 2053.33325 -1"/><docked v="1"/><dockDistance v="416 -448"/></GUILayout><compOutputs><compOutput><uid v="1407400006"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_select?dependency=1407203168"/><parameters><parameter><name v="constrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="range"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0700000003"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407400006"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407400038"/><connections><connection><identifier v="destination"/><connRef v="1407398326"/><connRefOutput v="1407398268"/></connection><connection><identifier v="opacity"/><connRef v="1407400005"/><connRefOutput v="1407400006"/></connection><connection><identifier v="source"/><connRef v="1407398170"/><connRefOutput v="1407333065"/></connection></connections><GUILayout><gpos v="-3418.6665 3002.66675 501"/></GUILayout><compOutputs><compOutput><uid v="1407398268"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1407400064"/><connections><connection><identifier v="input"/><connRef v="1407292203"/><connRefOutput v="1407213688"/></connection></connections><GUILayout><gpos v="578 2053.33325 -1"/><docked v="1"/><dockDistance v="640 -608"/></GUILayout><compOutputs><compOutput><uid v="1407400006"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_select?dependency=1407203168"/><parameters><parameter><name v="constrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="range"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0700000003"/></paramValue></parameter><parameter><name v="position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.589999974"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407400006"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407400159"/><connections><connection><identifier v="destination"/><connRef v="1407400038"/><connRefOutput v="1407398268"/></connection><connection><identifier v="source"/><connRef v="1407400178"/><connRefOutput v="1407333065"/></connection><connection><identifier v="opacity"/><connRef v="1407400064"/><connRefOutput v="1407400006"/></connection></connections><GUILayout><gpos v="-3194.6665 3002.66675 501"/></GUILayout><compOutputs><compOutput><uid v="1407398268"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1407400177"/><connections><connection><identifier v="input1"/><connRef v="1407292203"/><connRefOutput v="1407213688"/></connection></connections><GUILayout><gpos v="-318 2714.66675 500"/><docked v="1"/><dockDistance v="448 384"/></GUILayout><compOutputs><compOutput><uid v="1407333090"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1407400178"/><connections><connection><identifier v="vector_map"/><connRef v="1407400177"/><connRefOutput v="1407333090"/></connection><connection><identifier v="input"/><connRef v="1407400179"/><connRefOutput v="1407398077"/></connection></connections><GUILayout><gpos v="-4090.6665 3674.66675 501"/></GUILayout><compOutputs><compOutput><uid v="1407333065"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///vector_warp_grayscale?dependency=1407332989"/><parameters><parameter><name v="vector_format"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="2"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407333065"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407400179"/><connections><connection><identifier v="input1"/><connRef v="1407330056"/><connRefOutput v="1407330057"/></connection></connections><GUILayout><gpos v="-4378.6665 3674.66675 0"/></GUILayout><compOutputs><compOutput><uid v="1407398077"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="transformation"/><parameters><parameter><name v="matrix22"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.719099998 -0.694899976 0.694899976 0.719099998"/></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.0285 0.303600013"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407400339"/><connections><connection><identifier v="input"/><connRef v="1407282167"/><connRefOutput v="1407213688"/></connection></connections><GUILayout><gpos v="-3386.6665 3354.66675 0"/></GUILayout><compOutputs><compOutput><uid v="1407400340"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_select?dependency=1407203168"/><parameters><parameter><name v="constrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="range"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.099999994"/></paramValue></parameter><parameter><name v="position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.519999981"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407400340"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407400443"/><connections><connection><identifier v="destination"/><connRef v="1407400159"/><connRefOutput v="1407398268"/></connection><connection><identifier v="opacity"/><connRef v="1407400339"/><connRefOutput v="1407400340"/></connection><connection><identifier v="source"/><connRef v="1407401100"/><connRefOutput v="1407333065"/></connection></connections><GUILayout><gpos v="-2970.6665 3002.66675 0"/></GUILayout><compOutputs><compOutput><uid v="1407400444"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1407401100"/><connections><connection><identifier v="vector_map"/><connRef v="1407401101"/><connRefOutput v="1407333090"/></connection><connection><identifier v="input"/><connRef v="1407401102"/><connRefOutput v="1407398077"/></connection></connections><GUILayout><gpos v="-4090.6665 3866.66675 0"/></GUILayout><compOutputs><compOutput><uid v="1407333065"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///vector_warp_grayscale?dependency=1407332989"/><parameters><parameter><name v="vector_format"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407333065"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407401101"/><connections><connection><identifier v="input1"/><connRef v="1407292203"/><connRefOutput v="1407213688"/></connection></connections><GUILayout><gpos v="-318 2906.66675 -1"/><docked v="1"/><dockDistance v="448 384"/></GUILayout><compOutputs><compOutput><uid v="1407333090"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1407401102"/><connections><connection><identifier v="input1"/><connRef v="1407330056"/><connRefOutput v="1407330057"/></connection></connections><GUILayout><gpos v="-4378.6665 3866.66675 0"/></GUILayout><compOutputs><compOutput><uid v="1407398077"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="transformation"/><parameters><parameter><name v="matrix22"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.719099998 -0.694899976 0.694899976 0.719099998"/></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.0763000026 -0.0930000022"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407431746"/><connections><connection><identifier v="Source"/><connRef v="1407213687"/><connRefOutput v="1407213688"/></connection><connection><identifier v="Effect"/><connRef v="1407431756"/><connRefOutput v="1407431757"/></connection></connections><GUILayout><gpos v="-3201.02734 144 0"/></GUILayout><compOutputs><compOutput><uid v="1407431747"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///slope_blur_grayscale_2?dependency=1449402040"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="32"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.49000001"/></paramValue></parameter><parameter><name v="mode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407431747"/><identifier v="Slope_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407431756"/><GUILayout><gpos v="-3408 848 0"/></GUILayout><compOutputs><compOutput><uid v="1407431757"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///fractal_sum_base_2?dependency=1449402686"/><parameters><parameter><name v="Roughness"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.219999999"/></paramValue></parameter><parameter><name v="MinLevel"/><relativeTo v="0"/><paramValue><constantValueInt32 v="5"/></paramValue></parameter><parameter><name v="MaxLevel"/><relativeTo v="0"/><paramValue><constantValueInt32 v="7"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407431757"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407433074"/><connections><connection><identifier v="Effect"/><connRef v="1407431756"/><connRefOutput v="1407431757"/></connection><connection><identifier v="Source"/><connRef v="1407237835"/><connRefOutput v="1407213688"/></connection></connections><GUILayout><gpos v="-3216 -624 0"/></GUILayout><compOutputs><compOutput><uid v="1407431747"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///slope_blur_grayscale_2?dependency=1449402040"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="32"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="2.86999989"/></paramValue></parameter><parameter><name v="mode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407431747"/><identifier v="Slope_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407434323"/><connections><connection><identifier v="Effect"/><connRef v="1407431756"/><connRefOutput v="1407431757"/></connection><connection><identifier v="Source"/><connRef v="1407249360"/><connRefOutput v="1407213688"/></connection></connections><GUILayout><gpos v="-3216 -880 0"/></GUILayout><compOutputs><compOutput><uid v="1407431747"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///slope_blur_grayscale_2?dependency=1449402040"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="32"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.49000001"/></paramValue></parameter><parameter><name v="mode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407431747"/><identifier v="Slope_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407435567"/><connections><connection><identifier v="Effect"/><connRef v="1407431756"/><connRefOutput v="1407431757"/></connection><connection><identifier v="Source"/><connRef v="1407249362"/><connRefOutput v="1407213688"/></connection></connections><GUILayout><gpos v="-3216 -1008 0"/></GUILayout><compOutputs><compOutput><uid v="1407431747"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///slope_blur_grayscale_2?dependency=1449402040"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="32"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="9.17999935"/></paramValue></parameter><parameter><name v="mode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407431747"/><identifier v="Slope_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407438084"/><connections><connection><identifier v="Effect"/><connRef v="1407431756"/><connRefOutput v="1407431757"/></connection><connection><identifier v="Source"/><connRef v="1407229217"/><connRefOutput v="1407213688"/></connection></connections><GUILayout><gpos v="-3216 -240 0"/></GUILayout><compOutputs><compOutput><uid v="1407431747"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///slope_blur_grayscale_2?dependency=1449402040"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="32"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.09000003"/></paramValue></parameter><parameter><name v="mode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407431747"/><identifier v="Slope_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407450726"/><connections><connection><identifier v="Effect"/><connRef v="1407431756"/><connRefOutput v="1407431757"/></connection><connection><identifier v="Source"/><connRef v="1407229211"/><connRefOutput v="1407213688"/></connection></connections><GUILayout><gpos v="-3216 -368 0"/></GUILayout><compOutputs><compOutput><uid v="1407431747"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///slope_blur_grayscale_2?dependency=1449402040"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="32"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.09000003"/></paramValue></parameter><parameter><name v="mode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407431747"/><identifier v="Slope_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407452756"/><connections><connection><identifier v="input"/><connRef v="1407212376"/><connRefOutput v="1407212377"/></connection></connections><GUILayout><gpos v="-3728 656 0"/></GUILayout><compOutputs><compOutput><uid v="1407452757"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_bbox_size?dependency=1323881949"/><parameters/><outputBridgings><outputBridging><uid v="1407452757"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407452772"/><connections><connection><identifier v="Input"/><connRef v="1407452756"/><connRefOutput v="1407452757"/></connection></connections><GUILayout><gpos v="-3568 656 0"/></GUILayout><compOutputs><compOutput><uid v="1407452773"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1449400845"/><parameters/><outputBridgings><outputBridging><uid v="1407452773"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407454032"/><connections><connection><identifier v="mask"/><connRef v="1407203524"/><connRefOutput v="1407203295"/></connection><connection><identifier v="source"/><connRef v="1407452772"/><connRefOutput v="1407452773"/></connection></connections><GUILayout><gpos v="-3424.17017 656 0"/></GUILayout><compOutputs><compOutput><uid v="1407213688"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters><parameter><name v="combinedistance"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="256"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407455314"/><connections><connection><identifier v="Input"/><connRef v="1407454032"/><connRefOutput v="1407213688"/></connection></connections><GUILayout><gpos v="-3264.17017 656 0"/></GUILayout><compOutputs><compOutput><uid v="1407455315"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1449400845"/><parameters/><outputBridgings><outputBridging><uid v="1407455315"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407464089"/><connections><connection><identifier v="Effect"/><connRef v="1407464111"/><connRefOutput v="1407464112"/></connection><connection><identifier v="Source"/><connRef v="1407427627"/><connRefOutput v="1407427628"/></connection></connections><GUILayout><gpos v="-752 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407464090"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///slope_blur_grayscale_2?dependency=1449402040"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="32"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.519999981"/></paramValue></parameter><parameter><name v="mode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407464090"/><identifier v="Slope_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407464111"/><GUILayout><gpos v="-958.015686 432 0"/></GUILayout><compOutputs><compOutput><uid v="1407464112"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///fractal_sum_base_2?dependency=1449402686"/><parameters><parameter><name v="Roughness"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.399999976"/></paramValue></parameter><parameter><name v="MinLevel"/><relativeTo v="0"/><paramValue><constantValueInt32 v="5"/></paramValue></parameter><parameter><name v="MaxLevel"/><relativeTo v="0"/><paramValue><constantValueInt32 v="9"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407464112"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407475980"/><connections><connection><identifier v="pattern_input"/><connRef v="1407492497"/><connRefOutput v="1407492498"/></connection></connections><GUILayout><gpos v="780.782166 -4080 0"/></GUILayout><compOutputs><compOutput><uid v="1407475981"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1449399972"/><parameters><parameter><name v="rotation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.75"/></paramValue></parameter><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="40"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="50"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="15"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.560000002"/></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="3.03999996"/></paramValue></parameter><parameter><name v="rotation_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0299999993"/></paramValue></parameter><parameter><name v="color_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.479999989"/></paramValue></parameter><parameter><name v="size"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.969999969 1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407475981"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407476316"/><GUILayout><gpos v="108.782143 -4144 0"/></GUILayout><compOutputs><compOutput><uid v="1407476317"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///shape?dependency=1311189026"/><parameters><parameter><name v="Pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="Size"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.769999981"/></paramValue></parameter><parameter><name v="Size_xy"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.449999988 1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407476317"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407476634"/><connections><connection><identifier v="Source"/><connRef v="1407476316"/><connRefOutput v="1407476317"/></connection></connections><GUILayout><gpos v="268.782135 -4144 0"/></GUILayout><compOutputs><compOutput><uid v="1407476635"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///skew_grayscale?dependency=1407476587"/><parameters><parameter><name v="Amount"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="-0.159999996"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407476635"/><identifier v="Skew"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407476732"/><connections><connection><identifier v="input"/><connRef v="1407476634"/><connRefOutput v="1407476635"/></connection></connections><GUILayout><gpos v="428.782135 -4144 0"/></GUILayout><compOutputs><compOutput><uid v="1407476733"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///mirror_grayscale?dependency=1407476641"/><parameters/><outputBridgings><outputBridging><uid v="1407476733"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407476805"/><connections><connection><identifier v="Source"/><connRef v="1407476732"/><connRefOutput v="1407476733"/></connection><connection><identifier v="Effect"/><connRef v="1407476732"/><connRefOutput v="1407476733"/></connection></connections><GUILayout><gpos v="588.782166 -4144 0"/></GUILayout><compOutputs><compOutput><uid v="1407476806"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///non_uniform_blur_grayscale?dependency=1290776959"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="16"/></paramValue></parameter><parameter><name v="Blades"/><relativeTo v="0"/><paramValue><constantValueInt32 v="9"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="14.3400002"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407476806"/><identifier v="Non_Uniform_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407481372"/><connections><connection><identifier v="Input"/><connRef v="1407475980"/><connRefOutput v="1407475981"/></connection></connections><GUILayout><gpos v="922.219177 -4080 0"/></GUILayout><compOutputs><compOutput><uid v="1407481373"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1449400845"/><parameters/><outputBridgings><outputBridging><uid v="1407481373"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407481392"/><connections><connection><identifier v="input"/><connRef v="1407283127"/><connRefOutput v="1407283128"/></connection></connections><GUILayout><gpos v="956.590759 -3920 0"/></GUILayout><compOutputs><compOutput><uid v="1355424961"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///hbao?dependency=1297162667"/><parameters><parameter><name v="height_depth"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.370000005"/></paramValue></parameter><parameter><name v="samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="16"/></paramValue></parameter><parameter><name v="surface_size"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="268.820007"/></paramValue></parameter><parameter><name v="height_depth_cm"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="93.1899948"/></paramValue></parameter><parameter><name v="radius"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.579999983"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1355424961"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407484542"/><connections><connection><identifier v="Source"/><connRef v="1407481392"/><connRefOutput v="1355424961"/></connection></connections><GUILayout><gpos v="1119.2782 -3920 0"/></GUILayout><compOutputs><compOutput><uid v="1407484543"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1299236171"/><parameters/><outputBridgings><outputBridging><uid v="1407484543"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407485700"/><connections><connection><identifier v="Source"/><connRef v="1407484542"/><connRefOutput v="1407484543"/></connection></connections><GUILayout><gpos v="1279.2782 -3920 0"/></GUILayout><compOutputs><compOutput><uid v="1407485701"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///invert_grayscale?dependency=1368115694"/><parameters/><outputBridgings><outputBridging><uid v="1407485701"/><identifier v="Invert_Grayscale"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407485715"/><connections><connection><identifier v="Source"/><connRef v="1407481372"/><connRefOutput v="1407481373"/></connection></connections><GUILayout><gpos v="1068.7821 -4080 0"/></GUILayout><compOutputs><compOutput><uid v="1407485716"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1299236171"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.02999997"/></paramValue></parameter><parameter><name v="Quality"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407485716"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407485768"/><connections><connection><identifier v="source"/><connRef v="1407485774"/><connRefOutput v="1407485775"/></connection><connection><identifier v="destination"/><connRef v="1407485845"/><connRefOutput v="1407485846"/></connection></connections><GUILayout><gpos v="1722.66663 -4080 0"/></GUILayout><compOutputs><compOutput><uid v="1407485769"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407485774"/><GUILayout><gpos v="1420.7821 -4272 0"/></GUILayout><compOutputs><compOutput><uid v="1407485775"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///perlin_noise?dependency=1407332030"/><parameters><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueInt32 v="7"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407485775"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407485813"/><connections><connection><identifier v="source"/><connRef v="1407458892"/><connRefOutput v="1407458893"/></connection><connection><identifier v="destination"/><connRef v="1407283127"/><connRefOutput v="1407283128"/></connection></connections><GUILayout><gpos v="3440 240 0"/></GUILayout><compOutputs><compOutput><uid v="1407485814"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="5"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407485845"/><connections><connection><identifier v="input1"/><connRef v="1407493827"/><connRefOutput v="1407493828"/></connection></connections><GUILayout><gpos v="1516.7821 -4080 0"/></GUILayout><compOutputs><compOutput><uid v="1407485846"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelouthigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.484709471 0.484709471 0.484709471 1"/></paramValue></parameter><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.316513747 0.316513747 0.316513747 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407486069"/><connections><connection><identifier v="height_bottom"/><connRef v="1407458892"/><connRefOutput v="1407458893"/></connection><connection><identifier v="height_top"/><connRef v="1407283127"/><connRefOutput v="1407283128"/></connection></connections><GUILayout><gpos v="5456 -1474.61426 0"/></GUILayout><compOutputs><compOutput><uid v="1407486070"/><comptype v="2"/></compOutput><compOutput><uid v="1407486071"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///height_blend?dependency=1449623608"/><parameters><parameter><name v="contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407486070"/><identifier v="blended_height"/></outputBridging><outputBridging><uid v="1407486071"/><identifier v="height_mask"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407490500"/><connections><connection><identifier v="source"/><connRef v="1407490514"/><connRefOutput v="1407490515"/></connection><connection><identifier v="opacity"/><connRef v="1407490571"/><connRefOutput v="1407490572"/></connection><connection><identifier v="destination"/><connRef v="1407502679"/><connRefOutput v="1407502680"/></connection></connections><GUILayout><gpos v="6042.6665 -1488 0"/></GUILayout><compOutputs><compOutput><uid v="1407490501"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1407490514"/><connections><connection><identifier v="input1"/><connRef v="1407486069"/><connRefOutput v="1407486071"/></connection></connections><GUILayout><gpos v="5964.6665 -1509.33337 -1"/><docked v="1"/><dockDistance v="103.514404 -32"/></GUILayout><compOutputs><compOutput><uid v="1407490515"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1407490596"/><paramsArrayCells><paramsArrayCell><uid v="1489719502"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.259493679"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="1 0.772229433 0.548192739 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1407490571"/><connections><connection><identifier v="Source"/><connRef v="1407486069"/><connRefOutput v="1407486071"/></connection></connections><GUILayout><gpos v="5964.6665 -1466.66663 0"/><docked v="1"/><dockDistance v="215.514648 16"/></GUILayout><compOutputs><compOutput><uid v="1407490572"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///invert_grayscale?dependency=1368115694"/><parameters/><outputBridgings><outputBridging><uid v="1407490572"/><identifier v="Invert_Grayscale"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407492497"/><connections><connection><identifier v="input1"/><connRef v="1407492521"/><connRefOutput v="1407476317"/></connection><connection><identifier v="inputintensity"/><connRef v="1407492567"/><connRefOutput v="1407476317"/></connection></connections><GUILayout><gpos v="396.782135 -4464 0"/></GUILayout><compOutputs><compOutput><uid v="1407492498"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="directionalwarp"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="50"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407492521"/><GUILayout><gpos v="108.782143 -4592 0"/></GUILayout><compOutputs><compOutput><uid v="1407476317"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///shape?dependency=1311189026"/><parameters><parameter><name v="Pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="Size"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.769999981"/></paramValue></parameter><parameter><name v="Size_xy"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.449999988 1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407476317"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407492567"/><GUILayout><gpos v="108.782143 -4464 0"/></GUILayout><compOutputs><compOutput><uid v="1407476317"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///shape?dependency=1311189026"/><parameters><parameter><name v="Pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="Size"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.769999981"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407476317"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407493827"/><connections><connection><identifier v="inputintensity"/><connRef v="1407485700"/><connRefOutput v="1407485701"/></connection><connection><identifier v="input1"/><connRef v="1407504137"/><connRefOutput v="1407504138"/></connection></connections><GUILayout><gpos v="1360 -4080 0"/></GUILayout><compOutputs><compOutput><uid v="1407493828"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="directionalwarp"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="2.87999988"/></paramValue></parameter><parameter><name v="warpangle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="-0.732749999"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407499432"/><connections><connection><identifier v="Input"/><connRef v="1407485768"/><connRefOutput v="1407485769"/></connection></connections><GUILayout><gpos v="1882.66663 -4080 0"/></GUILayout><compOutputs><compOutput><uid v="1407499433"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1449400845"/><parameters/><outputBridgings><outputBridging><uid v="1407499433"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407500337"/><connections><connection><identifier v="input1"/><connRef v="1407499432"/><connRefOutput v="1407499433"/></connection></connections><GUILayout><gpos v="2042.66663 -4080 0"/></GUILayout><compOutputs><compOutput><uid v="1407500338"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelouthigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.814984739 0.814984739 0.814984739 1"/></paramValue></parameter><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.548929691 0.548929691 0.548929691 0"/></paramValue></parameter><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.533639133 0.533639133 0.533639133 0.5"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407504107"/><connections><connection><identifier v="pattern_input"/><connRef v="1407476805"/><connRefOutput v="1407476806"/></connection></connections><GUILayout><gpos v="972.782166 -4592 0"/></GUILayout><compOutputs><compOutput><uid v="1407475981"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1449399972"/><parameters><parameter><name v="rotation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.5"/></paramValue></parameter><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="20"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="20"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="8.77999973"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.560000002"/></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="3.03999996"/></paramValue></parameter><parameter><name v="rotation_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0299999993"/></paramValue></parameter><parameter><name v="color_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.479999989"/></paramValue></parameter><parameter><name v="size"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.969999969 1"/></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.680000007"/></paramValue></parameter><parameter><name v="mask_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.340000004"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407475981"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407504137"/><connections><connection><identifier v="inputintensity"/><connRef v="1407507539"/><connRefOutput v="1407507540"/></connection><connection><identifier v="input1"/><connRef v="1407485715"/><connRefOutput v="1407485716"/></connection></connections><GUILayout><gpos v="1196.7821 -4080 0"/></GUILayout><compOutputs><compOutput><uid v="1407504138"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="directionalwarp"/><parameters><parameter><name v="warpangle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="-0.25"/></paramValue></parameter><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="5.4000001"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407507539"/><connections><connection><identifier v="Source"/><connRef v="1407504107"/><connRefOutput v="1407475981"/></connection></connections><GUILayout><gpos v="1168 -4592 0"/></GUILayout><compOutputs><compOutput><uid v="1407507540"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1299236171"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="2.63999987"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407507540"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407509390"/><connections><connection><identifier v="RGBA"/><connRef v="1407509405"/><connRefOutput v="1407509406"/></connection></connections><GUILayout><gpos v="1708.7821 -4517.3335 0"/></GUILayout><compOutputs><compOutput><uid v="1407509391"/><comptype v="2"/></compOutput><compOutput><uid v="1407509392"/><comptype v="2"/></compOutput><compOutput><uid v="1407509393"/><comptype v="2"/></compOutput><compOutput><uid v="1407509394"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///rgba_split?dependency=1147283954"/><parameters/><outputBridgings><outputBridging><uid v="1407509391"/><identifier v="R"/></outputBridging><outputBridging><uid v="1407509392"/><identifier v="G"/></outputBridging><outputBridging><uid v="1407509393"/><identifier v="B"/></outputBridging><outputBridging><uid v="1407509394"/><identifier v="A"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407509405"/><connections><connection><identifier v="input1"/><connRef v="1407283127"/><connRefOutput v="1407283128"/></connection></connections><GUILayout><gpos v="1780.11548 -2565.3335 -1"/><docked v="1"/><dockDistance v="544 -922.666687"/></GUILayout><compOutputs><compOutput><uid v="1407509406"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="normal"/><parameters><parameter><name v="input2alpha"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="inversedy"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="10"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407509432"/><connections><connection><identifier v="Input_1"/><connRef v="1407509390"/><connRefOutput v="1407509392"/></connection></connections><GUILayout><gpos v="1868.7821 -4528 0"/></GUILayout><compOutputs><compOutput><uid v="1407509433"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1449404462"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.389999986"/></paramValue></parameter><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.789999962"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407509433"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407509716"/><connections><connection><identifier v="Source"/><connRef v="1407509432"/><connRefOutput v="1407509433"/></connection></connections><GUILayout><gpos v="1996.7821 -4528 0"/></GUILayout><compOutputs><compOutput><uid v="1407509717"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1299236171"/><parameters/><outputBridgings><outputBridging><uid v="1407509717"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407509757"/><connections><connection><identifier v="destination"/><connRef v="1407500337"/><connRefOutput v="1407500338"/></connection><connection><identifier v="source"/><connRef v="1407509716"/><connRefOutput v="1407509717"/></connection></connections><GUILayout><gpos v="2220.78223 -4080 0"/></GUILayout><compOutputs><compOutput><uid v="1407509758"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407520221"/><connections><connection><identifier v="destination"/><connRef v="1407509757"/><connRefOutput v="1407509758"/></connection><connection><identifier v="source"/><connRef v="1407484542"/><connRefOutput v="1407484543"/></connection></connections><GUILayout><gpos v="2412.78223 -4080 0"/></GUILayout><compOutputs><compOutput><uid v="1407520222"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.479999989"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407309663"/><connections><connection><identifier v="Effect"/><connRef v="1407335953"/><connRefOutput v="1407335954"/></connection><connection><identifier v="Source"/><connRef v="1407246477"/><connRefOutput v="1407213688"/></connection></connections><GUILayout><gpos v="-3216 -752 0"/></GUILayout><compOutputs><compOutput><uid v="1407287588"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///slope_blur_grayscale_2?dependency=1449402040"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="32"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.100000001"/></paramValue></parameter><parameter><name v="mode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407287588"/><identifier v="Slope_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407312563"/><connections><connection><identifier v="Effect"/><connRef v="1407335953"/><connRefOutput v="1407335954"/></connection><connection><identifier v="Source"/><connRef v="1407220798"/><connRefOutput v="1407213688"/></connection></connections><GUILayout><gpos v="-3200.85913 16 0"/></GUILayout><compOutputs><compOutput><uid v="1407287588"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///slope_blur_grayscale_2?dependency=1449402040"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="32"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.100000001"/></paramValue></parameter><parameter><name v="mode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407287588"/><identifier v="Slope_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407325955"/><connections><connection><identifier v="Effect"/><connRef v="1407335953"/><connRefOutput v="1407335954"/></connection><connection><identifier v="Source"/><connRef v="1407292229"/><connRefOutput v="1407213688"/></connection></connections><GUILayout><gpos v="-3184 -2160 0"/></GUILayout><compOutputs><compOutput><uid v="1407287588"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///slope_blur_grayscale_2?dependency=1449402040"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="32"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.100000001"/></paramValue></parameter><parameter><name v="mode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407287588"/><identifier v="Slope_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407341920"/><connections><connection><identifier v="Effect"/><connRef v="1407335953"/><connRefOutput v="1407335954"/></connection><connection><identifier v="Source"/><connRef v="1407225787"/><connRefOutput v="1407213688"/></connection></connections><GUILayout><gpos v="-3216 -112 0"/></GUILayout><compOutputs><compOutput><uid v="1407287588"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///slope_blur_grayscale_2?dependency=1449402040"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="32"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.100000001"/></paramValue></parameter><parameter><name v="mode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407287588"/><identifier v="Slope_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407350529"/><connections><connection><identifier v="Source"/><connRef v="1407486069"/><connRefOutput v="1407486071"/></connection></connections><GUILayout><gpos v="3440 112 0"/></GUILayout><compOutputs><compOutput><uid v="1407350530"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///invert_grayscale?dependency=1368115694"/><parameters/><outputBridgings><outputBridging><uid v="1407350530"/><identifier v="Invert_Grayscale"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407350544"/><connections><connection><identifier v="Source"/><connRef v="1407350529"/><connRefOutput v="1407350530"/></connection><connection><identifier v="Effect"/><connRef v="1407350529"/><connRefOutput v="1407350530"/></connection></connections><GUILayout><gpos v="3888 -16 0"/></GUILayout><compOutputs><compOutput><uid v="1407350545"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///non_uniform_blur_grayscale?dependency=1290776959"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="16"/></paramValue></parameter><parameter><name v="Blades"/><relativeTo v="0"/><paramValue><constantValueInt32 v="9"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="2.32999992"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407350545"/><identifier v="Non_Uniform_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407355537"/><connections><connection><identifier v="source"/><connRef v="1407350544"/><connRefOutput v="1407350545"/></connection><connection><identifier v="destination"/><connRef v="1407442049"/><connRefOutput v="1407442050"/></connection></connections><GUILayout><gpos v="4016 240 0"/></GUILayout><compOutputs><compOutput><uid v="1407355538"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0799999982"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407368937"/><connections><connection><identifier v="Input"/><connRef v="1407495853"/><connRefOutput v="1407355538"/></connection></connections><GUILayout><gpos v="4336 240 0"/></GUILayout><compOutputs><compOutput><uid v="1407368938"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1449400845"/><parameters/><outputBridgings><outputBridging><uid v="1407368938"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407395350"/><connections><connection><identifier v="Effect"/><connRef v="1407464111"/><connRefOutput v="1407464112"/></connection><connection><identifier v="Source"/><connRef v="1407203524"/><connRefOutput v="1407203295"/></connection></connections><GUILayout><gpos v="-4592 -416.223572 0"/></GUILayout><compOutputs><compOutput><uid v="1407464090"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///slope_blur_grayscale_2?dependency=1449402040"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="32"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="mode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407464090"/><identifier v="Slope_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407437385"/><connections><connection><identifier v="Source"/><connRef v="1407350529"/><connRefOutput v="1407350530"/></connection><connection><identifier v="Effect"/><connRef v="1407350529"/><connRefOutput v="1407350530"/></connection></connections><GUILayout><gpos v="3696 112 19"/></GUILayout><compOutputs><compOutput><uid v="1407350545"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///non_uniform_blur_grayscale?dependency=1290776959"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="16"/></paramValue></parameter><parameter><name v="Blades"/><relativeTo v="0"/><paramValue><constantValueInt32 v="9"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="4.83999968"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407350545"/><identifier v="Non_Uniform_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407442049"/><connections><connection><identifier v="source"/><connRef v="1407437385"/><connRefOutput v="1407350545"/></connection><connection><identifier v="destination"/><connRef v="1407485813"/><connRefOutput v="1407485814"/></connection></connections><GUILayout><gpos v="3824 240 0"/></GUILayout><compOutputs><compOutput><uid v="1407442050"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.229999989"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407458892"/><connections><connection><identifier v="source"/><connRef v="1407468888"/><connRefOutput v="1407468889"/></connection><connection><identifier v="destination"/><connRef v="1407520221"/><connRefOutput v="1407520222"/></connection></connections><GUILayout><gpos v="2572.78223 -4080 0"/></GUILayout><compOutputs><compOutput><uid v="1407458893"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.00999999978"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407465563"/><GUILayout><gpos v="1324.7821 -3536 0"/></GUILayout><compOutputs><compOutput><uid v="1407465564"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1449399972"/><parameters><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="250"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="250"/></paramValue></parameter><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.75"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.22000003"/></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.5"/></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.269999981"/></paramValue></parameter><parameter><name v="color_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.789999962"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407465564"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407468888"/><connections><connection><identifier v="Source"/><connRef v="1407465563"/><connRefOutput v="1407465564"/></connection></connections><GUILayout><gpos v="1452.7821 -3536 0"/></GUILayout><compOutputs><compOutput><uid v="1407468889"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1299236171"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.460000008"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407468889"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407498822"/><connections><connection><identifier v="input"/><connRef v="1407212376"/><connRefOutput v="1407212377"/></connection></connections><GUILayout><gpos v="5481.88623 -1793.66919 0"/></GUILayout><compOutputs><compOutput><uid v="1407498823"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.25"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407498823"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407498865"/><connections><connection><identifier v="source"/><connRef v="1407498822"/><connRefOutput v="1407498823"/></connection><connection><identifier v="mask"/><connRef v="1407203524"/><connRefOutput v="1407203295"/></connection></connections><GUILayout><gpos v="5712 -1360 0"/></GUILayout><compOutputs><compOutput><uid v="1407498866"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters><parameter><name v="combinedistance"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407502679"/><connections><connection><identifier v="destination"/><connRef v="1407322631"/><connRefOutput v="1407322632"/></connection></connections><GUILayout><gpos v="5808 -1633.19922 0"/></GUILayout><compOutputs><compOutput><uid v="1407502680"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.209999993"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407502993"/><connections><connection><identifier v="input"/><connRef v="1407368937"/><connRefOutput v="1407368938"/></connection></connections><GUILayout><gpos v="5562.6665 -1200 0"/></GUILayout><compOutputs><compOutput><uid v="1355424961"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///hbao?dependency=1297162667"/><parameters><parameter><name v="height_depth"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.370000005"/></paramValue></parameter><parameter><name v="samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="16"/></paramValue></parameter><parameter><name v="surface_size"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="268.820007"/></paramValue></parameter><parameter><name v="height_depth_cm"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="93.1899948"/></paramValue></parameter><parameter><name v="radius"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.579999983"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1355424961"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407503912"/><connections><connection><identifier v="Source"/><connRef v="1407502993"/><connRefOutput v="1355424961"/></connection></connections><GUILayout><gpos v="5722.6665 -1200 0"/></GUILayout><compOutputs><compOutput><uid v="1407503913"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///highpass_grayscale?dependency=1449753752"/><parameters/><outputBridgings><outputBridging><uid v="1407503913"/><identifier v="Highpass"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407503926"/><connections><connection><identifier v="destination"/><connRef v="1407490500"/><connRefOutput v="1407490501"/></connection><connection><identifier v="source"/><connRef v="1407504046"/><connRefOutput v="1407504047"/></connection></connections><GUILayout><gpos v="6224 -1488 0"/></GUILayout><compOutputs><compOutput><uid v="1407503927"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.149999991"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407503976"/><connections><connection><identifier v="Input"/><connRef v="1407503912"/><connRefOutput v="1407503913"/></connection></connections><GUILayout><gpos v="5882.6665 -1200 0"/></GUILayout><compOutputs><compOutput><uid v="1407503977"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///quantize_grayscale?dependency=1407503933"/><parameters><parameter><name v="Quantize"/><relativeTo v="0"/><paramValue><constantValueInt32 v="7"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407503977"/><identifier v="Quantize"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407504046"/><connections><connection><identifier v="input1"/><connRef v="1407503976"/><connRefOutput v="1407503977"/></connection></connections><GUILayout><gpos v="6146 -1509.33337 0"/><docked v="1"/><dockDistance v="160 -160"/></GUILayout><compOutputs><compOutput><uid v="1407504047"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1407504222"/><connections><connection><identifier v="input1"/><connRef v="1407368937"/><connRefOutput v="1407368938"/></connection></connections><GUILayout><gpos v="5498.6665 -2000 0"/></GUILayout><compOutputs><compOutput><uid v="1355431358"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="normal"/><parameters><parameter><name v="input2alpha"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="inversedy"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="10"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407505789"/><connections><connection><identifier v="input"/><connRef v="1407504222"/><connRefOutput v="1355431358"/></connection></connections><GUILayout><gpos v="5626.6665 -2160 0"/></GUILayout><compOutputs><compOutput><uid v="1407505790"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///curvature_smooth?dependency=1449590280"/><parameters><parameter><name v="normal_format"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407505790"/><identifier v="height"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407505805"/><connections><connection><identifier v="Input_1"/><connRef v="1407505789"/><connRefOutput v="1407505790"/></connection></connections><GUILayout><gpos v="5786.6665 -2160 0"/></GUILayout><compOutputs><compOutput><uid v="1407505806"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1449404462"/><parameters><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.379999995"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407505806"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407506213"/><connections><connection><identifier v="destination"/><connRef v="1407503926"/><connRefOutput v="1407503927"/></connection><connection><identifier v="source"/><connRef v="1407511607"/><connRefOutput v="1407511608"/></connection></connections><GUILayout><gpos v="6334.56104 -1808 0"/></GUILayout><compOutputs><compOutput><uid v="1407506214"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0700000003"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407511607"/><connections><connection><identifier v="input1"/><connRef v="1407505805"/><connRefOutput v="1407505806"/></connection></connections><GUILayout><gpos v="6256.56104 -1829.33337 0"/><docked v="1"/><dockDistance v="384 352"/></GUILayout><compOutputs><compOutput><uid v="1407511608"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1407516236"/><connections><connection><identifier v="destination"/><connRef v="1407506213"/><connRefOutput v="1407506214"/></connection><connection><identifier v="source"/><connRef v="1407521608"/><connRefOutput v="1407521609"/></connection></connections><GUILayout><gpos v="6544 -1808 0"/></GUILayout><compOutputs><compOutput><uid v="1407516237"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0399999991"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407521608"/><GUILayout><gpos v="6466 -1829.33337 0"/><docked v="1"/><dockDistance v="320 352"/></GUILayout><compOutputs><compOutput><uid v="1407521609"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1407527225"/><connections><connection><identifier v="input1"/><connRef v="1407368937"/><connRefOutput v="1407368938"/></connection></connections><GUILayout><gpos v="7120 -346.666656 0"/></GUILayout><compOutputs><compOutput><uid v="1407527226"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.836391449 0.836391449 0.836391449 0"/></paramValue></parameter><parameter><name v="levelouthigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.897553504 0.897553504 0.897553504 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407527465"/><connections><connection><identifier v="destination"/><connRef v="1407532249"/><connRefOutput v="1407532250"/></connection></connections><GUILayout><gpos v="7472 -346.666656 0"/></GUILayout><compOutputs><compOutput><uid v="1407527466"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0899999961"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407527515"/><connections><connection><identifier v="destination"/><connRef v="1407527465"/><connRefOutput v="1407527466"/></connection><connection><identifier v="source"/><connRef v="1407527536"/><connRefOutput v="1407527537"/></connection></connections><GUILayout><gpos v="7664 -336 0"/></GUILayout><compOutputs><compOutput><uid v="1407527516"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.149999991"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407527536"/><GUILayout><gpos v="7586 -357.333344 0"/><docked v="1"/><dockDistance v="400 720"/></GUILayout><compOutputs><compOutput><uid v="1407527537"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelouthigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.726299703 0.726299703 0.726299703 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407527732"/><connections><connection><identifier v="destination"/><connRef v="1407527515"/><connRefOutput v="1407527516"/></connection><connection><identifier v="source"/><connRef v="1407503976"/><connRefOutput v="1407503977"/></connection></connections><GUILayout><gpos v="7856 -336 0"/></GUILayout><compOutputs><compOutput><uid v="1407527733"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.109999999"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407532249"/><connections><connection><identifier v="destination"/><connRef v="1407527225"/><connRefOutput v="1407527226"/></connection><connection><identifier v="source"/><connRef v="1407532268"/><connRefOutput v="1407532269"/></connection></connections><GUILayout><gpos v="7312 -346.666656 0"/></GUILayout><compOutputs><compOutput><uid v="1407532250"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.140000001"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407532268"/><connections><connection><identifier v="Source"/><connRef v="1407486069"/><connRefOutput v="1407486071"/></connection></connections><GUILayout><gpos v="5794 -133.333328 -1"/><docked v="1"/><dockDistance v="208 280.834564"/></GUILayout><compOutputs><compOutput><uid v="1407532269"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///invert_grayscale?dependency=1368115694"/><parameters/><outputBridgings><outputBridging><uid v="1407532269"/><identifier v="Invert_Grayscale"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407420471"/><connections><connection><identifier v="Input"/><connRef v="1407368937"/><connRefOutput v="1407368938"/></connection></connections><GUILayout><gpos v="5424 240 0"/></GUILayout><compOutputs><compOutput><uid v="1407420472"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1449400845"/><parameters/><outputBridgings><outputBridging><uid v="1407420472"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407427580"/><connections><connection><identifier v="input"/><connRef v="1407282167"/><connRefOutput v="1407213688"/></connection></connections><GUILayout><gpos v="-1456 528 0"/><dockDistance v="-270 -261.88208"/></GUILayout><compOutputs><compOutput><uid v="1407427581"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///edge_detect?dependency=1407202483"/><parameters><parameter><name v="edge_roundness"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.229999989"/></paramValue></parameter><parameter><name v="edge_width"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.26999998"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407427581"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407427627"/><connections><connection><identifier v="destination"/><connRef v="1407249395"/><connRefOutput v="1407214063"/></connection><connection><identifier v="source"/><connRef v="1407461299"/><connRefOutput v="1407461300"/></connection></connections><GUILayout><gpos v="-957.452881 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407427628"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407427649"/><connections><connection><identifier v="Source"/><connRef v="1407427580"/><connRefOutput v="1407427581"/></connection></connections><GUILayout><gpos v="-1328 912 0"/><docked v="1"/><dockDistance v="192 -64"/></GUILayout><compOutputs><compOutput><uid v="1407427650"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///invert_grayscale?dependency=1368115694"/><parameters/><outputBridgings><outputBridging><uid v="1407427650"/><identifier v="Invert_Grayscale"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407459602"/><connections><connection><identifier v="input"/><connRef v="1407427649"/><connRefOutput v="1407427650"/></connection></connections><GUILayout><gpos v="-1264 528 0"/></GUILayout><compOutputs><compOutput><uid v="1407459603"/><comptype v="2"/></compOutput><compOutput><uid v="1407459604"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///bevel?dependency=1449405586"/><parameters><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="-0.129999995"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407459603"/><identifier v="height"/></outputBridging><outputBridging><uid v="1407459604"/><identifier v="normal"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407461299"/><connections><connection><identifier v="Input"/><connRef v="1407459602"/><connRefOutput v="1407459603"/></connection></connections><GUILayout><gpos v="-1035.45288 282.666656 -1"/><docked v="1"/><dockDistance v="-96 -288"/></GUILayout><compOutputs><compOutput><uid v="1407461300"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1449400845"/><parameters/><outputBridgings><outputBridging><uid v="1407461300"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407494179"/><connections><connection><identifier v="Source"/><connRef v="1407350529"/><connRefOutput v="1407350530"/></connection><connection><identifier v="Effect"/><connRef v="1407350529"/><connRefOutput v="1407350530"/></connection></connections><GUILayout><gpos v="4080 -16 0"/></GUILayout><compOutputs><compOutput><uid v="1407350545"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///non_uniform_blur_grayscale?dependency=1290776959"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="16"/></paramValue></parameter><parameter><name v="Blades"/><relativeTo v="0"/><paramValue><constantValueInt32 v="9"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.25"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407350545"/><identifier v="Non_Uniform_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407495853"/><connections><connection><identifier v="destination"/><connRef v="1407355537"/><connRefOutput v="1407355538"/></connection><connection><identifier v="source"/><connRef v="1407494179"/><connRefOutput v="1407350545"/></connection></connections><GUILayout><gpos v="4176 240 0"/></GUILayout><compOutputs><compOutput><uid v="1407355538"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0199999996"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode></compNodes><baseParameters/><GUIObjects><GUIObject><type v="COMMENT"/><GUILayout><gpos v="9120 -586.666687 -100"/><size v="629.333374 1024"/></GUILayout><GUIName v=""/><uid v="1407198765"/><title v="Output"/><frameColor v="0.101960786 0.552941203 1 0.250980407"/><isTitleVisible v="1"/><isFrameVisible v="1"/></GUIObject><GUIObject><type v="COMMENT"/><GUILayout><gpos v="-6026.6665 2506.66675 -100"/><size v="3296 1472"/></GUILayout><GUIName v=""/><uid v="1407455352"/><title v="Details"/><frameColor v="0.101960786 0.552941203 1 0.250980407"/><isTitleVisible v="1"/><isFrameVisible v="1"/></GUIObject><GUIObject><type v="COMMENT"/><GUILayout><gpos v="-6208 -544 0"/><size v="1312 992"/></GUILayout><GUIName v=""/><uid v="1407455369"/><title v="Shapes"/><frameColor v="0.101960786 0.552941203 1 0.250980407"/><isTitleVisible v="1"/><isFrameVisible v="1"/></GUIObject><GUIObject><type v="COMMENT"/><GUILayout><gpos v="5386.6665 -2496 -100"/><size v="1248 1408"/></GUILayout><GUIName v=""/><uid v="1407527161"/><title v="Albedo"/><frameColor v="0.101960786 0.552941203 1 0.250980407"/><isTitleVisible v="1"/><isFrameVisible v="1"/></GUIObject><GUIObject><type v="COMMENT"/><GUILayout><gpos v="10.666667 -4736 -100"/><size v="2656 1376"/></GUILayout><GUIName v=""/><uid v="1407527188"/><title v="Cement"/><frameColor v="0.101960786 0.552941203 1 0.250980407"/><isTitleVisible v="1"/><isFrameVisible v="1"/></GUIObject><GUIObject><type v="COMMENT"/><GUILayout><gpos v="-4768 -2240 0"/><size v="7040 3424"/></GUILayout><GUIName v=""/><uid v="1407527198"/><title v="Details Shapes"/><frameColor v="0.101960786 0.552941203 1 0.250980407"/><isTitleVisible v="1"/><isFrameVisible v="1"/></GUIObject><GUIObject><type v="COMMENT"/><GUILayout><gpos v="7040 -426.666656 -100"/><size v="896 192"/></GUILayout><GUIName v=""/><uid v="1407532430"/><title v="Roughness"/><frameColor v="0.101960786 0.552941203 1 0.250980407"/><isTitleVisible v="1"/><isFrameVisible v="1"/></GUIObject></GUIObjects><options><option><name v="defaultParentSize"/><value v="11x11"/></option><option><name v="export/fromGraph/autoExport"/><value v="true"/></option><option><name v="export/fromGraph/destination"/><value v="D:/work/Annery/stylized material/stylized material/images/dirt with rock"/></option><option><name v="export/fromGraph/extension"/><value v="png"/></option><option><name v="export/fromGraph/outputs/AO"/><value v="true"/></option><option><name v="export/fromGraph/outputs/basecolor_1"/><value v="true"/></option><option><name v="export/fromGraph/outputs/height_1"/><value v="true"/></option><option><name v="export/fromGraph/outputs/metallic_1"/><value v="true"/></option><option><name v="export/fromGraph/outputs/normal_1"/><value v="true"/></option><option><name v="export/fromGraph/outputs/roughness_1"/><value v="true"/></option><option><name v="export/fromGraph/outputsColorspace/AO"/><value v="Raw"/></option><option><name v="export/fromGraph/outputsColorspace/basecolor_1"/><value v="sRGB"/></option><option><name v="export/fromGraph/outputsColorspace/height_1"/><value v="Raw"/></option><option><name v="export/fromGraph/outputsColorspace/metallic_1"/><value v="Raw"/></option><option><name v="export/fromGraph/outputsColorspace/normal_1"/><value v="Raw"/></option><option><name v="export/fromGraph/outputsColorspace/roughness_1"/><value v="Raw"/></option><option><name v="export/fromGraph/pattern"/><value v="$(graph)_$(identifier)"/></option></options><root><rootOutputs><rootOutput><output v="1407198767"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1407198769"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1407198771"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1407198773"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1407198775"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1407198777"/><format v="0"/><usertag v=""/></rootOutput></rootOutputs></root></graph></content></package>
