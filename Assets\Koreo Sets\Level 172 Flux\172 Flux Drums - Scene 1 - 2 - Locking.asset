%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -1223223985, guid: aa81bbec85a3f423193272a7c6154986, type: 3}
  m_Name: 172 Flux Drums - Scene 1 - 2 - Locking
  m_EditorClassIdentifier: 
  mEventID: Locking
  mEventList:
  - mStartSample: 0
    mEndSample: 0
  - mStartSample: 30767
    mEndSample: 30767
  - mStartSample: 61534
    mEndSample: 61534
  - mStartSample: 92302
    mEndSample: 92302
  - mStartSample: 123069
    mEndSample: 123069
  - mStartSample: 153836
    mEndSample: 153836
  - mStartSample: 184603
    mEndSample: 184603
  - mStartSample: 215371
    mEndSample: 215371
  - mStartSample: 246139
    mEndSample: 246139
  - mStartSample: 276906
    mEndSample: 276906
  - mStartSample: 307673
    mEndSample: 307673
  - mStartSample: 338441
    mEndSample: 338441
  - mStartSample: 369209
    mEndSample: 369209
  - mStartSample: 399976
    mEndSample: 399976
  - mStartSample: 430743
    mEndSample: 430743
  - mStartSample: 461511
    mEndSample: 461511
  _SerializedPayloadTypes: []
  _AssetPayloads: []
  _AssetPayloadIdxs: 
  _ColorPayloads: []
  _ColorPayloadIdxs: 
  _CurvePayloads: []
  _CurvePayloadIdxs: 
  _FloatPayloads: []
  _FloatPayloadIdxs: 
  _GradientPayloads: []
  _GradientPayloadIdxs: 
  _IntPayloads: []
  _IntPayloadIdxs: 
  _SpectrumPayloads: []
  _SpectrumPayloadIdxs: 
  _TextPayloads: []
  _TextPayloadIdxs: 
