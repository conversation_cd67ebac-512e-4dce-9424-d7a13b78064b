using UnityEngine;
using UnityEditor;
using UnityEngine.SceneManagement;
using UnityEditor.SceneManagement;
using System.Collections.Generic;
using System.Linq;

namespace Stylo.Cadance.Editor
{
    /// <summary>
    /// Tool to inventory all Koreographer components and usage in the project.
    /// Used during migration planning to identify all components that need conversion.
    /// </summary>
    public class KoreographerInventoryTool : EditorWindow
    {
        private Vector2 scrollPosition;
        private List<ComponentInfo> foundComponents = new List<ComponentInfo>();
        private bool scanComplete = false;

        [System.Serializable]
        public class ComponentInfo
        {
            public string sceneName;
            public string gameObjectName;
            public string componentType;
            public string gameObjectPath;
            public bool isActive;
        }

        [MenuItem("Stylo/Cadance/Migration/Inventory Koreographer Usage")]
        public static void ShowWindow()
        {
            var window = GetWindow<KoreographerInventoryTool>("Koreographer Inventory");
            window.minSize = new Vector2(600, 400);
        }

        private void OnGUI()
        {
            EditorGUILayout.LabelField("Koreographer Component Inventory", EditorStyles.boldLabel);
            EditorGUILayout.Space();

            if (GUILayout.Button("Scan All Scenes for Koreographer Components"))
            {
                ScanAllScenes();
            }

            EditorGUILayout.Space();

            if (scanComplete)
            {
                EditorGUILayout.LabelField($"Found {foundComponents.Count} Koreographer components:", EditorStyles.boldLabel);

                scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);

                foreach (var component in foundComponents)
                {
                    EditorGUILayout.BeginVertical("box");

                    EditorGUILayout.LabelField($"Scene: {component.sceneName}", EditorStyles.boldLabel);
                    EditorGUILayout.LabelField($"GameObject: {component.gameObjectName}");
                    EditorGUILayout.LabelField($"Component: {component.componentType}");
                    EditorGUILayout.LabelField($"Path: {component.gameObjectPath}");
                    EditorGUILayout.LabelField($"Active: {component.isActive}");

                    EditorGUILayout.EndVertical();
                    EditorGUILayout.Space();
                }

                EditorGUILayout.EndScrollView();

                EditorGUILayout.Space();

                if (GUILayout.Button("Export Results to Console"))
                {
                    ExportResultsToConsole();
                }
            }
        }

        private void ScanAllScenes()
        {
            foundComponents.Clear();

            // Get all scene paths
            string[] scenePaths = new string[]
            {
                "Assets/_Scenes/Levels/Ouroboros - Base.unity",
                "Assets/_Scenes/Levels/Ouroboros - Scene 1.unity"
            };

            foreach (string scenePath in scenePaths)
            {
                if (System.IO.File.Exists(scenePath))
                {
                    ScanScene(scenePath);
                }
                else
                {
                    Debug.LogWarning($"Scene not found: {scenePath}");
                }
            }

            scanComplete = true;
            Debug.Log($"Koreographer inventory complete. Found {foundComponents.Count} components.");
        }

        private void ScanScene(string scenePath)
        {
            // Save current scene
            string currentScenePath = SceneManager.GetActiveScene().path;

            try
            {
                // Open the target scene
                var scene = EditorSceneManager.OpenScene(scenePath, OpenSceneMode.Single);

                // Find all GameObjects in the scene
                GameObject[] allObjects = FindObjectsOfType<GameObject>();

                foreach (GameObject go in allObjects)
                {
                    // Get all components
                    Component[] components = go.GetComponents<Component>();

                    foreach (Component comp in components)
                    {
                        if (comp == null) continue;

                        string typeName = comp.GetType().FullName;

                        // Check if it's a Koreographer component
                        if (IsKoreographerComponent(typeName))
                        {
                            var info = new ComponentInfo
                            {
                                sceneName = scene.name,
                                gameObjectName = go.name,
                                componentType = typeName,
                                gameObjectPath = GetGameObjectPath(go),
                                isActive = go.activeInHierarchy
                            };

                            foundComponents.Add(info);
                        }
                    }
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Error scanning scene {scenePath}: {e.Message}");
            }
            finally
            {
                // Restore original scene if it was different
                if (!string.IsNullOrEmpty(currentScenePath) && currentScenePath != scenePath)
                {
                    EditorSceneManager.OpenScene(currentScenePath, OpenSceneMode.Single);
                }
            }
        }

        private bool IsKoreographerComponent(string typeName)
        {
            return typeName.StartsWith("SonicBloom.Koreo") ||
                   typeName.Contains("Koreographer") ||
                   typeName.Contains("SimpleMusicPlayer") ||
                   typeName.Contains("AudioSourceVisor") ||
                   typeName.Contains("KoreographedEventEmitter") ||
                   typeName.Contains("KoreoVFXTrigger");
        }

        private string GetGameObjectPath(GameObject go)
        {
            string path = go.name;
            Transform parent = go.transform.parent;

            while (parent != null)
            {
                path = parent.name + "/" + path;
                parent = parent.parent;
            }

            return path;
        }

        private void ExportResultsToConsole()
        {
            Debug.Log("=== KOREOGRAPHER COMPONENT INVENTORY ===");

            var groupedByScene = foundComponents.GroupBy(c => c.sceneName);

            foreach (var sceneGroup in groupedByScene)
            {
                Debug.Log($"\n--- SCENE: {sceneGroup.Key} ---");

                var groupedByType = sceneGroup.GroupBy(c => c.componentType);

                foreach (var typeGroup in groupedByType)
                {
                    Debug.Log($"\n{typeGroup.Key}:");

                    foreach (var component in typeGroup)
                    {
                        Debug.Log($"  - {component.gameObjectPath} (Active: {component.isActive})");
                    }
                }
            }

            Debug.Log($"\n=== TOTAL: {foundComponents.Count} COMPONENTS FOUND ===");
        }
    }
}
