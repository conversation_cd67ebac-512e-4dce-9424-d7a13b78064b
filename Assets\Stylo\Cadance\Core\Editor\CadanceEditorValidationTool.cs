using UnityEngine;
using UnityEditor;
using UnityEditor.Callbacks;
using System.Collections.Generic;
using System.Linq;

namespace Stylo.Cadance.Editor
{
    /// <summary>
    /// Validation tool to test and verify that the Cadance Editor works correctly.
    /// Tests asset opening, display, editing capabilities, and feature parity with <PERSON><PERSON><PERSON>.
    /// </summary>
    public class CadanceEditorValidationTool : EditorWindow
    {
        private Vector2 scrollPosition;
        private List<ValidationResult> validationResults = new List<ValidationResult>();
        private bool validationComplete = false;
        private CadanceAsset testAsset;
        private AudioClip testAudioClip;

        [MenuItem("Stylo/Cadance/Testing/Editor Validation Tool")]
        public static void ShowWindow()
        {
            var window = GetWindow<CadanceEditorValidationTool>("Cadance Editor Validation");
            window.minSize = new Vector2(600, 400);
            window.Show();
        }

        private void OnGUI()
        {
            EditorGUILayout.LabelField("Cadance Editor Validation Tool", EditorStyles.boldLabel);
            EditorGUILayout.Space();

            EditorGUILayout.HelpBox(
                "This tool validates that the Cadance Editor functions correctly and has feature parity with <PERSON><PERSON><PERSON>'s editor.",
                MessageType.Info);

            EditorGUILayout.Space();

            // Test configuration
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("Test Configuration", EditorStyles.boldLabel);

            testAsset = (CadanceAsset)EditorGUILayout.ObjectField("Test CadanceAsset", testAsset, typeof(CadanceAsset), false);
            testAudioClip = (AudioClip)EditorGUILayout.ObjectField("Test AudioClip", testAudioClip, typeof(AudioClip), false);

            EditorGUILayout.Space();

            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("Run Full Validation", GUILayout.Height(30)))
            {
                RunFullValidation();
            }

            if (GUILayout.Button("Test Asset Opening", GUILayout.Height(30)))
            {
                TestAssetOpening();
            }

            if (GUILayout.Button("Test Editor Features", GUILayout.Height(30)))
            {
                TestEditorFeatures();
            }
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.EndVertical();

            // Results
            if (validationComplete)
            {
                DrawValidationResults();
            }
        }

        private void RunFullValidation()
        {
            validationResults.Clear();
            validationComplete = false;

            Debug.Log("[Cadance Editor Validation] Starting full validation...");

            // Test 1: OnOpenAsset Handler
            TestOnOpenAssetHandler();

            // Test 2: Asset Opening Mechanism
            TestAssetOpening();

            // Test 3: Editor Window Initialization
            TestEditorWindowInitialization();

            // Test 4: UI Components
            TestUIComponents();

            // Test 5: Timeline Features
            TestTimelineFeatures();

            // Test 6: Track Management
            TestTrackManagement();

            // Test 7: Event Handling
            TestEventHandling();

            // Test 8: Audio Integration
            TestAudioIntegration();

            // Test 9: Save/Load Functionality
            TestSaveLoadFunctionality();

            validationComplete = true;

            int passCount = validationResults.Count(r => r.passed);
            int totalCount = validationResults.Count;

            Debug.Log($"[Cadance Editor Validation] Validation complete: {passCount}/{totalCount} tests passed");

            if (passCount == totalCount)
            {
                Debug.Log("[Cadance Editor Validation] ✅ All tests passed! Cadance Editor is functioning correctly.");
            }
            else
            {
                Debug.LogWarning($"[Cadance Editor Validation] ⚠️ {totalCount - passCount} test(s) failed. See validation results for details.");
            }
        }

        private void TestOnOpenAssetHandler()
        {
            var result = new ValidationResult
            {
                testName = "OnOpenAsset Handler",
                description = "Verify that [OnOpenAsset] attribute is properly implemented"
            };

            try
            {
                // Check if the OnOpenAsset method exists
                var method = typeof(CadanceEditorWindow).GetMethod("OnOpenAsset",
                    System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Static);

                if (method != null)
                {
                    // Check if it has the OnOpenAsset attribute
                    var attributes = method.GetCustomAttributes(typeof(OnOpenAssetAttribute), false);
                    if (attributes.Length > 0)
                    {
                        result.passed = true;
                        result.message = "OnOpenAsset handler found and properly attributed";
                    }
                    else
                    {
                        result.passed = false;
                        result.message = "OnOpenAsset method exists but missing [OnOpenAsset] attribute";
                    }
                }
                else
                {
                    result.passed = false;
                    result.message = "OnOpenAsset method not found in CadanceEditorWindow";
                }
            }
            catch (System.Exception ex)
            {
                result.passed = false;
                result.message = $"Error testing OnOpenAsset handler: {ex.Message}";
            }

            validationResults.Add(result);
        }

        private void TestAssetOpening()
        {
            var result = new ValidationResult
            {
                testName = "Asset Opening",
                description = "Test opening CadanceAsset in the editor"
            };

            try
            {
                if (testAsset != null)
                {
                    // Try to open the asset
                    CadanceEditorWindow.OpenCadance(testAsset);

                    // Check if a window was opened
                    var windows = Resources.FindObjectsOfTypeAll<CadanceEditorWindow>();
                    if (windows.Length > 0)
                    {
                        result.passed = true;
                        result.message = "CadanceAsset opened successfully in editor window";
                    }
                    else
                    {
                        result.passed = false;
                        result.message = "Failed to open CadanceEditorWindow";
                    }
                }
                else
                {
                    result.passed = false;
                    result.message = "No test CadanceAsset provided";
                }
            }
            catch (System.Exception ex)
            {
                result.passed = false;
                result.message = $"Error opening asset: {ex.Message}";
            }

            validationResults.Add(result);
        }

        private void TestEditorWindowInitialization()
        {
            var result = new ValidationResult
            {
                testName = "Editor Window Initialization",
                description = "Verify editor window initializes correctly"
            };

            try
            {
                var window = EditorWindow.GetWindow<CadanceEditorWindow>("Test Window");

                if (window != null)
                {
                    result.passed = true;
                    result.message = "CadanceEditorWindow initialized successfully";
                    window.Close();
                }
                else
                {
                    result.passed = false;
                    result.message = "Failed to initialize CadanceEditorWindow";
                }
            }
            catch (System.Exception ex)
            {
                result.passed = false;
                result.message = $"Error initializing window: {ex.Message}";
            }

            validationResults.Add(result);
        }

        private void TestUIComponents()
        {
            var result = new ValidationResult
            {
                testName = "UI Components",
                description = "Check for essential UI components and methods"
            };

            try
            {
                var windowType = typeof(CadanceEditorWindow);
                var methods = windowType.GetMethods(System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                var requiredMethods = new string[]
                {
                    "DrawToolbar", "DrawCadanceSelection", "DrawTimeline", "DrawTracks", "DrawInspector"
                };

                var missingMethods = new List<string>();
                foreach (var methodName in requiredMethods)
                {
                    if (!methods.Any(m => m.Name == methodName))
                    {
                        missingMethods.Add(methodName);
                    }
                }

                if (missingMethods.Count == 0)
                {
                    result.passed = true;
                    result.message = "All essential UI methods found";
                }
                else
                {
                    result.passed = false;
                    result.message = $"Missing UI methods: {string.Join(", ", missingMethods)}";
                }
            }
            catch (System.Exception ex)
            {
                result.passed = false;
                result.message = $"Error checking UI components: {ex.Message}";
            }

            validationResults.Add(result);
        }

        private void TestTimelineFeatures()
        {
            var result = new ValidationResult
            {
                testName = "Timeline Features",
                description = "Verify timeline rendering and interaction methods"
            };

            try
            {
                var windowType = typeof(CadanceEditorWindow);
                var methods = windowType.GetMethods(System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                var timelineMethods = new string[]
                {
                    "DrawTimelineRuler", "DrawWaveform", "DrawGridLines", "DrawPlaybackPosition", "HandleTimelineInput"
                };

                var foundMethods = timelineMethods.Where(methodName =>
                    methods.Any(m => m.Name == methodName)).ToList();

                if (foundMethods.Count == timelineMethods.Length)
                {
                    result.passed = true;
                    result.message = "All timeline features implemented";
                }
                else
                {
                    result.passed = false;
                    result.message = $"Found {foundMethods.Count}/{timelineMethods.Length} timeline methods";
                }
            }
            catch (System.Exception ex)
            {
                result.passed = false;
                result.message = $"Error checking timeline features: {ex.Message}";
            }

            validationResults.Add(result);
        }

        private void TestTrackManagement()
        {
            var result = new ValidationResult
            {
                testName = "Track Management",
                description = "Verify track creation, editing, and management features"
            };

            try
            {
                var windowType = typeof(CadanceEditorWindow);
                var methods = windowType.GetMethods(System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                var trackMethods = new string[]
                {
                    "DrawTrack", "DrawTrackTimeline", "AddNewTrack", "RemoveTrack", "MoveTrack"
                };

                var foundMethods = trackMethods.Where(methodName =>
                    methods.Any(m => m.Name == methodName)).ToList();

                if (foundMethods.Count == trackMethods.Length)
                {
                    result.passed = true;
                    result.message = "All track management features implemented";
                }
                else
                {
                    result.passed = false;
                    result.message = $"Found {foundMethods.Count}/{trackMethods.Length} track management methods";
                }
            }
            catch (System.Exception ex)
            {
                result.passed = false;
                result.message = $"Error checking track management: {ex.Message}";
            }

            validationResults.Add(result);
        }

        private void TestEventHandling()
        {
            var result = new ValidationResult
            {
                testName = "Event Handling",
                description = "Verify event creation, editing, and manipulation features"
            };

            try
            {
                var windowType = typeof(CadanceEditorWindow);
                var methods = windowType.GetMethods(System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                var eventMethods = new string[]
                {
                    "DrawEvent", "GetEventAtPosition", "CreateEventAtPosition", "DragEvent", "DeleteSelectedEvent"
                };

                var foundMethods = eventMethods.Where(methodName =>
                    methods.Any(m => m.Name == methodName)).ToList();

                if (foundMethods.Count == eventMethods.Length)
                {
                    result.passed = true;
                    result.message = "All event handling features implemented";
                }
                else
                {
                    result.passed = false;
                    result.message = $"Found {foundMethods.Count}/{eventMethods.Length} event handling methods";
                }
            }
            catch (System.Exception ex)
            {
                result.passed = false;
                result.message = $"Error checking event handling: {ex.Message}";
            }

            validationResults.Add(result);
        }

        private void TestAudioIntegration()
        {
            var result = new ValidationResult
            {
                testName = "Audio Integration",
                description = "Verify audio playback and waveform generation"
            };

            try
            {
                var windowType = typeof(CadanceEditorWindow);
                var methods = windowType.GetMethods(System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                var audioMethods = new string[]
                {
                    "StartPlayback", "StopPlayback", "UpdateWaveformData", "HandleAudioScrubbing"
                };

                var foundMethods = audioMethods.Where(methodName =>
                    methods.Any(m => m.Name == methodName)).ToList();

                if (foundMethods.Count == audioMethods.Length)
                {
                    result.passed = true;
                    result.message = "All audio integration features implemented";
                }
                else
                {
                    result.passed = false;
                    result.message = $"Found {foundMethods.Count}/{audioMethods.Length} audio methods";
                }
            }
            catch (System.Exception ex)
            {
                result.passed = false;
                result.message = $"Error checking audio integration: {ex.Message}";
            }

            validationResults.Add(result);
        }

        private void TestSaveLoadFunctionality()
        {
            var result = new ValidationResult
            {
                testName = "Save/Load Functionality",
                description = "Verify asset save/load and undo/redo capabilities"
            };

            try
            {
                var windowType = typeof(CadanceEditorWindow);
                var methods = windowType.GetMethods(System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                var saveLoadMethods = new string[]
                {
                    "SaveCadance", "LoadCadance", "CreateNewCadance", "RecordUndo", "PerformUndo", "PerformRedo"
                };

                var foundMethods = saveLoadMethods.Where(methodName =>
                    methods.Any(m => m.Name == methodName)).ToList();

                if (foundMethods.Count == saveLoadMethods.Length)
                {
                    result.passed = true;
                    result.message = "All save/load features implemented";
                }
                else
                {
                    result.passed = false;
                    result.message = $"Found {foundMethods.Count}/{saveLoadMethods.Length} save/load methods";
                }
            }
            catch (System.Exception ex)
            {
                result.passed = false;
                result.message = $"Error checking save/load functionality: {ex.Message}";
            }

            validationResults.Add(result);
        }

        /// <summary>
        /// Tests specific editor features like payload editing, track type selection, etc.
        /// </summary>
        private void TestEditorFeatures()
        {
            validationResults.Clear();
            validationComplete = false;

            Debug.Log("[Cadance Editor Validation] Testing specific editor features...");

            // Test payload editor
            TestPayloadEditor();

            // Test track type selector
            TestTrackTypeSelector();

            // Test audio validator
            TestAudioValidator();

            // Test property drawers
            TestPropertyDrawers();

            validationComplete = true;

            int passCount = validationResults.Count(r => r.passed);
            int totalCount = validationResults.Count;

            Debug.Log($"[Cadance Editor Validation] Feature testing complete: {passCount}/{totalCount} tests passed");
        }

        private void TestPayloadEditor()
        {
            var result = new ValidationResult
            {
                testName = "Payload Editor",
                description = "Test CadancePayloadEditor functionality"
            };

            try
            {
                var payloadEditorType = typeof(CadancePayloadEditor);
                var drawMethod = payloadEditorType.GetMethod("DrawPayloadEditor",
                    System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Static);

                if (drawMethod != null)
                {
                    result.passed = true;
                    result.message = "CadancePayloadEditor.DrawPayloadEditor method found";
                }
                else
                {
                    result.passed = false;
                    result.message = "CadancePayloadEditor.DrawPayloadEditor method not found";
                }
            }
            catch (System.Exception ex)
            {
                result.passed = false;
                result.message = $"Error testing payload editor: {ex.Message}";
            }

            validationResults.Add(result);
        }

        private void TestTrackTypeSelector()
        {
            var result = new ValidationResult
            {
                testName = "Track Type Selector",
                description = "Test CadanceTrackTypeSelector functionality"
            };

            try
            {
                var selectorType = typeof(CadanceTrackTypeSelector);
                var showMenuMethod = selectorType.GetMethod("ShowTrackTypeMenu",
                    System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Static);

                if (showMenuMethod != null)
                {
                    result.passed = true;
                    result.message = "CadanceTrackTypeSelector.ShowTrackTypeMenu method found";
                }
                else
                {
                    result.passed = false;
                    result.message = "CadanceTrackTypeSelector.ShowTrackTypeMenu method not found";
                }
            }
            catch (System.Exception ex)
            {
                result.passed = false;
                result.message = $"Error testing track type selector: {ex.Message}";
            }

            validationResults.Add(result);
        }

        private void TestAudioValidator()
        {
            var result = new ValidationResult
            {
                testName = "Audio Validator",
                description = "Test CadanceAudioValidator functionality"
            };

            try
            {
                var validatorType = typeof(CadanceAudioValidator);
                var validateMethod = validatorType.GetMethod("IsAudioClipValid",
                    System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Static);

                if (validateMethod != null)
                {
                    result.passed = true;
                    result.message = "CadanceAudioValidator.IsAudioClipValid method found";
                }
                else
                {
                    result.passed = false;
                    result.message = "CadanceAudioValidator.IsAudioClipValid method not found";
                }
            }
            catch (System.Exception ex)
            {
                result.passed = false;
                result.message = $"Error testing audio validator: {ex.Message}";
            }

            validationResults.Add(result);
        }

        private void TestPropertyDrawers()
        {
            var result = new ValidationResult
            {
                testName = "Property Drawers",
                description = "Test custom property drawers"
            };

            try
            {
                var assetDrawerType = typeof(CadanceAssetPropertyDrawer);
                var trackDrawerType = typeof(CadanceTrackPropertyDrawer);

                if (assetDrawerType != null && trackDrawerType != null)
                {
                    result.passed = true;
                    result.message = "Custom property drawers found";
                }
                else
                {
                    result.passed = false;
                    result.message = "Custom property drawers not found";
                }
            }
            catch (System.Exception ex)
            {
                result.passed = false;
                result.message = $"Error testing property drawers: {ex.Message}";
            }

            validationResults.Add(result);
        }

        private void DrawValidationResults()
        {
            EditorGUILayout.Space();
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("Validation Results", EditorStyles.boldLabel);

            int passCount = validationResults.Count(r => r.passed);
            int totalCount = validationResults.Count;

            EditorGUILayout.LabelField($"Overall: {passCount}/{totalCount} tests passed", EditorStyles.boldLabel);

            EditorGUILayout.Space();

            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition, GUILayout.Height(300));

            foreach (var result in validationResults)
            {
                EditorGUILayout.BeginVertical("box");

                var style = result.passed ? EditorStyles.boldLabel : EditorStyles.label;
                var icon = result.passed ? "✅" : "❌";

                EditorGUILayout.LabelField($"{icon} {result.testName}", style);
                EditorGUILayout.LabelField(result.description, EditorStyles.miniLabel);

                if (!string.IsNullOrEmpty(result.message))
                {
                    var messageType = result.passed ? MessageType.Info : MessageType.Warning;
                    EditorGUILayout.HelpBox(result.message, messageType);
                }

                EditorGUILayout.EndVertical();
                EditorGUILayout.Space();
            }

            EditorGUILayout.EndScrollView();
            EditorGUILayout.EndVertical();
        }

        private class ValidationResult
        {
            public string testName;
            public string description;
            public bool passed;
            public string message;
        }
    }
}
