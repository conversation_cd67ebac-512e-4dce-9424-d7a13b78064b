/* Foldout */
.mm-foldout {
    background-color: rgba(0, 0, 0, 0.12);
    margin-top: 5px;
    margin-bottom: 5px;
    border-left-color: black;
    border-left-width: 3px;
    padding-right: 10px;
    padding-left: 0;
    padding-bottom: 0;
    padding-top: 0;
}

/* Foldout header bar */
.mm-foldout Toggle.mm-foldout-toggle {
    background-color: rgba(0, 0, 0, 0.12);
    padding-top: 5px;
    padding-left: 10px;
    margin-left: 0;
    padding-bottom: 5px;
    margin-bottom: 0;
    -unity-font-style: bold;
}

/* Foldout contents */
.mm-foldout #unity-content {
    padding-top: 5px;
    margin-top: 0;
    margin-left: 10px;
    padding-left: 10px;
    padding-bottom: 10px;
}

/* [Header] attributes */
.mm-foldout .unity-header-drawer__label {
    margin-top: 10px;
    padding-bottom: 5px;
    padding-top: 5px;
    margin-left: -20px;
    padding-left: 20px;
    -unity-font-style: bold;
    background-color: rgba(255, 255, 255, 0.04);
}

/* Button toolbars */
.mm-toolbar {
    margin-top: 5px;
    border-width: 0px;
    
}

.mm-toolbar ToolbarButton {
    border-width: 1px;
    -unity-text-align: middle-center;
}

.mm-toolbar ToolbarButton.main-call-to-action {
    background-color: rgba(30, 94, 88, 1);
    color: white;
    -unity-text-align: middle-center;
}

.mm-fixed-width-floatfield FloatInput TextElement
{
    width: 50px;
}

.mm-fixed-width-floatfield Label
{
    min-width: 50px;
}

    /* Spring */

.mm-spring-debug-track-background
{
    margin-top: 5px;
    background-color: rgba(0, 0, 0, 0.5);
    height: 20px;
    width: 100%;
    flex-direction: row;
    justify-content: flex-start;
}

.mm-spring-debug-track-foreground
{
    background-color: rgba(255, 196, 0, 1);
    height: 20px;
    width: 10px;
    margin-top: -20px;    
}

.mm-spring-debug-track-foreground-container-left
{
    background-color: green;
    height: 20px;
    width: 50%;
    left: 0px;
    margin-top: -20px;
    display: flex;
    justify-content: flex-end;
}

.mm-spring-debug-track-foreground-container-right
{
    background-color: blue;
    height: 20px;
    width: 50%;
    left: 50%;
    margin-top: -20px;
    display: flex;
    justify-content: flex-end;
}