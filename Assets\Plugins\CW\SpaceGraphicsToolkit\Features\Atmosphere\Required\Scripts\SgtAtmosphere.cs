using UnityEngine;
using CW.Common;
using SpaceGraphicsToolkit.LightAndShadow;

namespace SpaceGraphicsToolkit.Atmosphere
{
	/// <summary>This component allows you to draw a volumetric atmosphere. The atmosphere is rendered using two materials, one for the surface (inner), and one for the sky (outer).
	/// The outer part of the atmosphere is automatically generated by this component using the OuterMesh you specify.
	/// The inner part of the atmosphere is provided by you (e.g. a normal sphere GameObject), and is specified in the SgtSharedMaterial component that this component automatically adds.</summary>
	[ExecuteInEditMode]
	[RequireComponent(typeof(SgtSharedMaterial))]
	[HelpURL(SgtCommon.HelpUrlPrefix + "SgtAtmosphere")]
	[AddComponentMenu(SgtCommon.ComponentMenuPrefix + "Atmosphere")]
	public class SgtAtmosphere : MonoBehaviour, CwChild.IHasChildren
	{
		/// <summary>The material used to render this component.
		/// NOTE: This material must use the <b>Space Graphics Toolkit/Atmosphere</b> shader. You cannot use a normal shader.</summary>
		public Material SourceMaterial { set { if (sourceMaterial != value) { sourceMaterial = value; } } get { return sourceMaterial; } } [SerializeField] private Material sourceMaterial;

		/// <summary>This allows you to set the overall color of the atmosphere.</summary>
		public Color Color { set { color = value; } get { return color; } } [SerializeField] private Color color = Color.white;

		/// <summary>The <b>Color.rgb</b> values will be multiplied by this.</summary>
		public float Brightness { set { brightness = value; } get { return brightness; } } [SerializeField] private float brightness = 1.0f;

		/// <summary>The radius of the meshes set in the SgtSharedMaterial.</summary>
		public float InnerMeshRadius { set { innerMeshRadius = value; } get { return innerMeshRadius; } } [SerializeField] private float innerMeshRadius;

		/// <summary>This allows you to set the mesh used to render the atmosphere. This should be a sphere.</summary>
		public Mesh OuterMesh { set { outerMesh = value; } get { return outerMesh; } } [SerializeField] private Mesh outerMesh;

		/// <summary>This allows you to set the radius of the OuterMesh. If this is incorrectly set then the atmosphere will render incorrectly.</summary>
		public float OuterMeshRadius { set { outerMeshRadius = value; } get { return outerMeshRadius; } } [SerializeField] private float outerMeshRadius = 1.0f;

		/// <summary>If you have a big object that is both inside and outside of the atmosphere, it can cause a sharp intersection line. Increasing this setting allows you to change the smoothness of this intersection.</summary>
		public float OuterSoftness { set { outerSoftness = value; } get { return outerSoftness; } } [Range(0.0f, 1000.0f)] [SerializeField] private float outerSoftness;

		/// <summary>This allows you to set how high the atmosphere extends above the surface of the planet in local space.</summary>
		public float Height { set { height = value; } get { return height; } } [SerializeField] private float height = 0.1f;

		/// <summary>This allows you to adjust the fog level of the atmosphere on the surface.</summary>
		public float InnerFog { set { innerFog = value; } get { return innerFog; } } [SerializeField] private float innerFog;

		/// <summary>This allows you to adjust the fog level of the atmosphere in the sky.</summary>
		public float OuterFog { set { outerFog = value; } get { return outerFog; } } [SerializeField] private float outerFog;

		/// <summary>This allows you to control how quickly the sky reaches maximum opacity as the camera enters the atmosphere.
		/// 1 = You must go down to ground level before the opacity reaches max.
		/// 2 = You must go at least half way down to the surface.</summary>
		public float Sky { set { sky = value; } get { return sky; } } [SerializeField] private float sky = 1.0f;

		/// <summary>This allows you to set the altitude where atmospheric density reaches its maximum point. The lower you set this, the foggier the horizon will appear when approaching the surface.</summary>
		public float Middle { set { middle = value; } get { return middle; } } [Range(0.0f, 1.0f)] [SerializeField] private float middle = 0.5f;

		/// <summary>This allows you to offset the camera distance in world space when rendering the atmosphere, giving you fine control over the render order.</summary>
		public float CameraOffset { set { cameraOffset = value; } get { return cameraOffset; } } [SerializeField] private float cameraOffset;

		/// <summary>This setting allows you to increase or decrease how much this atmosphere occludes flares using the <b>SgtOcclusionScaler</b> component.</summary>
		public float OcclusionPower { set { occlusionPower = value; } get { return occlusionPower; } } [SerializeField] private float occlusionPower = 1.0f;

		/// <summary>Should the night side of the atmosphere have different sky values?</summary>
		public bool Night { set { night = value; } get { return night; } } [SerializeField] private bool night;

		/// <summary>The 'Sky' value of the night side.</summary>
		public float NightSky { set { nightSky = value; } get { return nightSky; } } [SerializeField] private float nightSky = 0.25f;

		/// <summary>The transition style between the day and night.</summary>
		public SgtEase.Type NightEase { set { nightEase = value; } get { return nightEase; } } [SerializeField] private SgtEase.Type nightEase;

		/// <summary>The start point of the day/sunset transition (0 = dark side, 1 = light side).</summary>
		public float NightStart { set { nightStart = value; } get { return nightStart; } } [Range(0.0f, 1.0f)] [SerializeField] private float nightStart = 0.4f;

		/// <summary>The end point of the day/sunset transition (0 = dark side, 1 = light side).</summary>
		public float NightEnd { set { nightEnd = value; } get { return nightEnd; } } [Range(0.0f, 1.0f)] [SerializeField] private float nightEnd = 0.6f;

		/// <summary>The power of the night transition.</summary>
		public float NightPower { set { nightPower = value; } get { return nightPower; } } [SerializeField] private float nightPower = 2.0f;

		public event System.Action<Material, Material> OnSetProperties;

		[SerializeField]
		private SgtAtmosphereModel model;

		[System.NonSerialized]
		protected Material innerMaterial;

		[System.NonSerialized]
		protected Material outerMaterial;

		[System.NonSerialized]
		private Transform cachedTransform;

		[System.NonSerialized]
		private bool cachedTransformSet;

		[System.NonSerialized]
		private SgtSharedMaterial cachedSharedMaterial;

		[System.NonSerialized]
		private bool cachedSharedMaterialSet;

		private static int _SGT_Color               = Shader.PropertyToID("_SGT_Color");
		private static int _SGT_Brightness          = Shader.PropertyToID("_SGT_Brightness");
		private static int _SGT_Cull                = Shader.PropertyToID("_SGT_Cull");
		private static int _SGT_InnerRatio          = Shader.PropertyToID("_SGT_InnerRatio");
		private static int _SGT_InnerScale          = Shader.PropertyToID("_SGT_InnerScale");
		private static int _SGT_SoftParticlesFactor = Shader.PropertyToID("_SGT_SoftParticlesFactor");
		private static int _SGT_WorldToLocal        = Shader.PropertyToID("_SGT_WorldToLocal");
		private static int _SGT_HorizonLengthRecip  = Shader.PropertyToID("_SGT_HorizonLengthRecip");
		private static int _SGT_Sky                 = Shader.PropertyToID("_SGT_Sky");
		private static int _SGT_InnerDepthTex       = Shader.PropertyToID("_SGT_InnerDepthTex");
		private static int _SGT_OuterDepthTex       = Shader.PropertyToID("_SGT_OuterDepthTex");
		private static int _SGT_LightingTex         = Shader.PropertyToID("_SGT_LightingTex");
		private static int _SGT_ScatteringTex       = Shader.PropertyToID("_SGT_ScatteringTex");

		public Transform CachedTransform
		{
			get
			{
				CacheTransform(); return cachedTransform;
			}
		}

		public SgtSharedMaterial CachedSharedMaterial
		{
			get
			{
				if (cachedSharedMaterialSet == false)
				{
					cachedSharedMaterial    = GetComponent<SgtSharedMaterial>();
					cachedSharedMaterialSet = true;
				}

				return cachedSharedMaterial;
			}
		}

		public float OuterRadius
		{
			get
			{
				return innerMeshRadius + height;
			}
		}

		public Material InnerMaterial
		{
			get
			{
				return innerMaterial;
			}
		}

		public Material OuterMaterial
		{
			get
			{
				return outerMaterial;
			}
		}

		public bool HasChild(CwChild child)
		{
			return child == model;
		}

		[ContextMenu("Sync Materials")]
		public void SyncMaterials()
		{
			if (sourceMaterial != null)
			{
				if (innerMaterial != null && outerMaterial != null)
				{
					if (innerMaterial.shader != sourceMaterial.shader)
					{
						innerMaterial.shader = sourceMaterial.shader;
					}

					if (outerMaterial.shader != sourceMaterial.shader)
					{
						outerMaterial.shader = sourceMaterial.shader;
					}

					innerMaterial.CopyPropertiesFromMaterial(sourceMaterial);
					outerMaterial.CopyPropertiesFromMaterial(sourceMaterial);

					if (OnSetProperties != null)
					{
						OnSetProperties.Invoke(innerMaterial, outerMaterial);
					}
				}
			}
		}

		private void HandleCameraPreRender(Camera camera)
		{
			var eye = camera.transform.position;

			if (sourceMaterial != null)
			{
				if (innerMaterial == null)
				{
					innerMaterial = CwHelper.CreateTempMaterial("Atmosphere Inner (Generated)", sourceMaterial);

					CachedSharedMaterial.Material = innerMaterial;
				}

				if (outerMaterial == null)
				{
					outerMaterial = CwHelper.CreateTempMaterial("Atmosphere Outer (Generated)", sourceMaterial);

					if (model != null)
					{
						model.CachedMeshRenderer.sharedMaterial = outerMaterial;
					}
				}

				if (OnSetProperties != null)
				{
					OnSetProperties.Invoke(innerMaterial, outerMaterial);
				}

				innerMaterial.SetColor(_SGT_Color, color);
				outerMaterial.SetColor(_SGT_Color, color);
				innerMaterial.SetFloat(_SGT_Brightness, brightness);
				outerMaterial.SetFloat(_SGT_Brightness, brightness);

				SgtCommon.EnableKeyword("_SGT_OUTER", outerMaterial);
				
				innerMaterial.SetInt(_SGT_Cull, 2); // Back
				outerMaterial.SetInt(_SGT_Cull, 1); // Front

				if (sourceMaterial != null && sourceMaterial.IsKeywordEnabled("_SGT_LIGHTING") == true)
				{
					// Write lights and shadows
					CwHelper.SetTempMaterial(innerMaterial, outerMaterial);

					var mask   = 1 << gameObject.layer;
					var lights = SgtLight.Find(mask, transform.position);

					SgtShadow.Find(true, mask, lights);
					SgtShadow.FilterOutRing(transform.position);
					SgtShadow.WriteSphere(SgtShadow.MAX_SPHERE_SHADOWS);
					SgtShadow.WriteRing(SgtShadow.MAX_RING_SHADOWS);

					SgtLight.FilterOut(transform.position);
					SgtLight.Write(transform.position, CwHelper.UniformScale(transform.lossyScale) * OuterRadius, cachedTransform, null, SgtLight.MAX_LIGHTS);
				}

				var innerRatio = CwHelper.Divide(innerMeshRadius, OuterRadius);

				innerMaterial.SetFloat(_SGT_InnerRatio, innerRatio);
				innerMaterial.SetFloat(_SGT_InnerScale, 1.0f / (1.0f - innerRatio));

				if (outerSoftness > 0.0f)
				{
					SgtCommon.EnableKeyword("_SGT_SOFTNESS", outerMaterial);

					outerMaterial.SetFloat(_SGT_SoftParticlesFactor, CwHelper.Reciprocal(outerSoftness));
				}
				else
				{
					SgtCommon.DisableKeyword("_SGT_SOFTNESS", outerMaterial);
				}

				var scale        = CwHelper.Divide(outerMeshRadius, OuterRadius);
				var worldToLocal = Matrix4x4.Scale(new Vector3(scale, scale, scale)) * cachedTransform.worldToLocalMatrix;

				innerMaterial.SetMatrix(_SGT_WorldToLocal, worldToLocal);
				outerMaterial.SetMatrix(_SGT_WorldToLocal, worldToLocal);

				// Write camera-dependent shader values
				if (innerMaterial != null && outerMaterial != null)
				{
					var localPosition  = cachedTransform.InverseTransformPoint(eye);
					var localDistance  = localPosition.magnitude;
					var innerThickness = default(float);
					var outerThickness = default(float);
					var middleRatio    = Mathf.Lerp(innerRatio, 1.0f, middle);
					var distance       = CwHelper.Divide(localDistance, OuterRadius);
					var innerDensity   = 1.0f - innerFog;
					var outerDensity   = 1.0f - outerFog;

					SgtCommon.CalculateHorizonThickness(innerRatio, middleRatio, distance, out innerThickness, out outerThickness);

					innerMaterial.SetFloat(_SGT_HorizonLengthRecip, CwHelper.Reciprocal(innerThickness * innerDensity));
					outerMaterial.SetFloat(_SGT_HorizonLengthRecip, CwHelper.Reciprocal(outerThickness * outerDensity));

					var outerDepthTex = GetOuterDepthTex();

					if (outerDepthTex != null)
					{
						outerMaterial.SetFloat(_SGT_Sky, GetSky(eye, localDistance, outerDepthTex));
					}
				}
			}

			model.transform.localScale = Vector3.one * CwHelper.Divide(OuterRadius, outerMeshRadius);

			if (cameraOffset != 0.0f)
			{
				var direction = Vector3.Normalize(eye - cachedTransform.position);

				model.transform.position = cachedTransform.position + direction * cameraOffset;
			}
			else
			{
				model.transform.localPosition = Vector3.zero;
			}
		}

		public static SgtAtmosphere Create(int layer = 0, Transform parent = null)
		{
			return Create(layer, parent, Vector3.zero, Quaternion.identity, Vector3.one);
		}

		public static SgtAtmosphere Create(int layer, Transform parent, Vector3 localPosition, Quaternion localRotation, Vector3 localScale)
		{
			return CwHelper.CreateGameObject("Atmosphere", layer, parent, localPosition, localRotation, localScale).AddComponent<SgtAtmosphere>();
		}

		protected virtual void OnEnable()
		{
			CwHelper.OnCameraPreRender     += HandleCameraPreRender;
			SgtCommon.OnCalculateOcclusion += HandleCalculateOcclusion;

			if (model == null)
			{
				model = SgtAtmosphereModel.Create(this);
			}

			model.CachedMeshRenderer.enabled = true;

			CacheTransform();

			CachedSharedMaterial.Material = innerMaterial;
		}

		protected virtual void OnDisable()
		{
			CwHelper.OnCameraPreRender     -= HandleCameraPreRender;
			SgtCommon.OnCalculateOcclusion -= HandleCalculateOcclusion;

			if (model != null)
			{
				model.CachedMeshRenderer.enabled = false;
			}

			CachedSharedMaterial.Material = null;
		}

		protected virtual void OnDestroy()
		{
			CwHelper.Destroy(outerMaterial);
			CwHelper.Destroy(innerMaterial);
		}

		protected virtual void LateUpdate()
		{
#if UNITY_EDITOR
			SyncMaterials();
#endif
			if (model != null)
			{
				model.CachedMeshFilter.sharedMesh = outerMesh;
			}
		}

#if UNITY_EDITOR
		protected virtual void OnDrawGizmosSelected()
		{
			if (isActiveAndEnabled == true)
			{
				var r1 = innerMeshRadius;
				var r2 = OuterRadius;

				SgtCommon.DrawSphere(transform.position, transform.right * transform.lossyScale.x * r1, transform.up * transform.lossyScale.y * r1, transform.forward * transform.lossyScale.z * r1);
				SgtCommon.DrawSphere(transform.position, transform.right * transform.lossyScale.x * r2, transform.up * transform.lossyScale.y * r2, transform.forward * transform.lossyScale.z * r2);
			}
		}
#endif

		public bool NeedsDepthTex
		{
			get
			{
				if (innerMaterial != null && innerMaterial.HasProperty(_SGT_InnerDepthTex) == true && innerMaterial.GetTexture(_SGT_InnerDepthTex) == null)
				{
					if (outerMaterial != null && outerMaterial.HasProperty(_SGT_OuterDepthTex) == true && outerMaterial.GetTexture(_SGT_OuterDepthTex) == null)
					{
						return true;
					}
				}

				return false;
			}
		}

		public bool NeedsLightingTex
		{
			get
			{
				if (innerMaterial != null && innerMaterial.IsKeywordEnabled("_SGT_LIGHTING") == true && innerMaterial.GetTexture(_SGT_LightingTex) == null)
				{
					if (outerMaterial != null && outerMaterial.IsKeywordEnabled("_SGT_LIGHTING") == true && outerMaterial.GetTexture(_SGT_LightingTex) == null)
					{
						return true;
					}
				}

				return false;
			}
		}

		public bool NeedsScatteringTex
		{
			get
			{
				if (innerMaterial != null && innerMaterial.IsKeywordEnabled("_SGT_SCATTERING") == true && innerMaterial.GetTexture(_SGT_ScatteringTex) == null)
				{
					if (outerMaterial != null && outerMaterial.IsKeywordEnabled("_SGT_SCATTERING") == true && outerMaterial.GetTexture(_SGT_ScatteringTex) == null)
					{
						return true;
					}
				}

				return false;
			}
		}

		public bool IsLit
		{
			get
			{
				if (innerMaterial != null && innerMaterial.IsKeywordEnabled("_SGT_LIGHTING") == true)
				{
					return true;
				}

				if (outerMaterial != null && outerMaterial.IsKeywordEnabled("_SGT_LIGHTING") == true)
				{
					return true;
				}

				return false;
			}
		}

		public Texture2D GetOuterDepthTex()
		{
			if (outerMaterial != null)
			{
				return outerMaterial.GetTexture(_SGT_OuterDepthTex) as Texture2D;
			}

			return null;
		}

		private float GetSky(Vector3 eye, float localDistance, Texture2D outerDepthTex)
		{
			if (outerDepthTex.isReadable == false)
			{
				return 0.0f;
			}

			var height01 = Mathf.InverseLerp(OuterRadius, innerMeshRadius, localDistance);
			var mul      = outerDepthTex.GetPixelBilinear(height01 / (1.0f - outerFog), 0.0f).a;

			if (night == true)
			{
				var mask   = 1 << gameObject.layer;
				var lights = SgtLight.Find(mask, cachedTransform.position);

				var lighting        = 0.0f;
				var cameraDirection = (eye - cachedTransform.position).normalized;

				for (var i = 0; i < lights.Count && i < 2; i++)
				{
					var light     = lights[i];
					var position  = default(Vector3);
					var direction = default(Vector3);
					var color     = default(Color);
					var intensity = 0.0f;

					SgtLight.Calculate(light, cachedTransform.position, 0.0f, null, null, ref position, ref direction, ref color, ref intensity);

					var dot     = Vector3.Dot(direction, cameraDirection) * 0.5f + 0.5f;
					var night01 = Mathf.InverseLerp(nightEnd, nightStart, dot);
					var night   = SgtEase.Evaluate(nightEase, 1.0f - Mathf.Pow(night01, nightPower));

					if (night > lighting)
					{
						lighting = night;
					}
				}

				return Mathf.Lerp(nightSky, sky, lighting) * mul;
			}
		
			return sky * mul;
		}

		private bool GetPoint(Vector3 ray, Vector3 dir, ref Vector3 point)
		{
			var a = Vector3.Dot(ray, dir);
			var b = Vector3.Dot(ray, ray) - 1.0f;

			if (b <= 0.0f) { point = ray; return true; } // Inside?

			var c = a * a - b;

			if (c < 0.0f) { return false; } // Miss?

			var d = -a - Mathf.Sqrt(c);

			if (d < 0.0f) { return false; } // Behind?

			point = ray + dir * d; return true;
		}

		private bool GetLength(Vector3 ray, Vector3 dir, float len, ref float length)
		{
			var a = default(Vector3);
			var b = default(Vector3);

			if (GetPoint(ray, dir, ref a) == true && GetPoint(ray + dir * len, -dir, ref b) == true)
			{
				length = Vector3.Distance(a, b); return true;
			}

			return false;
		}

		private void HandleCalculateOcclusion(int layers, Vector4 worldEye, Vector4 worldTgt, ref float occlusion)
		{
			if (SgtOcclusion.IsValid(occlusion, layers, gameObject) == true && OuterRadius > 0.0f)
			{
				var outerDepthTex = GetOuterDepthTex();

				if (outerDepthTex != null && outerDepthTex.isReadable == true)
				{
					SgtOcclusion.TryScaleBackDistantPositions(ref worldEye, ref worldTgt, cachedTransform.position, OuterRadius);

					var eye    = cachedTransform.InverseTransformPoint(worldEye) / OuterRadius;
					var tgt    = cachedTransform.InverseTransformPoint(worldTgt) / OuterRadius;
					var dir    = Vector3.Normalize(tgt - eye);
					var len    = Vector3.Magnitude(tgt - eye);
					var length = default(float);

					if (GetLength(eye, dir, len, ref length) == true)
					{
						var localPosition  = cachedTransform.InverseTransformPoint(worldEye);
						var localDistance  = localPosition.magnitude;
						var innerThickness = default(float);
						var outerThickness = default(float);
						var innerRatio     = CwHelper.Divide(innerMeshRadius, OuterRadius);
						var middleRatio    = Mathf.Lerp(innerRatio, 1.0f, middle);
						var distance       = CwHelper.Divide(localDistance, OuterRadius);
						var outerDensity   = 1.0f - outerFog;

						SgtCommon.CalculateHorizonThickness(innerRatio, middleRatio, distance, out innerThickness, out outerThickness);

						length *= CwHelper.Reciprocal(outerThickness * outerDensity);

						var depth = outerDepthTex.GetPixelBilinear(length, length).a;

						depth = 1.0f - Mathf.Pow(1.0f - depth, occlusionPower);

						depth = Mathf.Clamp01(depth + (1.0f - depth) * GetSky(worldEye, localDistance, outerDepthTex));

						occlusion += (1.0f - occlusion) * depth;
					}
				}
			}
		}

		private void CacheTransform()
		{
			if (cachedTransformSet == false)
			{
				cachedTransform    = GetComponent<Transform>();
				cachedTransformSet = true;
			}
		}
	}
}

#if UNITY_EDITOR
namespace SpaceGraphicsToolkit.Atmosphere
{
	using UnityEditor;
	using TARGET = SgtAtmosphere;

	[CanEditMultipleObjects]
	[CustomEditor(typeof(TARGET))]
	public class SgtAtmosphere_Editor : CwEditor
	{
		protected override void OnInspector()
		{
			TARGET tgt; TARGET[] tgts; GetTargets(out tgt, out tgts);

			BeginError(Any(tgts, t => t.SourceMaterial == null));
				Draw("sourceMaterial", "The material used to render this component.\n\nNOTE: This material must use the <b>Space Graphics Toolkit/Atmosphere</b> shader. You cannot use a normal shader.");
			EndError();
			Draw("color", "This allows you to set the overall color of the atmosphere.");
			Draw("brightness", "The <b>Color.rgb</b> values will be multiplied by this.");

			Separator();

			BeginError(Any(tgts, t => t.InnerMeshRadius <= 0.0f));
				Draw("innerMeshRadius", "The radius of the meshes set in the SgtSharedMaterial.");
			EndError();

			Separator();

			BeginError(Any(tgts, t => t.OuterMesh == null));
				Draw("outerMesh", "This allows you to set the mesh used to render the atmosphere. This should be a sphere.");
			EndError();
			BeginError(Any(tgts, t => t.OuterMeshRadius <= 0.0f));
				Draw("outerMeshRadius", "This allows you to set the radius of the OuterMesh. If this is incorrectly set then the atmosphere will render incorrectly.");
			EndError();
			Draw("outerSoftness", "If you have a big object that is both inside and outside of the atmosphere, it can cause a sharp intersection line. Increasing this setting allows you to change the smoothness of this intersection.");

			if (Any(tgts, t => t.OuterSoftness > 0.0f))
			{
				CwDepthTextureMode_Editor.RequireDepth();
			}

			Separator();

			BeginError(Any(tgts, t => t.Height <= 0.0f));
				Draw("height", "This allows you to set how high the atmosphere extends above the surface of the planet in local space.");
			EndError();
			BeginError(Any(tgts, t => t.InnerFog >= 1.0f));
				Draw("innerFog", "This allows you to adjust the fog level of the atmosphere on the surface.");
			EndError();
			BeginError(Any(tgts, t => t.OuterFog >= 1.0f));
				Draw("outerFog", "This allows you to adjust the fog level of the atmosphere in the sky.");
			EndError();
			BeginError(Any(tgts, t => t.Sky < 0.0f));
				Draw("sky", "This allows you to control how quickly the sky reaches maximum opacity as the camera enters the atmosphere.\n\n1 = You must go down to ground level before the opacity reaches max.\n\n2 = You must go at least half way down to the surface."); // Updated automatically
			EndError();
			Draw("middle", "This allows you to set the altitude where atmospheric density reaches its maximum point. The lower you set this, the foggier the horizon will appear when approaching the surface."); // Updated automatically
			Draw("cameraOffset", "This allows you to offset the camera distance in world space when rendering the atmosphere, giving you fine control over the render order."); // Updated automatically
			Draw("occlusionPower", "This setting allows you to increase or decrease how much this atmosphere occludes flares using the <b>SgtOcclusionScaler</b> component."); // Updated automatically

			if (Any(tgts, t => t.IsLit == true))
			{
				Separator();

				Draw("night", "Should the night side of the atmosphere have different sky values?"); // Updated automatically
				if (Any(tgts, t => t.Night == true))
				{
					BeginIndent();
						Draw("nightSky", "The 'Sky' value of the night side."); // Updated automatically
						Draw("nightEase", "The transition style between the day and night."); // Updated automatically
						BeginError(Any(tgts, t => t.NightStart >= t.NightEnd));
							Draw("nightStart", "The start point of the day/sunset transition (0 = dark side, 1 = light side)."); // Updated automatically
							Draw("nightEnd", "The end point of the day/sunset transition (0 = dark side, 1 = light side)."); // Updated automatically
						EndError();
						BeginError(Any(tgts, t => t.NightPower < 1.0f));
							Draw("nightPower", "The power of the night transition."); // Updated automatically
						EndError();
					EndIndent();
				}
			}

			if (Any(tgts, t => t.NeedsDepthTex == true && t.GetComponent<SgtAtmosphereDepthTex>() == null))
			{
				Separator();

				if (HelpButton("SourceMaterial doesn't contain an Inner/Outer DepthTex.", MessageType.Error, "Fix", 30) == true)
				{
					Each(tgts, t => CwHelper.GetOrAddComponent<SgtAtmosphereDepthTex>(t.gameObject));
				}
			}

			if (Any(tgts, t => t.SourceMaterial != null && t.SourceMaterial.IsKeywordEnabled("_SGT_LIGHTING") == true && SgtLight.InstanceCount == 0))
			{
				Separator();

				Warning("You need to add the SgtLight component to your scene lights for them to work with SGT.");
			}

			if (Any(tgts, t => t.NeedsLightingTex == true && t.GetComponent<SgtAtmosphereLightingTex>() == null))
			{
				Separator();

				if (HelpButton("SourceMaterial doesn't contain a LightingTex.", MessageType.Error, "Fix", 30) == true)
				{
					Each(tgts, t => CwHelper.GetOrAddComponent<SgtAtmosphereLightingTex>(t.gameObject));
				}
			}

			if (Any(tgts, t => t.NeedsScatteringTex == true && t.GetComponent<SgtAtmosphereScatteringTex>() == null))
			{
				Separator();

				if (HelpButton("SourceMaterial doesn't contain a ScatteringTex.", MessageType.Error, "Fix", 30) == true)
				{
					Each(tgts, t => CwHelper.GetOrAddComponent<SgtAtmosphereScatteringTex>(t.gameObject));
				}
			}

			if (Any(tgts, t => SetOuterMeshAndOuterMeshRadius(t, false)))
			{
				Separator();

				if (Button("Set Outer Mesh & Outer Mesh Radius") == true)
				{
					Each(tgts, t => SetOuterMeshAndOuterMeshRadius(t, true));
				}
			}

			if (Any(tgts, t => AddInnerRendererAndSetInnerMeshRadius(t, false)))
			{
				Separator();

				if (Button("Add Inner Renderer & Set Inner Mesh Radius") == true)
				{
					Each(tgts, t => AddInnerRendererAndSetInnerMeshRadius(t, true));
				}
			}
		}

		private bool SetOuterMeshAndOuterMeshRadius(SgtAtmosphere atmosphere, bool apply)
		{
			if (atmosphere.OuterMesh == null)
			{
				var mesh = CwHelper.LoadFirstAsset<Mesh>("Geosphere40 t:mesh");

				if (mesh != null)
				{
					if (apply == true)
					{
						atmosphere.OuterMesh       = mesh;
						atmosphere.OuterMeshRadius = SgtCommon.GetBoundsRadius(mesh.bounds);
					}

					return true;
				}
			}

			return false;
		}

		private bool AddInnerRendererAndSetInnerMeshRadius(SgtAtmosphere atmosphere, bool apply)
		{
			if (atmosphere.CachedSharedMaterial.RendererCount == 0)
			{
				var meshRenderer = atmosphere.GetComponentInParent<MeshRenderer>();

				if (meshRenderer != null)
				{
					var meshFilter = meshRenderer.GetComponent<MeshFilter>();

					if (meshFilter != null)
					{
						var mesh = meshFilter.sharedMesh;

						if (mesh != null)
						{
							if (apply == true)
							{
								atmosphere.CachedSharedMaterial.AddRenderer(meshRenderer);
								atmosphere.InnerMeshRadius = SgtCommon.GetBoundsRadius(mesh.bounds);
							}

							return true;
						}
					}
				}
			}

			return false;
		}

		[MenuItem(SgtCommon.GameObjectMenuPrefix + "Atmosphere", false, 10)]
		public static void CreateMenuItem()
		{
			var parent   = CwHelper.GetSelectedParent();
			var instance = SgtAtmosphere.Create(parent != null ? parent.gameObject.layer : 0, parent);

			CwHelper.SelectAndPing(instance);
		}
	}
}
#endif