%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e78934177bebe0545ab566bb787b0282, type: 3}
  m_Name: Readme
  m_EditorClassIdentifier: 
  Icon: {fileID: 2800000, guid: 055fe12fe3b3d664d8ce765b1a150328, type: 3}
  Title: Simple Sprite
  Description:
    Heading: 
    Text: 'Sprite Simple Simple sprite texture mixed with sprite set in Unity''s
      Sprite Renderer component.  '
    LinkText: 
    Url: 
  PropertiesHeader:
    Heading: Properties
    Text: 
    LinkText: 
    Url: 
  Properties:
  - Heading: Sprite Texture
    Text: 
    LinkText: 
    Url: 
  - Heading: Tint
    Text: Sprite tint color value.
    LinkText: 
    Url: 
  - Heading: Pixel snap
    Text: Toggle Pixel Snapping which prevents subpixel movement and make Sprites
      appear to move in pixel-by-pixel increments. This does not affect any GameObjects'
      Transform positions, it snaps Sprite Renderers to a grid in world space at
      render-time. The grid size is based on the Assets Pixels Per Unit value.
    LinkText: 
    Url: 
  AdditionalProperties: []
  AdditionalScripts: []
  LoadedLayout: 0
  RPType: 3
