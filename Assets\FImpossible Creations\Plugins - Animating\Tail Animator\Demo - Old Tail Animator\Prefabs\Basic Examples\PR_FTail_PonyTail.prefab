%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &1043577217435272
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4971127532170546}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4971127532170546
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1043577217435272}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.279955e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4749141543661990}
  m_Father: {fileID: 4848305313990176}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1048950261492162
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4920832120102498}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4920832120102498
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1048950261492162}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.279955e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4492924422443356}
  m_Father: {fileID: 4823033360559744}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1054868807963462
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4445487823936362}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4445487823936362
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1054868807963462}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.2779117e-10, z: 9.196545e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4979861626946692}
  m_Father: {fileID: 4454773770826702}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1094987262883980
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4090576759330264}
  - component: {fileID: 114832306753744818}
  - component: {fileID: 114072903691230218}
  - component: {fileID: 114830523487505498}
  m_Layer: 0
  m_Name: PR_FTail_PonyTail
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4090576759330264
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1094987262883980}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4742979576122076}
  - {fileID: 4481394801057438}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &114832306753744818
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1094987262883980}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc71bf79a039ae24894ef08c2171fb45, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  VibrationRate: 6
  BaseRange: 19
  SmoothTranslation: 0.5
  AxesMultiplier: {x: 1, y: 1, z: 1}
  ChangeObjectPosition: 1
--- !u!114 &114072903691230218
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1094987262883980}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ebc354317bbc5dd40b24b190775f892a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  RotationAxis: {x: 0, y: 1, z: 0}
  RotationRange: 77
  SinSpeed: 3
--- !u!114 &114830523487505498
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1094987262883980}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ebc354317bbc5dd40b24b190775f892a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  RotationAxis: {x: 0.3, y: 0, z: 0.25}
  RotationRange: 77
  SinSpeed: 4
--- !u!1 &1097936201721128
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4235880408904386}
  m_Layer: 0
  m_Name: Fur(Clone)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4235880408904386
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1097936201721128}
  serializedVersion: 2
  m_LocalRotation: {x: 0.052858386, y: -0.02868963, z: 0.0015192411, w: 0.9981887}
  m_LocalPosition: {x: -0.073032975, y: 2.187141, z: -4.037281}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4454773770826702}
  - {fileID: 4123084086500282}
  m_Father: {fileID: 4481394801057438}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1156367496792184
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4020662375805082}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4020662375805082
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1156367496792184}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844743, z: 0.000000024576083, w: 0.70325243}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4267983402491978}
  m_Father: {fileID: 4834378381298822}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1161232538268550
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4267983402491978}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4267983402491978
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1161232538268550}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.2779117e-10, z: 9.196545e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4247802260225544}
  m_Father: {fileID: 4020662375805082}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1175152883169094
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4979861626946692}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4979861626946692
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1175152883169094}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.279955e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4986336754231610}
  m_Father: {fileID: 4445487823936362}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1187005064198894
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4093576445921872}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4093576445921872
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1187005064198894}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844743, z: 0.000000024576083, w: 0.70325243}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4836700181815478}
  m_Father: {fileID: 4821125137964406}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1215450978700884
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4836454399441226}
  m_Layer: 0
  m_Name: Fur(Clone)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4836454399441226
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1215450978700884}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0089860875, y: -0.3951163, z: 0.0038652453, w: 0.91857904}
  m_LocalPosition: {x: -0.07710111, y: 2.1898417, z: -4.083273}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4187109426122220}
  - {fileID: 4150073314917038}
  m_Father: {fileID: 4481394801057438}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1222318487273600
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4572721661733120}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4572721661733120
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1222318487273600}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.279955e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4125156949121686}
  m_Father: {fileID: 4439350241508380}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1254150748065260
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4848305313990176}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4848305313990176
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1254150748065260}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.2779117e-10, z: 9.196545e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4971127532170546}
  m_Father: {fileID: 4251576564791096}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1269107762011160
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4986336754231610}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4986336754231610
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1269107762011160}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4642728776425012}
  m_Father: {fileID: 4979861626946692}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1275337440878894
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4823033360559744}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4823033360559744
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1275337440878894}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.2779117e-10, z: 9.196545e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4920832120102498}
  m_Father: {fileID: 4650564218618450}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1334428455476826
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4150073314917038}
  - component: {fileID: 137178791773383174}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4150073314917038
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1334428455476826}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4836454399441226}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137178791773383174
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1334428455476826}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4187109426122220}
  - {fileID: 4439350241508380}
  - {fileID: 4572721661733120}
  - {fileID: 4125156949121686}
  - {fileID: 4219382943780292}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4187109426122220}
  m_AABB:
    m_Center: {x: 0, y: -0.0006977841, z: -0.4683083}
    m_Extent: {x: 0.09854454, y: 0.1036482, z: 0.47101066}
  m_DirtyAABB: 0
--- !u!1 &1370577880162440
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4830901641744902}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4830901641744902
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1370577880162440}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4163580791611784}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1381233473126028
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4759648851163394}
  - component: {fileID: 137886258729751892}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4759648851163394
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1381233473126028}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4052276963958076}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137886258729751892
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1381233473126028}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4650564218618450}
  - {fileID: 4823033360559744}
  - {fileID: 4920832120102498}
  - {fileID: 4492924422443356}
  - {fileID: 4487877321827772}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4650564218618450}
  m_AABB:
    m_Center: {x: 0, y: -0.0006977841, z: -0.4683083}
    m_Extent: {x: 0.09854454, y: 0.1036482, z: 0.47101066}
  m_DirtyAABB: 0
--- !u!1 &1395609070964050
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4821125137964406}
  m_Layer: 0
  m_Name: Fur(Clone)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4821125137964406
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1395609070964050}
  serializedVersion: 2
  m_LocalRotation: {x: 0.10815387, y: -0.2335432, z: 0.026148736, w: 0.9659589}
  m_LocalPosition: {x: -0.12051718, y: 2.1775565, z: -3.9916348}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4093576445921872}
  - {fileID: 4933681620636744}
  m_Father: {fileID: 4481394801057438}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1458917351953776
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4187109426122220}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4187109426122220
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1458917351953776}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844743, z: 0.000000024576083, w: 0.70325243}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4439350241508380}
  m_Father: {fileID: 4836454399441226}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1520875838514866
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4219382943780292}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4219382943780292
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1520875838514866}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4125156949121686}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1522801798329406
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4561899917153714}
  m_Layer: 0
  m_Name: Fur(Clone)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4561899917153714
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1522801798329406}
  serializedVersion: 2
  m_LocalRotation: {x: 0.060304936, y: 0.28110212, z: -0.017702121, w: 0.9576177}
  m_LocalPosition: {x: -0.03609627, y: 2.185988, z: -4.037227}
  m_LocalScale: {x: 1, y: 0.9999999, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4251576564791096}
  - {fileID: 4943621293057736}
  m_Father: {fileID: 4481394801057438}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1553419639918148
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4836700181815478}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4836700181815478
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1553419639918148}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.2779117e-10, z: 9.196545e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4570469552491106}
  m_Father: {fileID: 4093576445921872}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1575935382453878
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4642728776425012}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4642728776425012
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1575935382453878}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4986336754231610}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1660613939783460
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4933681620636744}
  - component: {fileID: 137808986621172292}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4933681620636744
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1660613939783460}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4821125137964406}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137808986621172292
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1660613939783460}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4093576445921872}
  - {fileID: 4836700181815478}
  - {fileID: 4570469552491106}
  - {fileID: 4163580791611784}
  - {fileID: 4830901641744902}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4093576445921872}
  m_AABB:
    m_Center: {x: 0, y: -0.0006977841, z: -0.4683083}
    m_Extent: {x: 0.09854454, y: 0.1036482, z: 0.47101066}
  m_DirtyAABB: 0
--- !u!1 &1663537203295050
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4454773770826702}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4454773770826702
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1663537203295050}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844743, z: 0.000000024576083, w: 0.70325243}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4445487823936362}
  m_Father: {fileID: 4235880408904386}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1671032878649810
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4487877321827772}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4487877321827772
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1671032878649810}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4492924422443356}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1675092700143662
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4439350241508380}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4439350241508380
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1675092700143662}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.2779117e-10, z: 9.196545e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4572721661733120}
  m_Father: {fileID: 4187109426122220}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1688061690625684
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4749141543661990}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4749141543661990
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1688061690625684}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4488164558947728}
  m_Father: {fileID: 4971127532170546}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1724073471824908
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4834378381298822}
  m_Layer: 0
  m_Name: Fur(Clone)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4834378381298822
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1724073471824908}
  serializedVersion: 2
  m_LocalRotation: {x: 0.04894821, y: 0.11761691, z: -0.005804532, w: 0.991835}
  m_LocalPosition: {x: -0.058485728, y: 2.1875076, z: -4.0421343}
  m_LocalScale: {x: 0.9999999, y: 0.9999999, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4020662375805082}
  - {fileID: 4107873960958460}
  m_Father: {fileID: 4481394801057438}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1810284788947838
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4492924422443356}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4492924422443356
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1810284788947838}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4487877321827772}
  m_Father: {fileID: 4920832120102498}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1814627916079820
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4943621293057736}
  - component: {fileID: 137469981887917796}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4943621293057736
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1814627916079820}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4561899917153714}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137469981887917796
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1814627916079820}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4251576564791096}
  - {fileID: 4848305313990176}
  - {fileID: 4971127532170546}
  - {fileID: 4749141543661990}
  - {fileID: 4488164558947728}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4251576564791096}
  m_AABB:
    m_Center: {x: 0, y: -0.0006977841, z: -0.4683083}
    m_Extent: {x: 0.09854454, y: 0.1036482, z: 0.47101066}
  m_DirtyAABB: 0
--- !u!1 &1815396121196802
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4488164558947728}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4488164558947728
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1815396121196802}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4749141543661990}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1818128774959920
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4052276963958076}
  m_Layer: 0
  m_Name: Fur(Clone)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4052276963958076
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1818128774959920}
  serializedVersion: 2
  m_LocalRotation: {x: 0.10195727, y: -0.166167, z: 0.017276036, w: 0.9806604}
  m_LocalPosition: {x: -0.10388389, y: 2.1792445, z: -3.9928854}
  m_LocalScale: {x: 1, y: 0.9999999, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4650564218618450}
  - {fileID: 4759648851163394}
  m_Father: {fileID: 4481394801057438}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1862100557658156
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4251576564791096}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4251576564791096
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1862100557658156}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844743, z: 0.000000024576083, w: 0.70325243}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4848305313990176}
  m_Father: {fileID: 4561899917153714}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1868717656396478
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4650564218618450}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4650564218618450
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1868717656396478}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844743, z: 0.000000024576083, w: 0.70325243}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4823033360559744}
  m_Father: {fileID: 4052276963958076}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1887311179857532
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4163580791611784}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4163580791611784
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1887311179857532}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4830901641744902}
  m_Father: {fileID: 4570469552491106}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1899431818927452
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4570469552491106}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4570469552491106
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899431818927452}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.279955e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4163580791611784}
  m_Father: {fileID: 4836700181815478}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1901500966164318
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4125156949121686}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4125156949121686
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1901500966164318}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4219382943780292}
  m_Father: {fileID: 4572721661733120}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1925985919093934
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4408415981336888}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4408415981336888
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1925985919093934}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4311364958387930}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1928647989632342
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4311364958387930}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4311364958387930
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1928647989632342}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4408415981336888}
  m_Father: {fileID: 4247802260225544}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1933554776271452
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4107873960958460}
  - component: {fileID: 137096024311847122}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4107873960958460
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1933554776271452}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4834378381298822}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137096024311847122
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1933554776271452}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4020662375805082}
  - {fileID: 4267983402491978}
  - {fileID: 4247802260225544}
  - {fileID: 4311364958387930}
  - {fileID: 4408415981336888}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4020662375805082}
  m_AABB:
    m_Center: {x: 0, y: -0.0006977841, z: -0.4683083}
    m_Extent: {x: 0.09854454, y: 0.1036482, z: 0.47101066}
  m_DirtyAABB: 0
--- !u!1 &1948784089978982
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4123084086500282}
  - component: {fileID: 137757118538359770}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4123084086500282
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1948784089978982}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4235880408904386}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137757118538359770
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1948784089978982}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4454773770826702}
  - {fileID: 4445487823936362}
  - {fileID: 4979861626946692}
  - {fileID: 4986336754231610}
  - {fileID: 4642728776425012}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4454773770826702}
  m_AABB:
    m_Center: {x: 0, y: -0.0006977841, z: -0.4683083}
    m_Extent: {x: 0.09854454, y: 0.1036482, z: 0.47101066}
  m_DirtyAABB: 0
--- !u!1 &1950949000683722
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4481394801057438}
  m_Layer: 0
  m_Name: Duplicated-Fur-Container
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4481394801057438
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1950949000683722}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: 1, z: -0, w: 0.00005541741}
  m_LocalPosition: {x: -0.069546685, y: 0.29006243, z: -4.090008}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4836454399441226}
  - {fileID: 4561899917153714}
  - {fileID: 4235880408904386}
  - {fileID: 4834378381298822}
  - {fileID: 4821125137964406}
  - {fileID: 4052276963958076}
  m_Father: {fileID: 4090576759330264}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1969948421562728
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4247802260225544}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4247802260225544
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1969948421562728}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.279955e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4311364958387930}
  m_Father: {fileID: 4267983402491978}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1999886796731554
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4742979576122076}
  - component: {fileID: 33114582974125906}
  - component: {fileID: 135027779721840464}
  - component: {fileID: 23481780687369186}
  m_Layer: 1
  m_Name: Sphere
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4742979576122076
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1999886796731554}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 1.98, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4090576759330264}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &33114582974125906
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1999886796731554}
  m_Mesh: {fileID: 10207, guid: 0000000000000000e000000000000000, type: 0}
--- !u!135 &135027779721840464
SphereCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1999886796731554}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Radius: 0.5
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &23481780687369186
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1999886796731554}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10303, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 1
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
