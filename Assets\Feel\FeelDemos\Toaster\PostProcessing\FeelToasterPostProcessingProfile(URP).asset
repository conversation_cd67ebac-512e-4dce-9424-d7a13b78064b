%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-9035914584803264492
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 29fa0085f50d5e54f8144f766051a691, type: 3}
  m_Name: FilmGrain
  m_EditorClassIdentifier: 
  active: 1
  type:
    m_OverrideState: 1
    m_Value: 1
  intensity:
    m_OverrideState: 1
    m_Value: 0.227
  response:
    m_OverrideState: 1
    m_Value: 0.8
  texture:
    m_OverrideState: 0
    m_Value: {fileID: 0}
--- !u!114 &-8315555515906546732
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c5e1dc532bcb41949b58bc4f2abfbb7e, type: 3}
  m_Name: LensDistortion
  m_EditorClassIdentifier: 
  active: 1
  intensity:
    m_OverrideState: 1
    m_Value: 0
  xMultiplier:
    m_OverrideState: 1
    m_Value: 1
  yMultiplier:
    m_OverrideState: 1
    m_Value: 1
  center:
    m_OverrideState: 1
    m_Value: {x: 0, y: 0}
  scale:
    m_OverrideState: 1
    m_Value: 1
--- !u!114 &-7508789748578139213
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 81180773991d8724ab7f2d216912b564, type: 3}
  m_Name: ChromaticAberration
  m_EditorClassIdentifier: 
  active: 1
  intensity:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d7fd9488000d3734a9e00ee676215985, type: 3}
  m_Name: FeelToasterPostProcessingProfile(URP)
  m_EditorClassIdentifier: 
  components:
  - {fileID: 1273811206875593677}
  - {fileID: 1248199956506964094}
  - {fileID: 7739607259845976782}
  - {fileID: -9035914584803264492}
  - {fileID: -7508789748578139213}
  - {fileID: -8315555515906546732}
--- !u!114 &1248199956506964094
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0b2db86121404754db890f4c8dfe81b2, type: 3}
  m_Name: Bloom
  m_EditorClassIdentifier: 
  active: 1
  skipIterations:
    m_OverrideState: 0
    m_Value: 1
  threshold:
    m_OverrideState: 0
    m_Value: 1
  intensity:
    m_OverrideState: 1
    m_Value: 1
  scatter:
    m_OverrideState: 0
    m_Value: 0.35
  clamp:
    m_OverrideState: 0
    m_Value: 65472
  tint:
    m_OverrideState: 0
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  highQualityFiltering:
    m_OverrideState: 0
    m_Value: 1
  downscale:
    m_OverrideState: 0
    m_Value: 0
  maxIterations:
    m_OverrideState: 0
    m_Value: 6
  dirtTexture:
    m_OverrideState: 0
    m_Value: {fileID: 0}
    dimension: 1
  dirtIntensity:
    m_OverrideState: 0
    m_Value: 0
--- !u!114 &1273811206875593677
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 899c54efeace73346a0a16faa3afe726, type: 3}
  m_Name: Vignette
  m_EditorClassIdentifier: 
  active: 1
  color:
    m_OverrideState: 1
    m_Value: {r: 0.18867922, g: 0.0970096, b: 0.0970096, a: 1}
  center:
    m_OverrideState: 1
    m_Value: {x: 0.5, y: 0.5}
  intensity:
    m_OverrideState: 1
    m_Value: 0.455
  smoothness:
    m_OverrideState: 1
    m_Value: 0.2
  rounded:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &7739607259845976782
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c01700fd266d6914ababb731e09af2eb, type: 3}
  m_Name: DepthOfField
  m_EditorClassIdentifier: 
  active: 1
  mode:
    m_OverrideState: 1
    m_Value: 2
  gaussianStart:
    m_OverrideState: 0
    m_Value: 10
  gaussianEnd:
    m_OverrideState: 0
    m_Value: 30
  gaussianMaxRadius:
    m_OverrideState: 0
    m_Value: 1
  highQualitySampling:
    m_OverrideState: 0
    m_Value: 0
  focusDistance:
    m_OverrideState: 1
    m_Value: 32.02
  aperture:
    m_OverrideState: 1
    m_Value: 1
  focalLength:
    m_OverrideState: 1
    m_Value: 59.1
  bladeCount:
    m_OverrideState: 0
    m_Value: 5
  bladeCurvature:
    m_OverrideState: 0
    m_Value: 1
  bladeRotation:
    m_OverrideState: 0
    m_Value: 0
