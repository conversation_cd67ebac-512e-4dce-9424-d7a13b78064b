using UnityEngine;
using UnityEditor;
using UnityEditor.SceneManagement;
using System.Collections.Generic;
using System.Linq;
using UnityEngine.SceneManagement;
using System.IO;
using System.Text;
using System;

public class ShaderAnalyzer : EditorWindow
{
  private Vector2 scrollPosition;
  private Dictionary<Shader, ShaderUsageInfo> shaderUsage = new Dictionary<Shader, ShaderUsageInfo>();
  private bool showSceneDetails = true;
  private string searchFilter = "";
  private bool analyzeInactive = true;
  private bool isAnalyzing = false;
  private ShaderAnalysisStats currentStats = new ShaderAnalysisStats();
  private bool showMaterialAnalysis = true;
  private bool showBatchingDetails = true;
  private MaterialAnalysisStats materialStats = new MaterialAnalysisStats();

  [MenuItem("Tools/Shader Analyzer")]
  public static void ShowWindow()
  {
    GetWindow<ShaderAnalyzer>("Shader Analyzer");
  }

  private class ShaderFeatureInfo
  {
    public List<string> keywords = new List<string>();
    public List<string> localKeywords = new List<string>();
    public int passCount;
    public bool usesTessellation;
    public bool usesCompute;
    public bool usesGeometry;
    public int variantCount;
    public float estimatedSize;
    public List<string> enabledFeatures = new List<string>();
  }

  private class ShaderAnalysisStats
  {
    public int totalUniqueShaders;
    public int totalMaterials;
    public float totalEstimatedSize;
    public int totalVariants;
    public Dictionary<string, int> featureUsage = new Dictionary<string, int>();
  }

  private class ShaderUsageInfo
  {
    public int materialCount;
    public HashSet<string> scenePaths = new HashSet<string>();
    public Dictionary<string, int> materialsPerScene = new Dictionary<string, int>();
    public ShaderFeatureInfo featureInfo = new ShaderFeatureInfo();
  }

  private class MaterialAnalysisStats
  {
    public Dictionary<Shader, List<MaterialGroup>> materialGroups = new Dictionary<Shader, List<MaterialGroup>>();
    public int totalDrawCalls;
    public int potentialBatchSavings;
    public int instancedMaterialCount;
    public int nonInstancedMaterialCount;
    public Dictionary<int, int> renderQueueDistribution = new Dictionary<int, int>();
    public int uniquePropertyVariations;
  }

  private class MaterialGroup
  {
    public List<Material> materials = new List<Material>();
    public bool canBeBatched;
    public bool isInstanced;
    public int renderQueue;
    public string mainTextureName;
    public Dictionary<string, HashSet<string>> propertyVariations = new Dictionary<string, HashSet<string>>();
  }

  private void OnGUI()
  {
    GUILayout.Space(10);
    EditorGUILayout.BeginHorizontal();
    if (GUILayout.Button("Analyze Build Scenes", GUILayout.Height(30)))
    {
      AnalyzeBuildScenes();
    }

    if (GUILayout.Button("Export Analysis", GUILayout.Height(30)))
    {
      ExportAnalysis();
    }
    
    if (isAnalyzing)
    {
      if (GUILayout.Button("Cancel", GUILayout.Height(30)))
      {
        isAnalyzing = false;
      }
    }
    EditorGUILayout.EndHorizontal();

    GUILayout.Space(5);
    analyzeInactive = EditorGUILayout.Toggle("Include Inactive Objects", analyzeInactive);
    
    if (shaderUsage.Count > 0)
    {
      GUILayout.Space(10);
      EditorGUILayout.BeginVertical(EditorStyles.helpBox);
      EditorGUILayout.LabelField("Analysis Summary", EditorStyles.boldLabel);
      EditorGUILayout.LabelField($"Total Unique Shaders: {currentStats.totalUniqueShaders}");
      EditorGUILayout.LabelField($"Total Materials: {currentStats.totalMaterials}");
      EditorGUILayout.LabelField($"Total Shader Variants: {currentStats.totalVariants:N0}");
      EditorGUILayout.LabelField($"Estimated Total Size: {FormatSize(currentStats.totalEstimatedSize)}");
      
      if (currentStats.featureUsage.Count > 0)
      {
        EditorGUILayout.LabelField("Common Features:", EditorStyles.boldLabel);
        foreach (var feature in currentStats.featureUsage.OrderByDescending(x => x.Value).Take(5))
        {
          EditorGUILayout.LabelField($"- {feature.Key}: {feature.Value} shaders");
        }
      }
      EditorGUILayout.EndVertical();
    }

    GUILayout.Space(10);
    EditorGUILayout.LabelField("Filter Shaders:", EditorStyles.boldLabel);
    searchFilter = EditorGUILayout.TextField(searchFilter);

    if (shaderUsage.Count > 0)
    {
      GUILayout.Space(10);
      showSceneDetails = EditorGUILayout.Foldout(showSceneDetails, "Shader Usage Details");
      
      scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);
      
      foreach (var kvp in shaderUsage.OrderByDescending(x => x.Value.materialCount))
      {
        if (string.IsNullOrEmpty(searchFilter) || 
            kvp.Key.name.ToLower().Contains(searchFilter.ToLower()))
        {
          EditorGUILayout.BeginVertical(EditorStyles.helpBox);
          
          EditorGUILayout.LabelField($"Shader: {kvp.Key.name}", EditorStyles.boldLabel);
          EditorGUILayout.LabelField($"Total Materials: {kvp.Value.materialCount}");
          EditorGUILayout.LabelField($"Variants: {kvp.Value.featureInfo.variantCount:N0}");
          EditorGUILayout.LabelField($"Estimated Size: {FormatSize(kvp.Value.featureInfo.estimatedSize)}");
          
          if (kvp.Value.featureInfo.enabledFeatures.Count > 0)
          {
            EditorGUILayout.LabelField("Features:", EditorStyles.boldLabel);
            EditorGUI.indentLevel++;
            foreach (var feature in kvp.Value.featureInfo.enabledFeatures)
            {
              EditorGUILayout.LabelField($"- {feature}");
            }
            EditorGUI.indentLevel--;
          }
          
          if (showSceneDetails)
          {
            EditorGUI.indentLevel++;
            EditorGUILayout.LabelField("Used in Scenes:", EditorStyles.boldLabel);
            
            foreach (var scene in kvp.Value.materialsPerScene)
            {
              EditorGUILayout.LabelField($"- {Path.GetFileNameWithoutExtension(scene.Key)}: {scene.Value} materials");
            }
            EditorGUI.indentLevel--;
          }
          
          EditorGUILayout.EndVertical();
          GUILayout.Space(5);
        }
      }
      
      EditorGUILayout.EndScrollView();
    }

    if (shaderUsage.Count > 0)
    {
      GUILayout.Space(10);
      EditorGUILayout.BeginVertical(EditorStyles.helpBox);
      showMaterialAnalysis = EditorGUILayout.Foldout(showMaterialAnalysis, "Material Optimization Analysis");
      
      if (showMaterialAnalysis)
      {
        EditorGUI.indentLevel++;
        
        // Draw Call Analysis
        EditorGUILayout.LabelField("Draw Call Analysis:", EditorStyles.boldLabel);
        EditorGUILayout.LabelField($"Estimated Draw Calls: {materialStats.totalDrawCalls}");
        EditorGUILayout.LabelField($"Potential Batch Savings: {materialStats.potentialBatchSavings} draw calls");
        
        // Instancing Analysis
        EditorGUILayout.Space();
        EditorGUILayout.LabelField("GPU Instancing:", EditorStyles.boldLabel);
        float instancedPercentage = materialStats.instancedMaterialCount + materialStats.nonInstancedMaterialCount > 0 
          ? (float)materialStats.instancedMaterialCount / (materialStats.instancedMaterialCount + materialStats.nonInstancedMaterialCount) * 100
          : 0;
        EditorGUILayout.LabelField($"Materials with Instancing: {instancedPercentage:F1}% ({materialStats.instancedMaterialCount} materials)");
        
        if (materialStats.nonInstancedMaterialCount > 0)
        {
          EditorGUILayout.HelpBox($"{materialStats.nonInstancedMaterialCount} materials could benefit from GPU Instancing", 
            materialStats.nonInstancedMaterialCount > 100 ? MessageType.Warning : MessageType.Info);
        }
        
        // Render Queue Distribution
        EditorGUILayout.Space();
        EditorGUILayout.LabelField("Render Queue Distribution:", EditorStyles.boldLabel);
        foreach (var queue in materialStats.renderQueueDistribution.OrderBy(x => x.Key))
        {
          string queueName = GetRenderQueueName(queue.Key);
          EditorGUILayout.LabelField($"{queueName}: {queue.Value} materials");
        }
        
        // Property Variations
        EditorGUILayout.Space();
        EditorGUILayout.LabelField("Property Variations:", EditorStyles.boldLabel);
        EditorGUILayout.LabelField($"Unique Property Combinations: {materialStats.uniquePropertyVariations}");
        
        // Batching Details
        showBatchingDetails = EditorGUILayout.Foldout(showBatchingDetails, "Batching Opportunities");
        if (showBatchingDetails)
        {
          foreach (var shaderGroup in materialStats.materialGroups)
          {
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            EditorGUILayout.LabelField($"Shader: {shaderGroup.Key.name}", EditorStyles.boldLabel);
            
            foreach (var group in shaderGroup.Value)
            {
              EditorGUILayout.BeginHorizontal();
              EditorGUILayout.LabelField($"Group of {group.materials.Count} materials:");
              EditorGUILayout.LabelField(group.canBeBatched ? "Can be batched ✓" : "Cannot be batched ✗");
              EditorGUILayout.EndHorizontal();
              
              EditorGUI.indentLevel++;
              EditorGUILayout.LabelField($"Main Texture: {group.mainTextureName}");
              EditorGUILayout.LabelField($"Render Queue: {GetRenderQueueName(group.renderQueue)}");
              
              if (group.propertyVariations.Count > 0)
              {
                EditorGUILayout.LabelField("Property Variations:");
                foreach (var prop in group.propertyVariations)
                {
                  if (prop.Value.Count > 1)
                  {
                    EditorGUILayout.LabelField($"- {prop.Key}: {prop.Value.Count} different values");
                  }
                }
              }
              EditorGUI.indentLevel--;
            }
            EditorGUILayout.EndVertical();
          }
        }
        
        EditorGUI.indentLevel--;
      }
      EditorGUILayout.EndVertical();
    }
  }

  private string FormatSize(float sizeInKB)
  {
    if (sizeInKB >= 1024)
    {
      return $"{sizeInKB / 1024:F2} MB";
    }
    return $"{sizeInKB:F2} KB";
  }

  private void ExportAnalysis()
  {
    var exportPath = EditorUtility.SaveFilePanel(
      "Export Shader Analysis",
      Application.dataPath,
      $"ShaderAnalysis_{DateTime.Now:yyyyMMdd_HHmmss}",
      "json"
    );

    if (string.IsNullOrEmpty(exportPath)) return;

    var export = new Dictionary<string, object>
    {
      ["timestamp"] = DateTime.Now.ToString("o"),
      ["stats"] = new Dictionary<string, object>
      {
        ["totalUniqueShaders"] = currentStats.totalUniqueShaders,
        ["totalMaterials"] = currentStats.totalMaterials,
        ["totalVariants"] = currentStats.totalVariants,
        ["totalEstimatedSize"] = currentStats.totalEstimatedSize,
        ["featureUsage"] = currentStats.featureUsage
      },
      ["shaders"] = shaderUsage.ToDictionary(
        kvp => kvp.Key.name,
        kvp => new Dictionary<string, object>
        {
          ["materialCount"] = kvp.Value.materialCount,
          ["scenes"] = kvp.Value.materialsPerScene,
          ["features"] = new Dictionary<string, object>
          {
            ["keywords"] = kvp.Value.featureInfo.keywords,
            ["localKeywords"] = kvp.Value.featureInfo.localKeywords,
            ["passCount"] = kvp.Value.featureInfo.passCount,
            ["variantCount"] = kvp.Value.featureInfo.variantCount,
            ["estimatedSize"] = kvp.Value.featureInfo.estimatedSize,
            ["enabledFeatures"] = kvp.Value.featureInfo.enabledFeatures
          }
        }
      )
    };

    File.WriteAllText(exportPath, JsonUtility.ToJson(export, true));
    EditorUtility.RevealInFinder(exportPath);
  }

  private void AnalyzeBuildScenes()
  {
    isAnalyzing = true;
    shaderUsage.Clear();
    currentStats = new ShaderAnalysisStats();
    materialStats = new MaterialAnalysisStats();
    
    string currentScenePath = SceneManager.GetActiveScene().path;
    var scenes = EditorBuildSettings.scenes;
    
    Dictionary<Shader, Dictionary<int, List<Material>>> materialsByShaderAndQueue = new Dictionary<Shader, Dictionary<int, List<Material>>>();
    
    foreach (var scene in scenes)
    {
      if (!isAnalyzing) break;
      if (!scene.enabled) continue;
      
      EditorUtility.DisplayProgressBar("Analyzing Scenes", 
        $"Processing {Path.GetFileNameWithoutExtension(scene.path)}", 
        (float)scenes.ToList().IndexOf(scene) / scenes.Length);
      
      Scene loadedScene = EditorSceneManager.OpenScene(scene.path, OpenSceneMode.Single);
      var renderers = Resources.FindObjectsOfTypeAll<Renderer>();
      
      foreach (var renderer in renderers)
      {
        if (!analyzeInactive && !renderer.gameObject.activeInHierarchy) continue;
        
        var materials = renderer.sharedMaterials;
        foreach (var material in materials)
        {
          if (material != null && material.shader != null)
          {
            if (!shaderUsage.ContainsKey(material.shader))
            {
              shaderUsage[material.shader] = new ShaderUsageInfo();
              AnalyzeShaderFeatures(material.shader, shaderUsage[material.shader].featureInfo);
              currentStats.totalUniqueShaders++;
            }
            
            shaderUsage[material.shader].materialCount++;
            currentStats.totalMaterials++;
            shaderUsage[material.shader].scenePaths.Add(scene.path);
            
            if (!shaderUsage[material.shader].materialsPerScene.ContainsKey(scene.path))
            {
              shaderUsage[material.shader].materialsPerScene[scene.path] = 0;
            }
            shaderUsage[material.shader].materialsPerScene[scene.path]++;

            // New material analysis code
            if (!materialsByShaderAndQueue.ContainsKey(material.shader))
            {
              materialsByShaderAndQueue[material.shader] = new Dictionary<int, List<Material>>();
            }
            
            var queueDict = materialsByShaderAndQueue[material.shader];
            if (!queueDict.ContainsKey(material.renderQueue))
            {
              queueDict[material.renderQueue] = new List<Material>();
            }
            
            queueDict[material.renderQueue].Add(material);
            
            // Track render queue distribution
            if (!materialStats.renderQueueDistribution.ContainsKey(material.renderQueue))
            {
              materialStats.renderQueueDistribution[material.renderQueue] = 0;
            }
            materialStats.renderQueueDistribution[material.renderQueue]++;
            
            // Track instancing
            if (material.enableInstancing)
            {
              materialStats.instancedMaterialCount++;
            }
            else
            {
              materialStats.nonInstancedMaterialCount++;
            }
          }
        }
      }
    }
    
    // Analyze material groups for batching potential
    foreach (var shaderGroup in materialsByShaderAndQueue)
    {
      var shader = shaderGroup.Key;
      var materialGroups = new List<MaterialGroup>();
      
      foreach (var queueGroup in shaderGroup.Value)
      {
        var materials = queueGroup.Value;
        var groupedByProperties = GroupMaterialsByProperties(materials);
        
        foreach (var group in groupedByProperties)
        {
          var materialGroup = new MaterialGroup
          {
            materials = group,
            renderQueue = queueGroup.Key,
            isInstanced = group.Count > 0 ? group[0].enableInstancing : false,
            canBeBatched = CanBeBatched(group),
            mainTextureName = group.Count > 0 ? GetMainTextureName(group[0]) : "None"
          };
          
          materialGroup.propertyVariations = AnalyzePropertyVariations(group);
          materialGroups.Add(materialGroup);
        }
      }
      
      materialStats.materialGroups[shader] = materialGroups;
    }
    
    // Calculate draw call estimates
    CalculateDrawCallEstimates();
    
    // Calculate unique property variations
    materialStats.uniquePropertyVariations = materialStats.materialGroups.Sum(
      x => x.Value.Sum(g => g.propertyVariations.Sum(p => p.Value.Count))
    );
    
    // Restore the original scene
    EditorSceneManager.OpenScene(currentScenePath);
    EditorUtility.ClearProgressBar();
    isAnalyzing = false;
  }

  private List<List<Material>> GroupMaterialsByProperties(List<Material> materials)
  {
    // Group materials that share the same main texture and basic properties
    return materials.GroupBy(m => new 
    {
      MainTex = GetMainTextureName(m),
      RenderQueue = m.renderQueue,
      IsInstanced = m.enableInstancing
    })
    .Select(g => g.ToList())
    .ToList();
  }

  private string GetMainTextureName(Material material)
  {
    var mainTex = material.GetTexture("_MainTex") ?? material.GetTexture("_BaseMap");
    return mainTex != null ? mainTex.name : "None";
  }

  private bool CanBeBatched(List<Material> materials)
  {
    if (materials.Count <= 1) return false;
    
    var first = materials[0];
    var shader = first.shader;
    
    // Check if materials share core properties
    return materials.All(m => 
      m.enableInstancing == first.enableInstancing &&
      m.renderQueue == first.renderQueue &&
      GetMainTextureName(m) == GetMainTextureName(first)
    );
  }

  private Dictionary<string, HashSet<string>> AnalyzePropertyVariations(List<Material> materials)
  {
    var variations = new Dictionary<string, HashSet<string>>();
    var shader = materials[0].shader;
    
    int propertyCount = ShaderUtil.GetPropertyCount(shader);
    for (int i = 0; i < propertyCount; i++)
    {
      string propName = ShaderUtil.GetPropertyName(shader, i);
      var propType = ShaderUtil.GetPropertyType(shader, i);
      
      variations[propName] = new HashSet<string>();
      
      foreach (var material in materials)
      {
        string value = GetPropertyValueString(material, propName, propType);
        variations[propName].Add(value);
      }
    }
    
    return variations;
  }

  private string GetPropertyValueString(Material material, string propertyName, ShaderUtil.ShaderPropertyType propertyType)
  {
    switch (propertyType)
    {
      case ShaderUtil.ShaderPropertyType.Color:
        return material.GetColor(propertyName).ToString();
      case ShaderUtil.ShaderPropertyType.Vector:
        return material.GetVector(propertyName).ToString();
      case ShaderUtil.ShaderPropertyType.Float:
      case ShaderUtil.ShaderPropertyType.Range:
        return material.GetFloat(propertyName).ToString("F3");
      case ShaderUtil.ShaderPropertyType.TexEnv:
        var tex = material.GetTexture(propertyName);
        return tex != null ? tex.name : "None";
      default:
        return "Unknown";
    }
  }

  private void CalculateDrawCallEstimates()
  {
    materialStats.totalDrawCalls = 0;
    materialStats.potentialBatchSavings = 0;
    
    foreach (var shaderGroup in materialStats.materialGroups)
    {
      foreach (var group in shaderGroup.Value)
      {
        // Each non-instanced, non-batched group of materials potentially causes N draw calls
        if (!group.isInstanced && !group.canBeBatched)
        {
          materialStats.totalDrawCalls += group.materials.Count;
          // If they could be batched, we'd save (N-1) draw calls
          if (group.materials.Count > 1)
          {
            materialStats.potentialBatchSavings += group.materials.Count - 1;
          }
        }
        else
        {
          // Instanced or batched materials typically result in one draw call per group
          materialStats.totalDrawCalls++;
        }
      }
    }
  }

  private string GetRenderQueueName(int queue)
  {
    if (queue < 2000) return "Background";
    if (queue < 2450) return "Geometry";
    if (queue < 2500) return "Alpha Test";
    if (queue < 3000) return "Geometry Last";
    if (queue < 4000) return "Transparent";
    return "Overlay";
  }

  private void AnalyzeShaderFeatures(Shader shader, ShaderFeatureInfo info)
  {
    // Get keywords from shader properties
    var serializedShader = new SerializedObject(shader);
    var keywords = new List<string>();
    
    // Analyze shader properties
    int propertyCount = ShaderUtil.GetPropertyCount(shader);
    for (int i = 0; i < propertyCount; i++)
    {
      string propName = ShaderUtil.GetPropertyName(shader, i);
      ShaderUtil.ShaderPropertyType propType = ShaderUtil.GetPropertyType(shader, i);
      
      // Add to keywords if it's a keyword-related property
      if (propName.Contains("_KEYWORD_") || propName.Contains("_ENABLE_"))
      {
        keywords.Add(propName);
      }
    }
    
    info.keywords = keywords;
    info.localKeywords = new List<string>(); // Local keywords require runtime material instance
    
    // Analyze shader features based on name and properties
    info.usesTessellation = shader.name.Contains("Tessellation") || keywords.Any(k => k.Contains("TESSELLATION"));
    info.usesCompute = shader.name.Contains("Compute") || keywords.Any(k => k.Contains("COMPUTE"));
    info.usesGeometry = shader.name.Contains("Geometry") || keywords.Any(k => k.Contains("GEOMETRY"));
    
    // Estimate pass count based on common shader types
    info.passCount = EstimatePassCount(shader);
    
    // Calculate variants
    info.variantCount = CalculateVariantCount(info);
    
    // Estimate size
    info.estimatedSize = EstimateShaderSize(info);
    
    // Analyze enabled features
    AnalyzeEnabledFeatures(shader, info);
    
    // Update feature usage stats
    foreach (var feature in info.enabledFeatures)
    {
      if (!currentStats.featureUsage.ContainsKey(feature))
      {
        currentStats.featureUsage[feature] = 0;
      }
      currentStats.featureUsage[feature]++;
    }
  }

  private int EstimatePassCount(Shader shader)
  {
    // Estimate pass count based on shader type and name
    string name = shader.name.ToLower();
    
    if (name.Contains("transparent") || name.Contains("translucent"))
      return 2; // Forward base + additive
      
    if (name.Contains("particle") || name.Contains("vfx"))
      return 3; // Multiple blend modes
      
    if (name.Contains("skybox"))
      return 1;
      
    if (name.Contains("ui") || name.Contains("sprite"))
      return 1;
      
    // Default for standard shaders (base pass + shadow pass)
    return 2;
  }

  private int CalculateVariantCount(ShaderFeatureInfo info)
  {
    // Basic variant calculation (this is a simplified estimation)
    int keywordCount = info.keywords.Count + info.localKeywords.Count;
    return (int)Math.Pow(2, keywordCount) * info.passCount;
  }

  private float EstimateShaderSize(ShaderFeatureInfo info)
  {
    // Base size per pass (in KB)
    float baseSize = 5f;
    
    // Add size for features
    float featureSize = info.usesTessellation ? 20f : 0f;
    featureSize += info.usesCompute ? 15f : 0f;
    featureSize += info.usesGeometry ? 10f : 0f;
    
    // Add size for keywords
    float keywordSize = (info.keywords.Count + info.localKeywords.Count) * 0.5f;
    
    return (baseSize * info.passCount) + featureSize + keywordSize;
  }

  private void AnalyzeEnabledFeatures(Shader shader, ShaderFeatureInfo info)
  {
    info.enabledFeatures.Clear();
    
    // Check for common features
    if (info.usesTessellation)
      info.enabledFeatures.Add("Tessellation");
      
    if (info.usesCompute)
      info.enabledFeatures.Add("Compute");
      
    if (info.usesGeometry)
      info.enabledFeatures.Add("Geometry");
    
    // Check for rendering features
    if (info.keywords.Any(k => k.Contains("SHADOWS")))
      info.enabledFeatures.Add("Shadow Casting");
      
    if (info.keywords.Any(k => k.Contains("NORMAL")))
      info.enabledFeatures.Add("Normal Mapping");
      
    if (info.keywords.Any(k => k.Contains("PARALLAX")))
      info.enabledFeatures.Add("Parallax Mapping");
  }
} 