﻿
// =================================	
// Namespaces.
// =================================

using UnityEngine;

// =================================	
// Define namespace.
// =================================

namespace MirzaBeig
{

    namespace Demos
    {

        namespace TheLastParticle
        {

            // =================================	
            // Classes.
            // =================================

            //[ExecuteInEditMode]
            [System.Serializable]

            public class DestroyOnAudioStop : MonoBehaviour
            {
                // =================================	
                // Nested classes and structures.
                // =================================

                // ...

                // =================================	
                // Variables.
                // =================================

                // ...

                AudioSource audioSource;

                // =================================	
                // Functions.
                // =================================

                // ...

                void Start()
                {
                    audioSource = GetComponent<AudioSource>();
                }

                // ...

                void Update()
                {
                    if (!audioSource.isPlaying)
                    {
                        Destroy(gameObject);
                    }
                }

                // =================================	
                // End functions.
                // =================================

            }

            // =================================	
            // End namespace.
            // =================================

        }

    }

}

// =================================	
// --END-- //
// =================================
