%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-7343470517109139547
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0353a89b1f911e48b9e16bdc9f2e058, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 9
--- !u!28 &-1563147103653709517
Texture2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: z___Gradient_Color_MapTex{"mode":1,"colorKeys":[{"color":{"r":0.03921568766236305,"g":0.2078431397676468,"b":0.25882354378700259,"a":1.0},"time":0.1250019073486328},{"color":{"r":0.4313725531101227,"g":0.1411764770746231,"b":0.019607843831181527,"a":1.0},"time":0.2500038146972656},{"color":{"r":0.6901960968971252,"g":0.4941176474094391,"b":0.03529411926865578,"a":1.0},"time":0.37500572204589846},{"color":{"r":0.3803921639919281,"g":0.5490196347236633,"b":0.3294117748737335,"a":1.0},"time":0.5000076293945313},{"color":{"r":0.47058823704719546,"g":0.5843137502670288,"b":0.6431372761726379,"a":1.0},"time":0.6249942779541016},{"color":{"r":0.4431372582912445,"g":0.32549020648002627,"b":0.3607843220233917,"a":1.0},"time":0.7499961853027344},{"color":{"r":0.4941176474094391,"g":0.41960784792900088,"b":0.4431372582912445,"a":1.0},"time":0.8749980926513672},{"color":{"r":0.7764706015586853,"g":0.8039215803146362,"b":0.7803921699523926,"a":1.0},"time":1.0}],"alphaKeys":[{"alpha":1.0,"time":0.0},{"alpha":1.0,"time":1.0}]}
  m_ImageContentsHash:
    serializedVersion: 2
    Hash: 00000000000000000000000000000000
  m_IsAlphaChannelOptional: 0
  serializedVersion: 3
  m_Width: 256
  m_Height: 1
  m_CompleteImageSize: 1024
  m_MipsStripped: 0
  m_TextureFormat: 5
  m_MipCount: 1
  m_IsReadable: 1
  m_IsPreProcessed: 0
  m_IgnoreMipmapLimit: 0
  m_MipmapLimitGroupName: 
  m_StreamingMipmaps: 0
  m_StreamingMipmapsPriority: 0
  m_VTOnly: 0
  m_AlphaIsTransparency: 0
  m_ImageCount: 1
  m_TextureDimension: 2
  m_TextureSettings:
    serializedVersion: 2
    m_FilterMode: 0
    m_Aniso: 1
    m_MipBias: 0
    m_WrapU: 1
    m_WrapV: 1
    m_WrapW: 1
  m_LightmapFormat: 0
  m_ColorSpace: 1
  m_PlatformBlob: 
  image data: 1024
  _typelessdata: ff0a3542ff0a3542ff0a3542ff0a3542ff0a3542ff0a3542ff0a3542ff0a3542ff0a3542ff0a3542ff0a3542ff0a3542ff0a3542ff0a3542ff0a3542ff0a3542ff0a3542ff0a3542ff0a3542ff0a3542ff0a3542ff0a3542ff0a3542ff0a3542ff0a3542ff0a3542ff0a3542ff0a3542ff0a3542ff0a3542ff0a3542ff0a3542ff6e2405ff6e2405ff6e2405ff6e2405ff6e2405ff6e2405ff6e2405ff6e2405ff6e2405ff6e2405ff6e2405ff6e2405ff6e2405ff6e2405ff6e2405ff6e2405ff6e2405ff6e2405ff6e2405ff6e2405ff6e2405ff6e2405ff6e2405ff6e2405ff6e2405ff6e2405ff6e2405ff6e2405ff6e2405ff6e2405ff6e2405ff6e2405ffb07e09ffb07e09ffb07e09ffb07e09ffb07e09ffb07e09ffb07e09ffb07e09ffb07e09ffb07e09ffb07e09ffb07e09ffb07e09ffb07e09ffb07e09ffb07e09ffb07e09ffb07e09ffb07e09ffb07e09ffb07e09ffb07e09ffb07e09ffb07e09ffb07e09ffb07e09ffb07e09ffb07e09ffb07e09ffb07e09ffb07e09ffb07e09ff618c54ff618c54ff618c54ff618c54ff618c54ff618c54ff618c54ff618c54ff618c54ff618c54ff618c54ff618c54ff618c54ff618c54ff618c54ff618c54ff618c54ff618c54ff618c54ff618c54ff618c54ff618c54ff618c54ff618c54ff618c54ff618c54ff618c54ff618c54ff618c54ff618c54ff618c54ff618c54ff7895a4ff7895a4ff7895a4ff7895a4ff7895a4ff7895a4ff7895a4ff7895a4ff7895a4ff7895a4ff7895a4ff7895a4ff7895a4ff7895a4ff7895a4ff7895a4ff7895a4ff7895a4ff7895a4ff7895a4ff7895a4ff7895a4ff7895a4ff7895a4ff7895a4ff7895a4ff7895a4ff7895a4ff7895a4ff7895a4ff7895a4ff7895a4ff71535cff71535cff71535cff71535cff71535cff71535cff71535cff71535cff71535cff71535cff71535cff71535cff71535cff71535cff71535cff71535cff71535cff71535cff71535cff71535cff71535cff71535cff71535cff71535cff71535cff71535cff71535cff71535cff71535cff71535cff71535cff71535cff7e6b71ff7e6b71ff7e6b71ff7e6b71ff7e6b71ff7e6b71ff7e6b71ff7e6b71ff7e6b71ff7e6b71ff7e6b71ff7e6b71ff7e6b71ff7e6b71ff7e6b71ff7e6b71ff7e6b71ff7e6b71ff7e6b71ff7e6b71ff7e6b71ff7e6b71ff7e6b71ff7e6b71ff7e6b71ff7e6b71ff7e6b71ff7e6b71ff7e6b71ff7e6b71ff7e6b71ff7e6b71ffc6cdc7ffc6cdc7ffc6cdc7ffc6cdc7ffc6cdc7ffc6cdc7ffc6cdc7ffc6cdc7ffc6cdc7ffc6cdc7ffc6cdc7ffc6cdc7ffc6cdc7ffc6cdc7ffc6cdc7ffc6cdc7ffc6cdc7ffc6cdc7ffc6cdc7ffc6cdc7ffc6cdc7ffc6cdc7ffc6cdc7ffc6cdc7ffc6cdc7ffc6cdc7ffc6cdc7ffc6cdc7ffc6cdc7ffc6cdc7ffc6cdc7ffc6cdc7
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Pixel - Color Remap
  m_Shader: {fileID: -6465566751694194690, guid: 15413806d5d5193458fa0600f7dc97b7, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords: []
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses:
  - MOTIONVECTORS
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BaseMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - __Gradient_Color_Map:
        m_Texture: {fileID: -1563147103653709517}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AlphaClip: 0
    - _Blend: 0
    - _BumpScale: 1
    - _ClearCoatMask: 0
    - _ClearCoatSmoothness: 0
    - _Cull: 2
    - _Cutoff: 0.5
    - _DepthSensitivity: 0
    - _DetailAlbedoMapScale: 1
    - _DetailNormalMapScale: 1
    - _DstBlend: 0
    - _EdgeThickness: 0
    - _EnvironmentReflections: 1
    - _GlossMapScale: 0
    - _Glossiness: 0
    - _GlossyReflections: 0
    - _Metallic: 0
    - _NormalsSensitivity: 0
    - _OcclusionStrength: 1
    - _Opacity: 1
    - _Outline_Sample_Point: 0
    - _PREVIEW_LUMINANCE: 0
    - _Parallax: 0.005
    - _QueueControl: 0
    - _QueueOffset: 0
    - _ReceiveShadows: 1
    - _Smoothness: 0.5
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _Surface: 0
    - _WorkflowMode: 1
    - _ZWrite: 1
    - __d_5_Outline: 0
    - __f_Outline: 0
    - __h_12_Sensitivity: 0
    - __h_Sensitivity: 0
    m_Colors:
    - _BaseColor: {r: 1, g: 1, b: 1, a: 1}
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _SpecColor: {r: 0.19999996, g: 0.19999996, b: 0.19999996, a: 1}
    - __MinMax_Luminance_Range: {r: 0, g: 1, b: 0, a: 0}
  m_BuildTextureStacks: []
  m_AllowLocking: 1
