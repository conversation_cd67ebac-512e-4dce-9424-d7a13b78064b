# Enhanced Pixel Trailing System for Flux Datamosh

## Overview

The Flux datamosh effect has been completely enhanced to achieve authentic video compression-style pixel trailing instead of blocky artifacts. The new system creates smooth, organic pixel flow that follows motion vectors and camera movement, producing realistic datamosh behavior where pixels "stick" to moving objects.

## Key Improvements

### **Before: Blocky Artifacts**
- Block-based motion vector sampling created chunky, discrete artifacts
- Binary on/off reprojection decisions caused harsh transitions  
- Limited motion response with hard-coded limits
- No temporal persistence or organic flow

### **After: Smooth Pixel Trailing**
- Per-pixel motion sampling with smooth interpolation
- Gradual blending based on motion intensity
- Dramatically increased motion sensitivity
- Temporal accumulation and motion persistence
- Organic flow patterns that spread from motion centers

## New Pixel Flow & Trailing Parameters

### **Pixel Flow Intensity** (0-10)
- **Default**: 1.0
- **Purpose**: Controls how strongly pixels follow motion vectors
- **Effect**: Higher values create more dramatic pixel flow and trailing
- **Usage**: 
  - 1-3: Subtle trailing effects
  - 4-7: Moderate pixel flow
  - 8-10: Extreme trailing and smearing

### **Trail Smoothness** (0-1)
- **Default**: 0.5
- **Purpose**: Blends between blocky (0) and smooth (1) trailing effects
- **Effect**: Higher values reduce blockiness and create fluid motion
- **Technical**: Controls interpolation between block-based and per-pixel sampling
- **Usage**:
  - 0-0.3: Maintains some blockiness for retro effect
  - 0.4-0.7: Balanced smooth trailing
  - 0.8-1.0: Maximum smoothness, completely fluid

### **Motion Persistence** (0-1)
- **Default**: 0.3
- **Purpose**: How long pixels "stick" to moving objects
- **Effect**: Creates temporal trails that follow object motion
- **Technical**: Samples multiple points along motion vectors for persistence
- **Usage**:
  - 0-0.2: Short, immediate trails
  - 0.3-0.6: Medium persistence trails
  - 0.7-1.0: Long, sticky trails that follow objects

### **Flow Gradient** (0-5)
- **Default**: 1.0
- **Purpose**: Creates organic flow patterns around motion centers
- **Effect**: Makes trailing spread outward from areas of high motion
- **Technical**: Uses distance fields to create gradient-based flow
- **Usage**:
  - 0-1: Localized trailing
  - 2-3: Moderate outward flow
  - 4-5: Wide-spreading organic patterns

### **Temporal Accumulation** (0-1)
- **Default**: 0.2
- **Purpose**: Builds up trailing effects over multiple frames
- **Effect**: Creates cumulative motion blur and long-term persistence
- **Technical**: Accumulates motion history across frames
- **Usage**:
  - 0-0.1: Frame-by-frame trailing
  - 0.2-0.5: Medium temporal buildup
  - 0.6-1.0: Strong cumulative effects

## Technical Implementation

### **Enhanced Motion Vector Processing**
```hlsl
// Blend between block-based and per-pixel sampling
float2 motionSampleUV = lerp(snappedUV, pixelUV, _TrailSmoothness);

// Pixel Flow Intensity amplification
enhancedMotionVector *= (1.0 + _PixelFlowIntensity);

// Flow Gradient for organic patterns
float gradientInfluence = 1.0 + _FlowGradient * (1.0 - distanceFromMotion) * motionMagnitude;
```

### **Motion Persistence System**
```hlsl
// Sample multiple points along motion vector
float3 persistentSample1 = SAMPLE(_PrevScreen, uv - motionVector * 0.5);
float3 persistentSample2 = SAMPLE(_PrevScreen, uv - motionVector * 1.5);

// Blend for sticky pixel behavior
pull = lerp(pull, (persistentSample1 + persistentSample2) * 0.5, persistenceStrength);
```

### **Smooth Reprojection Logic**
```hlsl
// Dramatically increased motion influence limits
motionInfluence = min(motionInfluence, 2.0); // Was 0.7
cameraMotionContribution = min(cameraMotionContribution, 1.5); // Was 0.5

// Smooth blending instead of binary decisions
float smoothBlend = saturate(blendFactor * _TrailSmoothness);
float3 smoothResult = lerp(col, pull, smoothBlend);
```

## Usage Recommendations

### **Classic Video Compression Datamosh**
```
Pixel Flow Intensity: 3.0
Trail Smoothness: 0.8
Motion Persistence: 0.4
Flow Gradient: 1.5
Temporal Accumulation: 0.3
Camera Motion Amplification: 4.0
Length Influence: 3.0
```

### **Extreme Pixel Flow Effects**
```
Pixel Flow Intensity: 8.0
Trail Smoothness: 1.0
Motion Persistence: 0.7
Flow Gradient: 3.0
Temporal Accumulation: 0.6
Camera Motion Amplification: 8.0
Length Influence: 5.0
```

### **Subtle Organic Trailing**
```
Pixel Flow Intensity: 1.5
Trail Smoothness: 0.6
Motion Persistence: 0.2
Flow Gradient: 0.8
Temporal Accumulation: 0.1
Camera Motion Amplification: 2.0
Length Influence: 1.5
```

### **Retro Blocky with Some Smoothness**
```
Pixel Flow Intensity: 2.0
Trail Smoothness: 0.3
Motion Persistence: 0.1
Flow Gradient: 0.5
Temporal Accumulation: 0.0
Camera Motion Amplification: 3.0
Length Influence: 2.0
```

## Integration with Existing Features

### **Enhanced Camera Motion Response**
- Camera motion now has **dramatically stronger impact** on trailing
- Motion influence limits increased from 0.7 to 2.0
- Camera motion contribution increased from 0.5 to 1.5
- Works seamlessly with new pixel flow parameters

### **Improved Length Influence**
- Now affects **enhanced motion vectors** instead of raw motion
- Creates much more pronounced motion-based smearing
- Combines with Pixel Flow Intensity for compound effects

### **Advanced Datamoshing Compatibility**
- All advanced features (Error Accumulation, Motion Vector Corruption, etc.) work with new trailing
- Enhanced blending improves quality of all corruption effects
- Temporal Accumulation enhances Error Accumulation behavior

## Performance Considerations

### **Computational Cost**
- **Low Impact**: Trail Smoothness, Pixel Flow Intensity
- **Medium Impact**: Motion Persistence, Flow Gradient  
- **Higher Impact**: Temporal Accumulation (multiple frame samples)

### **Optimization Tips**
- Start with moderate settings and increase gradually
- Use Trail Smoothness 0.5-0.8 for best quality/performance balance
- Temporal Accumulation > 0.5 may impact performance on lower-end hardware

## Troubleshooting

### **Issue**: Still seeing blocky artifacts
**Solution**: Increase Trail Smoothness to 0.7+ and Pixel Flow Intensity to 2.0+

### **Issue**: Trailing effects too subtle
**Solution**: 
- Increase Pixel Flow Intensity (3.0+)
- Increase Camera Motion Amplification (5.0+)
- Increase Length Influence (3.0+)
- Set Trail Smoothness to 0.8+

### **Issue**: Trailing too extreme/overwhelming
**Solution**:
- Reduce Pixel Flow Intensity (1.0-2.0)
- Reduce Motion Persistence (0.1-0.3)
- Reduce Temporal Accumulation (0.0-0.2)

### **Issue**: Not enough camera motion response
**Solution**:
- Increase Camera Motion Amplification (5.0+)
- Decrease Camera Motion Threshold (0.001)
- Increase Camera Motion Influence (4.0+)
- Increase Pixel Flow Intensity (3.0+)

### **Issue**: Trails don't follow objects smoothly
**Solution**:
- Increase Motion Persistence (0.4-0.7)
- Increase Flow Gradient (2.0-4.0)
- Ensure Trail Smoothness is 0.6+

## Success Criteria

✅ **Smooth pixel trailing instead of blocky artifacts**
✅ **Dramatic camera motion response with visible trailing**
✅ **Length Influence creates pronounced motion-based smearing**
✅ **Pixels visibly "stick" to and follow moving objects**
✅ **Organic, fluid motion that resembles classic video compression datamoshing**
✅ **Adjustable intensity and smoothness for different visual styles**
✅ **Full integration with existing Flux features and Volume system**
