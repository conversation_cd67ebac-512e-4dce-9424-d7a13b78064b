%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: RChromeSimple
  m_Shader: {fileID: 4800000, guid: eadaa88eeab346c44abaf65b4433f3db, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords: []
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 1
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses:
  - MOTIONVECTORS
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BaseMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AddPrecomputedVelocity: 0
    - _AlphaClip: 0
    - _AlphaToMask: 0
    - _BCurveIntensity: 1.306
    - _Blend: 0
    - _BlendModePreserveSpecular: 1
    - _BumpScale: 1
    - _ChromaticAberration: 0.015
    - _ClearCoatMask: 0
    - _ClearCoatSmoothness: 0
    - _ColorBalance: 0.541
    - _ColorBlend: 1.53
    - _ColorMix: 1
    - _ColorShift: 0.395
    - _Contrast: 3.56
    - _Cull: 2
    - _Cutoff: 0.5
    - _DetailAlbedoMapScale: 1
    - _DetailNormalMapScale: 1
    - _DstBlend: 0
    - _DstBlendAlpha: 0
    - _EdgeBoost: 1.541
    - _EnvironmentReflections: 1
    - _FresnelIntensity: 1.59
    - _FresnelPower: 2.6
    - _GCurveIntensity: 1.051
    - _GlossMapScale: 0
    - _Glossiness: 0.5
    - _GlossyReflections: 0
    - _HighThreshold: 0.8
    - _Metallic: 0.301
    - _MidThreshold: 0.3
    - _Multiplier: 1.474
    - _OcclusionStrength: 1
    - _Parallax: 0.005
    - _QueueOffset: 0
    - _RCurveIntensity: 0.722
    - _RampEnd: 0.786
    - _RampStart: 0.284
    - _ReceiveShadows: 1
    - _ReflectionIntensity: 1.466
    - _ReflectionMultiplier: 1.2
    - _ReflectionStrength: 3.38
    - _Rotation: 180
    - _Smoothness: 0.748
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SpecularIntensity: 0.65
    - _SrcBlend: 1
    - _SrcBlendAlpha: 1
    - _Surface: 0
    - _Threshold: 0.009
    - _TransitionSharpness: 50
    - _TransitionThreshold: 0.5
    - _WorkflowMode: 1
    - _ZWrite: 1
    m_Colors:
    - _BaseColor: {r: 1, g: 0.7122642, b: 0.9291108, a: 1}
    - _ChromeColor: {r: 2, g: 0.3, b: 1.5, a: 1}
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _Color1: {r: 1, g: 1, b: 1, a: 1}
    - _Color2: {r: 1, g: 0, b: 0.020100594, a: 1}
    - _Color3: {r: 1, g: 0, b: 0.22340441, a: 1}
    - _ColorA: {r: 0, g: 0.09455051, b: 0.6509434, a: 1}
    - _ColorB: {r: 0.9150943, g: 0, b: 0.18757288, a: 1}
    - _DarkColor: {r: 0, g: 0, b: 0.5188679, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _GlossColor: {r: 0.5471698, g: 0, b: 0.3398067, a: 1}
    - _HighlightColor: {r: 2, g: 0.3, b: 1.5, a: 1}
    - _HighlightColor1: {r: 0.09999997, g: 0.29999998, b: 1, a: 1}
    - _HighlightColor2: {r: 1, g: 0.19999996, b: 0.6, a: 1}
    - _MidColor: {r: 0.2, g: 0.4, b: 1, a: 1}
    - _Offset: {r: 0, g: 0, b: 0, a: 0}
    - _Scale: {r: 1, g: 1, b: 1, a: 0}
    - _SpecColor: {r: 0.19999996, g: 0.19999996, b: 0.19999996, a: 1}
    - _SpecularColor: {r: 1, g: 0, b: 0, a: 1}
  m_BuildTextureStacks: []
  m_AllowLocking: 1
--- !u!114 &12686452890993159
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0353a89b1f911e48b9e16bdc9f2e058, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 9
