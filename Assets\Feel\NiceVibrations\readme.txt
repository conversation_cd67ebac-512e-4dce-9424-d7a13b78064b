Nice Vibrations | Haptic Feedback for Mobile
v4.1.2, using Lofelt Studio SDK v1.3.4

This Asset Package includes everything you need to begin adding advanced haptic feedback to your mobile applications created with Unity. It consists of a few important parts:

- The Nice Vibrations plug-in for Unity: Provides haptic playback functionality, as a code-only API for C# scripts and as a MonoBehaviour API for the Unity editor.

- Haptic Samples: To help you get started fast without needing to design any haptics first, the Asset includes a growing collection of free-to-use haptic clips with associated audio files covering use cases such as Application UX and game effects. You can drop these into your app and start experiencing haptics on your mobile device immediately.

- Demo: This is a project you can build for either iOS (iPhone) or Android which showcases the current functionality of this package.

- Older Versions: Older versions of Nice Vibrations are included as well, for reference and as a porting aid. You can run the current version side-by-side with the older 3.9 version in case you haven't ported everything to the new API yet.

Full documentation can be found at https://github.com/Lofelt/NiceVibrations/wiki.

Nice Vibrations includes the Lofelt Studio SDK, which uses third-party libraries. See 3RD-PARTY-LICENSES.md for a list of libraries and their licenses.
