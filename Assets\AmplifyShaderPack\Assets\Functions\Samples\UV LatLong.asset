%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: UV LatLong
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor v1.9.1.5\n// Available at the
    Unity Asset Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=19105\nNode;AmplifyShaderEditor.StickyNoteNode;278;625.9475,2337.779;Inherit;False;360.9391;129.989;UV
    Lat Long Node;;0,0,0,1;UV LatLong Node converts a vector into UV coordinates
    for sampling a LatLong-formated texture.;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;298;1529.212,2424.95;Inherit;False;397.8818;223.25;;;0,0,0,1;Result
    (which is in radians) to UV coordinate range (0-1).$$Longitude for the texture
    is a full circle, or 2*Pi radians. Thus, the first constant is 1/(2*Pi) or about
    0.15915493. In the latitude, the span is half a circle, or Pi radians. The second
    constant would be 1/Pi or about 0.3183099, but it's premultiplied by -1 to save
    a multiplication step in the node graph.;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;349;224.8872,2708.948;Inherit;False;331.8912;195.7905;;;0,0,0,1;Common
    to create this vector as the reflection of the Camera Direction and the Normal
    Vector $$See $Reflection Node connected to the input.;0;0\nNode;AmplifyShaderEditor.ATan2OpNode;352;790.4608,2504.347;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;353;991.985,2505.767;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;354;791.8799,2603.69;Inherit;False;Constant;_Float0;Float
    0;0;0;Create;True;0;0;0;False;0;False;1.570796;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.PiNode;355;763.8391,2686.513;Inherit;False;1;0;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;356;989.7815,2685.18;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.ASinOpNode;358;822.7815,2763.18;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.BreakToComponentsNode;350;619.8655,2494.155;Inherit;False;FLOAT3;1;0;FLOAT3;0,0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.SwizzleNode;359;610.7815,2756.18;Inherit;False;FLOAT;1;1;2;3;1;0;FLOAT3;0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.StickyNoteNode;361;1147.781,2502.18;Inherit;False;277;123;;;0,0,0,1;The
    U coordinate of the projection is created using the arctangent2 of the X and
    Z components of the input vector and then adding PI/2.;0;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;360;1531.781,2665.18;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0.1591549,-0.3183099;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.StickyNoteNode;362;641.7815,2836.18;Inherit;False;284;119;;;0,0,0,1;The
    V coordinate is calculated from the Y coordinate of the input vector.  We find
    the arccosine of that value and then add PI.;0;0\nNode;AmplifyShaderEditor.DdxOpNode;363;1697.028,2781.855;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.DdyOpNode;364;1694.427,2862.455;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.StickyNoteNode;368;1586.928,2945.454;Inherit;False;364.5;172.8;;;0,0,0,1;It's
    critical when using the UV coordinates generated by the UVLatLong node, that
    you set the Sample Texture 2D node to Gradient Mip Sampling Mode in the Inspector
    - and pass in the proper DDX, DDY derivatives as shown here.  Otherwise, you'll
    get an ugly seam on one side of the projection.;0;0\nNode;AmplifyShaderEditor.FunctionOutput;218;1855.521,2662.942;Inherit;False;True;-1;UV;0;True;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.FunctionOutput;365;1860.827,2781.854;Inherit;False;False;-1;DDX;1;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.FunctionOutput;367;1863.427,2862.455;Inherit;False;False;-1;DDY;2;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.RangedFloatNode;370;612.111,2655.334;Inherit;False;Constant;_Float1;Float
    1;0;0;Create;True;0;0;0;False;0;False;-0.5;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;194;432.1346,2632.053;Inherit;False;Dir;3;0;False;1;0;FLOAT3;0,1,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.DynamicAppendNode;357;1153.781,2661.88;Inherit;False;FLOAT2;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.TexCoordVertexDataNode;369;1485.947,2813.063;Inherit;False;0;2;0;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nWireConnection;352;0;350;2\nWireConnection;352;1;350;0\nWireConnection;353;0;352;0\nWireConnection;353;1;354;0\nWireConnection;355;0;370;0\nWireConnection;356;0;355;0\nWireConnection;356;1;358;0\nWireConnection;358;0;359;0\nWireConnection;350;0;194;0\nWireConnection;359;0;194;0\nWireConnection;360;0;357;0\nWireConnection;363;0;369;0\nWireConnection;364;0;369;0\nWireConnection;218;0;360;0\nWireConnection;365;0;363;0\nWireConnection;367;0;364;0\nWireConnection;357;0;353;0\nWireConnection;357;1;356;0\nASEEND*/\n//CHKSM=0AAA7CDFA9B7DD8A7FCA9DA4A0562698FF9BE4F6"
  m_functionName: 
  m_description: Amplify Shader Pack Example
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 0
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_customEdited: 0
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 0
  m_headerStyle: 2
  m_headerColor: {r: 0.6627451, g: 0.4392157, b: 0, a: 1}
  m_customNodeCategory: Amplify Shader Pack
  m_previewPosition: 0
  m_hidden: 0
  m_url: 
