%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!28 &-8444485339891319028
Texture2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: z__GradientTex{"mode":0,"colorKeys":[{"color":{"r":1.0,"g":0.9764705896377564,"b":0.7568627595901489,"a":1.0},"time":0.2500038146972656},{"color":{"r":1.0,"g":0.6313725709915161,"b":0.8156862854957581,"a":1.0},"time":0.5000076293945313},{"color":{"r":0.7607843279838562,"g":0.2666666805744171,"b":0.9843137264251709,"a":1.0},"time":0.7499961853027344},{"color":{"r":0.5176470875740051,"g":0.9137254953384399,"b":1.0,"a":1.0},"time":1.0}],"alphaKeys":[{"alpha":1.0,"time":0.0},{"alpha":1.0,"time":1.0}]}
  m_ImageContentsHash:
    serializedVersion: 2
    Hash: d9ad3edd83247f929c9c9503739cb1ef
  m_ForcedFallbackFormat: 4
  m_DownscaleFallback: 0
  m_IsAlphaChannelOptional: 0
  serializedVersion: 2
  m_Width: 1024
  m_Height: 1
  m_CompleteImageSize: 8192
  m_MipsStripped: 0
  m_TextureFormat: 17
  m_MipCount: 1
  m_IsReadable: 1
  m_IsPreProcessed: 0
  m_IgnoreMasterTextureLimit: 0
  m_StreamingMipmaps: 0
  m_StreamingMipmapsPriority: 0
  m_VTOnly: 0
  m_AlphaIsTransparency: 0
  m_ImageCount: 1
  m_TextureDimension: 2
  m_TextureSettings:
    serializedVersion: 2
    m_FilterMode: 1
    m_Aniso: 1
    m_MipBias: 0
    m_WrapU: 1
    m_WrapV: 1
    m_WrapW: 1
  m_LightmapFormat: 0
  m_ColorSpace: 0
  m_PlatformBlob: 
  image data: 8192
  _typelessdata: 003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003cd03b0e3a003c003ccf3b0e3a003c003ccc3b0f3a003c003cca3b0f3a003c003cc73b103a003c003cc43b103a003c003cc13b113a003c003cbf3b113a003c003cbc3b113a003c003cb93b123a003c003cb63b123a003c003cb33b133a003c003cb13b133a003c003cae3b143a003c003cab3b143a003c003ca83b153a003c003ca63b153a003c003ca33b163a003c003ca03b163a003c003c9d3b173a003c003c9b3b173a003c003c983b183a003c003c953b183a003c003c923b193a003c003c903b193a003c003c8d3b193a003c003c8a3b1a3a003c003c873b1a3a003c003c853b1b3a003c003c823b1b3a003c003c7f3b1c3a003c003c7c3b1c3a003c003c793b1d3a003c003c773b1d3a003c003c743b1e3a003c003c713b1e3a003c003c6e3b1f3a003c003c6c3b1f3a003c003c693b203a003c003c663b203a003c003c633b213a003c003c613b213a003c003c5e3b213a003c003c5b3b223a003c003c583b223a003c003c563b233a003c003c533b233a003c003c503b243a003c003c4d3b243a003c003c4a3b253a003c003c483b253a003c003c453b263a003c003c423b263a003c003c3f3b273a003c003c3d3b273a003c003c3a3b283a003c003c373b283a003c003c343b293a003c003c323b293a003c003c2f3b293a003c003c2c3b2a3a003c003c293b2a3a003c003c273b2b3a003c003c243b2b3a003c003c213b2c3a003c003c1e3b2c3a003c003c1c3b2d3a003c003c193b2d3a003c003c163b2e3a003c003c133b2e3a003c003c103b2f3a003c003c0e3b2f3a003c003c0b3b303a003c003c083b303a003c003c053b313a003c003c033b313a003c003c003b313a003c003cfd3a323a003c003cfa3a323a003c003cf83a333a003c003cf53a333a003c003cf23a343a003c003cef3a343a003c003ced3a353a003c003cea3a353a003c003ce73a363a003c003ce43a363a003c003ce13a373a003c003cdf3a373a003c003cdc3a383a003c003cd93a383a003c003cd63a393a003c003cd43a393a003c003cd13a3a3a003c003cce3a3a3a003c003ccb3a3a3a003c003cc93a3b3a003c003cc63a3b3a003c003cc33a3c3a003c003cc03a3c3a003c003cbe3a3d3a003c003cbb3a3d3a003c003cb83a3e3a003c003cb53a3e3a003c003cb23a3f3a003c003cb03a3f3a003c003cad3a403a003c003caa3a403a003c003ca73a413a003c003ca53a413a003c003ca23a423a003c003c9f3a423a003c003c9c3a423a003c003c9a3a433a003c003c973a433a003c003c943a443a003c003c913a443a003c003c8f3a453a003c003c8c3a453a003c003c893a463a003c003c863a463a003c003c843a473a003c003c813a473a003c003c7e3a483a003c003c7b3a483a003c003c783a493a003c003c763a493a003c003c733a4a3a003c003c703a4a3a003c003c6d3a4a3a003c003c6b3a4b3a003c003c683a4b3a003c003c653a4c3a003c003c623a4c3a003c003c603a4d3a003c003c5d3a4d3a003c003c5a3a4e3a003c003c573a4e3a003c003c553a4f3a003c003c523a4f3a003c003c4f3a503a003c003c4c3a503a003c003c493a513a003c003c473a513a003c003c443a523a003c003c413a523a003c003c3e3a523a003c003c3c3a533a003c003c393a533a003c003c363a543a003c003c333a543a003c003c313a553a003c003c2e3a553a003c003c2b3a563a003c003c283a563a003c003c263a573a003c003c233a573a003c003c203a583a003c003c1d3a583a003c003c1b3a593a003c003c183a593a003c003c153a5a3a003c003c123a5a3a003c003c0f3a5a3a003c003c0d3a5b3a003c003c0a3a5b3a003c003c073a5c3a003c003c043a5c3a003c003c023a5d3a003c003cff395d3a003c003cfc395e3a003c003cf9395e3a003c003cf7395f3a003c003cf4395f3a003c003cf139603a003c003cee39603a003c003cec39613a003c003ce939613a003c003ce639623a003c003ce339623a003c003ce039623a003c003cde39633a003c003cdb39633a003c003cd839643a003c003cd539643a003c003cd339653a003c003cd039653a003c003ccd39663a003c003cca39663a003c003cc839673a003c003cc539673a003c003cc239683a003c003cbf39683a003c003cbd39693a003c003cba39693a003c003cb7396a3a003c003cb4396a3a003c003cb1396a3a003c003caf396b3a003c003cac396b3a003c003ca9396c3a003c003ca6396c3a003c003ca4396d3a003c003ca1396d3a003c003c9e396e3a003c003c9b396e3a003c003c99396f3a003c003c96396f3a003c003c9339703a003c003c9039703a003c003c8e39713a003c003c8b39713a003c003c8839723a003c003c8539723a003c003c8339733a003c003c8039733a003c003c7d39733a003c003c7a39743a003c003c7739743a003c003c7539753a003c003c7239753a003c003c6f39763a003c003c6c39763a003c003c6a39773a003c003c6739773a003c003c6439783a003c003c6139783a003c003c5f39793a003c003c5c39793a003c003c59397a3a003c003c56397a3a003c003c54397b3a003c003c51397b3a003c003c4e397b3a003c003c4b397c3a003c003c48397c3a003c003c46397d3a003c003c43397d3a003c003c40397e3a003c003c3d397e3a003c003c3b397f3a003c003c38397f3a003c003c3539803a003c003c3239803a003c003c3039813a003c003c2d39813a003c003c2a39823a003c003c2739823a003c003c2539833a003c003c2239833a003c003c1f39833a003c003c1c39843a003c003c1a39843a003c003c1739853a003c003c1439853a003c003c1139863a003c003c0e39863a003cff3b0c39873a003cfd3b0939893a003cfb3b06398a3a003cf93b03398b3a003cf73b00398d3a003cf53bfd388e3a003cf43bfa388f3a003cf23bf738913a003cf03bf438923a003cee3bf138933a003cec3bee38953a003cea3beb38963a003ce83be938973a003ce63be638993a003ce43be3389a3a003ce23be0389b3a003ce03bdd389d3a003cde3bda389e3a003cdd3bd7389f3a003cdb3bd438a13a003cd93bd138a23a003cd73bce38a43a003cd53bcb38a53a003cd33bc838a63a003cd13bc638a83a003ccf3bc338a93a003ccd3bc038aa3a003ccb3bbd38ac3a003cc93bba38ad3a003cc83bb738ae3a003cc63bb438b03a003cc43bb138b13a003cc23bae38b23a003cc03bab38b43a003cbe3ba838b53a003cbc3ba538b63a003cba3ba238b83a003cb83ba038b93a003cb63b9d38bb3a003cb43b9a38bc3a003cb23b9738bd3a003cb13b9438bf3a003caf3b9138c03a003cad3b8e38c13a003cab3b8b38c33a003ca93b8838c43a003ca73b8538c53a003ca53b8238c73a003ca33b7f38c83a003ca13b7d38c93a003c9f3b7a38cb3a003c9d3b7738cc3a003c9b3b7438cd3a003c9a3b7138cf3a003c983b6e38d03a003c963b6b38d13a003c943b6838d33a003c923b6538d43a003c903b6238d63a003c8e3b5f38d73a003c8c3b5c38d83a003c8a3b5938da3a003c883b5738db3a003c863b5438dc3a003c843b5138de3a003c833b4e38df3a003c813b4b38e03a003c7f3b4838e23a003c7d3b4538e33a003c7b3b4238e43a003c793b3f38e63a003c773b3c38e73a003c753b3938e83a003c733b3638ea3a003c713b3338eb3a003c6f3b3138ec3a003c6d3b2e38ee3a003c6c3b2b38ef3a003c6a3b2838f13a003c683b2538f23a003c663b2238f33a003c643b1f38f53a003c623b1c38f63a003c603b1938f73a003c5e3b1638f93a003c5c3b1338fa3a003c5a3b1038fb3a003c583b0e38fd3a003c563b0b38fe3a003c553b0838ff3a003c533b0538013b003c513b0238023b003c4f3bfe37033b003c4d3bf837053b003c4b3bf237063b003c493bec37073b003c473be637093b003c453be1370a3b003c433bdb370c3b003c413bd5370d3b003c3f3bcf370e3b003c3e3bc937103b003c3c3bc337113b003c3a3bbe37123b003c383bb837143b003c363bb237153b003c343bac37163b003c323ba637183b003c303ba037193b003c2e3b9b371a3b003c2c3b95371c3b003c2a3b8f371d3b003c293b89371e3b003c273b8337203b003c253b7d37213b003c233b7737223b003c213b7237243b003c1f3b6c37253b003c1d3b6637273b003c1b3b6037283b003c193b5a37293b003c173b54372b3b003c153b4f372c3b003c133b49372d3b003c123b43372f3b003c103b3d37303b003c0e3b3737313b003c0c3b3137333b003c0a3b2c37343b003c083b2637353b003c063b2037373b003c043b1a37383b003c023b1437393b003c003b0e373b3b003cfe3a08373c3b003cfc3a03373d3b003cfb3afd363f3b003cf93af736403b003cf73af136423b003cf53aeb36433b003cf33ae536443b003cf13ae036463b003cef3ada36473b003ced3ad436483b003ceb3ace364a3b003ce93ac8364b3b003ce73ac2364c3b003ce53abd364e3b003ce43ab7364f3b003ce23ab136503b003ce03aab36523b003cde3aa536533b003cdc3a9f36543b003cda3a9a36563b003cd83a9436573b003cd63a8e36593b003cd43a88365a3b003cd23a82365b3b003cd03a7c365d3b003cce3a76365e3b003ccd3a71365f3b003ccb3a6b36613b003cc93a6536623b003cc73a5f36633b003cc53a5936653b003cc33a5336663b003cc13a4e36673b003cbf3a4836693b003cbd3a42366a3b003cbb3a3c366b3b003cb93a36366d3b003cb73a30366e3b003cb63a2b366f3b003cb43a2536713b003cb23a1f36723b003cb03a1936743b003cae3a1336753b003cac3a0d36763b003caa3a0736783b003ca83a0236793b003ca63afc357a3b003ca43af6357c3b003ca23af0357d3b003ca03aea357e3b003c9f3ae435803b003c9d3adf35813b003c9b3ad935823b003c993ad335843b003c973acd35853b003c953ac735863b003c933ac135883b003c913abc35893b003c8f3ab6358a3b003c8d3ab0358c3b003c8b3aaa358d3b003c893aa4358f3b003c883a9e35903b003c863a9835913b003c843a9335933b003c823a8d35943b003c803a8735953b003c7e3a8135973b003c7c3a7b35983b003c7a3a7535993b003c783a70359b3b003c763a6a359c3b003c743a64359d3b003c733a5e359f3b003c713a5835a03b003c6f3a5235a13b003c6d3a4d35a33b003c6b3a4735a43b003c693a4135a53b003c673a3b35a73b003c653a3535a83b003c633a2f35aa3b003c613a2a35ab3b003c5f3a2435ac3b003c5d3a1e35ae3b003c5c3a1835af3b003c5a3a1235b03b003c583a0c35b23b003c563a0635b33b003c543a0135b43b003c523afb34b63b003c503af534b73b003c4e3aef34b83b003c4c3ae934ba3b003c4a3ae334bb3b003c483ade34bc3b003c463ad834be3b003c453ad234bf3b003c433acc34c03b003c413ac634c23b003c3f3ac034c33b003c3d3abb34c53b003c3b3ab534c63b003c393aaf34c73b003c373aa934c93b003c353aa334ca3b003c333a9d34cb3b003c313a9734cd3b003c2f3a9234ce3b003c2e3a8c34cf3b003c2c3a8634d13b003c2a3a8034d23b003c283a7a34d33b003c263a7434d53b003c243a6f34d63b003c223a6934d73b003c203a6334d93b003c1e3a5d34da3b003c1c3a5734db3b003c1a3a5134dd3b003c183a4c34de3b003c173a4634e03b003c153a4c34e03b003c133a5634e03b003c113a6134e03b003c0f3a6b34e03b003c0d3a7634e03b003c0b3a8034e13b003c093a8a34e13b003c073a9534e13b003c053a9f34e13b003c033aa934e13b003c013ab434e13b003cff39be34e13b003cfd39c834e13b003cfb39d334e23b003cf939dd34e23b003cf739e834e23b003cf539f234e23b003cf439fc34e23b003cf2390735e23b003cf0391135e23b003cee391b35e23b003cec392635e33b003cea393035e33b003ce8393a35e33b003ce6394535e33b003ce4394f35e33b003ce2395a35e33b003ce0396435e33b003cde396e35e33b003cdc397935e43b003cda398335e43b003cd8398d35e43b003cd6399835e43b003cd439a235e43b003cd239ac35e43b003cd039b735e43b003ccf39c135e43b003ccd39cc35e53b003ccb39d635e53b003cc939e035e53b003cc739eb35e53b003cc539f535e53b003cc339ff35e53b003cc1390a36e53b003cbf391436e53b003cbd391e36e63b003cbb392936e63b003cb9393336e63b003cb7393d36e63b003cb5394836e63b003cb3395236e63b003cb1395d36e63b003caf396736e73b003cad397136e73b003cab397c36e73b003caa398636e73b003ca8399036e73b003ca6399b36e73b003ca439a536e73b003ca239af36e73b003ca039ba36e83b003c9e39c436e83b003c9c39cf36e83b003c9a39d936e83b003c9839e336e83b003c9639ee36e83b003c9439f836e83b003c92390237e83b003c90390d37e93b003c8e391737e93b003c8c392137e93b003c8a392c37e93b003c88393637e93b003c86394137e93b003c85394b37e93b003c83395537e93b003c81396037ea3b003c7f396a37ea3b003c7d397437ea3b003c7b397f37ea3b003c79398937ea3b003c77399337ea3b003c75399e37ea3b003c7339a837ea3b003c7139b337eb3b003c6f39bd37eb3b003c6d39c737eb3b003c6b39d237eb3b003c6939dc37eb3b003c6739e637eb3b003c6539f137eb3b003c6339fb37eb3b003c61390338ec3b003c60390838ec3b003c5e390d38ec3b003c5c391238ec3b003c5a391738ec3b003c58391d38ec3b003c56392238ec3b003c54392738ec3b003c52392c38ed3b003c50393138ed3b003c4e393738ed3b003c4c393c38ed3b003c4a394138ed3b003c48394638ed3b003c46394b38ed3b003c44395038ed3b003c42395638ee3b003c40395b38ee3b003c3e396038ee3b003c3d396538ee3b003c3b396a38ee3b003c39397038ee3b003c37397538ee3b003c35397a38ee3b003c33397f38ef3b003c31398438ef3b003c2f398938ef3b003c2d398f38ef3b003c2b399438ef3b003c29399938ef3b003c27399e38ef3b003c2539a338ef3b003c2339a938f03b003c2139ae38f03b003c1f39b338f03b003c1d39b838f03b003c1b39bd38f03b003c1939c238f03b003c1839c838f03b003c1639cd38f03b003c1439d238f13b003c1239d738f13b003c1039dc38f13b003c0e39e238f13b003c0c39e738f13b003c0a39ec38f13b003c0839f138f13b003c0639f638f13b003c0439fb38f23b003c02390139f23b003c00390639f23b003cfe380b39f23b003cfc381039f23b003cfa381539f23b003cf8381b39f23b003cf6382039f23b003cf4382539f33b003cf3382a39f33b003cf1382f39f33b003cef383439f33b003ced383a39f33b003ceb383f39f33b003ce9384439f33b003ce7384939f33b003ce5384e39f43b003ce3385439f43b003ce1385939f43b003cdf385e39f43b003cdd386339f43b003cdb386839f43b003cd9386d39f43b003cd7387339f43b003cd5387839f53b003cd3387d39f53b003cd1388239f53b003ccf388739f53b003cce388d39f53b003ccc389239f53b003cca389739f53b003cc8389c39f53b003cc638a139f63b003cc438a639f63b003cc238ac39f63b003cc038b139f63b003cbe38b639f63b003cbc38bb39f63b003cba38c039f63b003cb838c639f63b003cb638cb39f73b003cb438d039f73b003cb238d539f73b003cb038da39f73b003cae38df39f73b003cac38e539f73b003caa38ea39f73b003ca938ef39f73b003ca738f439f83b003ca538f939f83b003ca338ff39f83b003ca138043af83b003c9f38093af83b003c9d380e3af83b003c9b38133af83b003c9938183af83b003c97381e3af93b003c9538233af93b003c9338283af93b003c91382d3af93b003c8f38323af93b003c8d38383af93b003c8b383d3af93b003c8938423af93b003c8738473afa3b003c85384c3afa3b003c8438513afa3b003c8238573afa3b003c80385c3afa3b003c7e38613afa3b003c7c38663afa3b003c7a386b3afa3b003c7838713afb3b003c7638763afb3b003c74387b3afb3b003c7238803afb3b003c7038853afb3b003c6e388a3afb3b003c6c38903afb3b003c6a38953afb3b003c68389a3afc3b003c66389f3afc3b003c6438a43afc3b003c6238aa3afc3b003c6038af3afc3b003c5f38b43afc3b003c5d38b93afc3b003c5b38be3afc3b003c5938c33afd3b003c5738c93afd3b003c5538ce3afd3b003c5338d33afd3b003c5138d83afd3b003c4f38dd3afd3b003c4d38e23afd3b003c4b38e83afd3b003c4938ed3afe3b003c4738f23afe3b003c4538f73afe3b003c4338fc3afe3b003c4138023bfe3b003c3f38073bfe3b003c3d380c3bfe3b003c3c38113bfe3b003c3a38163bff3b003c38381b3bff3b003c3638213bff3b003c3438263bff3b003c32382b3bff3b003c3038303bff3b003c2e38353bff3b003c2c383b3bff3b003c2a38403b003c003c2838453b003c003c26384a3b003c003c24384f3b003c003c
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!114 &-3923895880809708522
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0353a89b1f911e48b9e16bdc9f2e058, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 5
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Vase - Skybox Chroma - Pink
  m_Shader: {fileID: 4800000, guid: 85e646ae6ff735241a05a66922d89b7b, type: 3}
  m_ValidKeywords: []
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BaseMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Gradient:
        m_Texture: {fileID: -8444485339891319028}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Intensity:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AlphaClip: 0
    - _Blend: 0
    - _BumpScale: 1
    - _ClearCoatMask: 0
    - _ClearCoatSmoothness: 0
    - _Cull: 2
    - _Cutoff: 0.5
    - _DetailAlbedoMapScale: 1
    - _DetailNormalMapScale: 1
    - _DirectionPitch: 0
    - _DirectionYaw: 0
    - _DstBlend: 0
    - _EnvironmentReflections: 1
    - _GlossMapScale: 0
    - _Glossiness: 0
    - _GlossyReflections: 0
    - _Metallic: 0
    - _OcclusionStrength: 1
    - _Parallax: 0.005
    - _QueueOffset: 0
    - _ReceiveShadows: 1
    - _Smoothness: 0.5
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _Surface: 0
    - _WorkflowMode: 1
    - _ZWrite: 1
    m_Colors:
    - _BaseColor: {r: 1, g: 1, b: 1, a: 1}
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _SpecColor: {r: 0.19999996, g: 0.19999996, b: 0.19999996, a: 1}
  m_BuildTextureStacks: []
