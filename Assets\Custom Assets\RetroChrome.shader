Shader "Custom/RetroChrome"
{
    Properties
    {
        _BaseMap ("Base Map", 2D) = "white" {}
        _BaseColor("Base Color", Color) = (0.8,0.8,0.8,1)
        [HDR]_EmissionColor("Emission Color", Color) = (0,0.5,1,1)
        _MetallicStrength("Metallic Strength", Range(0,1)) = 0.8
        _Smoothness("Smoothness", Range(0,1)) = 0.946
        _FresnelPower("Fresnel Power", Range(0,5)) = 2.5
        _FresnelIntensity("Fresnel Intensity", Range(0,5)) = 2.0
        _ChromaticAberration("Chromatic Aberration", Range(0,1)) = 0.15
        
        [Header(Noise Settings)]
        [Space(5)]
        _NoiseScale("NoiseScale", Range(0,1000)) = 50
        _NoiseSpeed("NoiseSpeed", Range(0,10)) = 1
        _NoiseStrength("NoiseStrength", Range(0,1)) = 0.05
        _NoiseOpacity("Noise Opacity", Range(0,1)) = 1
        [KeywordEnum(Multiply, Add, Overlay, Normal)] _NoiseMix ("Noise Mix Mode", Float) = 0
        _NoiseReflectionStrength("Noise Reflection Strength", Range(0,1)) = 0.1
        _NoiseDiffuseStrength("Noise Diffuse Strength", Range(0,1)) = 0.05

        [Header(Glow Settings)]
        [Space(5)]
        [HDR]_GlowColor("Glow Color", Color) = (1,1,1,1)
        _GlowStrength("Glow Strength", Range(0,5)) = 1.5
        _GlowFalloff("Glow Falloff", Range(0.1,5)) = 1.0
        _GlowRimPower("Glow Rim Power", Range(0.1,10)) = 2.0
        
        _StreakIntensity("StreakIntensity", Range(0,1.5)) = 0.5
        _EdgeSharpness("Edge Sharpness", Range(0,10)) = 10
        _ReflectionIntensity("Reflection Intensity", Range(0,2)) = 1.5
    }

    SubShader
    {
        Tags 
        { 
            "RenderType" = "Opaque"
            "RenderPipeline" = "UniversalPipeline"
            "UniversalMaterialType" = "Lit"
            "Queue" = "Geometry"
            "ShaderModel"="4.5"
            "IgnoreProjector" = "True"
        }

        HLSLINCLUDE
        #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
        #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Lighting.hlsl"
        #include "Packages/com.unity.render-pipelines.core/ShaderLibrary/Color.hlsl"

        // 1. Texture and sampler declarations FIRST
        TEXTURE2D(_BaseMap);
        SAMPLER(sampler_BaseMap);

        // 2. UnityPerMaterial CBUFFER BEFORE meta includes
        CBUFFER_START(UnityPerMaterial)
            float4 _BaseMap_ST;
            float4 _BaseColor;
            float4 _EmissionColor;
            float4 _GlowColor;
            float _MetallicStrength;
            float _Smoothness;
            float _FresnelPower;
            float _FresnelIntensity;
            float _ChromaticAberration;
            float _NoiseScale;
            float _NoiseSpeed;
            float _NoiseStrength;
            float _NoiseOpacity;
            float _NoiseReflectionStrength;
            float _NoiseDiffuseStrength;
            float _GlowStrength;
            float _GlowFalloff;
            float _GlowRimPower;
            float _StreakIntensity;
            float _EdgeSharpness;
            float _ReflectionIntensity;
        CBUFFER_END

        // 3. Now include meta pass headers
        #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/MetaInput.hlsl"
        #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/UniversalMetaPass.hlsl"

        #pragma multi_compile_instancing
        #pragma instancing_options renderinglayer

        #ifdef UNITY_INSTANCING_ENABLED
            UNITY_INSTANCING_BUFFER_START(Props)
                UNITY_DEFINE_INSTANCED_PROP(float4, _BaseColor)
                UNITY_DEFINE_INSTANCED_PROP(float4, _EmissionColor)
                UNITY_DEFINE_INSTANCED_PROP(float4, _GlowColor)
                UNITY_DEFINE_INSTANCED_PROP(float, _MetallicStrength)
                UNITY_DEFINE_INSTANCED_PROP(float, _Smoothness)
                UNITY_DEFINE_INSTANCED_PROP(float, _FresnelPower)
                UNITY_DEFINE_INSTANCED_PROP(float, _FresnelIntensity)
                UNITY_DEFINE_INSTANCED_PROP(float, _ChromaticAberration)
                UNITY_DEFINE_INSTANCED_PROP(float, _NoiseScale)
                UNITY_DEFINE_INSTANCED_PROP(float, _NoiseSpeed)
                UNITY_DEFINE_INSTANCED_PROP(float, _NoiseStrength)
                UNITY_DEFINE_INSTANCED_PROP(float, _NoiseOpacity)
                UNITY_DEFINE_INSTANCED_PROP(float, _NoiseReflectionStrength)
                UNITY_DEFINE_INSTANCED_PROP(float, _NoiseDiffuseStrength)
                UNITY_DEFINE_INSTANCED_PROP(float, _GlowStrength)
                UNITY_DEFINE_INSTANCED_PROP(float, _GlowFalloff)
                UNITY_DEFINE_INSTANCED_PROP(float, _GlowRimPower)
                UNITY_DEFINE_INSTANCED_PROP(float, _StreakIntensity)
                UNITY_DEFINE_INSTANCED_PROP(float, _EdgeSharpness)
                UNITY_DEFINE_INSTANCED_PROP(float, _ReflectionIntensity)
            UNITY_INSTANCING_BUFFER_END(Props)
        #endif

        float4 GetInstancedBaseColor()
        {
            #if defined(INSTANCING_ON)
                return UNITY_ACCESS_INSTANCED_PROP(Props, _BaseColor);
            #else
                return _BaseColor;
            #endif
        }

        float4 GetInstancedEmissionColor()
        {
            #if defined(INSTANCING_ON)
                return UNITY_ACCESS_INSTANCED_PROP(Props, _EmissionColor);
            #else
                return _EmissionColor;
            #endif
        }

        float4 GetInstancedGlowColor()
        {
            #if defined(INSTANCING_ON)
                return UNITY_ACCESS_INSTANCED_PROP(Props, _GlowColor);
            #else
                return _GlowColor;
            #endif
        }

        float GetInstancedMetallicStrength()
        {
            #if defined(INSTANCING_ON)
                return UNITY_ACCESS_INSTANCED_PROP(Props, _MetallicStrength);
            #else
                return _MetallicStrength;
            #endif
        }

        float GetInstancedSmoothness()
        {
            #if defined(INSTANCING_ON)
                return UNITY_ACCESS_INSTANCED_PROP(Props, _Smoothness);
            #else
                return _Smoothness;
            #endif
        }

        float GetInstancedFresnelPower()
        {
            #if defined(INSTANCING_ON)
                return UNITY_ACCESS_INSTANCED_PROP(Props, _FresnelPower);
            #else
                return _FresnelPower;
            #endif
        }

        float GetInstancedFresnelIntensity()
        {
            #if defined(INSTANCING_ON)
                return UNITY_ACCESS_INSTANCED_PROP(Props, _FresnelIntensity);
            #else
                return _FresnelIntensity;
            #endif
        }

        float GetInstancedChromaticAberration()
        {
            #if defined(INSTANCING_ON)
                return UNITY_ACCESS_INSTANCED_PROP(Props, _ChromaticAberration);
            #else
                return _ChromaticAberration;
            #endif
        }

        float GetInstancedNoiseScale()
        {
            #if defined(INSTANCING_ON)
                return UNITY_ACCESS_INSTANCED_PROP(Props, _NoiseScale);
            #else
                return _NoiseScale;
            #endif
        }

        float GetInstancedNoiseSpeed()
        {
            #if defined(INSTANCING_ON)
                return UNITY_ACCESS_INSTANCED_PROP(Props, _NoiseSpeed);
            #else
                return _NoiseSpeed;
            #endif
        }

        float GetInstancedNoiseStrength()
        {
            #if defined(INSTANCING_ON)
                return UNITY_ACCESS_INSTANCED_PROP(Props, _NoiseStrength);
            #else
                return _NoiseStrength;
            #endif
        }

        float GetInstancedNoiseOpacity()
        {
            #if defined(INSTANCING_ON)
                return UNITY_ACCESS_INSTANCED_PROP(Props, _NoiseOpacity);
            #else
                return _NoiseOpacity;
            #endif
        }

        float GetInstancedNoiseReflectionStrength()
        {
            #if defined(INSTANCING_ON)
                return UNITY_ACCESS_INSTANCED_PROP(Props, _NoiseReflectionStrength);
            #else
                return _NoiseReflectionStrength;
            #endif
        }

        float GetInstancedNoiseDiffuseStrength()
        {
            #if defined(INSTANCING_ON)
                return UNITY_ACCESS_INSTANCED_PROP(Props, _NoiseDiffuseStrength);
            #else
                return _NoiseDiffuseStrength;
            #endif
        }

        float GetInstancedGlowStrength()
        {
            #if defined(INSTANCING_ON)
                return UNITY_ACCESS_INSTANCED_PROP(Props, _GlowStrength);
            #else
                return _GlowStrength;
            #endif
        }

        float GetInstancedGlowFalloff()
        {
            #if defined(INSTANCING_ON)
                return UNITY_ACCESS_INSTANCED_PROP(Props, _GlowFalloff);
            #else
                return _GlowFalloff;
            #endif
        }

        float GetInstancedGlowRimPower()
        {
            #if defined(INSTANCING_ON)
                return UNITY_ACCESS_INSTANCED_PROP(Props, _GlowRimPower);
            #else
                return _GlowRimPower;
            #endif
        }

        float GetInstancedStreakIntensity()
        {
            #if defined(INSTANCING_ON)
                return UNITY_ACCESS_INSTANCED_PROP(Props, _StreakIntensity);
            #else
                return _StreakIntensity;
            #endif
        }

        float GetInstancedEdgeSharpness()
        {
            #if defined(INSTANCING_ON)
                return UNITY_ACCESS_INSTANCED_PROP(Props, _EdgeSharpness);
            #else
                return _EdgeSharpness;
            #endif
        }

        float GetInstancedReflectionIntensity()
        {
            #if defined(INSTANCING_ON)
                return UNITY_ACCESS_INSTANCED_PROP(Props, _ReflectionIntensity);
            #else
                return _ReflectionIntensity;
            #endif
        }

        // Simplex noise function
        float3 mod289(float3 x) { return x - floor(x * (1.0 / 289.0)) * 289.0; }
        float4 mod289(float4 x) { return x - floor(x * (1.0 / 289.0)) * 289.0; }
        float4 permute(float4 x) { return mod289(((x * 34.0) + 1.0) * x); }
        float4 taylorInvSqrt(float4 r) { return 1.79284291400159 - 0.85373472095314 * r; }

        float snoise(float3 v)
        {
            const float2 C = float2(1.0 / 6.0, 1.0 / 3.0);
            float3 i = floor(v + dot(v, C.yyy));
            float3 x0 = v - i + dot(i, C.xxx);
            float3 g = step(x0.yzx, x0.xyz);
            float3 l = 1.0 - g;
            float3 i1 = min(g.xyz, l.zxy);
            float3 i2 = max(g.xyz, l.zxy);
            float3 x1 = x0 - i1 + C.xxx;
            float3 x2 = x0 - i2 + C.yyy;
            float3 x3 = x0 - 0.5;
            i = mod289(i);
            float4 p = permute(permute(permute(i.z + float4(0.0, i1.z, i2.z, 1.0))
                + i.y + float4(0.0, i1.y, i2.y, 1.0))
                + i.x + float4(0.0, i1.x, i2.x, 1.0));
            float4 j = p - 49.0 * floor(p * 0.0039292 + 0.5);
            float4 x_ = floor(j * 0.0039292);
            float4 y_ = floor(j - 7.0 * x_);
            float4 x = x_ * 0.0039292 + 0.5;
            float4 y = y_ * 0.0039292 + 0.5;
            float4 h = 1.0 - abs(x) - abs(y);
            float4 b0 = float4(x.xy, y.xy);
            float4 b1 = float4(x.zw, y.zw);
            float4 s0 = floor(b0) * 2.0 + 1.0;
            float4 s1 = floor(b1) * 2.0 + 1.0;
            float4 sh = -step(h, 0.0);
            float4 a0 = b0.xzyw + s0.xzyw * sh.xxyy;
            float4 a1 = b1.xzyw + s1.xzyw * sh.zzww;
            float3 g0 = float3(a0.xy, h.x);
            float3 g1 = float3(a0.zw, h.y);
            float3 g2 = float3(a1.xy, h.z);
            float3 g3 = float3(a1.zw, h.w);
            float4 norm = taylorInvSqrt(float4(dot(g0, g0), dot(g1, g1), dot(g2, g2), dot(g3, g3)));
            g0 *= norm.x;
            g1 *= norm.y;
            g2 *= norm.z;
            g3 *= norm.w;
            float n = dot(float3(dot(g0, x0), dot(g1, x1), dot(g2, x2)), float3(70.0, 70.0, 70.0));
            return 2.0 * n;
        }

        float3 calculateGlareStreak(float3 reflectionDir, float3 viewDir, float edgeSharpness, float streakIntensity)
        {
            float rawDot = saturate(dot(reflectionDir, viewDir));
            float wideBase = smoothstep(0.2, 0.95, rawDot);
            float mediumBase = smoothstep(0.3, 0.85, rawDot);
            float tightBase = smoothstep(0.4, 0.75, rawDot);
            
            float softFalloff = exp(-5.0 * (1.0 - rawDot) * (1.0 - rawDot));
            float blendedStreak = lerp(wideBase, mediumBase, 0.7) * 0.6 +
                                lerp(mediumBase, tightBase, 0.5) * 0.4;
            
            float remappedIntensity = pow(abs(streakIntensity), 1.5) * 1.2;
            float finalStreak = lerp(blendedStreak, softFalloff, 0.4) * remappedIntensity;
            finalStreak = pow(abs(finalStreak), lerp(1.0, edgeSharpness * 0.2, 0.6));
            
            return finalStreak;
        }

        float3 calculateEdgeHighlight(float3 normal, float3 viewDir, float edgeSharpness)
        {
            float edge = 1.0 - saturate(dot(normal, viewDir));
            edge = pow(edge, edgeSharpness);
            return edge;
        }

        float3 overlayBlend(float3 base, float3 blend) {
            return base < 0.5 ? 
                   2.0 * base * blend : 
                   1.0 - 2.0 * (1.0 - base) * (1.0 - blend);
        }

        float3 calculateNoise(float2 uv, float3 normalWS, float3 baseColor) {
            float noiseScale = GetInstancedNoiseScale();
            float noiseSpeed = GetInstancedNoiseSpeed();
            
            // Scale UVs for better detail at high values
            float2 scaledUV = uv * (noiseScale * 0.1);
            
            // Primary noise
            float mainNoise = snoise(float3(scaledUV, _Time.y * noiseSpeed)) * GetInstancedNoiseStrength();
            float3 noiseColor = float3(mainNoise, mainNoise, mainNoise);
            
            // Apply a smoothstep curve to the opacity for better control
            float rawOpacity = GetInstancedNoiseOpacity();
            float curvedOpacity = smoothstep(0.0, 1.0, rawOpacity * rawOpacity);

            // Different mixing modes with curved opacity
            #if _NOISEMIX_MULTIPLY
                return lerp(1.0, 1.0 + noiseColor * 0.5, curvedOpacity);
            #elif _NOISEMIX_ADD
                return lerp(baseColor, baseColor + noiseColor * 0.5, curvedOpacity);
            #elif _NOISEMIX_OVERLAY
                float3 overlayResult = overlayBlend(baseColor, 1.0 + noiseColor * 0.5);
                return lerp(baseColor, overlayResult, curvedOpacity);
            #elif _NOISEMIX_NORMAL
                float normalFactor = dot(normalWS, float3(0,1,0));
                return lerp(baseColor, baseColor + noiseColor * normalFactor * 0.5, curvedOpacity);
            #else
                return lerp(1.0, 1.0 + noiseColor * 0.5, curvedOpacity);
            #endif
        }

        float3 calculateGlow(float3 normalWS, float3 viewDirWS, float3 baseColor, float3 emissionColor, float fresnel)
        {
            float rimDot = 1.0 - saturate(dot(normalWS, viewDirWS));
            float rimGlow = pow(abs(rimDot), _GlowRimPower);
            float viewFactor = pow(abs(dot(normalWS, viewDirWS)), _GlowFalloff);
            float glowMask = saturate(rimGlow + fresnel * 0.5) * viewFactor;
            
            float4 glowColor = GetInstancedGlowColor();
            float3 finalGlowColor = lerp(emissionColor, glowColor.rgb, glowMask);
            
            return finalGlowColor * glowMask * _GlowStrength;
        }
        ENDHLSL

        Pass
        {
            Name "ForwardLit"
            Tags { "LightMode" = "UniversalForward" }

            Blend One Zero
            ZWrite On
            Cull Back

            HLSLPROGRAM
            #pragma exclude_renderers gles gles3 glcore
            #pragma target 4.5

            #pragma vertex vert
            #pragma fragment frag

            #pragma multi_compile_instancing
            #pragma instancing_options renderinglayer
            #pragma multi_compile _ DOTS_INSTANCING_ON

            #pragma multi_compile _ _MAIN_LIGHT_SHADOWS _MAIN_LIGHT_SHADOWS_CASCADE _MAIN_LIGHT_SHADOWS_SCREEN
            #pragma multi_compile _ _ADDITIONAL_LIGHTS_VERTEX _ADDITIONAL_LIGHTS
            #pragma multi_compile_fragment _ _ADDITIONAL_LIGHT_SHADOWS
            #pragma multi_compile_fragment _ _REFLECTION_PROBE_BLENDING
            #pragma multi_compile_fragment _ _REFLECTION_PROBE_BOX_PROJECTION
            #pragma multi_compile_fragment _ _SHADOWS_SOFT
            #pragma multi_compile _ LIGHTMAP_SHADOW_MIXING
            #pragma multi_compile _ SHADOWS_SHADOWMASK
            #pragma multi_compile_fragment _ _SCREEN_SPACE_OCCLUSION
            #pragma multi_compile_fragment _ DEBUG_DISPLAY
            #pragma multi_compile_fog

            #pragma multi_compile _NOISEMIX_MULTIPLY _NOISEMIX_ADD _NOISEMIX_OVERLAY _NOISEMIX_NORMAL

            struct ForwardAttributes
            {
                float4 positionOS : POSITION;
                float3 normalOS : NORMAL;
                float4 tangentOS : TANGENT;
                float2 uv : TEXCOORD0;
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            struct ForwardVaryings
            {
                float4 positionCS : SV_POSITION;
                float3 positionWS : TEXCOORD0;
                float3 normalWS : TEXCOORD1;
                float3 viewDirWS : TEXCOORD2;
                float2 uv : TEXCOORD3;
                float4 screenPos : TEXCOORD4;
                UNITY_VERTEX_INPUT_INSTANCE_ID
                UNITY_VERTEX_OUTPUT_STEREO
            };

            ForwardVaryings vert(ForwardAttributes input)
            {
                ForwardVaryings output = (ForwardVaryings)0;
                UNITY_SETUP_INSTANCE_ID(input);
                UNITY_TRANSFER_INSTANCE_ID(input, output);
                UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(output);

                float3 positionOS = input.positionOS.xyz;
                
                VertexPositionInputs vertexInput = GetVertexPositionInputs(positionOS);
                VertexNormalInputs normalInput = GetVertexNormalInputs(input.normalOS, input.tangentOS);

                output.positionCS = vertexInput.positionCS;
                output.positionWS = vertexInput.positionWS;
                output.normalWS = normalInput.normalWS;
                output.viewDirWS = GetWorldSpaceViewDir(vertexInput.positionWS);
                output.uv = TRANSFORM_TEX(input.uv, _BaseMap);
                output.screenPos = ComputeScreenPos(output.positionCS);
                
                return output;
            }

            float4 frag(ForwardVaryings input) : SV_Target
            {
                UNITY_SETUP_INSTANCE_ID(input);
                UNITY_SETUP_STEREO_EYE_INDEX_POST_VERTEX(input);

                float4 baseMap = SAMPLE_TEXTURE2D(_BaseMap, sampler_BaseMap, input.uv);
                float3 normalWS = normalize(input.normalWS);
                float3 viewDirWS = normalize(input.viewDirWS);
                
                float4 baseColor = GetInstancedBaseColor();
                float4 emissionColor = GetInstancedEmissionColor();
                float metallicStrength = GetInstancedMetallicStrength();
                float smoothness = GetInstancedSmoothness();
                float fresnelPower = GetInstancedFresnelPower();
                float fresnelIntensity = GetInstancedFresnelIntensity();
                float chromaticAberration = GetInstancedChromaticAberration();
                float noiseScale = GetInstancedNoiseScale();
                float noiseSpeed = GetInstancedNoiseSpeed();
                float noiseStrength = GetInstancedNoiseStrength();
                float noiseReflectionStrength = GetInstancedNoiseReflectionStrength();
                float noiseDiffuseStrength = GetInstancedNoiseDiffuseStrength();
                float glowStrength = GetInstancedGlowStrength();
                float glowFalloff = GetInstancedGlowFalloff();
                float glowRimPower = GetInstancedGlowRimPower();
                float streakIntensity = GetInstancedStreakIntensity();
                float edgeSharpness = GetInstancedEdgeSharpness();
                float reflectionIntensity = GetInstancedReflectionIntensity();
                
                // Setup lighting data
                InputData inputData;
                ZERO_INITIALIZE(InputData, inputData);
                inputData.positionWS = input.positionWS;
                inputData.normalWS = normalWS;
                inputData.viewDirectionWS = viewDirWS;
                inputData.shadowCoord = TransformWorldToShadowCoord(input.positionWS);
                inputData.fogCoord = 0;
                inputData.vertexLighting = float3(0.0, 0.0, 0.0);
                inputData.bakedGI = float3(0.0, 0.0, 0.0);
                inputData.normalizedScreenSpaceUV = GetNormalizedScreenSpaceUV(input.positionCS);
                inputData.shadowMask = float4(1.0, 1.0, 1.0, 1.0);
                
                // Get main light and calculate lighting
                Light mainLight = GetMainLight(inputData.shadowCoord, inputData.positionWS, inputData.shadowMask);
                float3 lightDir = mainLight.direction;
                float3 halfDir = normalize(lightDir + viewDirWS);
                
                // Revised specular calculation with better smoothness control
                float specularPower = exp2(10 * smoothness + 1);
                float specular = pow(abs(saturate(dot(normalWS, halfDir))), specularPower);
                
                // Sample environment reflection with improved roughness
                float3 reflectVector = reflect(-viewDirWS, normalWS);
                float perceptualRoughness = 1 - smoothness;
                float mip = perceptualRoughness * perceptualRoughness * 6;
                
                // Get reflection probe and skybox contribution with chromatic aberration
                float3 reflectVectorR = reflect(normalize(-viewDirWS + float3(chromaticAberration, 0, 0)), normalWS);
                float3 reflectVectorB = reflect(normalize(-viewDirWS - float3(chromaticAberration, 0, 0)), normalWS);
                
                float3 envSampleR = GlossyEnvironmentReflection(reflectVectorR, perceptualRoughness, 1.0);
                float3 envSampleG = GlossyEnvironmentReflection(reflectVector, perceptualRoughness, 1.0);
                float3 envSampleB = GlossyEnvironmentReflection(reflectVectorB, perceptualRoughness, 1.0);
                
                // Combine RGB samples with chromatic aberration and smoothness-based attenuation
                float3 envSample = float3(envSampleR.r, envSampleG.g, envSampleB.b) * reflectionIntensity * smoothness;
                
                // Calculate streak effect once
                float3 streakEffect = calculateGlareStreak(reflectVector, viewDirWS, edgeSharpness, streakIntensity);
                float edge = calculateEdgeHighlight(normalWS, viewDirWS, edgeSharpness);
                
                float2 screenUV = input.screenPos.xy / input.screenPos.w;
                
                // Apply chromatic aberration to base texture
                float2 uvR = screenUV + float2(chromaticAberration, 0);
                float2 uvB = screenUV - float2(chromaticAberration, 0);
                
                float3 baseMapR = SAMPLE_TEXTURE2D(_BaseMap, sampler_BaseMap, uvR).rgb;
                float3 baseMapG = SAMPLE_TEXTURE2D(_BaseMap, sampler_BaseMap, screenUV).rgb;
                float3 baseMapB = SAMPLE_TEXTURE2D(_BaseMap, sampler_BaseMap, uvB).rgb;
                float3 baseMapCombined = float3(baseMapR.r, baseMapG.g, baseMapB.b);
                
                // Calculate geometric edge factor using screen-space derivatives
                float3 positionSS = input.positionCS.xyz / input.positionCS.w;
                float3 normalSS = normalize(mul((float3x3)UNITY_MATRIX_IT_MV, normalWS));
                
                // Calculate screen-space gradients
                float3 dx = ddx(positionSS);
                float3 dy = ddy(positionSS);
                float3 ndx = ddx(normalSS);
                float3 ndy = ddy(normalSS);
                
                // Edge detection factor
                float positionGrad = length(dx) + length(dy);
                float normalGrad = length(ndx) + length(ndy);
                float edgeFactor = saturate(positionGrad * 10 + normalGrad * 20);
                
                // Smooth geometric transitions
                float geometricSmoothness = 1.0 - edgeFactor;
                
                // Calculate lighting contribution with edge smoothing
                float3 lightContribution = mainLight.color * mainLight.shadowAttenuation * 
                    saturate(dot(normalWS, lightDir) + edgeFactor * 0.3);
                
                // Calculate fresnel with geometric smoothing
                float fresnel = pow(1.0 - saturate(dot(normalWS, viewDirWS)), fresnelPower);
                float fresnelFactor = fresnel * fresnelIntensity * (1.0 - edgeFactor * 0.5);
                
                // Improved metallic workflow with edge smoothing
                float3 baseColorWithMetallic = baseMapCombined * baseColor.rgb;
                float smoothedMetallic = metallicStrength * (1.0 - edgeFactor * 0.3);
                
                // Calculate noise with geometric awareness
                float2 noiseUV = input.uv + float2(edgeFactor * 0.1, edgeFactor * 0.1);
                float3 noiseEffect = calculateNoise(noiseUV, normalWS, baseColorWithMetallic);
                
                // Calculate diffuse and reflection components with edge smoothing
                float3 diffuseColor = baseColorWithMetallic * (1.0 - smoothedMetallic);
                float noiseDiffuseOpacity = GetInstancedNoiseDiffuseStrength() * (1.0 - edgeFactor * 0.5);
                diffuseColor *= lerp(1.0, noiseEffect, noiseDiffuseOpacity);
                
                // Enhanced reflection with edge-aware blending
                float reflectionMask = saturate(smoothedMetallic + fresnelFactor);
                float3 reflectionColor = envSample * baseColorWithMetallic * (1.0 + fresnelFactor);
                float noiseReflectionOpacity = GetInstancedNoiseReflectionStrength() * (1.0 - edgeFactor * 0.5);
                reflectionColor *= lerp(1.0, noiseEffect, noiseReflectionOpacity);
                
                // Blend streak with reflection more gradually
                float3 blendedStreak = lerp(streakEffect, streakEffect * reflectionColor, 0.25);
                blendedStreak *= (1.0 - pow(edgeFactor, 0.8) * 0.8);
                
                // Add subtle variation based on view angle with softer falloff
                float viewFalloff = pow(abs(saturate(dot(normalWS, viewDirWS))), 0.7);
                blendedStreak *= lerp(0.8, 1.0, viewFalloff);
                
                // Smooth blend between diffuse and reflection
                float blendFactor = smoothstep(0.2, 0.8, reflectionMask) * geometricSmoothness;
                float3 metallicColor = lerp(diffuseColor, reflectionColor, blendFactor);
                
                // Apply lighting with edge-aware ambient
                float3 ambientLight = float3(0.7, 0.7, 0.7) * (1.0 + fresnelFactor * 0.3);
                float lightBlend = smoothstep(0.2, 0.8, dot(normalWS, lightDir)) * geometricSmoothness;
                metallicColor *= lerp(ambientLight, lightContribution, lightBlend);
                
                // Edge highlights with geometric smoothing
                float enhancedEdge = pow(edge, 0.7) * (1.0 - edgeFactor * 0.5);
                float3 edgeColor = enhancedEdge * emissionColor.rgb;
                
                // Calculate glow effect
                float3 glowEffect = calculateGlow(normalWS, viewDirWS, baseColorWithMetallic, emissionColor.rgb, fresnelFactor);
                
                // Enhanced emission with glow and streak
                float3 enhancedEmissive = emissionColor.rgb * fresnelFactor + glowEffect + blendedStreak;
                
                float3 finalColor = metallicColor + enhancedEmissive + edgeColor;
                
                // Apply edge-based smoothing to final color
                finalColor = lerp(finalColor, finalColor * (1.0 + edgeFactor * 0.2), 0.5);
                finalColor = pow(finalColor, 0.95); // Slight contrast enhancement
                
                // Apply fog
                float fogFactor = ComputeFogFactor(input.positionCS.z);
                finalColor = MixFog(finalColor, fogFactor);
                
                return float4(finalColor, 1.0);
            }
            ENDHLSL
        }

        Pass
        {
            Name "ShadowCaster"
            Tags{"LightMode" = "ShadowCaster"}

            ZWrite On
            ZTest LEqual
            ColorMask 0
            Cull Back

            HLSLPROGRAM
            #pragma exclude_renderers gles gles3 glcore
            #pragma target 4.5

            #pragma vertex ShadowPassVertex
            #pragma fragment ShadowPassFragment

            #pragma multi_compile_instancing
            #pragma multi_compile _ DOTS_INSTANCING_ON

            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Lighting.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/ShaderVariablesFunctions.hlsl"
            #include "Packages/com.unity.render-pipelines.core/ShaderLibrary/CommonMaterial.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Shadows.hlsl"

            float3 _LightDirection;

            struct ShadowAttributes
            {
                float4 positionOS : POSITION;
                float3 normalOS : NORMAL;
                float2 texcoord : TEXCOORD0;
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            struct ShadowVaryings
            {
                float4 positionCS : SV_POSITION;
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            float4 GetShadowPositionHClip(ShadowAttributes input)
            {
                UNITY_SETUP_INSTANCE_ID(input);
                float3 positionWS = TransformObjectToWorld(input.positionOS.xyz);
                float3 normalWS = TransformObjectToWorldNormal(input.normalOS);

                float4 positionCS = TransformWorldToHClip(ApplyShadowBias(positionWS, normalWS, _LightDirection));

            #if UNITY_REVERSED_Z
                positionCS.z = min(positionCS.z, UNITY_NEAR_CLIP_VALUE);
            #else
                positionCS.z = max(positionCS.z, UNITY_NEAR_CLIP_VALUE);
            #endif

                return positionCS;
            }

            ShadowVaryings ShadowPassVertex(ShadowAttributes input)
            {
                ShadowVaryings output;
                UNITY_SETUP_INSTANCE_ID(input);
                UNITY_TRANSFER_INSTANCE_ID(input, output);
                output.positionCS = GetShadowPositionHClip(input);
                return output;
            }

            half4 ShadowPassFragment(ShadowVaryings input) : SV_TARGET
            {
                UNITY_SETUP_INSTANCE_ID(input);
                return 0;
            }
            ENDHLSL
        }

        Pass
        {
            Name "DepthOnly"
            Tags{"LightMode" = "DepthOnly"}

            ZWrite On
            ColorMask 0
            Cull Back

            HLSLPROGRAM
            #pragma exclude_renderers gles gles3 glcore
            #pragma target 4.5

            #pragma vertex DepthOnlyVertex
            #pragma fragment DepthOnlyFragment

            #pragma multi_compile_instancing
            #pragma multi_compile _ DOTS_INSTANCING_ON

            struct DepthOnlyAttributes
            {
                float4 position : POSITION;
                float2 texcoord : TEXCOORD0;
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            struct DepthOnlyVaryings
            {
                float2 uv : TEXCOORD0;
                float4 positionCS : SV_POSITION;
                UNITY_VERTEX_INPUT_INSTANCE_ID
                UNITY_VERTEX_OUTPUT_STEREO
            };

            DepthOnlyVaryings DepthOnlyVertex(DepthOnlyAttributes input)
            {
                DepthOnlyVaryings output = (DepthOnlyVaryings)0;
                UNITY_SETUP_INSTANCE_ID(input);
                UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(output);
                UNITY_TRANSFER_INSTANCE_ID(input, output);

                output.positionCS = TransformObjectToHClip(input.position.xyz);
                output.uv = TRANSFORM_TEX(input.texcoord, _BaseMap);
                return output;
            }

            half4 DepthOnlyFragment(DepthOnlyVaryings input) : SV_TARGET
            {
                UNITY_SETUP_INSTANCE_ID(input);
                UNITY_SETUP_STEREO_EYE_INDEX_POST_VERTEX(input);
                return 0;
            }
            ENDHLSL
        }

        Pass
        {
            Name "Meta"
            Tags{"LightMode" = "Meta"}

            Cull Off

            HLSLPROGRAM
            #pragma exclude_renderers gles gles3 glcore
            #pragma target 4.5

            #pragma vertex UniversalVertexMeta
            #pragma fragment UniversalFragmentMeta

            #pragma multi_compile_instancing

            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/MetaInput.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/UniversalMetaPass.hlsl"

            struct MetaVertexInput
            {
                float4 positionOS : POSITION;
                float3 normalOS   : NORMAL;
                float2 uv0        : TEXCOORD0;
                float2 uv1        : TEXCOORD1;
                float2 uv2        : TEXCOORD2;
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            half4 UniversalFragmentMeta(MetaVertexInput input) : SV_Target
            {
                UNITY_SETUP_INSTANCE_ID(input);
                
                float2 uv = TRANSFORM_TEX(input.uv0, _BaseMap);
                float4 baseMap = SAMPLE_TEXTURE2D(_BaseMap, sampler_BaseMap, uv);
                
                MetaInput metaInput;
                metaInput.Albedo = baseMap.rgb * GetInstancedBaseColor().rgb;
                metaInput.Emission = GetInstancedEmissionColor().rgb;
                
                #if defined(UNITY_META_SPECULAR_COLOR)
                    metaInput.SpecularColor = float3(0,0,0);
                #endif
                
                #if defined(UNITY_META_INPUT_HAS_ALPHA)
                    metaInput.Alpha = baseMap.a * GetInstancedBaseColor().a;
                #endif
                
                float3 finalRGB = metaInput.Albedo + metaInput.Emission;
                float  finalA   = (baseMap.a * GetInstancedBaseColor().a);

                return half4(finalRGB, finalA);
            }
            ENDHLSL
        }
    }
    FallBack "Hidden/InternalErrorShader"
} 