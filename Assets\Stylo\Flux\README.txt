Flux Effect
Created by <PERSON><PERSON><PERSON>.
Original concept by <PERSON><PERSON> Theory.

-----
USAGE:

Built-in render pipeline:
1) Add Flux Effect component to your camera. Adjust settings.

URP:
1) Import URP.unitypackage by double clicking it.
2) Your URP Renderer asset > Renderer Features > add FluxRenderFeature.
3) Make sure your camera's > Post Processing is on.
4) Effect will now be enabled by default. You can add 'Stylo - Flux Effect' to your Post Process Volume to adjust settings. To disable the effect set Effect Intensity to zero.

----
TIPS:

- Please check out the tooltips on the effect parameters, they will provide a lot of extra information.