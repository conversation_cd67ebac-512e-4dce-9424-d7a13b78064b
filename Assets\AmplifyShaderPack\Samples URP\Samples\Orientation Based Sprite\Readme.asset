%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e78934177bebe0545ab566bb787b0282, type: 3}
  m_Name: Readme
  m_EditorClassIdentifier: 
  Icon: {fileID: 2800000, guid: a9185a1f058633643993c1458d85f5f7, type: 3}
  Title: Orientation Based Sprite
  Description:
    Heading: 
    Text: This sample shows how to access a spritesheet according to current camera
      horizontal orientation similar to old Doom games.
    LinkText: 
    Url: 
  PropertiesHeader:
    Heading: Properties
    Text: 
    LinkText: 
    Url: 
  Properties:
  - Heading: Spritesheet
    Text: 'Texture containing all the necessary sprites for each view. '
    LinkText: 
    Url: 
  - Heading: Columns
    Text: Amount of columns on the spritesheet.
    LinkText: 
    Url: 
  - Heading: Rows
    Text: Amount of rows on the spritesheet.
    LinkText: 
    Url: 
  - Heading: Anim Speed
    Text: Animation speed for each sprite section.
    LinkText: 
    Url: 
  AdditionalProperties: []
  AdditionalScripts: []
  LoadedLayout: 0
  RPType: 3
