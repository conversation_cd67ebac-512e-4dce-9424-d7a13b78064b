﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class FlexalonRandomLayoutEditor
 | Flexalon 3D Layouts </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class FlexalonRandomLayoutEditor
 | Flexalon 3D Layouts ">
    <meta name="generator" content="docfx ">
  
    <link rel="shortcut icon" href="images/favicon.svg">
    <link rel="stylesheet" href="styles/docfx.vendor.css">
    <link rel="stylesheet" href="styles/docfx.css">
    <link rel="stylesheet" href="styles/main.css">
    <meta property="docfx:navrel" content="tochead.html">
    <meta property="docfx:tocrel" content="toc.html">
  
  <meta property="docfx:rel" content="">
  
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="https://www.flexalon.com/">
                <img id="logo" class="svg" src="images/logo_32x32.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first="First" data-prev="Previous" data-next="Next" data-last="Last"></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">

        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="Flexalon.Editor.FlexalonRandomLayoutEditor">


  <h1 id="Flexalon_Editor_FlexalonRandomLayoutEditor" data-uid="Flexalon.Editor.FlexalonRandomLayoutEditor" class="text-break">Class FlexalonRandomLayoutEditor
</h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">object</span></div>
    <div class="level1"><span class="xref">UnityEngine.Object</span></div>
    <div class="level2"><span class="xref">UnityEngine.ScriptableObject</span></div>
    <div class="level3"><span class="xref">UnityEditor.Editor</span></div>
    <div class="level4"><a class="xref" href="Flexalon.Editor.FlexalonComponentEditor.html">FlexalonComponentEditor</a></div>
    <div class="level5"><span class="xref">FlexalonRandomLayoutEditor</span></div>
  </div>
  <div class="inheritedMembers">
    <h5>Inherited Members</h5>
    <div>
      <a class="xref" href="Flexalon.Editor.FlexalonComponentEditor.html#Flexalon_Editor_FlexalonComponentEditor_Create__1_System_String_UnityEngine_Object_">FlexalonComponentEditor.Create&lt;T&gt;(string, Object)</a>
    </div>
    <div>
      <a class="xref" href="Flexalon.Editor.FlexalonComponentEditor.html#Flexalon_Editor_FlexalonComponentEditor_ForceUpdateButton">FlexalonComponentEditor.ForceUpdateButton()</a>
    </div>
    <div>
      <a class="xref" href="Flexalon.Editor.FlexalonComponentEditor.html#Flexalon_Editor_FlexalonComponentEditor_ApplyModifiedProperties">FlexalonComponentEditor.ApplyModifiedProperties()</a>
    </div>
    <div>
      <a class="xref" href="Flexalon.Editor.FlexalonComponentEditor.html#Flexalon_Editor_FlexalonComponentEditor_Record_Flexalon_FlexalonComponent_">FlexalonComponentEditor.Record(FlexalonComponent)</a>
    </div>
    <div>
      <a class="xref" href="Flexalon.Editor.FlexalonComponentEditor.html#Flexalon_Editor_FlexalonComponentEditor_MarkDirty_Flexalon_FlexalonComponent_">FlexalonComponentEditor.MarkDirty(FlexalonComponent)</a>
    </div>
    <div>
      <a class="xref" href="Flexalon.Editor.FlexalonComponentEditor.html#Flexalon_Editor_FlexalonComponentEditor_ForceUpdate_Flexalon_FlexalonComponent_">FlexalonComponentEditor.ForceUpdate(FlexalonComponent)</a>
    </div>
  </div>
  <h5 id="Flexalon_Editor_FlexalonRandomLayoutEditor_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">[CustomEditor(typeof(FlexalonRandomLayout))]
[CanEditMultipleObjects]
public class FlexalonRandomLayoutEditor : FlexalonComponentEditor</code></pre>
  </div>
  <h3 id="methods">Methods
</h3>
  <a id="Flexalon_Editor_FlexalonRandomLayoutEditor_Create_" data-uid="Flexalon.Editor.FlexalonRandomLayoutEditor.Create*"></a>
  <h4 id="Flexalon_Editor_FlexalonRandomLayoutEditor_Create_UnityEditor_MenuCommand_" data-uid="Flexalon.Editor.FlexalonRandomLayoutEditor.Create(UnityEditor.MenuCommand)">Create(MenuCommand)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">[MenuItem(&quot;GameObject/Flexalon/Random Layout&quot;)]
public static void Create(MenuCommand command)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">UnityEditor.MenuCommand</span></td>
        <td><span class="parametername">command</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_Editor_FlexalonRandomLayoutEditor_OnInspectorGUI_" data-uid="Flexalon.Editor.FlexalonRandomLayoutEditor.OnInspectorGUI*"></a>
  <h4 id="Flexalon_Editor_FlexalonRandomLayoutEditor_OnInspectorGUI" data-uid="Flexalon.Editor.FlexalonRandomLayoutEditor.OnInspectorGUI">OnInspectorGUI()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override void OnInspectorGUI()</code></pre>
  </div>
  <h5 class="overrides">Overrides</h5>
  <div><span class="xref">UnityEditor.Editor.OnInspectorGUI()</span></div>
</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In This Article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      Flexalon 3D Layouts
      
          </div>
        </div>
      </footer>
    </div>

    
    
    
  </body>
</html>
