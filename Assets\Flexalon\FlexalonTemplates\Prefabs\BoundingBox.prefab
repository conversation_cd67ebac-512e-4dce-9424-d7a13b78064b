%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &881125077781222217
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5380904882414257483}
  - component: {fileID: 7603905490493502819}
  - component: {fileID: 1009587246893188886}
  - component: {fileID: 6807006372017771642}
  - component: {fileID: 7771098058122296088}
  - component: {fileID: 2398550571237592606}
  - component: {fileID: 823836958081907092}
  - component: {fileID: 5778092113291966552}
  m_Layer: 0
  m_Name: Edge (10)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5380904882414257483
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 881125077781222217}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0.5, y: 0.5, z: 0}
  m_LocalScale: {x: 0.25, y: 0.25, z: 1}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 5201721041018657060}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &7603905490493502819
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 881125077781222217}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &1009587246893188886
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 881125077781222217}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &6807006372017771642
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 881125077781222217}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!114 &7771098058122296088
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 881125077781222217}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994c66b6148109b489689ed705d244cd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Parent: {fileID: 0}
  SiblingIndex: 1
  LayoutPosition: {x: 0.5, y: 0.5, z: 0}
  LayoutRotation: {x: 0, y: 0, z: 0, w: 1}
  AdapterBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.125, y: 0.125, z: 0.5}
  LayoutBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.125, y: 0.125, z: 0.5}
  RotatedAndScaledBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.125, y: 0.125, z: 0.5}
  ComponentScale: {x: 0.25, y: 0.25, z: 1}
  FillSize: {x: 0, y: 0, z: 0}
  ShrinkSize: {x: 999999, y: 999999, z: 999999}
  TargetPosition: {x: 0.5, y: 0.5, z: 0}
  TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  TargetScale: {x: 0.25, y: 0.25, z: 1}
  TargetRectSize: {x: 0, y: 0, z: 0}
  TransformPosition: {x: 0.5, y: 0.5, z: 0}
  TransformRotation: {x: 0, y: 0, z: 0, w: 1}
  TransformScale: {x: 0.25, y: 0.25, z: 1}
  TransformRectSize: {x: 0, y: 0}
--- !u!114 &2398550571237592606
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 881125077781222217}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 00b0da67f6b3c9a4e84ed47af9da4884, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _version: 1
  _widthType: 0
  _width: 0.25
  _widthOfParent: 1
  _heightType: 0
  _height: 0.25
  _heightOfParent: 1
  _depthType: 1
  _depth: 0.25
  _depthOfParent: 1
  _minWidthType: 0
  _minWidth: 0
  _minWidthOfParent: 0
  _minHeightType: 0
  _minHeight: 0
  _minHeightOfParent: 0
  _minDepthType: 0
  _minDepth: 0
  _minDepthOfParent: 0
  _maxWidthType: 0
  _maxWidth: 1
  _maxWidthOfParent: 1
  _maxHeightType: 0
  _maxHeight: 1
  _maxHeightOfParent: 1
  _maxDepthType: 0
  _maxDepth: 1
  _maxDepthOfParent: 1
  _offset: {x: 0, y: 0, z: 0}
  _scale: {x: 1, y: 1, z: 1}
  _rotation: {x: 0, y: 0, z: 0, w: 1}
  _marginLeft: 0
  _marginRight: 0
  _marginTop: 0
  _marginBottom: 0
  _marginFront: 0
  _marginBack: 0
  _paddingLeft: 0
  _paddingRight: 0
  _paddingTop: 0
  _paddingBottom: 0
  _paddingFront: 0
  _paddingBack: 0
  _skipLayout: 0
--- !u!114 &823836958081907092
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 881125077781222217}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5bc6c8a2e47ba064e8c2a0953ba81718, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _version: 1
  _target: {fileID: 5201721041018657067}
  _horizontalAlign: 2
  _verticalAlign: 2
  _depthAlign: 1
  _horizontalPivot: 1
  _verticalPivot: 1
  _depthPivot: 1
--- !u!114 &5778092113291966552
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 881125077781222217}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: aba4048c0905b9947bd3cc885659b584, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Standard: {fileID: 2100000, guid: d37fe08da39fb7a49b506268d2230e07, type: 2}
  URP: {fileID: 2100000, guid: ffbdc48912193e94ca6f086ef2338161, type: 2}
  HDRP: {fileID: 0}
  _color: {r: 1, g: 1, b: 1, a: 1}
--- !u!1 &891728526032282740
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8151845709390135717}
  - component: {fileID: 8638786418626738166}
  - component: {fileID: 4939752730872289074}
  - component: {fileID: 4573000282614208280}
  - component: {fileID: 1326684295857504302}
  - component: {fileID: 913296051124644017}
  - component: {fileID: 3528567465711244133}
  - component: {fileID: 6589799639234063059}
  m_Layer: 0
  m_Name: Edge (4)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8151845709390135717
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 891728526032282740}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0.5, y: 0, z: 0.5}
  m_LocalScale: {x: 0.25, y: 1, z: 0.25}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 5201721041018657060}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &8638786418626738166
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 891728526032282740}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &4939752730872289074
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 891728526032282740}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &4573000282614208280
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 891728526032282740}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!114 &1326684295857504302
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 891728526032282740}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994c66b6148109b489689ed705d244cd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Parent: {fileID: 0}
  SiblingIndex: 1
  LayoutPosition: {x: -0.5, y: 0, z: 0.5}
  LayoutRotation: {x: 0, y: 0, z: 0, w: 1}
  AdapterBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.125, y: 0.5, z: 0.125}
  LayoutBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.125, y: 0.5, z: 0.125}
  RotatedAndScaledBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.125, y: 0.5, z: 0.125}
  ComponentScale: {x: 0.25, y: 1, z: 0.25}
  FillSize: {x: 0, y: 0, z: 0}
  ShrinkSize: {x: 999999, y: 999999, z: 999999}
  TargetPosition: {x: -0.5, y: 0, z: 0.5}
  TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  TargetScale: {x: 0.25, y: 1, z: 0.25}
  TargetRectSize: {x: 0, y: 0, z: 0}
  TransformPosition: {x: -0.5, y: 0, z: 0.5}
  TransformRotation: {x: 0, y: 0, z: 0, w: 1}
  TransformScale: {x: 0.25, y: 1, z: 0.25}
  TransformRectSize: {x: 0, y: 0}
--- !u!114 &913296051124644017
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 891728526032282740}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 00b0da67f6b3c9a4e84ed47af9da4884, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _version: 1
  _widthType: 0
  _width: 0.25
  _widthOfParent: 1
  _heightType: 1
  _height: 0.25
  _heightOfParent: 1
  _depthType: 0
  _depth: 0.25
  _depthOfParent: 1
  _minWidthType: 0
  _minWidth: 0
  _minWidthOfParent: 0
  _minHeightType: 0
  _minHeight: 0
  _minHeightOfParent: 0
  _minDepthType: 0
  _minDepth: 0
  _minDepthOfParent: 0
  _maxWidthType: 0
  _maxWidth: 1
  _maxWidthOfParent: 1
  _maxHeightType: 0
  _maxHeight: 1
  _maxHeightOfParent: 1
  _maxDepthType: 0
  _maxDepth: 1
  _maxDepthOfParent: 1
  _offset: {x: 0, y: 0, z: 0}
  _scale: {x: 1, y: 1, z: 1}
  _rotation: {x: 0, y: 0, z: 0, w: 1}
  _marginLeft: 0
  _marginRight: 0
  _marginTop: 0
  _marginBottom: 0
  _marginFront: 0
  _marginBack: 0
  _paddingLeft: 0
  _paddingRight: 0
  _paddingTop: 0
  _paddingBottom: 0
  _paddingFront: 0
  _paddingBack: 0
  _skipLayout: 0
--- !u!114 &3528567465711244133
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 891728526032282740}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5bc6c8a2e47ba064e8c2a0953ba81718, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _version: 1
  _target: {fileID: 5201721041018657067}
  _horizontalAlign: 0
  _verticalAlign: 1
  _depthAlign: 2
  _horizontalPivot: 1
  _verticalPivot: 1
  _depthPivot: 1
--- !u!114 &6589799639234063059
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 891728526032282740}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: aba4048c0905b9947bd3cc885659b584, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Standard: {fileID: 2100000, guid: d37fe08da39fb7a49b506268d2230e07, type: 2}
  URP: {fileID: 2100000, guid: ffbdc48912193e94ca6f086ef2338161, type: 2}
  HDRP: {fileID: 0}
  _color: {r: 1, g: 1, b: 1, a: 1}
--- !u!1 &957980734175238827
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 542407387717635824}
  - component: {fileID: 7105394085760076604}
  - component: {fileID: 6447335428942401080}
  - component: {fileID: 8950800478943111373}
  - component: {fileID: 3416838197651460391}
  - component: {fileID: 1403488910124591512}
  - component: {fileID: 3151440652466304917}
  - component: {fileID: 1998821328042711247}
  m_Layer: 0
  m_Name: Corner
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &542407387717635824
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 957980734175238827}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0.5, y: 0.5, z: 0.5}
  m_LocalScale: {x: 0.5, y: 0.5, z: 0.5}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 5201721041018657060}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &7105394085760076604
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 957980734175238827}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &6447335428942401080
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 957980734175238827}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &8950800478943111373
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 957980734175238827}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!114 &3416838197651460391
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 957980734175238827}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994c66b6148109b489689ed705d244cd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Parent: {fileID: 0}
  SiblingIndex: 1
  LayoutPosition: {x: 0.5, y: 0.5, z: 0.5}
  LayoutRotation: {x: 0, y: 0, z: 0, w: 1}
  AdapterBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.25, y: 0.25, z: 0.25}
  LayoutBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.25, y: 0.25, z: 0.25}
  RotatedAndScaledBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.25, y: 0.25, z: 0.25}
  ComponentScale: {x: 0.5, y: 0.5, z: 0.5}
  FillSize: {x: 0, y: 0, z: 0}
  ShrinkSize: {x: 999999, y: 999999, z: 999999}
  TargetPosition: {x: 0.5, y: 0.5, z: 0.5}
  TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  TargetScale: {x: 0.5, y: 0.5, z: 0.5}
  TargetRectSize: {x: 0, y: 0, z: 0}
  TransformPosition: {x: 0.5, y: 0.5, z: 0.5}
  TransformRotation: {x: 0, y: 0, z: 0, w: 1}
  TransformScale: {x: 0.5, y: 0.5, z: 0.5}
  TransformRectSize: {x: 0, y: 0}
--- !u!114 &1403488910124591512
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 957980734175238827}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 00b0da67f6b3c9a4e84ed47af9da4884, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _version: 1
  _widthType: 2
  _width: 1
  _widthOfParent: 1
  _heightType: 2
  _height: 1
  _heightOfParent: 1
  _depthType: 0
  _depth: 0.5
  _depthOfParent: 1
  _minWidthType: 0
  _minWidth: 0
  _minWidthOfParent: 0
  _minHeightType: 0
  _minHeight: 0
  _minHeightOfParent: 0
  _minDepthType: 0
  _minDepth: 0
  _minDepthOfParent: 0
  _maxWidthType: 0
  _maxWidth: 1
  _maxWidthOfParent: 1
  _maxHeightType: 0
  _maxHeight: 1
  _maxHeightOfParent: 1
  _maxDepthType: 0
  _maxDepth: 1
  _maxDepthOfParent: 1
  _offset: {x: 0, y: 0, z: 0}
  _scale: {x: 1, y: 1, z: 1}
  _rotation: {x: 0, y: 0, z: 0, w: 1}
  _marginLeft: 0
  _marginRight: 0
  _marginTop: 0
  _marginBottom: 0
  _marginFront: 0
  _marginBack: 0
  _paddingLeft: 0
  _paddingRight: 0
  _paddingTop: 0
  _paddingBottom: 0
  _paddingFront: 0
  _paddingBack: 0
  _skipLayout: 0
--- !u!114 &3151440652466304917
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 957980734175238827}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5bc6c8a2e47ba064e8c2a0953ba81718, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _version: 1
  _target: {fileID: 5201721041018657067}
  _horizontalAlign: 2
  _verticalAlign: 2
  _depthAlign: 2
  _horizontalPivot: 1
  _verticalPivot: 1
  _depthPivot: 1
--- !u!114 &1998821328042711247
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 957980734175238827}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: aba4048c0905b9947bd3cc885659b584, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Standard: {fileID: 2100000, guid: d37fe08da39fb7a49b506268d2230e07, type: 2}
  URP: {fileID: 2100000, guid: ffbdc48912193e94ca6f086ef2338161, type: 2}
  HDRP: {fileID: 0}
  _color: {r: 1, g: 1, b: 1, a: 1}
--- !u!1 &1193464675404599060
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3034872380917434648}
  - component: {fileID: 5473252175801523423}
  - component: {fileID: 2507976538255126679}
  - component: {fileID: 5116944658941958229}
  - component: {fileID: 2988002699125123182}
  - component: {fileID: 8272783428700371278}
  - component: {fileID: 2954053044695223759}
  - component: {fileID: 3825923604314152666}
  m_Layer: 0
  m_Name: Edge (3)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3034872380917434648
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1193464675404599060}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0.5, y: 0, z: -0.5}
  m_LocalScale: {x: 0.25, y: 1, z: 0.25}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 5201721041018657060}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &5473252175801523423
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1193464675404599060}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &2507976538255126679
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1193464675404599060}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &5116944658941958229
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1193464675404599060}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!114 &2988002699125123182
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1193464675404599060}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994c66b6148109b489689ed705d244cd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Parent: {fileID: 0}
  SiblingIndex: 1
  LayoutPosition: {x: -0.5, y: 0, z: -0.5}
  LayoutRotation: {x: 0, y: 0, z: 0, w: 1}
  AdapterBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.125, y: 0.5, z: 0.125}
  LayoutBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.125, y: 0.5, z: 0.125}
  RotatedAndScaledBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.125, y: 0.5, z: 0.125}
  ComponentScale: {x: 0.25, y: 1, z: 0.25}
  FillSize: {x: 0, y: 0, z: 0}
  ShrinkSize: {x: 999999, y: 999999, z: 999999}
  TargetPosition: {x: -0.5, y: 0, z: -0.5}
  TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  TargetScale: {x: 0.25, y: 1, z: 0.25}
  TargetRectSize: {x: 0, y: 0, z: 0}
  TransformPosition: {x: -0.5, y: 0, z: -0.5}
  TransformRotation: {x: 0, y: 0, z: 0, w: 1}
  TransformScale: {x: 0.25, y: 1, z: 0.25}
  TransformRectSize: {x: 0, y: 0}
--- !u!114 &8272783428700371278
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1193464675404599060}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 00b0da67f6b3c9a4e84ed47af9da4884, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _version: 1
  _widthType: 0
  _width: 0.25
  _widthOfParent: 1
  _heightType: 1
  _height: 0.25
  _heightOfParent: 1
  _depthType: 0
  _depth: 0.25
  _depthOfParent: 1
  _minWidthType: 0
  _minWidth: 0
  _minWidthOfParent: 0
  _minHeightType: 0
  _minHeight: 0
  _minHeightOfParent: 0
  _minDepthType: 0
  _minDepth: 0
  _minDepthOfParent: 0
  _maxWidthType: 0
  _maxWidth: 1
  _maxWidthOfParent: 1
  _maxHeightType: 0
  _maxHeight: 1
  _maxHeightOfParent: 1
  _maxDepthType: 0
  _maxDepth: 1
  _maxDepthOfParent: 1
  _offset: {x: 0, y: 0, z: 0}
  _scale: {x: 1, y: 1, z: 1}
  _rotation: {x: 0, y: 0, z: 0, w: 1}
  _marginLeft: 0
  _marginRight: 0
  _marginTop: 0
  _marginBottom: 0
  _marginFront: 0
  _marginBack: 0
  _paddingLeft: 0
  _paddingRight: 0
  _paddingTop: 0
  _paddingBottom: 0
  _paddingFront: 0
  _paddingBack: 0
  _skipLayout: 0
--- !u!114 &2954053044695223759
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1193464675404599060}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5bc6c8a2e47ba064e8c2a0953ba81718, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _version: 1
  _target: {fileID: 5201721041018657067}
  _horizontalAlign: 0
  _verticalAlign: 1
  _depthAlign: 0
  _horizontalPivot: 1
  _verticalPivot: 1
  _depthPivot: 1
--- !u!114 &3825923604314152666
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1193464675404599060}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: aba4048c0905b9947bd3cc885659b584, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Standard: {fileID: 2100000, guid: d37fe08da39fb7a49b506268d2230e07, type: 2}
  URP: {fileID: 2100000, guid: ffbdc48912193e94ca6f086ef2338161, type: 2}
  HDRP: {fileID: 0}
  _color: {r: 1, g: 1, b: 1, a: 1}
--- !u!1 &1577478050910113134
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 920386800119566862}
  - component: {fileID: 2475168324337728290}
  - component: {fileID: 4170979909564136625}
  - component: {fileID: 7847776133344902020}
  - component: {fileID: 5469464483627771073}
  - component: {fileID: 92914438885376262}
  - component: {fileID: 7658643827287918341}
  - component: {fileID: 5807561747044541910}
  m_Layer: 0
  m_Name: Corner
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &920386800119566862
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1577478050910113134}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0.5, y: -0.5, z: 0.5}
  m_LocalScale: {x: 0.5, y: 0.5, z: 0.5}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 5201721041018657060}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &2475168324337728290
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1577478050910113134}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &4170979909564136625
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1577478050910113134}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &7847776133344902020
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1577478050910113134}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!114 &5469464483627771073
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1577478050910113134}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994c66b6148109b489689ed705d244cd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Parent: {fileID: 0}
  SiblingIndex: 1
  LayoutPosition: {x: -0.5, y: -0.5, z: 0.5}
  LayoutRotation: {x: 0, y: 0, z: 0, w: 1}
  AdapterBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.25, y: 0.25, z: 0.25}
  LayoutBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.25, y: 0.25, z: 0.25}
  RotatedAndScaledBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.25, y: 0.25, z: 0.25}
  ComponentScale: {x: 0.5, y: 0.5, z: 0.5}
  FillSize: {x: 0, y: 0, z: 0}
  ShrinkSize: {x: 999999, y: 999999, z: 999999}
  TargetPosition: {x: -0.5, y: -0.5, z: 0.5}
  TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  TargetScale: {x: 0.5, y: 0.5, z: 0.5}
  TargetRectSize: {x: 0, y: 0, z: 0}
  TransformPosition: {x: -0.5, y: -0.5, z: 0.5}
  TransformRotation: {x: 0, y: 0, z: 0, w: 1}
  TransformScale: {x: 0.5, y: 0.5, z: 0.5}
  TransformRectSize: {x: 0, y: 0}
--- !u!114 &92914438885376262
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1577478050910113134}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 00b0da67f6b3c9a4e84ed47af9da4884, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _version: 1
  _widthType: 2
  _width: 1
  _widthOfParent: 1
  _heightType: 2
  _height: 1
  _heightOfParent: 1
  _depthType: 0
  _depth: 0.5
  _depthOfParent: 1
  _minWidthType: 0
  _minWidth: 0
  _minWidthOfParent: 0
  _minHeightType: 0
  _minHeight: 0
  _minHeightOfParent: 0
  _minDepthType: 0
  _minDepth: 0
  _minDepthOfParent: 0
  _maxWidthType: 0
  _maxWidth: 1
  _maxWidthOfParent: 1
  _maxHeightType: 0
  _maxHeight: 1
  _maxHeightOfParent: 1
  _maxDepthType: 0
  _maxDepth: 1
  _maxDepthOfParent: 1
  _offset: {x: 0, y: 0, z: 0}
  _scale: {x: 1, y: 1, z: 1}
  _rotation: {x: 0, y: 0, z: 0, w: 1}
  _marginLeft: 0
  _marginRight: 0
  _marginTop: 0
  _marginBottom: 0
  _marginFront: 0
  _marginBack: 0
  _paddingLeft: 0
  _paddingRight: 0
  _paddingTop: 0
  _paddingBottom: 0
  _paddingFront: 0
  _paddingBack: 0
  _skipLayout: 0
--- !u!114 &7658643827287918341
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1577478050910113134}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5bc6c8a2e47ba064e8c2a0953ba81718, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _version: 1
  _target: {fileID: 5201721041018657067}
  _horizontalAlign: 0
  _verticalAlign: 0
  _depthAlign: 2
  _horizontalPivot: 1
  _verticalPivot: 1
  _depthPivot: 1
--- !u!114 &5807561747044541910
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1577478050910113134}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: aba4048c0905b9947bd3cc885659b584, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Standard: {fileID: 2100000, guid: d37fe08da39fb7a49b506268d2230e07, type: 2}
  URP: {fileID: 2100000, guid: ffbdc48912193e94ca6f086ef2338161, type: 2}
  HDRP: {fileID: 0}
  _color: {r: 1, g: 1, b: 1, a: 1}
--- !u!1 &2067343434197702451
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6759271806915010934}
  - component: {fileID: 8520017619298488729}
  - component: {fileID: 984666945581129036}
  - component: {fileID: 2755075685843919930}
  - component: {fileID: 2023912460244687771}
  - component: {fileID: 2947844669319729928}
  - component: {fileID: 6270305960998297463}
  - component: {fileID: 7287003807377496657}
  m_Layer: 0
  m_Name: Corner
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6759271806915010934
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2067343434197702451}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0.5, y: 0.5, z: -0.5}
  m_LocalScale: {x: 0.5, y: 0.5, z: 0.5}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 5201721041018657060}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &8520017619298488729
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2067343434197702451}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &984666945581129036
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2067343434197702451}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &2755075685843919930
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2067343434197702451}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!114 &2023912460244687771
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2067343434197702451}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994c66b6148109b489689ed705d244cd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Parent: {fileID: 0}
  SiblingIndex: 1
  LayoutPosition: {x: -0.5, y: 0.5, z: -0.5}
  LayoutRotation: {x: 0, y: 0, z: 0, w: 1}
  AdapterBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.25, y: 0.25, z: 0.25}
  LayoutBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.25, y: 0.25, z: 0.25}
  RotatedAndScaledBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.25, y: 0.25, z: 0.25}
  ComponentScale: {x: 0.5, y: 0.5, z: 0.5}
  FillSize: {x: 0, y: 0, z: 0}
  ShrinkSize: {x: 999999, y: 999999, z: 999999}
  TargetPosition: {x: -0.5, y: 0.5, z: -0.5}
  TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  TargetScale: {x: 0.5, y: 0.5, z: 0.5}
  TargetRectSize: {x: 0, y: 0, z: 0}
  TransformPosition: {x: -0.5, y: 0.5, z: -0.5}
  TransformRotation: {x: 0, y: 0, z: 0, w: 1}
  TransformScale: {x: 0.5, y: 0.5, z: 0.5}
  TransformRectSize: {x: 0, y: 0}
--- !u!114 &2947844669319729928
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2067343434197702451}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 00b0da67f6b3c9a4e84ed47af9da4884, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _version: 1
  _widthType: 2
  _width: 1
  _widthOfParent: 1
  _heightType: 2
  _height: 1
  _heightOfParent: 1
  _depthType: 0
  _depth: 0.5
  _depthOfParent: 1
  _minWidthType: 0
  _minWidth: 0
  _minWidthOfParent: 0
  _minHeightType: 0
  _minHeight: 0
  _minHeightOfParent: 0
  _minDepthType: 0
  _minDepth: 0
  _minDepthOfParent: 0
  _maxWidthType: 0
  _maxWidth: 1
  _maxWidthOfParent: 1
  _maxHeightType: 0
  _maxHeight: 1
  _maxHeightOfParent: 1
  _maxDepthType: 0
  _maxDepth: 1
  _maxDepthOfParent: 1
  _offset: {x: 0, y: 0, z: 0}
  _scale: {x: 1, y: 1, z: 1}
  _rotation: {x: 0, y: 0, z: 0, w: 1}
  _marginLeft: 0
  _marginRight: 0
  _marginTop: 0
  _marginBottom: 0
  _marginFront: 0
  _marginBack: 0
  _paddingLeft: 0
  _paddingRight: 0
  _paddingTop: 0
  _paddingBottom: 0
  _paddingFront: 0
  _paddingBack: 0
  _skipLayout: 0
--- !u!114 &6270305960998297463
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2067343434197702451}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5bc6c8a2e47ba064e8c2a0953ba81718, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _version: 1
  _target: {fileID: 5201721041018657067}
  _horizontalAlign: 0
  _verticalAlign: 2
  _depthAlign: 0
  _horizontalPivot: 1
  _verticalPivot: 1
  _depthPivot: 1
--- !u!114 &7287003807377496657
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2067343434197702451}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: aba4048c0905b9947bd3cc885659b584, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Standard: {fileID: 2100000, guid: d37fe08da39fb7a49b506268d2230e07, type: 2}
  URP: {fileID: 2100000, guid: ffbdc48912193e94ca6f086ef2338161, type: 2}
  HDRP: {fileID: 0}
  _color: {r: 1, g: 1, b: 1, a: 1}
--- !u!1 &2700797952683796505
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1222129817647586004}
  - component: {fileID: 7926451554489324872}
  - component: {fileID: 7918188674348168055}
  - component: {fileID: 8226823929810911326}
  - component: {fileID: 4283953625415660941}
  - component: {fileID: 6036612763073819525}
  - component: {fileID: 730510800005748713}
  - component: {fileID: 822530311591254265}
  m_Layer: 0
  m_Name: Edge (6)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1222129817647586004
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2700797952683796505}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0.5, y: 0, z: 0.5}
  m_LocalScale: {x: 0.25, y: 1, z: 0.25}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 5201721041018657060}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &7926451554489324872
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2700797952683796505}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &7918188674348168055
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2700797952683796505}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &8226823929810911326
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2700797952683796505}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!114 &4283953625415660941
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2700797952683796505}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994c66b6148109b489689ed705d244cd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Parent: {fileID: 0}
  SiblingIndex: 1
  LayoutPosition: {x: 0.5, y: 0, z: 0.5}
  LayoutRotation: {x: 0, y: 0, z: 0, w: 1}
  AdapterBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.125, y: 0.5, z: 0.125}
  LayoutBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.125, y: 0.5, z: 0.125}
  RotatedAndScaledBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.125, y: 0.5, z: 0.125}
  ComponentScale: {x: 0.25, y: 1, z: 0.25}
  FillSize: {x: 0, y: 0, z: 0}
  ShrinkSize: {x: 999999, y: 999999, z: 999999}
  TargetPosition: {x: 0.5, y: 0, z: 0.5}
  TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  TargetScale: {x: 0.25, y: 1, z: 0.25}
  TargetRectSize: {x: 0, y: 0, z: 0}
  TransformPosition: {x: 0.5, y: 0, z: 0.5}
  TransformRotation: {x: 0, y: 0, z: 0, w: 1}
  TransformScale: {x: 0.25, y: 1, z: 0.25}
  TransformRectSize: {x: 0, y: 0}
--- !u!114 &6036612763073819525
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2700797952683796505}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 00b0da67f6b3c9a4e84ed47af9da4884, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _version: 1
  _widthType: 0
  _width: 0.25
  _widthOfParent: 1
  _heightType: 1
  _height: 0.25
  _heightOfParent: 1
  _depthType: 0
  _depth: 0.25
  _depthOfParent: 1
  _minWidthType: 0
  _minWidth: 0
  _minWidthOfParent: 0
  _minHeightType: 0
  _minHeight: 0
  _minHeightOfParent: 0
  _minDepthType: 0
  _minDepth: 0
  _minDepthOfParent: 0
  _maxWidthType: 0
  _maxWidth: 1
  _maxWidthOfParent: 1
  _maxHeightType: 0
  _maxHeight: 1
  _maxHeightOfParent: 1
  _maxDepthType: 0
  _maxDepth: 1
  _maxDepthOfParent: 1
  _offset: {x: 0, y: 0, z: 0}
  _scale: {x: 1, y: 1, z: 1}
  _rotation: {x: 0, y: 0, z: 0, w: 1}
  _marginLeft: 0
  _marginRight: 0
  _marginTop: 0
  _marginBottom: 0
  _marginFront: 0
  _marginBack: 0
  _paddingLeft: 0
  _paddingRight: 0
  _paddingTop: 0
  _paddingBottom: 0
  _paddingFront: 0
  _paddingBack: 0
  _skipLayout: 0
--- !u!114 &730510800005748713
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2700797952683796505}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5bc6c8a2e47ba064e8c2a0953ba81718, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _version: 1
  _target: {fileID: 5201721041018657067}
  _horizontalAlign: 2
  _verticalAlign: 1
  _depthAlign: 2
  _horizontalPivot: 1
  _verticalPivot: 1
  _depthPivot: 1
--- !u!114 &822530311591254265
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2700797952683796505}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: aba4048c0905b9947bd3cc885659b584, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Standard: {fileID: 2100000, guid: d37fe08da39fb7a49b506268d2230e07, type: 2}
  URP: {fileID: 2100000, guid: ffbdc48912193e94ca6f086ef2338161, type: 2}
  HDRP: {fileID: 0}
  _color: {r: 1, g: 1, b: 1, a: 1}
--- !u!1 &3577085792146453012
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7332791520346093443}
  - component: {fileID: 7343749295889620840}
  - component: {fileID: 1928156120635672242}
  - component: {fileID: 1534237889236735269}
  - component: {fileID: 5715417984396781742}
  - component: {fileID: 5655086420671667316}
  - component: {fileID: 2639856103345793407}
  - component: {fileID: 1401054787846901294}
  m_Layer: 0
  m_Name: Corner
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7332791520346093443
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3577085792146453012}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0.5, y: -0.5, z: 0.5}
  m_LocalScale: {x: 0.5, y: 0.5, z: 0.5}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 5201721041018657060}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &7343749295889620840
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3577085792146453012}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &1928156120635672242
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3577085792146453012}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &1534237889236735269
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3577085792146453012}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!114 &5715417984396781742
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3577085792146453012}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994c66b6148109b489689ed705d244cd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Parent: {fileID: 0}
  SiblingIndex: 1
  LayoutPosition: {x: 0.5, y: -0.5, z: 0.5}
  LayoutRotation: {x: 0, y: 0, z: 0, w: 1}
  AdapterBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.25, y: 0.25, z: 0.25}
  LayoutBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.25, y: 0.25, z: 0.25}
  RotatedAndScaledBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.25, y: 0.25, z: 0.25}
  ComponentScale: {x: 0.5, y: 0.5, z: 0.5}
  FillSize: {x: 0, y: 0, z: 0}
  ShrinkSize: {x: 999999, y: 999999, z: 999999}
  TargetPosition: {x: 0.5, y: -0.5, z: 0.5}
  TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  TargetScale: {x: 0.5, y: 0.5, z: 0.5}
  TargetRectSize: {x: 0, y: 0, z: 0}
  TransformPosition: {x: 0.5, y: -0.5, z: 0.5}
  TransformRotation: {x: 0, y: 0, z: 0, w: 1}
  TransformScale: {x: 0.5, y: 0.5, z: 0.5}
  TransformRectSize: {x: 0, y: 0}
--- !u!114 &5655086420671667316
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3577085792146453012}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 00b0da67f6b3c9a4e84ed47af9da4884, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _version: 1
  _widthType: 2
  _width: 1
  _widthOfParent: 1
  _heightType: 2
  _height: 1
  _heightOfParent: 1
  _depthType: 0
  _depth: 0.5
  _depthOfParent: 1
  _minWidthType: 0
  _minWidth: 0
  _minWidthOfParent: 0
  _minHeightType: 0
  _minHeight: 0
  _minHeightOfParent: 0
  _minDepthType: 0
  _minDepth: 0
  _minDepthOfParent: 0
  _maxWidthType: 0
  _maxWidth: 1
  _maxWidthOfParent: 1
  _maxHeightType: 0
  _maxHeight: 1
  _maxHeightOfParent: 1
  _maxDepthType: 0
  _maxDepth: 1
  _maxDepthOfParent: 1
  _offset: {x: 0, y: 0, z: 0}
  _scale: {x: 1, y: 1, z: 1}
  _rotation: {x: 0, y: 0, z: 0, w: 1}
  _marginLeft: 0
  _marginRight: 0
  _marginTop: 0
  _marginBottom: 0
  _marginFront: 0
  _marginBack: 0
  _paddingLeft: 0
  _paddingRight: 0
  _paddingTop: 0
  _paddingBottom: 0
  _paddingFront: 0
  _paddingBack: 0
  _skipLayout: 0
--- !u!114 &2639856103345793407
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3577085792146453012}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5bc6c8a2e47ba064e8c2a0953ba81718, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _version: 1
  _target: {fileID: 5201721041018657067}
  _horizontalAlign: 2
  _verticalAlign: 0
  _depthAlign: 2
  _horizontalPivot: 1
  _verticalPivot: 1
  _depthPivot: 1
--- !u!114 &1401054787846901294
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3577085792146453012}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: aba4048c0905b9947bd3cc885659b584, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Standard: {fileID: 2100000, guid: d37fe08da39fb7a49b506268d2230e07, type: 2}
  URP: {fileID: 2100000, guid: ffbdc48912193e94ca6f086ef2338161, type: 2}
  HDRP: {fileID: 0}
  _color: {r: 1, g: 1, b: 1, a: 1}
--- !u!1 &3684344895990334925
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6423625778075406130}
  - component: {fileID: 4053938095358257296}
  - component: {fileID: 6987984399982235259}
  - component: {fileID: 2006838402888734502}
  - component: {fileID: 4572592129860425619}
  - component: {fileID: 1574924531044751038}
  - component: {fileID: 4689030522890873744}
  - component: {fileID: 3986838653480258697}
  m_Layer: 0
  m_Name: Edge
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6423625778075406130
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3684344895990334925}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: -0.5, z: -0.5}
  m_LocalScale: {x: 1, y: 0.25, z: 0.25}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 5201721041018657060}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &4053938095358257296
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3684344895990334925}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &6987984399982235259
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3684344895990334925}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &2006838402888734502
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3684344895990334925}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!114 &4572592129860425619
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3684344895990334925}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994c66b6148109b489689ed705d244cd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Parent: {fileID: 0}
  SiblingIndex: 1
  LayoutPosition: {x: 0, y: -0.5, z: -0.5}
  LayoutRotation: {x: 0, y: 0, z: 0, w: 1}
  AdapterBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.5, y: 0.125, z: 0.125}
  LayoutBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.5, y: 0.125, z: 0.125}
  RotatedAndScaledBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.5, y: 0.125, z: 0.125}
  ComponentScale: {x: 1, y: 0.25, z: 0.25}
  FillSize: {x: 0, y: 0, z: 0}
  ShrinkSize: {x: 999999, y: 999999, z: 999999}
  TargetPosition: {x: 0, y: -0.5, z: -0.5}
  TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  TargetScale: {x: 1, y: 0.25, z: 0.25}
  TargetRectSize: {x: 0, y: 0, z: 0}
  TransformPosition: {x: 0, y: -0.5, z: -0.5}
  TransformRotation: {x: 0, y: 0, z: 0, w: 1}
  TransformScale: {x: 1, y: 0.25, z: 0.25}
  TransformRectSize: {x: 0, y: 0}
--- !u!114 &1574924531044751038
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3684344895990334925}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 00b0da67f6b3c9a4e84ed47af9da4884, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _version: 1
  _widthType: 1
  _width: 0.1
  _widthOfParent: 1
  _heightType: 0
  _height: 0.25
  _heightOfParent: 1
  _depthType: 0
  _depth: 0.25
  _depthOfParent: 1
  _minWidthType: 0
  _minWidth: 0
  _minWidthOfParent: 0
  _minHeightType: 0
  _minHeight: 0
  _minHeightOfParent: 0
  _minDepthType: 0
  _minDepth: 0
  _minDepthOfParent: 0
  _maxWidthType: 0
  _maxWidth: 1
  _maxWidthOfParent: 1
  _maxHeightType: 0
  _maxHeight: 1
  _maxHeightOfParent: 1
  _maxDepthType: 0
  _maxDepth: 1
  _maxDepthOfParent: 1
  _offset: {x: 0, y: 0, z: 0}
  _scale: {x: 1, y: 1, z: 1}
  _rotation: {x: 0, y: 0, z: 0, w: 1}
  _marginLeft: 0
  _marginRight: 0
  _marginTop: 0
  _marginBottom: 0
  _marginFront: 0
  _marginBack: 0
  _paddingLeft: 0
  _paddingRight: 0
  _paddingTop: 0
  _paddingBottom: 0
  _paddingFront: 0
  _paddingBack: 0
  _skipLayout: 0
--- !u!114 &4689030522890873744
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3684344895990334925}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5bc6c8a2e47ba064e8c2a0953ba81718, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _version: 1
  _target: {fileID: 5201721041018657067}
  _horizontalAlign: 1
  _verticalAlign: 0
  _depthAlign: 0
  _horizontalPivot: 1
  _verticalPivot: 1
  _depthPivot: 1
--- !u!114 &3986838653480258697
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3684344895990334925}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: aba4048c0905b9947bd3cc885659b584, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Standard: {fileID: 2100000, guid: d37fe08da39fb7a49b506268d2230e07, type: 2}
  URP: {fileID: 2100000, guid: ffbdc48912193e94ca6f086ef2338161, type: 2}
  HDRP: {fileID: 0}
  _color: {r: 1, g: 1, b: 1, a: 1}
--- !u!1 &4137177422677864261
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8766202411328382980}
  - component: {fileID: 4378585010608737220}
  - component: {fileID: 1673709045979908585}
  - component: {fileID: 6071610702955278756}
  - component: {fileID: 4236582679361042929}
  - component: {fileID: 7056675006240719823}
  - component: {fileID: 590167112093245038}
  - component: {fileID: 6864302305834158357}
  m_Layer: 0
  m_Name: Edge (8)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8766202411328382980
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4137177422677864261}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0.5, y: 0.5, z: 0}
  m_LocalScale: {x: 0.25, y: 0.25, z: 1}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 5201721041018657060}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &4378585010608737220
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4137177422677864261}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &1673709045979908585
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4137177422677864261}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &6071610702955278756
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4137177422677864261}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!114 &4236582679361042929
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4137177422677864261}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994c66b6148109b489689ed705d244cd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Parent: {fileID: 0}
  SiblingIndex: 1
  LayoutPosition: {x: -0.5, y: 0.5, z: 0}
  LayoutRotation: {x: 0, y: 0, z: 0, w: 1}
  AdapterBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.125, y: 0.125, z: 0.5}
  LayoutBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.125, y: 0.125, z: 0.5}
  RotatedAndScaledBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.125, y: 0.125, z: 0.5}
  ComponentScale: {x: 0.25, y: 0.25, z: 1}
  FillSize: {x: 0, y: 0, z: 0}
  ShrinkSize: {x: 999999, y: 999999, z: 999999}
  TargetPosition: {x: -0.5, y: 0.5, z: 0}
  TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  TargetScale: {x: 0.25, y: 0.25, z: 1}
  TargetRectSize: {x: 0, y: 0, z: 0}
  TransformPosition: {x: -0.5, y: 0.5, z: 0}
  TransformRotation: {x: 0, y: 0, z: 0, w: 1}
  TransformScale: {x: 0.25, y: 0.25, z: 1}
  TransformRectSize: {x: 0, y: 0}
--- !u!114 &7056675006240719823
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4137177422677864261}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 00b0da67f6b3c9a4e84ed47af9da4884, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _version: 1
  _widthType: 0
  _width: 0.25
  _widthOfParent: 1
  _heightType: 0
  _height: 0.25
  _heightOfParent: 1
  _depthType: 1
  _depth: 0.25
  _depthOfParent: 1
  _minWidthType: 0
  _minWidth: 0
  _minWidthOfParent: 0
  _minHeightType: 0
  _minHeight: 0
  _minHeightOfParent: 0
  _minDepthType: 0
  _minDepth: 0
  _minDepthOfParent: 0
  _maxWidthType: 0
  _maxWidth: 1
  _maxWidthOfParent: 1
  _maxHeightType: 0
  _maxHeight: 1
  _maxHeightOfParent: 1
  _maxDepthType: 0
  _maxDepth: 1
  _maxDepthOfParent: 1
  _offset: {x: 0, y: 0, z: 0}
  _scale: {x: 1, y: 1, z: 1}
  _rotation: {x: 0, y: 0, z: 0, w: 1}
  _marginLeft: 0
  _marginRight: 0
  _marginTop: 0
  _marginBottom: 0
  _marginFront: 0
  _marginBack: 0
  _paddingLeft: 0
  _paddingRight: 0
  _paddingTop: 0
  _paddingBottom: 0
  _paddingFront: 0
  _paddingBack: 0
  _skipLayout: 0
--- !u!114 &590167112093245038
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4137177422677864261}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5bc6c8a2e47ba064e8c2a0953ba81718, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _version: 1
  _target: {fileID: 5201721041018657067}
  _horizontalAlign: 0
  _verticalAlign: 2
  _depthAlign: 1
  _horizontalPivot: 1
  _verticalPivot: 1
  _depthPivot: 1
--- !u!114 &6864302305834158357
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4137177422677864261}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: aba4048c0905b9947bd3cc885659b584, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Standard: {fileID: 2100000, guid: d37fe08da39fb7a49b506268d2230e07, type: 2}
  URP: {fileID: 2100000, guid: ffbdc48912193e94ca6f086ef2338161, type: 2}
  HDRP: {fileID: 0}
  _color: {r: 1, g: 1, b: 1, a: 1}
--- !u!1 &4718738862534515522
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6826702734520317690}
  - component: {fileID: 1550681310336356566}
  - component: {fileID: 2515639748086073604}
  - component: {fileID: 269302025408153922}
  - component: {fileID: 141375209641325330}
  - component: {fileID: 7775694417046544889}
  - component: {fileID: 6320512082100750502}
  - component: {fileID: 4978853933355365867}
  m_Layer: 0
  m_Name: Edge (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6826702734520317690
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4718738862534515522}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0.5, z: -0.5}
  m_LocalScale: {x: 1, y: 0.25, z: 0.25}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 5201721041018657060}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &1550681310336356566
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4718738862534515522}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &2515639748086073604
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4718738862534515522}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &269302025408153922
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4718738862534515522}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!114 &141375209641325330
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4718738862534515522}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994c66b6148109b489689ed705d244cd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Parent: {fileID: 0}
  SiblingIndex: 1
  LayoutPosition: {x: 0, y: 0.5, z: -0.5}
  LayoutRotation: {x: 0, y: 0, z: 0, w: 1}
  AdapterBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.5, y: 0.125, z: 0.125}
  LayoutBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.5, y: 0.125, z: 0.125}
  RotatedAndScaledBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.5, y: 0.125, z: 0.125}
  ComponentScale: {x: 1, y: 0.25, z: 0.25}
  FillSize: {x: 0, y: 0, z: 0}
  ShrinkSize: {x: 999999, y: 999999, z: 999999}
  TargetPosition: {x: 0, y: 0.5, z: -0.5}
  TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  TargetScale: {x: 1, y: 0.25, z: 0.25}
  TargetRectSize: {x: 0, y: 0, z: 0}
  TransformPosition: {x: 0, y: 0.5, z: -0.5}
  TransformRotation: {x: 0, y: 0, z: 0, w: 1}
  TransformScale: {x: 1, y: 0.25, z: 0.25}
  TransformRectSize: {x: 0, y: 0}
--- !u!114 &7775694417046544889
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4718738862534515522}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 00b0da67f6b3c9a4e84ed47af9da4884, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _version: 1
  _widthType: 1
  _width: 0.1
  _widthOfParent: 1
  _heightType: 0
  _height: 0.25
  _heightOfParent: 1
  _depthType: 0
  _depth: 0.25
  _depthOfParent: 1
  _minWidthType: 0
  _minWidth: 0
  _minWidthOfParent: 0
  _minHeightType: 0
  _minHeight: 0
  _minHeightOfParent: 0
  _minDepthType: 0
  _minDepth: 0
  _minDepthOfParent: 0
  _maxWidthType: 0
  _maxWidth: 1
  _maxWidthOfParent: 1
  _maxHeightType: 0
  _maxHeight: 1
  _maxHeightOfParent: 1
  _maxDepthType: 0
  _maxDepth: 1
  _maxDepthOfParent: 1
  _offset: {x: 0, y: 0, z: 0}
  _scale: {x: 1, y: 1, z: 1}
  _rotation: {x: 0, y: 0, z: 0, w: 1}
  _marginLeft: 0
  _marginRight: 0
  _marginTop: 0
  _marginBottom: 0
  _marginFront: 0
  _marginBack: 0
  _paddingLeft: 0
  _paddingRight: 0
  _paddingTop: 0
  _paddingBottom: 0
  _paddingFront: 0
  _paddingBack: 0
  _skipLayout: 0
--- !u!114 &6320512082100750502
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4718738862534515522}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5bc6c8a2e47ba064e8c2a0953ba81718, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _version: 1
  _target: {fileID: 5201721041018657067}
  _horizontalAlign: 1
  _verticalAlign: 2
  _depthAlign: 0
  _horizontalPivot: 1
  _verticalPivot: 1
  _depthPivot: 1
--- !u!114 &4978853933355365867
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4718738862534515522}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: aba4048c0905b9947bd3cc885659b584, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Standard: {fileID: 2100000, guid: d37fe08da39fb7a49b506268d2230e07, type: 2}
  URP: {fileID: 2100000, guid: ffbdc48912193e94ca6f086ef2338161, type: 2}
  HDRP: {fileID: 0}
  _color: {r: 1, g: 1, b: 1, a: 1}
--- !u!1 &5113897062892517467
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 771535779654206156}
  - component: {fileID: 3028172996655813552}
  - component: {fileID: 5355850521565166408}
  - component: {fileID: 2647205770803560619}
  - component: {fileID: 6122295877029843780}
  - component: {fileID: 8273498334818877711}
  - component: {fileID: 2628453842221185902}
  - component: {fileID: 2448188561318267098}
  m_Layer: 0
  m_Name: Corner
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &771535779654206156
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5113897062892517467}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0.5, y: 0.5, z: -0.5}
  m_LocalScale: {x: 0.5, y: 0.5, z: 0.5}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 5201721041018657060}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &3028172996655813552
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5113897062892517467}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &5355850521565166408
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5113897062892517467}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &2647205770803560619
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5113897062892517467}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!114 &6122295877029843780
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5113897062892517467}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994c66b6148109b489689ed705d244cd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Parent: {fileID: 0}
  SiblingIndex: 1
  LayoutPosition: {x: 0.5, y: 0.5, z: -0.5}
  LayoutRotation: {x: 0, y: 0, z: 0, w: 1}
  AdapterBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.25, y: 0.25, z: 0.25}
  LayoutBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.25, y: 0.25, z: 0.25}
  RotatedAndScaledBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.25, y: 0.25, z: 0.25}
  ComponentScale: {x: 0.5, y: 0.5, z: 0.5}
  FillSize: {x: 0, y: 0, z: 0}
  ShrinkSize: {x: 999999, y: 999999, z: 999999}
  TargetPosition: {x: 0.5, y: 0.5, z: -0.5}
  TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  TargetScale: {x: 0.5, y: 0.5, z: 0.5}
  TargetRectSize: {x: 0, y: 0, z: 0}
  TransformPosition: {x: 0.5, y: 0.5, z: -0.5}
  TransformRotation: {x: 0, y: 0, z: 0, w: 1}
  TransformScale: {x: 0.5, y: 0.5, z: 0.5}
  TransformRectSize: {x: 0, y: 0}
--- !u!114 &8273498334818877711
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5113897062892517467}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 00b0da67f6b3c9a4e84ed47af9da4884, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _version: 1
  _widthType: 2
  _width: 1
  _widthOfParent: 1
  _heightType: 2
  _height: 1
  _heightOfParent: 1
  _depthType: 0
  _depth: 0.5
  _depthOfParent: 1
  _minWidthType: 0
  _minWidth: 0
  _minWidthOfParent: 0
  _minHeightType: 0
  _minHeight: 0
  _minHeightOfParent: 0
  _minDepthType: 0
  _minDepth: 0
  _minDepthOfParent: 0
  _maxWidthType: 0
  _maxWidth: 1
  _maxWidthOfParent: 1
  _maxHeightType: 0
  _maxHeight: 1
  _maxHeightOfParent: 1
  _maxDepthType: 0
  _maxDepth: 1
  _maxDepthOfParent: 1
  _offset: {x: 0, y: 0, z: 0}
  _scale: {x: 1, y: 1, z: 1}
  _rotation: {x: 0, y: 0, z: 0, w: 1}
  _marginLeft: 0
  _marginRight: 0
  _marginTop: 0
  _marginBottom: 0
  _marginFront: 0
  _marginBack: 0
  _paddingLeft: 0
  _paddingRight: 0
  _paddingTop: 0
  _paddingBottom: 0
  _paddingFront: 0
  _paddingBack: 0
  _skipLayout: 0
--- !u!114 &2628453842221185902
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5113897062892517467}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5bc6c8a2e47ba064e8c2a0953ba81718, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _version: 1
  _target: {fileID: 5201721041018657067}
  _horizontalAlign: 2
  _verticalAlign: 2
  _depthAlign: 0
  _horizontalPivot: 1
  _verticalPivot: 1
  _depthPivot: 1
--- !u!114 &2448188561318267098
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5113897062892517467}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: aba4048c0905b9947bd3cc885659b584, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Standard: {fileID: 2100000, guid: d37fe08da39fb7a49b506268d2230e07, type: 2}
  URP: {fileID: 2100000, guid: ffbdc48912193e94ca6f086ef2338161, type: 2}
  HDRP: {fileID: 0}
  _color: {r: 1, g: 1, b: 1, a: 1}
--- !u!1 &5169008494585005232
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5249493774574757547}
  - component: {fileID: 4841977941304960858}
  - component: {fileID: 2777311838491477602}
  - component: {fileID: 3021783662437192427}
  - component: {fileID: 7650883729405778822}
  - component: {fileID: 7527734121933588259}
  - component: {fileID: 6220185450284619008}
  - component: {fileID: 4867593237457909063}
  m_Layer: 0
  m_Name: Edge (7)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5249493774574757547
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5169008494585005232}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0.5, y: -0.5, z: 0}
  m_LocalScale: {x: 0.25, y: 0.25, z: 1}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 5201721041018657060}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &4841977941304960858
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5169008494585005232}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &2777311838491477602
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5169008494585005232}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &3021783662437192427
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5169008494585005232}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!114 &7650883729405778822
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5169008494585005232}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994c66b6148109b489689ed705d244cd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Parent: {fileID: 0}
  SiblingIndex: 1
  LayoutPosition: {x: -0.5, y: -0.5, z: 0}
  LayoutRotation: {x: 0, y: 0, z: 0, w: 1}
  AdapterBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.125, y: 0.125, z: 0.5}
  LayoutBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.125, y: 0.125, z: 0.5}
  RotatedAndScaledBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.125, y: 0.125, z: 0.5}
  ComponentScale: {x: 0.25, y: 0.25, z: 1}
  FillSize: {x: 0, y: 0, z: 0}
  ShrinkSize: {x: 999999, y: 999999, z: 999999}
  TargetPosition: {x: -0.5, y: -0.5, z: 0}
  TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  TargetScale: {x: 0.25, y: 0.25, z: 1}
  TargetRectSize: {x: 0, y: 0, z: 0}
  TransformPosition: {x: -0.5, y: -0.5, z: 0}
  TransformRotation: {x: 0, y: 0, z: 0, w: 1}
  TransformScale: {x: 0.25, y: 0.25, z: 1}
  TransformRectSize: {x: 0, y: 0}
--- !u!114 &7527734121933588259
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5169008494585005232}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 00b0da67f6b3c9a4e84ed47af9da4884, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _version: 1
  _widthType: 0
  _width: 0.25
  _widthOfParent: 1
  _heightType: 0
  _height: 0.25
  _heightOfParent: 1
  _depthType: 1
  _depth: 0.25
  _depthOfParent: 1
  _minWidthType: 0
  _minWidth: 0
  _minWidthOfParent: 0
  _minHeightType: 0
  _minHeight: 0
  _minHeightOfParent: 0
  _minDepthType: 0
  _minDepth: 0
  _minDepthOfParent: 0
  _maxWidthType: 0
  _maxWidth: 1
  _maxWidthOfParent: 1
  _maxHeightType: 0
  _maxHeight: 1
  _maxHeightOfParent: 1
  _maxDepthType: 0
  _maxDepth: 1
  _maxDepthOfParent: 1
  _offset: {x: 0, y: 0, z: 0}
  _scale: {x: 1, y: 1, z: 1}
  _rotation: {x: 0, y: 0, z: 0, w: 1}
  _marginLeft: 0
  _marginRight: 0
  _marginTop: 0
  _marginBottom: 0
  _marginFront: 0
  _marginBack: 0
  _paddingLeft: 0
  _paddingRight: 0
  _paddingTop: 0
  _paddingBottom: 0
  _paddingFront: 0
  _paddingBack: 0
  _skipLayout: 0
--- !u!114 &6220185450284619008
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5169008494585005232}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5bc6c8a2e47ba064e8c2a0953ba81718, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _version: 1
  _target: {fileID: 5201721041018657067}
  _horizontalAlign: 0
  _verticalAlign: 0
  _depthAlign: 1
  _horizontalPivot: 1
  _verticalPivot: 1
  _depthPivot: 1
--- !u!114 &4867593237457909063
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5169008494585005232}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: aba4048c0905b9947bd3cc885659b584, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Standard: {fileID: 2100000, guid: d37fe08da39fb7a49b506268d2230e07, type: 2}
  URP: {fileID: 2100000, guid: ffbdc48912193e94ca6f086ef2338161, type: 2}
  HDRP: {fileID: 0}
  _color: {r: 1, g: 1, b: 1, a: 1}
--- !u!1 &5201721041018657067
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5201721041018657060}
  - component: {fileID: 5201721041018657061}
  - component: {fileID: 1726011980073429545}
  - component: {fileID: 4855439110935016730}
  m_Layer: 0
  m_Name: BoundingBox
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5201721041018657060
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5201721041018657067}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 2.08, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 1
  m_Children:
  - {fileID: 5201721041922639306}
  - {fileID: 920386800119566862}
  - {fileID: 6759271806915010934}
  - {fileID: 5095048325222857267}
  - {fileID: 4137997887937577135}
  - {fileID: 7332791520346093443}
  - {fileID: 771535779654206156}
  - {fileID: 542407387717635824}
  - {fileID: 6423625778075406130}
  - {fileID: 6680215582770010048}
  - {fileID: 6826702734520317690}
  - {fileID: 9177726942328818290}
  - {fileID: 3034872380917434648}
  - {fileID: 8151845709390135717}
  - {fileID: 5282026955063531248}
  - {fileID: 1222129817647586004}
  - {fileID: 5249493774574757547}
  - {fileID: 8766202411328382980}
  - {fileID: 762433522960353005}
  - {fileID: 5380904882414257483}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &5201721041018657061
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5201721041018657067}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994c66b6148109b489689ed705d244cd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Parent: {fileID: 0}
  SiblingIndex: 1
  LayoutPosition: {x: 0, y: 0, z: 0}
  LayoutRotation: {x: 0, y: 0, z: 0, w: 1}
  AdapterBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.5, y: 0.5, z: 0.5}
  LayoutBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.5, y: 0.5, z: 0.5}
  RotatedAndScaledBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.5, y: 0.5, z: 0.5}
  ComponentScale: {x: 1, y: 1, z: 1}
  FillSize: {x: 0, y: 0, z: 0}
  ShrinkSize: {x: 999999, y: 999999, z: 999999}
  TargetPosition: {x: 0, y: 2.08, z: 0}
  TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  TargetScale: {x: 1, y: 1, z: 1}
  TargetRectSize: {x: 0, y: 0, z: 0}
  TransformPosition: {x: 0, y: 2.08, z: 0}
  TransformRotation: {x: 0, y: 0, z: 0, w: 1}
  TransformScale: {x: 1, y: 1, z: 1}
  TransformRectSize: {x: 0, y: 0}
--- !u!114 &1726011980073429545
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5201721041018657067}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5bc6c8a2e47ba064e8c2a0953ba81718, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _version: 1
  _target: {fileID: 0}
  _horizontalAlign: 1
  _verticalAlign: 1
  _depthAlign: 1
  _horizontalPivot: 1
  _verticalPivot: 1
  _depthPivot: 1
--- !u!114 &4855439110935016730
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5201721041018657067}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 00b0da67f6b3c9a4e84ed47af9da4884, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _version: 1
  _widthType: 1
  _width: 1
  _widthOfParent: 1
  _heightType: 1
  _height: 1
  _heightOfParent: 1
  _depthType: 1
  _depth: 1
  _depthOfParent: 1
  _minWidthType: 0
  _minWidth: 0
  _minWidthOfParent: 0
  _minHeightType: 0
  _minHeight: 0
  _minHeightOfParent: 0
  _minDepthType: 0
  _minDepth: 0
  _minDepthOfParent: 0
  _maxWidthType: 0
  _maxWidth: 1
  _maxWidthOfParent: 1
  _maxHeightType: 0
  _maxHeight: 1
  _maxHeightOfParent: 1
  _maxDepthType: 0
  _maxDepth: 1
  _maxDepthOfParent: 1
  _offset: {x: 0, y: 0, z: 0}
  _scale: {x: 1, y: 1, z: 1}
  _rotation: {x: 0, y: 0, z: 0, w: 1}
  _marginLeft: 0
  _marginRight: 0
  _marginTop: 0
  _marginBottom: 0
  _marginFront: 0
  _marginBack: 0
  _paddingLeft: 0
  _paddingRight: 0
  _paddingTop: 0
  _paddingBottom: 0
  _paddingFront: 0
  _paddingBack: 0
  _skipLayout: 0
--- !u!1 &5201721041922639305
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5201721041922639306}
  - component: {fileID: 5201721041922639296}
  - component: {fileID: 5201721041922639303}
  - component: {fileID: 5201721041922639302}
  - component: {fileID: 5201721041922639301}
  - component: {fileID: 5201721041922639300}
  - component: {fileID: 6639498437964864553}
  - component: {fileID: 3421244263728740399}
  m_Layer: 0
  m_Name: Corner
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5201721041922639306
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5201721041922639305}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0.5, y: -0.5, z: -0.5}
  m_LocalScale: {x: 0.5, y: 0.5, z: 0.5}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 5201721041018657060}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &5201721041922639296
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5201721041922639305}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &5201721041922639303
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5201721041922639305}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &5201721041922639302
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5201721041922639305}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!114 &5201721041922639301
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5201721041922639305}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994c66b6148109b489689ed705d244cd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Parent: {fileID: 0}
  SiblingIndex: 1
  LayoutPosition: {x: -0.5, y: -0.5, z: -0.5}
  LayoutRotation: {x: 0, y: 0, z: 0, w: 1}
  AdapterBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.25, y: 0.25, z: 0.25}
  LayoutBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.25, y: 0.25, z: 0.25}
  RotatedAndScaledBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.25, y: 0.25, z: 0.25}
  ComponentScale: {x: 0.5, y: 0.5, z: 0.5}
  FillSize: {x: 0, y: 0, z: 0}
  ShrinkSize: {x: 999999, y: 999999, z: 999999}
  TargetPosition: {x: -0.5, y: -0.5, z: -0.5}
  TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  TargetScale: {x: 0.5, y: 0.5, z: 0.5}
  TargetRectSize: {x: 0, y: 0, z: 0}
  TransformPosition: {x: -0.5, y: -0.5, z: -0.5}
  TransformRotation: {x: 0, y: 0, z: 0, w: 1}
  TransformScale: {x: 0.5, y: 0.5, z: 0.5}
  TransformRectSize: {x: 0, y: 0}
--- !u!114 &5201721041922639300
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5201721041922639305}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 00b0da67f6b3c9a4e84ed47af9da4884, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _version: 1
  _widthType: 2
  _width: 1
  _widthOfParent: 1
  _heightType: 2
  _height: 1
  _heightOfParent: 1
  _depthType: 0
  _depth: 0.5
  _depthOfParent: 1
  _minWidthType: 0
  _minWidth: 0
  _minWidthOfParent: 0
  _minHeightType: 0
  _minHeight: 0
  _minHeightOfParent: 0
  _minDepthType: 0
  _minDepth: 0
  _minDepthOfParent: 0
  _maxWidthType: 0
  _maxWidth: 1
  _maxWidthOfParent: 1
  _maxHeightType: 0
  _maxHeight: 1
  _maxHeightOfParent: 1
  _maxDepthType: 0
  _maxDepth: 1
  _maxDepthOfParent: 1
  _offset: {x: 0, y: 0, z: 0}
  _scale: {x: 1, y: 1, z: 1}
  _rotation: {x: 0, y: 0, z: 0, w: 1}
  _marginLeft: 0
  _marginRight: 0
  _marginTop: 0
  _marginBottom: 0
  _marginFront: 0
  _marginBack: 0
  _paddingLeft: 0
  _paddingRight: 0
  _paddingTop: 0
  _paddingBottom: 0
  _paddingFront: 0
  _paddingBack: 0
  _skipLayout: 0
--- !u!114 &6639498437964864553
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5201721041922639305}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5bc6c8a2e47ba064e8c2a0953ba81718, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _version: 1
  _target: {fileID: 5201721041018657067}
  _horizontalAlign: 0
  _verticalAlign: 0
  _depthAlign: 0
  _horizontalPivot: 1
  _verticalPivot: 1
  _depthPivot: 1
--- !u!114 &3421244263728740399
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5201721041922639305}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: aba4048c0905b9947bd3cc885659b584, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Standard: {fileID: 2100000, guid: d37fe08da39fb7a49b506268d2230e07, type: 2}
  URP: {fileID: 2100000, guid: ffbdc48912193e94ca6f086ef2338161, type: 2}
  HDRP: {fileID: 0}
  _color: {r: 1, g: 1, b: 1, a: 1}
--- !u!1 &5216509609997064534
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9177726942328818290}
  - component: {fileID: 3287324103727944918}
  - component: {fileID: 7580833840402974328}
  - component: {fileID: 2657179127659035704}
  - component: {fileID: 6127888505573730303}
  - component: {fileID: 6393995014919191685}
  - component: {fileID: 5570179213729536259}
  - component: {fileID: 1648950211949405266}
  m_Layer: 0
  m_Name: Edge (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9177726942328818290
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5216509609997064534}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0.5, z: 0.5}
  m_LocalScale: {x: 1, y: 0.25, z: 0.25}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 5201721041018657060}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &3287324103727944918
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5216509609997064534}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &7580833840402974328
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5216509609997064534}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &2657179127659035704
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5216509609997064534}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!114 &6127888505573730303
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5216509609997064534}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994c66b6148109b489689ed705d244cd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Parent: {fileID: 0}
  SiblingIndex: 1
  LayoutPosition: {x: 0, y: 0.5, z: 0.5}
  LayoutRotation: {x: 0, y: 0, z: 0, w: 1}
  AdapterBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.5, y: 0.125, z: 0.125}
  LayoutBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.5, y: 0.125, z: 0.125}
  RotatedAndScaledBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.5, y: 0.125, z: 0.125}
  ComponentScale: {x: 1, y: 0.25, z: 0.25}
  FillSize: {x: 0, y: 0, z: 0}
  ShrinkSize: {x: 999999, y: 999999, z: 999999}
  TargetPosition: {x: 0, y: 0.5, z: 0.5}
  TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  TargetScale: {x: 1, y: 0.25, z: 0.25}
  TargetRectSize: {x: 0, y: 0, z: 0}
  TransformPosition: {x: 0, y: 0.5, z: 0.5}
  TransformRotation: {x: 0, y: 0, z: 0, w: 1}
  TransformScale: {x: 1, y: 0.25, z: 0.25}
  TransformRectSize: {x: 0, y: 0}
--- !u!114 &6393995014919191685
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5216509609997064534}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 00b0da67f6b3c9a4e84ed47af9da4884, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _version: 1
  _widthType: 1
  _width: 0.1
  _widthOfParent: 1
  _heightType: 0
  _height: 0.25
  _heightOfParent: 1
  _depthType: 0
  _depth: 0.25
  _depthOfParent: 1
  _minWidthType: 0
  _minWidth: 0
  _minWidthOfParent: 0
  _minHeightType: 0
  _minHeight: 0
  _minHeightOfParent: 0
  _minDepthType: 0
  _minDepth: 0
  _minDepthOfParent: 0
  _maxWidthType: 0
  _maxWidth: 1
  _maxWidthOfParent: 1
  _maxHeightType: 0
  _maxHeight: 1
  _maxHeightOfParent: 1
  _maxDepthType: 0
  _maxDepth: 1
  _maxDepthOfParent: 1
  _offset: {x: 0, y: 0, z: 0}
  _scale: {x: 1, y: 1, z: 1}
  _rotation: {x: 0, y: 0, z: 0, w: 1}
  _marginLeft: 0
  _marginRight: 0
  _marginTop: 0
  _marginBottom: 0
  _marginFront: 0
  _marginBack: 0
  _paddingLeft: 0
  _paddingRight: 0
  _paddingTop: 0
  _paddingBottom: 0
  _paddingFront: 0
  _paddingBack: 0
  _skipLayout: 0
--- !u!114 &5570179213729536259
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5216509609997064534}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5bc6c8a2e47ba064e8c2a0953ba81718, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _version: 1
  _target: {fileID: 5201721041018657067}
  _horizontalAlign: 1
  _verticalAlign: 2
  _depthAlign: 2
  _horizontalPivot: 1
  _verticalPivot: 1
  _depthPivot: 1
--- !u!114 &1648950211949405266
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5216509609997064534}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: aba4048c0905b9947bd3cc885659b584, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Standard: {fileID: 2100000, guid: d37fe08da39fb7a49b506268d2230e07, type: 2}
  URP: {fileID: 2100000, guid: ffbdc48912193e94ca6f086ef2338161, type: 2}
  HDRP: {fileID: 0}
  _color: {r: 1, g: 1, b: 1, a: 1}
--- !u!1 &5996740622783306326
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 762433522960353005}
  - component: {fileID: 8274897612589385327}
  - component: {fileID: 6957005885383072940}
  - component: {fileID: 8225032432407356030}
  - component: {fileID: 5640643881384401121}
  - component: {fileID: 6764654244027997729}
  - component: {fileID: 4256407264383933198}
  - component: {fileID: 3483023215370034153}
  m_Layer: 0
  m_Name: Edge (9)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &762433522960353005
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5996740622783306326}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0.5, y: -0.5, z: 0}
  m_LocalScale: {x: 0.25, y: 0.25, z: 1}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 5201721041018657060}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &8274897612589385327
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5996740622783306326}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &6957005885383072940
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5996740622783306326}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &8225032432407356030
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5996740622783306326}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!114 &5640643881384401121
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5996740622783306326}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994c66b6148109b489689ed705d244cd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Parent: {fileID: 0}
  SiblingIndex: 1
  LayoutPosition: {x: 0.5, y: -0.5, z: 0}
  LayoutRotation: {x: 0, y: 0, z: 0, w: 1}
  AdapterBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.125, y: 0.125, z: 0.5}
  LayoutBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.125, y: 0.125, z: 0.5}
  RotatedAndScaledBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.125, y: 0.125, z: 0.5}
  ComponentScale: {x: 0.25, y: 0.25, z: 1}
  FillSize: {x: 0, y: 0, z: 0}
  ShrinkSize: {x: 999999, y: 999999, z: 999999}
  TargetPosition: {x: 0.5, y: -0.5, z: 0}
  TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  TargetScale: {x: 0.25, y: 0.25, z: 1}
  TargetRectSize: {x: 0, y: 0, z: 0}
  TransformPosition: {x: 0.5, y: -0.5, z: 0}
  TransformRotation: {x: 0, y: 0, z: 0, w: 1}
  TransformScale: {x: 0.25, y: 0.25, z: 1}
  TransformRectSize: {x: 0, y: 0}
--- !u!114 &6764654244027997729
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5996740622783306326}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 00b0da67f6b3c9a4e84ed47af9da4884, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _version: 1
  _widthType: 0
  _width: 0.25
  _widthOfParent: 1
  _heightType: 0
  _height: 0.25
  _heightOfParent: 1
  _depthType: 1
  _depth: 0.25
  _depthOfParent: 1
  _minWidthType: 0
  _minWidth: 0
  _minWidthOfParent: 0
  _minHeightType: 0
  _minHeight: 0
  _minHeightOfParent: 0
  _minDepthType: 0
  _minDepth: 0
  _minDepthOfParent: 0
  _maxWidthType: 0
  _maxWidth: 1
  _maxWidthOfParent: 1
  _maxHeightType: 0
  _maxHeight: 1
  _maxHeightOfParent: 1
  _maxDepthType: 0
  _maxDepth: 1
  _maxDepthOfParent: 1
  _offset: {x: 0, y: 0, z: 0}
  _scale: {x: 1, y: 1, z: 1}
  _rotation: {x: 0, y: 0, z: 0, w: 1}
  _marginLeft: 0
  _marginRight: 0
  _marginTop: 0
  _marginBottom: 0
  _marginFront: 0
  _marginBack: 0
  _paddingLeft: 0
  _paddingRight: 0
  _paddingTop: 0
  _paddingBottom: 0
  _paddingFront: 0
  _paddingBack: 0
  _skipLayout: 0
--- !u!114 &4256407264383933198
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5996740622783306326}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5bc6c8a2e47ba064e8c2a0953ba81718, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _version: 1
  _target: {fileID: 5201721041018657067}
  _horizontalAlign: 2
  _verticalAlign: 0
  _depthAlign: 1
  _horizontalPivot: 1
  _verticalPivot: 1
  _depthPivot: 1
--- !u!114 &3483023215370034153
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5996740622783306326}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: aba4048c0905b9947bd3cc885659b584, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Standard: {fileID: 2100000, guid: d37fe08da39fb7a49b506268d2230e07, type: 2}
  URP: {fileID: 2100000, guid: ffbdc48912193e94ca6f086ef2338161, type: 2}
  HDRP: {fileID: 0}
  _color: {r: 1, g: 1, b: 1, a: 1}
--- !u!1 &6241806268440390713
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6680215582770010048}
  - component: {fileID: 5654226156279199400}
  - component: {fileID: 3410457601539425315}
  - component: {fileID: 8469975886218085662}
  - component: {fileID: 6843082010510241268}
  - component: {fileID: 4269548244270831170}
  - component: {fileID: 4399815364670065894}
  - component: {fileID: 2835294362292648631}
  m_Layer: 0
  m_Name: Edge
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6680215582770010048
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6241806268440390713}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: -0.5, z: 0.5}
  m_LocalScale: {x: 1, y: 0.25, z: 0.25}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 5201721041018657060}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &5654226156279199400
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6241806268440390713}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &3410457601539425315
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6241806268440390713}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &8469975886218085662
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6241806268440390713}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!114 &6843082010510241268
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6241806268440390713}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994c66b6148109b489689ed705d244cd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Parent: {fileID: 0}
  SiblingIndex: 1
  LayoutPosition: {x: 0, y: -0.5, z: 0.5}
  LayoutRotation: {x: 0, y: 0, z: 0, w: 1}
  AdapterBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.5, y: 0.125, z: 0.125}
  LayoutBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.5, y: 0.125, z: 0.125}
  RotatedAndScaledBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.5, y: 0.125, z: 0.125}
  ComponentScale: {x: 1, y: 0.25, z: 0.25}
  FillSize: {x: 0, y: 0, z: 0}
  ShrinkSize: {x: 999999, y: 999999, z: 999999}
  TargetPosition: {x: 0, y: -0.5, z: 0.5}
  TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  TargetScale: {x: 1, y: 0.25, z: 0.25}
  TargetRectSize: {x: 0, y: 0, z: 0}
  TransformPosition: {x: 0, y: -0.5, z: 0.5}
  TransformRotation: {x: 0, y: 0, z: 0, w: 1}
  TransformScale: {x: 1, y: 0.25, z: 0.25}
  TransformRectSize: {x: 0, y: 0}
--- !u!114 &4269548244270831170
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6241806268440390713}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 00b0da67f6b3c9a4e84ed47af9da4884, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _version: 1
  _widthType: 1
  _width: 0.1
  _widthOfParent: 1
  _heightType: 0
  _height: 0.25
  _heightOfParent: 1
  _depthType: 0
  _depth: 0.25
  _depthOfParent: 1
  _minWidthType: 0
  _minWidth: 0
  _minWidthOfParent: 0
  _minHeightType: 0
  _minHeight: 0
  _minHeightOfParent: 0
  _minDepthType: 0
  _minDepth: 0
  _minDepthOfParent: 0
  _maxWidthType: 0
  _maxWidth: 1
  _maxWidthOfParent: 1
  _maxHeightType: 0
  _maxHeight: 1
  _maxHeightOfParent: 1
  _maxDepthType: 0
  _maxDepth: 1
  _maxDepthOfParent: 1
  _offset: {x: 0, y: 0, z: 0}
  _scale: {x: 1, y: 1, z: 1}
  _rotation: {x: 0, y: 0, z: 0, w: 1}
  _marginLeft: 0
  _marginRight: 0
  _marginTop: 0
  _marginBottom: 0
  _marginFront: 0
  _marginBack: 0
  _paddingLeft: 0
  _paddingRight: 0
  _paddingTop: 0
  _paddingBottom: 0
  _paddingFront: 0
  _paddingBack: 0
  _skipLayout: 0
--- !u!114 &4399815364670065894
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6241806268440390713}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5bc6c8a2e47ba064e8c2a0953ba81718, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _version: 1
  _target: {fileID: 5201721041018657067}
  _horizontalAlign: 1
  _verticalAlign: 0
  _depthAlign: 2
  _horizontalPivot: 1
  _verticalPivot: 1
  _depthPivot: 1
--- !u!114 &2835294362292648631
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6241806268440390713}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: aba4048c0905b9947bd3cc885659b584, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Standard: {fileID: 2100000, guid: d37fe08da39fb7a49b506268d2230e07, type: 2}
  URP: {fileID: 2100000, guid: ffbdc48912193e94ca6f086ef2338161, type: 2}
  HDRP: {fileID: 0}
  _color: {r: 1, g: 1, b: 1, a: 1}
--- !u!1 &6473348185859426097
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5095048325222857267}
  - component: {fileID: 8387887696704039257}
  - component: {fileID: 4487901377604568478}
  - component: {fileID: 3255450027137444176}
  - component: {fileID: 5544979725102424603}
  - component: {fileID: 3558041065863300778}
  - component: {fileID: 6807495829302130064}
  - component: {fileID: 6016331382242328616}
  m_Layer: 0
  m_Name: Corner
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5095048325222857267
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6473348185859426097}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0.5, y: 0.5, z: 0.5}
  m_LocalScale: {x: 0.5, y: 0.5, z: 0.5}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 5201721041018657060}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &8387887696704039257
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6473348185859426097}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &4487901377604568478
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6473348185859426097}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &3255450027137444176
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6473348185859426097}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!114 &5544979725102424603
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6473348185859426097}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994c66b6148109b489689ed705d244cd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Parent: {fileID: 0}
  SiblingIndex: 1
  LayoutPosition: {x: -0.5, y: 0.5, z: 0.5}
  LayoutRotation: {x: 0, y: 0, z: 0, w: 1}
  AdapterBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.25, y: 0.25, z: 0.25}
  LayoutBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.25, y: 0.25, z: 0.25}
  RotatedAndScaledBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.25, y: 0.25, z: 0.25}
  ComponentScale: {x: 0.5, y: 0.5, z: 0.5}
  FillSize: {x: 0, y: 0, z: 0}
  ShrinkSize: {x: 999999, y: 999999, z: 999999}
  TargetPosition: {x: -0.5, y: 0.5, z: 0.5}
  TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  TargetScale: {x: 0.5, y: 0.5, z: 0.5}
  TargetRectSize: {x: 0, y: 0, z: 0}
  TransformPosition: {x: -0.5, y: 0.5, z: 0.5}
  TransformRotation: {x: 0, y: 0, z: 0, w: 1}
  TransformScale: {x: 0.5, y: 0.5, z: 0.5}
  TransformRectSize: {x: 0, y: 0}
--- !u!114 &3558041065863300778
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6473348185859426097}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 00b0da67f6b3c9a4e84ed47af9da4884, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _version: 1
  _widthType: 2
  _width: 1
  _widthOfParent: 1
  _heightType: 2
  _height: 1
  _heightOfParent: 1
  _depthType: 0
  _depth: 0.5
  _depthOfParent: 1
  _minWidthType: 0
  _minWidth: 0
  _minWidthOfParent: 0
  _minHeightType: 0
  _minHeight: 0
  _minHeightOfParent: 0
  _minDepthType: 0
  _minDepth: 0
  _minDepthOfParent: 0
  _maxWidthType: 0
  _maxWidth: 1
  _maxWidthOfParent: 1
  _maxHeightType: 0
  _maxHeight: 1
  _maxHeightOfParent: 1
  _maxDepthType: 0
  _maxDepth: 1
  _maxDepthOfParent: 1
  _offset: {x: 0, y: 0, z: 0}
  _scale: {x: 1, y: 1, z: 1}
  _rotation: {x: 0, y: 0, z: 0, w: 1}
  _marginLeft: 0
  _marginRight: 0
  _marginTop: 0
  _marginBottom: 0
  _marginFront: 0
  _marginBack: 0
  _paddingLeft: 0
  _paddingRight: 0
  _paddingTop: 0
  _paddingBottom: 0
  _paddingFront: 0
  _paddingBack: 0
  _skipLayout: 0
--- !u!114 &6807495829302130064
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6473348185859426097}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5bc6c8a2e47ba064e8c2a0953ba81718, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _version: 1
  _target: {fileID: 5201721041018657067}
  _horizontalAlign: 0
  _verticalAlign: 2
  _depthAlign: 2
  _horizontalPivot: 1
  _verticalPivot: 1
  _depthPivot: 1
--- !u!114 &6016331382242328616
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6473348185859426097}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: aba4048c0905b9947bd3cc885659b584, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Standard: {fileID: 2100000, guid: d37fe08da39fb7a49b506268d2230e07, type: 2}
  URP: {fileID: 2100000, guid: ffbdc48912193e94ca6f086ef2338161, type: 2}
  HDRP: {fileID: 0}
  _color: {r: 1, g: 1, b: 1, a: 1}
--- !u!1 &8070605622160774572
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4137997887937577135}
  - component: {fileID: 6614266941247828701}
  - component: {fileID: 7740006092732014642}
  - component: {fileID: 8874010376431775112}
  - component: {fileID: 521752136533091624}
  - component: {fileID: 845296917796012291}
  - component: {fileID: 6123303855934432559}
  - component: {fileID: 5281750614521606745}
  m_Layer: 0
  m_Name: Corner
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4137997887937577135
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8070605622160774572}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0.5, y: -0.5, z: -0.5}
  m_LocalScale: {x: 0.5, y: 0.5, z: 0.5}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 5201721041018657060}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &6614266941247828701
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8070605622160774572}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &7740006092732014642
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8070605622160774572}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &8874010376431775112
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8070605622160774572}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!114 &521752136533091624
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8070605622160774572}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994c66b6148109b489689ed705d244cd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Parent: {fileID: 0}
  SiblingIndex: 1
  LayoutPosition: {x: 0.5, y: -0.5, z: -0.5}
  LayoutRotation: {x: 0, y: 0, z: 0, w: 1}
  AdapterBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.25, y: 0.25, z: 0.25}
  LayoutBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.25, y: 0.25, z: 0.25}
  RotatedAndScaledBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.25, y: 0.25, z: 0.25}
  ComponentScale: {x: 0.5, y: 0.5, z: 0.5}
  FillSize: {x: 0, y: 0, z: 0}
  ShrinkSize: {x: 999999, y: 999999, z: 999999}
  TargetPosition: {x: 0.5, y: -0.5, z: -0.5}
  TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  TargetScale: {x: 0.5, y: 0.5, z: 0.5}
  TargetRectSize: {x: 0, y: 0, z: 0}
  TransformPosition: {x: 0.5, y: -0.5, z: -0.5}
  TransformRotation: {x: 0, y: 0, z: 0, w: 1}
  TransformScale: {x: 0.5, y: 0.5, z: 0.5}
  TransformRectSize: {x: 0, y: 0}
--- !u!114 &845296917796012291
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8070605622160774572}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 00b0da67f6b3c9a4e84ed47af9da4884, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _version: 1
  _widthType: 2
  _width: 1
  _widthOfParent: 1
  _heightType: 2
  _height: 1
  _heightOfParent: 1
  _depthType: 0
  _depth: 0.5
  _depthOfParent: 1
  _minWidthType: 0
  _minWidth: 0
  _minWidthOfParent: 0
  _minHeightType: 0
  _minHeight: 0
  _minHeightOfParent: 0
  _minDepthType: 0
  _minDepth: 0
  _minDepthOfParent: 0
  _maxWidthType: 0
  _maxWidth: 1
  _maxWidthOfParent: 1
  _maxHeightType: 0
  _maxHeight: 1
  _maxHeightOfParent: 1
  _maxDepthType: 0
  _maxDepth: 1
  _maxDepthOfParent: 1
  _offset: {x: 0, y: 0, z: 0}
  _scale: {x: 1, y: 1, z: 1}
  _rotation: {x: 0, y: 0, z: 0, w: 1}
  _marginLeft: 0
  _marginRight: 0
  _marginTop: 0
  _marginBottom: 0
  _marginFront: 0
  _marginBack: 0
  _paddingLeft: 0
  _paddingRight: 0
  _paddingTop: 0
  _paddingBottom: 0
  _paddingFront: 0
  _paddingBack: 0
  _skipLayout: 0
--- !u!114 &6123303855934432559
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8070605622160774572}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5bc6c8a2e47ba064e8c2a0953ba81718, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _version: 1
  _target: {fileID: 5201721041018657067}
  _horizontalAlign: 2
  _verticalAlign: 0
  _depthAlign: 0
  _horizontalPivot: 1
  _verticalPivot: 1
  _depthPivot: 1
--- !u!114 &5281750614521606745
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8070605622160774572}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: aba4048c0905b9947bd3cc885659b584, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Standard: {fileID: 2100000, guid: d37fe08da39fb7a49b506268d2230e07, type: 2}
  URP: {fileID: 2100000, guid: ffbdc48912193e94ca6f086ef2338161, type: 2}
  HDRP: {fileID: 0}
  _color: {r: 1, g: 1, b: 1, a: 1}
--- !u!1 &8193955696025153179
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5282026955063531248}
  - component: {fileID: 8742220245861652921}
  - component: {fileID: 7753806957148318669}
  - component: {fileID: 8372147045068036684}
  - component: {fileID: 8595108111356666159}
  - component: {fileID: 6728501533912436260}
  - component: {fileID: 5173661126426348305}
  - component: {fileID: 8875745820524241405}
  m_Layer: 0
  m_Name: Edge (5)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5282026955063531248
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8193955696025153179}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0.5, y: 0, z: -0.5}
  m_LocalScale: {x: 0.25, y: 1, z: 0.25}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 5201721041018657060}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &8742220245861652921
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8193955696025153179}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &7753806957148318669
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8193955696025153179}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &8372147045068036684
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8193955696025153179}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!114 &8595108111356666159
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8193955696025153179}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994c66b6148109b489689ed705d244cd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Parent: {fileID: 0}
  SiblingIndex: 1
  LayoutPosition: {x: 0.5, y: 0, z: -0.5}
  LayoutRotation: {x: 0, y: 0, z: 0, w: 1}
  AdapterBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.125, y: 0.5, z: 0.125}
  LayoutBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.125, y: 0.5, z: 0.125}
  RotatedAndScaledBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.125, y: 0.5, z: 0.125}
  ComponentScale: {x: 0.25, y: 1, z: 0.25}
  FillSize: {x: 0, y: 0, z: 0}
  ShrinkSize: {x: 999999, y: 999999, z: 999999}
  TargetPosition: {x: 0.5, y: 0, z: -0.5}
  TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  TargetScale: {x: 0.25, y: 1, z: 0.25}
  TargetRectSize: {x: 0, y: 0, z: 0}
  TransformPosition: {x: 0.5, y: 0, z: -0.5}
  TransformRotation: {x: 0, y: 0, z: 0, w: 1}
  TransformScale: {x: 0.25, y: 1, z: 0.25}
  TransformRectSize: {x: 0, y: 0}
--- !u!114 &6728501533912436260
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8193955696025153179}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 00b0da67f6b3c9a4e84ed47af9da4884, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _version: 1
  _widthType: 0
  _width: 0.25
  _widthOfParent: 1
  _heightType: 1
  _height: 0.25
  _heightOfParent: 1
  _depthType: 0
  _depth: 0.25
  _depthOfParent: 1
  _minWidthType: 0
  _minWidth: 0
  _minWidthOfParent: 0
  _minHeightType: 0
  _minHeight: 0
  _minHeightOfParent: 0
  _minDepthType: 0
  _minDepth: 0
  _minDepthOfParent: 0
  _maxWidthType: 0
  _maxWidth: 1
  _maxWidthOfParent: 1
  _maxHeightType: 0
  _maxHeight: 1
  _maxHeightOfParent: 1
  _maxDepthType: 0
  _maxDepth: 1
  _maxDepthOfParent: 1
  _offset: {x: 0, y: 0, z: 0}
  _scale: {x: 1, y: 1, z: 1}
  _rotation: {x: 0, y: 0, z: 0, w: 1}
  _marginLeft: 0
  _marginRight: 0
  _marginTop: 0
  _marginBottom: 0
  _marginFront: 0
  _marginBack: 0
  _paddingLeft: 0
  _paddingRight: 0
  _paddingTop: 0
  _paddingBottom: 0
  _paddingFront: 0
  _paddingBack: 0
  _skipLayout: 0
--- !u!114 &5173661126426348305
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8193955696025153179}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5bc6c8a2e47ba064e8c2a0953ba81718, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _version: 1
  _target: {fileID: 5201721041018657067}
  _horizontalAlign: 2
  _verticalAlign: 1
  _depthAlign: 0
  _horizontalPivot: 1
  _verticalPivot: 1
  _depthPivot: 1
--- !u!114 &8875745820524241405
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8193955696025153179}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: aba4048c0905b9947bd3cc885659b584, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Standard: {fileID: 2100000, guid: d37fe08da39fb7a49b506268d2230e07, type: 2}
  URP: {fileID: 2100000, guid: ffbdc48912193e94ca6f086ef2338161, type: 2}
  HDRP: {fileID: 0}
  _color: {r: 1, g: 1, b: 1, a: 1}
