%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: Material Sample Billboard
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor v1.9.6.2\n// Available at the
    Unity Asset Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=19602\nNode;AmplifyShaderEditor.CommentaryNode;1696;-98,1022;Inherit;False;644;323;Depth
    Value;3;1694;1692;1695;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;1690;-1056,1488;Inherit;False;2356;898.6666;Billboard;28;1053;1107;1409;1411;1412;1420;1418;1416;1415;1421;1422;1423;1417;722;1414;1052;1051;1049;1050;1427;1407;1419;1406;1410;458;1691;1697;1698;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;1387;-1408,-4304;Inherit;False;2858.354;1357.231;Alpha;54;649;49;1290;351;1393;1338;1390;1389;1392;1391;1388;1299;1340;1297;1298;1288;1289;1327;1360;1353;1352;1354;1355;1337;1335;1334;1333;1332;1331;1330;1306;1329;1296;1328;1319;1372;1375;1370;1365;1366;1363;1364;1362;1361;1277;1260;1257;1256;1255;1253;1251;1250;1249;1248;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;1210;-320,-32;Inherit;False;1588;923;Normal
    Flip;29;1054;1056;1058;1074;1076;1082;684;1084;1077;1064;1069;1070;1071;1067;1079;1060;1059;1397;1404;1405;1403;1402;1400;1401;1399;1398;1396;1394;1395;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;1109;-1216,-848;Inherit;False;811.1378;275.5403;Phong;7;1139;1138;1129;1128;1120;1177;1244;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;1110;-1216,-1392;Inherit;False;805.3036;517.7374;BlinnPhong
    Half Vector;7;1211;1142;1141;1140;1136;1135;1134;;0.01886791,0.01886791,0.01886791,1;0;0\nNode;AmplifyShaderEditor.WireNode;880;-1467.498,-2313.313;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.SwizzleNode;742;-1088,-2384;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SwizzleNode;767;-832,-2560;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.StickyNoteNode;909;-432,-1776;Inherit;False;356.3911;100;_MetallicStrength;;0,0,0,1;value
    0 being dielectric (non-metallic) & 1 full metal;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;964;112,2816;Inherit;False;102;100;;;0.0471698,0.0471698,0.0471698,1;NdotV;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;965;544,2688;Inherit;False;227.0494;101.264;Vertex
    Normal;;0,0,0,1;vertex normals reconstruct based on the vertex offset;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;1053;912,2144;Inherit;False;329.1016;201.0392;Add
    Vertex Offset;;0,0,0,1;BIRP$v.vertex.xyz += vertexoffset@$$SRP $v.positionOS.xyz
    += vertexoffset@$$SRP 17x and higher $input.positionOS.xyz += vertexoffset@;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;1054;352,528;Inherit;False;268.3748;181.6522;Custom
    Expression Node;;0,0,0,1;Float(Flip, 0, Mirror, 1, None, 2)]$_DoubleSidedConstantsFlip$*Flip     
    = (-1, -1, -1)$_DoubleSidedConstantsMirror$*Mirror  = (1, 1, -1)$_DoubleSidedConstantsNone$*None  
    = (1, 1, 1) $$;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;1056;-272,416;Inherit;False;236.2744;236.5184;Face
    Node;;0,0,0,1;The Face node outputs a positive value of 1 if the rendered surface
    is facing the camera or a negative value of -1 if its facing away from the camera.$$Exsample:
    (vect3)$Flip      = (-1, -1, -1)$Mirror  = (1, 1, -1)$None   = (1, 1, 1) $$$$$;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;1058;800,304;Inherit;False;268.4819;138.4555;Switch
    by Face Node;;0,0,0,1;The Switch by Face outputs relays one of its inputs according
    to if the rendered surface is facing the camera ( Front ) or facing away from
    the camera ( Back ).;0;0\nNode;AmplifyShaderEditor.RangedFloatNode;21;-944,-2304;Half;False;Property;_Brightness;Brightness;6;0;Create;True;1;;0;0;False;0;False;1;1.597811;0;2;0;1;FLOAT;0\nNode;AmplifyShaderEditor.StickyNoteNode;1107;96,2208;Inherit;False;205.6936;100;Add
    Wind ?;;0,0,0,1;User may add custom wind into Billboard vertex offset;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;1108;-240,-1504;Inherit;False;178.6;100;;;0,0,0,1;Saturate
    Occclusion to Prevent Nan in Unity;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;1111;2384,-944;Inherit;False;255.6832;252.7083;;;0,0,0,1;_SpecularEnable$_SpecularColor$_SpecularMode$_SpecularRound$_SpecularWrapScale$_SpecularWrapOffset$_SpecularPower$_SpecularStrength$_SpecularStrengthDielectric$_SpecularStrengthDielectricIOR$;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;1113;912,-1248;Inherit;False;401.0635;223.9172;_SpecularColor;;0,0,0,1;;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;1153;592,-656;Inherit;False;256.716;115.5864;Specular
    Power;;0,0,0,1;;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;1155;1104,-656;Inherit;False;414.5996;148.0714;Specular
    Strength;;0,0,0,1;The vast majority of non-metallic materials have a reflectance
    of 4 percent - which can be acheived by setting the range slider to 0.5. Setting
    the slider to 1 gives you a 4.8 percent reflectance and setting the slider to
    0.01 gives you a reflectance of 3.4 percent.;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;1169;-208,-368;Inherit;False;257;110;Specular
    Wrap Offset;;0,0,0,1;;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;1170;-208,-576;Inherit;False;257;110;Specular
    Wrap Scale;;0,0,0,1;;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;1175;320,-608;Inherit;False;192.5423;105.1787;Specular
    Map ?;;0.2075472,0.2075472,0.2075472,1;User option specular map can be multiply
    in here;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;1177;-1168,-784;Inherit;False;301.8135;119.0774;Custom
    Phong Based Reflection vector;;0,0,0,1;Other specular algorithms may be added
    and customized based in user preferences ;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;1198;1600,-896;Inherit;False;179.8879;100;Specular
    Mode Default ;;0,0,0,1;Similar to the built-in Specular Shader$$;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;1248;1072,-4080;Inherit;False;321.4203;115.3568;Alpha;;0,0,0,1;Defines
    material's alpha value. Used for transparency and/or alpha clip. Expected range
    0 - 1.$;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;1249;-688,-3472;Inherit;False;239.1846;138;;;0,0,0,1;The
    result of this dot product is white when the faces are pointed toward the camera,
    and black when they're pointed away.;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;1250;-240,-4032;Inherit;False;323.2876;132.1172;Alpha
    Clip Threshold;;0,0,0,1;Conditionally discards a pixel before being outputted$This
    happens when Alpha value is less than Threshold (Alpha - Threshold < 0);0;0\nNode;AmplifyShaderEditor.StickyNoteNode;1251;1072,-3792;Inherit;False;319.5595;104.7868;Alpha
    Clip Threshold;;0,0,0,1;Fragments with an alpha below this value are discarded.
    Expected range 0 - 1.$;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;1253;464,-3664;Inherit;False;175.8867;100;_Cutoff;;0,0,0,1;HDRP
    Hidden in Inspector for GUI;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;1255;320,-3216;Inherit;False;535.1436;244.5579;_AlphaCutoffShadow;;0,0,0,1;Alpha
    Clip Threshold Shadow$$_AlphaCutoffShadow(\"_AlphaCutoffShadow\", Range(0.0,
    1.0)) = 0.5$$Hide in inspector:$this property will show in the \"Surface Options\"
    of HD template$works with Opaue also when a texture has alpha channel$$$working
    with surface or cutout the alpha port should be connected to the albedo alpha
    channel \"Alpha\" in ASE templates ;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;1256;112,-3296;Inherit;False;176.0459;110.0484;_AlphaCutoffShadow;;0,0,0,1;HDRP
    Hidden in Inspector for GUI;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;1257;1056,-3296;Inherit;False;319.1143;106.021;Alpha
    Clip Threshold Shadow;;0,0,0,1;URP HDRP;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;1260;-720,-3744;Inherit;False;257.0596;102.7212;Alpha
    Cutoff Bias;;0,0,0,1;Fragments with an alpha below this value are discarded.
    Expected range 0 - 1.$;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;1277;144,-3552;Inherit;False;337.0259;133.7739;Clip
    Glancing Angle;;0,0,0,1;Opacity Mask Glancing Angles$$Set UNITY_PASS_SHADOWCASTER
    to False so shadows do not get included in clip for glancing angles;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;1361;-1216,-4176;Inherit;False;255.3574;107.2468;Alpha;;0,0,0,1;Defines
    material's alpha value. Used for transparency and/or alpha clip. Expected range
    0 - 1.$;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;1362;-912,-4144;Inherit;False;615.7344;245.8354;Alpha
    Cutoff Bias;;0,0,0,1;Sharpens the alpha to the width of a single pixel$Fragments
    with an alpha below this value are discarded. Expected range 0 - 1.$;0;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;1364;-416,-4048;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0.5;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleDivideOpNode;1363;-560,-4048;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMaxOpNode;1366;-720,-4048;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0.0001;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FWidthOpNode;1365;-896,-4048;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.ClipNode;1370;-240,-4160;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;1375;-416,-3872;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SaturateNode;1372;-32,-4160;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.LerpOp;1328;448,-4016;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;1296;464,-3744;Inherit;False;Property;_Cutoff;Cutoff;0;1;[HideInInspector];Create;False;1;;0;0;False;0;False;0;0.9;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.StaticSwitch;1329;80,-3728;Inherit;False;Property;_Keyword1;Keyword
    0;16;0;Create;True;0;0;0;False;0;False;0;0;0;False;UNITY_PASS_SHADOWCASTER;Toggle;2;Key0;Key1;Fetch;False;True;All;9;1;FLOAT;0;False;0;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;1306;144,-3632;Half;False;Property;_GlancingClipMode;Enable
    Clip Glancing Angle;4;1;[ToggleUI];Create;False;0;0;0;False;0;False;0;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;1330;-96,-3488;Inherit;False;Constant;_Float1;Float
    1;18;0;Create;True;0;0;0;False;0;False;1;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;1331;-112,-3584;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;1332;-272,-3584;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;1333;-432,-3568;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.AbsOpNode;1334;-560,-3568;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.DotProductOpNode;1335;-688,-3568;Inherit;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.ViewDirInputsCoordNode;1337;-896,-3456;Inherit;False;World;True;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.NormalizeNode;1355;-864,-3568;Inherit;False;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.CrossProductOpNode;1354;-1040,-3568;Inherit;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.DdyOpNode;1352;-1168,-3568;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.DdxOpNode;1353;-1168,-3488;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WorldPosInputsNode;1360;-1376,-3568;Inherit;False;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.RangedFloatNode;1327;272,-4016;Inherit;False;Constant;_Constant;Constant;15;0;Create;True;1;;0;0;False;0;False;1;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1289;800,-4160;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1288;800,-4160;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitchByPipeline;1340;624,-3872;Inherit;False;4;0;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitchByPipeline;1299;624,-3376;Inherit;False;4;0;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1388;-1120,-4256;Inherit;False;1243;Alpha;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1391;-656,-4224;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1392;-640,-4192;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1389;-368,-4224;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1390;-336,-4192;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;1338;624,-4256;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1393;768,-4128;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitchByPipeline;1290;848,-4160;Inherit;False;4;0;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;28;-640,-2400;Inherit;False;3;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SaturateNode;904;-160,-2048;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SaturateNode;926;-192,-1584;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;923;-384,-1584;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.ScaleAndOffsetNode;1144;128,-800;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;1;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;1204;384,-704;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;1115;1600,-992;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SaturateNode;1208;1760,-992;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;1114;1408,-992;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;1147;1200,-976;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMaxOpNode;1148;1072,-896;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1214;1344,-1152;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1215;1344,-1008;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMaxOpNode;1137;-48,-800;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.DotProductOpNode;1120;-576,-800;Inherit;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1128;-832,-800;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1129;-832,-800;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WorldNormalVector;1134;-1024,-1024;Inherit;False;False;1;0;FLOAT3;0,0,1;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.WorldSpaceLightDirHlpNode;1135;-1088,-1184;Inherit;False;False;1;0;FLOAT;0;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.ViewDirInputsCoordNode;1136;-1024,-1344;Inherit;False;World;False;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.NegateNode;1138;-784,-688;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1139;-832,-688;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1140;-832,-1120;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1141;-832,-960;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1142;-832,-1296;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.ReflectOpNode;1244;-784,-800;Inherit;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.PowerNode;1116;896,-800;Inherit;False;True;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;1158;1408,-800;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.LerpOp;1117;2224,-1024;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.Vector3Node;1152;2000,-1120;Inherit;False;Constant;_Vector1;Vector
    1;8;0;Create;True;0;0;0;False;0;False;0,0,0;0,0,0;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.RangedFloatNode;1157;592,-736;Half;False;Property;_SpecularPower;Specular
    Power;19;0;Create;False;0;0;0;False;0;False;25;0;0;35;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;1172;-208,-448;Half;False;Property;_SpecularWrapOffset;Specular
    Wrap Offset;18;0;Create;False;0;0;0;False;0;False;0;0.5;0;3;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;1171;-208,-656;Half;False;Property;_SpecularWrapScale;Specular
    Wrap Scale;17;0;Create;False;0;0;0;False;0;False;0.85;0.5;0;5;0;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1211;-1200,-1024;Inherit;False;937;Normal;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SaturateNode;1123;-384,-800;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SwizzleNode;1151;1168,-1200;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.ColorNode;1150;928,-1200;Half;False;Property;_SpecularColor;Specular
    Color;16;1;[HDR];Create;False;0;0;0;False;0;False;0.06666667,0.06666667,0.05882353,0;1,1,1,1;False;True;0;6;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT3;5\nNode;AmplifyShaderEditor.RangedFloatNode;1298;320,-3376;Half;False;Property;_AlphaCutoffBiasShadow;Alpha
    Cutoff Bias Shadow;2;0;Create;False;1;;0;0;False;0;False;0.5;0.9;0.01;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;1297;320,-3296;Half;False;Property;_AlphaCutoffShadow;_AlphaCutoffShadow;3;1;[HideInInspector];Create;False;0;0;0;False;0;False;0.5;0.851;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SamplerNode;17;-1408,-16;Inherit;True;Property;_NormalMap;Normal
    Map;14;3;[NoScaleOffset];[Normal];[SingleLineTexture];Create;True;1;;0;0;False;0;False;-1;None;None;True;0;True;bump;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;6;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT3;5\nNode;AmplifyShaderEditor.UnpackScaleNormalNode;748;-816,-16;Inherit;False;Tangent;2;0;FLOAT4;0,0,0,0;False;1;FLOAT;1;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.FunctionSwitch;885;1296,-16;Inherit;False;Enable
    Normal Flip;True;1;2;4;In 0;In 1;Object;-1;10;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.BreakToComponentsNode;1074;-160,208;Inherit;False;FLOAT3;1;0;FLOAT3;0,0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;1076;-16,304;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;1082;-16,112;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.DynamicAppendNode;1084;144,208;Inherit;False;FLOAT3;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FaceVariableNode;1077;-176,336;Inherit;False;0;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitchByPipeline;1064;1024,96;Inherit;False;4;0;FLOAT3;0,0,0;False;3;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.Vector3Node;1070;16,560;Inherit;False;Constant;_DoubleSidedConstantsMirror1;_DoubleSidedConstantsMirror;4;0;Create;True;0;0;0;False;0;False;1,1,-1;0,0,0;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.Vector3Node;1071;16,704;Inherit;False;Constant;_DoubleSidedConstantsNone1;_DoubleSidedConstantsNone;4;0;Create;True;0;0;0;False;0;False;1,1,1;0,0,0;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.CustomExpressionNode;1067;352,384;Inherit;False;if(m_switch
    ==0)$\treturn m_Flip@$else if(m_switch ==1)$\treturn m_Mirror@$else if(m_switch
    ==2)$\treturn m_None@$else$return float3(0,0,0)@;3;Create;4;True;m_switch;FLOAT;0;In;;Inherit;False;True;m_Flip;FLOAT3;0,0,0;In;;Inherit;False;True;m_Mirror;FLOAT3;0,0,0;In;;Inherit;False;True;m_None;FLOAT3;0,0,0;In;;Inherit;False;_NormalMode
    float3 switch ;False;False;0;;False;4;0;FLOAT;0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;3;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SwitchByFaceNode;1060;800,192;Inherit;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;1059;624,240;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1395;928,48;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1394;848,16;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1396;672,16;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1397;720,64;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1398;512,16;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1399;560,64;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1401;288,48;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1400;240,16;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1402;-128,16;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1403;-80,48;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1405;-224,64;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1404;-272,16;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.CustomExpressionNode;1079;368,96;Inherit;False;if(m_switch
    ==0)$\treturn m_Flip@$else if(m_switch ==1)$\treturn m_Mirror@$else if(m_switch
    ==2)$\treturn m_None@$else$return float3(0,0,0)@;3;Create;4;True;m_switch;FLOAT;0;In;;Inherit;False;True;m_Flip;FLOAT3;0,0,0;In;;Inherit;False;True;m_Mirror;FLOAT3;0,0,0;In;;Inherit;False;True;m_None;FLOAT3;0,0,0;In;;Inherit;False;_NormalMode
    float3 switch ;False;False;0;;False;4;0;FLOAT;0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;3;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.Vector3Node;1069;16,416;Inherit;False;Constant;_DoubleSidedConstantsFlip1;_DoubleSidedConstantsFlip;4;0;Create;True;0;0;0;False;0;False;-1,-1,-1;0,0,0;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.RangedFloatNode;260;-1072,64;Half;False;Property;_NormalStrength;Normal
    Strength;14;0;Create;False;1;;0;0;False;0;False;1;1;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;1156;1104,-736;Half;False;Property;_SpecularStrength;Specular
    Strength;20;0;Create;False;0;0;0;False;0;False;0.15;0;0;15;0;1;FLOAT;0\nNode;AmplifyShaderEditor.LightColorNode;1149;896,-1008;Inherit;False;0;3;COLOR;0;FLOAT3;1;FLOAT;2\nNode;AmplifyShaderEditor.RangedFloatNode;1319;-720,-3824;Half;False;Property;_AlphaCutoffBias;Alpha
    Cutoff Bias;1;1;[Header];Create;False;1;ALPHA CLIPPING;0;0;False;0;False;0.5;0.9;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.ColorNode;768;-1088,-2560;Half;False;Property;_BaseColor;Base
    Color;5;1;[Header];Create;False;1;COLOR;0;0;False;0;False;1,1,1,0;0,0,0,0;True;True;0;6;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT3;5\nNode;AmplifyShaderEditor.RangedFloatNode;910;-464,-2048;Half;False;Property;_SmoothnessStrength;Smoothness
    Strength;10;0;Create;False;0;0;0;False;1;Space(10);False;0;0;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;913;-432,-1872;Inherit;False;Property;_MetallicStrength;Metallic
    Strength;9;0;Create;False;0;0;0;False;1;Space(10);False;0;0;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;925;-672,-1584;Half;False;Property;_OcclusionStrengthAO;Occlusion
    Strength;11;0;Create;False;0;0;0;False;1;Space(10);False;0;0;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;1194;1968,-912;Half;False;Property;_SpecularEnable;ENABLE
    SPECULAR;15;2;[Header];[ToggleUI];Create;False;1;SPECULAR;6;Default;0;Layer 2;1;Layer
    3;2;Layer 4;3;Layer 5;4;Layer 6;5;0;False;0;False;0;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;937;-560,-80;Inherit;False;Normal;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.TexturePropertyNode;518;-1712,-16;Inherit;True;Property;_BumpMap;Normal
    Map;12;2;[Normal];[SingleLineTexture];Create;False;0;0;0;False;1;Space(10);False;None;None;False;bump;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.SamplerNode;3;-1408,-2384;Inherit;True;Property;_AlbedoMap;Albedo
    Map;4;1;[SingleLineTexture];Create;True;1;;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;6;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT3;5\nNode;AmplifyShaderEditor.RegisterLocalVarNode;1243;-1056,-2208;Inherit;False;Alpha;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.TexturePropertyNode;522;-1760,-2384;Inherit;True;Property;_MainTex;Base
    Map;8;2;[Header];[SingleLineTexture];Create;False;1;SURFACE INPUTS;0;0;False;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.WireNode;1628;-1504,-2384;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;1629;-1488,-32;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;1626;-1504,-2656;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;1627;-1504,-2656;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SwizzleNode;1620;-1984,-2784;Inherit;False;FLOAT2;0;1;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SwizzleNode;1621;-1984,-2704;Inherit;False;FLOAT2;2;3;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.Vector4Node;1625;-2208,-2704;Inherit;False;Property;_MainUVs;Main
    UVs;7;0;Create;False;0;0;0;False;0;False;1,1,0,0;0,0,0,0;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.TexCoordVertexDataNode;1624;-2240,-2880;Inherit;False;0;2;0;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;1622;-1824,-2880;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;1623;-1632,-2720;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.StickyNoteNode;1691;-384,2160;Inherit;False;396.0328;152.9258;Billboards
    can be set through its Type;;0,0,0,1;Cylindrical: $Only aligns object x and Z
    axis with camera$( useful when rendering trees )$$Spherical: $Completely aligns
    object axis with camera$$;0;0\nNode;AmplifyShaderEditor.FunctionSubtitle;1409;-96,1712;Inherit;False;Cylindrical;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitch;1411;-384,1712;Inherit;False;Billboard
    Ignore Rotation;True;0;2;1;In 0;In 1;Object;-1;10;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitch;1412;-384,2032;Inherit;False;Ignore
    Rotation;True;0;2;1;In 0;In 1;Instance;1411;10;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.BillboardNode;1420;-992,1792;Inherit;False;Cylindrical;False;True;0;1;FLOAT3;0\nNode;AmplifyShaderEditor.BillboardNode;1418;-992,1872;Inherit;False;Cylindrical;True;False;0;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitch;1416;-768,1872;Inherit;False;Effect
    Normal and Tangent;True;0;2;2;In 0;In 1;Instance;1414;10;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitch;1415;-768,2032;Inherit;False;Effect
    Normal and Tangent;True;0;2;2;In 0;In 1;Instance;1414;10;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.BillboardNode;1421;-992,2112;Inherit;False;Spherical;False;True;0;1;FLOAT3;0\nNode;AmplifyShaderEditor.BillboardNode;1422;-992,2192;Inherit;False;Spherical;True;False;0;1;FLOAT3;0\nNode;AmplifyShaderEditor.BillboardNode;1423;-992,2272;Inherit;False;Spherical;True;True;0;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitch;1417;-768,2192;Inherit;False;Effect
    Normal and Tangent;True;0;2;2;In 0;In 1;Instance;1414;10;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.BillboardNode;1407;-992,2032;Inherit;False;Spherical;False;False;0;1;FLOAT3;0\nNode;AmplifyShaderEditor.BillboardNode;1419;-992,1952;Inherit;False;Cylindrical;True;True;0;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSubtitle;1410;-96,2032;Inherit;False;Spherical;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitch;1414;-768,1712;Inherit;False;Billboard
    Effect Normal and Tangent;True;0;2;2;In 0;In 1;Object;-1;10;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.BillboardNode;722;-992,1712;Inherit;False;Cylindrical;False;False;0;1;FLOAT3;0\nNode;AmplifyShaderEditor.WorldNormalVector;957;-112,2720;Inherit;False;True;1;0;FLOAT3;0,0,1;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.ViewDirInputsCoordNode;958;-112,2864;Inherit;False;World;True;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.DotProductOpNode;960;112,2720;Inherit;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.NegateNode;962;368,2624;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.StepOpNode;963;400,2720;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.NormalVertexDataNode;954;-112,3056;Inherit;False;0;5;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.DynamicAppendNode;956;272,3056;Inherit;False;FLOAT4;4;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;-1;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.CrossProductOpNode;955;112,3056;Inherit;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.NormalVertexDataNode;959;160,2560;Inherit;False;0;5;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.LerpOp;966;544,2560;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.Vector3Node;953;-112,3200;Inherit;False;Constant;_Vector7;Vector
    7;234;0;Create;True;0;0;0;False;0;False;0,0,1;0,0,1;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.RangedFloatNode;961;240,2784;Inherit;False;Constant;_Float6;Float
    6;5;0;Create;True;0;0;0;False;0;False;-1;-1;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.StickyNoteNode;1694;112,1072;Inherit;False;300.2422;117.3834;Used
    in SRP;;0,0,0,1;Returns Device Depth to be used as direct input, or to manipulate,
    Depth Value output.;0;0\nNode;AmplifyShaderEditor.ScreenPosInputsNode;1695;-64,1136;Inherit;False;1;False;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SimpleAddOpNode;1049;400,2064;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.Vector3Node;1427;96,2064;Inherit;False;Constant;_Vector0;Vector
    0;27;0;Create;True;0;0;0;False;0;False;0,0,0;0,0,0;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.SwitchBySRPVersionNode;1697;656,2144;Inherit;False;9;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;3;FLOAT3;0,0,0;False;4;FLOAT3;0,0,0;False;5;FLOAT3;0,0,0;False;6;FLOAT3;0,0,0;False;7;FLOAT3;0,0,0;False;8;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitchByPipeline;1050;912,2000;Inherit;False;4;0;FLOAT3;0,0,0;False;3;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.CustomExpressionNode;1052;400,1872;Inherit;False;v.vertex.xyz
    += vertexoffset@;1;Call;1;True;vertexoffset;FLOAT3;0,0,0;In;;Float;True;VetexOffset
    BIRP;False;False;0;;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.CustomExpressionNode;1051;400,1968;Inherit;False;v.positionOS.xyz
    += vertexoffset@;1;Call;1;True;vertexoffset;FLOAT3;0,0,0;In;;Float;True;VetexOffset
    SRP;False;False;0;;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitch;1406;96,1712;Inherit;False;Billboard
    Type;False;0;2;0;Cylindrical;Spherical;Object;-1;10;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.CustomExpressionNode;1698;400,2192;Inherit;False;input.positionOS.xyz
    += vertexoffset@;1;Call;1;True;vertexoffset;FLOAT3;0,0,0;In;;Float;True;VetexOffset
    SRP 17x;False;False;0;;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RangedFloatNode;684;128,64;Half;False;Property;_DoubleSidedNormalMode;Normal
    Mode;13;1;[Enum];Create;False;0;3;Flip;0;Mirror;1;None;2;0;False;0;False;0;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;0;-448,-2400;Inherit;False;True;-1;BaseColor;0;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionOutput;351;1056,-3872;Inherit;False;False;-1;Alpha
    Clip Threshold;7;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;49;1056,-4160;Inherit;False;False;-1;Alpha;6;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;649;1056,-3376;Inherit;False;False;-1;Alpha
    Clip Threshold Shadow;8;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;50;0,-2048;Inherit;False;False;-1;Smoothness;4;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;56;0,-1872;Inherit;False;False;-1;Metallic;2;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;57;0,-1584;Inherit;False;False;-1;Occlusion;5;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;770;2384,-1024;Inherit;False;False;-1;Specular;3;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionOutput;556;1664,-16;Inherit;False;False;-1;Normal;1;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionOutput;967;912,2560;Inherit;False;False;-1;Vertex
    Normal;11;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionOutput;968;912,3056;Inherit;False;False;-1;Vertex
    Tangent;12;False;1;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.FunctionOutput;1692;336,1216;Inherit;False;False;-1;Depth
    Offset;9;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;458;896,1712;Inherit;False;False;-1;Vertex
    Offset;10;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nWireConnection;880;0;522;1\nWireConnection;742;0;3;0\nWireConnection;767;0;768;0\nWireConnection;1364;0;1363;0\nWireConnection;1363;0;1392;0\nWireConnection;1363;1;1366;0\nWireConnection;1366;0;1365;0\nWireConnection;1365;0;1388;0\nWireConnection;1370;0;1364;0\nWireConnection;1370;1;1390;0\nWireConnection;1370;2;1375;0\nWireConnection;1375;0;1319;0\nWireConnection;1372;0;1370;0\nWireConnection;1328;0;1327;0\nWireConnection;1328;1;1329;0\nWireConnection;1328;2;1306;0\nWireConnection;1329;1;1331;0\nWireConnection;1329;0;1330;0\nWireConnection;1331;0;1332;0\nWireConnection;1332;0;1333;0\nWireConnection;1332;1;1333;0\nWireConnection;1333;0;1334;0\nWireConnection;1334;0;1335;0\nWireConnection;1335;0;1355;0\nWireConnection;1335;1;1337;0\nWireConnection;1355;0;1354;0\nWireConnection;1354;0;1352;0\nWireConnection;1354;1;1353;0\nWireConnection;1352;0;1360;0\nWireConnection;1353;0;1360;0\nWireConnection;1289;0;1338;0\nWireConnection;1288;0;1338;0\nWireConnection;1340;0;1328;0\nWireConnection;1340;3;1328;0\nWireConnection;1340;1;1319;0\nWireConnection;1340;2;1296;0\nWireConnection;1299;0;1298;0\nWireConnection;1299;3;1298;0\nWireConnection;1299;1;1298;0\nWireConnection;1299;2;1297;0\nWireConnection;1391;0;1388;0\nWireConnection;1392;0;1391;0\nWireConnection;1389;0;1388;0\nWireConnection;1390;0;1389;0\nWireConnection;1338;0;1388;0\nWireConnection;1338;1;1328;0\nWireConnection;1393;0;1372;0\nWireConnection;1290;0;1372;0\nWireConnection;1290;3;1393;0\nWireConnection;1290;1;1289;0\nWireConnection;1290;2;1288;0\nWireConnection;28;0;767;0\nWireConnection;28;1;742;0\nWireConnection;28;2;21;0\nWireConnection;904;0;910;0\nWireConnection;926;0;923;0\nWireConnection;923;0;925;0\nWireConnection;1144;0;1137;0\nWireConnection;1144;1;1171;0\nWireConnection;1144;2;1172;0\nWireConnection;1204;0;1144;0\nWireConnection;1115;0;1114;0\nWireConnection;1115;1;1158;0\nWireConnection;1208;0;1115;0\nWireConnection;1114;0;1215;0\nWireConnection;1114;1;1147;0\nWireConnection;1147;0;1149;1\nWireConnection;1147;1;1148;0\nWireConnection;1148;0;1149;2\nWireConnection;1214;0;1151;0\nWireConnection;1215;0;1214;0\nWireConnection;1137;0;1123;0\nWireConnection;1120;0;1244;0\nWireConnection;1120;1;1138;0\nWireConnection;1128;0;1141;0\nWireConnection;1129;0;1140;0\nWireConnection;1134;0;1211;0\nWireConnection;1138;0;1139;0\nWireConnection;1139;0;1142;0\nWireConnection;1140;0;1135;0\nWireConnection;1141;0;1134;0\nWireConnection;1142;0;1136;0\nWireConnection;1244;0;1129;0\nWireConnection;1244;1;1128;0\nWireConnection;1116;0;1144;0\nWireConnection;1116;1;1157;0\nWireConnection;1158;0;1116;0\nWireConnection;1158;1;1156;0\nWireConnection;1117;0;1152;0\nWireConnection;1117;1;1208;0\nWireConnection;1117;2;1194;0\nWireConnection;1123;0;1120;0\nWireConnection;1151;0;1150;0\nWireConnection;17;0;518;0\nWireConnection;17;1;1629;0\nWireConnection;17;7;518;1\nWireConnection;748;0;17;0\nWireConnection;748;1;260;0\nWireConnection;885;0;748;0\nWireConnection;885;1;1064;0\nWireConnection;1074;0;1405;0\nWireConnection;1076;0;1074;2\nWireConnection;1076;1;1077;0\nWireConnection;1082;0;1403;0\nWireConnection;1082;1;1077;0\nWireConnection;1084;0;1074;0\nWireConnection;1084;1;1074;1\nWireConnection;1084;2;1076;0\nWireConnection;1064;0;1079;0\nWireConnection;1064;3;1079;0\nWireConnection;1064;1;1060;0\nWireConnection;1064;2;1395;0\nWireConnection;1067;0;684;0\nWireConnection;1067;1;1069;0\nWireConnection;1067;2;1070;0\nWireConnection;1067;3;1071;0\nWireConnection;1060;0;1397;0\nWireConnection;1060;1;1059;0\nWireConnection;1059;0;1399;0\nWireConnection;1059;1;1067;0\nWireConnection;1395;0;1394;0\nWireConnection;1394;0;748;0\nWireConnection;1396;0;748;0\nWireConnection;1397;0;1396;0\nWireConnection;1398;0;748;0\nWireConnection;1399;0;1398;0\nWireConnection;1401;0;1400;0\nWireConnection;1400;0;748;0\nWireConnection;1402;0;748;0\nWireConnection;1403;0;1402;0\nWireConnection;1405;0;1404;0\nWireConnection;1404;0;748;0\nWireConnection;1079;0;684;0\nWireConnection;1079;1;1082;0\nWireConnection;1079;2;1084;0\nWireConnection;1079;3;1401;0\nWireConnection;937;0;748;0\nWireConnection;3;0;522;0\nWireConnection;3;1;1628;0\nWireConnection;3;7;880;0\nWireConnection;1243;0;3;4\nWireConnection;1628;0;1626;0\nWireConnection;1629;0;1627;0\nWireConnection;1626;0;1623;0\nWireConnection;1627;0;1623;0\nWireConnection;1620;0;1625;0\nWireConnection;1621;0;1625;0\nWireConnection;1622;0;1624;0\nWireConnection;1622;1;1620;0\nWireConnection;1623;0;1622;0\nWireConnection;1623;1;1621;0\nWireConnection;1409;0;1411;0\nWireConnection;1411;0;1414;0\nWireConnection;1411;1;1416;0\nWireConnection;1412;0;1415;0\nWireConnection;1412;1;1417;0\nWireConnection;1416;0;1418;0\nWireConnection;1416;1;1419;0\nWireConnection;1415;0;1407;0\nWireConnection;1415;1;1421;0\nWireConnection;1417;0;1422;0\nWireConnection;1417;1;1423;0\nWireConnection;1410;0;1412;0\nWireConnection;1414;0;722;0\nWireConnection;1414;1;1420;0\nWireConnection;960;0;957;0\nWireConnection;960;1;958;0\nWireConnection;962;0;959;0\nWireConnection;963;0;960;0\nWireConnection;963;1;961;0\nWireConnection;956;0;955;0\nWireConnection;955;0;954;0\nWireConnection;955;1;953;0\nWireConnection;966;0;959;0\nWireConnection;966;1;962;0\nWireConnection;966;2;963;0\nWireConnection;1049;0;1427;0\nWireConnection;1049;1;1406;0\nWireConnection;1697;0;1052;0\nWireConnection;1697;1;1052;0\nWireConnection;1697;2;1052;0\nWireConnection;1697;3;1051;0\nWireConnection;1697;4;1051;0\nWireConnection;1697;5;1051;0\nWireConnection;1697;6;1051;0\nWireConnection;1697;7;1051;0\nWireConnection;1697;8;1698;0\nWireConnection;1050;0;1052;0\nWireConnection;1050;3;1052;0\nWireConnection;1050;1;1697;0\nWireConnection;1050;2;1049;0\nWireConnection;1052;1;1427;0\nWireConnection;1051;0;1406;0\nWireConnection;1051;1;1427;0\nWireConnection;1406;0;1409;0\nWireConnection;1406;1;1410;0\nWireConnection;1698;0;1406;0\nWireConnection;1698;1;1427;0\nWireConnection;0;0;28;0\nWireConnection;351;0;1340;0\nWireConnection;49;0;1290;0\nWireConnection;649;0;1299;0\nWireConnection;50;0;904;0\nWireConnection;56;0;913;0\nWireConnection;57;0;926;0\nWireConnection;770;0;1117;0\nWireConnection;556;0;885;0\nWireConnection;967;0;966;0\nWireConnection;968;0;956;0\nWireConnection;1692;0;1695;3\nWireConnection;458;0;1406;0\nASEEND*/\n//CHKSM=A48BF5DE8C4E44024116751BAD555164C3244DC3"
  m_functionName: 
  m_description: http://paste.amplify.pt/view/raw/0fad5bb3
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 1
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_customEdited: 0
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 0
  m_headerStyle: 2
  m_headerColor: {r: 0.7647059, g: 0.30588236, b: 0, a: 1}
  m_customNodeCategory: Amplify Shader Pack
  m_previewPosition: 0
  m_hidden: 0
  m_url: 
