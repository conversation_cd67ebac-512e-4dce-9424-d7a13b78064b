fileFormatVersion: 2
guid: fe42c8b526e77e54ab91d0590272d9e8
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -122496617620796665
    second: RegularPresetsIcons_0
  - first:
      213: -970488182351979515
    second: RegularPresetsIcons_1
  - first:
      213: 7590535577056678407
    second: RegularPresetsIcons_2
  - first:
      213: 1161443714384828998
    second: RegularPresetsIcons_3
  - first:
      213: -5435255387823749147
    second: RegularPresetsIcons_4
  - first:
      213: -4594720345167028440
    second: RegularPresetsIcons_5
  - first:
      213: -1927911570766903740
    second: RegularPresetsIcons_6
  - first:
      213: -1001581268832274596
    second: RegularPresetsIcons_7
  - first:
      213: -6205461316961850552
    second: RegularPresetsIcons_8
  - first:
      213: 1833921066897734877
    second: RegularPresetsIcons_9
  externalObjects: {}
  serializedVersion: 12
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMasterTextureLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 0
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 1
  cookieLightType: 1
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: iPhone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: RegularPresetsIcons_0
      rect:
        serializedVersion: 2
        x: 0
        y: 768
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 011901e28230f8049bfc58cb4bc07a9e
      internalID: -122496617620796665
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RegularPresetsIcons_1
      rect:
        serializedVersion: 2
        x: 256
        y: 768
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 37f8e2e6cc5337446b63ed7c0dfc4545
      internalID: -970488182351979515
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RegularPresetsIcons_2
      rect:
        serializedVersion: 2
        x: 512
        y: 768
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9da53191e18e6534aa340d76cc4b3c5f
      internalID: 7590535577056678407
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RegularPresetsIcons_3
      rect:
        serializedVersion: 2
        x: 768
        y: 768
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7af071f9ac857a04a960c85fcf310c32
      internalID: 1161443714384828998
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RegularPresetsIcons_4
      rect:
        serializedVersion: 2
        x: 0
        y: 512
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1b60e974781595f4e8ae48c754d5124c
      internalID: -5435255387823749147
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RegularPresetsIcons_5
      rect:
        serializedVersion: 2
        x: 256
        y: 512
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 81f63304c83064d45997b541b35a35a8
      internalID: -4594720345167028440
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RegularPresetsIcons_6
      rect:
        serializedVersion: 2
        x: 512
        y: 512
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2ecd73262770ecc47bfa842c3f3bf555
      internalID: -1927911570766903740
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RegularPresetsIcons_7
      rect:
        serializedVersion: 2
        x: 768
        y: 512
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d6a13fca8e4f8d44ebd8ef3ffe23ada2
      internalID: -1001581268832274596
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RegularPresetsIcons_8
      rect:
        serializedVersion: 2
        x: 0
        y: 256
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 48f53ce42f625204da2899891a606ea9
      internalID: -6205461316961850552
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RegularPresetsIcons_9
      rect:
        serializedVersion: 2
        x: 256
        y: 256
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f3d8094016d64d24189889e668fe5602
      internalID: 1833921066897734877
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: 7622dacdd343c004898a60b9efe1f933
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      RegularPresetsIcons_0: -122496617620796665
      RegularPresetsIcons_1: -970488182351979515
      RegularPresetsIcons_2: 7590535577056678407
      RegularPresetsIcons_3: 1161443714384828998
      RegularPresetsIcons_4: -5435255387823749147
      RegularPresetsIcons_5: -4594720345167028440
      RegularPresetsIcons_6: -1927911570766903740
      RegularPresetsIcons_7: -1001581268832274596
      RegularPresetsIcons_8: -6205461316961850552
      RegularPresetsIcons_9: 1833921066897734877
  spritePackingTag: 
  pSDRemoveMatte: 0
  pSDShowRemoveMatteOption: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
AssetOrigin:
  serializedVersion: 1
  productId: 183370
  packageName: Feel
  packageVersion: 5.5
  assetPath: Assets/Feel/NiceVibrations/Demo/DemoAssets/RegularPresetsDemo/Sprites/RegularPresetsIcons.png
  uploadId: 759320
