# FMOD Event Description Visor Migration Guide

## Overview

This guide explains how to migrate from Koreographer's **FMOD Event Description Visor** to Cadance's equivalent **FMOD Cadance Visor**.

**🎵 Koreography Compatibility**: Cadance now supports loading existing Koreography files directly! You can use your current Koreography assets with the new FMODCadanceVisor while transitioning to the newer CadanceAsset format.

## Component Comparison

| Koreographer                | Cadance                         | Purpose                    |
| --------------------------- | ------------------------------- | -------------------------- |
| `FMODEventDescriptionVisor` | `FMODCadanceVisor`              | Main visor component       |
| `EventDescPair`             | `EventCadancePair`              | Event-choreography pairing |
| `FMODKoreographySet`        | `CadanceAsset` or `Koreography` | Choreography data          |
| `Koreographer` target       | `Cadance` target                | System reference           |

## Key Features Implemented

### ✅ Event-Choreography Pairing

- **Koreographer**: Pairs FMOD Events with `FMODKoreographySet`
- **Cadance**: Pairs FMOD Events with `CadanceAsset`

### ✅ Automatic Event Detection

- **Koreographer**: Automatically detects and manages FMOD event instances
- **Cadance**: Uses `FMODCadanceManager` for automatic event management

### ✅ Global Event Management

- **Koreographer**: "Super Visor" approach for managing all events
- **Cadance**: Centralized management through singleton pattern

### ✅ Music Player Integration

- **Koreographer**: Can act as music player for target Koreographer
- **Cadance**: Can act as music player for target Cadance

### ✅ Choreography Loading

- **Koreographer**: Automatically loads Koreography from FMODKoreographySet into Koreographer
- **Cadance**: Automatically loads CadanceAssets into Cadance system

### ✅ Inspector Display

- **Koreographer**: Shows "Loaded Koreography" section in inspector
- **Cadance**: Shows "Loaded Cadance" section in Cadance component inspector

### ✅ FMODKoreographySet Compatibility

- **Full Set Support**: Cadance can load entire FMODKoreographySet collections directly
- **Automatic Conversion**: All Koreography files in sets are converted to CadanceAsset format at runtime
- **Mixed Usage**: Can use both CadanceAsset and FMODKoreographySet in the same visor
- **Core Integration**: FMODKoreographySet support built into the core Cadance system
- **Migration Utility**: Automated tool to migrate existing FMODEventDescriptionVisor setups

## Koreography Compatibility Guide

### Using Existing FMODKoreographySet Files

You can now use your existing FMODKoreographySet files directly with FMODCadanceVisor:

1. **Add FMODCadanceVisor** to your GameObject
2. **Configure Event-KoreographySet pairs** in the inspector:
   - **Event**: Select your FMOD Studio Event
   - **Koreography Set**: Assign your existing FMODKoreographySet asset (leave CadanceAsset empty)
3. **Automatic Loading**: All Koreography files in the set will be automatically converted and loaded

### Migration Strategies

**Option 1: Immediate Migration (Recommended)**

```csharp
// Use the Migration Utility: Stylo/Cadance/Migration/Koreography Migration Utility
// This will automatically convert your existing FMODEventDescriptionVisor setups
```

**Option 2: Gradual Migration**

```csharp
// Keep using FMODKoreographySet files in FMODCadanceVisor
// Gradually replace with CadanceAssets as you create new content
```

**Option 3: Mixed Approach**

```csharp
// Use both CadanceAssets and FMODKoreographySet in the same visor
// New content uses CadanceAssets, existing content uses FMODKoreographySet
```

## Migration Steps

### 1. Replace Component

```csharp
// OLD: Koreographer
[AddComponentMenu("Koreographer/FMOD Studio/FMOD Event Description Visor")]
public class FMODEventDescriptionVisor : MonoBehaviour, IKoreographedPlayer

// NEW: Cadance
[AddComponentMenu("Stylo/Cadance/FMOD/FMOD Cadance Visor")]
public class FMODCadanceVisor : MonoBehaviour, ICadancePlayer
```

### 2. Update Event Pairing Structure

```csharp
// OLD: Koreographer EventDescPair
[Serializable]
struct EventDescPair
{
    public EventReference Event;
    public FMODKoreographySet koreographySet;
    [NonSerialized]
    public EventDescription description;
}

// NEW: Cadance EventCadancePair
[Serializable]
public struct EventCadancePair
{
    public EventReference Event;
    public CadanceSet cadanceSet;
    public string customCadanceID;
    [NonSerialized]
    public EventDescription description;
}
```

### 3. Update Inspector Configuration

- **Event Field**: Same `EventReference` field
- **Choreography Field**: Change from `FMODKoreographySet` to `CadanceSet`
- **Target System**: Change from `Koreographer` to `Cadance`
- **New Field**: Optional `customCadanceID` for custom naming

### 4. Update Code References

```csharp
// OLD: Koreographer
FMODEventDescriptionVisor visor = GetComponent<FMODEventDescriptionVisor>();
visor.LoadKoreography();

// NEW: Cadance
FMODCadanceVisor visor = GetComponent<FMODCadanceVisor>();
visor.RegisterAllEvents();
```

## Configuration Guide

### Basic Setup

1. Add `FMODCadanceVisor` component to a GameObject
2. Configure the event-cadance pairs in the inspector:
   - **Event**: Select your FMOD Studio Event
   - **Cadance Set**: Assign your CadanceSet (converted from FMODKoreographySet)
   - **Custom Cadance ID**: (Optional) Custom identifier
3. Set **Target Cadance** (or leave null for singleton)
4. Enable **Is Music Player** if this should control music playback
5. Enable **Auto Register Events** for automatic setup

### Advanced Configuration

```csharp
// Manual event registration
FMODCadanceVisor visor = GetComponent<FMODCadanceVisor>();
visor.RegisterAllEvents();

// Play specific event
visor.PlayEvent("MainTheme");

// Stop specific event
visor.StopEvent("MainTheme");

// Get event tracker for advanced control
FMODEventInstanceTracker tracker = visor.GetEventTracker("MainTheme");
if (tracker != null)
{
    int currentSample = tracker.CurrentSamplePosition;
    bool isPlaying = tracker.IsPlaying;
    float pitch = tracker.CurrentPitch;
}
```

## Feature Parity Status

| Feature                     | Koreographer | Cadance | Status      |
| --------------------------- | ------------ | ------- | ----------- |
| Event-Choreography Pairing  | ✅           | ✅      | ✅ Complete |
| Automatic Event Detection   | ✅           | ✅      | ✅ Complete |
| Music Player Integration    | ✅           | ✅      | ✅ Complete |
| Event Instance Management   | ✅           | ✅      | ✅ Complete |
| Runtime Event Control       | ✅           | ✅      | ✅ Complete |
| Inspector Integration       | ✅           | ✅      | ✅ Complete |
| Debug Logging               | ✅           | ✅      | ✅ Complete |
| Loaded Choreography Display | ✅           | ✅      | ✅ Complete |
| Automatic Asset Loading     | ✅           | ✅      | ✅ Complete |
| Custom Event IDs            | ❌           | ✅      | ✅ Enhanced |
| Runtime Registration        | ❌           | ✅      | ✅ Enhanced |
| Manual Load/Unload Controls | ❌           | ✅      | ✅ Enhanced |

## Usage Examples

### Simple Music Setup

```csharp
// 1. Create FMODCadanceVisor component
// 2. Add event pair:
//    - Event: event:/Music/MainTheme
//    - CadanceAsset: MainTheme_Cadance
// 3. Enable "Is Music Player"
// 4. Enable "Auto Register Events"
```

### Multiple Event Management

```csharp
// Configure multiple event pairs in inspector:
// - Background Music: event:/Music/Background -> Background_Cadance
// - Combat Music: event:/Music/Combat -> Combat_Cadance
// - Menu Music: event:/Music/Menu -> Menu_Cadance

// Runtime control:
visor.PlayEvent("event:/Music/Background");
visor.StopEvent("event:/Music/Combat");
```

### Custom Event IDs

```csharp
// Use custom Cadance IDs for cleaner code:
// Event: event:/Music/Level1/Intro
// Custom Cadance ID: "Level1Intro"

visor.PlayEvent("Level1Intro");  // Much cleaner than full path
```

## Migration Checklist

- [ ] Replace `FMODEventDescriptionVisor` with `FMODCadanceVisor`
- [ ] Convert `FMODKoreographySet` references to `CadanceAsset`
- [ ] Update `Koreographer` target to `Cadance` target
- [ ] Test event registration and playback
- [ ] Verify music player functionality
- [ ] Update any code references to use new API
- [ ] Test runtime event control
- [ ] Verify debug logging works as expected

## Troubleshooting

### Events Not Playing

1. Check that `FMODCadanceManager` is present in scene
2. Verify FMOD events are valid and banks are loaded
3. Enable debug logging to see registration status
4. Check console for error messages

### CadanceAssets Not Loading

1. Ensure CadanceAssets are properly configured
2. Check that `Cadance` singleton is available
3. Verify auto-registration is enabled or call `RegisterAllEvents()` manually
4. Ensure CadanceAssets have valid `SourceClip` assigned for length calculations

### Compilation Errors

1. If you see `TotalSampleCount` errors, ensure you're using the latest CadanceAsset version
2. The `TotalSampleCount` property was added to provide AudioClip sample count access
3. Verify all using directives are present at the top of files
4. Missing payload types (`BoolPayload`, `ColorPayload`) have been added to Cadance
5. Koreography conversion uses reflection for robust payload handling

### Performance Issues

1. Disable debug logging in production builds
2. Consider using custom Cadance IDs for better performance
3. Monitor the number of active event trackers

## API Reference

### FMODCadanceVisor Methods

- `RegisterAllEvents()` - Register all configured events
- `UnregisterAllEvents()` - Unregister all events
- `PlayEvent(string cadanceID)` - Play specific event
- `StopEvent(string cadanceID)` - Stop specific event
- `GetEventTracker(string cadanceID)` - Get tracker for advanced control

### EventCadancePair Properties

- `Event` - FMOD Studio Event Reference
- `cadanceAsset` - Associated CadanceAsset
- `customCadanceID` - Optional custom identifier
- `GetCadanceID()` - Gets effective Cadance ID

This migration provides complete feature parity with Koreographer's FMOD Event Description Visor while adding enhanced functionality for better workflow integration.
