using UnityEngine;
using UnityEditor;
using NUnit.Framework;

namespace Stylo.Cadance.Editor.Tests
{
    /// <summary>
    /// Test suite for the Cadance Real-Time Spectrum Analyzer.
    /// Verifies core functionality and integration with the Cadance system.
    /// </summary>
    public class CadanceSpectrumAnalyzerTests
    {
        private CadanceRealtimeSpectrumAnalyzer analyzer;
        private CadanceAsset testCadance;
        private CadanceTrack testTrack;

        [SetUp]
        public void SetUp()
        {
            // Create test analyzer
            analyzer = new CadanceRealtimeSpectrumAnalyzer();
            
            // Create test Cadance asset
            testCadance = ScriptableObject.CreateInstance<CadanceAsset>();
            testCadance.name = "TestCadance";
            
            // Create test track
            testTrack = CadanceTrack.CreateTrack("TestSpectrum");
            testCadance.AddTrack(testTrack);
        }

        [TearDown]
        public void TearDown()
        {
            if (analyzer != null)
            {
                analyzer.StopAnalysis();
            }
            
            if (testCadance != null)
            {
                Object.DestroyImmediate(testCadance);
            }
            
            if (testTrack != null)
            {
                Object.DestroyImmediate(testTrack);
            }
        }

        [Test]
        public void TestAnalyzerInitialization()
        {
            // Test default configuration
            Assert.AreEqual(60f, analyzer.minFrequency, "Default min frequency should be 60Hz");
            Assert.AreEqual(8000f, analyzer.maxFrequency, "Default max frequency should be 8000Hz");
            Assert.AreEqual(0.1f, analyzer.threshold, "Default threshold should be 0.1");
            Assert.AreEqual(0.1f, analyzer.minEventGap, "Default min event gap should be 0.1s");
            Assert.IsTrue(analyzer.enableEventCreation, "Event creation should be enabled by default");
            Assert.IsTrue(analyzer.enableVisualization, "Visualization should be enabled by default");
        }

        [Test]
        public void TestAnalyzerConfiguration()
        {
            // Test configuration changes
            analyzer.minFrequency = 100f;
            analyzer.maxFrequency = 5000f;
            analyzer.threshold = 0.2f;
            analyzer.minEventGap = 0.05f;
            analyzer.enableEventCreation = false;
            analyzer.enableVisualization = false;

            Assert.AreEqual(100f, analyzer.minFrequency);
            Assert.AreEqual(5000f, analyzer.maxFrequency);
            Assert.AreEqual(0.2f, analyzer.threshold);
            Assert.AreEqual(0.05f, analyzer.minEventGap);
            Assert.IsFalse(analyzer.enableEventCreation);
            Assert.IsFalse(analyzer.enableVisualization);
        }

        [Test]
        public void TestInitializeWithCadanceAndTrack()
        {
            // Test initialization with Cadance asset and track
            analyzer.Initialize(testCadance, testTrack);
            
            // Verify analyzer is properly initialized
            Assert.IsNotNull(analyzer, "Analyzer should be initialized");
            
            // Test that analyzer can be cleared
            analyzer.ClearEvents();
            Assert.IsNotNull(analyzer, "Analyzer should remain valid after clearing events");
        }

        [Test]
        public void TestAnalysisStateManagement()
        {
            // Test analysis state management
            Assert.IsFalse(analyzer.IsAnalyzing(), "Analyzer should not be analyzing initially");
            
            // Note: We can't test actual analysis start/stop without FMOD audio system
            // in unit tests, but we can test the state management
            analyzer.StopAnalysis(); // Should handle being called when not analyzing
            Assert.IsFalse(analyzer.IsAnalyzing(), "Analyzer should still not be analyzing");
        }

        [Test]
        public void TestVisualizationSettings()
        {
            // Test visualization configuration
            analyzer.spectrumBars = 128;
            analyzer.visualizationHeight = 150f;
            analyzer.spectrumColor = Color.red;
            analyzer.thresholdColor = Color.yellow;
            analyzer.backgroundColor = Color.blue;

            Assert.AreEqual(128, analyzer.spectrumBars);
            Assert.AreEqual(150f, analyzer.visualizationHeight);
            Assert.AreEqual(Color.red, analyzer.spectrumColor);
            Assert.AreEqual(Color.yellow, analyzer.thresholdColor);
            Assert.AreEqual(Color.blue, analyzer.backgroundColor);
        }

        [Test]
        public void TestEventCreationSettings()
        {
            // Test event creation configuration
            analyzer.enableEventCreation = true;
            analyzer.threshold = 0.3f;
            analyzer.minEventGap = 0.2f;

            Assert.IsTrue(analyzer.enableEventCreation);
            Assert.AreEqual(0.3f, analyzer.threshold);
            Assert.AreEqual(0.2f, analyzer.minEventGap);
        }

        [Test]
        public void TestFrequencyRangeValidation()
        {
            // Test frequency range settings
            analyzer.minFrequency = 20f;
            analyzer.maxFrequency = 20000f;

            Assert.AreEqual(20f, analyzer.minFrequency);
            Assert.AreEqual(20000f, analyzer.maxFrequency);
            Assert.IsTrue(analyzer.maxFrequency > analyzer.minFrequency, 
                "Max frequency should be greater than min frequency");
        }

        [Test]
        public void TestClearEventsFunction()
        {
            // Initialize analyzer
            analyzer.Initialize(testCadance, testTrack);
            
            // Clear events (should not throw)
            Assert.DoesNotThrow(() => analyzer.ClearEvents(), 
                "ClearEvents should not throw exceptions");
        }

        [Test]
        public void TestGetCurrentSpectrum()
        {
            // Test spectrum data retrieval
            var spectrum = analyzer.GetCurrentSpectrum();
            
            // Should return null when not analyzing
            Assert.IsNull(spectrum, "Spectrum should be null when not analyzing");
        }

        /// <summary>
        /// Integration test that verifies the analyzer can be used with the Cadance Editor.
        /// This test requires manual verification in the editor.
        /// </summary>
        [Test]
        [Category("Integration")]
        public void TestEditorIntegration()
        {
            // This test verifies that the analyzer integrates properly with editor systems
            // Create a test rect for visualization
            Rect testRect = new Rect(0, 0, 400, 100);
            
            // Test that drawing doesn't throw exceptions
            Assert.DoesNotThrow(() => analyzer.DrawVisualization(testRect), 
                "DrawVisualization should not throw exceptions");
            
            // Test configuration UI drawing
            Assert.DoesNotThrow(() => analyzer.DrawConfigurationUI(), 
                "DrawConfigurationUI should not throw exceptions");
        }

        /// <summary>
        /// Performance test to ensure the analyzer doesn't cause significant overhead.
        /// </summary>
        [Test]
        [Category("Performance")]
        public void TestPerformance()
        {
            // Initialize analyzer
            analyzer.Initialize(testCadance, testTrack);
            
            // Measure time for multiple update calls
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            
            for (int i = 0; i < 100; i++)
            {
                analyzer.Update(i * 0.01f); // Simulate 10ms intervals
            }
            
            stopwatch.Stop();
            
            // Should complete 100 updates in reasonable time (< 10ms)
            Assert.Less(stopwatch.ElapsedMilliseconds, 10, 
                "100 analyzer updates should complete in less than 10ms");
        }

        /// <summary>
        /// Test that verifies proper cleanup and resource management.
        /// </summary>
        [Test]
        public void TestResourceCleanup()
        {
            // Initialize analyzer
            analyzer.Initialize(testCadance, testTrack);
            
            // Simulate some usage
            analyzer.Update(1.0f);
            analyzer.Update(2.0f);
            
            // Clear events and stop analysis
            analyzer.ClearEvents();
            analyzer.StopAnalysis();
            
            // Verify clean state
            Assert.IsFalse(analyzer.IsAnalyzing(), "Analyzer should not be analyzing after stop");
            Assert.IsNull(analyzer.GetCurrentSpectrum(), "Spectrum should be null after stop");
        }
    }

    /// <summary>
    /// Manual test helper for editor testing.
    /// Use this in the editor to manually verify spectrum analyzer functionality.
    /// </summary>
    public static class CadanceSpectrumAnalyzerManualTests
    {
        [MenuItem("Stylo/Cadance/Test Spectrum Analyzer")]
        public static void TestSpectrumAnalyzer()
        {
            var window = EditorWindow.GetWindow<CadanceEditorWindow>("Cadance Editor");
            
            EditorUtility.DisplayDialog("Spectrum Analyzer Test", 
                "1. Load a CadanceAsset with AudioClip\n" +
                "2. Select a track\n" +
                "3. Enable 'Spectrum' in toolbar\n" +
                "4. Press play to see real-time analysis\n" +
                "5. Adjust frequency range and threshold\n" +
                "6. Verify events are created automatically", 
                "OK");
        }

        [MenuItem("Stylo/Cadance/Reset Spectrum Analyzer")]
        public static void ResetSpectrumAnalyzer()
        {
            // This can be used to reset the analyzer state if needed
            Debug.Log("[Cadance] Spectrum analyzer reset requested. " +
                     "Disable and re-enable the spectrum analyzer in the editor.");
        }
    }
}
