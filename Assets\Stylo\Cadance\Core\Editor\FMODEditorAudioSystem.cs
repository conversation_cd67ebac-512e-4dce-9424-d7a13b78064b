using UnityEngine;
using UnityEditor;
using System;
using System.IO;
using System.Collections.Generic;
using FMODUnity;
using FMODCore = global::FMOD;

namespace Stylo.Cadance
{
    /// <summary>
    /// FMOD-based audio system for Cadance Editor that bypasses Unity's disabled audio system.
    /// Provides audio preview, waveform generation, and timeline scrubbing using FMOD directly.
    /// Uses existing FMOD RuntimeManager for compatibility.
    /// </summary>
    public static class FMODEditorAudioSystem
    {
        private static FMODCore.Sound currentSound;
        private static FMODCore.Channel currentChannel;
        private static FMODCore.DSP fftDSP;
        private static FMODCore.DSP volumeDSP; // Separate volume DSP for editor control
        private static bool isInitialized = false;
        private static bool isPlaying = false;
        private static string currentAudioPath = "";

        // Volume control (independent from game audio)
        private static float editorVolume = 1.0f; // 0.0 to 1.0

        // Spectrum analysis data
        private static float[] spectrumData;
        private static bool spectrumAnalysisEnabled = false;

        /// <summary>
        /// Initializes the FMOD editor audio system using FMOD Editor system.
        /// </summary>
        public static bool Initialize()
        {
            if (isInitialized) return true;

            try
            {
                // Use FMOD Editor system (not RuntimeManager which is runtime-only)
                var editorSystem = FMODUnity.EditorUtils.System;
                if (!editorSystem.isValid())
                {
                    Debug.LogWarning("[FMOD Editor Audio] FMOD Editor system not available");
                    return false;
                }

                isInitialized = true;
                Debug.Log("[FMOD Editor Audio] System initialized successfully using FMOD Editor system");
                return true;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[FMOD Editor Audio] Failed to initialize: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Shuts down the FMOD editor audio system.
        /// </summary>
        public static void Shutdown()
        {
            if (!isInitialized) return;

            try
            {
                StopAudio();
                DisableSpectrumAnalysis();

                if (currentSound.hasHandle())
                {
                    currentSound.release();
                }

                isInitialized = false;
                Debug.Log("[FMOD Editor Audio] System shut down");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[FMOD Editor Audio] Error during shutdown: {ex.Message}");
            }
        }

        /// <summary>
        /// Loads an audio file for preview using FMOD.
        /// </summary>
        /// <param name="audioFilePath">Path to the audio file</param>
        /// <returns>True if loaded successfully</returns>
        public static bool LoadAudioFile(string audioFilePath)
        {
            if (!isInitialized && !Initialize()) return false;

            try
            {
                // Stop current audio
                StopAudio();

                // Release previous sound
                if (currentSound.hasHandle())
                {
                    currentSound.release();
                }

                // Load new sound using FMOD Editor system with seamless looping
                var editorSystem = FMODUnity.EditorUtils.System;
                FMODCore.System coreSystem;
                editorSystem.getCoreSystem(out coreSystem);
                var result = coreSystem.createSound(audioFilePath,
                    FMODCore.MODE._2D | FMODCore.MODE.CREATESAMPLE | FMODCore.MODE.LOOP_NORMAL,
                    out currentSound);
                if (result != FMODCore.RESULT.OK)
                {
                    Debug.LogError($"[FMOD Editor Audio] Failed to load audio file: {result}");
                    return false;
                }

                currentAudioPath = audioFilePath;
                Debug.Log($"[FMOD Editor Audio] Loaded audio file: {Path.GetFileName(audioFilePath)}");
                return true;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[FMOD Editor Audio] Error loading audio file: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Loads an AudioClip for preview by finding its source file.
        /// </summary>
        /// <param name="audioClip">The AudioClip to load</param>
        /// <returns>True if loaded successfully</returns>
        public static bool LoadAudioClip(AudioClip audioClip)
        {
            if (audioClip == null) return false;

            // Try to find the source file path
            string audioPath = GetAudioClipSourcePath(audioClip);
            if (!string.IsNullOrEmpty(audioPath) && File.Exists(audioPath))
            {
                return LoadAudioFile(audioPath);
            }

            Debug.LogWarning($"[FMOD Editor Audio] Could not find source file for AudioClip: {audioClip.name}");
            return false;
        }

        /// <summary>
        /// Starts audio playback.
        /// </summary>
        /// <param name="startTimeSeconds">Start time in seconds</param>
        /// <returns>True if playback started successfully</returns>
        public static bool PlayAudio(float startTimeSeconds = 0f)
        {
            if (!isInitialized || !currentSound.hasHandle()) return false;

            try
            {
                // Play the sound using FMOD Editor system
                var editorSystem = FMODUnity.EditorUtils.System;
                FMODCore.System coreSystem;
                editorSystem.getCoreSystem(out coreSystem);
                FMODCore.ChannelGroup channelGroup = new FMODCore.ChannelGroup();
                var result = coreSystem.playSound(currentSound, channelGroup, false, out currentChannel);
                if (result != FMODCore.RESULT.OK)
                {
                    Debug.LogError($"[FMOD Editor Audio] Failed to play sound: {result}");
                    return false;
                }

                // Set start position if specified
                if (startTimeSeconds > 0f)
                {
                    uint startPosition = (uint)(startTimeSeconds * 1000f); // Convert to milliseconds
                    currentChannel.setPosition(startPosition, FMODCore.TIMEUNIT.MS);
                }

                // Create and configure volume DSP for editor control
                SetupVolumeDSP();

                // Set channel volume to 1.0 (full volume) so spectrum analysis gets unattenuated data
                currentChannel.setVolume(1.0f);

                isPlaying = true;
                return true;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[FMOD Editor Audio] Error starting playback: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Stops audio playback.
        /// </summary>
        public static void StopAudio()
        {
            if (!isInitialized) return;

            try
            {
                if (currentChannel.hasHandle())
                {
                    currentChannel.stop();
                }
                isPlaying = false;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[FMOD Editor Audio] Error stopping playback: {ex.Message}");
            }
        }

        /// <summary>
        /// Pauses or resumes audio playback.
        /// </summary>
        /// <param name="paused">True to pause, false to resume</param>
        public static void SetPaused(bool paused)
        {
            if (!isInitialized || !currentChannel.hasHandle()) return;

            try
            {
                currentChannel.setPaused(paused);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[FMOD Editor Audio] Error setting pause state: {ex.Message}");
            }
        }

        /// <summary>
        /// Sets the playback position.
        /// </summary>
        /// <param name="timeSeconds">Time in seconds</param>
        public static void SetPosition(float timeSeconds)
        {
            if (!isInitialized || !currentChannel.hasHandle()) return;

            try
            {
                uint position = (uint)(timeSeconds * 1000f); // Convert to milliseconds
                currentChannel.setPosition(position, FMODCore.TIMEUNIT.MS);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[FMOD Editor Audio] Error setting position: {ex.Message}");
            }
        }

        /// <summary>
        /// Sets the playback speed/pitch.
        /// </summary>
        /// <param name="speed">Playback speed multiplier (1.0 = normal speed)</param>
        public static void SetPlaybackSpeed(float speed)
        {
            if (!isInitialized || !currentChannel.hasHandle()) return;

            try
            {
                currentChannel.setPitch(speed);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[FMOD Editor Audio] Error setting playback speed: {ex.Message}");
            }
        }

        /// <summary>
        /// Sets the editor volume (independent from game audio and spectrum analysis).
        /// </summary>
        /// <param name="volume">Volume level (0.0 to 1.0)</param>
        public static void SetVolume(float volume)
        {
            editorVolume = Mathf.Clamp01(volume);

            if (!isInitialized || !currentChannel.hasHandle()) return;

            try
            {
                // Use volume DSP instead of channel volume to preserve unattenuated spectrum analysis
                if (volumeDSP.hasHandle())
                {
                    volumeDSP.setParameterFloat((int)FMODCore.DSP_FADER.GAIN, editorVolume);
                }
                else
                {
                    // Fallback to channel volume if DSP not available
                    currentChannel.setVolume(editorVolume);
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[FMOD Editor Audio] Error setting volume: {ex.Message}");
            }
        }

        /// <summary>
        /// Gets the current editor volume.
        /// </summary>
        /// <returns>Volume level (0.0 to 1.0)</returns>
        public static float GetVolume()
        {
            return editorVolume;
        }

        /// <summary>
        /// Gets the current playback position in seconds.
        /// </summary>
        /// <returns>Current position in seconds</returns>
        public static float GetPosition()
        {
            if (!isInitialized || !currentChannel.hasHandle()) return 0f;

            try
            {
                currentChannel.getPosition(out uint position, FMODCore.TIMEUNIT.MS);
                return position / 1000f; // Convert from milliseconds to seconds
            }
            catch (Exception ex)
            {
                Debug.LogError($"[FMOD Editor Audio] Error getting position: {ex.Message}");
                return 0f;
            }
        }

        /// <summary>
        /// Gets the total length of the current audio in seconds.
        /// </summary>
        /// <returns>Length in seconds</returns>
        public static float GetLength()
        {
            if (!isInitialized || !currentSound.hasHandle()) return 0f;

            try
            {
                currentSound.getLength(out uint length, FMODCore.TIMEUNIT.MS);
                return length / 1000f; // Convert from milliseconds to seconds
            }
            catch (Exception ex)
            {
                Debug.LogError($"[FMOD Editor Audio] Error getting length: {ex.Message}");
                return 0f;
            }
        }

        /// <summary>
        /// Checks if audio is currently playing.
        /// </summary>
        /// <returns>True if playing</returns>
        public static bool IsPlaying()
        {
            if (!isInitialized || !currentChannel.hasHandle()) return false;

            try
            {
                currentChannel.isPlaying(out bool playing);
                isPlaying = playing;
                return playing;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[FMOD Editor Audio] Error checking play state: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Sets whether the audio should loop seamlessly.
        /// </summary>
        /// <param name="loop">True to enable looping, false to disable</param>
        public static void SetLooping(bool loop)
        {
            if (!isInitialized || !currentSound.hasHandle()) return;

            try
            {
                if (loop)
                {
                    currentSound.setMode(FMODCore.MODE.LOOP_NORMAL);
                }
                else
                {
                    currentSound.setMode(FMODCore.MODE.LOOP_OFF);
                }
                Debug.Log($"[FMOD Editor Audio] Looping set to: {loop}");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[FMOD Editor Audio] Error setting loop mode: {ex.Message}");
            }
        }

        /// <summary>
        /// Updates the FMOD system (should be called regularly).
        /// </summary>
        public static void Update()
        {
            if (!isInitialized) return;

            try
            {
                // Update FMOD Editor system
                var editorSystem = FMODUnity.EditorUtils.System;
                if (editorSystem.isValid())
                {
                    editorSystem.update();
                }

                // Check if spectrum analysis needs to be re-enabled after loop
                if (spectrumAnalysisEnabled && currentChannel.hasHandle() && !fftDSP.hasHandle())
                {
                    Debug.Log("[FMOD Editor Audio] Re-enabling spectrum analysis after loop");
                    EnableSpectrumAnalysis(spectrumData?.Length * 2 ?? 1024);
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[FMOD Editor Audio] Error during update: {ex.Message}");
            }
        }

        /// <summary>
        /// Sets up a separate volume DSP for editor control that doesn't affect spectrum analysis.
        /// </summary>
        private static void SetupVolumeDSP()
        {
            if (!isInitialized || !currentChannel.hasHandle()) return;

            try
            {
                // Create fader DSP for volume control
                var editorSystem = FMODUnity.EditorUtils.System;
                FMODCore.System coreSystem;
                editorSystem.getCoreSystem(out coreSystem);

                var result = coreSystem.createDSPByType(FMODCore.DSP_TYPE.FADER, out volumeDSP);
                if (result != FMODCore.RESULT.OK)
                {
                    Debug.LogWarning($"[FMOD Editor Audio] Failed to create volume DSP: {result}. Using channel volume instead.");
                    return;
                }

                // Add volume DSP after FFT DSP in the chain
                result = currentChannel.addDSP(FMODCore.CHANNELCONTROL_DSP_INDEX.HEAD, volumeDSP);
                if (result != FMODCore.RESULT.OK)
                {
                    Debug.LogWarning($"[FMOD Editor Audio] Failed to add volume DSP to channel: {result}. Using channel volume instead.");
                    volumeDSP.release();
                    return;
                }

                // Set initial volume
                volumeDSP.setParameterFloat((int)FMODCore.DSP_FADER.GAIN, editorVolume);
                Debug.Log("[FMOD Editor Audio] Volume DSP created and configured for independent volume control");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[FMOD Editor Audio] Error setting up volume DSP: {ex.Message}");
            }
        }

        /// <summary>
        /// Enables real-time spectrum analysis using FMOD DSP FFT.
        /// </summary>
        /// <param name="fftSize">FFT window size (default 1024)</param>
        /// <returns>True if spectrum analysis was enabled successfully</returns>
        public static bool EnableSpectrumAnalysis(int fftSize = 1024)
        {
            if (!isInitialized)
            {
                Debug.LogWarning("[FMOD Editor Audio] FMOD system not initialized. Spectrum analysis not available.");
                return false;
            }

            if (!currentChannel.hasHandle())
            {
                Debug.LogWarning("[FMOD Editor Audio] No active FMOD channel. Start audio playback first for spectrum analysis.");
                return false;
            }

            // Disable existing spectrum analysis first
            if (spectrumAnalysisEnabled)
            {
                DisableSpectrumAnalysis();
            }

            try
            {
                Debug.Log($"[FMOD Editor Audio] Attempting to enable spectrum analysis with FFT size: {fftSize}");

                // Create FFT DSP using FMOD Editor system
                var editorSystem = FMODUnity.EditorUtils.System;
                FMODCore.System coreSystem;
                editorSystem.getCoreSystem(out coreSystem);
                var result = coreSystem.createDSPByType(FMODCore.DSP_TYPE.FFT, out fftDSP);
                if (result != FMODCore.RESULT.OK)
                {
                    Debug.LogWarning($"[FMOD Editor Audio] Failed to create FFT DSP: {result}. Spectrum analysis not available.");
                    return false;
                }
                Debug.Log("[FMOD Editor Audio] FFT DSP created successfully");

                // Configure FFT parameters
                result = fftDSP.setParameterInt((int)FMODCore.DSP_FFT.WINDOWSIZE, fftSize);
                Debug.Log($"[FMOD Editor Audio] Set FFT window size result: {result}");

                result = fftDSP.setParameterInt((int)FMODCore.DSP_FFT.WINDOW, (int)FMODCore.DSP_FFT_WINDOW_TYPE.BLACKMANHARRIS);
                Debug.Log($"[FMOD Editor Audio] Set FFT window type result: {result}");

                // Add FFT DSP early in the chain to receive unattenuated audio
                // This ensures spectrum analysis gets full-volume data regardless of editor volume setting
                result = currentChannel.addDSP(0, fftDSP); // Add at index 0 (before any volume processing)
                if (result != FMODCore.RESULT.OK)
                {
                    Debug.LogWarning($"[FMOD Editor Audio] Failed to add FFT DSP to channel: {result}. Spectrum analysis not available.");
                    fftDSP.release();
                    return false;
                }
                Debug.Log("[FMOD Editor Audio] FFT DSP added to channel at index 0 for unattenuated spectrum analysis");

                // Initialize spectrum data array
                spectrumData = new float[fftSize / 2];
                spectrumAnalysisEnabled = true;

                Debug.Log($"[FMOD Editor Audio] Spectrum analysis enabled with FFT size: {fftSize}, spectrum array size: {spectrumData.Length}");
                return true;
            }
            catch (Exception ex)
            {
                Debug.LogWarning($"[FMOD Editor Audio] Error enabling spectrum analysis: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Disables real-time spectrum analysis and cleans up volume DSP.
        /// </summary>
        public static void DisableSpectrumAnalysis()
        {
            if (!spectrumAnalysisEnabled) return;

            try
            {
                // Clean up FFT DSP
                if (fftDSP.hasHandle())
                {
                    if (currentChannel.hasHandle())
                    {
                        currentChannel.removeDSP(fftDSP);
                    }
                    fftDSP.release();
                }

                // Clean up volume DSP
                if (volumeDSP.hasHandle())
                {
                    if (currentChannel.hasHandle())
                    {
                        currentChannel.removeDSP(volumeDSP);
                    }
                    volumeDSP.release();
                }

                spectrumAnalysisEnabled = false;
                spectrumData = null;
                Debug.Log("[FMOD Editor Audio] Spectrum analysis and volume DSP disabled");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[FMOD Editor Audio] Error disabling spectrum analysis: {ex.Message}");
            }
        }

        /// <summary>
        /// Gets the current spectrum data from FMOD FFT analysis.
        /// </summary>
        /// <returns>Array of spectrum magnitudes, or null if not available</returns>
        public static float[] GetSpectrumData()
        {
            if (!spectrumAnalysisEnabled || !fftDSP.hasHandle() || spectrumData == null)
            {
                return null;
            }

            try
            {
                // Check if channel is still valid - if not, spectrum analysis may have been disconnected
                if (!currentChannel.hasHandle())
                {
                    Debug.LogWarning("[FMOD Editor Audio] Channel lost during spectrum analysis");
                    return null;
                }

                // Get FFT data from DSP
                IntPtr unmanagedData;
                uint length;
                var result = fftDSP.getParameterData((int)FMODCore.DSP_FFT.SPECTRUMDATA, out unmanagedData, out length);
                if (result != FMODCore.RESULT.OK)
                {
                    // DSP might have been disconnected during loop - try to re-enable
                    if (result == FMODCore.RESULT.ERR_INVALID_HANDLE)
                    {
                        Debug.LogWarning("[FMOD Editor Audio] FFT DSP handle invalid - attempting to re-enable spectrum analysis");
                        // Don't re-enable here to avoid recursion, let the Update method handle it
                    }
                    return null;
                }

                if (unmanagedData == IntPtr.Zero) return null;

                // Convert to managed data structure
                var fftData = (FMODCore.DSP_PARAMETER_FFT)System.Runtime.InteropServices.Marshal.PtrToStructure(unmanagedData, typeof(FMODCore.DSP_PARAMETER_FFT));

                if (fftData.length > 0 && fftData.numchannels > 0)
                {
                    // Get spectrum for first channel
                    float[][] spectrum = fftData.spectrum;
                    if (spectrum != null && spectrum.Length > 0 && spectrum[0] != null)
                    {
                        // Copy data to our spectrum array
                        int copyLength = Math.Min(spectrumData.Length, spectrum[0].Length);
                        Array.Copy(spectrum[0], spectrumData, copyLength);
                        return spectrumData;
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[FMOD Editor Audio] Error getting spectrum data: {ex.Message}");
            }

            return null;
        }

        /// <summary>
        /// Gets the magnitude in a specific frequency range.
        /// </summary>
        /// <param name="minFreq">Minimum frequency in Hz</param>
        /// <param name="maxFreq">Maximum frequency in Hz</param>
        /// <param name="sampleRate">Sample rate (default 44100)</param>
        /// <returns>Average magnitude in the frequency range</returns>
        public static float GetMagnitudeInRange(float minFreq, float maxFreq, int sampleRate = 44100)
        {
            var spectrum = GetSpectrumData();
            if (spectrum == null || spectrum.Length == 0) return 0f;

            float nyquist = sampleRate * 0.5f;
            int minBin = Mathf.FloorToInt((minFreq / nyquist) * spectrum.Length);
            int maxBin = Mathf.CeilToInt((maxFreq / nyquist) * spectrum.Length);

            minBin = Mathf.Clamp(minBin, 0, spectrum.Length - 1);
            maxBin = Mathf.Clamp(maxBin, 0, spectrum.Length - 1);

            if (minBin >= maxBin) return spectrum[minBin];

            float sum = 0f;
            for (int i = minBin; i <= maxBin; i++)
            {
                sum += spectrum[i];
            }

            return sum / (maxBin - minBin + 1);
        }

        /// <summary>
        /// Checks if spectrum analysis is currently enabled.
        /// </summary>
        /// <returns>True if spectrum analysis is enabled</returns>
        public static bool IsSpectrumAnalysisEnabled()
        {
            return spectrumAnalysisEnabled;
        }

        /// <summary>
        /// Attempts to find the source file path for an AudioClip.
        /// </summary>
        /// <param name="audioClip">The AudioClip</param>
        /// <returns>Source file path or empty string if not found</returns>
        private static string GetAudioClipSourcePath(AudioClip audioClip)
        {
            if (audioClip == null) return "";

            // Get the asset path
            string assetPath = AssetDatabase.GetAssetPath(audioClip);
            if (!string.IsNullOrEmpty(assetPath))
            {
                // Convert to absolute path
                return Path.GetFullPath(assetPath);
            }

            // Try to find by name in imported audio directory
            string importedAudioDir = Path.Combine(Application.dataPath, "Stylo/Cadance/ImportedAudio");
            if (Directory.Exists(importedAudioDir))
            {
                string[] extensions = { ".wav", ".mp3", ".ogg", ".aiff" };
                foreach (string ext in extensions)
                {
                    string filePath = Path.Combine(importedAudioDir, audioClip.name + ext);
                    if (File.Exists(filePath))
                    {
                        return filePath;
                    }
                }
            }

            return "";
        }
    }
}
