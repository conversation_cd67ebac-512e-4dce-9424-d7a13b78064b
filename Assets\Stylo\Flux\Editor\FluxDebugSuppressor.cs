using UnityEngine;
using UnityEditor;

namespace Stylo.Flux.Universal
{
    [InitializeOnLoad]
    public class FluxDebugSuppressor
    {
        static FluxDebugSuppressor()
        {
            // Register to intercept log messages
            Application.logMessageReceived += OnLogMessageReceived;
        }

        private static void OnLogMessageReceived(string condition, string stackTrace, LogType type)
        {
            // Check if this is one of the Flux debug messages we want to suppress
            if ((type == LogType.Warning && condition.Contains("[Flux] Camera color format") && condition.Contains("differs from JPG Bitcrunch standard")) ||
                (type == LogType.Log && condition.Contains("[Flux] Previous frame texture available:")))
            {
                // Suppress the message by doing nothing
                // This effectively prevents it from appearing in the console
            }
        }
    }
}
