using UnityEngine;
using UnityEditor;
using Stylo.Cadance.FMOD;

namespace Stylo.Cadance.Editor
{
    /// <summary>
    /// Custom editor for FMODCadanceVisor to provide better inspector experience.
    /// </summary>
    [CustomEditor(typeof(FMODCadanceVisor))]
    public class FMODCadanceVisorEditor : UnityEditor.Editor
    {
        private SerializedProperty cadancedEventsProperty;
        private SerializedProperty targetCadanceProperty;
        private SerializedProperty isMusicPlayerProperty;
        private SerializedProperty autoRegisterEventsProperty;
        private SerializedProperty enableDebugLoggingProperty;

        private void OnEnable()
        {
            cadancedEventsProperty = serializedObject.FindProperty("cadancedEvents");
            targetCadanceProperty = serializedObject.FindProperty("targetCadance");
            isMusicPlayerProperty = serializedObject.FindProperty("isMusicPlayer");
            autoRegisterEventsProperty = serializedObject.FindProperty("autoRegisterEvents");
            enableDebugLoggingProperty = serializedObject.FindProperty("enableDebugLogging");
        }

        public override void OnInspectorGUI()
        {
            serializedObject.Update();

            FMODCadanceVisor visor = (FMODCadanceVisor)target;

            // Header
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("FMOD Cadance Visor", EditorStyles.boldLabel);
            EditorGUILayout.HelpBox("Pairs FMOD Studio Events with CadanceSets for automatic synchronization. " +
                                   "This is the Cadance equivalent of Koreographer's FMOD Event Description Visor.",
                                   MessageType.Info);

            EditorGUILayout.Space();

            // Configuration section
            EditorGUILayout.LabelField("Configuration", EditorStyles.boldLabel);
            EditorGUILayout.PropertyField(targetCadanceProperty);
            EditorGUILayout.PropertyField(isMusicPlayerProperty);
            EditorGUILayout.PropertyField(autoRegisterEventsProperty);
            EditorGUILayout.PropertyField(enableDebugLoggingProperty);

            EditorGUILayout.Space();

            // Event pairs section
            EditorGUILayout.LabelField("Event-Cadance Pairs", EditorStyles.boldLabel);
            EditorGUILayout.HelpBox("Each pair connects an FMOD Studio Event with a CadanceSet or Koreography (for backward compatibility). " +
                                   "When the FMOD event plays, the corresponding choreography will be synchronized automatically.",
                                   MessageType.None);

            EditorGUILayout.PropertyField(cadancedEventsProperty, true);

            EditorGUILayout.Space();

            // Runtime information
            if (Application.isPlaying && visor.IsInitialized)
            {
                EditorGUILayout.LabelField("Runtime Information", EditorStyles.boldLabel);

                EditorGUI.BeginDisabledGroup(true);
                EditorGUILayout.Toggle("Is Initialized", visor.IsInitialized);
                EditorGUILayout.IntField("Configured Events", visor.CadancedEvents.Count);
                EditorGUI.EndDisabledGroup();

                EditorGUILayout.Space();

                // Runtime controls
                EditorGUILayout.LabelField("Runtime Controls", EditorStyles.boldLabel);

                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button("Register All Events"))
                {
                    visor.RegisterAllEvents();
                }
                if (GUILayout.Button("Unregister All Events"))
                {
                    visor.UnregisterAllEvents();
                }
                EditorGUILayout.EndHorizontal();

                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button("Load All Cadances"))
                {
                    visor.LoadAllCadanceAssets();
                }
                if (GUILayout.Button("Unload All Cadances"))
                {
                    visor.UnloadAllCadanceAssets();
                }
                EditorGUILayout.EndHorizontal();

                // Individual event controls
                if (visor.CadancedEvents.Count > 0)
                {
                    EditorGUILayout.Space();
                    EditorGUILayout.LabelField("Individual Event Controls", EditorStyles.boldLabel);

                    foreach (var eventPair in visor.CadancedEvents)
                    {
                        if (eventPair.Event.IsNull || !eventPair.HasChoreographyData())
                            continue;

                        string cadanceID = eventPair.GetCadanceID();
                        var tracker = visor.GetEventTracker(cadanceID);

                        EditorGUILayout.BeginVertical(GUI.skin.box);
                        EditorGUILayout.LabelField($"Event: {eventPair.Event.Path}", EditorStyles.miniLabel);

                        if (eventPair.cadanceSet != null)
                        {
                            EditorGUILayout.LabelField($"Cadance Set: {eventPair.cadanceSet.name} ({eventPair.cadanceSet.Count} files)", EditorStyles.miniLabel);
                        }
                        else if (eventPair.koreographySet != null)
                        {
                            EditorGUILayout.LabelField($"Koreography Set: {eventPair.koreographySet.name} ({eventPair.koreographySet.koreographies.Count} files)", EditorStyles.miniLabel);
                        }

                        if (tracker != null)
                        {
                            EditorGUI.BeginDisabledGroup(true);
                            EditorGUILayout.Toggle("Is Playing", tracker.IsPlaying);
                            EditorGUILayout.IntField("Current Sample", tracker.CurrentSamplePosition);
                            EditorGUILayout.FloatField("Current Pitch", tracker.CurrentPitch);
                            EditorGUI.EndDisabledGroup();

                            EditorGUILayout.BeginHorizontal();
                            if (GUILayout.Button("Play"))
                            {
                                visor.PlayEvent(cadanceID);
                            }
                            if (GUILayout.Button("Stop"))
                            {
                                visor.StopEvent(cadanceID);
                            }
                            EditorGUILayout.EndHorizontal();
                        }
                        else
                        {
                            EditorGUILayout.LabelField("Not Registered", EditorStyles.miniLabel);
                        }

                        EditorGUILayout.EndVertical();
                        EditorGUILayout.Space(2);
                    }
                }
            }
            else if (Application.isPlaying)
            {
                EditorGUILayout.HelpBox("Visor is not initialized. Check console for errors.", MessageType.Warning);
            }
            else
            {
                EditorGUILayout.HelpBox("Runtime information will be available when playing.", MessageType.Info);
            }

            serializedObject.ApplyModifiedProperties();
        }
    }
}
