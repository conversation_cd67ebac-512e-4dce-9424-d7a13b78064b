%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: Unity Normal From Height
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor v1.9.2.2\n// Available at the
    Unity Asset Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=19202\nNode;AmplifyShaderEditor.WorldToTangentMatrix;2;-706.0457,229.9493;Inherit;False;0;1;FLOAT3x3;0\nNode;AmplifyShaderEditor.WorldPosInputsNode;3;-696.0447,58.61633;Inherit;False;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.FunctionOutput;0;35.33337,-15.33331;Inherit;False;True;-1;Output;0;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.CustomExpressionNode;1;-323.4106,-16.06665;Inherit;False;Unity_NormalFromHeight_Tangent;3;File;4;True;in;FLOAT;0;In;;Inherit;False;True;strength;FLOAT;0;In;;Inherit;False;True;position;FLOAT3;0,0,0;In;;Inherit;False;True;tangentMatrix;FLOAT3x3;1,0,0,1,1,1,1,0,1;In;;Inherit;False;Unity_NormalFromHeight_Tangent;False;False;0;bbbc7e04c36345044973a6b3a2b28950;False;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT3;0,0,0;False;3;FLOAT3x3;1,0,0,1,1,1,1,0,1;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionInput;5;-699.4153,-106.347;Inherit;False;Strength;1;1;False;1;0;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;4;-703.7488,-219.0137;Inherit;False;Height;1;0;False;1;0;FLOAT;0;False;1;FLOAT;0\nWireConnection;0;0;1;0\nWireConnection;1;0;4;0\nWireConnection;1;1;5;0\nWireConnection;1;2;3;0\nWireConnection;1;3;2;0\nASEEND*/\n//CHKSM=2552F602BC0E65C75B12C2E91AF1FDFFACE60DA6"
  m_functionName: 
  m_description: 
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 0
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_customEdited: 0
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 3
  m_headerStyle: 0
  m_headerColor: {r: 1, g: 0.4, b: 0, a: 1}
  m_customNodeCategory: 
  m_previewPosition: 0
  m_hidden: 0
  m_url: 
