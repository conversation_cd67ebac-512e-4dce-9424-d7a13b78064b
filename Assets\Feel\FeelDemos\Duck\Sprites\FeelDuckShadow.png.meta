fileFormatVersion: 2
guid: 9ce434ac86a021e418c3060399e7abdd
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -1488195887490226262
    second: FeelDuckShadow_0
  - first:
      213: -4435484407326516809
    second: FeelDuckShadow_1
  - first:
      213: 716272977604518773
    second: FeelDuckShadow_2
  - first:
      213: -4947257590945785970
    second: FeelDuckShadow_3
  - first:
      213: 466204985308708396
    second: FeelDuckShadow_4
  - first:
      213: -1800869760373755936
    second: FeelDuckShadow_5
  - first:
      213: -2857420933867021769
    second: FeelDuckShadow_6
  - first:
      213: 5970199749122678860
    second: FeelDuckShadow_7
  - first:
      213: 9119986006023652670
    second: FeelDuckShadow_8
  - first:
      213: -5418687466495508228
    second: FeelDuckShadow_9
  - first:
      213: 2801126888289971305
    second: FeelDuckShadow_10
  - first:
      213: -4930013394555498610
    second: FeelDuckShadow_11
  - first:
      213: 4336739000701341329
    second: FeelDuckShadow_12
  - first:
      213: 7711199070588570761
    second: FeelDuckShadow_13
  - first:
      213: 3666431415229514628
    second: FeelDuckShadow_14
  - first:
      213: 2315293671311913038
    second: FeelDuckShadow_15
  - first:
      213: -9211355452573300150
    second: FeelDuckShadow_16
  externalObjects: {}
  serializedVersion: 12
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMasterTextureLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 0
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  cookieLightType: 1
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: iPhone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: FeelDuckShadow_0
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: aa7a2f1a9bdd85be0800000000000000
      internalID: -1488195887490226262
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: FeelDuckShadow_1
      rect:
        serializedVersion: 2
        x: 66
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7b9d4868e6ef172c0800000000000000
      internalID: -4435484407326516809
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: FeelDuckShadow_2
      rect:
        serializedVersion: 2
        x: 132
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 57324586a76b0f900800000000000000
      internalID: 716272977604518773
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: FeelDuckShadow_3
      rect:
        serializedVersion: 2
        x: 198
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e875257ce6fc75bb0800000000000000
      internalID: -4947257590945785970
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: FeelDuckShadow_4
      rect:
        serializedVersion: 2
        x: 264
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c2e77ddc6fa487600800000000000000
      internalID: 466204985308708396
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: FeelDuckShadow_5
      rect:
        serializedVersion: 2
        x: 330
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 0efed2e8e760207e0800000000000000
      internalID: -1800869760373755936
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: FeelDuckShadow_6
      rect:
        serializedVersion: 2
        x: 396
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 73a5717ccc66858d0800000000000000
      internalID: -2857420933867021769
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: FeelDuckShadow_7
      rect:
        serializedVersion: 2
        x: 462
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c4065945a096ad250800000000000000
      internalID: 5970199749122678860
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: FeelDuckShadow_8
      rect:
        serializedVersion: 2
        x: 528
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e35c295e3f2b09e70800000000000000
      internalID: 9119986006023652670
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: FeelDuckShadow_9
      rect:
        serializedVersion: 2
        x: 594
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: cf08caa6574fcc4b0800000000000000
      internalID: -5418687466495508228
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: FeelDuckShadow_10
      rect:
        serializedVersion: 2
        x: 660
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 96ca15b501a9fd620800000000000000
      internalID: 2801126888289971305
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: FeelDuckShadow_11
      rect:
        serializedVersion: 2
        x: 726
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e8f0a9f10f2159bb0800000000000000
      internalID: -4930013394555498610
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: FeelDuckShadow_12
      rect:
        serializedVersion: 2
        x: 792
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 19ec264e4213f2c30800000000000000
      internalID: 4336739000701341329
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: FeelDuckShadow_13
      rect:
        serializedVersion: 2
        x: 858
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 98c01c2f2bea30b60800000000000000
      internalID: 7711199070588570761
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: FeelDuckShadow_14
      rect:
        serializedVersion: 2
        x: 924
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 483861252f7c1e230800000000000000
      internalID: 3666431415229514628
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: FeelDuckShadow_15
      rect:
        serializedVersion: 2
        x: 990
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e40b14db353912020800000000000000
      internalID: 2315293671311913038
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: FeelDuckShadow_16
      rect:
        serializedVersion: 2
        x: 1056
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a4ac28d0501ba2080800000000000000
      internalID: -9211355452573300150
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: 5e97eb03825dee720800000000000000
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      FeelDuckShadow_0: -1488195887490226262
      FeelDuckShadow_1: -4435484407326516809
      FeelDuckShadow_10: 2801126888289971305
      FeelDuckShadow_11: -4930013394555498610
      FeelDuckShadow_12: 4336739000701341329
      FeelDuckShadow_13: 7711199070588570761
      FeelDuckShadow_14: 3666431415229514628
      FeelDuckShadow_15: 2315293671311913038
      FeelDuckShadow_16: -9211355452573300150
      FeelDuckShadow_2: 716272977604518773
      FeelDuckShadow_3: -4947257590945785970
      FeelDuckShadow_4: 466204985308708396
      FeelDuckShadow_5: -1800869760373755936
      FeelDuckShadow_6: -2857420933867021769
      FeelDuckShadow_7: 5970199749122678860
      FeelDuckShadow_8: 9119986006023652670
      FeelDuckShadow_9: -5418687466495508228
  spritePackingTag: 
  pSDRemoveMatte: 0
  pSDShowRemoveMatteOption: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
AssetOrigin:
  serializedVersion: 1
  productId: 183370
  packageName: Feel
  packageVersion: 5.5
  assetPath: Assets/Feel/FeelDemos/Duck/Sprites/FeelDuckShadow.png
  uploadId: 759320
