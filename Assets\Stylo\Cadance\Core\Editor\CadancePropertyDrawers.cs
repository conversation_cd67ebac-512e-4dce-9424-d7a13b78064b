using UnityEngine;
using UnityEditor;

namespace Stylo.Cadance.Editor
{
    /// <summary>
    /// Custom property drawer for CadanceAsset, equivalent to Koreographer's KoreographyPropertyDrawer.
    /// </summary>
    [CustomPropertyDrawer(typeof(CadanceAsset))]
    public class CadanceAssetPropertyDrawer : PropertyDrawer
    {
        public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
        {
            EditorGUI.BeginProperty(position, label, property);

            // Calculate rects
            var objectFieldRect = new Rect(position.x, position.y, position.width - 60, position.height);
            var buttonRect = new Rect(position.x + position.width - 55, position.y, 55, position.height);

            // Draw object field
            EditorGUI.PropertyField(objectFieldRect, property, label);

            // Draw "Edit" button
            var cadanceAsset = property.objectReferenceValue as CadanceAsset;
            GUI.enabled = cadanceAsset != null;

            if (GUI.Button(buttonRect, "Edit"))
            {
                CadanceEditorWindow.OpenCadance(cadanceAsset);
            }

            GUI.enabled = true;

            EditorGUI.EndProperty();
        }
    }

    /// <summary>
    /// Custom property drawer for CadanceTrackBase, equivalent to Koreographer's track property drawers.
    /// </summary>
    [CustomPropertyDrawer(typeof(CadanceTrackBase))]
    public class CadanceTrackPropertyDrawer : PropertyDrawer
    {
        public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
        {
            EditorGUI.BeginProperty(position, label, property);

            // Calculate rects
            var objectFieldRect = new Rect(position.x, position.y, position.width - 60, position.height);
            var buttonRect = new Rect(position.x + position.width - 55, position.y, 55, position.height);

            // Draw object field
            EditorGUI.PropertyField(objectFieldRect, property, label);

            // Draw "Edit" button
            var track = property.objectReferenceValue as CadanceTrackBase;
            GUI.enabled = track != null;

            if (GUI.Button(buttonRect, "Edit"))
            {
                // Find the parent CadanceAsset
                var cadanceAsset = FindParentCadanceAsset(track);
                if (cadanceAsset != null)
                {
                    CadanceEditorWindow.OpenCadance(cadanceAsset, track);
                }
                else
                {
                    EditorUtility.DisplayDialog("Cannot Edit Track",
                        "This track is not part of a CadanceAsset. Please assign it to a CadanceAsset first.",
                        "OK");
                }
            }

            GUI.enabled = true;

            EditorGUI.EndProperty();
        }

        private CadanceAsset FindParentCadanceAsset(CadanceTrackBase track)
        {
            if (track == null) return null;

            // Search all CadanceAssets for one that contains this track
            string[] cadanceGuids = AssetDatabase.FindAssets("t:CadanceAsset");

            foreach (string guid in cadanceGuids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                var cadanceAsset = AssetDatabase.LoadAssetAtPath<CadanceAsset>(path);

                if (cadanceAsset != null && cadanceAsset.Tracks.Contains(track))
                {
                    return cadanceAsset;
                }
            }

            return null;
        }
    }



    /// <summary>
    /// Custom inspector for CadanceAsset that provides enhanced editing capabilities.
    /// </summary>
    [CustomEditor(typeof(CadanceAsset))]
    public class CadanceAssetInspector : UnityEditor.Editor
    {
        public override void OnInspectorGUI()
        {
            var cadanceAsset = target as CadanceAsset;

            // Header
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Cadance Asset", EditorStyles.boldLabel);
            EditorGUILayout.HelpBox("This asset contains musical event data and timing information. " +
                                   "It is equivalent to Koreographer's Koreography asset.",
                                   MessageType.Info);

            EditorGUILayout.Space();

            // Quick actions
            EditorGUILayout.BeginHorizontal();

            if (GUILayout.Button("Open in Cadance Editor"))
            {
                CadanceEditorWindow.OpenCadance(cadanceAsset);
            }

            if (GUILayout.Button("Validate Audio"))
            {
                if (cadanceAsset.SourceClip != null)
                {
                    CadanceAudioValidator.CheckAudioClipValidity(cadanceAsset.SourceClip, true);
                }
                else
                {
                    EditorUtility.DisplayDialog("No AudioClip",
                        "Please assign an AudioClip to this CadanceAsset before validation.",
                        "OK");
                }
            }

            EditorGUILayout.EndHorizontal();

            EditorGUILayout.Space();

            // Default inspector
            DrawDefaultInspector();

            // Additional information
            if (cadanceAsset.Tracks.Count > 0)
            {
                EditorGUILayout.Space();
                EditorGUILayout.LabelField("Track Summary", EditorStyles.boldLabel);

                foreach (var track in cadanceAsset.Tracks)
                {
                    if (track != null)
                    {
                        EditorGUILayout.BeginHorizontal();
                        EditorGUILayout.LabelField($"• {track.EventID}", GUILayout.Width(150));
                        EditorGUILayout.LabelField($"{track.EventCount} events", EditorStyles.miniLabel);

                        if (GUILayout.Button("Edit", GUILayout.Width(40)))
                        {
                            CadanceEditorWindow.OpenCadance(cadanceAsset, track);
                        }

                        EditorGUILayout.EndHorizontal();
                    }
                }
            }
        }
    }

    /// <summary>
    /// Custom inspector for CadanceTrackBase that provides enhanced editing capabilities.
    /// </summary>
    [CustomEditor(typeof(CadanceTrackBase), true)]
    public class CadanceTrackInspector : UnityEditor.Editor
    {
        public override void OnInspectorGUI()
        {
            var track = target as CadanceTrackBase;

            // Header
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Cadance Track", EditorStyles.boldLabel);
            EditorGUILayout.HelpBox("This track contains musical events with timing information. " +
                                   "It is equivalent to Koreographer's KoreographyTrack.",
                                   MessageType.Info);

            EditorGUILayout.Space();

            // Quick actions
            if (GUILayout.Button("Open in Cadance Editor"))
            {
                // Find parent CadanceAsset
                var cadanceAsset = FindParentCadanceAsset(track);
                if (cadanceAsset != null)
                {
                    CadanceEditorWindow.OpenCadance(cadanceAsset, track);
                }
                else
                {
                    EditorUtility.DisplayDialog("Cannot Open Track",
                        "This track is not part of a CadanceAsset. Please assign it to a CadanceAsset first.",
                        "OK");
                }
            }

            EditorGUILayout.Space();

            // Default inspector
            DrawDefaultInspector();

            // Track statistics
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Track Statistics", EditorStyles.boldLabel);
            EditorGUILayout.LabelField($"Event Count: {track.EventCount}");
            EditorGUILayout.LabelField($"Event ID: {track.EventID}");
        }

        private CadanceAsset FindParentCadanceAsset(CadanceTrackBase track)
        {
            if (track == null) return null;

            string[] cadanceGuids = AssetDatabase.FindAssets("t:CadanceAsset");

            foreach (string guid in cadanceGuids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                var cadanceAsset = AssetDatabase.LoadAssetAtPath<CadanceAsset>(path);

                if (cadanceAsset != null && cadanceAsset.Tracks.Contains(track))
                {
                    return cadanceAsset;
                }
            }

            return null;
        }
    }
}
