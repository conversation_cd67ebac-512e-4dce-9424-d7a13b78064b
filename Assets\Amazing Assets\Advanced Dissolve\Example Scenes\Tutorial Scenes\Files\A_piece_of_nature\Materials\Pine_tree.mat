%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Pine_tree
  m_Shader: {fileID: 4800000, guid: dcda956a6808f8c4e84b1460ad3ac6eb, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords:
  - _AD_CUTOUT_GEOMETRIC_TYPE_PLANE
  - _AD_CUTOUT_STANDARD_SOURCE_CUSTOM_MAP
  - _AD_EDGE_BASE_SOURCE_ALL
  - _AD_STATE_ENABLED
  - _ALPHATEST_ON
  - _NORMALMAP
  m_InvalidKeywords:
  - _AD_CUTOUT_GEOMETRIC_COUNT_ONE
  - _AD_CUTOUT_STANDARD_SOURCE_MAPS_MAPPING_TYPE_DEFAULT
  - _AD_EDGE_ADDITIONAL_COLOR_NONE
  - _AD_EDGE_UV_DISTORTION_SOURCE_DEFAULT
  - _AD_GLOBAL_CONTROL_ID_NONE
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 1
  m_CustomRenderQueue: 2450
  stringTagMap:
    RenderType: TransparentCutout
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _AdvancedDissolveCutoutMap1:
        m_Texture: {fileID: 2800000, guid: fd1dc4bfebdc60c4dba7febfdd078109, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _AdvancedDissolveCutoutMap2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _AdvancedDissolveCutoutMap3:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _AdvancedDissolveCutoutSimpleMap1:
        m_Texture: {fileID: 2800000, guid: 15156bfb2d3680b42b640ccf7d864400, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _AdvancedDissolveCutoutSimpleMap2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _AdvancedDissolveCutoutSimpleMap3:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _AdvancedDissolveCutoutStandardMap1:
        m_Texture: {fileID: 2800000, guid: 15156bfb2d3680b42b640ccf7d864400, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _AdvancedDissolveCutoutStandardMap2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _AdvancedDissolveCutoutStandardMap3:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _AdvancedDissolveEdgeAdditionalColorMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _AdvancedDissolveEdgeMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _AdvancedDissolveEdgeUVDistortionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BaseMap:
        m_Texture: {fileID: 2800000, guid: 5d94dd1e58b739a4793916b2029d252d, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 2800000, guid: a1715eded4b51e94389dff8deae63d21, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DissolveEdgeTexture:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DissolveMap1:
        m_Texture: {fileID: 2800000, guid: 86f4a7aa7baef1b4aa0b9ab725119b32, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DissolveMap2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DissolveMap3:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _LightLookupTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: 5d94dd1e58b739a4793916b2029d252d, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ReflectionCubeMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SecondaryNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SecondaryTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecGlossMap:
        m_Texture: {fileID: 2800000, guid: 53d342d54f861b44abe2ea87d3a4db09, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AdvancedDissolveCutoutBaseInvert: 0
    - _AdvancedDissolveCutoutClip: 0
    - _AdvancedDissolveCutoutGeometric1Height: 1
    - _AdvancedDissolveCutoutGeometric1Radius: 1
    - _AdvancedDissolveCutoutGeometric2Height: 1
    - _AdvancedDissolveCutoutGeometric2Radius: 1
    - _AdvancedDissolveCutoutGeometric3Height: 1
    - _AdvancedDissolveCutoutGeometric3Radius: 1
    - _AdvancedDissolveCutoutGeometric4Height: 1
    - _AdvancedDissolveCutoutGeometric4Radius: 1
    - _AdvancedDissolveCutoutGeometricInvert: 0
    - _AdvancedDissolveCutoutGeometricNoise: 0.1
    - _AdvancedDissolveCutoutGeometricXYZAxis: 0
    - _AdvancedDissolveCutoutGeometricXYZRollout: 0
    - _AdvancedDissolveCutoutGeometricXYZSpace: 0
    - _AdvancedDissolveCutoutGeometricXYZStyle: 0
    - _AdvancedDissolveCutoutMap1Channel: 3
    - _AdvancedDissolveCutoutMap1Intensity: 1
    - _AdvancedDissolveCutoutMap1Invert: 0
    - _AdvancedDissolveCutoutMap2Channel: 3
    - _AdvancedDissolveCutoutMap2Intensity: 1
    - _AdvancedDissolveCutoutMap2Invert: 0
    - _AdvancedDissolveCutoutMap3Channel: 3
    - _AdvancedDissolveCutoutMap3Intensity: 1
    - _AdvancedDissolveCutoutMap3Invert: 0
    - _AdvancedDissolveCutoutMapsBlendType: 0
    - _AdvancedDissolveCutoutParametric1Height: 1
    - _AdvancedDissolveCutoutParametric1Radius: 1
    - _AdvancedDissolveCutoutParametric2Height: 1
    - _AdvancedDissolveCutoutParametric2Radius: 1
    - _AdvancedDissolveCutoutParametric3Height: 1
    - _AdvancedDissolveCutoutParametric3Radius: 1
    - _AdvancedDissolveCutoutParametric4Height: 1
    - _AdvancedDissolveCutoutParametric4Radius: 1
    - _AdvancedDissolveCutoutParametricInvert: 0
    - _AdvancedDissolveCutoutParametricNoise: 0.05
    - _AdvancedDissolveCutoutParametricXYZAxis: 0
    - _AdvancedDissolveCutoutParametricXYZRollout: 0
    - _AdvancedDissolveCutoutParametricXYZSpace: 0
    - _AdvancedDissolveCutoutParametricXYZType: 0
    - _AdvancedDissolveCutoutScreenSpaceUVScale: 0
    - _AdvancedDissolveCutoutSimpleBaseInvert: 0
    - _AdvancedDissolveCutoutSimpleClip: 0
    - _AdvancedDissolveCutoutSimpleMap1Channel: 3
    - _AdvancedDissolveCutoutSimpleMap1Intensity: 1
    - _AdvancedDissolveCutoutSimpleMap1Invert: 0
    - _AdvancedDissolveCutoutSimpleMap2Channel: 3
    - _AdvancedDissolveCutoutSimpleMap2Intensity: 1
    - _AdvancedDissolveCutoutSimpleMap2Invert: 0
    - _AdvancedDissolveCutoutSimpleMap3Channel: 3
    - _AdvancedDissolveCutoutSimpleMap3Intensity: 1
    - _AdvancedDissolveCutoutSimpleMap3Invert: 0
    - _AdvancedDissolveCutoutSimpleMapsBlendType: 0
    - _AdvancedDissolveCutoutSimpleMapsScreenSpaceUVScale: 0
    - _AdvancedDissolveCutoutSimpleMapsTriplanarMappingSpace: 0
    - _AdvancedDissolveCutoutStandardBaseInvert: 0
    - _AdvancedDissolveCutoutStandardClip: 0
    - _AdvancedDissolveCutoutStandardMap1Channel: 3
    - _AdvancedDissolveCutoutStandardMap1Intensity: 1
    - _AdvancedDissolveCutoutStandardMap1Invert: 0
    - _AdvancedDissolveCutoutStandardMap2Channel: 3
    - _AdvancedDissolveCutoutStandardMap2Intensity: 1
    - _AdvancedDissolveCutoutStandardMap2Invert: 0
    - _AdvancedDissolveCutoutStandardMap3Channel: 3
    - _AdvancedDissolveCutoutStandardMap3Intensity: 1
    - _AdvancedDissolveCutoutStandardMap3Invert: 0
    - _AdvancedDissolveCutoutStandardMapsBlendType: 0
    - _AdvancedDissolveCutoutStandardMapsScreenSpaceUVScale: 0
    - _AdvancedDissolveCutoutStandardMapsTriplanarMappingSpace: 0
    - _AdvancedDissolveCutoutTriplanarMappingSpace: 0
    - _AdvancedDissolveDynamicMask2Height: 1
    - _AdvancedDissolveDynamicMask2Radius: 1
    - _AdvancedDissolveDynamicMask3Height: 1
    - _AdvancedDissolveDynamicMask3Radius: 1
    - _AdvancedDissolveDynamicMask4Height: 1
    - _AdvancedDissolveDynamicMask4Radius: 1
    - _AdvancedDissolveDynamicMaskAxis: 0
    - _AdvancedDissolveDynamicMaskAxisPosition: 0
    - _AdvancedDissolveDynamicMaskAxisRollout: 0
    - _AdvancedDissolveDynamicMaskAxisSpace: 0
    - _AdvancedDissolveDynamicMaskAxisType: 0
    - _AdvancedDissolveDynamicMaskHeight: 1
    - _AdvancedDissolveDynamicMaskInvert: 0
    - _AdvancedDissolveDynamicMaskNoise: 0
    - _AdvancedDissolveDynamicMaskRadius: 1
    - _AdvancedDissolveEdgeAdditionalColorAlphaOffset: 0
    - _AdvancedDissolveEdgeAdditionalColorClipInterpolation: 0
    - _AdvancedDissolveEdgeAdditionalColorMapMipmap: 1
    - _AdvancedDissolveEdgeAdditionalColorMapReverse: 0
    - _AdvancedDissolveEdgeAdditionalColorPhaseOffset: 0
    - _AdvancedDissolveEdgeAdditionalColorTransparency: 1
    - _AdvancedDissolveEdgeAlphaOffset: 0
    - _AdvancedDissolveEdgeBaseColorTransparency: 1
    - _AdvancedDissolveEdgeBaseShape: 0
    - _AdvancedDissolveEdgeBaseWidthGeometric: 0.1
    - _AdvancedDissolveEdgeBaseWidthParametric: 0.1
    - _AdvancedDissolveEdgeBaseWidthSimple: 0.1
    - _AdvancedDissolveEdgeBaseWidthStandard: 0.1
    - _AdvancedDissolveEdgeColor2Transparency: 1
    - _AdvancedDissolveEdgeColorTransparency: 1
    - _AdvancedDissolveEdgeGIMetaPassMultiplier: 1
    - _AdvancedDissolveEdgeIsDynamic: 0
    - _AdvancedDissolveEdgeMapMipmap: 1
    - _AdvancedDissolveEdgeMapReverse: 0
    - _AdvancedDissolveEdgePhaseOffset: 0
    - _AdvancedDissolveEdgeShape: 0
    - _AdvancedDissolveEdgeUVDistortionStrength: 0
    - _AdvancedDissolveEdgeWidth: 0.1
    - _AdvancedDissolveKeywordCutoutGeometricCount: 0
    - _AdvancedDissolveKeywordCutoutGeometricType: 2
    - _AdvancedDissolveKeywordCutoutMappingType: 0
    - _AdvancedDissolveKeywordCutoutParametricCount: 0
    - _AdvancedDissolveKeywordCutoutParametricType: 2
    - _AdvancedDissolveKeywordCutoutSimpleMapsMappingType: 0
    - _AdvancedDissolveKeywordCutoutSimpleSource: 2
    - _AdvancedDissolveKeywordCutoutSource: 1
    - _AdvancedDissolveKeywordCutoutStandardSource: 2
    - _AdvancedDissolveKeywordCutoutStandardSourceMapsMappingType: 0
    - _AdvancedDissolveKeywordDynamicMaskCount: 0
    - _AdvancedDissolveKeywordDynamicMaskType: 2
    - _AdvancedDissolveKeywordEdgeAdditionalColorSource: 0
    - _AdvancedDissolveKeywordEdgeBaseSource: 3
    - _AdvancedDissolveKeywordEdgeBlendColor: 2
    - _AdvancedDissolveKeywordEdgeUVDistortionSource: 0
    - _AdvancedDissolveKeywordGlobalControlID: 0
    - _AdvancedDissolveKeywordState: 1
    - _AdvancedDissolveMetaPassMultiplier: 1
    - _AlphaClip: 1
    - _AlphaToMask: 1
    - _AmbientLights: 1
    - _Blend: 0
    - _BlendModePreserveSpecular: 1
    - _BumpScale: 1
    - _ClearCoatMask: 0
    - _ClearCoatSmoothness: 0
    - _Cull: 0
    - _Cutoff: 0.5
    - _DetailAlbedoMapScale: 1
    - _DetailNormalMapScale: 1
    - _DissolveAlphaSource: 1
    - _DissolveAlphaSourceTexturesUVSet: 0
    - _DissolveCutoff: 0.25
    - _DissolveEdgeColorIntensity: 5
    - _DissolveEdgeDistortionSource: 0
    - _DissolveEdgeDistortionStrength: 0
    - _DissolveEdgeShape: 0
    - _DissolveEdgeTextureAlphaOffset: 0
    - _DissolveEdgeTextureIsDynamic: 0
    - _DissolveEdgeTextureMipmap: 1
    - _DissolveEdgeTexturePhaseOffset: 0
    - _DissolveEdgeTextureReverse: 0
    - _DissolveEdgeTextureSource: 0
    - _DissolveEdgeWidth: 0.05
    - _DissolveGIMultiplier: 1
    - _DissolveGlobalControl: 0
    - _DissolveMainMapTiling: 1
    - _DissolveMap1Channel: 3
    - _DissolveMap1Intensity: 1
    - _DissolveMap2Channel: 3
    - _DissolveMap2Intensity: 1
    - _DissolveMap3Channel: 3
    - _DissolveMap3Intensity: 1
    - _DissolveMappingType: 0
    - _DissolveMask: 2
    - _DissolveMaskAxis: 0
    - _DissolveMaskCount: 0
    - _DissolveMaskInvert: -1
    - _DissolveMaskOffset: 0
    - _DissolveMaskRadius: 4.0478525
    - _DissolveMaskSpace: 0
    - _DissolveNoiseStrength: 0.3
    - _DissolveSourceAlphaTexturesBlend: 0
    - _DissolveTriplanarMappingSpace: 0
    - _DstBlend: 0
    - _DstBlendAlpha: 0
    - _EmissionScaleUI: 0
    - _EnvironmentReflections: 1
    - _GlossMapScale: 1
    - _Glossiness: 0
    - _GlossyReflections: 1
    - _IncludeVertexColor: 0
    - _LightLookupOffset: 0
    - _Metallic: 0
    - _Mode: 1
    - _NormalMapStrength: 1
    - _OcclusionStrength: 1
    - _Parallax: 0.02
    - _PerVertexLights: 1
    - _QueueOffset: 0
    - _ReceiveShadows: 1
    - _ReflectionFresnelBias: 0
    - _ReflectionMaskOffset: 0
    - _RimBias: 0
    - _SecondaryTex_Blend: 0
    - _Shininess: 0.7
    - _Smoothness: 0
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SpecularMaskOffset: 0
    - _SrcBlend: 1
    - _SrcBlendAlpha: 1
    - _Surface: 0
    - _TextureMix: 0
    - _UVSec: 0
    - _WorkflowMode: 1
    - _ZWrite: 1
    m_Colors:
    - _AdvancedDissolveBakedKeywords: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutGeometric1Normal: {r: -0.8456284, g: -0.00000011920929, b: -0.5337723, a: 0}
    - _AdvancedDissolveCutoutGeometric1Position: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutGeometric2Normal: {r: 1, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutGeometric2Position: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutGeometric3Normal: {r: 1, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutGeometric3Position: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutGeometric4Normal: {r: 1, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutGeometric4Position: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutGeometricXYZPosition: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutMap1_Offset: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutMap1_Scroll: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutMap1_Tiling: {r: 1, g: 1, b: 1, a: 0}
    - _AdvancedDissolveCutoutMap2_Offset: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutMap2_Scroll: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutMap2_Tiling: {r: 1, g: 1, b: 1, a: 0}
    - _AdvancedDissolveCutoutMap3_Offset: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutMap3_Scroll: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutMap3_Tiling: {r: 1, g: 1, b: 1, a: 0}
    - _AdvancedDissolveCutoutParametric1Normal: {r: -1.0000001, g: -0.00000011920929, b: 0, a: 0}
    - _AdvancedDissolveCutoutParametric1Position: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutParametric2Normal: {r: 1, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutParametric2Position: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutParametric3Normal: {r: 1, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutParametric3Position: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutParametric4Normal: {r: 1, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutParametric4Position: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutParametricXYZPosition: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutSimpleMap1Offset: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutSimpleMap1Scroll: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutSimpleMap1Tiling: {r: 1, g: 1, b: 1, a: 0}
    - _AdvancedDissolveCutoutSimpleMap2Offset: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutSimpleMap2Scroll: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutSimpleMap2Tiling: {r: 1, g: 1, b: 1, a: 0}
    - _AdvancedDissolveCutoutSimpleMap3Offset: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutSimpleMap3Scroll: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutSimpleMap3Tiling: {r: 1, g: 1, b: 1, a: 0}
    - _AdvancedDissolveCutoutStandardMap1Offset: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutStandardMap1Scroll: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutStandardMap1Tiling: {r: 1, g: 1, b: 1, a: 0}
    - _AdvancedDissolveCutoutStandardMap2Offset: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutStandardMap2Scroll: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutStandardMap2Tiling: {r: 1, g: 1, b: 1, a: 0}
    - _AdvancedDissolveCutoutStandardMap3Offset: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutStandardMap3Scroll: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutStandardMap3Tiling: {r: 1, g: 1, b: 1, a: 0}
    - _AdvancedDissolveDynamicMask2Normal: {r: 1, g: 0, b: 0, a: 0}
    - _AdvancedDissolveDynamicMask2Position: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveDynamicMask3Normal: {r: 1, g: 0, b: 0, a: 0}
    - _AdvancedDissolveDynamicMask3Position: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveDynamicMask4Normal: {r: 1, g: 0, b: 0, a: 0}
    - _AdvancedDissolveDynamicMask4Position: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveDynamicMaskNormal: {r: -0.9848078, g: -0.000000070780516, b: -0.17364845, a: 0}
    - _AdvancedDissolveDynamicMaskPosition: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveEdgeAdditionalColor: {r: 1, g: 0, b: 0, a: 1}
    - _AdvancedDissolveEdgeAdditionalColorIntensity: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveEdgeAdditionalColorMapOffset: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveEdgeAdditionalColorMapScroll: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveEdgeAdditionalColorMapTiling: {r: 1, g: 1, b: 0, a: 0}
    - _AdvancedDissolveEdgeBaseColor: {r: 0.0060488326, g: 0.21586053, b: 0.8796226, a: 1}
    - _AdvancedDissolveEdgeBaseColorIntensity: {r: 3.5, g: 32.11545, b: 0, a: 0}
    - _AdvancedDissolveEdgeColor: {r: 0.006119758, g: 0.2152672, b: 0.87603444, a: 1}
    - _AdvancedDissolveEdgeColor2: {r: 1, g: 0, b: 0, a: 1}
    - _AdvancedDissolveEdgeColor2Intensity: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveEdgeColorIntensity: {r: 3.3, g: 26.112638, b: 0, a: 0}
    - _AdvancedDissolveEdgeMap_Offset: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveEdgeMap_Scroll: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveEdgeMap_Tiling: {r: 1, g: 1, b: 0, a: 0}
    - _AdvancedDissolveEdgeUVDistortionMapOffset: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveEdgeUVDistortionMapScroll: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveEdgeUVDistortionMapTiling: {r: 1, g: 1, b: 0, a: 0}
    - _AdvancedDissolveMetaPassObjectPosition: {r: 0, g: 0, b: 0, a: 0}
    - _BaseColor: {r: 1, g: 1, b: 1, a: 1}
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _DissolveEdgeColor: {r: 0, g: 1, b: 0, a: 1}
    - _DissolveMap1_Scroll: {r: 0, g: 0, b: 0, a: 0}
    - _DissolveMap2_Scroll: {r: 0, g: 0, b: 0, a: 0}
    - _DissolveMap3_Scroll: {r: 0, g: 0, b: 0, a: 0}
    - _DissolveMaskNormal: {r: 1.0000001, g: -0.00000011921031, b: 0.0000020230486, a: 0}
    - _DissolveMaskPosition: {r: 0, g: 0, b: 0, a: 0}
    - _Dissolve_ObjectWorldPos: {r: 0, g: 0, b: 0, a: 0}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 0}
    - _EmissionColorUI: {r: 1, g: 1, b: 1, a: 1}
    - _EmissionMap_Scroll: {r: 0, g: 0, b: 0, a: 0}
    - _MainTex_Scroll: {r: 0, g: 0, b: 0, a: 0}
    - _NormalMap_Scroll: {r: 0, g: 0, b: 0, a: 0}
    - _ReflectionColor: {r: 1, g: 1, b: 1, a: 1}
    - _RimColor: {r: 1, g: 1, b: 1, a: 1}
    - _SecondaryNormalMap_Scroll: {r: 0, g: 0, b: 0, a: 0}
    - _SecondaryTex_Scroll: {r: 0, g: 0, b: 0, a: 0}
    - _SpecColor: {r: 0.2, g: 0.2, b: 0.2, a: 1}
    - _SpecularColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_BuildTextureStacks: []
