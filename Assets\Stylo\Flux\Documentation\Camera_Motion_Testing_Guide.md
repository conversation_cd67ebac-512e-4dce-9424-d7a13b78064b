# Camera Motion Testing Guide

## Quick Test Setup

### 1. Open Demo Scene
1. Open `Assets/Stylo/Flux/Demo/Demo_URP.unity`
2. Enter Play Mode
3. Use WASD + Mouse to move the camera around

### 2. Configure Camera Motion Settings
In the Volume component (or Volume Profile), find the **Flux Effect** and expand **Camera Motion Sensitivity**:

**Recommended Test Settings:**
```
Camera Motion Amplification: 5.0
Camera Motion Threshold: 0.001
Camera Motion Influence: 3.0
Camera Motion Smoothing: 0.1
```

### 3. Test Camera Motion Response
1. **Slow camera movement**: Should see subtle datamosh effects
2. **Fast camera movement**: Should see dramatic datamosh corruption
3. **Camera rotation**: Should trigger motion-based artifacts
4. **Stationary camera**: Should show minimal motion-based corruption

## Validation Checklist

### ✅ Basic Functionality
- [ ] Camera motion triggers visible datamosh effects
- [ ] Faster camera movement creates more corruption
- [ ] Stationary camera shows less motion-based artifacts
- [ ] Effect responds to both translation and rotation

### ✅ Parameter Responsiveness  
- [ ] **Amplification 0**: No camera motion response
- [ ] **Amplification 10**: Very dramatic camera motion effects
- [ ] **Threshold 0.001**: Sensitive to small movements
- [ ] **Threshold 0.01**: Only responds to larger movements
- [ ] **Influence 0**: Camera motion doesn't affect corruption chance
- [ ] **Influence 5**: Camera motion strongly affects corruption
- [ ] **Smoothing 0**: Immediate, potentially jittery response
- [ ] **Smoothing 0.8**: Smooth, delayed response

### ✅ Integration with Existing Features
- [ ] Works with different **Block Sizes** (2x2, 4x4, 8x8, 16x16, 32x32)
- [ ] Works with **Base Noise** settings
- [ ] Works with **Length Influence** settings  
- [ ] Works with **Advanced Datamoshing** features
- [ ] Works with **Enhanced Corruption** features
- [ ] Compatible with **Visualize Motion Vectors** mode

## Debugging Motion Issues

### Enable Motion Vector Visualization
1. Set **Visualize Motion Vectors** to `true`
2. Camera movement should show colored motion vectors
3. Red/Green colors indicate X/Y motion components
4. No colors = no motion vectors being generated

### Check Motion Vector Generation
If motion vectors aren't visible:
1. Ensure objects in scene use shaders that write motion vectors
2. Check that camera has **Motion Vectors** enabled in rendering settings
3. Verify URP Renderer has **Motion Vectors** enabled
4. Make sure scene has moving objects or camera movement

### Common Issues and Solutions

**Issue**: No camera motion response
- **Solution**: Check Camera Motion Amplification > 0
- **Solution**: Verify Base Noise or Length Influence > 0 (required for reprojection)
- **Solution**: Ensure motion vectors are being generated

**Issue**: Effect too subtle
- **Solution**: Increase Camera Motion Amplification (5.0+)
- **Solution**: Increase Camera Motion Influence (3.0+)
- **Solution**: Decrease Camera Motion Threshold (0.001)

**Issue**: Effect too jittery
- **Solution**: Increase Camera Motion Smoothing (0.3+)
- **Solution**: Reduce Camera Motion Amplification
- **Solution**: Increase Camera Motion Threshold slightly

**Issue**: Effect not smooth
- **Solution**: Adjust Camera Motion Smoothing (0.2 - 0.5)
- **Solution**: Check frame rate (low FPS can cause jittery motion)

## Performance Testing

### Frame Rate Impact
Test with different settings to ensure acceptable performance:
- **Low impact**: Amplification 1-3, Influence 1-2
- **Medium impact**: Amplification 3-6, Influence 2-4  
- **High impact**: Amplification 6-10, Influence 4-5

### Resolution Scaling
The enhanced system now properly scales with resolution:
- Test at different resolutions (1080p, 1440p, 4K)
- Motion sensitivity should remain consistent across resolutions
- No hardcoded 1920x1080 artifacts

## Expected Behavior

### Correct Camera Motion Response
- **Gentle camera panning**: Subtle trailing/smearing effects
- **Fast camera movement**: Strong corruption and artifacts
- **Camera rotation**: Motion-based distortion around rotation center
- **Zoom in/out**: Radial motion effects from screen center
- **Stationary camera**: Minimal motion-based corruption (only from Base Noise)

### Visual Characteristics
- **Motion trails**: Previous frame data follows camera movement
- **Block corruption**: Motion triggers block-based artifacts
- **Temporal smearing**: Fast motion creates temporal ghosting
- **Adaptive intensity**: Effect strength matches motion speed
- **Smooth transitions**: No sudden jumps or discontinuities (with proper smoothing)

## Comparison with Reference

The enhanced implementation should now behave similarly to:
- Traditional datamosh effects where camera motion creates trailing
- Video compression artifacts during fast camera movement
- Reference implementations from the blog post and Asset Store package
- Previous non-Render Graph Flux versions (but with better performance)

## Success Criteria

✅ **Camera motion visibly affects the datamosh effect**
✅ **Motion sensitivity is adjustable via Volume parameters**  
✅ **Effect scales properly with screen resolution**
✅ **Integration works with existing Flux features**
✅ **Performance remains acceptable**
✅ **No compilation errors or runtime issues**
