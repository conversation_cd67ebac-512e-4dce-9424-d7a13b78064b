%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: Mask (Sphere)
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor\n// Available at the Unity
    Asset Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=18921\n701.6727;90.76363;1618.036;752.2363;597.6756;498.087;1;True;True\nNode;AmplifyShaderEditor.FunctionInput;7;34.84253,71.47659;Inherit;False;Edge
    Smooth;1;0;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;8;31.84253,172.4766;Inherit;False;Invert;1;1;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WorldPosInputsNode;6;20.84253,-106.5234;Inherit;False;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.CustomExpressionNode;3;360,-103.5;Inherit;False;return
    WireframeShaderMaskSphere(SpherePosition, SphereRadius,  vertexPositionWS, edgeFalloff,
    invert)@;1;Create;5;True;SpherePosition;FLOAT3;0,0,0;In;;Inherit;False;True;SphereRadius;FLOAT;0;In;;Inherit;False;False;vertexPositionWS;FLOAT3;0,0,0;In;;Inherit;False;False;edgeFalloff;FLOAT;0;In;;Inherit;False;False;invert;FLOAT;0;In;Enum(Off,0,On,1);Inherit;False;AAWireframeShaderMaskSphere;False;False;0;;False;5;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;2;FLOAT3;0,0,0;False;3;FLOAT;0;False;4;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;10;34.34253,-233.0234;Inherit;False;Property;_WireframeShaderMaskSphereRadius;Sphere
    Radius;1;1;[HideInInspector];Create;False;0;0;0;False;0;False;0;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.Vector3Node;9;18.3425,-401.0234;Inherit;False;Property;_WireframeShaderMaskSpherePosition;Sphere
    Position;0;1;[HideInInspector];Create;False;0;0;0;False;0;False;0,0,0;0,0,0;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.FunctionOutput;0;680,-102;Inherit;False;True;-1;Mask
    Value;0;False;1;0;FLOAT;0;False;1;FLOAT;0\nWireConnection;3;0;9;0\nWireConnection;3;1;10;0\nWireConnection;3;2;6;0\nWireConnection;3;3;7;0\nWireConnection;3;4;8;0\nWireConnection;0;0;3;0\nASEEND*/\n//CHKSM=C1A8DD3EB4D587456BE33B6BFCDBF252206BC34F"
  m_functionName: 
  m_description: 
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 1
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_customEdited: 0
    m_additionalDirectives:
    - {fileID: 0}
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems:
    - LineType: 0
      LineValue: 
      GUIDToggle: 1
      GUIDValue: 36072c560231e604cb8f0c03c7ff456d
      Origin: 2
  m_nodeCategory: 0
  m_customNodeCategory: Amazing Assets
  m_previewPosition: 0
  m_hidden: 1
  m_url: 
