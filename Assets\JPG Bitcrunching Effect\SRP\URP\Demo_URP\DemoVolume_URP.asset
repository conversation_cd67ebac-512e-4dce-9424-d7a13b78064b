%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d7fd9488000d3734a9e00ee676215985, type: 3}
  m_Name: DemoVolume_URP
  m_EditorClassIdentifier: 
  components:
  - {fileID: 277022162085034876}
--- !u!114 &277022162085034876
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d468b943de483f641a8eb80fcd52e584, type: 3}
  m_Name: JPG
  m_EditorClassIdentifier: 
  active: 1
  EffectIntensity:
    m_OverrideState: 1
    m_Value: 1
  OnlyStenciled:
    m_OverrideState: 1
    m_Value: 1
  ColorCrunch:
    m_OverrideState: 1
    m_Value: 0.528
  Downscaling:
    m_OverrideState: 1
    m_Value: 7
  BlockSize:
    m_OverrideState: 1
    m_Value: 0
  Oversharpening:
    m_OverrideState: 1
    m_Value: 0.738
  DontCrunchSkybox:
    m_OverrideState: 1
    m_Value: 0
  ReprojectBaseNoise:
    m_OverrideState: 1
    m_Value: 0
  ReprojectBaseRerollSpeed:
    m_OverrideState: 1
    m_Value: 3
  ReprojectLengthInfluence:
    m_OverrideState: 1
    m_Value: 0
  VisualizeMotionVectors:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &1024402090557769300
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 97c23e3b12dc18c42a140437e53d3951, type: 3}
  m_Name: Tonemapping
  m_EditorClassIdentifier: 
  active: 0
  mode:
    m_OverrideState: 1
    m_Value: 2
--- !u!114 &5633232736217173614
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0b2db86121404754db890f4c8dfe81b2, type: 3}
  m_Name: Bloom
  m_EditorClassIdentifier: 
  active: 0
  threshold:
    m_OverrideState: 1
    m_Value: 0.19
  intensity:
    m_OverrideState: 1
    m_Value: 1
  scatter:
    m_OverrideState: 0
    m_Value: 0.7
  clamp:
    m_OverrideState: 0
    m_Value: 65472
  tint:
    m_OverrideState: 0
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  highQualityFiltering:
    m_OverrideState: 0
    m_Value: 0
  skipIterations:
    m_OverrideState: 0
    m_Value: 1
  dirtTexture:
    m_OverrideState: 0
    m_Value: {fileID: 0}
  dirtIntensity:
    m_OverrideState: 0
    m_Value: 0
--- !u!114 &7678543493164762238
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 899c54efeace73346a0a16faa3afe726, type: 3}
  m_Name: Vignette
  m_EditorClassIdentifier: 
  active: 0
  color:
    m_OverrideState: 0
    m_Value: {r: 0, g: 0, b: 0, a: 1}
  center:
    m_OverrideState: 0
    m_Value: {x: 0.5, y: 0.5}
  intensity:
    m_OverrideState: 1
    m_Value: 0.48
  smoothness:
    m_OverrideState: 0
    m_Value: 0.2
  rounded:
    m_OverrideState: 0
    m_Value: 0
