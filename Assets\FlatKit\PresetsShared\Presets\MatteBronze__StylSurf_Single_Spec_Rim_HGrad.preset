%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!181963792 &2655988077585873504
Preset:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: MatteBronze__StylSurf_Single_Spec_Rim_HGrad
  m_TargetType:
    m_NativeTypeID: 21
    m_ManagedTypePPtr: {fileID: 0}
    m_ManagedTypeFallback: 
  m_Properties:
  - target: {fileID: 0}
    propertyPath: m_Shader
    value: 
    objectReference: {fileID: 4800000, guid: bee44b4a58655ee4cbff107302a3e131, type: 3}
  - target: {fileID: 0}
    propertyPath: m_ShaderKeywords
    value: DR_GRADIENT_ON DR_RIM_ON DR_SPECULAR_ON _CELPRIMARYMODE_SINGLE _FLAT_RIM_ENABLED
      _FLAT_SHADOWS_ENABLED _FLAT_SPECULAR_ENABLED
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LightmapFlags
    value: 4
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_EnableInstancingVariants
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_DoubleSidedGI
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_CustomRenderQueue
    value: -1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: stringTagMap.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: disabledShaderPasses.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.size
    value: 11
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[0].first
    value: _BumpMap
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[0].second.m_Texture
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[0].second.m_Scale.x
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[0].second.m_Scale.y
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[0].second.m_Offset.x
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[0].second.m_Offset.y
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[1].first
    value: _CelCurveTexture
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[1].second.m_Texture
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[1].second.m_Scale.x
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[1].second.m_Scale.y
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[1].second.m_Offset.x
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[1].second.m_Offset.y
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[2].first
    value: _CelStepTexture
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[2].second.m_Texture
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[2].second.m_Scale.x
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[2].second.m_Scale.y
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[2].second.m_Offset.x
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[2].second.m_Offset.y
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[3].first
    value: _DetailAlbedoMap
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[3].second.m_Texture
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[3].second.m_Scale.x
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[3].second.m_Scale.y
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[3].second.m_Offset.x
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[3].second.m_Offset.y
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[4].first
    value: _DetailMask
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[4].second.m_Texture
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[4].second.m_Scale.x
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[4].second.m_Scale.y
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[4].second.m_Offset.x
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[4].second.m_Offset.y
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[5].first
    value: _DetailNormalMap
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[5].second.m_Texture
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[5].second.m_Scale.x
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[5].second.m_Scale.y
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[5].second.m_Offset.x
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[5].second.m_Offset.y
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[6].first
    value: _EmissionMap
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[6].second.m_Texture
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[6].second.m_Scale.x
    value: 30
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[6].second.m_Scale.y
    value: 15
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[6].second.m_Offset.x
    value: 1.5
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[6].second.m_Offset.y
    value: 1.7
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[7].first
    value: _MainTex
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[7].second.m_Texture
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[7].second.m_Scale.x
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[7].second.m_Scale.y
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[7].second.m_Offset.x
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[7].second.m_Offset.y
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[8].first
    value: _MetallicGlossMap
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[8].second.m_Texture
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[8].second.m_Scale.x
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[8].second.m_Scale.y
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[8].second.m_Offset.x
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[8].second.m_Offset.y
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[9].first
    value: _OcclusionMap
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[9].second.m_Texture
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[9].second.m_Scale.x
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[9].second.m_Scale.y
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[9].second.m_Offset.x
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[9].second.m_Offset.y
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[10].first
    value: _ParallaxMap
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[10].second.m_Texture
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[10].second.m_Scale.x
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[10].second.m_Scale.y
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[10].second.m_Offset.x
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[10].second.m_Offset.y
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.size
    value: 59
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[0].first
    value: _BumpScale
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[0].second
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[1].first
    value: _CelExtraEnabled
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[1].second
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[2].first
    value: _CelNumSteps
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[2].second
    value: 3
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[3].first
    value: _CelPrimaryMode
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[3].second
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[4].first
    value: _Cutoff
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[4].second
    value: 0.5
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[5].first
    value: _DetailNormalMapScale
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[5].second
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[6].first
    value: _DstBlend
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[6].second
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[7].first
    value: _FlatRimAmount
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[7].second
    value: 0.5
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[8].first
    value: _FlatRimEdgeSmoothness
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[8].second
    value: 0.666
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[9].first
    value: _FlatRimEnabled
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[9].second
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[10].first
    value: _FlatRimLightAlign
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[10].second
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[11].first
    value: _FlatRimSize
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[11].second
    value: 0.527
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[12].first
    value: _FlatShadowsEnabled
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[12].second
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[13].first
    value: _FlatSmoothness
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[13].second
    value: 0.362
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[14].first
    value: _FlatSpecularEdgeSmoothness
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[14].second
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[15].first
    value: _FlatSpecularEnabled
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[15].second
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[16].first
    value: _FlatSpecularSize
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[16].second
    value: 0.81
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[17].first
    value: _FlatSpecularSmoothness
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[17].second
    value: 0.5
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[18].first
    value: _Flatness
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[18].second
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[19].first
    value: _FlatnessExtra
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[19].second
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[20].first
    value: _FlatnessSecondary
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[20].second
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[21].first
    value: _GlossMapScale
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[21].second
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[22].first
    value: _Glossiness
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[22].second
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[23].first
    value: _GlossyReflections
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[23].second
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[24].first
    value: _GradientAngle
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[24].second
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[25].first
    value: _GradientBottom
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[25].second
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[26].first
    value: _GradientCenterX
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[26].second
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[27].first
    value: _GradientCenterY
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[27].second
    value: 0.21
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[28].first
    value: _GradientEnabled
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[28].second
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[29].first
    value: _GradientEnd
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[29].second
    value: 0.55
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[30].first
    value: _GradientSize
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[30].second
    value: 0.5
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[31].first
    value: _GradientStart
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[31].second
    value: 1.17
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[32].first
    value: _GradientTop
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[32].second
    value: 10
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[33].first
    value: _LightContribution
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[33].second
    value: 0.5
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[34].first
    value: _Metallic
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[34].second
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[35].first
    value: _Mode
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[35].second
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[36].first
    value: _OcclusionStrength
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[36].second
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[37].first
    value: _OverrideShadows
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[37].second
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[38].first
    value: _OverrideShadowsEnabled
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[38].second
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[39].first
    value: _Parallax
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[39].second
    value: 0.02
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[40].first
    value: _RimEnabled
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[40].second
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[41].first
    value: _SecondaryColor
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[41].second
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[42].first
    value: _SecondaryColorEnabled
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[42].second
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[43].first
    value: _SelfShadingSize
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[43].second
    value: 0.696
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[44].first
    value: _SelfShadingSizeExtra
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[44].second
    value: 0.386
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[45].first
    value: _ShadowEdgeSize
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[45].second
    value: 0.127
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[46].first
    value: _ShadowEdgeSizeExtra
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[46].second
    value: 0.05
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[47].first
    value: _ShadowEdgeSmoothness
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[47].second
    value: 0.01
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[48].first
    value: _ShadowFalloff
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[48].second
    value: 0.01
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[49].first
    value: _SmoothnessTextureChannel
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[49].second
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[50].first
    value: _SpecularEnabled
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[50].second
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[51].first
    value: _SpecularHighlights
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[51].second
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[52].first
    value: _SrcBlend
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[52].second
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[53].first
    value: _TextureImpact
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[53].second
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[54].first
    value: _UVSec
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[54].second
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[55].first
    value: _UnityShadowMode
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[55].second
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[56].first
    value: _UnityShadowPower
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[56].second
    value: 0.2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[57].first
    value: _UnityShadowSharpness
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[57].second
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[58].first
    value: _ZWrite
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[58].second
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.size
    value: 14
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[0].first
    value: _Color
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[0].second.r
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[0].second.g
    value: 0.6618828
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[0].second.b
    value: 0.26886788
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[0].second.a
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[1].first
    value: _ColorCelShadow
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[1].second.r
    value: 0.38581347
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[1].second.g
    value: 0.38787434
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[1].second.b
    value: 0.9622642
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[1].second.a
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[2].first
    value: _ColorDim
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[2].second.r
    value: 0.8584906
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[2].second.g
    value: 0.42607445
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[2].second.b
    value: 0.26321644
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[2].second.a
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[3].first
    value: _ColorDimCurve
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[3].second.r
    value: 0.85
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[3].second.g
    value: 0.85
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[3].second.b
    value: 0.85
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[3].second.a
    value: 0.85
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[4].first
    value: _ColorDimExtra
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[4].second.r
    value: 0.46226412
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[4].second.g
    value: 0.40775186
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[4].second.b
    value: 0.40775186
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[4].second.a
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[5].first
    value: _ColorDimSteps
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[5].second.r
    value: 0.85
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[5].second.g
    value: 0.85
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[5].second.b
    value: 0.85
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[5].second.a
    value: 0.85
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[6].first
    value: _ColorGradient
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[6].second.r
    value: 0.9911022
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[6].second.g
    value: 0.8307701
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[6].second.b
    value: 0.5457247
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[6].second.a
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[7].first
    value: _ColorSecondary
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[7].second.r
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[7].second.g
    value: 0.55879205
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[7].second.b
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[7].second.a
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[8].first
    value: _ColorShadow
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[8].second.r
    value: 0.5
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[8].second.g
    value: 0.5
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[8].second.b
    value: 0.5
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[8].second.a
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[9].first
    value: _ColorShadows
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[9].second.r
    value: 0.37835297
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[9].second.g
    value: 0.4602353
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[9].second.b
    value: 0.72
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[9].second.a
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[10].first
    value: _EmissionColor
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[10].second.r
    value: 4.9245777
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[10].second.g
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[10].second.b
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[10].second.a
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[11].first
    value: _FlatRimColor
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[11].second.r
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[11].second.g
    value: 0.8387512
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[11].second.b
    value: 0.5254902
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[11].second.a
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[12].first
    value: _FlatSpecularColor
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[12].second.r
    value: 0.8213369
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[12].second.g
    value: 0.8301887
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[12].second.b
    value: 0.70879316
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[12].second.a
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[13].first
    value: _UnityShadowColor
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[13].second.r
    value: 0.65
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[13].second.g
    value: 0.65
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[13].second.b
    value: 0.65
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[13].second.a
    value: 1
    objectReference: {fileID: 0}
