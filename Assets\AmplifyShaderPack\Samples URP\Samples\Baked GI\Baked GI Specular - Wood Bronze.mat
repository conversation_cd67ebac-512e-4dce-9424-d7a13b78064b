%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 6
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Baked GI Specular - Wood Bronze
  m_Shader: {fileID: 4800000, guid: 37a3182d69b34284965dbdbe90e61ff5, type: 3}
  m_ShaderKeywords: 
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap:
    MotionVector: User
  disabledShaderPasses:
  - TransparentBackface
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _Albedo:
        m_Texture: {fileID: 2800000, guid: 85e3723e62d44f758723754190c67911, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 2800000, guid: abfd39fa1d6a42ba9b322e4301333932, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: 85e3723e62d44f758723754190c67911, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Normal:
        m_Texture: {fileID: 2800000, guid: abfd39fa1d6a42ba9b322e4301333932, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Occlusion:
        m_Texture: {fileID: 2800000, guid: ffc41f1810014593ba2069132ba4d0f3, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 2800000, guid: ffc41f1810014593ba2069132ba4d0f3, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SmoothnessMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Specular:
        m_Texture: {fileID: 2800000, guid: f4ef9b0dca354d7aaf185497d6b77ea5, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecularMap:
        m_Texture: {fileID: 2800000, guid: 3723accbbd1d47eaa125e5b942a19c86, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _texcoord:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Floats:
    - _AddPrecomputedVelocity: 1
    - _AlphaCutoff: 0.5
    - _AlphaCutoffEnable: 0
    - _AlphaCutoffShadow: 1
    - _AlphaDstBlend: 0
    - _AlphaSrcBlend: 1
    - _AlphaToMask: 0
    - _AlphaToMaskInspectorValue: 0
    - _BakedGIEnable: 1
    - _BlendMode: 0
    - _Brightness: 1
    - _Cull: 2
    - _CullMode: 2
    - _CullModeForward: 2
    - _Cutoff: 0.5
    - _DoubleSidedEnable: 0
    - _DoubleSidedGIMode: 0
    - _DoubleSidedNormalMode: 2
    - _DstBlend: 0
    - _EnableBlendModePreserveSpecularLighting: 1
    - _EnableFogOnTransparent: 1
    - _EnvironmentReflections: 1
    - _Float0: 4.37
    - _GlossyReflections: 1
    - _IndirectDiffuse: 0.773
    - _IndirectSpecular: 0.61
    - _IndirectSpecularOcclusion: 0.46
    - _IndirectSpecularRoughness: 0.213
    - _NormalStrength: 1
    - _OcclusionStrengthAO: 0.923
    - _OcclusionStrengthAOMax: 1
    - _OcclusionStrengthAOMin: 0
    - _OpaqueCullMode: 2
    - _QueueControl: 0
    - _QueueOffset: 0
    - _ReceiveShadows: 1
    - _ReceivesSSR: 1
    - _ReceivesSSRTransparent: 0
    - _RenderQueueType: 1
    - _RequireSplitLighting: 0
    - _ShadowApproxmation: 0
    - _ShadowIntensity: 0.429
    - _ShadowOffset: 0.357
    - _ShadowSharpness: 0.666
    - _ShadowStrength: 1
    - _SmoothnessStrength: 0.516
    - _SmoothnessStrengthMax: 1
    - _SmoothnessStrengthMin: 0
    - _SpecularColorIOR: 0
    - _SpecularColorWeight: 1.3
    - _SpecularHighlights: 1
    - _SpecularStrength: 0.351
    - _SrcBlend: 1
    - _StencilRef: 0
    - _StencilRefDepth: 8
    - _StencilRefDistortionVec: 4
    - _StencilRefGBuffer: 10
    - _StencilRefMV: 40
    - _StencilWriteMask: 6
    - _StencilWriteMaskDepth: 8
    - _StencilWriteMaskDistortionVec: 4
    - _StencilWriteMaskGBuffer: 14
    - _StencilWriteMaskMV: 40
    - _SupportDecals: 1
    - _SurfaceType: 0
    - _TransparentBackfaceEnable: 0
    - _TransparentCullMode: 2
    - _TransparentSortPriority: 0
    - _TransparentWritingMotionVec: 0
    - _TransparentZWrite: 0
    - _UseShadowThreshold: 1
    - _ZTestDepthEqualForOpaque: 3
    - _ZTestGBuffer: 4
    - _ZTestTransparent: 4
    - _ZWrite: 1
    - __dirty: 0
    m_Colors:
    - _BaseColor: {r: 0.5411765, g: 0.30980393, b: 0.20784314, a: 0}
    - _DoubleSidedConstants: {r: 1, g: 1, b: -1, a: 0}
    - _EmissionColor: {r: 1, g: 1, b: 1, a: 1}
    - _MainUVs: {r: 2, g: 2, b: 0, a: 0}
    - _SpecularColor: {r: 0.5377358, g: 0.48497683, b: 0.47939655, a: 1}
    - _Tint: {r: 1, g: 1, b: 1, a: 0}
  m_BuildTextureStacks: []
