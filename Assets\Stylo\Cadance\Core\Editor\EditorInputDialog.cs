using UnityEngine;
using UnityEditor;

namespace Stylo.Cadance.Editor
{
    /// <summary>
    /// Simple input dialog for editor operations, equivalent to Koreographer's input dialogs.
    /// </summary>
    public class EditorInputDialog : EditorWindow
    {
        private string title;
        private string message;
        private string inputText;
        private string defaultText;
        private System.Action<string> onConfirm;
        private System.Action onCancel;

        /// <summary>
        /// Shows a simple input dialog.
        /// </summary>
        /// <param name="title">Dialog title</param>
        /// <param name="message">Dialog message</param>
        /// <param name="defaultValue">Default input value</param>
        /// <returns>User input or null if cancelled</returns>
        public static string Show(string title, string message, string defaultValue = "")
        {
            string result = null;
            bool dialogComplete = false;

            var dialog = CreateInstance<EditorInputDialog>();
            dialog.title = title;
            dialog.message = message;
            dialog.inputText = defaultValue;
            dialog.defaultText = defaultValue;
            dialog.onConfirm = (input) => { result = input; dialogComplete = true; };
            dialog.onCancel = () => { result = null; dialogComplete = true; };

            dialog.titleContent = new GUIContent(title);
            dialog.minSize = new Vector2(300, 120);
            dialog.maxSize = new Vector2(400, 120);
            dialog.ShowModal();

            // Wait for dialog completion
            while (!dialogComplete && dialog != null)
            {
                System.Threading.Thread.Sleep(10);
            }

            if (dialog != null)
            {
                dialog.Close();
            }

            return result;
        }

        /// <summary>
        /// Shows an asynchronous input dialog with callbacks.
        /// </summary>
        /// <param name="title">Dialog title</param>
        /// <param name="message">Dialog message</param>
        /// <param name="defaultValue">Default input value</param>
        /// <param name="onConfirm">Callback when confirmed</param>
        /// <param name="onCancel">Callback when cancelled</param>
        public static void ShowAsync(string title, string message, string defaultValue, 
            System.Action<string> onConfirm, System.Action onCancel = null)
        {
            var dialog = CreateInstance<EditorInputDialog>();
            dialog.title = title;
            dialog.message = message;
            dialog.inputText = defaultValue;
            dialog.defaultText = defaultValue;
            dialog.onConfirm = onConfirm;
            dialog.onCancel = onCancel;

            dialog.titleContent = new GUIContent(title);
            dialog.minSize = new Vector2(300, 120);
            dialog.maxSize = new Vector2(400, 120);
            dialog.ShowUtility();
        }

        private void OnGUI()
        {
            EditorGUILayout.Space();
            
            // Message
            EditorGUILayout.LabelField(message, EditorStyles.wordWrappedLabel);
            
            EditorGUILayout.Space();
            
            // Input field
            GUI.SetNextControlName("InputField");
            inputText = EditorGUILayout.TextField("Input:", inputText);
            
            // Focus on input field
            if (Event.current.type == EventType.Repaint)
            {
                GUI.FocusControl("InputField");
            }
            
            EditorGUILayout.Space();
            
            // Buttons
            EditorGUILayout.BeginHorizontal();
            GUILayout.FlexibleSpace();
            
            if (GUILayout.Button("Cancel", GUILayout.Width(80)))
            {
                onCancel?.Invoke();
                Close();
            }
            
            if (GUILayout.Button("OK", GUILayout.Width(80)))
            {
                onConfirm?.Invoke(inputText);
                Close();
            }
            
            EditorGUILayout.EndHorizontal();
            
            // Handle Enter and Escape keys
            if (Event.current.type == EventType.KeyDown)
            {
                if (Event.current.keyCode == KeyCode.Return || Event.current.keyCode == KeyCode.KeypadEnter)
                {
                    onConfirm?.Invoke(inputText);
                    Close();
                    Event.current.Use();
                }
                else if (Event.current.keyCode == KeyCode.Escape)
                {
                    onCancel?.Invoke();
                    Close();
                    Event.current.Use();
                }
            }
        }
    }
}
