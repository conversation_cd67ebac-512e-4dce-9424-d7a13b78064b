%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1b0c486dee8904d95a666bbd0aafda94, type: 3}
  m_Name: settings
  m_EditorClassIdentifier: 
  tabs:
  - name: Trees
    brushes:
    - name: tree1
      settings:
        brushRadius: 8
        brushSpacing: 3
        brushOverlapCheckMode: 0
        brushOverlapDistance: 4
        brushOverlapCheckObjects: 0
        brushOverlapCheckLayers:
          serializedVersion: 2
          m_Bits: 0
        surfaceOffset: 0
        orientationTransformMode: 0
        orientationMode: 2
        alongBrushStroke: 0
        rotation: {x: 0, y: 0, z: 0}
        randomizeOrientationX: 5
        randomizeOrientationY: 100
        randomizeOrientationZ: 5
        scaleTransformMode: 0
        scaleMode: 0
        scaleUniformMin: 0.8
        scaleUniformMax: 1.3
        scalePerAxisMin: {x: 1, y: 1, z: 1}
        scalePerAxisMax: {x: 1, y: 1, z: 1}
        pinFixedRotation: 0
        pinFixedRotationValue: {x: 0, y: 0, z: 0}
        pinFixedScale: 0
        pinFixedScaleValue: {x: 1, y: 1, z: 1}
        placeEulerAngles: {x: 0, y: 135, z: 0}
        placeScale: 1.3000001
        throwRadius: 5
        throwSpacing: 2
        throwHeight: 5
        throwVelocity: 0
        throwRandomRotation: {x: 18.5, y: 33.1, z: 14.8}
        throwScaleMinMax: {x: 1, y: 1}
        multibrushEnabled: 0
        multibrushSlots:
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        multibrushPaintSelectedSlot: 0
        multibrushMode: 0
        multibrushPattern: 0 1 2 3 5 6 7 8 9
        multibrushPatternContinue: 0
        slopeEnabled: 0
        slopeAngleMin: 0
        slopeAngleMax: 35
        slopeVector: 1
        slopeVectorCustom: {x: 0, y: 1, z: 0}
        slopeVectorFlip: 0
        gridEnabled: 0
        gridOrigin: {x: 0, y: 0}
        gridStep: {x: 5, y: 5}
        gridPlane: 1
        gridNormal: {x: 0, y: 1, z: 0}
        gridAngle: 0
      prefabSlots:
      - gameObject: {fileID: 142772, guid: 8e66ff4afa28c4be7821a9ae9372d1f7, type: 3}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      selectedSlot: 0
      selected: 0
      colorTag: 0
      customPreviewImage: {fileID: 0}
    - name: tree2
      settings:
        brushRadius: 8
        brushSpacing: 3
        brushOverlapCheckMode: 0
        brushOverlapDistance: 4
        brushOverlapCheckObjects: 0
        brushOverlapCheckLayers:
          serializedVersion: 2
          m_Bits: 0
        surfaceOffset: 0
        orientationTransformMode: 0
        orientationMode: 2
        alongBrushStroke: 0
        rotation: {x: 0, y: 0, z: 0}
        randomizeOrientationX: 5
        randomizeOrientationY: 100
        randomizeOrientationZ: 5
        scaleTransformMode: 0
        scaleMode: 0
        scaleUniformMin: 0.8
        scaleUniformMax: 1.3
        scalePerAxisMin: {x: 1, y: 1, z: 1}
        scalePerAxisMax: {x: 1, y: 1, z: 1}
        pinFixedRotation: 0
        pinFixedRotationValue: {x: 0, y: 0, z: 0}
        pinFixedScale: 0
        pinFixedScaleValue: {x: 1, y: 1, z: 1}
        placeEulerAngles: {x: 0, y: 0, z: 0}
        placeScale: 1
        throwRadius: 5
        throwSpacing: 2
        throwHeight: 5
        throwVelocity: 0
        throwRandomRotation: {x: 0, y: 0, z: 0}
        throwScaleMinMax: {x: 1, y: 1}
        multibrushEnabled: 0
        multibrushSlots:
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        multibrushPaintSelectedSlot: 0
        multibrushMode: 0
        multibrushPattern: 0 1 2 3 5 6 7 8 9
        multibrushPatternContinue: 0
        slopeEnabled: 0
        slopeAngleMin: 0
        slopeAngleMax: 35
        slopeVector: 1
        slopeVectorCustom: {x: 0, y: 1, z: 0}
        slopeVectorFlip: 0
        gridEnabled: 0
        gridOrigin: {x: 0, y: 0}
        gridStep: {x: 5, y: 5}
        gridPlane: 1
        gridNormal: {x: 0, y: 1, z: 0}
        gridAngle: 0
      prefabSlots:
      - gameObject: {fileID: 100618, guid: e07b9ca3ecaff4d4fbf82c30d96cf38b, type: 3}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      selectedSlot: 0
      selected: 0
      colorTag: 0
      customPreviewImage: {fileID: 0}
    - name: tree3
      settings:
        brushRadius: 8
        brushSpacing: 3
        brushOverlapCheckMode: 0
        brushOverlapDistance: 4
        brushOverlapCheckObjects: 0
        brushOverlapCheckLayers:
          serializedVersion: 2
          m_Bits: 0
        surfaceOffset: 0
        orientationTransformMode: 0
        orientationMode: 2
        alongBrushStroke: 0
        rotation: {x: 0, y: 0, z: 0}
        randomizeOrientationX: 5
        randomizeOrientationY: 100
        randomizeOrientationZ: 5
        scaleTransformMode: 0
        scaleMode: 0
        scaleUniformMin: 0.8
        scaleUniformMax: 1.3
        scalePerAxisMin: {x: 1, y: 1, z: 1}
        scalePerAxisMax: {x: 1, y: 1, z: 1}
        pinFixedRotation: 0
        pinFixedRotationValue: {x: 0, y: 0, z: 0}
        pinFixedScale: 0
        pinFixedScaleValue: {x: 1, y: 1, z: 1}
        placeEulerAngles: {x: 0, y: 0, z: 0}
        placeScale: 0.5
        throwRadius: 5
        throwSpacing: 2
        throwHeight: 5
        throwVelocity: 0
        throwRandomRotation: {x: 0, y: 0, z: 0}
        throwScaleMinMax: {x: 1, y: 1}
        multibrushEnabled: 0
        multibrushSlots:
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        multibrushPaintSelectedSlot: 0
        multibrushMode: 0
        multibrushPattern: 0 1 2 3 5 6 7 8 9
        multibrushPatternContinue: 0
        slopeEnabled: 0
        slopeAngleMin: 0
        slopeAngleMax: 35
        slopeVector: 1
        slopeVectorCustom: {x: 0, y: 1, z: 0}
        slopeVectorFlip: 0
        gridEnabled: 0
        gridOrigin: {x: 0, y: 0}
        gridStep: {x: 5, y: 5}
        gridPlane: 1
        gridNormal: {x: 0, y: 1, z: 0}
        gridAngle: 0
      prefabSlots:
      - gameObject: {fileID: 178792, guid: 0dd60fda408f047bfb07894449319218, type: 3}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      selectedSlot: 0
      selected: 0
      colorTag: 0
      customPreviewImage: {fileID: 0}
    - name: tree4
      settings:
        brushRadius: 8
        brushSpacing: 3
        brushOverlapCheckMode: 0
        brushOverlapDistance: 4
        brushOverlapCheckObjects: 0
        brushOverlapCheckLayers:
          serializedVersion: 2
          m_Bits: 0
        surfaceOffset: 0
        orientationTransformMode: 0
        orientationMode: 2
        alongBrushStroke: 0
        rotation: {x: 0, y: 0, z: 0}
        randomizeOrientationX: 5
        randomizeOrientationY: 100
        randomizeOrientationZ: 5
        scaleTransformMode: 0
        scaleMode: 0
        scaleUniformMin: 0.8
        scaleUniformMax: 1.3
        scalePerAxisMin: {x: 1, y: 1, z: 1}
        scalePerAxisMax: {x: 1, y: 1, z: 1}
        pinFixedRotation: 0
        pinFixedRotationValue: {x: 0, y: 0, z: 0}
        pinFixedScale: 0
        pinFixedScaleValue: {x: 1, y: 1, z: 1}
        placeEulerAngles: {x: 0, y: 0, z: 0}
        placeScale: 1
        throwRadius: 5
        throwSpacing: 2
        throwHeight: 5
        throwVelocity: 0
        throwRandomRotation: {x: 0, y: 0, z: 0}
        throwScaleMinMax: {x: 1, y: 1}
        multibrushEnabled: 0
        multibrushSlots:
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        multibrushPaintSelectedSlot: 0
        multibrushMode: 0
        multibrushPattern: 0 1 2 3 5 6 7 8 9
        multibrushPatternContinue: 0
        slopeEnabled: 0
        slopeAngleMin: 0
        slopeAngleMax: 35
        slopeVector: 1
        slopeVectorCustom: {x: 0, y: 1, z: 0}
        slopeVectorFlip: 0
        gridEnabled: 0
        gridOrigin: {x: 0, y: 0}
        gridStep: {x: 5, y: 5}
        gridPlane: 1
        gridNormal: {x: 0, y: 1, z: 0}
        gridAngle: 0
      prefabSlots:
      - gameObject: {fileID: 131906, guid: ba260558c316b4cda92178dc6fef1161, type: 3}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      selectedSlot: 0
      selected: 0
      colorTag: 0
      customPreviewImage: {fileID: 0}
    - name: tree random
      settings:
        brushRadius: 5.9484143
        brushSpacing: 3
        brushOverlapCheckMode: 0
        brushOverlapDistance: 5
        brushOverlapCheckObjects: 1
        brushOverlapCheckLayers:
          serializedVersion: 2
          m_Bits: 0
        surfaceOffset: 0
        orientationTransformMode: 0
        orientationMode: 2
        alongBrushStroke: 0
        rotation: {x: 0, y: 0, z: 0}
        randomizeOrientationX: 5
        randomizeOrientationY: 100
        randomizeOrientationZ: 5
        scaleTransformMode: 0
        scaleMode: 0
        scaleUniformMin: 0.8
        scaleUniformMax: 1.3
        scalePerAxisMin: {x: 1, y: 1, z: 1}
        scalePerAxisMax: {x: 1, y: 1, z: 1}
        pinFixedRotation: 0
        pinFixedRotationValue: {x: 0, y: 0, z: 0}
        pinFixedScale: 0
        pinFixedScaleValue: {x: 1, y: 1, z: 1}
        placeEulerAngles: {x: 0, y: 0, z: 0}
        placeScale: 1
        throwRadius: 5.71
        throwSpacing: 0.48
        throwHeight: 5
        throwVelocity: 0
        throwRandomRotation: {x: 36.5, y: 43.5, z: 48.4}
        throwScaleMinMax: {x: 1, y: 1}
        multibrushEnabled: 1
        multibrushSlots:
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 0.15714286
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 0.2
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 0.071428575
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        multibrushPaintSelectedSlot: 0
        multibrushMode: 0
        multibrushPattern: 0 1 2 3 5 6 7 8 9
        multibrushPatternContinue: 0
        slopeEnabled: 0
        slopeAngleMin: 0
        slopeAngleMax: 35
        slopeVector: 1
        slopeVectorCustom: {x: 0, y: 1, z: 0}
        slopeVectorFlip: 0
        gridEnabled: 0
        gridOrigin: {x: 0, y: 0}
        gridStep: {x: 5, y: 5}
        gridPlane: 1
        gridNormal: {x: 0, y: 1, z: 0}
        gridAngle: 0
      prefabSlots:
      - gameObject: {fileID: 142772, guid: 8e66ff4afa28c4be7821a9ae9372d1f7, type: 3}
      - gameObject: {fileID: 100618, guid: e07b9ca3ecaff4d4fbf82c30d96cf38b, type: 3}
      - gameObject: {fileID: 178792, guid: 0dd60fda408f047bfb07894449319218, type: 3}
      - gameObject: {fileID: 131906, guid: ba260558c316b4cda92178dc6fef1161, type: 3}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      selectedSlot: 6
      selected: 0
      colorTag: 0
      customPreviewImage: {fileID: 0}
    levelsGamma: 1
    levelsInBlack: 0
    levelsInWhite: 255
    levelsOutBlack: 0
    levelsOutWhite: 255
  - name: Decals
    brushes:
    - name: decal1
      settings:
        brushRadius: 1.25
        brushSpacing: 1
        brushOverlapCheckMode: 0
        brushOverlapDistance: 0.5
        brushOverlapCheckObjects: 1
        brushOverlapCheckLayers:
          serializedVersion: 2
          m_Bits: 0
        surfaceOffset: 0.2
        orientationTransformMode: 0
        orientationMode: 0
        alongBrushStroke: 0
        rotation: {x: 0, y: 0, z: 0}
        randomizeOrientationX: 0
        randomizeOrientationY: 100
        randomizeOrientationZ: 0
        scaleTransformMode: 0
        scaleMode: 0
        scaleUniformMin: 1
        scaleUniformMax: 1
        scalePerAxisMin: {x: 1, y: 1, z: 1}
        scalePerAxisMax: {x: 1, y: 1, z: 1}
        pinFixedRotation: 0
        pinFixedRotationValue: {x: 0, y: 0, z: 0}
        pinFixedScale: 0
        pinFixedScaleValue: {x: 1, y: 1, z: 1}
        placeEulerAngles: {x: 0, y: -30, z: 0}
        placeScale: 5
        throwRadius: 5
        throwSpacing: 2
        throwHeight: 5
        throwVelocity: 0
        throwRandomRotation: {x: 0, y: 0, z: 0}
        throwScaleMinMax: {x: 1, y: 1}
        multibrushEnabled: 0
        multibrushSlots:
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        multibrushPaintSelectedSlot: 0
        multibrushMode: 0
        multibrushPattern: 0 1 2 3 5 6 7 8 9
        multibrushPatternContinue: 0
        slopeEnabled: 0
        slopeAngleMin: 0
        slopeAngleMax: 35
        slopeVector: 1
        slopeVectorCustom: {x: 0, y: 1, z: 0}
        slopeVectorFlip: 0
        gridEnabled: 0
        gridOrigin: {x: 0, y: 0}
        gridStep: {x: 5, y: 5}
        gridPlane: 1
        gridNormal: {x: 0, y: 1, z: 0}
        gridAngle: 0
      prefabSlots:
      - gameObject: {fileID: 124718, guid: 826898d6c67534099ae1c62db6396484, type: 3}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      selectedSlot: 0
      selected: 0
      colorTag: 0
      customPreviewImage: {fileID: 0}
    - name: footstep_A
      settings:
        brushRadius: 0.01
        brushSpacing: 1
        brushOverlapCheckMode: 0
        brushOverlapDistance: 0.5
        brushOverlapCheckObjects: 1
        brushOverlapCheckLayers:
          serializedVersion: 2
          m_Bits: 0
        surfaceOffset: 0.2
        orientationTransformMode: 0
        orientationMode: 0
        alongBrushStroke: 1
        rotation: {x: 0, y: 0, z: 0}
        randomizeOrientationX: 0
        randomizeOrientationY: 0
        randomizeOrientationZ: 0
        scaleTransformMode: 0
        scaleMode: 0
        scaleUniformMin: 1
        scaleUniformMax: 1
        scalePerAxisMin: {x: 1, y: 1, z: 1}
        scalePerAxisMax: {x: 1, y: 1, z: 1}
        pinFixedRotation: 0
        pinFixedRotationValue: {x: 0, y: 0, z: 0}
        pinFixedScale: 0
        pinFixedScaleValue: {x: 1, y: 1, z: 1}
        placeEulerAngles: {x: 0, y: 0, z: 0}
        placeScale: 1
        throwRadius: 5
        throwSpacing: 2
        throwHeight: 5
        throwVelocity: 0
        throwRandomRotation: {x: 0, y: 0, z: 0}
        throwScaleMinMax: {x: 1, y: 1}
        multibrushEnabled: 1
        multibrushSlots:
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0.3}
          rotation: {x: 0, y: -10, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: -0.3}
          rotation: {x: 0, y: 10, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        multibrushPaintSelectedSlot: 0
        multibrushMode: 1
        multibrushPattern: 0 1
        multibrushPatternContinue: 1
        slopeEnabled: 0
        slopeAngleMin: 0
        slopeAngleMax: 35
        slopeVector: 1
        slopeVectorCustom: {x: 0, y: 1, z: 0}
        slopeVectorFlip: 0
        gridEnabled: 0
        gridOrigin: {x: 0, y: 0}
        gridStep: {x: 5, y: 5}
        gridPlane: 1
        gridNormal: {x: 0, y: 1, z: 0}
        gridAngle: 0
      prefabSlots:
      - gameObject: {fileID: 167392, guid: 62b4cd3ac819640e49ea9de71d47400c, type: 3}
      - gameObject: {fileID: 135010, guid: 01f4555e876dc457fb72a6fddd3dea58, type: 3}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      selectedSlot: 1
      selected: 0
      colorTag: 0
      customPreviewImage: {fileID: 0}
    - name: footstep_A
      settings:
        brushRadius: 0.01
        brushSpacing: 1
        brushOverlapCheckMode: 0
        brushOverlapDistance: 0.5
        brushOverlapCheckObjects: 1
        brushOverlapCheckLayers:
          serializedVersion: 2
          m_Bits: 0
        surfaceOffset: 0.2
        orientationTransformMode: 0
        orientationMode: 0
        alongBrushStroke: 1
        rotation: {x: 0, y: 0, z: 0}
        randomizeOrientationX: 0
        randomizeOrientationY: 0
        randomizeOrientationZ: 0
        scaleTransformMode: 0
        scaleMode: 0
        scaleUniformMin: 1
        scaleUniformMax: 1
        scalePerAxisMin: {x: 1, y: 1, z: 1}
        scalePerAxisMax: {x: 1, y: 1, z: 1}
        pinFixedRotation: 0
        pinFixedRotationValue: {x: 0, y: 0, z: 0}
        pinFixedScale: 0
        pinFixedScaleValue: {x: 1, y: 1, z: 1}
        placeEulerAngles: {x: 0, y: 0, z: 0}
        placeScale: 1
        throwRadius: 5
        throwSpacing: 2
        throwHeight: 5
        throwVelocity: 0
        throwRandomRotation: {x: 0, y: 0, z: 0}
        throwScaleMinMax: {x: 1, y: 1}
        multibrushEnabled: 1
        multibrushSlots:
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0.3}
          rotation: {x: 0, y: -10, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: -0.3}
          rotation: {x: 0, y: 10, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        multibrushPaintSelectedSlot: 0
        multibrushMode: 1
        multibrushPattern: 0 1
        multibrushPatternContinue: 0
        slopeEnabled: 0
        slopeAngleMin: 0
        slopeAngleMax: 35
        slopeVector: 1
        slopeVectorCustom: {x: 0, y: 1, z: 0}
        slopeVectorFlip: 0
        gridEnabled: 0
        gridOrigin: {x: 0, y: 0}
        gridStep: {x: 5, y: 5}
        gridPlane: 1
        gridNormal: {x: 0, y: 1, z: 0}
        gridAngle: 0
      prefabSlots:
      - gameObject: {fileID: 140332, guid: 388a4661237104b49b1ede1cda315eb6, type: 3}
      - gameObject: {fileID: 199396, guid: 9561f2160ee6343b287375c0c4787967, type: 3}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      selectedSlot: 1
      selected: 0
      colorTag: 0
      customPreviewImage: {fileID: 0}
    - name: decal2
      settings:
        brushRadius: 1.25
        brushSpacing: 1
        brushOverlapCheckMode: 0
        brushOverlapDistance: 0.5
        brushOverlapCheckObjects: 1
        brushOverlapCheckLayers:
          serializedVersion: 2
          m_Bits: 0
        surfaceOffset: 0.2
        orientationTransformMode: 0
        orientationMode: 0
        alongBrushStroke: 0
        rotation: {x: 0, y: 0, z: 0}
        randomizeOrientationX: 0
        randomizeOrientationY: 100
        randomizeOrientationZ: 0
        scaleTransformMode: 0
        scaleMode: 0
        scaleUniformMin: 1
        scaleUniformMax: 1
        scalePerAxisMin: {x: 1, y: 1, z: 1}
        scalePerAxisMax: {x: 1, y: 1, z: 1}
        pinFixedRotation: 0
        pinFixedRotationValue: {x: 0, y: 0, z: 0}
        pinFixedScale: 0
        pinFixedScaleValue: {x: 1, y: 1, z: 1}
        placeEulerAngles: {x: 0, y: 0, z: 0}
        placeScale: 1
        throwRadius: 5
        throwSpacing: 2
        throwHeight: 5
        throwVelocity: 0
        throwRandomRotation: {x: 0, y: 0, z: 0}
        throwScaleMinMax: {x: 1, y: 1}
        multibrushEnabled: 0
        multibrushSlots:
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        multibrushPaintSelectedSlot: 0
        multibrushMode: 0
        multibrushPattern: 0 1 2 3 5 6 7 8 9
        multibrushPatternContinue: 0
        slopeEnabled: 0
        slopeAngleMin: 0
        slopeAngleMax: 35
        slopeVector: 1
        slopeVectorCustom: {x: 0, y: 1, z: 0}
        slopeVectorFlip: 0
        gridEnabled: 0
        gridOrigin: {x: 0, y: 0}
        gridStep: {x: 5, y: 5}
        gridPlane: 1
        gridNormal: {x: 0, y: 1, z: 0}
        gridAngle: 0
      prefabSlots:
      - gameObject: {fileID: 150248, guid: fccdaf2af7593429990a3f305d8fefa2, type: 3}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      selectedSlot: 0
      selected: 0
      colorTag: 0
      customPreviewImage: {fileID: 0}
    - name: decal3
      settings:
        brushRadius: 0.01
        brushSpacing: 1
        brushOverlapCheckMode: 0
        brushOverlapDistance: 0.5
        brushOverlapCheckObjects: 1
        brushOverlapCheckLayers:
          serializedVersion: 2
          m_Bits: 0
        surfaceOffset: 0.2
        orientationTransformMode: 0
        orientationMode: 0
        alongBrushStroke: 1
        rotation: {x: 0, y: 0, z: 0}
        randomizeOrientationX: 0
        randomizeOrientationY: 0
        randomizeOrientationZ: 0
        scaleTransformMode: 0
        scaleMode: 0
        scaleUniformMin: 1
        scaleUniformMax: 1
        scalePerAxisMin: {x: 1, y: 1, z: 1}
        scalePerAxisMax: {x: 1, y: 1, z: 1}
        pinFixedRotation: 0
        pinFixedRotationValue: {x: 0, y: 0, z: 0}
        pinFixedScale: 0
        pinFixedScaleValue: {x: 1, y: 1, z: 1}
        placeEulerAngles: {x: 0, y: 0, z: 0}
        placeScale: 1
        throwRadius: 5
        throwSpacing: 2
        throwHeight: 5
        throwVelocity: 0
        throwRandomRotation: {x: 0, y: 0, z: 0}
        throwScaleMinMax: {x: 1, y: 1}
        multibrushEnabled: 0
        multibrushSlots:
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        multibrushPaintSelectedSlot: 0
        multibrushMode: 0
        multibrushPattern: 0 1 2 3 5 6 7 8 9
        multibrushPatternContinue: 0
        slopeEnabled: 0
        slopeAngleMin: 0
        slopeAngleMax: 35
        slopeVector: 1
        slopeVectorCustom: {x: 0, y: 1, z: 0}
        slopeVectorFlip: 0
        gridEnabled: 0
        gridOrigin: {x: 0, y: 0}
        gridStep: {x: 5, y: 5}
        gridPlane: 1
        gridNormal: {x: 0, y: 1, z: 0}
        gridAngle: 0
      prefabSlots:
      - gameObject: {fileID: 100044, guid: 74dd510445e38470dbda34cbd3419c2d, type: 3}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      selectedSlot: 0
      selected: 0
      colorTag: 0
      customPreviewImage: {fileID: 0}
    - name: decal4
      settings:
        brushRadius: 1.25
        brushSpacing: 1
        brushOverlapCheckMode: 0
        brushOverlapDistance: 0.5
        brushOverlapCheckObjects: 1
        brushOverlapCheckLayers:
          serializedVersion: 2
          m_Bits: 0
        surfaceOffset: 0.2
        orientationTransformMode: 0
        orientationMode: 0
        alongBrushStroke: 0
        rotation: {x: 0, y: 0, z: 0}
        randomizeOrientationX: 0
        randomizeOrientationY: 100
        randomizeOrientationZ: 0
        scaleTransformMode: 0
        scaleMode: 0
        scaleUniformMin: 1
        scaleUniformMax: 1
        scalePerAxisMin: {x: 1, y: 1, z: 1}
        scalePerAxisMax: {x: 1, y: 1, z: 1}
        pinFixedRotation: 0
        pinFixedRotationValue: {x: 0, y: 0, z: 0}
        pinFixedScale: 0
        pinFixedScaleValue: {x: 1, y: 1, z: 1}
        placeEulerAngles: {x: 0, y: 0, z: 0}
        placeScale: 1
        throwRadius: 5
        throwSpacing: 2
        throwHeight: 5
        throwVelocity: 0
        throwRandomRotation: {x: 0, y: 0, z: 0}
        throwScaleMinMax: {x: 1, y: 1}
        multibrushEnabled: 0
        multibrushSlots:
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        multibrushPaintSelectedSlot: 0
        multibrushMode: 0
        multibrushPattern: 0 1 2 3 5 6 7 8 9
        multibrushPatternContinue: 0
        slopeEnabled: 0
        slopeAngleMin: 0
        slopeAngleMax: 35
        slopeVector: 1
        slopeVectorCustom: {x: 0, y: 1, z: 0}
        slopeVectorFlip: 0
        gridEnabled: 0
        gridOrigin: {x: 0, y: 0}
        gridStep: {x: 5, y: 5}
        gridPlane: 1
        gridNormal: {x: 0, y: 1, z: 0}
        gridAngle: 0
      prefabSlots:
      - gameObject: {fileID: 180820, guid: d56ef037a2ece46958febec692c028c9, type: 3}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      selectedSlot: 0
      selected: 1
      colorTag: 0
      customPreviewImage: {fileID: 0}
    levelsGamma: 1
    levelsInBlack: 0
    levelsInWhite: 255
    levelsOutBlack: 0
    levelsOutWhite: 255
  - name: Misc
    brushes:
    - name: leaf
      settings:
        brushRadius: 2
        brushSpacing: 0.2
        brushOverlapCheckMode: 0
        brushOverlapDistance: 0.5
        brushOverlapCheckObjects: 1
        brushOverlapCheckLayers:
          serializedVersion: 2
          m_Bits: 0
        surfaceOffset: 0
        orientationTransformMode: 0
        orientationMode: 2
        alongBrushStroke: 0
        rotation: {x: 0, y: 0, z: 0}
        randomizeOrientationX: 100
        randomizeOrientationY: 100
        randomizeOrientationZ: 100
        scaleTransformMode: 0
        scaleMode: 0
        scaleUniformMin: 0.9
        scaleUniformMax: 1.1
        scalePerAxisMin: {x: 1, y: 1, z: 1}
        scalePerAxisMax: {x: 1, y: 1, z: 1}
        pinFixedRotation: 0
        pinFixedRotationValue: {x: 0, y: 0, z: 0}
        pinFixedScale: 0
        pinFixedScaleValue: {x: 1, y: 1, z: 1}
        placeEulerAngles: {x: 0, y: 0, z: 0}
        placeScale: 1
        throwRadius: 5
        throwSpacing: 2
        throwHeight: 5
        throwVelocity: 0
        throwRandomRotation: {x: 0, y: 0, z: 0}
        throwScaleMinMax: {x: 1, y: 1}
        multibrushEnabled: 0
        multibrushSlots:
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        multibrushPaintSelectedSlot: 0
        multibrushMode: 0
        multibrushPattern: 0 1 2 3 5 6 7 8 9
        multibrushPatternContinue: 0
        slopeEnabled: 1
        slopeAngleMin: 0
        slopeAngleMax: 35
        slopeVector: 1
        slopeVectorCustom: {x: 0, y: 1, z: 0}
        slopeVectorFlip: 0
        gridEnabled: 0
        gridOrigin: {x: 0, y: 0}
        gridStep: {x: 5, y: 5}
        gridPlane: 1
        gridNormal: {x: 0, y: 1, z: 0}
        gridAngle: 0
      prefabSlots:
      - gameObject: {fileID: 148494, guid: d268d5ba1abbf433caa62bec3d3d1583, type: 3}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      selectedSlot: 0
      selected: 0
      colorTag: 0
      customPreviewImage: {fileID: 0}
    - name: grass
      settings:
        brushRadius: 8
        brushSpacing: 3
        brushOverlapCheckMode: 0
        brushOverlapDistance: 0.5
        brushOverlapCheckObjects: 1
        brushOverlapCheckLayers:
          serializedVersion: 2
          m_Bits: 0
        surfaceOffset: 0
        orientationTransformMode: 0
        orientationMode: 0
        alongBrushStroke: 0
        rotation: {x: 0, y: 0, z: 0}
        randomizeOrientationX: 0
        randomizeOrientationY: 100
        randomizeOrientationZ: 0
        scaleTransformMode: 0
        scaleMode: 0
        scaleUniformMin: 1
        scaleUniformMax: 1.5
        scalePerAxisMin: {x: 1, y: 1, z: 1}
        scalePerAxisMax: {x: 1, y: 1, z: 1}
        pinFixedRotation: 0
        pinFixedRotationValue: {x: 0, y: 0, z: 0}
        pinFixedScale: 0
        pinFixedScaleValue: {x: 1, y: 1, z: 1}
        placeEulerAngles: {x: 0, y: 0, z: 0}
        placeScale: 1
        throwRadius: 5
        throwSpacing: 2
        throwHeight: 5
        throwVelocity: 0
        throwRandomRotation: {x: 0, y: 0, z: 0}
        throwScaleMinMax: {x: 1, y: 1}
        multibrushEnabled: 0
        multibrushSlots:
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        multibrushPaintSelectedSlot: 0
        multibrushMode: 0
        multibrushPattern: 0 1 2 3 5 6 7 8 9
        multibrushPatternContinue: 0
        slopeEnabled: 1
        slopeAngleMin: 0
        slopeAngleMax: 35
        slopeVector: 1
        slopeVectorCustom: {x: 0, y: 1, z: 0}
        slopeVectorFlip: 0
        gridEnabled: 0
        gridOrigin: {x: 0, y: 0}
        gridStep: {x: 5, y: 5}
        gridPlane: 1
        gridNormal: {x: 0, y: 1, z: 0}
        gridAngle: 0
      prefabSlots:
      - gameObject: {fileID: 112622, guid: 061faed207d7a4b52b09b4f68119bae4, type: 3}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      selectedSlot: 0
      selected: 0
      colorTag: 0
      customPreviewImage: {fileID: 0}
    - name: powerup
      settings:
        brushRadius: 0.01
        brushSpacing: 1
        brushOverlapCheckMode: 0
        brushOverlapDistance: 0.5
        brushOverlapCheckObjects: 1
        brushOverlapCheckLayers:
          serializedVersion: 2
          m_Bits: 0
        surfaceOffset: 0.5
        orientationTransformMode: 0
        orientationMode: 2
        alongBrushStroke: 0
        rotation: {x: 0, y: 0, z: 0}
        randomizeOrientationX: 0
        randomizeOrientationY: 0
        randomizeOrientationZ: 0
        scaleTransformMode: 0
        scaleMode: 0
        scaleUniformMin: 1
        scaleUniformMax: 1
        scalePerAxisMin: {x: 1, y: 1, z: 1}
        scalePerAxisMax: {x: 1, y: 1, z: 1}
        pinFixedRotation: 0
        pinFixedRotationValue: {x: 0, y: 0, z: 0}
        pinFixedScale: 0
        pinFixedScaleValue: {x: 1, y: 1, z: 1}
        placeEulerAngles: {x: 0, y: 0, z: 0}
        placeScale: 1
        throwRadius: 5
        throwSpacing: 2
        throwHeight: 5
        throwVelocity: 0
        throwRandomRotation: {x: 47.3, y: 61.3, z: 62}
        throwScaleMinMax: {x: 1, y: 1}
        multibrushEnabled: 0
        multibrushSlots:
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        multibrushPaintSelectedSlot: 0
        multibrushMode: 0
        multibrushPattern: 0 1 2 3 5 6 7 8 9
        multibrushPatternContinue: 0
        slopeEnabled: 0
        slopeAngleMin: 0
        slopeAngleMax: 35
        slopeVector: 1
        slopeVectorCustom: {x: 0, y: 1, z: 0}
        slopeVectorFlip: 0
        gridEnabled: 1
        gridOrigin: {x: 0, y: 0}
        gridStep: {x: 1, y: 1}
        gridPlane: 1
        gridNormal: {x: 0, y: 1, z: 0}
        gridAngle: 0
      prefabSlots:
      - gameObject: {fileID: 101508, guid: 15f8621e4067f419dafbbb40b80416b5, type: 3}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      selectedSlot: 0
      selected: 0
      colorTag: 0
      customPreviewImage: {fileID: 0}
    levelsGamma: 1
    levelsInBlack: 0
    levelsInWhite: 255
    levelsOutBlack: 0
    levelsOutWhite: 255
  - name: Throw
    brushes:
    - name: stones random
      settings:
        brushRadius: 5
        brushSpacing: 2
        brushOverlapCheckMode: 0
        brushOverlapDistance: 0.5
        brushOverlapCheckObjects: 1
        brushOverlapCheckLayers:
          serializedVersion: 2
          m_Bits: 0
        surfaceOffset: 0
        orientationTransformMode: 0
        orientationMode: 2
        alongBrushStroke: 0
        rotation: {x: 0, y: 0, z: 0}
        randomizeOrientationX: 100
        randomizeOrientationY: 100
        randomizeOrientationZ: 100
        scaleTransformMode: 0
        scaleMode: 0
        scaleUniformMin: 0.5
        scaleUniformMax: 2
        scalePerAxisMin: {x: 1, y: 1, z: 1}
        scalePerAxisMax: {x: 1, y: 1, z: 1}
        pinFixedRotation: 0
        pinFixedRotationValue: {x: 0, y: 0, z: 0}
        pinFixedScale: 0
        pinFixedScaleValue: {x: 1, y: 1, z: 1}
        placeEulerAngles: {x: 0, y: 0, z: 0}
        placeScale: 1
        throwRadius: 5
        throwSpacing: 2
        throwHeight: 5
        throwVelocity: 0
        throwRandomRotation: {x: 33, y: 44.2, z: 85.8}
        throwScaleMinMax: {x: 1, y: 1}
        multibrushEnabled: 1
        multibrushSlots:
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 0, y: 0, z: 0}
        multibrushPaintSelectedSlot: 0
        multibrushMode: 0
        multibrushPattern: 0 1 2 3 5 6 7 8 9
        multibrushPatternContinue: 0
        slopeEnabled: 1
        slopeAngleMin: 0
        slopeAngleMax: 35
        slopeVector: 1
        slopeVectorCustom: {x: 0, y: 1, z: 0}
        slopeVectorFlip: 0
        gridEnabled: 0
        gridOrigin: {x: 0, y: 0}
        gridStep: {x: 5, y: 5}
        gridPlane: 1
        gridNormal: {x: 0, y: 1, z: 0}
        gridAngle: 0
      prefabSlots:
      - gameObject: {fileID: 152170, guid: 13209b503b9464a889e6cdcd72c36431, type: 3}
      - gameObject: {fileID: 171914, guid: 84a4a2a0b8dfe40e289fd2d4812d4019, type: 3}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      selectedSlot: 0
      selected: 0
      colorTag: 0
      customPreviewImage: {fileID: 0}
    - name: box
      settings:
        brushRadius: 1
        brushSpacing: 0.5
        brushOverlapCheckMode: 2
        brushOverlapDistance: 0.5
        brushOverlapCheckObjects: 0
        brushOverlapCheckLayers:
          serializedVersion: 2
          m_Bits: 0
        surfaceOffset: 0
        orientationTransformMode: 0
        orientationMode: 0
        alongBrushStroke: 0
        rotation: {x: 0, y: 0, z: 0}
        randomizeOrientationX: 2.6
        randomizeOrientationY: 100
        randomizeOrientationZ: 3.7
        scaleTransformMode: 0
        scaleMode: 0
        scaleUniformMin: 1
        scaleUniformMax: 1
        scalePerAxisMin: {x: 1, y: 1, z: 1}
        scalePerAxisMax: {x: 1, y: 1, z: 1}
        pinFixedRotation: 0
        pinFixedRotationValue: {x: 0, y: 0, z: 0}
        pinFixedScale: 0
        pinFixedScaleValue: {x: 1, y: 1, z: 1}
        placeEulerAngles: {x: 0, y: -60, z: 0}
        placeScale: 1
        throwRadius: 1.32
        throwSpacing: 0.68
        throwHeight: 3.65
        throwVelocity: 0
        throwRandomRotation: {x: 43.3, y: 27.6, z: 42.7}
        throwScaleMinMax: {x: 1, y: 1}
        multibrushEnabled: 0
        multibrushSlots:
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        multibrushPaintSelectedSlot: 0
        multibrushMode: 0
        multibrushPattern: 0 1 2 3 5 6 7 8 9
        multibrushPatternContinue: 0
        slopeEnabled: 0
        slopeAngleMin: 0
        slopeAngleMax: 35
        slopeVector: 1
        slopeVectorCustom: {x: 0, y: 1, z: 0}
        slopeVectorFlip: 0
        gridEnabled: 0
        gridOrigin: {x: 0, y: 0}
        gridStep: {x: 5, y: 5}
        gridPlane: 1
        gridNormal: {x: 0, y: 1, z: 0}
        gridAngle: 0
      prefabSlots:
      - gameObject: {fileID: 3995375172139233161, guid: 2775c26e84b054356801677a43337ef5, type: 3}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      selectedSlot: 0
      selected: 0
      colorTag: 0
      customPreviewImage: {fileID: 0}
    - name: cans
      settings:
        brushRadius: 1
        brushSpacing: 0.5
        brushOverlapCheckMode: 0
        brushOverlapDistance: 0.5
        brushOverlapCheckObjects: 1
        brushOverlapCheckLayers:
          serializedVersion: 2
          m_Bits: 0
        surfaceOffset: 0
        orientationTransformMode: 0
        orientationMode: 0
        alongBrushStroke: 0
        rotation: {x: 0, y: 0, z: 0}
        randomizeOrientationX: 100
        randomizeOrientationY: 100
        randomizeOrientationZ: 100
        scaleTransformMode: 0
        scaleMode: 0
        scaleUniformMin: 1
        scaleUniformMax: 1
        scalePerAxisMin: {x: 1, y: 1, z: 1}
        scalePerAxisMax: {x: 1, y: 1, z: 1}
        pinFixedRotation: 0
        pinFixedRotationValue: {x: 0, y: 0, z: 0}
        pinFixedScale: 0
        pinFixedScaleValue: {x: 1, y: 1, z: 1}
        placeEulerAngles: {x: 0, y: 0, z: 0}
        placeScale: 1
        throwRadius: 1
        throwSpacing: 1
        throwHeight: 3
        throwVelocity: 1
        throwRandomRotation: {x: 100, y: 100, z: 100}
        throwScaleMinMax: {x: 1, y: 1}
        multibrushEnabled: 1
        multibrushSlots:
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        multibrushPaintSelectedSlot: 0
        multibrushMode: 0
        multibrushPattern: 0 1 2 3 5 6 7 8 9
        multibrushPatternContinue: 0
        slopeEnabled: 0
        slopeAngleMin: 0
        slopeAngleMax: 35
        slopeVector: 1
        slopeVectorCustom: {x: 0, y: 1, z: 0}
        slopeVectorFlip: 0
        gridEnabled: 0
        gridOrigin: {x: 0, y: 0}
        gridStep: {x: 5, y: 5}
        gridPlane: 1
        gridNormal: {x: 0, y: 1, z: 0}
        gridAngle: 0
      prefabSlots:
      - gameObject: {fileID: 5121792053650911359, guid: 2bf9cf2ab57374cc0bd173227f593e11, type: 3}
      - gameObject: {fileID: 1500413753295065714, guid: 21931cfe3cae34bd9a826707883dbdce, type: 3}
      - gameObject: {fileID: 1732320645968458946, guid: 9b29c018d8b234e2c8468c8ee78f3f18, type: 3}
      - gameObject: {fileID: 4244195985474975456, guid: cd9a0ed516cdd4b76a0070280139496d, type: 3}
      - gameObject: {fileID: 6823240390559314668, guid: e51a37bcc81e8447ba329ac52a59897a, type: 3}
      - gameObject: {fileID: 6220326741828520350, guid: 5fe35a31ea3e54d81b646b823679426f, type: 3}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      selectedSlot: 0
      selected: 0
      colorTag: 0
      customPreviewImage: {fileID: 0}
    - name: boxwcans
      settings:
        brushRadius: 1
        brushSpacing: 0.5
        brushOverlapCheckMode: 0
        brushOverlapDistance: 0.5
        brushOverlapCheckObjects: 1
        brushOverlapCheckLayers:
          serializedVersion: 2
          m_Bits: 0
        surfaceOffset: 0
        orientationTransformMode: 0
        orientationMode: 0
        alongBrushStroke: 0
        rotation: {x: 0, y: 0, z: 0}
        randomizeOrientationX: 3.9
        randomizeOrientationY: 100
        randomizeOrientationZ: 5.8
        scaleTransformMode: 0
        scaleMode: 0
        scaleUniformMin: 1
        scaleUniformMax: 1
        scalePerAxisMin: {x: 1, y: 1, z: 1}
        scalePerAxisMax: {x: 1, y: 1, z: 1}
        pinFixedRotation: 0
        pinFixedRotationValue: {x: 0, y: 0, z: 0}
        pinFixedScale: 0
        pinFixedScaleValue: {x: 1, y: 1, z: 1}
        placeEulerAngles: {x: 0, y: 60, z: 0}
        placeScale: 1
        throwRadius: 1.9371026
        throwSpacing: 2
        throwHeight: 3.63
        throwVelocity: 0
        throwRandomRotation: {x: 100, y: 100, z: 100}
        throwScaleMinMax: {x: 1, y: 1}
        multibrushEnabled: 0
        multibrushSlots:
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        multibrushPaintSelectedSlot: 0
        multibrushMode: 0
        multibrushPattern: 0 1 2 3 5 6 7 8 9
        multibrushPatternContinue: 0
        slopeEnabled: 0
        slopeAngleMin: 0
        slopeAngleMax: 35
        slopeVector: 1
        slopeVectorCustom: {x: 0, y: 1, z: 0}
        slopeVectorFlip: 0
        gridEnabled: 0
        gridOrigin: {x: 0, y: 0}
        gridStep: {x: 5, y: 5}
        gridPlane: 1
        gridNormal: {x: 0, y: 1, z: 0}
        gridAngle: 0
      prefabSlots:
      - gameObject: {fileID: 6461832190177709707, guid: b1a7023ae95504990b85830956b5db29, type: 3}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      selectedSlot: 0
      selected: 0
      colorTag: 0
      customPreviewImage: {fileID: 0}
    - name: books
      settings:
        brushRadius: 1
        brushSpacing: 0.5
        brushOverlapCheckMode: 0
        brushOverlapDistance: 0.5
        brushOverlapCheckObjects: 1
        brushOverlapCheckLayers:
          serializedVersion: 2
          m_Bits: 0
        surfaceOffset: 0
        orientationTransformMode: 0
        orientationMode: 0
        alongBrushStroke: 0
        rotation: {x: 0, y: 0, z: 0}
        randomizeOrientationX: 1.9
        randomizeOrientationY: 100
        randomizeOrientationZ: 2.2
        scaleTransformMode: 0
        scaleMode: 0
        scaleUniformMin: 1
        scaleUniformMax: 1
        scalePerAxisMin: {x: 1, y: 1, z: 1}
        scalePerAxisMax: {x: 1, y: 1, z: 1}
        pinFixedRotation: 0
        pinFixedRotationValue: {x: 0, y: 0, z: 0}
        pinFixedScale: 0
        pinFixedScaleValue: {x: 1, y: 1, z: 1}
        placeEulerAngles: {x: 0, y: 0, z: 0}
        placeScale: 1
        throwRadius: 1
        throwSpacing: 1
        throwHeight: 3
        throwVelocity: 1
        throwRandomRotation: {x: 100, y: 100, z: 100}
        throwScaleMinMax: {x: 1, y: 1}
        multibrushEnabled: 1
        multibrushSlots:
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        multibrushPaintSelectedSlot: 0
        multibrushMode: 0
        multibrushPattern: 0 1 2 3 5 6 7 8 9
        multibrushPatternContinue: 0
        slopeEnabled: 0
        slopeAngleMin: 0
        slopeAngleMax: 35
        slopeVector: 1
        slopeVectorCustom: {x: 0, y: 1, z: 0}
        slopeVectorFlip: 0
        gridEnabled: 0
        gridOrigin: {x: 0, y: 0}
        gridStep: {x: 5, y: 5}
        gridPlane: 1
        gridNormal: {x: 0, y: 1, z: 0}
        gridAngle: 0
      prefabSlots:
      - gameObject: {fileID: 85133633994234828, guid: 80bd617a1597f4350b98c07b85d60a05, type: 3}
      - gameObject: {fileID: 7337436880604310812, guid: aae38cd2299054ce9a800d5aadc0e100, type: 3}
      - gameObject: {fileID: 29270360122064922, guid: 8f1f768a561714ad09e362c746ee7d95, type: 3}
      - gameObject: {fileID: 2044340557379106779, guid: a2fa6ff3c15d6494c9050e9e701060c1, type: 3}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      selectedSlot: 3
      selected: 0
      colorTag: 0
      customPreviewImage: {fileID: 0}
    - name: Prefab Sphere
      settings:
        brushRadius: 1
        brushSpacing: 0.5
        brushOverlapCheckMode: 0
        brushOverlapDistance: 0.5
        brushOverlapCheckObjects: 1
        brushOverlapCheckLayers:
          serializedVersion: 2
          m_Bits: 0
        surfaceOffset: 0
        orientationTransformMode: 0
        orientationMode: 0
        alongBrushStroke: 0
        rotation: {x: 0, y: 0, z: 0}
        randomizeOrientationX: 0
        randomizeOrientationY: 0
        randomizeOrientationZ: 0
        scaleTransformMode: 1
        scaleMode: 0
        scaleUniformMin: 8
        scaleUniformMax: 8
        scalePerAxisMin: {x: 1, y: 1, z: 1}
        scalePerAxisMax: {x: 1, y: 1, z: 1}
        pinFixedRotation: 0
        pinFixedRotationValue: {x: 0, y: 0, z: 0}
        pinFixedScale: 0
        pinFixedScaleValue: {x: 1, y: 1, z: 1}
        placeEulerAngles: {x: 0, y: 0, z: 0}
        placeScale: 1
        throwRadius: 1
        throwSpacing: 1
        throwHeight: 3
        throwVelocity: 1
        throwRandomRotation: {x: 100, y: 100, z: 100}
        throwScaleMinMax: {x: 1, y: 1}
        multibrushEnabled: 0
        multibrushSlots:
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        multibrushPaintSelectedSlot: 0
        multibrushMode: 0
        multibrushPattern: 0 1 2 3 5 6 7 8 9
        multibrushPatternContinue: 0
        slopeEnabled: 0
        slopeAngleMin: 0
        slopeAngleMax: 35
        slopeVector: 1
        slopeVectorCustom: {x: 0, y: 1, z: 0}
        slopeVectorFlip: 0
        gridEnabled: 0
        gridOrigin: {x: 0, y: 0}
        gridStep: {x: 5, y: 5}
        gridPlane: 1
        gridNormal: {x: 0, y: 1, z: 0}
        gridAngle: 0
      prefabSlots:
      - gameObject: {fileID: 7263691018866096393, guid: d4140d5bb823a3444be9a95e5054ac2b, type: 3}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      selectedSlot: 0
      selected: 0
      colorTag: 0
      customPreviewImage: {fileID: 0}
    - name: Spawn Point
      settings:
        brushRadius: 1
        brushSpacing: 0.5
        brushOverlapCheckMode: 0
        brushOverlapDistance: 0.5
        brushOverlapCheckObjects: 1
        brushOverlapCheckLayers:
          serializedVersion: 2
          m_Bits: 0
        surfaceOffset: 0
        orientationTransformMode: 0
        orientationMode: 0
        alongBrushStroke: 0
        rotation: {x: 0, y: 0, z: 0}
        randomizeOrientationX: 0
        randomizeOrientationY: 0
        randomizeOrientationZ: 0
        scaleTransformMode: 0
        scaleMode: 0
        scaleUniformMin: 1
        scaleUniformMax: 1
        scalePerAxisMin: {x: 1, y: 1, z: 1}
        scalePerAxisMax: {x: 1, y: 1, z: 1}
        pinFixedRotation: 0
        pinFixedRotationValue: {x: 0, y: 0, z: 0}
        pinFixedScale: 0
        pinFixedScaleValue: {x: 1, y: 1, z: 1}
        placeEulerAngles: {x: 0, y: 0, z: 0}
        placeScale: 1
        throwRadius: 1
        throwSpacing: 1
        throwHeight: 3
        throwVelocity: 1
        throwRandomRotation: {x: 100, y: 100, z: 100}
        throwScaleMinMax: {x: 1, y: 1}
        multibrushEnabled: 0
        multibrushSlots:
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        multibrushPaintSelectedSlot: 0
        multibrushMode: 0
        multibrushPattern: 0 1 2 3 5 6 7 8 9
        multibrushPatternContinue: 0
        slopeEnabled: 0
        slopeAngleMin: 0
        slopeAngleMax: 35
        slopeVector: 1
        slopeVectorCustom: {x: 0, y: 1, z: 0}
        slopeVectorFlip: 0
        gridEnabled: 0
        gridOrigin: {x: 0, y: 0}
        gridStep: {x: 5, y: 5}
        gridPlane: 1
        gridNormal: {x: 0, y: 1, z: 0}
        gridAngle: 0
      prefabSlots:
      - gameObject: {fileID: 529268341918681579, guid: bc34a6882135ebb4a839926a70286baa, type: 3}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      selectedSlot: 0
      selected: 0
      colorTag: 0
      customPreviewImage: {fileID: 0}
    - name: Snake Eyeball
      settings:
        brushRadius: 1
        brushSpacing: 0.5
        brushOverlapCheckMode: 0
        brushOverlapDistance: 0.5
        brushOverlapCheckObjects: 1
        brushOverlapCheckLayers:
          serializedVersion: 2
          m_Bits: 0
        surfaceOffset: 0
        orientationTransformMode: 0
        orientationMode: 0
        alongBrushStroke: 0
        rotation: {x: 270, y: 90, z: 90}
        randomizeOrientationX: 0
        randomizeOrientationY: 0
        randomizeOrientationZ: 0
        scaleTransformMode: 1
        scaleMode: 0
        scaleUniformMin: 100
        scaleUniformMax: 100
        scalePerAxisMin: {x: 1, y: 1, z: 1}
        scalePerAxisMax: {x: 1, y: 1, z: 1}
        pinFixedRotation: 0
        pinFixedRotationValue: {x: 0, y: 0, z: 0}
        pinFixedScale: 0
        pinFixedScaleValue: {x: 1, y: 1, z: 1}
        placeEulerAngles: {x: 0, y: 0, z: 0}
        placeScale: 1
        throwRadius: 1
        throwSpacing: 1
        throwHeight: 3
        throwVelocity: 1
        throwRandomRotation: {x: 100, y: 100, z: 100}
        throwScaleMinMax: {x: 1, y: 1}
        multibrushEnabled: 0
        multibrushSlots:
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        - enabled: 1
          raiting: 1
          position: {x: 0, y: 0, z: 0}
          rotation: {x: 0, y: 0, z: 0}
          scale: {x: 1, y: 1, z: 1}
          pivotMode: 0
          pivotOffset: {x: 1, y: 1, z: 1}
        multibrushPaintSelectedSlot: 0
        multibrushMode: 0
        multibrushPattern: 0 1 2 3 5 6 7 8 9
        multibrushPatternContinue: 0
        slopeEnabled: 0
        slopeAngleMin: 0
        slopeAngleMax: 35
        slopeVector: 1
        slopeVectorCustom: {x: 0, y: 1, z: 0}
        slopeVectorFlip: 0
        gridEnabled: 0
        gridOrigin: {x: 0, y: 0}
        gridStep: {x: 5, y: 5}
        gridPlane: 1
        gridNormal: {x: 0, y: 1, z: 0}
        gridAngle: 0
      prefabSlots:
      - gameObject: {fileID: 8018552758275487368, guid: b8e9c33ed8574b44ea155063737e471e, type: 3}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      - gameObject: {fileID: 0}
      selectedSlot: 0
      selected: 1
      colorTag: 0
      customPreviewImage: {fileID: 0}
    levelsGamma: 1
    levelsInBlack: 0
    levelsInWhite: 255
    levelsOutBlack: 0
    levelsOutWhite: 255
  activeTab: 3
  paintOnSelected: 0
  paintLayers:
    serializedVersion: 2
    m_Bits: 8
  ignoreLayers:
    serializedVersion: 2
    m_Bits: 33
  placeUnder: 2
  overwritePrefabLayer: 1
  prefabPlaceLayer: 0
  pinSnapRotationValue: 15
  pinSnapScaleValue: 0.1
  pinSnapRotation: 0
  pinSnapScale: 0
  placeAngleStep: 15
  placeScaleStep: 0.1
  eraseBrushRadius: 5
  eraseByLayer: 1
  eraseLayers:
    serializedVersion: 2
    m_Bits: 32
  selectBrushRadius: 5
  selectByLayer: 0
  selectLayers:
    serializedVersion: 2
    m_Bits: 32
  modifyBrushRadius: 5
  modifyStrength: 1
  modifyByLayer: 0
  modifyLayers:
    serializedVersion: 2
    m_Bits: 32
  modifyPivotMode: 3
  modifyRandomRotation: 0
  modifyRandomRotationValues: {x: 0, y: 0, z: 0}
  modifyRotationValues: {x: 0, y: 0, z: 0}
  modifyScale: 0
  modifyScaleRandomize: 0
  orientRotation: {x: 0, y: 0, z: 0}
  orientLockUp: 1
  orientPivotMode: 3
  orientSameDirection: 0
  orientFlipDirection: 0
  moveSurfaceOffset: 0
  movePivotMode: 3
  moveLockUp: 0
  moveOrientationMode: 0
  maxBrushRadius: 20
  maxBrushSpacing: 5
  surfaceCoords: 0
  hideSceneSettingsObject: 1
  groupPrefabs: 1
  groupName: _group
  gridRaycastHeight: 5
  handlesColor: {r: 1, g: 0, b: 0, a: 1}
  useAdditionalVertexStreams: 0
  enableToolsShortcuts: 0
  disableUndo: 0
  throwPhysicsTimeStep: 0.02
