%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Enemy Dodeca Ty Shader 2
  m_Shader: {fileID: 4800000, guid: 68c389d2ae68ea0418f652fdf33a54d2, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords:
  - DR_GRADIENT_ON
  - DR_LIGHT_ATTENUATION
  - _DETAILMAPBLENDINGMODE_MULTIPLY
  m_InvalidKeywords:
  - DR_DEPTH_EDGE_ON
  - DR_GEOMETRIC_SPECULAR_ON
  - DR_NORMAL_EDGE_ON
  - _ALPHAMODULATE_ON
  - _SPECULAR_COLOR
  - _SURFACE_TYPE_TRANSPARENT
  - _TEXTUREBLENDINGMODE_ADD
  m_LightmapFlags: 6
  m_EnableInstancingVariants: 1
  m_DoubleSidedGI: 1
  m_CustomRenderQueue: 3000
  stringTagMap:
    RenderType: Transparent
  disabledShaderPasses:
  - DepthOnly
  - SHADOWCASTER
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BakedGIRamp:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BaseMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 200, y: 200}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 20, y: 20}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 73.63, y: 62.5}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _GradientRamp:
        m_Texture: {fileID: 4922915278164763026, guid: 4d1a016a026db1e429a82e6a70ce7678, type: 2}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AlphaClip: 0
    - _Blend: 0
    - _BumpScale: 1
    - _CameraDistanceImpact: 0.415
    - _ClearCoatMask: 0
    - _ClearCoatSmoothness: 0
    - _ContrastEnhancement: 1
    - _Cull: 3.05
    - _Cutoff: 0
    - _DepthEdgeEnabled: 1
    - _DepthEdgeStrength: 0.2
    - _DepthThreshold: 0
    - _DetailAlbedoMapScale: 1
    - _DetailMapBlendingMode: 0
    - _DetailMapImpact: 1
    - _DetailNormalMapScale: 1
    - _DstBlend: 10
    - _EdgeGradientIntensity: 0.609
    - _EdgeGradientOffset: 0.251
    - _EdgeGradientPower: 4.2
    - _EdgePower: 1
    - _EdgeSharpness: 1
    - _EdgeStrength: 0
    - _EdgeThreshold: 0
    - _EmissionPower: 1
    - _EnvironmentReflections: 1
    - _FlatRimEdgeSmoothness: 0.5
    - _FlatRimLightAlign: 0.12
    - _FlatRimSize: 0.5
    - _FlatSpecularEdgeSmoothness: 0.721
    - _FlatSpecularSize: 0.585
    - _GeometricSpecularAA: 1
    - _GeometricSpecularIntensity: 0.765
    - _GlossMapScale: 0
    - _Glossiness: 0
    - _GlossyReflections: 0
    - _GradientAngle: 99
    - _GradientCenterX: 0
    - _GradientCenterY: 0
    - _GradientEnabled: 1
    - _GradientSize: 0.34
    - _LightContribution: 0.85
    - _LightmapDirectionPitch: 0
    - _LightmapDirectionYaw: 0
    - _Metallic: 0
    - _NormalEdgeEnabled: 1
    - _NormalEdgeStrength: 0
    - _OcclusionStrength: 1
    - _OutlineDepthOffset: 0.492
    - _OutlineEnabled: 0
    - _OutlineScale: 1.11
    - _OutlineWidth: 3.06
    - _OverrideBakedGi: 0
    - _OverrideLightAttenuation: 1
    - _OverrideLightmapDir: 0
    - _Parallax: 0.005
    - _QueueOffset: 0
    - _ReceiveShadows: 1
    - _RimEnabled: 0
    - _RimSize: 0.303
    - _RimSmoothness: 0.509
    - _SelfShadingSize: 1
    - _ShadowSharpness: 1
    - _Smoothness: 0.5
    - _SmoothnessSource: 0
    - _SmoothnessTextureChannel: 0
    - _SpecHighlights: 1
    - _SpecularEnabled: 0
    - _SpecularHighlights: 1
    - _SpecularSize: 0.546
    - _SpecularSmoothness: 0
    - _SrcBlend: 5
    - _StencilID: 10
    - _Surface: 0
    - _TextureBlendingMode: 1
    - _TextureImpact: 0
    - _UnityShadowOcclusion: 1
    - _VertexColorsEnabled: 0
    - _WorkflowMode: 1
    - _ZWrite: 0
    m_Colors:
    - _BaseColor: {r: 5.9921575, g: 5.9921575, b: 5.9921575, a: 1}
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _ColorGradient: {r: 16.94838, g: 0, b: 7.8490195, a: 1}
    - _DetailMapColor: {r: 0.7169812, g: 0, b: 0.14937085, a: 1}
    - _EdgeGradientColor: {r: 0.8, g: 0.8, b: 0.8, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _FlatRimColor: {r: 0.85023, g: 0.85034, b: 0.85045, a: 0.85056}
    - _FlatSpecularColor: {r: 3.4357, g: 3.4357, b: 3.4357, a: 0.85056}
    - _LightAttenuation: {r: 0.42463386, g: 1, b: 0, a: 0}
    - _LightmapDirection: {r: 0, g: 1, b: 0, a: 0}
    - _OutlineColor: {r: 11.984357, g: 11.984357, b: 11.984357, a: 0.87058824}
    - _RimColor: {r: 1, g: 1, b: 1, a: 1}
    - _ShadowColor: {r: 0, g: 0, b: 0, a: 1}
    - _SpecColor: {r: 0.19999996, g: 0.19999996, b: 0.19999996, a: 0.5}
    - _SpecularColor: {r: 72.659294, g: 72.659294, b: 72.659294, a: 1}
  m_BuildTextureStacks: []
  m_AllowLocking: 1
