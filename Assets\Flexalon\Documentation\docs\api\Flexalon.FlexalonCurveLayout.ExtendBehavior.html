﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Enum FlexalonCurveLayout.ExtendBehavior
 | Flexalon 3D Layouts </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Enum FlexalonCurveLayout.ExtendBehavior
 | Flexalon 3D Layouts ">
    <meta name="generator" content="docfx ">
  
    <link rel="shortcut icon" href="images/favicon.svg">
    <link rel="stylesheet" href="styles/docfx.vendor.css">
    <link rel="stylesheet" href="styles/docfx.css">
    <link rel="stylesheet" href="styles/main.css">
    <meta property="docfx:navrel" content="tochead.html">
    <meta property="docfx:tocrel" content="toc.html">
  
  <meta property="docfx:rel" content="">
  
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="https://www.flexalon.com/">
                <img id="logo" class="svg" src="images/logo_32x32.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first="First" data-prev="Previous" data-next="Next" data-last="Last"></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">

        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="Flexalon.FlexalonCurveLayout.ExtendBehavior">


  <h1 id="Flexalon_FlexalonCurveLayout_ExtendBehavior" data-uid="Flexalon.FlexalonCurveLayout.ExtendBehavior" class="text-break">Enum FlexalonCurveLayout.ExtendBehavior
</h1>
  <div class="markdown level0 summary"><p>Determines how the curve is extended before the beginning and after the end.</p>
</div>
  <div class="markdown level0 conceptual"></div>
  <h5 id="Flexalon_FlexalonCurveLayout_ExtendBehavior_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public enum FlexalonCurveLayout.ExtendBehavior</code></pre>
  </div>
  <h3 id="fields">Fields
</h3>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    <thead>
    <tbody>
      <tr>
        <td id="Flexalon_FlexalonCurveLayout_ExtendBehavior_ExtendLine">ExtendLine</td>
        <td><p>Extend the curve in a straight line based on the tangent at the start/end of the curve.</p>
</td>
      </tr>
      <tr>
        <td id="Flexalon_FlexalonCurveLayout_ExtendBehavior_PingPong">PingPong</td>
        <td><p>Extend the curve by continuing in the opposite direction.</p>
</td>
      </tr>
      <tr>
        <td id="Flexalon_FlexalonCurveLayout_ExtendBehavior_Repeat">Repeat</td>
        <td><p>Extend the curve by repeating the curve.</p>
</td>
      </tr>
      <tr>
        <td id="Flexalon_FlexalonCurveLayout_ExtendBehavior_RepeatMirror">RepeatMirror</td>
        <td><p>Extend the curve by mirroring the curve and repeating it.</p>
</td>
      </tr>
      <tr>
        <td id="Flexalon_FlexalonCurveLayout_ExtendBehavior_Stop">Stop</td>
        <td><p>Do not extend the curve. All objects before the beginning are placed at the start, and all objects after the end are placed at the end.</p>
</td>
      </tr>
    </tbody>
  </thead></thead></table>
</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In This Article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      Flexalon 3D Layouts
      
          </div>
        </div>
      </footer>
    </div>

    
    
    
  </body>
</html>
