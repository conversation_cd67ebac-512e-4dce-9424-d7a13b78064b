%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Outline Toon Style 1
  m_Shader: {fileID: 4800000, guid: f90fb708fac042ccb3f191802bed7d4c, type: 3}
  m_ValidKeywords: []
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: 359c4962c0a3407eb969c1d9eeb6426b, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Normal:
        m_Texture: {fileID: 2800000, guid: b36d82dce1944357b33e03f7e2975b06, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TextureSample0:
        m_Texture: {fileID: 2800000, guid: e70a4cc9a27a530468623a76c6c025fe, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TextureSample1:
        m_Texture: {fileID: 2800000, guid: baa87608a082eff4a8c5c1aa7c5bb623, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _texcoord:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AlphaCutoff: 0.5
    - _Dimming: 0.5
    - _DistanceCutoff: 20
    - _Float0: 166.4001
    - _Float1: 3.348565
    - _Float2: 1
    - _Float3: 0.5
    - _Float4: 0.69
    - _Keyword0: 0
    - _NormalScale: 0.486
    - _OutlineWidth: 0.0018
    - _QueueControl: 0
    - _QueueOffset: 0
    - _RimOffset: 0.5
    - _RimOffset1: 0.7
    - _RimShadow: 0.491
    - __dirty: 0
    m_Colors:
    - _Color0: {r: 1, g: 1, b: 1, a: 0}
    - _EmissionColor: {r: 1, g: 1, b: 1, a: 1}
    - _RimColor: {r: 0, g: 0.16342613, b: 1, a: 0}
    - _RimFalloff: {r: 0, g: 0.4, b: 0, a: 0}
    - _Vector0: {r: 0, g: 0.4, b: 0, a: 0}
    - _WireColor: {r: 0.9733328, g: 1, b: 0, a: 0}
  m_BuildTextureStacks: []
