%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: UV Light Reveal Transparent
  m_Shader: {fileID: 4800000, guid: a10106f848d442ccb275805c46abc035, type: 3}
  m_ValidKeywords: []
  m_InvalidKeywords: []
  m_LightmapFlags: 2
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BaseTexture:
        m_Texture: {fileID: 2800000, guid: b099462194604bd2957f0114083b8cf0, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _UVTexture:
        m_Texture: {fileID: 2800000, guid: d34037cb12979c143b3aa73646ec9f06, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _texcoord:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AlphaCutoff: 0.5
    - _AlphaCutoffBias: 0.583
    - _Cull: 2
    - _Cutoff: 0.15
    - _DifferenceThreshold: 0.243
    - _EnvironmentReflections: 1
    - _MaskClipValue: 0.3
    - _QueueControl: 0
    - _QueueOffset: 0
    - _ReceiveShadows: 1
    - _SpecularHighlights: 1
    - _UVLight00Brightness: 1
    - _UVLight00ThresholdTemp: 0.337
    - _UVLightThreshold: 0.303
    - __dirty: 0
    m_Colors:
    - _ColortoBeFiltered: {r: 0.4595611, g: 1, b: 0, a: 1}
    - _EmissionColor: {r: 1, g: 1, b: 1, a: 1}
    - _EmissiveColor: {r: 0.5254902, g: 0.02745098, b: 0, a: 0}
    - _UVLightFilteredColor: {r: 0.40392157, g: 0, b: 1, a: 1}
    - _Vector0: {r: 0, g: 0, b: 0, a: 0}
  m_BuildTextureStacks: []
