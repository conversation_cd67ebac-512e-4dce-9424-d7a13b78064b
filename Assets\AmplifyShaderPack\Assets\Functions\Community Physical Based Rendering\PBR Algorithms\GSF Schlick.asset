%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: GSF Schlick
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor v1.9.5.1\n// Available at the
    Unity Asset Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=19501\nNode;AmplifyShaderEditor.CommentaryNode;56;-2301.99,-256;Inherit;False;2568.031;831.2757;Math;29;0;34;37;32;154;155;153;23;45;40;39;150;149;42;43;41;44;49;38;52;35;25;30;36;53;47;46;55;54;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;57;-3423.569,-256;Inherit;False;1056.792;670.4929;Dot
    Products;9;85;122;121;115;114;107;106;151;152;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;51;-2304,-656;Inherit;False;734.0221;337.6666;GSF
    Schlick Node;1;50;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;54;-1200,-176;Inherit;False;190.04;115.0029;NdotV;1;145;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;55;-1200,-48;Inherit;False;190;123;NdotL;1;146;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;22;-2304,-832;Inherit;False;514.3489;156.4844;Credits;;0,0,0,0.5019608;Method
    by Jordan Stevens$Link: https://www.jordanstevenstechart.com/physically-based-rendering$$Node
    Made by Tiber Legacy$Special Thanks to Dawie;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;46;-944,-208;Inherit;False;203.03;100;Vectorized;;0,0,0,0.5019608;to
    simplify and Optimize the Algorithm;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;47;-1168,240;Inherit;False;551.0957;226.9939;Schlick's
    Approximations;;0,0,0,0.5019608;Most of Schlick's Approx.-s mostly modify the
    'Roughness' value and the rest of the Algorithim stays the same.$$Base: \"the
    baseline Schlick Approximation of Smith GSF\" - Jordan Stevens$$Beckman: \"It
    works by multiplying roughness by the square root of 2/PI.\"  - Jordan Stevens$$GGX:
    \"The Schlick Approximation of GGX simply divides our roughness value by two.\" 
    - Jordan Stevens;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;50;-1936,-608;Inherit;False;353.5797;185.5377;;;0,0,0,1;Schlick
    has made several approximations of the Smith GSF that can be applied to other
    Smith GSFs as well - Jordan Stevens$$Schlick Approx: The different Approximations
    of other GSFs by Schlick, see internal notes for details.;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;53;-1600,-16;Inherit;False;196.3697;110.9603;Roughness;;0,0,0,1;;0;0\nNode;AmplifyShaderEditor.DynamicAppendNode;36;-944,-96;Inherit;False;FLOAT2;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.ScaleAndOffsetNode;30;-784,-16;Inherit;False;3;0;FLOAT2;0,0;False;1;FLOAT;1;False;2;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.OneMinusNode;25;-944,16;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;35;-1168,112;Inherit;False;Schlick
    Approx;False;0;3;-1;Base;Beckman;GGX;Object;-1;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;52;-832,128;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;38;-1360,208;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;49;-1360,112;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;145;-1184,-144;Inherit;False;106;NdotV;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;146;-1184,0;Inherit;False;107;NdotL;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;106;-2624,-160;Inherit;True;NdotV;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;107;-2624,64;Inherit;True;NdotL;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;114;-3072,-80;Inherit;False;NdotV;1;2;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;115;-3072,144;Inherit;False;NdotL;1;1;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;121;-2912,-160;Inherit;False;Custom
    Dot Products;True;0;2;1;In 0;In 1;Object;-1;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;122;-2912,64;Inherit;False;Option;False;0;2;1;In
    0;In 1;Instance;121;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;85;-3328,-160;Inherit;False;Normal;3;0;False;1;0;FLOAT3;0,0,1;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleDivideOpNode;44;-1360,304;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SqrtOpNode;41;-1888,288;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleDivideOpNode;43;-2048,288;Inherit;False;2;0;FLOAT;2;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.PiNode;42;-2256,288;Inherit;False;1;0;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.StickyNoteNode;149;-1728,336;Inherit;False;200.8291;191.118;PI
    Inv Approx;;0,0,0,1;Near:$0.797884560802865$$Exact:$sqrt(2 / Pi)$$PI Inv Approx$0.7978846$;0;0\nNode;AmplifyShaderEditor.FunctionInput;150;-1904,384;Inherit;False;PI
    Inv;1;3;False;1;0;FLOAT;0.7978846;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;39;-1728,208;Inherit;False;PI
    Inv Approx;False;0;3;1;Near;Exact;Custom;Object;-1;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;40;-1952,208;Inherit;False;Constant;_2PIAprrox;_2/PI
    Aprrox;0;0;Create;True;0;0;0;False;0;False;0.7978846;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;45;-1504,352;Inherit;False;Constant;_Two;Two;0;0;Create;True;0;0;0;False;0;False;2;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;23;-1600,112;Inherit;False;Roughness;1;4;False;1;0;FLOAT;0.5;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionNode;151;-3136,-160;Inherit;False;NdotV;-1;;3;65be4ec269a534f4e8867af2f999aaa9;4,27,1,377,0,452,0,451,0;3;230;FLOAT3;0,0,1;False;443;FLOAT3;0,0,0;False;415;FLOAT;1E-37;False;1;FLOAT;23\nNode;AmplifyShaderEditor.FunctionNode;152;-3136,64;Inherit;False;NdotL;-1;;4;6d7ee1aa8c79587448475fa2b37affc1;5,27,1,381,0,460,0,461,0,463,0;4;230;FLOAT3;0,0,1;False;462;FLOAT3;0,0,0;False;454;FLOAT3;0,0,0;False;416;FLOAT;1E-37;False;1;FLOAT;111\nNode;AmplifyShaderEditor.RangedFloatNode;153;-704,112;Inherit;False;Constant;_0_1;_0_1;0;0;Create;True;0;0;0;False;0;False;0.1;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.StickyNoteNode;155;-528,80;Inherit;False;160;100;Max
    0.1;;0,0,0,0.5019608;To Prevent NaN in Division and Output;0;0\nNode;AmplifyShaderEditor.SimpleMaxOpNode;154;-528,-16;Inherit;False;2;0;FLOAT2;0,0;False;1;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleDivideOpNode;32;-384,-96;Inherit;False;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.BreakToComponentsNode;37;-256,-96;Inherit;False;FLOAT2;1;0;FLOAT2;0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;34;-128,-96;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;0;32,-96;Inherit;True;True;-1;Out;0;False;1;0;FLOAT;0;False;1;FLOAT;0\nWireConnection;36;0;145;0\nWireConnection;36;1;146;0\nWireConnection;30;0;36;0\nWireConnection;30;1;25;0\nWireConnection;30;2;52;0\nWireConnection;25;0;35;0\nWireConnection;35;0;49;0\nWireConnection;35;1;38;0\nWireConnection;35;2;44;0\nWireConnection;52;0;35;0\nWireConnection;38;0;39;0\nWireConnection;38;1;23;0\nWireConnection;49;0;23;0\nWireConnection;49;1;23;0\nWireConnection;106;0;121;0\nWireConnection;107;0;122;0\nWireConnection;121;0;151;23\nWireConnection;121;1;114;0\nWireConnection;122;0;152;111\nWireConnection;122;1;115;0\nWireConnection;44;0;23;0\nWireConnection;44;1;45;0\nWireConnection;41;0;43;0\nWireConnection;43;1;42;0\nWireConnection;39;0;40;0\nWireConnection;39;1;41;0\nWireConnection;39;2;150;0\nWireConnection;151;230;85;0\nWireConnection;152;230;85;0\nWireConnection;154;0;30;0\nWireConnection;154;1;153;0\nWireConnection;32;0;36;0\nWireConnection;32;1;154;0\nWireConnection;37;0;32;0\nWireConnection;34;0;37;0\nWireConnection;34;1;37;1\nWireConnection;0;0;34;0\nASEEND*/\n//CHKSM=D476C28F1259D1AF5471A480BE4D4F5489909C48"
  m_functionName: 
  m_description: 'GSF Schlick Node

'
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 0
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_customEdited: 0
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 0
  m_headerStyle: 2
  m_headerColor: {r: 0.7529412, g: 0.6784314, b: 0, a: 1}
  m_customNodeCategory: Amplify Shader Pack
  m_previewPosition: 0
  m_hidden: 0
  m_url: 
