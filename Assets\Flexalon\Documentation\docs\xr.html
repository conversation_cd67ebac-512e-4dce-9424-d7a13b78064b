<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<link rel="icon" href="../images/favicon.svg" />
		<link rel="icon" href="../images/favicon.ico" />
		<meta name="viewport" content="width=device-width, height=device-height, initial-scale=1">
		<meta property="og:type" content="website">
		<meta http-equiv="content-security-policy" content="">
		<link href="../_app/immutable/assets/_layout.21aea584.css" rel="stylesheet">
		<link href="../_app/immutable/assets/_layout.0154a58a.css" rel="stylesheet">
		
		
		
		
		
		
		
		
		
		
		
		
		<title>XR Interactions</title><!-- HEAD_svelte-i5aht6_START --><meta property="og:title" content="XR Interactions"><meta name="description" property="og:description" content="Flexalon lets Unity developers arrange objects in 3D layouts that are precise and easy to adjust. Use built-in interactions and animations to create powerful experiences."><meta name="image" property="og:image" content="../images/flexalon-1200x630.png"><meta name="twitter:card" content="summary_large_image"><meta name="twitter:title" content="XR Interactions"><meta name="twitter:site" content="@alon_farchy"><meta name="twitter:description" content="Flexalon lets Unity developers arrange objects in 3D layouts that are precise and easy to adjust. Use built-in interactions and animations to create powerful experiences."><meta name="twitter:image" content="../images/flexalon-1200x630.png"><!-- HEAD_svelte-i5aht6_END -->
	</head>
	<body>
		<div>


<div class="text-white font-body p-4 sticky top-0 bg-black flex gap-5 w-full text-gray-200 z-20 border-b-2 border-[#21a6f0]"><button class="lg:hidden"><svg width="24" height="24"><path d="M5 6h14M5 12h14M5 18h14" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"></path></svg></button>
    <a href="https://www.flexalon.com/" class="font-logo upper text-lg text-[#21a6f0] lg:pl-5 svelte-194nj6y">Flexalon Layouts</a></div>

<div class="w-full max-w-[1600px] lg:pl-10"><div class="flex w-full min-h-full text-zinc-400 relative"><div class="h-full fixed bg-black z-10 max-lg:hidden"><div class="flex flex-col w-[300px] gap-2 border-[#21a6f0] border-r-2 bg-black min-h-0 h-full pt-10 pb-36 overflow-auto max-lg:pl-10"><b class="text-zinc-200">Getting Started</b>
                <a href="../docs.html" class="svelte-194nj6y">Installation</a>
                <a href="../docs/coreConcepts.html" class="svelte-194nj6y">Core Concepts</a>
                <a href="../docs/ui.html" class="svelte-194nj6y">Flexalon UI <span class="ml-2 text-xs font-bold text-pink-300">(New in v4.0!)</span></a>
                <a href="../docs/templates.html" class="svelte-194nj6y">Template Pack</a>
                <a href="../docs/challenges.html" class="svelte-194nj6y">Challenges!</a>

                <a class="text-zinc-200 mt-5 font-bold svelte-194nj6y" href="../docs/flexalonObject.html">Flexalon Objects</a>
                <a href="../docs/flexalonObject.html#size" class="svelte-194nj6y">Width, Height, and Depth</a>
                <a href="../docs/flexalonObject.html#minmax" class="svelte-194nj6y">Min / Max Size<span class="ml-2 text-xs font-bold text-purple-400">(v4.1)</span></a>
                <a href="../docs/flexalonObject.html#margins" class="svelte-194nj6y">Margins and Padding</a>
                <a href="../docs/flexalonObject.html#offset" class="svelte-194nj6y">Offset, Scale, and Rotation</a>

                <b class="text-zinc-200 mt-5">Layouts</b>
                <a href="../docs/flexibleLayout.html" class="svelte-194nj6y">Flexible Layout</a>
                <a href="../docs/gridLayout.html" class="svelte-194nj6y">Grid Layout</a>
                <a href="../docs/circleLayout.html" class="svelte-194nj6y">Circle / Spiral Layout</a>
                <a href="../docs/curveLayout.html" class="svelte-194nj6y">Curve Layout</a>
                <a href="../docs/randomLayout.html" class="svelte-194nj6y">Random Layout</a>
                <a href="../docs/alignLayout.html" class="svelte-194nj6y">Align Layout</a>
                <a href="../docs/shapeLayout.html" class="svelte-194nj6y">Shape Layout</a>
                <a href="../docs/constraints.html" class="svelte-194nj6y">Constraints</a>
                <a href="../docs/randomModifier.html" class="svelte-194nj6y">Random Modifier</a>

                <a class="text-zinc-200 mt-5 font-bold svelte-194nj6y" href="../docs/animators.html">Animators</a>
                <a href="../docs/animators.html#curve" class="svelte-194nj6y">Curve Animator</a>
                <a href="../docs/animators.html#lerp" class="svelte-194nj6y">Lerp Animator</a>
                <a href="../docs/animators.html#rb" class="svelte-194nj6y">Rigid Body Animator</a>
                <a href="../docs/animators.html#layouts" class="svelte-194nj6y">Animating Layouts</a>

                <b class="text-zinc-200 mt-5">Click &amp; Drag</b>
                <a href="../docs/interactable.html" class="svelte-194nj6y">Interactable</a>
                <a href="../docs/interactable.html#dragTarget" class="svelte-194nj6y">Drag Target</a>
                <a href="../docs/xr.html" class="svelte-194nj6y">XR Interactions <span class="ml-2 text-xs font-bold text-blue-300">(v3.2)</span></a>
                <a href="../docs/interactable.html#customInputProvider" class="svelte-194nj6y">Custom Input Provider</a>

                <b class="text-zinc-200 mt-5">Data Binding</b>
                <a href="../docs/cloner.html" class="svelte-194nj6y">Cloner</a>
                <a href="../docs/cloner.html#databinding" class="svelte-194nj6y">Data Binding</a>

                <b class="text-zinc-200 mt-5">Advanced</b>
                <a href="../docs/pipeline.html" class="svelte-194nj6y">Flexalon Pipeline</a>
                <a href="../docs/customLayout.html" class="svelte-194nj6y">Custom Layouts</a>
                <a href="../docs/customAnimators.html" class="svelte-194nj6y">Custom Animators</a>
                <a href="../docs/adapters.html" class="svelte-194nj6y">Adapters</a>

                <a class="text-zinc-200 mt-5 font-bold svelte-194nj6y" href="../docs/api/Flexalon.html">Scripting API</a>
                <div class="mb-20"></div></div></div>
        
        <div class="flex flex-col w-full p-10 lg:ml-[300px]"><div class="doc svelte-194nj6y">

<div class="text-white text-2xl mb-10">XR Interactions</div>

<p>Flexalon has built-in input providers for Unity&#39;s XR Interaction Toolkit (XRI) and Oculus Interaction SDK.
    When using these providers, the drag movement is handled by the respective SDK, and Flexalon Interactable
    is used to insert and remove objects from layouts.
</p>

<div class="flex m-5 mb-10"><video muted autoplay loop playsinline><source src="https://www.flexalon.com/videos/doc-interactable-xr.mp4"></video></div>

<h2>Limitations</h2>
<p>Since Flexalon does not control the dragged gameObjects, several features of Flexalon Interactable will be disabled when using XR input providers:</p>
<img src="../images/doc-interactable-xr.png" alt="Flexalon Interactable with XR Input Provider">

<h2>Using Flexalon with XR Interaction Toolkit</h2>
<ul class="list-disc ml-10 leading-8 text-white"><li>Follow <a target="_blank" rel="noreferrer" href="https://docs.unity3d.com/Packages/com.unity.xr.interaction.toolkit@2.3/manual/index.html">Unity&#39;s documentation</a>
        to set up XR Interaction Toolkit. Ensure you can grab and move objects with hands or controllers before proceeding.</li>
    <li class="ml-6">If using <b>XR Hand Grab Interactable</b>, uncheck the <b>Retain Transform Parent</b> property. Flexalon Interactable will decide if to retain the parent.</li>
    <li>Flexalon will detect that XR Interaction Toolkit is installed and add the <b>Flexalon XR Input Provider</b> component.
        Add this component to all gameObjects with XR Interactable components that you want to add or remove from layouts.</li>
    <li>Add and configure <b>Flexalon Interactable</b> components to the same gameObjects.</li>
    <li>Add and configure <b>Flexalon Drag Target</b> components to any layouts which should support inserting, removing, or reordering objects.</li>
    <li>Optionally, add a <b>Flexalon Lerp Animator</b> or <b>Flexalon Rigid Body Animator</b> components to make objects under the drag targets reorder smoothly.</li>
    <li>Your component list should look something like this:</li>
    <img src="../images/doc-xri-components.png" alt="XRI Component List"></ul>

<h2>Using Flexalon with Oculus Interaction SDK</h2>
<ul class="list-disc ml-10 leading-8 text-white"><li>Follow <a target="_blank" rel="noreferrer" href="https://developer.oculus.com/documentation/unity/unity-gs-overview/">Meta&#39;s documentation</a>
        to set up Oculus Interaction SDK. Ensure you can grab and move objects with hands or controllers before proceeding.</li>
    <li>Add the FLEXALON_OCULUS Script Define Symbol so that Flexalon needs to know that Oculus Interaction SDK has been installed.
        To do this, go to Project Settings &gt; Player &gt; Other Settings and find Scripting Define Symbols</li>
    <li>Flexalon should now have a <b>Flexalon Oculus Input Provider</b> component.
            Add this component to all gameObjects with Interactable components that you want to add or remove from layouts.</li>
    <li>Add and configure <b>Flexalon Interactable</b> components to the same gameObjects.</li>
    <li>Add and configure <b>Flexalon Drag Target</b> components to any layouts which should support inserting, removing, or reordering objects.</li>
    <li>Optionally, add a <b>Flexalon Lerp Animator</b> or <b>Flexalon Rigid Body Animator</b> components to make objects under the drag targets reorder smoothly.</li>
    <li>Your component list should look something like this:</li>
    <img src="../images/doc-oculus-components.png" alt="Oculus Component List"></ul></div></div></div>
</div>


			
			
		</div>
	</body>
</html>
