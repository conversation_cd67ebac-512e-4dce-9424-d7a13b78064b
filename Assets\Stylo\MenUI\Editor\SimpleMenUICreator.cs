using UnityEngine;
using UnityEngine.UI;
using UnityEditor;
using TMPro;

namespace Stylo.MenUI.Editor
{
    /// <summary>
    /// Simple, streamlined editor script to create a working MenUI system.
    /// No over-engineering - just creates what's needed and assigns everything properly.
    /// </summary>
    public static class SimpleMenUICreator
    {
        [MenuItem("Stylo/MenUI/Create Simple MenUI System")]
        public static void CreateSimpleMenUISystem()
        {
            // Find or create canvas
            Canvas canvas = Object.FindObjectOfType<Canvas>();
            if (canvas == null)
            {
                GameObject canvasGO = new GameObject("UI Canvas");
                canvas = canvasGO.AddComponent<Canvas>();
                canvas.renderMode = RenderMode.ScreenSpaceOverlay;
                canvas.sortingOrder = 100;
                canvasGO.AddComponent<GraphicRaycaster>();
                canvasGO.AddComponent<CanvasScaler>();
            }

            // Create main MenUI system
            GameObject menUIRoot = new GameObject("Simple MenUI System");
            menUIRoot.transform.SetParent(canvas.transform, false);
            SimpleMenUISystem menUISystem = menUIRoot.AddComponent<SimpleMenUISystem>();

            // Create overlay panel
            GameObject overlay = CreateOverlay(menUIRoot.transform);

            // Create pause menu panel
            GameObject pauseMenu = CreatePauseMenu(menUIRoot.transform);

            // Create settings panel
            GameObject settingsPanel = CreateSettingsPanel(menUIRoot.transform);

            // Assign all references
            AssignReferences(menUISystem, overlay, pauseMenu, settingsPanel);

            // Select the created system
            Selection.activeGameObject = menUIRoot;

            Debug.Log("Simple MenUI System created successfully! All references assigned and ready to use.");
        }

        private static GameObject CreateOverlay(Transform parent)
        {
            GameObject overlay = new GameObject("Overlay Panel");
            overlay.transform.SetParent(parent, false);

            // Add Image component for background
            Image image = overlay.AddComponent<Image>();
            image.color = new Color(0f, 0f, 0f, 0.7f);

            // Set to full screen
            RectTransform rect = overlay.GetComponent<RectTransform>();
            rect.anchorMin = Vector2.zero;
            rect.anchorMax = Vector2.one;
            rect.offsetMin = Vector2.zero;
            rect.offsetMax = Vector2.zero;

            return overlay;
        }

        private static GameObject CreatePauseMenu(Transform parent)
        {
            GameObject pauseMenu = new GameObject("Pause Menu Panel");
            pauseMenu.transform.SetParent(parent, false);

            // Add Image component
            Image image = pauseMenu.AddComponent<Image>();
            image.color = new Color(0.2f, 0.2f, 0.2f, 0.9f);

            // Set size and position
            RectTransform rect = pauseMenu.GetComponent<RectTransform>();
            rect.sizeDelta = new Vector2(400, 300);

            // Create vertical layout
            VerticalLayoutGroup layout = pauseMenu.AddComponent<VerticalLayoutGroup>();
            layout.spacing = 20;
            layout.padding = new RectOffset(40, 40, 40, 40);
            layout.childAlignment = TextAnchor.MiddleCenter;
            layout.childControlHeight = false;
            layout.childControlWidth = true;

            // Create title
            CreateText("PAUSED", pauseMenu.transform, 36);

            // Create buttons
            CreateButton("Resume", pauseMenu.transform);
            CreateButton("Settings", pauseMenu.transform);
            CreateButton("Exit", pauseMenu.transform);

            return pauseMenu;
        }

        private static GameObject CreateSettingsPanel(Transform parent)
        {
            GameObject settingsPanel = new GameObject("Settings Panel");
            settingsPanel.transform.SetParent(parent, false);

            // Add RectTransform component first
            RectTransform rect = settingsPanel.AddComponent<RectTransform>();
            rect.anchorMin = Vector2.zero;
            rect.anchorMax = Vector2.one;
            rect.offsetMin = Vector2.zero;
            rect.offsetMax = Vector2.zero;

            // Add Image component
            Image image = settingsPanel.AddComponent<Image>();
            image.color = new Color(0.15f, 0.15f, 0.15f, 0.95f);

            // Create horizontal layout
            HorizontalLayoutGroup layout = settingsPanel.AddComponent<HorizontalLayoutGroup>();
            layout.spacing = 20;
            layout.padding = new RectOffset(40, 40, 40, 40);
            layout.childControlHeight = true;
            layout.childControlWidth = false;

            // Create category tabs (left side)
            GameObject categoryTabs = CreateCategoryTabs(settingsPanel.transform);

            // Create content area (right side)
            GameObject contentArea = CreateContentArea(settingsPanel.transform);

            return settingsPanel;
        }

        private static GameObject CreateCategoryTabs(Transform parent)
        {
            GameObject categoryTabs = new GameObject("Category Tabs");
            categoryTabs.transform.SetParent(parent, false);

            // Add RectTransform component
            RectTransform rect = categoryTabs.AddComponent<RectTransform>();
            rect.sizeDelta = new Vector2(200, 0);

            // Add layout
            VerticalLayoutGroup layout = categoryTabs.AddComponent<VerticalLayoutGroup>();
            layout.spacing = 10;
            layout.childControlHeight = false;
            layout.childControlWidth = true;

            // Create category buttons
            CreateButton("Graphics", categoryTabs.transform);
            CreateButton("Audio", categoryTabs.transform);
            CreateButton("Controls", categoryTabs.transform);
            CreateButton("Gameplay", categoryTabs.transform);
            CreateButton("Debug", categoryTabs.transform);

            // Add back button at bottom
            GameObject spacer = new GameObject("Spacer");
            spacer.transform.SetParent(categoryTabs.transform, false);
            spacer.AddComponent<RectTransform>(); // Add RectTransform for UI layout
            LayoutElement spacerLayout = spacer.AddComponent<LayoutElement>();
            spacerLayout.flexibleHeight = 1;

            CreateButton("Back", categoryTabs.transform);

            return categoryTabs;
        }

        private static GameObject CreateContentArea(Transform parent)
        {
            GameObject contentArea = new GameObject("Content Area");
            contentArea.transform.SetParent(parent, false);

            // Add RectTransform component
            RectTransform rect = contentArea.AddComponent<RectTransform>();

            // Add layout element to take remaining space
            LayoutElement layout = contentArea.AddComponent<LayoutElement>();
            layout.flexibleWidth = 1;

            // Create settings panels for each category
            CreateSettingsCategoryPanel("Graphics Settings Panel", contentArea.transform);
            CreateSettingsCategoryPanel("Audio Settings Panel", contentArea.transform);
            CreateSettingsCategoryPanel("Controls Settings Panel", contentArea.transform);
            CreateSettingsCategoryPanel("Gameplay Settings Panel", contentArea.transform);
            CreateSettingsCategoryPanel("Debug Settings Panel", contentArea.transform);

            return contentArea;
        }

        private static GameObject CreateSettingsCategoryPanel(string name, Transform parent)
        {
            GameObject panel = new GameObject(name);
            panel.transform.SetParent(parent, false);

            // Add RectTransform component first
            RectTransform rect = panel.AddComponent<RectTransform>();

            // Add Image component
            Image image = panel.AddComponent<Image>();
            image.color = new Color(0.1f, 0.1f, 0.1f, 0.8f);

            // Set to full size
            rect.anchorMin = Vector2.zero;
            rect.anchorMax = Vector2.one;
            rect.offsetMin = Vector2.zero;
            rect.offsetMax = Vector2.zero;

            // Add title
            string categoryName = name.Replace(" Settings Panel", "");
            CreateText($"{categoryName} Settings", panel.transform, 24);

            return panel;
        }

        private static GameObject CreateButton(string text, Transform parent)
        {
            GameObject buttonGO = new GameObject(text);
            buttonGO.transform.SetParent(parent, false);

            // Add RectTransform component first
            RectTransform rect = buttonGO.AddComponent<RectTransform>();
            rect.sizeDelta = new Vector2(0, 50);

            // Add Image component
            Image image = buttonGO.AddComponent<Image>();
            image.color = new Color(0.3f, 0.3f, 0.3f, 1f);

            // Add Button component
            Button button = buttonGO.AddComponent<Button>();

            // Create text child
            GameObject textGO = new GameObject("Text");
            textGO.transform.SetParent(buttonGO.transform, false);

            // Add RectTransform for text
            RectTransform textRect = textGO.AddComponent<RectTransform>();
            textRect.anchorMin = Vector2.zero;
            textRect.anchorMax = Vector2.one;
            textRect.offsetMin = Vector2.zero;
            textRect.offsetMax = Vector2.zero;

            // Add TextMeshPro component
            TextMeshProUGUI textComponent = textGO.AddComponent<TextMeshProUGUI>();
            textComponent.text = text;
            textComponent.fontSize = 18;
            textComponent.color = Color.white;
            textComponent.alignment = TextAlignmentOptions.Center;

            // Try to assign a default font (LiberationSans SDF is usually available)
            var defaultFont = Resources.GetBuiltinResource<TMPro.TMP_FontAsset>("LiberationSans SDF");
            if (defaultFont != null)
                textComponent.font = defaultFont;
            textRect.anchorMin = Vector2.zero;
            textRect.anchorMax = Vector2.one;
            textRect.offsetMin = Vector2.zero;
            textRect.offsetMax = Vector2.zero;

            return buttonGO;
        }

        private static GameObject CreateText(string text, Transform parent, float fontSize)
        {
            GameObject textGO = new GameObject($"Text - {text}");
            textGO.transform.SetParent(parent, false);

            // Add RectTransform component first
            RectTransform rect = textGO.AddComponent<RectTransform>();
            rect.sizeDelta = new Vector2(0, fontSize + 10);

            // Add TextMeshPro component
            TextMeshProUGUI textComponent = textGO.AddComponent<TextMeshProUGUI>();
            textComponent.text = text;
            textComponent.fontSize = fontSize;
            textComponent.color = Color.white;
            textComponent.alignment = TextAlignmentOptions.Center;

            // Try to assign a default font (LiberationSans SDF is usually available)
            var defaultFont = Resources.GetBuiltinResource<TMPro.TMP_FontAsset>("LiberationSans SDF");
            if (defaultFont != null)
                textComponent.font = defaultFont;

            return textGO;
        }

        private static void AssignReferences(SimpleMenUISystem menUISystem, GameObject overlay, GameObject pauseMenu, GameObject settingsPanel)
        {
            SerializedObject serializedSystem = new SerializedObject(menUISystem);

            // Assign panels
            serializedSystem.FindProperty("overlayPanel").objectReferenceValue = overlay;
            serializedSystem.FindProperty("pauseMenuPanel").objectReferenceValue = pauseMenu;
            serializedSystem.FindProperty("settingsPanel").objectReferenceValue = settingsPanel;

            // Assign pause menu buttons
            serializedSystem.FindProperty("resumeButton").objectReferenceValue = pauseMenu.transform.Find("Resume")?.GetComponent<Button>();
            serializedSystem.FindProperty("settingsButton").objectReferenceValue = pauseMenu.transform.Find("Settings")?.GetComponent<Button>();
            serializedSystem.FindProperty("exitButton").objectReferenceValue = pauseMenu.transform.Find("Exit")?.GetComponent<Button>();

            // Assign settings buttons
            Transform categoryTabs = settingsPanel.transform.Find("Category Tabs");
            serializedSystem.FindProperty("backButton").objectReferenceValue = categoryTabs?.Find("Back")?.GetComponent<Button>();
            serializedSystem.FindProperty("graphicsButton").objectReferenceValue = categoryTabs?.Find("Graphics")?.GetComponent<Button>();
            serializedSystem.FindProperty("audioButton").objectReferenceValue = categoryTabs?.Find("Audio")?.GetComponent<Button>();
            serializedSystem.FindProperty("controlsButton").objectReferenceValue = categoryTabs?.Find("Controls")?.GetComponent<Button>();
            serializedSystem.FindProperty("gameplayButton").objectReferenceValue = categoryTabs?.Find("Gameplay")?.GetComponent<Button>();
            serializedSystem.FindProperty("debugButton").objectReferenceValue = categoryTabs?.Find("Debug")?.GetComponent<Button>();

            // Assign settings panels
            Transform contentArea = settingsPanel.transform.Find("Content Area");
            serializedSystem.FindProperty("graphicsPanel").objectReferenceValue = contentArea?.Find("Graphics Settings Panel")?.gameObject;
            serializedSystem.FindProperty("audioPanel").objectReferenceValue = contentArea?.Find("Audio Settings Panel")?.gameObject;
            serializedSystem.FindProperty("controlsPanel").objectReferenceValue = contentArea?.Find("Controls Settings Panel")?.gameObject;
            serializedSystem.FindProperty("gameplayPanel").objectReferenceValue = contentArea?.Find("Gameplay Settings Panel")?.gameObject;
            serializedSystem.FindProperty("debugPanel").objectReferenceValue = contentArea?.Find("Debug Settings Panel")?.gameObject;

            serializedSystem.ApplyModifiedProperties();

            Debug.Log("All references assigned successfully!");
        }
    }
}
