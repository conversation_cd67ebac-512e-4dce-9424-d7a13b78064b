%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: UV Interior LatLong
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor v1.9.1.5\n// Available at the
    Unity Asset Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=19105\nNode;AmplifyShaderEditor.StickyNoteNode;1;-595.546,2279.329;Inherit;False;375.0465;133.3052;UV
    Interior LatLong Node;;0,0,0,1;The UV Interior LatLong Node generates the direction
    vector used to sample a LatLong Texture. The purpose is to simulate building
    interiors through windows without using any geometry.;0;0\nNode;AmplifyShaderEditor.CommentaryNode;2;-1536.499,-123.2731;Inherit;False;1188.627;842.8054;Hash;15;38;36;15;14;13;12;11;10;9;8;7;6;5;4;3;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.DynamicAppendNode;3;-1318.755,170.5277;Inherit;False;FLOAT3;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.DotProductOpNode;4;-1092.623,168.4349;Inherit;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.DotProductOpNode;5;-1099.484,-70.02136;Inherit;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.DynamicAppendNode;6;-926.3718,144.0443;Inherit;False;FLOAT3;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.DotProductOpNode;7;-1087.345,414.6067;Inherit;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.DynamicAppendNode;8;-1324.312,417.375;Inherit;False;FLOAT3;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SinOpNode;9;-749.2289,147.5291;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.DynamicAppendNode;10;-1318.756,-73.27289;Inherit;False;FLOAT3;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.Vector3Node;11;-1325.033,36.59399;Inherit;False;Constant;_Vector4;Vector
    4;3;0;Create;True;0;0;0;False;0;False;127.1,311.7,74.7;0,0,0;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.Vector3Node;12;-1320.848,280.3951;Inherit;False;Constant;_Vector5;Vector
    5;3;0;Create;True;0;0;0;False;0;False;269.5,183.3,246.1;0,0,0;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.Vector3Node;13;-1325.987,529.4741;Inherit;False;Constant;_Vector6;Vector
    6;3;0;Create;True;0;0;0;False;0;False;113.5,271.9,124.6;0,0,0;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;15;-595.465,148.2702;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FractNode;38;-464.465,147.2702;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.StickyNoteNode;53;-3035.925,2641.94;Inherit;False;224.3918;105.2247;Room
    Count ;;0,0,0,1;Controls the number of rooms in the building.;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;60;-160.944,24.11759;Inherit;False;286.28;108.0164;Random;;0,0,0,1;Random
    creates variations in the room by using random sides for the left, right, font,
    and back.;0;0\nNode;AmplifyShaderEditor.RoundOpNode;39;-326.5129,146.9198;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.BreakToComponentsNode;40;-167.5168,235.2545;Inherit;False;FLOAT3;1;0;FLOAT3;0,0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;44;189.9825,355.9603;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;91;650.1039,246.5512;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;90;635.1039,223.5512;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;92;225.0305,-351.8686;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;93;246.59,-322.5117;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;94;243.7688,162.6385;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;89;359.7375,193.2313;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;55;694.5916,240.8921;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;43;26.01411,467.8947;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;42;29.90215,352.9326;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FloorOpNode;37;-1657.548,-74.76825;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.BreakToComponentsNode;36;-1506.246,-74.7691;Inherit;False;FLOAT2;1;0;FLOAT2;0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.Vector3Node;49;-203.6968,349.6739;Inherit;False;Constant;_Vector0;Vector
    0;2;0;Create;True;0;0;0;False;0;False;-1,1,1;-1,1,1;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.Vector3Node;50;-204.9878,485.8352;Inherit;False;Constant;_Vector1;Vector
    1;2;0;Create;True;0;0;0;False;0;False;1,1,-1;1,1,-1;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.Vector3Node;51;-201.9089,627.8166;Inherit;False;Constant;_Vector2;Vector
    2;2;0;Create;True;0;0;0;False;0;False;1,1,1;1,1,1;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.Vector3Node;54;-203.9548,770.6967;Inherit;False;Constant;_Vector3;Vector
    3;2;0;Create;True;0;0;0;False;0;False;-1,1,1;-1,1,1;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.RangedFloatNode;14;-791.6044,225.0709;Inherit;False;Constant;_Float4;Float
    4;0;0;Create;True;0;0;0;False;0;False;43758.55;43758.55;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SwizzleNode;46;509.384,258.0056;Inherit;False;FLOAT3;2;1;0;3;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.AbsOpNode;16;-1011.65,-629.486;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;18;-855.241,-628.9871;Inherit;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.BreakToComponentsNode;19;-708.377,-632.236;Inherit;False;FLOAT3;1;0;FLOAT3;0,0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.SimpleMinOpNode;20;-414.377,-606.236;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;22;-251.376,-582.313;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;23;-1029.814,-470.0941;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;24;-1364.881,-359.4831;Inherit;False;2;0;FLOAT2;0,0;False;1;FLOAT2;1,-1;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;25;-1534.523,-359.5881;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;2,-2;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.FractNode;26;-1673.627,-359.4763;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;30;-80.57706,-388.407;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleDivideOpNode;17;-1170.444,-628.9871;Inherit;False;2;0;FLOAT3;1,1,1;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMinOpNode;21;-567.377,-633.236;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.DynamicAppendNode;29;-1181.88,-358.4831;Inherit;False;FLOAT3;4;0;FLOAT2;0,0;False;1;FLOAT;0;False;2;FLOAT;-1;False;3;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;28;-1365.984,-543.7399;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;-1,1,1;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.ViewDirInputsCoordNode;52;-1588.421,-548.3912;Inherit;False;Tangent;False;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.CommentaryNode;123;-2419.801,2731.301;Inherit;False;1188.627;842.8054;Hash;15;164;159;158;151;134;133;132;131;130;129;128;127;126;125;124;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.DynamicAppendNode;124;-2202.058,3025.102;Inherit;False;FLOAT3;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.DotProductOpNode;125;-1975.925,3023.009;Inherit;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.DynamicAppendNode;126;-1809.674,2998.619;Inherit;False;FLOAT3;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.DotProductOpNode;127;-1970.648,3269.181;Inherit;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.DynamicAppendNode;128;-2207.614,3271.948;Inherit;False;FLOAT3;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SinOpNode;129;-1632.531,3002.103;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;133;-1478.768,3002.844;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FractNode;134;-1347.768,3001.844;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.StickyNoteNode;136;-1203.576,2885.643;Inherit;False;286.28;108.0164;Random;;0,0,0,1;Random
    creates variations in the room by using random sides for the left, right, font,
    and back.;0;0\nNode;AmplifyShaderEditor.RoundOpNode;137;-1209.817,3001.494;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.DotProductOpNode;158;-1982.786,2784.552;Inherit;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.DynamicAppendNode;159;-2202.059,2781.301;Inherit;False;FLOAT3;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;160;-2604.736,2786.95;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.FloorOpNode;163;-2540.851,2779.806;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.BreakToComponentsNode;164;-2389.548,2779.804;Inherit;False;FLOAT2;1;0;FLOAT2;0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;167;-2248.184,2495.092;Inherit;False;2;0;FLOAT2;0,0;False;1;FLOAT2;1,-1;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;168;-2417.826,2494.986;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;2,-2;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.FractNode;169;-2556.93,2495.098;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;170;-2729.425,2494.551;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;171;-2602.736,2545.952;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.TexturePropertyNode;157;930.08,-804.0977;Inherit;True;Property;_LatLongMap;LatLong
    Map;0;1;[SingleLineTexture];Create;True;0;0;0;False;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.FunctionInput;62;-2926.144,2492.11;Inherit;False;UV;2;0;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleDivideOpNode;173;-2058.747,2321.586;Inherit;False;2;0;FLOAT3;1,1,1;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.DynamicAppendNode;199;-2058.916,2414.514;Inherit;False;FLOAT3;4;0;FLOAT2;0,0;False;1;FLOAT;0;False;2;FLOAT;-1;False;3;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;166;-1889.117,2390.479;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;200;-2218.287,2344.834;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;-1,1,1;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.AbsOpNode;165;-1875.953,2322.088;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;176;-1735.995,2320.586;Inherit;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;172;-761.8805,2461.167;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.TransformDirectionNode;182;-2465.508,2164.329;Inherit;False;World;Tangent;False;Fast;False;1;0;FLOAT3;0,0,0;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.WorldNormalVector;181;-2878.871,2193.297;Inherit;False;True;1;0;FLOAT3;0,0,1;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.ViewDirInputsCoordNode;174;-2856.247,2051.899;Inherit;False;World;True;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.FunctionInput;58;-2944.967,2568.773;Inherit;False;Room
    Count;2;1;False;1;0;FLOAT2;1,1;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.TexCoordVertexDataNode;31;-3186.095,2497.129;Inherit;False;0;2;0;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.FunctionOutput;61;-1042.714,3001.809;Inherit;False;False;-1;Random;3;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.DdxOpNode;153;-2723.758,2346.438;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.DdyOpNode;155;-2724.509,2418.729;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.FunctionOutput;201;-2572.153,2345.714;Inherit;False;False;-1;DDX;1;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.FunctionOutput;202;-2571.097,2418.547;Inherit;False;False;-1;DDY;2;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.Vector3Node;130;-2208.335,2891.167;Inherit;False;Constant;_Vector7;Vector
    4;3;0;Create;True;0;0;0;False;0;False;127.1,311.7,74.7;0,0,0;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.Vector3Node;131;-2204.15,3134.969;Inherit;False;Constant;_Vector8;Vector
    5;3;0;Create;True;0;0;0;False;0;False;269.5,183.3,246.1;0,0,0;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.Vector3Node;132;-2209.29,3384.048;Inherit;False;Constant;_Vector9;Vector
    6;3;0;Create;True;0;0;0;False;0;False;113.5,271.9,124.6;0,0,0;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.RangedFloatNode;151;-1674.906,3079.645;Inherit;False;Constant;_Float5;Float
    4;0;0;Create;True;0;0;0;False;0;False;43758.55;43758.55;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;188;-1160.372,2333.152;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.DynamicAppendNode;192;-935.9755,2332.764;Inherit;False;FLOAT2;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;191;-1087.568,2090.705;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.ASinOpNode;194;-1254.568,2161.705;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SwizzleNode;195;-1464.361,2158.705;Inherit;False;FLOAT;1;1;2;3;1;0;FLOAT3;0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.BreakToComponentsNode;197;-1591.301,2319.413;Inherit;False;FLOAT3;1;0;FLOAT3;0,0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.ATan2OpNode;198;-1453.906,2330.438;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;196;-514.2944,2461.412;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0.1591549,-0.3183099;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.RangedFloatNode;189;-1335.912,2403.99;Inherit;False;Constant;_Float0;Float
    0;0;0;Create;True;0;0;0;False;0;False;1.570796;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.PiNode;190;-1312.176,2062.67;Inherit;False;1;0;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;193;-1469.244,2059.524;Inherit;False;Constant;_Float1;Float
    1;0;0;Create;True;0;0;0;False;0;False;-0.5;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;56;-319.7309,2456.59;Inherit;False;True;-1;UV;0;True;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.ReflectOpNode;203;-2652.016,2166.493;Inherit;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nWireConnection;3;0;36;1\nWireConnection;3;1;36;0\nWireConnection;3;2;36;0\nWireConnection;4;0;3;0\nWireConnection;4;1;12;0\nWireConnection;5;0;10;0\nWireConnection;5;1;11;0\nWireConnection;6;0;5;0\nWireConnection;6;1;4;0\nWireConnection;6;2;7;0\nWireConnection;7;0;8;0\nWireConnection;7;1;13;0\nWireConnection;8;0;36;0\nWireConnection;8;1;36;1\nWireConnection;8;2;36;1\nWireConnection;9;0;6;0\nWireConnection;10;0;36;0\nWireConnection;10;1;36;1\nWireConnection;10;2;36;0\nWireConnection;15;0;9;0\nWireConnection;15;1;14;0\nWireConnection;38;0;15;0\nWireConnection;39;0;38;0\nWireConnection;40;0;39;0\nWireConnection;44;0;42;0\nWireConnection;44;1;43;0\nWireConnection;91;0;90;0\nWireConnection;90;0;89;0\nWireConnection;92;0;30;0\nWireConnection;93;0;92;0\nWireConnection;94;0;93;0\nWireConnection;89;0;94;0\nWireConnection;89;1;44;0\nWireConnection;55;0;91;0\nWireConnection;55;1;46;0\nWireConnection;55;2;40;2\nWireConnection;43;0;51;0\nWireConnection;43;1;54;0\nWireConnection;43;2;40;1\nWireConnection;42;0;49;0\nWireConnection;42;1;50;0\nWireConnection;42;2;40;0\nWireConnection;36;0;37;0\nWireConnection;46;0;89;0\nWireConnection;16;0;17;0\nWireConnection;18;0;16;0\nWireConnection;18;1;23;0\nWireConnection;19;0;18;0\nWireConnection;20;0;21;0\nWireConnection;20;1;19;2\nWireConnection;22;0;20;0\nWireConnection;22;1;28;0\nWireConnection;23;0;17;0\nWireConnection;23;1;29;0\nWireConnection;24;0;25;0\nWireConnection;25;0;26;0\nWireConnection;30;0;22;0\nWireConnection;30;1;29;0\nWireConnection;17;1;28;0\nWireConnection;21;0;19;0\nWireConnection;21;1;19;1\nWireConnection;29;0;24;0\nWireConnection;28;0;52;0\nWireConnection;124;0;164;1\nWireConnection;124;1;164;0\nWireConnection;124;2;164;0\nWireConnection;125;0;124;0\nWireConnection;125;1;131;0\nWireConnection;126;0;158;0\nWireConnection;126;1;125;0\nWireConnection;126;2;127;0\nWireConnection;127;0;128;0\nWireConnection;127;1;132;0\nWireConnection;128;0;164;0\nWireConnection;128;1;164;1\nWireConnection;128;2;164;1\nWireConnection;129;0;126;0\nWireConnection;133;0;129;0\nWireConnection;133;1;151;0\nWireConnection;134;0;133;0\nWireConnection;137;0;134;0\nWireConnection;158;0;159;0\nWireConnection;158;1;130;0\nWireConnection;159;0;164;0\nWireConnection;159;1;164;1\nWireConnection;159;2;164;0\nWireConnection;160;0;171;0\nWireConnection;163;0;160;0\nWireConnection;164;0;163;0\nWireConnection;167;0;168;0\nWireConnection;168;0;169;0\nWireConnection;169;0;170;0\nWireConnection;170;0;62;0\nWireConnection;170;1;58;0\nWireConnection;171;0;170;0\nWireConnection;62;0;31;0\nWireConnection;173;1;200;0\nWireConnection;199;0;167;0\nWireConnection;166;0;173;0\nWireConnection;166;1;199;0\nWireConnection;200;0;182;0\nWireConnection;165;0;173;0\nWireConnection;176;0;165;0\nWireConnection;176;1;166;0\nWireConnection;172;0;192;0\nWireConnection;172;1;167;0\nWireConnection;182;0;203;0\nWireConnection;61;0;137;0\nWireConnection;153;0;62;0\nWireConnection;155;0;62;0\nWireConnection;201;0;153;0\nWireConnection;202;0;155;0\nWireConnection;188;0;198;0\nWireConnection;188;1;189;0\nWireConnection;192;0;188;0\nWireConnection;192;1;191;0\nWireConnection;191;0;190;0\nWireConnection;191;1;194;0\nWireConnection;194;0;195;0\nWireConnection;195;0;182;0\nWireConnection;197;0;176;0\nWireConnection;198;0;197;2\nWireConnection;198;1;197;0\nWireConnection;196;0;172;0\nWireConnection;190;0;193;0\nWireConnection;56;0;196;0\nWireConnection;203;0;174;0\nWireConnection;203;1;181;0\nASEEND*/\n//CHKSM=71AE222832D3E654C24B41F2B456DFF62D9AD541"
  m_functionName: 
  m_description: Amplify Shader Pack Example
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 0
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_customEdited: 0
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 0
  m_headerStyle: 2
  m_headerColor: {r: 0.6627451, g: 0.4392157, b: 0, a: 1}
  m_customNodeCategory: Amplify Shader Pack
  m_previewPosition: 0
  m_hidden: 0
  m_url: 
