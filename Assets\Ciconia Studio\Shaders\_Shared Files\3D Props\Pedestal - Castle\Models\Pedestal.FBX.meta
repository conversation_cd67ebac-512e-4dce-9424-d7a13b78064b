fileFormatVersion: 2
guid: a6fe69db28524cb4596152fd22c0d608
ModelImporter:
  serializedVersion: 19300
  internalIDToNameTable:
  - first:
      1: 100000
    second: Chain link
  - first:
      1: 100002
    second: //RootNode
  - first:
      1: 100004
    second: Pedestal
  - first:
      1: 100006
    second: <PERSON>_<PERSON>
  - first:
      1: 100008
    second: Stone_mid
  - first:
      1: 100010
    second: <PERSON>_Small
  - first:
      1: 100012
    second: Torche
  - first:
      1: 100014
    second: Wall
  - first:
      4: 400000
    second: Chain link
  - first:
      4: 400002
    second: //RootNode
  - first:
      4: 400004
    second: Pedestal
  - first:
      4: 400006
    second: <PERSON>_Big
  - first:
      4: 400008
    second: Stone_mid
  - first:
      4: 400010
    second: Stones_Small
  - first:
      4: 400012
    second: Torche
  - first:
      4: 400014
    second: Wall
  - first:
      23: 2300000
    second: Chain link
  - first:
      23: 2300002
    second: Pedestal
  - first:
      23: 2300004
    second: Stone_Big
  - first:
      23: 2300006
    second: Stone_mid
  - first:
      23: 2300008
    second: <PERSON>_Small
  - first:
      23: 2300010
    second: Torche
  - first:
      23: 2300012
    second: Wall
  - first:
      33: 3300000
    second: Chain link
  - first:
      33: 3300002
    second: Pedestal
  - first:
      33: 3300004
    second: <PERSON>_<PERSON>
  - first:
      33: 3300006
    second: Stone_mid
  - first:
      33: 3300008
    second: Stones_Small
  - first:
      33: 3300010
    second: Torche
  - first:
      33: 3300012
    second: Wall
  - first:
      43: 4300000
    second: Chain link
  - first:
      43: 4300002
    second: Stones_Small
  - first:
      43: 4300004
    second: Wall
  - first:
      43: 4300006
    second: Torche
  - first:
      43: 4300008
    second: Pedestal
  - first:
      43: 4300010
    second: Stone_Big
  - first:
      43: 4300012
    second: Stone_mid
  externalObjects:
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Pedestal
    second: {fileID: 2100000, guid: d0e5abc3144c16e449806330b43f6eeb, type: 2}
  materials:
    materialImportMode: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 0
    importBlendShapes: 1
    importCameras: 0
    importLights: 0
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    meshOptimizationFlags: -1
    indexFormat: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 0
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 1
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 0
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 0
  humanoidOversampling: 1
  avatarSetup: 0
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
