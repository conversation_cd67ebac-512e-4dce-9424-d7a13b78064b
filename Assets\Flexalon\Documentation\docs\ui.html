<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<link rel="icon" href="../images/favicon.svg" />
		<link rel="icon" href="../images/favicon.ico" />
		<meta name="viewport" content="width=device-width, height=device-height, initial-scale=1">
		<meta property="og:type" content="website">
		<meta http-equiv="content-security-policy" content="">
		<link href="../_app/immutable/assets/_layout.21aea584.css" rel="stylesheet">
		<link href="../_app/immutable/assets/_layout.0154a58a.css" rel="stylesheet">
		
		
		
		
		
		
		
		
		
		
		
		
		<title>Flexalon UI</title><!-- HEAD_svelte-i5aht6_START --><meta property="og:title" content="Flexalon UI"><meta name="description" property="og:description" content="Flexalon lets Unity developers arrange objects in 3D layouts that are precise and easy to adjust. Use built-in interactions and animations to create powerful experiences."><meta name="image" property="og:image" content="../images/flexalon-1200x630.png"><meta name="twitter:card" content="summary_large_image"><meta name="twitter:title" content="Flexalon UI"><meta name="twitter:site" content="@alon_farchy"><meta name="twitter:description" content="Flexalon lets Unity developers arrange objects in 3D layouts that are precise and easy to adjust. Use built-in interactions and animations to create powerful experiences."><meta name="twitter:image" content="../images/flexalon-1200x630.png"><!-- HEAD_svelte-i5aht6_END -->
	</head>
	<body>
		<div>


<div class="text-white font-body p-4 sticky top-0 bg-black flex gap-5 w-full text-gray-200 z-20 border-b-2 border-[#21a6f0]"><button class="lg:hidden"><svg width="24" height="24"><path d="M5 6h14M5 12h14M5 18h14" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"></path></svg></button>
    <a href="https://www.flexalon.com/" class="font-logo upper text-lg text-[#21a6f0] lg:pl-5 svelte-194nj6y">Flexalon Layouts</a></div>

<div class="w-full max-w-[1600px] lg:pl-10"><div class="flex w-full min-h-full text-zinc-400 relative"><div class="h-full fixed bg-black z-10 max-lg:hidden"><div class="flex flex-col w-[300px] gap-2 border-[#21a6f0] border-r-2 bg-black min-h-0 h-full pt-10 pb-36 overflow-auto max-lg:pl-10"><b class="text-zinc-200">Getting Started</b>
                <a href="../docs.html" class="svelte-194nj6y">Installation</a>
                <a href="../docs/coreConcepts.html" class="svelte-194nj6y">Core Concepts</a>
                <a href="../docs/ui.html" class="svelte-194nj6y">Flexalon UI <span class="ml-2 text-xs font-bold text-pink-300">(New in v4.0!)</span></a>
                <a href="../docs/templates.html" class="svelte-194nj6y">Template Pack</a>
                <a href="../docs/challenges.html" class="svelte-194nj6y">Challenges!</a>

                <a class="text-zinc-200 mt-5 font-bold svelte-194nj6y" href="../docs/flexalonObject.html">Flexalon Objects</a>
                <a href="../docs/flexalonObject.html#size" class="svelte-194nj6y">Width, Height, and Depth</a>
                <a href="../docs/flexalonObject.html#minmax" class="svelte-194nj6y">Min / Max Size<span class="ml-2 text-xs font-bold text-purple-400">(v4.1)</span></a>
                <a href="../docs/flexalonObject.html#margins" class="svelte-194nj6y">Margins and Padding</a>
                <a href="../docs/flexalonObject.html#offset" class="svelte-194nj6y">Offset, Scale, and Rotation</a>

                <b class="text-zinc-200 mt-5">Layouts</b>
                <a href="../docs/flexibleLayout.html" class="svelte-194nj6y">Flexible Layout</a>
                <a href="../docs/gridLayout.html" class="svelte-194nj6y">Grid Layout</a>
                <a href="../docs/circleLayout.html" class="svelte-194nj6y">Circle / Spiral Layout</a>
                <a href="../docs/curveLayout.html" class="svelte-194nj6y">Curve Layout</a>
                <a href="../docs/randomLayout.html" class="svelte-194nj6y">Random Layout</a>
                <a href="../docs/alignLayout.html" class="svelte-194nj6y">Align Layout</a>
                <a href="../docs/shapeLayout.html" class="svelte-194nj6y">Shape Layout</a>
                <a href="../docs/constraints.html" class="svelte-194nj6y">Constraints</a>
                <a href="../docs/randomModifier.html" class="svelte-194nj6y">Random Modifier</a>

                <a class="text-zinc-200 mt-5 font-bold svelte-194nj6y" href="../docs/animators.html">Animators</a>
                <a href="../docs/animators.html#curve" class="svelte-194nj6y">Curve Animator</a>
                <a href="../docs/animators.html#lerp" class="svelte-194nj6y">Lerp Animator</a>
                <a href="../docs/animators.html#rb" class="svelte-194nj6y">Rigid Body Animator</a>
                <a href="../docs/animators.html#layouts" class="svelte-194nj6y">Animating Layouts</a>

                <b class="text-zinc-200 mt-5">Click &amp; Drag</b>
                <a href="../docs/interactable.html" class="svelte-194nj6y">Interactable</a>
                <a href="../docs/interactable.html#dragTarget" class="svelte-194nj6y">Drag Target</a>
                <a href="../docs/xr.html" class="svelte-194nj6y">XR Interactions <span class="ml-2 text-xs font-bold text-blue-300">(v3.2)</span></a>
                <a href="../docs/interactable.html#customInputProvider" class="svelte-194nj6y">Custom Input Provider</a>

                <b class="text-zinc-200 mt-5">Data Binding</b>
                <a href="../docs/cloner.html" class="svelte-194nj6y">Cloner</a>
                <a href="../docs/cloner.html#databinding" class="svelte-194nj6y">Data Binding</a>

                <b class="text-zinc-200 mt-5">Advanced</b>
                <a href="../docs/pipeline.html" class="svelte-194nj6y">Flexalon Pipeline</a>
                <a href="../docs/customLayout.html" class="svelte-194nj6y">Custom Layouts</a>
                <a href="../docs/customAnimators.html" class="svelte-194nj6y">Custom Animators</a>
                <a href="../docs/adapters.html" class="svelte-194nj6y">Adapters</a>

                <a class="text-zinc-200 mt-5 font-bold svelte-194nj6y" href="../docs/api/Flexalon.html">Scripting API</a>
                <div class="mb-20"></div></div></div>
        
        <div class="flex flex-col w-full p-10 lg:ml-[300px]"><div class="doc svelte-194nj6y">

<div class="text-white text-2xl mb-10">Flexalon UI</div>
<p>Since update 4.0, Flexalon can be used to create UI in Unity. You can:</p>
<ul class="ml-5 list-disc"><li>Use all Flexalon layouts, constraints, and modifiers to position your content.</li>
    <li>Animate your UI with Lerp and Curve Animators.</li>
    <li>Create click and drag interactions with Flexalon Interactables.</li>
    <li>Bind data to your UI with Flexalon Cloner.</li>
    <li><i>Coming Soon</i>: Use <a class="font-bold text-purple-400" href="https://www.flexalon.com/ai">Flexalon UI Copilot</a> to create UI with through natural conversation with an AI.</li></ul>
<img src="../images/ChatCanvasStyles.png" alt="UI Example">

<h2>Getting Started</h2>
<p>If you&#39;re familiar with uGUI layouts, adopting Flexalon UI should be easy. Here&#39;s the basics:</p>
<ul class="ml-5 list-decimal"><li>Create a canvas by selecting GameObject &gt; UI &gt; Canvas. Learn more about canvas <a href="https://docs.unity3d.com/Packages/com.unity.ugui@1.0/manual/UICanvas.html">here</a>.</li>
    <li>Start building your UI by adding Flexalon layout objects under your Canvas.
        For example, here we create a Flexalon Flexible Layout under the canvas and add some buttons to it:
        <div class="max-w-[800px] m-5"><img src="../images/doc-ui-flex.png" alt="Flexible Layout"></div></li>
    <li>It&#39;s often desirable to build UI that dynamically reflows to match the screen size.
        To do this, set the width and height properties on the layout&#39;s Flexalon Object component  to &quot;Fill&quot;.
        If we also check the &quot;Wrap&quot; option on the Flexible Layout, then our buttons will wrap as necessary to fit on the screen.
        <div class="max-w-[800px] m-5"><img src="../images/doc-ui-wrap.png" alt="Flexible Layout with Wrap"></div></li>
    <li>Check out the sample scenes under Flexalon/Samples/Scenes/UI to see how to use other layouts, animators, interactables, and scroll views.
    </li></ul>

<h2>Comparing Flexalon UI to uGUI Layout Groups</h2>
<p>You can Flexalon components instead of Unity&#39;s built-in uGUI layout group components. Here&#39;s how their capabilities map:</p>
<table class="w-full"><tr><td class="w-1/2"><h3 class="font-bold">uGUI Component</h3></td>
        <td class="w-1/2"><h3 class="font-bold">Flexalon UI</h3></td></tr>
    <tr><td>Layout Element</td>
        <td><a href="../docs/flexalonObject.html">Flexalon Object</a> with size set to &quot;Fixed&quot; or &quot;Fill&quot;</td></tr>
    <tr><td>Vertical / Horizontal Layout Group</td>
        <td><a href="../docs/flexibleLayout.html">Flexalon Flexible Layout</a></td></tr>
    <tr><td>Grid Layout Group</td>
        <td><a href="../docs/gridLayout.html">Flexalon Grid Layout</a></td></tr>
    <tr><td>Content Size Fitter</td>
        <td><a href="../docs/flexalonObject.html">Flexalon Object</a> with size set to &quot;Layout&quot;</td></tr>
    <tr><td>Aspect Ratio Fitter</td>
        <td>See <a href="#preservingAspectRatio">Preserving Aspect Ratio</a></td></tr></table>

<h2>Interactions</h2>

Flexalon Interactables can be used to let users click and drag objects with a Graphic component. See the <a href="../docs/interactable.html">Interactions</a> page for more information, and check out the Flexalon/Samples/Scenes/UI/InteractableUISample scene.

<div class="flex m-5 mb-10"><video muted autoplay loop playsinline><source src="https://www.flexalon.com/videos/doc-interactable-ui.mp4"></video></div>

<h2>Animations</h2>

Flexalon animations let you animate objects between layout positions. See the <a href="../docs/animators.html">Animations</a> page for more information, and check out the Flexalon/Samples/Scenes/UI/AnimatorsUISample scene.

<div class="flex m-5 mb-10"><video muted autoplay loop playsinline><source src="https://www.flexalon.com/videos/doc-animators-ui.mp4"></video></div>

<h2 id="preservingAspectRatio">Preserving Aspect Ratio</h2>
<p>To preserve aspect ratio for images:</p>
<ul class="ml-5 list-disc"><li>On the Image component, set &quot;Image Type&quot; to &quot;Simple&quot; and check &quot;Preserve Aspect&quot;.</li>
    <li>Add a <a href="../docs/flexalonObject.html">Flexalon Object</a> and set one axis to &quot;Component&quot; and the other to &quot;Fixed&quot; or &quot;Fill&quot;.</li></ul>
<p>To preserve aspect ratio for text:</p>
<ul class="ml-5 list-disc"><li>Add a <a href="../docs/flexalonObject.html">Flexalon Object</a> and set one axis to &quot;Component&quot; and the other to &quot;Fixed&quot; or &quot;Fill&quot;.</li></ul></div></div></div>
</div>


			
			
		</div>
	</body>
</html>
