%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Decal <PERSON>ddy Ground 3
  m_Shader: {fileID: 4800000, guid: 16bdc16ce5b30044c9b8546f143d4db1, type: 3}
  m_ValidKeywords: []
  m_InvalidKeywords:
  - _ADD_PRECOMPUTED_VELOCITY
  - _MATERIAL_AFFECTS_ALBEDO
  - _MATERIAL_AFFECTS_MASKMAP
  - _MATERIAL_AFFECTS_NORMAL
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 1
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap:
    MotionVector: User
  disabledShaderPasses:
  - TransparentBackface
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BaseColor:
        m_Texture: {fileID: 2800000, guid: 3f19e5aabc6340ee9098748a5c075c67, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Mask:
        m_Texture: {fileID: 2800000, guid: 7d02b70a11844539aa69f4dfdf8a5771, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Normal:
        m_Texture: {fileID: 2800000, guid: ba478537f03c452f82cf7e0c4a9587c2, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _texcoord:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AddPrecomputedVelocity: 1
    - _AffectAO: 1
    - _AffectAlbedo: 1
    - _AffectEmission: 1
    - _AffectMetal: 1
    - _AffectNormal: 1
    - _AffectSmoothness: 1
    - _AlphaCutoff: 0.5
    - _AlphaCutoffEnable: 0
    - _AlphaDstBlend: 0
    - _AlphaSrcBlend: 1
    - _BlendMode: 0
    - _CullMode: 2
    - _CullModeForward: 2
    - _DecalAngleFadeSupported: 1
    - _DecalBlend: 0.5
    - _DecalColorMask0: 15
    - _DecalColorMask1: 15
    - _DecalColorMask2: 15
    - _DecalColorMask3: 12
    - _DecalMaskMapBlueScale: 1
    - _DecalMeshBiasType: 0
    - _DecalMeshDepthBias: 0
    - _DecalMeshViewBias: 0
    - _DecalQuantity: 2
    - _DecalStencilRef: 16
    - _DecalStencilWriteMask: 16
    - _DecalType: 2
    - _DoubleSidedEnable: 0
    - _DoubleSidedNormalMode: 2
    - _DrawOrder: 0
    - _DstBlend: 0
    - _EnableFogOnTransparent: 1
    - _Float0: 0.84705883
    - _MaskBlendSrc: 1
    - _NormalBlendSrc: 0
    - _NormalIntensity: 1
    - _ReceivesSSR: 1
    - _RenderQueueType: 1
    - _RequireSplitLighting: 0
    - _SmoothnessMultiplier: 0.541
    - _SrcBlend: 1
    - _StencilRef: 2
    - _StencilRefDepth: 0
    - _StencilRefDistortionVec: 64
    - _StencilRefGBuffer: 2
    - _StencilRefMV: 128
    - _StencilWriteMask: 3
    - _StencilWriteMaskDepth: 48
    - _StencilWriteMaskDistortionVec: 64
    - _StencilWriteMaskGBuffer: 51
    - _StencilWriteMaskMV: 176
    - _SurfaceType: 0
    - _TessEdgeLength: 16
    - _TessMax: 25
    - _TessMaxDisp: 25
    - _TessMin: 10
    - _TessPhongStrength: 0.5
    - _TessValue: 16
    - _TransparentBackfaceEnable: 0
    - _TransparentCullMode: 2
    - _TransparentSortPriority: 0
    - _TransparentZWrite: 0
    - _UseShadowThreshold: 0
    - _ZTestDepthEqualForOpaque: 3
    - _ZTestGBuffer: 4
    - _ZTestTransparent: 4
    - _ZWrite: 1
    m_Colors:
    - _DoubleSidedConstants: {r: 1, g: 1, b: -1, a: 0}
    - _EmissionColor: {r: 1, g: 1, b: 1, a: 1}
  m_BuildTextureStacks: []
