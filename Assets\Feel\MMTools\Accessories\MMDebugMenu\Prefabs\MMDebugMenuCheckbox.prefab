%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &2731002163667230419
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2731002163667230418}
  - component: {fileID: 2731002163667230428}
  - component: {fileID: 4925546967600633982}
  m_Layer: 0
  m_Name: MMDebugMenuCheckbox
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2731002163667230418
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2731002163667230419}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 2731002165425871624}
  - {fileID: 2731002165404421086}
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 1000, y: 75}
  m_Pivot: {x: 0, y: 0}
--- !u!114 &2731002163667230428
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2731002163667230419}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 28ad17ba84c916d499cb74e758d28e8e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Switch: {fileID: 2731002165425871639}
  SwitchText: {fileID: 2731002165404421081}
  CheckboxEventName: Checkbox
--- !u!114 &4925546967600633982
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2731002163667230419}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 306cc8c2b49d7114eaa3623786fc2126, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreLayout: 0
  m_MinWidth: -1
  m_MinHeight: -1
  m_PreferredWidth: 1000
  m_PreferredHeight: 75
  m_FlexibleWidth: -1
  m_FlexibleHeight: -1
  m_LayoutPriority: 1
--- !u!1 &2731002165404421087
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2731002165404421086}
  - component: {fileID: 2731002165404421080}
  - component: {fileID: 2731002165404421081}
  - component: {fileID: 2731002165404421082}
  - component: {fileID: 2731002165404421083}
  m_Layer: 0
  m_Name: Title
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2731002165404421086
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2731002165404421087}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 2731002163667230418}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0.5}
  m_AnchorMax: {x: 0, y: 0.5}
  m_AnchoredPosition: {x: 100, y: -8.179981}
  m_SizeDelta: {x: 827, y: 70.65}
  m_Pivot: {x: 0, y: 0.5}
--- !u!222 &2731002165404421080
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2731002165404421087}
  m_CullTransparentMesh: 0
--- !u!114 &2731002165404421081
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2731002165404421087}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5f7201a12d95ffc409449d95f23cf332, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_FontData:
    m_Font: {fileID: 12800000, guid: 53aebb2602873ed4a9807e8a254a7d2f, type: 3}
    m_FontSize: 50
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 1
    m_MaxSize: 100
    m_Alignment: 0
    m_AlignByGeometry: 0
    m_RichText: 1
    m_HorizontalOverflow: 0
    m_VerticalOverflow: 0
    m_LineSpacing: 1
  m_Text: Hello I'm a checkbox
--- !u!225 &2731002165404421082
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2731002165404421087}
  m_Enabled: 1
  m_Alpha: 1
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!114 &2731002165404421083
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2731002165404421087}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 87a286771fa86194e979f187a1691a2a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  ButtonPressedFirstTime:
    m_PersistentCalls:
      m_Calls: []
  ButtonReleased:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 2731002165425871639}
        m_MethodName: ToggleState
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  ButtonPressed:
    m_PersistentCalls:
      m_Calls: []
  DisabledSprite: {fileID: 0}
  DisabledChangeColor: 0
  DisabledColor: {r: 1, g: 1, b: 1, a: 1}
  PressedSprite: {fileID: 0}
  PressedChangeColor: 0
  PressedColor: {r: 1, g: 1, b: 1, a: 1}
  HighlightedSprite: {fileID: 0}
  HighlightedChangeColor: 0
  HighlightedColor: {r: 1, g: 1, b: 1, a: 1}
  PressedOpacity: 1
  IdleOpacity: 1
  DisabledOpacity: 1
  PressedFirstTimeDelay: 0
  ReleasedDelay: 0
  BufferDuration: 0
  Animator: {fileID: 0}
  IdleAnimationParameterName: Idle
  DisabledAnimationParameterName: Disabled
  PressedAnimationParameterName: Pressed
  MouseMode: 1
--- !u!1 &2731002165425871625
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2731002165425871624}
  - component: {fileID: 2731002165425871636}
  - component: {fileID: 2731002165425871637}
  - component: {fileID: 2731002165425871626}
  - component: {fileID: 2731002165425871627}
  - component: {fileID: 2731002165425871639}
  m_Layer: 0
  m_Name: Checkbox
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2731002165425871624
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2731002165425871625}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.5, y: 0.5, z: 0.5}
  m_Children: []
  m_Father: {fileID: 2731002163667230418}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0.5}
  m_AnchorMax: {x: 0, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0.14873886}
  m_SizeDelta: {x: 150, y: 150}
  m_Pivot: {x: 0, y: 0.5}
--- !u!222 &2731002165425871636
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2731002165425871625}
  m_CullTransparentMesh: 0
--- !u!114 &2731002165425871637
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2731002165425871625}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: -8121766140965501267, guid: 72f7a32ac9c7676409eb438757bc6b4a,
    type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!225 &2731002165425871626
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2731002165425871625}
  m_Enabled: 1
  m_Alpha: 1
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!114 &2731002165425871627
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2731002165425871625}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 67c3b5deddd7fd5438aebce3a8ea178e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  OnSprite: {fileID: -2239278724076068761, guid: 72f7a32ac9c7676409eb438757bc6b4a,
    type: 3}
  OffSprite: {fileID: -8121766140965501267, guid: 72f7a32ac9c7676409eb438757bc6b4a,
    type: 3}
  StartsOn: 0
--- !u!114 &2731002165425871639
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2731002165425871625}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 752e7f2cbc3684a4ca1c13a049c09d5d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  ButtonPressedFirstTime:
    m_PersistentCalls:
      m_Calls: []
  ButtonReleased:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 2731002165425871639}
        m_MethodName: ToggleState
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
      - m_Target: {fileID: 2731002163667230428}
        m_MethodName: TriggerCheckboxEvent
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  ButtonPressed:
    m_PersistentCalls:
      m_Calls: []
  DisabledSprite: {fileID: 0}
  DisabledChangeColor: 0
  DisabledColor: {r: 1, g: 1, b: 1, a: 1}
  PressedSprite: {fileID: 0}
  PressedChangeColor: 0
  PressedColor: {r: 1, g: 1, b: 1, a: 1}
  HighlightedSprite: {fileID: 0}
  HighlightedChangeColor: 0
  HighlightedColor: {r: 1, g: 1, b: 1, a: 1}
  PressedOpacity: 1
  IdleOpacity: 1
  DisabledOpacity: 1
  PressedFirstTimeDelay: 0
  ReleasedDelay: 0
  BufferDuration: 0
  Animator: {fileID: 0}
  IdleAnimationParameterName: Idle
  DisabledAnimationParameterName: Disabled
  PressedAnimationParameterName: Pressed
  MouseMode: 1
  SwitchKnob: {fileID: 2731002165425871627}
  SwitchState: 0
  InitialState: 0
  OnSwitchOn:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 2731002163667230428}
        m_MethodName: TriggerCheckboxEventTrue
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  OnSwitchOff:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 2731002163667230428}
        m_MethodName: TriggerCheckboxEventFalse
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
