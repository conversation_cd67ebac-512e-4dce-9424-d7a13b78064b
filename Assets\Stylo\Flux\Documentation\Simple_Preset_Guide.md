# Flux Preset System - Simple Guide

## Overview

The Flux Preset system adds a **preset field at the top of every Flux Effect inspector**. You can select a preset from your project assets and apply it instantly, while still being able to adjust individual parameters afterward.

---

## 🎯 **How It Works**

### **1. Preset Field**

- **Location**: At the very top of the Flux Effect inspector
- **Function**: Select any FluxPreset asset from your project
- **Apply Button**: Click "Apply" to instantly configure all parameters
- **Optional**: Flux works normally without any preset selected

### **2. Workflow**

1. **Select a Preset**: Choose from the dropdown or drag a FluxPreset asset
2. **Apply**: Click the "Apply" button to configure all parameters
3. **Customize**: Adjust any individual parameters as needed
4. **Save**: Create new presets from your customized settings

---

## 🎨 **Using Presets**

### **Built-in Presets Available:**

- **VHS Datamosh** - Classic retro video corruption
- **Digital Glitch** - Modern cyberpunk glitch effects
- **Subtle Compression** - Realistic video quality degradation
- **Satellite Feed Loss** - Cinematic transmission corruption

### **Creating Custom Presets:**

1. Configure your Flux Effect parameters as desired
2. Click "Save as Preset" in the inspector
3. Name your preset and choose a category
4. The preset is saved as a project asset

### **Applying Presets:**

1. Select a preset in the preset field
2. Click "Apply" to instantly configure all parameters
3. Adjust individual parameters if needed
4. Your changes don't affect the original preset

---

## 🔧 **Technical Details**

### **For Built-in Render Pipeline:**

- Preset field appears at the top of the FluxEffect component inspector
- Apply button configures all component parameters instantly
- Full undo/redo support for preset application

### **For URP (Universal Render Pipeline):**

- Preset field appears at the top of the Flux Effect volume override
- Apply button configures all volume parameters instantly
- Integrates seamlessly with Unity's Volume system

### **Asset Management:**

- Presets are ScriptableObject assets in your project
- Can be organized in folders and shared between projects
- Version control friendly (Git, Perforce, etc.)
- No runtime performance impact

---

## 🚀 **Quick Start**

### **Step 1: Create Default Presets**

```
Tools → Stylo → Flux → Create Default Presets
```

This creates 4 starter presets in your project.

### **Step 2: Use a Preset**

1. Add a Flux Effect to your scene (component or volume)
2. In the inspector, select a preset from the dropdown
3. Click "Apply" to instantly configure the effect
4. Adjust parameters as needed

### **Step 3: Create Your Own**

1. Configure Flux parameters to your liking
2. Click "Save as Preset" in the inspector
3. Name it and save - now it's available for reuse

---

## 💡 **Benefits**

### **Simple Workflow**

- **One Field**: Just a preset field at the top of the inspector
- **Instant Application**: Click Apply to configure everything
- **No Interference**: Works alongside normal parameter adjustment
- **Optional Usage**: Flux works fine without any preset

### **Creative Efficiency**

- **Quick Starting Points**: Jump to proven effect configurations
- **Consistent Results**: Reuse exact parameter combinations
- **Easy Experimentation**: Try different presets quickly
- **Custom Libraries**: Build your own preset collections

### **Professional Features**

- **Team Sharing**: Share preset assets across team members
- **Project Templates**: Include presets in project templates
- **Version Control**: Track preset changes over time
- **Asset Organization**: Organize presets in project folders

---

## 🎯 **Example Workflow**

### **Creating a VHS Effect:**

1. Select "VHS Datamosh" preset
2. Click "Apply" - instant VHS-style corruption
3. Adjust "Motion Vector Corruption" for more/less smearing
4. Tweak "Error Accumulation" for trailing artifacts
5. Save as "My VHS Style" preset for reuse

### **Building a Glitch Transition:**

1. Start with "Digital Glitch" preset
2. Click "Apply" for base digital corruption
3. Animate "Glitch Transition" parameter over time
4. Adjust "Feedback Intensity" for recursive effects
5. Save as "Transition Glitch" preset

### **Subtle Compression Look:**

1. Select "Subtle Compression" preset
2. Click "Apply" for realistic video degradation
3. Fine-tune "DCT Corruption" for compression blocks
4. Adjust "Chroma Corruption" for color bleeding
5. Perfect for realistic broadcast/streaming effects

---

**The Flux Preset system transforms parameter management from complex tweaking to simple asset selection, while preserving full creative control over individual parameters.**
