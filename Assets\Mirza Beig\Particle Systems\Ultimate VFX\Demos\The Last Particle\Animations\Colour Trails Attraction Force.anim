%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!74 &7400000
AnimationClip:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: Colour Trails Attraction Force
  serializedVersion: 6
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves: []
  m_PositionCurves: []
  m_ScaleCurves: []
  m_FloatCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 25
        inSlope: -0.006474987
        outSlope: -0.006474987
        tangentMode: 0
      - serializedVersion: 2
        time: 5
        value: -25
        inSlope: -16.15556
        outSlope: 17.956697
        tangentMode: 1
      - serializedVersion: 2
        time: 10
        value: 25
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      - serializedVersion: 2
        time: 15
        value: -10
        inSlope: Infinity
        outSlope: Infinity
        tangentMode: 103
      - serializedVersion: 2
        time: 20
        value: 10
        inSlope: Infinity
        outSlope: Infinity
        tangentMode: 103
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: force
    path: 
    classID: 114
    script: {fileID: 11500000, guid: 3bf6d970cd2b79547a98f2dbf434935f, type: 3}
  m_PPtrCurves: []
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - serializedVersion: 2
      path: 0
      attribute: 2540208936
      script: {fileID: 11500000, guid: 3bf6d970cd2b79547a98f2dbf434935f, type: 3}
      typeID: 114
      customType: 0
      isPPtrCurve: 0
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 20
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 1
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 25
        inSlope: -0.006474987
        outSlope: -0.006474987
        tangentMode: 0
      - serializedVersion: 2
        time: 5
        value: -25
        inSlope: -16.15556
        outSlope: 17.956697
        tangentMode: 1
      - serializedVersion: 2
        time: 10
        value: 25
        inSlope: 0
        outSlope: 0
        tangentMode: 136
      - serializedVersion: 2
        time: 15
        value: -10
        inSlope: Infinity
        outSlope: Infinity
        tangentMode: 103
      - serializedVersion: 2
        time: 20
        value: 10
        inSlope: Infinity
        outSlope: Infinity
        tangentMode: 103
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: force
    path: 
    classID: 114
    script: {fileID: 11500000, guid: 3bf6d970cd2b79547a98f2dbf434935f, type: 3}
  m_EulerEditorCurves: []
  m_HasGenericRootTransform: 0
  m_HasMotionFloatCurves: 0
  m_GenerateMotionCurves: 0
  m_Events: []
