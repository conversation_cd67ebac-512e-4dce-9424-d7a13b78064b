%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e78934177bebe0545ab566bb787b0282, type: 3}
  m_Name: Readme
  m_EditorClassIdentifier: 
  Icon: {fileID: 2800000, guid: c64060fa59466744386fcc42f0efb1d6, type: 3}
  Title: UV Light Reveal
  Description:
    Heading: 
    Text: This sample shows a specific texture over an object if lit with a specific
      color.
    LinkText: 
    Url: 
  PropertiesHeader:
    Heading: Properties
    Text: 
    LinkText: 
    Url: 
  Properties:
  - Heading: Base Texture
    Text: Main texture to be applied to surface.
    LinkText: 
    Url: 
  - Heading: UV Texture
    Text: Texture to be revealed under a specific light color.
    LinkText: 
    Url: 
  - Heading: Color to Be Filtered
    Text: Color value to be used to reveal hidden texture.
    LinkText: 
    Url: 
  - Heading: Difference Threshold
    Text: Threshold/error value to determine if current analyzed color is valid to
      reveal.
    LinkText: 
    Url: 
  AdditionalProperties: []
  AdditionalScripts: []
  LoadedLayout: 0
  RPType: 3
