%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7be522ae007ba5a4f87ffaaf2e793f4c, type: 3}
  m_Name: Heavy Corruption
  m_EditorClassIdentifier: 
  presetName: Heavy Corruption
  description: Extreme compression artifacts with heavy color crunching, aggressive reprojection, and maximum visual corruption for dramatic datamosh effects.
  category: Heavy
  effectIntensity: 1
  colorCrunch: 0.9
  downscaling: 3
  blockSize: 2
  oversharpening: 0.5
  dontCrunchSkybox: 1
  onlyStenciled: 0
  reprojectBaseNoise: 0.5
  reprojectBaseRerollSpeed: 12
  reprojectLengthInfluence: 2.5
  cameraMotionAmplification: 0
  cameraMotionThreshold: 0.001
  cameraMotionInfluence: 0
  cameraMotionSmoothing: 0.1
  pixelFlowIntensity: 0
  trailSmoothness: 0
  motionPersistence: 0
  flowGradient: 0
  temporalAccumulation: 0
  keyframeResetRate: 0.03
  motionVectorCorruption: 1.8
  errorAccumulation: 0.7
  dctCorruption: 0.8
  corruptionMask: {fileID: 0}
  chromaCorruption: 0
  glitchTransition: 0
  feedbackIntensity: 0
  multiScaleCorruption: 0
  jpegQuality: 15
  luminanceQuantization: 1.2
  chrominanceQuantization: 1.5
  chromaSubsampling: 1
  ringingArtifacts: 0.6
  mosquitoNoise: 0.4
  edgeSensitivity: 1.2

  visualizeMotionVectors: 0
