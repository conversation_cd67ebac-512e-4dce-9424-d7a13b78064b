#if URP_INSTALLED
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Rendering;

namespace Stylo.BFI.Universal
{
    /// <summary>
    /// Monitors BFI performance and provides optimization recommendations
    /// </summary>
    public class BFIPerformanceMonitor : MonoBehaviour
    {
        [Header("Monitoring Settings")]
        [SerializeField] private bool enableMonitoring = true;
        [SerializeField] private float updateInterval = 1f;
        [SerializeField] private int sampleCount = 60;
        [SerializeField] private bool showDebugUI = false;

        [Header("Performance Thresholds")]
        [SerializeField] private float targetFrameTime = 16.67f; // 60 FPS
        [SerializeField] private float warningFrameTime = 20f;   // 50 FPS
        [SerializeField] private float criticalFrameTime = 33.33f; // 30 FPS

        private Queue<float> frameTimeHistory = new Queue<float>();
        private Queue<float> bfiOverheadHistory = new Queue<float>();
        private float lastUpdateTime;
        private float baselineFrameTime;
        private bool hasBaseline = false;

        // Performance metrics
        public float AverageFrameTime { get; private set; }
        public float AverageFPS { get; private set; }
        public float BFIOverhead { get; private set; }
        public PerformanceStatus Status { get; private set; }

        public enum PerformanceStatus
        {
            Excellent,
            Good,
            Warning,
            Critical
        }

        private void Start()
        {
            if (enableMonitoring)
            {
                // Establish baseline performance without BFI
                EstablishBaseline();
            }
        }

        private void Update()
        {
            if (!enableMonitoring) return;

            if (Time.unscaledTime - lastUpdateTime >= updateInterval)
            {
                UpdatePerformanceMetrics();
                lastUpdateTime = Time.unscaledTime;
            }
        }

        private void EstablishBaseline()
        {
            // Try to get baseline performance measurement
            var stack = VolumeManager.instance.stack;
            var bfiEffect = stack?.GetComponent<BFIEffect>();

            if (bfiEffect != null && bfiEffect.IsActive())
            {
                // BFI is active, we'll need to estimate baseline
                baselineFrameTime = targetFrameTime; // Use target as estimate
                hasBaseline = true;
            }
            else
            {
                // BFI is not active, measure current performance as baseline
                StartCoroutine(MeasureBaseline());
            }
        }

        private System.Collections.IEnumerator MeasureBaseline()
        {
            var measurements = new List<float>();

            // Measure for a few seconds to get stable baseline
            float measureTime = 3f;
            float startTime = Time.unscaledTime;

            while (Time.unscaledTime - startTime < measureTime)
            {
                measurements.Add(Time.unscaledDeltaTime * 1000f);
                yield return null;
            }

            // Calculate average baseline
            float total = 0f;
            foreach (float measurement in measurements)
            {
                total += measurement;
            }

            baselineFrameTime = total / measurements.Count;
            hasBaseline = true;

            Debug.Log($"BFI Performance Monitor: Baseline frame time established at {baselineFrameTime:F2}ms");
        }

        private void UpdatePerformanceMetrics()
        {
            float currentFrameTime = Time.unscaledDeltaTime * 1000f;

            // Add to history
            frameTimeHistory.Enqueue(currentFrameTime);
            if (frameTimeHistory.Count > sampleCount)
            {
                frameTimeHistory.Dequeue();
            }

            // Calculate average frame time
            float total = 0f;
            foreach (float frameTime in frameTimeHistory)
            {
                total += frameTime;
            }
            AverageFrameTime = total / frameTimeHistory.Count;
            AverageFPS = 1000f / AverageFrameTime;

            // Calculate BFI overhead if we have baseline
            if (hasBaseline)
            {
                BFIOverhead = AverageFrameTime - baselineFrameTime;
                bfiOverheadHistory.Enqueue(BFIOverhead);
                if (bfiOverheadHistory.Count > sampleCount)
                {
                    bfiOverheadHistory.Dequeue();
                }
            }

            // Determine performance status
            UpdatePerformanceStatus();

            // Check for optimization opportunities
            CheckOptimizationOpportunities();
        }

        private void UpdatePerformanceStatus()
        {
            if (AverageFrameTime <= targetFrameTime)
                Status = PerformanceStatus.Excellent;
            else if (AverageFrameTime <= warningFrameTime)
                Status = PerformanceStatus.Good;
            else if (AverageFrameTime <= criticalFrameTime)
                Status = PerformanceStatus.Warning;
            else
                Status = PerformanceStatus.Critical;
        }

        private void CheckOptimizationOpportunities()
        {
            var stack = VolumeManager.instance.stack;
            var bfiEffect = stack?.GetComponent<BFIEffect>();

            if (bfiEffect == null || !bfiEffect.IsActive()) return;

            // Suggest optimizations based on performance
            if (Status == PerformanceStatus.Warning || Status == PerformanceStatus.Critical)
            {
                SuggestOptimizations(bfiEffect);
            }
        }

        private void SuggestOptimizations(BFIEffect bfiEffect)
        {
            var suggestions = new List<string>();

            // Check BFI mode
            if (bfiEffect.Mode.value == BFIEffect.BFIMode.QuadFrameRate)
            {
                suggestions.Add("Consider reducing BFI mode from 4x to 3x or 2x for better performance");
            }
            else if (bfiEffect.Mode.value == BFIEffect.BFIMode.TripleFrameRate && Status == PerformanceStatus.Critical)
            {
                suggestions.Add("Consider reducing BFI mode from 3x to 2x for better performance");
            }

            // Check motion adaptive features
            if (bfiEffect.MotionAdaptive.value)
            {
                suggestions.Add("Disable Motion Adaptive BFI to reduce processing overhead");
            }

            // Check flicker reduction
            if (bfiEffect.FlickerReductionMode.value == BFIEffect.FlickerReduction.High)
            {
                suggestions.Add("Reduce Flicker Reduction from High to Medium for better performance");
            }

            // Check subpixel precision
            if (bfiEffect.SubpixelPrecision.value > 1f)
            {
                suggestions.Add("Reduce Subpixel Precision to 1.0 or lower for better performance");
            }

            // Log suggestions
            if (suggestions.Count > 0)
            {
                Debug.LogWarning($"BFI Performance Monitor: Performance issues detected. Suggestions:\n" +
                    string.Join("\n", suggestions));
            }
        }

        private void OnGUI()
        {
            if (!showDebugUI || !enableMonitoring) return;

            // Create debug UI
            GUILayout.BeginArea(new Rect(10, 10, 300, 200));
            GUILayout.BeginVertical("box");

            GUILayout.Label("BFI Performance Monitor");

            // Performance metrics
            GUILayout.Label($"FPS: {AverageFPS:F1} ({AverageFrameTime:F2}ms)");

            if (hasBaseline)
            {
                GUILayout.Label($"BFI Overhead: {BFIOverhead:F2}ms");
            }

            // Status with color
            var oldColor = GUI.color;
            GUI.color = GetStatusColor(Status);
            GUILayout.Label($"Status: {Status}");
            GUI.color = oldColor;

            // BFI settings
            var stack = VolumeManager.instance.stack;
            var bfiEffect = stack?.GetComponent<BFIEffect>();

            if (bfiEffect != null && bfiEffect.IsActive())
            {
                GUILayout.Label($"BFI Mode: {bfiEffect.Mode.value}");
                GUILayout.Label($"Intensity: {bfiEffect.Intensity.value:F2}");
            }
            else
            {
                GUILayout.Label("BFI: Inactive");
            }

            GUILayout.EndVertical();
            GUILayout.EndArea();
        }

        private Color GetStatusColor(PerformanceStatus status)
        {
            return status switch
            {
                PerformanceStatus.Excellent => Color.green,
                PerformanceStatus.Good => Color.yellow,
                PerformanceStatus.Warning => Color.orange,
                PerformanceStatus.Critical => Color.red,
                _ => Color.white
            };
        }

        /// <summary>
        /// Get performance recommendations based on current metrics
        /// </summary>
        /// <returns>List of optimization recommendations</returns>
        public List<string> GetOptimizationRecommendations()
        {
            var recommendations = new List<string>();

            var stack = VolumeManager.instance.stack;
            var bfiEffect = stack?.GetComponent<BFIEffect>();

            if (bfiEffect == null || !bfiEffect.IsActive())
            {
                recommendations.Add("BFI is not active");
                return recommendations;
            }

            // Performance-based recommendations
            if (Status == PerformanceStatus.Critical)
            {
                recommendations.Add("Critical performance issues detected");
                recommendations.Add("Consider disabling BFI or reducing to 2x mode");
            }
            else if (Status == PerformanceStatus.Warning)
            {
                recommendations.Add("Performance warnings detected");
                recommendations.Add("Consider optimizing BFI settings");
            }

            // Display compatibility recommendations
            int refreshRate = Screen.currentResolution.refreshRate;
            var recommendedMode = BFIPresetManager.GetRecommendedPresetForRefreshRate(refreshRate);

            if (recommendedMode != null && recommendedMode.mode != bfiEffect.Mode.value)
            {
                recommendations.Add($"Consider using {recommendedMode.mode} for {refreshRate}Hz display");
            }

            return recommendations;
        }

        /// <summary>
        /// Reset performance monitoring data
        /// </summary>
        public void ResetMonitoring()
        {
            frameTimeHistory.Clear();
            bfiOverheadHistory.Clear();
            hasBaseline = false;
            EstablishBaseline();
        }
    }
}
#endif
