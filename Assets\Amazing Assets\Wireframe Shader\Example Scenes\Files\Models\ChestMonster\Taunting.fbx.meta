fileFormatVersion: 2
guid: a45e5f41380cac844ad15cb62cd7fb1f
ModelImporter:
  serializedVersion: 19301
  internalIDToNameTable:
  - first:
      1: 100000
    second: Body
  - first:
      1: 100002
    second: ChestMesh
  - first:
      1: 100004
    second: EyeBallCTRL
  - first:
      1: 100006
    second: EyeCTRL
  - first:
      1: 100008
    second: FrontLeftLeg01
  - first:
      1: 100010
    second: FrontLeftLeg02
  - first:
      1: 100012
    second: FrontLeftLeg03
  - first:
      1: 100014
    second: FrontRightLeg01
  - first:
      1: 100016
    second: FrontRightLeg02
  - first:
      1: 100018
    second: FrontRightLeg03
  - first:
      1: 100020
    second: Head
  - first:
      1: 100022
    second: LockCTRL
  - first:
      1: 100024
    second: LowerEyelid
  - first:
      1: 100026
    second: LowerTeethCTRL
  - first:
      1: 100028
    second: RearLeftLeg01
  - first:
      1: 100030
    second: RearLeftLeg02
  - first:
      1: 100032
    second: RearLeftLeg03
  - first:
      1: 100034
    second: RearRightLeg01
  - first:
      1: 100036
    second: RearRightLeg02
  - first:
      1: 100038
    second: RearRightLeg03
  - first:
      1: 100040
    second: Root
  - first:
      1: 100042
    second: //RootNode
  - first:
      1: 100044
    second: UppderEyeLid
  - first:
      1: 100046
    second: UpperTeethCTRL
  - first:
      4: 400000
    second: Body
  - first:
      4: 400002
    second: ChestMesh
  - first:
      4: 400004
    second: EyeBallCTRL
  - first:
      4: 400006
    second: EyeCTRL
  - first:
      4: 400008
    second: FrontLeftLeg01
  - first:
      4: 400010
    second: FrontLeftLeg02
  - first:
      4: 400012
    second: FrontLeftLeg03
  - first:
      4: 400014
    second: FrontRightLeg01
  - first:
      4: 400016
    second: FrontRightLeg02
  - first:
      4: 400018
    second: FrontRightLeg03
  - first:
      4: 400020
    second: Head
  - first:
      4: 400022
    second: LockCTRL
  - first:
      4: 400024
    second: LowerEyelid
  - first:
      4: 400026
    second: LowerTeethCTRL
  - first:
      4: 400028
    second: RearLeftLeg01
  - first:
      4: 400030
    second: RearLeftLeg02
  - first:
      4: 400032
    second: RearLeftLeg03
  - first:
      4: 400034
    second: RearRightLeg01
  - first:
      4: 400036
    second: RearRightLeg02
  - first:
      4: 400038
    second: RearRightLeg03
  - first:
      4: 400040
    second: Root
  - first:
      4: 400042
    second: //RootNode
  - first:
      4: 400044
    second: UppderEyeLid
  - first:
      4: 400046
    second: UpperTeethCTRL
  - first:
      21: 2100000
    second: lambert1
  - first:
      43: 4300000
    second: ChestMesh
  - first:
      74: 7400000
    second: Taunting
  - first:
      95: 9500000
    second: //RootNode
  - first:
      137: 13700000
    second: ChestMesh
  externalObjects: {}
  materials:
    materialImportMode: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: Taunting
      takeName: Take 001
      internalID: 0
      firstFrame: 0
      lastFrame: 60
      wrapMode: 2
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    fileIdsGeneration: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    meshOptimizationFlags: -1
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 1
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 1
  humanoidOversampling: 1
  avatarSetup: 0
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
