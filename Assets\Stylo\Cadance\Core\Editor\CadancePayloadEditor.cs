using UnityEngine;
using UnityEditor;
using System;
using System.Reflection;

namespace Stylo.Cadance.Editor
{
    /// <summary>
    /// Advanced payload editing system that matches Koreographer's payload editing capabilities.
    /// Provides type-specific editing interfaces for all payload types.
    /// </summary>
    public static class CadancePayloadEditor
    {
        /// <summary>
        /// Draws the payload editor for a given event, equivalent to Koreographer's payload editing.
        /// </summary>
        /// <param name="evt">Event to edit payload for</param>
        /// <param name="track">Track containing the event</param>
        /// <returns>True if payload was modified</returns>
        public static bool DrawPayloadEditor(CadanceEvent evt, CadanceTrackBase track)
        {
            if (evt == null) return false;

            bool modified = false;
            EditorGUILayout.LabelField("Event Payload", EditorStyles.boldLabel);

            // Payload type selection
            var currentPayloadType = evt.Payload?.GetType();
            var availableTypes = CadanceTrackTypeSelector.GetPayloadTypesForTrackType(track.GetType());

            // Add null option
            var typeOptions = new Type[availableTypes.Count + 1];
            typeOptions[0] = null;
            availableTypes.CopyTo(typeOptions, 1);

            var typeNames = new string[typeOptions.Length];
            typeNames[0] = "No Payload";
            for (int i = 1; i < typeOptions.Length; i++)
            {
                typeNames[i] = CadanceTrackTypeSelector.GetFriendlyPayloadTypeName(typeOptions[i]);
            }

            int currentIndex = Array.IndexOf(typeOptions, currentPayloadType);
            if (currentIndex < 0) currentIndex = 0;

            int newIndex = EditorGUILayout.Popup("Payload Type", currentIndex, typeNames);
            if (newIndex != currentIndex)
            {
                var newPayloadType = typeOptions[newIndex];
                if (newPayloadType == null)
                {
                    evt.SetPayload(null);
                }
                else
                {
                    var newPayload = CreatePayloadInstance(newPayloadType);
                    evt.SetPayload(newPayload);
                }
                modified = true;
            }

            // Draw payload-specific editor
            if (evt.Payload != null)
            {
                EditorGUILayout.Space();
                modified |= DrawPayloadSpecificEditor(evt.Payload);
            }

            return modified;
        }

        /// <summary>
        /// Draws type-specific payload editor based on payload type.
        /// </summary>
        /// <param name="payload">Payload to edit</param>
        /// <returns>True if payload was modified</returns>
        private static bool DrawPayloadSpecificEditor(IPayload payload)
        {
            if (payload == null) return false;

            bool modified = false;
            var payloadType = payload.GetType();

            // Handle built-in payload types
            switch (payload)
            {
                case IntPayload intPayload:
                    modified = DrawIntPayloadEditor(intPayload);
                    break;
                case FloatPayload floatPayload:
                    modified = DrawFloatPayloadEditor(floatPayload);
                    break;
                case TextPayload textPayload:
                    modified = DrawTextPayloadEditor(textPayload);
                    break;
                case BoolPayload boolPayload:
                    modified = DrawBoolPayloadEditor(boolPayload);
                    break;
                case ColorPayload colorPayload:
                    modified = DrawColorPayloadEditor(colorPayload);
                    break;
                case CurvePayload curvePayload:
                    modified = DrawCurvePayloadEditor(curvePayload);
                    break;
                case GradientPayload gradientPayload:
                    modified = DrawGradientPayloadEditor(gradientPayload);
                    break;
                case AssetPayload assetPayload:
                    modified = DrawAssetPayloadEditor(assetPayload);
                    break;
                case RMSPayload rmsPayload:
                    modified = DrawRMSPayloadEditor(rmsPayload);
                    break;
                case SpectrumPayload spectrumPayload:
                    modified = DrawSpectrumPayloadEditor(spectrumPayload);
                    break;
                default:
                    // Use reflection for custom payload types
                    modified = DrawGenericPayloadEditor(payload);
                    break;
            }

            return modified;
        }

        private static bool DrawIntPayloadEditor(IntPayload payload)
        {
            int oldValue = payload.IntValue;
            int newValue = EditorGUILayout.IntField("Value", oldValue);
            if (newValue != oldValue)
            {
                payload.IntValue = newValue;
                return true;
            }
            return false;
        }

        private static bool DrawFloatPayloadEditor(FloatPayload payload)
        {
            float oldValue = payload.FloatValue;
            float newValue = EditorGUILayout.FloatField("Value", oldValue);
            if (newValue != oldValue)
            {
                payload.FloatValue = newValue;
                return true;
            }
            return false;
        }

        private static bool DrawTextPayloadEditor(TextPayload payload)
        {
            string oldValue = payload.TextValue;
            string newValue = EditorGUILayout.TextField("Value", oldValue);
            if (newValue != oldValue)
            {
                payload.TextValue = newValue;
                return true;
            }
            return false;
        }

        private static bool DrawBoolPayloadEditor(BoolPayload payload)
        {
            bool oldValue = payload.BoolValue;
            bool newValue = EditorGUILayout.Toggle("Value", oldValue);
            if (newValue != oldValue)
            {
                payload.BoolValue = newValue;
                return true;
            }
            return false;
        }

        private static bool DrawAssetPayloadEditor(AssetPayload payload)
        {
            UnityEngine.Object oldValue = payload.Asset;
            UnityEngine.Object newValue = EditorGUILayout.ObjectField("Asset", oldValue, typeof(UnityEngine.Object), false);
            if (newValue != oldValue)
            {
                payload.Asset = newValue;
                return true;
            }
            return false;
        }

        private static bool DrawRMSPayloadEditor(RMSPayload payload)
        {
            bool modified = false;

            float oldRMS = payload.RMSValue;
            float newRMS = EditorGUILayout.FloatField("RMS Value", oldRMS);
            if (newRMS != oldRMS)
            {
                payload.RMSValue = newRMS;
                modified = true;
            }

            float oldPeak = payload.PeakValue;
            float newPeak = EditorGUILayout.FloatField("Peak Value", oldPeak);
            if (newPeak != oldPeak)
            {
                payload.PeakValue = newPeak;
                modified = true;
            }

            return modified;
        }

        private static bool DrawSpectrumPayloadEditor(SpectrumPayload payload)
        {
            EditorGUILayout.LabelField("Spectrum Data", EditorStyles.boldLabel);
            EditorGUILayout.LabelField($"Samples: {payload.SpectrumData?.Length ?? 0}");
            EditorGUILayout.HelpBox("Spectrum data is read-only and generated by audio analysis.", MessageType.Info);
            return false;
        }

        private static bool DrawColorPayloadEditor(ColorPayload payload)
        {
            Color oldValue = payload.ColorValue;
            Color newValue = EditorGUILayout.ColorField("Value", oldValue);
            if (newValue != oldValue)
            {
                payload.ColorValue = newValue;
                return true;
            }
            return false;
        }

        private static bool DrawCurvePayloadEditor(CurvePayload payload)
        {
            AnimationCurve oldValue = payload.Curve;
            AnimationCurve newValue = EditorGUILayout.CurveField("Value", oldValue);
            if (newValue != oldValue)
            {
                payload.Curve = newValue;
                return true;
            }
            return false;
        }

        private static bool DrawGradientPayloadEditor(GradientPayload payload)
        {
            Gradient oldValue = payload.Gradient;
            Gradient newValue = EditorGUILayout.GradientField("Value", oldValue);
            if (newValue != oldValue)
            {
                payload.Gradient = newValue;
                return true;
            }
            return false;
        }

        /// <summary>
        /// Generic payload editor using reflection for custom payload types.
        /// </summary>
        /// <param name="payload">Payload to edit</param>
        /// <returns>True if payload was modified</returns>
        private static bool DrawGenericPayloadEditor(IPayload payload)
        {
            bool modified = false;
            var type = payload.GetType();
            var fields = type.GetFields(BindingFlags.Public | BindingFlags.Instance);
            var properties = type.GetProperties(BindingFlags.Public | BindingFlags.Instance);

            EditorGUILayout.LabelField($"Custom Payload: {type.Name}", EditorStyles.boldLabel);

            // Edit public fields
            foreach (var field in fields)
            {
                if (field.IsInitOnly) continue;

                var oldValue = field.GetValue(payload);
                var newValue = DrawFieldEditor(field.Name, field.FieldType, oldValue);

                if (!Equals(oldValue, newValue))
                {
                    field.SetValue(payload, newValue);
                    modified = true;
                }
            }

            // Edit public properties with setters
            foreach (var property in properties)
            {
                if (!property.CanWrite || !property.CanRead) continue;

                var oldValue = property.GetValue(payload);
                var newValue = DrawFieldEditor(property.Name, property.PropertyType, oldValue);

                if (!Equals(oldValue, newValue))
                {
                    property.SetValue(payload, newValue);
                    modified = true;
                }
            }

            return modified;
        }

        /// <summary>
        /// Draws an editor for a specific field type.
        /// </summary>
        /// <param name="name">Field name</param>
        /// <param name="type">Field type</param>
        /// <param name="value">Current value</param>
        /// <returns>New value</returns>
        private static object DrawFieldEditor(string name, Type type, object value)
        {
            if (type == typeof(int))
                return EditorGUILayout.IntField(name, (int)(value ?? 0));
            else if (type == typeof(float))
                return EditorGUILayout.FloatField(name, (float)(value ?? 0f));
            else if (type == typeof(string))
                return EditorGUILayout.TextField(name, (string)(value ?? ""));
            else if (type == typeof(bool))
                return EditorGUILayout.Toggle(name, (bool)(value ?? false));
            else if (type == typeof(Color))
                return EditorGUILayout.ColorField(name, (Color)(value ?? Color.white));
            else if (type == typeof(AnimationCurve))
                return EditorGUILayout.CurveField(name, (AnimationCurve)(value ?? new AnimationCurve()));
            else if (type == typeof(Gradient))
                return EditorGUILayout.GradientField(name, (Gradient)(value ?? new Gradient()));
            else if (typeof(UnityEngine.Object).IsAssignableFrom(type))
                return EditorGUILayout.ObjectField(name, (UnityEngine.Object)value, type, false);
            else
            {
                EditorGUILayout.LabelField(name, $"Unsupported type: {type.Name}");
                return value;
            }
        }

        /// <summary>
        /// Creates an instance of the specified payload type.
        /// </summary>
        /// <param name="payloadType">Type of payload to create</param>
        /// <returns>New payload instance</returns>
        private static IPayload CreatePayloadInstance(Type payloadType)
        {
            try
            {
                return (IPayload)Activator.CreateInstance(payloadType);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[CadancePayloadEditor] Failed to create payload of type {payloadType.Name}: {ex.Message}");
                return null;
            }
        }
    }
}
