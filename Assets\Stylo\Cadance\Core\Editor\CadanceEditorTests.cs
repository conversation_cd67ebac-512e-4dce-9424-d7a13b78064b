using UnityEngine;
using UnityEditor;
using System.IO;

namespace Stylo.Cadance.Editor
{
    /// <summary>
    /// Simple validation tests for the Cadance editor functionality.
    /// These tests verify that the core editor improvements work correctly.
    /// </summary>
    public static class CadanceEditorTests
    {
        [MenuItem("Stylo/Cadance/Tools/Run Editor Tests", false, 200)]
        public static void RunEditorTests()
        {
            Debug.Log("[Cadance Tests] Starting editor functionality tests...");
            
            bool allTestsPassed = true;
            
            // Test 1: Validate CadanceAsset creation
            allTestsPassed &= TestCadanceAssetCreation();
            
            // Test 2: Validate AudioClip validation system
            allTestsPassed &= TestAudioClipValidation();
            
            // Test 3: Validate editor window opening
            allTestsPassed &= TestEditorWindowOpening();
            
            // Test 4: Validate asset health dashboard
            allTestsPassed &= TestAssetHealthDashboard();
            
            // Test 5: Validate auto-discovery functionality
            allTestsPassed &= TestAutoDiscoveryFunctionality();
            
            if (allTestsPassed)
            {
                Debug.Log("[Cadance Tests] ✅ All tests passed!");
                EditorUtility.DisplayDialog("Cadance Editor Tests", 
                    "All tests passed successfully!\n\nThe Cadance editor improvements are working correctly.", "OK");
            }
            else
            {
                Debug.LogError("[Cadance Tests] ❌ Some tests failed. Check the console for details.");
                EditorUtility.DisplayDialog("Cadance Editor Tests", 
                    "Some tests failed. Check the console for details.", "OK");
            }
        }
        
        private static bool TestCadanceAssetCreation()
        {
            try
            {
                Debug.Log("[Cadance Tests] Testing CadanceAsset creation...");
                
                var testAsset = ScriptableObject.CreateInstance<CadanceAsset>();
                if (testAsset == null)
                {
                    Debug.LogError("[Cadance Tests] Failed to create CadanceAsset instance");
                    return false;
                }
                
                // Test basic properties
                testAsset.SampleRate = 44100;
                testAsset.SourceClipName = "TestClip";
                
                if (testAsset.SampleRate != 44100)
                {
                    Debug.LogError("[Cadance Tests] SampleRate property not working correctly");
                    return false;
                }
                
                if (testAsset.SourceClipName != "TestClip")
                {
                    Debug.LogError("[Cadance Tests] SourceClipName property not working correctly");
                    return false;
                }
                
                Object.DestroyImmediate(testAsset);
                Debug.Log("[Cadance Tests] ✅ CadanceAsset creation test passed");
                return true;
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[Cadance Tests] CadanceAsset creation test failed: {ex.Message}");
                return false;
            }
        }
        
        private static bool TestAudioClipValidation()
        {
            try
            {
                Debug.Log("[Cadance Tests] Testing AudioClip validation...");
                
                // Test null validation
                if (CadanceAudioValidator.IsAudioClipValid(null))
                {
                    Debug.LogError("[Cadance Tests] Null AudioClip should not be valid");
                    return false;
                }
                
                // Test detailed validation
                var result = CadanceAudioValidator.ValidateAudioClipDetailed(null);
                if (result.IsValid)
                {
                    Debug.LogError("[Cadance Tests] Null AudioClip detailed validation should fail");
                    return false;
                }
                
                if (result.Issues.Count == 0)
                {
                    Debug.LogError("[Cadance Tests] Null AudioClip should have validation issues");
                    return false;
                }
                
                Debug.Log("[Cadance Tests] ✅ AudioClip validation test passed");
                return true;
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[Cadance Tests] AudioClip validation test failed: {ex.Message}");
                return false;
            }
        }
        
        private static bool TestEditorWindowOpening()
        {
            try
            {
                Debug.Log("[Cadance Tests] Testing editor window opening...");
                
                // Test opening editor window without asset
                var window = EditorWindow.GetWindow<CadanceEditorWindow>("Test Cadance Editor");
                if (window == null)
                {
                    Debug.LogError("[Cadance Tests] Failed to create CadanceEditorWindow");
                    return false;
                }
                
                // Test opening with asset
                var testAsset = ScriptableObject.CreateInstance<CadanceAsset>();
                CadanceEditorWindow.OpenCadance(testAsset);
                
                window.Close();
                Object.DestroyImmediate(testAsset);
                
                Debug.Log("[Cadance Tests] ✅ Editor window opening test passed");
                return true;
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[Cadance Tests] Editor window opening test failed: {ex.Message}");
                return false;
            }
        }
        
        private static bool TestAssetHealthDashboard()
        {
            try
            {
                Debug.Log("[Cadance Tests] Testing asset health dashboard...");
                
                // Test opening dashboard
                var dashboard = EditorWindow.GetWindow<CadanceAssetHealthDashboard>("Test Dashboard");
                if (dashboard == null)
                {
                    Debug.LogError("[Cadance Tests] Failed to create CadanceAssetHealthDashboard");
                    return false;
                }
                
                // Test with asset
                var testAsset = ScriptableObject.CreateInstance<CadanceAsset>();
                CadanceAssetHealthDashboard.ShowWindow(testAsset);
                
                dashboard.Close();
                Object.DestroyImmediate(testAsset);
                
                Debug.Log("[Cadance Tests] ✅ Asset health dashboard test passed");
                return true;
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[Cadance Tests] Asset health dashboard test failed: {ex.Message}");
                return false;
            }
        }
        
        private static bool TestAutoDiscoveryFunctionality()
        {
            try
            {
                Debug.Log("[Cadance Tests] Testing auto-discovery functionality...");
                
                // Test project validation
                var projectValidation = CadanceAudioValidator.ValidateProjectAudioClips();
                if (projectValidation == null)
                {
                    Debug.LogError("[Cadance Tests] Project validation returned null");
                    return false;
                }
                
                // Test discovery tool
                var discoveryTool = EditorWindow.GetWindow<AudioClipDiscoveryTool>("Test Discovery");
                if (discoveryTool == null)
                {
                    Debug.LogError("[Cadance Tests] Failed to create AudioClipDiscoveryTool");
                    return false;
                }
                
                discoveryTool.Close();
                
                Debug.Log("[Cadance Tests] ✅ Auto-discovery functionality test passed");
                return true;
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[Cadance Tests] Auto-discovery functionality test failed: {ex.Message}");
                return false;
            }
        }
    }
}
