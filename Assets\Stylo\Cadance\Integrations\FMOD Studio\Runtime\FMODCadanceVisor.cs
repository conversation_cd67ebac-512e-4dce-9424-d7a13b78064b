using UnityEngine;
using System;
using System.Collections.Generic;
using FMOD.Studio;
using FMODUnity;
using Stylo.Cadance;

namespace Stylo.Cadance.FMOD
{
    /// <summary>
    /// The FMODCadanceVisor is the FMOD Integration's equivalent to Koreographer's "FMOD Event Description Visor".
    /// It provides a simple interface for allowing every single instance of an FMOD Event to be
    /// synchronized with Cadance, regardless of startup approach.
    /// </summary>
    [AddComponentMenu("Stylo/Cadance/FMOD/FMOD Cadance Visor")]
    public class FMODCadanceVisor : MonoBehaviour, ICadancePlayer
    {
        #region Custom Structs

        /// <summary>
        /// A pairing of FMOD Event Description to a CadanceSet or FMODKoreographySet instance.
        /// </summary>
        [Serializable]
        public struct EventCadancePair
        {
            [Tooltip("The FMOD Studio Event to monitor")]
            public EventReference Event;

            [Tooltip("The CadanceSet to use when this event is played (collection of CadanceAssets)")]
            public CadanceSet cadanceSet;

            [Tooltip("The FMODKoreographySet to use when this event is played (for backward compatibility)")]
            public SonicBloom.Koreo.Players.FMODStudio.FMODKoreographySet koreographySet;

            [Tooltip("Optional custom Cadance ID. If empty, will use the event path")]
            public string customCadanceID;

            [NonSerialized]
            public EventDescription description;

            /// <summary>
            /// Gets the effective Cadance ID for this pair.
            /// </summary>
            public string GetCadanceID()
            {
                if (!string.IsNullOrEmpty(customCadanceID))
                    return customCadanceID;

                return Event.Path;
            }

            /// <summary>
            /// Gets whether this pair has valid choreography data (CadanceSet or FMODKoreographySet).
            /// </summary>
            public bool HasChoreographyData()
            {
                return (cadanceSet != null && cadanceSet.Count > 0) ||
                       (koreographySet != null && koreographySet.koreographies.Count > 0);
            }

            /// <summary>
            /// Gets the source clip name from CadanceSet or the first Koreography in the set.
            /// </summary>
            public string GetSourceClipName()
            {
                if (cadanceSet != null && cadanceSet.Count > 0)
                {
                    var firstCadance = cadanceSet.GetCadanceAtIndex(0);
                    return firstCadance?.SourceClipName ?? string.Empty;
                }
                if (koreographySet != null && koreographySet.koreographies.Count > 0)
                    return koreographySet.koreographies[0].koreo?.SourceClipName ?? string.Empty;
                return string.Empty;
            }
        }

        #endregion

        #region Fields

        /// <summary>
        /// The set of Event Description and CadanceSet pairings for this visor to manage.
        /// </summary>
        [SerializeField]
        [Tooltip("Pairs of FMOD Studio Events and CadanceSets. Whenever a specified Event is played, it will be synchronized using the Cadance found in the CadanceSet.")]
        List<EventCadancePair> cadancedEvents = new List<EventCadancePair>();

        /// <summary>
        /// The target Cadance component to use for event triggering.
        /// </summary>
        [SerializeField]
        [Tooltip("A specific Cadance component to use for event triggering. If not specified, the singleton [global] instance will be used.")]
        Cadance targetCadance = null;

        /// <summary>
        /// Whether or not this component should register itself as the music player for the target
        /// Cadance component.
        /// </summary>
        [SerializeField]
        [Tooltip("Whether or not this visor should act as the Music Player for the Target Cadance component (or the singleton [global] instance if none is specified).")]
        bool isMusicPlayer = false;

        /// <summary>
        /// Whether to automatically register events with FMODCadanceManager on Start.
        /// </summary>
        [SerializeField]
        [Tooltip("Automatically register all configured events with FMODCadanceManager when this component starts.")]
        bool autoRegisterEvents = true;

        /// <summary>
        /// Whether to enable debug logging for this visor.
        /// </summary>
        [SerializeField]
        [Tooltip("Enable debug logging to track event registration and management.")]
        bool enableDebugLogging = false;

        // Runtime tracking
        private Dictionary<string, string> registeredEventPaths = new Dictionary<string, string>();
        private bool isInitialized = false;

        #endregion

        #region Properties

        /// <summary>
        /// Gets the list of configured event-cadance pairs.
        /// </summary>
        public List<EventCadancePair> CadancedEvents => cadancedEvents;

        /// <summary>
        /// Gets whether this visor is initialized.
        /// </summary>
        public bool IsInitialized => isInitialized;

        #endregion

        #region Unity Lifecycle

        /// <summary>
        /// Initializes the EventDescriptions under the visor's purview.
        /// </summary>
        void Awake()
        {
            // Initialize event descriptions
            int numEvts = cadancedEvents.Count;
            for (int i = 0; i < numEvts; ++i)
            {
                EventCadancePair item = cadancedEvents[i];

                if (!item.Event.IsNull)
                {
                    // Get and store the EventDescription
                    item.description = RuntimeManager.GetEventDescription(item.Event);

                    // Store the updated item
                    cadancedEvents[i] = item;

                    if (enableDebugLogging)
                    {
                        UnityEngine.Debug.Log($"[FMODCadanceVisor] Initialized event description for: {item.Event.Path}");
                    }
                }
            }
        }

        /// <summary>
        /// Handles component startup. Cadance loading and registration occurs here.
        /// </summary>
        void Start()
        {
            // Set up target Cadance
            if (targetCadance == null)
            {
                targetCadance = Cadance.Instance;
            }

            if (targetCadance == null)
            {
                UnityEngine.Debug.LogWarning("No Cadance component specified and could not find the " +
                                 "singleton. Please add a Cadance component to the scene " +
                                 "or make sure to specify a default Cadance. Disabling " +
                                 "this FMOD Cadance Visor (on GameObject '" +
                                 gameObject.name + ").");
                enabled = false;
                return;
            }
            else
            {
                if (isMusicPlayer)
                {
                    targetCadance.musicPlaybackController = this;
                }

                // Set initialized flag BEFORE calling other methods
                isInitialized = true;

                if (autoRegisterEvents)
                {
                    RegisterAllEvents();
                }

                // Load all CadanceAssets into the target Cadance system
                LoadAllCadanceAssets();
            }
        }

        void OnDestroy()
        {
            if (isMusicPlayer && targetCadance != null && targetCadance.musicPlaybackController == this)
            {
                targetCadance.musicPlaybackController = null;
            }

            // Unload all CadanceAssets
            UnloadAllCadanceAssets();

            // Unregister all events
            UnregisterAllEvents();
        }

        #endregion

        #region Public API

        /// <summary>
        /// Loads all CadanceAssets and FMODKoreographySet assets into the target Cadance system.
        /// </summary>
        public void LoadAllCadanceAssets()
        {
            if (targetCadance == null)
            {
                UnityEngine.Debug.LogWarning("[FMODCadanceVisor] Cannot load choreography assets: no target Cadance specified");
                return;
            }

            int loadedCount = 0;
            foreach (var eventPair in cadancedEvents)
            {
                if (eventPair.cadanceSet != null)
                {
                    // Load the entire CadanceSet
                    eventPair.cadanceSet.LoadAll();
                    loadedCount += eventPair.cadanceSet.Count;

                    if (enableDebugLogging)
                    {
                        UnityEngine.Debug.Log($"[FMODCadanceVisor] Loaded CadanceSet: {eventPair.cadanceSet.name} ({eventPair.cadanceSet.Count} files)");
                    }
                }
                else if (eventPair.koreographySet != null)
                {
                    // Load the entire FMODKoreographySet
                    targetCadance.LoadFMODKoreographySet(eventPair.koreographySet);
                    loadedCount += eventPair.koreographySet.koreographies.Count;

                    if (enableDebugLogging)
                    {
                        UnityEngine.Debug.Log($"[FMODCadanceVisor] Loaded FMODKoreographySet: {eventPair.koreographySet.name} ({eventPair.koreographySet.koreographies.Count} files)");
                    }
                }
            }

            if (enableDebugLogging)
            {
                UnityEngine.Debug.Log($"[FMODCadanceVisor] Successfully loaded {loadedCount} choreography assets into Cadance system");
            }
        }

        /// <summary>
        /// Registers all configured events with the FMODCadanceManager.
        /// </summary>
        public void RegisterAllEvents()
        {
            if (!isInitialized && Application.isPlaying)
            {
                UnityEngine.Debug.LogWarning("[FMODCadanceVisor] Cannot register events before initialization");
                return;
            }

            var manager = FMODCadanceManager.Instance;
            int registeredCount = 0;

            foreach (var eventPair in cadancedEvents)
            {
                if (eventPair.Event.IsNull || !eventPair.HasChoreographyData())
                    continue;

                string cadanceID = eventPair.GetCadanceID();
                string eventPath = eventPair.Event.Path;

                // Use first asset from CadanceSet, otherwise the Koreography will be converted automatically
                CadanceAsset assetToRegister = null;
                if (eventPair.cadanceSet != null && eventPair.cadanceSet.Count > 0)
                {
                    assetToRegister = eventPair.cadanceSet.GetCadanceAtIndex(0);
                }

                if (manager.RegisterFMODEvent(cadanceID, eventPath, assetToRegister))
                {
                    registeredEventPaths[cadanceID] = eventPath;
                    registeredCount++;

                    if (enableDebugLogging)
                    {
                        string assetType = eventPair.cadanceSet != null ? "CadanceSet" : "Koreography";
                        UnityEngine.Debug.Log($"[FMODCadanceVisor] Registered event: {eventPath} with Cadance ID: {cadanceID} using {assetType}");
                    }
                }
            }

            if (enableDebugLogging)
            {
                UnityEngine.Debug.Log($"[FMODCadanceVisor] Successfully registered {registeredCount} out of {cadancedEvents.Count} events");
            }
        }

        /// <summary>
        /// Unloads all CadanceAssets and converted FMODKoreographySet assets from the target Cadance system.
        /// </summary>
        public void UnloadAllCadanceAssets()
        {
            if (targetCadance == null) return;

            int unloadedCount = 0;
            foreach (var eventPair in cadancedEvents)
            {
                if (eventPair.cadanceSet != null)
                {
                    // Unload the entire CadanceSet
                    eventPair.cadanceSet.UnloadAll();
                    unloadedCount += eventPair.cadanceSet.Count;

                    if (enableDebugLogging)
                    {
                        UnityEngine.Debug.Log($"[FMODCadanceVisor] Unloaded CadanceSet: {eventPair.cadanceSet.name} ({eventPair.cadanceSet.Count} files)");
                    }
                }
                else if (eventPair.koreographySet != null)
                {
                    // Unload the entire FMODKoreographySet
                    targetCadance.UnloadFMODKoreographySet(eventPair.koreographySet);
                    unloadedCount += eventPair.koreographySet.koreographies.Count;

                    if (enableDebugLogging)
                    {
                        UnityEngine.Debug.Log($"[FMODCadanceVisor] Unloaded FMODKoreographySet: {eventPair.koreographySet.name} ({eventPair.koreographySet.koreographies.Count} files)");
                    }
                }
            }

            if (enableDebugLogging)
            {
                UnityEngine.Debug.Log($"[FMODCadanceVisor] Successfully unloaded {unloadedCount} choreography assets from Cadance system");
            }
        }

        /// <summary>
        /// Unregisters all events from the FMODCadanceManager.
        /// </summary>
        public void UnregisterAllEvents()
        {
            var manager = FMODCadanceManager.Instance;
            if (manager == null) return;

            foreach (var kvp in registeredEventPaths)
            {
                manager.UnregisterFMODEvent(kvp.Key);

                if (enableDebugLogging)
                {
                    UnityEngine.Debug.Log($"[FMODCadanceVisor] Unregistered event: {kvp.Value} with Cadance ID: {kvp.Key}");
                }
            }

            registeredEventPaths.Clear();
        }

        /// <summary>
        /// Plays a specific event by its Cadance ID.
        /// </summary>
        /// <param name="cadanceID">The Cadance ID of the event to play</param>
        /// <returns>True if the event was played successfully</returns>
        public bool PlayEvent(string cadanceID)
        {
            var manager = FMODCadanceManager.Instance;
            return manager != null && manager.PlayEvent(cadanceID);
        }

        /// <summary>
        /// Stops a specific event by its Cadance ID.
        /// </summary>
        /// <param name="cadanceID">The Cadance ID of the event to stop</param>
        /// <returns>True if the event was stopped successfully</returns>
        public bool StopEvent(string cadanceID)
        {
            var manager = FMODCadanceManager.Instance;
            return manager != null && manager.StopEvent(cadanceID);
        }

        /// <summary>
        /// Gets the event tracker for a specific Cadance ID.
        /// </summary>
        /// <param name="cadanceID">The Cadance ID</param>
        /// <returns>The event tracker, or null if not found</returns>
        public FMODEventInstanceTracker GetEventTracker(string cadanceID)
        {
            var manager = FMODCadanceManager.Instance;
            return manager?.GetEventTracker(cadanceID);
        }

        #endregion

        #region ICadancePlayer Implementation

        public int GetSampleTimeForClip(string clipName)
        {
            // Find the first event that matches this clip name
            foreach (var eventPair in cadancedEvents)
            {
                if (eventPair.GetSourceClipName() == clipName)
                {
                    var tracker = GetEventTracker(eventPair.GetCadanceID());
                    if (tracker != null)
                    {
                        return tracker.CurrentSamplePosition;
                    }
                }
            }
            return 0;
        }

        public int GetTotalSampleTimeForClip(string clipName)
        {
            // Find the first event that matches this clip name
            foreach (var eventPair in cadancedEvents)
            {
                if (eventPair.GetSourceClipName() == clipName)
                {
                    var tracker = GetEventTracker(eventPair.GetCadanceID());
                    if (tracker != null)
                    {
                        return tracker.TotalSampleLength;
                    }
                    // Fallback to asset if tracker not available
                    if (eventPair.cadanceSet != null && eventPair.cadanceSet.Count > 0)
                    {
                        var firstCadance = eventPair.cadanceSet.GetCadanceAtIndex(0);
                        if (firstCadance != null)
                        {
                            return firstCadance.TotalSampleCount;
                        }
                    }
                    else if (eventPair.koreographySet != null && eventPair.koreographySet.koreographies.Count > 0)
                    {
                        var firstKoreo = eventPair.koreographySet.koreographies[0].koreo;
                        if (firstKoreo != null && firstKoreo.SourceClip != null)
                        {
                            return firstKoreo.SourceClip.samples;
                        }
                    }
                }
            }
            return 0;
        }

        public bool GetIsPlaying(string clipName)
        {
            // Find the first event that matches this clip name
            foreach (var eventPair in cadancedEvents)
            {
                if (eventPair.GetSourceClipName() == clipName)
                {
                    var tracker = GetEventTracker(eventPair.GetCadanceID());
                    if (tracker != null)
                    {
                        return tracker.IsPlaying;
                    }
                }
            }
            return false;
        }

        public float GetPitch(string clipName)
        {
            // Find the first event that matches this clip name
            foreach (var eventPair in cadancedEvents)
            {
                if (eventPair.GetSourceClipName() == clipName)
                {
                    var tracker = GetEventTracker(eventPair.GetCadanceID());
                    if (tracker != null)
                    {
                        return tracker.CurrentPitch;
                    }
                }
            }
            return 1f; // Default pitch
        }

        public string GetCurrentClipName()
        {
            // Return the first playing clip name
            foreach (var eventPair in cadancedEvents)
            {
                string clipName = eventPair.GetSourceClipName();
                if (!string.IsNullOrEmpty(clipName) && GetIsPlaying(clipName))
                {
                    return clipName;
                }
            }
            return string.Empty;
        }

        #endregion

        #region Runtime Inspection (Professional Feature)

        /// <summary>
        /// Gets comprehensive runtime inspection data for debugging and monitoring.
        /// Equivalent to Koreographer's runtime asset display functionality.
        /// </summary>
        public RuntimeInspectionData GetRuntimeInspectionData()
        {
            var data = new RuntimeInspectionData
            {
                isInitialized = isInitialized,
                isMusicPlayer = isMusicPlayer,
                totalConfiguredEvents = cadancedEvents.Count,
                registeredEventCount = registeredEventPaths.Count,
                loadedCadanceSets = new List<string>(),
                loadedKoreographySets = new List<string>(),
                activeEventTrackers = new List<string>(),
                eventMappings = new List<EventMappingInfo>()
            };

            // Collect loaded CadanceSets and KoreographySets
            foreach (var eventPair in cadancedEvents)
            {
                if (eventPair.cadanceSet != null)
                {
                    data.loadedCadanceSets.Add($"{eventPair.cadanceSet.name} ({eventPair.cadanceSet.Count} assets)");
                }
                if (eventPair.koreographySet != null)
                {
                    data.loadedKoreographySets.Add($"{eventPair.koreographySet.name} ({eventPair.koreographySet.koreographies.Count} assets)");
                }

                // Collect event mapping information
                var mappingInfo = new EventMappingInfo
                {
                    eventPath = eventPair.Event.Path,
                    cadanceID = eventPair.GetCadanceID(),
                    hasChoreographyData = eventPair.HasChoreographyData(),
                    sourceClipName = eventPair.GetSourceClipName(),
                    isRegistered = registeredEventPaths.ContainsKey(eventPair.GetCadanceID())
                };

                // Check if event tracker is active
                var tracker = GetEventTracker(eventPair.GetCadanceID());
                if (tracker != null)
                {
                    mappingInfo.isActive = tracker.IsPlaying;
                    mappingInfo.currentSamplePosition = tracker.CurrentSamplePosition;
                    data.activeEventTrackers.Add(eventPair.GetCadanceID());
                }

                data.eventMappings.Add(mappingInfo);
            }

            return data;
        }

        /// <summary>
        /// Runtime inspection data structure for professional debugging and monitoring.
        /// </summary>
        [System.Serializable]
        public class RuntimeInspectionData
        {
            public bool isInitialized;
            public bool isMusicPlayer;
            public int totalConfiguredEvents;
            public int registeredEventCount;
            public List<string> loadedCadanceSets;
            public List<string> loadedKoreographySets;
            public List<string> activeEventTrackers;
            public List<EventMappingInfo> eventMappings;

            public override string ToString()
            {
                var sb = new System.Text.StringBuilder();
                sb.AppendLine("=== FMOD Cadance Visor Runtime Inspection ===");
                sb.AppendLine($"Initialized: {isInitialized}");
                sb.AppendLine($"Music Player: {isMusicPlayer}");
                sb.AppendLine($"Configured Events: {totalConfiguredEvents}");
                sb.AppendLine($"Registered Events: {registeredEventCount}");
                sb.AppendLine($"Active Trackers: {activeEventTrackers.Count}");
                sb.AppendLine();

                if (loadedCadanceSets.Count > 0)
                {
                    sb.AppendLine("Loaded CadanceSets:");
                    foreach (var set in loadedCadanceSets)
                    {
                        sb.AppendLine($"  - {set}");
                    }
                    sb.AppendLine();
                }

                if (loadedKoreographySets.Count > 0)
                {
                    sb.AppendLine("Loaded KoreographySets:");
                    foreach (var set in loadedKoreographySets)
                    {
                        sb.AppendLine($"  - {set}");
                    }
                    sb.AppendLine();
                }

                if (eventMappings.Count > 0)
                {
                    sb.AppendLine("Event Mappings:");
                    foreach (var mapping in eventMappings)
                    {
                        sb.AppendLine($"  - {mapping.eventPath} -> {mapping.cadanceID}");
                        sb.AppendLine($"    Registered: {mapping.isRegistered}, Active: {mapping.isActive}");
                        if (!string.IsNullOrEmpty(mapping.sourceClipName))
                        {
                            sb.AppendLine($"    Source Clip: {mapping.sourceClipName}");
                        }
                    }
                }

                return sb.ToString();
            }
        }

        /// <summary>
        /// Information about an individual event mapping.
        /// </summary>
        [System.Serializable]
        public class EventMappingInfo
        {
            public string eventPath;
            public string cadanceID;
            public bool hasChoreographyData;
            public string sourceClipName;
            public bool isRegistered;
            public bool isActive;
            public int currentSamplePosition;
        }

        /// <summary>
        /// Logs comprehensive runtime inspection data to the console.
        /// Useful for debugging and monitoring during development.
        /// </summary>
        [ContextMenu("Log Runtime Inspection Data")]
        public void LogRuntimeInspectionData()
        {
            var data = GetRuntimeInspectionData();
            UnityEngine.Debug.Log($"[FMODCadanceVisor] Runtime Inspection:\n{data}");
        }

        /// <summary>
        /// Validates all configured event mappings and reports any issues.
        /// Professional feature for ensuring proper setup.
        /// </summary>
        [ContextMenu("Validate Event Mappings")]
        public void ValidateEventMappings()
        {
            var issues = new List<string>();
            int validMappings = 0;

            foreach (var eventPair in cadancedEvents)
            {
                if (eventPair.Event.IsNull)
                {
                    issues.Add("Event reference is null or invalid");
                    continue;
                }

                if (!eventPair.HasChoreographyData())
                {
                    issues.Add($"Event '{eventPair.Event.Path}' has no choreography data (CadanceSet or KoreographySet)");
                    continue;
                }

                if (string.IsNullOrEmpty(eventPair.GetSourceClipName()))
                {
                    issues.Add($"Event '{eventPair.Event.Path}' has no source clip name");
                }

                validMappings++;
            }

            if (issues.Count == 0)
            {
                UnityEngine.Debug.Log($"[FMODCadanceVisor] Validation passed! All {validMappings} event mappings are valid.");
            }
            else
            {
                UnityEngine.Debug.LogWarning($"[FMODCadanceVisor] Validation found {issues.Count} issues:\n" + string.Join("\n", issues));
            }
        }

        #endregion
    }
}
