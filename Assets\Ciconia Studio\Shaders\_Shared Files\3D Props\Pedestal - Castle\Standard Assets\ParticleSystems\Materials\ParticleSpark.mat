%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 3
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: ParticleSpark
  m_Shader: {fileID: 10720, guid: 0000000000000000f000000000000000, type: 0}
  m_ShaderKeywords: []
  m_CustomRenderQueue: -1
  m_SavedProperties:
    serializedVersion: 2
    m_TexEnvs:
      data:
        first:
          name: _MainTex
        second:
          m_Texture: {fileID: 2800000, guid: 269490de66e33a34dad45a50e3d4c168, type: 3}
          m_Scale: {x: 1, y: 1}
          m_Offset: {x: 0, y: 0}
    m_Floats:
      data:
        first:
          name: _InvFade
        second: .947164237
    m_Colors:
      data:
        first:
          name: _Color
        second: {r: 1, g: 1, b: 1, a: 1}
      data:
        first:
          name: _TintColor
        second: {r: .5, g: .5, b: .5, a: .5}
