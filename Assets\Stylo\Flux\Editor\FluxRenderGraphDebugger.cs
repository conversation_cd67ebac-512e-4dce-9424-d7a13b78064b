using UnityEngine;
using UnityEditor;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;

#if URP_INSTALLED
using Stylo.Flux.Universal;
#endif

namespace Stylo.Flux.Editor
{
    public class FluxRenderGraphDebugger : EditorWindow
    {
        [MenuItem("Stylo/Flux/Render Graph Debugger")]
        public static void ShowWindow()
        {
            GetWindow<FluxRenderGraphDebugger>("Flux Render Graph Debugger");
        }

        private bool showTextureInfo = true;
        private bool showMotionVectorInfo = true;
        private bool showParameterInfo = true;
        private Vector2 scrollPosition;

        void OnGUI()
        {
            EditorGUILayout.LabelField("Flux Render Graph Debugging Tool", EditorStyles.boldLabel);
            EditorGUILayout.Space();

            if (!Application.isPlaying)
            {
                EditorGUILayout.HelpBox("This tool requires Play Mode to analyze runtime data.", MessageType.Warning);
                return;
            }

            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);

            // Find active Volume with FluxEffect
            var volumes = FindObjectsByType<Volume>(FindObjectsSortMode.None);
            FluxEffect fluxEffect = null;
            Volume activeVolume = null;

            foreach (var volume in volumes)
            {
                if (volume.profile != null && volume.profile.TryGet<FluxEffect>(out fluxEffect))
                {
                    activeVolume = volume;
                    break;
                }
            }

            if (fluxEffect == null)
            {
                EditorGUILayout.HelpBox("No Flux Effect found in scene.", MessageType.Warning);
                EditorGUILayout.EndScrollView();
                return;
            }

            // Parameter Analysis
            showParameterInfo = EditorGUILayout.Foldout(showParameterInfo, "Parameter Analysis");
            if (showParameterInfo)
            {
                EditorGUI.indentLevel++;

                EditorGUILayout.LabelField("DATAMOSH PARAMETERS:", EditorStyles.boldLabel);

                float effectiveBaseNoise = fluxEffect.ReprojectBaseNoise.value * fluxEffect.EffectIntensity.value;
                float effectiveLengthInfluence = fluxEffect.ReprojectLengthInfluence.value * fluxEffect.EffectIntensity.value;
                float effectiveMotion = fluxEffect.MotionAmplification.value;

                EditorGUILayout.LabelField("Effect Intensity", fluxEffect.EffectIntensity.value.ToString("F3"));
                EditorGUILayout.LabelField("Reproject Base Noise", $"{fluxEffect.ReprojectBaseNoise.value:F3} → {effectiveBaseNoise:F3}");
                EditorGUILayout.LabelField("Reproject Length Influence", $"{fluxEffect.ReprojectLengthInfluence.value:F3} → {effectiveLengthInfluence:F3}");
                EditorGUILayout.LabelField("Motion Amplification", effectiveMotion.ToString("F3"));
                EditorGUILayout.LabelField("Core Datamosh", "Always Active");

                bool reprojectEnabled = effectiveBaseNoise > 0f || effectiveLengthInfluence > 0f ||
                                       effectiveMotion > 0f || fluxEffect.VisualizeMotionVectors.value;

                EditorGUILayout.Space();
                if (reprojectEnabled)
                {
                    EditorGUILayout.HelpBox("✓ Reprojection ENABLED - Datamosh should be working", MessageType.Info);
                }
                else
                {
                    EditorGUILayout.HelpBox("✗ Reprojection DISABLED - No datamosh effects will be visible", MessageType.Error);
                }

                EditorGUI.indentLevel--;
            }

            EditorGUILayout.Space();

            // Motion Vector Analysis
            showMotionVectorInfo = EditorGUILayout.Foldout(showMotionVectorInfo, "Motion Vector Analysis");
            if (showMotionVectorInfo)
            {
                EditorGUI.indentLevel++;

                var camera = Camera.main;
                if (camera == null) camera = FindObjectOfType<Camera>();

                if (camera != null)
                {
                    EditorGUILayout.LabelField("CAMERA ANALYSIS:", EditorStyles.boldLabel);
                    EditorGUILayout.LabelField("Camera Type", camera.cameraType.ToString());
                    EditorGUILayout.LabelField("Is Scene View Camera", (camera.cameraType == CameraType.SceneView).ToString());

                    if (camera.cameraType == CameraType.SceneView)
                    {
                        EditorGUILayout.HelpBox("⚠️ Scene View cameras are excluded from motion vector generation!", MessageType.Warning);
                        EditorGUILayout.LabelField("Solution", "Test in Game View during Play Mode");
                    }
                    else
                    {
                        EditorGUILayout.HelpBox("✓ Camera type supports motion vectors", MessageType.Info);
                    }

                    // Check depth texture mode
                    EditorGUILayout.Space();
                    EditorGUILayout.LabelField("DEPTH TEXTURE MODE:", EditorStyles.boldLabel);
                    EditorGUILayout.LabelField("Current Mode", camera.depthTextureMode.ToString());

                    bool hasMotionVectors = (camera.depthTextureMode & DepthTextureMode.MotionVectors) != 0;
                    if (hasMotionVectors)
                    {
                        EditorGUILayout.HelpBox("✓ Motion vectors enabled on camera", MessageType.Info);
                    }
                    else
                    {
                        EditorGUILayout.HelpBox("✗ Motion vectors NOT enabled on camera", MessageType.Warning);
                    }
                }
                else
                {
                    EditorGUILayout.LabelField("No camera found in scene");
                }

                EditorGUI.indentLevel--;
            }

            EditorGUILayout.Space();

            // Texture Availability Analysis
            showTextureInfo = EditorGUILayout.Foldout(showTextureInfo, "Texture Availability Analysis");
            if (showTextureInfo)
            {
                EditorGUI.indentLevel++;

                EditorGUILayout.LabelField("RENDER GRAPH TEXTURE STATUS:", EditorStyles.boldLabel);

                // Check URP asset
                var urpAsset = GraphicsSettings.currentRenderPipeline as UniversalRenderPipelineAsset;
                if (urpAsset != null)
                {
                    EditorGUILayout.LabelField("URP Asset", urpAsset.name);
                    EditorGUILayout.HelpBox("✓ URP pipeline detected", MessageType.Info);
                }
                else
                {
                    EditorGUILayout.HelpBox("✗ URP pipeline not found", MessageType.Error);
                }

                EditorGUILayout.Space();
                EditorGUILayout.LabelField("CRITICAL ISSUES IDENTIFIED:", EditorStyles.boldLabel);

                EditorGUILayout.HelpBox(
                    "🚨 RENDER GRAPH TEXTURE TIMING ISSUE:\n\n" +
                    "The previous frame texture (prevFrameRTHandle) is only created in the CopyToPrev pass, " +
                    "but it's needed BEFORE that in the Upscale pass. This creates a chicken-and-egg problem:\n\n" +
                    "1. First frame: prevFrameRTHandle is null\n" +
                    "2. Upscale pass runs without previous frame data\n" +
                    "3. CopyToPrev pass creates texture for next frame\n\n" +
                    "This is why no trailing effects are visible!",
                    MessageType.Error);

                EditorGUI.indentLevel--;
            }

            EditorGUILayout.Space();

            // Quick Fix Buttons
            EditorGUILayout.LabelField("QUICK ACTIONS:", EditorStyles.boldLabel);

            if (GUILayout.Button("Enable Motion Vector Visualization"))
            {
                if (fluxEffect != null)
                {
                    fluxEffect.VisualizeMotionVectors.value = true;
                    fluxEffect.VisualizeMotionVectors.overrideState = true;
                    EditorUtility.SetDirty(activeVolume.profile);
                    Debug.Log("Enabled motion vector visualization");
                }
            }

            if (GUILayout.Button("Apply Strong Datamosh Settings"))
            {
                if (fluxEffect != null)
                {
                    fluxEffect.EffectIntensity.value = 1.0f;
                    fluxEffect.EffectIntensity.overrideState = true;

                    fluxEffect.ReprojectBaseNoise.value = 0.35f;
                    fluxEffect.ReprojectBaseNoise.overrideState = true;

                    fluxEffect.ReprojectLengthInfluence.value = 3.5f;
                    fluxEffect.ReprojectLengthInfluence.overrideState = true;



                    EditorUtility.SetDirty(activeVolume.profile);
                    Debug.Log("Applied strong datamosh settings");
                }
            }

            if (GUILayout.Button("Force Camera Motion Vector Mode"))
            {
                var camera = Camera.main;
                if (camera == null) camera = FindObjectOfType<Camera>();

                if (camera != null)
                {
                    camera.depthTextureMode |= DepthTextureMode.MotionVectors;
                    Debug.Log("Forced motion vector mode on camera");
                }
            }

            if (GUILayout.Button("Validate Texture Formats"))
            {
                var camera = Camera.main;
                if (camera == null) camera = FindObjectOfType<Camera>();

                if (camera != null)
                {
                    var urpAsset = GraphicsSettings.currentRenderPipeline as UniversalRenderPipelineAsset;
                    if (urpAsset != null)
                    {
                        Debug.Log($"[Flux Debug] Camera Color Format: {camera.targetTexture?.format ?? RenderTextureFormat.Default}");
                        Debug.Log($"[Flux Debug] Expected Previous Frame Format: R32G32B32A32_SFloat");
                        Debug.Log($"[Flux Debug] URP Asset: {urpAsset.name}");
                        Debug.Log($"[Flux Debug] HDR Enabled: {camera.allowHDR}");
                    }
                }
            }

            EditorGUILayout.EndScrollView();
        }
    }
}
