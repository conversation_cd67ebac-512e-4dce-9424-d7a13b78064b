#if UNITY_EDITOR && URP_INSTALLED
using System.Reflection;
using UnityEngine;
using UnityEngine.Rendering.Universal;
using UnityEditor;
using Stylo.BFI.Universal;

namespace Stylo.BFI.Editor
{
    public static class BFIIntegration
    {
        [MenuItem("Stylo/BFI/Add to Current Renderer")]
        public static void AddBFIToCurrentRenderer()
        {
            // Get the current URP asset
            var urpAsset = UnityEngine.Rendering.GraphicsSettings.currentRenderPipeline as UniversalRenderPipelineAsset;
            if (urpAsset == null)
            {
                Debug.LogError("No URP asset found in Graphics Settings. Please ensure URP is set up correctly.");
                return;
            }

            // Get the default renderer data using reflection
            var rendererDataListField = typeof(UniversalRenderPipelineAsset).GetField("m_RendererDataList",
                BindingFlags.NonPublic | BindingFlags.Instance);

            if (rendererDataListField == null)
            {
                Debug.LogError("Could not access URP renderer data list. This may be due to URP version differences.");
                return;
            }

            var rendererDataArray = rendererDataListField.GetValue(urpAsset) as ScriptableRendererData[];
            if (rendererDataArray == null || rendererDataArray.Length == 0)
            {
                Debug.LogError("No URP Renderer Data found. Please check your URP configuration.");
                return;
            }

            var rendererData = rendererDataArray[0] as UniversalRendererData;
            if (rendererData == null)
            {
                Debug.LogError("First renderer is not UniversalRendererData. Please check your URP configuration.");
                return;
            }

            // Check if BFI feature already exists
            foreach (var feature in rendererData.rendererFeatures)
            {
                if (feature is BFIRendererFeature)
                {
                    Debug.Log("BFI Renderer Feature already exists in the renderer.");
                    EditorGUIUtility.PingObject(feature);
                    return;
                }
            }

            // Create and add BFI feature
            var bfiFeature = ScriptableObject.CreateInstance<BFIRendererFeature>();
            bfiFeature.name = "BFI Renderer Feature";

            // Add to renderer data asset
            AssetDatabase.AddObjectToAsset(bfiFeature, rendererData);

            // Add to renderer features list
            var featuresList = new System.Collections.Generic.List<ScriptableRendererFeature>(rendererData.rendererFeatures);
            featuresList.Add(bfiFeature);

            // Use reflection to set the private field (Unity's internal API)
            var field = typeof(UniversalRendererData).GetField("m_RendererFeatures",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            if (field != null)
            {
                field.SetValue(rendererData, featuresList.ToArray());
            }

            // Mark dirty and save
            EditorUtility.SetDirty(rendererData);
            AssetDatabase.SaveAssets();

            Debug.Log($"BFI Renderer Feature added successfully to {rendererData.name}");
            EditorGUIUtility.PingObject(rendererData);
        }

        [MenuItem("Stylo/BFI/Create BFI Volume Profile")]
        public static void CreateBFIVolumeProfile()
        {
            // Create a new Volume Profile
            var volumeProfile = ScriptableObject.CreateInstance<UnityEngine.Rendering.VolumeProfile>();

            // Add BFI Effect to the profile
            var bfiEffect = volumeProfile.Add<BFIEffect>();

            // Set some default values
            bfiEffect.Mode.value = BFIEffect.BFIMode.DoubleFrameRate;
            bfiEffect.Mode.overrideState = true;
            bfiEffect.Intensity.value = 1f;
            bfiEffect.Intensity.overrideState = true;
            bfiEffect.BrightnessCompensation.value = 1.5f;
            bfiEffect.BrightnessCompensation.overrideState = true;

            // Save the asset
            string path = EditorUtility.SaveFilePanelInProject(
                "Save BFI Volume Profile",
                "BFI_VolumeProfile",
                "asset",
                "Choose where to save the BFI Volume Profile");

            if (!string.IsNullOrEmpty(path))
            {
                AssetDatabase.CreateAsset(volumeProfile, path);
                AssetDatabase.SaveAssets();

                Debug.Log($"BFI Volume Profile created at: {path}");
                EditorGUIUtility.PingObject(volumeProfile);
                Selection.activeObject = volumeProfile;
            }
        }

        [MenuItem("Stylo/BFI/Create Default Presets")]
        public static void CreateDefaultPresets()
        {
            BFIPresetManager.CreateDefaultPresets();
            Debug.Log("Default BFI presets created successfully!");
        }

        [MenuItem("Stylo/BFI/Setup Complete BFI Scene")]
        public static void SetupCompleteBFIScene()
        {
            // Check if BFI is in renderer
            var urpAsset = UnityEngine.Rendering.GraphicsSettings.currentRenderPipeline as UniversalRenderPipelineAsset;
            if (urpAsset == null)
            {
                Debug.LogError("No URP asset found. Please set up URP first.");
                return;
            }

            // Get renderer data using reflection
            var rendererDataListField = typeof(UniversalRenderPipelineAsset).GetField("m_RendererDataList",
                BindingFlags.NonPublic | BindingFlags.Instance);

            bool hasBFI = false;

            if (rendererDataListField != null)
            {
                var rendererDataArray = rendererDataListField.GetValue(urpAsset) as ScriptableRendererData[];
                if (rendererDataArray != null && rendererDataArray.Length > 0)
                {
                    var rendererData = rendererDataArray[0] as UniversalRendererData;
                    if (rendererData != null)
                    {
                        foreach (var feature in rendererData.rendererFeatures)
                        {
                            if (feature is BFIRendererFeature)
                            {
                                hasBFI = true;
                                break;
                            }
                        }
                    }
                }
            }

            if (!hasBFI)
            {
                if (EditorUtility.DisplayDialog("BFI Setup",
                    "BFI Renderer Feature not found. Add it to the renderer?",
                    "Yes", "Cancel"))
                {
                    AddBFIToCurrentRenderer();
                }
                else
                {
                    return;
                }
            }

            // Create Volume Profile if needed
            CreateBFIVolumeProfile();

            // Create Global Volume GameObject
            var globalVolumeGO = new GameObject("Global Volume (BFI)");
            var volume = globalVolumeGO.AddComponent<UnityEngine.Rendering.Volume>();
            volume.isGlobal = true;

            // The user will need to assign the profile manually or we could try to find the one just created
            Debug.Log("BFI Scene setup complete! Please assign the BFI Volume Profile to the Global Volume component.");

            Selection.activeGameObject = globalVolumeGO;
        }

        [MenuItem("Stylo/BFI/Open Documentation")]
        public static void OpenDocumentation()
        {
            var readmePath = "Assets/Stylo/BFI/README.md";
            var readme = AssetDatabase.LoadAssetAtPath<TextAsset>(readmePath);
            if (readme != null)
            {
                Selection.activeObject = readme;
                EditorGUIUtility.PingObject(readme);
            }
            else
            {
                Debug.LogWarning("BFI README not found at expected path: " + readmePath);
            }
        }

        // Validation methods
        [MenuItem("Stylo/BFI/Add to Current Renderer", true)]
        public static bool ValidateAddBFIToCurrentRenderer()
        {
            var urpAsset = UnityEngine.Rendering.GraphicsSettings.currentRenderPipeline as UniversalRenderPipelineAsset;
            return urpAsset != null;
        }

        [MenuItem("Stylo/BFI/Setup Complete BFI Scene", true)]
        public static bool ValidateSetupCompleteBFIScene()
        {
            var urpAsset = UnityEngine.Rendering.GraphicsSettings.currentRenderPipeline as UniversalRenderPipelineAsset;
            return urpAsset != null;
        }
    }
}
#endif
