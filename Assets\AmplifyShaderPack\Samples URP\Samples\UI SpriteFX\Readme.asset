%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e78934177bebe0545ab566bb787b0282, type: 3}
  m_Name: Readme
  m_EditorClassIdentifier: 
  Icon: {fileID: 2800000, guid: 050132ea75adf844cae0e9427adf41af, type: 3}
  Title: UI SpriteFX
  Description:
    Heading: 
    Text: This sample contains a miscellaneous of effects to be applied over 2D UI
      components.
    LinkText: 
    Url: 
  PropertiesHeader:
    Heading: UI Sprite FX Properties
    Text: This effect simulates a 3D ring rotating around the UI using a 2D texture.
    LinkText: 
    Url: 
  Properties:
  - Heading: Sprite Texture
    Text: Main UI Texture.
    LinkText: 
    Url: 
  - Heading: Tint
    Text: Color value for main UI tint.
    LinkText: 
    Url: 
  - Heading: Pixel snap
    Text: Enable/disable pixel snap.
    LinkText: 
    Url: 
  - Heading: External Alpha
    Text: Texture to be used if alpha to be gotten from another texture.
    LinkText: 
    Url: 
  - Heading: Ring Color
    Text: Color value for ring shape.
    LinkText: 
    Url: 
  - Heading: Ring
    Text: Texture containing ring shape to be animated.
    LinkText: 
    Url: 
  - Heading: Ring Pos
    Text: Ring position over UI element.
    LinkText: 
    Url: 
  AdditionalProperties:
  - BlockHeader:
      Heading: UI Sprite FX 3 Properties
      Text: This effect is more a mix of multiple effects such as animated fire distortion,
        rotation ring and energy flow.
      LinkText: 
      Url: 
    BlockContent:
    - Heading: Tint
      Text: Color value for main UI tint.
      LinkText: 
      Url: 
    - Heading: Pixel snap
      Text: Enable/disable pixel snap.
      LinkText: 
      Url: 
    - Heading: Fire
      Text: Texture containing fire color.
      LinkText: 
      Url: 
    - Heading: Blend Fire Mask
      Text: Texture mask to delimit where distorted fire is to be shown.
      LinkText: 
      Url: 
    - Heading: Distortion Normal Map
      Text: Normals texture to be used to distort fire texture.
      LinkText: 
      Url: 
    - Heading: Distort Amount
      Text: Value to control overall distortion intensity.
      LinkText: 
      Url: 
    - Heading: Main FX
      Text: Background texture for back layer flow effect.
      LinkText: 
      Url: 
    - Heading: Energy Flow
      Text: Flow texture for back layer flow effect.
      LinkText: 
      Url: 
    - Heading: Flow
      Text: Texture containing energy flow shape to be animated over front layer.
      LinkText: 
      Url: 
    - Heading: Flow Direction
      Text: Flow texture for foreground flow effect.
      LinkText: 
      Url: 
    - Heading: Rotation
      Text: Texture containing ring shape to be animated.
      LinkText: 
      Url: 
    - Heading: Rotation Mask
      Text: Texture mask to delimit where ring shape is to be shown.
      LinkText: 
      Url: 
    - Heading: Rotation Pos Scale
      Text: Vector containing both ring position (XY) and scale (ZW) values.
      LinkText: 
      Url: 
  AdditionalScripts: []
  LoadedLayout: 0
  RPType: 3
