%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 6
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: FeelTacticalSmokeMaterial
  m_Shader: {fileID: 4800000, guid: e5691787f973b2c4c9e7333c45119dd2, type: 3}
  m_ShaderKeywords: _ALPHABLEND_ON _TIMECLAMP_HOURS_SIXTEENTH _UV1_SOURCE_WORLDXZ
  m_LightmapFlags: 0
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: d97ec25ad6eb82242bd9451e92c8ba45, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Mask1:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Floats:
    - PixelSnap: 0
    - _ALPHACLIP: 0
    - _AlphaMultiplier: 1
    - _AlphaThreshold: 0
    - _ApplyErosion: 0
    - _ApplyOpacityAfterErosion: 0
    - _BlendDst: 10
    - _BlendOp: 0
    - _BlendSrc: 1
    - _BumpScale: 1
    - _CameraFadingEnabled: 0
    - _CameraFarFadeDistance: 2
    - _CameraNearFadeDistance: 1
    - _ColorMask: 255
    - _ColorMode: 0
    - _Cull: 2
    - _CullMode: 0
    - _Cutoff: 0.5
    - _DetailNormalMapScale: 1
    - _DistortionBlend: 0.5
    - _DistortionEnabled: 0
    - _DistortionStrength: 1
    - _DistortionStrengthScaled: 0
    - _DstBlend: 1
    - _EmissionEnabled: 0
    - _ErosionOffset: 0
    - _ErosionSmoothness: 1
    - _FlipbookMode: 0
    - _FresnelBias: 0
    - _FresnelPower: 1
    - _FresnelScale: 1
    - _GlossMapScale: 1
    - _Glossiness: 0.5
    - _GlossyReflections: 1
    - _InvFade: 1
    - _LightingEnabled: 1
    - _Metallic: 0
    - _Mode: 4
    - _OcclusionStrength: 1
    - _Opacity: 1
    - _PUSHNORMALS: 0
    - _Parallax: 0.02
    - _PushNormalAmount: 0
    - _SmoothnessTextureChannel: 0
    - _SoftParticlesEnabled: 0
    - _SoftParticlesFarFadeDistance: 1
    - _SoftParticlesNearFadeDistance: 0
    - _SpecularHighlights: 1
    - _SrcBlend: 5
    - _TIMECLAMP: 0
    - _USECOLOR: 0
    - _USE_CUSTOM1: 0
    - _USE_FRESNEL: 0
    - _USE_FRESNEL_VS: 0
    - _UV0_Rotation: 0
    - _UV1_Rotation: 0
    - _UV1_SOURCE: 0
    - _UVSec: 0
    - _VertexColorAsErosionOffset: 0
    - _ZTest: 4
    - _ZWrite: 0
    m_Colors:
    - _CameraFadeParams: {r: 0, g: Infinity, b: 0, a: 0}
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _ColorAddSubDiff: {r: 0, g: 0, b: 0, a: 0}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _SoftParticleFadeParams: {r: 0, g: 0, b: 0, a: 0}
    - _TintColor: {r: 0.5, g: 0.5, b: 0.5, a: 0.078431375}
    - _UV0_RotCenter: {r: 0.5, g: 0.5, b: 0, a: 0}
    - _UV0_Speed: {r: 0, g: 0, b: 0, a: 0}
    - _UV1_Offset: {r: 0, g: 0, b: 0, a: 0}
    - _UV1_RotCenter: {r: 0.5, g: 0.5, b: 0, a: 0}
    - _UV1_Scale: {r: 1, g: 1, b: 0, a: 0}
    - _UV1_Speed: {r: 0, g: 0, b: 0, a: 0}
