%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: GSF Schlick GGX
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor v1.9.5.1\n// Available at the
    Unity Asset Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=19501\nNode;AmplifyShaderEditor.CommentaryNode;38;-896,-160;Inherit;False;1786.475;561.7919;Math;18;139;138;137;0;34;30;29;25;23;31;37;24;26;41;33;32;36;35;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;42;-1982.531,-160;Inherit;False;1023.755;571.8639;Dot
    Products;9;70;107;106;92;100;99;91;134;136;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;40;-896,-496;Inherit;False;637.0081;265.9156;GSF
    Schlick GGX Node;1;39;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;35;-592,-112;Inherit;False;192.7147;125.2546;NdotV;1;130;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;36;-592,32;Inherit;False;192.7147;125.2546;NdotL;1;131;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;1;-896,-672;Inherit;False;516.1898;155.222;Credits;;0,0,0,0.5019608;Method
    by Jordan Stevens$Link: https://www.jordanstevenstechart.com/physically-based-rendering$$Node
    Made by Tiber Legacy$Special Thanks to Dawie;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;32;-336,-112;Inherit;False;230.999;100;Vectorized;;0,0,0,0.5019608;to
    simplify and Optimize the Algorithm;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;39;-576,-432;Inherit;False;293.5023;130.2581;;;0,0,0,1;The
    Schlick Approximation of GGX simply divides our roughness value by two - Jordan
    Stevens;0;0\nNode;AmplifyShaderEditor.DynamicAppendNode;33;-336,0;Inherit;False;FLOAT2;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.StickyNoteNode;41;-864,192;Inherit;False;191.4479;100;Roughness;;0,0,0,1;;0;0\nNode;AmplifyShaderEditor.ScaleAndOffsetNode;26;-144,80;Inherit;False;3;0;FLOAT2;0,0;False;1;FLOAT;1;False;2;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleDivideOpNode;24;-448,192;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;37;-192,208;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;31;-320,112;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;23;-640,192;Inherit;False;Roughness;1;3;False;1;0;FLOAT;0.8;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;25;-624,272;Inherit;False;Constant;_Two;Two;0;0;Create;True;0;0;0;False;0;False;2;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;131;-576,64;Inherit;False;92;NdotL;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;130;-576,-80;Inherit;False;91;NdotV;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;91;-1216,-64;Inherit;True;NdotV;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;99;-1664,16;Inherit;False;NdotV;1;2;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;100;-1664,240;Inherit;False;NdotL;1;1;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;92;-1216,160;Inherit;True;NdotL;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;106;-1504,-64;Inherit;False;Custom
    Dot Products;True;0;2;1;In 0;In 1;Object;-1;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;107;-1504,160;Inherit;False;Option;False;0;2;1;In
    0;In 1;Instance;106;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;70;-1920,-64;Inherit;False;Normal;3;0;False;1;0;FLOAT3;0,0,1;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionNode;134;-1728,-64;Inherit;False;NdotV;-1;;3;65be4ec269a534f4e8867af2f999aaa9;4,27,1,377,0,452,0,451,0;3;230;FLOAT3;0,0,1;False;443;FLOAT3;0,0,0;False;415;FLOAT;1E-37;False;1;FLOAT;23\nNode;AmplifyShaderEditor.FunctionNode;136;-1728,160;Inherit;False;NdotL;-1;;5;6d7ee1aa8c79587448475fa2b37affc1;5,27,1,381,0,460,0,461,0,463,0;4;230;FLOAT3;0,0,1;False;462;FLOAT3;0,0,0;False;454;FLOAT3;0,0,0;False;416;FLOAT;1E-37;False;1;FLOAT;111\nNode;AmplifyShaderEditor.SimpleDivideOpNode;29;240,0;Inherit;False;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;30;496,0;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.BreakToComponentsNode;34;384,0;Inherit;False;FLOAT2;1;0;FLOAT2;0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.StickyNoteNode;137;96,176;Inherit;False;160;100;Max
    0.1;;0,0,0,0.5019608;To Prevent NaN in Division and Output;0;0\nNode;AmplifyShaderEditor.RangedFloatNode;138;-112,208;Inherit;False;Constant;_0_1;_0_1;0;0;Create;True;0;0;0;False;0;False;0.1;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMaxOpNode;139;96,80;Inherit;False;2;0;FLOAT2;0,0;False;1;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.FunctionOutput;0;656,0;Inherit;True;True;-1;Out;0;False;1;0;FLOAT;0;False;1;FLOAT;0\nWireConnection;33;0;130;0\nWireConnection;33;1;131;0\nWireConnection;26;0;33;0\nWireConnection;26;1;31;0\nWireConnection;26;2;37;0\nWireConnection;24;0;23;0\nWireConnection;24;1;25;0\nWireConnection;37;0;24;0\nWireConnection;31;0;24;0\nWireConnection;91;0;106;0\nWireConnection;92;0;107;0\nWireConnection;106;0;134;23\nWireConnection;106;1;99;0\nWireConnection;107;0;136;111\nWireConnection;107;1;100;0\nWireConnection;134;230;70;0\nWireConnection;136;230;70;0\nWireConnection;29;0;33;0\nWireConnection;29;1;139;0\nWireConnection;30;0;34;0\nWireConnection;30;1;34;1\nWireConnection;34;0;29;0\nWireConnection;139;0;26;0\nWireConnection;139;1;138;0\nWireConnection;0;0;30;0\nASEEND*/\n//CHKSM=5119C8459E3C42D5372D25A62B228EEA9F386264"
  m_functionName: 
  m_description: 'GSF Schlick GGX Node

'
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 0
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_customEdited: 0
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 0
  m_headerStyle: 2
  m_headerColor: {r: 0.7529412, g: 0.6784314, b: 0, a: 1}
  m_customNodeCategory: Amplify Shader Pack
  m_previewPosition: 0
  m_hidden: 0
  m_url: 
