%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &1399490162906741866
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1399490162906741869}
  - component: {fileID: 1399490162906741868}
  - component: {fileID: 7743608946084996736}
  m_Layer: 0
  m_Name: SnakeBodyPart
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1399490162906741869
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1399490162906741866}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0.65, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 2941045151869456356}
  - {fileID: 8983197464926562192}
  - {fileID: 4498738655432475941}
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1399490162906741868
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1399490162906741866}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b83ebdb48c79df04381830c0d5dc54a2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  TargetRecorder: {fileID: 0}
  EatFeedback: {fileID: 6386137849870764196}
  NewFeedback: {fileID: 762393565746647285}
  Offset: 10
  Index: 0
--- !u!61 &7743608946084996736
BoxCollider2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1399490162906741866}
  m_Enabled: 0
  m_Density: 1
  m_Material: {fileID: 0}
  m_IsTrigger: 1
  m_UsedByEffector: 0
  m_UsedByComposite: 0
  m_Offset: {x: 0, y: 0}
  m_SpriteTilingProperty:
    border: {x: 0, y: 0, z: 0, w: 0}
    pivot: {x: 0, y: 0}
    oldSize: {x: 0, y: 0}
    newSize: {x: 0, y: 0}
    adaptiveTilingThreshold: 0
    drawMode: 0
    adaptiveTiling: 0
  m_AutoTiling: 0
  serializedVersion: 2
  m_Size: {x: 0.1, y: 0.1}
  m_EdgeRadius: 0
--- !u!1 &3403001524479936354
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2941045151869456356}
  - component: {fileID: 6437689195354365481}
  m_Layer: 0
  m_Name: Model
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2941045151869456356
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3403001524479936354}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1399490162906741869}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!212 &6437689195354365481
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3403001524479936354}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Sprite: {fileID: 21300000, guid: bbd1a94a71bfd4c419bdb35055a38cec, type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 0.01, y: 0.01}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!1 &6004400618820588014
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4498738655432475941}
  - component: {fileID: 762393565746647285}
  m_Layer: 0
  m_Name: NewFeedback
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4498738655432475941
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6004400618820588014}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1399490162906741869}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &762393565746647285
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6004400618820588014}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6da43522623d4704e979466dc7650b65, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Feedbacks:
  - {fileID: 0}
  InitializationMode: 2
  SafeMode: 3
  Direction: 0
  AutoChangeDirectionOnEnd: 0
  AutoPlayOnStart: 0
  AutoPlayOnEnable: 0
  ForceTimescaleMode: 0
  ForcedTimescaleMode: 1
  DurationMultiplier: 1
  RandomizeDuration: 0
  RandomDurationMultiplier: {x: 0.5, y: 1.5}
  DisplayFullDurationDetails: 0
  PlayerTimescaleMode: 1
  OnlyPlayIfWithinRange: 0
  RangeCenter: {fileID: 0}
  RangeDistance: 5
  UseRangeFalloff: 0
  RangeFalloff:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  RemapRangeFalloff: {x: 0, y: 1}
  IgnoreRangeEvents: 0
  CooldownDuration: 0
  InitialDelay: 0
  CanPlay: 1
  CanPlayWhileAlreadyPlaying: 1
  ChanceToPlay: 100
  FeedbacksIntensity: 1
  Events:
    TriggerMMFeedbacksEvents: 0
    TriggerUnityEvents: 1
    OnPlay:
      m_PersistentCalls:
        m_Calls: []
    OnPause:
      m_PersistentCalls:
        m_Calls: []
    OnResume:
      m_PersistentCalls:
        m_Calls: []
    OnRevert:
      m_PersistentCalls:
        m_Calls: []
    OnComplete:
      m_PersistentCalls:
        m_Calls: []
    OnRestoreInitialValues:
      m_PersistentCalls:
        m_Calls: []
    OnSkipToTheEnd:
      m_PersistentCalls:
        m_Calls: []
  DebugActive: 0
  FeedbacksList:
  - id: 0
  KeepPlayModeChanges: 0
  PerformanceMode: 0
  ForceStopFeedbacksOnDisable: 1
  PlayCount: 0
  references:
    version: 1
    00000000:
      type: {class: MMF_Scale, ns: MoreMountains.Feedbacks, asm: MoreMountains.Feedbacks}
      data:
        Active: 1
        UniqueID: -643047020
        Label: Scale
        ChannelMode: 0
        Channel: 0
        MMChannelDefinition: {fileID: 0}
        Chance: 100
        DisplayColor: {r: 0, g: 0, b: 0, a: 1}
        Timing:
          TimescaleMode: 0
          ExcludeFromHoldingPauses: 0
          ContributeToTotalDuration: 1
          InitialDelay: 0.2
          CooldownDuration: 0
          InterruptsOnStop: 1
          NumberOfRepeats: 0
          RepeatForever: 0
          DelayBetweenRepeats: 1
          MMFeedbacksDirectionCondition: 0
          PlayDirection: 0
          ConstantIntensity: 1
          UseIntensityInterval: 0
          IntensityIntervalMin: 0
          IntensityIntervalMax: 0
          Sequence: {fileID: 0}
          TrackID: 0
          Quantized: 0
          TargetBPM: 120
        AutomatedTargetAcquisition:
          Mode: 0
          ChildIndex: 0
        RandomizeOutput: 0
        RandomMultiplier: {x: 0.8, y: 1}
        RandomizeDuration: 0
        RandomDurationMultiplier: {x: 0.5, y: 2}
        UseRange: 0
        RangeDistance: 5
        UseRangeFalloff: 0
        RangeFalloff:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        RemapRangeFalloff: {x: 0, y: 1}
        Owner: {fileID: 762393565746647285}
        DebugActive: 0
        Mode: 0
        AnimateScaleTarget: {fileID: 1399490162906741869}
        AnimateScaleDuration: 0.2
        RemapCurveZero: 1
        RemapCurveOne: 2.2
        Offset: 0
        AnimateX: 1
        AnimateScaleTweenX:
          MMTweenDefinitionType: 1
          MMTweenCurve: 4
          Curve:
            serializedVersion: 2
            m_Curve:
            - serializedVersion: 3
              time: 0
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 0.3
              value: 1.5
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 1
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            m_PreInfinity: 2
            m_PostInfinity: 2
            m_RotationOrder: 4
          Initialized: 1
        AnimateY: 1
        AnimateScaleTweenY:
          MMTweenDefinitionType: 1
          MMTweenCurve: 4
          Curve:
            serializedVersion: 2
            m_Curve:
            - serializedVersion: 3
              time: 0
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 0.3
              value: 1.5
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 1
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            m_PreInfinity: 2
            m_PostInfinity: 2
            m_RotationOrder: 4
          Initialized: 1
        AnimateZ: 1
        AnimateScaleTweenZ:
          MMTweenDefinitionType: 1
          MMTweenCurve: 4
          Curve:
            serializedVersion: 2
            m_Curve:
            - serializedVersion: 3
              time: 0
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 0.3
              value: 1.5
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 1
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            m_PreInfinity: 2
            m_PostInfinity: 2
            m_RotationOrder: 4
          Initialized: 1
        UniformScaling: 0
        AllowAdditivePlays: 0
        DetermineScaleOnPlay: 0
        DestinationScale: {x: 0.5, y: 0.5, z: 0.5}
        AnimateScaleX:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 0.3
            value: 1.5
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        AnimateScaleY:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 0.3
            value: 1.5
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        AnimateScaleZ:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 0.3
            value: 1.5
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
--- !u!1 &6479198486931226794
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8983197464926562192}
  - component: {fileID: 6386137849870764196}
  m_Layer: 0
  m_Name: EatFeedback
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8983197464926562192
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6479198486931226794}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1399490162906741869}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &6386137849870764196
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6479198486931226794}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6da43522623d4704e979466dc7650b65, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Feedbacks:
  - {fileID: 0}
  InitializationMode: 2
  SafeMode: 3
  Direction: 0
  AutoChangeDirectionOnEnd: 0
  AutoPlayOnStart: 0
  AutoPlayOnEnable: 0
  ForceTimescaleMode: 0
  ForcedTimescaleMode: 1
  DurationMultiplier: 1
  RandomizeDuration: 0
  RandomDurationMultiplier: {x: 0.5, y: 1.5}
  DisplayFullDurationDetails: 0
  PlayerTimescaleMode: 1
  OnlyPlayIfWithinRange: 0
  RangeCenter: {fileID: 0}
  RangeDistance: 5
  UseRangeFalloff: 0
  RangeFalloff:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  RemapRangeFalloff: {x: 0, y: 1}
  IgnoreRangeEvents: 0
  CooldownDuration: 0
  InitialDelay: 0
  CanPlay: 1
  CanPlayWhileAlreadyPlaying: 1
  ChanceToPlay: 100
  FeedbacksIntensity: 1
  Events:
    TriggerMMFeedbacksEvents: 0
    TriggerUnityEvents: 1
    OnPlay:
      m_PersistentCalls:
        m_Calls: []
    OnPause:
      m_PersistentCalls:
        m_Calls: []
    OnResume:
      m_PersistentCalls:
        m_Calls: []
    OnRevert:
      m_PersistentCalls:
        m_Calls: []
    OnComplete:
      m_PersistentCalls:
        m_Calls: []
    OnRestoreInitialValues:
      m_PersistentCalls:
        m_Calls: []
    OnSkipToTheEnd:
      m_PersistentCalls:
        m_Calls: []
  DebugActive: 0
  FeedbacksList:
  - id: 0
  KeepPlayModeChanges: 0
  PerformanceMode: 0
  ForceStopFeedbacksOnDisable: 1
  PlayCount: 0
  references:
    version: 1
    00000000:
      type: {class: MMF_Scale, ns: MoreMountains.Feedbacks, asm: MoreMountains.Feedbacks}
      data:
        Active: 1
        UniqueID: 1651064665
        Label: Scale
        ChannelMode: 0
        Channel: 0
        MMChannelDefinition: {fileID: 0}
        Chance: 100
        DisplayColor: {r: 0, g: 0, b: 0, a: 1}
        Timing:
          TimescaleMode: 0
          ExcludeFromHoldingPauses: 0
          ContributeToTotalDuration: 1
          InitialDelay: 0
          CooldownDuration: 0
          InterruptsOnStop: 1
          NumberOfRepeats: 0
          RepeatForever: 0
          DelayBetweenRepeats: 1
          MMFeedbacksDirectionCondition: 0
          PlayDirection: 0
          ConstantIntensity: 1
          UseIntensityInterval: 0
          IntensityIntervalMin: 0
          IntensityIntervalMax: 0
          Sequence: {fileID: 0}
          TrackID: 0
          Quantized: 0
          TargetBPM: 120
        AutomatedTargetAcquisition:
          Mode: 0
          ChildIndex: 0
        RandomizeOutput: 0
        RandomMultiplier: {x: 0.8, y: 1}
        RandomizeDuration: 0
        RandomDurationMultiplier: {x: 0.5, y: 2}
        UseRange: 0
        RangeDistance: 5
        UseRangeFalloff: 0
        RangeFalloff:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        RemapRangeFalloff: {x: 0, y: 1}
        Owner: {fileID: 6386137849870764196}
        DebugActive: 0
        Mode: 0
        AnimateScaleTarget: {fileID: 2941045151869456356}
        AnimateScaleDuration: 0.2
        RemapCurveZero: 1
        RemapCurveOne: 1.7
        Offset: 0
        AnimateX: 1
        AnimateScaleTweenX:
          MMTweenDefinitionType: 1
          MMTweenCurve: 4
          Curve:
            serializedVersion: 2
            m_Curve:
            - serializedVersion: 3
              time: 0
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 0.3
              value: 1.5
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 1
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            m_PreInfinity: 2
            m_PostInfinity: 2
            m_RotationOrder: 4
          Initialized: 1
        AnimateY: 1
        AnimateScaleTweenY:
          MMTweenDefinitionType: 1
          MMTweenCurve: 4
          Curve:
            serializedVersion: 2
            m_Curve:
            - serializedVersion: 3
              time: 0
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 0.3
              value: 1.5
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 1
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            m_PreInfinity: 2
            m_PostInfinity: 2
            m_RotationOrder: 4
          Initialized: 1
        AnimateZ: 1
        AnimateScaleTweenZ:
          MMTweenDefinitionType: 1
          MMTweenCurve: 4
          Curve:
            serializedVersion: 2
            m_Curve:
            - serializedVersion: 3
              time: 0
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 0.3
              value: 1.5
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 1
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            m_PreInfinity: 2
            m_PostInfinity: 2
            m_RotationOrder: 4
          Initialized: 1
        UniformScaling: 0
        AllowAdditivePlays: 0
        DetermineScaleOnPlay: 0
        DestinationScale: {x: 0.5, y: 0.5, z: 0.5}
        AnimateScaleX:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 0.3
            value: 1.5
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        AnimateScaleY:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 0.3
            value: 1.5
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        AnimateScaleZ:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 0.3
            value: 1.5
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
