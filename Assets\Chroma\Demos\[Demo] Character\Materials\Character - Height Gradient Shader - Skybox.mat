%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!28 &-8444485339891319028
Texture2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: z__GradientTex{"mode":0,"colorKeys":[{"color":{"r":0.9333333373069763,"g":0.8079060316085815,"b":0.5960784554481506,"a":1.0},"time":0.06868086010217667},{"color":{"r":0.7904503345489502,"g":0.844175398349762,"b":0.8773584961891174,"a":1.0},"time":0.45604637265205386}],"alphaKeys":[{"alpha":1.0,"time":0.0},{"alpha":1.0,"time":1.0}]}
  m_ImageContentsHash:
    serializedVersion: 2
    Hash: 4dc39883a722ce2e74020035cab5bf7a
  m_ForcedFallbackFormat: 4
  m_DownscaleFallback: 0
  m_IsAlphaChannelOptional: 0
  serializedVersion: 2
  m_Width: 1024
  m_Height: 1
  m_CompleteImageSize: 8192
  m_MipsStripped: 0
  m_TextureFormat: 17
  m_MipCount: 1
  m_IsReadable: 1
  m_IsPreProcessed: 0
  m_IgnoreMasterTextureLimit: 0
  m_StreamingMipmaps: 0
  m_StreamingMipmapsPriority: 0
  m_VTOnly: 0
  m_AlphaIsTransparency: 0
  m_ImageCount: 1
  m_TextureDimension: 2
  m_TextureSettings:
    serializedVersion: 2
    m_FilterMode: 1
    m_Aniso: 1
    m_MipBias: 0
    m_WrapU: 1
    m_WrapV: 1
    m_WrapW: 1
  m_LightmapFormat: 0
  m_ColorSpace: 0
  m_PlatformBlob: 
  image data: 8192
  _typelessdata: 773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac538003c773b773ac638003c763b773ac738003c753b773ac938003c753b773aca38003c743b773acc38003c733b783acd38003c723b783acf38003c723b783ad038003c713b783ad138003c703b783ad338003c703b793ad438003c6f3b793ad638003c6e3b793ad738003c6d3b793ad938003c6d3b793ada38003c6c3b7a3adc38003c6b3b7a3add38003c6a3b7a3adf38003c6a3b7a3ae038003c693b7a3ae138003c683b7a3ae338003c673b7b3ae438003c673b7b3ae638003c663b7b3ae738003c653b7b3ae938003c643b7b3aea38003c643b7c3aec38003c633b7c3aed38003c623b7c3aef38003c623b7c3af038003c613b7c3af138003c603b7d3af338003c5f3b7d3af438003c5f3b7d3af638003c5e3b7d3af738003c5d3b7d3af938003c5c3b7d3afa38003c5c3b7e3afc38003c5b3b7e3afd38003c5a3b7e3aff38003c593b7e3a0039003c593b7e3a0139003c583b7f3a0339003c573b7f3a0439003c563b7f3a0639003c563b7f3a0739003c553b7f3a0939003c543b803a0a39003c533b803a0c39003c533b803a0d39003c523b803a0f39003c513b803a1039003c513b803a1139003c503b813a1339003c4f3b813a1439003c4e3b813a1639003c4e3b813a1739003c4d3b813a1939003c4c3b823a1a39003c4b3b823a1c39003c4b3b823a1d39003c4a3b823a1f39003c493b823a2039003c483b833a2139003c483b833a2339003c473b833a2439003c463b833a2639003c453b833a2739003c453b833a2939003c443b843a2a39003c433b843a2c39003c423b843a2d39003c423b843a2f39003c413b843a3039003c403b853a3139003c403b853a3339003c3f3b853a3439003c3e3b853a3639003c3d3b853a3739003c3d3b863a3939003c3c3b863a3a39003c3b3b863a3c39003c3a3b863a3d39003c3a3b863a3f39003c393b863a4039003c383b873a4139003c373b873a4339003c373b873a4439003c363b873a4639003c353b873a4739003c343b883a4939003c343b883a4a39003c333b883a4c39003c323b883a4d39003c323b883a4e39003c313b893a5039003c303b893a5139003c2f3b893a5339003c2f3b893a5439003c2e3b893a5639003c2d3b893a5739003c2c3b8a3a5939003c2c3b8a3a5a39003c2b3b8a3a5c39003c2a3b8a3a5d39003c293b8a3a5e39003c293b8b3a6039003c283b8b3a6139003c273b8b3a6339003c263b8b3a6439003c263b8b3a6639003c253b8c3a6739003c243b8c3a6939003c233b8c3a6a39003c233b8c3a6c39003c223b8c3a6d39003c213b8c3a6e39003c213b8d3a7039003c203b8d3a7139003c1f3b8d3a7339003c1e3b8d3a7439003c1e3b8d3a7639003c1d3b8e3a7739003c1c3b8e3a7939003c1b3b8e3a7a39003c1b3b8e3a7c39003c1a3b8e3a7d39003c193b8f3a7e39003c183b8f3a8039003c183b8f3a8139003c173b8f3a8339003c163b8f3a8439003c153b8f3a8639003c153b903a8739003c143b903a8939003c133b903a8a39003c123b903a8c39003c123b903a8d39003c113b913a8e39003c103b913a9039003c103b913a9139003c0f3b913a9339003c0e3b913a9439003c0d3b923a9639003c0d3b923a9739003c0c3b923a9939003c0b3b923a9a39003c0a3b923a9c39003c0a3b923a9d39003c093b933a9e39003c083b933aa039003c073b933aa139003c073b933aa339003c063b933aa439003c053b943aa639003c043b943aa739003c043b943aa939003c033b943aaa39003c023b943aac39003c023b953aad39003c013b953aae39003c003b953ab039003cff3a953ab139003cff3a953ab339003cfe3a953ab439003cfd3a963ab639003cfc3a963ab739003cfc3a963ab939003cfb3a963aba39003cfa3a963abc39003cf93a973abd39003cf93a973abe39003cf83a973ac039003cf73a973ac139003cf63a973ac339003cf63a983ac439003cf53a983ac639003cf43a983ac739003cf33a983ac939003cf33a983aca39003cf23a983acc39003cf13a993acd39003cf13a993ace39003cf03a993ad039003cef3a993ad139003cee3a993ad339003cee3a9a3ad439003ced3a9a3ad639003cec3a9a3ad739003ceb3a9a3ad939003ceb3a9a3ada39003cea3a9b3adb39003ce93a9b3add39003ce83a9b3ade39003ce83a9b3ae039003ce73a9b3ae139003ce63a9b3ae339003ce53a9c3ae439003ce53a9c3ae639003ce43a9c3ae739003ce33a9c3ae939003ce23a9c3aea39003ce23a9d3aeb39003ce13a9d3aed39003ce03a9d3aee39003ce03a9d3af039003cdf3a9d3af139003cde3a9e3af339003cdd3a9e3af439003cdd3a9e3af639003cdc3a9e3af739003cdb3a9e3af939003cda3a9e3afa39003cda3a9f3afb39003cd93a9f3afd39003cd83a9f3afe39003cd73a9f3a003a003cd73a9f3a013a003cd63aa03a033a003cd53aa03a043a003cd43aa03a063a003cd43aa03a073a003cd33aa03a093a003cd23aa13a0a3a003cd23aa13a0b3a003cd13aa13a0d3a003cd03aa13a0e3a003ccf3aa13a103a003ccf3aa13a113a003cce3aa23a133a003ccd3aa23a143a003ccc3aa23a163a003ccc3aa23a173a003ccb3aa23a193a003cca3aa33a1a3a003cc93aa33a1b3a003cc93aa33a1d3a003cc83aa33a1e3a003cc73aa33a203a003cc63aa43a213a003cc63aa43a233a003cc53aa43a243a003cc43aa43a263a003cc33aa43a273a003cc33aa43a293a003cc23aa53a2a3a003cc13aa53a2b3a003cc13aa53a2d3a003cc03aa53a2e3a003cbf3aa53a303a003cbe3aa63a313a003cbe3aa63a333a003cbd3aa63a343a003cbc3aa63a363a003cbb3aa63a373a003cbb3aa73a393a003cba3aa73a3a3a003cb93aa73a3b3a003cb83aa73a3d3a003cb83aa73a3e3a003cb73aa73a403a003cb63aa83a413a003cb53aa83a433a003cb53aa83a443a003cb43aa83a463a003cb33aa83a473a003cb23aa93a493a003cb23aa93a4a3a003cb13aa93a4b3a003cb03aa93a4d3a003cb03aa93a4e3a003caf3aaa3a503a003cae3aaa3a513a003cad3aaa3a533a003cad3aaa3a543a003cac3aaa3a563a003cab3aaa3a573a003caa3aab3a593a003caa3aab3a5a3a003ca93aab3a5b3a003ca83aab3a5d3a003ca73aab3a5e3a003ca73aac3a603a003ca63aac3a613a003ca53aac3a633a003ca43aac3a643a003ca43aac3a663a003ca33aad3a673a003ca23aad3a693a003ca23aad3a6a3a003ca13aad3a6b3a003ca03aad3a6d3a003c9f3aad3a6e3a003c9f3aae3a703a003c9e3aae3a713a003c9d3aae3a733a003c9c3aae3a743a003c9c3aae3a763a003c9b3aaf3a773a003c9a3aaf3a783a003c993aaf3a7a3a003c993aaf3a7b3a003c983aaf3a7d3a003c973ab03a7e3a003c963ab03a803a003c963ab03a813a003c953ab03a833a003c943ab03a843a003c933ab03a863a003c933ab13a873a003c923ab13a883a003c913ab13a8a3a003c913ab13a8b3a003c903ab13a8d3a003c8f3ab23a8e3a003c8e3ab23a903a003c8e3ab23a913a003c8d3ab23a933a003c8c3ab23a943a003c8b3ab33a963a003c8b3ab33a973a003c8a3ab33a983a003c893ab33a9a3a003c883ab33a9b3a003c883ab33a9d3a003c873ab43a9e3a003c863ab43aa03a003c853ab43aa13a003c853ab43aa33a003c843ab43aa43a003c833ab53aa63a003c823ab53aa73a003c823ab53aa83a003c813ab53aaa3a003c803ab53aab3a003c803ab63aad3a003c7f3ab63aae3a003c7e3ab63ab03a003c7d3ab63ab13a003c7d3ab63ab33a003c7c3ab63ab43a003c7b3ab73ab63a003c7a3ab73ab73a003c7a3ab73ab83a003c793ab73aba3a003c783ab73abb3a003c773ab83abd3a003c773ab83abe3a003c763ab83ac03a003c753ab83ac13a003c743ab83ac33a003c743ab93ac43a003c733ab93ac63a003c723ab93ac73a003c723ab93ac83a003c713ab93aca3a003c703ab93acb3a003c6f3aba3acd3a003c6f3aba3ace3a003c6e3aba3ad03a003c6d3aba3ad13a003c6c3aba3ad33a003c6c3abb3ad43a003c6b3abb3ad63a003c6a3abb3ad73a003c693abb3ad83a003c693abb3ada3a003c683abc3adb3a003c673abc3add3a003c663abc3ade3a003c663abc3ae03a003c653abc3ae13a003c643abc3ae33a003c633abd3ae43a003c633abd3ae63a003c623abd3ae73a003c613abd3ae83a003c613abd3aea3a003c603abe3aeb3a003c5f3abe3aed3a003c5e3abe3aee3a003c5e3abe3af03a003c5d3abe3af13a003c5c3abf3af33a003c5b3abf3af43a003c5b3abf3af63a003c5a3abf3af73a003c593abf3af83a003c583abf3afa3a003c583ac03afb3a003c573ac03afd3a003c563ac03afe3a003c553ac03a003b003c553ac03a013b003c543ac13a033b003c533ac13a043b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c533ac13a053b003c
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!114 &-3923895880809708522
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0353a89b1f911e48b9e16bdc9f2e058, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 5
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Character - Height Gradient Shader - Skybox
  m_Shader: {fileID: 4800000, guid: ********************************, type: 3}
  m_ValidKeywords: []
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BaseMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Gradient:
        m_Texture: {fileID: -8444485339891319028}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Intensity:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AlphaClip: 0
    - _Blend: 0
    - _BumpScale: 1
    - _ClearCoatMask: 0
    - _ClearCoatSmoothness: 0
    - _Cull: 2
    - _Cutoff: 0.5
    - _DetailAlbedoMapScale: 1
    - _DetailNormalMapScale: 1
    - _DirectionPitch: 0.185
    - _DirectionYaw: 0.651
    - _DstBlend: 0
    - _EnvironmentReflections: 1
    - _GlossMapScale: 0
    - _Glossiness: 0
    - _GlossyReflections: 0
    - _Metallic: 0
    - _OcclusionStrength: 1
    - _Parallax: 0.005
    - _QueueOffset: 0
    - _ReceiveShadows: 1
    - _Smoothness: 0.5
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _Surface: 0
    - _WorkflowMode: 1
    - _ZWrite: 1
    m_Colors:
    - _BaseColor: {r: 1, g: 1, b: 1, a: 1}
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _SpecColor: {r: 0.19999996, g: 0.19999996, b: 0.19999996, a: 1}
  m_BuildTextureStacks: []
