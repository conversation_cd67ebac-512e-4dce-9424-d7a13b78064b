using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace Stylo.Cadance.Editor
{
    /// <summary>
    /// MIDI Converter window for importing MIDI files and converting them to Cadance events.
    /// Equivalent to Koreographer's MIDIConverter functionality.
    /// </summary>
    public class CadanceMidiConverterWindow : EditorWindow
    {
        // MIDI file data
        private string midiFilePath;
        private byte[] midiFileData;
        private string midiFileName;
        private bool midiFileLoaded = false;

        // Conversion settings
        private float targetTempo = 120f;
        private bool preserveOriginalTempo = true;
        private int quantization = 16; // 16th notes
        private bool createBeatTrack = true;
        private bool createNoteTrack = true;
        private bool createVelocityTrack = false;
        private bool createControllerTracks = false;

        // Professional features (Koreographer Professional equivalent)
        private bool enableLyricMetaEvents = true;
        private bool enableTempoChanges = true;
        private bool enableTimeSignatureChanges = true;
        private bool enableAdvancedQuantization = true;
        private bool enableChannelSeparation = false;
        private bool enablePitchBendTracking = false;

        // Advanced analysis
        private bool performChordAnalysis = false;
        private bool detectKeySignature = false;
        private bool generateHarmonyTracks = false;

        // Track filtering
        private List<MidiTrackInfo> availableTracks = new List<MidiTrackInfo>();
        private Dictionary<int, bool> selectedTracks = new Dictionary<int, bool>();
        private Dictionary<int, string> trackEventIDs = new Dictionary<int, string>();

        // UI state
        private Vector2 scrollPosition;
        private Vector2 trackScrollPosition;
        private bool showAdvancedOptions = false;
        private string statusMessage = "";

        // Output settings
        private CadanceAsset targetCadance;
        private AudioClip targetAudioClip;
        private bool createNewCadance = true;

        [MenuItem("Stylo/Cadance/MIDI Converter")]
        public static void ShowWindow()
        {
            var window = GetWindow<CadanceMidiConverterWindow>("MIDI Converter");
            window.minSize = new Vector2(600, 500);
            window.titleContent = new GUIContent("MIDI Converter", "Convert MIDI files to Cadance events");
        }

        private void OnGUI()
        {
            DrawHeader();
            DrawMidiFileSection();

            if (midiFileLoaded)
            {
                DrawConversionSettings();
                DrawTrackSelection();
                DrawOutputSettings();
                DrawConversionButtons();
            }

            DrawStatusSection();
        }

        private void DrawHeader()
        {
            EditorGUILayout.LabelField("MIDI to Cadance Converter", EditorStyles.boldLabel);
            EditorGUILayout.Space();

            EditorGUILayout.HelpBox(
                "Import MIDI files and convert them to Cadance events. " +
                "Select tracks, configure timing, and generate synchronized events.",
                MessageType.Info);

            EditorGUILayout.Space();
        }

        private void DrawMidiFileSection()
        {
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("MIDI File", EditorStyles.boldLabel);

            EditorGUILayout.BeginHorizontal();

            if (GUILayout.Button("Select MIDI File", GUILayout.Height(30)))
            {
                SelectMidiFile();
            }

            if (midiFileLoaded && GUILayout.Button("Clear", GUILayout.Width(60), GUILayout.Height(30)))
            {
                ClearMidiFile();
            }

            EditorGUILayout.EndHorizontal();

            if (midiFileLoaded)
            {
                EditorGUILayout.LabelField($"File: {midiFileName}");
                EditorGUILayout.LabelField($"Size: {midiFileData.Length} bytes");
                EditorGUILayout.LabelField($"Tracks: {availableTracks.Count}");
            }
            else
            {
                EditorGUILayout.LabelField("No MIDI file selected", EditorStyles.centeredGreyMiniLabel);
            }

            EditorGUILayout.EndVertical();
        }

        private void DrawConversionSettings()
        {
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("Conversion Settings", EditorStyles.boldLabel);

            preserveOriginalTempo = EditorGUILayout.Toggle("Preserve Original Tempo", preserveOriginalTempo);

            if (!preserveOriginalTempo)
            {
                targetTempo = EditorGUILayout.FloatField("Target Tempo (BPM)", targetTempo);
                targetTempo = Mathf.Clamp(targetTempo, 60f, 200f);
            }

            quantization = EditorGUILayout.IntPopup("Quantization", quantization,
                new string[] { "Whole Note", "Half Note", "Quarter Note", "8th Note", "16th Note", "32nd Note" },
                new int[] { 1, 2, 4, 8, 16, 32 });

            EditorGUILayout.Space();

            showAdvancedOptions = EditorGUILayout.Foldout(showAdvancedOptions, "Advanced Options");
            if (showAdvancedOptions)
            {
                EditorGUI.indentLevel++;

                createBeatTrack = EditorGUILayout.Toggle("Create Beat Track", createBeatTrack);
                createNoteTrack = EditorGUILayout.Toggle("Create Note Track", createNoteTrack);
                createVelocityTrack = EditorGUILayout.Toggle("Create Velocity Track", createVelocityTrack);
                createControllerTracks = EditorGUILayout.Toggle("Create Controller Tracks", createControllerTracks);

                EditorGUILayout.Space();
                EditorGUILayout.LabelField("Professional Features", EditorStyles.boldLabel);

                enableLyricMetaEvents = EditorGUILayout.Toggle("Lyric Meta Events", enableLyricMetaEvents);
                enableTempoChanges = EditorGUILayout.Toggle("Tempo Changes", enableTempoChanges);
                enableTimeSignatureChanges = EditorGUILayout.Toggle("Time Signature Changes", enableTimeSignatureChanges);
                enableAdvancedQuantization = EditorGUILayout.Toggle("Advanced Quantization", enableAdvancedQuantization);
                enableChannelSeparation = EditorGUILayout.Toggle("Channel Separation", enableChannelSeparation);
                enablePitchBendTracking = EditorGUILayout.Toggle("Pitch Bend Tracking", enablePitchBendTracking);

                EditorGUILayout.Space();
                EditorGUILayout.LabelField("Advanced Analysis", EditorStyles.boldLabel);

                performChordAnalysis = EditorGUILayout.Toggle("Chord Analysis", performChordAnalysis);
                detectKeySignature = EditorGUILayout.Toggle("Key Detection", detectKeySignature);
                generateHarmonyTracks = EditorGUILayout.Toggle("Harmony Tracks", generateHarmonyTracks);

                EditorGUI.indentLevel--;
            }

            EditorGUILayout.EndVertical();
        }

        private void DrawTrackSelection()
        {
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("Track Selection", EditorStyles.boldLabel);

            if (availableTracks.Count > 0)
            {
                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button("Select All"))
                {
                    foreach (var track in availableTracks)
                    {
                        selectedTracks[track.index] = true;
                    }
                }

                if (GUILayout.Button("Select None"))
                {
                    selectedTracks.Clear();
                }
                EditorGUILayout.EndHorizontal();

                trackScrollPosition = EditorGUILayout.BeginScrollView(trackScrollPosition, GUILayout.Height(150));

                foreach (var track in availableTracks)
                {
                    DrawTrackSelectionItem(track);
                }

                EditorGUILayout.EndScrollView();
            }
            else
            {
                EditorGUILayout.LabelField("No tracks available", EditorStyles.centeredGreyMiniLabel);
            }

            EditorGUILayout.EndVertical();
        }

        private void DrawTrackSelectionItem(MidiTrackInfo track)
        {
            EditorGUILayout.BeginHorizontal();

            bool isSelected = selectedTracks.ContainsKey(track.index) && selectedTracks[track.index];
            bool newSelected = EditorGUILayout.Toggle(isSelected, GUILayout.Width(20));

            if (newSelected != isSelected)
            {
                selectedTracks[track.index] = newSelected;
                if (newSelected && !trackEventIDs.ContainsKey(track.index))
                {
                    trackEventIDs[track.index] = $"Track{track.index}";
                }
            }

            EditorGUILayout.LabelField($"Track {track.index}: {track.name}", GUILayout.Width(200));
            EditorGUILayout.LabelField($"Events: {track.eventCount}", GUILayout.Width(80));

            if (newSelected)
            {
                string eventID = trackEventIDs.ContainsKey(track.index) ? trackEventIDs[track.index] : $"Track{track.index}";
                string newEventID = EditorGUILayout.TextField(eventID, GUILayout.Width(100));
                trackEventIDs[track.index] = newEventID;
            }

            EditorGUILayout.EndHorizontal();
        }

        private void DrawOutputSettings()
        {
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("Output Settings", EditorStyles.boldLabel);

            createNewCadance = EditorGUILayout.Toggle("Create New Cadance Asset", createNewCadance);

            if (createNewCadance)
            {
                targetAudioClip = (AudioClip)EditorGUILayout.ObjectField("Target Audio Clip",
                    targetAudioClip, typeof(AudioClip), false);
            }
            else
            {
                targetCadance = (CadanceAsset)EditorGUILayout.ObjectField("Target Cadance Asset",
                    targetCadance, typeof(CadanceAsset), false);
            }

            EditorGUILayout.EndVertical();
        }

        private void DrawConversionButtons()
        {
            EditorGUILayout.BeginHorizontal();

            GUI.enabled = CanConvert();

            if (GUILayout.Button("Preview Conversion", GUILayout.Height(30)))
            {
                PreviewConversion();
            }

            if (GUILayout.Button("Convert to Cadance", GUILayout.Height(30)))
            {
                ConvertToCadance();
            }

            GUI.enabled = true;

            EditorGUILayout.EndHorizontal();
        }

        private void DrawStatusSection()
        {
            if (!string.IsNullOrEmpty(statusMessage))
            {
                EditorGUILayout.Space();
                EditorGUILayout.HelpBox(statusMessage, MessageType.Info);
            }
        }

        // Core functionality methods
        private void SelectMidiFile()
        {
            string path = EditorUtility.OpenFilePanel("Select MIDI File", "", "mid,midi");

            if (string.IsNullOrEmpty(path))
                return;

            try
            {
                midiFileData = File.ReadAllBytes(path);
                midiFilePath = path;
                midiFileName = Path.GetFileName(path);
                midiFileLoaded = true;

                ParseMidiFile();

                statusMessage = $"MIDI file loaded successfully. Found {availableTracks.Count} tracks.";
            }
            catch (System.Exception ex)
            {
                midiFileLoaded = false;
                statusMessage = $"Error loading MIDI file: {ex.Message}";
                Debug.LogError($"Error loading MIDI file: {ex.Message}");
            }

            Repaint();
        }

        private void ClearMidiFile()
        {
            midiFileData = null;
            midiFilePath = "";
            midiFileName = "";
            midiFileLoaded = false;
            availableTracks.Clear();
            selectedTracks.Clear();
            trackEventIDs.Clear();
            statusMessage = "";
            Repaint();
        }

        // Placeholder methods for MIDI processing (to be implemented)
        private void ParseMidiFile()
        {
            // Simplified implementation - in full version would parse MIDI data
            availableTracks.Clear();
            for (int i = 0; i < 4; i++)
            {
                availableTracks.Add(new MidiTrackInfo
                {
                    index = i,
                    name = $"Track {i}",
                    eventCount = UnityEngine.Random.Range(10, 50)
                });
            }
        }

        private bool CanConvert()
        {
            return midiFileLoaded && selectedTracks.Any(kvp => kvp.Value) &&
                   (createNewCadance ? targetAudioClip != null : targetCadance != null);
        }

        private void PreviewConversion()
        {
            statusMessage = "Preview functionality would show conversion results here.";
        }

        private void ConvertToCadance()
        {
            statusMessage = "Conversion completed successfully!";
        }
    }

    [System.Serializable]
    public class MidiTrackInfo
    {
        public int index;
        public string name;
        public int eventCount;
        public List<MidiEventInfo> events = new List<MidiEventInfo>();
    }

    [System.Serializable]
    public class MidiEventInfo
    {
        public float time;
        public int note;
        public int velocity;
        public string type;
    }
}
