%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Force Shield
  m_Shader: {fileID: 4800000, guid: 1fe08586c5f844bc8460d79822219df4, type: 3}
  m_ValidKeywords: []
  m_InvalidKeywords:
  - _ADD_PRECOMPUTED_VELOCITY
  - _DISABLE_SSR_TRANSPARENT
  - _EMISSION
  - _ENABLE_FOG_ON_TRANSPARENT
  - _SURFACE_TYPE_TRANSPARENT
  m_LightmapFlags: 1
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: 3000
  stringTagMap:
    MotionVector: User
  disabledShaderPasses:
  - TransparentBackface
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _Albedo:
        m_Texture: {fileID: 2800000, guid: aab8de1052c54173b3d61e7f8b05aedf, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Normal:
        m_Texture: {fileID: 2800000, guid: 161b0899cec643d9b2a5b72bb8e1788b, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ShieldPattern:
        m_Texture: {fileID: 2800000, guid: a6da1193c8ce46cc8eb5493dcb772419, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ShieldPatternWaves:
        m_Texture: {fileID: 2800000, guid: 937c2d88ef9b4e889d5a0a6a952c1a4b, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _texcoord:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AddPrecomputedVelocity: 1
    - _AlphaCutoff: 0.5
    - _AlphaCutoffEnable: 0
    - _AlphaDstBlend: 10
    - _AlphaSrcBlend: 0
    - _AlphaToMask: 0
    - _AlphaToMaskInspectorValue: 0
    - _BlendMode: 0
    - _Cull: 2
    - _CullMode: 2
    - _CullModeForward: 2
    - _DoubleSidedEnable: 0
    - _DoubleSidedGIMode: 0
    - _DoubleSidedNormalMode: 2
    - _DstBlend: 10
    - _EmissionIntensity: 9.53
    - _EmissionWeight: 0.44
    - _EnableBlendModePreserveSpecularLighting: 1
    - _EnableFogOnTransparent: 1
    - _EnvironmentReflections: 1
    - _HitSize: 0.25
    - _HitTime: 3
    - _Intensity: 12.63
    - _IntersectIntensity: 0.382
    - _MaskClipValue: 0
    - _Opacity: 0.661
    - _OpaqueCullMode: 2
    - _QueueControl: 0
    - _QueueOffset: 0
    - _ReceiveShadows: 1
    - _ReceivesSSR: 1
    - _ReceivesSSRTransparent: 0
    - _RenderQueueType: 5
    - _RequireSplitLighting: 0
    - _ShieldAnimSpeed: 6
    - _ShieldDistortion: 0.00597
    - _ShieldPatternPower: 27.6
    - _ShieldPatternSize: 5
    - _ShieldRimPower: 7
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _StencilRef: 0
    - _StencilRefDepth: 0
    - _StencilRefDistortionVec: 4
    - _StencilRefGBuffer: 2
    - _StencilRefMV: 32
    - _StencilWriteMask: 6
    - _StencilWriteMaskDepth: 8
    - _StencilWriteMaskDistortionVec: 4
    - _StencilWriteMaskGBuffer: 14
    - _StencilWriteMaskMV: 40
    - _SupportDecals: 1
    - _SurfaceType: 1
    - _TransparentBackfaceEnable: 0
    - _TransparentCullMode: 2
    - _TransparentSortPriority: 0
    - _TransparentWritingMotionVec: 0
    - _TransparentZWrite: 0
    - _UseShadowThreshold: 0
    - _ZTestDepthEqualForOpaque: 4
    - _ZTestGBuffer: 4
    - _ZTestTransparent: 4
    - _ZWrite: 0
    - __dirty: 0
    m_Colors:
    - _Color: {r: 0, g: 0, b: 0, a: 0}
    - _DoubleSidedConstants: {r: 1, g: 1, b: -1, a: 0}
    - _EmissionColor: {r: 1, g: 1, b: 1, a: 1}
    - _HitColor: {r: 1, g: 1, b: 1, a: 1}
    - _HitPosition: {r: 0, g: 0, b: 0, a: 0}
    - _IntersectColor: {r: 0.9607843, g: 0.21960784, b: 0, a: 1}
    - _ShieldPatternColor: {r: 0.6784314, g: 0.45882353, b: 0.30588236, a: 1}
  m_BuildTextureStacks: []
