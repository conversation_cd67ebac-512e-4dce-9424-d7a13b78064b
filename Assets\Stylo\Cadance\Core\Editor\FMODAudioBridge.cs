using System;
using System.IO;
using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.Linq;

namespace Stylo.Cadance
{
    /// <summary>
    /// Bridge system that connects FMOD audio files to Unity AudioClips for Cadance Editor timeline functionality.
    /// Provides seamless audio preview and waveform display for FMOD-based assets.
    /// </summary>
    public static class FMODAudioBridge
    {
        private static Dictionary<string, AudioClip> audioClipCache = new Dictionary<string, AudioClip>();
        private static Dictionary<string, string> fmodPathCache = new Dictionary<string, string>();

        /// <summary>
        /// Attempts to find and import an FMOD audio file as a Unity AudioClip for timeline preview.
        /// </summary>
        /// <param name="sourceClipName">The source clip name from the CadanceAsset</param>
        /// <returns>AudioClip for timeline use, or null if not found</returns>
        public static AudioClip GetOrCreateAudioClipForTimeline(string sourceClipName)
        {
            if (string.IsNullOrEmpty(sourceClipName))
                return null;

            // Check cache first
            if (audioClipCache.TryGetValue(sourceClipName, out AudioClip cachedClip) && cachedClip != null)
                return cachedClip;

            // Try to find existing Unity AudioClip
            AudioClip existingClip = FindExistingAudioClip(sourceClipName);
            if (existingClip != null)
            {
                audioClipCache[sourceClipName] = existingClip;
                return existingClip;
            }

            // Try to import from FMOD project
            AudioClip importedClip = ImportFromFMODProject(sourceClipName);
            if (importedClip != null)
            {
                audioClipCache[sourceClipName] = importedClip;
                return importedClip;
            }

            return null;
        }

        /// <summary>
        /// Finds an existing AudioClip in the Unity project that matches the source clip name.
        /// </summary>
        private static AudioClip FindExistingAudioClip(string sourceClipName)
        {
            // Search for exact match
            string[] guids = AssetDatabase.FindAssets($"{sourceClipName} t:AudioClip");
            if (guids.Length > 0)
            {
                string path = AssetDatabase.GUIDToAssetPath(guids[0]);
                return AssetDatabase.LoadAssetAtPath<AudioClip>(path);
            }

            // Search for partial matches
            guids = AssetDatabase.FindAssets("t:AudioClip");
            foreach (string guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                string fileName = Path.GetFileNameWithoutExtension(path);

                if (fileName.Contains(sourceClipName) || sourceClipName.Contains(fileName))
                {
                    return AssetDatabase.LoadAssetAtPath<AudioClip>(path);
                }
            }

            return null;
        }

        /// <summary>
        /// Attempts to import an audio file from the FMOD project directory.
        /// </summary>
        private static AudioClip ImportFromFMODProject(string sourceClipName)
        {
            string fmodAudioPath = FindFMODAudioFile(sourceClipName);
            if (string.IsNullOrEmpty(fmodAudioPath))
                return null;

            return ImportAudioFileToUnity(fmodAudioPath, sourceClipName);
        }

        /// <summary>
        /// Finds the FMOD audio file path for the given source clip name.
        /// </summary>
        private static string FindFMODAudioFile(string sourceClipName)
        {
            // Check cache first
            if (fmodPathCache.TryGetValue(sourceClipName, out string cachedPath) && File.Exists(cachedPath))
                return cachedPath;

            // Common FMOD project paths
            string[] fmodPaths = {
                "FMOD Studio Projects/Beat Traveller Reload/Assets",
                "../FMOD Studio Projects/Beat Traveller Reload/Assets",
                "../../FMOD Studio Projects/Beat Traveller Reload/Assets"
            };

            foreach (string basePath in fmodPaths)
            {
                string fullBasePath = Path.GetFullPath(Path.Combine(Application.dataPath, basePath));
                if (!Directory.Exists(fullBasePath))
                    continue;

                // Search for the audio file
                string[] extensions = { ".wav", ".mp3", ".ogg", ".aiff" };
                foreach (string ext in extensions)
                {
                    string filePath = Path.Combine(fullBasePath, sourceClipName + ext);
                    if (File.Exists(filePath))
                    {
                        fmodPathCache[sourceClipName] = filePath;
                        return filePath;
                    }
                }

                // Search recursively
                try
                {
                    foreach (string ext in extensions)
                    {
                        string[] files = Directory.GetFiles(fullBasePath, sourceClipName + ext, SearchOption.AllDirectories);
                        if (files.Length > 0)
                        {
                            fmodPathCache[sourceClipName] = files[0];
                            return files[0];
                        }
                    }
                }
                catch (Exception ex)
                {
                    Debug.LogWarning($"[FMOD Audio Bridge] Error searching directory {fullBasePath}: {ex.Message}");
                }
            }

            return null;
        }

        /// <summary>
        /// Imports an audio file from outside the Unity project into the Assets folder.
        /// </summary>
        private static AudioClip ImportAudioFileToUnity(string sourcePath, string sourceClipName)
        {
            try
            {
                // Create import directory
                string importDir = "Assets/Stylo/Cadance/ImportedAudio";
                if (!AssetDatabase.IsValidFolder(importDir))
                {
                    Directory.CreateDirectory(Path.Combine(Application.dataPath, "Stylo/Cadance/ImportedAudio"));
                    AssetDatabase.Refresh();
                }

                // Copy file to Unity project
                string extension = Path.GetExtension(sourcePath);
                string targetPath = Path.Combine(importDir, sourceClipName + extension);

                File.Copy(sourcePath, targetPath, true);
                AssetDatabase.Refresh();

                // Load as AudioClip
                AudioClip audioClip = AssetDatabase.LoadAssetAtPath<AudioClip>(targetPath);
                if (audioClip != null)
                {
                    Debug.Log($"[FMOD Audio Bridge] Successfully imported {sourceClipName} from FMOD project");
                    return audioClip;
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[FMOD Audio Bridge] Failed to import {sourceClipName}: {ex.Message}");
            }

            return null;
        }

        /// <summary>
        /// Clears the audio clip cache.
        /// </summary>
        public static void ClearCache()
        {
            audioClipCache.Clear();
            fmodPathCache.Clear();
        }

        /// <summary>
        /// Gets information about available FMOD audio files.
        /// </summary>
        public static List<string> GetAvailableFMODAudioFiles()
        {
            var files = new List<string>();

            string[] fmodPaths = {
                "FMOD Studio Projects/Beat Traveller Reload/Assets",
                "../FMOD Studio Projects/Beat Traveller Reload/Assets"
            };

            foreach (string basePath in fmodPaths)
            {
                string fullBasePath = Path.GetFullPath(Path.Combine(Application.dataPath, basePath));
                if (!Directory.Exists(fullBasePath))
                    continue;

                try
                {
                    string[] audioFiles = Directory.GetFiles(fullBasePath, "*.*", SearchOption.AllDirectories)
                        .Where(f => f.EndsWith(".wav") || f.EndsWith(".mp3") || f.EndsWith(".ogg") || f.EndsWith(".aiff"))
                        .ToArray();

                    foreach (string file in audioFiles)
                    {
                        string fileName = Path.GetFileNameWithoutExtension(file);
                        if (!files.Contains(fileName))
                            files.Add(fileName);
                    }
                }
                catch (Exception ex)
                {
                    Debug.LogWarning($"[FMOD Audio Bridge] Error scanning {fullBasePath}: {ex.Message}");
                }
            }

            return files;
        }

        /// <summary>
        /// Batch import multiple FMOD audio files for a CadanceSet.
        /// </summary>
        public static int BatchImportForCadanceSet(CadanceSet cadanceSet)
        {
            if (cadanceSet == null)
                return 0;

            int importedCount = 0;
            foreach (var entry in cadanceSet.Cadances)
            {
                if (entry.cadance != null && !string.IsNullOrEmpty(entry.cadance.SourceClipName))
                {
                    AudioClip imported = GetOrCreateAudioClipForTimeline(entry.cadance.SourceClipName);
                    if (imported != null && entry.cadance.SourceClip == null)
                    {
                        entry.cadance.SourceClip = imported;
                        EditorUtility.SetDirty(entry.cadance);
                        importedCount++;
                    }
                }
            }

            if (importedCount > 0)
            {
                AssetDatabase.SaveAssets();
                Debug.Log($"[FMOD Audio Bridge] Batch imported {importedCount} audio files for CadanceSet '{cadanceSet.name}'");
            }

            return importedCount;
        }
    }
}
