fileFormatVersion: 2
guid: dde72cd477072284786978889ffce393
MonoImporter:
  externalObjects: {}
  serializedVersion: 2
  defaultReferences:
  - internalPostFXResources: {fileID: 11400000, guid: 4be3c01c56742ae44be75ba170c06426,
      type: 2}
  - defaultReflectorMesh: {fileID: 4300002, guid: 7895f1f943551db4e9784958a7e88a58,
      type: 3}
  - defaultReflectorMaterial: {fileID: 2100000, guid: fbfc4efe23a9b0046a787d829353352b,
      type: 2}
  - externalReflectionTex: {instanceID: 0}
  - externalReflectionDepth: {instanceID: 0}
  executionOrder: 300
  icon: {fileID: 2800000, guid: 3c249ea879248f340a8701634b45b2a0, type: 3}
  userData: 
  assetBundleName: 
  assetBundleVariant: 
AssetOrigin:
  serializedVersion: 1
  productId: 257485
  packageName: 'PIDI : Planar Reflections 5 - URP Edition'
  packageVersion: 5.4.2
  assetPath: Assets/PIDI Game Development Framework/Planar Reflections 5/Source Code/PlanarReflectionRenderer.cs
  uploadId: 749443
