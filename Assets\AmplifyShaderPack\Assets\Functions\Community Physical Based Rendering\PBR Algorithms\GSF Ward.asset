%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: GSF Ward
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor v1.9.4.4\n// Available at the
    Unity Asset Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=19404\nNode;AmplifyShaderEditor.CommentaryNode;30;-1792,1920;Inherit;False;835.233;378.179;Math;5;223;149;135;338;336;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;265;-2877.201,1920;Inherit;False;1024.938;573.2673;Dot
    Products;9;268;269;302;327;326;271;270;341;342;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;336;-1760,1984;Inherit;False;195.7427;137.5905;NdotV;1;152;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;232;-1792,1632;Inherit;False;732.4076;253.31;GSF
    Ward Node;1;52;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;338;-1760,2128;Inherit;False;195.7427;137.5905;NdotL;1;337;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;52;-1440,1680;Inherit;False;345.8074;138.0092;;;0.06603771,0.06603771,0.06603771,1;The
    Ward GSF is a strengthened Implicit GSF. Ward uses this approach to strengthen
    the Normal Distribution Functions -Jorden Stevens;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;335;-1792,1472;Inherit;False;547.627;143.3716;Credits;;0,0,0,0.5019608;Method
    by Jordan Stevens$Link: https://www.jordanstevenstechart.com/physically-based-rendering$$Node
    Made by Tiber Legacy$Special Thanks to Dawie;0;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;135;-1520,2032;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.PowerNode;149;-1344,2032;Inherit;False;True;2;0;FLOAT;0;False;1;FLOAT;0.5;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;270;-2544,2096;Inherit;False;NdotV;1;2;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;271;-2544,2320;Inherit;False;NdotL;1;1;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;326;-2112,2016;Inherit;True;NdotV;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;327;-2112,2240;Inherit;True;NdotL;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;302;-2800,2016;Inherit;False;Normal;3;0;False;1;0;FLOAT3;0,0,1;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitch;269;-2384,2240;Inherit;False;Option;False;0;2;1;In
    0;In 1;Instance;268;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;268;-2384,2016;Inherit;False;Custom
    Dot Products;True;0;2;1;In 0;In 1;Object;-1;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;152;-1744,2032;Inherit;False;326;NdotV;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;337;-1744,2176;Inherit;False;327;NdotL;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionNode;341;-2624,2016;Inherit;False;NdotV;-1;;4;65be4ec269a534f4e8867af2f999aaa9;4,27,1,377,0,452,0,451,0;3;230;FLOAT3;0,0,1;False;443;FLOAT3;0,0,0;False;415;FLOAT;1E-37;False;1;FLOAT;23\nNode;AmplifyShaderEditor.FunctionNode;342;-2624,2240;Inherit;False;NdotL;-1;;5;6d7ee1aa8c79587448475fa2b37affc1;5,27,1,381,0,460,0,461,0,463,0;4;230;FLOAT3;0,0,1;False;462;FLOAT3;0,0,0;False;454;FLOAT3;0,0,0;False;416;FLOAT;1E-37;False;1;FLOAT;111\nNode;AmplifyShaderEditor.FunctionOutput;223;-1184,2032;Inherit;True;True;-1;Out;0;False;1;0;FLOAT;0;False;1;FLOAT;0\nWireConnection;135;0;152;0\nWireConnection;135;1;337;0\nWireConnection;149;0;135;0\nWireConnection;326;0;268;0\nWireConnection;327;0;269;0\nWireConnection;269;0;342;111\nWireConnection;269;1;271;0\nWireConnection;268;0;341;23\nWireConnection;268;1;270;0\nWireConnection;341;230;302;0\nWireConnection;342;230;302;0\nWireConnection;223;0;149;0\nASEEND*/\n//CHKSM=F692E5DA2B0206E084CFDCCC3BFDFA0BD751EDB4"
  m_functionName: 
  m_description: 'GSF Ward Node

'
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 0
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_customEdited: 0
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 0
  m_headerStyle: 2
  m_headerColor: {r: 0.7529412, g: 0.6784314, b: 0, a: 1}
  m_customNodeCategory: Amplify Shader Pack
  m_previewPosition: 0
  m_hidden: 0
  m_url: 
