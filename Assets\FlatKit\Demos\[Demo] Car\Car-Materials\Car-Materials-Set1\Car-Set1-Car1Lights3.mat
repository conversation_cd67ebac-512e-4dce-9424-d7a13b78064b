%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Car-Set1-Car1Lights3
  m_Shader: {fileID: 4800000, guid: bee44b4a58655ee4cbff107302a3e131, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords:
  - DR_CEL_EXTRA_ON
  - _CELPRIMARYMODE_SINGLE
  - _TEXTUREBLENDINGMODE_MULTIPLY
  - _UNITYSHADOWMODE_MULTIPLY
  m_InvalidKeywords:
  - DR_CEL_PRIMARY_ON
  - _FLAT_SHADOWS_ENABLED
  - _FLAT_SPECULAR_ENABLED
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 1
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses:
  - SRPDEFAULTUNLIT
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BaseMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _CelCurveTexture:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _CelStepTexture:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AlphaClip: 0
    - _Blend: 0
    - _BumpScale: 1
    - _CameraDistanceImpact: 0
    - _CelExtraEnabled: 1
    - _CelNumSteps: 3
    - _CelPrimaryEnabled: 1
    - _CelPrimaryMode: 1
    - _Cull: 2
    - _Cutoff: 0.5
    - _DetailNormalMapScale: 1
    - _DstBlend: 0
    - _FlatRimAmount: 0.5
    - _FlatRimEdgeSmoothness: 0.611
    - _FlatRimEnabled: 1
    - _FlatRimLightAlign: 0
    - _FlatRimSize: 0.156
    - _FlatShadowsEnabled: 1
    - _FlatSmoothness: 0.362
    - _FlatSpecularEdgeSmoothness: 0.897
    - _FlatSpecularEnabled: 1
    - _FlatSpecularSize: 0.1
    - _FlatSpecularSmoothness: 0.5
    - _Flatness: 1
    - _FlatnessExtra: 1
    - _FlatnessSecondary: 1
    - _GlossMapScale: 1
    - _Glossiness: 0
    - _GlossyReflections: 1
    - _GradientAngle: 0
    - _GradientCenterX: 0
    - _GradientCenterY: 0
    - _GradientEnabled: 0
    - _GradientEnd: 0.55
    - _GradientSize: 10
    - _GradientStart: 1.17
    - _LightContribution: 0.5
    - _LightFalloffSize: 0.0001
    - _LightmapDirectionPitch: 0
    - _LightmapDirectionYaw: 0
    - _Metallic: 0
    - _Mode: 0
    - _OcclusionStrength: 1
    - _OutlineDepthOffset: 0
    - _OutlineEnabled: 0
    - _OutlineScale: 1
    - _OutlineWidth: 1
    - _OverrideLightmapDir: 0
    - _OverrideShadows: 1
    - _OverrideShadowsEnabled: 1
    - _Parallax: 0.02
    - _QueueOffset: 0
    - _RimEnabled: 0
    - _SecondaryColor: 1
    - _SecondaryColorEnabled: 1
    - _SelfShadingSize: -0.93
    - _SelfShadingSizeExtra: 0.5
    - _ShadowEdgeSize: 0.05
    - _ShadowEdgeSizeExtra: 0.05
    - _ShadowEdgeSmoothness: 0.071
    - _ShadowFalloff: 0.01
    - _SmoothnessTextureChannel: 0
    - _SpecularEnabled: 0
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _Surface: 0
    - _TextureBlendingMode: 0
    - _TextureImpact: 1
    - _UVSec: 0
    - _UnityShadowMode: 1
    - _UnityShadowOcclusion: 0
    - _UnityShadowPower: 0.2
    - _UnityShadowSharpness: 1
    - _VertexColorsEnabled: 0
    - _ZWrite: 1
    m_Colors:
    - _BaseColor: {r: 0.945098, g: 0.3683742, b: 0.039215684, a: 1}
    - _Color: {r: 0.94509804, g: 0.36837423, b: 0.039215684, a: 1}
    - _ColorCelShadow: {r: 0.49056602, g: 0.34478462, b: 0.4476891, a: 1}
    - _ColorDim: {r: 0.7735849, g: 0.7735849, b: 0.7735849, a: 0.85}
    - _ColorDimCurve: {r: 0.85, g: 0.85, b: 0.85, a: 0.85}
    - _ColorDimExtra: {r: 0.8617097, g: 0.8962264, b: 0.19023676, a: 0.85}
    - _ColorDimSteps: {r: 0.85, g: 0.85, b: 0.85, a: 0.85}
    - _ColorGradient: {r: 0.65, g: 0.65, b: 0.65, a: 1}
    - _ColorSecondary: {r: 1, g: 0.55879205, b: 0, a: 1}
    - _ColorShadow: {r: 0.5, g: 0.5, b: 0.5, a: 1}
    - _ColorShadows: {r: 0.5471698, g: 0.37346402, b: 0.26584196, a: 1}
    - _EmissionColor: {r: 4.9245777, g: 0, b: 0, a: 1}
    - _FlatRimColor: {r: 0.06274509, g: 0.3764706, b: 0.36200908, a: 1}
    - _FlatSpecularColor: {r: 0.3474101, g: 0.3490566, b: 0.348743, a: 1}
    - _LightmapDirection: {r: 0, g: 1, b: 0, a: 0}
    - _OutlineColor: {r: 1, g: 1, b: 1, a: 1}
    - _UnityShadowColor: {r: 0.65, g: 0.65, b: 0.65, a: 1}
  m_BuildTextureStacks: []
  m_AllowLocking: 1
