﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class FlexalonGridLayout
 | Flexalon 3D Layouts </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class FlexalonGridLayout
 | Flexalon 3D Layouts ">
    <meta name="generator" content="docfx ">
  
    <link rel="shortcut icon" href="images/favicon.svg">
    <link rel="stylesheet" href="styles/docfx.vendor.css">
    <link rel="stylesheet" href="styles/docfx.css">
    <link rel="stylesheet" href="styles/main.css">
    <meta property="docfx:navrel" content="tochead.html">
    <meta property="docfx:tocrel" content="toc.html">
  
  <meta property="docfx:rel" content="">
  
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="https://www.flexalon.com/">
                <img id="logo" class="svg" src="images/logo_32x32.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first="First" data-prev="Previous" data-next="Next" data-last="Last"></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">

        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="Flexalon.FlexalonGridLayout">


  <h1 id="Flexalon_FlexalonGridLayout" data-uid="Flexalon.FlexalonGridLayout" class="text-break">Class FlexalonGridLayout
</h1>
  <div class="markdown level0 summary"><p>Use a grid layout to position children at fixed intervals.
Objects are placed in cells in column-row-layer order.</p>
</div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">object</span></div>
    <div class="level1"><span class="xref">UnityEngine.Object</span></div>
    <div class="level2"><span class="xref">UnityEngine.Component</span></div>
    <div class="level3"><span class="xref">UnityEngine.Behaviour</span></div>
    <div class="level4"><span class="xref">UnityEngine.MonoBehaviour</span></div>
    <div class="level5"><a class="xref" href="Flexalon.FlexalonComponent.html">FlexalonComponent</a></div>
    <div class="level6"><a class="xref" href="Flexalon.LayoutBase.html">LayoutBase</a></div>
    <div class="level7"><span class="xref">FlexalonGridLayout</span></div>
  </div>
  <div class="implements">
    <h5>Implements</h5>
    <div><a class="xref" href="Flexalon.Layout.html">Layout</a></div>
  </div>
  <div class="inheritedMembers">
    <h5>Inherited Members</h5>
    <div>
      <a class="xref" href="Flexalon.LayoutBase.html#Flexalon_LayoutBase_DoOnEnable">LayoutBase.DoOnEnable()</a>
    </div>
    <div>
      <a class="xref" href="Flexalon.LayoutBase.html#Flexalon_LayoutBase_DoOnDisable">LayoutBase.DoOnDisable()</a>
    </div>
    <div>
      <a class="xref" href="Flexalon.LayoutBase.html#Flexalon_LayoutBase_ResetProperties">LayoutBase.ResetProperties()</a>
    </div>
    <div>
      <a class="xref" href="Flexalon.LayoutBase.html#Flexalon_LayoutBase_Initialize">LayoutBase.Initialize()</a>
    </div>
    <div>
      <a class="xref" href="Flexalon.LayoutBase.html#Flexalon_LayoutBase_SetChildrenFillShrinkSize_Flexalon_FlexalonNode_UnityEngine_Vector3_UnityEngine_Vector3_">LayoutBase.SetChildrenFillShrinkSize(FlexalonNode, Vector3, Vector3)</a>
    </div>
    <div>
      <a class="xref" href="Flexalon.FlexalonComponent.html#Flexalon_FlexalonComponent__node">FlexalonComponent._node</a>
    </div>
    <div>
      <a class="xref" href="Flexalon.FlexalonComponent.html#Flexalon_FlexalonComponent_Node">FlexalonComponent.Node</a>
    </div>
    <div>
      <a class="xref" href="Flexalon.FlexalonComponent.html#Flexalon_FlexalonComponent_MarkDirty">FlexalonComponent.MarkDirty()</a>
    </div>
    <div>
      <a class="xref" href="Flexalon.FlexalonComponent.html#Flexalon_FlexalonComponent_ForceUpdate">FlexalonComponent.ForceUpdate()</a>
    </div>
    <div>
      <a class="xref" href="Flexalon.FlexalonComponent.html#Flexalon_FlexalonComponent_UpdateProperties">FlexalonComponent.UpdateProperties()</a>
    </div>
    <div>
      <a class="xref" href="Flexalon.FlexalonComponent.html#Flexalon_FlexalonComponent_DoUpdate">FlexalonComponent.DoUpdate()</a>
    </div>
    <div>
      <a class="xref" href="Flexalon.FlexalonComponent.html#Flexalon_FlexalonComponent_Upgrade_System_Int32_">FlexalonComponent.Upgrade(int)</a>
    </div>
  </div>
  <h5 id="Flexalon_FlexalonGridLayout_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">[AddComponentMenu(&quot;Flexalon/Flexalon Grid Layout&quot;)]
[HelpURL(&quot;https://www.flexalon.com/docs/gridLayout&quot;)]
public class FlexalonGridLayout : LayoutBase, Layout</code></pre>
  </div>
  <h3 id="properties">Properties
</h3>
  <a id="Flexalon_FlexalonGridLayout_CellType_" data-uid="Flexalon.FlexalonGridLayout.CellType*"></a>
  <h4 id="Flexalon_FlexalonGridLayout_CellType" data-uid="Flexalon.FlexalonGridLayout.CellType">CellType</h4>
  <div class="markdown level1 summary"><p>The type of cell to use on the column-row axes.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public FlexalonGridLayout.CellTypes CellType { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.FlexalonGridLayout.html">FlexalonGridLayout</a>.<a class="xref" href="Flexalon.FlexalonGridLayout.CellTypes.html">CellTypes</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonGridLayout_ColumnDirection_" data-uid="Flexalon.FlexalonGridLayout.ColumnDirection*"></a>
  <h4 id="Flexalon_FlexalonGridLayout_ColumnDirection" data-uid="Flexalon.FlexalonGridLayout.ColumnDirection">ColumnDirection</h4>
  <div class="markdown level1 summary"><p>The direction of the column axis.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Direction ColumnDirection { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.Direction.html">Direction</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonGridLayout_Columns_" data-uid="Flexalon.FlexalonGridLayout.Columns*"></a>
  <h4 id="Flexalon_FlexalonGridLayout_Columns" data-uid="Flexalon.FlexalonGridLayout.Columns">Columns</h4>
  <div class="markdown level1 summary"><p>The number of columns in the grid.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public uint Columns { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">uint</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonGridLayout_ColumnSize_" data-uid="Flexalon.FlexalonGridLayout.ColumnSize*"></a>
  <h4 id="Flexalon_FlexalonGridLayout_ColumnSize" data-uid="Flexalon.FlexalonGridLayout.ColumnSize">ColumnSize</h4>
  <div class="markdown level1 summary"><p>The fixed size of the columns.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float ColumnSize { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">float</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonGridLayout_ColumnSizeType_" data-uid="Flexalon.FlexalonGridLayout.ColumnSizeType*"></a>
  <h4 id="Flexalon_FlexalonGridLayout_ColumnSizeType" data-uid="Flexalon.FlexalonGridLayout.ColumnSizeType">ColumnSizeType</h4>
  <div class="markdown level1 summary"><p>How to determine the size of the columns,</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public FlexalonGridLayout.CellSizeTypes ColumnSizeType { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.FlexalonGridLayout.html">FlexalonGridLayout</a>.<a class="xref" href="Flexalon.FlexalonGridLayout.CellSizeTypes.html">CellSizeTypes</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonGridLayout_ColumnSpacing_" data-uid="Flexalon.FlexalonGridLayout.ColumnSpacing*"></a>
  <h4 id="Flexalon_FlexalonGridLayout_ColumnSpacing" data-uid="Flexalon.FlexalonGridLayout.ColumnSpacing">ColumnSpacing</h4>
  <div class="markdown level1 summary"><p>The spacing between columns.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float ColumnSpacing { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">float</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonGridLayout_DepthAlign_" data-uid="Flexalon.FlexalonGridLayout.DepthAlign*"></a>
  <h4 id="Flexalon_FlexalonGridLayout_DepthAlign" data-uid="Flexalon.FlexalonGridLayout.DepthAlign">DepthAlign</h4>
  <div class="markdown level1 summary"><p>How to align each child in its cell in depth.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Align DepthAlign { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.Align.html">Align</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonGridLayout_HorizontalAlign_" data-uid="Flexalon.FlexalonGridLayout.HorizontalAlign*"></a>
  <h4 id="Flexalon_FlexalonGridLayout_HorizontalAlign" data-uid="Flexalon.FlexalonGridLayout.HorizontalAlign">HorizontalAlign</h4>
  <div class="markdown level1 summary"><p>How to align each child in its cell horizontally.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Align HorizontalAlign { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.Align.html">Align</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonGridLayout_LayerDirection_" data-uid="Flexalon.FlexalonGridLayout.LayerDirection*"></a>
  <h4 id="Flexalon_FlexalonGridLayout_LayerDirection" data-uid="Flexalon.FlexalonGridLayout.LayerDirection">LayerDirection</h4>
  <div class="markdown level1 summary"><p>The direction of the layer axis.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Direction LayerDirection { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.Direction.html">Direction</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonGridLayout_Layers_" data-uid="Flexalon.FlexalonGridLayout.Layers*"></a>
  <h4 id="Flexalon_FlexalonGridLayout_Layers" data-uid="Flexalon.FlexalonGridLayout.Layers">Layers</h4>
  <div class="markdown level1 summary"><p>The number of layers in the grid.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public uint Layers { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">uint</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonGridLayout_LayerSizeSize_" data-uid="Flexalon.FlexalonGridLayout.LayerSizeSize*"></a>
  <h4 id="Flexalon_FlexalonGridLayout_LayerSizeSize" data-uid="Flexalon.FlexalonGridLayout.LayerSizeSize">LayerSizeSize</h4>
  <div class="markdown level1 summary"><p>The fixed size of the layers.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float LayerSizeSize { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">float</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonGridLayout_LayerSizeType_" data-uid="Flexalon.FlexalonGridLayout.LayerSizeType*"></a>
  <h4 id="Flexalon_FlexalonGridLayout_LayerSizeType" data-uid="Flexalon.FlexalonGridLayout.LayerSizeType">LayerSizeType</h4>
  <div class="markdown level1 summary"><p>How to determine the size of the layers.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public FlexalonGridLayout.CellSizeTypes LayerSizeType { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.FlexalonGridLayout.html">FlexalonGridLayout</a>.<a class="xref" href="Flexalon.FlexalonGridLayout.CellSizeTypes.html">CellSizeTypes</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonGridLayout_LayerSpacing_" data-uid="Flexalon.FlexalonGridLayout.LayerSpacing*"></a>
  <h4 id="Flexalon_FlexalonGridLayout_LayerSpacing" data-uid="Flexalon.FlexalonGridLayout.LayerSpacing">LayerSpacing</h4>
  <div class="markdown level1 summary"><p>The spacing between layers.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float LayerSpacing { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">float</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonGridLayout_RowDirection_" data-uid="Flexalon.FlexalonGridLayout.RowDirection*"></a>
  <h4 id="Flexalon_FlexalonGridLayout_RowDirection" data-uid="Flexalon.FlexalonGridLayout.RowDirection">RowDirection</h4>
  <div class="markdown level1 summary"><p>The direction of the row axis.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Direction RowDirection { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.Direction.html">Direction</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonGridLayout_Rows_" data-uid="Flexalon.FlexalonGridLayout.Rows*"></a>
  <h4 id="Flexalon_FlexalonGridLayout_Rows" data-uid="Flexalon.FlexalonGridLayout.Rows">Rows</h4>
  <div class="markdown level1 summary"><p>The number of rows in the grid.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public uint Rows { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">uint</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonGridLayout_RowSize_" data-uid="Flexalon.FlexalonGridLayout.RowSize*"></a>
  <h4 id="Flexalon_FlexalonGridLayout_RowSize" data-uid="Flexalon.FlexalonGridLayout.RowSize">RowSize</h4>
  <div class="markdown level1 summary"><p>The fixed size of the rows.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float RowSize { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">float</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonGridLayout_RowSizeType_" data-uid="Flexalon.FlexalonGridLayout.RowSizeType*"></a>
  <h4 id="Flexalon_FlexalonGridLayout_RowSizeType" data-uid="Flexalon.FlexalonGridLayout.RowSizeType">RowSizeType</h4>
  <div class="markdown level1 summary"><p>How to determine the size of the rows.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public FlexalonGridLayout.CellSizeTypes RowSizeType { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.FlexalonGridLayout.html">FlexalonGridLayout</a>.<a class="xref" href="Flexalon.FlexalonGridLayout.CellSizeTypes.html">CellSizeTypes</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonGridLayout_RowSpacing_" data-uid="Flexalon.FlexalonGridLayout.RowSpacing*"></a>
  <h4 id="Flexalon_FlexalonGridLayout_RowSpacing" data-uid="Flexalon.FlexalonGridLayout.RowSpacing">RowSpacing</h4>
  <div class="markdown level1 summary"><p>The spacing between rows.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float RowSpacing { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">float</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonGridLayout_VerticalAlign_" data-uid="Flexalon.FlexalonGridLayout.VerticalAlign*"></a>
  <h4 id="Flexalon_FlexalonGridLayout_VerticalAlign" data-uid="Flexalon.FlexalonGridLayout.VerticalAlign">VerticalAlign</h4>
  <div class="markdown level1 summary"><p>How to align each child in its cell vertically.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Align VerticalAlign { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.Align.html">Align</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
</h3>
  <a id="Flexalon_FlexalonGridLayout_Arrange_" data-uid="Flexalon.FlexalonGridLayout.Arrange*"></a>
  <h4 id="Flexalon_FlexalonGridLayout_Arrange_Flexalon_FlexalonNode_UnityEngine_Vector3_" data-uid="Flexalon.FlexalonGridLayout.Arrange(Flexalon.FlexalonNode,UnityEngine.Vector3)">Arrange(FlexalonNode, Vector3)</h4>
  <div class="markdown level1 summary"><p>Position the children of node within the available bounds.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override void Arrange(FlexalonNode node, Vector3 layoutSize)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.FlexalonNode.html">FlexalonNode</a></td>
        <td><span class="parametername">node</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><span class="parametername">layoutSize</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="overrides">Overrides</h5>
  <div><a class="xref" href="Flexalon.LayoutBase.html#Flexalon_LayoutBase_Arrange_Flexalon_FlexalonNode_UnityEngine_Vector3_">LayoutBase.Arrange(FlexalonNode, Vector3)</a></div>
  <a id="Flexalon_FlexalonGridLayout_GetChildAt_" data-uid="Flexalon.FlexalonGridLayout.GetChildAt*"></a>
  <h4 id="Flexalon_FlexalonGridLayout_GetChildAt_System_Int32_System_Int32_System_Int32_" data-uid="Flexalon.FlexalonGridLayout.GetChildAt(System.Int32,System.Int32,System.Int32)">GetChildAt(int, int, int)</h4>
  <div class="markdown level1 summary"><p>Returns the first child in the cell.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Transform GetChildAt(int column, int row, int layer = 0)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">int</span></td>
        <td><span class="parametername">column</span></td>
        <td><p>The column of the cell.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">int</span></td>
        <td><span class="parametername">row</span></td>
        <td><p>The row of the cell.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">int</span></td>
        <td><span class="parametername">layer</span></td>
        <td><p>The layer of the cell.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">UnityEngine.Transform</span></td>
        <td><p>The first child in the cell.</p>
</td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonGridLayout_GetChildrenAt_" data-uid="Flexalon.FlexalonGridLayout.GetChildrenAt*"></a>
  <h4 id="Flexalon_FlexalonGridLayout_GetChildrenAt_System_Int32_System_Int32_System_Int32_" data-uid="Flexalon.FlexalonGridLayout.GetChildrenAt(System.Int32,System.Int32,System.Int32)">GetChildrenAt(int, int, int)</h4>
  <div class="markdown level1 summary"><p>Returns all children in the cell.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Transform[] GetChildrenAt(int column, int row, int layer = 0)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">int</span></td>
        <td><span class="parametername">column</span></td>
        <td><p>The column of the cell.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">int</span></td>
        <td><span class="parametername">row</span></td>
        <td><p>The row of the cell.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">int</span></td>
        <td><span class="parametername">layer</span></td>
        <td><p>The layer of the cell.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">UnityEngine.Transform</span>[]</td>
        <td><p>A list of children in the cell.</p>
</td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonGridLayout_Measure_" data-uid="Flexalon.FlexalonGridLayout.Measure*"></a>
  <h4 id="Flexalon_FlexalonGridLayout_Measure_Flexalon_FlexalonNode_UnityEngine_Vector3_UnityEngine_Vector3_UnityEngine_Vector3_" data-uid="Flexalon.FlexalonGridLayout.Measure(Flexalon.FlexalonNode,UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3)">Measure(FlexalonNode, Vector3, Vector3, Vector3)</h4>
  <div class="markdown level1 summary"><p>Perform minimal work to determine what the size of node and available size for node's children.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override Bounds Measure(FlexalonNode node, Vector3 size, Vector3 min, Vector3 max)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.FlexalonNode.html">FlexalonNode</a></td>
        <td><span class="parametername">node</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><span class="parametername">size</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><span class="parametername">min</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><span class="parametername">max</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">UnityEngine.Bounds</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="overrides">Overrides</h5>
  <div><a class="xref" href="Flexalon.LayoutBase.html#Flexalon_LayoutBase_Measure_Flexalon_FlexalonNode_UnityEngine_Vector3_UnityEngine_Vector3_UnityEngine_Vector3_">LayoutBase.Measure(FlexalonNode, Vector3, Vector3, Vector3)</a></div>
  <h3 id="implements">Implements</h3>
  <div>
      <a class="xref" href="Flexalon.Layout.html">Layout</a>
  </div>
</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In This Article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      Flexalon 3D Layouts
      
          </div>
        </div>
      </footer>
    </div>

    
    
    
  </body>
</html>
