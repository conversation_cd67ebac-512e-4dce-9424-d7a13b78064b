using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.Linq;
using Stylo.Cadance;

namespace Stylo.Cadance.Editor
{
    /// <summary>
    /// Utility for validating Cadance conversion results and FMOD event associations.
    /// </summary>
    public class CadanceConversionValidator : EditorWindow
    {
        private Vector2 scrollPosition;
        private List<CadanceSet> foundCadanceSets = new List<CadanceSet>();
        private bool scanComplete = false;
        private ValidationResults results = new ValidationResults();

        [MenuItem("Stylo/Cadance/Validation/Conversion Validator")]
        public static void ShowWindow()
        {
            var window = GetWindow<CadanceConversionValidator>("Cadance Conversion Validator");
            window.minSize = new Vector2(600, 400);
            window.Show();
        }

        private void OnGUI()
        {
            DrawHeader();
            
            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);
            
            DrawScanSection();
            
            if (scanComplete)
            {
                DrawValidationResults();
                DrawDetailedResults();
            }
            
            EditorGUILayout.EndScrollView();
        }

        private void DrawHeader()
        {
            EditorGUILayout.LabelField("Cadance Conversion Validator", EditorStyles.boldLabel);
            EditorGUILayout.Space();
            
            EditorGUILayout.HelpBox(
                "This tool validates converted CadanceSet assets to ensure FMOD event references are properly preserved.",
                MessageType.Info);
            
            EditorGUILayout.Space();
        }

        private void DrawScanSection()
        {
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("Validation Scan", EditorStyles.boldLabel);
            
            if (!scanComplete)
            {
                EditorGUILayout.LabelField("Click 'Validate Conversions' to scan all CadanceSet assets.");
                
                if (GUILayout.Button("Validate Conversions", GUILayout.Height(30)))
                {
                    ValidateConversions();
                }
            }
            else
            {
                EditorGUILayout.LabelField($"Validation complete. Scanned {foundCadanceSets.Count} CadanceSet assets.");
                
                if (GUILayout.Button("Re-scan Assets"))
                {
                    ValidateConversions();
                }
            }
            
            EditorGUILayout.EndVertical();
        }

        private void DrawValidationResults()
        {
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("Validation Summary", EditorStyles.boldLabel);
            
            // Overall statistics
            EditorGUILayout.LabelField($"Total CadanceSet Assets: {results.totalSets}");
            EditorGUILayout.LabelField($"Total CadanceEntry Objects: {results.totalEntries}");
            EditorGUILayout.LabelField($"Entries with FMOD Events: {results.entriesWithFMODEvents}");
            EditorGUILayout.LabelField($"Entries without FMOD Events: {results.entriesWithoutFMODEvents}");
            
            EditorGUILayout.Space();
            
            // Success rate
            float successRate = results.totalEntries > 0 ? (float)results.entriesWithFMODEvents / results.totalEntries * 100f : 0f;
            EditorGUILayout.LabelField($"FMOD Event Association Rate: {successRate:F1}%");
            
            // Status indicator
            if (successRate >= 90f)
            {
                EditorGUILayout.HelpBox("Excellent! Most CadanceEntry objects have FMOD event associations.", MessageType.Info);
            }
            else if (successRate >= 50f)
            {
                EditorGUILayout.HelpBox("Good progress, but some CadanceEntry objects are missing FMOD events.", MessageType.Warning);
            }
            else
            {
                EditorGUILayout.HelpBox("Many CadanceEntry objects are missing FMOD event associations. Consider re-running the conversion tool.", MessageType.Error);
            }
            
            EditorGUILayout.EndVertical();
        }

        private void DrawDetailedResults()
        {
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("Detailed Results", EditorStyles.boldLabel);
            
            foreach (var setResult in results.setResults)
            {
                DrawCadanceSetResult(setResult);
            }
            
            EditorGUILayout.EndVertical();
        }

        private void DrawCadanceSetResult(CadanceSetValidationResult setResult)
        {
            EditorGUILayout.BeginVertical("box");
            
            // Set header
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField(setResult.setName, EditorStyles.boldLabel);
            
            if (GUILayout.Button("Select", GUILayout.Width(60)))
            {
                Selection.activeObject = setResult.cadanceSet;
                EditorGUIUtility.PingObject(setResult.cadanceSet);
            }
            
            EditorGUILayout.EndHorizontal();
            
            // Set statistics
            EditorGUILayout.LabelField($"Entries: {setResult.totalEntries}, With FMOD: {setResult.entriesWithFMOD}, Without FMOD: {setResult.entriesWithoutFMOD}");
            
            // Entry details
            if (setResult.entriesWithoutFMOD > 0)
            {
                EditorGUI.indentLevel++;
                EditorGUILayout.LabelField("Entries missing FMOD events:", EditorStyles.miniLabel);
                
                foreach (string entryName in setResult.entriesWithoutFMODEvents)
                {
                    EditorGUILayout.LabelField($"• {entryName}", EditorStyles.miniLabel);
                }
                
                EditorGUI.indentLevel--;
            }
            
            EditorGUILayout.EndVertical();
        }

        private void ValidateConversions()
        {
            foundCadanceSets.Clear();
            results = new ValidationResults();
            
            try
            {
                EditorUtility.DisplayProgressBar("Validating Conversions", "Scanning for CadanceSet assets...", 0f);
                
                // Find all CadanceSet assets
                string[] cadanceSetGuids = AssetDatabase.FindAssets("t:CadanceSet");
                
                foreach (string guid in cadanceSetGuids)
                {
                    string path = AssetDatabase.GUIDToAssetPath(guid);
                    var cadanceSet = AssetDatabase.LoadAssetAtPath<CadanceSet>(path);
                    
                    if (cadanceSet != null)
                    {
                        foundCadanceSets.Add(cadanceSet);
                        ValidateCadanceSet(cadanceSet);
                    }
                }
                
                scanComplete = true;
                Debug.Log($"[Cadance Validation] Validated {foundCadanceSets.Count} CadanceSet assets");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[Cadance Validation] Error during validation: {ex.Message}");
            }
            finally
            {
                EditorUtility.ClearProgressBar();
            }
        }

        private void ValidateCadanceSet(CadanceSet cadanceSet)
        {
            var setResult = new CadanceSetValidationResult
            {
                cadanceSet = cadanceSet,
                setName = cadanceSet.name,
                totalEntries = cadanceSet.Count
            };
            
            foreach (var entry in cadanceSet.Cadances)
            {
                if (entry.HasValidFMODEvent)
                {
                    setResult.entriesWithFMOD++;
                    results.entriesWithFMODEvents++;
                }
                else
                {
                    setResult.entriesWithoutFMOD++;
                    setResult.entriesWithoutFMODEvents.Add(entry.DisplayName);
                    results.entriesWithoutFMODEvents++;
                }
                
                results.totalEntries++;
            }
            
            results.setResults.Add(setResult);
            results.totalSets++;
        }

        [System.Serializable]
        private class ValidationResults
        {
            public int totalSets = 0;
            public int totalEntries = 0;
            public int entriesWithFMODEvents = 0;
            public int entriesWithoutFMODEvents = 0;
            public List<CadanceSetValidationResult> setResults = new List<CadanceSetValidationResult>();
        }

        [System.Serializable]
        private class CadanceSetValidationResult
        {
            public CadanceSet cadanceSet;
            public string setName;
            public int totalEntries;
            public int entriesWithFMOD;
            public int entriesWithoutFMOD;
            public List<string> entriesWithoutFMODEvents = new List<string>();
        }
    }
}
