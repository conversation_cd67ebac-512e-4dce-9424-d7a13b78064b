# Retro Chrome Shader Implementation Guide
*Updated: February 2024*

## Overview
This document details the implementation of a retro chrome shader effect, featuring high-contrast metallic reflections with characteristic retro-style highlights and aging effects. The implementation focuses on recreating the classic chrome look with sharp edges, strong reflections, and vintage-style distortions.

## Key Visual Elements
1. Pure metallic surface with sharp reflections
2. Strong edge highlights and glare streaks
3. Subtle chromatic aberration
4. Aging effects through animated noise
5. HDR emission for bloom and glow

## 1. Base Material Properties
```
Core Settings:
- Metallic: 1.000 (pure metallic)
- Smoothness: 0.946 (nearly mirror-like)
- Base Color: Neutral metallic (0.8, 0.8, 0.8)
- Edge Sharpness: 10.0
- Reflection Intensity: 1.5
```

## 2. Environment Requirements
1. Dark environment setup:
   - Gradient background (deep blue to black)
   - Multiple area lights as thin strips
   - High-intensity, cool-toned lighting
   - HDR lighting for proper reflection handling

## 3. Material Features

### Reflection System:
1. Environment mapping with enhanced intensity
2. Sharp edge reflections using fresnel
3. Glare streak calculation
4. Edge highlighting system

### Aging Effects:
1. Time-based noise:
   - Moderate scale (50.0)
   - Low strength (0.15)
   - Slow animation rate
2. Chromatic aberration:
   - Subtle RGB splitting
   - Screen-space distortion

### Emission and Glow:
1. HDR emission color (default: 0, 0.5, 1.0)
2. Streak intensity control
3. Edge glow enhancement
4. Contrast enhancement for better definition

## 4. Shader Parameters

### Core Parameters:
```
- _MetallicStrength: Controls reflection metallic blend
- _Smoothness: Controls reflection sharpness
- _EdgeSharpness: Controls edge definition
- _ReflectionIntensity: Controls reflection strength
```

### Effect Parameters:
```
- _FresnelPower: Controls edge falloff
- _FresnelIntensity: Controls edge highlight strength
- _StreakIntensity: Controls light streak intensity
- _ChromaticAberration: Controls color separation
- _NoiseScale: Controls aging effect scale
- _NoiseStrength: Controls aging effect intensity
```

### Color and Emission:
```
- _BaseColor: Base material color
- _EmissionColor: HDR color for glows
- _GlowStrength: Overall emission intensity
- _GlowThreshold: Emission cutoff threshold
```

## 5. Post-Processing Requirements

### Bloom Setup:
1. Enable URP HDR
2. Bloom settings:
   - Threshold: 1.0
   - Intensity: 1.0-1.5
   - Scatter: 0.7
   - High Quality Filtering: Enabled

### Additional Effects:
1. Vignette (optional):
   - Intensity: 0.3
   - Smoothness: 0.2
2. Color Grading:
   - Slight contrast boost
   - Cool temperature bias

## 6. Performance Considerations
1. Environment reflection probe optimization:
   - Use box projection for accurate reflections
   - Update mode: On Awake
   - Resolution: 256x256 minimum
2. Shader feature optimization:
   - Adjust noise calculation frequency
   - Balance edge highlight quality
3. Post-process overhead:
   - Monitor bloom resolution
   - Adjust quality settings for performance

## 7. Common Issues & Solutions

### Visual Issues:
1. Too much reflection:
   - Reduce _ReflectionIntensity
   - Adjust environment lighting
2. Harsh edges:
   - Lower _EdgeSharpness
   - Adjust _FresnelPower
3. Weak retro effect:
   - Increase _ChromaticAberration
   - Enhance _NoiseStrength
   - Adjust bloom settings

### Performance Issues:
1. High GPU cost:
   - Reduce environment probe resolution
   - Lower noise calculation frequency
2. Bloom artifacts:
   - Adjust bloom threshold
   - Reduce _GlowStrength

## Technical Notes
- The shader heavily relies on precise edge detection and reflection handling
- Environment lighting plays a crucial role in achieving the desired look
- Post-processing bloom is essential for the retro effect
- Time-based effects create subtle animation in reflections

## References
Original implementation notes:
> The most important part with everything "retro" is usually the aging and distortion to bring back the textures of whatever old technology was used in the original. Bloom is usually your best friend to smudge the whole picture a bit and blend the pixels together.

![[Pasted image 20250210134025.png]]