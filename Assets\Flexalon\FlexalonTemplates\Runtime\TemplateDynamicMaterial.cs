using UnityEngine;
using UnityEngine.Rendering;

namespace Flexalon.Templates
{
    // Automatically selects the right material based on render pipeline and provides helpers for setting the color.
    [ExecuteAlways, AddComponentMenu("Flexalon Templates/Template Dynamic Material")]
    public class TemplateDynamicMaterial : MonoBehaviour
    {
        public Material Standard;
        public Material URP;
        public Material HDRP;

        [SerializeField]
        private Color _color = Color.white;
        public Color Color => _color;

        private MeshRenderer _meshRenderer;

        void OnEnable()
        {
            UpdateMeshRenderer();
            if (_meshRenderer)
            {
                if (GraphicsSettings.defaultRenderPipeline?.GetType().Name.Contains("HDRenderPipelineAsset") ?? false)
                {
                    _meshRenderer.sharedMaterial = HDRP;
                }
                else if (GraphicsSettings.defaultRenderPipeline?.GetType().Name.Contains("UniversalRenderPipelineAsset") ?? false)
                {
                    _meshRenderer.sharedMaterial = URP;
                }
                else
                {
                    _meshRenderer.sharedMaterial = Standard;
                }

                SetColor(_color);
            }
        }

        private string GetColorPropertyName()
        {
            if (_meshRenderer.sharedMaterial.HasProperty("_BaseColor")) // HRDP.Lit / URP.Lit
            {
                return "_BaseColor";
            }
            else if (_meshRenderer.sharedMaterial.HasProperty("_Color")) // Standard
            {
                return "_Color";
            }

            return null;
        }

        public void SetColor(Color color)
        {
            _color = color;
            UpdateMeshRenderer();
            if (_meshRenderer)
            {
                var propertyBlock = new MaterialPropertyBlock();
                propertyBlock.SetColor(GetColorPropertyName(), color);
                _meshRenderer.SetPropertyBlock(propertyBlock);
            }
        }

        private void UpdateMeshRenderer()
        {
            if (_meshRenderer == null)
            {
                _meshRenderer = GetComponent<MeshRenderer>();
            }
        }
    }
}