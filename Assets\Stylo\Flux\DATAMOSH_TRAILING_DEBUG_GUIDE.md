# Datamosh Trailing Debug Guide
## Step-by-Step Debugging for Missing Trailing Effects

---

## 🚨 **CRITICAL BUG FIXED**

**Hash Function Call Issue:**
- **Problem**: <PERSON>lux was using `hash1(uint(...))` instead of `hash1(...)`
- **Impact**: Different hash values than JPG Bitcrunch, preventing reprojection
- **Status**: ✅ FIXED - Removed explicit `uint()` cast to match JPG Bitcrunch exactly

---

## 🔍 **SYSTEMATIC DEBUGGING STEPS**

### **Step 1: Enable Debug Visualizations**

**In `Assets\Stylo\Flux\Shaders\Shared.cginc`, uncomment these debug lines:**

**A. Previous Frame Data Availability (Line 417):**
```hlsl
// Uncomment this line:
if (any(pull > 0.0)) return float4(1, 0, 1, 1); // Magenta = previous frame data available
```
**Expected Result**: Screen should be magenta if previous frame texture is working

**B. Reprojection Threshold Visualization (Line 420):**
```hlsl
// Uncomment these lines:
float debugThreshold = _ReprojectPercent + min(length(motionVector * float2(1920, 1080)) * _ReprojectLengthInfluence, 0.7);
if (debugThreshold > 0.1) return float4(0, 1, 0, 1); // Green = high reprojection chance
```
**Expected Result**: Moving areas should show green indicating high reprojection probability

**C. Actual Reprojection Occurrence (Line 540):**
```hlsl
// Uncomment this line:
return float4(1, 0, 0, 1); // Red = reprojection happening
```
**Expected Result**: Red blocks should appear where reprojection is occurring

### **Step 2: Verify Prerequisites**

**✅ Confirmed Working:**
- [x] Game View (not Scene View)
- [x] Play Mode active
- [x] Motion vectors exist
- [x] JPG Bitcrunch Exact preset loaded
- [x] Previous frame texture format fixed
- [x] Hash function call fixed

**🔍 Still Need to Verify:**
- [ ] Previous frame data actually contains valid image data
- [ ] Motion vector values are reasonable (not zero or extreme)
- [ ] Reprojection threshold calculation produces expected values
- [ ] Hash function produces values in expected range

### **Step 3: Parameter Verification**

**Current JPG Bitcrunch Exact Preset Values:**
```
_ReprojectPercent = 0.15        (15% base reprojection chance)
_ReprojectLengthInfluence = 1.2 (motion amplification)
_ReprojectSpeed = 1.0           (temporal variation speed)
```

**Manual Test Values (try these if preset doesn't work):**
```
_ReprojectPercent = 0.5         (50% base chance - should be very visible)
_ReprojectLengthInfluence = 0.0 (disable motion influence for pure random test)
_ReprojectSpeed = 0.0           (disable temporal variation for consistent test)
```

### **Step 4: Motion Vector Analysis**

**Enable Motion Vector Visualization:**
```hlsl
// In Flux shader, add this at the end of the function:
return 0.5 + float4(motionVector * 10.0, 0.0, 1.0);
```
**Expected Result**: 
- Gray = no motion
- Red/Green shifts = motion detected
- Extreme colors = motion vectors too large

### **Step 5: Hash Function Verification**

**Test Hash Function Output:**
```hlsl
// Add this debug code:
float hashValue = hash1((123 + blockID.x) * (456 + blockID.y) + (_Time.y * _ReprojectSpeed));
if (hashValue < 0.5) return float4(1, 1, 0, 1); // Yellow = hash < 0.5
```
**Expected Result**: About 50% of blocks should be yellow (random distribution)

---

## 🎯 **DEBUGGING SEQUENCE**

### **Test 1: Previous Frame Data**
1. Enable magenta debug visualization
2. **Expected**: Screen turns magenta after first frame
3. **If not magenta**: Previous frame texture issue

### **Test 2: Reprojection Threshold**
1. Enable green debug visualization  
2. Move camera or objects
3. **Expected**: Green areas where motion occurs
4. **If no green**: Motion vector or threshold calculation issue

### **Test 3: Hash Function**
1. Enable yellow debug visualization
2. **Expected**: Random yellow blocks (about 50%)
3. **If no yellow or wrong pattern**: Hash function issue

### **Test 4: Actual Reprojection**
1. Enable red debug visualization
2. **Expected**: Red blocks appearing randomly and with motion
3. **If no red**: Reprojection logic not triggering

### **Test 5: Final Trailing**
1. Disable all debug visualizations
2. Use strong test parameters (50% base chance)
3. **Expected**: Visible trailing/smearing effects

---

## 🚨 **COMMON ISSUES & SOLUTIONS**

### **Issue 1: No Magenta (Previous Frame Problem)**
**Symptoms**: Debug shows no magenta
**Causes**: 
- Previous frame texture not created
- Texture format mismatch
- Render Graph timing issue
**Solution**: Check console for texture creation logs

### **Issue 2: No Green (Motion/Threshold Problem)**
**Symptoms**: Debug shows no green during movement
**Causes**:
- Motion vectors are zero
- Threshold calculation incorrect
- Motion vector texture not bound
**Solution**: Check motion vector visualization

### **Issue 3: No Yellow (Hash Function Problem)**
**Symptoms**: Debug shows no yellow or wrong pattern
**Causes**:
- Hash function implementation error
- Block ID calculation wrong
- Time parameter not updating
**Solution**: Verify hash function matches JPG Bitcrunch

### **Issue 4: No Red (Reprojection Logic Problem)**
**Symptoms**: Debug shows no red blocks
**Causes**:
- Hash comparison logic error
- Threshold always too low/high
- Conditional logic error
**Solution**: Check threshold values and comparison

### **Issue 5: No Trailing (Final Integration Problem)**
**Symptoms**: All debug tests pass but no visible trailing
**Causes**:
- Previous frame sampling incorrect
- UV coordinate calculation wrong
- Color blending issue
**Solution**: Verify previous frame sampling logic

---

## 💡 **NEXT STEPS**

1. **Start with Test 1** (magenta debug)
2. **Work through tests sequentially** until you find the failing point
3. **Use the specific solutions** for each identified issue
4. **Report which test fails** for targeted debugging

The hash function fix should resolve the core issue, but these debug steps will help identify any remaining problems in the pipeline.

---

## 🔧 **EMERGENCY FALLBACK TEST**

If all else fails, try these **extreme test parameters**:
```
_ReprojectPercent = 1.0         (100% reprojection - everything should trail)
_ReprojectLengthInfluence = 0.0 (no motion dependency)
_ReprojectSpeed = 0.0           (no temporal variation)
```

With these settings, **every pixel should show trailing** if the system is working at all.
