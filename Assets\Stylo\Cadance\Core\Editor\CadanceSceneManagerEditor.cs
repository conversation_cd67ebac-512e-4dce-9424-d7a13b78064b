using UnityEngine;
using UnityEditor;
using Stylo.Cadance.FMOD;

namespace Stylo.Cadance.Editor
{
    /// <summary>
    /// Custom editor for CadanceSceneManager.
    /// </summary>
    [CustomEditor(typeof(CadanceSceneManager))]
    public class CadanceSceneManagerEditor : UnityEditor.Editor
    {
        // Serialized properties
        private SerializedProperty autoInitialize;
        private SerializedProperty persistAcrossSceneLoads;
        private SerializedProperty fmodManager;
        private SerializedProperty verboseLogging;
        
        // Editor state
        private bool showConfiguration = true;
        private bool showComponents = true;
        private bool showDebug = true;
        private bool showEvents = false;
        
        private void OnEnable()
        {
            // Get serialized properties
            autoInitialize = serializedObject.FindProperty("autoInitialize");
            persistAcrossSceneLoads = serializedObject.FindProperty("persistAcrossSceneLoads");
            fmodManager = serializedObject.FindProperty("fmodManager");
            verboseLogging = serializedObject.FindProperty("verboseLogging");
        }
        
        public override void OnInspectorGUI()
        {
            serializedObject.Update();
            
            CadanceSceneManager manager = (CadanceSceneManager)target;
            
            EditorGUILayout.Space();
            
            // Header
            EditorGUILayout.BeginHorizontal();
            GUILayout.FlexibleSpace();
            EditorGUILayout.LabelField("Cadance Scene Manager", EditorStyles.boldLabel);
            GUILayout.FlexibleSpace();
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.Space();
            
            // Configuration section
            showConfiguration = EditorGUILayout.Foldout(showConfiguration, "Configuration", true, EditorStyles.foldoutHeader);
            if (showConfiguration)
            {
                EditorGUI.indentLevel++;
                EditorGUILayout.PropertyField(autoInitialize);
                EditorGUILayout.PropertyField(persistAcrossSceneLoads);
                EditorGUI.indentLevel--;
            }
            
            EditorGUILayout.Space();
            
            // Components section
            showComponents = EditorGUILayout.Foldout(showComponents, "Components", true, EditorStyles.foldoutHeader);
            if (showComponents)
            {
                EditorGUI.indentLevel++;
                
                // FMOD Manager
                EditorGUILayout.PropertyField(fmodManager);
                
                if (fmodManager.objectReferenceValue == null)
                {
                    EditorGUILayout.BeginHorizontal();
                    EditorGUILayout.HelpBox("No FMOD Cadance Manager assigned. This is required for FMOD integration.", MessageType.Warning);
                    
                    if (GUILayout.Button("Add", GUILayout.Width(60)))
                    {
                        FMODCadanceManager existingManager = ((Component)target).GetComponent<FMODCadanceManager>();
                        if (existingManager == null)
                        {
                            existingManager = ((Component)target).gameObject.AddComponent<FMODCadanceManager>();
                        }
                        
                        fmodManager.objectReferenceValue = existingManager;
                    }
                    
                    EditorGUILayout.EndHorizontal();
                }
                
                EditorGUI.indentLevel--;
            }
            
            EditorGUILayout.Space();
            
            // Debug section
            showDebug = EditorGUILayout.Foldout(showDebug, "Debug", true, EditorStyles.foldoutHeader);
            if (showDebug)
            {
                EditorGUI.indentLevel++;
                EditorGUILayout.PropertyField(verboseLogging);
                EditorGUI.indentLevel--;
            }
            
            EditorGUILayout.Space();
            
            // Action buttons
            DrawActionButtons();
            
            // FMOD events section (if a manager is assigned)
            DrawFMODEventsSection();
            
            serializedObject.ApplyModifiedProperties();
        }
        
        private void DrawActionButtons()
        {
            CadanceSceneManager manager = (CadanceSceneManager)target;
            
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            EditorGUILayout.LabelField("Actions", EditorStyles.boldLabel);
            
            EditorGUILayout.BeginHorizontal();
            
            if (GUILayout.Button("Initialize Cadance", GUILayout.Height(30)))
            {
                manager.InitializeCadance();
                EditorUtility.SetDirty(target);
            }
            
            if (GUILayout.Button("Auto-Detect FMOD Events", GUILayout.Height(30)))
            {
                manager.AutoDetectFMODEvents();
                EditorUtility.SetDirty(target);
            }
            
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.EndVertical();
        }
        
        private void DrawFMODEventsSection()
        {
            CadanceSceneManager manager = (CadanceSceneManager)target;
            FMODCadanceManager fmodManager = manager.GetComponent<FMODCadanceManager>();
            
            if (fmodManager == null)
            {
                return;
            }
            
            EditorGUILayout.Space();
            
            // Events section
            showEvents = EditorGUILayout.Foldout(showEvents, "FMOD Events", true, EditorStyles.foldoutHeader);
            if (showEvents)
            {
                // Display registered events
                var events = fmodManager.GetRegisteredEvents();
                
                if (events.Count == 0)
                {
                    EditorGUILayout.HelpBox("No FMOD events registered.", MessageType.Info);
                }
                else
                {
                    EditorGUILayout.BeginVertical(EditorStyles.helpBox);
                    
                    // Header
                    EditorGUILayout.BeginHorizontal();
                    EditorGUILayout.LabelField("Event Name", EditorStyles.boldLabel, GUILayout.Width(150));
                    EditorGUILayout.LabelField("Event Path", EditorStyles.boldLabel, GUILayout.Width(200));
                    EditorGUILayout.LabelField("Status", EditorStyles.boldLabel, GUILayout.Width(100));
                    EditorGUILayout.LabelField("Actions", EditorStyles.boldLabel, GUILayout.Width(150));
                    EditorGUILayout.EndHorizontal();
                    
                    EditorGUILayout.Space(2);
                    
                    // Draw events
                    foreach (var evt in events)
                    {
                        EditorGUILayout.BeginHorizontal();
                        
                        // Name
                        EditorGUILayout.LabelField(evt.Key, GUILayout.Width(150));
                        
                        // Path
                        EditorGUILayout.LabelField(evt.Value, GUILayout.Width(200));
                        
                        // Status (checking if the event is currently playing)
                        bool isPlaying = false;
                        FMODEventInstanceTracker tracker = fmodManager.GetEventTracker(evt.Value);
                        if (tracker != null)
                        {
                            isPlaying = tracker.IsPlaying;
                        }
                        
                        EditorGUILayout.LabelField(isPlaying ? "Playing" : "Stopped", GUILayout.Width(100));
                        
                        // Actions (play/stop buttons)
                        if (isPlaying)
                        {
                            if (GUILayout.Button("Stop", GUILayout.Width(60)))
                            {
                                manager.StopFMODEvent(evt.Value);
                            }
                        }
                        else
                        {
                            if (GUILayout.Button("Play", GUILayout.Width(60)))
                            {
                                manager.PlayFMODEvent(evt.Value);
                            }
                        }
                        
                        if (GUILayout.Button("Unregister", GUILayout.Width(80)))
                        {
                            if (EditorUtility.DisplayDialog(
                                "Unregister Event",
                                $"Are you sure you want to unregister '{evt.Key}'?",
                                "Unregister",
                                "Cancel"))
                            {
                                fmodManager.UnregisterFMODEvent(evt.Value);
                                EditorUtility.SetDirty(fmodManager);
                            }
                        }
                        
                        EditorGUILayout.EndHorizontal();
                    }
                    
                    EditorGUILayout.EndVertical();
                }
                
                // Add new event section
                EditorGUILayout.Space();
                
                EditorGUILayout.BeginVertical(EditorStyles.helpBox);
                EditorGUILayout.LabelField("Register New Event", EditorStyles.boldLabel);
                
                EditorGUILayout.EndVertical();
            }
        }
    }
}
