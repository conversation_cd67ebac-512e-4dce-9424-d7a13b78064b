{
    "m_SGVersion": 2,
    "m_Type": "UnityEditor.ShaderGraph.GraphData",
    "m_ObjectId": "aa4c84d672dc4cc09dc1cd5335a929a9",
    "m_Properties": [
        {
            "m_Id": "bd08b0ed6b218e86a97f0e7ee9b63f08"
        },
        {
            "m_Id": "3c31925a8c6e7a84a326987201890472"
        },
        {
            "m_Id": "df86315a836c678183968500e32e8a9d"
        },
        {
            "m_Id": "f12a598a3be2a88194a58fc9189346bd"
        }
    ],
    "m_Keywords": [],
    "m_Nodes": [
        {
            "m_Id": "2433ef33b51be98e946b431d6954a428"
        },
        {
            "m_Id": "6d37c27fbc6cbb8286b7a7e2eeba570e"
        },
        {
            "m_Id": "3a6ad0b0d45eea8fa5e323d9de8d5ad3"
        },
        {
            "m_Id": "8fe758c84e8ae78791b4682d08b24bd1"
        },
        {
            "m_Id": "5cb840a387da5c8984d5a67bad132f44"
        },
        {
            "m_Id": "0d82b9c78cd56587a6875a045a1e4c57"
        },
        {
            "m_Id": "810afc4263833a8b8e9ec307107564e2"
        }
    ],
    "m_GroupDatas": [],
    "m_StickyNoteDatas": [],
    "m_Edges": [
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "0d82b9c78cd56587a6875a045a1e4c57"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "5cb840a387da5c8984d5a67bad132f44"
                },
                "m_SlotId": 5
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "3a6ad0b0d45eea8fa5e323d9de8d5ad3"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "5cb840a387da5c8984d5a67bad132f44"
                },
                "m_SlotId": 2
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "5cb840a387da5c8984d5a67bad132f44"
                },
                "m_SlotId": 4
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "2433ef33b51be98e946b431d6954a428"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "6d37c27fbc6cbb8286b7a7e2eeba570e"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "5cb840a387da5c8984d5a67bad132f44"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "810afc4263833a8b8e9ec307107564e2"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "5cb840a387da5c8984d5a67bad132f44"
                },
                "m_SlotId": 6
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "8fe758c84e8ae78791b4682d08b24bd1"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "5cb840a387da5c8984d5a67bad132f44"
                },
                "m_SlotId": 3
            }
        }
    ],
    "m_VertexContext": {
        "m_Position": {
            "x": 266.0,
            "y": -5.0
        },
        "m_Blocks": []
    },
    "m_FragmentContext": {
        "m_Position": {
            "x": 266.0,
            "y": 195.0
        },
        "m_Blocks": []
    },
    "m_PreviewData": {
        "serializedMesh": {
            "m_SerializedMesh": "{\"mesh\":{\"instanceID\":0}}",
            "m_Guid": ""
        }
    },
    "m_Path": "Amazing Assets/Wireframe Shader",
    "m_ConcretePrecision": 0,
    "m_PreviewMode": 2,
    "m_OutputNode": {
        "m_Id": "2433ef33b51be98e946b431d6954a428"
    },
    "m_ActiveTargets": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "0a5c063bf5a63c81b88542d5793aec49",
    "m_Id": 1,
    "m_DisplayName": "Mask Value",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "MaskValue",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": [
        "X"
    ]
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PropertyNode",
    "m_ObjectId": "0d82b9c78cd56587a6875a045a1e4c57",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Property",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -420.0,
            "y": -101.0,
            "width": 165.0,
            "height": 34.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "c621f4e26c48aa8aad98084e21855104"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Property": {
        "m_Id": "bd08b0ed6b218e86a97f0e7ee9b63f08"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "0e41ee201d918383ac0879b280c074b2",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [
        "X",
        "Y",
        "Z"
    ]
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "1303ab1d77c7ba8a803ed62419b45dc6",
    "m_Id": 0,
    "m_DisplayName": "Sphere Radius",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": [
        "X"
    ]
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "203188124dcace819fd2015cb29927e5",
    "m_Id": 2,
    "m_DisplayName": "edgeFalloff",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "edgeFalloff",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": [
        "X"
    ]
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SubGraphOutputNode",
    "m_ObjectId": "2433ef33b51be98e946b431d6954a428",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Out_Vector4",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 266.0,
            "y": -5.0,
            "width": 138.0,
            "height": 77.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "0a5c063bf5a63c81b88542d5793aec49"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "IsFirstSlotValid": true
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "29a8500db045ad89b1cb8f4ecfb04bdc",
    "m_Id": 3,
    "m_DisplayName": "invert",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "invert",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": [
        "X"
    ]
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PropertyNode",
    "m_ObjectId": "3a6ad0b0d45eea8fa5e323d9de8d5ad3",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Property",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -404.0,
            "y": 132.00001525878907,
            "width": 151.0,
            "height": 34.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "462bed1ddd40798b8f8b622894b9cd42"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Property": {
        "m_Id": "df86315a836c678183968500e32e8a9d"
    }
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.Internal.Vector1ShaderProperty",
    "m_ObjectId": "3c31925a8c6e7a84a326987201890472",
    "m_Guid": {
        "m_GuidSerialized": "5d58713a-22ef-4163-bed3-9737d2190f93"
    },
    "m_Name": "Sphere Radius",
    "m_DefaultReferenceName": "Vector1_DAD46621",
    "m_OverrideReferenceName": "",
    "m_GeneratePropertyBlock": true,
    "m_Precision": 0,
    "overrideHLSLDeclaration": false,
    "hlslDeclarationOverride": 0,
    "m_Hidden": false,
    "m_Value": 0.0,
    "m_FloatType": 0,
    "m_RangeValues": {
        "x": 0.0,
        "y": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "3c6b241e76673b87989fb8fff2daf2de",
    "m_Id": 0,
    "m_DisplayName": "Invert",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": [
        "X"
    ]
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "462bed1ddd40798b8f8b622894b9cd42",
    "m_Id": 0,
    "m_DisplayName": "EdgeSmooth",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": [
        "X"
    ]
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.CustomFunctionNode",
    "m_ObjectId": "5cb840a387da5c8984d5a67bad132f44",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "WireframeShaderMaskSphere (Custom Function)",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -121.0,
            "y": -4.0,
            "width": 266.0,
            "height": 166.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "9512839b130fa48a8921543aaee0cf0b"
        },
        {
            "m_Id": "af5367070bbc7f81b113fb7686bc0d4a"
        },
        {
            "m_Id": "e35a0dbbb2708b89826f13e21bdf4566"
        },
        {
            "m_Id": "5efe54e35f17a48286926a626634a149"
        },
        {
            "m_Id": "203188124dcace819fd2015cb29927e5"
        },
        {
            "m_Id": "29a8500db045ad89b1cb8f4ecfb04bdc"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SourceType": 0,
    "m_FunctionName": "WireframeShaderMaskSphere",
    "m_FunctionSource": "36072c560231e604cb8f0c03c7ff456d",
    "m_FunctionBody": "Enter function body here..."
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "5efe54e35f17a48286926a626634a149",
    "m_Id": 1,
    "m_DisplayName": "vertexPositionWS",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "vertexPositionWS",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [
        "X",
        "Y",
        "Z"
    ]
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.PositionNode",
    "m_ObjectId": "6d37c27fbc6cbb8286b7a7e2eeba570e",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Position",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -454.0,
            "y": -6.0,
            "width": 206.0,
            "height": 131.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "0e41ee201d918383ac0879b280c074b2"
        }
    ],
    "synonyms": [],
    "m_Precision": 1,
    "m_PreviewExpanded": false,
    "m_PreviewMode": 2,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Space": 4
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PropertyNode",
    "m_ObjectId": "810afc4263833a8b8e9ec307107564e2",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Property",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -413.0,
            "y": -54.0,
            "width": 159.0,
            "height": 34.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "1303ab1d77c7ba8a803ed62419b45dc6"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Property": {
        "m_Id": "3c31925a8c6e7a84a326987201890472"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PropertyNode",
    "m_ObjectId": "8fe758c84e8ae78791b4682d08b24bd1",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Property",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -376.0,
            "y": 176.00001525878907,
            "width": 112.0,
            "height": 34.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "3c6b241e76673b87989fb8fff2daf2de"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Property": {
        "m_Id": "f12a598a3be2a88194a58fc9189346bd"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "9512839b130fa48a8921543aaee0cf0b",
    "m_Id": 4,
    "m_DisplayName": "value",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "value",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": [
        "X"
    ]
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "af5367070bbc7f81b113fb7686bc0d4a",
    "m_Id": 5,
    "m_DisplayName": "spherePosition",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "spherePosition",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [
        "X",
        "Y",
        "Z"
    ]
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.Internal.Vector3ShaderProperty",
    "m_ObjectId": "bd08b0ed6b218e86a97f0e7ee9b63f08",
    "m_Guid": {
        "m_GuidSerialized": "5f8d4007-1d86-4288-aee7-257461504f3a"
    },
    "m_Name": "Sphere Position",
    "m_DefaultReferenceName": "Vector3_FF543593",
    "m_OverrideReferenceName": "",
    "m_GeneratePropertyBlock": true,
    "m_Precision": 0,
    "overrideHLSLDeclaration": false,
    "hlslDeclarationOverride": 0,
    "m_Hidden": false,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "c621f4e26c48aa8aad98084e21855104",
    "m_Id": 0,
    "m_DisplayName": "Sphere Position",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [
        "X",
        "Y",
        "Z"
    ]
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.Internal.Vector1ShaderProperty",
    "m_ObjectId": "df86315a836c678183968500e32e8a9d",
    "m_Guid": {
        "m_GuidSerialized": "4c1755af-d938-4686-8341-d1b75d1c2058"
    },
    "m_Name": "EdgeSmooth",
    "m_DefaultReferenceName": "Vector1_C99B3E15",
    "m_OverrideReferenceName": "",
    "m_GeneratePropertyBlock": true,
    "m_Precision": 0,
    "overrideHLSLDeclaration": false,
    "hlslDeclarationOverride": 0,
    "m_Hidden": false,
    "m_Value": 0.0,
    "m_FloatType": 0,
    "m_RangeValues": {
        "x": 0.0,
        "y": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "e35a0dbbb2708b89826f13e21bdf4566",
    "m_Id": 6,
    "m_DisplayName": "sphereRadius",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "sphereRadius",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": [
        "X"
    ]
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.Internal.Vector1ShaderProperty",
    "m_ObjectId": "f12a598a3be2a88194a58fc9189346bd",
    "m_Guid": {
        "m_GuidSerialized": "baf9261c-3c39-4cdb-bbc3-1502d4a4b343"
    },
    "m_Name": "Invert",
    "m_DefaultReferenceName": "Vector1_98EDB234",
    "m_OverrideReferenceName": "",
    "m_GeneratePropertyBlock": true,
    "m_Precision": 0,
    "overrideHLSLDeclaration": false,
    "hlslDeclarationOverride": 0,
    "m_Hidden": false,
    "m_Value": 0.0,
    "m_FloatType": 0,
    "m_RangeValues": {
        "x": 0.0,
        "y": 1.0
    }
}

