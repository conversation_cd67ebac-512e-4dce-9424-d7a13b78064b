fileFormatVersion: 2
guid: 46654d52b98074046baa66ee08f42c13
ModelImporter:
  serializedVersion: 15
  fileIDToRecycleName:
    100000: EyeAdvanced_LOD0
    100002: EyeAdvanced_LOD1
    100004: EyeAdvanced_LOD2
    100006: //RootNode
    400000: EyeAdvanced_LOD0
    400002: EyeAdvanced_LOD1
    400004: EyeAdvanced_LOD2
    400006: //RootNode
    2300000: EyeAdvanced_LOD0
    2300002: EyeAdvanced_LOD1
    2300004: EyeAdvanced_LOD2
    3300000: EyeAdvanced_LOD0
    3300002: EyeAdvanced_LOD1
    3300004: EyeAdvanced_LOD2
    4300000: EyeAdvanced_LOD0
    4300002: EyeAdvanced_LOD1
    4300004: EyeAdvanced_LOD2
    9500000: //RootNode
    20500000: //RootNode
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    optimizeGameObjects: 0
    animationCompression: 1
    animationRotationError: .5
    animationPositionError: .5
    animationScaleError: .5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages:
    - .25
    - .125
    - .00999999978
    globalScale: .00999999978
    meshCompression: 0
    addColliders: 0
    importBlendShapes: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    weldVertices: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
  tangentSpace:
    normalSmoothAngle: 60
    splitTangentsAcrossUV: 1
    normalImportMode: 0
    tangentImportMode: 1
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    human: []
    skeleton: []
    armTwist: .5
    foreArmTwist: .5
    upperLegTwist: .5
    legTwist: .5
    armStretch: .0500000007
    legStretch: .0500000007
    feetSpacing: 0
    rootMotionBoneName: 
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 0
  userData: 
