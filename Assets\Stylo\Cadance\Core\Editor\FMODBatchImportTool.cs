using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.Linq;

namespace Stylo.Cadance
{
    /// <summary>
    /// Batch import tool for importing FMOD audio files for entire CadanceSets.
    /// Provides comprehensive FMOD-to-Unity audio integration for timeline functionality.
    /// </summary>
    public class FMODBatchImportTool : EditorWindow
    {
        private Vector2 scrollPosition;
        private List<CadanceSet> availableSets = new List<CadanceSet>();
        private Dictionary<CadanceSet, bool> setSelections = new Dictionary<CadanceSet, bool>();
        private Dictionary<CadanceSet, ImportStatus> importStatuses = new Dictionary<CadanceSet, ImportStatus>();
        private bool showAdvancedOptions = false;
        private bool importOnlyMissing = true;
        private bool createBackups = true;

        private enum ImportStatus
        {
            NotStarted,
            InProgress,
            Completed,
            Failed,
            PartialSuccess
        }

        [MenuItem("Stylo/Cadance/Tools/FMOD Batch Import Tool")]
        public static void ShowWindow()
        {
            var window = GetWindow<FMODBatchImportTool>("FMOD Batch Import");
            window.minSize = new Vector2(500, 400);
            window.RefreshCadanceSets();
        }

        private void OnGUI()
        {
            EditorGUILayout.LabelField("FMOD Batch Import Tool", EditorStyles.boldLabel);
            EditorGUILayout.Space();

            EditorGUILayout.HelpBox(
                "This tool imports FMOD audio files as Unity AudioClips for timeline functionality.\n" +
                "• Searches FMOD project directories for matching audio files\n" +
                "• Imports files to Assets/Stylo/Cadance/ImportedAudio/\n" +
                "• Links AudioClips to CadanceAssets for waveform display",
                MessageType.Info);

            EditorGUILayout.Space();

            // Toolbar
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("🔄 Refresh Sets"))
            {
                RefreshCadanceSets();
            }

            if (GUILayout.Button("✅ Select All"))
            {
                SelectAllSets(true);
            }

            if (GUILayout.Button("❌ Select None"))
            {
                SelectAllSets(false);
            }

            if (GUILayout.Button("🎵 Import Selected"))
            {
                ImportSelectedSets();
            }
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.Space();

            // Advanced options
            showAdvancedOptions = EditorGUILayout.Foldout(showAdvancedOptions, "Advanced Options");
            if (showAdvancedOptions)
            {
                EditorGUI.indentLevel++;
                importOnlyMissing = EditorGUILayout.Toggle("Import Only Missing AudioClips", importOnlyMissing);
                createBackups = EditorGUILayout.Toggle("Create Asset Backups", createBackups);

                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button("🧹 Clear Import Cache"))
                {
                    FMODAudioBridge.ClearCache();
                    Debug.Log("[FMOD Batch Import] Cache cleared");
                }

                if (GUILayout.Button("📊 Show Available FMOD Files"))
                {
                    ShowAvailableFMODFiles();
                }
                EditorGUILayout.EndHorizontal();
                EditorGUI.indentLevel--;
            }

            EditorGUILayout.Space();

            // CadanceSet list
            EditorGUILayout.LabelField("Available CadanceSets", EditorStyles.boldLabel);

            if (availableSets.Count == 0)
            {
                EditorGUILayout.HelpBox("No CadanceSets found in project. Create some CadanceSets first.", MessageType.Warning);
                return;
            }

            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);

            foreach (var cadanceSet in availableSets)
            {
                DrawCadanceSetEntry(cadanceSet);
            }

            EditorGUILayout.EndScrollView();
        }

        private void DrawCadanceSetEntry(CadanceSet cadanceSet)
        {
            EditorGUILayout.BeginVertical("box");

            // Header with selection checkbox
            EditorGUILayout.BeginHorizontal();

            bool isSelected = setSelections.ContainsKey(cadanceSet) && setSelections[cadanceSet];
            bool newSelection = EditorGUILayout.Toggle(isSelected, GUILayout.Width(20));
            setSelections[cadanceSet] = newSelection;

            EditorGUILayout.LabelField(cadanceSet.name, EditorStyles.boldLabel);

            // Status indicator
            if (importStatuses.ContainsKey(cadanceSet))
            {
                string statusText = GetStatusText(importStatuses[cadanceSet]);
                EditorGUILayout.LabelField(statusText, GUILayout.Width(100));
            }

            EditorGUILayout.EndHorizontal();

            // Set details
            EditorGUI.indentLevel++;
            EditorGUILayout.LabelField($"Assets: {cadanceSet.Count}");

            // Count missing AudioClips
            int missingCount = CountMissingAudioClips(cadanceSet);
            if (missingCount > 0)
            {
                EditorGUILayout.LabelField($"Missing AudioClips: {missingCount}", EditorStyles.miniLabel);
            }
            else
            {
                EditorGUILayout.LabelField("All AudioClips present ✓", EditorStyles.miniLabel);
            }

            // Individual import button
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button($"🎵 Import This Set", GUILayout.Width(120)))
            {
                ImportCadanceSet(cadanceSet);
            }

            if (GUILayout.Button($"📋 Show Details", GUILayout.Width(100)))
            {
                ShowSetDetails(cadanceSet);
            }
            EditorGUILayout.EndHorizontal();

            EditorGUI.indentLevel--;
            EditorGUILayout.EndVertical();
        }

        private void RefreshCadanceSets()
        {
            availableSets.Clear();
            setSelections.Clear();
            importStatuses.Clear();

            string[] guids = AssetDatabase.FindAssets("t:CadanceSet");
            foreach (string guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                var cadanceSet = AssetDatabase.LoadAssetAtPath<CadanceSet>(path);
                if (cadanceSet != null)
                {
                    availableSets.Add(cadanceSet);
                    setSelections[cadanceSet] = false;
                    importStatuses[cadanceSet] = ImportStatus.NotStarted;
                }
            }

            Debug.Log($"[FMOD Batch Import] Found {availableSets.Count} CadanceSets");
        }

        private void SelectAllSets(bool selected)
        {
            foreach (var set in availableSets)
            {
                setSelections[set] = selected;
            }
        }

        private void ImportSelectedSets()
        {
            var selectedSets = availableSets.Where(set => setSelections.ContainsKey(set) && setSelections[set]).ToList();

            if (selectedSets.Count == 0)
            {
                EditorUtility.DisplayDialog("No Selection", "Please select at least one CadanceSet to import.", "OK");
                return;
            }

            int totalImported = 0;
            foreach (var set in selectedSets)
            {
                int imported = ImportCadanceSet(set);
                totalImported += imported;
            }

            EditorUtility.DisplayDialog("Import Complete",
                $"Successfully imported {totalImported} audio files from {selectedSets.Count} CadanceSets.", "OK");
        }

        private int ImportCadanceSet(CadanceSet cadanceSet)
        {
            if (cadanceSet == null) return 0;

            importStatuses[cadanceSet] = ImportStatus.InProgress;
            Repaint();

            try
            {
                int imported = FMODAudioBridge.BatchImportForCadanceSet(cadanceSet);

                if (imported > 0)
                {
                    importStatuses[cadanceSet] = ImportStatus.Completed;
                }
                else
                {
                    importStatuses[cadanceSet] = ImportStatus.Failed;
                }

                Repaint();
                return imported;
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[FMOD Batch Import] Error importing CadanceSet '{cadanceSet.name}': {ex.Message}");
                importStatuses[cadanceSet] = ImportStatus.Failed;
                Repaint();
                return 0;
            }
        }

        private int CountMissingAudioClips(CadanceSet cadanceSet)
        {
            if (cadanceSet == null) return 0;

            int missing = 0;
            foreach (var entry in cadanceSet.Cadances)
            {
                if (entry.cadance != null && entry.cadance.SourceClip == null && !string.IsNullOrEmpty(entry.cadance.SourceClipName))
                {
                    missing++;
                }
            }
            return missing;
        }

        private string GetStatusText(ImportStatus status)
        {
            switch (status)
            {
                case ImportStatus.NotStarted: return "⏳ Ready";
                case ImportStatus.InProgress: return "🔄 Importing...";
                case ImportStatus.Completed: return "✅ Complete";
                case ImportStatus.Failed: return "❌ Failed";
                case ImportStatus.PartialSuccess: return "⚠ Partial";
                default: return "❓ Unknown";
            }
        }

        private void ShowSetDetails(CadanceSet cadanceSet)
        {
            string details = $"CadanceSet: {cadanceSet.name}\n\n";
            details += $"Total Assets: {cadanceSet.Count}\n";
            details += $"Missing AudioClips: {CountMissingAudioClips(cadanceSet)}\n\n";

            details += "Assets:\n";
            foreach (var entry in cadanceSet.Cadances)
            {
                if (entry.cadance != null)
                {
                    string status = entry.cadance.SourceClip != null ? "✓" : "❌";
                    details += $"  {status} {entry.cadance.name} ({entry.cadance.SourceClipName})\n";
                }
            }

            EditorUtility.DisplayDialog($"CadanceSet Details", details, "OK");
        }

        private void ShowAvailableFMODFiles()
        {
            var fmodFiles = FMODAudioBridge.GetAvailableFMODAudioFiles();
            string fileList = $"Available FMOD Audio Files ({fmodFiles.Count}):\n\n";

            foreach (string file in fmodFiles.Take(50)) // Limit display to first 50
            {
                fileList += $"• {file}\n";
            }

            if (fmodFiles.Count > 50)
            {
                fileList += $"\n... and {fmodFiles.Count - 50} more files";
            }

            EditorUtility.DisplayDialog("Available FMOD Files", fileList, "OK");
        }
    }
}
