# BTR Task Completion Checklist

## Before Committing Code Changes

### 1. Code Quality Checks
- [ ] **No compilation errors** in Unity Console
- [ ] **No runtime errors** during Play Mode testing
- [ ] **Follow BTR naming conventions** and namespace structure
- [ ] **Add XML documentation** for public APIs
- [ ] **Remove debug/temporary code** and comments

### 2. Testing Requirements
- [ ] **Test in Play Mode** with relevant scenes (especially Ouroboros - Base)
- [ ] **Verify system integration** with other components
- [ ] **Check performance impact** using Unity Profiler if needed
- [ ] **Test input systems** if UI/controls are affected
- [ ] **Validate audio systems** if FMOD/Koreographer integration is involved

### 3. Documentation Updates
- [ ] **Update relevant README files** if system behavior changes
- [ ] **Update architecture diagrams** if system structure changes
- [ ] **Add usage examples** for new public APIs
- [ ] **Update migration guides** if breaking changes are introduced

### 4. Scene and Asset Validation
- [ ] **Save all modified scenes** and prefabs
- [ ] **Check for missing references** in Inspector
- [ ] **Validate ScriptableObject configurations** if modified
- [ ] **Test with different render pipeline settings** if graphics are affected

### 5. Version Control
- [ ] **Stage only relevant files** (avoid staging temp files)
- [ ] **Write descriptive commit message** following project conventions
- [ ] **Include issue/task references** if applicable
- [ ] **Consider creating backup** for major system changes

## System-Specific Checks

### For Stylo Framework Changes
- [ ] **Test module isolation** - ensure no unwanted dependencies
- [ ] **Verify assembly definitions** are correct
- [ ] **Check editor/runtime separation** is maintained

### For Audio System Changes
- [ ] **Test FMOD integration** in Play Mode
- [ ] **Verify rhythm synchronization** with Koreographer/Cadance
- [ ] **Check audio pooling** performance

### For UI Changes (MenUI)
- [ ] **Test in both Editor and Play Mode**
- [ ] **Verify controller navigation** works
- [ ] **Check responsive design** on different screen sizes
- [ ] **Test pause/resume functionality**

### For Performance-Critical Changes
- [ ] **Profile before and after** changes
- [ ] **Check memory allocations** in hot paths
- [ ] **Verify Job System** integration if applicable
- [ ] **Test with realistic scene complexity**