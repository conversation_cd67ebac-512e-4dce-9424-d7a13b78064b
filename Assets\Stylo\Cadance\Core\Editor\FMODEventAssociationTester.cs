using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using SonicBloom.Koreo;
using SonicBloom.Koreo.Players.FMODStudio;
using Stylo.Cadance;

namespace Stylo.Cadance.Editor
{
    /// <summary>
    /// Test utility to validate FMOD event association discovery in the enhanced conversion tool.
    /// </summary>
    public class FMODEventAssociationTester : EditorWindow
    {
        private FMODKoreographySet testSet;
        private Vector2 scrollPosition;
        private Dictionary<FMODKoreographySet, FMODUnity.EventReference> foundAssociations = new Dictionary<FMODKoreographySet, FMODUnity.EventReference>();
        private bool testComplete = false;

        [MenuItem("Stylo/Cadance/Testing/FMOD Event Association Tester")]
        public static void ShowWindow()
        {
            var window = GetWindow<FMODEventAssociationTester>("FMOD Event Association Tester");
            window.minSize = new Vector2(500, 300);
            window.Show();
        }

        private void OnGUI()
        {
            EditorGUILayout.LabelField("FMOD Event Association Tester", EditorStyles.boldLabel);
            EditorGUILayout.Space();

            EditorGUILayout.HelpBox(
                "This tool tests the enhanced conversion tool's ability to find FMOD event associations for FMODKoreographySet assets.",
                MessageType.Info);

            EditorGUILayout.Space();

            // Test set selection
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("Test Configuration", EditorStyles.boldLabel);

            testSet = (FMODKoreographySet)EditorGUILayout.ObjectField("FMODKoreographySet to Test", testSet, typeof(FMODKoreographySet), false);

            EditorGUI.BeginDisabledGroup(testSet == null);
            if (GUILayout.Button("Test FMOD Event Association Discovery", GUILayout.Height(30)))
            {
                TestEventAssociationDiscovery();
            }
            EditorGUI.EndDisabledGroup();

            EditorGUILayout.EndVertical();

            // Results
            if (testComplete)
            {
                EditorGUILayout.Space();
                DrawTestResults();
            }
        }

        private void TestEventAssociationDiscovery()
        {
            if (testSet == null)
            {
                Debug.LogError("[FMOD Test] No FMODKoreographySet selected for testing");
                return;
            }

            foundAssociations.Clear();
            testComplete = false;

            try
            {
                EditorUtility.DisplayProgressBar("Testing FMOD Event Association", "Discovering associations...", 0.5f);

                Debug.Log($"[FMOD Test] Testing FMOD event association discovery for: {testSet.name}");

                // Use the same logic as the enhanced conversion tool
                var associations = FindFMODEventAssociationsForSet(testSet);
                foundAssociations = associations;

                testComplete = true;

                if (associations.Count > 0)
                {
                    Debug.Log($"[FMOD Test] SUCCESS: Found {associations.Count} FMOD event association(s)");
                    foreach (var kvp in associations)
                    {
                        Debug.Log($"[FMOD Test] Association: '{kvp.Value.Path}' -> '{kvp.Key.name}'");
                    }
                }
                else
                {
                    Debug.LogWarning($"[FMOD Test] No FMOD event associations found for '{testSet.name}'");
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[FMOD Test] Error during testing: {ex.Message}");
            }
            finally
            {
                EditorUtility.ClearProgressBar();
            }
        }

        private void DrawTestResults()
        {
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("Test Results", EditorStyles.boldLabel);

            if (foundAssociations.Count == 0)
            {
                EditorGUILayout.HelpBox("No FMOD event associations found. This could mean:\n" +
                    "• No FMODEventDescriptionVisor components reference this set\n" +
                    "• The set is not used in any scenes or prefabs\n" +
                    "• FMOD event names don't match naming conventions", MessageType.Warning);
            }
            else
            {
                EditorGUILayout.HelpBox($"Found {foundAssociations.Count} FMOD event association(s)!", MessageType.Info);

                scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition, GUILayout.Height(150));

                foreach (var kvp in foundAssociations)
                {
                    EditorGUILayout.BeginVertical("box");
                    EditorGUILayout.LabelField($"Set: {kvp.Key.name}", EditorStyles.boldLabel);
                    EditorGUILayout.LabelField($"FMOD Event Path: {kvp.Value.Path}");
                    EditorGUILayout.LabelField($"FMOD Event GUID: {kvp.Value.Guid}");
                    EditorGUILayout.EndVertical();
                }

                EditorGUILayout.EndScrollView();
            }

            EditorGUILayout.EndVertical();
        }

        // Copy of the enhanced conversion tool's logic for testing
        private Dictionary<FMODKoreographySet, FMODUnity.EventReference> FindFMODEventAssociationsForSet(FMODKoreographySet targetSet)
        {
            var associations = new Dictionary<FMODKoreographySet, FMODUnity.EventReference>();

            try
            {
                // Method 1: Find all FMODEventDescriptionVisor components in scenes and prefabs
                FindEventAssociationsInScenes(targetSet, associations);

                // Method 2: Find all FMODEventDescriptionVisor components in prefabs
                FindEventAssociationsInPrefabs(targetSet, associations);

                // Method 3: Try to find associations based on naming conventions
                if (associations.Count == 0)
                {
                    FindEventAssociationsByNaming(targetSet, associations);
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"[FMOD Test] Error finding FMOD event associations: {ex.Message}");
            }

            return associations;
        }

        private void FindEventAssociationsInScenes(FMODKoreographySet targetSet, Dictionary<FMODKoreographySet, FMODUnity.EventReference> associations)
        {
            try
            {
                string[] sceneGuids = AssetDatabase.FindAssets("t:Scene");

                foreach (string sceneGuid in sceneGuids)
                {
                    string scenePath = AssetDatabase.GUIDToAssetPath(sceneGuid);
                    string sceneContent = System.IO.File.ReadAllText(scenePath);

                    if (sceneContent.Contains(targetSet.name) || sceneContent.Contains(AssetDatabase.AssetPathToGUID(AssetDatabase.GetAssetPath(targetSet))))
                    {
                        Debug.Log($"[FMOD Test] Found potential reference to '{targetSet.name}' in scene: {scenePath}");
                        ExtractFMODEventFromSceneContent(sceneContent, targetSet, associations);
                    }
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"[FMOD Test] Error scanning scenes: {ex.Message}");
            }
        }

        private void FindEventAssociationsInPrefabs(FMODKoreographySet targetSet, Dictionary<FMODKoreographySet, FMODUnity.EventReference> associations)
        {
            try
            {
                string[] prefabGuids = AssetDatabase.FindAssets("t:Prefab");

                foreach (string prefabGuid in prefabGuids)
                {
                    string prefabPath = AssetDatabase.GUIDToAssetPath(prefabGuid);
                    string prefabContent = System.IO.File.ReadAllText(prefabPath);

                    if (prefabContent.Contains(targetSet.name) || prefabContent.Contains(AssetDatabase.AssetPathToGUID(AssetDatabase.GetAssetPath(targetSet))))
                    {
                        Debug.Log($"[FMOD Test] Found potential reference to '{targetSet.name}' in prefab: {prefabPath}");
                        ExtractFMODEventFromSceneContent(prefabContent, targetSet, associations);
                    }
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"[FMOD Test] Error scanning prefabs: {ex.Message}");
            }
        }

        private void ExtractFMODEventFromSceneContent(string content, FMODKoreographySet targetSet, Dictionary<FMODKoreographySet, FMODUnity.EventReference> associations)
        {
            try
            {
                var lines = content.Split('\n');
                string currentEventPath = null;

                for (int i = 0; i < lines.Length; i++)
                {
                    string line = lines[i].Trim();

                    if (line.StartsWith("Path: event:/"))
                    {
                        currentEventPath = line.Substring("Path: ".Length).Trim();
                    }

                    if (line.Contains(targetSet.name) || line.Contains(AssetDatabase.AssetPathToGUID(AssetDatabase.GetAssetPath(targetSet))))
                    {
                        if (!string.IsNullOrEmpty(currentEventPath))
                        {
                            var eventRef = CreateProperEventReference(currentEventPath);
                            if (!eventRef.IsNull)
                            {
                                associations[targetSet] = eventRef;
                                Debug.Log($"[FMOD Test] Found FMOD event association: '{currentEventPath}' -> '{targetSet.name}'");
                                return;
                            }
                        }
                    }
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"[FMOD Test] Error extracting FMOD event from content: {ex.Message}");
            }
        }

        private void FindEventAssociationsByNaming(FMODKoreographySet targetSet, Dictionary<FMODKoreographySet, FMODUnity.EventReference> associations)
        {
            try
            {
                string[] eventGuids = AssetDatabase.FindAssets("t:FMODEvent");

                foreach (string eventGuid in eventGuids)
                {
                    string eventPath = AssetDatabase.GUIDToAssetPath(eventGuid);
                    string eventName = System.IO.Path.GetFileNameWithoutExtension(eventPath);

                    if (DoesEventMatchSet(eventName, targetSet))
                    {
                        var eventRef = CreateProperEventReference($"event:/{eventName}");
                        if (!eventRef.IsNull)
                        {
                            associations[targetSet] = eventRef;
                            Debug.Log($"[FMOD Test] Found FMOD event by naming convention: '{eventRef.Path}' -> '{targetSet.name}'");
                            return;
                        }
                    }
                }

                Debug.LogWarning($"[FMOD Test] No FMOD event associations found for '{targetSet.name}' - naming convention matching failed");
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"[FMOD Test] Error finding FMOD event associations by naming: {ex.Message}");
            }
        }

        private bool DoesEventMatchSet(string eventName, FMODKoreographySet targetSet)
        {
            if (eventName.ToLower().Contains(targetSet.name.ToLower()))
                return true;

            foreach (var entry in targetSet.koreographies)
            {
                if (entry.koreo != null && !string.IsNullOrEmpty(entry.koreo.SourceClipName))
                {
                    if (eventName.ToLower().Contains(entry.koreo.SourceClipName.ToLower()) ||
                        entry.koreo.SourceClipName.ToLower().Contains(eventName.ToLower()))
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        /// <summary>
        /// Creates a proper FMOD EventReference with both GUID and Path populated.
        /// </summary>
        private FMODUnity.EventReference CreateProperEventReference(string eventPath)
        {
            try
            {
                // Method 1: Use EventManager.EventFromPath (Editor only)
#if UNITY_EDITOR
                var editorEventRef = FMODUnity.EventManager.EventFromPath(eventPath);
                if (editorEventRef != null)
                {
                    return new FMODUnity.EventReference
                    {
                        Path = editorEventRef.Path,
                        Guid = editorEventRef.Guid
                    };
                }
#endif

                // Method 2: Use EventReference.Find (requires GuidLookupDelegate)
                try
                {
                    if (FMODUnity.EventReference.GuidLookupDelegate != null)
                    {
                        return FMODUnity.EventReference.Find(eventPath);
                    }
                }
                catch (System.Exception ex)
                {
                    Debug.LogWarning($"[FMOD Test] EventReference.Find failed for '{eventPath}': {ex.Message}");
                }

                // Method 3: Use RuntimeManager.PathToEventReference (fallback)
                try
                {
                    return FMODUnity.RuntimeManager.PathToEventReference(eventPath);
                }
                catch (System.Exception ex)
                {
                    Debug.LogWarning($"[FMOD Test] RuntimeManager.PathToEventReference failed for '{eventPath}': {ex.Message}");
                }

                Debug.LogWarning($"[FMOD Test] Could not create proper EventReference for path: '{eventPath}' - all methods failed");
                return new FMODUnity.EventReference(); // Return empty/null reference
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[FMOD Test] Error creating EventReference for '{eventPath}': {ex.Message}");
                return new FMODUnity.EventReference();
            }
        }
    }
}
