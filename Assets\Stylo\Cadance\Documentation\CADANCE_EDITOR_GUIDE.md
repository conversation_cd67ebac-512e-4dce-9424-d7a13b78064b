# Cadance Editor Guide

## Overview

The Cadance Editor provides comprehensive timeline editing capabilities for CadanceAsset files, offering complete feature parity with <PERSON><PERSON><PERSON>'s editor. This guide covers all functionality and usage instructions.

## Key Features

### ✅ **Complete Feature Parity with <PERSON><PERSON>ographer**

The Cadance Editor implements all essential features from <PERSON><PERSON><PERSON>'s editor:

- **Asset Opening**: Double-click CadanceAsset files to open them directly in the editor
- **Timeline Editing**: Full timeline with waveform display, ruler, and grid snapping
- **Track Management**: Create, edit, move, and delete tracks with full event support
- **Event Manipulation**: Create, edit, drag, and delete events with precision timing
- **Audio Integration**: Real-time playback, scrubbing, and waveform visualization
- **Professional Tools**: Undo/redo, snap-to-grid, zoom controls, and keyboard shortcuts

## Opening CadanceAsset Files

### Method 1: Double-Click (Recommended)
1. Navigate to your CadanceAsset in the Project window
2. Double-click the asset file
3. The Cadance Editor will open automatically with the asset loaded

### Method 2: Inspector <PERSON>
1. Select a CadanceAsset in the Project window
2. In the Inspector, click "Open in Cadance Editor"
3. The editor will open with the asset loaded

### Method 3: Menu Access
1. Go to `Stylo > Cadance > Cadance Editor`
2. Use the "Open" button in the toolbar to select an asset
3. Or drag a CadanceAsset into the asset field

## Editor Interface

### Toolbar
- **File Operations**: New, Open, Save buttons for asset management
- **Undo/Redo**: Full undo/redo support for all editing operations
- **Playback Controls**: Play, pause, stop, and seek controls
- **View Options**: Waveform toggle, snap-to-grid, zoom controls
- **Analysis Tools**: Access to audio analysis features

### Timeline
- **Ruler**: Time markers with major/minor ticks based on zoom level
- **Waveform Display**: Visual representation of audio data (toggleable)
- **Grid Lines**: Snap-to-grid visualization when enabled
- **Playback Position**: Red line showing current playback position

### Track Area
- **Track Headers**: Event ID, selection, and track controls
- **Track Timelines**: Visual representation of events on each track
- **Event Visualization**: Color-coded events with payload indicators
- **Track Management**: Add, remove, reorder tracks

### Inspector Panel
- **Event Inspector**: Details for selected events (time, sample, payload)
- **Track Inspector**: Information about selected tracks
- **Asset Inspector**: Overall asset information and statistics

## Working with Tracks

### Creating Tracks
1. Click "Add Track" button in the track area
2. Choose from predefined track types:
   - Basic Track
   - Beat Track
   - Melody Track
   - Effect Track
3. Or use the menu: Right-click in track area > Add Track

### Managing Tracks
- **Rename**: Edit the Event ID field in the track header
- **Reorder**: Use ↑/↓ buttons to move tracks up/down
- **Delete**: Click × button (with confirmation dialog)
- **Select**: Click the checkbox to select/deselect tracks

## Working with Events

### Creating Events
- **Double-Click**: Double-click on a track timeline to create an event
- **Add Button**: Select a track and click "Add Event"
- **Playhead**: Use "Add Event at Playhead" when a track is selected
- **Context Menu**: Right-click on track > Add Event Here

### Editing Events
- **Select**: Click on an event to select it
- **Drag**: Click and drag events to move them in time
- **Delete**: Select an event and press Delete key
- **Inspect**: Select an event to view details in the inspector

### Event Types
- **One-Off Events**: Displayed as vertical lines (instantaneous)
- **Span Events**: Displayed as rectangles (duration-based)
- **Payload Events**: Events with data payloads (white indicator)

### Event Colors
Events are color-coded by payload type:
- **Yellow**: No payload
- **Cyan**: IntPayload
- **Green**: FloatPayload
- **Magenta**: TextPayload
- **Blue**: CurvePayload
- **Orange**: GradientPayload
- **Red**: AssetPayload

## Audio Features

### Playback Controls
- **Spacebar**: Play/pause toggle
- **Play Button**: Start playback from current position
- **Stop Button**: Stop playback and return to start
- **Seek**: Click on timeline to jump to specific time

### Waveform Display
- **Toggle**: Use "Waveform" button in toolbar
- **Visualization**: Real-time waveform rendering
- **Performance**: Optimized for smooth editing experience

### Audio Scrubbing
- **Timeline Interaction**: Click and drag on timeline for audio scrubbing
- **Real-time Feedback**: Hear audio while scrubbing through timeline

## Keyboard Shortcuts

- **Spacebar**: Play/pause audio
- **Delete**: Delete selected event
- **Ctrl+Z**: Undo last operation
- **Ctrl+Shift+Z**: Redo last undone operation

## Grid and Snapping

### Snap to Grid
- **Enable**: Toggle "Snap" button in toolbar
- **Grid Size**: Adjust grid size value (in seconds)
- **Visual**: Grid lines displayed when enabled
- **Behavior**: Events snap to grid when creating or moving

### Zoom Controls
- **Slider**: Use zoom slider in toolbar
- **Range**: 0.1x to 10x zoom levels
- **Timeline**: Affects timeline resolution and detail level

## Audio Clip Discovery

### Automatic Discovery
- **Missing AudioClip**: Shows warning when SourceClip is missing
- **Discovery Button**: Click 🔍 button to auto-discover AudioClip
- **Matching Logic**: Searches by exact name and partial matching
- **Manual Assignment**: Drag AudioClip to SourceClip field if auto-discovery fails

## Validation and Testing

### Editor Validation Tool
Access the validation tool at: `Stylo > Cadance > Testing > Editor Validation Tool`

**Features:**
- **Full Validation**: Comprehensive test of all editor features
- **Asset Opening Test**: Verify double-click functionality works
- **Feature Testing**: Test individual components and capabilities
- **Results Display**: Detailed pass/fail results with explanations

### Common Issues and Solutions

**Issue**: CadanceAsset doesn't open when double-clicked
- **Solution**: Ensure the OnOpenAsset handler is properly implemented
- **Validation**: Run the Editor Validation Tool to check

**Issue**: Waveform not displaying
- **Solution**: Ensure AudioClip is assigned to the CadanceAsset
- **Tool**: Use the AudioClip Discovery feature (🔍 button)

**Issue**: Events not visible on timeline
- **Solution**: Check zoom level and timeline offset
- **Tip**: Use zoom controls to adjust view

## Migration from Koreographer

### Converted Assets
- **Automatic Opening**: Converted CadanceAssets open seamlessly
- **Data Preservation**: All original event data and timing preserved
- **FMOD Integration**: FMOD event associations maintained
- **Workflow**: Identical editing experience to Koreographer

### Feature Comparison
| Feature | Koreographer | Cadance Editor | Status |
|---------|-------------|----------------|---------|
| Asset Opening | ✅ | ✅ | ✅ Complete |
| Timeline Editing | ✅ | ✅ | ✅ Complete |
| Waveform Display | ✅ | ✅ | ✅ Complete |
| Event Management | ✅ | ✅ | ✅ Complete |
| Audio Playback | ✅ | ✅ | ✅ Complete |
| Undo/Redo | ✅ | ✅ | ✅ Complete |
| Track Management | ✅ | ✅ | ✅ Complete |
| Grid Snapping | ✅ | ✅ | ✅ Complete |
| Keyboard Shortcuts | ✅ | ✅ | ✅ Complete |

## Best Practices

### Performance
- **Large Files**: Use zoom controls for better performance with large audio files
- **Waveform**: Disable waveform display for very long audio clips if needed
- **Events**: Organize events across multiple tracks for better visualization

### Workflow
- **Save Frequently**: Use Ctrl+S or Save button to save changes
- **Undo Safety**: Use undo/redo for safe experimentation
- **Track Organization**: Use descriptive Event IDs for track identification
- **Grid Alignment**: Enable snap-to-grid for precise event placement

### Asset Management
- **AudioClip Assignment**: Always ensure AudioClip is properly assigned
- **File Organization**: Keep CadanceAssets and AudioClips in organized folders
- **Backup**: Maintain backups of important CadanceAssets before major edits

## Troubleshooting

### Editor Won't Open
1. Check console for error messages
2. Run the Editor Validation Tool
3. Verify CadanceAsset is not corrupted
4. Try creating a new CadanceAsset to test

### Performance Issues
1. Reduce zoom level for large audio files
2. Disable waveform display temporarily
3. Close other Unity windows to free memory
4. Check audio file format and size

### Audio Issues
1. Verify AudioClip is assigned and valid
2. Check audio import settings in Unity
3. Test with different audio files
4. Ensure audio drivers are working

## Support and Validation

The Cadance Editor includes comprehensive validation tools to ensure proper functionality. Use the Editor Validation Tool regularly to verify that all features are working correctly and to identify any potential issues early.

For additional support or to report issues, use the validation tools to gather detailed information about the editor's state and functionality.
