%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -2076007791, guid: aa81bbec85a3f423193272a7c6154986, type: 3}
  m_Name: 172 Flux Drums - Scene 1 - 1
  m_EditorClassIdentifier: 
  mSourceClip: {fileID: 0}
  mAudioFilePath: ../FMOD Studio Projects/Beat Traveller Reload/Assets/GS_FLUX_172_Drum_Loop_01_V2.wav
  mSampleRate: 44100
  mIgnoreLatencyOffset: 0
  mTempoSections:
  - sectionName: New Section
    startSample: 0
    samplesPerBeat: 15383.************
    beatsPerMeasure: 4
    bStartNewMeasure: 1
  mTracks:
  - {fileID: 11400000, guid: 940c03172c7bdc84ead60115cd7fa429, type: 2}
  - {fileID: 11400000, guid: 0363969e5cd1a1d4c8ba145cde780360, type: 2}
  - {fileID: 11400000, guid: 84c6caadee0004a43886b6f0d6d5322a, type: 2}
  - {fileID: 11400000, guid: d501cf7e81d278041a9f23a7e59069d8, type: 2}
