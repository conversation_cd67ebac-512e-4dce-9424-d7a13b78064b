﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Namespace Flexalon
 | Flexalon 3D Layouts </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Namespace Flexalon
 | Flexalon 3D Layouts ">
    <meta name="generator" content="docfx ">
  
    <link rel="shortcut icon" href="images/favicon.svg">
    <link rel="stylesheet" href="styles/docfx.vendor.css">
    <link rel="stylesheet" href="styles/docfx.css">
    <link rel="stylesheet" href="styles/main.css">
    <meta property="docfx:navrel" content="tochead.html">
    <meta property="docfx:tocrel" content="toc.html">
  
  <meta property="docfx:rel" content="">
  
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="https://www.flexalon.com/">
                <img id="logo" class="svg" src="images/logo_32x32.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first="First" data-prev="Previous" data-next="Next" data-last="Last"></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">

        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="Flexalon">

  <h1 id="Flexalon" data-uid="Flexalon" class="text-break">Namespace Flexalon
</h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="markdown level0 remarks"></div>
    <h3 id="classes">Classes
</h3>
      <h4><a class="xref" href="Flexalon.Flex.html">Flex</a></h4>
      <section></section>
      <h4><a class="xref" href="Flexalon.Flexalon.html">Flexalon</a></h4>
      <section><p>Singleton class which tracks and updates all FlexalonNodes in the scene.
See <a href="..\../docs/coreConcepts.html">core concepts</a> for more information.</p>
</section>
      <h4><a class="xref" href="Flexalon.FlexalonAlignLayout.html">FlexalonAlignLayout</a></h4>
      <section><p>Use a align layout to align all children to the parent on the specified axes.
For example, use a align layout to place all children along a floor, wall, or edge.</p>
<p>Once aligned, you can adjust the position, rotation, or size of each child by
editing the Offset, Rotation, Size, and Scale properties on that child's Flexalon Object Component.</p>
</section>
      <h4><a class="xref" href="Flexalon.FlexalonCircleLayout.html">FlexalonCircleLayout</a></h4>
      <section><p>Use a circle layout to position children along a circle or spiral.</p>
</section>
      <h4><a class="xref" href="Flexalon.FlexalonCloner.html">FlexalonCloner</a></h4>
      <section><p>Sometimes, it's useful to generate child objects instead of defining them statically.
The Flexalon Cloner can generate objects from a set of prefabs iteratively or randomly,
and can optionally bind to a data source.</p>
</section>
      <h4><a class="xref" href="Flexalon.FlexalonComponent.html">FlexalonComponent</a></h4>
      <section><p>Base type for many Flexalon components. Deals with FlexalonNode lifecycle,
and provides the ForceUpdate and MarkDirty methods to trigger a Flexalon update.</p>
</section>
      <h4><a class="xref" href="Flexalon.FlexalonConstraint.html">FlexalonConstraint</a></h4>
      <section><p>The Flexalon Constraint component positions its gameObject relative to a target,
which can be any other gameObject. The constrained object or the target can also
have layout components, which provides a powerful way to combine layouts together.</p>
</section>
      <h4><a class="xref" href="Flexalon.FlexalonCurveAnimator.html">FlexalonCurveAnimator</a></h4>
      <section><p>The curve animator applies a curve the the position, rotation, and scale
of the object. The curve is restarted each time the layout position changes.
This is ideal for scenarios in which the layout position does not change often.</p>
</section>
      <h4><a class="xref" href="Flexalon.FlexalonCurveLayout.html">FlexalonCurveLayout</a></h4>
      <section><p>Use a curve layout to position children along a bézier curve.</p>
</section>
      <h4><a class="xref" href="Flexalon.FlexalonDragTarget.html">FlexalonDragTarget</a></h4>
      <section><p>A drag target allows a layout to accept  dragged FlexalonInteractable objects.</p>
</section>
      <h4><a class="xref" href="Flexalon.FlexalonFlexibleLayout.html">FlexalonFlexibleLayout</a></h4>
      <section><p>Use a flexible layout to position children linearly along the x, y, or z axis.
The sizes of the children are considered so that they are evenly spaced.</p>
</section>
      <h4><a class="xref" href="Flexalon.FlexalonGridCell.html">FlexalonGridCell</a></h4>
      <section><p>Specifies which cell a gameObject should occupy in a grid layout.</p>
</section>
      <h4><a class="xref" href="Flexalon.FlexalonGridLayout.html">FlexalonGridLayout</a></h4>
      <section><p>Use a grid layout to position children at fixed intervals.
Objects are placed in cells in column-row-layer order.</p>
</section>
      <h4><a class="xref" href="Flexalon.FlexalonInteractable.html">FlexalonInteractable</a></h4>
      <section><p>Allows a gameObject to be clicked and dragged.</p>
</section>
      <h4><a class="xref" href="Flexalon.FlexalonInteractable.InteractableEvent.html">FlexalonInteractable.InteractableEvent</a></h4>
      <section><p>An event that occurs to a FlexalonInteractable.</p>
</section>
      <h4><a class="xref" href="Flexalon.FlexalonLerpAnimator.html">FlexalonLerpAnimator</a></h4>
      <section><p>The lerp animator constnatly performs a linear interpolation between
the object's current position and its layout position. This is useful
if the layout position is continuously changing.</p>
</section>
      <h4><a class="xref" href="Flexalon.FlexalonMouseInputProvider.html">FlexalonMouseInputProvider</a></h4>
      <section><p>Simple input provider that uses the mouse for input.</p>
</section>
      <h4><a class="xref" href="Flexalon.FlexalonObject.html">FlexalonObject</a></h4>
      <section><p>To control the size of an object, add a Flexalon Object
component to it and edit the width, height, or depth properties.</p>
</section>
      <h4><a class="xref" href="Flexalon.FlexalonRandomLayout.html">FlexalonRandomLayout</a></h4>
      <section><p>Use a random layout to position, rotate, and size children randomly within bounds.</p>
</section>
      <h4><a class="xref" href="Flexalon.FlexalonRandomModifier.html">FlexalonRandomModifier</a></h4>
      <section><p>The Flexalon Random Modifier component can be added to any layout
to randomly modify the positions and rotations of the children.</p>
</section>
      <h4><a class="xref" href="Flexalon.FlexalonResult.html">FlexalonResult</a></h4>
      <section><p>This component is added to each object in a layout. It stores the results of the layout process
so they can be loaded from a scene/prefab without rerunning layout.</p>
</section>
      <h4><a class="xref" href="Flexalon.FlexalonRigidBodyAnimator.html">FlexalonRigidBodyAnimator</a></h4>
      <section><p>If you add a Rigid Body or Rigid Body 2D component a gameObject which is managed by Flexalon, then
the physics system will fight with Flexalon over the object's position and rotation.
Adding a Rigid Body animator will resolve this by applying forces to the the rigid body component
instead of changing the transform directly.</p>
</section>
      <h4><a class="xref" href="Flexalon.FlexalonShapeLayout.html">FlexalonShapeLayout</a></h4>
      <section><p>Use a shape layout to position children in a shape formation with a specified number of sides.
The first child is placed in the center, and subsequent children are placed in concentric layers
around the center, with each layer forming the desired shape.</p>
</section>
      <h4><a class="xref" href="Flexalon.FlexItem.html">FlexItem</a></h4>
      <section></section>
      <h4><a class="xref" href="Flexalon.LayoutBase.html">LayoutBase</a></h4>
      <section><p>Base class for all layout componets. See <a href="..\../docs/customLayout.html">custom layout</a> for details
on how to extend this class. Assigns the Layout method to FlexalonNode and keeps the
node's children up to date.</p>
</section>
      <h4><a class="xref" href="Flexalon.Math.html">Math</a></h4>
      <section><p>Common math help functions.</p>
</section>
    <h3 id="structs">Structs
</h3>
      <h4><a class="xref" href="Flexalon.Directions.html">Directions</a></h4>
      <section><p>Six floats representing right, left, top, bottom, back, front.</p>
</section>
      <h4><a class="xref" href="Flexalon.FlexalonCurveLayout.CurvePoint.html">FlexalonCurveLayout.CurvePoint</a></h4>
      <section><p>A point on the curve.</p>
</section>
    <h3 id="interfaces">Interfaces
</h3>
      <h4><a class="xref" href="Flexalon.Adapter.html">Adapter</a></h4>
      <section><p>Adapters determine how Flexalon measures other Unity components.
See <a href="..\../docs/adapters.html">adapters</a> documentation.</p>
</section>
      <h4><a class="xref" href="Flexalon.Constraint.html">Constraint</a></h4>
      <section><p>A constraint runs whenever a target layout is updated.</p>
</section>
      <h4><a class="xref" href="Flexalon.DataBinding.html">DataBinding</a></h4>
      <section><p>When the Cloner creates objects from a DataSource, it will search the cloned objects
for any component which implements DataBinding to bind the data entry from the
data source to the visual item. The component can then use this data to change its appearance.</p>
</section>
      <h4><a class="xref" href="Flexalon.DataSource.html">DataSource</a></h4>
      <section><p>Provides data for a FlexalonCloner.</p>
</section>
      <h4><a class="xref" href="Flexalon.FlexalonModifier.html">FlexalonModifier</a></h4>
      <section><p>Interface for components that modify layout results.</p>
</section>
      <h4><a class="xref" href="Flexalon.FlexalonNode.html">FlexalonNode</a></h4>
      <section><p>Represents a node in the Flexalon layout tree.</p>
</section>
      <h4><a class="xref" href="Flexalon.InputProvider.html">InputProvider</a></h4>
      <section><p>Implement this interface and assign it to the Flexalon.InputProvider
to override how FlexalonInteractables receive input.</p>
</section>
      <h4><a class="xref" href="Flexalon.Layout.html">Layout</a></h4>
      <section><p>A layout determines how the children of a node are positioned.</p>
</section>
      <h4><a class="xref" href="Flexalon.TransformUpdater.html">TransformUpdater</a></h4>
      <section><p>A transform updater determines how an object
gets from its current position to the computed layout position.</p>
</section>
    <h3 id="enums">Enums
</h3>
      <h4><a class="xref" href="Flexalon.Align.html">Align</a></h4>
      <section><p>Represents a direction to align.</p>
</section>
      <h4><a class="xref" href="Flexalon.Axis.html">Axis</a></h4>
      <section><p>Represents an axis.</p>
</section>
      <h4><a class="xref" href="Flexalon.Direction.html">Direction</a></h4>
      <section><p>Represents an axis and direction.</p>
</section>
      <h4><a class="xref" href="Flexalon.FlexalonCircleLayout.InitialRadiusOptions.html">FlexalonCircleLayout.InitialRadiusOptions</a></h4>
      <section><p>Determines the initial radius of the circle.</p>
</section>
      <h4><a class="xref" href="Flexalon.FlexalonCircleLayout.RadiusOptions.html">FlexalonCircleLayout.RadiusOptions</a></h4>
      <section><p>Determines if and how the radius changes.</p>
</section>
      <h4><a class="xref" href="Flexalon.FlexalonCircleLayout.RotateOptions.html">FlexalonCircleLayout.RotateOptions</a></h4>
      <section><p>Determines how children should be rotated.</p>
</section>
      <h4><a class="xref" href="Flexalon.FlexalonCircleLayout.SpacingOptions.html">FlexalonCircleLayout.SpacingOptions</a></h4>
      <section><p>Determines how the space between children is distributed.</p>
</section>
      <h4><a class="xref" href="Flexalon.FlexalonCloner.CloneTypes.html">FlexalonCloner.CloneTypes</a></h4>
      <section><p>In which order should prefabs be cloned.</p>
</section>
      <h4><a class="xref" href="Flexalon.FlexalonCurveLayout.ExtendBehavior.html">FlexalonCurveLayout.ExtendBehavior</a></h4>
      <section><p>Determines how the curve is extended before the beginning and after the end.</p>
</section>
      <h4><a class="xref" href="Flexalon.FlexalonCurveLayout.RotationOptions.html">FlexalonCurveLayout.RotationOptions</a></h4>
      <section><p>Determines how children should be rotated</p>
</section>
      <h4><a class="xref" href="Flexalon.FlexalonCurveLayout.SpacingOptions.html">FlexalonCurveLayout.SpacingOptions</a></h4>
      <section><p>Determines how the children will be spaced along the curve.</p>
</section>
      <h4><a class="xref" href="Flexalon.FlexalonCurveLayout.TangentMode.html">FlexalonCurveLayout.TangentMode</a></h4>
      <section><p>Determines how the tangent for a CurvePoint is determined.</p>
</section>
      <h4><a class="xref" href="Flexalon.FlexalonFlexibleLayout.GapOptions.html">FlexalonFlexibleLayout.GapOptions</a></h4>
      <section><p>Determines how the space between children is distributed.</p>
</section>
      <h4><a class="xref" href="Flexalon.FlexalonGridLayout.CellSizeTypes.html">FlexalonGridLayout.CellSizeTypes</a></h4>
      <section><p>How to determine the size of the cell.</p>
</section>
      <h4><a class="xref" href="Flexalon.FlexalonGridLayout.CellTypes.html">FlexalonGridLayout.CellTypes</a></h4>
      <section><p>The type of cell to use on the column-row axes.</p>
</section>
      <h4><a class="xref" href="Flexalon.FlexalonInteractable.InteractableState.html">FlexalonInteractable.InteractableState</a></h4>
      <section><p>The current state of the interactable.</p>
</section>
      <h4><a class="xref" href="Flexalon.FlexalonInteractable.RestrictionType.html">FlexalonInteractable.RestrictionType</a></h4>
      <section><p>Restricts the movement of an object during a drag.</p>
</section>
      <h4><a class="xref" href="Flexalon.InputMode.html">InputMode</a></h4>
      <section></section>
      <h4><a class="xref" href="Flexalon.MinMaxSizeType.html">MinMaxSizeType</a></h4>
      <section><p>Determines how a FlexalonObject min or max should be determined.</p>
</section>
      <h4><a class="xref" href="Flexalon.Plane.html">Plane</a></h4>
      <section><p>Represents a plane along two axes.</p>
</section>
      <h4><a class="xref" href="Flexalon.SizeType.html">SizeType</a></h4>
      <section><p>Determines how a FlexalonObject should be sized.</p>
</section>
</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In This Article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      Flexalon 3D Layouts
      
          </div>
        </div>
      </footer>
    </div>

    
    
    
  </body>
</html>
