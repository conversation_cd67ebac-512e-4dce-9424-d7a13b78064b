# BTR Key Systems Overview

## Core Game Systems

### 1. Scene Management (SceneManagerBTR)
- **Location**: `Assets/_Scripts/Management/SceneManagerBTR.cs`
- **Purpose**: Handles scene loading, transitions, and wave management
- **Key Features**: Async scene loading, music synchronization, performance monitoring
- **Integration**: Works with AudioManager, WaveEventSubscriptions, LoadingScreen

### 2. Projectile System
- **Location**: `Assets/_Scripts/Projectiles/`
- **Architecture**: Component-based with ProjectileEntity, ProjectileCore, ProjectileInteraction
- **Key Features**: Advanced pooling, Job System optimization, unified interface
- **Performance**: Burst-compiled jobs for high-performance projectile processing

### 3. Enemy System
- **Location**: `Assets/_Scripts/EnemySystem/`
- **Architecture**: Strategy pattern with EnemyCore, behavior components
- **Key Features**: AI pathfinding, combat behaviors, state management
- **Integration**: A* Pathfinding Project, projectile system integration

### 4. Player System
- **Location**: `Assets/_Scripts/Player/`
- **Components**: PlayerMovement, PlayerShooting, PlayerLocking, CrosshairCore
- **Key Features**: Rhythm-based movement, time control abilities, combat mechanics
- **Integration**: Input System, Chronos time manipulation

## Stylo Framework Modules

### 1. Epoch (Time Management)
- **Location**: `Assets/Stylo/Epoch/`
- **Purpose**: Advanced time control and chronos integration
- **Key Features**: Global time scaling, pause systems, time manipulation effects
- **Integration**: Replaces/enhances Unity's Time system

### 2. MenUI (Menu System)
- **Location**: `Assets/Stylo/MenUI/`
- **Technology**: UI Toolkit (UXML/USS)
- **Key Features**: Pause menus, settings, controller navigation, cyberpunk theming
- **Integration**: Input System, Epoch time pausing

### 3. Flux (Visual Effects)
- **Location**: `Assets/Stylo/Flux/`
- **Purpose**: Datamoshing effects and visual distortions
- **Key Features**: JPG bitcrunching recreation, motion vector effects, Render Graph integration
- **Integration**: URP Render Graph, camera motion sensitivity

### 4. Cadance (Audio/Rhythm)
- **Location**: `Assets/Stylo/Cadance/`
- **Purpose**: Replacing Koreographer with modern rhythm system
- **Key Features**: FMOD integration, beat detection, rhythm synchronization
- **Status**: In development, migrating from Koreographer

## Audio Systems

### 1. FMOD Integration
- **Location**: `Assets/_Scripts/FMOD/`
- **Purpose**: Advanced audio management and spatial audio
- **Key Features**: 3D audio, dynamic mixing, performance optimization
- **Integration**: Koreographer rhythm sync, custom pooling

### 2. AudioManager
- **Location**: `Assets/_Scripts/Management/AudioManager.cs`
- **Purpose**: Centralized audio control and pooling
- **Key Features**: Audio pooling, performance monitoring, FMOD integration
- **Performance**: Phase 4 optimizations with detailed statistics

## Management Layer

### 1. GameManager
- **Purpose**: Central game state management
- **Integration**: Coordinates all major systems
- **Key Features**: Game state, player references, system initialization

### 2. TimeManager
- **Purpose**: Time system coordination
- **Integration**: Works with Stylo.Epoch for advanced time control
- **Key Features**: Global time scaling, pause management