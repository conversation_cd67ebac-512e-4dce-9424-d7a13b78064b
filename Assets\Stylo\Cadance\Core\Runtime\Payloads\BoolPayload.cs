using System;
using UnityEngine;

namespace Stylo.Cadance
{
    /// <summary>
    /// Boolean payload for Cadance events.
    /// Compatible with Koreographer's BoolPayload.
    /// </summary>
    [Serializable]
    public class BoolPayload : IPayload
    {
        [SerializeField] private bool boolValue;
        
        /// <summary>
        /// Gets or sets the boolean value.
        /// </summary>
        public bool BoolValue
        {
            get => boolValue;
            set => boolValue = value;
        }
        
        /// <summary>
        /// Creates a new BoolPayload with the specified value.
        /// </summary>
        /// <param name="value">The boolean value</param>
        public BoolPayload(bool value = false)
        {
            boolValue = value;
        }
        
        /// <summary>
        /// Returns a copy of this payload.
        /// </summary>
        /// <returns>A copy of the BoolPayload</returns>
        public IPayload GetCopy()
        {
            return new BoolPayload(boolValue);
        }
        
        /// <summary>
        /// Gets the display width for editor visualization.
        /// </summary>
        /// <returns>The display width in pixels</returns>
        public float GetDisplayWidth()
        {
            return 60f;
        }
        
        /// <summary>
        /// Returns the boolean value as a string.
        /// </summary>
        /// <returns>The boolean value as a string</returns>
        public override string ToString()
        {
            return boolValue.ToString();
        }
        
        /// <summary>
        /// Implicit conversion from bool to BoolPayload.
        /// </summary>
        /// <param name="value">The boolean value</param>
        /// <returns>A new BoolPayload</returns>
        public static implicit operator BoolPayload(bool value)
        {
            return new BoolPayload(value);
        }
        
        /// <summary>
        /// Implicit conversion from BoolPayload to bool.
        /// </summary>
        /// <param name="payload">The BoolPayload</param>
        /// <returns>The boolean value</returns>
        public static implicit operator bool(BoolPayload payload)
        {
            return payload?.boolValue ?? false;
        }
    }
}
