%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b340139c9e4d054f904d8b452798652, type: 3}
  m_Name: ES3Defaults
  m_EditorClassIdentifier: 
  settings:
    _location: 0
    path: SaveFile.es3
    encryptionType: 0
    compressionType: 0
    encryptionPassword: password
    directory: 0
    format: 0
    prettyPrint: 1
    bufferSize: 2048
    saveChildren: 1
    postprocessRawCachedData: 0
    typeChecking: 1
    safeReflection: 1
    memberReferenceMode: 0
    referenceMode: 2
    serializationDepthLimit: 64
    assemblyNames:
    - AD_FimpAnimating
    - AD_FimpOther
    - AD_FimpShared
    - AD_FimpSharedTools
    - AllIn13DShaderAssemebly
    - AllIn13DShaderDemoScriptsAssembly
    - AmazingAssets.AdvancedDissolve
    - AmazingAssets.AdvancedDissolve.Examples
    - andywiecko.BurstTriangulator
    - Assembly-CSharp
    - Assembly-CSharp-firstpass
    - AstarPathfindingProject
    - AstarPathfindingProjectExamples
    - Autodesk.Fbx
    - Autodesk.Fbx.BuildTestAssets
    - Beans.Unity.Collections
    - Beans.Unity.Mathematics
    - CW.Common
    - Deform
    - Domain_Reload
    - DOTween.Modules
    - DOTweenPro.Scripts
    - Drawing
    - Dreamteck.Splines
    - Dreamteck.Utilities
    - Duotone.Runtime
    - ExternalAttributes.Core
    - Febucci.Attributes.Runtime
    - Febucci.TextAnimator.Demo.Runtime
    - Febucci.TextAnimator.Runtime
    - Febucci.TextAnimator.TMP.Runtime
    - Flexalon
    - Flexalon Templates
    - Flexalon.Samples
    - FluffyUnderware.DevTools
    - FMODUnity
    - FMODUnityResonance
    - GUPS.EasyPerformanceMonitor
    - GUPS.EasyPerformanceMonitor.Demo
    - JPG.Runtime
    - JPG.Universal.Runtime
    - KinoEight.Runtime
    - Klak.UIToolkit.Runtime
    - LibTessDotNet
    - Lofelt.NiceVibrations
    - Lofelt.NiceVibrations.Demo
    - Micosmo.SensorToolkit
    - MoreMountains.Tools
    - OccaSoftware.Altos.Demo
    - OccaSoftware.Altos.Runtime
    - OccaSoftware.Altos.ShaderLibrary
    - OccaSoftware.Altos.Shaders
    - OccaSoftware.AutoExposure.Runtime
    - OccaSoftware.Bloom.Runtime
    - OccaSoftware.Bloom.Shaders
    - OccaSoftware.Buto.Demo
    - OccaSoftware.Buto.Runtime
    - OccaSoftware.Buto.Shaders
    - OccaSoftware.Exposure.Demo
    - OccaSoftware.Exposure.Shaders
    - OccaSoftware.HazeFX.Runtime
    - OccaSoftware.LSPP.Demo
    - OccaSoftware.LSPP.Runtime
    - OccaSoftware.LSPP.Shaders
    - OccaSoftware.OutlineObjects.Demo
    - OccaSoftware.OutlineObjects.Runtime
    - OccaSoftware.RadialBlur.Demo
    - OccaSoftware.RadialBlur.Runtime
    - OccaSoftware.RadialBlur.Shaders
    - OccaSoftware.ToonKit2.Runtime
    - OccaSoftware.ToonKit2.Shaders
    - OccaSoftware.VFXLibrary.Demo
    - OrderIndependentTransparency
    - OrderIndependentTransparency.PostProcessingStackV2
    - OrderIndependentTransparency.URP
    - PackageTools
    - PlanarReflections5
    - PrimeTween.Debug
    - PrimeTween.Demo
    - PrimeTween.Runtime
    - PrimeTween.Samples
    - ProjectDawn.Impostor
    - ProjectDawn.Impostor.HighDefinition
    - ProjectDawn.Impostor.Universal
    - ShapesRuntime
    - ShapesSamples
    - SpaceGraphicsToolkit
    - SpaceGraphicsToolkit.Galaxy
    - SpaceGraphicsToolkit.Nebula
    - SpaceGraphicsToolkit.Terrain
    - Stylo.BFI.Universal.Runtime
    - Stylo.Epoch
    - Stylo.Flux.Shared
    - Stylo.Flux.Universal.Runtime
    - Test
    - ToolBuddy.ArraysPooling
    - ToolBuddy.Curvy
    - ToolBuddy.Curvy.Examples
    - ToolBuddy.Dependencies.VectorGraphics
    - UniTask
    - UniTask.Addressables
    - UniTask.DOTween
    - UniTask.Linq
    - UniTask.TextMeshPro
    - Unity.Cinemachine.Samples
    - Unity.InputSystem.RebindingUI
    - Unity.RenderPipelines.Core.Samples.Runtime
    - VFXGraph.OutputEventHandlers
    - Wingman
    - ZLogger.Unity
    showAdvancedSettings: 0
  addMgrToSceneAutomatically: 0
  autoUpdateReferences: 1
  addAllPrefabsToManager: 1
  collectDependenciesDepth: 4
  collectDependenciesTimeout: 10
  updateReferencesWhenSceneChanges: 1
  updateReferencesWhenSceneIsSaved: 1
  updateReferencesWhenSceneIsOpened: 1
  referenceFolders: []
  logDebugInfo: 0
  logWarnings: 1
  logErrors: 1
