---
description: 
globs: 
alwaysApply: true
---

# Your rule content

- You can @ files here
- You can use markdown but dont have to

We are on a windows 11 computer building a Untiy 6 Render Graph URP Project. Follow convetions and API according to Unity 6. We are using FMOD for our audio, <PERSON><PERSON>og<PERSON>aher for syncing events and that audio, and we are often looking to make optimizations such as Burst and Jobs. Other important assets to the project are Chronos for timing and time control, Curvy Spline, Ultimate Spawner and it's Waves Add On, Buto, VolFx, Reach UI, and others.

All custom scripts should be in the 'Assets/_Scripts' directory. Scriptable objects should be in the 'Assets/_Script Objects' directory. 

Unity 6 URP Render Graph: Development Rules & AI Collaboration Guide

Note: All project documentation resides within the "BTR Docs" Obsidian vault located in the project root directory. References like [[Note Name]] point to specific notes within this vault.

Core Philosophy

    Simplicity: Prioritize simple, clear, and maintainable solutions. Avoid unnecessary complexity.

    Iterate: Prefer iterating on existing, working code/assets/configurations.

    Focus: Concentrate efforts on the assigned task. Avoid scope creep.

    Quality: Strive for clean, organized, well-commented, performant, and stable code/assets.

    Collaboration: This document guides humans and AI, referencing "BTR Docs".

    Clarity & Precision: Provide clear, technical responses with precise C# and Unity examples.

    Leverage Unity: Use Unity’s built-in features, tools, and best practices first.

    Modularity: Structure the project in a modular way using Unity's component architecture and established patterns (see Architecture Standards).

Project Context & Understanding

    Documentation First (in "BTR Docs"):

        Always review relevant documentation in the "BTR Docs" vault before starting. Key notes:

            GDDs / TDDs

            [[Project Overview]] or [[README]] (Setup, C# standards, Unity version, key packages)

            [[Architecture]] (System architecture, Assembly Defs, components, scenes, Render Graph overview, System Managers, Behavior Patterns, State Management - see Architecture Standards section below)

            [[Technical Patterns]] (C# patterns, SO usage, URP/Render Graph techniques, shader conventions, performance patterns like Object Pooling, Job System usage)

            [[Current Tasks]] (Or linked tool)

            Notes on specific third-party assets like [[AmplifyShaderPack Usage]], [[Quibli Usage]], [[Reach UI Guide]] (#11)

            [[Fixes]] folder/tag for past bug solutions (#4 Debugging)

            System Guides (e.g., [[Enemy System Guide]]) (#15 Docs)

            Unity Manual, URP/Render Graph Docs, Scripting API (Linked externally)

        If docs are missing/unclear, ask for clarification.

    Architecture Adherence:

        Respect boundaries, data flow, interfaces defined in [[Architecture]].

        Follow established System Architecture Standards (#13).

        Understand and adhere to the project's Render Graph Structure documented within [[Architecture]] or related notes.

        Validate changes against architecture; warn if violations detected.

    Pattern & Tech Stack Awareness:

        Reference [[Project Overview]], [[Technical Patterns]], and specific asset notes ([[AmplifyShaderPack Usage]], etc.) in "BTR Docs".

        Utilize existing patterns (C#, URP, Render Graph, Performance) before proposing new ones.

        Be aware of which URP Asset is active ([[URP Configuration]] note?) and if multiple are used (#4 URP).

        Know which Input System is canonical ([[Input System Guide]] note?) (#5).

Task Execution & Workflow

    Task Definition: Understand requirements from [[Current Tasks]] and GDD/TDD notes.

    Systematic Change Protocol: Before significant changes: Identify impact, Plan steps, Verify testing.

    Progress Tracking: Update [[Development Status]] or [[Current Tasks]]. Reference "BTR Docs" notes.

AI Collaboration & Prompting

    Clarity & Context: Provide clear, specific instructions. Define outcome, constraints, and reference relevant code/assets or "BTR Docs" notes (e.g., [[Specific System Design]], [[Render Feature X Implementation Notes]]). Specify which Input System (#5) or architectural pattern (#13) to use.

    Context Referencing: Remind AI of previous context or specific "BTR Docs" notes.

    Suggest vs. Apply: Clearly state intent ("Suggestion:" vs. "Applying fix:").

    Question AI Output: Humans must critically review AI code/configs. Verify logic, performance, URP best practices (check [[Technical Patterns]]), architecture adherence ([[Architecture]]), and naming/style.

    Focus the AI: Guide AI to specific parts. Reference "BTR Docs" notes if applicable (e.g., "Refactor the shader in [[Custom Water Shader]]").

    Leverage Strengths: Use AI for boilerplate, standard algorithms, basic shaders, tests, syntax errors, pattern refactoring, initial Render Graph pass setup. Human oversight for complex logic, architecture, performance, security.

    Incremental Interaction: Break down complex tasks. Review each step.

    Standard Check-in: "Confirming understanding: Reviewed [specific 'BTR Docs' note/context]. Goal is [task goal], adhering to [pattern from [[Technical Patterns]]/constraint]. Proceeding with [step]."

Code Quality & Style

    C# Guidelines:

        Explicit typing (var sparingly). Avoid object.

        XML Docs (///) for public API, complex logic.

        Naming: PascalCase for Types, Public Members; camelCase for Local Vars, Parameters. Use _ prefix for private fields ONLY if it's the established project convention (#2 Style).

        readonly where applicable. Follow nullability standards. async/await correctly.

    Readability & Maintainability: Clean C#, HLSL. Logical project folders. Clear Scene hierarchies. Meaningful names.

    Bracket Placement: Follow the existing project convention (e.g., K&R or Allman) (#2 Style).

    Regions: Use #region to group related members only if it's the established project convention (#2 Style).

    Small Files & Components: C# scripts < 300-500 lines. Small MonoBehaviours. Concise Render Graph passes.

    Avoid Duplication (DRY): Reuse code/functions. Use ScriptableObjects for config/data. Use shader includes (.hlslinc). Create reusable Render Graph passes/helpers.

    Build System: Use Unity's build system.

    Linting/Formatting: Adhere to Roslyn Analyzers / .editorconfig. Format shaders consistently.

    Pattern Consistency: Adhere to established patterns (Unity, C#, Render Graph, Architecture - see [[Technical Patterns]], [[Architecture]]). Discuss before introducing new ones. Remove old implementations fully when replacing.

    Asset Naming: Clear, descriptive names with consistent prefixes/suffixes. Avoid "Temp", "New".

    Folder Structure:

        Maintain standard folders: Scenes, Scripts, Prefabs, Materials, Shaders, UI, Audio, etc. (#3 Folders).

        Keep specialized asset folders separate and documented: EnemySystem, Assets/AmplifyShaderPack, Assets/Quibli, Assets/Reach - Complete Sci-Fi UI, etc. (#3 Folders).

        Follow existing naming for new feature modules (PlayerSystem, WeaponSystem) (#3 Folders).

        Store runtime scripts separately from editor scripts (e.g., in Editor folders) (#3 Folders).

    No One-Time Scripts: Keep utility scripts out of runtime folders. Place editor utilities in Editor folders.

Input System

    Consistency: Use the New Input System primarily, as documented in [[Input System Guide]]. (#5 Input).

    Legacy Usage: If legacy input is unavoidably needed, clearly document the specific location and reason in code comments and potentially in [[Input System Guide]] (#5 Input).

    Clarity: Confirm in prompts/code which input approach is required for the task.

Performance & Optimization

    Profiling: Profile regularly (Unity Profiler, platform-specific tools) for CPU/GPU bottlenecks (#6 Perf). Document findings for complex issues in "BTR Docs" ([[Performance Issues]] note?).

    Object Pooling: Use the designated PoolManager for frequently instantiated/destroyed objects (projectiles, VFX). Implement IPoolable interface. Pre-warm pools (#6 Perf, #14 Perf). Document usage in [[Technical Patterns]] or [[Performance Guide]].

    Rendering Optimization: Optimize LODs, use GPU Instancing, static/dynamic batching, Occlusion Culling. Keep materials batch-friendly. Use texture atlases/compression (#6 Perf).

    URP Optimization: Configure URP Asset(s) according to performance targets (disable unused features). Document settings in [[URP Configuration]] (#4 URP, #6 Perf).

    Job System: Use C# Job System + Burst Compiler for heavy computations (pathfinding, complex simulations) where appropriate (#14 Perf). Document job structure and data flow in relevant system guides (e.g., [[Enemy System Guide]]) or [[Technical Patterns]].

DOTS / ECS (If Applicable)

    Documentation: If using DOTS (ECS, Burst, Jobs beyond general performance tasks), document its integration with MonoBehaviour architecture in [[Architecture]] or a dedicated [[DOTS Integration]] note (#7 DOTS).

    Clarity: Clearly mark systems/components using ECS.

    Phasing: If planned but not fully implemented, label as "Future" or "Experimental" in documentation.

Custom Tools & Third-Party Packages

    Documentation: Document the purpose and usage of specialized assets (AmplifyShaderPack, Quibli, Reach - Complete Sci-Fi UI, etc.) in dedicated "BTR Docs" notes (e.g., [[AmplifyShaderPack Usage]]) (#11 Tools). Provide quick references in code comments linking back to these notes where helpful (#10 Docs).

    Modification: Avoid direct modification of third-party assets. Prefer extension methods, partial classes, wrappers, or ScriptableObject configurations. If direct modification is unavoidable, document it thoroughly in the asset's documentation note and potentially add warnings in code (#11 Tools).

Architecture Standards (Project Specific)

    System Managers:

        Use Singleton pattern for core managers (e.g., GameManager, UIManager, PoolManager) if established. Check [[Architecture]]. (#13 Arch)

        Implement IManager interface (if used) for consistent initialization/lifecycle. Document in [[Architecture]] or [[IManager Interface Guide]]. (#13 Arch)

        Keep managers focused on coordination, delegating implementation details. (#13 Arch)

    Behavior Patterns:

        Use ScriptableObjects extensively for configuration data. Document patterns in [[Technical Patterns]]. (#13 Arch)

        Implement IBehavior interface (if used) for modular entity behaviors. Document in [[Architecture]] or [[IBehavior Interface Guide]]. (#13 Arch)

        Document behavior dependencies clearly in code comments or related "BTR Docs" notes. (#13 Arch)

    State Management:

        Use State pattern (via interfaces, classes, or state machines) for complex entity logic if established. Document in [[Architecture]] or relevant system guides (e.g., [[Enemy AI State Machine]]). (#13 Arch)

        Document state transitions, conditions, and responsibilities clearly. (#13 Arch)

        Keep states isolated and testable where possible. (#13 Arch)

Refactoring

    Purposeful: Refactor for clarity, DRY, simplicity, performance, or architecture adherence.

    Holistic Check: Look for related duplication/simplification opportunities.

    Edit, Don't Copy: Modify existing assets directly. Use version control history.

    Verify Integrations: Test thoroughly (automated + manual + visual) after refactoring.

Testing & Validation

    TDD (Where Applicable): Use Unity Test Framework for logic (Edit Mode) and interactions (Play Mode). Write tests before code for new features/bug fixes.

    Comprehensive Tests: Cover critical paths, edge cases, core functionality.

    Tests Must Pass: All tests must pass before commit/merge.

    No Mock Data (Runtime): Use mocks only in Tests assembly. Use SOs/config for runtime data.

    Manual & Visual Verification: Essential for UI, rendering, gameplay feel. Use Frame Debugger, Profiler.

Debugging & Troubleshooting

    Fix Root Cause: Prioritize fixing the underlying issue.

    Console Analysis: Check Unity Editor Console for errors/warnings.

    Targeted Debugging: Use Debug.Log (gated by debug flags for builds - #9 Debug), Debug.Assert (#9 Debug), Unity Debugger, Frame Debugger, Profiler.

    Check "BTR Docs" Fixes: Look in the [[Fixes]] folder/tag within "BTR Docs" for documented solutions (#4 Debugging).

    Document Complex Fixes: Create a note in the [[Fixes]] folder/tag in "BTR Docs" detailing complex bug investigations/solutions (e.g., [[Fixes/Resolve RenderGraph Resource Contention]]) (#5 Debugging).

    Error Handling: Use try-catch appropriately for fragile operations like File I/O or Networking (#9 Debug).

    Custom Logging: If a custom logging system exists, use it consistently and ensure it's documented in [[Technical Patterns]] or [[Logging Guide]] (#9 Debug).

    Research: Use Unity Docs, Forums, URP/Render Graph docs, asset docs, etc.

Security


    Save Data: Use checksums/obfuscation for local saves if needed. Validate loaded data.

    Input Validation: Validate player/external input.

    Dependency Awareness: Be mindful of package/asset security.

    Credentials: Never hardcode secrets. Use secure methods (env vars via build server, excluded SOs).

Version Control & Environment (Git)

    Branching Strategy: (#8 Git)

        Create feature/bugfix branches from main or develop (confirm project standard).

        Use descriptive names: feature/player-jump, fix/ui-overlap-bug.

    Commits: (#8 Git)

        Commit frequently with clear, atomic messages.

        Use conventional commit format (if standard): feat: Add double jump ability, fix: Prevent null reference in InventoryManager, refactor: Optimize enemy pathfinding query.

        Keep working directory clean. Ensure .gitignore is effective (include Library, Temp, Logs, Builds, potentially Obsidian cache files). Ensure "BTR Docs" vault itself IS committed.

    Pull Requests (PRs) / Merge Requests (MRs): (#8 Git)

        Push branches regularly.

        Create PRs/MRs for merging back to main/develop.

        Require at least one review before merging (if team policy).

        Ensure CI checks pass (if applicable).

        Update CHANGELOG.md under [Unreleased] before merging (#8 Git, #10 Docs).

    Tagging & Releases: (#8 Git)

        Use Semantic Versioning tags (e.g., v1.0.0, v1.1.0, v1.1.1).

        Before tagging a release:

            Finalize the [Unreleased] section in CHANGELOG.md into a new dated version section.

            Update the bundleVersion in ProjectSettings/ProjectSettings.asset.

            Ensure all tests pass and build is stable.

            Tag the corresponding commit.

    Configuration Files: Never commit sensitive keys/settings. Use templates or excluded assets.

    Environment Awareness: Use platform defines (#if), SOs for configuration.

    Editor State Management: Restart Editor if unstable. Reload scenes if needed.

Documentation Maintenance ("BTR Docs")

    Update Docs: If changes impact architecture, technical decisions, patterns, URP/Render Graph settings, system behavior, third-party asset usage, or task status, update the relevant notes within "BTR Docs" (#1 Docs Maintenance). This includes:

        [[Architecture]], [[Technical Patterns]]

        System Guides ([[Enemy System Guide]], etc.) (#15 Docs)

        Configuration Docs (within SOs or linked notes) (#15 Docs)

        Asset Usage Guides ([[AmplifyShaderPack Usage]], etc.)

        [[Current Tasks]], [[Development Status]]

    Behavior Documentation: Document IBehavior implementations, requirements, and usage examples within the script's XML comments or linked notes (#15 Docs). Maintain compatibility info if necessary.

    Changelog: Keep CHANGELOG.md updated before merging PRs/MRs (#10 Docs). Link significant changes back to relevant "BTR Docs" notes if helpful.

    In-Code References: Add brief comments in code linking to more detailed explanations in "BTR Docs" where appropriate (e.g., // See [[Complex Algorithm Details]] for explanation).

    Keep Rules Updated: This rule set (.cursorrules or equivalent linked in "BTR Docs") should be periodically reviewed and updated.

Final Dos & Don'ts Summary (#12 Checklist)

    DO:

        Keep code/assets consistent with established style and patterns (reference "BTR Docs").

        Commit regularly with clear, conventional messages.

        Update CHANGELOG.md and bundleVersion before releasing.

        Keep URP settings consistent or documented if varied.

        Reference official Unity/URP/Asset docs for advanced topics.

        Document new systems, refactors, and complex fixes in "BTR Docs".

        Profile performance regularly.

        Follow architectural standards (Managers, Behaviors, States).

    DON'T:

        Bypass PR reviews for critical changes (if applicable).

        Commit untested, broken, or commented-out code.

        Mix Input Systems without clear documentation and necessity.

        Neglect documentation updates in "BTR Docs".

        Hardcode secrets or sensitive data.

        Directly modify third-party assets unless absolutely necessary and heavily documented.