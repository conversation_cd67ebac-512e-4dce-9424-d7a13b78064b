using UnityEngine;
using UnityEditor;
using System;
using System.Collections.Generic;

namespace Stylo.Cadance.Editor
{
    /// <summary>
    /// Real-time spectrum analyzer for Cadance Editor that provides visual frequency analysis
    /// and automatic event creation based on frequency thresholds.
    /// Replaces the complex offline analysis system with a focused real-time tool.
    /// </summary>
    public class CadanceRealtimeSpectrumAnalyzer
    {
        // Configuration - full audible spectrum coverage (20 Hz - 20 kHz)
        public float minFrequency = 80f;      // Hz - reasonable default for musical content
        public float maxFrequency = 8000f;    // Hz - good default covering most musical frequencies
        public float threshold = 1f;          // Default threshold for better usability
        public float minEventGap = 0.15f;     // Default gap for musical timing (150ms)
        public float latencyCompensation = 0.02f; // Compensate for FFT processing delay (20ms default)
        public bool enableEventCreation = true;
        public bool enableVisualization = true;

        // Visualization settings
        public int spectrumBars = 64;
        public float visualizationHeight = 100f;
        public Color spectrumColor = Color.cyan;
        public Color thresholdColor = Color.red;
        public Color backgroundColor = Color.black;
        public Color frequencyRangeOverlayColor = new Color(0f, 0f, 0f, 0.6f); // Semi-transparent black for overlay

        // Internal state
        private float[] currentSpectrum;
        private float lastEventTime = -1f;
        private List<float> recentEventTimes = new List<float>();
        private bool isAnalyzing = false;
        private int sampleRate = 44100;

        // UI feedback variables
        private float currentMagnitudeForUI = 0f;
        private float lastThresholdExceededTime = -1f;

        // Event creation tracking
        private CadanceAsset targetCadance;
        private CadanceTrackBase targetTrack;
        private HashSet<int> createdEventSamples = new HashSet<int>();

        // Grid settings for quantization
        private float currentTempo = 120f;
        private int gridDivision = 4;

        // Playback control callback
        private System.Action onStartPlayback;

        /// <summary>
        /// Initializes the spectrum analyzer with the specified Cadance asset and track.
        /// </summary>
        public void Initialize(CadanceAsset cadance, CadanceTrackBase track)
        {
            targetCadance = cadance;
            targetTrack = track;
            createdEventSamples.Clear();
            recentEventTimes.Clear();
            lastEventTime = -1f;

            // Set sample rate from cadance asset if available
            if (cadance != null)
            {
                sampleRate = cadance.SampleRate;
            }
        }

        /// <summary>
        /// Sets the grid settings for quantization.
        /// </summary>
        public void SetGridSettings(float tempo, int division)
        {
            currentTempo = tempo;
            gridDivision = division;
        }

        /// <summary>
        /// Sets the callback to start playback when analysis starts.
        /// </summary>
        public void SetPlaybackCallback(System.Action startPlaybackCallback)
        {
            onStartPlayback = startPlaybackCallback;
        }

        /// <summary>
        /// Starts real-time spectrum analysis using FMOD only.
        /// Also triggers playback if a callback is set.
        /// </summary>
        public bool StartAnalysis()
        {
            if (isAnalyzing) return true;

            // Start playback first if callback is available
            onStartPlayback?.Invoke();

            // Try to enable FMOD spectrum analysis
            if (FMODEditorAudioSystem.EnableSpectrumAnalysis(1024))
            {
                isAnalyzing = true;
                Debug.Log("[Spectrum Analyzer] Real-time FMOD analysis started");
                return true;
            }
            else
            {
                // FMOD spectrum analysis failed - provide helpful error message
                isAnalyzing = false;
                enableEventCreation = false;
                Debug.LogError("[Spectrum Analyzer] FMOD spectrum analysis failed to start.");
                Debug.LogError("[Spectrum Analyzer] Ensure FMOD audio is loaded and playing before enabling spectrum analysis.");
                return false;
            }
        }

        /// <summary>
        /// Stops real-time spectrum analysis.
        /// </summary>
        public void StopAnalysis()
        {
            if (!isAnalyzing) return;

            FMODEditorAudioSystem.DisableSpectrumAnalysis();
            isAnalyzing = false;
            currentSpectrum = null;
            Debug.Log("[Spectrum Analyzer] Real-time analysis stopped");
        }

        /// <summary>
        /// Updates the spectrum analysis and processes event creation.
        /// Optimized for performance and responsiveness.
        /// </summary>
        public void Update(float currentTime)
        {
            if (!isAnalyzing) return;

            // Get current spectrum data from FMOD
            currentSpectrum = FMODEditorAudioSystem.GetSpectrumData();
            if (currentSpectrum == null) return;

            // Always update magnitude for UI display (independent of event creation)
            UpdateCurrentMagnitude();

            // Check for frequency peaks and create events if enabled
            if (enableEventCreation && targetCadance != null && targetTrack != null)
            {
                ProcessEventCreation(currentTime);
            }

            // Clean up old event times (keep only last 10 seconds)
            CleanupOldEventTimes(currentTime);
        }

        /// <summary>
        /// Updates the current magnitude for UI display (independent of event creation).
        /// </summary>
        private void UpdateCurrentMagnitude()
        {
            // Get magnitude using both FMOD and direct spectrum analysis
            float fmodMagnitude = FMODEditorAudioSystem.GetMagnitudeInRange(minFrequency, maxFrequency, sampleRate);
            float spectrumMagnitude = CalculateMagnitudeFromSpectrum();

            // Use the higher of the two magnitudes for better detection
            currentMagnitudeForUI = Mathf.Max(fmodMagnitude, spectrumMagnitude);

            // Update threshold exceeded indicator for UI feedback
            if (currentMagnitudeForUI >= threshold)
            {
                lastThresholdExceededTime = Time.realtimeSinceStartup;
            }
        }

        /// <summary>
        /// Processes automatic event creation based on frequency thresholds.
        /// Uses the already calculated magnitude from UpdateCurrentMagnitude.
        /// </summary>
        private void ProcessEventCreation(float currentTime)
        {
            // Quick prerequisite check without debug spam
            if (targetCadance == null || targetTrack == null) return;

            // Check if enough time has passed since last event
            if (currentTime - lastEventTime < minEventGap) return;

            // Use the magnitude already calculated in UpdateCurrentMagnitude
            float magnitude = currentMagnitudeForUI;

            // Check if magnitude exceeds threshold
            if (magnitude >= threshold)
            {
                // Apply latency compensation to align events with actual audio peaks
                float compensatedTime = currentTime - latencyCompensation;
                int compensatedSample = Mathf.RoundToInt(compensatedTime * sampleRate);

                // Ensure compensated sample is not negative
                compensatedSample = Mathf.Max(0, compensatedSample);

                if (ShouldCreateEvent(compensatedSample, compensatedTime))
                {
                    Debug.Log($"[Spectrum Analyzer] ✓ Event created at {compensatedTime:F3}s (compensated from {currentTime:F3}s) with magnitude {magnitude:F4} (threshold: {threshold:F4})");
                    CreateEvent(compensatedSample, magnitude);
                    lastEventTime = currentTime; // Use original time for gap calculation
                    recentEventTimes.Add(currentTime);
                }
            }
        }

        /// <summary>
        /// Calculates magnitude from current spectrum data in the specified frequency range.
        /// Optimized for performance and better peak detection.
        /// </summary>
        private float CalculateMagnitudeFromSpectrum()
        {
            if (currentSpectrum == null || currentSpectrum.Length == 0) return 0f;

            float nyquistFrequency = sampleRate * 0.5f;
            float maxMagnitude = 0f;
            float totalMagnitude = 0f;
            int samplesInRange = 0;

            // Pre-calculate frequency step for performance
            float frequencyStep = nyquistFrequency / currentSpectrum.Length;

            for (int i = 0; i < currentSpectrum.Length; i++)
            {
                float frequency = i * frequencyStep;

                if (frequency >= minFrequency && frequency <= maxFrequency)
                {
                    float magnitude = currentSpectrum[i];
                    totalMagnitude += magnitude;
                    if (magnitude > maxMagnitude) maxMagnitude = magnitude;
                    samplesInRange++;
                }
            }

            // Return the maximum magnitude in range for better peak detection
            // This is more responsive to sudden audio peaks than average
            return maxMagnitude;
        }

        /// <summary>
        /// Determines if an event should be created at the given position.
        /// Implements duplicate prevention logic.
        /// </summary>
        private bool ShouldCreateEvent(int samplePosition, float timePosition)
        {
            // Check if we already created an event at this exact sample position
            if (createdEventSamples.Contains(samplePosition)) return false;

            // Check minimum time gap
            foreach (float eventTime in recentEventTimes)
            {
                if (Mathf.Abs(timePosition - eventTime) < minEventGap)
                {
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// Creates a new Cadance event at the specified sample position.
        /// Enhanced with detailed debugging and validation.
        /// </summary>
        private void CreateEvent(int samplePosition, float magnitude)
        {
            if (targetTrack == null)
            {
                Debug.LogError("[Spectrum Analyzer] CreateEvent: targetTrack is null");
                return;
            }

            try
            {
                // Debug track information
                Debug.Log($"[Spectrum Analyzer] Creating event on track '{targetTrack.name}' (ID: {targetTrack.EventID})");

                // Create event with magnitude as payload
                var eventPayload = new FloatPayload(magnitude);
                var cadanceEvent = new CadanceEvent(targetTrack.EventID, samplePosition, eventPayload);

                // Debug event details
                Debug.Log($"[Spectrum Analyzer] Event created: Sample={samplePosition}, Magnitude={magnitude:F4}, EventID={targetTrack.EventID}");

                // Add event to track
                int eventCountBefore = targetTrack.EventCount;
                targetTrack.AddEvent(cadanceEvent);
                int eventCountAfter = targetTrack.EventCount;

                // Verify event was added
                if (eventCountAfter > eventCountBefore)
                {
                    createdEventSamples.Add(samplePosition);
                    Debug.Log($"[Spectrum Analyzer] ✓ Event successfully added to track! Events: {eventCountBefore} → {eventCountAfter}");

                    // Mark the cadance asset as dirty to ensure it saves
                    if (targetCadance != null)
                    {
                        UnityEditor.EditorUtility.SetDirty(targetCadance);
                        Debug.Log($"[Spectrum Analyzer] Marked CadanceAsset '{targetCadance.name}' as dirty for saving");
                    }
                }
                else
                {
                    Debug.LogWarning($"[Spectrum Analyzer] Event may not have been added properly. Events: {eventCountBefore} → {eventCountAfter}");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Spectrum Analyzer] Error creating event at sample {samplePosition}: {ex.Message}\n{ex.StackTrace}");
            }
        }

        /// <summary>
        /// Cleans up old event times to prevent memory buildup.
        /// </summary>
        private void CleanupOldEventTimes(float currentTime)
        {
            recentEventTimes.RemoveAll(time => currentTime - time > 10f);
        }

        /// <summary>
        /// Draws the spectrum visualization in the specified rect.
        /// </summary>
        public void DrawVisualization(Rect rect)
        {
            if (!enableVisualization) return;

            // Draw background
            EditorGUI.DrawRect(rect, backgroundColor);

            if (currentSpectrum == null || currentSpectrum.Length == 0)
            {
                // Show message when no spectrum data is available
                var centeredStyle = new GUIStyle(EditorStyles.label)
                {
                    alignment = TextAnchor.MiddleCenter,
                    normal = { textColor = Color.gray }
                };

                string message = isAnalyzing ?
                    "No spectrum data available\n(FMOD audio required for real-time analysis)" :
                    "Spectrum analyzer not running\nPress play to start analysis";

                GUI.Label(rect, message, centeredStyle);
                return;
            }

            // Calculate bar width and frequency mapping
            float barWidth = rect.width / spectrumBars;
            float nyquistFrequency = sampleRate * 0.5f; // Maximum frequency in spectrum

            // Draw spectrum bars with frequency range overlay
            for (int i = 0; i < spectrumBars && i < currentSpectrum.Length; i++)
            {
                float magnitude = currentSpectrum[i];

                // Apply amplitude scaling and use visualizationHeight setting
                // Multiply by visualizationHeight for user control and add base amplification
                float amplifiedMagnitude = magnitude * visualizationHeight * 5f; // 5x base amplification for better visibility
                float barHeight = Mathf.Min(amplifiedMagnitude, rect.height); // Clamp to rect height

                // Calculate frequency for this bar
                float frequency = (i / (float)spectrumBars) * nyquistFrequency;

                // Determine if this frequency is within the analysis range
                bool inRange = frequency >= minFrequency && frequency <= maxFrequency;

                Rect barRect = new Rect(
                    rect.x + i * barWidth,
                    rect.y + rect.height - barHeight,
                    barWidth - 1f,
                    barHeight
                );

                // Draw spectrum bar with appropriate color
                Color barColor = inRange ? spectrumColor : Color.Lerp(spectrumColor, Color.gray, 0.7f);
                EditorGUI.DrawRect(barRect, barColor);

                // Draw overlay for frequencies outside the analysis range
                if (!inRange)
                {
                    Rect overlayRect = new Rect(
                        rect.x + i * barWidth,
                        rect.y,
                        barWidth - 1f,
                        rect.height
                    );
                    EditorGUI.DrawRect(overlayRect, frequencyRangeOverlayColor);
                }
            }

            // Draw threshold line - scale threshold relative to visualization height
            // The threshold should be positioned relative to the amplified magnitude scale
            float thresholdRatio = threshold / (visualizationHeight * 5f); // Match the amplification in spectrum drawing
            float thresholdY = rect.y + rect.height - (thresholdRatio * rect.height);
            thresholdY = Mathf.Clamp(thresholdY, rect.y, rect.y + rect.height - 1f); // Keep within bounds
            Rect thresholdRect = new Rect(rect.x, thresholdY, rect.width, 2f); // Make line slightly thicker for visibility
            EditorGUI.DrawRect(thresholdRect, thresholdColor);

            // Draw frequency range indicators
            DrawFrequencyRangeIndicators(rect, nyquistFrequency);
        }

        /// <summary>
        /// Draws frequency range indicators on the spectrum visualization.
        /// </summary>
        private void DrawFrequencyRangeIndicators(Rect rect, float nyquistFrequency)
        {
            // Calculate positions for min and max frequency indicators
            float minFreqPosition = (minFrequency / nyquistFrequency) * rect.width;
            float maxFreqPosition = (maxFrequency / nyquistFrequency) * rect.width;

            // Draw min frequency line
            if (minFreqPosition >= 0 && minFreqPosition <= rect.width)
            {
                Rect minLineRect = new Rect(rect.x + minFreqPosition, rect.y, 2f, rect.height);
                EditorGUI.DrawRect(minLineRect, Color.green);

                // Label for min frequency
                var labelStyle = new GUIStyle(EditorStyles.miniLabel)
                {
                    normal = { textColor = Color.green },
                    fontSize = 9
                };

                Rect labelRect = new Rect(rect.x + minFreqPosition + 3f, rect.y + 2f, 50f, 15f);
                GUI.Label(labelRect, $"{minFrequency:F0}Hz", labelStyle);
            }

            // Draw max frequency line
            if (maxFreqPosition >= 0 && maxFreqPosition <= rect.width)
            {
                Rect maxLineRect = new Rect(rect.x + maxFreqPosition, rect.y, 2f, rect.height);
                EditorGUI.DrawRect(maxLineRect, Color.red);

                // Label for max frequency
                var labelStyle = new GUIStyle(EditorStyles.miniLabel)
                {
                    normal = { textColor = Color.red },
                    fontSize = 9
                };

                Rect labelRect = new Rect(rect.x + maxFreqPosition - 47f, rect.y + 2f, 50f, 15f);
                GUI.Label(labelRect, $"{maxFrequency:F0}Hz", labelStyle);
            }
        }

        /// <summary>
        /// Draws the configuration UI for the spectrum analyzer.
        /// </summary>
        public void DrawConfigurationUI()
        {
            EditorGUILayout.LabelField("Spectrum Analyzer Configuration", EditorStyles.boldLabel);

            // Enhanced frequency range controls with sliders
            EditorGUILayout.LabelField("Frequency Range:", EditorStyles.boldLabel);

            // Min frequency control - full audible spectrum range
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("Min:", GUILayout.Width(30));
            float newMinFreq = EditorGUILayout.Slider(minFrequency, 20f, 10000f, GUILayout.Width(120));
            if (newMinFreq != minFrequency)
            {
                minFrequency = Mathf.Min(newMinFreq, maxFrequency - 50f); // Ensure min < max
            }
            minFrequency = EditorGUILayout.FloatField(minFrequency, GUILayout.Width(60));
            EditorGUILayout.LabelField("Hz", GUILayout.Width(20));
            EditorGUILayout.EndHorizontal();

            // Max frequency control - full audible spectrum range
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("Max:", GUILayout.Width(30));
            float newMaxFreq = EditorGUILayout.Slider(maxFrequency, 100f, 20000f, GUILayout.Width(120));
            if (newMaxFreq != maxFrequency)
            {
                maxFrequency = Mathf.Max(newMaxFreq, minFrequency + 50f); // Ensure max > min
            }
            maxFrequency = EditorGUILayout.FloatField(maxFrequency, GUILayout.Width(60));
            EditorGUILayout.LabelField("Hz", GUILayout.Width(20));
            EditorGUILayout.EndHorizontal();

            // Quick preset buttons - full audible spectrum coverage
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("Presets:", GUILayout.Width(50));
            if (GUILayout.Button("Bass", GUILayout.Width(50)))
            {
                minFrequency = 20f;
                maxFrequency = 250f;
            }
            if (GUILayout.Button("Mid", GUILayout.Width(50)))
            {
                minFrequency = 250f;
                maxFrequency = 4000f;
            }
            if (GUILayout.Button("High", GUILayout.Width(50)))
            {
                minFrequency = 4000f;
                maxFrequency = 20000f;
            }
            if (GUILayout.Button("Full", GUILayout.Width(50)))
            {
                minFrequency = 20f;
                maxFrequency = 20000f;
            }
            EditorGUILayout.EndHorizontal();

            // Threshold control
            threshold = EditorGUILayout.Slider("Threshold", threshold, 0f, 1f);

            // Event creation settings
            enableEventCreation = EditorGUILayout.Toggle("Enable Event Creation", enableEventCreation);
            if (enableEventCreation)
            {
                minEventGap = EditorGUILayout.Slider("Min Event Gap (s)", minEventGap, 0.01f, 1f);
                latencyCompensation = EditorGUILayout.Slider("Latency Compensation (s)", latencyCompensation, 0f, 0.1f);
                EditorGUILayout.HelpBox("Latency compensation adjusts event timing to account for FFT processing delay.", MessageType.Info);
            }

            // Visualization settings
            enableVisualization = EditorGUILayout.Toggle("Enable Visualization", enableVisualization);
            if (enableVisualization)
            {
                spectrumBars = EditorGUILayout.IntSlider("Spectrum Bars", spectrumBars, 16, 128);
                visualizationHeight = EditorGUILayout.Slider("Height", visualizationHeight, 50f, 200f);
            }

            // Manual control buttons
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Control", EditorStyles.boldLabel);

            EditorGUILayout.BeginHorizontal();

            // Start button
            GUI.enabled = !isAnalyzing && targetCadance != null && targetTrack != null;
            if (GUILayout.Button("▶ Start Analysis", GUILayout.Height(25)))
            {
                StartAnalysis();
            }

            // Stop button
            GUI.enabled = isAnalyzing;
            if (GUILayout.Button("⏹ Stop Analysis", GUILayout.Height(25)))
            {
                StopAnalysis();
            }

            // Quantize button
            GUI.enabled = targetTrack != null && targetTrack.EventCount > 0;
            if (GUILayout.Button("⚡ Quantize Events", GUILayout.Height(25)))
            {
                QuantizeTrackEvents();
            }

            GUI.enabled = true;
            EditorGUILayout.EndHorizontal();

            // Status display
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Status", EditorStyles.boldLabel);

            string analysisStatus = isAnalyzing ? "Running" : "Stopped";
            if (isAnalyzing && !FMODEditorAudioSystem.IsSpectrumAnalysisEnabled())
            {
                analysisStatus += " (Visualization Only)";
            }

            EditorGUILayout.LabelField($"Analysis: {analysisStatus}");
            EditorGUILayout.LabelField($"Spectrum Source: {GetSpectrumSource()}");
            EditorGUILayout.LabelField($"FMOD System: {(FMODEditorAudioSystem.IsSpectrumAnalysisEnabled() ? "Active" : "Inactive")}");
            EditorGUILayout.LabelField($"Events Created: {createdEventSamples.Count}");
            EditorGUILayout.LabelField($"Last Event: {(lastEventTime >= 0 ? $"{lastEventTime:F2}s" : "None")}");

            // Enhanced debugging information with real-time feedback
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Debug Info", EditorStyles.boldLabel);

            // Current spectrum data status
            string spectrumStatus = currentSpectrum != null ? $"{currentSpectrum.Length} samples" : "No data";
            EditorGUILayout.LabelField($"Spectrum Data: {spectrumStatus}");

            // Real-time magnitude display with visual feedback
            if (isAnalyzing)
            {
                EditorGUILayout.LabelField($"Current Magnitude: {currentMagnitudeForUI:F4}");

                // Threshold status with visual feedback
                bool thresholdExceeded = currentMagnitudeForUI >= threshold;
                bool recentlyExceeded = (Time.realtimeSinceStartup - lastThresholdExceededTime) < 0.5f; // Flash for 0.5 seconds

                if (recentlyExceeded)
                {
                    GUI.color = Color.yellow; // Flash yellow when recently exceeded
                }
                else if (thresholdExceeded)
                {
                    GUI.color = Color.green; // Green when currently exceeded
                }
                else
                {
                    GUI.color = Color.white; // White when below threshold
                }

                string thresholdStatus = thresholdExceeded ? "EXCEEDED ✓" : "Below";
                if (recentlyExceeded && !thresholdExceeded) thresholdStatus = "RECENT PEAK ⚡";

                EditorGUILayout.LabelField($"Threshold Status: {thresholdStatus}");
                GUI.color = Color.white;

                // Magnitude bar visualization
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField("Magnitude:", GUILayout.Width(70));
                Rect barRect = GUILayoutUtility.GetRect(100, 16);

                // Draw magnitude bar background
                EditorGUI.DrawRect(barRect, Color.black);

                // Calculate scale factor for magnitude display (use 2x threshold as max scale)
                float maxScale = threshold * 2f;
                float magnitudeRatio = Mathf.Clamp01(currentMagnitudeForUI / maxScale);

                // Draw magnitude level
                Rect fillRect = new Rect(barRect.x, barRect.y, barRect.width * magnitudeRatio, barRect.height);
                Color barColor = thresholdExceeded ? Color.green : Color.cyan;
                EditorGUI.DrawRect(fillRect, barColor);

                // Draw threshold line at correct position (threshold / maxScale = 0.5)
                float thresholdRatio = threshold / maxScale; // This should be 0.5 when maxScale = threshold * 2
                float thresholdX = barRect.x + (barRect.width * thresholdRatio);
                EditorGUI.DrawRect(new Rect(thresholdX, barRect.y, 2, barRect.height), Color.red);

                EditorGUILayout.EndHorizontal();
            }

            // Target information
            EditorGUILayout.LabelField($"Target Cadance: {(targetCadance != null ? targetCadance.name : "None")}");
            EditorGUILayout.LabelField($"Target Track: {(targetTrack != null ? targetTrack.name : "None")}");

            // Event creation status with color coding
            string eventCreationStatus = "Disabled";
            Color statusColor = Color.white;

            if (enableEventCreation)
            {
                if (targetCadance == null)
                {
                    eventCreationStatus = "Missing Cadance";
                    statusColor = Color.red;
                }
                else if (targetTrack == null)
                {
                    eventCreationStatus = "Missing Track";
                    statusColor = Color.red;
                }
                else
                {
                    eventCreationStatus = "Ready ✓";
                    statusColor = Color.green;
                }
            }

            GUI.color = statusColor;
            EditorGUILayout.LabelField($"Event Creation: {eventCreationStatus}");
            GUI.color = Color.white;
        }

        /// <summary>
        /// Clears all created events and resets the analyzer state.
        /// </summary>
        public void ClearEvents()
        {
            createdEventSamples.Clear();
            recentEventTimes.Clear();
            lastEventTime = -1f;
            Debug.Log("[Spectrum Analyzer] Cleared all events and reset state");
        }

        /// <summary>
        /// Gets the current spectrum data for external use.
        /// </summary>
        public float[] GetCurrentSpectrum()
        {
            return currentSpectrum;
        }

        /// <summary>
        /// Checks if the analyzer is currently running.
        /// </summary>
        public bool IsAnalyzing()
        {
            return isAnalyzing;
        }

        /// <summary>
        /// Gets the current spectrum source information for debugging.
        /// </summary>
        public string GetSpectrumSource()
        {
            if (!isAnalyzing) return "Not analyzing";
            if (FMODEditorAudioSystem.IsSpectrumAnalysisEnabled()) return "FMOD DSP";
            return "No spectrum data";
        }

        /// <summary>
        /// Gets the number of events created by the analyzer.
        /// </summary>
        public int GetCreatedEventCount()
        {
            return createdEventSamples?.Count ?? 0;
        }

        /// <summary>
        /// Gets the time of the last created event.
        /// </summary>
        public float GetLastEventTime()
        {
            return lastEventTime;
        }

        /// <summary>
        /// Quantizes all events in the target track to the current grid settings.
        /// </summary>
        private void QuantizeTrackEvents()
        {
            if (targetTrack == null || targetCadance == null)
            {
                Debug.LogWarning("[Spectrum Analyzer] Cannot quantize: missing track or cadance asset");
                return;
            }

            if (targetTrack.EventCount == 0)
            {
                Debug.LogWarning("[Spectrum Analyzer] No events to quantize");
                return;
            }

            // Calculate grid timing
            float beatDuration = 60f / currentTempo; // Duration of one quarter note in seconds
            float noteDuration = beatDuration / (gridDivision / 4f); // Duration of selected grid division
            int noteDurationSamples = Mathf.RoundToInt(noteDuration * sampleRate);

            Debug.Log($"[Spectrum Analyzer] Quantizing {targetTrack.EventCount} events to grid (Tempo: {currentTempo} BPM, Division: 1/{gridDivision}, Note Duration: {noteDuration:F3}s)");

            // Collect all events for processing
            var eventsToQuantize = new List<CadanceEvent>();
            for (int i = 0; i < targetTrack.EventCount; i++)
            {
                var evt = targetTrack.GetEventAtIndex(i);
                if (evt != null)
                {
                    eventsToQuantize.Add(evt);
                }
            }

            // Clear the track and add quantized events
            targetTrack.ClearEvents();
            int quantizedCount = 0;

            foreach (var originalEvent in eventsToQuantize)
            {
                // Calculate the quantized sample position
                float originalTime = (float)originalEvent.StartSample / sampleRate;
                float quantizedTime = Mathf.Round(originalTime / noteDuration) * noteDuration;
                int quantizedSample = Mathf.RoundToInt(quantizedTime * sampleRate);

                // Ensure sample is within bounds
                quantizedSample = Mathf.Max(0, quantizedSample);

                // Create new quantized event
                CadanceEvent quantizedEvent;
                if (originalEvent.IsOneOff())
                {
                    quantizedEvent = new CadanceEvent(originalEvent.EventID, quantizedSample, originalEvent.Payload);
                }
                else
                {
                    quantizedEvent = new CadanceEvent(originalEvent.EventID, quantizedSample, originalEvent.EndSample, originalEvent.Payload);
                }

                targetTrack.AddEvent(quantizedEvent);
                quantizedCount++;

                Debug.Log($"[Spectrum Analyzer] Quantized event: {originalEvent.StartSample} → {quantizedSample} samples ({originalTime:F3}s → {quantizedTime:F3}s)");
            }

            // Mark assets as dirty for saving
            if (targetCadance != null)
            {
                UnityEditor.EditorUtility.SetDirty(targetCadance);
            }
            UnityEditor.EditorUtility.SetDirty(targetTrack);

            Debug.Log($"[Spectrum Analyzer] ✓ Quantized {quantizedCount} events to {currentTempo} BPM, 1/{gridDivision} note grid");
        }

    }
}
