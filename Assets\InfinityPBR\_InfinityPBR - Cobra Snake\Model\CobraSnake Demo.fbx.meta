fileFormatVersion: 2
guid: 3416ab0f3d8ca4e48aec75bc83531080
ModelImporter:
  serializedVersion: 22
  fileIDToRecycleName:
    100000: base_snake
    100002: brow_L
    100004: brow_R
    100006: cobraBody
    100008: //RootNode
    100010: CobraTongue
    100012: eye_blink_L
    100014: eye_blink_R
    100016: eye_L
    100018: eye_R
    100020: joint1
    100022: L_cobra_eye
    100024: L_cobraFang
    100026: nose_jnt_L
    100028: nose_jnt_R
    100030: R_cobraEye
    100032: R_cobraFang
    100034: Snake_jnt1
    100036: Snake_jnt10
    100038: Snake_jnt11
    100040: Snake_jnt12
    100042: Snake_jnt13
    100044: Snake_jnt14
    100046: Snake_jnt15
    100048: Snake_jnt16
    100050: Snake_jnt17
    100052: Snake_jnt18
    100054: Snake_jnt19
    100056: Snake_jnt2
    100058: Snake_jnt20
    100060: Snake_jnt21
    100062: Snake_jnt22
    100064: Snake_jnt23
    100066: <PERSON>_jnt24
    100068: Snake_jnt25
    100070: <PERSON>_jnt26
    100072: <PERSON>_jnt27
    100074: Snake_jnt28
    100076: Snake_jnt29
    100078: Snake_jnt3
    100080: Snake_jnt30
    100082: Snake_jnt31
    100084: Snake_jnt32
    100086: Snake_jnt33
    100088: Snake_jnt34
    100090: Snake_jnt35
    100092: Snake_jnt36
    100094: Snake_jnt37
    100096: Snake_jnt38
    100098: Snake_jnt39
    100100: Snake_jnt4
    100102: Snake_jnt40
    100104: Snake_jnt41
    100106: Snake_jnt42
    100108: Snake_jnt43
    100110: Snake_jnt5
    100112: Snake_jnt6
    100114: Snake_jnt7
    100116: Snake_jnt8
    100118: Snake_jnt9
    100120: snake_tongue_jnt10
    100122: snake_tongue_jnt11
    100124: snake_tongue_jnt12
    100126: snake_tongue_jnt13
    100128: snake_tongue_jnt14
    100130: snake_tongue_jnt15
    100132: snake_tongue_jnt16
    100134: snake_tongue_jnt17
    100136: snake_tongue_jnt2
    100138: snake_tongue_jnt3
    100140: snake_tongue_jnt4
    100142: snake_tongue_jnt5
    100144: snake_tongue_jnt6
    100146: snake_tongue_jnt7
    100148: snake_tongue_jnt8
    100150: snake_tongue_jnt9
    100152: tooth_L
    100154: tooth_R
    400000: base_snake
    400002: brow_L
    400004: brow_R
    400006: cobraBody
    400008: //RootNode
    400010: CobraTongue
    400012: eye_blink_L
    400014: eye_blink_R
    400016: eye_L
    400018: eye_R
    400020: joint1
    400022: L_cobra_eye
    400024: L_cobraFang
    400026: nose_jnt_L
    400028: nose_jnt_R
    400030: R_cobraEye
    400032: R_cobraFang
    400034: Snake_jnt1
    400036: Snake_jnt10
    400038: Snake_jnt11
    400040: Snake_jnt12
    400042: Snake_jnt13
    400044: Snake_jnt14
    400046: Snake_jnt15
    400048: Snake_jnt16
    400050: Snake_jnt17
    400052: Snake_jnt18
    400054: Snake_jnt19
    400056: Snake_jnt2
    400058: Snake_jnt20
    400060: Snake_jnt21
    400062: Snake_jnt22
    400064: Snake_jnt23
    400066: Snake_jnt24
    400068: Snake_jnt25
    400070: Snake_jnt26
    400072: Snake_jnt27
    400074: Snake_jnt28
    400076: Snake_jnt29
    400078: Snake_jnt3
    400080: Snake_jnt30
    400082: Snake_jnt31
    400084: Snake_jnt32
    400086: Snake_jnt33
    400088: Snake_jnt34
    400090: Snake_jnt35
    400092: Snake_jnt36
    400094: Snake_jnt37
    400096: Snake_jnt38
    400098: Snake_jnt39
    400100: Snake_jnt4
    400102: Snake_jnt40
    400104: Snake_jnt41
    400106: Snake_jnt42
    400108: Snake_jnt43
    400110: Snake_jnt5
    400112: Snake_jnt6
    400114: Snake_jnt7
    400116: Snake_jnt8
    400118: Snake_jnt9
    400120: snake_tongue_jnt10
    400122: snake_tongue_jnt11
    400124: snake_tongue_jnt12
    400126: snake_tongue_jnt13
    400128: snake_tongue_jnt14
    400130: snake_tongue_jnt15
    400132: snake_tongue_jnt16
    400134: snake_tongue_jnt17
    400136: snake_tongue_jnt2
    400138: snake_tongue_jnt3
    400140: snake_tongue_jnt4
    400142: snake_tongue_jnt5
    400144: snake_tongue_jnt6
    400146: snake_tongue_jnt7
    400148: snake_tongue_jnt8
    400150: snake_tongue_jnt9
    400152: tooth_L
    400154: tooth_R
    2100000: cobraMat4
    4300000: cobraBody
    4300002: R_cobraFang
    4300004: L_cobra_eye
    4300006: L_cobraFang
    4300008: CobraTongue
    4300010: R_cobraEye
    7400000: Attack1
    7400002: Attack2
    7400004: Attack3
    7400006: Spit1
    7400008: Spit2
    7400010: Walk
    7400012: AlertWalk
    7400014: WalkBackward
    7400016: Idle
    7400018: IdleBreak
    7400020: AlertIdle
    7400022: AlertIdleBreak
    7400024: Taunt
    7400026: GotHit
    7400028: Dodge
    7400030: Death
    7400032: Turn180
    7400034: CalmToAlert
    7400036: AlertToCalm
    7400038: TongueLoop
    9500000: //RootNode
    13700000: cobraBody
    13700002: CobraTongue
    13700004: L_cobra_eye
    13700006: L_cobraFang
    13700008: R_cobraEye
    13700010: R_cobraFang
  externalObjects: {}
  materials:
    importMaterials: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: Attack1
      takeName: Take 001
      firstFrame: 860
      lastFrame: 896
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: Attack2
      takeName: Take 001
      firstFrame: 808
      lastFrame: 854
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: Attack3
      takeName: Take 001
      firstFrame: 756
      lastFrame: 799
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: Spit1
      takeName: Take 001
      firstFrame: 377
      lastFrame: 423
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events:
      - time: 0.3276088
        functionName: Spit
        data: 
        objectReferenceParameter: {instanceID: 0}
        floatParameter: 0
        intParameter: 0
        messageOptions: 0
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: Spit2
      takeName: Take 001
      firstFrame: 434
      lastFrame: 567
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events:
      - time: 0.29142013
        functionName: StartSnakeBreath
        data: 
        objectReferenceParameter: {instanceID: 0}
        floatParameter: 0
        intParameter: 0
        messageOptions: 0
      - time: 0.75443774
        functionName: StopSnakeBreath
        data: 
        objectReferenceParameter: {instanceID: 0}
        floatParameter: 0
        intParameter: 0
        messageOptions: 0
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: Walk
      takeName: Take 001
      firstFrame: 1459
      lastFrame: 1516
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 1459
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: AlertWalk
      takeName: Take 001
      firstFrame: 1034
      lastFrame: 1152
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 1033
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: WalkBackward
      takeName: Take 001
      firstFrame: 904
      lastFrame: 1022
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 903
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: Idle
      takeName: Take 001
      firstFrame: 1544
      lastFrame: 1618
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 1528
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: IdleBreak
      takeName: Take 001
      firstFrame: 1628
      lastFrame: 1751
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: AlertIdle
      takeName: Take 001
      firstFrame: 1388
      lastFrame: 1440
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: AlertIdleBreak
      takeName: Take 001
      firstFrame: 1278
      lastFrame: 1378
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: Taunt
      takeName: Take 001
      firstFrame: 262
      lastFrame: 367
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: GotHit
      takeName: Take 001
      firstFrame: 590
      lastFrame: 638
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: Dodge
      takeName: Take 001
      firstFrame: 650
      lastFrame: 680
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: Death
      takeName: Take 001
      firstFrame: 690
      lastFrame: 730
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: Turn180
      takeName: Take 001
      firstFrame: 209
      lastFrame: 249
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: CalmToAlert
      takeName: Take 001
      firstFrame: 1163
      lastFrame: 1208
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: AlertToCalm
      takeName: Take 001
      firstFrame: 1223
      lastFrame: 1268
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: TongueLoop
      takeName: Take 001
      firstFrame: 0
      lastFrame: 160
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 50
    meshCompression: 0
    addColliders: 0
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: base_snake
    rootMotionBoneRotation: {x: 0, y: 0, z: 0, w: 1}
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 2
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
