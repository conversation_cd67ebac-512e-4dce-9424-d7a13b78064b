using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.Linq;
using System.IO;

namespace Stylo.Cadance.Editor
{
    /// <summary>
    /// Window for displaying detailed conversion results from the Cadance Asset Conversion Tool.
    /// </summary>
    public class ConversionResultsWindow : EditorWindow
    {
        private List<ConversionResult> results = new List<ConversionResult>();
        private Vector2 scrollPosition;
        private bool showSuccessful = true;
        private bool showFailed = true;
        private string searchFilter = "";
        private ConversionResultFilter currentFilter = ConversionResultFilter.All;
        
        private enum ConversionResultFilter
        {
            All,
            Successful,
            Failed,
            FMODKoreographySet,
            Koreography,
            KoreographyTrack
        }
        
        public void SetResults(List<ConversionResult> conversionResults)
        {
            results = new List<ConversionResult>(conversionResults);
            Repaint();
        }
        
        private void OnGUI()
        {
            DrawHeader();
            DrawFilters();
            DrawResultsList();
            DrawFooter();
        }
        
        private void DrawHeader()
        {
            EditorGUILayout.LabelField("Conversion Results", EditorStyles.boldLabel);
            EditorGUILayout.Space();
            
            if (results.Count > 0)
            {
                int successful = results.Count(r => r.success);
                int failed = results.Count - successful;
                
                EditorGUILayout.LabelField($"Total: {results.Count} | Successful: {successful} | Failed: {failed}");
            }
            else
            {
                EditorGUILayout.LabelField("No conversion results to display.");
            }
            
            EditorGUILayout.Space();
        }
        
        private void DrawFilters()
        {
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("Filters", EditorStyles.boldLabel);
            
            EditorGUILayout.BeginHorizontal();
            
            // Search filter
            EditorGUILayout.LabelField("Search:", GUILayout.Width(50));
            searchFilter = EditorGUILayout.TextField(searchFilter);
            
            // Clear search
            if (GUILayout.Button("Clear", GUILayout.Width(50)))
            {
                searchFilter = "";
            }
            
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.BeginHorizontal();
            
            // Result type filter
            EditorGUILayout.LabelField("Filter:", GUILayout.Width(50));
            currentFilter = (ConversionResultFilter)EditorGUILayout.EnumPopup(currentFilter);
            
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.EndVertical();
        }
        
        private void DrawResultsList()
        {
            var filteredResults = GetFilteredResults();
            
            if (filteredResults.Count == 0)
            {
                EditorGUILayout.LabelField("No results match the current filter.", EditorStyles.centeredGreyMiniLabel);
                return;
            }
            
            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);
            
            foreach (var result in filteredResults)
            {
                DrawResultItem(result);
            }
            
            EditorGUILayout.EndScrollView();
        }
        
        private void DrawResultItem(ConversionResult result)
        {
            Color originalColor = GUI.backgroundColor;
            GUI.backgroundColor = result.success ? new Color(0.8f, 1f, 0.8f) : new Color(1f, 0.8f, 0.8f);
            
            EditorGUILayout.BeginVertical("box");
            GUI.backgroundColor = originalColor;
            
            // Header line
            EditorGUILayout.BeginHorizontal();
            
            // Status icon
            string statusIcon = result.success ? "✓" : "✗";
            Color statusColor = result.success ? Color.green : Color.red;
            
            var originalGUIColor = GUI.color;
            GUI.color = statusColor;
            EditorGUILayout.LabelField(statusIcon, GUILayout.Width(20));
            GUI.color = originalGUIColor;
            
            // Asset name and type
            EditorGUILayout.LabelField($"{Path.GetFileNameWithoutExtension(result.originalPath)} ({result.assetType})", 
                EditorStyles.boldLabel);
            
            // Conversion time
            EditorGUILayout.LabelField(result.conversionTime.ToString("HH:mm:ss"), 
                EditorStyles.miniLabel, GUILayout.Width(60));
            
            EditorGUILayout.EndHorizontal();
            
            // Original path
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("Original:", EditorStyles.miniLabel, GUILayout.Width(60));
            EditorGUILayout.SelectableLabel(result.originalPath, EditorStyles.miniLabel, GUILayout.Height(16));
            
            if (GUILayout.Button("Select", GUILayout.Width(50)))
            {
                var asset = AssetDatabase.LoadAssetAtPath<Object>(result.originalPath);
                if (asset != null)
                {
                    Selection.activeObject = asset;
                    EditorGUIUtility.PingObject(asset);
                }
            }
            
            EditorGUILayout.EndHorizontal();
            
            // Result-specific content
            if (result.success)
            {
                // Converted path
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField("Converted:", EditorStyles.miniLabel, GUILayout.Width(60));
                EditorGUILayout.SelectableLabel(result.convertedPath, EditorStyles.miniLabel, GUILayout.Height(16));
                
                if (GUILayout.Button("Select", GUILayout.Width(50)))
                {
                    var asset = AssetDatabase.LoadAssetAtPath<Object>(result.convertedPath);
                    if (asset != null)
                    {
                        Selection.activeObject = asset;
                        EditorGUIUtility.PingObject(asset);
                    }
                }
                
                EditorGUILayout.EndHorizontal();
            }
            else
            {
                // Error message
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField("Error:", EditorStyles.miniLabel, GUILayout.Width(60));
                EditorGUILayout.SelectableLabel(result.errorMessage, EditorStyles.miniLabel, GUILayout.Height(16));
                EditorGUILayout.EndHorizontal();
            }
            
            EditorGUILayout.EndVertical();
            EditorGUILayout.Space();
        }
        
        private void DrawFooter()
        {
            EditorGUILayout.BeginHorizontal();
            
            if (GUILayout.Button("Export to CSV"))
            {
                ExportToCSV();
            }
            
            if (GUILayout.Button("Copy to Clipboard"))
            {
                CopyToClipboard();
            }
            
            if (GUILayout.Button("Clear Results"))
            {
                if (EditorUtility.DisplayDialog("Clear Results", 
                    "Are you sure you want to clear all conversion results?", "Clear", "Cancel"))
                {
                    results.Clear();
                    Repaint();
                }
            }
            
            EditorGUILayout.EndHorizontal();
        }
        
        private List<ConversionResult> GetFilteredResults()
        {
            var filtered = results.AsEnumerable();
            
            // Apply search filter
            if (!string.IsNullOrEmpty(searchFilter))
            {
                filtered = filtered.Where(r => 
                    r.originalPath.ToLower().Contains(searchFilter.ToLower()) ||
                    r.assetType.ToLower().Contains(searchFilter.ToLower()) ||
                    (r.convertedPath != null && r.convertedPath.ToLower().Contains(searchFilter.ToLower())) ||
                    (r.errorMessage != null && r.errorMessage.ToLower().Contains(searchFilter.ToLower())));
            }
            
            // Apply result filter
            switch (currentFilter)
            {
                case ConversionResultFilter.Successful:
                    filtered = filtered.Where(r => r.success);
                    break;
                case ConversionResultFilter.Failed:
                    filtered = filtered.Where(r => !r.success);
                    break;
                case ConversionResultFilter.FMODKoreographySet:
                    filtered = filtered.Where(r => r.assetType == "FMODKoreographySet");
                    break;
                case ConversionResultFilter.Koreography:
                    filtered = filtered.Where(r => r.assetType == "Koreography");
                    break;
                case ConversionResultFilter.KoreographyTrack:
                    filtered = filtered.Where(r => r.assetType == "KoreographyTrack");
                    break;
            }
            
            return filtered.ToList();
        }
        
        private void ExportToCSV()
        {
            string path = EditorUtility.SaveFilePanel("Export Conversion Results", "", "ConversionResults.csv", "csv");
            if (string.IsNullOrEmpty(path)) return;
            
            try
            {
                using (var writer = new StreamWriter(path))
                {
                    // Header
                    writer.WriteLine("Original Path,Asset Type,Status,Converted Path,Error Message,Conversion Time");
                    
                    // Data
                    foreach (var result in GetFilteredResults())
                    {
                        writer.WriteLine($"\"{result.originalPath}\",\"{result.assetType}\"," +
                            $"\"{(result.success ? "Success" : "Failed")}\",\"{result.convertedPath ?? ""}\","  +
                            $"\"{result.errorMessage ?? ""}\",\"{result.conversionTime}\"");
                    }
                }
                
                Debug.Log($"[Cadance Conversion] Results exported to: {path}");
                EditorUtility.DisplayDialog("Export Complete", $"Results exported to:\n{path}", "OK");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[Cadance Conversion] Failed to export results: {ex.Message}");
                EditorUtility.DisplayDialog("Export Failed", $"Failed to export results:\n{ex.Message}", "OK");
            }
        }
        
        private void CopyToClipboard()
        {
            var filteredResults = GetFilteredResults();
            if (filteredResults.Count == 0)
            {
                EditorUtility.DisplayDialog("No Results", "No results to copy.", "OK");
                return;
            }
            
            var text = "Cadance Conversion Results\n";
            text += $"Generated: {System.DateTime.Now}\n";
            text += $"Total Results: {filteredResults.Count}\n\n";
            
            foreach (var result in filteredResults)
            {
                text += $"Original: {result.originalPath}\n";
                text += $"Type: {result.assetType}\n";
                text += $"Status: {(result.success ? "SUCCESS" : "FAILED")}\n";
                
                if (result.success)
                {
                    text += $"Converted: {result.convertedPath}\n";
                }
                else
                {
                    text += $"Error: {result.errorMessage}\n";
                }
                
                text += $"Time: {result.conversionTime}\n\n";
            }
            
            EditorGUIUtility.systemCopyBuffer = text;
            Debug.Log("[Cadance Conversion] Results copied to clipboard");
        }
    }
}
