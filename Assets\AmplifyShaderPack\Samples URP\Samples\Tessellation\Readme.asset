%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e78934177bebe0545ab566bb787b0282, type: 3}
  m_Name: Readme
  m_EditorClassIdentifier: 
  Icon: {fileID: 2800000, guid: 3e1a727c7f1bd204aae329f696de09b5, type: 3}
  Title: Tessellation
  Description:
    Heading: 
    Text: This sample performs edge-length tessellation with vertex displacement.
    LinkText: 
    Url: 
  PropertiesHeader:
    Heading: Properties
    Text: 
    LinkText: 
    Url: 
  Properties:
  - Heading: Edge length
    Text: Desired edge length in pixels. (controls tessellation factor)
    LinkText: 
    Url: 
  - Heading: Normal
    Text: Normal map texture to be applied to surface.
    LinkText: 
    Url: 
  - Heading: Metallic
    Text: Metallic texture to be applied to surface. (Only R channel used)
    LinkText: 
    Url: 
  - Heading: Heighmap
    Text: Texture containg height values used for displacent along surface normals.
    LinkText: 
    Url: 
  - Heading: Occlusion
    Text: Occlusion texture to be applied to surface. (Only R channel used)
    LinkText: 
    Url: 
  - Heading: Albedo
    Text: Albedo texture to be applied to surface.
    LinkText: 
    Url: 
  - Heading: Displacement
    Text: Value that controls strength of applied displacement.
    LinkText: 
    Url: 
  - Heading: Mask
    Text: Texture to control displacement area of effect.
    LinkText: 
    Url: 
  AdditionalProperties: []
  AdditionalScripts: []
  LoadedLayout: 0
  RPType: 3
