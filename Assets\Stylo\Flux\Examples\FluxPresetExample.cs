using UnityEngine;
#if UNITY_PIPELINE_URP
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
#endif
using Stylo.Flux;

namespace Stylo.Flux.Examples
{
    /// <summary>
    /// Example script demonstrating how to use Flux presets programmatically
    /// </summary>
    public class FluxPresetExample : MonoBehaviour
    {
        [Header("Preset Management")]
        [Tooltip("Array of Flux presets to switch between")]
        public FluxPreset[] presets;

#if UNITY_PIPELINE_URP
        [Tooltip("Volume component to apply presets to (URP)")]
        public Volume targetVolume;
#endif

        [Tooltip("Flux Effect component to apply presets to (Built-in RP)")]
        public UnityEngine.Object targetFluxEffect; // Use Object for flexibility

        [Header("Runtime Controls")]
        [Tooltip("Current preset index")]
        [Range(0, 10)]
        public int currentPresetIndex = 0;

        [Tooltip("Automatically cycle through presets")]
        public bool autoCycle = false;

        [Tooltip("Time between preset changes when auto-cycling")]
        public float cycleInterval = 3f;

        private int lastPresetIndex = -1;
        private float lastCycleTime;

        void Start()
        {
            // Apply initial preset
            if (presets != null && presets.Length > 0)
            {
                ApplyPreset(currentPresetIndex);
            }
        }

        void Update()
        {
            // Handle auto-cycling
            if (autoCycle && presets != null && presets.Length > 1)
            {
                if (Time.time - lastCycleTime >= cycleInterval)
                {
                    currentPresetIndex = (currentPresetIndex + 1) % presets.Length;
                    lastCycleTime = Time.time;
                }
            }

            // Apply preset if index changed
            if (currentPresetIndex != lastPresetIndex)
            {
                ApplyPreset(currentPresetIndex);
                lastPresetIndex = currentPresetIndex;
            }
        }

        /// <summary>
        /// Applies a preset by index
        /// </summary>
        public void ApplyPreset(int index)
        {
            if (presets == null || index < 0 || index >= presets.Length)
            {
                Debug.LogWarning($"Invalid preset index: {index}");
                return;
            }

            FluxPreset preset = presets[index];
            if (preset == null)
            {
                Debug.LogWarning($"Preset at index {index} is null");
                return;
            }

            // Try URP Volume first
#if UNITY_PIPELINE_URP
            if (targetVolume != null && targetVolume.profile != null)
            {
#if UNITY_EDITOR
                preset.ApplyToVolumeProfile(targetVolume.profile);
#else
                // Runtime application for URP
                if (targetVolume.profile.TryGet<Universal.FluxEffect>(out var urpFlux))
                {
                    preset.ApplyToURP(urpFlux);
                }
                else
                {
                    var newFlux = targetVolume.profile.Add<Universal.FluxEffect>();
                    preset.ApplyToURP(newFlux);
                }
#endif

                Debug.Log($"Applied preset '{preset.GetDisplayName()}' to Volume Profile");
                return;
            }
#endif

            // Try Built-in RP Flux Effect
            if (targetFluxEffect != null)
            {
                preset.ApplyToRuntime(targetFluxEffect);
                Debug.Log($"Applied preset '{preset.GetDisplayName()}' to Flux Effect component");
                return;
            }

            Debug.LogWarning("No target Volume or Flux Effect component found");
        }

        /// <summary>
        /// Applies a preset by name
        /// </summary>
        public void ApplyPresetByName(string presetName)
        {
            if (presets == null) return;

            for (int i = 0; i < presets.Length; i++)
            {
                if (presets[i] != null && presets[i].GetDisplayName() == presetName)
                {
                    currentPresetIndex = i;
                    ApplyPreset(i);
                    return;
                }
            }

            Debug.LogWarning($"Preset '{presetName}' not found in presets array");
        }

        /// <summary>
        /// Gets the currently applied preset
        /// </summary>
        public FluxPreset GetCurrentPreset()
        {
            if (presets != null && currentPresetIndex >= 0 && currentPresetIndex < presets.Length)
            {
                return presets[currentPresetIndex];
            }
            return null;
        }

        /// <summary>
        /// Cycles to the next preset
        /// </summary>
        public void NextPreset()
        {
            if (presets != null && presets.Length > 0)
            {
                currentPresetIndex = (currentPresetIndex + 1) % presets.Length;
            }
        }

        /// <summary>
        /// Cycles to the previous preset
        /// </summary>
        public void PreviousPreset()
        {
            if (presets != null && presets.Length > 0)
            {
                currentPresetIndex = (currentPresetIndex - 1 + presets.Length) % presets.Length;
            }
        }

        /// <summary>
        /// Randomizes the current preset
        /// </summary>
        public void RandomPreset()
        {
            if (presets != null && presets.Length > 0)
            {
                currentPresetIndex = Random.Range(0, presets.Length);
            }
        }

        // UI Button methods for easy integration
        public void OnNextPresetButton() => NextPreset();
        public void OnPreviousPresetButton() => PreviousPreset();
        public void OnRandomPresetButton() => RandomPreset();

        // Preset-specific button methods
        public void ApplyVHSPreset() => ApplyPresetByName("VHS Datamosh");
        public void ApplyDigitalPreset() => ApplyPresetByName("Digital Glitch");
        public void ApplySubtlePreset() => ApplyPresetByName("Subtle Compression");
        public void ApplySatellitePreset() => ApplyPresetByName("Satellite Feed Loss");

#if UNITY_EDITOR
        void OnValidate()
        {
            // Clamp preset index in editor
            if (presets != null)
            {
                currentPresetIndex = Mathf.Clamp(currentPresetIndex, 0, Mathf.Max(0, presets.Length - 1));
            }
        }
#endif
    }
}

#if UNITY_EDITOR
namespace Stylo.Flux.Examples.Editor
{
    using UnityEditor;
    
    [CustomEditor(typeof(FluxPresetExample))]
    public class FluxPresetExampleEditor : UnityEditor.Editor
    {
        public override void OnInspectorGUI()
        {
            DrawDefaultInspector();
            
            FluxPresetExample example = (FluxPresetExample)target;
            
            EditorGUILayout.Space(10);
            EditorGUILayout.LabelField("Preset Controls", EditorStyles.boldLabel);
            
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("Previous"))
            {
                example.PreviousPreset();
            }
            if (GUILayout.Button("Next"))
            {
                example.NextPreset();
            }
            if (GUILayout.Button("Random"))
            {
                example.RandomPreset();
            }
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.Space(5);
            
            // Show current preset info
            var currentPreset = example.GetCurrentPreset();
            if (currentPreset != null)
            {
                EditorGUILayout.HelpBox($"Current: {currentPreset.GetDisplayName()}\n{currentPreset.description}", MessageType.Info);
            }
            else
            {
                EditorGUILayout.HelpBox("No preset selected or presets array is empty", MessageType.Warning);
            }
        }
    }
}
#endif
