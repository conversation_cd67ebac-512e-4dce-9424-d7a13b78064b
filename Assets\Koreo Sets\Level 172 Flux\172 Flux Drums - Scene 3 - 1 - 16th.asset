%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -1223223985, guid: aa81bbec85a3f423193272a7c6154986, type: 3}
  m_Name: 172 Flux Drums - Scene 3 - 1 - 16th
  m_EditorClassIdentifier: 
  mEventID: 16th
  mEventList:
  - mStartSample: 0
    mEndSample: 0
  - mStartSample: 7691
    mEndSample: 7691
  - mStartSample: 15383
    mEndSample: 15383
  - mStartSample: 23074
    mEndSample: 23074
  - mStartSample: 30767
    mEndSample: 30767
  - mStartSample: 38458
    mEndSample: 38458
  - mStartSample: 46151
    mEndSample: 46151
  - mStartSample: 53842
    mEndSample: 53842
  - mStartSample: 61534
    mEndSample: 61534
  - mStartSample: 69225
    mEndSample: 69225
  - mStartSample: 76917
    mEndSample: 76917
  - mStartSample: 84608
    mEndSample: 84608
  - mStartSample: 92301
    mEndSample: 92301
  - mStartSample: 99992
    mEndSample: 99992
  - mStartSample: 107685
    mEndSample: 107685
  - mStartSample: 115376
    mEndSample: 115376
  - mStartSample: 123069
    mEndSample: 123069
  - mStartSample: 130760
    mEndSample: 130760
  - mStartSample: 138452
    mEndSample: 138452
  - mStartSample: 146143
    mEndSample: 146143
  - mStartSample: 153836
    mEndSample: 153836
  - mStartSample: 161527
    mEndSample: 161527
  - mStartSample: 169220
    mEndSample: 169220
  - mStartSample: 176911
    mEndSample: 176911
  - mStartSample: 184604
    mEndSample: 184604
  - mStartSample: 192295
    mEndSample: 192295
  - mStartSample: 199987
    mEndSample: 199987
  - mStartSample: 207678
    mEndSample: 207678
  - mStartSample: 215371
    mEndSample: 215371
  - mStartSample: 223062
    mEndSample: 223062
  - mStartSample: 230755
    mEndSample: 230755
  - mStartSample: 238446
    mEndSample: 238446
  - mStartSample: 246139
    mEndSample: 246139
  - mStartSample: 253830
    mEndSample: 253830
  - mStartSample: 261522
    mEndSample: 261522
  - mStartSample: 269213
    mEndSample: 269213
  - mStartSample: 276906
    mEndSample: 276906
  - mStartSample: 284597
    mEndSample: 284597
  - mStartSample: 292290
    mEndSample: 292290
  - mStartSample: 299981
    mEndSample: 299981
  - mStartSample: 307673
    mEndSample: 307673
  - mStartSample: 315364
    mEndSample: 315364
  - mStartSample: 323056
    mEndSample: 323056
  - mStartSample: 330747
    mEndSample: 330747
  - mStartSample: 338440
    mEndSample: 338440
  - mStartSample: 346131
    mEndSample: 346131
  - mStartSample: 353824
    mEndSample: 353824
  - mStartSample: 361515
    mEndSample: 361515
  - mStartSample: 369208
    mEndSample: 369208
  - mStartSample: 376899
    mEndSample: 376899
  - mStartSample: 384591
    mEndSample: 384591
  - mStartSample: 392282
    mEndSample: 392282
  - mStartSample: 399975
    mEndSample: 399975
  - mStartSample: 407666
    mEndSample: 407666
  - mStartSample: 415359
    mEndSample: 415359
  - mStartSample: 423050
    mEndSample: 423050
  - mStartSample: 430743
    mEndSample: 430743
  - mStartSample: 438434
    mEndSample: 438434
  - mStartSample: 446126
    mEndSample: 446126
  - mStartSample: 453817
    mEndSample: 453817
  - mStartSample: 461510
    mEndSample: 461510
  - mStartSample: 469201
    mEndSample: 469201
  - mStartSample: 476894
    mEndSample: 476894
  - mStartSample: 484585
    mEndSample: 484585
  _SerializedPayloadTypes: []
  _AssetPayloads: []
  _AssetPayloadIdxs: 
  _ColorPayloads: []
  _ColorPayloadIdxs: 
  _CurvePayloads: []
  _CurvePayloadIdxs: 
  _FloatPayloads: []
  _FloatPayloadIdxs: 
  _GradientPayloads: []
  _GradientPayloadIdxs: 
  _IntPayloads: []
  _IntPayloadIdxs: 
  _SpectrumPayloads: []
  _SpectrumPayloadIdxs: 
  _TextPayloads: []
  _TextPayloadIdxs: 
