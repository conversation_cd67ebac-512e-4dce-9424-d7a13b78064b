%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e78934177bebe0545ab566bb787b0282, type: 3}
  m_Name: Readme
  m_EditorClassIdentifier: 
  Icon: {fileID: 2800000, guid: 6c3fd0335dadbdd46b8d084354c0ea4f, type: 3}
  Title: Two Sided With Face
  Description:
    Heading: 
    Text: This sample sets an onbject rendering to both front and back faces with
      separate properties for each one.
    LinkText: 
    Url: 
  PropertiesHeader:
    Heading: Properties
    Text: 
    LinkText: 
    Url: 
  Properties:
  - Heading: Mask Clip Value
    Text: Value to perform alpha clip.
    LinkText: 
    Url: 
  - Heading: Mask
    Text: Texture containing values to clip off. ( Only A channel used)
    LinkText: 
    Url: 
  - Heading: Front Albedo
    Text: Albedo texture to apply on front faces.
    LinkText: 
    Url: 
  - Heading: Front Normal Map
    Text: Normals texture to apply on front faces.
    LinkText: 
    Url: 
  - Heading: Front Color
    Text: Tint color value texture to apply on front faces.
    LinkText: 
    Url: 
  - Heading: Back Albedo
    Text: Albedo texture to apply on back faces.
    LinkText: 
    Url: 
  - Heading: Back Normal Map
    Text: Normals texture to apply on back faces.
    LinkText: 
    Url: 
  - Heading: Back Color
    Text: Tint color value texture to apply on back faces.
    LinkText: 
    Url: 
  AdditionalProperties: []
  AdditionalScripts: []
  LoadedLayout: 0
  RPType: 3
