%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-7255415435859457617
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0353a89b1f911e48b9e16bdc9f2e058, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 9
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: RChrome
  m_Shader: {fileID: 4800000, guid: a8b5a5aaaee6dfc4191a3c36e4ba1354, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords:
  - _NOISEMIX_OVERLAY
  m_InvalidKeywords:
  - _ENABLEGLITCH_ON
  - _GLITCHMODE_SPLIT
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses:
  - MOTIONVECTORS
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BaseMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AddPrecomputedVelocity: 0
    - _AlphaClip: 0
    - _AlphaToMask: 0
    - _Blend: 0
    - _BlendModePreserveSpecular: 1
    - _BumpScale: 1
    - _ChromaticAberration: 0.566
    - _ClearCoatMask: 0
    - _ClearCoatSmoothness: 0
    - _Cull: 2
    - _Cutoff: 0.5
    - _DetailAlbedoMapScale: 1
    - _DetailNormalMapScale: 1
    - _DstBlend: 0
    - _DstBlendAlpha: 0
    - _EdgeSharpness: 6.83
    - _EnableGlitch: 1
    - _EnvironmentReflections: 1
    - _FresnelIntensity: 2.24
    - _FresnelPower: 3.53
    - _GlitchAmplitude: 1
    - _GlitchFrequency: 6.55
    - _GlitchGridSizeX: 7.56
    - _GlitchGridSizeY: 2.71
    - _GlitchGridSizeZ: 8.61
    - _GlitchIntensity: 0.399
    - _GlitchMode: 2
    - _GlitchSpeed: 1.38
    - _GlitchSplitValue: 0.547
    - _GlossMapScale: 0
    - _Glossiness: 0
    - _GlossyReflections: 0
    - _GlowFalloff: 0.89
    - _GlowRimPower: 6.12
    - _GlowStrength: 0.17
    - _GlowThreshold: 0.121
    - _InstancingToggle: 1
    - _Metallic: 0
    - _MetallicStrength: 0.491
    - _NoiseDiffuseStrength: 0.294
    - _NoiseMix: 2
    - _NoiseOpacity: 0.351
    - _NoiseReflectionStrength: 0.807
    - _NoiseScale: 668
    - _NoiseSpeed: 2.43
    - _NoiseStrength: 1
    - _OcclusionStrength: 1
    - _Parallax: 0.005
    - _QueueOffset: 0
    - _ReceiveShadows: 1
    - _ReflectionIntensity: 1.375
    - _SecondaryNoiseScale: 730
    - _SecondaryNoiseStrength: 0.247
    - _Smoothness: 1
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _SrcBlendAlpha: 1
    - _StreakIntensity: 0.452
    - _Surface: 0
    - _UseSecondaryNoise: 0
    - _WorkflowMode: 1
    - _ZWrite: 1
    m_Colors:
    - _BaseColor: {r: 0.6886792, g: 0, b: 0.32181188, a: 1}
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _EmissionColor: {r: 0.26415092, g: 0, b: 0.21235666, a: 1}
    - _GlitchOffset: {r: 1.45, g: -4.37, b: -0.81, a: -1.76}
    - _GlowColor: {r: 14.754424, g: 12.875322, b: 12.875322, a: 1}
    - _SpecColor: {r: 0.19999996, g: 0.19999996, b: 0.19999996, a: 1}
  m_BuildTextureStacks: []
  m_AllowLocking: 1
