# BFI Quick Start Guide

## ✅ Installation Complete

Your BFI (Black Frame Insertion) system has been successfully created under `Assets/Stylo/BFI/` with all necessary components:

- **✅ BFI Volume Component** - Complete with all parameters
- **✅ BFI Renderer Feature** - Unity 6 Render Graph compatible
- **✅ BFI Shader** - Custom frame insertion logic with proper render graph handling
- **✅ Preset System** - Easy configuration management
- **✅ Validation Tools** - Automated testing and debugging
- **✅ Documentation** - Comprehensive guides and references
- **✅ Render Graph Fixes** - All texture handling issues resolved

## 🚀 Next Steps (5 minutes to get running)

### 1. Add BFI to Your Render Pipeline

**Option A: Automated Setup (Recommended)**

1. Go to `Stylo → BFI → Add to Current Renderer`
2. This will automatically add BFI to your BT Render Pipeline
3. Check console for confirmation message

**Option B: Manual Setup**

1. Open `Assets/BT Render Pipeline/BT Render 1.asset`
2. In Inspector, scroll to "Renderer Features"
3. Click "+" → Select "BFI Renderer Feature"
4. Ensure it's set to "Active"

**Option C: Complete Scene Setup**

1. Go to `Stylo → BFI → Setup Complete BFI Scene`
2. This will set up everything automatically

### 2. Create BFI Volume Profile

**Option A: Automated Creation**

1. Go to `Stylo → BFI → Create BFI Volume Profile`
2. Choose save location when prompted
3. Profile will be created with optimal default settings

**Option B: Manual Creation**

1. Right-click in Project → Create → Rendering → Volume Profile
2. Name it "BFI_Profile"
3. Select it → Add Override → "Stylo - BFI Effect"
4. Configure initial settings:
   - **Mode**: 2x Frame Rate (good starting point)
   - **Intensity**: 1.0
   - **Brightness Compensation**: 1.5

### 3. Apply to Scene

1. Create GameObject → Volume → Global Volume
2. Assign your BFI_Profile to the "Profile" field
3. Enter Play Mode to test

### 4. Verify Setup

1. Open `Stylo → BFI → Validation Tool`
2. Check all items show green checkmarks
3. Enable "Debug Visualization" in BFI Effect
4. Look for timing bars at bottom of screen

## 🎯 Recommended Settings by Display

| Display Type  | BFI Mode      | Brightness Comp | Notes           |
| ------------- | ------------- | --------------- | --------------- |
| 60Hz Monitor  | 2x Frame Rate | 1.8             | Limited benefit |
| 120Hz Monitor | 2x Frame Rate | 1.8             | Full benefit    |
| 144Hz Monitor | 2x Frame Rate | 1.8             | Excellent       |
| 240Hz Monitor | 4x Frame Rate | 2.8             | Maximum clarity |

## 🔧 Key Parameters Explained

### Master Controls

- **Mode**: Frame rate multiplier (Off/2x/3x/4x)
- **Intensity**: Overall effect strength (0-1)

### Brightness & Compensation

- **Brightness Compensation**: Auto-brightness boost (1-3x)
- **Manual Brightness**: Additional brightness control

### Flicker Reduction

- **Flicker Reduction Mode**: None/Low/Medium/High
- **Temporal Smoothing**: Reduces flicker (0-1)

### Motion Adaptive (Advanced)

- **Motion Adaptive**: Enable motion-based intensity
- **Motion Sensitivity**: How responsive to motion

## 🐛 Troubleshooting

### BFI Not Visible

- ✅ Check BFI Renderer Feature is active in BT Render 1
- ✅ Verify BFI Effect intensity > 0
- ✅ Ensure Mode is not "Off"
- ✅ Confirm Volume Profile applied to scene

### Performance Issues

- 🔧 Lower BFI Mode (4x → 2x)
- 🔧 Disable Motion Adaptive
- 🔧 Reduce Flicker Reduction

### Too Much Flicker

- 🔧 Increase Flicker Reduction Mode
- 🔧 Adjust Temporal Smoothing
- 🔧 Consider lower BFI mode

### Too Dark

- 🔧 Increase Brightness Compensation
- 🔧 Adjust Manual Brightness

## 📚 Documentation

- **Integration Guide**: `Documentation/BFI_Integration_Guide.md`
- **Testing Guide**: `Documentation/BFI_Testing_Guide.md`
- **Main README**: `README.md`

## 🛠️ Tools Available

- **Validation Tool**: `Stylo → BFI → Validation Tool`
- **Performance Monitor**: Add `BFIPerformanceMonitor` component
- **Preset Manager**: Automatic preset creation and management

## 🎮 What BFI Does

Black Frame Insertion improves motion clarity by:

1. **Inserting black frames** between rendered frames
2. **Reducing motion blur** through strobing effect
3. **Maintaining smooth motion** with proper timing
4. **Compensating brightness** automatically

Perfect for:

- ✅ Competitive gaming (better motion tracking)
- ✅ Fast-paced action games
- ✅ High refresh rate displays
- ✅ Motion-sensitive content

## 🚨 Important Notes

- **Display Compatibility**: Works best with 120Hz+ displays
- **Performance**: Minimal GPU overhead, mainly timing-based
- **Eye Comfort**: Use Flicker Reduction for sensitive users
- **Integration**: Fully compatible with existing Stylo effects

## 🎉 You're Ready!

Your BFI system is now ready to use. Start with the recommended settings above and adjust based on your display and preferences. The validation tool will help ensure everything is working correctly.

For advanced configuration and troubleshooting, refer to the comprehensive documentation in the `Documentation/` folder.
