﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2013/05/nuspec.xsd">
  <metadata>
    <id>ZLinq</id>
    <version>1.4.10</version>
    <authors>Cysharp</authors>
    <license type="expression">MIT</license>
    <licenseUrl>https://licenses.nuget.org/MIT</licenseUrl>
    <icon>Icon.png</icon>
    <readme>README.md</readme>
    <projectUrl>https://github.com/Cysharp/ZLinq</projectUrl>
    <description>Zero allocation LINQ with LINQ to Span, LINQ to SIMD, and LINQ to Tree (FileSystem, JSON, GameObject, etc.) for all .NET platforms and Unity.</description>
    <copyright>© Cysharp, Inc.</copyright>
    <tags>linq</tags>
    <repository type="git" url="https://github.com/Cysharp/ZLinq" branch="refs/heads/main" commit="7157f8f60c9e93cd5bbeb2e7f600089e0c489232" />
    <dependencies>
      <group targetFramework="net8.0" />
      <group targetFramework="net9.0" />
      <group targetFramework=".NETStandard2.0">
        <dependency id="Microsoft.Bcl.Memory" version="9.0.5" exclude="Build,Analyzers" />
        <dependency id="System.Buffers" version="4.6.1" exclude="Build,Analyzers" />
        <dependency id="System.Memory" version="4.6.3" exclude="Build,Analyzers" />
        <dependency id="System.Runtime.CompilerServices.Unsafe" version="6.1.2" exclude="Build,Analyzers" />
      </group>
      <group targetFramework=".NETStandard2.1">
        <dependency id="System.Runtime.CompilerServices.Unsafe" version="6.1.2" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
</package>