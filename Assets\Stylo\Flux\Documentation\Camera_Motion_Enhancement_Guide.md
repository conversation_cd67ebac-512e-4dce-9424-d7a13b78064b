# Flux Camera Motion Enhancement Guide

## Overview

The Flux datamosh effect has been enhanced with dedicated camera motion sensitivity controls that make camera movement dramatically affect the visual corruption. This addresses the issue where camera motion wasn't properly influencing the datamosh effect in the Render Graph implementation.

## New Camera Motion Parameters

### Camera Motion Amplification
- **Range**: 0 - 10
- **Default**: 1.0
- **Description**: Amplifies camera motion effects. Higher values make camera movement more dramatically affect the datamosh. Set to 0 to disable camera motion response entirely.

### Camera Motion Threshold  
- **Range**: 0 - 0.1
- **Default**: 0.001
- **Description**: Minimum camera motion required to trigger datamosh effects. Lower values make the effect more sensitive to small camera movements.

### Camera Motion Influence
- **Range**: 0 - 10  
- **Default**: 2.0
- **Description**: How much camera motion contributes to reprojection chance, separate from object motion. Works alongside the existing Length Influence parameter.

### Camera Motion Smoothing
- **Range**: 0 - 1
- **Default**: 0.1
- **Description**: Smoothing factor for camera motion detection. Higher values create smoother motion response but less immediate reaction to camera changes.

## How It Works

### Enhanced Motion Vector Processing
The system now:
1. **Uses actual screen resolution** instead of hardcoded 1920x1080 values for motion vector scaling
2. **Detects camera motion magnitude** using proper resolution-aware calculations
3. **Applies amplification** when motion exceeds the threshold
4. **Smooths motion response** to prevent jittery artifacts
5. **Contributes to reprojection chance** as a separate component from object motion

### Integration with Existing System
The camera motion system works alongside existing parameters:
- **Base Noise**: Still provides random reprojection chance
- **Length Influence**: Still affects object motion sensitivity  
- **Camera Motion Influence**: Adds camera-specific motion contribution
- **All advanced features** (corruption, feedback, etc.) still work normally

## Usage Recommendations

### For Subtle Camera Motion Effects
```
Camera Motion Amplification: 1.5 - 3.0
Camera Motion Threshold: 0.001 - 0.005
Camera Motion Influence: 1.0 - 2.0
Camera Motion Smoothing: 0.2 - 0.5
```

### For Dramatic Camera Motion Effects
```
Camera Motion Amplification: 5.0 - 10.0
Camera Motion Threshold: 0.001 - 0.002
Camera Motion Influence: 3.0 - 5.0
Camera Motion Smoothing: 0.0 - 0.2
```

### For Smooth Cinematic Effects
```
Camera Motion Amplification: 2.0 - 4.0
Camera Motion Threshold: 0.002 - 0.01
Camera Motion Influence: 1.5 - 3.0
Camera Motion Smoothing: 0.3 - 0.7
```

## Technical Implementation

### Key Improvements
1. **Fixed hardcoded resolution scaling** - Now uses `_Screen_TexelSize.zw` for proper resolution adaptation
2. **Added camera motion detection** - Calculates motion magnitude using actual screen dimensions
3. **Implemented motion amplification** - Applies configurable amplification when motion exceeds threshold
4. **Added motion smoothing** - Prevents jittery artifacts with configurable smoothing
5. **Enhanced reprojection logic** - Camera motion contributes separately to corruption chance

### Shader Changes
- Added 4 new shader parameters for camera motion control
- Enhanced motion vector processing in `Shared.cginc`
- Improved reprojection chance calculation with camera motion contribution
- Fixed resolution scaling to use actual screen dimensions

### Volume Integration
- New parameters appear in the "Camera Motion Sensitivity" section
- Fully integrated with Unity's Volume system
- Supports parameter overrides and blending
- Compatible with existing Flux presets (defaults to original behavior)

## Troubleshooting

### Camera Motion Not Responding
1. Check that **Camera Motion Amplification > 0**
2. Verify **Camera Motion Threshold** isn't too high
3. Ensure **Base Noise** or **Length Influence** > 0 (required for reprojection)
4. Check that motion vectors are being generated (enable **Visualize Motion Vectors** to debug)

### Effect Too Jittery
1. Increase **Camera Motion Smoothing** (0.3 - 0.7)
2. Reduce **Camera Motion Amplification**
3. Increase **Camera Motion Threshold** slightly

### Effect Too Subtle
1. Increase **Camera Motion Amplification** (3.0 - 8.0)
2. Increase **Camera Motion Influence** (3.0 - 5.0)
3. Decrease **Camera Motion Threshold** (0.001 - 0.002)
4. Decrease **Camera Motion Smoothing** (0.0 - 0.2)

## Compatibility

- **Unity 6**: Full support with Render Graph
- **Unity 2022.3+**: Full support with legacy rendering
- **All render pipelines**: URP (primary), Built-in (legacy)
- **All platforms**: Tested on Windows, should work on all Unity-supported platforms
- **Existing presets**: Fully backward compatible (new parameters default to safe values)
