fileFormatVersion: 2
guid: 6b417d114ba28c84aafa17634078e33c
ModelImporter:
  serializedVersion: 21300
  internalIDToNameTable: []
  externalObjects:
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Glow
    second: {fileID: 2100000, guid: 691f9252e26d3c84d86e4b80e9ef07f9, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: skeleton
    second: {fileID: 2100000, guid: 3af9a29e73f330045a88f5cbcabbdc2f, type: 2}
  materials:
    materialImportMode: 2
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    removeConstantScaleCurves: 1
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 0.6
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    nodeNameCollisionStrategy: 1
    fileIdsGeneration: 2
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    bakeAxisConversion: 0
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    optimizeBones: 1
    meshOptimizationFlags: -1
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVMarginMethod: 1
    secondaryUVMinLightmapResolution: 40
    secondaryUVMinObjectScale: 1
    secondaryUVPackMargin: 4
    useFileScale: 1
    strictVertexDataChecks: 0
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 0
  humanDescription:
    serializedVersion: 3
    human:
    - boneName: hips
      humanName: Hips
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: upperleg.l
      humanName: LeftUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: upperleg.r
      humanName: RightUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: lowerleg.l
      humanName: LeftLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: lowerleg.r
      humanName: RightLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: foot.l
      humanName: LeftFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: foot.r
      humanName: RightFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: spine
      humanName: Spine
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: head
      humanName: Head
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: upperarm.l
      humanName: LeftUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: upperarm.r
      humanName: RightUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: lowerarm.l
      humanName: LeftLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: lowerarm.r
      humanName: RightLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: wrist.l
      humanName: LeftHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: wrist.r
      humanName: RightHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: toes.l
      humanName: LeftToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: toes.r
      humanName: RightToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    skeleton:
    - name: Skeleton_Rogue(Clone)
      parentName: 
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Rig
      parentName: Skeleton_Rogue(Clone)
      position: {x: -0, y: 0, z: 0}
      rotation: {x: 0.000000021855694, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: root
      parentName: Rig
      position: {x: -0, y: 0, z: 0}
      rotation: {x: -0.000000021855692, y: -0.000000119209275, z: 2.6054013e-15, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: hips
      parentName: root
      position: {x: -0, y: 0.24339807, z: 0}
      rotation: {x: -5.3290705e-15, y: 0.000000119209275, z: 6.3527476e-22, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: spine
      parentName: hips
      position: {x: 1.42e-43, y: 0.115186505, z: 0}
      rotation: {x: 5.3290705e-15, y: -7.1054274e-15, z: -1.3883952e-28, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: chest
      parentName: spine
      position: {x: 2.217666e-29, y: 0.22499278, z: 1.0658141e-15}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 0.99999994, z: 1}
    - name: Skeleton_Rogue_Cape
      parentName: chest
      position: {x: 0.0000011444092, y: 0.14594662, z: -0.00000008046627}
      rotation: {x: 0.000000021855692, y: 7.1054274e-15, z: -1.5529402e-22, w: 1}
      scale: {x: 1, y: 1.0000001, z: 1}
    - name: upperarm.l
      parentName: chest
      position: {x: -0.12720442, y: 0.08047936, z: 0.000000050927046}
      rotation: {x: -0.514122, y: 0.48546723, z: 0.48546833, w: 0.51412123}
      scale: {x: 0.99999934, y: 0.99999934, z: 0.9999998}
    - name: lowerarm.l
      parentName: upperarm.l
      position: {x: 1.9016257e-10, y: 0.14513844, z: -0.00000006938378}
      rotation: {x: 4.8521726e-10, y: 0.0000000594392, z: 0.055285525, w: 0.9984706}
      scale: {x: 1.0000001, y: 0.99999994, z: 1}
    - name: wrist.l
      parentName: lowerarm.l
      position: {x: -0.0000000017830729, y: 0.1560263, z: 9.857521e-10}
      rotation: {x: 0.0000000010654014, y: 2.6744193e-10, z: -0.026658101, w: 0.99964464}
      scale: {x: 0.99999994, y: 1, z: 1}
    - name: hand.l
      parentName: wrist.l
      position: {x: -7.2475364e-13, y: 0.04429554, z: 0.00000007161441}
      rotation: {x: 5.9566346e-10, y: -0.00000013300505, z: 0.000005455195, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: handslot.l
      parentName: hand.l
      position: {x: -5.051575e-10, y: 0.05767515, z: -0.034499932}
      rotation: {x: -0.0000000119789805, y: -0.000000015198488, z: 0.7071068, w: 0.7071067}
      scale: {x: 1, y: 1, z: 1}
    - name: upperarm.r
      parentName: chest
      position: {x: 0.12720442, y: 0.08047936, z: 0.000000050927046}
      rotation: {x: -0.5141217, y: -0.48546743, z: -0.48546806, w: 0.5141216}
      scale: {x: 0.99999934, y: 0.99999934, z: 0.9999998}
    - name: lowerarm.r
      parentName: upperarm.r
      position: {x: -1.2940576e-10, y: 0.14513844, z: -0.000000070111824}
      rotation: {x: 4.8521726e-10, y: -0.0000000594392, z: -0.055285525, w: 0.9984706}
      scale: {x: 1.0000001, y: 0.99999994, z: 1}
    - name: wrist.r
      parentName: lowerarm.r
      position: {x: 0.0000000018309919, y: 0.1560263, z: 9.857514e-10}
      rotation: {x: 0.0000000010654018, y: -2.6743324e-10, z: 0.026658101, w: 0.99964464}
      scale: {x: 0.99999994, y: 1, z: 1}
    - name: hand.r
      parentName: wrist.r
      position: {x: 9.46443e-13, y: 0.04429554, z: 0.000000071614366}
      rotation: {x: 5.9541505e-10, y: 0.0000000892218, z: -0.000005455195, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: handslot.r
      parentName: hand.r
      position: {x: 5.0512344e-10, y: 0.05767515, z: -0.034499932}
      rotation: {x: -0.000000011979, y: 0.000000015198468, z: -0.7071068, w: 0.7071067}
      scale: {x: 1, y: 1, z: 1}
    - name: head
      parentName: chest
      position: {x: 7.030534e-30, y: 0.16127795, z: -3.31e-43}
      rotation: {x: -3.5527137e-15, y: 0, z: 5.0487104e-29, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Skeleton_Rogue_Hood
      parentName: head
      position: {x: -0.0000011444092, y: -0.0153312925, z: -0.00000008270144}
      rotation: {x: 0.000000021855692, y: 7.1054274e-15, z: -1.5529402e-22, w: 1}
      scale: {x: 1, y: 1.0000001, z: 1}
    - name: upperleg.l
      parentName: hips
      position: {x: -0.10256704, y: 0.06815236, z: 0.000000008353964}
      rotation: {x: 0.9998491, y: 0.00000007132798, z: 0.00000001204276, w: 0.017373588}
      scale: {x: 1, y: 0.99999976, z: 1.0000011}
    - name: lowerleg.l
      parentName: upperleg.l
      position: {x: -0.000000010570935, y: 0.13624647, z: -0.000000001605871}
      rotation: {x: 0.10622619, y: -0.000000068652874, z: 0.00000007377092, w: 0.994342}
      scale: {x: 1, y: 0.9999998, z: 0.99999964}
    - name: foot.l
      parentName: lowerleg.l
      position: {x: 0.0000000019028403, y: 0.08966224, z: 0.0000000016394321}
      rotation: {x: -0.45523942, y: -0.000000016196793, z: 0.00000003435627, w: 0.8903691}
      scale: {x: 1, y: 1.0000008, z: 0.9999996}
    - name: toes.l
      parentName: foot.l
      position: {x: -6.328378e-11, y: 0.09939003, z: 0.00000002287987}
      rotation: {x: -0.0000000625307, y: 0.9203549, z: -0.39108422, w: -0.00000011811018}
      scale: {x: 1, y: 0.9999977, z: 1.0000033}
    - name: upperleg.r
      parentName: hips
      position: {x: 0.10256704, y: 0.06815236, z: 0.000000008353964}
      rotation: {x: 0.9998491, y: -0.00000007426609, z: 0.00000003172626, w: 0.017373588}
      scale: {x: 1, y: 0.99999976, z: 1.0000011}
    - name: lowerleg.r
      parentName: upperleg.r
      position: {x: 0.000000010222002, y: 0.13624647, z: -0.0000000010210948}
      rotation: {x: 0.10622622, y: 0.000000068652895, z: -0.00000007377092, w: 0.994342}
      scale: {x: 1, y: 0.9999999, z: 0.9999997}
    - name: foot.r
      parentName: lowerleg.r
      position: {x: -0.0000000032594711, y: 0.08966217, z: 5.057267e-10}
      rotation: {x: -0.45523942, y: -0.00000015576714, z: 0.000000017729546, w: 0.8903691}
      scale: {x: 1, y: 1.0000005, z: 0.999999}
    - name: toes.r
      parentName: foot.r
      position: {x: 0.00000001148823, y: 0.09939003, z: -0.0000000061149636}
      rotation: {x: -0.000000022296454, y: 0.9203549, z: -0.39108422, w: -0.00000013520682}
      scale: {x: 1, y: 0.9999976, z: 1.0000035}
    - name: kneeIK.l
      parentName: root
      position: {x: -0.10256694, y: 0.17538616, z: 0.34548748}
      rotation: {x: 0.7071068, y: 0.00000022930232, z: 0.000000046055135, w: 0.7071067}
      scale: {x: 1, y: 0.99999994, z: 0.99999994}
    - name: control-toe-roll.l
      parentName: root
      position: {x: -0.10256699, y: 0.015594158, z: 0.14747363}
      rotation: {x: -0.7071067, y: 0.000000070528486, z: 0.00000007052848, w: 0.7071068}
      scale: {x: 1, y: 1, z: 1}
    - name: control-heel-roll.l
      parentName: control-toe-roll.l
      position: {x: -0.000000011674866, y: 0.2176996, z: -0.000000011757941}
      rotation: {x: -7.1054375e-15, y: -1.00916035e-22, z: 1, w: -6.123234e-17}
      scale: {x: 1, y: 1, z: 1}
    - name: control-foot-roll.l
      parentName: control-heel-roll.l
      position: {x: -0.0000000022517668, y: 0.1280617, z: 0.0000000021230022}
      rotation: {x: 0.00000012957192, y: 0.39108437, z: 0.92035484, w: 0.0000000896225}
      scale: {x: 1, y: 1.0000002, z: 1.0000002}
    - name: heelIK.l
      parentName: control-foot-roll.l
      position: {x: 0.000000012727464, y: 0.09939004, z: -2.6085376e-10}
      rotation: {x: -0.39108443, y: 0.00000006885003, z: 0.00000006562883, w: 0.92035484}
      scale: {x: 1, y: 1, z: 1.0000001}
    - name: IK-foot.l
      parentName: control-foot-roll.l
      position: {x: 0.000000012727464, y: 0.09939004, z: -2.6085376e-10}
      rotation: {x: 1, y: 4.6537017e-14, z: -0.00000019470718, w: 0.00000013315805}
      scale: {x: 1, y: 1, z: 1}
    - name: IK-toe.l
      parentName: control-heel-roll.l
      position: {x: 0.0000000027341696, y: -0.0000000016543418, z: -0.00000001175794}
      rotation: {x: 0.00000005860104, y: 1.2081922e-15, z: 0.000000019466935, w: 1}
      scale: {x: 1, y: 0.99999994, z: 1}
    - name: kneeIK.r
      parentName: root
      position: {x: 0.10256711, y: 0.17538616, z: 0.34548742}
      rotation: {x: 0.7071068, y: 0.000000038238493, z: -0.00000014500868, w: 0.7071067}
      scale: {x: 1, y: 0.99999994, z: 0.99999994}
    - name: control-toe-roll.r
      parentName: root
      position: {x: 0.10256706, y: 0.015594158, z: 0.14747359}
      rotation: {x: -0.7071067, y: 0.000000070528486, z: 0.00000007052848, w: 0.7071068}
      scale: {x: 1, y: 1, z: 1}
    - name: control-heel-roll.r
      parentName: control-toe-roll.r
      position: {x: -0.000000011674866, y: 0.2176996, z: -0.0000000117579395}
      rotation: {x: -7.1054375e-15, y: -1.00916035e-22, z: 1, w: -6.123234e-17}
      scale: {x: 1, y: 1, z: 1}
    - name: control-foot-roll.r
      parentName: control-heel-roll.r
      position: {x: -0.0000000022517672, y: 0.1280617, z: 0.000000002123002}
      rotation: {x: 0.00000012957192, y: 0.39108437, z: 0.92035484, w: 0.0000000896225}
      scale: {x: 1, y: 1.0000002, z: 1.0000002}
    - name: heelIK.r
      parentName: control-foot-roll.r
      position: {x: -0.000000005153928, y: 0.09939004, z: 8.196473e-10}
      rotation: {x: -0.39108443, y: 0.000000068850035, z: 0.00000006562883, w: 0.92035484}
      scale: {x: 1, y: 1, z: 1.0000001}
    - name: IK-foot.r
      parentName: control-foot-roll.r
      position: {x: -0.000000005153928, y: 0.09939004, z: 8.196473e-10}
      rotation: {x: 1, y: 4.6537017e-14, z: -0.00000019470718, w: 0.00000013315805}
      scale: {x: 1, y: 1, z: 1}
    - name: IK-toe.r
      parentName: control-heel-roll.r
      position: {x: 0.000000002734169, y: -7.0030864e-10, z: -0.00000001175794}
      rotation: {x: 0.00000005860104, y: 1.2081923e-15, z: 0.000000019466935, w: 1}
      scale: {x: 1, y: 0.99999994, z: 1}
    - name: elbowIK.l
      parentName: root
      position: {x: -0.2721045, y: 0.66405666, z: -0.35331568}
      rotation: {x: -0.7071067, y: 0.0000000727951, z: 0.00000007279507, w: 0.7071069}
      scale: {x: 1, y: 1, z: 1}
    - name: handIK.l
      parentName: root
      position: {x: -0.42790896, y: 0.66405666, z: 0.00000015294849}
      rotation: {x: -0.50000006, y: 0.49999997, z: 0.49999997, w: 0.50000006}
      scale: {x: 0.9999999, y: 0.99999976, z: 0.99999994}
    - name: elbowIK.r
      parentName: root
      position: {x: 0.27210432, y: 0.66405666, z: -0.35331577}
      rotation: {x: -0.7071067, y: 0.0000000727951, z: 0.00000007279507, w: 0.7071069}
      scale: {x: 1, y: 1, z: 1}
    - name: handIK.r
      parentName: root
      position: {x: 0.42790896, y: 0.66405666, z: -0.00000005109439}
      rotation: {x: 0.50000006, y: 0.49999994, z: 0.5, w: -0.5}
      scale: {x: 1, y: 1, z: 1}
    - name: Skeleton_Rogue_ArmLeft
      parentName: Skeleton_Rogue(Clone)
      position: {x: -0, y: 0, z: 0}
      rotation: {x: 0.000000021855694, y: 0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Skeleton_Rogue_ArmRight
      parentName: Skeleton_Rogue(Clone)
      position: {x: -0, y: 0, z: 0}
      rotation: {x: 0.000000021855694, y: 0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Skeleton_Rogue_Body
      parentName: Skeleton_Rogue(Clone)
      position: {x: -0, y: 0, z: 0}
      rotation: {x: 0.000000021855694, y: 0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Skeleton_Rogue_Eyes
      parentName: Skeleton_Rogue(Clone)
      position: {x: -0.0000034332277, y: 0.7295234, z: -0.00000008158386}
      rotation: {x: 0.000000021855694, y: 0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Skeleton_Rogue_Head
      parentName: Skeleton_Rogue(Clone)
      position: {x: -0, y: 0.72952425, z: -0.00000008158386}
      rotation: {x: 0.000000021855694, y: 0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Skeleton_Rogue_Jaw
      parentName: Skeleton_Rogue(Clone)
      position: {x: -0.0000034332277, y: 0.7869187, z: -0.019599322}
      rotation: {x: 0.000000021855694, y: 0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Skeleton_Rogue_LegLeft
      parentName: Skeleton_Rogue(Clone)
      position: {x: -0, y: 0, z: 0}
      rotation: {x: 0.000000021855694, y: 0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Skeleton_Rogue_LegRight
      parentName: Skeleton_Rogue(Clone)
      position: {x: -0, y: 0, z: 0}
      rotation: {x: 0.000000021855694, y: 0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 0.6
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 1
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 3
  humanoidOversampling: 1
  avatarSetup: 1
  addHumanoidExtraRootOnlyWhenUsingAvatar: 1
  remapMaterialsIfMaterialImportModeIsNone: 0
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
AssetOrigin:
  serializedVersion: 1
  productId: 316173
  packageName: All In 1 3D-Shader
  packageVersion: 1.6
  assetPath: Assets/Plugins/AllIn13DShader/Demo/Models/SkeletonMesh.fbx
  uploadId: 760663
