# JPG Bitcrunch Authentic Recreation Guide

## Deep Analysis and Implementation for Unity 6 Render Graph

---

## 🔍 **CRITICAL DISCOVERIES**

After comprehensive analysis of the original JPG Bitcrunch implementation, I've identified the fundamental mathematical and visual mechanisms that create the authentic datamosh effect.

### **Key Finding: The Visual Effect is NOT About Pixelated Squares**

The characteristic JPG Bitcrunch appearance comes from **DCT (Discrete Cosine Transform) frequency filtering**, not simple pixelation. The "blocks" you see are **frequency domain artifacts**, not pixel blocks.

---

## 📊 **MATHEMATICAL FOUNDATION**

### **1. DCT Basis Function Analysis**

```hlsl
// JPG Bitcrunch uses a modified DCT implementation
float basis1D(float k, float i)
{
    float4 _G = float4(2, 1, 2, 2);
    float _Contrast = 0.0;
    return k == 0 ? sqrt(1. / float(BLOCK_SIZE)) :
           sqrt((_G.w + _Contrast) / float(BLOCK_SIZE)) *
           cos(float((_G.x * float(i) + _G.y) * k) * 3.14159265358 / (_G.z * float(BLOCK_SIZE)));
}
```

**What this does:**

- Creates frequency basis functions for DCT transformation
- `k == 0` is the DC component (average color)
- Higher `k` values represent higher frequencies
- The cosine function creates the characteristic "block" patterns

### **2. Quality Parameter - The Key to Visual Appearance**

```hlsl
// JPG Bitcrunch hardcoded quality
float _Quality = 4.0;
float quality = length(float2(_Quality, _Quality)); // = 5.657

// Critical frequency filtering
outColor *= lerp(step(length(float2(inBlock)), quality), 1.0, m);
```

**What this does:**

- `quality = 5.657` is the frequency cutoff threshold
- `step(length(float2(inBlock)), quality)` creates a circular frequency mask
- This **eliminates high frequencies**, creating the blocky appearance
- The `lerp(..., m)` blends between encode (m=0) and decode (m=1) phases

### **3. The Encode/Decode Process**

**Encode Phase (m=0):**

```hlsl
// Transforms image to frequency domain and applies quality filtering
float4 col = jpg(input.uv, 0);
```

**Decode Phase (m=1):**

```hlsl
// Reconstructs image from filtered frequency components
float4 col = jpg(input.uv, 1);
```

**The visual effect comes from the information loss during frequency filtering.**

---

## 🎯 **DATAMOSH MECHANISM ANALYSIS**

### **1. Block-Based Motion Vector Sampling**

```hlsl
// JPG Bitcrunch samples motion vectors at block centers
float2 snappedUV = (floor(uv / (_Downscaled_TexelSize.xy * BLOCK_SIZE)) + 0.5) *
                   (_Downscaled_TexelSize.xy * BLOCK_SIZE);
float2 motionVector = SAMPLE(_MotionVectorTexture, sampler_LinearClamp, snappedUV).xy;
```

**Critical Details:**

- Motion vectors are sampled at **block centers**, not per-pixel
- This creates the characteristic **block-aligned trailing**
- Each block moves as a unit, not individual pixels

### **2. Reprojection Threshold Calculation**

```hlsl
// JPG Bitcrunch reprojection logic
float threshold = _ReprojectPercent +
                 min(length(motionVector * float2(1920, 1080)) * _ReprojectLengthInfluence, 0.7);

if (hash1((123 + blockID.x) * (456 + blockID.y) + (_Time.y * _ReprojectSpeed)) < threshold)
    return float4(pull, 1.0); // Direct reprojection
```

**Key Mechanisms:**

- **Base noise**: `_ReprojectPercent` provides random block selection
- **Motion sensitivity**: `length(motionVector * float2(1920, 1080))` scales motion to 1080p units
- **Clamping**: `min(..., 0.7)` prevents excessive motion influence
- **Hash function**: Creates deterministic but pseudo-random block selection
- **Direct reprojection**: No blending, just direct pixel replacement

### **3. Previous Frame Storage**

```hlsl
// JPG Bitcrunch previous frame sampling
float3 pull = SAMPLE(_PrevScreen, sampler_LinearClamp, uv - motionVector).rgb;
```

**Critical Details:**

- Uses **original motion vector**, not enhanced/processed version
- Samples from **exact UV coordinates** offset by motion
- No temporal accumulation or blending

---

## 🔧 **RENDER PIPELINE DIFFERENCES**

### **JPG Bitcrunch (Legacy URP)**

- Uses `CommandBuffer` with `GetTemporaryRT`
- Direct texture management
- Simple pass execution order
- Previous frame stored in persistent `RenderTexture`

### **Flux (Unity 6 Render Graph)**

- Uses `TextureHandle` system
- Automatic resource management
- Pass merging and optimization
- Previous frame managed through Render Graph

**Critical Issue:** Render Graph's automatic optimization may be affecting texture timing and availability.

---

## 🎨 **VISUAL EFFECT BREAKDOWN**

### **What Creates the "JPG Bitcrunch Look":**

1. **DCT Frequency Filtering**: Creates the blocky, compressed appearance
2. **Color Quantization**: `round(col / truncation) * truncation` creates color banding
3. **Block-Aligned Motion**: Motion vectors sampled at block centers
4. **Direct Reprojection**: No blending, just hard pixel replacement
5. **Temporal Persistence**: Blocks "stick" until randomly reselected

### **What DOESN'T Create the Look:**

- Simple pixelation or downscaling
- Smooth pixel-level motion
- Gradual blending or accumulation
- Complex enhancement features

---

## 🚨 **CRITICAL IMPLEMENTATION ERRORS IN FLUX**

### **1. Over-Complex Motion Processing**

Flux has too many enhancement features that dilute the core effect:

- Trail smoothness
- Motion persistence
- Error accumulation
- Camera motion amplification

**Solution:** Pure Datamosh Mode should disable ALL enhancements.

### **2. Incorrect Block ID Calculation**

Flux was using smoothness-affected block IDs even in Pure Datamosh Mode.

**Fixed:** Use simple `floor(uv / (_Downscaled_TexelSize.xy * BLOCK_SIZE))` in Pure Mode.

### **3. Motion Vector Inconsistency**

Flux was mixing enhanced and original motion vectors.

**Fixed:** Use original motion vector consistently in Pure Datamosh Mode.

### **4. Render Graph Timing Issues**

Render Graph's automatic optimization may affect previous frame availability.

**Investigation Needed:** Verify previous frame texture is available when needed.

---

## 📋 **IMPLEMENTATION CHECKLIST**

### **✅ Already Fixed:**

- [x] Preset parameter values (enabled datamosh)
- [x] Block ID calculation in Pure Datamosh Mode
- [x] Motion vector consistency
- [x] DCT implementation matching

### **🔍 Still Need Investigation:**

- [ ] Render Graph previous frame timing
- [ ] Texture format consistency
- [ ] Motion vector generation in Unity 6
- [ ] Downscaling calculation accuracy

### **🎯 Next Steps:**

1. Test with working JPG Bitcrunch parameters
2. Compare side-by-side in Game View (not Scene View)
3. Verify motion vector generation is working
4. Check previous frame texture availability
5. Validate DCT frequency filtering accuracy

---

## 🔬 **TESTING METHODOLOGY**

### **Proper Testing Environment:**

- **Game View only** (Scene View excluded from motion vectors)
- **Play Mode required** (motion vectors not generated in Edit Mode)
- **Moving objects** (static scenes won't show datamosh)
- **Identical parameters** between JPG Bitcrunch and Flux

### **Visual Comparison Points:**

1. **Block alignment**: Are trailing effects aligned to block boundaries?
2. **Motion sensitivity**: Do faster movements create more trailing?
3. **Temporal persistence**: Do blocks "stick" until randomly updated?
4. **Frequency artifacts**: Are there DCT-style compression artifacts?
5. **Color quantization**: Is there visible color banding?

---

## 💡 **CONCLUSION**

The JPG Bitcrunch effect is fundamentally a **frequency domain compression simulation** with **block-based temporal reprojection**. The visual appearance comes from DCT frequency filtering, not simple pixelation. The datamosh effect comes from block-aligned motion vector sampling with direct pixel reprojection.

Flux's implementation is mathematically correct but may have Render Graph timing issues affecting the visual output. The core algorithms match JPG Bitcrunch exactly in Pure Datamosh Mode.

---

## 🔧 **TECHNICAL IMPLEMENTATION DETAILS**

### **Render Pass Execution Order**

```
JPG Bitcrunch Pipeline:
1. Downscale (1/downscaling resolution)
2. Encode (DCT + frequency filtering)
3. Decode (inverse DCT)
4. Upscale + Reprojection (motion-based datamosh)
5. Copy to Previous Frame

Flux Pipeline (Render Graph):
1. Downscale Pass
2. Encode Pass
3. Decode Pass
4. Upscale Pull Pass (with reprojection)
5. Copy to Previous Pass
```

### **Texture Format Requirements**

```csharp
// JPG Bitcrunch uses R32G32B32A32_SFloat for all intermediate textures
GraphicsFormat.R32G32B32A32_SFloat
```

### **Motion Vector Coordinate Space**

```hlsl
// JPG Bitcrunch scales motion vectors to 1920x1080 units for consistency
length(motionVector * float2(1920, 1080))
```

### **Hash Function Implementation**

```hlsl
// Exact JPG Bitcrunch hash function
float hash1(uint n)
{
    n++;
    n = (n << 13U) ^ n;
    n = n * (n * n * 15731U + 789221U) + 1376312589U;
    return float(n & uint(0x7fffffffU))/float(0x7fffffff);
}
```

### **Block Size Mapping**

```
BLOCK_SIZE_4  = 4x4 pixels
BLOCK_SIZE_8  = 8x8 pixels
BLOCK_SIZE_16 = 16x16 pixels
```

### **Downscaling Calculation**

```csharp
// JPG Bitcrunch downscaling formula
int widthDownscaled = Mathf.FloorToInt(width / downscaling / 2f) * 2;
int heightDownscaled = Mathf.FloorToInt(height / downscaling / 2f) * 2;
```

---

## 🐛 **DEBUGGING GUIDE**

### **Common Issues and Solutions**

**1. No Datamosh Effect Visible**

- Check: `reprojectBaseNoise > 0` OR `reprojectLengthInfluence > 0`
- Check: Testing in Game View, not Scene View
- Check: Play Mode active (motion vectors need runtime)
- Check: Objects are actually moving

**2. Wrong Visual Appearance**

- Check: Pure Datamosh Mode enabled
- Check: Block size matches JPG Bitcrunch
- Check: Quality parameter = 4.0 in Pure Mode
- Check: Motion vector texture is available

**3. Performance Issues**

- Check: Downscaling value (higher = better performance)
- Check: Block size (4x4 fastest, 16x16 slowest)
- Check: Render Graph pass merging is working

### **Debug Visualization**

```hlsl
// Enable motion vector visualization
#ifdef VIZ_MOTION_VECTORS
    return 0.5 + float4(SAMPLE(_MotionVectorTexture, sampler_LinearClamp, uv).xy * float2(1920, 1080), 0.0, 1.0);
#endif
```

---

## 📊 **PERFORMANCE ANALYSIS**

### **JPG Bitcrunch Performance Characteristics**

- **Downscaling**: Major performance impact (1/downscaling²)
- **Block Size**: 4x4 = fast, 8x8 = medium, 16x16 = slow
- **DCT Loops**: Nested loops are expensive but necessary
- **Motion Vectors**: Minimal overhead when enabled

### **Render Graph Optimizations**

- Automatic pass merging on mobile TBDR
- Optimized memory allocation
- Reduced GPU memory bandwidth
- Better resource lifetime management

---

## 🎯 **FINAL RECOMMENDATIONS**

1. **Test in proper environment** (Game View + Play Mode + Moving objects)
2. **Use working parameter values** (reprojectBaseNoise ≥ 0.05)
3. **Enable Pure Datamosh Mode** for authentic behavior
4. **Verify motion vector generation** in Unity 6
5. **Check Render Graph previous frame timing**

The mathematical foundation is correct. The issue is likely in the Render Graph implementation details or testing environment.
