<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<link rel="icon" href="../images/favicon.svg" />
		<link rel="icon" href="../images/favicon.ico" />
		<meta name="viewport" content="width=device-width, height=device-height, initial-scale=1">
		<meta property="og:type" content="website">
		<meta http-equiv="content-security-policy" content="">
		<link href="../_app/immutable/assets/_layout.21aea584.css" rel="stylesheet">
		<link href="../_app/immutable/assets/_layout.0154a58a.css" rel="stylesheet">
		
		
		
		
		
		
		
		
		
		
		
		
		<title>Circle / Sprial Layout</title><!-- HEAD_svelte-i5aht6_START --><meta property="og:title" content="Circle / Sprial Layout"><meta name="description" property="og:description" content="Flexalon lets Unity developers arrange objects in 3D layouts that are precise and easy to adjust. Use built-in interactions and animations to create powerful experiences."><meta name="image" property="og:image" content="../images/flexalon-1200x630.png"><meta name="twitter:card" content="summary_large_image"><meta name="twitter:title" content="Circle / Sprial Layout"><meta name="twitter:site" content="@alon_farchy"><meta name="twitter:description" content="Flexalon lets Unity developers arrange objects in 3D layouts that are precise and easy to adjust. Use built-in interactions and animations to create powerful experiences."><meta name="twitter:image" content="../images/flexalon-1200x630.png"><!-- HEAD_svelte-i5aht6_END -->
	</head>
	<body>
		<div>


<div class="text-white font-body p-4 sticky top-0 bg-black flex gap-5 w-full text-gray-200 z-20 border-b-2 border-[#21a6f0]"><button class="lg:hidden"><svg width="24" height="24"><path d="M5 6h14M5 12h14M5 18h14" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"></path></svg></button>
    <a href="https://www.flexalon.com/" class="font-logo upper text-lg text-[#21a6f0] lg:pl-5 svelte-194nj6y">Flexalon Layouts</a></div>

<div class="w-full max-w-[1600px] lg:pl-10"><div class="flex w-full min-h-full text-zinc-400 relative"><div class="h-full fixed bg-black z-10 max-lg:hidden"><div class="flex flex-col w-[300px] gap-2 border-[#21a6f0] border-r-2 bg-black min-h-0 h-full pt-10 pb-36 overflow-auto max-lg:pl-10"><b class="text-zinc-200">Getting Started</b>
                <a href="../docs.html" class="svelte-194nj6y">Installation</a>
                <a href="../docs/coreConcepts.html" class="svelte-194nj6y">Core Concepts</a>
                <a href="../docs/ui.html" class="svelte-194nj6y">Flexalon UI <span class="ml-2 text-xs font-bold text-pink-300">(New in v4.0!)</span></a>
                <a href="../docs/templates.html" class="svelte-194nj6y">Template Pack</a>
                <a href="../docs/challenges.html" class="svelte-194nj6y">Challenges!</a>

                <a class="text-zinc-200 mt-5 font-bold svelte-194nj6y" href="../docs/flexalonObject.html">Flexalon Objects</a>
                <a href="../docs/flexalonObject.html#size" class="svelte-194nj6y">Width, Height, and Depth</a>
                <a href="../docs/flexalonObject.html#minmax" class="svelte-194nj6y">Min / Max Size<span class="ml-2 text-xs font-bold text-purple-400">(v4.1)</span></a>
                <a href="../docs/flexalonObject.html#margins" class="svelte-194nj6y">Margins and Padding</a>
                <a href="../docs/flexalonObject.html#offset" class="svelte-194nj6y">Offset, Scale, and Rotation</a>

                <b class="text-zinc-200 mt-5">Layouts</b>
                <a href="../docs/flexibleLayout.html" class="svelte-194nj6y">Flexible Layout</a>
                <a href="../docs/gridLayout.html" class="svelte-194nj6y">Grid Layout</a>
                <a href="../docs/circleLayout.html" class="svelte-194nj6y">Circle / Spiral Layout</a>
                <a href="../docs/curveLayout.html" class="svelte-194nj6y">Curve Layout</a>
                <a href="../docs/randomLayout.html" class="svelte-194nj6y">Random Layout</a>
                <a href="../docs/alignLayout.html" class="svelte-194nj6y">Align Layout</a>
                <a href="../docs/shapeLayout.html" class="svelte-194nj6y">Shape Layout</a>
                <a href="../docs/constraints.html" class="svelte-194nj6y">Constraints</a>
                <a href="../docs/randomModifier.html" class="svelte-194nj6y">Random Modifier</a>

                <a class="text-zinc-200 mt-5 font-bold svelte-194nj6y" href="../docs/animators.html">Animators</a>
                <a href="../docs/animators.html#curve" class="svelte-194nj6y">Curve Animator</a>
                <a href="../docs/animators.html#lerp" class="svelte-194nj6y">Lerp Animator</a>
                <a href="../docs/animators.html#rb" class="svelte-194nj6y">Rigid Body Animator</a>
                <a href="../docs/animators.html#layouts" class="svelte-194nj6y">Animating Layouts</a>

                <b class="text-zinc-200 mt-5">Click &amp; Drag</b>
                <a href="../docs/interactable.html" class="svelte-194nj6y">Interactable</a>
                <a href="../docs/interactable.html#dragTarget" class="svelte-194nj6y">Drag Target</a>
                <a href="../docs/xr.html" class="svelte-194nj6y">XR Interactions <span class="ml-2 text-xs font-bold text-blue-300">(v3.2)</span></a>
                <a href="../docs/interactable.html#customInputProvider" class="svelte-194nj6y">Custom Input Provider</a>

                <b class="text-zinc-200 mt-5">Data Binding</b>
                <a href="../docs/cloner.html" class="svelte-194nj6y">Cloner</a>
                <a href="../docs/cloner.html#databinding" class="svelte-194nj6y">Data Binding</a>

                <b class="text-zinc-200 mt-5">Advanced</b>
                <a href="../docs/pipeline.html" class="svelte-194nj6y">Flexalon Pipeline</a>
                <a href="../docs/customLayout.html" class="svelte-194nj6y">Custom Layouts</a>
                <a href="../docs/customAnimators.html" class="svelte-194nj6y">Custom Animators</a>
                <a href="../docs/adapters.html" class="svelte-194nj6y">Adapters</a>

                <a class="text-zinc-200 mt-5 font-bold svelte-194nj6y" href="../docs/api/Flexalon.html">Scripting API</a>
                <div class="mb-20"></div></div></div>
        
        <div class="flex flex-col w-full p-10 lg:ml-[300px]"><div class="doc svelte-194nj6y">

<div class="text-white text-2xl mb-10">Circle / Sprial Layout</div>

<p>Use a circle layout to position children along a circle or spiral.</p>
<div class="flex ml-10 m-5 mb-10"><img src="../images/doc-circle-2.png" alt="Circle layout"></div>

<p class="text-xl mt-10">Circle Layout Options</p>
<div class="flex ml-10 m-5 mb-10"><img src="../images/doc-circle-options.png" alt="Circle layout options"></div>
<table><tr><td>Plane <span class="ml-2 text-xs font-bold text-pink-300">(v4.0)</span></td>
        <td>Determines on which plane to create the circle.</td></tr>
    <tr><td class="whitespace-nowrap">Initial Radius <span class="ml-2 text-xs font-bold text-pink-300">(v4.0)</span></td>
        <td>Determines the radius of the circle:
            <ul class="list-disc mt-5 ml-5"><li><b>Fixed</b>: The initial radius is a fixed value.</li>
                <li><b>HalfAxis1</b>: The initial radius is half the size of the layout on the first plane axis.</li>
                <li><b>HalfAxis2</b>: The initial radius is half the size of the layout on the other plane axis.</li>
                <li><b>HalfMinAxis</b>: The initial radius is half the size of the layout on the smaller plane axis</li>
                <li><b>HalfMaxAxis</b>: The initial radius is half the size of the layout on the larger plane axis</li></ul>

            <p>The Half* options help fit the circle into a parent layout when the size is set to &quot;Fill&quot; on the Flexalon Object.
            For example, if the circle is on the &quot;XZ&quot; plane, then the first axis is X and the second axis is Z. If Initial Radius is set to HalfAxis1,
            and the Width is set to Fill, then the circle&#39;s width (diameter) will match the layout width.</p></td></tr>
    <tr><td>Radius</td>
        <td>Radius of the circle, if initial radius is Fixed.</td></tr>
    <tr><td>Spiral</td>
        <td>If checked, positions each object at increasing heights to form a spiral.</td></tr>
    <tr><td>Spiral Spacing</td>
        <td>Vertical spacing between objects in the spiral.</td></tr>
    <tr><td>Spacing Type</td>
        <td>If set to Evenly, the space around the circle is distributed between children.
            If set to Fixed, the Spacing Degrees property determines the space between children.</td></tr>
    <tr><td>Radius Type</td>
        <td><p>If set to Step, the radius is incremented for each child by the Radius Step property.
                This can be used to create an inward or outward spiral.
            </p>
            <p>If set to Wrap, the radius is incremented each time around the circle.
                This can be used to create concentric circles of objects.
            </p></td></tr>
    <tr><td>Start At Degrees</td>
        <td>By default, the first child will be placed at (radius, 0, 0). Start At Degrees value will add an offset all children around the circle.</td></tr>
    <tr><td>Rotate</td>
        <td>Determines how children should be rotated - None, In to the circle, Out of the circle, Forward along the circle, or Backward along the circle.</td></tr>
    <tr><td>Plane Align <span class="ml-2 text-xs font-bold text-pink-300">(v4.0)</span></td>
        <td>Aligns the layout on the plane&#39;s normal axis.
            For a circle, this will align each individual object in the layout.
            For a spiral, this will align the entire spiral.
        </td></tr></table></div></div></div>
</div>


			
			
		</div>
	</body>
</html>
