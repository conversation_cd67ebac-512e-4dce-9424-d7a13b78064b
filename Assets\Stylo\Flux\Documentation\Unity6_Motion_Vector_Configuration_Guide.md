# Unity 6 URP Motion Vector Configuration Guide

## 🎯 **Motion Vector Generation Requirements**

Based on Unity 6 URP documentation and research, motion vectors have specific requirements that must be met for proper generation.

### **Core Requirements for Motion Vector Generation**

1. **ScriptableRenderPassInput.Motion Flag**
   - Custom render passes MUST request motion vectors using `ConfigureInput(ScriptableRenderPassInput.Motion)`
   - This forces URP to inject the motion vector render pass into the frame
   - Without this flag, motion vectors may not be generated even if other conditions are met

2. **Active Motion Vector Consumers**
   - Motion vectors are only generated when there are active features requesting them
   - Examples: TAA (Temporal Anti-Aliasing), Motion Blur, Custom Renderer Features
   - URP optimizes by skipping motion vector generation when no consumers exist

3. **Camera Type Restrictions**
   - **Scene View cameras are EXCLUDED** from motion vector generation
   - Only Game View cameras generate motion vectors
   - This is a critical point often missed during testing

## 🔧 **Common Dependencies and Requirements**

### **1. URP Renderer Feature Configuration**
```csharp
// In your ScriptableRenderPass
public override void OnCameraSetup(CommandBuffer cmd, ref RenderingData renderingData)
{
    // CRITICAL: Request motion vectors as input
    ConfigureInput(ScriptableRenderPassInput.Motion);
}
```

### **2. Camera Settings**
- **No specific camera inspector setting** for motion vectors in Unity 6 URP
- Motion vectors are automatically generated based on camera movement
- Camera motion vectors are calculated using depth buffer + camera matrices

### **3. Object Motion Vector Settings**
For GameObjects with MeshRenderer:
- **Location**: `Mesh Renderer → Additional Settings → Motion Vectors`
- **Options**:
  - `Camera Motion Only`: Object treated as stationary (default)
  - `Per Object Motion`: Enables per-object motion vector rendering
  - `Force No Motion`: Renders motion vectors but sets them to zero

### **4. Material and Shader Requirements**
- **Opaque materials only**: URP does not support motion vectors for transparent materials
- **MotionVectors pass required**: Custom shaders need `LightMode = "MotionVectors"` pass
- **Built-in URP shaders**: Support motion vectors automatically

### **5. MSAA Considerations**
- Motion vectors work with MSAA enabled
- No specific MSAA requirements found in documentation
- Depth texture dependency: Motion vectors depend on depth texture availability

## 📋 **Step-by-Step Configuration**

### **Step 1: Verify URP Setup**
1. **Check URP Renderer Asset**:
   - Ensure using URP Renderer Asset (not Built-in)
   - Verify no conflicting renderer features

2. **Check Quality Settings**:
   - `Edit → Project Settings → Quality`
   - Ensure URP asset is assigned to quality levels

### **Step 2: Configure Renderer Feature**
```csharp
// In FluxRendererFeature.cs - ALREADY IMPLEMENTED
public override void OnCameraSetup(CommandBuffer cmd, ref RenderingData renderingData)
{
    // Configure input requirements based on features used
    ScriptableRenderPassInput inputRequirements = ScriptableRenderPassInput.Color;
    
    if (DoReprojection)
        inputRequirements |= ScriptableRenderPassInput.Motion; // ✅ CORRECT
        
    ConfigureInput(inputRequirements);
}
```

### **Step 3: Verify DoReprojection Logic**
```csharp
// In FluxRendererFeature.cs - CHECK THIS CONDITION
bool DoReprojection => (reprojectPercent > 0f || 
                       reprojectLengthInfluence > 0f || 
                       cameraMotionAmplification > 0f || 
                       v.VisualizeMotionVectors.value) && 
                       !cameraData.isSceneViewCamera; // ✅ Scene view excluded
```

### **Step 4: Test in Game View Only**
- **CRITICAL**: Test motion vectors in Game View, NOT Scene View
- Scene View cameras are explicitly excluded from motion vector generation
- This is the most common cause of "motion vectors not working"

### **Step 5: Verify Shader Implementation**
```hlsl
// In shader - ALREADY IMPLEMENTED
TEXTURE2D_X(_MotionVectorTexture);
SAMPLER(sampler_MotionVectorTexture);

// Sampling - ALREADY IMPLEMENTED  
float2 motionVector = SAMPLE_TEXTURE2D_X(_MotionVectorTexture, sampler_MotionVectorTexture, uv).xy;
```

## 🧪 **Verification Methods**

### **Method 1: Motion Vector Visualization**
1. Enable `VisualizeMotionVectors = true` in Flux Effect
2. Enter Play Mode in **Game View** (not Scene View)
3. Move camera with WASD + Mouse
4. **Expected**: Colored motion vector overlay (red/green channels)

### **Method 2: Frame Debugger Analysis**
1. `Window → Analysis → Frame Debugger`
2. Look for "MotionVectors" pass in the frame breakdown
3. Verify motion vector texture is being generated
4. Check if objects appear in MotionVectors pass

### **Method 3: Debug Tool Verification**
```csharp
// Use the FluxMotionVectorDebugTool we created
// Tools → Flux → Motion Vector Debug Tool
// Click "Diagnose Motion Vector System"
```

## 🚨 **Common Issues and Solutions**

### **Issue 1: Testing in Scene View**
- **Problem**: Scene View cameras don't generate motion vectors
- **Solution**: Always test in Game View during Play Mode

### **Issue 2: DoReprojection = False**
- **Problem**: No motion vector consumers detected
- **Solution**: Ensure at least one of these > 0:
  - Base Noise (reprojectPercent)
  - Length Influence (reprojectLengthInfluence)  
  - Camera Motion Amplification (cameraMotionAmplification)

### **Issue 3: Motion Vector Texture Null**
- **Problem**: `resourceData.motionVectorColor` is null in Render Graph
- **Solution**: Verify `ScriptableRenderPassInput.Motion` is requested

### **Issue 4: No Object Motion**
- **Problem**: Objects don't contribute to motion vectors
- **Solution**: Set `Mesh Renderer → Motion Vectors` to "Per Object Motion"

### **Issue 5: Transparent Materials**
- **Problem**: Motion vectors not working on transparent objects
- **Solution**: URP doesn't support motion vectors for transparent materials

## 🔍 **Unity 6 Render Graph Specific Notes**

### **Render Graph Motion Vector Access**
```csharp
// In Unity 6 Render Graph - ALREADY IMPLEMENTED
TextureHandle motionVectorTexture = doReprojection ? 
    resourceData.motionVectorColor : TextureHandle.nullHandle;
    
// Pass to shader
if (data.motionVectorTexture.IsValid())
{
    data.fluxMaterial.SetTexture("_MotionVectorTexture", data.motionVectorTexture);
}
```

### **Render Graph Dependencies**
- Motion vectors depend on depth texture generation
- Render Graph automatically handles dependencies when `ScriptableRenderPassInput.Motion` is requested
- No manual dependency management required

## ✅ **Verification Checklist**

- [ ] **Testing in Game View** (not Scene View)
- [ ] **DoReprojection = true** (check debug tool output)
- [ ] **ScriptableRenderPassInput.Motion** requested in ConfigureInput
- [ ] **At least one reprojection parameter > 0** (Base Noise, Length Influence, or Camera Motion Amplification)
- [ ] **URP Renderer Asset** properly configured
- [ ] **Motion vector visualization** shows colored overlay when enabled
- [ ] **Frame Debugger** shows MotionVectors pass
- [ ] **Objects set to "Per Object Motion"** if object-specific motion needed

## 🎯 **Expected Results After Proper Configuration**

1. **Motion Vector Visualization**: Colored overlay showing camera/object motion
2. **Frame Debugger**: MotionVectors pass visible in frame breakdown  
3. **Debug Tool**: Reports "DoReprojection = TRUE"
4. **Flux Effects**: Enhanced trailing parameters produce visible effects

The key insight is that Unity 6's motion vector system is **conditional and optimized** - it only generates motion vectors when there are active consumers, and it excludes Scene View cameras entirely.
