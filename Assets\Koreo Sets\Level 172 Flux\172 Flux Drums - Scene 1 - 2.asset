%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -2076007791, guid: aa81bbec85a3f423193272a7c6154986, type: 3}
  m_Name: 172 Flux Drums - Scene 1 - 2
  m_EditorClassIdentifier: 
  mSourceClip: {fileID: 0}
  mAudioFilePath: ../FMOD Studio Projects/Beat Traveller Reload/Assets/GS_FLUX_172_Drum_Loop_15_V1.wav
  mSampleRate: 44100
  mIgnoreLatencyOffset: 0
  mTempoSections:
  - sectionName: New Section
    startSample: 0
    samplesPerBeat: 15383.************
    beatsPerMeasure: 4
    bStartNewMeasure: 1
  mTracks:
  - {fileID: 11400000, guid: bc9e3351b8595e14d8cd6ed134082759, type: 2}
  - {fileID: 11400000, guid: 5795c6b978201e8489c8c3ec69339c34, type: 2}
  - {fileID: 11400000, guid: 7b2c953f473e94e478ebc22eb4911dbd, type: 2}
  - {fileID: 11400000, guid: caef2b66bd290dc41a30ad689493c1b9, type: 2}
