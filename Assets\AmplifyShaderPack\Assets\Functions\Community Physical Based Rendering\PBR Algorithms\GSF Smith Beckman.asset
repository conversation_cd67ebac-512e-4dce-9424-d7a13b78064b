%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: G<PERSON> <PERSON>
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor v1.9.5.1\n// Available at the
    Unity Asset Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=19501\nNode;AmplifyShaderEditor.CommentaryNode;76;-1666,62;Inherit;False;2474;915;Math;28;73;69;30;25;27;67;31;66;70;74;35;38;75;17;42;43;33;34;41;46;48;47;51;68;36;0;172;173;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;78;-2780.841,64;Inherit;False;1054.064;641.1273;Dot
    Products;9;106;142;143;136;135;128;127;170;171;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;72;-1664,-288;Inherit;False;613.8345;288.7466;GSF
    Smith Beckman Node;1;71;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;73;-1616,208;Inherit;False;200;116;NdotL;1;166;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;74;-1616,352;Inherit;False;197;123;NdotV;1;167;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;65;-1664,-464;Inherit;False;518.6614;153.2232;Credits;;0,0,0,0.5019608;Method
    by Jordan Stevens$Link: https://www.jordanstevenstechart.com/physically-based-rendering$$Node
    Made by Tiber Legacy$Special Thanks to Dawie;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;69;-1344,464;Inherit;False;200.333;101.4815;Vectorized;;0,0,0,0.5019608;to
    simplify and Optimize the Algorithm;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;71;-1424,-240;Inherit;False;331.9333;127.3819;;;0.2358491,0.2358491,0.2358491,1;Originally
    created to be used with the Beckman NDF, Walter et al. surmised that it is an
    appropriate GSF for use with the Phong NDF - Jordan Stevens;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;75;-1248,96;Inherit;False;222;101;Roughness;;0,0,0,1;;0;0\nNode;AmplifyShaderEditor.SimpleDivideOpNode;30;-576,368;Inherit;False;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.OneMinusNode;25;-1040,320;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SqrtOpNode;27;-864,320;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;67;-1184,320;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;31;-736,320;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.DynamicAppendNode;66;-1344,368;Inherit;False;FLOAT2;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;38;-384,432;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;42;-224,432;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleDivideOpNode;43;-32,432;Inherit;False;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.Compare;34;144,368;Inherit;False;4;4;0;FLOAT2;0,0;False;1;FLOAT;0;False;2;FLOAT2;0,0;False;3;FLOAT;1;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;41;-384,528;Inherit;False;3;3;0;FLOAT;0;False;1;FLOAT2;0,0;False;2;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;46;-384,640;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;48;-384,736;Inherit;False;3;3;0;FLOAT;0;False;1;FLOAT2;0,0;False;2;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;47;-192,640;Inherit;False;3;3;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.RangedFloatNode;51;-384,864;Inherit;False;Constant;_One;One;0;0;Create;True;0;0;0;False;0;False;1;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.BreakToComponentsNode;68;304,368;Inherit;False;FLOAT2;1;0;FLOAT2;0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;36;416,368;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;77;48,800;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;33;-16,320;Inherit;False;Constant;_OneSix;One.Six;0;0;Create;True;0;0;0;False;0;False;1.6;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.Vector4Node;35;-704,480;Inherit;False;Constant;_SmithBeckmanValues;Smith
    Beckman Values;0;0;Create;True;0;0;0;False;0;False;3.535,2.181,2.276,2.577;0,0,0,0;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.GetLocalVarNode;167;-1600,400;Inherit;False;127;NdotV;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;166;-1600,256;Inherit;False;128;NdotL;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;127;-1984,192;Inherit;True;NdotV;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;128;-1984,416;Inherit;True;NdotL;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;135;-2432,272;Inherit;False;NdotV;1;2;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;136;-2432,496;Inherit;False;NdotL;1;1;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;142;-2272,192;Inherit;False;Custom
    Dot Products;True;0;2;1;In 0;In 1;Object;-1;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;106;-2688,192;Inherit;False;Normal;3;0;False;1;0;FLOAT3;0,0,1;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitch;143;-2272,416;Inherit;False;Option;False;0;2;1;In
    0;In 1;Instance;142;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionNode;170;-2496,192;Inherit;False;NdotV;-1;;3;65be4ec269a534f4e8867af2f999aaa9;4,27,1,377,0,452,0,451,0;3;230;FLOAT3;0,0,1;False;443;FLOAT3;0,0,0;False;415;FLOAT;1E-37;False;1;FLOAT;23\nNode;AmplifyShaderEditor.FunctionNode;171;-2496,416;Inherit;False;NdotL;-1;;4;6d7ee1aa8c79587448475fa2b37affc1;5,27,1,381,0,460,0,461,0,463,0;4;230;FLOAT3;0,0,1;False;462;FLOAT3;0,0,0;False;454;FLOAT3;0,0,0;False;416;FLOAT;1E-37;False;1;FLOAT;111\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;70;-1040,224;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;17;-1232,224;Inherit;False;Roughness;1;3;False;1;0;FLOAT;0.5;False;1;FLOAT;0\nNode;AmplifyShaderEditor.StickyNoteNode;172;-736,208;Inherit;False;177;100;Max
    Non Zero;;0,0,0,0.5019608;To prevent Nan in the Division and Output;0;0\nNode;AmplifyShaderEditor.SimpleMaxOpNode;173;-864,224;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0.0001;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;0;576,368;Inherit;True;True;-1;Out;0;False;1;0;FLOAT;0;False;1;FLOAT;0\nWireConnection;30;0;66;0\nWireConnection;30;1;31;0\nWireConnection;25;0;67;0\nWireConnection;27;0;25;0\nWireConnection;67;0;66;0\nWireConnection;67;1;66;0\nWireConnection;31;0;27;0\nWireConnection;31;1;173;0\nWireConnection;66;0;166;0\nWireConnection;66;1;167;0\nWireConnection;38;0;35;1\nWireConnection;38;1;30;0\nWireConnection;42;0;38;0\nWireConnection;42;1;41;0\nWireConnection;43;0;42;0\nWireConnection;43;1;47;0\nWireConnection;34;0;30;0\nWireConnection;34;1;33;0\nWireConnection;34;2;43;0\nWireConnection;34;3;77;0\nWireConnection;41;0;35;2\nWireConnection;41;1;30;0\nWireConnection;41;2;30;0\nWireConnection;46;0;35;3\nWireConnection;46;1;30;0\nWireConnection;48;0;35;4\nWireConnection;48;1;30;0\nWireConnection;48;2;30;0\nWireConnection;47;0;46;0\nWireConnection;47;1;48;0\nWireConnection;47;2;51;0\nWireConnection;68;0;34;0\nWireConnection;36;0;68;0\nWireConnection;36;1;68;1\nWireConnection;77;0;51;0\nWireConnection;127;0;142;0\nWireConnection;128;0;143;0\nWireConnection;142;0;170;23\nWireConnection;142;1;135;0\nWireConnection;143;0;171;111\nWireConnection;143;1;136;0\nWireConnection;170;230;106;0\nWireConnection;171;230;106;0\nWireConnection;70;0;17;0\nWireConnection;70;1;17;0\nWireConnection;173;0;70;0\nWireConnection;0;0;36;0\nASEEND*/\n//CHKSM=5B0B1607112DC4430628ABF44B0BBFE4DE5D0197"
  m_functionName: 
  m_description: 'GSF Smith Beckman Node

'
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 0
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_customEdited: 0
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 0
  m_headerStyle: 2
  m_headerColor: {r: 0.7529412, g: 0.6784314, b: 0, a: 1}
  m_customNodeCategory: Amplify Shader Pack
  m_previewPosition: 0
  m_hidden: 0
  m_url: 
