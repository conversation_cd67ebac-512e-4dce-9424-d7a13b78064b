%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Interior Mapping LatLong
  m_Shader: {fileID: 4800000, guid: 63b31be3d32a41c08fae9d0424e9f444, type: 3}
  m_ValidKeywords: []
  m_InvalidKeywords:
  - _SWITCHPLANE_ON
  m_LightmapFlags: 0
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _Cube:
        m_Texture: {fileID: 8900000, guid: 3c3ca0540cf25f84b847c628e7580e47, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _FacadeMap:
        m_Texture: {fileID: 2800000, guid: ce6a56788ccccef4c8603eb6903c8746, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _InteriorCube:
        m_Texture: {fileID: 8900000, guid: 3c3ca0540cf25f84b847c628e7580e47, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _LatLongMap:
        m_Texture: {fileID: 2800000, guid: 48de6c31db55ac946a2eb4671db8c0d2, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _LatLongMap0:
        m_Texture: {fileID: 2800000, guid: 48de6c31db55ac946a2eb4671db8c0d2, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _LatLongMap1:
        m_Texture: {fileID: 2800000, guid: 48de6c31db55ac946a2eb4671db8c0d2, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _LatLongMap2:
        m_Texture: {fileID: 2800000, guid: 48de6c31db55ac946a2eb4671db8c0d2, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Roof:
        m_Texture: {fileID: 2800000, guid: aab8de1052c54173b3d61e7f8b05aedf, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Texture0:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TextureSample0:
        m_Texture: {fileID: 8900000, guid: 3c3ca0540cf25f84b847c628e7580e47, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TextureSample1:
        m_Texture: {fileID: 2800000, guid: 4a78764018124872b6f511e00fb51f97, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Window:
        m_Texture: {fileID: 2800000, guid: 08e24ca7b158156448eb0fdc20b5e15c, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Windows:
        m_Texture: {fileID: 2800000, guid: 08e24ca7b158156448eb0fdc20b5e15c, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _texcoord:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AlphaCutoff: 0.5
    - _Brightness0: 0.91
    - _Brightness1: 0.905
    - _Brightness2: 0.712
    - _EmissiveIntensity: 20
    - _EnableFacade: 1
    - _EnvironmentReflections: 1
    - _FacadeTilling: 1
    - _Float0: 12.5
    - _Float2: 1.25
    - _InteriorLight: 27.53
    - _QueueControl: 0
    - _QueueOffset: 0
    - _ReceiveShadows: 1
    - _RoomCountX: 3
    - _RoomCountY: 3
    - _SpecularHighlights: 1
    - _SwitchPlane: 1
    - _WindowTilling: 2
    - _Z: -1
    - __dirty: 0
    m_Colors:
    - _EmissionColor: {r: 1, g: 1, b: 1, a: 1}
    - _FacadeTilling: {r: 3, g: 3, b: 0, a: 0}
    - _Mul: {r: 2, g: -2, b: 0, a: 0}
    - _Sub: {r: 1, g: -1, b: 0, a: 0}
    - _Vector0: {r: 1, g: -1, b: 1, a: 0}
    - _Vector0x: {r: 1, g: 1, b: 1, a: 0}
    - _Vector1x: {r: 1, g: 1, b: -1, a: 0}
    - _Vector2y: {r: 1, g: 1, b: 1, a: 0}
    - _Vector3y: {r: 1, g: 1, b: 1, a: 0}
    - _Vector7: {r: -1, g: 1, b: 1, a: 0}
    - _Veiw: {r: -1, g: 1, b: 1, a: 0}
  m_BuildTextureStacks: []
