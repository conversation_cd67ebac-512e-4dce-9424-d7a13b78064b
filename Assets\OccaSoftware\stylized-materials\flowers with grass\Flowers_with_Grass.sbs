<?xml version="1.0" encoding="UTF-8"?><package><identifier v="Unsaved Package"/><formatVersion v="1.1.0.202201"/><updaterVersion v="1.1.0.202201"/><fileUID v="{74de0308-11d9-43c5-a818-bfe32a67117d}"/><versionUID v="0"/><dependencies><dependency><filename v="sbs://pattern_shape.sbs"/><uid v="1311189026"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_gaussian_noise.sbs"/><uid v="1486875707"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://pattern_tile_sampler.sbs"/><uid v="1486875900"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://trapezoid_transform.sbs"/><uid v="1486830014"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://gradient_linear_1.sbs"/><uid v="1335474237"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://histogram_scan.sbs"/><uid v="1149349207"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://hbao_2.sbs"/><uid v="1297162667"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://pattern_splatter_circular.sbs"/><uid v="1487668795"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency></dependencies><content><graph><identifier v="Flowers_with_Grass"/><uid v="1488359158"/><graphOutputs><graphoutput><identifier v="basecolor"/><uid v="1488360207"/><attributes><label v="Base Color"/></attributes><usages><usage><components v="RGBA"/><name v="baseColor"/></usage></usages><group v="Material"/></graphoutput><graphoutput><identifier v="normal"/><uid v="1488360209"/><attributes><label v="Normal"/></attributes><usages><usage><components v="RGBA"/><name v="normal"/></usage></usages><group v="Material"/></graphoutput><graphoutput><identifier v="roughness"/><uid v="1488360211"/><attributes><label v="Roughness"/></attributes><usages><usage><components v="RGBA"/><name v="roughness"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput><graphoutput><identifier v="height"/><uid v="1488360213"/><attributes><label v="Height"/></attributes><usages><usage><components v="RGBA"/><name v="height"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput><graphoutput><identifier v="ambientOcclusion"/><uid v="1488360215"/><attributes><label v="Ambient Occlusion"/></attributes><usages><usage><components v="RGBA"/><name v="ambientOcclusion"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput></graphOutputs><compNodes><compNode><uid v="1488360147"/><GUILayout><gpos v="-464 482.954407 0"/></GUILayout><compOutputs><compOutput><uid v="1487670044"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1488360187"/></connection></connections><compImplementation><compFilter><filter v="transformation"/><parameters><parameter><name v="matrix22"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="1.65289998 0 0 1.7622"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488360149"/><GUILayout><gpos v="-493.588013 -1184.57727 0"/></GUILayout><compOutputs><compOutput><uid v="1482590705"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="source"/><connRef v="1488360159"/><connRefOutput v="1482590232"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="maskrectangle"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.199999988 0.779999971 0 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488360152"/><GUILayout><gpos v="-656 -588.274292 0"/></GUILayout><compOutputs><compOutput><uid v="1487668329"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="source"/><connRef v="1488360204"/><connRefOutput v="1487668231"/></connection><connection><identifier v="destination"/><connRef v="1488360180"/><connRefOutput v="1487668231"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488360153"/><GUILayout><gpos v="200.177048 -1067.25928 0"/></GUILayout><compOutputs><compOutput><uid v="1482625016"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1488360172"/><connRefOutput v="1482609709"/></connection></connections><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1482625039"/><paramsArrayCells><paramsArrayCell><uid v="1482625052"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.286956519 0.182096973 0.0550556034 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1482625053"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.875527442"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.650328338 0.908695638 0.507178962 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1482625213"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.0354818478"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.168359771 0.352173924 0.114661269 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1482625257"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.386075944"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.41995281 0.682608724 0.170652181 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1482625330"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.630801678"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.660464287 0.813043475 0.207987875 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487693664"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.0126582272"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.0372851379 0.256521732 0.0391915254 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1488360154"/><GUILayout><gpos v="-464 368 0"/></GUILayout><compOutputs><compOutput><uid v="1487670288"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1488360147"/><connRefOutput v="1487670044"/></connection></connections><compImplementation><compFilter><filter v="blur"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="9.35999966"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488360156"/><GUILayout><gpos v="-333.799194 -1184.57715 0"/></GUILayout><compOutputs><compOutput><uid v="1482591088"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="destination"/><connRef v="1488360149"/><connRefOutput v="1482590705"/></connection><connection><identifier v="source"/><connRef v="1488360202"/><connRefOutput v="1482590975"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="maskrectangle"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0 1 0 0"/></paramValue></parameter><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488360159"/><GUILayout><gpos v="-623.164917 -1184.57727 0"/></GUILayout><compOutputs><compOutput><uid v="1482590232"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1488360197"/><connRefOutput v="1482591428"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.149234697 0.149234697 0.149234697 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488360165"/><GUILayout><gpos v="-848 482.954407 0"/></GUILayout><compOutputs><compOutput><uid v="1487667975"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1488360176"/><connRefOutput v="1487664356"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.40487805 0.40487805 0.40487805 0.5"/></paramValue></parameter><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.391581625 0.391581625 0.391581625 1"/></paramValue></parameter><parameter><name v="levelinlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.3125 0.3125 0.3125 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488360167"/><GUILayout><gpos v="-720 482.954407 0"/></GUILayout><compOutputs><compOutput><uid v="1487669872"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1488360165"/></connection></connections><compImplementation><compFilter><filter v="transformation"/><parameters><parameter><name v="matrix22"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="1 0 0 1.33290005"/></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0 -0.0401000008"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488360168"/><GUILayout><gpos v="-877.448364 -1184.14014 0"/></GUILayout><compOutputs><compOutput><uid v="1482576333"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///shape?dependency=1311189026"/><parameters><parameter><name v="Pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="16"/></paramValue></parameter><parameter><name v="Size"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.74000001"/></paramValue></parameter><parameter><name v="Pattern_Specific"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.829999983"/></paramValue></parameter><parameter><name v="Size_xy"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.199999988 1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1482576333"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1488360170"/><GUILayout><gpos v="-144 -1055.22864 0"/></GUILayout><compOutputs><compOutput><uid v="1482609943"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///gaussian_noise?dependency=1486875707"/><parameters><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueInt32 v="9"/></paramValue></parameter><parameter><name v="disorder"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.099999994"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1482609943"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1488360172"/><GUILayout><gpos v="16 -1065.89539 0"/></GUILayout><compOutputs><compOutput><uid v="1482609709"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="pattern_input"/><connRef v="1488360186"/><connRefOutput v="1482608790"/></connection><connection><identifier v="rotation_map_input"/><connRef v="1488360170"/><connRefOutput v="1482609943"/></connection></connections><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1486875900"/><parameters><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="130"/></paramValue></parameter><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="size"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="13.6499996 14.3999996"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="3.50999999"/></paramValue></parameter><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="130"/></paramValue></parameter><parameter><name v="size_random"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.329999983 0.319999993"/></paramValue></parameter><parameter><name v="rotation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.920000017"/></paramValue></parameter><parameter><name v="non_square_expansion"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="pattern_input_number"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter><parameter><name v="rotation_map_multiplier"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.569999993"/></paramValue></parameter><parameter><name v="rotation_vector_map_multiplier"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.340000004"/></paramValue></parameter><parameter><name v="color_parametrization_mode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="scale_vector_map_multiplier"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.519999981"/></paramValue></parameter><parameter><name v="mask_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.229999989"/></paramValue></parameter><parameter><name v="vector_map_displacement"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.969999969"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.550000012"/></paramValue></parameter><parameter><name v="color_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="10"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1482609709"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1488360175"/><GUILayout><gpos v="-336 368 0"/></GUILayout><compOutputs><compOutput><uid v="1487670142"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="source"/><connRef v="1488360154"/><connRefOutput v="1487670288"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="maskrectangle"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.0899999961 0.839999974 0.149999991 0.859999955"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488360176"/><GUILayout><gpos v="-1040 -588.274292 0"/></GUILayout><compOutputs><compOutput><uid v="1487664356"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///shape?dependency=1311189026"/><parameters><parameter><name v="Pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="15"/></paramValue></parameter><parameter><name v="Size_xy"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.719999969 1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487664356"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1488360180"/><GUILayout><gpos v="-809.819946 -455.387329 0"/></GUILayout><compOutputs><compOutput><uid v="1487668231"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1488360191"/></connection></connections><compImplementation><compFilter><filter v="transformation"/><parameters><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="-0.0780000016 -0.0256999992"/></paramValue></parameter><parameter><name v="matrix22"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.909699976 0.414999992 -0.414999992 0.909699976"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488360186"/><GUILayout><gpos v="-205.79895 -1184.57715 0"/></GUILayout><compOutputs><compOutput><uid v="1482608790"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1488360156"/></connection></connections><compImplementation><compFilter><filter v="blur"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.91999996"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488360187"/><GUILayout><gpos v="-592 482.954407 0"/></GUILayout><compOutputs><compOutput><uid v="1487669978"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="source"/><connRef v="1488360167"/><connRefOutput v="1487669872"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="maskrectangle"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0 1 0.179999992 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488360189"/><GUILayout><gpos v="-528 -588.274292 0"/></GUILayout><compOutputs><compOutput><uid v="1487672267"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1488360152"/></connection></connections><compImplementation><compFilter><filter v="blur"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="6.35999966"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488360191"/><GUILayout><gpos v="-912 -588.274292 0"/></GUILayout><compOutputs><compOutput><uid v="1487667975"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1488360176"/><connRefOutput v="1487664356"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.40487805 0.40487805 0.40487805 0.5"/></paramValue></parameter><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.391581625 0.391581625 0.391581625 1"/></paramValue></parameter><parameter><name v="levelinlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.3125 0.3125 0.3125 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488360197"/><GUILayout><gpos v="-751.962524 -1184.57727 0"/></GUILayout><compOutputs><compOutput><uid v="1482591428"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input"/><connRef v="1488360168"/><connRefOutput v="1482576333"/></connection></connections><compImplementation><compInstance><path v="pkg:///trapezoid_transform_grayscale?dependency=1486830014"/><parameters><parameter><name v="top_stretch"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.620000005"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1482591428"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1488360199"/><GUILayout><gpos v="-400 -588.274292 0"/></GUILayout><compOutputs><compOutput><uid v="1487669407"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="pattern_input_1"/><connRef v="1488360189"/><connRefOutput v="1487672267"/></connection></connections><compImplementation><compInstance><path v="pkg:///splatter_circular?dependency=1487668795"/><parameters><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="pattern_scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.620000005"/></paramValue></parameter><parameter><name v="pattern_size"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.839999974 1"/></paramValue></parameter><parameter><name v="pattern_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="5"/></paramValue></parameter><parameter><name v="radius"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.140000001"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487669407"/><identifier v="Splatter_Circular"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1488360201"/><GUILayout><gpos v="-624 -1055.22864 0"/></GUILayout><compOutputs><compOutput><uid v="1482591041"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///gradient_linear_1?dependency=1335474237"/><parameters><parameter><name v="rotation"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1482591041"/><identifier v="Simple_Gradient"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1488360202"/><GUILayout><gpos v="-496 -1055.22864 0"/></GUILayout><compOutputs><compOutput><uid v="1482590975"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="Input_1"/><connRef v="1488360201"/><connRefOutput v="1482591041"/></connection></connections><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1149349207"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.610000014"/></paramValue></parameter><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0700000003"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1482590975"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1488360204"/><GUILayout><gpos v="-784 -588.274292 0"/></GUILayout><compOutputs><compOutput><uid v="1487668231"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1488360191"/></connection></connections><compImplementation><compFilter><filter v="transformation"/><parameters><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.0760999992 -0.0188999996"/></paramValue></parameter><parameter><name v="matrix22"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.939899981 -0.341500014 0.341500014 0.939899981"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488360205"/><GUILayout><gpos v="1904 336 0"/></GUILayout><compOutputs><compOutput><uid v="1488297734"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input"/><connRef v="1488467113"/><connRefOutput v="1488467114"/></connection></connections><compImplementation><compInstance><path v="pkg:///hbao?dependency=1297162667"/><parameters><parameter><name v="height_depth"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0299999993"/></paramValue></parameter><parameter><name v="samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="16"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1488297734"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1488360206"/><GUILayout><gpos v="2096 -176 0"/></GUILayout><connections><connection><identifier v="inputNodeOutput"/><connRef v="1488456062"/><connRefOutput v="1488456063"/></connection></connections><compImplementation><compOutputBridge><output v="1488360207"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1488360208"/><GUILayout><gpos v="2096 -48 0"/></GUILayout><connections><connection><identifier v="inputNodeOutput"/><connRef v="1488457093"/><connRefOutput v="1488457094"/></connection></connections><compImplementation><compOutputBridge><output v="1488360209"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1488360210"/><GUILayout><gpos v="2096 80 0"/></GUILayout><connections><connection><identifier v="inputNodeOutput"/><connRef v="1488458025"/><connRefOutput v="1488458026"/></connection></connections><compImplementation><compOutputBridge><output v="1488360211"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1488360212"/><GUILayout><gpos v="2096 208 0"/></GUILayout><connections><connection><identifier v="inputNodeOutput"/><connRef v="1488472366"/><connRefOutput v="1488472367"/></connection></connections><compImplementation><compOutputBridge><output v="1488360213"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1488360214"/><GUILayout><gpos v="2096 336 0"/></GUILayout><connections><connection><identifier v="inputNodeOutput"/><connRef v="1488360205"/><connRefOutput v="1488297734"/></connection></connections><compImplementation><compOutputBridge><output v="1488360215"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1488372867"/><GUILayout><gpos v="368 -720.236572 0"/></GUILayout><compOutputs><compOutput><uid v="1488372868"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="destination"/><connRef v="1488360153"/><connRefOutput v="1482625016"/></connection><connection><identifier v="source"/><connRef v="1488374694"/><connRefOutput v="1488374695"/></connection><connection><identifier v="opacity"/><connRef v="1488374207"/><connRefOutput v="1488374208"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1488374207"/><GUILayout><gpos v="16 -666.666687 0"/></GUILayout><compOutputs><compOutput><uid v="1488374208"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="pattern_input"/><connRef v="1488454744"/><connRefOutput v="1488454745"/></connection></connections><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1486875900"/><parameters><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.88999999"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.299999982"/></paramValue></parameter><parameter><name v="scale_map_multiplier"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0799999982"/></paramValue></parameter><parameter><name v="rotation_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.629999995"/></paramValue></parameter><parameter><name v="rotation_map_multiplier"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.439999998"/></paramValue></parameter><parameter><name v="mask_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.699999988"/></paramValue></parameter><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="15"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="15"/></paramValue></parameter><parameter><name v="offset_type"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="5.5"/></paramValue></parameter><parameter><name v="pattern_input_number"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1488374208"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1488374694"/><GUILayout><gpos v="193.351959 -735.228699 0"/></GUILayout><compOutputs><compOutput><uid v="1488374695"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1488374207"/><connRefOutput v="1488374208"/></connection></connections><compImplementation><compFilter><filter v="gradient"/><parameters><parameter><name v="smoothness"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter></parameters><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1488391571"/><paramsArrayCells><paramsArrayCell><uid v="1488392840"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="1 1 1 0"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1488392841"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.891304374 0.316102117 0.706239223 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1488392394"/><GUILayout><gpos v="16 -346.666656 0"/></GUILayout><compOutputs><compOutput><uid v="1488374208"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="mask_map_input"/><connRef v="1488374207"/><connRefOutput v="1488374208"/></connection><connection><identifier v="pattern_input"/><connRef v="1488454744"/><connRefOutput v="1488454745"/></connection></connections><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1486875900"/><parameters><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.88999999"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.299999982"/></paramValue></parameter><parameter><name v="scale_map_multiplier"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0799999982"/></paramValue></parameter><parameter><name v="rotation_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.629999995"/></paramValue></parameter><parameter><name v="rotation_map_multiplier"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.439999998"/></paramValue></parameter><parameter><name v="mask_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.850000024"/></paramValue></parameter><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="15"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="15"/></paramValue></parameter><parameter><name v="offset_type"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="6.85999966"/></paramValue></parameter><parameter><name v="pattern_input_number"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="mask_map_invert"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="mask_map_threshold"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.129999995"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1488374208"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1488392790"/><GUILayout><gpos v="368 -432.236603 0"/></GUILayout><compOutputs><compOutput><uid v="1488392791"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="destination"/><connRef v="1488372867"/></connection><connection><identifier v="source"/><connRef v="1488392805"/><connRefOutput v="1488392806"/></connection><connection><identifier v="opacity"/><connRef v="1488392394"/><connRefOutput v="1488374208"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488392805"/><GUILayout><gpos v="208 -447.228699 0"/></GUILayout><compOutputs><compOutput><uid v="1488392806"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1488392394"/><connRefOutput v="1488374208"/></connection></connections><compImplementation><compFilter><filter v="gradient"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1488393549"/><GUILayout><gpos v="16 -41.8953819 0"/></GUILayout><compOutputs><compOutput><uid v="1488374208"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="mask_map_input"/><connRef v="1488374207"/><connRefOutput v="1488374208"/></connection><connection><identifier v="pattern_input"/><connRef v="1488454744"/><connRefOutput v="1488454745"/></connection></connections><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1486875900"/><parameters><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="2.5"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.299999982"/></paramValue></parameter><parameter><name v="scale_map_multiplier"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0799999982"/></paramValue></parameter><parameter><name v="rotation_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.629999995"/></paramValue></parameter><parameter><name v="rotation_map_multiplier"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.439999998"/></paramValue></parameter><parameter><name v="mask_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.779999971"/></paramValue></parameter><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="15"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="15"/></paramValue></parameter><parameter><name v="offset_type"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="pattern_input_number"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="mask_map_invert"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="mask_map_threshold"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.129999995"/></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="9.69999981"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1488374208"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1488393558"/><GUILayout><gpos v="208 -191.228699 0"/></GUILayout><compOutputs><compOutput><uid v="1488393559"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1488393549"/><connRefOutput v="1488374208"/></connection></connections><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1488393912"/><paramsArrayCells><paramsArrayCell><uid v="1488393913"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0 0 0 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1488393914"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0 0.799621999 0.978260875 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1488393567"/><GUILayout><gpos v="368 -176.236603 0"/></GUILayout><compOutputs><compOutput><uid v="1488393568"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="destination"/><connRef v="1488392790"/></connection><connection><identifier v="source"/><connRef v="1488393558"/><connRefOutput v="1488393559"/></connection><connection><identifier v="opacity"/><connRef v="1488393549"/><connRefOutput v="1488374208"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1488454744"/><GUILayout><gpos v="-272 -589.717834 0"/></GUILayout><compOutputs><compOutput><uid v="1488454745"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1488360199"/><connRefOutput v="1487669407"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.508928597 0.508928597 0.508928597 0.5"/></paramValue></parameter><parameter><name v="levelinlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.391581625 0.391581625 0.391581625 0"/></paramValue></parameter><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.786989808 0.786989808 0.786989808 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488455605"/><GUILayout><gpos v="-208 368 501"/></GUILayout><compOutputs><compOutput><uid v="1488455606"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1488360175"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.434948981 0.434948981 0.434948981 0.5"/></paramValue></parameter><parameter><name v="levelinlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.445153058 0.445153058 0.445153058 0"/></paramValue></parameter><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.516581655 0.516581655 0.516581655 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488455948"/><GUILayout><gpos v="1.93164551 634.666626 0"/></GUILayout><compOutputs><compOutput><uid v="1488374208"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="pattern_input"/><connRef v="1488455605"/><connRefOutput v="1488455606"/></connection><connection><identifier v="mask_map_input"/><connRef v="1488455949"/><connRefOutput v="1488374208"/></connection></connections><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1486875900"/><parameters><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.54999995"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.299999982"/></paramValue></parameter><parameter><name v="scale_map_multiplier"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0799999982"/></paramValue></parameter><parameter><name v="rotation_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.629999995"/></paramValue></parameter><parameter><name v="rotation_map_multiplier"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.439999998"/></paramValue></parameter><parameter><name v="mask_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.850000024"/></paramValue></parameter><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="15"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="15"/></paramValue></parameter><parameter><name v="offset_type"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="6.85999966"/></paramValue></parameter><parameter><name v="pattern_input_number"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="mask_map_invert"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="mask_map_threshold"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.129999995"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1488374208"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1488455949"/><GUILayout><gpos v="1.93164551 325.333344 501"/></GUILayout><compOutputs><compOutput><uid v="1488374208"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="pattern_input"/><connRef v="1488455605"/><connRefOutput v="1488455606"/></connection></connections><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1486875900"/><parameters><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.30999994"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.299999982"/></paramValue></parameter><parameter><name v="scale_map_multiplier"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0799999982"/></paramValue></parameter><parameter><name v="rotation_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.629999995"/></paramValue></parameter><parameter><name v="rotation_map_multiplier"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.439999998"/></paramValue></parameter><parameter><name v="mask_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.699999988"/></paramValue></parameter><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="15"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="15"/></paramValue></parameter><parameter><name v="offset_type"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="5.5"/></paramValue></parameter><parameter><name v="pattern_input_number"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1488374208"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1488455950"/><GUILayout><gpos v="1.93164551 950.104614 0"/></GUILayout><compOutputs><compOutput><uid v="1488374208"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="pattern_input"/><connRef v="1488455605"/><connRefOutput v="1488455606"/></connection><connection><identifier v="mask_map_input"/><connRef v="1488455948"/><connRefOutput v="1488374208"/></connection></connections><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1486875900"/><parameters><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.91999996"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.299999982"/></paramValue></parameter><parameter><name v="scale_map_multiplier"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0799999982"/></paramValue></parameter><parameter><name v="rotation_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.629999995"/></paramValue></parameter><parameter><name v="rotation_map_multiplier"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.439999998"/></paramValue></parameter><parameter><name v="mask_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.779999971"/></paramValue></parameter><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="15"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="15"/></paramValue></parameter><parameter><name v="offset_type"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="pattern_input_number"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="mask_map_invert"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="mask_map_threshold"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.129999995"/></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="9.69999981"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1488374208"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1488456019"/><GUILayout><gpos v="240 432 501"/></GUILayout><compOutputs><compOutput><uid v="1488456020"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="destination"/><connRef v="1488455949"/><connRefOutput v="1488374208"/></connection><connection><identifier v="source"/><connRef v="1488455948"/><connRefOutput v="1488374208"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488456040"/><GUILayout><gpos v="240 848 501"/></GUILayout><compOutputs><compOutput><uid v="1488456041"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="destination"/><connRef v="1488456019"/></connection><connection><identifier v="source"/><connRef v="1488455950"/><connRefOutput v="1488374208"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488456062"/><GUILayout><gpos v="784 48 501"/></GUILayout><compOutputs><compOutput><uid v="1488456063"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="opacity"/><connRef v="1488456040"/><connRefOutput v="1488456041"/></connection><connection><identifier v="destination"/><connRef v="1488393567"/><connRefOutput v="1488393568"/></connection><connection><identifier v="source"/><connRef v="1488456190"/><connRefOutput v="1488456191"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1488456172"/><GUILayout><gpos v="478.854156 674.167969 501"/></GUILayout><compOutputs><compOutput><uid v="1488456173"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="destination"/><connRef v="1488456019"/></connection><connection><identifier v="source"/><connRef v="1488456040"/><connRefOutput v="1488456041"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488456190"/><GUILayout><gpos v="606.026367 304 501"/></GUILayout><compOutputs><compOutput><uid v="1488456191"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1488456172"/></connection></connections><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1488456295"/><paramsArrayCells><paramsArrayCell><uid v="1488456301"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0 0 0 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1488456302"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.904347897 1 0 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1488457084"/><GUILayout><gpos v="944 144 501"/></GUILayout><compOutputs><compOutput><uid v="1488457085"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1488456062"/></connection></connections><compImplementation><compFilter><filter v="grayscaleconversion"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1488457093"/><GUILayout><gpos v="1904 -48 501"/></GUILayout><compOutputs><compOutput><uid v="1488457094"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1488467113"/><connRefOutput v="1488467114"/></connection></connections><compImplementation><compFilter><filter v="normal"/><parameters><parameter><name v="input2alpha"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488458025"/><GUILayout><gpos v="1904 80 501"/></GUILayout><compOutputs><compOutput><uid v="1488458026"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1488467113"/><connRefOutput v="1488467114"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.360969394 0.360969394 0.360969394 0.5"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488458084"/><GUILayout><gpos v="1072 144 501"/></GUILayout><compOutputs><compOutput><uid v="1488458085"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="source"/><connRef v="1488457084"/><connRefOutput v="1488457085"/></connection><connection><identifier v="destination"/><connRef v="1488392394"/><connRefOutput v="1488374208"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488462596"/><GUILayout><gpos v="1200 144 501"/></GUILayout><compOutputs><compOutput><uid v="1488462597"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="destination"/><connRef v="1488458084"/><connRefOutput v="1488458085"/></connection><connection><identifier v="source"/><connRef v="1488393549"/><connRefOutput v="1488374208"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488467113"/><GUILayout><gpos v="1328 144 501"/></GUILayout><compOutputs><compOutput><uid v="1488467114"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="destination"/><connRef v="1488462596"/><connRefOutput v="1488462597"/></connection><connection><identifier v="source"/><connRef v="1488374207"/><connRefOutput v="1488374208"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488472366"/><GUILayout><gpos v="1904 208 501"/></GUILayout><compOutputs><compOutput><uid v="1488472367"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="opacity"/><connRef v="1488473276"/><connRefOutput v="1488473277"/></connection><connection><identifier v="destination"/><connRef v="1488473276"/><connRefOutput v="1488473277"/></connection><connection><identifier v="source"/><connRef v="1488467113"/><connRefOutput v="1488467114"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488473276"/><GUILayout><gpos v="1277.20203 -80 501"/></GUILayout><compOutputs><compOutput><uid v="1488473277"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1488360172"/><connRefOutput v="1482609709"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.0727040842 0.0727040842 0.0727040842 0.5"/></paramValue></parameter><parameter><name v="levelouthigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.465561211 0.465561211 0.465561211 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode></compNodes><baseParameters/><options><option><name v="defaultParentSize"/><value v="11x11"/></option><option><name v="export/fromGraph/autoExport"/><value v="false"/></option><option><name v="export/fromGraph/destination"/><value v="C:/Users/<USER>/Desktop"/></option><option><name v="export/fromGraph/extension"/><value v="png"/></option><option><name v="export/fromGraph/outputs/ambientOcclusion"/><value v="true"/></option><option><name v="export/fromGraph/outputs/basecolor"/><value v="true"/></option><option><name v="export/fromGraph/outputs/height"/><value v="true"/></option><option><name v="export/fromGraph/outputs/normal"/><value v="true"/></option><option><name v="export/fromGraph/outputs/roughness"/><value v="true"/></option><option><name v="export/fromGraph/outputsColorspace/ambientOcclusion"/><value v="Raw"/></option><option><name v="export/fromGraph/outputsColorspace/basecolor"/><value v="sRGB"/></option><option><name v="export/fromGraph/outputsColorspace/height"/><value v="Raw"/></option><option><name v="export/fromGraph/outputsColorspace/normal"/><value v="Raw"/></option><option><name v="export/fromGraph/outputsColorspace/roughness"/><value v="Raw"/></option><option><name v="export/fromGraph/pattern"/><value v="$(graph)_$(identifier)"/></option></options><root><rootOutputs><rootOutput><output v="1488360207"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1488360209"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1488360211"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1488360213"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1488360215"/><format v="0"/><usertag v=""/></rootOutput></rootOutputs></root></graph></content></package>
