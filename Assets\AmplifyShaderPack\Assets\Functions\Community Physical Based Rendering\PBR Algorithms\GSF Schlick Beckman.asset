%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: GSF <PERSON><PERSON><PERSON>man
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor v1.9.5.1\n// Available at the
    Unity Asset Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=19501\nNode;AmplifyShaderEditor.CommentaryNode;49;-1664,-256;Inherit;False;2176.19;848.9688;Math;23;147;148;146;0;41;40;35;143;26;33;2;142;28;29;32;1;39;34;27;45;42;44;43;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;50;-2785.525,-256;Inherit;False;1058.748;583.8187;Dot
    Products;9;78;115;114;108;107;100;99;144;145;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;48;-1664,-608;Inherit;False;681.7852;285.4783;GSF
    Schlick Beckman Node;1;47;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;43;-944,-208;Inherit;False;193.6633;126.8163;NdotV;1;138;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;44;-944,-64;Inherit;False;193.6633;127.8163;NdotL;1;139;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;3;-1664,-800;Inherit;False;525.8999;174.2449;Credits;;0,0,0,0.5019608;Method
    by Jordan Stevens$Link: https://www.jordanstevenstechart.com/physically-based-rendering$$Node
    Made by Tiber Legacy$Special Thanks to Dawie;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;42;-704,-160;Inherit;False;191;100;Vectorized;;0,0,0,0.5019608;to
    simplify and Optimize the Algorithm;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;45;-1168,-16;Inherit;False;210.4045;102.1194;Roughness;;0,0,0,1;;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;47;-1344,-560;Inherit;False;313.71;134.7883;;;0,0,0,1;This
    is the Schlick Approximation for the Beckman Function. It works by multiplying
    roughness by the square root of 2/PI - Jordan Stevens;0;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;27;-880,96;Inherit;False;3;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.ScaleAndOffsetNode;34;-544,48;Inherit;False;3;0;FLOAT2;0,0;False;1;FLOAT;1;False;2;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.DynamicAppendNode;39;-704,-48;Inherit;False;FLOAT2;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.GetLocalVarNode;138;-928,-176;Inherit;False;99;NdotV;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;139;-928,-32;Inherit;False;100;NdotL;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;99;-1984,-160;Inherit;True;NdotV;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;100;-1984,64;Inherit;True;NdotL;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;107;-2432,-80;Inherit;False;NdotV;1;2;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;108;-2432,144;Inherit;False;NdotL;1;1;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;114;-2272,-160;Inherit;False;Custom
    Dot Products;True;0;2;0;In 0;In 1;Object;-1;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;115;-2272,64;Inherit;False;Option;False;0;2;0;In
    0;In 1;Instance;114;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;78;-2688,-160;Inherit;False;Normal;3;0;False;1;0;FLOAT3;0,0,1;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RangedFloatNode;1;-1328,192;Inherit;False;Constant;_2PIAprrox;2/PI
    Aprrox;0;0;Create;True;0;0;0;False;0;False;0.7978846;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SqrtOpNode;32;-1264,272;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleDivideOpNode;29;-1424,272;Inherit;False;2;0;FLOAT;2;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.PiNode;28;-1632,272;Inherit;False;1;0;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;142;-1296,384;Inherit;False;PI
    Inv;1;3;False;1;0;FLOAT;0.7978846;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;2;-1120,192;Inherit;False;PI
    Inv Approx;False;0;3;1;Near;Exact;Custom;Object;-1;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;33;-720,64;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;26;-1120,96;Inherit;False;Roughness;1;4;False;1;0;FLOAT;0.5;False;1;FLOAT;0\nNode;AmplifyShaderEditor.StickyNoteNode;143;-1120,320;Inherit;False;261.9292;189.818;PI
    Inv Approx;;0,0,0,1;Near:$0.797884560802865$$Exact:$sqrt(2 / Pi)$$PI Inv Approx$0.7978846$;0;0\nNode;AmplifyShaderEditor.FunctionNode;144;-2496,-160;Inherit;False;NdotV;-1;;3;65be4ec269a534f4e8867af2f999aaa9;4,27,1,377,0,452,0,451,0;3;230;FLOAT3;0,0,1;False;443;FLOAT3;0,0,0;False;415;FLOAT;1E-37;False;1;FLOAT;23\nNode;AmplifyShaderEditor.FunctionNode;145;-2496,64;Inherit;False;NdotL;-1;;4;6d7ee1aa8c79587448475fa2b37affc1;5,27,1,381,0,460,0,461,0,463,0;4;230;FLOAT3;0,0,1;False;462;FLOAT3;0,0,0;False;454;FLOAT3;0,0,0;False;416;FLOAT;1E-37;False;1;FLOAT;111\nNode;AmplifyShaderEditor.SimpleDivideOpNode;35;-144,-48;Inherit;False;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.BreakToComponentsNode;40;0,-48;Inherit;False;FLOAT2;1;0;FLOAT2;0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;41;112,-48;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;146;-480,176;Inherit;False;Constant;_0_1;_0_1;0;0;Create;True;0;0;0;False;0;False;0.1;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.StickyNoteNode;148;-288,144;Inherit;False;160;100;Max
    0.1;;0,0,0,0.5019608;To Prevent NaN in Division and Output;0;0\nNode;AmplifyShaderEditor.SimpleMaxOpNode;147;-288,48;Inherit;False;2;0;FLOAT2;0,0;False;1;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.FunctionOutput;0;272,-48;Inherit;True;True;-1;Out;0;False;1;0;FLOAT;0;False;1;FLOAT;0\nWireConnection;27;0;26;0\nWireConnection;27;1;26;0\nWireConnection;27;2;2;0\nWireConnection;34;0;39;0\nWireConnection;34;1;33;0\nWireConnection;34;2;27;0\nWireConnection;39;0;138;0\nWireConnection;39;1;139;0\nWireConnection;99;0;114;0\nWireConnection;100;0;115;0\nWireConnection;114;0;144;23\nWireConnection;114;1;107;0\nWireConnection;115;0;145;111\nWireConnection;115;1;108;0\nWireConnection;32;0;29;0\nWireConnection;29;1;28;0\nWireConnection;2;0;1;0\nWireConnection;2;1;32;0\nWireConnection;2;2;142;0\nWireConnection;33;0;27;0\nWireConnection;144;230;78;0\nWireConnection;145;230;78;0\nWireConnection;35;0;39;0\nWireConnection;35;1;147;0\nWireConnection;40;0;35;0\nWireConnection;41;0;40;0\nWireConnection;41;1;40;1\nWireConnection;147;0;34;0\nWireConnection;147;1;146;0\nWireConnection;0;0;41;0\nASEEND*/\n//CHKSM=7A524BF3AD8319658E6AD41FC9FE55CD95B0B782"
  m_functionName: 
  m_description: 'GSF Schlick Beckman Node

'
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 0
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_customEdited: 0
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 0
  m_headerStyle: 2
  m_headerColor: {r: 0.7529412, g: 0.6784314, b: 0, a: 1}
  m_customNodeCategory: Amplify Shader Pack
  m_previewPosition: 0
  m_hidden: 0
  m_url: 
