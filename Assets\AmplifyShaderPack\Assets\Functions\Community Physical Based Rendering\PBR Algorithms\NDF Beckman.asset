%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: NDF Beckman
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor v1.9.5.1\n// Available at the
    Unity Asset Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=19501\nNode;AmplifyShaderEditor.CommentaryNode;40;-3328,1536;Inherit;False;1511.39;648.7653;Math;18;0;342;199;198;193;341;340;201;195;202;226;196;262;194;191;192;339;348;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;264;-4392.002,1536;Inherit;False;997.3918;388.8805;Dot
    Products;5;346;334;329;324;347;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;339;-3168,1728;Inherit;False;195;141.8666;NdotH;1;204;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;263;-3328,1184;Inherit;False;763.1187;263.315;NDF
    Beckman Node;1;41;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;41;-3008,1248;Inherit;False;404.0176;138.4137;;;0,0,0,1;The
    Beckman Normal Distribution function Accounts for the roughness, as well as the
    dot product between our normal and half direction -Jorden Stevens;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;262;-3280,2016;Inherit;False;192.1113;114.3586;Roughness;;0,0,0,1;;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;343;-3328,992;Inherit;False;512.2102;155.358;Credits;;0.********,0.********,0.********,0.5019608;Method
    by Jordan Stevens$Link: https://www.jordanstevenstechart.com/physically-based-rendering$$Node
    Made by Tiber Legacy$Special Thanks to Dawie;0;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;324;-3648,1632;Inherit;True;NdotH;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;329;-4096,1712;Inherit;False;NdotH;1;1;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;334;-3936,1632;Inherit;False;Custom
    Dot Products;True;0;2;1;In 0;In 1;Object;-1;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;346;-4320,1632;Inherit;False;Normal;3;0;False;1;0;FLOAT3;0,0,1;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionNode;347;-4160,1632;Inherit;False;NdotH;-1;;3;fadb6690f179c1448859861f76ebb061;7,27,1,373,0,451,0,463,0,460,0,461,0,452,0;6;230;FLOAT3;0,0,1;False;443;FLOAT3;0,0,0;False;462;FLOAT3;0,0,0;False;454;FLOAT3;0,0,0;False;453;FLOAT3;0,0,0;False;417;FLOAT;1E-37;False;1;FLOAT;109\nNode;AmplifyShaderEditor.StickyNoteNode;348;-2912,2032;Inherit;False;223;100;Max
    .0001;;0,0,0,0.5019608;prevents the Specular dot from \"disappearing\" at 'Low/Zero
    Roughness\";0;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;191;-2768,1920;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleDivideOpNode;194;-2576,1776;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;196;-2768,1616;Inherit;False;4;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;202;-2944,1776;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;340;-2944,1696;Inherit;False;Constant;_One;One;0;0;Create;True;0;0;0;False;0;False;1;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.ReciprocalOpNode;341;-2592,1616;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.ExpOpNode;193;-2432,1776;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;198;-2288,1616;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMaxOpNode;199;-2112,1616;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;1E-06;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;342;-2288,1712;Inherit;False;Constant;_NonZero;NonZero;0;0;Create;True;0;0;0;False;0;False;1E-37;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;204;-3152,1776;Inherit;False;324;NdotH;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;192;-2768,1776;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.PiNode;201;-2976,1616;Inherit;False;1;0;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMaxOpNode;349;-2912,1920;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0.0001;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;195;-3088,1920;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;226;-3280,1920;Inherit;False;Roughness;1;2;False;1;0;FLOAT;0.5;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;0;-1984,1616;Inherit;True;True;-1;Out;0;True;1;0;FLOAT;0;False;1;FLOAT;0\nWireConnection;324;0;334;0\nWireConnection;334;0;347;109\nWireConnection;334;1;329;0\nWireConnection;347;230;346;0\nWireConnection;191;0;202;0\nWireConnection;191;1;349;0\nWireConnection;194;0;192;0\nWireConnection;194;1;191;0\nWireConnection;196;0;201;0\nWireConnection;196;1;202;0\nWireConnection;196;2;202;0\nWireConnection;196;3;349;0\nWireConnection;202;0;204;0\nWireConnection;202;1;204;0\nWireConnection;341;0;196;0\nWireConnection;193;0;194;0\nWireConnection;198;0;341;0\nWireConnection;198;1;193;0\nWireConnection;199;0;198;0\nWireConnection;199;1;342;0\nWireConnection;192;0;202;0\nWireConnection;192;1;340;0\nWireConnection;349;0;195;0\nWireConnection;195;0;226;0\nWireConnection;195;1;226;0\nWireConnection;0;0;199;0\nASEEND*/\n//CHKSM=D7869DCA2ECFC0F716B2462C2AC44097AD510025"
  m_functionName: 
  m_description: 'NDF Beckman Node

'
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 0
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_customEdited: 0
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 0
  m_headerStyle: 2
  m_headerColor: {r: 0.7529412, g: 0.6784314, b: 0, a: 1}
  m_customNodeCategory: Amplify Shader Pack
  m_previewPosition: 0
  m_hidden: 0
  m_url: 
