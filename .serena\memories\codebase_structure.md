# BTR Codebase Structure

## Main Directory Structure
```
Assets/
├── _Scripts/                    # Main game scripts
│   ├── Management/              # Core managers (SceneManagerBTR, GameManager, etc.)
│   ├── EnemySystem/            # Enemy AI and behavior systems
│   ├── Projectiles/            # Projectile system with pooling
│   ├── Player/                 # Player movement, combat, controls
│   ├── FMOD/                   # Audio system integration
│   ├── Events/                 # Event system architecture
│   ├── UI/                     # User interface systems
│   ├── Utilities/              # Helper classes and extensions
│   └── Documentation/          # System architecture docs
├── Stylo/                      # Custom framework modules
│   ├── Epoch/                  # Time management system
│   ├── Flux/                   # Visual effects and datamoshing
│   ├── MenUI/                  # UI Toolkit menu system
│   ├── BFI/                    # Black Frame Insertion
│   └── Cadance/                # Rhythm/audio system
├── _Scenes/                    # Game scenes
│   └── Levels/                 # Level scenes (Ouroboros series)
├── _Prefabs/                   # Reusable game objects
├── _Scriptable Objects/        # Configuration assets
└── Settings/                   # Project settings and configurations
```

## Key System Locations
- **Scene Management**: `Assets/_Scripts/Management/SceneManagerBTR.cs`
- **Projectile System**: `Assets/_Scripts/Projectiles/`
- **Enemy System**: `Assets/_Scripts/EnemySystem/`
- **Player System**: `Assets/_Scripts/Player/`
- **Audio Management**: `Assets/_Scripts/FMOD/` and `Assets/_Scripts/Management/AudioManager.cs`
- **Time Management**: `Assets/Stylo/Epoch/` and `Assets/_Scripts/Management/TimeManager.cs`
- **UI System**: `Assets/Stylo/MenUI/` and `Assets/_Scripts/UI/`

## Assembly Definitions
- **Stylo modules** have their own assembly definitions for modularity
- **Third-party assets** maintain separate assemblies
- **Main game scripts** use default assembly with selective references

## Documentation Structure
Each major system includes:
- **Master Documentation**: Comprehensive system overview
- **Architecture Diagrams**: Visual system relationships
- **Usage Guides**: Implementation examples
- **Setup Guides**: Configuration instructions
- **Migration Guides**: Update procedures