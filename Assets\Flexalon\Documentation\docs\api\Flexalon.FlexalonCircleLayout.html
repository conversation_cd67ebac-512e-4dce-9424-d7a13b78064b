﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class FlexalonCircleLayout
 | Flexalon 3D Layouts </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class FlexalonCircleLayout
 | Flexalon 3D Layouts ">
    <meta name="generator" content="docfx ">
  
    <link rel="shortcut icon" href="images/favicon.svg">
    <link rel="stylesheet" href="styles/docfx.vendor.css">
    <link rel="stylesheet" href="styles/docfx.css">
    <link rel="stylesheet" href="styles/main.css">
    <meta property="docfx:navrel" content="tochead.html">
    <meta property="docfx:tocrel" content="toc.html">
  
  <meta property="docfx:rel" content="">
  
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="https://www.flexalon.com/">
                <img id="logo" class="svg" src="images/logo_32x32.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first="First" data-prev="Previous" data-next="Next" data-last="Last"></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">

        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="Flexalon.FlexalonCircleLayout">


  <h1 id="Flexalon_FlexalonCircleLayout" data-uid="Flexalon.FlexalonCircleLayout" class="text-break">Class FlexalonCircleLayout
</h1>
  <div class="markdown level0 summary"><p>Use a circle layout to position children along a circle or spiral.</p>
</div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">object</span></div>
    <div class="level1"><span class="xref">UnityEngine.Object</span></div>
    <div class="level2"><span class="xref">UnityEngine.Component</span></div>
    <div class="level3"><span class="xref">UnityEngine.Behaviour</span></div>
    <div class="level4"><span class="xref">UnityEngine.MonoBehaviour</span></div>
    <div class="level5"><a class="xref" href="Flexalon.FlexalonComponent.html">FlexalonComponent</a></div>
    <div class="level6"><a class="xref" href="Flexalon.LayoutBase.html">LayoutBase</a></div>
    <div class="level7"><span class="xref">FlexalonCircleLayout</span></div>
  </div>
  <div class="implements">
    <h5>Implements</h5>
    <div><a class="xref" href="Flexalon.Layout.html">Layout</a></div>
  </div>
  <div class="inheritedMembers">
    <h5>Inherited Members</h5>
    <div>
      <a class="xref" href="Flexalon.LayoutBase.html#Flexalon_LayoutBase_DoOnEnable">LayoutBase.DoOnEnable()</a>
    </div>
    <div>
      <a class="xref" href="Flexalon.LayoutBase.html#Flexalon_LayoutBase_DoOnDisable">LayoutBase.DoOnDisable()</a>
    </div>
    <div>
      <a class="xref" href="Flexalon.LayoutBase.html#Flexalon_LayoutBase_ResetProperties">LayoutBase.ResetProperties()</a>
    </div>
    <div>
      <a class="xref" href="Flexalon.LayoutBase.html#Flexalon_LayoutBase_SetChildrenFillShrinkSize_Flexalon_FlexalonNode_UnityEngine_Vector3_UnityEngine_Vector3_">LayoutBase.SetChildrenFillShrinkSize(FlexalonNode, Vector3, Vector3)</a>
    </div>
    <div>
      <a class="xref" href="Flexalon.FlexalonComponent.html#Flexalon_FlexalonComponent__node">FlexalonComponent._node</a>
    </div>
    <div>
      <a class="xref" href="Flexalon.FlexalonComponent.html#Flexalon_FlexalonComponent_Node">FlexalonComponent.Node</a>
    </div>
    <div>
      <a class="xref" href="Flexalon.FlexalonComponent.html#Flexalon_FlexalonComponent_MarkDirty">FlexalonComponent.MarkDirty()</a>
    </div>
    <div>
      <a class="xref" href="Flexalon.FlexalonComponent.html#Flexalon_FlexalonComponent_ForceUpdate">FlexalonComponent.ForceUpdate()</a>
    </div>
    <div>
      <a class="xref" href="Flexalon.FlexalonComponent.html#Flexalon_FlexalonComponent_UpdateProperties">FlexalonComponent.UpdateProperties()</a>
    </div>
    <div>
      <a class="xref" href="Flexalon.FlexalonComponent.html#Flexalon_FlexalonComponent_DoUpdate">FlexalonComponent.DoUpdate()</a>
    </div>
  </div>
  <h5 id="Flexalon_FlexalonCircleLayout_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">[AddComponentMenu(&quot;Flexalon/Flexalon Circle Layout&quot;)]
[HelpURL(&quot;https://www.flexalon.com/docs/circleLayout&quot;)]
public class FlexalonCircleLayout : LayoutBase, Layout</code></pre>
  </div>
  <h3 id="properties">Properties
</h3>
  <a id="Flexalon_FlexalonCircleLayout_InitialRadius_" data-uid="Flexalon.FlexalonCircleLayout.InitialRadius*"></a>
  <h4 id="Flexalon_FlexalonCircleLayout_InitialRadius" data-uid="Flexalon.FlexalonCircleLayout.InitialRadius">InitialRadius</h4>
  <div class="markdown level1 summary"><p>Determines the initial radius of the circle.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public FlexalonCircleLayout.InitialRadiusOptions InitialRadius { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.FlexalonCircleLayout.html">FlexalonCircleLayout</a>.<a class="xref" href="Flexalon.FlexalonCircleLayout.InitialRadiusOptions.html">InitialRadiusOptions</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonCircleLayout_Plane_" data-uid="Flexalon.FlexalonCircleLayout.Plane*"></a>
  <h4 id="Flexalon_FlexalonCircleLayout_Plane" data-uid="Flexalon.FlexalonCircleLayout.Plane">Plane</h4>
  <div class="markdown level1 summary"><p>Determines on which plane to create the circle.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Plane Plane { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.Plane.html">Plane</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonCircleLayout_PlaneAlign_" data-uid="Flexalon.FlexalonCircleLayout.PlaneAlign*"></a>
  <h4 id="Flexalon_FlexalonCircleLayout_PlaneAlign" data-uid="Flexalon.FlexalonCircleLayout.PlaneAlign">PlaneAlign</h4>
  <div class="markdown level1 summary"><p>Aligns the layout with the size set by the Flexalon Object Component.
For a circle, this will align each individual object in the layout. For a spiral,
this will align the entire spiral.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Align PlaneAlign { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.Align.html">Align</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonCircleLayout_Radius_" data-uid="Flexalon.FlexalonCircleLayout.Radius*"></a>
  <h4 id="Flexalon_FlexalonCircleLayout_Radius" data-uid="Flexalon.FlexalonCircleLayout.Radius">Radius</h4>
  <div class="markdown level1 summary"><p>Initial radius of the circle.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float Radius { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">float</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonCircleLayout_RadiusStep_" data-uid="Flexalon.FlexalonCircleLayout.RadiusStep*"></a>
  <h4 id="Flexalon_FlexalonCircleLayout_RadiusStep" data-uid="Flexalon.FlexalonCircleLayout.RadiusStep">RadiusStep</h4>
  <div class="markdown level1 summary"><p>Determines how much the radius should change at each interval.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float RadiusStep { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">float</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonCircleLayout_RadiusType_" data-uid="Flexalon.FlexalonCircleLayout.RadiusType*"></a>
  <h4 id="Flexalon_FlexalonCircleLayout_RadiusType" data-uid="Flexalon.FlexalonCircleLayout.RadiusType">RadiusType</h4>
  <div class="markdown level1 summary"><p>Determines if and how the radius changes.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public FlexalonCircleLayout.RadiusOptions RadiusType { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.FlexalonCircleLayout.html">FlexalonCircleLayout</a>.<a class="xref" href="Flexalon.FlexalonCircleLayout.RadiusOptions.html">RadiusOptions</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonCircleLayout_Rotate_" data-uid="Flexalon.FlexalonCircleLayout.Rotate*"></a>
  <h4 id="Flexalon_FlexalonCircleLayout_Rotate" data-uid="Flexalon.FlexalonCircleLayout.Rotate">Rotate</h4>
  <div class="markdown level1 summary"><p>Determines how children should be rotated.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public FlexalonCircleLayout.RotateOptions Rotate { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.FlexalonCircleLayout.html">FlexalonCircleLayout</a>.<a class="xref" href="Flexalon.FlexalonCircleLayout.RotateOptions.html">RotateOptions</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonCircleLayout_SpacingDegrees_" data-uid="Flexalon.FlexalonCircleLayout.SpacingDegrees*"></a>
  <h4 id="Flexalon_FlexalonCircleLayout_SpacingDegrees" data-uid="Flexalon.FlexalonCircleLayout.SpacingDegrees">SpacingDegrees</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float SpacingDegrees { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">float</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonCircleLayout_SpacingType_" data-uid="Flexalon.FlexalonCircleLayout.SpacingType*"></a>
  <h4 id="Flexalon_FlexalonCircleLayout_SpacingType" data-uid="Flexalon.FlexalonCircleLayout.SpacingType">SpacingType</h4>
  <div class="markdown level1 summary"><p>Determines how the space between children is distributed.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public FlexalonCircleLayout.SpacingOptions SpacingType { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.FlexalonCircleLayout.html">FlexalonCircleLayout</a>.<a class="xref" href="Flexalon.FlexalonCircleLayout.SpacingOptions.html">SpacingOptions</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonCircleLayout_Spiral_" data-uid="Flexalon.FlexalonCircleLayout.Spiral*"></a>
  <h4 id="Flexalon_FlexalonCircleLayout_Spiral" data-uid="Flexalon.FlexalonCircleLayout.Spiral">Spiral</h4>
  <div class="markdown level1 summary"><p>If checked, positions each object at increasing heights to form a spiral.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool Spiral { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">bool</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonCircleLayout_SpiralSpacing_" data-uid="Flexalon.FlexalonCircleLayout.SpiralSpacing*"></a>
  <h4 id="Flexalon_FlexalonCircleLayout_SpiralSpacing" data-uid="Flexalon.FlexalonCircleLayout.SpiralSpacing">SpiralSpacing</h4>
  <div class="markdown level1 summary"><p>Vertical spacing between objects in the spiral.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float SpiralSpacing { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">float</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonCircleLayout_StartAtDegrees_" data-uid="Flexalon.FlexalonCircleLayout.StartAtDegrees*"></a>
  <h4 id="Flexalon_FlexalonCircleLayout_StartAtDegrees" data-uid="Flexalon.FlexalonCircleLayout.StartAtDegrees">StartAtDegrees</h4>
  <div class="markdown level1 summary"><p>By default, the first child will be placed at (radius, 0, 0).
Start At Degrees value will add an offset all children around the circle.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float StartAtDegrees { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">float</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
</h3>
  <a id="Flexalon_FlexalonCircleLayout_Arrange_" data-uid="Flexalon.FlexalonCircleLayout.Arrange*"></a>
  <h4 id="Flexalon_FlexalonCircleLayout_Arrange_Flexalon_FlexalonNode_UnityEngine_Vector3_" data-uid="Flexalon.FlexalonCircleLayout.Arrange(Flexalon.FlexalonNode,UnityEngine.Vector3)">Arrange(FlexalonNode, Vector3)</h4>
  <div class="markdown level1 summary"><p>Position the children of node within the available bounds.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override void Arrange(FlexalonNode node, Vector3 layoutSize)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.FlexalonNode.html">FlexalonNode</a></td>
        <td><span class="parametername">node</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><span class="parametername">layoutSize</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="overrides">Overrides</h5>
  <div><a class="xref" href="Flexalon.LayoutBase.html#Flexalon_LayoutBase_Arrange_Flexalon_FlexalonNode_UnityEngine_Vector3_">LayoutBase.Arrange(FlexalonNode, Vector3)</a></div>
  <a id="Flexalon_FlexalonCircleLayout_Initialize_" data-uid="Flexalon.FlexalonCircleLayout.Initialize*"></a>
  <h4 id="Flexalon_FlexalonCircleLayout_Initialize" data-uid="Flexalon.FlexalonCircleLayout.Initialize">Initialize()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected override void Initialize()</code></pre>
  </div>
  <h5 class="overrides">Overrides</h5>
  <div><a class="xref" href="Flexalon.LayoutBase.html#Flexalon_LayoutBase_Initialize">LayoutBase.Initialize()</a></div>
  <a id="Flexalon_FlexalonCircleLayout_Measure_" data-uid="Flexalon.FlexalonCircleLayout.Measure*"></a>
  <h4 id="Flexalon_FlexalonCircleLayout_Measure_Flexalon_FlexalonNode_UnityEngine_Vector3_UnityEngine_Vector3_UnityEngine_Vector3_" data-uid="Flexalon.FlexalonCircleLayout.Measure(Flexalon.FlexalonNode,UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3)">Measure(FlexalonNode, Vector3, Vector3, Vector3)</h4>
  <div class="markdown level1 summary"><p>Perform minimal work to determine what the size of node and available size for node's children.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override Bounds Measure(FlexalonNode node, Vector3 size, Vector3 min, Vector3 max)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.FlexalonNode.html">FlexalonNode</a></td>
        <td><span class="parametername">node</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><span class="parametername">size</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><span class="parametername">min</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><span class="parametername">max</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">UnityEngine.Bounds</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="overrides">Overrides</h5>
  <div><a class="xref" href="Flexalon.LayoutBase.html#Flexalon_LayoutBase_Measure_Flexalon_FlexalonNode_UnityEngine_Vector3_UnityEngine_Vector3_UnityEngine_Vector3_">LayoutBase.Measure(FlexalonNode, Vector3, Vector3, Vector3)</a></div>
  <a id="Flexalon_FlexalonCircleLayout_ShrinkFillChildrenToCircleHeight_" data-uid="Flexalon.FlexalonCircleLayout.ShrinkFillChildrenToCircleHeight*"></a>
  <h4 id="Flexalon_FlexalonCircleLayout_ShrinkFillChildrenToCircleHeight_Flexalon_FlexalonNode_System_Single_System_Int32_" data-uid="Flexalon.FlexalonCircleLayout.ShrinkFillChildrenToCircleHeight(Flexalon.FlexalonNode,System.Single,System.Int32)">ShrinkFillChildrenToCircleHeight(FlexalonNode, float, int)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void ShrinkFillChildrenToCircleHeight(FlexalonNode node, float height, int heightAxis)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.FlexalonNode.html">FlexalonNode</a></td>
        <td><span class="parametername">node</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">float</span></td>
        <td><span class="parametername">height</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">int</span></td>
        <td><span class="parametername">heightAxis</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonCircleLayout_ShrinkFillChildrenToDiameter_" data-uid="Flexalon.FlexalonCircleLayout.ShrinkFillChildrenToDiameter*"></a>
  <h4 id="Flexalon_FlexalonCircleLayout_ShrinkFillChildrenToDiameter_Flexalon_FlexalonNode_System_Single_System_Int32_System_Int32_UnityEngine_Vector3_" data-uid="Flexalon.FlexalonCircleLayout.ShrinkFillChildrenToDiameter(Flexalon.FlexalonNode,System.Single,System.Int32,System.Int32,UnityEngine.Vector3)">ShrinkFillChildrenToDiameter(FlexalonNode, float, int, int, Vector3)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void ShrinkFillChildrenToDiameter(FlexalonNode node, float spacing, int circleAxis1, int circleAxis2, Vector3 size)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.FlexalonNode.html">FlexalonNode</a></td>
        <td><span class="parametername">node</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">float</span></td>
        <td><span class="parametername">spacing</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">int</span></td>
        <td><span class="parametername">circleAxis1</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">int</span></td>
        <td><span class="parametername">circleAxis2</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><span class="parametername">size</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonCircleLayout_ShrinkFillChildrenToSpiralHeight_" data-uid="Flexalon.FlexalonCircleLayout.ShrinkFillChildrenToSpiralHeight*"></a>
  <h4 id="Flexalon_FlexalonCircleLayout_ShrinkFillChildrenToSpiralHeight_Flexalon_FlexalonNode_System_Single_System_Int32_System_Single_" data-uid="Flexalon.FlexalonCircleLayout.ShrinkFillChildrenToSpiralHeight(Flexalon.FlexalonNode,System.Single,System.Int32,System.Single)">ShrinkFillChildrenToSpiralHeight(FlexalonNode, float, int, float)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void ShrinkFillChildrenToSpiralHeight(FlexalonNode node, float height, int heightAxis, float size)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.FlexalonNode.html">FlexalonNode</a></td>
        <td><span class="parametername">node</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">float</span></td>
        <td><span class="parametername">height</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">int</span></td>
        <td><span class="parametername">heightAxis</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">float</span></td>
        <td><span class="parametername">size</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonCircleLayout_Upgrade_" data-uid="Flexalon.FlexalonCircleLayout.Upgrade*"></a>
  <h4 id="Flexalon_FlexalonCircleLayout_Upgrade_System_Int32_" data-uid="Flexalon.FlexalonCircleLayout.Upgrade(System.Int32)">Upgrade(int)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected override void Upgrade(int fromVersion)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">int</span></td>
        <td><span class="parametername">fromVersion</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="overrides">Overrides</h5>
  <div><a class="xref" href="Flexalon.FlexalonComponent.html#Flexalon_FlexalonComponent_Upgrade_System_Int32_">FlexalonComponent.Upgrade(int)</a></div>
  <h3 id="implements">Implements</h3>
  <div>
      <a class="xref" href="Flexalon.Layout.html">Layout</a>
  </div>
</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In This Article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      Flexalon 3D Layouts
      
          </div>
        </div>
      </footer>
    </div>

    
    
    
  </body>
</html>
