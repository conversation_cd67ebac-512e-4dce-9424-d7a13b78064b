%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: UV Interior Cubemap
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor v1.9.1.5\n// Available at the
    Unity Asset Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=19105\nNode;AmplifyShaderEditor.StickyNoteNode;1;887.846,-520.9398;Inherit;False;375.0465;133.3052;UV
    Interior Cubemap Node;;0,0,0,1;The UV Interior Cubemap Node generates the direction
    vector used to sample a cubemap. The purpose is to simulate building interiors
    through windows without using any geometry.;0;0\nNode;AmplifyShaderEditor.CommentaryNode;2;-1536.499,-123.2731;Inherit;False;1188.627;842.8054;Hash;15;38;36;15;14;13;12;11;10;9;8;7;6;5;4;3;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;53;-2144.897,-182.4385;Inherit;False;224.3918;105.2247;Room
    Count ;;0,0,0,1;Controls the number of rooms in the building.;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;60;-160.944,24.11759;Inherit;False;286.28;108.0164;Random;;0,0,0,1;Random
    creates variations in the room by using random sides for the left, right, font,
    and back.;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;81;904.8578,-282.7414;Inherit;False;250.0153;160.9592;Cube
    Map;;0,0,0,1;1- Positive X     - Side Wall$2- Negative X   - Side Wall$3- Positive
    Y    - Ceiling$4- Negative Y  - Floor$5- Positive Z    - Back Wall$6- Negative
    Z  - Back Face;0;0\nNode;AmplifyShaderEditor.FunctionSwitch;59;903.6815,-382.4464;Inherit;False;Random;True;0;2;-1;In
    0;In 1;Object;-1;9;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.BreakToComponentsNode;120;1176.246,-375.6107;Inherit;False;FLOAT3;1;0;FLOAT3;0,0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;97;1310.573,-265.36;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;-1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.DynamicAppendNode;121;1452.015,-375.3769;Inherit;False;FLOAT3;4;0;FLOAT;0;False;1;FLOAT;1;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionOutput;56;1601.617,-377.3906;Inherit;False;True;-1;Dir;0;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.DynamicAppendNode;3;-1318.755,170.5277;Inherit;False;FLOAT3;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.DotProductOpNode;4;-1092.623,168.4349;Inherit;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.DotProductOpNode;5;-1099.484,-70.02136;Inherit;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.DynamicAppendNode;6;-926.3718,144.0443;Inherit;False;FLOAT3;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.DotProductOpNode;7;-1087.345,414.6067;Inherit;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.DynamicAppendNode;8;-1324.312,417.375;Inherit;False;FLOAT3;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SinOpNode;9;-749.2289,147.5291;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.DynamicAppendNode;10;-1318.756,-73.27289;Inherit;False;FLOAT3;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;15;-595.465,148.2702;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FractNode;38;-464.465,147.2702;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RoundOpNode;39;-326.5129,146.9198;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.BreakToComponentsNode;40;-167.5168,235.2545;Inherit;False;FLOAT3;1;0;FLOAT3;0,0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.WireNode;71;-1721.434,-67.62292;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;44;189.9825,355.9603;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;91;650.1039,246.5512;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;90;635.1039,223.5512;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;92;225.0305,-351.8686;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;93;246.59,-322.5117;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;94;243.7688,162.6385;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;89;359.7375,193.2313;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;55;694.5916,240.8921;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;43;26.01411,467.8947;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;42;29.90215,352.9326;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FloorOpNode;37;-1657.548,-74.76825;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.BreakToComponentsNode;36;-1506.246,-74.7691;Inherit;False;FLOAT2;1;0;FLOAT2;0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.SwizzleNode;46;509.384,258.0056;Inherit;False;FLOAT3;2;1;0;3;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.AbsOpNode;16;-1011.65,-629.486;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;18;-855.241,-628.9871;Inherit;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.BreakToComponentsNode;19;-708.377,-632.236;Inherit;False;FLOAT3;1;0;FLOAT3;0,0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.SimpleMinOpNode;20;-414.377,-606.236;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;22;-251.376,-582.313;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;23;-1029.814,-470.0941;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;24;-1364.881,-359.4831;Inherit;False;2;0;FLOAT2;0,0;False;1;FLOAT2;1,-1;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;25;-1534.523,-359.5881;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;2,-2;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.FractNode;26;-1673.627,-359.4763;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;32;-1846.123,-360.0232;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;70;-1719.434,-308.6229;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;30;-80.57706,-388.407;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionInput;62;-2038.915,-363.9689;Inherit;False;UV;2;0;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleDivideOpNode;17;-1170.444,-628.9871;Inherit;False;2;0;FLOAT3;1,1,1;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.TexCoordVertexDataNode;31;-2352.867,-356.9491;Inherit;False;0;2;0;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SimpleMinOpNode;21;-567.377,-633.236;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.DynamicAppendNode;29;-1181.88,-358.4831;Inherit;False;FLOAT3;4;0;FLOAT2;0,0;False;1;FLOAT;0;False;2;FLOAT;-1;False;3;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;28;-1365.984,-543.7399;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;-1,1,1;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.ViewDirInputsCoordNode;52;-1588.421,-548.3912;Inherit;False;Tangent;False;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.FunctionOutput;61;-168.4052,147.0084;Inherit;False;False;-1;Random;1;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.Vector3Node;49;-203.6968,349.6739;Inherit;False;Constant;_Vector0;Vector
    0;2;0;Create;True;0;0;0;False;0;False;-1,1,1;-1,1,1;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.Vector3Node;50;-204.9878,485.8352;Inherit;False;Constant;_Vector1;Vector
    1;2;0;Create;True;0;0;0;False;0;False;1,1,-1;1,1,-1;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.Vector3Node;51;-201.9089,627.8166;Inherit;False;Constant;_Vector2;Vector
    2;2;0;Create;True;0;0;0;False;0;False;1,1,1;1,1,1;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.Vector3Node;54;-203.9548,770.6967;Inherit;False;Constant;_Vector3;Vector
    3;2;0;Create;True;0;0;0;False;0;False;-1,1,1;-1,1,1;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.RangedFloatNode;14;-791.6044,225.0709;Inherit;False;Constant;_Float4;Float
    4;0;0;Create;True;0;0;0;False;0;False;43758.55;43758.55;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.Vector3Node;12;-1320.848,280.3951;Inherit;False;Constant;_Vector5;Vector
    5;3;0;Create;True;0;0;0;False;0;False;269.5,183.3,246.1;0,0,0;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.Vector3Node;13;-1325.987,529.4741;Inherit;False;Constant;_Vector6;Vector
    6;3;0;Create;True;0;0;0;False;0;False;113.5,271.9,124.6;0,0,0;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.Vector3Node;11;-1325.033,36.59399;Inherit;False;Constant;_Vector4;Vector
    4;3;0;Create;True;0;0;0;False;0;False;127.1,311.7,74.7;0,0,0;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.FunctionInput;58;-2059.939,-262.5055;Inherit;False;Room
    Count;2;1;False;1;0;FLOAT2;1,1;False;1;FLOAT2;0\nWireConnection;59;0;30;0\nWireConnection;59;1;55;0\nWireConnection;120;0;59;0\nWireConnection;97;0;120;1\nWireConnection;121;0;120;0\nWireConnection;121;1;97;0\nWireConnection;121;2;120;2\nWireConnection;56;0;121;0\nWireConnection;3;0;36;1\nWireConnection;3;1;36;0\nWireConnection;3;2;36;0\nWireConnection;4;0;3;0\nWireConnection;4;1;12;0\nWireConnection;5;0;10;0\nWireConnection;5;1;11;0\nWireConnection;6;0;5;0\nWireConnection;6;1;4;0\nWireConnection;6;2;7;0\nWireConnection;7;0;8;0\nWireConnection;7;1;13;0\nWireConnection;8;0;36;0\nWireConnection;8;1;36;1\nWireConnection;8;2;36;1\nWireConnection;9;0;6;0\nWireConnection;10;0;36;0\nWireConnection;10;1;36;1\nWireConnection;10;2;36;0\nWireConnection;15;0;9;0\nWireConnection;15;1;14;0\nWireConnection;38;0;15;0\nWireConnection;39;0;38;0\nWireConnection;40;0;39;0\nWireConnection;71;0;70;0\nWireConnection;44;0;42;0\nWireConnection;44;1;43;0\nWireConnection;91;0;90;0\nWireConnection;90;0;89;0\nWireConnection;92;0;30;0\nWireConnection;93;0;92;0\nWireConnection;94;0;93;0\nWireConnection;89;0;94;0\nWireConnection;89;1;44;0\nWireConnection;55;0;91;0\nWireConnection;55;1;46;0\nWireConnection;55;2;40;2\nWireConnection;43;0;51;0\nWireConnection;43;1;54;0\nWireConnection;43;2;40;1\nWireConnection;42;0;49;0\nWireConnection;42;1;50;0\nWireConnection;42;2;40;0\nWireConnection;37;0;71;0\nWireConnection;36;0;37;0\nWireConnection;46;0;89;0\nWireConnection;16;0;17;0\nWireConnection;18;0;16;0\nWireConnection;18;1;23;0\nWireConnection;19;0;18;0\nWireConnection;20;0;21;0\nWireConnection;20;1;19;2\nWireConnection;22;0;20;0\nWireConnection;22;1;28;0\nWireConnection;23;0;17;0\nWireConnection;23;1;29;0\nWireConnection;24;0;25;0\nWireConnection;25;0;26;0\nWireConnection;26;0;32;0\nWireConnection;32;0;62;0\nWireConnection;32;1;58;0\nWireConnection;70;0;32;0\nWireConnection;30;0;22;0\nWireConnection;30;1;29;0\nWireConnection;62;0;31;0\nWireConnection;17;1;28;0\nWireConnection;21;0;19;0\nWireConnection;21;1;19;1\nWireConnection;29;0;24;0\nWireConnection;28;0;52;0\nWireConnection;61;0;39;0\nASEEND*/\n//CHKSM=87EF7ACEF8982E2704D84B7A1E614324B3B15663"
  m_functionName: 
  m_description: Amplify Shader Pack Example
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 0
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_customEdited: 0
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 0
  m_headerStyle: 2
  m_headerColor: {r: 0.6627451, g: 0.4392157, b: 0, a: 1}
  m_customNodeCategory: Amplify Shader Pack
  m_previewPosition: 0
  m_hidden: 0
  m_url: 
