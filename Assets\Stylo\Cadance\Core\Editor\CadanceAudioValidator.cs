using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace Stylo.Cadance.Editor
{
    /// <summary>
    /// Audio clip validation system that matches <PERSON><PERSON><PERSON>'s audio validation functionality.
    /// Equivalent to KoreographyEditor.IsAudioClipValid() and KoreographyEditor.CheckAudioClipValidity().
    /// </summary>
    public static class CadanceAudioValidator
    {
        /// <summary>
        /// Checks if an AudioClip is valid for use with Cadance, equivalent to KoreographyEditor.IsAudioClipValid().
        /// </summary>
        /// <param name="clip">AudioClip to validate</param>
        /// <returns>True if clip is valid for Cadance editing</returns>
        public static bool IsAudioClipValid(AudioClip clip)
        {
            if (clip == null) return false;

            // Check basic requirements
            if (clip.samples <= 0) return false;
            if (clip.frequency <= 0) return false;
            if (clip.channels <= 0) return false;

            // Check load type - streaming clips can't be used for GetData()
            if (clip.loadType == AudioClipLoadType.Streaming) return false;

            // Check if we can actually get data from the clip
            try
            {
                float[] testData = new float[Mathf.Min(1024, clip.samples * clip.channels)];
                return clip.GetData(testData, 0);
            }
            catch (System.Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// Comprehensive validation with detailed error reporting, equivalent to KoreographyEditor.CheckAudioClipValidity().
        /// </summary>
        /// <param name="clip">AudioClip to validate</param>
        /// <param name="showDialog">Whether to show dialog for invalid clips</param>
        /// <returns>True if clip is valid, false otherwise</returns>
        public static bool CheckAudioClipValidity(AudioClip clip, bool showDialog = true)
        {
            if (clip == null)
            {
                if (showDialog)
                {
                    EditorUtility.DisplayDialog("Invalid AudioClip", 
                        "No AudioClip assigned. Please assign an AudioClip to continue.", 
                        "OK");
                }
                return false;
            }

            var validationResult = ValidateAudioClipDetailed(clip);
            
            if (validationResult.IsValid)
            {
                return true;
            }

            if (showDialog)
            {
                bool shouldFix = EditorUtility.DisplayDialog("AudioClip Validation Failed", 
                    $"AudioClip '{clip.name}' has issues that prevent it from working with Cadance:\n\n" +
                    $"{string.Join("\n", validationResult.Issues)}\n\n" +
                    "Would you like to attempt to fix these issues automatically?", 
                    "Fix Issues", "Cancel");

                if (shouldFix)
                {
                    return AttemptToFixAudioClip(clip);
                }
            }

            return false;
        }

        /// <summary>
        /// Performs detailed validation of an AudioClip.
        /// </summary>
        /// <param name="clip">AudioClip to validate</param>
        /// <returns>Validation result with detailed information</returns>
        public static AudioClipValidationResult ValidateAudioClipDetailed(AudioClip clip)
        {
            var result = new AudioClipValidationResult();
            
            if (clip == null)
            {
                result.Issues.Add("AudioClip is null");
                return result;
            }

            // Check basic properties
            if (clip.samples <= 0)
                result.Issues.Add("AudioClip has no samples");
            
            if (clip.frequency <= 0)
                result.Issues.Add("AudioClip has invalid sample rate");
            
            if (clip.channels <= 0)
                result.Issues.Add("AudioClip has no channels");

            // Check load type
            if (clip.loadType == AudioClipLoadType.Streaming)
            {
                result.Issues.Add("AudioClip load type is 'Streaming' - change to 'Decompress On Load' or 'Compressed In Memory'");
                result.CanAutoFix = true;
            }

            // Check if clip data is accessible
            try
            {
                float[] testData = new float[Mathf.Min(1024, clip.samples * clip.channels)];
                if (!clip.GetData(testData, 0))
                {
                    result.Issues.Add("Cannot read data from AudioClip - check import settings");
                    result.CanAutoFix = true;
                }
            }
            catch (System.Exception ex)
            {
                result.Issues.Add($"Exception reading AudioClip data: {ex.Message}");
                result.CanAutoFix = true;
            }

            // Check import settings
            string assetPath = AssetDatabase.GetAssetPath(clip);
            if (!string.IsNullOrEmpty(assetPath))
            {
                var importer = AssetImporter.GetAtPath(assetPath) as AudioImporter;
                if (importer != null)
                {
                    var settings = importer.defaultSampleSettings;
                    
                    if (settings.loadType == AudioClipLoadType.Streaming)
                    {
                        result.Issues.Add("Import settings use 'Streaming' load type");
                        result.CanAutoFix = true;
                    }

                    if (!importer.loadInBackground)
                    {
                        result.Issues.Add("'Load In Background' is disabled - this may cause editor freezing");
                        result.CanAutoFix = true;
                    }
                }
            }

            result.IsValid = result.Issues.Count == 0;
            return result;
        }

        /// <summary>
        /// Attempts to automatically fix AudioClip import settings.
        /// </summary>
        /// <param name="clip">AudioClip to fix</param>
        /// <returns>True if fixes were applied successfully</returns>
        public static bool AttemptToFixAudioClip(AudioClip clip)
        {
            if (clip == null) return false;

            string assetPath = AssetDatabase.GetAssetPath(clip);
            if (string.IsNullOrEmpty(assetPath))
            {
                Debug.LogWarning($"[CadanceAudioValidator] Cannot fix AudioClip '{clip.name}' - not found in project");
                return false;
            }

            var importer = AssetImporter.GetAtPath(assetPath) as AudioImporter;
            if (importer == null)
            {
                Debug.LogWarning($"[CadanceAudioValidator] Cannot fix AudioClip '{clip.name}' - no AudioImporter found");
                return false;
            }

            bool madeChanges = false;

            // Fix load type
            var settings = importer.defaultSampleSettings;
            if (settings.loadType == AudioClipLoadType.Streaming)
            {
                settings.loadType = AudioClipLoadType.DecompressOnLoad;
                importer.defaultSampleSettings = settings;
                madeChanges = true;
                Debug.Log($"[CadanceAudioValidator] Changed load type to 'Decompress On Load' for '{clip.name}'");
            }

            // Enable load in background
            if (!importer.loadInBackground)
            {
                importer.loadInBackground = true;
                madeChanges = true;
                Debug.Log($"[CadanceAudioValidator] Enabled 'Load In Background' for '{clip.name}'");
            }

            if (madeChanges)
            {
                importer.SaveAndReimport();
                
                EditorUtility.DisplayDialog("AudioClip Fixed", 
                    $"Import settings for '{clip.name}' have been updated.\n\n" +
                    "The AudioClip should now work properly with Cadance.", 
                    "OK");
                
                return true;
            }

            return false;
        }

        /// <summary>
        /// Validates all AudioClips in the project for Cadance compatibility.
        /// </summary>
        /// <returns>Validation summary</returns>
        public static ProjectAudioValidationSummary ValidateProjectAudioClips()
        {
            var summary = new ProjectAudioValidationSummary();
            
            string[] audioGuids = AssetDatabase.FindAssets("t:AudioClip");
            
            foreach (string guid in audioGuids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                var clip = AssetDatabase.LoadAssetAtPath<AudioClip>(path);
                
                if (clip != null)
                {
                    var result = ValidateAudioClipDetailed(clip);
                    
                    if (result.IsValid)
                    {
                        summary.ValidClips.Add(clip);
                    }
                    else
                    {
                        summary.InvalidClips.Add(new InvalidClipInfo
                        {
                            Clip = clip,
                            Path = path,
                            ValidationResult = result
                        });
                    }
                }
            }

            return summary;
        }
    }

    /// <summary>
    /// Result of AudioClip validation with detailed information.
    /// </summary>
    public class AudioClipValidationResult
    {
        public bool IsValid { get; set; }
        public bool CanAutoFix { get; set; }
        public List<string> Issues { get; set; } = new List<string>();
    }

    /// <summary>
    /// Information about an invalid AudioClip.
    /// </summary>
    public class InvalidClipInfo
    {
        public AudioClip Clip { get; set; }
        public string Path { get; set; }
        public AudioClipValidationResult ValidationResult { get; set; }
    }

    /// <summary>
    /// Summary of project-wide AudioClip validation.
    /// </summary>
    public class ProjectAudioValidationSummary
    {
        public List<AudioClip> ValidClips { get; set; } = new List<AudioClip>();
        public List<InvalidClipInfo> InvalidClips { get; set; } = new List<InvalidClipInfo>();
        
        public int TotalClips => ValidClips.Count + InvalidClips.Count;
        public int ValidCount => ValidClips.Count;
        public int InvalidCount => InvalidClips.Count;
        public float ValidPercentage => TotalClips > 0 ? (float)ValidCount / TotalClips * 100f : 0f;
    }
}
