#if UNITY_EDITOR && URP_INSTALLED
using System.Reflection;
using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using UnityEditor;
using Stylo.BFI.Universal;

namespace Stylo.BFI.Editor
{
    public class BFIDebugger : EditorWindow
    {
        private Vector2 scrollPosition;
        private bool autoRefresh = true;
        private float lastRefreshTime;

        [MenuItem("Stylo/BFI/Debug Tool")]
        public static void ShowWindow()
        {
            GetWindow<BFIDebugger>("BFI Debugger");
        }

        private void OnGUI()
        {
            EditorGUILayout.LabelField("BFI Debug Tool", EditorStyles.boldLabel);
            EditorGUILayout.Space();

            autoRefresh = EditorGUILayout.Toggle("Auto Refresh", autoRefresh);

            if (GUILayout.Button("Refresh Debug Info") || (autoRefresh && Time.realtimeSinceStartup - lastRefreshTime > 1f))
            {
                Repaint();
                lastRefreshTime = Time.realtimeSinceStartup;
            }

            EditorGUILayout.Space();
            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);

            // Check URP Setup
            CheckURPSetup();
            EditorGUILayout.Space();

            // Check BFI Renderer Feature
            CheckBFIRendererFeature();
            EditorGUILayout.Space();

            // Check Volume Setup
            CheckVolumeSetup();
            EditorGUILayout.Space();

            // Check Shader
            CheckShader();
            EditorGUILayout.Space();

            // Runtime Debug Info
            if (Application.isPlaying)
            {
                RuntimeDebugInfo();
            }
            else
            {
                EditorGUILayout.HelpBox("Enter Play Mode for runtime debugging information", MessageType.Info);
            }

            EditorGUILayout.Space();
            QuickFixes();

            EditorGUILayout.EndScrollView();
        }

        private void CheckURPSetup()
        {
            EditorGUILayout.LabelField("URP Configuration", EditorStyles.boldLabel);

            var urpAsset = GraphicsSettings.currentRenderPipeline as UniversalRenderPipelineAsset;
            if (urpAsset == null)
            {
                EditorGUILayout.HelpBox("❌ No URP asset found in Graphics Settings!", MessageType.Error);
                return;
            }

            EditorGUILayout.LabelField($"✅ URP Asset: {urpAsset.name}");
            EditorGUILayout.LabelField($"✅ Unity Version: {Application.unityVersion}");
        }

        private void CheckBFIRendererFeature()
        {
            EditorGUILayout.LabelField("BFI Renderer Feature", EditorStyles.boldLabel);

            var urpAsset = GraphicsSettings.currentRenderPipeline as UniversalRenderPipelineAsset;
            if (urpAsset == null) return;

            // Get renderer data using reflection
            var rendererDataListField = typeof(UniversalRenderPipelineAsset).GetField("m_RendererDataList",
                BindingFlags.NonPublic | BindingFlags.Instance);

            if (rendererDataListField == null)
            {
                EditorGUILayout.HelpBox("❌ Could not access renderer data list", MessageType.Error);
                return;
            }

            var rendererDataArray = rendererDataListField.GetValue(urpAsset) as ScriptableRendererData[];
            if (rendererDataArray == null || rendererDataArray.Length == 0)
            {
                EditorGUILayout.HelpBox("❌ No renderer data found", MessageType.Error);
                return;
            }

            for (int i = 0; i < rendererDataArray.Length; i++)
            {
                var rendererData = rendererDataArray[i] as UniversalRendererData;
                if (rendererData == null) continue;

                EditorGUILayout.LabelField($"Renderer {i}: {rendererData.name}");

                bool hasBFI = false;
                bool bfiActive = false;

                foreach (var feature in rendererData.rendererFeatures)
                {
                    if (feature is BFIRendererFeature bfiFeature)
                    {
                        hasBFI = true;
                        bfiActive = feature.isActive;
                        EditorGUILayout.LabelField($"  ✅ BFI Feature Found: {feature.name}");
                        EditorGUILayout.LabelField($"  Status: {(bfiActive ? "✅ Active" : "❌ Inactive")}");
                        break;
                    }
                }

                if (!hasBFI)
                {
                    EditorGUILayout.LabelField($"  ❌ No BFI Feature found in {rendererData.name}");
                }
            }
        }

        private void CheckVolumeSetup()
        {
            EditorGUILayout.LabelField("Volume Configuration", EditorStyles.boldLabel);

            // Check Volume Manager
            if (VolumeManager.instance == null)
            {
                EditorGUILayout.HelpBox("❌ Volume Manager not found", MessageType.Error);
                return;
            }

            EditorGUILayout.LabelField("✅ Volume Manager active");

            // Check for BFI in volume stack
            var stack = VolumeManager.instance.stack;
            if (stack == null)
            {
                EditorGUILayout.HelpBox("❌ Volume stack is null", MessageType.Error);
                return;
            }

            var bfiEffect = stack.GetComponent<BFIEffect>();
            if (bfiEffect == null)
            {
                EditorGUILayout.HelpBox("❌ No BFI Effect found in volume stack", MessageType.Warning);
                EditorGUILayout.LabelField("Make sure you have a Volume Profile with BFI Effect active in the scene");
            }
            else
            {
                EditorGUILayout.LabelField("✅ BFI Effect found in volume stack");
                EditorGUILayout.LabelField($"  Mode: {bfiEffect.Mode.value}");
                EditorGUILayout.LabelField($"  Intensity: {bfiEffect.Intensity.value}");
                EditorGUILayout.LabelField($"  Is Active: {bfiEffect.IsActive()}");
                EditorGUILayout.LabelField($"  Debug Visualization: {bfiEffect.DebugVisualization.value}");
            }

            // List all volumes in scene
            var volumes = FindObjectsOfType<Volume>();
            EditorGUILayout.LabelField($"Volumes in scene: {volumes.Length}");

            foreach (var volume in volumes)
            {
                var profile = volume.profile;
                bool hasBFI = profile != null && profile.Has<BFIEffect>();
                string status = hasBFI ? "✅ Has BFI" : "❌ No BFI";
                EditorGUILayout.LabelField($"  {volume.name}: {status} (Global: {volume.isGlobal})");

                if (hasBFI && profile.TryGet<BFIEffect>(out var volumeBFI))
                {
                    EditorGUILayout.LabelField($"    Mode Override: {volumeBFI.Mode.overrideState}");
                    EditorGUILayout.LabelField($"    Intensity Override: {volumeBFI.Intensity.overrideState}");
                }
            }
        }

        private void CheckShader()
        {
            EditorGUILayout.LabelField("Shader Status", EditorStyles.boldLabel);

            var bfiShader = Shader.Find("Hidden/Universal Render Pipeline/BFI");
            if (bfiShader == null)
            {
                EditorGUILayout.HelpBox("❌ BFI Shader not found! Check shader compilation.", MessageType.Error);
            }
            else
            {
                EditorGUILayout.LabelField("✅ BFI Shader found");
                EditorGUILayout.LabelField($"  Name: {bfiShader.name}");
                EditorGUILayout.LabelField($"  Supported: {bfiShader.isSupported}");

                // Check for compilation errors
                var shaderMessages = ShaderUtil.GetShaderMessages(bfiShader);
                if (shaderMessages.Length > 0)
                {
                    EditorGUILayout.LabelField($"  ⚠️ Shader has {shaderMessages.Length} messages");
                    foreach (var message in shaderMessages)
                    {
                        EditorGUILayout.LabelField($"    {message.severity}: {message.message}");
                    }
                }
                else
                {
                    EditorGUILayout.LabelField("  ✅ No shader compilation errors");
                }
            }
        }

        private void RuntimeDebugInfo()
        {
            EditorGUILayout.LabelField("Runtime Debug Info", EditorStyles.boldLabel);

            // Frame rate info
            float fps = 1f / Time.unscaledDeltaTime;
            EditorGUILayout.LabelField($"Current FPS: {fps:F1}");
            EditorGUILayout.LabelField($"Frame Time: {Time.unscaledDeltaTime * 1000f:F2}ms");

            // Camera info
            var camera = Camera.main;
            if (camera != null)
            {
                EditorGUILayout.LabelField($"Main Camera: {camera.name}");

                // Check if camera has URP additional camera data
                var cameraData = camera.GetComponent<UniversalAdditionalCameraData>();
                if (cameraData != null)
                {
                    EditorGUILayout.LabelField($"  Render Type: {cameraData.renderType}");
                    // Note: Renderer index access varies by URP version
                    EditorGUILayout.LabelField($"  Has Additional Camera Data: Yes");
                }
            }

            // Volume stack info
            var stack = VolumeManager.instance?.stack;
            if (stack != null)
            {
                var bfiEffect = stack.GetComponent<BFIEffect>();
                if (bfiEffect != null)
                {
                    EditorGUILayout.LabelField("BFI Effect Runtime Status:");
                    EditorGUILayout.LabelField($"  Is Active: {bfiEffect.IsActive()}");
                    EditorGUILayout.LabelField($"  Mode: {bfiEffect.Mode.value}");
                    EditorGUILayout.LabelField($"  Intensity: {bfiEffect.Intensity.value:F2}");
                    EditorGUILayout.LabelField($"  Brightness Comp: {bfiEffect.BrightnessCompensation.value:F2}");
                    EditorGUILayout.LabelField($"  Debug Vis: {bfiEffect.DebugVisualization.value}");
                }
            }
        }

        private void QuickFixes()
        {
            EditorGUILayout.LabelField("Quick Fixes", EditorStyles.boldLabel);

            EditorGUILayout.BeginHorizontal();

            if (GUILayout.Button("Add BFI to Renderer"))
            {
                BFIIntegration.AddBFIToCurrentRenderer();
            }

            if (GUILayout.Button("Create BFI Volume"))
            {
                BFIIntegration.CreateBFIVolumeProfile();
            }

            EditorGUILayout.EndHorizontal();

            EditorGUILayout.BeginHorizontal();

            if (GUILayout.Button("Setup Complete Scene"))
            {
                BFIIntegration.SetupCompleteBFIScene();
            }

            if (GUILayout.Button("Force Shader Recompile"))
            {
                var bfiShader = Shader.Find("Hidden/Universal Render Pipeline/BFI");
                if (bfiShader != null)
                {
                    ShaderUtil.ClearShaderMessages(bfiShader);
                    AssetDatabase.ImportAsset(AssetDatabase.GetAssetPath(bfiShader), ImportAssetOptions.ForceUpdate);
                }
            }

            EditorGUILayout.EndHorizontal();

            if (GUILayout.Button("Create Test Scene with Moving Objects"))
            {
                CreateTestScene();
            }
        }

        private void CreateTestScene()
        {
            // Create a simple test scene with moving objects
            var cube = GameObject.CreatePrimitive(PrimitiveType.Cube);
            cube.name = "Moving Cube";
            cube.transform.position = new Vector3(0, 0, 5);

            // Add a simple movement script
            var mover = cube.AddComponent<SimpleMover>();

            // Create a sphere
            var sphere = GameObject.CreatePrimitive(PrimitiveType.Sphere);
            sphere.name = "Moving Sphere";
            sphere.transform.position = new Vector3(2, 0, 5);
            sphere.AddComponent<SimpleMover>();

            Debug.Log("Test scene created with moving objects. Enable BFI and observe motion clarity differences.");
        }
    }

    // Simple component to make objects move for testing
    public class SimpleMover : MonoBehaviour
    {
        public float speed = 2f;
        public float range = 3f;
        private Vector3 startPos;

        void Start()
        {
            startPos = transform.position;
        }

        void Update()
        {
            float offset = Mathf.Sin(Time.time * speed) * range;
            transform.position = startPos + Vector3.right * offset;
        }
    }
}
#endif
