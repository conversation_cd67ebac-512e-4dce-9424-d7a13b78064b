%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Interior Mapping Cubemap
  m_Shader: {fileID: 4800000, guid: 72aa9174784d458eb2057c4c1a14718f, type: 3}
  m_ValidKeywords: []
  m_InvalidKeywords: []
  m_LightmapFlags: 0
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _FacadeMap:
        m_Texture: {fileID: 2800000, guid: ce6a56788ccccef4c8603eb6903c8746, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _InteriorCube:
        m_Texture: {fileID: 8900000, guid: 07945fa9a8dd46c4f9cfb63a938d1897, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _LatLongMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _LatLongMap1:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _LatLongMap2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Roof:
        m_Texture: {fileID: 2800000, guid: aab8de1052c54173b3d61e7f8b05aedf, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Window:
        m_Texture: {fileID: 2800000, guid: 08e24ca7b158156448eb0fdc20b5e15c, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _texcoord:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AlphaCutoff: 0.5
    - _Brightness0: 1.815
    - _Emission: 20
    - _EmissiveIntensity: 25
    - _EnableFacade: 1
    - _EnvironmentReflections: 1
    - _Float0: 1
    - _InteriorLight: 20
    - _Lights: 20
    - _QueueControl: 0
    - _QueueOffset: 0
    - _ReceiveShadows: 1
    - _RoomCountX: 4
    - _RoomCountY: 4
    - _SpecularHighlights: 1
    - _WindowTilling: 2
    - __dirty: 0
    m_Colors:
    - _EmissionColor: {r: 1, g: 1, b: 1, a: 1}
    - _FacadeTilling: {r: 4, g: 4, b: 0, a: 0}
  m_BuildTextureStacks: []
