%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Specular (Mask Plane)
  m_Shader: {fileID: -6465566751694194690, guid: 7875802bd5f3695409c61045439bb933, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords:
  - ENUM_5442A9AC_PLANE
  m_InvalidKeywords:
  - WIREFRAME_DYNAMIC_MASK_PLANE
  - _METALLICGLOSSMAP
  - _SMOOTHNESS_TEXTURE_ALBEDO_CHANNEL_A
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses:
  - MOTIONVECTORS
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BaseMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Wireframe_ColorTexture:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - ENUM_5442A9AC: 0
    - Vector1_BD6DE493: 10
    - Vector1_C691CA19: 0
    - Vector1_CF184A9E: 0
    - _BumpScale: 1
    - _CurvedWorldTitle: 0
    - _Cutoff: 0.5
    - _DetailNormalMapScale: 1
    - _DstBlend: 0
    - _GlossMapScale: 1
    - _Glossiness: 0.816
    - _GlossyReflections: 1
    - _Metallic: 0
    - _Mode: 0
    - _OcclusionStrength: 1
    - _Parallax: 0.02
    - _QueueControl: 0
    - _QueueOffset: 0
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _UVSec: 0
    - _WireframeShader_Diameter: 0.5
    - _WireframeShader_Smoothness: 0
    - _WireframeShader_Thickness: 0
    - _Wireframe_ColorEmissionStrength: 0.05
    - _Wireframe_DistanceFade: 0
    - _Wireframe_DistanceFadeEnd: 10
    - _Wireframe_DistanceFadeStart: 5
    - _Wireframe_DynamicMaskEdgeSmooth: 0
    - _Wireframe_DynamicMaskEffectsBaseTexEnumID: 0
    - _Wireframe_DynamicMaskEffectsBaseTexInvert: 0
    - _Wireframe_DynamicMaskEnumID: 0
    - _Wireframe_DynamicMaskInvert: -1
    - _Wireframe_DynamicMaskType: 1
    - _Wireframe_FresnelBias: 0
    - _Wireframe_FresnelEnumID: 0
    - _Wireframe_FresnelInvert: 0
    - _Wireframe_FresnelPow: 1
    - _Wireframe_MetaPassMultiplier: 0
    - _Wireframe_Title_GI_Options: 0
    - _Wireframe_Title_M_Options: 0
    - _Wireframe_Title_S_Options: 0
    - _Wireframe_Title_W_Options: 0
    - _Wireframe_TransparencyEnumID: 0
    - _Wireframe_Transparency_M_Options: 0
    - _Wireframe_TransparentTex_Alpha_Offset: 0
    - _Wireframe_TransparentTex_Invert: 0
    - _ZWrite: 1
    m_Colors:
    - _BaseColor: {r: 0.14117648, g: 0.13725491, b: 0.13725491, a: 1}
    - _Color: {r: 0.14150941, g: 0.13750443, b: 0.13750443, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _SpecColor: {r: 0.2, g: 0.2, b: 0.2, a: 1}
    - _Wireframe_Color: {r: 1, g: 0, b: 0, a: 1}
    - _Wireframe_ColorTexture_Scroll: {r: 0, g: 0, b: 0, a: 0}
  m_BuildTextureStacks: []
  m_AllowLocking: 1
--- !u!114 &6999095414104845558
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0353a89b1f911e48b9e16bdc9f2e058, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 9
