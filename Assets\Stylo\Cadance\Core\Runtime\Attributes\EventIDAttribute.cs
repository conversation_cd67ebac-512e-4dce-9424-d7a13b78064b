using UnityEngine;

namespace Stylo.Cadance
{
    /// <summary>
    /// The EventID Attribute allows you to mark a serializable or public string
    /// field as being an EventID. When this happens, the field gets special
    /// consideration in the Inspector, showing a customizable field and a list of
    /// Event ID options configured across Cadance Tracks found within the current
    /// project.
    /// 
    /// This provides full compatibility with Koreographer's EventIDAttribute.
    /// </summary>
    public class EventIDAttribute : PropertyAttribute
    {
        /// <summary>
        /// Creates a new EventID attribute for marking string fields as event IDs.
        /// </summary>
        public EventIDAttribute()
        {
        }
    }
}
