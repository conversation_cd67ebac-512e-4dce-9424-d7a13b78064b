%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Orientation Based Sprite
  m_Shader: {fileID: 4800000, guid: 7941045cd85e46b4a4b8ab97829c9f22, type: 3}
  m_ValidKeywords: []
  m_InvalidKeywords:
  - _ADD_PRECOMPUTED_VELOCITY
  - _DISABLE_SSR
  m_LightmapFlags: 2
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap:
    MotionVector: User
  disabledShaderPasses:
  - TransparentBackface
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: 8bad0b0ea8a87cd4ab57ff281be3b6df, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Texture0:
        m_Texture: {fileID: 2800000, guid: 57a4654414af494dba173363c8833086, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TextureSheet:
        m_Texture: {fileID: 2800000, guid: af1413e1f4cddc041a517895567be041, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AddPrecomputedVelocity: 1
    - _AlphaCutoff: 0.5
    - _AlphaCutoffEnable: 1
    - _AlphaDstBlend: 0
    - _AlphaSrcBlend: 1
    - _AnimSpeed: 1
    - _BlendMode: 0
    - _Columns: 3
    - _CullMode: 2
    - _CullModeForward: 2
    - _Cutout: 0.5
    - _DoubleSidedEnable: 0
    - _DoubleSidedNormalMode: 2
    - _DstBlend: 0
    - _EnableFogOnTransparent: 1
    - _Frames: 8
    - _QueueControl: 0
    - _QueueOffset: 0
    - _ReceivesSSR: 0
    - _RenderQueueType: 1
    - _RequireSplitLighting: 0
    - _Rows: 8
    - _SrcBlend: 1
    - _StencilRef: 0
    - _StencilRefDepth: 0
    - _StencilRefDistortionVec: 4
    - _StencilRefGBuffer: 2
    - _StencilRefMV: 32
    - _StencilWriteMask: 6
    - _StencilWriteMaskDepth: 8
    - _StencilWriteMaskDistortionVec: 4
    - _StencilWriteMaskGBuffer: 14
    - _StencilWriteMaskMV: 40
    - _SurfaceType: 0
    - _TransparentBackfaceEnable: 0
    - _TransparentCullMode: 2
    - _TransparentSortPriority: 0
    - _TransparentZWrite: 1
    - _UseShadowThreshold: 0
    - _ZTestDepthEqualForOpaque: 3
    - _ZTestGBuffer: 3
    - _ZTestTransparent: 4
    - _ZWrite: 1
    - __dirty: 0
    m_Colors:
    - _DoubleSidedConstants: {r: 1, g: 1, b: -1, a: 0}
    - _EmissionColor: {r: 1, g: 1, b: 1, a: 1}
  m_BuildTextureStacks: []
