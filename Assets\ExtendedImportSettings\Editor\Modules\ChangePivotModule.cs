using UnityEngine;

namespace ExtendedImportSettings
{
    [System.Serializable]
    public class ChangePivotData : PerMeshDataBase
    {
        public bool PositionToZero;
        public Vector3 PositionOffset;
        public bool RotationToZero;
        public Vector3 RotationOffset;
    }

    [System.Serializable]
    public class ChangePivotModule : PerMeshModuleBase<ChangePivotData>
    {
        protected override void OnPostprocessModelPerMesh(Mesh mesh, ChangePivotData data,
            GameObject meshGameObject, GameObject rootGameObject)
        {
            var rootTransform = rootGameObject.transform;
            var transform = meshGameObject.transform;
            var vertices = mesh.vertices;
            var normals = mesh.normals;
            var tangents = mesh.tangents;

            Quaternion rotation = transform.rotation;
            Quaternion rotationOffsetQuat = Quaternion.Euler(data.RotationOffset);

            for (var j = 0; j < vertices.Length; j++)
            {
                Vector3 worldPos = transform.TransformPoint(vertices[j]);

                worldPos = rootTransform.position + rotationOffsetQuat * (worldPos - rootTransform.position);
                if (data.PositionToZero)
                    worldPos += transform.position;
                worldPos += data.PositionOffset;

                if (data.RotationToZero)
                    worldPos = rotation * (worldPos - transform.position) + transform.position;

                vertices[j] = transform.InverseTransformPoint(worldPos);

                if (normals != null && normals.Length > j)
                {
                    Vector3 worldNormal = transform.TransformDirection(normals[j]);
                    worldNormal = rotationOffsetQuat * worldNormal;
                    if (data.RotationToZero)
                        worldNormal = rotation * worldNormal;
                    normals[j] = transform.InverseTransformDirection(worldNormal);
                }

                if (tangents != null && tangents.Length > j)
                {
                    Vector3 worldTangent =
                        transform.TransformDirection(new Vector3(tangents[j].x, tangents[j].y, tangents[j].z));
                    worldTangent = rotationOffsetQuat * worldTangent;
                    if (data.RotationToZero)
                        worldTangent = rotation * worldTangent;

                    tangents[j] = new Vector4(transform.InverseTransformDirection(worldTangent).x,
                        transform.InverseTransformDirection(worldTangent).y,
                        transform.InverseTransformDirection(worldTangent).z,
                        tangents[j].w);
                }
            }

            if (data.PositionToZero)
                transform.position = Vector3.zero;
            if (data.RotationToZero)
                transform.rotation = Quaternion.identity;

            mesh.vertices = vertices;
            if (normals != null && normals.Length > 0)
                mesh.normals = normals;
            if (tangents != null && tangents.Length > 0)
                mesh.tangents = tangents;

            mesh.RecalculateBounds();
        }
    }
}