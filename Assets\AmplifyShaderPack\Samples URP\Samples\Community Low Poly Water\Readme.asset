%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e78934177bebe0545ab566bb787b0282, type: 3}
  m_Name: Readme
  m_EditorClassIdentifier: 
  Icon: {fileID: 2800000, guid: 3660afec11cee3140a2dada4a8aa6810, type: 3}
  Title: Community Low Poly Water
  Description:
    Heading: 
    Text: 'Animated Low Poly Water effect with foam effect. '
    LinkText: Created by The Four Headed Cat
    Url: https://twitter.com/fourheadedcat
  PropertiesHeader:
    Heading: Properties
    Text: 
    LinkText: 
    Url: 
  Properties:
  - Heading: Water Color
    Text: Water Color Value.
    LinkText: 
    Url: 
  - Heading: Wave Guide
    Text: Scrolling wave guide texture used to calculate displacement.
    LinkText: 
    Url: 
  - Heading: Wave Speed
    Text: Scrolling speed of the wave effect.
    LinkText: 
    Url: 
  - Heading: Wave Height
    Text: Height value of the wave displacement.
    LinkText: 
    Url: 
  - Heading: Foam Color
    Text: Foam effect Color Value.
    LinkText: 
    Url: 
  - Heading: Foam
    Text: Foam texture blended on top of the base color value according to the distance
      between the surface and geometry behind it.
    LinkText: 
    Url: 
  - Heading: Foam Distortion
    Text: 'Controls foam visibility depending on its distance from geometry behind
      its surface. '
    LinkText: 
    Url: 
  - Heading: Foam Dist
    Text: 'Base opacity amount. '
    LinkText: 
    Url: 
  - Heading: Emission Intensity
    Text: Foam texture emission value.
    LinkText: 
    Url: 
  - Heading: Opacity
    Text: 'Base opacity amount. '
    LinkText: 
    Url: 
  - Heading: Low Poly
    Text: Toggle Low Poly shading look.
    LinkText: 
    Url: 
  - Heading: Normal (Only No Poly Mode)
    Text: Normals texture to be applied to surface when Low Poly is Toggled OFF.
    LinkText: 
    Url: 
  AdditionalProperties: []
  AdditionalScripts: []
  LoadedLayout: 0
  RPType: 3
