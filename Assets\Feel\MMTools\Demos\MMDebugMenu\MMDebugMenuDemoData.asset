%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b950a5ce764708a4ca0425a952fce75f, type: 3}
  m_Name: MMDebugMenuDemoData
  m_EditorClassIdentifier: 
  TitlePrefab: {fileID: 2112759074905327847, guid: 80be1ec3d14da9c489fbefa63b82915f,
    type: 3}
  ButtonPrefab: {fileID: 7110842687765492728, guid: dbb3e138861cf2040b20217e62a5edf2,
    type: 3}
  ButtonBorderPrefab: {fileID: 1985568128000399461, guid: f2be8011322fbf943a44edc64dedbf2c,
    type: 3}
  CheckboxPrefab: {fileID: 2731002163667230428, guid: e48b9501c5356cf4e9c390d40d81438c,
    type: 3}
  SliderPrefab: {fileID: 8595552693496322647, guid: addcaa6f7df18984691dc300b3be4f7b,
    type: 3}
  SpacerSmallPrefab: {fileID: 3952073977153845988, guid: 1a4b9ae95443d5a489bd918c848e2390,
    type: 3}
  SpacerBigPrefab: {fileID: 8096383012134595547, guid: f72e7b8c96262ed4d976576afafd53b0,
    type: 3}
  TextTinyPrefab: {fileID: 7812354180902260709, guid: 57336066c3d7ff5418ae88b7880aafed,
    type: 3}
  TextSmallPrefab: {fileID: 6550416838355534067, guid: 6689802a48b01104ebd8e101f5cce555,
    type: 3}
  TextLongPrefab: {fileID: 7073910567540320170, guid: 4332ad3ebf8592c41bc275ff77d24b29,
    type: 3}
  ValuePrefab: {fileID: 3610448643810830552, guid: 855a2d614e531f44caecc17a8c3bd51c,
    type: 3}
  TwoChoicesPrefab: {fileID: 5258430688713385724, guid: 90a0e72c11dfcbe4f88bccc465c6a0cd,
    type: 3}
  ThreeChoicesPrefab: {fileID: 207464785538935126, guid: 804c003da46ec914e80badaf93e6d2da,
    type: 3}
  TabPrefab: {fileID: 3232960445196542265, guid: a8296d633c27f554a99bbbf94958722e,
    type: 3}
  TabContentsPrefab: {fileID: 2838565908494160927, guid: f2f0b25301bf93e43ac5ecc33208a420,
    type: 3}
  TabSpacerPrefab: {fileID: 2188017814106785258, guid: 57633cde530d56a48995d0733efa9450,
    type: 3}
  DebugTabPrefab: {fileID: 521718878734460126, guid: 94cfa935aa4cff74a9451b22cb7627a6,
    type: 3}
  DebugTabName: Logs
  Tabs:
  - Name: Settings
    Active: 1
    MenuItems:
      array:
      - Name: Title
        Active: 1
        Type: 0
        TitleText: Debug Menu Demo
        TextType: 0
        TextContents: 
        ChoicesType: 0
        ChoiceOneText: 
        ChoiceOneEventName: 
        ChoiceTwoText: 
        ChoiceTwoEventName: 
        ChoiceThreeText: 
        ChoiceThreeEventName: 
        SelectedChoice: 0
        ValueLabel: 
        ValueInitialValue: 
        ValueMMRadioReceiverChannel: 0
        ButtonText: 
        ButtonType: 0
        ButtonEventName: 
        SpacerType: 0
        CheckboxText: 
        CheckboxInitialState: 0
        CheckboxEventName: 
        SliderMode: 0
        SliderText: 
        SliderRemapZero: 0
        SliderRemapOne: 0
        SliderInitialValue: 0
        SliderEventName: 
        TargetSlider: {fileID: 0}
        TargetButton: {fileID: 0}
        TargetCheckbox: {fileID: 0}
      - Name: Intro
        Active: 1
        Type: 5
        TitleText: 
        TextType: 1
        TextContents: This demo showcases how you can easily setup a debug menu,
          exposing quick access controls to pilot your game.
        ChoicesType: 0
        ChoiceOneText: 
        ChoiceOneEventName: 
        ChoiceTwoText: 
        ChoiceTwoEventName: 
        ChoiceThreeText: 
        ChoiceThreeEventName: 
        SelectedChoice: 0
        ValueLabel: 
        ValueInitialValue: 
        ValueMMRadioReceiverChannel: 0
        ButtonText: 
        ButtonType: 0
        ButtonEventName: 
        SpacerType: 0
        CheckboxText: 
        CheckboxInitialState: 0
        CheckboxEventName: 
        SliderMode: 0
        SliderText: 
        SliderRemapZero: 0
        SliderRemapOne: 0
        SliderInitialValue: 0
        SliderEventName: 
        TargetSlider: {fileID: 0}
        TargetButton: {fileID: 0}
        TargetCheckbox: {fileID: 0}
      - Name: Sphere Checkbox
        Active: 1
        Type: 3
        TitleText: 
        TextType: 1
        TextContents: This demo showcases how you can easily setup a debug menu,
          exposing quick access controls to pilot your game.
        ChoicesType: 0
        ChoiceOneText: 
        ChoiceOneEventName: 
        ChoiceTwoText: 
        ChoiceTwoEventName: 
        ChoiceThreeText: 
        ChoiceThreeEventName: 
        SelectedChoice: 0
        ValueLabel: 
        ValueInitialValue: 
        ValueMMRadioReceiverChannel: 0
        ButtonText: 
        ButtonType: 0
        ButtonEventName: 
        SpacerType: 0
        CheckboxText: Sphere should rotate
        CheckboxInitialState: 1
        CheckboxEventName: SphereShouldRotate
        SliderMode: 0
        SliderText: 
        SliderRemapZero: 0
        SliderRemapOne: 0
        SliderInitialValue: 0
        SliderEventName: 
        TargetSlider: {fileID: 0}
        TargetButton: {fileID: 0}
        TargetCheckbox: {fileID: 0}
      - Name: Cylinder Y Position
        Active: 1
        Type: 6
        TitleText: 
        TextType: 1
        TextContents: This demo showcases how you can easily setup a debug menu,
          exposing quick access controls to pilot your game.
        ChoicesType: 0
        ChoiceOneText: 
        ChoiceOneEventName: 
        ChoiceTwoText: 
        ChoiceTwoEventName: 
        ChoiceThreeText: 
        ChoiceThreeEventName: 
        SelectedChoice: 0
        ValueLabel: Cylinder Y Position
        ValueInitialValue: 0
        ValueMMRadioReceiverChannel: 1000
        ButtonText: 
        ButtonType: 0
        ButtonEventName: 
        SpacerType: 0
        CheckboxText: Cylinder Y Position
        CheckboxInitialState: 1
        CheckboxEventName: SphereShouldRotate
        SliderMode: 0
        SliderText: 
        SliderRemapZero: 0
        SliderRemapOne: 0
        SliderInitialValue: 0
        SliderEventName: 
        TargetSlider: {fileID: 0}
        TargetButton: {fileID: 0}
        TargetCheckbox: {fileID: 0}
      - Name: Toggle Right Cube Button
        Active: 1
        Type: 2
        TitleText: 
        TextType: 1
        TextContents: This demo showcases how you can easily setup a debug menu,
          exposing quick access controls to pilot your game.
        ChoicesType: 0
        ChoiceOneText: 
        ChoiceOneEventName: 
        ChoiceTwoText: 
        ChoiceTwoEventName: 
        ChoiceThreeText: 
        ChoiceThreeEventName: 
        SelectedChoice: 0
        ValueLabel: Cylinder Y Position
        ValueInitialValue: 0
        ValueMMRadioReceiverChannel: 1000
        ButtonText: Toggle Right Cube
        ButtonType: 0
        ButtonEventName: ToggleRightCube
        SpacerType: 0
        CheckboxText: Cylinder Y Position
        CheckboxInitialState: 1
        CheckboxEventName: SphereShouldRotate
        SliderMode: 0
        SliderText: 
        SliderRemapZero: 0
        SliderRemapOne: 0
        SliderInitialValue: 0
        SliderEventName: 
        TargetSlider: {fileID: 0}
        TargetButton: {fileID: 0}
        TargetCheckbox: {fileID: 0}
      - Name: Sphere Slider
        Active: 1
        Type: 4
        TitleText: 
        TextType: 1
        TextContents: This demo showcases how you can easily setup a debug menu,
          exposing quick access controls to pilot your game.
        ChoicesType: 0
        ChoiceOneText: 
        ChoiceOneEventName: 
        ChoiceTwoText: 
        ChoiceTwoEventName: 
        ChoiceThreeText: 
        ChoiceThreeEventName: 
        SelectedChoice: 0
        ValueLabel: Cylinder Y Position
        ValueInitialValue: 0
        ValueMMRadioReceiverChannel: 1000
        ButtonText: Toggle Right Cube
        ButtonType: 0
        ButtonEventName: ToggleRightCube
        SpacerType: 0
        CheckboxText: Cylinder Y Position
        CheckboxInitialState: 1
        CheckboxEventName: SphereShouldRotate
        SliderMode: 0
        SliderText: Sphere Position
        SliderRemapZero: 0
        SliderRemapOne: 1
        SliderInitialValue: 0
        SliderEventName: SpherePosition
        TargetSlider: {fileID: 0}
        TargetButton: {fileID: 0}
        TargetCheckbox: {fileID: 0}
      - Name: Lift Instructions Text
        Active: 1
        Type: 5
        TitleText: 
        TextType: 0
        TextContents: Lift cube
        ChoicesType: 0
        ChoiceOneText: 
        ChoiceOneEventName: 
        ChoiceTwoText: 
        ChoiceTwoEventName: 
        ChoiceThreeText: 
        ChoiceThreeEventName: 
        SelectedChoice: 0
        ValueLabel: Cylinder Y Position
        ValueInitialValue: 0
        ValueMMRadioReceiverChannel: 1000
        ButtonText: Toggle Right Cube
        ButtonType: 0
        ButtonEventName: ToggleRightCube
        SpacerType: 0
        CheckboxText: Cylinder Y Position
        CheckboxInitialState: 1
        CheckboxEventName: SphereShouldRotate
        SliderMode: 0
        SliderText: Sphere Position
        SliderRemapZero: 0
        SliderRemapOne: 1
        SliderInitialValue: 0
        SliderEventName: SpherePosition
        TargetSlider: {fileID: 0}
        TargetButton: {fileID: 0}
        TargetCheckbox: {fileID: 0}
      - Name: Lift Three Choices
        Active: 1
        Type: 7
        TitleText: 
        TextType: 0
        TextContents: Lift cube
        ChoicesType: 1
        ChoiceOneText: Left
        ChoiceOneEventName: LiftLeft
        ChoiceTwoText: Middle
        ChoiceTwoEventName: LiftMiddle
        ChoiceThreeText: Right
        ChoiceThreeEventName: LiftRight
        SelectedChoice: 0
        ValueLabel: Cylinder Y Position
        ValueInitialValue: 0
        ValueMMRadioReceiverChannel: 1000
        ButtonText: Toggle Right Cube
        ButtonType: 0
        ButtonEventName: ToggleRightCube
        SpacerType: 0
        CheckboxText: Cylinder Y Position
        CheckboxInitialState: 1
        CheckboxEventName: SphereShouldRotate
        SliderMode: 0
        SliderText: Sphere Position
        SliderRemapZero: 0
        SliderRemapOne: 1
        SliderInitialValue: 0
        SliderEventName: SpherePosition
        TargetSlider: {fileID: 0}
        TargetButton: {fileID: 0}
        TargetCheckbox: {fileID: 0}
      - Name: Long text example
        Active: 1
        Type: 5
        TitleText: 
        TextType: 2
        TextContents: You can also display longer texts in your debug menu. It can
          be useful when you have a lot of info to provide, or when you really really
          like long sentences that never end because of how extremely long they are.
        ChoicesType: 1
        ChoiceOneText: Left
        ChoiceOneEventName: LiftLeft
        ChoiceTwoText: Middle
        ChoiceTwoEventName: LiftMiddle
        ChoiceThreeText: Right
        ChoiceThreeEventName: LiftRight
        SelectedChoice: 0
        ValueLabel: Cylinder Y Position
        ValueInitialValue: 0
        ValueMMRadioReceiverChannel: 1000
        ButtonText: Toggle Right Cube
        ButtonType: 0
        ButtonEventName: ToggleRightCube
        SpacerType: 0
        CheckboxText: Cylinder Y Position
        CheckboxInitialState: 1
        CheckboxEventName: SphereShouldRotate
        SliderMode: 0
        SliderText: Sphere Position
        SliderRemapZero: 0
        SliderRemapOne: 1
        SliderInitialValue: 0
        SliderEventName: SpherePosition
        TargetSlider: {fileID: 0}
        TargetButton: {fileID: 0}
        TargetCheckbox: {fileID: 0}
      - Name: Rotate Middle Cube Button
        Active: 1
        Type: 2
        TitleText: 
        TextType: 2
        TextContents: You can also display longer texts in your debug menu. It can
          be useful when you have a lot of info to provide, or when you really really
          like long sentences that never end because of how extremely long they are.
        ChoicesType: 1
        ChoiceOneText: Left
        ChoiceOneEventName: LiftLeft
        ChoiceTwoText: Middle
        ChoiceTwoEventName: LiftMiddle
        ChoiceThreeText: Right
        ChoiceThreeEventName: LiftRight
        SelectedChoice: 0
        ValueLabel: Cylinder Y Position
        ValueInitialValue: 0
        ValueMMRadioReceiverChannel: 1000
        ButtonText: Rotate Middle Cube
        ButtonType: 1
        ButtonEventName: RotateMiddleCube
        SpacerType: 0
        CheckboxText: Cylinder Y Position
        CheckboxInitialState: 1
        CheckboxEventName: SphereShouldRotate
        SliderMode: 0
        SliderText: Sphere Position
        SliderRemapZero: 0
        SliderRemapOne: 1
        SliderInitialValue: 0
        SliderEventName: SpherePosition
        TargetSlider: {fileID: 0}
        TargetButton: {fileID: 0}
        TargetCheckbox: {fileID: 0}
      - Name: Tilt cube instructions
        Active: 1
        Type: 5
        TitleText: 
        TextType: 0
        TextContents: Tilt cube
        ChoicesType: 1
        ChoiceOneText: Left
        ChoiceOneEventName: LiftLeft
        ChoiceTwoText: Middle
        ChoiceTwoEventName: LiftMiddle
        ChoiceThreeText: Right
        ChoiceThreeEventName: LiftRight
        SelectedChoice: 0
        ValueLabel: Cylinder Y Position
        ValueInitialValue: 0
        ValueMMRadioReceiverChannel: 1000
        ButtonText: Rotate Middle Cube
        ButtonType: 1
        ButtonEventName: RotateMiddleCube
        SpacerType: 0
        CheckboxText: Cylinder Y Position
        CheckboxInitialState: 1
        CheckboxEventName: SphereShouldRotate
        SliderMode: 0
        SliderText: Sphere Position
        SliderRemapZero: 0
        SliderRemapOne: 1
        SliderInitialValue: 0
        SliderEventName: SpherePosition
        TargetSlider: {fileID: 0}
        TargetButton: {fileID: 0}
        TargetCheckbox: {fileID: 0}
      - Name: Tilt Cube
        Active: 1
        Type: 7
        TitleText: 
        TextType: 0
        TextContents: Tilt cube
        ChoicesType: 0
        ChoiceOneText: Left
        ChoiceOneEventName: TiltLeft
        ChoiceTwoText: Right
        ChoiceTwoEventName: TiltRight
        ChoiceThreeText: 
        ChoiceThreeEventName: 
        SelectedChoice: 0
        ValueLabel: Cylinder Y Position
        ValueInitialValue: 0
        ValueMMRadioReceiverChannel: 1000
        ButtonText: Rotate Middle Cube
        ButtonType: 1
        ButtonEventName: RotateMiddleCube
        SpacerType: 0
        CheckboxText: Cylinder Y Position
        CheckboxInitialState: 1
        CheckboxEventName: SphereShouldRotate
        SliderMode: 0
        SliderText: Sphere Position
        SliderRemapZero: 0
        SliderRemapOne: 1
        SliderInitialValue: 0
        SliderEventName: SpherePosition
        TargetSlider: {fileID: 0}
        TargetButton: {fileID: 0}
        TargetCheckbox: {fileID: 0}
  - Name: About
    Active: 1
    MenuItems:
      array:
      - Name: About Title
        Active: 1
        Type: 0
        TitleText: About
        TextType: 0
        TextContents: 
        ChoicesType: 0
        ChoiceOneText: 
        ChoiceOneEventName: 
        ChoiceTwoText: 
        ChoiceTwoEventName: 
        ChoiceThreeText: 
        ChoiceThreeEventName: 
        SelectedChoice: 0
        ValueLabel: 
        ValueInitialValue: 
        ValueMMRadioReceiverChannel: 0
        ButtonText: 
        ButtonType: 0
        ButtonEventName: 
        SpacerType: 0
        CheckboxText: 
        CheckboxInitialState: 0
        CheckboxEventName: 
        SliderMode: 0
        SliderText: 
        SliderRemapZero: 0
        SliderRemapOne: 0
        SliderInitialValue: 0
        SliderEventName: 
        TargetSlider: {fileID: 0}
        TargetButton: {fileID: 0}
        TargetCheckbox: {fileID: 0}
      - Name: Text
        Active: 1
        Type: 5
        TitleText: 
        TextType: 2
        TextContents: This MMDebugMenu system can be used to create debug menus of
          course, but you could repurpose it to create an in-game option menu as
          well. It's up to you!
        ChoicesType: 0
        ChoiceOneText: 
        ChoiceOneEventName: 
        ChoiceTwoText: 
        ChoiceTwoEventName: 
        ChoiceThreeText: 
        ChoiceThreeEventName: 
        SelectedChoice: 0
        ValueLabel: 
        ValueInitialValue: 
        ValueMMRadioReceiverChannel: 0
        ButtonText: 
        ButtonType: 0
        ButtonEventName: 
        SpacerType: 0
        CheckboxText: 
        CheckboxInitialState: 0
        CheckboxEventName: 
        SliderMode: 0
        SliderText: 
        SliderRemapZero: 0
        SliderRemapOne: 0
        SliderInitialValue: 0
        SliderEventName: 
        TargetSlider: {fileID: 0}
        TargetButton: {fileID: 0}
        TargetCheckbox: {fileID: 0}
  - Name: Sliders
    Active: 1
    MenuItems:
      array:
      - Name: Title
        Active: 1
        Type: 0
        TitleText: Sliders
        TextType: 2
        TextContents: Do you like sliders? I think they're amazing. So here are a
          few sliders. They don't do anything, but you can play with them nonetheless.
        ChoicesType: 0
        ChoiceOneText: 
        ChoiceOneEventName: 
        ChoiceTwoText: 
        ChoiceTwoEventName: 
        ChoiceThreeText: 
        ChoiceThreeEventName: 
        SelectedChoice: 0
        ValueLabel: 
        ValueInitialValue: 
        ValueMMRadioReceiverChannel: 0
        ButtonText: 
        ButtonType: 0
        ButtonEventName: 
        SpacerType: 0
        CheckboxText: 
        CheckboxInitialState: 0
        CheckboxEventName: 
        SliderMode: 0
        SliderText: 
        SliderRemapZero: 0
        SliderRemapOne: 0
        SliderInitialValue: 0
        SliderEventName: 
        TargetSlider: {fileID: 0}
        TargetButton: {fileID: 0}
        TargetCheckbox: {fileID: 0}
      - Name: Intro
        Active: 1
        Type: 5
        TitleText: 
        TextType: 2
        TextContents: Do you like sliders? I think they're amazing. So here are a
          few sliders. They don't do anything, but you can play with them nonetheless.
        ChoicesType: 0
        ChoiceOneText: 
        ChoiceOneEventName: 
        ChoiceTwoText: 
        ChoiceTwoEventName: 
        ChoiceThreeText: 
        ChoiceThreeEventName: 
        SelectedChoice: 0
        ValueLabel: 
        ValueInitialValue: 
        ValueMMRadioReceiverChannel: 0
        ButtonText: 
        ButtonType: 0
        ButtonEventName: 
        SpacerType: 0
        CheckboxText: 
        CheckboxInitialState: 0
        CheckboxEventName: 
        SliderMode: 0
        SliderText: 
        SliderRemapZero: 0
        SliderRemapOne: 0
        SliderInitialValue: 0
        SliderEventName: 
        TargetSlider: {fileID: 0}
        TargetButton: {fileID: 0}
        TargetCheckbox: {fileID: 0}
      - Name: SliderZeroToTen
        Active: 1
        Type: 4
        TitleText: 
        TextType: 2
        TextContents: Do you like sliders? I think they're amazing. So here are a
          few sliders. They don't do anything, but you can play with them nonetheless.
        ChoicesType: 0
        ChoiceOneText: 
        ChoiceOneEventName: 
        ChoiceTwoText: 
        ChoiceTwoEventName: 
        ChoiceThreeText: 
        ChoiceThreeEventName: 
        SelectedChoice: 0
        ValueLabel: 
        ValueInitialValue: 
        ValueMMRadioReceiverChannel: 0
        ButtonText: 
        ButtonType: 0
        ButtonEventName: 
        SpacerType: 0
        CheckboxText: 
        CheckboxInitialState: 0
        CheckboxEventName: 
        SliderMode: 0
        SliderText: Zero to Ten
        SliderRemapZero: 0
        SliderRemapOne: 10
        SliderInitialValue: 3
        SliderEventName: 
        TargetSlider: {fileID: 0}
        TargetButton: {fileID: 0}
        TargetCheckbox: {fileID: 0}
      - Name: SliderZeroToTenInt
        Active: 1
        Type: 4
        TitleText: 
        TextType: 2
        TextContents: Do you like sliders? I think they're amazing. So here are a
          few sliders. They don't do anything, but you can play with them nonetheless.
        ChoicesType: 0
        ChoiceOneText: 
        ChoiceOneEventName: 
        ChoiceTwoText: 
        ChoiceTwoEventName: 
        ChoiceThreeText: 
        ChoiceThreeEventName: 
        SelectedChoice: 0
        ValueLabel: 
        ValueInitialValue: 
        ValueMMRadioReceiverChannel: 0
        ButtonText: 
        ButtonType: 0
        ButtonEventName: 
        SpacerType: 0
        CheckboxText: 
        CheckboxInitialState: 0
        CheckboxEventName: 
        SliderMode: 1
        SliderText: Zero to Ten (int)
        SliderRemapZero: 0
        SliderRemapOne: 10
        SliderInitialValue: 8
        SliderEventName: 
        TargetSlider: {fileID: 0}
        TargetButton: {fileID: 0}
        TargetCheckbox: {fileID: 0}
      - Name: SliderMillion
        Active: 1
        Type: 4
        TitleText: 
        TextType: 2
        TextContents: Do you like sliders? I think they're amazing. So here are a
          few sliders. They don't do anything, but you can play with them nonetheless.
        ChoicesType: 0
        ChoiceOneText: 
        ChoiceOneEventName: 
        ChoiceTwoText: 
        ChoiceTwoEventName: 
        ChoiceThreeText: 
        ChoiceThreeEventName: 
        SelectedChoice: 0
        ValueLabel: 
        ValueInitialValue: 
        ValueMMRadioReceiverChannel: 0
        ButtonText: 
        ButtonType: 0
        ButtonEventName: 
        SpacerType: 0
        CheckboxText: 
        CheckboxInitialState: 0
        CheckboxEventName: 
        SliderMode: 1
        SliderText: The Million
        SliderRemapZero: 0
        SliderRemapOne: 1000000
        SliderInitialValue: 500000
        SliderEventName: 
        TargetSlider: {fileID: 0}
        TargetButton: {fileID: 0}
        TargetCheckbox: {fileID: 0}
      - Name: SliderCountToTen
        Active: 1
        Type: 4
        TitleText: 
        TextType: 2
        TextContents: Do you like sliders? I think they're amazing. So here are a
          few sliders. They don't do anything, but you can play with them nonetheless.
        ChoicesType: 0
        ChoiceOneText: 
        ChoiceOneEventName: 
        ChoiceTwoText: 
        ChoiceTwoEventName: 
        ChoiceThreeText: 
        ChoiceThreeEventName: 
        SelectedChoice: 0
        ValueLabel: 
        ValueInitialValue: 
        ValueMMRadioReceiverChannel: 0
        ButtonText: 
        ButtonType: 0
        ButtonEventName: 
        SpacerType: 0
        CheckboxText: 
        CheckboxInitialState: 0
        CheckboxEventName: 
        SliderMode: 1
        SliderText: Count to ten
        SliderRemapZero: 0
        SliderRemapOne: 10
        SliderInitialValue: 3
        SliderEventName: 
        TargetSlider: {fileID: 0}
        TargetButton: {fileID: 0}
        TargetCheckbox: {fileID: 0}
      - Name: SliderFifty
        Active: 1
        Type: 4
        TitleText: 
        TextType: 2
        TextContents: Do you like sliders? I think they're amazing. So here are a
          few sliders. They don't do anything, but you can play with them nonetheless.
        ChoicesType: 0
        ChoiceOneText: 
        ChoiceOneEventName: 
        ChoiceTwoText: 
        ChoiceTwoEventName: 
        ChoiceThreeText: 
        ChoiceThreeEventName: 
        SelectedChoice: 0
        ValueLabel: 
        ValueInitialValue: 
        ValueMMRadioReceiverChannel: 0
        ButtonText: 
        ButtonType: 0
        ButtonEventName: 
        SpacerType: 0
        CheckboxText: 
        CheckboxInitialState: 0
        CheckboxEventName: 
        SliderMode: 1
        SliderText: Fifty
        SliderRemapZero: 0
        SliderRemapOne: 50
        SliderInitialValue: 25
        SliderEventName: 
        TargetSlider: {fileID: 0}
        TargetButton: {fileID: 0}
        TargetCheckbox: {fileID: 0}
      - Name: SliderZeroToOne
        Active: 1
        Type: 4
        TitleText: 
        TextType: 2
        TextContents: Do you like sliders? I think they're amazing. So here are a
          few sliders. They don't do anything, but you can play with them nonetheless.
        ChoicesType: 0
        ChoiceOneText: 
        ChoiceOneEventName: 
        ChoiceTwoText: 
        ChoiceTwoEventName: 
        ChoiceThreeText: 
        ChoiceThreeEventName: 
        SelectedChoice: 0
        ValueLabel: 
        ValueInitialValue: 
        ValueMMRadioReceiverChannel: 0
        ButtonText: 
        ButtonType: 0
        ButtonEventName: 
        SpacerType: 0
        CheckboxText: 
        CheckboxInitialState: 0
        CheckboxEventName: 
        SliderMode: 0
        SliderText: Zero to One
        SliderRemapZero: 0
        SliderRemapOne: 1
        SliderInitialValue: 0.7
        SliderEventName: 
        TargetSlider: {fileID: 0}
        TargetButton: {fileID: 0}
        TargetCheckbox: {fileID: 0}
  DisplayDebugTab: 1
  MaxTabs: 5
  InitialActiveTabIndex: 0
  ToggleDirection: 2
  ToggleDuration: 0.25
  ToggleCurve: 11
  ToggleKey: 4
  RegularFont: {fileID: 12800000, guid: 0f03458534bea45e4bba9ab3411321b2, type: 3}
  BoldFont: {fileID: 12800000, guid: ae4e4b833eb9f63448b39edf3f03e309, type: 3}
  BackgroundColor: {r: 0, g: 0, b: 0, a: 0.78431374}
  AccentColor: {r: 1, g: 0.76862746, b: 0, a: 1}
  TextColor: {r: 0, g: 0.6127534, b: 1, a: 1}
