# Unity6 Datamosh Guide - Quick Start Guide

## 🚀 **GET STARTED IN 5 MINUTES**

This guide helps you quickly start using the new Unity6 Datamosh Guide enhancements in Flux.

---

## 🎯 **WHAT'S NEW**

### **Enhanced Motion Vector Processing**

- **Better trailing effects** with Unity6 Guide coordinate transformation
- **Improved motion sensitivity** with pixelated noise generation
- **Smoother datamosh transitions** with enhanced sampling

### **Custom Render Texture Pipeline**

- **Maximum quality mode** using Unity6 Guide Custom RT techniques
- **Scalable performance** with resolution and update rate controls
- **Professional-grade results** for cinematics and high-end projects

---

## ⚡ **QUICK SETUP**

### **Step 1: Enable Enhanced Motion Processing**

1. **Select your Volume** in the scene
2. **Open Flux Effect** component
3. **Scroll to "Unity6 Render Graph Enhancements"** section
4. **Verify "Enhanced Motion Processing" is enabled** (it's on by default)

✅ **You're done!** Enhanced motion processing is now active.

### **Step 1.5: CRITICAL - Ensure Motion Vectors Are Working**

⚠️ **IMPORTANT**: Unity6 Guide enhancements require motion vectors to be active. If you don't see any difference:

1. **Check "True Datamoshing" section** in Flux Effect
2. **Set "Base Noise" to at least 0.1** (this enables motion vector generation)
3. **Set "Length Influence" to at least 0.5** (this makes motion vectors visible)
4. **Temporarily enable "Visualize Motion Vectors"** to verify they're working
5. **Move the camera or objects** - you should see colored motion trails

🔧 **If motion vectors aren't working:**

- Check your URP Renderer asset has "Motion Vectors" enabled
- Ensure objects have motion vector generation enabled
- Verify you're not in Scene View (motion vectors don't work in Scene View)

### **Step 2: Adjust Enhancement Intensity**

**✅ Default Settings (Good Starting Point):**

- **Coordinate Transform Intensity**: 2.0 (increased for visibility)
- **Enhanced Sampling Blend**: 0.7 (increased for stronger effect)
- **Pixelated Noise Scale**: 75 (balanced detail)

**For Subtle Enhancement:**

- **Coordinate Transform Intensity**: 1.0-1.5
- **Enhanced Sampling Blend**: 0.3-0.5
- **Pixelated Noise Scale**: 30-50

**For Dramatic Enhancement:**

- **Coordinate Transform Intensity**: 2.5-3.5
- **Enhanced Sampling Blend**: 0.8-1.0
- **Pixelated Noise Scale**: 100-150

**For Maximum Enhancement:**

- **Coordinate Transform Intensity**: 4.0-5.0
- **Enhanced Sampling Blend**: 1.0
- **Pixelated Noise Scale**: 150-200

💡 **Pro Tip**: The Unity6 Guide enhancements now work in **both** Pure Datamosh Mode and Enhanced Mode, so you'll see improvements regardless of your current Flux settings!

---

## 🎨 **CUSTOM RT PIPELINE (ADVANCED)**

### **What is Custom RT Pipeline?**

The Custom RT Pipeline implements the Unity6 Datamosh Guide's advanced Custom Render Texture techniques within Flux. Unlike the original guide which requires manual setup of Custom RT assets and Shader Graphs, Flux automatically handles all the complexity for you.

**Original Unity6 Guide**: Manual Custom RT creation + Shader Graph setup + Blit passes
**Flux Integration**: Single checkbox enables everything automatically

### **When to Use Custom RT**

- ✅ **Cinematics and cutscenes** - Maximum visual quality
- ✅ **High-end PC/Console** - When performance allows
- ✅ **Screenshot/Video capture** - Professional results
- ❌ **Mobile platforms** - Too resource intensive
- ❌ **Real-time gameplay** - May impact framerate

### **Enable Custom RT Pipeline**

1. **Navigate to "Custom Render Texture Pipeline"** section
2. **Enable "Enable Custom RT Pipeline"**
3. **Set "Custom RT Resolution Scale"**:
   - **1.0** = Full resolution (best quality, highest cost)
   - **0.75** = 75% resolution (good balance)
   - **0.5** = Half resolution (performance mode)
4. **Adjust "Custom RT Blend"** to taste (1.0 = full Custom RT effect)

### **Custom RT Performance Settings**

| Setting                           | Performance | Quality   | Recommended For              |
| --------------------------------- | ----------- | --------- | ---------------------------- |
| **Resolution 0.5x, Update 0.5x**  | High        | Good      | Real-time with quality boost |
| **Resolution 0.75x, Update 1.0x** | Medium      | Very Good | Balanced approach            |
| **Resolution 1.0x, Update 1.0x**  | Low         | Excellent | Cinematics only              |

---

## 🎛️ **PARAMETER REFERENCE**

### **Enhanced Motion Processing**

#### **Coordinate Transform Intensity** (0-5)

- **What it does**: Controls how strongly Unity6 Guide coordinate transformation affects motion vectors
- **Low values (0.5-1.0)**: Subtle enhancement, maintains original feel
- **Medium values (1.5-2.5)**: Noticeable improvement in trailing effects
- **High values (3.0-5.0)**: Dramatic motion-based effects

#### **Pixelated Noise Scale** (10-200)

- **What it does**: Controls the scale of Unity6 Guide pixelated noise generation
- **Low values (10-30)**: Large, chunky noise patterns
- **Medium values (50-80)**: Balanced noise detail
- **High values (120-200)**: Fine, detailed noise patterns

#### **Enhanced Sampling Blend** (0-1)

- **What it does**: Blends between standard and Unity6 Guide enhanced motion vector sampling
- **0.0**: Pure standard Flux behavior
- **0.5**: Balanced blend of standard and enhanced
- **1.0**: Pure Unity6 Guide enhanced sampling

### **Custom RT Pipeline**

#### **Custom RT Resolution Scale** (0.25-2.0)

- **What it does**: Controls the resolution of the Custom Render Texture
- **0.25x**: Quarter resolution (very fast, lower quality)
- **0.5x**: Half resolution (good performance/quality balance)
- **1.0x**: Full resolution (best quality, standard performance)
- **2.0x**: Double resolution (maximum quality, very slow)

#### **Custom RT Update Rate** (0.1-2.0)

- **What it does**: Controls how frequently the Custom RT updates
- **0.1-0.5**: Slow updates (performance mode)
- **1.0**: Standard update rate
- **1.5-2.0**: Fast updates (responsive mode)

#### **Custom RT Blend** (0-1)

- **What it does**: Blends between standard Flux output and Custom RT enhanced output
- **0.0**: Pure standard Flux
- **0.5**: 50/50 blend
- **1.0**: Pure Custom RT enhanced

---

## 🎯 **PRESET RECOMMENDATIONS**

### **"Enhanced Subtle" Preset**

```
Enhanced Motion Processing: ✅ Enabled
Coordinate Transform Intensity: 1.2
Pixelated Noise Scale: 45
Enhanced Sampling Blend: 0.4
Custom RT Pipeline: ❌ Disabled
```

**Use for**: General gameplay enhancement without performance impact

### **"Enhanced Dramatic" Preset**

```
Enhanced Motion Processing: ✅ Enabled
Coordinate Transform Intensity: 2.8
Pixelated Noise Scale: 95
Enhanced Sampling Blend: 0.8
Custom RT Pipeline: ❌ Disabled
```

**Use for**: Strong datamosh effects while maintaining good performance

### **"Custom RT Cinematic" Preset**

```
Enhanced Motion Processing: ✅ Enabled
Coordinate Transform Intensity: 3.5
Pixelated Noise Scale: 120
Enhanced Sampling Blend: 1.0
Custom RT Pipeline: ✅ Enabled
Custom RT Resolution Scale: 1.0
Custom RT Update Rate: 1.0
Custom RT Blend: 1.0
```

**Use for**: Maximum quality cinematics and video capture

### **"Custom RT Performance" Preset**

```
Enhanced Motion Processing: ✅ Enabled
Coordinate Transform Intensity: 2.0
Pixelated Noise Scale: 70
Enhanced Sampling Blend: 0.6
Custom RT Pipeline: ✅ Enabled
Custom RT Resolution Scale: 0.5
Custom RT Update Rate: 0.7
Custom RT Blend: 0.8
```

**Use for**: Enhanced quality with manageable performance impact

---

## 🔧 **TROUBLESHOOTING**

### **Performance Issues**

**Problem**: Frame rate drops when using Custom RT
**Solution**:

- Reduce "Custom RT Resolution Scale" to 0.5 or 0.25
- Lower "Custom RT Update Rate" to 0.5-0.7
- Reduce "Custom RT Blend" to 0.5-0.7

**Problem**: Enhanced motion processing causes stuttering
**Solution**:

- Reduce "Coordinate Transform Intensity" to 1.0-1.5
- Lower "Enhanced Sampling Blend" to 0.3-0.5

### **Visual Issues**

**Problem**: Effects are too subtle
**Solution**:

- Increase "Coordinate Transform Intensity" to 2.0+
- Increase "Enhanced Sampling Blend" to 0.7+
- Enable Custom RT Pipeline for maximum effect

**Problem**: Effects are too strong/chaotic
**Solution**:

- Reduce "Coordinate Transform Intensity" to 0.5-1.0
- Lower "Enhanced Sampling Blend" to 0.2-0.4
- Reduce "Pixelated Noise Scale" to 20-40

**Problem**: No visible difference from standard Flux
**Solution**:

- ✅ **MOST COMMON ISSUE**: Motion vectors not working
  - Set "Base Noise" to 0.2+ in True Datamoshing section
  - Set "Length Influence" to 1.0+ in True Datamoshing section
  - Enable "Visualize Motion Vectors" - you should see colored trails when moving
- ✅ **Check Unity6 Guide parameters**:
  - Ensure "Enhanced Motion Processing" is enabled
  - Increase "Coordinate Transform Intensity" to 2.0+
  - Increase "Enhanced Sampling Blend" to 0.7+
- ✅ **Verify setup**:
  - Test in Play Mode, not Scene View
  - Move camera or objects to generate motion
  - Check URP Renderer has Motion Vectors enabled
- ✅ **Try Custom RT Pipeline**:
  - Enable "Custom RT Pipeline" for maximum effect
  - Set "Custom RT Blend" to 1.0

---

## 📊 **PERFORMANCE MONITORING**

### **Check Performance Impact**

1. **Open Unity Profiler** (Window → Analysis → Profiler)
2. **Enable GPU Profiler**
3. **Look for "Flux Custom RT Processing" passes**
4. **Monitor frame time increase**

### **Performance Targets**

| Platform        | Target | Enhanced Motion | Custom RT 0.5x | Custom RT 1.0x |
| --------------- | ------ | --------------- | -------------- | -------------- |
| **Mobile**      | 60 FPS | ✅ Good         | ⚠️ Caution     | ❌ Too Heavy   |
| **Console**     | 60 FPS | ✅ Excellent    | ✅ Good        | ⚠️ Caution     |
| **High-end PC** | 60 FPS | ✅ Excellent    | ✅ Excellent   | ✅ Good        |

---

## 🎉 **YOU'RE READY!**

You now have access to cutting-edge Unity6 Datamosh Guide techniques integrated seamlessly into Flux. Start with the **"Enhanced Subtle"** preset and experiment from there!

### **Next Steps**

1. **Try different presets** to find your preferred look
2. **Experiment with Custom RT** for high-quality scenarios
3. **Create custom presets** combining Unity6 enhancements with existing Flux features
4. **Share your results** with the community!

**Happy datamoshing! 🎬✨**
