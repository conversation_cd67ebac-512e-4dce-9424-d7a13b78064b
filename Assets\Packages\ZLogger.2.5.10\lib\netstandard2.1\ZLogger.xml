<?xml version="1.0"?>
<doc>
    <assembly>
        <name>ZLogger</name>
    </assembly>
    <members>
        <member name="F:ZLogger.KeyNameMutator.LastMemberName">
            <summary>
            Returns the last member name of the source.
            </summary>
        </member>
        <member name="F:ZLogger.KeyNameMutator.LowerFirstCharacter">
            <summary>
            The first character converted to lowercase.
            </summary>
        </member>
        <member name="F:ZLogger.KeyNameMutator.UpperFirstCharacter">
            <summary>
            The first character converted to uppercase.
            </summary>
        </member>
        <member name="F:ZLogger.KeyNameMutator.LastMemberNameLowerFirstCharacter">
            <summary>
            Returns the last member name of the source with the first character converted to lowercase.
            </summary>
        </member>
        <member name="F:ZLogger.KeyNameMutator.LastMemberNameUpperFirstCharacter">
            <summary>
            Returns the last member name of the source with the first character converted to uppercase.
            </summary>
        </member>
        <member name="M:ZLogger.ZLoggerBuilderExtensions.AddZLoggerRollingFile(Microsoft.Extensions.Logging.ILoggingBuilder,System.Func{System.DateTimeOffset,System.Int32,System.String},ZLogger.Providers.RollingInterval)">
            <param name="filePathSelector">DateTimeOffset is date of file open time(UTC), int is number sequence.</param>
            <param name="rollInterval">Interval to automatically rotate files</param>
        </member>
        <member name="M:ZLogger.ZLoggerBuilderExtensions.AddZLoggerRollingFile(Microsoft.Extensions.Logging.ILoggingBuilder,System.Func{System.DateTimeOffset,System.Int32,System.String},System.Int32)">
            <param name="filePathSelector">DateTimeOffset is date of file open time(UTC), int is number sequence.</param>
            <param name="rollSizeKB">Limit size of single file.</param>
        </member>
        <member name="M:ZLogger.ZLoggerBuilderExtensions.AddZLoggerRollingFile(Microsoft.Extensions.Logging.ILoggingBuilder,System.Func{System.DateTimeOffset,System.Int32,System.String},ZLogger.Providers.RollingInterval,System.Int32)">
            <param name="filePathSelector">DateTimeOffset is date of file open time(UTC), int is number sequence.</param>
            <param name="rollInterval">Interval to automatically rotate files</param>
            <param name="rollSizeKB">Limit size of single file.</param>
        </member>
        <member name="M:ZLogger.ZLoggerInterpolatedStringHandler.#ctor(System.Int32,System.Int32,Microsoft.Extensions.Logging.ILogger,Microsoft.Extensions.Logging.LogLevel,System.Boolean@)">
            <summary>
            DO NOT ALLOW DIRECT USE.
            </summary>
        </member>
        <member name="T:ZLogger.ZLoggerMessageAttribute">
            <summary>
            Provides information to guide the production of a strongly-typed logging method.
            </summary>
            <remarks>
            <para>The method this attribute is applied to:</para>
            <para>   - Must be a partial method.</para>
            <para>   - Must return <c>void</c>.</para>
            <para>   - Must not be generic.</para>
            <para>   - Must have an <see cref="T:Microsoft.Extensions.Logging.ILogger"/> as one of its parameters.</para>
            <para>   - Must have a <see cref="T:Microsoft.Extensions.Logging.LogLevel"/> as one of its parameters.</para>
            <para>   - None of the parameters can be generic.</para>
            </remarks>
            <example>
            <format type="text/markdown"><![CDATA[
            ```csharp
            static partial class Log
            {
                [ZLoggerMessage(EventId = 0, Message = "Could not open socket for {hostName}")]
                static partial void CouldNotOpenSocket(ILogger logger, LogLevel level, string hostName);
            }
            ```
            ]]></format>
            </example>
        </member>
        <member name="M:ZLogger.ZLoggerMessageAttribute.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:ZLogger.ZLoggerMessageAttribute"/> class
            which is used to guide the production of a strongly-typed logging method.
            </summary>
        </member>
        <member name="M:ZLogger.ZLoggerMessageAttribute.#ctor(Microsoft.Extensions.Logging.LogLevel,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:ZLogger.ZLoggerMessageAttribute"/> class
            which is used to guide the production of a strongly-typed logging method.
            </summary>
            <param name="level">The log level.</param>
            <param name="message">Format string of the log message.</param>
        </member>
        <member name="M:ZLogger.ZLoggerMessageAttribute.#ctor(System.Int32,Microsoft.Extensions.Logging.LogLevel,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:ZLogger.ZLoggerMessageAttribute"/> class
            which is used to guide the production of a strongly-typed logging method.
            </summary>
            <param name="eventId">The log event Id.</param>
            <param name="level">The log level.</param>
            <param name="message">Format string of the log message.</param>
        </member>
        <member name="M:ZLogger.ZLoggerMessageAttribute.#ctor(Microsoft.Extensions.Logging.LogLevel)">
            <summary>
            Initializes a new instance of the <see cref="T:ZLogger.ZLoggerMessageAttribute"/> class
            which is used to guide the production of a strongly-typed logging method.
            </summary>
            <param name="level">The log level.</param>
        </member>
        <member name="M:ZLogger.ZLoggerMessageAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Extensions.Logging.LoggerMessageAttribute"/> class
            which is used to guide the production of a strongly-typed logging method.
            </summary>
            <param name="message">Format string of the log message.</param>
        </member>
        <member name="P:ZLogger.ZLoggerMessageAttribute.EventId">
            <summary>
            Gets the logging event id for the logging method.
            </summary>
        </member>
        <member name="P:ZLogger.ZLoggerMessageAttribute.EventName">
            <summary>
            Gets or sets the logging event name for the logging method.
            </summary>
            <remarks>
            This will equal the method name if not specified.
            </remarks>
        </member>
        <member name="P:ZLogger.ZLoggerMessageAttribute.Level">
            <summary>
            Gets the logging level for the logging method.
            </summary>
        </member>
        <member name="P:ZLogger.ZLoggerMessageAttribute.Message">
            <summary>
            Gets the message text for the logging method.
            </summary>
        </member>
        <member name="P:ZLogger.ZLoggerMessageAttribute.SkipEnabledCheck">
            <summary>
            Gets the flag to skip IsEnabled check for the logging method.
            </summary>
        </member>
        <member name="F:ZLogger.BackgroundBufferFullMode.Grow">
            <summary>
            Grow the queue.
            </summary>
        </member>
        <member name="F:ZLogger.BackgroundBufferFullMode.Block">
            <summary>
            Wait until there's more room in the queue.
            </summary>
        </member>
        <member name="F:ZLogger.BackgroundBufferFullMode.Drop">
            <summary>
            Drop the overflowing log entry.
            </summary>
        </member>
        <member name="P:ZLogger.ZLoggerOptions.InternalErrorLogger">
            <summary>
            `InternalErrorLogger` is a delegate that is called when an exception occurs in the log writing process (such as a serialization error). The default value is `null`, which means errors are ignored.
            </summary>
        </member>
        <member name="P:ZLogger.ZLoggerOptions.IncludeScopes">
            <summary>
            Enable `ILogger.BeginScope`, default is `false`.
            </summary>
        </member>
        <member name="P:ZLogger.ZLoggerOptions.TimeProvider">
            <summary>
            Gets or sets the time provider for the logger. The Timestamp of LogInfo is generated by TimeProvider's GetUtcNow() and LocalTimeZone when TimeProvider is set. The default value is null, which means use the system standard.
            </summary>
        </member>
        <member name="P:ZLogger.ZLoggerOptions.FullMode">
            <summary>
            Gets or sets behavior when the queue capacity for writing in the background is full
            </summary>
        </member>
        <member name="P:ZLogger.ZLoggerOptions.BackgroundBufferCapacity">
            <summary>
            Sets the queue capacity for writing in the background; has no meaning if FullMode is Grow.
            </summary>
        </member>
        <member name="M:ZLogger.ZLoggerOptions.CreateFormatter">
            <summary>
            Create an formatter to use in ZLoggerProvider.
            </summary>
        </member>
        <member name="P:ZLogger.ZLoggerOptions.IsFormatLogImmediatelyInStandardLog">
            <summary>
            Fallback of standard logger.Log, message stringify immediately or not. Default is true.
            </summary>
        </member>
        <member name="P:ZLogger.ZLoggerOptions.CaptureThreadInfo">
            <summary>
            Capture information about the thread that generated a log entry. Default is false.
            </summary>
        </member>
        <member name="T:System.Runtime.InteropServices.SuppressGCTransitionAttribute">
             <summary>
             An attribute used to indicate a GC transition should be skipped when making an unmanaged function call.
             </summary>
             <example>
             Example of a valid use case. The Win32 `GetTickCount()` function is a small performance related function
             that reads some global memory and returns the value. In this case, the GC transition overhead is significantly
             more than the memory read.
             <code>
             using System;
             using System.Runtime.InteropServices;
             class Program
             {
                 [DllImport("Kernel32")]
                 [SuppressGCTransition]
                 static extern int GetTickCount();
                 static void Main()
                 {
                     Console.WriteLine($"{GetTickCount()}");
                 }
             }
             </code>
             </example>
             <remarks>
             This attribute is ignored if applied to a method without the <see cref="T:System.Runtime.InteropServices.DllImportAttribute"/>.
            
             Forgoing this transition can yield benefits when the cost of the transition is more than the execution time
             of the unmanaged function. However, avoiding this transition removes some of the guarantees the runtime
             provides through a normal P/Invoke. When exiting the managed runtime to enter an unmanaged function the
             GC must transition from Cooperative mode into Preemptive mode. Full details on these modes can be found at
             https://github.com/dotnet/runtime/blob/main/docs/coding-guidelines/clr-code-guide.md#2.1.8.
             Suppressing the GC transition is an advanced scenario and should not be done without fully understanding
             potential consequences.
            
             One of these consequences is an impact to Mixed-mode debugging (https://docs.microsoft.com/visualstudio/debugger/how-to-debug-in-mixed-mode).
             During Mixed-mode debugging, it is not possible to step into or set breakpoints in a P/Invoke that
             has been marked with this attribute. A workaround is to switch to native debugging and set a breakpoint in the native function.
             In general, usage of this attribute is not recommended if debugging the P/Invoke is important, for example
             stepping through the native code or diagnosing an exception thrown from the native code.
            
             The runtime may load the native library for method marked with this attribute in advance before the method is called for the first time.
             Usage of this attribute is not recommended for platform neutral libraries with conditional platform specific code.
            
             The P/Invoke method that this attribute is applied to must have all of the following properties:
               * Native function always executes for a trivial amount of time (less than 1 microsecond).
               * Native function does not perform a blocking syscall (e.g. any type of I/O).
               * Native function does not call back into the runtime (e.g. Reverse P/Invoke).
               * Native function does not throw exceptions.
               * Native function does not manipulate locks or other concurrency primitives.
            
             Consequences of invalid uses of this attribute:
               * GC starvation.
               * Immediate runtime termination.
               * Data corruption.
             </remarks>
        </member>
        <member name="T:System.Runtime.InteropServices.UnmanagedCallersOnlyAttribute">
            <summary>
            Any method marked with <see cref="T:System.Runtime.InteropServices.UnmanagedCallersOnlyAttribute" /> can be directly called from
            native code. The function token can be loaded to a local variable using the <see href="https://docs.microsoft.com/dotnet/csharp/language-reference/operators/pointer-related-operators#address-of-operator-">address-of</see> operator
            in C# and passed as a callback to a native method.
            </summary>
            <remarks>
            Methods marked with this attribute have the following restrictions:
              * Method must be marked "static".
              * Must not be called from managed code.
              * Must only have <see href="https://docs.microsoft.com/dotnet/framework/interop/blittable-and-non-blittable-types">blittable</see> arguments.
            </remarks>
        </member>
        <member name="F:System.Runtime.InteropServices.UnmanagedCallersOnlyAttribute.CallConvs">
            <summary>
            Optional. If omitted, the runtime will use the default platform calling convention.
            </summary>
            <remarks>
            Supplied types must be from the official "System.Runtime.CompilerServices" namespace and
            be of the form "CallConvXXX".
            </remarks>
        </member>
        <member name="F:System.Runtime.InteropServices.UnmanagedCallersOnlyAttribute.EntryPoint">
            <summary>
            Optional. If omitted, no named export is emitted during compilation.
            </summary>
        </member>
        <member name="T:System.Runtime.CompilerServices.DefaultInterpolatedStringHandler">
            <summary>Provides a handler used by the language compiler to process interpolated strings into <see cref="T:System.String"/> instances.</summary>
        </member>
        <member name="F:System.Runtime.CompilerServices.DefaultInterpolatedStringHandler.GuessedLengthPerHole">
            <summary>Expected average length of formatted data used for an individual interpolation expression result.</summary>
            <remarks>
            This is inherited from string.Format, and could be changed based on further data.
            string.Format actually uses `format.Length + args.Length * 8`, but format.Length
            includes the format items themselves, e.g. "{0}", and since it's rare to have double-digit
            numbers of items, we bump the 8 up to 11 to account for the three extra characters in "{d}",
            since the compiler-provided base length won't include the equivalent character count.
            </remarks>
        </member>
        <member name="F:System.Runtime.CompilerServices.DefaultInterpolatedStringHandler.MinimumArrayPoolLength">
            <summary>Minimum size array to rent from the pool.</summary>
            <remarks>Same as stack-allocation size used today by string.Format.</remarks>
        </member>
        <member name="F:System.Runtime.CompilerServices.DefaultInterpolatedStringHandler._provider">
            <summary>Optional provider to pass to IFormattable.ToString or ISpanFormattable.TryFormat calls.</summary>
        </member>
        <member name="F:System.Runtime.CompilerServices.DefaultInterpolatedStringHandler._arrayToReturnToPool">
            <summary>Array rented from the array pool and used to back <see cref="F:System.Runtime.CompilerServices.DefaultInterpolatedStringHandler._chars"/>.</summary>
        </member>
        <member name="F:System.Runtime.CompilerServices.DefaultInterpolatedStringHandler._chars">
            <summary>The span to write into.</summary>
        </member>
        <member name="F:System.Runtime.CompilerServices.DefaultInterpolatedStringHandler._pos">
            <summary>Position at which to write the next character.</summary>
        </member>
        <member name="F:System.Runtime.CompilerServices.DefaultInterpolatedStringHandler._hasCustomFormatter">
            <summary>Whether <see cref="F:System.Runtime.CompilerServices.DefaultInterpolatedStringHandler._provider"/> provides an ICustomFormatter.</summary>
            <remarks>
            Custom formatters are very rare.  We want to support them, but it's ok if we make them more expensive
            in order to make them as pay-for-play as possible.  So, we avoid adding another reference type field
            to reduce the size of the handler and to reduce required zero'ing, by only storing whether the provider
            provides a formatter, rather than actually storing the formatter.  This in turn means, if there is a
            formatter, we pay for the extra interface call on each AppendFormatted that needs it.
            </remarks>
        </member>
        <member name="M:System.Runtime.CompilerServices.DefaultInterpolatedStringHandler.#ctor(System.Int32,System.Int32)">
            <summary>Creates a handler used to translate an interpolated string into a <see cref="T:System.String"/>.</summary>
            <param name="literalLength">The number of constant characters outside of interpolation expressions in the interpolated string.</param>
            <param name="formattedCount">The number of interpolation expressions in the interpolated string.</param>
            <remarks>This is intended to be called only by compiler-generated code. Arguments are not validated as they'd otherwise be for members intended to be used directly.</remarks>
        </member>
        <member name="M:System.Runtime.CompilerServices.DefaultInterpolatedStringHandler.#ctor(System.Int32,System.Int32,System.IFormatProvider)">
            <summary>Creates a handler used to translate an interpolated string into a <see cref="T:System.String"/>.</summary>
            <param name="literalLength">The number of constant characters outside of interpolation expressions in the interpolated string.</param>
            <param name="formattedCount">The number of interpolation expressions in the interpolated string.</param>
            <param name="provider">An object that supplies culture-specific formatting information.</param>
            <remarks>This is intended to be called only by compiler-generated code. Arguments are not validated as they'd otherwise be for members intended to be used directly.</remarks>
        </member>
        <member name="M:System.Runtime.CompilerServices.DefaultInterpolatedStringHandler.#ctor(System.Int32,System.Int32,System.IFormatProvider,System.Span{System.Char})">
            <summary>Creates a handler used to translate an interpolated string into a <see cref="T:System.String"/>.</summary>
            <param name="literalLength">The number of constant characters outside of interpolation expressions in the interpolated string.</param>
            <param name="formattedCount">The number of interpolation expressions in the interpolated string.</param>
            <param name="provider">An object that supplies culture-specific formatting information.</param>
            <param name="initialBuffer">A buffer temporarily transferred to the handler for use as part of its formatting.  Contents may be overwritten.</param>
            <remarks>This is intended to be called only by compiler-generated code. Arguments are not validated as they'd otherwise be for members intended to be used directly.</remarks>
        </member>
        <member name="M:System.Runtime.CompilerServices.DefaultInterpolatedStringHandler.GetDefaultLength(System.Int32,System.Int32)">
            <summary>Derives a default length with which to seed the handler.</summary>
            <param name="literalLength">The number of constant characters outside of interpolation expressions in the interpolated string.</param>
            <param name="formattedCount">The number of interpolation expressions in the interpolated string.</param>
        </member>
        <member name="M:System.Runtime.CompilerServices.DefaultInterpolatedStringHandler.ToString">
            <summary>Gets the built <see cref="T:System.String"/>.</summary>
            <returns>The built string.</returns>
        </member>
        <member name="M:System.Runtime.CompilerServices.DefaultInterpolatedStringHandler.ToStringAndClear">
            <summary>Gets the built <see cref="T:System.String"/> and clears the handler.</summary>
            <returns>The built string.</returns>
            <remarks>
            This releases any resources used by the handler. The method should be invoked only
            once and as the last thing performed on the handler. Subsequent use is erroneous, ill-defined,
            and may destabilize the process, as may using any other copies of the handler after ToStringAndClear
            is called on any one of them.
            </remarks>
        </member>
        <member name="M:System.Runtime.CompilerServices.DefaultInterpolatedStringHandler.Clear">
            <summary>Clears the handler, returning any rented array to the pool.</summary>
        </member>
        <member name="P:System.Runtime.CompilerServices.DefaultInterpolatedStringHandler.Text">
            <summary>Gets a span of the written characters thus far.</summary>
        </member>
        <member name="M:System.Runtime.CompilerServices.DefaultInterpolatedStringHandler.AppendLiteral(System.String)">
            <summary>Writes the specified string to the handler.</summary>
            <param name="value">The string to write.</param>
        </member>
        <member name="M:System.Runtime.CompilerServices.DefaultInterpolatedStringHandler.AppendFormatted``1(``0)">
            <summary>Writes the specified value to the handler.</summary>
            <param name="value">The value to write.</param>
            <typeparam name="T">The type of the value to write.</typeparam>
        </member>
        <member name="M:System.Runtime.CompilerServices.DefaultInterpolatedStringHandler.AppendFormatted``1(``0,System.String)">
            <summary>Writes the specified value to the handler.</summary>
            <param name="value">The value to write.</param>
            <param name="format">The format string.</param>
            <typeparam name="T">The type of the value to write.</typeparam>
        </member>
        <member name="M:System.Runtime.CompilerServices.DefaultInterpolatedStringHandler.AppendFormatted``1(``0,System.Int32)">
            <summary>Writes the specified value to the handler.</summary>
            <param name="value">The value to write.</param>
            <param name="alignment">Minimum number of characters that should be written for this value.  If the value is negative, it indicates left-aligned and the required minimum is the absolute value.</param>
            <typeparam name="T">The type of the value to write.</typeparam>
        </member>
        <member name="M:System.Runtime.CompilerServices.DefaultInterpolatedStringHandler.AppendFormatted``1(``0,System.Int32,System.String)">
            <summary>Writes the specified value to the handler.</summary>
            <param name="value">The value to write.</param>
            <param name="format">The format string.</param>
            <param name="alignment">Minimum number of characters that should be written for this value.  If the value is negative, it indicates left-aligned and the required minimum is the absolute value.</param>
            <typeparam name="T">The type of the value to write.</typeparam>
        </member>
        <member name="M:System.Runtime.CompilerServices.DefaultInterpolatedStringHandler.AppendFormatted(System.ReadOnlySpan{System.Char})">
            <summary>Writes the specified character span to the handler.</summary>
            <param name="value">The span to write.</param>
        </member>
        <member name="M:System.Runtime.CompilerServices.DefaultInterpolatedStringHandler.AppendFormatted(System.ReadOnlySpan{System.Char},System.Int32,System.String)">
            <summary>Writes the specified string of chars to the handler.</summary>
            <param name="value">The span to write.</param>
            <param name="alignment">Minimum number of characters that should be written for this value.  If the value is negative, it indicates left-aligned and the required minimum is the absolute value.</param>
            <param name="format">The format string.</param>
        </member>
        <member name="M:System.Runtime.CompilerServices.DefaultInterpolatedStringHandler.AppendFormatted(System.String)">
            <summary>Writes the specified value to the handler.</summary>
            <param name="value">The value to write.</param>
        </member>
        <member name="M:System.Runtime.CompilerServices.DefaultInterpolatedStringHandler.AppendFormattedSlow(System.String)">
            <summary>Writes the specified value to the handler.</summary>
            <param name="value">The value to write.</param>
            <remarks>
            Slow path to handle a custom formatter, potentially null value,
            or a string that doesn't fit in the current buffer.
            </remarks>
        </member>
        <member name="M:System.Runtime.CompilerServices.DefaultInterpolatedStringHandler.AppendFormatted(System.String,System.Int32,System.String)">
            <summary>Writes the specified value to the handler.</summary>
            <param name="value">The value to write.</param>
            <param name="alignment">Minimum number of characters that should be written for this value.  If the value is negative, it indicates left-aligned and the required minimum is the absolute value.</param>
            <param name="format">The format string.</param>
        </member>
        <member name="M:System.Runtime.CompilerServices.DefaultInterpolatedStringHandler.AppendFormatted(System.Object,System.Int32,System.String)">
            <summary>Writes the specified value to the handler.</summary>
            <param name="value">The value to write.</param>
            <param name="alignment">Minimum number of characters that should be written for this value.  If the value is negative, it indicates left-aligned and the required minimum is the absolute value.</param>
            <param name="format">The format string.</param>
        </member>
        <member name="M:System.Runtime.CompilerServices.DefaultInterpolatedStringHandler.HasCustomFormatter(System.IFormatProvider)">
            <summary>Gets whether the provider provides a custom formatter.</summary>
        </member>
        <member name="M:System.Runtime.CompilerServices.DefaultInterpolatedStringHandler.AppendCustomFormatter``1(``0,System.String)">
            <summary>Formats the value using the custom formatter from the provider.</summary>
            <param name="value">The value to write.</param>
            <param name="format">The format string.</param>
            <typeparam name="T">The type of the value to write.</typeparam>
        </member>
        <member name="M:System.Runtime.CompilerServices.DefaultInterpolatedStringHandler.AppendOrInsertAlignmentIfNeeded(System.Int32,System.Int32)">
            <summary>Handles adding any padding required for aligning a formatted value in an interpolation expression.</summary>
            <param name="startingPos">The position at which the written value started.</param>
            <param name="alignment">Non-zero minimum number of characters that should be written for this value.  If the value is negative, it indicates left-aligned and the required minimum is the absolute value.</param>
        </member>
        <member name="M:System.Runtime.CompilerServices.DefaultInterpolatedStringHandler.EnsureCapacityForAdditionalChars(System.Int32)">
            <summary>Ensures <see cref="F:System.Runtime.CompilerServices.DefaultInterpolatedStringHandler._chars"/> has the capacity to store <paramref name="additionalChars"/> beyond <see cref="F:System.Runtime.CompilerServices.DefaultInterpolatedStringHandler._pos"/>.</summary>
        </member>
        <member name="M:System.Runtime.CompilerServices.DefaultInterpolatedStringHandler.GrowThenCopyString(System.String)">
            <summary>Fallback for fast path in <see cref="M:System.Runtime.CompilerServices.DefaultInterpolatedStringHandler.AppendLiteral(System.String)"/> when there's not enough space in the destination.</summary>
            <param name="value">The string to write.</param>
        </member>
        <member name="M:System.Runtime.CompilerServices.DefaultInterpolatedStringHandler.GrowThenCopySpan(System.ReadOnlySpan{System.Char})">
            <summary>Fallback for <see cref="M:System.Runtime.CompilerServices.DefaultInterpolatedStringHandler.AppendFormatted(System.ReadOnlySpan{System.Char})"/> for when not enough space exists in the current buffer.</summary>
            <param name="value">The span to write.</param>
        </member>
        <member name="M:System.Runtime.CompilerServices.DefaultInterpolatedStringHandler.Grow(System.Int32)">
            <summary>Grows <see cref="F:System.Runtime.CompilerServices.DefaultInterpolatedStringHandler._chars"/> to have the capacity to store at least <paramref name="additionalChars"/> beyond <see cref="F:System.Runtime.CompilerServices.DefaultInterpolatedStringHandler._pos"/>.</summary>
        </member>
        <member name="M:System.Runtime.CompilerServices.DefaultInterpolatedStringHandler.Grow">
            <summary>Grows the size of <see cref="F:System.Runtime.CompilerServices.DefaultInterpolatedStringHandler._chars"/>.</summary>
        </member>
        <member name="M:System.Runtime.CompilerServices.DefaultInterpolatedStringHandler.GrowCore(System.UInt32)">
            <summary>Grow the size of <see cref="F:System.Runtime.CompilerServices.DefaultInterpolatedStringHandler._chars"/> to at least the specified <paramref name="requiredMinCapacity"/>.</summary>
        </member>
        <member name="T:System.Runtime.CompilerServices.CallerArgumentExpressionAttribute">
            <summary>
            An attribute that allows parameters to receive the expression of other parameters.
            </summary>
        </member>
        <member name="M:System.Runtime.CompilerServices.CallerArgumentExpressionAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Runtime.CompilerServices.CallerArgumentExpressionAttribute"/> class.
            </summary>
            <param name="parameterName">The condition parameter value.</param>
        </member>
        <member name="P:System.Runtime.CompilerServices.CallerArgumentExpressionAttribute.ParameterName">
            <summary>
            Gets the parameter name the expression is retrieved from.
            </summary>
        </member>
        <member name="M:System.Runtime.CompilerServices.CollectionBuilderAttribute.#ctor(System.Type,System.String)">
            <summary>
            Initialize the attribute to refer to the <paramref name="methodName"/> method on the <paramref name="builderType"/> type.
            </summary>
            <param name="builderType">The type of the builder to use to construct the collection.</param>
            <param name="methodName">The name of the method on the builder to use to construct the collection.</param>
            <remarks>
            <paramref name="methodName"/> must refer to a static method that accepts a single parameter of
            type <see cref="T:System.ReadOnlySpan`1"/> and returns an instance of the collection being built containing
            a copy of the data from that span.  In future releases of .NET, additional patterns may be supported.
            </remarks>
        </member>
        <member name="P:System.Runtime.CompilerServices.CollectionBuilderAttribute.BuilderType">
            <summary>
            Gets the type of the builder to use to construct the collection.
            </summary>
        </member>
        <member name="P:System.Runtime.CompilerServices.CollectionBuilderAttribute.MethodName">
            <summary>
            Gets the name of the method on the builder to use to construct the collection.
            </summary>
            <remarks>
            This should match the metadata name of the target method.
            For example, this might be ".ctor" if targeting the type's constructor.
            </remarks>
        </member>
        <member name="T:System.Runtime.CompilerServices.CompilerFeatureRequiredAttribute">
            <summary>
            Indicates that compiler support for a particular feature is required for the location where this attribute is applied.
            </summary>
        </member>
        <member name="M:System.Runtime.CompilerServices.CompilerFeatureRequiredAttribute.#ctor(System.String)">
            <summary>
            Creates a new instance of the <see cref="T:System.Runtime.CompilerServices.CompilerFeatureRequiredAttribute"/> type.
            </summary>
            <param name="featureName">The name of the feature to indicate.</param>
        </member>
        <member name="P:System.Runtime.CompilerServices.CompilerFeatureRequiredAttribute.FeatureName">
            <summary>
            The name of the compiler feature.
            </summary>
        </member>
        <member name="P:System.Runtime.CompilerServices.CompilerFeatureRequiredAttribute.IsOptional">
            <summary>
            If true, the compiler can choose to allow access to the location where this attribute is applied if it does not understand <see cref="P:System.Runtime.CompilerServices.CompilerFeatureRequiredAttribute.FeatureName"/>.
            </summary>
        </member>
        <member name="F:System.Runtime.CompilerServices.CompilerFeatureRequiredAttribute.RefStructs">
            <summary>
            The <see cref="P:System.Runtime.CompilerServices.CompilerFeatureRequiredAttribute.FeatureName"/> used for the ref structs C# feature.
            </summary>
        </member>
        <member name="F:System.Runtime.CompilerServices.CompilerFeatureRequiredAttribute.RequiredMembers">
            <summary>
            The <see cref="P:System.Runtime.CompilerServices.CompilerFeatureRequiredAttribute.FeatureName"/> used for the required members C# feature.
            </summary>
        </member>
        <member name="T:System.Runtime.CompilerServices.InterpolatedStringHandlerArgumentAttribute">
            <summary>
            Indicates which arguments to a method involving an interpolated string handler should be passed to that handler.
            </summary>
        </member>
        <member name="M:System.Runtime.CompilerServices.InterpolatedStringHandlerArgumentAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Runtime.CompilerServices.InterpolatedStringHandlerArgumentAttribute"/> class.
            </summary>
            <param name="argument">The name of the argument that should be passed to the handler.</param>
            <remarks><see langword="null"/> may be used as the name of the receiver in an instance method.</remarks>
        </member>
        <member name="M:System.Runtime.CompilerServices.InterpolatedStringHandlerArgumentAttribute.#ctor(System.String[])">
            <summary>
            Initializes a new instance of the <see cref="T:System.Runtime.CompilerServices.InterpolatedStringHandlerArgumentAttribute"/> class.
            </summary>
            <param name="arguments">The names of the arguments that should be passed to the handler.</param>
            <remarks><see langword="null"/> may be used as the name of the receiver in an instance method.</remarks>
        </member>
        <member name="P:System.Runtime.CompilerServices.InterpolatedStringHandlerArgumentAttribute.Arguments">
            <summary>
            Gets the names of the arguments that should be passed to the handler.
            </summary>
            <remarks><see langword="null"/> may be used as the name of the receiver in an instance method.</remarks>
        </member>
        <member name="T:System.Runtime.CompilerServices.InterpolatedStringHandlerAttribute">
            <summary>
            Indicates the attributed type is to be used as an interpolated string handler.
            </summary>
        </member>
        <member name="T:System.Runtime.CompilerServices.IsExternalInit">
            <summary>
            Reserved to be used by the compiler for tracking metadata.
            This class should not be used by developers in source code.
            </summary>
        </member>
        <member name="T:System.Runtime.CompilerServices.ModuleInitializerAttribute">
             <summary>
             Used to indicate to the compiler that a method should be called
             in its containing module's initializer.
             </summary>
             <remarks>
             When one or more valid methods
             with this attribute are found in a compilation, the compiler will
             emit a module initializer which calls each of the attributed methods.
            
             Certain requirements are imposed on any method targeted with this attribute:
             - The method must be `static`.
             - The method must be an ordinary member method, as opposed to a property accessor, constructor, local function, etc.
             - The method must be parameterless.
             - The method must return `void`.
             - The method must not be generic or be contained in a generic type.
             - The method's effective accessibility must be `internal` or `public`.
            
             The specification for module initializers in the .NET runtime can be found here:
             https://github.com/dotnet/runtime/blob/main/docs/design/specs/Ecma-335-Augments.md#module-initializer
             </remarks>
        </member>
        <member name="T:System.Runtime.CompilerServices.RequiredMemberAttribute">
            <summary>
            Specifies that a type has required members or that a member is required.
            </summary>
        </member>
        <member name="T:System.Runtime.CompilerServices.RequiresLocationAttribute">
            <summary>
            Reserved for use by a compiler for tracking metadata.
            This attribute should not be used by developers in source code.
            </summary>
        </member>
        <member name="T:System.Runtime.CompilerServices.SkipLocalsInitAttribute">
            <summary>
            Used to indicate to the compiler that the <c>.locals init</c> flag should not be set in method headers.
            </summary>
        </member>
        <member name="T:System.Runtime.CompilerServices.DisableRuntimeMarshallingAttribute">
             <summary>
             Disables the built-in runtime managed/unmanaged marshalling subsystem for
             P/Invokes, Delegate types, and unmanaged function pointer invocations.
             </summary>
             <remarks>
             The built-in marshalling subsystem has some behaviors that cannot be changed due to
             backward-compatibility requirements. This attribute allows disabling the built-in
             subsystem and instead uses the following rules for P/Invokes, Delegates,
             and unmanaged function pointer invocations:
            
             - All value types that do not contain reference type fields recursively (<c>unmanaged</c> in C#) are blittable
             - Value types that recursively have any fields that have <c>[StructLayout(LayoutKind.Auto)]</c> are disallowed from interop.
             - All reference types are disallowed from usage in interop scenarios.
             - SetLastError support in P/Invokes is disabled.
             - varargs support is disabled.
             - LCIDConversionAttribute support is disabled.
             </remarks>
        </member>
        <member name="T:System.Runtime.CompilerServices.UnsafeAccessorAttribute">
             <summary>
             Provides access to an inaccessible member of a specific type.
             </summary>
             <remarks>
             This attribute may be applied to an <code>extern static</code> method.
             The implementation of the <code>extern static</code> method annotated with
             this attribute will be provided by the runtime based on the information in
             the attribute and the signature of the method that the attribute is applied to.
             The runtime will try to find the matching method or field and forward the call
             to it. If the matching method or field is not found, the body of the <code>extern</code>
             method will throw <see cref="T:System.MissingFieldException" /> or <see cref="T:System.MissingMethodException" />.
             Only the specific type defined will be examined for inaccessible members. The type hierarchy
             is not walked looking for a match.
            
             For <see cref="F:System.Runtime.CompilerServices.UnsafeAccessorKind.Method"/>,
             <see cref="F:System.Runtime.CompilerServices.UnsafeAccessorKind.StaticMethod"/>,
             <see cref="F:System.Runtime.CompilerServices.UnsafeAccessorKind.Field"/>,
             and <see cref="F:System.Runtime.CompilerServices.UnsafeAccessorKind.StaticField"/>, the type of
             the first argument of the annotated <code>extern</code> method identifies the owning type.
             The value of the first argument is treated as <code>this</code> pointer for instance fields and methods.
             The first argument must be passed as <code>ref</code> for instance fields and methods on structs.
             The value of the first argument is not used by the implementation for <code>static</code> fields and methods.
            
             Return type is considered for the signature match. modreqs and modopts are initially not considered for
             the signature match. However, if an ambiguity exists ignoring modreqs and modopts, a precise match
             is attempted. If an ambiguity still exists <see cref="T:System.Reflection.AmbiguousMatchException" /> is thrown.
            
             By default, the attributed method's name dictates the name of the method/field. This can cause confusion
             in some cases since language abstractions, like C# local functions, generate mangled IL names. The
             solution to this is to use the <code>nameof</code> mechanism and define the <see cref="P:System.Runtime.CompilerServices.UnsafeAccessorAttribute.Name"/> property.
            
             <code>
             public void Method(Class c)
             {
                 PrivateMethod(c);
            
                 [UnsafeAccessor(UnsafeAccessorKind.Method, Name = nameof(PrivateMethod))]
                 extern static void PrivateMethod(Class c);
             }
             </code>
             </remarks>
        </member>
        <member name="M:System.Runtime.CompilerServices.UnsafeAccessorAttribute.#ctor(System.Runtime.CompilerServices.UnsafeAccessorKind)">
            <summary>
            Instantiates an <see cref="T:System.Runtime.CompilerServices.UnsafeAccessorAttribute"/>
            providing access to a member of kind <see cref="T:System.Runtime.CompilerServices.UnsafeAccessorKind"/>.
            </summary>
            <param name="kind">The kind of the target to which access is provided.</param>
        </member>
        <member name="P:System.Runtime.CompilerServices.UnsafeAccessorAttribute.Kind">
            <summary>
            Gets the kind of member to which access is provided.
            </summary>
        </member>
        <member name="P:System.Runtime.CompilerServices.UnsafeAccessorAttribute.Name">
            <summary>
            Gets or sets the name of the member to which access is provided.
            </summary>
            <remarks>
            The name defaults to the annotated method name if not specified.
            The name must be unset/<code>null</code> for <see cref="F:System.Runtime.CompilerServices.UnsafeAccessorKind.Constructor"/>.
            </remarks>
        </member>
        <member name="T:System.Runtime.CompilerServices.UnsafeAccessorKind">
            <summary>
            Specifies the kind of target to which an <see cref="T:System.Runtime.CompilerServices.UnsafeAccessorAttribute" /> is providing access.
            </summary>
        </member>
        <member name="F:System.Runtime.CompilerServices.UnsafeAccessorKind.Constructor">
            <summary>
            Provide access to a constructor.
            </summary>
        </member>
        <member name="F:System.Runtime.CompilerServices.UnsafeAccessorKind.Method">
            <summary>
            Provide access to a method.
            </summary>
        </member>
        <member name="F:System.Runtime.CompilerServices.UnsafeAccessorKind.StaticMethod">
            <summary>
            Provide access to a static method.
            </summary>
        </member>
        <member name="F:System.Runtime.CompilerServices.UnsafeAccessorKind.Field">
            <summary>
            Provide access to a field.
            </summary>
        </member>
        <member name="F:System.Runtime.CompilerServices.UnsafeAccessorKind.StaticField">
            <summary>
            Provide access to a static field.
            </summary>
        </member>
        <member name="M:System.Runtime.Versioning.RequiresPreviewFeaturesAttribute.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:System.Runtime.Versioning.RequiresPreviewFeaturesAttribute"/> class.
            </summary>
        </member>
        <member name="M:System.Runtime.Versioning.RequiresPreviewFeaturesAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Runtime.Versioning.RequiresPreviewFeaturesAttribute"/> class with the specified message.
            </summary>
            <param name="message">An optional message associated with this attribute instance.</param>
        </member>
        <member name="P:System.Runtime.Versioning.RequiresPreviewFeaturesAttribute.Message">
            <summary>
            Returns the optional message associated with this attribute instance.
            </summary>
        </member>
        <member name="P:System.Runtime.Versioning.RequiresPreviewFeaturesAttribute.Url">
            <summary>
            Returns the optional URL associated with this attribute instance.
            </summary>
        </member>
        <member name="T:System.Runtime.Versioning.ObsoletedOSPlatformAttribute">
            <summary>
            Marks APIs that were obsoleted in a given operating system version.
            </summary>
            <remarks>
            Primarily used by OS bindings to indicate APIs that should not be used anymore.
            </remarks>
        </member>
        <member name="T:System.Runtime.Versioning.SupportedOSPlatformAttribute">
             <summary>
             Records the operating system (and minimum version) that supports an API. Multiple attributes can be
             applied to indicate support on multiple operating systems.
             </summary>
             <remarks>
             Callers can apply a <see cref="T:System.Runtime.Versioning.SupportedOSPlatformAttribute" />
             or use guards to prevent calls to APIs on unsupported operating systems.
            
             A given platform should only be specified once.
             </remarks>
        </member>
        <member name="T:System.Runtime.Versioning.SupportedOSPlatformGuardAttribute">
             <summary>
             Annotates a custom guard field, property or method with a supported platform name and optional version.
             Multiple attributes can be applied to indicate guard for multiple supported platforms.
             </summary>
             <remarks>
             Callers can apply a <see cref="T:System.Runtime.Versioning.SupportedOSPlatformGuardAttribute" /> to a field, property or method
             and use that field, property or method in a conditional or assert statements in order to safely call platform specific APIs.
            
             The type of the field or property should be boolean, the method return type should be boolean in order to be used as platform guard.
             </remarks>
        </member>
        <member name="T:System.Runtime.Versioning.TargetPlatformAttribute">
            <summary>
            Records the platform that the project targeted.
            </summary>
        </member>
        <member name="T:System.Runtime.Versioning.UnsupportedOSPlatformAttribute">
            <summary>
            Marks APIs that were removed in a given operating system version.
            </summary>
            <remarks>
            Primarily used by OS bindings to indicate APIs that are only available in
            earlier versions.
            </remarks>
        </member>
        <member name="T:System.Runtime.Versioning.UnsupportedOSPlatformGuardAttribute">
             <summary>
             Annotates the custom guard field, property or method with an unsupported platform name and optional version.
             Multiple attributes can be applied to indicate guard for multiple unsupported platforms.
             </summary>
             <remarks>
             Callers can apply a <see cref="T:System.Runtime.Versioning.UnsupportedOSPlatformGuardAttribute" /> to a field, property or method
             and use that  field, property or method in a conditional or assert statements as a guard to safely call APIs unsupported on those platforms.
            
             The type of the field or property should be boolean, the method return type should be boolean in order to be used as platform guard.
             </remarks>
        </member>
        <member name="T:System.Runtime.CompilerServices2.InlineArrayAttribute">
            <summary>
            Indicates that the instance's storage is sequentially replicated "length" times.
            </summary>
            <remarks>
            <para>
            This attribute can be used to annotate a <see langword="struct"/> type with a single field.
            The runtime will replicate that field in the actual type layout as many times as is specified.
            </para>
            <para>
            Here's an example of how an inline array type with 8 <see cref="T:System.Single"/> values can be declared:
            <code lang="csharp">
            [InlineArray(8)]
            struct Float8InlineArray
            {
                private float _value;
            }
            </code>
            </para>
            </remarks>
        </member>
        <member name="M:System.Runtime.CompilerServices2.InlineArrayAttribute.#ctor(System.Int32)">
            <summary>Creates a new <see cref="T:System.Runtime.CompilerServices2.InlineArrayAttribute"/> instance with the specified length.</summary>
            <param name="length">The number of sequential fields to replicate in the inline array type.</param>
        </member>
        <member name="P:System.Runtime.CompilerServices2.InlineArrayAttribute.Length">
            <summary>Gets the number of sequential fields to replicate in the inline array type.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.ConstantExpectedAttribute">
            <summary>
            Indicates that the specified method parameter expects a constant.
            </summary>
            <remarks>
            This can be used to inform tooling that a constant should be used as an argument for the annotated parameter.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.ConstantExpectedAttribute.Min">
            <summary>
            Indicates the minimum bound of the expected constant, inclusive.
            </summary>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.ConstantExpectedAttribute.Max">
            <summary>
            Indicates the maximum bound of the expected constant, inclusive.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.ExperimentalAttribute">
            <summary>
            Indicates that an API is experimental and it may change in the future.
            </summary>
            <remarks>
            This attribute allows call sites to be flagged with a diagnostic that indicates that an experimental
            feature is used. Authors can use this attribute to ship preview features in their assemblies.
            </remarks>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.ExperimentalAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Diagnostics.CodeAnalysis.ExperimentalAttribute"/> class,
            specifying the ID that the compiler will use when reporting a use of the API the attribute applies to.
            </summary>
            <param name="diagnosticId">The ID that the compiler will use when reporting a use of the API the attribute applies to.</param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.ExperimentalAttribute.DiagnosticId">
            <summary>
            Gets the ID that the compiler will use when reporting a use of the API the attribute applies to.
            </summary>
            <value>The unique diagnostic ID.</value>
            <remarks>
            The diagnostic ID is shown in build output for warnings and errors.
            <para>This property represents the unique ID that can be used to suppress the warnings or errors, if needed.</para>
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.ExperimentalAttribute.UrlFormat">
            <summary>
            Gets or sets the URL for corresponding documentation.
            The API accepts a format string instead of an actual URL, creating a generic URL that includes the diagnostic ID.
            </summary>
            <value>The format string that represents a URL to corresponding documentation.</value>
            <remarks>An example format string is <c>https://contoso.com/obsoletion-warnings/{0}</c>.</remarks>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute">
            <summary>
            Specifies that the method or property will ensure that the listed field and property members have not-null values.
            </summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.#ctor(System.String)">
            <summary>
            Initializes the attribute with a field or property member.
            </summary>
            <param name="member">The field or property member that is promised to be not-null.</param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.#ctor(System.String[])">
            <summary>
            Initializes the attribute with the list of field and property members.
            </summary>
            <param name="members">The list of field and property members that are promised to be not-null.</param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.Members">
            <summary>
            Gets field or property member names.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute">
            <summary>
            Specifies that the method or property will ensure that the listed field and property
            members have not-null values when returning with the specified return value condition.
            </summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.#ctor(System.Boolean,System.String)">
            <summary>
            Initializes the attribute with the specified return value condition and a field or property member.
            </summary>
            <param name="returnValue">The return value condition. If the method returns this value, the associated parameter will not be null.</param>
            <param name="member">The field or property member that is promised to be not-null.</param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.#ctor(System.Boolean,System.String[])">
            <summary>
            Initializes the attribute with the specified return value condition and list of field and property members.
            </summary>
            <param name="returnValue">The return value condition. If the method returns this value, the associated parameter will not be null.</param>
            <param name="members">The list of field and property members that are promised to be not-null.</param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.ReturnValue">
            <summary>
            Gets the return value condition.
            </summary>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.Members">
            <summary>
            Gets field or property member names.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.SetsRequiredMembersAttribute">
            <summary>
            Specifies that this constructor sets all required members for the current type,
            and callers do not need to set any required members themselves.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute">
            <summary>
            Specifies the syntax used in a string.
            </summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.#ctor(System.String)">
            <summary>
            Initializes the <see cref="T:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute"/> with the identifier of the syntax used.
            </summary>
            <param name="syntax">The syntax identifier.</param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.#ctor(System.String,System.Object[])">
            <summary>Initializes the <see cref="T:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute"/> with the identifier of the syntax used.</summary>
            <param name="syntax">The syntax identifier.</param>
            <param name="arguments">Optional arguments associated with the specific syntax employed.</param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.Syntax">
            <summary>Gets the identifier of the syntax used.</summary>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.Arguments">
            <summary>Optional arguments associated with the specific syntax employed.</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.CompositeFormat">
            <summary>The syntax identifier for strings containing composite formats for string formatting.</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.DateOnlyFormat">
            <summary>The syntax identifier for strings containing date format specifiers.</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.DateTimeFormat">
            <summary>The syntax identifier for strings containing date and time format specifiers.</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.EnumFormat">
            <summary>The syntax identifier for strings containing <see cref="T:System.Enum"/> format specifiers.</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.GuidFormat">
            <summary>The syntax identifier for strings containing <see cref="T:System.Guid"/> format specifiers.</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.Json">
            <summary>The syntax identifier for strings containing JavaScript Object Notation (JSON).</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.NumericFormat">
            <summary>The syntax identifier for strings containing numeric format specifiers.</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.Regex">
            <summary>The syntax identifier for strings containing regular expressions.</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.TimeOnlyFormat">
            <summary>The syntax identifier for strings containing time format specifiers.</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.TimeSpanFormat">
            <summary>The syntax identifier for strings containing <see cref="T:System.TimeSpan"/> format specifiers.</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.Uri">
            <summary>The syntax identifier for strings containing URIs.</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.Xml">
            <summary>The syntax identifier for strings containing XML.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.UnscopedRefAttribute">
            <summary>
            Used to indicate a byref escapes and is not scoped.
            </summary>
            <remarks>
            <para>
            There are several cases where the C# compiler treats a <see langword="ref"/> as implicitly
            <see langword="scoped"/> - where the compiler does not allow the <see langword="ref"/> to escape the method.
            </para>
            <para>
            For example:
            <list type="number">
                <item><see langword="this"/> for <see langword="struct"/> instance methods.</item>
                <item><see langword="ref"/> parameters that refer to <see langword="ref"/> <see langword="struct"/> types.</item>
                <item><see langword="out"/> parameters.</item>
            </list>
            </para>
            <para>
            This attribute is used in those instances where the <see langword="ref"/> should be allowed to escape.
            </para>
            <para>
            Applying this attribute, in any form, has impact on consumers of the applicable API. It is necessary for
            API authors to understand the lifetime implications of applying this attribute and how it may impact their users.
            </para>
            </remarks>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMembersAttribute">
             <summary>
             Indicates that certain members on a specified <see cref="T:System.Type"/> are accessed dynamically,
             for example through <see cref="N:System.Reflection"/>.
             </summary>
             <remarks>
             This allows tools to understand which members are being accessed during the execution
             of a program.
            
             This attribute is valid on members whose type is <see cref="T:System.Type"/> or <see cref="T:System.String"/>.
            
             When this attribute is applied to a location of type <see cref="T:System.String"/>, the assumption is
             that the string represents a fully qualified type name.
            
             When this attribute is applied to a class, interface, or struct, the members specified
             can be accessed dynamically on <see cref="T:System.Type"/> instances returned from calling
             <see cref="M:System.Object.GetType"/> on instances of that class, interface, or struct.
            
             If the attribute is applied to a method it's treated as a special case and it implies
             the attribute should be applied to the "this" parameter of the method. As such the attribute
             should only be used on instance methods of types assignable to System.Type (or string, but no methods
             will use it there).
             </remarks>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMembersAttribute.#ctor(System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMembersAttribute"/> class
            with the specified member types.
            </summary>
            <param name="memberTypes">The types of members dynamically accessed.</param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMembersAttribute.MemberTypes">
            <summary>
            Gets the <see cref="T:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes"/> which specifies the type
            of members dynamically accessed.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes">
             <summary>
             Specifies the types of members that are dynamically accessed.
            
             This enumeration has a <see cref="T:System.FlagsAttribute"/> attribute that allows a
             bitwise combination of its member values.
             </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.None">
            <summary>
            Specifies no members.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.PublicParameterlessConstructor">
            <summary>
            Specifies the default, parameterless public constructor.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.PublicConstructors">
            <summary>
            Specifies all public constructors.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.NonPublicConstructors">
            <summary>
            Specifies all non-public constructors.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.PublicMethods">
            <summary>
            Specifies all public methods.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.NonPublicMethods">
            <summary>
            Specifies all non-public methods.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.PublicFields">
            <summary>
            Specifies all public fields.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.NonPublicFields">
            <summary>
            Specifies all non-public fields.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.PublicNestedTypes">
            <summary>
            Specifies all public nested types.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.NonPublicNestedTypes">
            <summary>
            Specifies all non-public nested types.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.PublicProperties">
            <summary>
            Specifies all public properties.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.NonPublicProperties">
            <summary>
            Specifies all non-public properties.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.PublicEvents">
            <summary>
            Specifies all public events.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.NonPublicEvents">
            <summary>
            Specifies all non-public events.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.Interfaces">
            <summary>
            Specifies all interfaces implemented by the type.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.All">
            <summary>
            Specifies all members.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute">
            <summary>
            States a dependency that one member has on another.
            </summary>
            <remarks>
            This can be used to inform tooling of a dependency that is otherwise not evident purely from
            metadata and IL, for example a member relied on via reflection.
            </remarks>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute"/> class
            with the specified signature of a member on the same type as the consumer.
            </summary>
            <param name="memberSignature">The signature of the member depended on.</param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.#ctor(System.String,System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute"/> class
            with the specified signature of a member on a <see cref="T:System.Type"/>.
            </summary>
            <param name="memberSignature">The signature of the member depended on.</param>
            <param name="type">The <see cref="T:System.Type"/> containing <paramref name="memberSignature"/>.</param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.#ctor(System.String,System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute"/> class
            with the specified signature of a member on a type in an assembly.
            </summary>
            <param name="memberSignature">The signature of the member depended on.</param>
            <param name="typeName">The full name of the type containing the specified member.</param>
            <param name="assemblyName">The assembly name of the type containing the specified member.</param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.#ctor(System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes,System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute"/> class
            with the specified types of members on a <see cref="T:System.Type"/>.
            </summary>
            <param name="memberTypes">The types of members depended on.</param>
            <param name="type">The <see cref="T:System.Type"/> containing the specified members.</param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.#ctor(System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes,System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute"/> class
            with the specified types of members on a type in an assembly.
            </summary>
            <param name="memberTypes">The types of members depended on.</param>
            <param name="typeName">The full name of the type containing the specified members.</param>
            <param name="assemblyName">The assembly name of the type containing the specified members.</param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.MemberSignature">
            <summary>
            Gets the signature of the member depended on.
            </summary>
            <remarks>
            Either <see cref="P:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.MemberSignature"/> must be a valid string or <see cref="P:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.MemberTypes"/>
            must not equal <see cref="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.None"/>, but not both.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.MemberTypes">
            <summary>
            Gets the <see cref="T:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes"/> which specifies the type
            of members depended on.
            </summary>
            <remarks>
            Either <see cref="P:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.MemberSignature"/> must be a valid string or <see cref="P:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.MemberTypes"/>
            must not equal <see cref="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.None"/>, but not both.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.Type">
            <summary>
            Gets the <see cref="T:System.Type"/> containing the specified member.
            </summary>
            <remarks>
            If neither <see cref="P:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.Type"/> nor <see cref="P:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.TypeName"/> are specified,
            the type of the consumer is assumed.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.TypeName">
            <summary>
            Gets the full name of the type containing the specified member.
            </summary>
            <remarks>
            If neither <see cref="P:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.Type"/> nor <see cref="P:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.TypeName"/> are specified,
            the type of the consumer is assumed.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.AssemblyName">
            <summary>
            Gets the assembly name of the specified type.
            </summary>
            <remarks>
            <see cref="P:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.AssemblyName"/> is only valid when <see cref="P:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.TypeName"/> is specified.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.DynamicDependencyAttribute.Condition">
            <summary>
            Gets or sets the condition in which the dependency is applicable, e.g. "DEBUG".
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.RequiresAssemblyFilesAttribute">
            <summary>
            Indicates that the specified member requires assembly files to be on disk.
            </summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.RequiresAssemblyFilesAttribute.#ctor">
            <summary>
            Initializes a new instance of the <see cref="!:global::System.RequiresAssemblyFilesAttribute"/> class.
            </summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.RequiresAssemblyFilesAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="!:global::System.RequiresAssemblyFilesAttribute"/> class.
            </summary>
            <param name="message">
            A message that contains information about the need for assembly files to be on disk.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.RequiresAssemblyFilesAttribute.Message">
            <summary>
            Gets an optional message that contains information about the need for
            assembly files to be on disk.
            </summary>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.RequiresAssemblyFilesAttribute.Url">
            <summary>
            Gets or sets an optional URL that contains more information about the member,
            why it requires assembly files to be on disk, and what options a consumer has
            to deal with it.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.RequiresDynamicCodeAttribute">
            <summary>
            Indicates that the specified method requires the ability to generate new code at runtime,
            for example through <see cref="N:System.Reflection"/>.
            </summary>
            <remarks>
            This allows tools to understand which methods are unsafe to call when compiling ahead of time.
            </remarks>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.RequiresDynamicCodeAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Diagnostics.CodeAnalysis.RequiresDynamicCodeAttribute"/> class
            with the specified message.
            </summary>
            <param name="message">
            A message that contains information about the usage of dynamic code.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.RequiresDynamicCodeAttribute.Message">
            <summary>
            Gets a message that contains information about the usage of dynamic code.
            </summary>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.RequiresDynamicCodeAttribute.Url">
            <summary>
            Gets or sets an optional URL that contains more information about the method,
            why it requires dynamic code, and what options a consumer has to deal with it.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.RequiresUnreferencedCodeAttribute">
            <summary>
            Indicates that the specified method requires dynamic access to code that is not referenced
            statically, for example through <see cref="N:System.Reflection"/>.
            </summary>
            <remarks>
            This allows tools to understand which methods are unsafe to call when removing unreferenced
            code from an application.
            </remarks>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.RequiresUnreferencedCodeAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Diagnostics.CodeAnalysis.RequiresUnreferencedCodeAttribute"/> class
            with the specified message.
            </summary>
            <param name="message">
            A message that contains information about the usage of unreferenced code.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.RequiresUnreferencedCodeAttribute.Message">
            <summary>
            Gets a message that contains information about the usage of unreferenced code.
            </summary>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.RequiresUnreferencedCodeAttribute.Url">
            <summary>
            Gets or sets an optional URL that contains more information about the method,
            why it requires unreferenced code, and what options a consumer has to deal with it.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute">
            <summary>
            Suppresses reporting of a specific rule violation, allowing multiple suppressions on a
            single code artifact.
            </summary>
            <remarks>
            <see cref="T:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute"/> is different than
            <see cref="T:System.Diagnostics.CodeAnalysis.SuppressMessageAttribute"/> in that it doesn't have a
            <see cref="T:System.Diagnostics.ConditionalAttribute"/>. So it is always preserved in the compiled assembly.
            </remarks>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute"/>
            class, specifying the category of the tool and the identifier for an analysis rule.
            </summary>
            <param name="category">The category for the attribute.</param>
            <param name="checkId">The identifier of the analysis rule the attribute applies to.</param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.Category">
            <summary>
            Gets the category identifying the classification of the attribute.
            </summary>
            <remarks>
            The <see cref="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.Category"/> property describes the tool or tool analysis category
            for which a message suppression attribute applies.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.CheckId">
            <summary>
            Gets the identifier of the analysis tool rule to be suppressed.
            </summary>
            <remarks>
            Concatenated together, the <see cref="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.Category"/> and <see cref="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.CheckId"/>
            properties form a unique check identifier.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.Scope">
            <summary>
            Gets or sets the scope of the code that is relevant for the attribute.
            </summary>
            <remarks>
            The Scope property is an optional argument that specifies the metadata scope for which
            the attribute is relevant.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.Target">
            <summary>
            Gets or sets a fully qualified path that represents the target of the attribute.
            </summary>
            <remarks>
            The <see cref="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.Target"/> property is an optional argument identifying the analysis target
            of the attribute. An example value is "System.IO.Stream.ctor():System.Void".
            Because it is fully qualified, it can be long, particularly for targets such as parameters.
            The analysis tool user interface should be capable of automatically formatting the parameter.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.MessageId">
            <summary>
            Gets or sets an optional argument expanding on exclusion criteria.
            </summary>
            <remarks>
            The <see cref="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.MessageId"/> property is an optional argument that specifies additional
            exclusion where the literal metadata target is not sufficiently precise. For example,
            the <see cref="T:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute"/> cannot be applied within a method,
            and it may be desirable to suppress a violation against a statement in the method that will
            give a rule violation, but not against all statements in the method.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.Justification">
            <summary>
            Gets or sets the justification for suppressing the code analysis message.
            </summary>
        </member>
        <member name="T:System.Diagnostics.StackTraceHiddenAttribute">
            <summary>
            Types and Methods attributed with StackTraceHidden will be omitted from the stack trace text shown in StackTrace.ToString()
            and Exception.StackTrace
            </summary>
        </member>
        <member name="M:System.Diagnostics.StackTraceHiddenAttribute.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:System.Diagnostics.StackTraceHiddenAttribute"/> class.
            </summary>
        </member>
    </members>
</doc>
