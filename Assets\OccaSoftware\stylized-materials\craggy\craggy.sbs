<?xml version="1.0" encoding="UTF-8"?><package><identifier v="Unsaved Package"/><formatVersion v="1.1.0.202302"/><updaterVersion v="1.1.0.202302"/><fileUID v="{e685a27c-b894-401d-90e0-a38bfd665617}"/><versionUID v="0"/><dependencies><dependency><filename v="sbs://hbao_2.sbs"/><uid v="1297162667"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://pattern_tile_sampler.sbs"/><uid v="1449399972"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://auto_levels.sbs"/><uid v="1383786570"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://gradient_linear_1.sbs"/><uid v="1357065436"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://invert.sbs"/><uid v="1368115694"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_gaussian_noise.sbs"/><uid v="1357066167"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://height_blend.sbs"/><uid v="1449623608"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://curvature_smooth.sbs"/><uid v="1449590280"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://histogram_scan.sbs"/><uid v="1149349207"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://curvature.sbs"/><uid v="1446841574"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://blur_hq.sbs"/><uid v="1299236171"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://non_uniform_blur.sbs"/><uid v="1290776959"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://highpass.sbs"/><uid v="1446843354"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://slope_blur.sbs"/><uid v="1449402040"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_fractal_sum_base.sbs"/><uid v="1447014740"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://quantize.sbs"/><uid v="1448187982"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://flood_fill_2.sbs"/><uid v="1323881949"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency></dependencies><content><graph><identifier v="craggy"/><uid v="1448061698"/><graphtype v="material"/><attributes><author v="3dex"/><authorURL v="3dex.net"/><tags v="wall, ground, rocks"/></attributes><paraminputs><paraminput><identifier v="x_amount"/><uid v="1484880303"/><attributes><label v="X Amount"/></attributes><type v="16"/><defaultValue><constantValueInt1 v="3"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="3"/></option><option><name v="isRandomizable"/><value v="0"/></option><option><name v="max"/><value v="64"/></option><option><name v="min"/><value v="1"/></option><option><name v="step"/><value v="1"/></option></options></defaultWidget><group v="Rocks"/></paraminput><paraminput><identifier v="y_amount"/><uid v="1484889251"/><attributes><label v="Y Amount"/></attributes><type v="16"/><defaultValue><constantValueInt1 v="4"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="4"/></option><option><name v="isRandomizable"/><value v="0"/></option><option><name v="max"/><value v="64"/></option><option><name v="min"/><value v="1"/></option><option><name v="step"/><value v="1"/></option></options></defaultWidget><group v="Rocks"/></paraminput><paraminput><identifier v="scale"/><uid v="1484898202"/><attributes><label v="Scale"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="8.31000042"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="8.31"/></option><option><name v="max"/><value v="10"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Rocks"/></paraminput><paraminput><identifier v="scale_random"/><uid v="1484907150"/><attributes><label v="Scale Random"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.370000005"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.37"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Rocks"/></paraminput><paraminput><identifier v="offset"/><uid v="1484916100"/><attributes><label v="Offset"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.730000019"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.73"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Rocks"/></paraminput><paraminput><identifier v="rotation"/><uid v="1484969768"/><attributes><label v="Rotation"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="-0.125"/></defaultValue><defaultWidget><name v="angle"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="-0.125"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="valueInterpretation"/><value v="angle"/></option></options></defaultWidget><group v="Rocks"/></paraminput><paraminput><identifier v="rotation_random"/><uid v="1484978716"/><attributes><label v="Rotation Random"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.0599999987"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.06"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Rocks"/></paraminput><paraminput><identifier v="scale_1"/><uid v="1485076241"/><attributes><label v="Scale"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="7.92000008"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="7.92"/></option><option><name v="max"/><value v="10"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Terrain"/></paraminput><paraminput><identifier v="scale_random_1"/><uid v="1485085011"/><attributes><label v="Scale Random"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.449999988"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.45"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Terrain"/></paraminput><paraminput><identifier v="rotation_1"/><uid v="1485163901"/><attributes><label v="Rotation"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="-0.125"/></defaultValue><defaultWidget><name v="angle"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="-0.125"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="valueInterpretation"/><value v="angle"/></option></options></defaultWidget><group v="Terrain"/></paraminput><paraminput><identifier v="rotation_random_1"/><uid v="1485172670"/><attributes><label v="Rotation Random"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.0299999993"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.03"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Terrain"/></paraminput><paraminput><identifier v="x_amount_1"/><uid v="1485207734"/><attributes><label v="X Amount"/></attributes><type v="16"/><defaultValue><constantValueInt1 v="14"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="14"/></option><option><name v="isRandomizable"/><value v="0"/></option><option><name v="max"/><value v="64"/></option><option><name v="min"/><value v="1"/></option><option><name v="step"/><value v="1"/></option></options></defaultWidget><group v="Terrain"/></paraminput><paraminput><identifier v="y_amount_1"/><uid v="1485216502"/><attributes><label v="Y Amount"/></attributes><type v="16"/><defaultValue><constantValueInt1 v="4"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="4"/></option><option><name v="isRandomizable"/><value v="0"/></option><option><name v="max"/><value v="64"/></option><option><name v="min"/><value v="1"/></option><option><name v="step"/><value v="1"/></option></options></defaultWidget><group v="Terrain"/></paraminput><paraminput><identifier v="intensity"/><uid v="1485251496"/><attributes><label v="Warp Intensity"/><description v="The &lt;b&gt;Intensity&lt;/b&gt; parameter sets the intensity of the warp"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="70"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="70"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Terrain"/></paraminput><paraminput><identifier v="warpangle"/><uid v="1485295187"/><attributes><label v="Warp Angle"/><description v="The &lt;b&gt;Angle&lt;/b&gt; parameter sets the angle of the warp"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.377555996"/></defaultValue><defaultWidget><name v="angle"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="0.377556"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="valueInterpretation"/><value v="angle"/></option></options></defaultWidget><group v="Terrain"/></paraminput><paraminput><identifier v="height_position"/><uid v="1485303940"/><attributes><label v="Height Offset"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.5"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.5"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Terrain"/></paraminput><paraminput><identifier v="height_position_1"/><uid v="1485323985"/><attributes><label v="Height Offset"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.5"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.5"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Medium Rocks"/></paraminput><paraminput><identifier v="x_amount_2"/><uid v="1485328509"/><attributes><label v="X Amount"/></attributes><type v="16"/><defaultValue><constantValueInt1 v="7"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="7"/></option><option><name v="isRandomizable"/><value v="0"/></option><option><name v="max"/><value v="64"/></option><option><name v="min"/><value v="1"/></option><option><name v="step"/><value v="1"/></option></options></defaultWidget><group v="Medium Rocks"/></paraminput><paraminput><identifier v="y_amount_2"/><uid v="1485333238"/><attributes><label v="Y Amount"/></attributes><type v="16"/><defaultValue><constantValueInt1 v="7"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="7"/></option><option><name v="isRandomizable"/><value v="0"/></option><option><name v="max"/><value v="64"/></option><option><name v="min"/><value v="1"/></option><option><name v="step"/><value v="1"/></option></options></defaultWidget><group v="Medium Rocks"/></paraminput><paraminput><identifier v="scale_2"/><uid v="1485337969"/><attributes><label v="Scale"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="2.5"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="2.5"/></option><option><name v="max"/><value v="10"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Medium Rocks"/></paraminput><paraminput><identifier v="scale_random_2"/><uid v="1485342700"/><attributes><label v="Scale Random"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.479999989"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.48"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Medium Rocks"/></paraminput><paraminput><identifier v="mask_random"/><uid v="1485347431"/><attributes><label v="Mask Random"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.579999983"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.58"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Medium Rocks"/></paraminput><paraminput><identifier v="opacitymult"/><uid v="1485360155"/><attributes><label v="Opacity"/><description v="The &lt;b&gt;Opacity&lt;/b&gt; parameter sets the transparency of the blend"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="0"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Small Rocks"/></paraminput><paraminput><identifier v="x_amount_3"/><uid v="1485362820"/><attributes><label v="X Amount"/></attributes><type v="16"/><defaultValue><constantValueInt1 v="14"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="14"/></option><option><name v="isRandomizable"/><value v="0"/></option><option><name v="max"/><value v="64"/></option><option><name v="min"/><value v="1"/></option><option><name v="step"/><value v="1"/></option></options></defaultWidget><group v="Small Rocks"/></paraminput><paraminput><identifier v="y_amount_3"/><uid v="1485365499"/><attributes><label v="Y Amount"/></attributes><type v="16"/><defaultValue><constantValueInt1 v="14"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="14"/></option><option><name v="isRandomizable"/><value v="0"/></option><option><name v="max"/><value v="64"/></option><option><name v="min"/><value v="1"/></option><option><name v="step"/><value v="1"/></option></options></defaultWidget><group v="Small Rocks"/></paraminput><paraminput><identifier v="scale_3"/><uid v="1485368180"/><attributes><label v="Scale"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="1.83000004"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="1.83"/></option><option><name v="max"/><value v="10"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Small Rocks"/></paraminput><paraminput><identifier v="scale_random_3"/><uid v="1485370858"/><attributes><label v="Scale Random"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.479999989"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.48"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Small Rocks"/></paraminput><paraminput><identifier v="mask_random_1"/><uid v="1485373539"/><attributes><label v="Mask Random"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.670000017"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.67"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Small Rocks"/></paraminput><paraminput><identifier v="opacitymult_1"/><uid v="1485382782"/><attributes><label v="Opacity"/><description v="The &lt;b&gt;Opacity&lt;/b&gt; parameter sets the transparency of the blend"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="0"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Grass"/></paraminput><paraminput><identifier v="x_amount_4"/><uid v="1485384974"/><attributes><label v="X Amount"/></attributes><type v="16"/><defaultValue><constantValueInt1 v="64"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="64"/></option><option><name v="isRandomizable"/><value v="0"/></option><option><name v="max"/><value v="64"/></option><option><name v="min"/><value v="1"/></option><option><name v="step"/><value v="1"/></option></options></defaultWidget><group v="Grass"/></paraminput><paraminput><identifier v="y_amount_4"/><uid v="1485387158"/><attributes><label v="Y Amount"/></attributes><type v="16"/><defaultValue><constantValueInt1 v="64"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="64"/></option><option><name v="isRandomizable"/><value v="0"/></option><option><name v="max"/><value v="64"/></option><option><name v="min"/><value v="1"/></option><option><name v="step"/><value v="1"/></option></options></defaultWidget><group v="Grass"/></paraminput><paraminput><identifier v="scale_4"/><uid v="1485389343"/><attributes><label v="Scale"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="13.7299995"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="13.73"/></option><option><name v="max"/><value v="10"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Grass"/></paraminput><paraminput><identifier v="scale_random_4"/><uid v="1485391527"/><attributes><label v="Scale Random"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.689999998"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.69"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Grass"/></paraminput><paraminput><identifier v="rotation_2"/><uid v="1485393715"/><attributes><label v="Rotation"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.0388889015"/></defaultValue><defaultWidget><name v="angle"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="0.0388889"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="valueInterpretation"/><value v="angle"/></option></options></defaultWidget><group v="Grass"/></paraminput><paraminput><identifier v="rotation_random_2"/><uid v="1485395899"/><attributes><label v="Rotation Random"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.109999999"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.11"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Grass"/></paraminput><paraminput><identifier v="mask_random_2"/><uid v="1485398087"/><attributes><label v="Mask Random"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Grass"/></paraminput><paraminput><identifier v="outputcolor"/><uid v="1485415669"/><attributes><label v="Rocks"/><description v="The &lt;b&gt;Color Output&lt;/b&gt; parameter creates a uniform color output image"/></attributes><type v="2048"/><defaultValue><constantValueFloat4 v="0.349020004 0.349020004 0.349020004 1"/></defaultValue><defaultWidget><name v="color"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.34902;0.34902;0.34902;1"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="valueInterpretation"/><value v="color"/></option></options></defaultWidget><group v="Color"/></paraminput><paraminput><identifier v="outputcolor_1"/><uid v="1485415740"/><attributes><label v="Rocks Medium"/><description v="The &lt;b&gt;Color Output&lt;/b&gt; parameter creates a uniform color output image"/></attributes><type v="2048"/><defaultValue><constantValueFloat4 v="0.349020004 0.349020004 0.349020004 1"/></defaultValue><defaultWidget><name v="color"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.34902;0.34902;0.34902;1"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="valueInterpretation"/><value v="color"/></option></options></defaultWidget><group v="Color"/></paraminput><paraminput><identifier v="outputcolor_2"/><uid v="1485415813"/><attributes><label v="Rocks Small"/><description v="The &lt;b&gt;Color Output&lt;/b&gt; parameter creates a uniform color output image"/></attributes><type v="2048"/><defaultValue><constantValueFloat4 v="0.349020004 0.349020004 0.349020004 1"/></defaultValue><defaultWidget><name v="color"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.34902;0.34902;0.34902;1"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="valueInterpretation"/><value v="color"/></option></options></defaultWidget><group v="Color"/></paraminput><paraminput><identifier v="opacitymult_2"/><uid v="1485415953"/><attributes><label v="Rocks Surface Intensity"/><description v="The &lt;b&gt;Opacity&lt;/b&gt; parameter sets the transparency of the blend"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.0700000003"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="0.07"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Color"/></paraminput><paraminput><identifier v="opacitymult_3"/><uid v="1485415997"/><attributes><label v="Rocks Curvature"/><description v="The &lt;b&gt;Opacity&lt;/b&gt; parameter sets the transparency of the blend"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.0599999987"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="0.06"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Color"/></paraminput><paraminput><identifier v="outputcolor_3"/><uid v="1485416110"/><attributes><label v="Rocks Cavity"/><description v="The &lt;b&gt;Color Output&lt;/b&gt; parameter creates a uniform color output image"/></attributes><type v="2048"/><defaultValue><constantValueFloat4 v="0.345097989 0.290196002 0.211765006 1"/></defaultValue><defaultWidget><name v="color"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.345098;0.290196;0.211765;1"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="valueInterpretation"/><value v="color"/></option></options></defaultWidget><group v="Color"/></paraminput><paraminput><identifier v="opacitymult_4"/><uid v="1485416153"/><attributes><label v="Rocks Cavity Opacity"/><description v="The &lt;b&gt;Opacity&lt;/b&gt; parameter sets the transparency of the blend"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.389999986"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="0.39"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Color"/></paraminput><paraminput><identifier v="outputcolor_4"/><uid v="1485416214"/><attributes><label v="Terrain"/><description v="The &lt;b&gt;Color Output&lt;/b&gt; parameter creates a uniform color output image"/></attributes><type v="2048"/><defaultValue><constantValueFloat4 v="0.345097989 0.290196002 0.211765006 1"/></defaultValue><defaultWidget><name v="color"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.345098;0.290196;0.211765;1"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="valueInterpretation"/><value v="color"/></option></options></defaultWidget><group v="Color"/></paraminput><paraminput><identifier v="opacitymult_5"/><uid v="1485416257"/><attributes><label v="Terrain Curvature"/><description v="The &lt;b&gt;Opacity&lt;/b&gt; parameter sets the transparency of the blend"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.0399999991"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="0.04"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Color"/></paraminput><paraminput><identifier v="scale_5"/><uid v="1485416411"/><attributes><label v="Sand Dirt Scale"/></attributes><type v="16"/><defaultValue><constantValueInt1 v="10"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="10"/></option><option><name v="isRandomizable"/><value v="0"/></option><option><name v="max"/><value v="64"/></option><option><name v="min"/><value v="1"/></option><option><name v="step"/><value v="1"/></option></options></defaultWidget><group v="Color"/></paraminput><paraminput><identifier v="opacitymult_6"/><uid v="1485416443"/><attributes><label v="Sand Dirt Opacity"/><description v="The &lt;b&gt;Opacity&lt;/b&gt; parameter sets the transparency of the blend"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.0299999993"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="0.03"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Color"/></paraminput><paraminput><identifier v="outputcolor_5"/><uid v="1485416564"/><attributes><label v="Grass"/><description v="The &lt;b&gt;Color Output&lt;/b&gt; parameter creates a uniform color output image"/></attributes><type v="2048"/><defaultValue><constantValueFloat4 v="0.231372997 0.262744993 0.129411995 1"/></defaultValue><defaultWidget><name v="color"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.231373;0.262745;0.129412;1"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="valueInterpretation"/><value v="color"/></option></options></defaultWidget><group v="Color"/></paraminput><paraminput><identifier v="opacitymult_7"/><uid v="1485416586"/><attributes><label v="Grass Tip"/><description v="The &lt;b&gt;Opacity&lt;/b&gt; parameter sets the transparency of the blend"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.150000006"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="0.15"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Color"/></paraminput><paraminput><identifier v="outputcolor_6"/><uid v="1485417228"/><attributes><label v="Rocks"/><description v="The &lt;b&gt;Color Output&lt;/b&gt; parameter creates a uniform color output image"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.756862998"/></defaultValue><defaultWidget><name v="color"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.756863"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="valueInterpretation"/><value v="color"/></option></options></defaultWidget><group v="Roughness"/></paraminput><paraminput><identifier v="outputcolor_7"/><uid v="1485417377"/><attributes><label v="Terrain"/><description v="The &lt;b&gt;Color Output&lt;/b&gt; parameter creates a uniform color output image"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.839215994"/></defaultValue><defaultWidget><name v="color"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.839216"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="valueInterpretation"/><value v="color"/></option></options></defaultWidget><group v="Roughness"/></paraminput><paraminput><identifier v="outputcolor_8"/><uid v="1485417447"/><attributes><label v="Grass"/><description v="The &lt;b&gt;Color Output&lt;/b&gt; parameter creates a uniform color output image"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.74901998"/></defaultValue><defaultWidget><name v="color"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.74902"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="valueInterpretation"/><value v="color"/></option></options></defaultWidget><group v="Roughness"/></paraminput><paraminput><identifier v="intensity_1"/><uid v="1485417474"/><attributes><label v="Normal Intensity"/><description v="The &lt;b&gt;Intensity&lt;/b&gt; parameter modifies the intensity of height map "/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="10"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="10"/></option><option><name v="max"/><value v="50"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget></paraminput><paraminput><identifier v="height_depth"/><uid v="1485417486"/><attributes><label v="Height Depth"/><description v="Defines the height map scale compared to the image size.&#10;A value of 1 means the height map depth is the same as its the largest border."/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.180000007"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.18"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="AO"/></paraminput><paraminput><identifier v="radius"/><uid v="1485418387"/><attributes><label v="Radius"/><description v="Adjusts the Ambient Occlusion radius. Corresponds to the &quot;Max Occluder Distance&quot; parameter in the bakers.&#10;A value of 1 equals to the largest border of the image."/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.270000011"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.27"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="AO"/></paraminput></paraminputs><graphOutputs><graphoutput><identifier v="basecolor"/><uid v="1448075773"/><attributes><label v="Base Color"/></attributes><usages><usage><components v="RGBA"/><name v="baseColor"/></usage></usages><group v="Material"/></graphoutput><graphoutput><identifier v="normal"/><uid v="1448075775"/><attributes><label v="Normal"/></attributes><usages><usage><components v="RGBA"/><name v="normal"/></usage></usages><group v="Material"/></graphoutput><graphoutput><identifier v="roughness"/><uid v="1448075777"/><attributes><label v="Roughness"/></attributes><usages><usage><components v="RGBA"/><name v="roughness"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput><graphoutput><identifier v="metallic"/><uid v="1448075779"/><attributes><label v="Metallic"/></attributes><usages><usage><components v="RGBA"/><name v="metallic"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput><graphoutput><identifier v="height"/><uid v="1448075781"/><attributes><label v="Height"/></attributes><usages><usage><components v="RGBA"/><name v="height"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput><graphoutput><identifier v="AO"/><uid v="1448075783"/><usages><usage><components v="RGBA"/><name v="ambientOcclusion"/></usage></usages><channels v="2"/></graphoutput></graphOutputs><compNodes><compNode><uid v="1448075766"/><connections><connection><identifier v="input"/><connRef v="1448075767"/><connRefOutput v="1410296043"/></connection></connections><GUILayout><gpos v="19115.084 289.083344 0"/></GUILayout><compOutputs><compOutput><uid v="1410296137"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///hbao?dependency=1297162667"/><parameters><parameter><name v="radius"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1485418388"/><paramNodes><paramNode><uid v="1485418388"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="radius"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter><parameter><name v="height_depth"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1485417487"/><paramNodes><paramNode><uid v="1485417487"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="height_depth"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter><parameter><name v="samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="16"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1410296137"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448075767"/><connections><connection><identifier v="destination"/><connRef v="1448198539"/><connRefOutput v="1448198540"/></connection></connections><GUILayout><gpos v="18933.375 139.041656 0"/></GUILayout><compOutputs><compOutput><uid v="1410296043"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448075769"/><GUILayout><gpos v="19115.084 449.083344 0"/></GUILayout><compOutputs><compOutput><uid v="1360419830"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="uniform"/><parameters><parameter><name v="colorswitch"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448075770"/><connections><connection><identifier v="input1"/><connRef v="1448075767"/><connRefOutput v="1410296043"/></connection></connections><GUILayout><gpos v="19115.084 -30.9165955 0"/></GUILayout><compOutputs><compOutput><uid v="1410297936"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="normal"/><parameters><parameter><name v="input2alpha"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="inversedy"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1485417475"/><paramNodes><paramNode><uid v="1485417475"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="intensity_1"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448075772"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1448261541"/><connRefOutput v="1448261542"/></connection></connections><GUILayout><gpos v="19275.084 -350.916565 0"/></GUILayout><compImplementation><compOutputBridge><output v="1448075773"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1448075774"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1448075770"/><connRefOutput v="1410297936"/></connection></connections><GUILayout><gpos v="19275.084 -30.9165955 0"/></GUILayout><compImplementation><compOutputBridge><output v="1448075775"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1448075776"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1448274730"/><connRefOutput v="1448274731"/></connection></connections><GUILayout><gpos v="19275.084 -190.916565 0"/></GUILayout><compImplementation><compOutputBridge><output v="1448075777"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1448075778"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1448075769"/><connRefOutput v="1360419830"/></connection></connections><GUILayout><gpos v="19275.084 449.083344 0"/></GUILayout><compImplementation><compOutputBridge><output v="1448075779"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1448075780"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1448075767"/><connRefOutput v="1410296043"/></connection></connections><GUILayout><gpos v="19275.084 129.083405 0"/></GUILayout><compImplementation><compOutputBridge><output v="1448075781"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1448075782"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1448075766"/><connRefOutput v="1410296137"/></connection></connections><GUILayout><gpos v="19275.084 289.083344 0"/></GUILayout><compImplementation><compOutputBridge><output v="1448075783"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1448082482"/><GUILayout><gpos v="-5279.50439 272 0"/></GUILayout><compOutputs><compOutput><uid v="1448082483"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1449399972"/><parameters><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="size"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="1 0.620000005"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448082483"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448082494"/><GUILayout><gpos v="-5279.50439 -16 0"/></GUILayout><compOutputs><compOutput><uid v="1448082483"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1449399972"/><parameters><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="20"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="6.44000006"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.459999979"/></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="2.53999996"/></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.629999995"/></paramValue></parameter><parameter><name v="color_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.449999988"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="21"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448082483"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448082557"/><connections><connection><identifier v="source"/><connRef v="1448082494"/><connRefOutput v="1448082483"/></connection><connection><identifier v="destination"/><connRef v="1448094092"/><connRefOutput v="1448094093"/></connection></connections><GUILayout><gpos v="-4942.23633 272 0"/></GUILayout><compOutputs><compOutput><uid v="1448082558"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.620000005"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448082644"/><connections><connection><identifier v="input1"/><connRef v="1448082557"/><connRefOutput v="1448082558"/></connection></connections><GUILayout><gpos v="-4735.50439 272 0"/></GUILayout><compOutputs><compOutput><uid v="1448082645"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="transformation"/><parameters><parameter><name v="matrix22"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.499000013 0 0 0.499000013"/></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.25 0.25"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448083508"/><connections><connection><identifier v="Input"/><connRef v="1448082644"/><connRefOutput v="1448082645"/></connection></connections><GUILayout><gpos v="-4575.50439 272 0"/></GUILayout><compOutputs><compOutput><uid v="1448083509"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1383786570"/><parameters/><outputBridgings><outputBridging><uid v="1448083509"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448083525"/><connections><connection><identifier v="input1"/><connRef v="1448082557"/><connRefOutput v="1448082558"/></connection></connections><GUILayout><gpos v="-4735.50439 144 0"/></GUILayout><compOutputs><compOutput><uid v="1448082645"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="transformation"/><parameters><parameter><name v="matrix22"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.499000013 0 0 0.499000013"/></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="-0.25 0.25"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448083526"/><connections><connection><identifier v="Input"/><connRef v="1448083525"/><connRefOutput v="1448082645"/></connection></connections><GUILayout><gpos v="-4575.50439 144 0"/></GUILayout><compOutputs><compOutput><uid v="1448083509"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1383786570"/><parameters/><outputBridgings><outputBridging><uid v="1448083509"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448083533"/><connections><connection><identifier v="input1"/><connRef v="1448082557"/><connRefOutput v="1448082558"/></connection></connections><GUILayout><gpos v="-4735.50439 16 0"/></GUILayout><compOutputs><compOutput><uid v="1448082645"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="transformation"/><parameters><parameter><name v="matrix22"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.499000013 0 0 0.499000013"/></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.25 -0.25"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448083534"/><connections><connection><identifier v="Input"/><connRef v="1448083533"/><connRefOutput v="1448082645"/></connection></connections><GUILayout><gpos v="-4575.50439 16 0"/></GUILayout><compOutputs><compOutput><uid v="1448083509"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1383786570"/><parameters/><outputBridgings><outputBridging><uid v="1448083509"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448083538"/><connections><connection><identifier v="input1"/><connRef v="1448082557"/><connRefOutput v="1448082558"/></connection></connections><GUILayout><gpos v="-4735.50439 -112 0"/></GUILayout><compOutputs><compOutput><uid v="1448082645"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="transformation"/><parameters><parameter><name v="matrix22"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.499000013 0 0 0.499000013"/></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="-0.25 -0.25"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448083539"/><connections><connection><identifier v="Input"/><connRef v="1448083538"/><connRefOutput v="1448082645"/></connection></connections><GUILayout><gpos v="-4575.50439 -112 0"/></GUILayout><compOutputs><compOutput><uid v="1448083509"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1383786570"/><parameters/><outputBridgings><outputBridging><uid v="1448083509"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448083685"/><connections><connection><identifier v="destination"/><connRef v="1448083539"/><connRefOutput v="1448083509"/></connection><connection><identifier v="source"/><connRef v="1448083708"/><connRefOutput v="1448083709"/></connection></connections><GUILayout><gpos v="-4287.50439 -112 0"/></GUILayout><compOutputs><compOutput><uid v="1448083686"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448083697"/><GUILayout><gpos v="-5311.50439 -912 0"/></GUILayout><compOutputs><compOutput><uid v="1448082483"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1449399972"/><parameters><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="6.44000006"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.459999979"/></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="2.53999996"/></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.629999995"/></paramValue></parameter><parameter><name v="color_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.449999988"/></paramValue></parameter><parameter><name v="randomseed"/><relativeTo v="2"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="25"/></paramValue></parameter><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="25"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448082483"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448083708"/><connections><connection><identifier v="source"/><connRef v="1448083833"/><connRefOutput v="1448083834"/></connection><connection><identifier v="destination"/><connRef v="1448084755"/><connRefOutput v="1448084756"/></connection></connections><GUILayout><gpos v="-4927.50439 -912 0"/></GUILayout><compOutputs><compOutput><uid v="1448083709"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.269999981"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448083764"/><GUILayout><gpos v="-5311.50439 -720 0"/></GUILayout><compOutputs><compOutput><uid v="1448083765"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///gradient_linear_1?dependency=1357065436"/><parameters><parameter><name v="rotation"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448083765"/><identifier v="Simple_Gradient"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448083833"/><connections><connection><identifier v="Source"/><connRef v="1448083697"/><connRefOutput v="1448082483"/></connection></connections><GUILayout><gpos v="-5151.50439 -912 0"/></GUILayout><compOutputs><compOutput><uid v="1448083834"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///invert_grayscale?dependency=1368115694"/><parameters/><outputBridgings><outputBridging><uid v="1448083834"/><identifier v="Invert_Grayscale"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448084755"/><connections><connection><identifier v="input1"/><connRef v="1448083764"/><connRefOutput v="1448083765"/></connection></connections><GUILayout><gpos v="-5151.50439 -720 0"/></GUILayout><compOutputs><compOutput><uid v="1448084756"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="transformation"/><parameters><parameter><name v="matrix22"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="1.46749997 0 0 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448087513"/><connections><connection><identifier v="destination"/><connRef v="1448083534"/><connRefOutput v="1448083509"/></connection><connection><identifier v="source"/><connRef v="1448087526"/><connRefOutput v="1448083709"/></connection></connections><GUILayout><gpos v="-4287.50439 16 0"/></GUILayout><compOutputs><compOutput><uid v="1448083686"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448087526"/><connections><connection><identifier v="destination"/><connRef v="1448084755"/><connRefOutput v="1448084756"/></connection><connection><identifier v="source"/><connRef v="1448087536"/><connRefOutput v="1448087537"/></connection></connections><GUILayout><gpos v="-4927.50439 -784 0"/></GUILayout><compOutputs><compOutput><uid v="1448083709"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.270000011"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448087536"/><connections><connection><identifier v="input1"/><connRef v="1448083833"/><connRefOutput v="1448083834"/></connection></connections><GUILayout><gpos v="-4990 -741.333313 -1"/><docked v="1"/><dockDistance v="112 72.8289795"/></GUILayout><compOutputs><compOutput><uid v="1448087537"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="transformation"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1448087544"/><connections><connection><identifier v="destination"/><connRef v="1448084755"/><connRefOutput v="1448084756"/></connection><connection><identifier v="source"/><connRef v="1448087545"/><connRefOutput v="1448087537"/></connection></connections><GUILayout><gpos v="-4927.50439 -656 0"/></GUILayout><compOutputs><compOutput><uid v="1448083709"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.25"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448087545"/><connections><connection><identifier v="input1"/><connRef v="1448083833"/><connRefOutput v="1448083834"/></connection></connections><GUILayout><gpos v="-4990 -613.333313 -1"/><docked v="1"/><dockDistance v="112 72.8289795"/></GUILayout><compOutputs><compOutput><uid v="1448087537"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="transformation"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1448087551"/><connections><connection><identifier v="destination"/><connRef v="1448084755"/><connRefOutput v="1448084756"/></connection><connection><identifier v="source"/><connRef v="1448087552"/><connRefOutput v="1448087537"/></connection></connections><GUILayout><gpos v="-4927.50439 -528 0"/></GUILayout><compOutputs><compOutput><uid v="1448083709"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.25"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448087552"/><connections><connection><identifier v="input1"/><connRef v="1448083833"/><connRefOutput v="1448083834"/></connection></connections><GUILayout><gpos v="-4990 -485.333344 -1"/><docked v="1"/><dockDistance v="112 72.8289795"/></GUILayout><compOutputs><compOutput><uid v="1448087537"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="transformation"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1448087575"/><connections><connection><identifier v="destination"/><connRef v="1448083526"/><connRefOutput v="1448083509"/></connection><connection><identifier v="source"/><connRef v="1448087544"/><connRefOutput v="1448083709"/></connection></connections><GUILayout><gpos v="-4287.50439 144 0"/></GUILayout><compOutputs><compOutput><uid v="1448083686"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448087580"/><connections><connection><identifier v="destination"/><connRef v="1448083508"/><connRefOutput v="1448083509"/></connection><connection><identifier v="source"/><connRef v="1448087551"/><connRefOutput v="1448083709"/></connection></connections><GUILayout><gpos v="-4287.50439 272 0"/></GUILayout><compOutputs><compOutput><uid v="1448083686"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448087616"/><connections><connection><identifier v="pattern_input"/><connRef v="1448087916"/><connRefOutput v="1448087917"/></connection><connection><identifier v="pattern_input_2"/><connRef v="1448088818"/><connRefOutput v="1448088819"/></connection><connection><identifier v="pattern_input_3"/><connRef v="1448088825"/><connRefOutput v="1448088826"/></connection><connection><identifier v="pattern_input_4"/><connRef v="1448088832"/><connRefOutput v="1448088833"/></connection></connections><GUILayout><gpos v="-3711.50464 -16 0"/></GUILayout><compOutputs><compOutput><uid v="1448087617"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1449399972"/><parameters><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="pattern_input_number"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter><parameter><name v="pattern_symmetry_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="pattern_symmetry_random_mode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="size"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="1 0.789999962"/></paramValue></parameter><parameter><name v="size_random"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.119999997 0.219999999"/></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.209999993"/></paramValue></parameter><parameter><name v="color_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.439999998"/></paramValue></parameter><parameter><name v="randomseed"/><relativeTo v="2"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="color_parametrization_mode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="color_parametrization_multiplier"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.75999999"/></paramValue></parameter><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="7"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="5"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="10"/></paramValue></parameter><parameter><name v="rotation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="-0.161111116"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.98999995"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448087617"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448087916"/><connections><connection><identifier v="Input"/><connRef v="1448083685"/><connRefOutput v="1448083686"/></connection></connections><GUILayout><gpos v="-4127.50439 -112 0"/></GUILayout><compOutputs><compOutput><uid v="1448087917"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1383786570"/><parameters/><outputBridgings><outputBridging><uid v="1448087917"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448088818"/><connections><connection><identifier v="Input"/><connRef v="1448087513"/><connRefOutput v="1448083686"/></connection></connections><GUILayout><gpos v="-4127.50439 16 0"/></GUILayout><compOutputs><compOutput><uid v="1448088819"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1383786570"/><parameters/><outputBridgings><outputBridging><uid v="1448088819"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448088825"/><connections><connection><identifier v="Input"/><connRef v="1448087575"/><connRefOutput v="1448083686"/></connection></connections><GUILayout><gpos v="-4127.50439 144 0"/></GUILayout><compOutputs><compOutput><uid v="1448088826"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1383786570"/><parameters/><outputBridgings><outputBridging><uid v="1448088826"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448088832"/><connections><connection><identifier v="Input"/><connRef v="1448087580"/><connRefOutput v="1448083686"/></connection></connections><GUILayout><gpos v="-4127.50439 272 0"/></GUILayout><compOutputs><compOutput><uid v="1448088833"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1383786570"/><parameters/><outputBridgings><outputBridging><uid v="1448088833"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448089911"/><connections><connection><identifier v="Input"/><connRef v="1448087616"/><connRefOutput v="1448087617"/></connection></connections><GUILayout><gpos v="-3423.50439 -16 0"/></GUILayout><compOutputs><compOutput><uid v="1448089912"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1383786570"/><parameters/><outputBridgings><outputBridging><uid v="1448089912"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448092044"/><connections><connection><identifier v="height_bottom"/><connRef v="1448089911"/><connRefOutput v="1448089912"/></connection></connections><GUILayout><gpos v="304 -80 0"/></GUILayout><compOutputs><compOutput><uid v="1448092045"/><comptype v="2"/></compOutput><compOutput><uid v="1448092046"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///height_blend?dependency=1449623608"/><parameters><parameter><name v="contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="height_position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0199999996"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448092045"/><identifier v="blended_height"/></outputBridging><outputBridging><uid v="1448092046"/><identifier v="height_mask"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448094092"/><connections><connection><identifier v="input1"/><connRef v="1448082482"/><connRefOutput v="1448082483"/></connection></connections><GUILayout><gpos v="-5105.72803 272 0"/></GUILayout><compOutputs><compOutput><uid v="1448094093"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1448156420"/><connections><connection><identifier v="Input"/><connRef v="1448291347"/><connRefOutput v="1448291348"/></connection></connections><GUILayout><gpos v="1296 -560 0"/></GUILayout><compOutputs><compOutput><uid v="1448156421"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1383786570"/><parameters/><outputBridgings><outputBridging><uid v="1448156421"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448174718"/><connections><connection><identifier v="height_bottom"/><connRef v="1448156420"/><connRefOutput v="1448156421"/></connection></connections><GUILayout><gpos v="2672 48 0"/></GUILayout><compOutputs><compOutput><uid v="1448174719"/><comptype v="2"/></compOutput><compOutput><uid v="1448174720"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///height_blend?dependency=1449623608"/><parameters><parameter><name v="contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448174719"/><identifier v="blended_height"/></outputBridging><outputBridging><uid v="1448174720"/><identifier v="height_mask"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448176301"/><connections><connection><identifier v="opacity"/><connRef v="1448174718"/><connRefOutput v="1448174720"/></connection><connection><identifier v="destination"/><connRef v="1448249701"/><connRefOutput v="1448249702"/></connection><connection><identifier v="source"/><connRef v="1485415699"/><connRefOutput v="1485415553"/></connection></connections><GUILayout><gpos v="11650.4541 -3446.91064 0"/></GUILayout><compOutputs><compOutput><uid v="1448176302"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1448198136"/><connections><connection><identifier v="Source"/><connRef v="1448136276"/><connRefOutput v="1448136277"/></connection></connections><GUILayout><gpos v="5424 48 0"/></GUILayout><compOutputs><compOutput><uid v="1448119503"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///non_uniform_blur_grayscale?dependency=1290776959"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="16"/></paramValue></parameter><parameter><name v="Blades"/><relativeTo v="0"/><paramValue><constantValueInt32 v="9"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="10.6700001"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448119503"/><identifier v="Non_Uniform_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448198539"/><connections><connection><identifier v="destination"/><connRef v="1448198136"/><connRefOutput v="1448119503"/></connection></connections><GUILayout><gpos v="5648 48 0"/></GUILayout><compOutputs><compOutput><uid v="1448198540"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448204460"/><GUILayout><gpos v="14670.2021 -4821.67676 0"/></GUILayout><compOutputs><compOutput><uid v="1448204461"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1149349207"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.579999983"/></paramValue></parameter><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448204461"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448204490"/><connections><connection><identifier v="opacity"/><connRef v="1448204460"/><connRefOutput v="1448204461"/></connection><connection><identifier v="destination"/><connRef v="1448222504"/><connRefOutput v="1448222505"/></connection></connections><GUILayout><gpos v="15152 -3504 0"/></GUILayout><compOutputs><compOutput><uid v="1448204491"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1448136227"/><connections><connection><identifier v="Source"/><connRef v="1448302245"/><connRefOutput v="1448291348"/></connection></connections><GUILayout><gpos v="4157.19531 48 0"/></GUILayout><compOutputs><compOutput><uid v="1448119503"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///non_uniform_blur_grayscale?dependency=1290776959"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="16"/></paramValue></parameter><parameter><name v="Blades"/><relativeTo v="0"/><paramValue><constantValueInt32 v="9"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="10.6700001"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448119503"/><identifier v="Non_Uniform_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448136276"/><connections><connection><identifier v="destination"/><connRef v="1448136227"/><connRefOutput v="1448119503"/></connection></connections><GUILayout><gpos v="4592 176 0"/></GUILayout><compOutputs><compOutput><uid v="1448136277"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448140058"/><GUILayout><gpos v="9584 -1296 0"/></GUILayout><compOutputs><compOutput><uid v="1448140059"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1149349207"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.99000001"/></paramValue></parameter><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448140059"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448140124"/><connections><connection><identifier v="input1"/><connRef v="1448140058"/><connRefOutput v="1448140059"/></connection></connections><GUILayout><gpos v="12208 -3958.91064 501"/></GUILayout><compOutputs><compOutput><uid v="1448176120"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1448176133"/><paramsArrayCells><paramsArrayCell><uid v="1448176295"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.347826093 0.347826093 0.347826093 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1448140133"/><connections><connection><identifier v="opacity"/><connRef v="1448140058"/><connRefOutput v="1448140059"/></connection><connection><identifier v="destination"/><connRef v="1448204220"/><connRefOutput v="1448204221"/></connection><connection><identifier v="source"/><connRef v="1485415764"/><connRefOutput v="1485415553"/></connection></connections><GUILayout><gpos v="12609.4131 -3440 0"/></GUILayout><compOutputs><compOutput><uid v="1448140134"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1448173648"/><connections><connection><identifier v="source"/><connRef v="1448175114"/><connRefOutput v="1448175115"/></connection><connection><identifier v="opacity"/><connRef v="1448175132"/><connRefOutput v="1448175133"/></connection><connection><identifier v="destination"/><connRef v="1448188045"/><connRefOutput v="1448188046"/></connection></connections><GUILayout><gpos v="12112 -3446.91064 0"/></GUILayout><compOutputs><compOutput><uid v="1448173649"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1485415998"/><paramNodes><paramNode><uid v="1485415998"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="opacitymult_3"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448175082"/><connections><connection><identifier v="input"/><connRef v="1448175083"/><connRefOutput v="1448116110"/></connection></connections><GUILayout><gpos v="11184 -3766.91064 0"/></GUILayout><compOutputs><compOutput><uid v="1448116899"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///curvature_smooth?dependency=1449590280"/><parameters><parameter><name v="normal_format"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448116899"/><identifier v="height"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448175083"/><connections><connection><identifier v="input1"/><connRef v="1448178054"/><connRefOutput v="1448178055"/></connection></connections><GUILayout><gpos v="11009.5957 -3766.91064 0"/></GUILayout><compOutputs><compOutput><uid v="1448116110"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="normal"/><parameters><parameter><name v="input2alpha"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="inversedy"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="10"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448175085"/><connections><connection><identifier v="Input_1"/><connRef v="1448175082"/><connRefOutput v="1448116899"/></connection></connections><GUILayout><gpos v="11329.5957 -3771.2251 0"/></GUILayout><compOutputs><compOutput><uid v="1448116961"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1149349207"/><parameters><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.99000001"/></paramValue></parameter><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.429999977"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448116961"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448175114"/><connections><connection><identifier v="input1"/><connRef v="1448182401"/><connRefOutput v="1448182402"/></connection></connections><GUILayout><gpos v="11649.5957 -3771.2251 0"/></GUILayout><compOutputs><compOutput><uid v="1448175115"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1448175132"/><connections><connection><identifier v="Source"/><connRef v="1448092044"/><connRefOutput v="1448092046"/></connection></connections><GUILayout><gpos v="11536 -3254.91064 0"/></GUILayout><compOutputs><compOutput><uid v="1448175133"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///invert_grayscale?dependency=1368115694"/><parameters/><outputBridgings><outputBridging><uid v="1448175133"/><identifier v="Invert_Grayscale"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448178054"/><GUILayout><gpos v="10832 -3766.91064 0"/></GUILayout><compOutputs><compOutput><uid v="1448178055"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///highpass_grayscale?dependency=1446843354"/><parameters/><outputBridgings><outputBridging><uid v="1448178055"/><identifier v="Highpass"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448182265"/><connections><connection><identifier v="Input"/><connRef v="1448175083"/><connRefOutput v="1448116110"/></connection></connections><GUILayout><gpos v="11184 -3926.91064 0"/></GUILayout><compOutputs><compOutput><uid v="1448182266"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///curvature?dependency=1446841574"/><parameters><parameter><name v="normal_format"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="10"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448182266"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448182285"/><connections><connection><identifier v="Input_1"/><connRef v="1448195552"/><connRefOutput v="1448195553"/></connection></connections><GUILayout><gpos v="11440 -3926.91064 0"/></GUILayout><compOutputs><compOutput><uid v="1448182286"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1149349207"/><parameters><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.449999988"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448182286"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448182401"/><connections><connection><identifier v="destination"/><connRef v="1448175085"/><connRefOutput v="1448116961"/></connection><connection><identifier v="source"/><connRef v="1448182285"/><connRefOutput v="1448182286"/></connection></connections><GUILayout><gpos v="11504 -3766.91064 0"/></GUILayout><compOutputs><compOutput><uid v="1448182402"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448185980"/><connections><connection><identifier v="Source"/><connRef v="1448187705"/><connRefOutput v="1448187706"/></connection><connection><identifier v="Effect"/><connRef v="1448187698"/><connRefOutput v="1448187699"/></connection></connections><GUILayout><gpos v="11152 -4246.91064 0"/></GUILayout><compOutputs><compOutput><uid v="1448185981"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///slope_blur_grayscale_2?dependency=1449402040"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="32"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="9.92000008"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448185981"/><identifier v="Slope_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448187698"/><GUILayout><gpos v="10832 -4182.91064 0"/></GUILayout><compOutputs><compOutput><uid v="1448187699"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///fractal_sum_base_2?dependency=1447014740"/><parameters><parameter><name v="Roughness"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.409999996"/></paramValue></parameter><parameter><name v="MinLevel"/><relativeTo v="0"/><paramValue><constantValueInt32 v="5"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448187699"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448187705"/><GUILayout><gpos v="10832 -4342.91064 0"/></GUILayout><compOutputs><compOutput><uid v="1448187706"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///gaussian_noise?dependency=1357066167"/><parameters><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448187706"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448187772"/><connections><connection><identifier v="input1"/><connRef v="1448185980"/><connRefOutput v="1448185981"/></connection><connection><identifier v="inputgradient"/><connRef v="1448187813"/><connRefOutput v="1448187814"/></connection></connections><GUILayout><gpos v="11312 -4246.91064 0"/></GUILayout><compOutputs><compOutput><uid v="1448187773"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="warp"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1448187795"/><connections><connection><identifier v="Input"/><connRef v="1448187772"/><connRefOutput v="1448187773"/></connection></connections><GUILayout><gpos v="11472 -4246.91064 0"/></GUILayout><compOutputs><compOutput><uid v="1448187796"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1383786570"/><parameters/><outputBridgings><outputBridging><uid v="1448187796"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448187813"/><connections><connection><identifier v="Source"/><connRef v="1448188230"/><connRefOutput v="1448188231"/></connection></connections><GUILayout><gpos v="11152 -4086.91064 0"/></GUILayout><compOutputs><compOutput><uid v="1448187814"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1299236171"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.43999994"/></paramValue></parameter><parameter><name v="Quality"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448187814"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448188025"/><connections><connection><identifier v="Input"/><connRef v="1448187795"/><connRefOutput v="1448187796"/></connection></connections><GUILayout><gpos v="11632 -4246.91064 0"/></GUILayout><compOutputs><compOutput><uid v="1448188026"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///quantize_grayscale?dependency=1448187982"/><parameters><parameter><name v="Quantize"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448188026"/><identifier v="Quantize"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448188045"/><connections><connection><identifier v="destination"/><connRef v="1448176301"/><connRefOutput v="1448176302"/></connection><connection><identifier v="opacity"/><connRef v="1448175132"/><connRefOutput v="1448175133"/></connection><connection><identifier v="source"/><connRef v="1448188065"/><connRefOutput v="1448188066"/></connection></connections><GUILayout><gpos v="11888 -3440 0"/></GUILayout><compOutputs><compOutput><uid v="1448188046"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1485415954"/><paramNodes><paramNode><uid v="1485415954"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="opacitymult_2"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448188065"/><connections><connection><identifier v="input1"/><connRef v="1448188025"/><connRefOutput v="1448188026"/></connection></connections><GUILayout><gpos v="11808 -3974.91064 0"/></GUILayout><compOutputs><compOutput><uid v="1448188066"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1448188230"/><connections><connection><identifier v="input"/><connRef v="1448178054"/><connRefOutput v="1448178055"/></connection></connections><GUILayout><gpos v="10845.085 -4054.91064 0"/></GUILayout><compOutputs><compOutput><uid v="1448188231"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///hbao?dependency=1297162667"/><parameters><parameter><name v="height_depth"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.560000002"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448188231"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448195552"/><connections><connection><identifier v="Source"/><connRef v="1448182265"/><connRefOutput v="1448182266"/></connection></connections><GUILayout><gpos v="11312 -3926.91064 0"/></GUILayout><compOutputs><compOutput><uid v="1448195553"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1299236171"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.429999977"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448195553"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448196832"/><GUILayout><gpos v="11120 -4534.91064 0"/></GUILayout><compOutputs><compOutput><uid v="1448196833"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///hbao?dependency=1297162667"/><parameters><parameter><name v="height_depth"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.359999985"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448196833"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448204039"/><connections><connection><identifier v="Input_1"/><connRef v="1448196832"/><connRefOutput v="1448196833"/></connection></connections><GUILayout><gpos v="11312 -4534.91064 0"/></GUILayout><compOutputs><compOutput><uid v="1448204040"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1149349207"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.340000004"/></paramValue></parameter><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448204040"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448204179"/><connections><connection><identifier v="destination"/><connRef v="1448175132"/><connRefOutput v="1448175133"/></connection><connection><identifier v="source"/><connRef v="1448204205"/><connRefOutput v="1448204206"/></connection></connections><GUILayout><gpos v="12112 -3240.84033 0"/></GUILayout><compOutputs><compOutput><uid v="1448204180"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448204205"/><connections><connection><identifier v="Source"/><connRef v="1448204039"/><connRefOutput v="1448204040"/></connection></connections><GUILayout><gpos v="11473.4199 -4422.91064 0"/></GUILayout><compOutputs><compOutput><uid v="1448204206"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///invert_grayscale?dependency=1368115694"/><parameters/><outputBridgings><outputBridging><uid v="1448204206"/><identifier v="Invert_Grayscale"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448204220"/><connections><connection><identifier v="destination"/><connRef v="1448173648"/><connRefOutput v="1448173649"/></connection><connection><identifier v="opacity"/><connRef v="1448204179"/><connRefOutput v="1448204180"/></connection><connection><identifier v="source"/><connRef v="1485416065"/><connRefOutput v="1485415553"/></connection></connections><GUILayout><gpos v="12400 -3408 0"/></GUILayout><compOutputs><compOutput><uid v="1448204221"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1485416154"/><paramNodes><paramNode><uid v="1485416154"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="opacitymult_4"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448208717"/><connections><connection><identifier v="destination"/><connRef v="1448140133"/><connRefOutput v="1448140134"/></connection><connection><identifier v="source"/><connRef v="1448212388"/><connRefOutput v="1448175115"/></connection><connection><identifier v="opacity"/><connRef v="1448174718"/><connRefOutput v="1448174720"/></connection></connections><GUILayout><gpos v="12880 -3446.91064 0"/></GUILayout><compOutputs><compOutput><uid v="1448208718"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0399999991"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448212386"/><connections><connection><identifier v="Source"/><connRef v="1448212392"/><connRefOutput v="1448182266"/></connection></connections><GUILayout><gpos v="12400 -4406.91064 0"/></GUILayout><compOutputs><compOutput><uid v="1448195553"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1299236171"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.429999977"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448195553"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448212387"/><connections><connection><identifier v="destination"/><connRef v="1448212393"/><connRefOutput v="1448116961"/></connection><connection><identifier v="source"/><connRef v="1448212394"/><connRefOutput v="1448182286"/></connection></connections><GUILayout><gpos v="12592 -4246.91064 0"/></GUILayout><compOutputs><compOutput><uid v="1448182402"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448212388"/><connections><connection><identifier v="input1"/><connRef v="1448212387"/><connRefOutput v="1448182402"/></connection></connections><GUILayout><gpos v="12737.5957 -4246.91064 502"/></GUILayout><compOutputs><compOutput><uid v="1448175115"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1448212389"/><connections><connection><identifier v="input1"/><connRef v="1448212391"/><connRefOutput v="1448178055"/></connection></connections><GUILayout><gpos v="12097.5967 -4246.91064 501"/></GUILayout><compOutputs><compOutput><uid v="1448116110"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="normal"/><parameters><parameter><name v="input2alpha"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="inversedy"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="10"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448212390"/><connections><connection><identifier v="input"/><connRef v="1448212389"/><connRefOutput v="1448116110"/></connection></connections><GUILayout><gpos v="12272 -4246.91064 0"/></GUILayout><compOutputs><compOutput><uid v="1448116899"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///curvature_smooth?dependency=1449590280"/><parameters><parameter><name v="normal_format"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448116899"/><identifier v="height"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448212391"/><GUILayout><gpos v="11920 -4246.91064 501"/></GUILayout><compOutputs><compOutput><uid v="1448178055"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///highpass_grayscale?dependency=1446843354"/><parameters/><outputBridgings><outputBridging><uid v="1448178055"/><identifier v="Highpass"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448212392"/><connections><connection><identifier v="Input"/><connRef v="1448212389"/><connRefOutput v="1448116110"/></connection></connections><GUILayout><gpos v="12272 -4406.91064 0"/></GUILayout><compOutputs><compOutput><uid v="1448182266"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///curvature?dependency=1446841574"/><parameters><parameter><name v="normal_format"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="10"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448182266"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448212393"/><connections><connection><identifier v="Input_1"/><connRef v="1448212390"/><connRefOutput v="1448116899"/></connection></connections><GUILayout><gpos v="12417.5957 -4251.2251 501"/></GUILayout><compOutputs><compOutput><uid v="1448116961"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1149349207"/><parameters><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.99000001"/></paramValue></parameter><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.429999977"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448116961"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448212394"/><connections><connection><identifier v="Input_1"/><connRef v="1448212386"/><connRefOutput v="1448195553"/></connection></connections><GUILayout><gpos v="12528 -4406.91064 0"/></GUILayout><compOutputs><compOutput><uid v="1448182286"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1149349207"/><parameters><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.449999988"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448182286"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448213171"/><connections><connection><identifier v="mask"/><connRef v="1448174718"/><connRefOutput v="1448174720"/></connection></connections><GUILayout><gpos v="12720 -3222.91064 0"/></GUILayout><compOutputs><compOutput><uid v="1448213172"/><comptype v="1"/></compOutput><compOutput><uid v="1448213173"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill?dependency=1323881949"/><parameters/><outputBridgings><outputBridging><uid v="1448213172"/><identifier v="output"/></outputBridging><outputBridging><uid v="1448213173"/><identifier v="referenceMap"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448213363"/><connections><connection><identifier v="input"/><connRef v="1448213171"/><connRefOutput v="1448213172"/></connection></connections><GUILayout><gpos v="12880 -3222.91064 0"/></GUILayout><compOutputs><compOutput><uid v="1448213364"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_random_grayscale?dependency=1323881949"/><parameters/><outputBridgings><outputBridging><uid v="1448213364"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448213376"/><connections><connection><identifier v="destination"/><connRef v="1448208717"/><connRefOutput v="1448208718"/></connection><connection><identifier v="source"/><connRef v="1448213390"/><connRefOutput v="1448213391"/></connection><connection><identifier v="opacity"/><connRef v="1448174718"/><connRefOutput v="1448174720"/></connection></connections><GUILayout><gpos v="13136 -3472 0"/></GUILayout><compOutputs><compOutput><uid v="1448213377"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.099999994"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448213390"/><connections><connection><identifier v="input1"/><connRef v="1448213363"/><connRefOutput v="1448213364"/></connection></connections><GUILayout><gpos v="13040 -3222.91064 0"/></GUILayout><compOutputs><compOutput><uid v="1448213391"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1448213629"/><connections><connection><identifier v="input"/><connRef v="1448302245"/><connRefOutput v="1448291348"/></connection></connections><GUILayout><gpos v="12240 -4681.15332 0"/></GUILayout><compOutputs><compOutput><uid v="1448196833"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///hbao?dependency=1297162667"/><parameters><parameter><name v="height_depth"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.359999985"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448196833"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448213630"/><connections><connection><identifier v="input1"/><connRef v="1448213632"/><connRefOutput v="1448204040"/></connection></connections><GUILayout><gpos v="12592 -4726.91064 0"/></GUILayout><compOutputs><compOutput><uid v="1448176120"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1448176133"/><paramsArrayCells><paramsArrayCell><uid v="1448204174"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.343478262 0.291557133 0.213675424 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1448213631"/><connections><connection><identifier v="Source"/><connRef v="1448213632"/><connRefOutput v="1448204040"/></connection></connections><GUILayout><gpos v="12592 -4598.91064 0"/></GUILayout><compOutputs><compOutput><uid v="1448204206"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///invert_grayscale?dependency=1368115694"/><parameters/><outputBridgings><outputBridging><uid v="1448204206"/><identifier v="Invert_Grayscale"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448213632"/><connections><connection><identifier v="Input_1"/><connRef v="1448213629"/><connRefOutput v="1448196833"/></connection></connections><GUILayout><gpos v="12432 -4681.15332 0"/></GUILayout><compOutputs><compOutput><uid v="1448204040"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1149349207"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.340000004"/></paramValue></parameter><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448204040"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448214566"/><connections><connection><identifier v="destination"/><connRef v="1448174718"/><connRefOutput v="1448174720"/></connection><connection><identifier v="source"/><connRef v="1448213631"/><connRefOutput v="1448204206"/></connection></connections><GUILayout><gpos v="13168 -4022.91064 0"/></GUILayout><compOutputs><compOutput><uid v="1448214567"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448214593"/><connections><connection><identifier v="destination"/><connRef v="1448213376"/><connRefOutput v="1448213377"/></connection><connection><identifier v="opacity"/><connRef v="1448214566"/><connRefOutput v="1448214567"/></connection><connection><identifier v="source"/><connRef v="1448213630"/><connRefOutput v="1448176120"/></connection></connections><GUILayout><gpos v="13504.6016 -3461.77563 0"/></GUILayout><compOutputs><compOutput><uid v="1448214594"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.459999979"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448214668"/><connections><connection><identifier v="destination"/><connRef v="1448214593"/><connRefOutput v="1448214594"/></connection><connection><identifier v="opacity"/><connRef v="1448140058"/><connRefOutput v="1448140059"/></connection><connection><identifier v="source"/><connRef v="1448214680"/><connRefOutput v="1448213391"/></connection></connections><GUILayout><gpos v="13904 -3460.61694 0"/></GUILayout><compOutputs><compOutput><uid v="1448214669"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.099999994"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448214680"/><connections><connection><identifier v="input1"/><connRef v="1448214681"/><connRefOutput v="1448213364"/></connection></connections><GUILayout><gpos v="13488 -4214.91064 0"/></GUILayout><compOutputs><compOutput><uid v="1448213391"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1448214681"/><connections><connection><identifier v="input"/><connRef v="1448214682"/><connRefOutput v="1448213172"/></connection></connections><GUILayout><gpos v="13328 -4214.91064 0"/></GUILayout><compOutputs><compOutput><uid v="1448213364"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_random_grayscale?dependency=1323881949"/><parameters/><outputBridgings><outputBridging><uid v="1448213364"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448214682"/><connections><connection><identifier v="mask"/><connRef v="1448140058"/><connRefOutput v="1448140059"/></connection></connections><GUILayout><gpos v="13168 -4214.91064 0"/></GUILayout><compOutputs><compOutput><uid v="1448213172"/><comptype v="1"/></compOutput><compOutput><uid v="1448213173"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill?dependency=1323881949"/><parameters/><outputBridgings><outputBridging><uid v="1448213172"/><identifier v="output"/></outputBridging><outputBridging><uid v="1448213173"/><identifier v="referenceMap"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448214844"/><connections><connection><identifier v="destination"/><connRef v="1448214668"/><connRefOutput v="1448214669"/></connection><connection><identifier v="opacity"/><connRef v="1448140058"/><connRefOutput v="1448140059"/></connection><connection><identifier v="source"/><connRef v="1448214864"/><connRefOutput v="1448175115"/></connection></connections><GUILayout><gpos v="14224 -3478.91064 0"/></GUILayout><compOutputs><compOutput><uid v="1448214845"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.049999997"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448214856"/><connections><connection><identifier v="destination"/><connRef v="1448214858"/><connRefOutput v="1448116961"/></connection><connection><identifier v="source"/><connRef v="1448214863"/><connRefOutput v="1448182286"/></connection></connections><GUILayout><gpos v="13840 -4374.91064 0"/></GUILayout><compOutputs><compOutput><uid v="1448182402"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448214857"/><connections><connection><identifier v="input"/><connRef v="1448214859"/><connRefOutput v="1448116110"/></connection></connections><GUILayout><gpos v="13520 -4374.91064 0"/></GUILayout><compOutputs><compOutput><uid v="1448116899"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///curvature_smooth?dependency=1449590280"/><parameters><parameter><name v="normal_format"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448116899"/><identifier v="height"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448214858"/><connections><connection><identifier v="Input_1"/><connRef v="1448214857"/><connRefOutput v="1448116899"/></connection></connections><GUILayout><gpos v="13665.5957 -4379.2251 0"/></GUILayout><compOutputs><compOutput><uid v="1448116961"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1149349207"/><parameters><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.99000001"/></paramValue></parameter><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.429999977"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448116961"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448214859"/><connections><connection><identifier v="input1"/><connRef v="1448214862"/><connRefOutput v="1448178055"/></connection></connections><GUILayout><gpos v="13345.5967 -4374.91064 0"/></GUILayout><compOutputs><compOutput><uid v="1448116110"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="normal"/><parameters><parameter><name v="input2alpha"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="inversedy"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="10"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448214860"/><connections><connection><identifier v="Input"/><connRef v="1448214859"/><connRefOutput v="1448116110"/></connection></connections><GUILayout><gpos v="13520 -4534.91064 502"/></GUILayout><compOutputs><compOutput><uid v="1448182266"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///curvature?dependency=1446841574"/><parameters><parameter><name v="normal_format"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="10"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448182266"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448214861"/><connections><connection><identifier v="Source"/><connRef v="1448214860"/><connRefOutput v="1448182266"/></connection></connections><GUILayout><gpos v="13648 -4534.91064 501"/></GUILayout><compOutputs><compOutput><uid v="1448195553"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1299236171"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.429999977"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448195553"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448214862"/><GUILayout><gpos v="13168 -4374.91064 0"/></GUILayout><compOutputs><compOutput><uid v="1448178055"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///highpass_grayscale?dependency=1446843354"/><parameters/><outputBridgings><outputBridging><uid v="1448178055"/><identifier v="Highpass"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448214863"/><connections><connection><identifier v="Input_1"/><connRef v="1448214861"/><connRefOutput v="1448195553"/></connection></connections><GUILayout><gpos v="13776 -4534.91064 502"/></GUILayout><compOutputs><compOutput><uid v="1448182286"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1149349207"/><parameters><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.199999988"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448182286"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448214864"/><connections><connection><identifier v="input1"/><connRef v="1448214856"/><connRefOutput v="1448182402"/></connection></connections><GUILayout><gpos v="13985.5957 -4374.91064 0"/></GUILayout><compOutputs><compOutput><uid v="1448175115"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1448222474"/><connections><connection><identifier v="input1"/><connRef v="1448222477"/><connRefOutput v="1448204040"/></connection></connections><GUILayout><gpos v="13552 -4804.66797 0"/></GUILayout><compOutputs><compOutput><uid v="1448176120"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1448176133"/><paramsArrayCells><paramsArrayCell><uid v="1448204174"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.343478262 0.291557133 0.213675424 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1448222475"/><connections><connection><identifier v="Source"/><connRef v="1448222477"/><connRefOutput v="1448204040"/></connection></connections><GUILayout><gpos v="13552 -4676.66797 0"/></GUILayout><compOutputs><compOutput><uid v="1448204206"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///invert_grayscale?dependency=1368115694"/><parameters/><outputBridgings><outputBridging><uid v="1448204206"/><identifier v="Invert_Grayscale"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448222476"/><connections><connection><identifier v="input"/><connRef v="1448198539"/><connRefOutput v="1448198540"/></connection></connections><GUILayout><gpos v="13200 -4758.91064 0"/></GUILayout><compOutputs><compOutput><uid v="1448196833"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///hbao?dependency=1297162667"/><parameters><parameter><name v="height_depth"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.359999985"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448196833"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448222477"/><connections><connection><identifier v="Input_1"/><connRef v="1448222476"/><connRefOutput v="1448196833"/></connection></connections><GUILayout><gpos v="13392 -4758.91064 0"/></GUILayout><compOutputs><compOutput><uid v="1448204040"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1149349207"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.469999999"/></paramValue></parameter><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448204040"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448222504"/><connections><connection><identifier v="destination"/><connRef v="1448214844"/><connRefOutput v="1448214845"/></connection><connection><identifier v="source"/><connRef v="1448222474"/><connRefOutput v="1448176120"/></connection><connection><identifier v="opacity"/><connRef v="1448232198"/><connRefOutput v="1448232199"/></connection></connections><GUILayout><gpos v="14480 -3478.91064 0"/></GUILayout><compOutputs><compOutput><uid v="1448222505"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.659999967"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448232198"/><connections><connection><identifier v="source"/><connRef v="1448222475"/><connRefOutput v="1448204206"/></connection><connection><identifier v="destination"/><connRef v="1448140058"/><connRefOutput v="1448140059"/></connection></connections><GUILayout><gpos v="14224 -3748.37646 0"/></GUILayout><compOutputs><compOutput><uid v="1448232199"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448239851"/><connections><connection><identifier v="opacity"/><connRef v="1448092044"/><connRefOutput v="1448092046"/></connection><connection><identifier v="destination"/><connRef v="1485415583"/><connRefOutput v="1485415584"/></connection></connections><GUILayout><gpos v="9424 -3397.3335 0"/></GUILayout><compOutputs><compOutput><uid v="1448239852"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1485416258"/><paramNodes><paramNode><uid v="1485416258"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="opacitymult_5"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448240218"/><GUILayout><gpos v="8336 -3781.3335 0"/></GUILayout><compOutputs><compOutput><uid v="1448187699"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///fractal_sum_base_2?dependency=1447014740"/><parameters><parameter><name v="Roughness"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.50999999"/></paramValue></parameter><parameter><name v="MinLevel"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448187699"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448240219"/><connections><connection><identifier v="Source"/><connRef v="1448240220"/><connRefOutput v="1448187706"/></connection><connection><identifier v="Effect"/><connRef v="1448240218"/><connRefOutput v="1448187699"/></connection></connections><GUILayout><gpos v="8528 -3781.3335 0"/></GUILayout><compOutputs><compOutput><uid v="1448185981"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///slope_blur_grayscale_2?dependency=1449402040"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="32"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="16"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448185981"/><identifier v="Slope_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448240220"/><GUILayout><gpos v="8336 -3941.3335 0"/></GUILayout><compOutputs><compOutput><uid v="1448187706"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///gaussian_noise?dependency=1357066167"/><parameters><parameter><name v="scale"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1485416412"/><paramNodes><paramNode><uid v="1485416412"/><function v="get_integer1"/><type v="16"/><funcDatas><funcData><name v="get_integer1"/><constantValue><constantValueString v="scale_5"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter><parameter><name v="disorder"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.5"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448187706"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448240299"/><connections><connection><identifier v="input1"/><connRef v="1448240219"/><connRefOutput v="1448185981"/></connection><connection><identifier v="inputgradient"/><connRef v="1448247514"/><connRefOutput v="1448247515"/></connection></connections><GUILayout><gpos v="8688 -3781.3335 0"/></GUILayout><compOutputs><compOutput><uid v="1448240300"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="warp"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1448240319"/><GUILayout><gpos v="8336 -3589.3335 0"/></GUILayout><compOutputs><compOutput><uid v="1448240320"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///hbao?dependency=1297162667"/><parameters><parameter><name v="height_depth"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.620000005"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448240320"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448247507"/><connections><connection><identifier v="Source"/><connRef v="1448240319"/><connRefOutput v="1448240320"/></connection></connections><GUILayout><gpos v="8496 -3589.3335 0"/></GUILayout><compOutputs><compOutput><uid v="1448247508"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1299236171"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="2.28999996"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448247508"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448247514"/><connections><connection><identifier v="Input"/><connRef v="1448247507"/><connRefOutput v="1448247508"/></connection></connections><GUILayout><gpos v="8656 -3589.3335 0"/></GUILayout><compOutputs><compOutput><uid v="1448247515"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1383786570"/><parameters/><outputBridgings><outputBridging><uid v="1448247515"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448247570"/><connections><connection><identifier v="Input_1"/><connRef v="1448240299"/><connRefOutput v="1448240300"/></connection></connections><GUILayout><gpos v="8848 -3781.3335 0"/></GUILayout><compOutputs><compOutput><uid v="1448247571"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1149349207"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.699999988"/></paramValue></parameter><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.969999969"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448247571"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448247643"/><connections><connection><identifier v="destination"/><connRef v="1448239851"/><connRefOutput v="1448239852"/></connection><connection><identifier v="opacity"/><connRef v="1448092044"/><connRefOutput v="1448092046"/></connection><connection><identifier v="source"/><connRef v="1448247663"/><connRefOutput v="1448247664"/></connection></connections><GUILayout><gpos v="9648 -3408 0"/></GUILayout><compOutputs><compOutput><uid v="1448247644"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1485416444"/><paramNodes><paramNode><uid v="1485416444"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="opacitymult_6"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448247663"/><connections><connection><identifier v="input1"/><connRef v="1448247570"/><connRefOutput v="1448247571"/></connection></connections><GUILayout><gpos v="9008 -3781.3335 0"/></GUILayout><compOutputs><compOutput><uid v="1448247664"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1448249608"/><connections><connection><identifier v="Input_1"/><connRef v="1448240319"/><connRefOutput v="1448240320"/></connection></connections><GUILayout><gpos v="9328 -3621.3335 0"/></GUILayout><compOutputs><compOutput><uid v="1448249609"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1149349207"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.389999986"/></paramValue></parameter><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448249609"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448249701"/><connections><connection><identifier v="destination"/><connRef v="1448247643"/><connRefOutput v="1448247644"/></connection><connection><identifier v="opacity"/><connRef v="1448092044"/><connRefOutput v="1448092046"/></connection><connection><identifier v="source"/><connRef v="1448249722"/><connRefOutput v="1448249723"/></connection></connections><GUILayout><gpos v="10032 -3408 0"/></GUILayout><compOutputs><compOutput><uid v="1448249702"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0299999993"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448249722"/><connections><connection><identifier v="input1"/><connRef v="1448249740"/><connRefOutput v="1448249741"/></connection></connections><GUILayout><gpos v="9808 -3621.3335 0"/></GUILayout><compOutputs><compOutput><uid v="1448249723"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1448249740"/><connections><connection><identifier v="Source"/><connRef v="1448249608"/><connRefOutput v="1448249609"/></connection></connections><GUILayout><gpos v="9488 -3621.3335 0"/></GUILayout><compOutputs><compOutput><uid v="1448249741"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///invert_grayscale?dependency=1368115694"/><parameters/><outputBridgings><outputBridging><uid v="1448249741"/><identifier v="Invert_Grayscale"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448258565"/><GUILayout><gpos v="14672 -4997.3335 0"/></GUILayout><compOutputs><compOutput><uid v="1448204461"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1149349207"/><parameters><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.769999981"/></paramValue></parameter><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0599999987"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448204461"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448261541"/><connections><connection><identifier v="destination"/><connRef v="1448204490"/><connRefOutput v="1448204491"/></connection><connection><identifier v="opacity"/><connRef v="1448204460"/><connRefOutput v="1448204461"/></connection><connection><identifier v="source"/><connRef v="1448270971"/><connRefOutput v="1448270972"/></connection></connections><GUILayout><gpos v="15440 -3504 0"/></GUILayout><compOutputs><compOutput><uid v="1448261542"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1485416587"/><paramNodes><paramNode><uid v="1485416587"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="opacitymult_7"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448270971"/><connections><connection><identifier v="input1"/><connRef v="1448258565"/><connRefOutput v="1448204461"/></connection></connections><GUILayout><gpos v="14832 -4997.3335 0"/></GUILayout><compOutputs><compOutput><uid v="1448270972"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1448271159"/><connections><connection><identifier v="opacity"/><connRef v="1448092044"/><connRefOutput v="1448092046"/></connection><connection><identifier v="destination"/><connRef v="1485416874"/><connRefOutput v="1485416875"/></connection></connections><GUILayout><gpos v="11088 -656 0"/></GUILayout><compOutputs><compOutput><uid v="1448271160"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0399999991"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448271261"/><connections><connection><identifier v="destination"/><connRef v="1448271159"/><connRefOutput v="1448271160"/></connection><connection><identifier v="opacity"/><connRef v="1448092044"/><connRefOutput v="1448092046"/></connection><connection><identifier v="source"/><connRef v="1448249740"/><connRefOutput v="1448249741"/></connection></connections><GUILayout><gpos v="11312 -656 0"/></GUILayout><compOutputs><compOutput><uid v="1448271219"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.109999999"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448271568"/><connections><connection><identifier v="opacity"/><connRef v="1448175132"/><connRefOutput v="1448175133"/></connection><connection><identifier v="source"/><connRef v="1448188025"/><connRefOutput v="1448188026"/></connection><connection><identifier v="destination"/><connRef v="1448271261"/><connRefOutput v="1448271219"/></connection></connections><GUILayout><gpos v="11664 -688 0"/></GUILayout><compOutputs><compOutput><uid v="1448271569"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.049999997"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448274118"/><connections><connection><identifier v="destination"/><connRef v="1448271568"/><connRefOutput v="1448271569"/></connection><connection><identifier v="opacity"/><connRef v="1448175132"/><connRefOutput v="1448175133"/></connection><connection><identifier v="source"/><connRef v="1448182401"/><connRefOutput v="1448182402"/></connection></connections><GUILayout><gpos v="11952 -656 0"/></GUILayout><compOutputs><compOutput><uid v="1448274119"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0399999991"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448274152"/><connections><connection><identifier v="destination"/><connRef v="1448274118"/><connRefOutput v="1448274119"/></connection><connection><identifier v="opacity"/><connRef v="1448204179"/><connRefOutput v="1448204180"/></connection><connection><identifier v="source"/><connRef v="1448204205"/><connRefOutput v="1448204206"/></connection></connections><GUILayout><gpos v="12208 -688 0"/></GUILayout><compOutputs><compOutput><uid v="1448274153"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0599999987"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448274269"/><connections><connection><identifier v="opacity"/><connRef v="1448174718"/><connRefOutput v="1448174720"/></connection><connection><identifier v="source"/><connRef v="1448213363"/><connRefOutput v="1448213364"/></connection><connection><identifier v="destination"/><connRef v="1448274152"/><connRefOutput v="1448274153"/></connection></connections><GUILayout><gpos v="12528 -669.706299 0"/></GUILayout><compOutputs><compOutput><uid v="1448274270"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0799999982"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448274344"/><connections><connection><identifier v="destination"/><connRef v="1448274269"/><connRefOutput v="1448274270"/></connection><connection><identifier v="opacity"/><connRef v="1448214566"/><connRefOutput v="1448214567"/></connection><connection><identifier v="source"/><connRef v="1448213631"/><connRefOutput v="1448204206"/></connection></connections><GUILayout><gpos v="12720 -669.307251 0"/></GUILayout><compOutputs><compOutput><uid v="1448274345"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0700000003"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448274391"/><connections><connection><identifier v="destination"/><connRef v="1448274344"/><connRefOutput v="1448274345"/></connection><connection><identifier v="opacity"/><connRef v="1448140058"/><connRefOutput v="1448140059"/></connection><connection><identifier v="source"/><connRef v="1448214681"/><connRefOutput v="1448213364"/></connection></connections><GUILayout><gpos v="12880 -670.50531 0"/></GUILayout><compOutputs><compOutput><uid v="1448274392"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0399999991"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448274456"/><connections><connection><identifier v="destination"/><connRef v="1448274391"/><connRefOutput v="1448274392"/></connection><connection><identifier v="opacity"/><connRef v="1448140058"/><connRefOutput v="1448140059"/></connection><connection><identifier v="source"/><connRef v="1448214856"/><connRefOutput v="1448182402"/></connection></connections><GUILayout><gpos v="13040 -669.307251 0"/></GUILayout><compOutputs><compOutput><uid v="1448274457"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0599999987"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448274490"/><connections><connection><identifier v="destination"/><connRef v="1448274456"/><connRefOutput v="1448274457"/></connection><connection><identifier v="opacity"/><connRef v="1448232198"/><connRefOutput v="1448232199"/></connection><connection><identifier v="source"/><connRef v="1448222475"/><connRefOutput v="1448204206"/></connection></connections><GUILayout><gpos v="13200 -669.307251 0"/></GUILayout><compOutputs><compOutput><uid v="1448274491"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0700000003"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448274529"/><connections><connection><identifier v="destination"/><connRef v="1448274490"/><connRefOutput v="1448274491"/></connection><connection><identifier v="opacity"/><connRef v="1448204460"/><connRefOutput v="1448204461"/></connection></connections><GUILayout><gpos v="13360 -671.803223 0"/></GUILayout><compOutputs><compOutput><uid v="1448274530"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1448274730"/><connections><connection><identifier v="destination"/><connRef v="1448274529"/><connRefOutput v="1448274530"/></connection><connection><identifier v="opacity"/><connRef v="1448204460"/><connRefOutput v="1448204461"/></connection><connection><identifier v="source"/><connRef v="1448258565"/><connRefOutput v="1448204461"/></connection></connections><GUILayout><gpos v="13520 -669.307251 0"/></GUILayout><compOutputs><compOutput><uid v="1448274731"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.049999997"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448291347"/><connections><connection><identifier v="destination"/><connRef v="1448092044"/><connRefOutput v="1448092045"/></connection></connections><GUILayout><gpos v="1104 -560 0"/></GUILayout><compOutputs><compOutput><uid v="1448291348"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448302245"/><connections><connection><identifier v="destination"/><connRef v="1448174718"/><connRefOutput v="1448174719"/></connection></connections><GUILayout><gpos v="3888 -98.2482147 0"/></GUILayout><compOutputs><compOutput><uid v="1448291348"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.049999997"/></paramValue></parameter><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1485415552"/><GUILayout><gpos v="8784 -3329.07544 0"/></GUILayout><compOutputs><compOutput><uid v="1485415553"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="uniform"/><parameters><parameter><name v="outputsize"/><relativeTo v="0"/><paramValue><constantValueInt2 v="4 4"/></paramValue></parameter><parameter><name v="outputcolor"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1485415670"/><paramNodes><paramNode><uid v="1485415670"/><function v="get_float4"/><type v="2048"/><funcDatas><funcData><name v="get_float4"/><constantValue><constantValueString v="outputcolor"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1485415583"/><connections><connection><identifier v="destination"/><connRef v="1485415552"/><connRefOutput v="1485415553"/></connection><connection><identifier v="opacity"/><connRef v="1448092044"/><connRefOutput v="1448092046"/></connection></connections><GUILayout><gpos v="9040 -3376 0"/></GUILayout><compOutputs><compOutput><uid v="1485415584"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="outputsize"/><relativeTo v="1"/><paramValue><constantValueInt2 v="0 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1485415699"/><GUILayout><gpos v="11344 -3536 0"/></GUILayout><compOutputs><compOutput><uid v="1485415553"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="uniform"/><parameters><parameter><name v="outputsize"/><relativeTo v="0"/><paramValue><constantValueInt2 v="4 4"/></paramValue></parameter><parameter><name v="outputcolor"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1485415741"/><paramNodes><paramNode><uid v="1485415741"/><function v="get_float4"/><type v="2048"/><funcDatas><funcData><name v="get_float4"/><constantValue><constantValueString v="outputcolor_1"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1485415764"/><GUILayout><gpos v="12400 -3600 0"/></GUILayout><compOutputs><compOutput><uid v="1485415553"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="uniform"/><parameters><parameter><name v="outputsize"/><relativeTo v="0"/><paramValue><constantValueInt2 v="4 4"/></paramValue></parameter><parameter><name v="outputcolor"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1485415814"/><paramNodes><paramNode><uid v="1485415814"/><function v="get_float4"/><type v="2048"/><funcDatas><funcData><name v="get_float4"/><constantValue><constantValueString v="outputcolor_2"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1485416065"/><GUILayout><gpos v="12144 -3649.1333 0"/></GUILayout><compOutputs><compOutput><uid v="1485415553"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="uniform"/><parameters><parameter><name v="outputsize"/><relativeTo v="0"/><paramValue><constantValueInt2 v="4 4"/></paramValue></parameter><parameter><name v="outputcolor"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1485416111"/><paramNodes><paramNode><uid v="1485416111"/><function v="get_float4"/><type v="2048"/><funcDatas><funcData><name v="get_float4"/><constantValue><constantValueString v="outputcolor_3"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1485416531"/><GUILayout><gpos v="10514.708 -819.924988 0"/></GUILayout><compOutputs><compOutput><uid v="1485415553"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="uniform"/><parameters><parameter><name v="outputsize"/><relativeTo v="0"/><paramValue><constantValueInt2 v="4 4"/></paramValue></parameter><parameter><name v="colorswitch"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="outputcolor"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1485417229"/><paramNodes><paramNode><uid v="1485417229"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="outputcolor_6"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1485416641"/><connections><connection><identifier v="destination"/><connRef v="1448140058"/><connRefOutput v="1448140059"/></connection><connection><identifier v="source"/><connRef v="1485416763"/><connRefOutput v="1485416764"/></connection></connections><GUILayout><gpos v="10463.0332 -336 0"/></GUILayout><compOutputs><compOutput><uid v="1485416642"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1485416684"/><connections><connection><identifier v="destination"/><connRef v="1485416641"/><connRefOutput v="1485416642"/></connection><connection><identifier v="source"/><connRef v="1448174718"/><connRefOutput v="1448174720"/></connection></connections><GUILayout><gpos v="10672 -400 0"/></GUILayout><compOutputs><compOutput><uid v="1485416685"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1485416763"/><connections><connection><identifier v="Source"/><connRef v="1448092044"/><connRefOutput v="1448092046"/></connection></connections><GUILayout><gpos v="5824 -192 0"/></GUILayout><compOutputs><compOutput><uid v="1485416764"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///invert_grayscale?dependency=1368115694"/><parameters/><outputBridgings><outputBridging><uid v="1485416764"/><identifier v="Invert_Grayscale"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1485416874"/><connections><connection><identifier v="source"/><connRef v="1485416531"/><connRefOutput v="1485415553"/></connection><connection><identifier v="opacity"/><connRef v="1485416684"/><connRefOutput v="1485416685"/></connection></connections><GUILayout><gpos v="10896 -496 0"/></GUILayout><compOutputs><compOutput><uid v="1485416875"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="outputsize"/><relativeTo v="1"/><paramValue><constantValueInt2 v="0 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode></compNodes><baseParameters><parameter><name v="outputsize"/><relativeTo v="0"/><paramValue><constantValueInt2 v="11 11"/></paramValue></parameter></baseParameters><GUIObjects><GUIObject><type v="COMMENT"/><GUILayout><gpos v="224 -160 0"/><size v="160 160"/></GUILayout><GUIName v=""/><uid v="1485310417"/><title v="Height Offset"/><frameColor v="0 0.791666687 0.185546875 0.250980407"/><isTitleVisible v="1"/><isFrameVisible v="1"/></GUIObject><GUIObject><type v="COMMENT"/><GUILayout><gpos v="11264 -3616 0"/><size v="160 160"/></GUILayout><GUIName v=""/><uid v="1485415750"/><title v="Rocks Medium"/><frameColor v="0 0.708333313 0.1328125 0.250980407"/><isTitleVisible v="1"/><isFrameVisible v="1"/></GUIObject><GUIObject><type v="COMMENT"/><GUILayout><gpos v="12320 -3680 0"/><size v="160 160"/></GUILayout><GUIName v=""/><uid v="1485415822"/><title v="Rocks Small"/><frameColor v="0 0.916666687 0.100260422 0.250980407"/><isTitleVisible v="1"/><isFrameVisible v="1"/></GUIObject><GUIObject><type v="COMMENT"/><GUILayout><gpos v="11808 -3520 0"/><size v="160 160"/></GUILayout><GUIName v=""/><uid v="1485415963"/><title v="Surface Intensity"/><frameColor v="0.208333328 0.833333313 0 0.250980407"/><isTitleVisible v="1"/><isFrameVisible v="1"/></GUIObject><GUIObject><type v="COMMENT"/><GUILayout><gpos v="12032 -3520 0"/><size v="160 160"/></GUILayout><GUIName v=""/><uid v="1485416007"/><title v="Rocks Curvature"/><frameColor v="0 0.833333313 0.0911458284 0.250980407"/><isTitleVisible v="1"/><isFrameVisible v="1"/></GUIObject><GUIObject><type v="COMMENT"/><GUILayout><gpos v="12064 -3712 0"/><size v="160 128"/></GUILayout><GUIName v=""/><uid v="1485416119"/><title v="Rocks Cavity"/><frameColor v="0 0.958333313 0.464192688 0.250980407"/><isTitleVisible v="1"/><isFrameVisible v="1"/></GUIObject><GUIObject><type v="COMMENT"/><GUILayout><gpos v="12320 -3488 0"/><size v="160 160"/></GUILayout><GUIName v=""/><uid v="1485416164"/><title v="Rocks Cavity Opacity"/><frameColor v="0 0.833333313 0.1171875 0.250980407"/><isTitleVisible v="1"/><isFrameVisible v="1"/></GUIObject><GUIObject><type v="COMMENT"/><GUILayout><gpos v="9568 -3488 0"/><size v="160 160"/></GUILayout><GUIName v=""/><uid v="1485416456"/><title v="Sand Dirt Opacity"/><frameColor v="0.109375 0.875 0 0.250980407"/><isTitleVisible v="1"/><isFrameVisible v="1"/></GUIObject><GUIObject><type v="COMMENT"/><GUILayout><gpos v="10453.333 -885.333313 -100"/><size v="128 128"/></GUILayout><GUIName v=""/><uid v="1485417250"/><title v="Rocks"/><frameColor v="0 0.791666687 0.272135437 0.250980407"/><isTitleVisible v="1"/><isFrameVisible v="1"/></GUIObject></GUIObjects><options><option><name v="export/fromGraph/autoExport"/><value v="true"/></option><option><name v="export/fromGraph/destination"/><value v="D:/work/Annery/stylized material/stylized material/images/craggy"/></option><option><name v="export/fromGraph/extension"/><value v="png"/></option><option><name v="export/fromGraph/outputs/AO"/><value v="true"/></option><option><name v="export/fromGraph/outputs/basecolor"/><value v="true"/></option><option><name v="export/fromGraph/outputs/height"/><value v="true"/></option><option><name v="export/fromGraph/outputs/metallic"/><value v="true"/></option><option><name v="export/fromGraph/outputs/normal"/><value v="true"/></option><option><name v="export/fromGraph/outputs/roughness"/><value v="true"/></option><option><name v="export/fromGraph/outputsColorspace/AO"/><value v="Raw"/></option><option><name v="export/fromGraph/outputsColorspace/basecolor"/><value v="sRGB"/></option><option><name v="export/fromGraph/outputsColorspace/height"/><value v="Raw"/></option><option><name v="export/fromGraph/outputsColorspace/metallic"/><value v="Raw"/></option><option><name v="export/fromGraph/outputsColorspace/normal"/><value v="Raw"/></option><option><name v="export/fromGraph/outputsColorspace/roughness"/><value v="Raw"/></option><option><name v="export/fromGraph/pattern"/><value v="$(graph)_$(identifier)"/></option></options><root><rootOutputs><rootOutput><output v="1448075773"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1448075775"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1448075777"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1448075779"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1448075781"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1448075783"/><format v="0"/><usertag v=""/></rootOutput></rootOutputs></root></graph></content></package>
