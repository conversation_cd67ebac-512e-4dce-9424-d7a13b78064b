<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<link rel="icon" href="../images/favicon.svg" />
		<link rel="icon" href="../images/favicon.ico" />
		<meta name="viewport" content="width=device-width, height=device-height, initial-scale=1">
		<meta property="og:type" content="website">
		<meta http-equiv="content-security-policy" content="">
		<link href="../_app/immutable/assets/_layout.21aea584.css" rel="stylesheet">
		<link href="../_app/immutable/assets/_layout.0154a58a.css" rel="stylesheet">
		
		
		
		
		
		
		
		
		
		
		
		
		<title>Grid Layout</title><!-- HEAD_svelte-i5aht6_START --><meta property="og:title" content="Grid Layout"><meta name="description" property="og:description" content="Flexalon lets Unity developers arrange objects in 3D layouts that are precise and easy to adjust. Use built-in interactions and animations to create powerful experiences."><meta name="image" property="og:image" content="../images/flexalon-1200x630.png"><meta name="twitter:card" content="summary_large_image"><meta name="twitter:title" content="Grid Layout"><meta name="twitter:site" content="@alon_farchy"><meta name="twitter:description" content="Flexalon lets Unity developers arrange objects in 3D layouts that are precise and easy to adjust. Use built-in interactions and animations to create powerful experiences."><meta name="twitter:image" content="../images/flexalon-1200x630.png"><!-- HEAD_svelte-i5aht6_END -->
	</head>
	<body>
		<div>


<div class="text-white font-body p-4 sticky top-0 bg-black flex gap-5 w-full text-gray-200 z-20 border-b-2 border-[#21a6f0]"><button class="lg:hidden"><svg width="24" height="24"><path d="M5 6h14M5 12h14M5 18h14" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"></path></svg></button>
    <a href="https://www.flexalon.com/" class="font-logo upper text-lg text-[#21a6f0] lg:pl-5 svelte-194nj6y">Flexalon Layouts</a></div>

<div class="w-full max-w-[1600px] lg:pl-10"><div class="flex w-full min-h-full text-zinc-400 relative"><div class="h-full fixed bg-black z-10 max-lg:hidden"><div class="flex flex-col w-[300px] gap-2 border-[#21a6f0] border-r-2 bg-black min-h-0 h-full pt-10 pb-36 overflow-auto max-lg:pl-10"><b class="text-zinc-200">Getting Started</b>
                <a href="../docs.html" class="svelte-194nj6y">Installation</a>
                <a href="../docs/coreConcepts.html" class="svelte-194nj6y">Core Concepts</a>
                <a href="../docs/ui.html" class="svelte-194nj6y">Flexalon UI <span class="ml-2 text-xs font-bold text-pink-300">(New in v4.0!)</span></a>
                <a href="../docs/templates.html" class="svelte-194nj6y">Template Pack</a>
                <a href="../docs/challenges.html" class="svelte-194nj6y">Challenges!</a>

                <a class="text-zinc-200 mt-5 font-bold svelte-194nj6y" href="../docs/flexalonObject.html">Flexalon Objects</a>
                <a href="../docs/flexalonObject.html#size" class="svelte-194nj6y">Width, Height, and Depth</a>
                <a href="../docs/flexalonObject.html#minmax" class="svelte-194nj6y">Min / Max Size<span class="ml-2 text-xs font-bold text-purple-400">(v4.1)</span></a>
                <a href="../docs/flexalonObject.html#margins" class="svelte-194nj6y">Margins and Padding</a>
                <a href="../docs/flexalonObject.html#offset" class="svelte-194nj6y">Offset, Scale, and Rotation</a>

                <b class="text-zinc-200 mt-5">Layouts</b>
                <a href="../docs/flexibleLayout.html" class="svelte-194nj6y">Flexible Layout</a>
                <a href="../docs/gridLayout.html" class="svelte-194nj6y">Grid Layout</a>
                <a href="../docs/circleLayout.html" class="svelte-194nj6y">Circle / Spiral Layout</a>
                <a href="../docs/curveLayout.html" class="svelte-194nj6y">Curve Layout</a>
                <a href="../docs/randomLayout.html" class="svelte-194nj6y">Random Layout</a>
                <a href="../docs/alignLayout.html" class="svelte-194nj6y">Align Layout</a>
                <a href="../docs/shapeLayout.html" class="svelte-194nj6y">Shape Layout</a>
                <a href="../docs/constraints.html" class="svelte-194nj6y">Constraints</a>
                <a href="../docs/randomModifier.html" class="svelte-194nj6y">Random Modifier</a>

                <a class="text-zinc-200 mt-5 font-bold svelte-194nj6y" href="../docs/animators.html">Animators</a>
                <a href="../docs/animators.html#curve" class="svelte-194nj6y">Curve Animator</a>
                <a href="../docs/animators.html#lerp" class="svelte-194nj6y">Lerp Animator</a>
                <a href="../docs/animators.html#rb" class="svelte-194nj6y">Rigid Body Animator</a>
                <a href="../docs/animators.html#layouts" class="svelte-194nj6y">Animating Layouts</a>

                <b class="text-zinc-200 mt-5">Click &amp; Drag</b>
                <a href="../docs/interactable.html" class="svelte-194nj6y">Interactable</a>
                <a href="../docs/interactable.html#dragTarget" class="svelte-194nj6y">Drag Target</a>
                <a href="../docs/xr.html" class="svelte-194nj6y">XR Interactions <span class="ml-2 text-xs font-bold text-blue-300">(v3.2)</span></a>
                <a href="../docs/interactable.html#customInputProvider" class="svelte-194nj6y">Custom Input Provider</a>

                <b class="text-zinc-200 mt-5">Data Binding</b>
                <a href="../docs/cloner.html" class="svelte-194nj6y">Cloner</a>
                <a href="../docs/cloner.html#databinding" class="svelte-194nj6y">Data Binding</a>

                <b class="text-zinc-200 mt-5">Advanced</b>
                <a href="../docs/pipeline.html" class="svelte-194nj6y">Flexalon Pipeline</a>
                <a href="../docs/customLayout.html" class="svelte-194nj6y">Custom Layouts</a>
                <a href="../docs/customAnimators.html" class="svelte-194nj6y">Custom Animators</a>
                <a href="../docs/adapters.html" class="svelte-194nj6y">Adapters</a>

                <a class="text-zinc-200 mt-5 font-bold svelte-194nj6y" href="../docs/api/Flexalon.html">Scripting API</a>
                <div class="mb-20"></div></div></div>
        
        <div class="flex flex-col w-full p-10 lg:ml-[300px]"><div class="doc svelte-194nj6y">

<div class="text-white text-2xl mb-10">Grid Layout</div>

<p>Use a grid layout to position children at fixed intervals. Objects are placed in cells in <b>column-row-layer</b> order.</p>
<div class="flex ml-10 m-5 mb-10"><img src="../images/doc-grid.png" alt="Grid layout"></div>

<p class="text-xl mt-10">Grid Layout Options</p>
<div class="flex ml-10 m-5 mb-10"><img src="../images/doc-grid-options.png" alt="Grid layout options"></div>
<table><tr><td>Cell Type</td>
        <td>Rectangular or Hexagonal cells.</td></tr>
    <tr><td>Columns</td>
        <td>Number of columns.</td></tr>
    <tr><td>Rows</td>
        <td>Number of rows.</td></tr>
    <tr><td>Layers <span class="m-2 text-xs font-bold text-pink-400">(v3.0)</span></td>
        <td>Number of layers.</td></tr>
    <tr><td>Column Direction</td>
        <td>Direction of the columns. Children are placed in column-row-layer order.</td></tr>
    <tr><td>Row Direction</td>
        <td>Direction of the rows. Children are placed in column-row-layer order.</td></tr>
    <tr><td>Layer Direction <span class="m-2 text-xs font-bold text-pink-400">(v3.0)</span></td>
        <td>Direction of the layers. Children are placed in column-row-layer order.</td></tr>
    <tr><td>Column Size <span class="m-2 text-xs font-bold text-pink-400">(v3.0)</span></td>
        <td>Determines the column size of the cell. If set to Fill, the object size is divided by the number of columns.</td></tr>
    <tr><td>Row Size <span class="ml-2 text-xs font-bold text-pink-400">(v3.0)</span></td>
        <td>Determines the row size of the cell. If set to Fill, the object size is divided by the number of rows.</td></tr>
    <tr><td>Layer Size <span class="ml-2 text-xs font-bold text-pink-400">(v3.0)</span></td>
        <td>Determines the layer size of the cell. If set to Fill, the object size is divided by the number of layers.</td></tr>
    <tr><td>Column Spacing</td>
        <td>Adds empty space between columns.</td></tr>
    <tr><td>Row Spacing</td>
        <td>Adds empty space between rows.</td></tr>
    <tr><td>Layer Spacing <span class="m-2 text-xs font-bold text-pink-400">(v3.0)</span></td>
        <td>Adds empty space between layers.</td></tr>
    <tr><td>Align</td>
        <td>Aligns each item within its cell.</td></tr></table>

<p class="text-xl mt-10">Flexalon Grid Cell <span class="ml-2 text-xs font-bold text-pink-400">(v3.0)</span></p>
<p>Normally, Flexalon Grid Layout will position children in column-row order. If you want to specify which cell a child should occupy,
    you can add a <b>Flexalon Grid Cell</b> component. This child will be skipped when positioning children in column-row order.
    The Grid Cell component can also allow multiple children to occupy the same cell.
</p></div></div></div>
</div>


			
			
		</div>
	</body>
</html>
