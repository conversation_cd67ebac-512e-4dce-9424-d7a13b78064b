%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &101456529138992981
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8435588430071067673}
  m_Layer: 0
  m_Name: upperleg.r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8435588430071067673
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 101456529138992981}
  m_LocalRotation: {x: 0.9998491, y: -0.00000007426609, z: 0.00000003172626, w: 0.017373588}
  m_LocalPosition: {x: 0.17094506, y: 0.11358726, z: 0.000000013923272}
  m_LocalScale: {x: 1, y: 0.99999976, z: 1.0000011}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4195780095517961237}
  m_Father: {fileID: 7958641572909903515}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &340784406388562736
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1148810637686079882}
  - component: {fileID: 6417992190534212592}
  m_Layer: 0
  m_Name: Skeleton
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1148810637686079882
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 340784406388562736}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3485984968507346849}
  - {fileID: 3635084287642562088}
  - {fileID: 7566205932244560705}
  - {fileID: 3832458110705105065}
  - {fileID: 6306642032789124042}
  - {fileID: 5089336202988804993}
  - {fileID: 7244070426300131497}
  - {fileID: 2363567631713998136}
  - {fileID: 155345000074206606}
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!95 &6417992190534212592
Animator:
  serializedVersion: 5
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 340784406388562736}
  m_Enabled: 1
  m_Avatar: {fileID: 9000000, guid: 6b417d114ba28c84aafa17634078e33c, type: 3}
  m_Controller: {fileID: 9100000, guid: 4a00167a617ae884f9a2d5e5def3cd7a, type: 2}
  m_CullingMode: 1
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!1 &351010796786549318
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9091163045023956537}
  m_Layer: 0
  m_Name: root
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9091163045023956537
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 351010796786549318}
  m_LocalRotation: {x: -0.000000021855692, y: -0.000000119209275, z: 2.6054013e-15, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4674339227767886027}
  - {fileID: 1007877658666005422}
  - {fileID: 8432469221394114824}
  - {fileID: 5815351910490631401}
  - {fileID: 4432709933453782320}
  - {fileID: 2142372919606153743}
  - {fileID: 7958641572909903515}
  - {fileID: 6821736451461349674}
  - {fileID: 7242484880559672717}
  m_Father: {fileID: 3485984968507346849}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &379546427861062371
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4604595600572607194}
  m_Layer: 0
  m_Name: chest
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4604595600572607194
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 379546427861062371}
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 3.69611e-29, y: 0.37498796, z: 1.7763568e-15}
  m_LocalScale: {x: 1, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4341938192780654018}
  - {fileID: 4652599469352068687}
  - {fileID: 8657769629828879697}
  - {fileID: 8737808306444604344}
  m_Father: {fileID: 8171633537370485388}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &565976380293514473
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4710253140000276070}
  m_Layer: 0
  m_Name: control-heel-roll.l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4710253140000276070
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 565976380293514473}
  m_LocalRotation: {x: -7.1054375e-15, y: -1.00916035e-22, z: 1, w: -6.123234e-17}
  m_LocalPosition: {x: -0.00000001945811, y: 0.36283267, z: -0.000000019596568}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2639685787003968554}
  - {fileID: 9207964391755388661}
  m_Father: {fileID: 4674339227767886027}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &654322510912906252
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2943670409170553231}
  m_Layer: 0
  m_Name: heelIK.r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2943670409170553231
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 654322510912906252}
  m_LocalRotation: {x: -0.39108443, y: 0.000000068850035, z: 0.00000006562883, w: 0.92035484}
  m_LocalPosition: {x: -0.00000000858988, y: 0.16565005, z: 0.0000000013660788}
  m_LocalScale: {x: 1, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7761077288355988}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &698588097573080451
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1755227705811107991}
  m_Layer: 0
  m_Name: hand.l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1755227705811107991
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 698588097573080451}
  m_LocalRotation: {x: 5.9566346e-10, y: -0.00000013300505, z: 0.000005455195, w: 1}
  m_LocalPosition: {x: -1.2079227e-12, y: 0.073825896, z: 0.00000011935734}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1585858863607960597}
  m_Father: {fileID: 5203902218573427349}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &722400649340907053
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2639685787003968554}
  m_Layer: 0
  m_Name: control-foot-roll.l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2639685787003968554
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 722400649340907053}
  m_LocalRotation: {x: 0.00000012957192, y: 0.39108437, z: 0.92035484, w: 0.0000000896225}
  m_LocalPosition: {x: -0.0000000037529446, y: 0.21343614, z: 0.000000003538337}
  m_LocalScale: {x: 1, y: 1.0000002, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: ************2495030}
  - {fileID: 7552358146636554425}
  m_Father: {fileID: 4710253140000276070}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &796839699719321365
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: ************2495030}
  m_Layer: 0
  m_Name: heelIK.l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &************2495030
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 796839699719321365}
  m_LocalRotation: {x: -0.39108443, y: 0.00000006885003, z: 0.00000006562883, w: 0.92035484}
  m_LocalPosition: {x: 0.00000002121244, y: 0.16565005, z: -4.3475623e-10}
  m_LocalScale: {x: 1, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2639685787003968554}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &819657748552230962
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5203902218573427349}
  m_Layer: 0
  m_Name: wrist.l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5203902218573427349
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 819657748552230962}
  m_LocalRotation: {x: 0.0000000010654014, y: 2.6744193e-10, z: -0.026658101, w: 0.99964464}
  m_LocalPosition: {x: -0.0000000029717881, y: 0.26004383, z: 0.0000000016429202}
  m_LocalScale: {x: 0.99999994, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1755227705811107991}
  m_Father: {fileID: 7261989691374450576}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &949209915801421089
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2910101817788475654}
  m_Layer: 0
  m_Name: toes.l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2910101817788475654
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 949209915801421089}
  m_LocalRotation: {x: -0.0000000625307, y: 0.9203549, z: -0.39108422, w: -0.00000011811018}
  m_LocalPosition: {x: -1.05472964e-10, y: 0.16565004, z: 0.000000038133116}
  m_LocalScale: {x: 1, y: 0.9999977, z: 1.0000033}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8175108708189361675}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &971531806309288238
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1585858863607960597}
  m_Layer: 0
  m_Name: handslot.l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1585858863607960597
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 971531806309288238}
  m_LocalRotation: {x: -0.0000000119789805, y: -0.000000015198488, z: 0.7071068, w: 0.7071067}
  m_LocalPosition: {x: -8.419292e-10, y: 0.096125245, z: -0.057499886}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1755227705811107991}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1098897880437573490
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7242484880559672717}
  m_Layer: 0
  m_Name: kneeIK.r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7242484880559672717
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1098897880437573490}
  m_LocalRotation: {x: 0.7071068, y: 0.000000038238493, z: -0.00000014500868, w: 0.7071067}
  m_LocalPosition: {x: 0.17094517, y: 0.29231027, z: 0.57581234}
  m_LocalScale: {x: 1, y: 0.99999994, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 9091163045023956537}
  m_RootOrder: 8
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1177676539478395090
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7244070426300131497}
  - component: {fileID: 3051230385353312764}
  m_Layer: 0
  m_Name: Skeleton_Rogue_Jaw
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7244070426300131497
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1177676539478395090}
  m_LocalRotation: {x: 0.000000021855694, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: -0.000005722046, y: 1.3115311, z: -0.032665536}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1148810637686079882}
  m_RootOrder: 6
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &3051230385353312764
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1177676539478395090}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 3af9a29e73f330045a88f5cbcabbdc2f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: -1138243361108649023, guid: 6b417d114ba28c84aafa17634078e33c, type: 3}
  m_Bones:
  - {fileID: 9091163045023956537}
  - {fileID: 7958641572909903515}
  - {fileID: 8171633537370485388}
  - {fileID: 4604595600572607194}
  - {fileID: 8657769629828879697}
  - {fileID: 7261989691374450576}
  - {fileID: 5203902218573427349}
  - {fileID: 1755227705811107991}
  - {fileID: 1585858863607960597}
  - {fileID: 8737808306444604344}
  - {fileID: 827639197856478921}
  - {fileID: 743764369915614746}
  - {fileID: 4219289444781325925}
  - {fileID: 7387511971328698703}
  - {fileID: 4341938192780654018}
  - {fileID: 1073180046605342055}
  - {fileID: 6871212718273157075}
  - {fileID: 8175108708189361675}
  - {fileID: 2910101817788475654}
  - {fileID: 8435588430071067673}
  - {fileID: 4195780095517961237}
  - {fileID: 9202000106483099298}
  - {fileID: 1077937534433769719}
  - {fileID: 6821736451461349674}
  - {fileID: 4674339227767886027}
  - {fileID: 4710253140000276070}
  - {fileID: 2639685787003968554}
  - {fileID: ************2495030}
  - {fileID: 7552358146636554425}
  - {fileID: 9207964391755388661}
  - {fileID: 7242484880559672717}
  - {fileID: 1007877658666005422}
  - {fileID: 4517774784669809633}
  - {fileID: 7761077288355988}
  - {fileID: 2943670409170553231}
  - {fileID: 7043568085372433949}
  - {fileID: 3944353398667568260}
  - {fileID: 8432469221394114824}
  - {fileID: 4432709933453782320}
  - {fileID: 5815351910490631401}
  - {fileID: 2142372919606153743}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 9091163045023956537}
  m_AABB:
    m_Center: {x: -0.0000056922436, y: 1.3548601, z: 0.11976354}
    m_Extent: {x: 0.22748968, y: 0.16156507, z: 0.20360751}
  m_DirtyAABB: 0
--- !u!1 &2030691747811791804
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5415394811044864824}
  - component: {fileID: 6228156984724474510}
  - component: {fileID: 4316387878395735687}
  m_Layer: 0
  m_Name: Skeleton_Rogue_Hood
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5415394811044864824
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2030691747811791804}
  m_LocalRotation: {x: 0.000000021855692, y: 7.1054274e-15, z: -1.5529402e-22, w: 1}
  m_LocalPosition: {x: -0.0000019073486, y: -0.025552154, z: -0.00000013783573}
  m_LocalScale: {x: 1, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4341938192780654018}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &6228156984724474510
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2030691747811791804}
  m_Mesh: {fileID: 6087569168993494485, guid: 6b417d114ba28c84aafa17634078e33c, type: 3}
--- !u!23 &4316387878395735687
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2030691747811791804}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 3af9a29e73f330045a88f5cbcabbdc2f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &2090903944132097174
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3635084287642562088}
  - component: {fileID: 8591139543785910100}
  m_Layer: 0
  m_Name: Skeleton_Rogue_ArmLeft
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3635084287642562088
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2090903944132097174}
  m_LocalRotation: {x: 0.000000021855694, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1148810637686079882}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &8591139543785910100
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2090903944132097174}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 3af9a29e73f330045a88f5cbcabbdc2f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: -1267527682260398228, guid: 6b417d114ba28c84aafa17634078e33c, type: 3}
  m_Bones:
  - {fileID: 9091163045023956537}
  - {fileID: 7958641572909903515}
  - {fileID: 8171633537370485388}
  - {fileID: 4604595600572607194}
  - {fileID: 8657769629828879697}
  - {fileID: 7261989691374450576}
  - {fileID: 5203902218573427349}
  - {fileID: 1755227705811107991}
  - {fileID: 1585858863607960597}
  - {fileID: 8737808306444604344}
  - {fileID: 827639197856478921}
  - {fileID: 743764369915614746}
  - {fileID: 4219289444781325925}
  - {fileID: 7387511971328698703}
  - {fileID: 4341938192780654018}
  - {fileID: 1073180046605342055}
  - {fileID: 6871212718273157075}
  - {fileID: 8175108708189361675}
  - {fileID: 2910101817788475654}
  - {fileID: 8435588430071067673}
  - {fileID: 4195780095517961237}
  - {fileID: 9202000106483099298}
  - {fileID: 1077937534433769719}
  - {fileID: 6821736451461349674}
  - {fileID: 4674339227767886027}
  - {fileID: 4710253140000276070}
  - {fileID: 2639685787003968554}
  - {fileID: ************2495030}
  - {fileID: 7552358146636554425}
  - {fileID: 9207964391755388661}
  - {fileID: 7242484880559672717}
  - {fileID: 1007877658666005422}
  - {fileID: 4517774784669809633}
  - {fileID: 7761077288355988}
  - {fileID: 2943670409170553231}
  - {fileID: 7043568085372433949}
  - {fileID: 3944353398667568260}
  - {fileID: 8432469221394114824}
  - {fileID: 4432709933453782320}
  - {fileID: 5815351910490631401}
  - {fileID: 2142372919606153743}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 9091163045023956537}
  m_AABB:
    m_Center: {x: -0.5505473, y: 1.1007043, z: -0.0061443485}
    m_Extent: {x: 0.42070603, y: 0.116155595, z: 0.13109195}
  m_DirtyAABB: 0
--- !u!1 &2272006946428080863
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7387511971328698703}
  m_Layer: 0
  m_Name: handslot.r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7387511971328698703
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2272006946428080863}
  m_LocalRotation: {x: -0.000000011979, y: 0.000000015198468, z: -0.7071068, w: 0.7071067}
  m_LocalPosition: {x: 8.4187235e-10, y: 0.096125245, z: -0.057499886}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4219289444781325925}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2497095855055453325
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7566205932244560705}
  - component: {fileID: 2987463399215469976}
  m_Layer: 0
  m_Name: Skeleton_Rogue_ArmRight
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7566205932244560705
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2497095855055453325}
  m_LocalRotation: {x: 0.000000021855694, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1148810637686079882}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &2987463399215469976
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2497095855055453325}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 3af9a29e73f330045a88f5cbcabbdc2f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: -7899652447604561854, guid: 6b417d114ba28c84aafa17634078e33c, type: 3}
  m_Bones:
  - {fileID: 9091163045023956537}
  - {fileID: 7958641572909903515}
  - {fileID: 8171633537370485388}
  - {fileID: 4604595600572607194}
  - {fileID: 8657769629828879697}
  - {fileID: 7261989691374450576}
  - {fileID: 5203902218573427349}
  - {fileID: 1755227705811107991}
  - {fileID: 1585858863607960597}
  - {fileID: 8737808306444604344}
  - {fileID: 827639197856478921}
  - {fileID: 743764369915614746}
  - {fileID: 4219289444781325925}
  - {fileID: 7387511971328698703}
  - {fileID: 4341938192780654018}
  - {fileID: 1073180046605342055}
  - {fileID: 6871212718273157075}
  - {fileID: 8175108708189361675}
  - {fileID: 2910101817788475654}
  - {fileID: 8435588430071067673}
  - {fileID: 4195780095517961237}
  - {fileID: 9202000106483099298}
  - {fileID: 1077937534433769719}
  - {fileID: 6821736451461349674}
  - {fileID: 4674339227767886027}
  - {fileID: 4710253140000276070}
  - {fileID: 2639685787003968554}
  - {fileID: ************2495030}
  - {fileID: 7552358146636554425}
  - {fileID: 9207964391755388661}
  - {fileID: 7242484880559672717}
  - {fileID: 1007877658666005422}
  - {fileID: 4517774784669809633}
  - {fileID: 7761077288355988}
  - {fileID: 2943670409170553231}
  - {fileID: 7043568085372433949}
  - {fileID: 3944353398667568260}
  - {fileID: 8432469221394114824}
  - {fileID: 4432709933453782320}
  - {fileID: 5815351910490631401}
  - {fileID: 2142372919606153743}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 9091163045023956537}
  m_AABB:
    m_Center: {x: 0.55054724, y: 1.1007048, z: -0.0061447024}
    m_Extent: {x: 0.4207059, y: 0.11615506, z: 0.13109186}
  m_DirtyAABB: 0
--- !u!1 &2619432822524302690
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6306642032789124042}
  - component: {fileID: 1413607382783060655}
  m_Layer: 0
  m_Name: Skeleton_Rogue_Eyes
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6306642032789124042
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2619432822524302690}
  m_LocalRotation: {x: 0.000000021855694, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: -0.000005722046, y: 1.2158723, z: -0.0000001359731}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1148810637686079882}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &1413607382783060655
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2619432822524302690}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 691f9252e26d3c84d86e4b80e9ef07f9, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 6198906313763841654, guid: 6b417d114ba28c84aafa17634078e33c, type: 3}
  m_Bones:
  - {fileID: 9091163045023956537}
  - {fileID: 7958641572909903515}
  - {fileID: 8171633537370485388}
  - {fileID: 4604595600572607194}
  - {fileID: 8657769629828879697}
  - {fileID: 7261989691374450576}
  - {fileID: 5203902218573427349}
  - {fileID: 1755227705811107991}
  - {fileID: 1585858863607960597}
  - {fileID: 8737808306444604344}
  - {fileID: 827639197856478921}
  - {fileID: 743764369915614746}
  - {fileID: 4219289444781325925}
  - {fileID: 7387511971328698703}
  - {fileID: 4341938192780654018}
  - {fileID: 1073180046605342055}
  - {fileID: 6871212718273157075}
  - {fileID: 8175108708189361675}
  - {fileID: 2910101817788475654}
  - {fileID: 8435588430071067673}
  - {fileID: 4195780095517961237}
  - {fileID: 9202000106483099298}
  - {fileID: 1077937534433769719}
  - {fileID: 6821736451461349674}
  - {fileID: 4674339227767886027}
  - {fileID: 4710253140000276070}
  - {fileID: 2639685787003968554}
  - {fileID: ************2495030}
  - {fileID: 7552358146636554425}
  - {fileID: 9207964391755388661}
  - {fileID: 7242484880559672717}
  - {fileID: 1007877658666005422}
  - {fileID: 4517774784669809633}
  - {fileID: 7761077288355988}
  - {fileID: 2943670409170553231}
  - {fileID: 7043568085372433949}
  - {fileID: 3944353398667568260}
  - {fileID: 8432469221394114824}
  - {fileID: 4432709933453782320}
  - {fileID: 5815351910490631401}
  - {fileID: 2142372919606153743}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 9091163045023956537}
  m_AABB:
    m_Center: {x: -0.0000056773424, y: 1.6409333, z: 0.26019937}
    m_Extent: {x: 0.22978659, y: 0.063201904, z: 0.03776092}
  m_DirtyAABB: 0
--- !u!1 &2839688771839805001
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4341938192780654018}
  m_Layer: 0
  m_Name: head
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4341938192780654018
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2839688771839805001}
  m_LocalRotation: {x: -3.5527137e-15, y: 0, z: 5.0487104e-29, w: 1}
  m_LocalPosition: {x: 1.17175564e-29, y: 0.26879656, z: -5.51e-43}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5415394811044864824}
  m_Father: {fileID: 4604595600572607194}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3075351142020090512
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9207964391755388661}
  m_Layer: 0
  m_Name: IK-toe.l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9207964391755388661
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3075351142020090512}
  m_LocalRotation: {x: 0.00000005860104, y: 1.2081922e-15, z: 0.000000019466935, w: 1}
  m_LocalPosition: {x: 0.000000004556949, y: -0.0000000027572362, z: -0.000000019596566}
  m_LocalScale: {x: 1, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4710253140000276070}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3274986523402380999
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3485984968507346849}
  m_Layer: 0
  m_Name: Rig
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3485984968507346849
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3274986523402380999}
  m_LocalRotation: {x: 0.000000021855694, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 9091163045023956537}
  m_Father: {fileID: 1148810637686079882}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3315466597095665958
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2142372919606153743}
  m_Layer: 0
  m_Name: handIK.r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2142372919606153743
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3315466597095665958}
  m_LocalRotation: {x: 0.50000006, y: 0.49999994, z: 0.5, w: -0.5}
  m_LocalPosition: {x: 0.71318156, y: 1.1067611, z: -0.00000008515731}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 9091163045023956537}
  m_RootOrder: 5
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3388257130329469814
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2363567631713998136}
  - component: {fileID: 7111491784618808286}
  m_Layer: 0
  m_Name: Skeleton_Rogue_LegLeft
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2363567631713998136
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3388257130329469814}
  m_LocalRotation: {x: 0.000000021855694, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1148810637686079882}
  m_RootOrder: 7
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &7111491784618808286
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3388257130329469814}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 3af9a29e73f330045a88f5cbcabbdc2f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: -2979936350486113118, guid: 6b417d114ba28c84aafa17634078e33c, type: 3}
  m_Bones:
  - {fileID: 9091163045023956537}
  - {fileID: 7958641572909903515}
  - {fileID: 8171633537370485388}
  - {fileID: 4604595600572607194}
  - {fileID: 8657769629828879697}
  - {fileID: 7261989691374450576}
  - {fileID: 5203902218573427349}
  - {fileID: 1755227705811107991}
  - {fileID: 1585858863607960597}
  - {fileID: 8737808306444604344}
  - {fileID: 827639197856478921}
  - {fileID: 743764369915614746}
  - {fileID: 4219289444781325925}
  - {fileID: 7387511971328698703}
  - {fileID: 4341938192780654018}
  - {fileID: 1073180046605342055}
  - {fileID: 6871212718273157075}
  - {fileID: 8175108708189361675}
  - {fileID: 2910101817788475654}
  - {fileID: 8435588430071067673}
  - {fileID: 4195780095517961237}
  - {fileID: 9202000106483099298}
  - {fileID: 1077937534433769719}
  - {fileID: 6821736451461349674}
  - {fileID: 4674339227767886027}
  - {fileID: 4710253140000276070}
  - {fileID: 2639685787003968554}
  - {fileID: ************2495030}
  - {fileID: 7552358146636554425}
  - {fileID: 9207964391755388661}
  - {fileID: 7242484880559672717}
  - {fileID: 1007877658666005422}
  - {fileID: 4517774784669809633}
  - {fileID: 7761077288355988}
  - {fileID: 2943670409170553231}
  - {fileID: 7043568085372433949}
  - {fileID: 3944353398667568260}
  - {fileID: 8432469221394114824}
  - {fileID: 4432709933453782320}
  - {fileID: 5815351910490631401}
  - {fileID: 2142372919606153743}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 9091163045023956537}
  m_AABB:
    m_Center: {x: -0.167844, y: 0.16256839, z: 0.07535869}
    m_Extent: {x: 0.1309245, y: 0.37478346, z: 0.2933068}
  m_DirtyAABB: 0
--- !u!1 &3489642922206221179
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9202000106483099298}
  m_Layer: 0
  m_Name: foot.r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9202000106483099298
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3489642922206221179}
  m_LocalRotation: {x: -0.45523942, y: -0.00000015576714, z: 0.000000017729546, w: 0.8903691}
  m_LocalPosition: {x: -0.000000005432452, y: 0.14943695, z: 8.4287777e-10}
  m_LocalScale: {x: 1, y: 1.0000005, z: 0.999999}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1077937534433769719}
  m_Father: {fileID: 4195780095517961237}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3605228657223924549
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7958641572909903515}
  m_Layer: 0
  m_Name: hips
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7958641572909903515
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3605228657223924549}
  m_LocalRotation: {x: -5.3290705e-15, y: 0.000000119209275, z: 6.3527476e-22, w: 1}
  m_LocalPosition: {x: -0, y: 0.40566343, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8171633537370485388}
  - {fileID: 1073180046605342055}
  - {fileID: 8435588430071067673}
  m_Father: {fileID: 9091163045023956537}
  m_RootOrder: 6
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4200294843785553233
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4674339227767886027}
  m_Layer: 0
  m_Name: control-toe-roll.l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4674339227767886027
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4200294843785553233}
  m_LocalRotation: {x: -0.7071067, y: 0.000000070528486, z: 0.00000007052848, w: 0.7071068}
  m_LocalPosition: {x: -0.17094497, y: 0.025990263, z: 0.24578938}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4710253140000276070}
  m_Father: {fileID: 9091163045023956537}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4445340197582754569
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 155345000074206606}
  - component: {fileID: 1596737629886347025}
  m_Layer: 0
  m_Name: Skeleton_Rogue_LegRight
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &155345000074206606
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4445340197582754569}
  m_LocalRotation: {x: 0.000000021855694, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1148810637686079882}
  m_RootOrder: 8
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &1596737629886347025
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4445340197582754569}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 3af9a29e73f330045a88f5cbcabbdc2f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: -7795427817919744074, guid: 6b417d114ba28c84aafa17634078e33c, type: 3}
  m_Bones:
  - {fileID: 9091163045023956537}
  - {fileID: 7958641572909903515}
  - {fileID: 8171633537370485388}
  - {fileID: 4604595600572607194}
  - {fileID: 8657769629828879697}
  - {fileID: 7261989691374450576}
  - {fileID: 5203902218573427349}
  - {fileID: 1755227705811107991}
  - {fileID: 1585858863607960597}
  - {fileID: 8737808306444604344}
  - {fileID: 827639197856478921}
  - {fileID: 743764369915614746}
  - {fileID: 4219289444781325925}
  - {fileID: 7387511971328698703}
  - {fileID: 4341938192780654018}
  - {fileID: 1073180046605342055}
  - {fileID: 6871212718273157075}
  - {fileID: 8175108708189361675}
  - {fileID: 2910101817788475654}
  - {fileID: 8435588430071067673}
  - {fileID: 4195780095517961237}
  - {fileID: 9202000106483099298}
  - {fileID: 1077937534433769719}
  - {fileID: 6821736451461349674}
  - {fileID: 4674339227767886027}
  - {fileID: 4710253140000276070}
  - {fileID: 2639685787003968554}
  - {fileID: ************2495030}
  - {fileID: 7552358146636554425}
  - {fileID: 9207964391755388661}
  - {fileID: 7242484880559672717}
  - {fileID: 1007877658666005422}
  - {fileID: 4517774784669809633}
  - {fileID: 7761077288355988}
  - {fileID: 2943670409170553231}
  - {fileID: 7043568085372433949}
  - {fileID: 3944353398667568260}
  - {fileID: 8432469221394114824}
  - {fileID: 4432709933453782320}
  - {fileID: 5815351910490631401}
  - {fileID: 2142372919606153743}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 9091163045023956537}
  m_AABB:
    m_Center: {x: 0.16784406, y: 0.16256851, z: 0.07535857}
    m_Extent: {x: 0.13092446, y: 0.37478334, z: 0.29330665}
  m_DirtyAABB: 0
--- !u!1 &4503567587789744010
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4432709933453782320}
  m_Layer: 0
  m_Name: handIK.l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4432709933453782320
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4503567587789744010}
  m_LocalRotation: {x: -0.50000006, y: 0.49999997, z: 0.49999997, w: 0.50000006}
  m_LocalPosition: {x: -0.71318156, y: 1.1067611, z: 0.00000025491414}
  m_LocalScale: {x: 0.9999999, y: 0.99999976, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 9091163045023956537}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4666816659797017388
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 743764369915614746}
  m_Layer: 0
  m_Name: wrist.r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &743764369915614746
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4666816659797017388}
  m_LocalRotation: {x: 0.0000000010654018, y: -2.6743324e-10, z: 0.026658101, w: 0.99964464}
  m_LocalPosition: {x: 0.0000000030516532, y: 0.26004383, z: 0.0000000016429189}
  m_LocalScale: {x: 0.99999994, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4219289444781325925}
  m_Father: {fileID: 827639197856478921}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4891277021461658833
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6871212718273157075}
  m_Layer: 0
  m_Name: lowerleg.l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6871212718273157075
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4891277021461658833}
  m_LocalRotation: {x: 0.10622619, y: -0.000000068652874, z: 0.00000007377092, w: 0.994342}
  m_LocalPosition: {x: -0.000000017618225, y: 0.22707745, z: -0.0000000026764515}
  m_LocalScale: {x: 1, y: 0.9999998, z: 0.99999964}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8175108708189361675}
  m_Father: {fileID: 1073180046605342055}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5244812306800162221
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7761077288355988}
  m_Layer: 0
  m_Name: control-foot-roll.r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7761077288355988
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5244812306800162221}
  m_LocalRotation: {x: 0.00000012957192, y: 0.39108437, z: 0.92035484, w: 0.0000000896225}
  m_LocalPosition: {x: -0.000000003752945, y: 0.21343614, z: 0.0000000035383365}
  m_LocalScale: {x: 1, y: 1.0000002, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2943670409170553231}
  - {fileID: 7043568085372433949}
  m_Father: {fileID: 4517774784669809633}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5340066542913077294
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8432469221394114824}
  m_Layer: 0
  m_Name: elbowIK.l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8432469221394114824
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5340066542913077294}
  m_LocalRotation: {x: -0.7071067, y: 0.0000000727951, z: 0.00000007279507, w: 0.7071069}
  m_LocalPosition: {x: -0.45350748, y: 1.1067611, z: -0.58885944}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 9091163045023956537}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5373671212236614626
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8737808306444604344}
  m_Layer: 0
  m_Name: upperarm.r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8737808306444604344
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5373671212236614626}
  m_LocalRotation: {x: -0.5141217, y: -0.48546743, z: -0.48546806, w: 0.5141216}
  m_LocalPosition: {x: 0.21200736, y: 0.13413227, z: 0.00000008487841}
  m_LocalScale: {x: 0.99999934, y: 0.99999934, z: 0.9999998}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 827639197856478921}
  m_Father: {fileID: 4604595600572607194}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5549439846751305747
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 827639197856478921}
  m_Layer: 0
  m_Name: lowerarm.r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &827639197856478921
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5549439846751305747}
  m_LocalRotation: {x: 4.8521726e-10, y: -0.0000000594392, z: -0.055285525, w: 0.9984706}
  m_LocalPosition: {x: -2.1567625e-10, y: 0.24189739, z: -0.00000011685303}
  m_LocalScale: {x: 1.0000001, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 743764369915614746}
  m_Father: {fileID: 8737808306444604344}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5585443194903676591
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4517774784669809633}
  m_Layer: 0
  m_Name: control-heel-roll.r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4517774784669809633
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5585443194903676591}
  m_LocalRotation: {x: -7.1054375e-15, y: -1.00916035e-22, z: 1, w: -6.123234e-17}
  m_LocalPosition: {x: -0.00000001945811, y: 0.36283267, z: -0.000000019596564}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7761077288355988}
  - {fileID: 3944353398667568260}
  m_Father: {fileID: 1007877658666005422}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5730207955932061479
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6821736451461349674}
  m_Layer: 0
  m_Name: kneeIK.l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6821736451461349674
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5730207955932061479}
  m_LocalRotation: {x: 0.7071068, y: 0.00000022930232, z: 0.000000046055135, w: 0.7071067}
  m_LocalPosition: {x: -0.1709449, y: 0.29231027, z: 0.57581246}
  m_LocalScale: {x: 1, y: 0.99999994, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 9091163045023956537}
  m_RootOrder: 7
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5839428571484875066
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8175108708189361675}
  m_Layer: 0
  m_Name: foot.l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8175108708189361675
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5839428571484875066}
  m_LocalRotation: {x: -0.45523942, y: -0.000000016196793, z: 0.00000003435627, w: 0.8903691}
  m_LocalPosition: {x: 0.0000000031714003, y: 0.14943705, z: 0.0000000027323868}
  m_LocalScale: {x: 1, y: 1.0000008, z: 0.9999996}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2910101817788475654}
  m_Father: {fileID: 6871212718273157075}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5963602658704629257
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7043568085372433949}
  m_Layer: 0
  m_Name: IK-foot.r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7043568085372433949
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5963602658704629257}
  m_LocalRotation: {x: 1, y: 4.6537017e-14, z: -0.00000019470718, w: 0.00000013315805}
  m_LocalPosition: {x: -0.00000000858988, y: 0.16565005, z: 0.0000000013660788}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7761077288355988}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6021874985144943606
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8171633537370485388}
  m_Layer: 0
  m_Name: spine
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8171633537370485388
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6021874985144943606}
  m_LocalRotation: {x: 5.3290705e-15, y: -7.1054274e-15, z: -1.3883952e-28, w: 1}
  m_LocalPosition: {x: 2.35e-43, y: 0.1919775, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4604595600572607194}
  m_Father: {fileID: 7958641572909903515}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6134578152419624711
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7261989691374450576}
  m_Layer: 0
  m_Name: lowerarm.l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7261989691374450576
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6134578152419624711}
  m_LocalRotation: {x: 4.8521726e-10, y: 0.0000000594392, z: 0.055285525, w: 0.9984706}
  m_LocalPosition: {x: 3.169376e-10, y: 0.24189739, z: -0.00000011563963}
  m_LocalScale: {x: 1.0000001, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5203902218573427349}
  m_Father: {fileID: 8657769629828879697}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6547569746592316255
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4652599469352068687}
  - component: {fileID: 8524940743031268823}
  - component: {fileID: 8835633092461910382}
  m_Layer: 0
  m_Name: Skeleton_Rogue_Cape
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &4652599469352068687
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6547569746592316255}
  m_LocalRotation: {x: 0.000000021855692, y: 7.1054274e-15, z: -1.5529402e-22, w: 1}
  m_LocalPosition: {x: 0.0000019073486, y: 0.24324435, z: -0.00000013411045}
  m_LocalScale: {x: 1, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4604595600572607194}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &8524940743031268823
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6547569746592316255}
  m_Mesh: {fileID: -8103023216118402382, guid: 6b417d114ba28c84aafa17634078e33c, type: 3}
--- !u!23 &8835633092461910382
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6547569746592316255}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 3af9a29e73f330045a88f5cbcabbdc2f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &6847852897993666883
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1007877658666005422}
  m_Layer: 0
  m_Name: control-toe-roll.r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1007877658666005422
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6847852897993666883}
  m_LocalRotation: {x: -0.7071067, y: 0.000000070528486, z: 0.00000007052848, w: 0.7071068}
  m_LocalPosition: {x: 0.1709451, y: 0.025990263, z: 0.2457893}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4517774784669809633}
  m_Father: {fileID: 9091163045023956537}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6996950003412666313
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3832458110705105065}
  - component: {fileID: 8774998977662057676}
  m_Layer: 0
  m_Name: Skeleton_Rogue_Body
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3832458110705105065
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6996950003412666313}
  m_LocalRotation: {x: 0.000000021855694, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1148810637686079882}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &8774998977662057676
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6996950003412666313}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 3af9a29e73f330045a88f5cbcabbdc2f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 3465671500138671952, guid: 6b417d114ba28c84aafa17634078e33c, type: 3}
  m_Bones:
  - {fileID: 9091163045023956537}
  - {fileID: 7958641572909903515}
  - {fileID: 8171633537370485388}
  - {fileID: 4604595600572607194}
  - {fileID: 8657769629828879697}
  - {fileID: 7261989691374450576}
  - {fileID: 5203902218573427349}
  - {fileID: 1755227705811107991}
  - {fileID: 1585858863607960597}
  - {fileID: 8737808306444604344}
  - {fileID: 827639197856478921}
  - {fileID: 743764369915614746}
  - {fileID: 4219289444781325925}
  - {fileID: 7387511971328698703}
  - {fileID: 4341938192780654018}
  - {fileID: 1073180046605342055}
  - {fileID: 6871212718273157075}
  - {fileID: 8175108708189361675}
  - {fileID: 2910101817788475654}
  - {fileID: 8435588430071067673}
  - {fileID: 4195780095517961237}
  - {fileID: 9202000106483099298}
  - {fileID: 1077937534433769719}
  - {fileID: 6821736451461349674}
  - {fileID: 4674339227767886027}
  - {fileID: 4710253140000276070}
  - {fileID: 2639685787003968554}
  - {fileID: ************2495030}
  - {fileID: 7552358146636554425}
  - {fileID: 9207964391755388661}
  - {fileID: 7242484880559672717}
  - {fileID: 1007877658666005422}
  - {fileID: 4517774784669809633}
  - {fileID: 7761077288355988}
  - {fileID: 2943670409170553231}
  - {fileID: 7043568085372433949}
  - {fileID: 3944353398667568260}
  - {fileID: 8432469221394114824}
  - {fileID: 4432709933453782320}
  - {fileID: 5815351910490631401}
  - {fileID: 2142372919606153743}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 9091163045023956537}
  m_AABB:
    m_Center: {x: -0.005131036, y: 0.8976847, z: -0.010277152}
    m_Extent: {x: 0.39025036, y: 0.5212852, z: 0.3325666}
  m_DirtyAABB: 0
--- !u!1 &7149360282939786328
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1077937534433769719}
  m_Layer: 0
  m_Name: toes.r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1077937534433769719
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7149360282939786328}
  m_LocalRotation: {x: -0.000000022296454, y: 0.9203549, z: -0.39108422, w: -0.00000013520682}
  m_LocalPosition: {x: 0.000000019147048, y: 0.16565004, z: -0.000000010191606}
  m_LocalScale: {x: 1, y: 0.9999976, z: 1.0000035}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 9202000106483099298}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7574304068174210775
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4219289444781325925}
  m_Layer: 0
  m_Name: hand.r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4219289444781325925
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7574304068174210775}
  m_LocalRotation: {x: 5.9541505e-10, y: 0.0000000892218, z: -0.000005455195, w: 1}
  m_LocalPosition: {x: 1.5774049e-12, y: 0.073825896, z: 0.00000011935727}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7387511971328698703}
  m_Father: {fileID: 743764369915614746}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8243929428809527328
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4195780095517961237}
  m_Layer: 0
  m_Name: lowerleg.r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4195780095517961237
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8243929428809527328}
  m_LocalRotation: {x: 0.10622622, y: 0.000000068652895, z: -0.00000007377092, w: 0.994342}
  m_LocalPosition: {x: 0.000000017036669, y: 0.22707745, z: -0.0000000017018245}
  m_LocalScale: {x: 1, y: 0.9999999, z: 0.9999997}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 9202000106483099298}
  m_Father: {fileID: 8435588430071067673}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8321310272333673374
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5815351910490631401}
  m_Layer: 0
  m_Name: elbowIK.r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5815351910490631401
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8321310272333673374}
  m_LocalRotation: {x: -0.7071067, y: 0.0000000727951, z: 0.00000007279507, w: 0.7071069}
  m_LocalPosition: {x: 0.45350718, y: 1.1067611, z: -0.5888596}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 9091163045023956537}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8353419320256457192
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3944353398667568260}
  m_Layer: 0
  m_Name: IK-toe.r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3944353398667568260
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8353419320256457192}
  m_LocalRotation: {x: 0.00000005860104, y: 1.2081923e-15, z: 0.000000019466935, w: 1}
  m_LocalPosition: {x: 0.000000004556948, y: -0.000000001167181, z: -0.000000019596566}
  m_LocalScale: {x: 1, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4517774784669809633}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8486694145761369626
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1073180046605342055}
  m_Layer: 0
  m_Name: upperleg.l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1073180046605342055
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8486694145761369626}
  m_LocalRotation: {x: 0.9998491, y: 0.00000007132798, z: 0.00000001204276, w: 0.017373588}
  m_LocalPosition: {x: -0.17094506, y: 0.11358726, z: 0.000000013923272}
  m_LocalScale: {x: 1, y: 0.99999976, z: 1.0000011}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6871212718273157075}
  m_Father: {fileID: 7958641572909903515}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &9042178031831143679
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7552358146636554425}
  m_Layer: 0
  m_Name: IK-foot.l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7552358146636554425
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9042178031831143679}
  m_LocalRotation: {x: 1, y: 4.6537017e-14, z: -0.00000019470718, w: 0.00000013315805}
  m_LocalPosition: {x: 0.00000002121244, y: 0.16565005, z: -4.3475623e-10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2639685787003968554}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &9133677892099334688
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8657769629828879697}
  m_Layer: 0
  m_Name: upperarm.l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8657769629828879697
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9133677892099334688}
  m_LocalRotation: {x: -0.514122, y: 0.48546723, z: 0.48546833, w: 0.51412123}
  m_LocalPosition: {x: -0.21200736, y: 0.13413227, z: 0.00000008487841}
  m_LocalScale: {x: 0.99999934, y: 0.99999934, z: 0.9999998}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7261989691374450576}
  m_Father: {fileID: 4604595600572607194}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &9176575822429116426
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5089336202988804993}
  - component: {fileID: 8203941922029014427}
  m_Layer: 0
  m_Name: Skeleton_Rogue_Head
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5089336202988804993
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9176575822429116426}
  m_LocalRotation: {x: 0.000000021855694, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 1.2158737, z: -0.0000001359731}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1148810637686079882}
  m_RootOrder: 5
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &8203941922029014427
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9176575822429116426}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 3af9a29e73f330045a88f5cbcabbdc2f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: -8745038683474382566, guid: 6b417d114ba28c84aafa17634078e33c, type: 3}
  m_Bones:
  - {fileID: 9091163045023956537}
  - {fileID: 7958641572909903515}
  - {fileID: 8171633537370485388}
  - {fileID: 4604595600572607194}
  - {fileID: 8657769629828879697}
  - {fileID: 7261989691374450576}
  - {fileID: 5203902218573427349}
  - {fileID: 1755227705811107991}
  - {fileID: 1585858863607960597}
  - {fileID: 8737808306444604344}
  - {fileID: 827639197856478921}
  - {fileID: 743764369915614746}
  - {fileID: 4219289444781325925}
  - {fileID: 7387511971328698703}
  - {fileID: 4341938192780654018}
  - {fileID: 1073180046605342055}
  - {fileID: 6871212718273157075}
  - {fileID: 8175108708189361675}
  - {fileID: 2910101817788475654}
  - {fileID: 8435588430071067673}
  - {fileID: 4195780095517961237}
  - {fileID: 9202000106483099298}
  - {fileID: 1077937534433769719}
  - {fileID: 6821736451461349674}
  - {fileID: 4674339227767886027}
  - {fileID: 4710253140000276070}
  - {fileID: 2639685787003968554}
  - {fileID: ************2495030}
  - {fileID: 7552358146636554425}
  - {fileID: 9207964391755388661}
  - {fileID: 7242484880559672717}
  - {fileID: 1007877658666005422}
  - {fileID: 4517774784669809633}
  - {fileID: 7761077288355988}
  - {fileID: 2943670409170553231}
  - {fileID: 7043568085372433949}
  - {fileID: 3944353398667568260}
  - {fileID: 8432469221394114824}
  - {fileID: 4432709933453782320}
  - {fileID: 5815351910490631401}
  - {fileID: 2142372919606153743}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 9091163045023956537}
  m_AABB:
    m_Center: {x: 0, y: 1.7413026, z: -0.0023028553}
    m_Extent: {x: 0.43542883, y: 0.42478478, z: 0.45611683}
  m_DirtyAABB: 0
