%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!28 &-7856168392026190441
Texture2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: z__GradientRampTex{"mode":0,"colorKeys":[{"color":{"r":0.46226418018341067,"g":0.0,"b":0.0,"a":1.0},"time":0.23234912753105164},{"color":{"r":1.0,"g":0.3459118604660034,"b":0.3459118604660034,"a":1.0},"time":1.0}],"alphaKeys":[{"alpha":1.0,"time":0.0},{"alpha":1.0,"time":1.0}]}
  m_ImageContentsHash:
    serializedVersion: 2
    Hash: 00000000000000000000000000000000
  m_IsAlphaChannelOptional: 0
  serializedVersion: 3
  m_Width: 256
  m_Height: 1
  m_CompleteImageSize: 1024
  m_MipsStripped: 0
  m_TextureFormat: 5
  m_MipCount: 1
  m_IsReadable: 1
  m_IsPreProcessed: 0
  m_IgnoreMipmapLimit: 0
  m_MipmapLimitGroupName: 
  m_StreamingMipmaps: 0
  m_StreamingMipmapsPriority: 0
  m_VTOnly: 0
  m_AlphaIsTransparency: 0
  m_ImageCount: 1
  m_TextureDimension: 2
  m_TextureSettings:
    serializedVersion: 2
    m_FilterMode: 1
    m_Aniso: 1
    m_MipBias: 0
    m_WrapU: 1
    m_WrapV: 1
    m_WrapW: 1
  m_LightmapFormat: 0
  m_ColorSpace: 1
  m_PlatformBlob: 
  image data: 1024
  _typelessdata: ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff760000ff770101ff780101ff790202ff790202ff7a0303ff7b0303ff7b0303ff7c0404ff7d0404ff7d0505ff7e0505ff7f0606ff800606ff800707ff810707ff820808ff820808ff830808ff840909ff840909ff850a0aff860a0aff870b0bff870b0bff880c0cff890c0cff890d0dff8a0d0dff8b0d0dff8b0e0eff8c0e0eff8d0f0fff8e0f0fff8e1010ff8f1010ff901111ff901111ff911111ff921212ff921212ff931313ff941313ff951414ff951414ff961515ff971515ff971616ff981616ff991616ff991717ff9a1717ff9b1818ff9c1818ff9c1919ff9d1919ff9e1a1aff9e1a1aff9f1a1affa01b1bffa01b1bffa11c1cffa21c1cffa31d1dffa31d1dffa41e1effa51e1effa51f1fffa61f1fffa71f1fffa72020ffa82020ffa92121ffaa2121ffaa2222ffab2222ffac2323ffac2323ffad2323ffae2424ffae2424ffaf2525ffb02525ffb12626ffb12626ffb22727ffb32727ffb32828ffb42828ffb52828ffb52929ffb62929ffb72a2affb82a2affb82b2bffb92b2bffba2c2cffba2c2cffbb2c2cffbc2d2dffbc2d2dffbd2e2effbe2e2effbf2f2fffbf2f2fffc03030ffc13030ffc13131ffc23131ffc33131ffc33232ffc43232ffc53333ffc63333ffc63434ffc73434ffc83535ffc83535ffc93636ffca3636ffca3636ffcb3737ffcc3737ffcd3838ffcd3838ffce3939ffcf3939ffcf3a3affd03a3affd13a3affd13b3bffd23b3bffd33c3cffd43c3cffd43d3dffd53d3dffd63e3effd63e3effd73f3fffd83f3fffd83f3fffd94040ffda4040ffdb4141ffdb4141ffdc4242ffdd4242ffdd4343ffde4343ffdf4343ffdf4444ffe04444ffe14545ffe24545ffe24646ffe34646ffe44747ffe44747ffe54848ffe64848ffe64848ffe74949ffe84949ffe94a4affe94a4affea4b4bffeb4b4bffeb4c4cffec4c4cffed4c4cffed4d4dffee4d4dffef4e4efff04e4efff04f4ffff14f4ffff25050fff25050fff35151fff45151fff45151fff55252fff65252fff75353fff75353fff85454fff95454fff95555fffa5555fffb5656fffb5656fffc5656fffd5757fffe5757fffe5858ffff5858
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!114 &-6769844358222141695
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0353a89b1f911e48b9e16bdc9f2e058, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 9
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Head 2
  m_Shader: {fileID: 4800000, guid: 6d7dbbdf9194b6745ae79aaf2f3c053c, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords:
  - _ExcludeFromJPG
  m_InvalidKeywords:
  - DR_OUTLINE_ON
  - DR_RIM_ON
  - _DETAILMAPBLENDINGMODE_MULTIPLY
  - _EXCLUDE_FROM_JPG
  - _ReceiveFogEnabled
  - _ReceiveShadowsEnabled
  - _SubsurfaceEnabled
  - _TEXTUREBLENDINGMODE_MULTIPLY
  - _UNITYSHADOW_OCCLUSION
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: 2000
  stringTagMap:
    RenderType: Opaque
  disabledShaderPasses:
  - MOTIONVECTORS
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BakedGIRamp:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BaseMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Emission_Map:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _GradientRamp:
        m_Texture: {fileID: -7856168392026190441}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _HeightMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _HeightMap2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetalnessMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetalnessMap2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalMap2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _RoughnessMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _RoughnessMap2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecularDabTexture:
        m_Texture: {fileID: 2800000, guid: 442e6933888925c48b05d56bcfba9809, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SubsurfaceThicknessMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AOEnabled: 1
    - _AOStrength: 0.196
    - _AOToonShadingEnabled: 1
    - _AdditionalLightToonShadingEnabled: 1
    - _AdditionalLightToonShadingThreshold: 0.128
    - _AllowAlphaOverflow: 0
    - _AlphaClip: 0
    - _AlphaClipEnabled: 0
    - _AlphaClipThreshold: 0
    - _AlphaToMask: 0
    - _AmbientLightStrength: 1
    - _Blend: 0
    - _BumpScale: 1
    - _CameraDistanceFadeClose: 0
    - _CameraDistanceFadeFar: 10
    - _CameraDistanceImpact: 0.5
    - _CastShadows: 1
    - _ClearCoatMask: 0
    - _ClearCoatSmoothness: 0
    - _Cull: 2
    - _Culling: 2
    - _Cutoff: 0.5
    - _Depth: 100
    - _DetailAlbedoMapScale: 1
    - _DetailMapBlendingMode: 0
    - _DetailMapImpact: 0
    - _DetailNormalMapScale: 1
    - _DstBlend: 0
    - _Emissive: 0
    - _EnvironmentReflections: 1
    - _ExcludeFromJPG: 1
    - _FlatRimEdgeSmoothness: 0.466
    - _FlatRimLightAlign: 0.159
    - _FlatRimSize: 0.032
    - _FlatSpecularEdgeSmoothness: 0
    - _FlatSpecularSize: 0.1
    - _FogEnabled: 1
    - _GlossMapScale: 1
    - _Glossiness: 0
    - _GlossyReflections: 1
    - _GradientAngle: 0
    - _GradientCenterX: 0
    - _GradientCenterY: 0
    - _GradientEnabled: 0
    - _GradientSize: 10
    - _HasEmissionMap: 0
    - _HasEmissionMap2: 0
    - _HasHeightMap: 0
    - _HasHeightMap2: 0
    - _HasMetalnessMap: 0
    - _HasMetalnessMap2: 0
    - _HasNormalMap: 0
    - _HasNormalMap2: 0
    - _HasRoughnessMap: 0
    - _HasRoughnessMap2: 0
    - _HasSubsurfaceMap: 0
    - _HeightStrength: 0.02
    - _HeightStrength2: 0.02
    - _LightContribution: 0.398
    - _LightingMode: 1
    - _LightmapDirectionPitch: 0
    - _LightmapDirectionYaw: 0
    - _Metallic: 0
    - _Metalness: 0.038
    - _Metalness2: 0
    - _MetalnessMapExposure: 0
    - _MetalnessMapExposure2: 0
    - _Midpoint: 0.222
    - _MidpointStrength: 0.5
    - _Mode: 0
    - _NoiseAmount: 0.5
    - _NoiseScale: 1
    - _NormalStrength: 1
    - _NormalStrength2: 1
    - _OcclusionStrength: 1
    - _OcclusionStrength2: 1
    - _OutlineDepthOffset: 0
    - _OutlineEnabled: 1
    - _OutlineScale: 1
    - _OutlineWidth: 0.3
    - _OverrideBakedGi: 0
    - _OverrideLightAttenuation: 0
    - _OverrideLightmapDir: 0
    - _Parallax: 0.02
    - _QueueControl: -1
    - _QueueOffset: 0
    - _ReceiveAdditionalLights: 1
    - _ReceiveFogEnabled: 1
    - _ReceiveShadows: 1
    - _ReceiveShadowsEnabled: 1
    - _RimEnabled: 1
    - _RimLightingEnabled: 1
    - _RimThreshold: 0.85
    - _Roughness: 0.224
    - _Roughness2: 0.517
    - _RoughnessAmount: 0.25
    - _RoughnessMapExposure: 0
    - _RoughnessMapExposure2: 0
    - _SelfShadingSize: 0.256
    - _Smoothness: 0.5
    - _SmoothnessTextureChannel: 0
    - _SortPriority: 0
    - _SpecularColorAmount: 0
    - _SpecularDabRotation: 0
    - _SpecularDabScale: 20
    - _SpecularEnabled: 0
    - _SpecularHighlights: 1
    - _SpecularHighlightsEnabled: 1
    - _Specularity: 0
    - _Specularity2: 0.5
    - _SrcBlend: 1
    - _SubsurfaceAmbient: 0.299
    - _SubsurfaceDistortion: 0.519
    - _SubsurfaceEnabled: 1
    - _SubsurfaceFalloff: 2.45
    - _SubsurfaceThickness: 0.596
    - _Surface: 0
    - _TextureBlendingMode: 0
    - _TextureImpact: 0
    - _UVSec: 0
    - _UnityShadowOcclusion: 1
    - _UseVertexColors: 0
    - _UvFadeX: 0.1
    - _UvFadeY: 0.1
    - _VertexColorsEnabled: 0
    - _ViewState: 0
    - _WorkflowMode: 1
    - _ZTest: 7
    - _ZWrite: 1
    - _ZWriteControl: 0
    m_Colors:
    - _BaseColor: {r: 0.8000001, g: 0, b: 0.0049758465, a: 1}
    - _BaseColor2: {r: 0.5, g: 0.5, b: 0.5, a: 1}
    - _Color: {r: 0.8000001, g: 0, b: 0.0049758465, a: 1}
    - _ColorGradient: {r: 0.85023, g: 0.85034, b: 0.8504499, a: 0.85056}
    - _Color_Lit: {r: 1, g: 1, b: 1, a: 0}
    - _DetailMapColor: {r: 1, g: 1, b: 1, a: 1}
    - _Emission: {r: 0, g: 0, b: 0, a: 1}
    - _Emission2: {r: 0, g: 0, b: 0, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _Emission_Color: {r: 1, g: 1, b: 1, a: 0}
    - _FlatRimColor: {r: 0.85023, g: 0.85034, b: 0.85045, a: 0.85056}
    - _FlatSpecularColor: {r: 0.85023, g: 0.85034, b: 0.85045, a: 0.85056}
    - _LightAttenuation: {r: 0, g: 1, b: 0, a: 0}
    - _LightmapDirection: {r: 0, g: 1, b: 0, a: 0}
    - _MainColor: {r: 7.6981134, g: 0, b: 0.3927332, a: 1}
    - _NoiseSpeed: {r: 0, g: 0, b: 0, a: 0}
    - _OutlineColor: {r: 1, g: 1, b: 1, a: 1}
    - _RimColor: {r: 5.9921575, g: 5.9921575, b: 5.9921575, a: 0}
    - _ShadowColor: {r: 0, g: 0, b: 0, a: 1}
    - _ShadowTint: {r: 0, g: 0, b: 0, a: 0}
    - _SpecColor: {r: 0.2, g: 0.2, b: 0.2, a: 1}
    - _SpecularColor: {r: 1, g: 1, b: 1, a: 0}
    - _SubsurfaceColor: {r: 14.928529, g: 14.928529, b: 14.928529, a: 1}
  m_BuildTextureStacks: []
  m_AllowLocking: 1
