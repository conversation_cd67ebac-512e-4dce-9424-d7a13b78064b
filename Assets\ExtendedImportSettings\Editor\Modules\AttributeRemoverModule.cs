using System;
using UnityEngine;

namespace ExtendedImportSettings
{
    [Serializable]
    public class AttributeRemoverData : PerMeshDataBase
    {
        public bool RemoveColor;
        public bool RemoveNormal;
        public bool RemoveTangent;
        public UVChannel RemoveUV;
    }
    
    [Serializable]
    public class AttributeRemoverModule : PerMeshModuleBase<AttributeRemoverData>
    {
        protected override void OnPostprocessModelPerMesh(Mesh mesh, AttributeRemoverData data,
            GameObject meshGameObject, GameObject rootGameObject)
        {
            if (data.RemoveNormal)
                mesh.normals = null;
            if (data.RemoveTangent)
                mesh.tangents = null;
            if (data.RemoveColor)
            {
                mesh.colors = null;
                mesh.colors32 = null;
            }

            Vector2[] uvs = null;
            var uvIndexes = data.RemoveUV.ToIndexes();
            foreach (var uvIndex in uvIndexes)
                mesh.SetUVs(uvIndex, uvs);
            
            mesh.RecalculateBounds();
        }
    }
}