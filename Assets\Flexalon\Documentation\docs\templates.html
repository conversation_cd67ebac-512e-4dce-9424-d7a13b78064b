<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<link rel="icon" href="../images/favicon.svg" />
		<link rel="icon" href="../images/favicon.ico" />
		<meta name="viewport" content="width=device-width, height=device-height, initial-scale=1">
		<meta property="og:type" content="website">
		<meta http-equiv="content-security-policy" content="">
		<link href="../_app/immutable/assets/_layout.21aea584.css" rel="stylesheet">
		<link href="../_app/immutable/assets/_layout.0154a58a.css" rel="stylesheet">
		
		
		
		
		
		
		
		
		
		
		
		
		<title>Flexalon Template Pack</title><!-- HEAD_svelte-i5aht6_START --><meta property="og:title" content="Flexalon Template Pack"><meta name="description" property="og:description" content="Flexalon lets Unity developers arrange objects in 3D layouts that are precise and easy to adjust. Use built-in interactions and animations to create powerful experiences."><meta name="image" property="og:image" content="../images/flexalon-1200x630.png"><meta name="twitter:card" content="summary_large_image"><meta name="twitter:title" content="Flexalon Template Pack"><meta name="twitter:site" content="@alon_farchy"><meta name="twitter:description" content="Flexalon lets Unity developers arrange objects in 3D layouts that are precise and easy to adjust. Use built-in interactions and animations to create powerful experiences."><meta name="twitter:image" content="../images/flexalon-1200x630.png"><!-- HEAD_svelte-i5aht6_END -->
	</head>
	<body>
		<div>


<div class="text-white font-body p-4 sticky top-0 bg-black flex gap-5 w-full text-gray-200 z-20 border-b-2 border-[#21a6f0]"><button class="lg:hidden"><svg width="24" height="24"><path d="M5 6h14M5 12h14M5 18h14" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"></path></svg></button>
    <a href="https://www.flexalon.com/" class="font-logo upper text-lg text-[#21a6f0] lg:pl-5 svelte-194nj6y">Flexalon Layouts</a></div>

<div class="w-full max-w-[1600px] lg:pl-10"><div class="flex w-full min-h-full text-zinc-400 relative"><div class="h-full fixed bg-black z-10 max-lg:hidden"><div class="flex flex-col w-[300px] gap-2 border-[#21a6f0] border-r-2 bg-black min-h-0 h-full pt-10 pb-36 overflow-auto max-lg:pl-10"><b class="text-zinc-200">Getting Started</b>
                <a href="../docs.html" class="svelte-194nj6y">Installation</a>
                <a href="../docs/coreConcepts.html" class="svelte-194nj6y">Core Concepts</a>
                <a href="../docs/ui.html" class="svelte-194nj6y">Flexalon UI <span class="ml-2 text-xs font-bold text-pink-300">(New in v4.0!)</span></a>
                <a href="../docs/templates.html" class="svelte-194nj6y">Template Pack</a>
                <a href="../docs/challenges.html" class="svelte-194nj6y">Challenges!</a>

                <a class="text-zinc-200 mt-5 font-bold svelte-194nj6y" href="../docs/flexalonObject.html">Flexalon Objects</a>
                <a href="../docs/flexalonObject.html#size" class="svelte-194nj6y">Width, Height, and Depth</a>
                <a href="../docs/flexalonObject.html#minmax" class="svelte-194nj6y">Min / Max Size<span class="ml-2 text-xs font-bold text-purple-400">(v4.1)</span></a>
                <a href="../docs/flexalonObject.html#margins" class="svelte-194nj6y">Margins and Padding</a>
                <a href="../docs/flexalonObject.html#offset" class="svelte-194nj6y">Offset, Scale, and Rotation</a>

                <b class="text-zinc-200 mt-5">Layouts</b>
                <a href="../docs/flexibleLayout.html" class="svelte-194nj6y">Flexible Layout</a>
                <a href="../docs/gridLayout.html" class="svelte-194nj6y">Grid Layout</a>
                <a href="../docs/circleLayout.html" class="svelte-194nj6y">Circle / Spiral Layout</a>
                <a href="../docs/curveLayout.html" class="svelte-194nj6y">Curve Layout</a>
                <a href="../docs/randomLayout.html" class="svelte-194nj6y">Random Layout</a>
                <a href="../docs/alignLayout.html" class="svelte-194nj6y">Align Layout</a>
                <a href="../docs/shapeLayout.html" class="svelte-194nj6y">Shape Layout</a>
                <a href="../docs/constraints.html" class="svelte-194nj6y">Constraints</a>
                <a href="../docs/randomModifier.html" class="svelte-194nj6y">Random Modifier</a>

                <a class="text-zinc-200 mt-5 font-bold svelte-194nj6y" href="../docs/animators.html">Animators</a>
                <a href="../docs/animators.html#curve" class="svelte-194nj6y">Curve Animator</a>
                <a href="../docs/animators.html#lerp" class="svelte-194nj6y">Lerp Animator</a>
                <a href="../docs/animators.html#rb" class="svelte-194nj6y">Rigid Body Animator</a>
                <a href="../docs/animators.html#layouts" class="svelte-194nj6y">Animating Layouts</a>

                <b class="text-zinc-200 mt-5">Click &amp; Drag</b>
                <a href="../docs/interactable.html" class="svelte-194nj6y">Interactable</a>
                <a href="../docs/interactable.html#dragTarget" class="svelte-194nj6y">Drag Target</a>
                <a href="../docs/xr.html" class="svelte-194nj6y">XR Interactions <span class="ml-2 text-xs font-bold text-blue-300">(v3.2)</span></a>
                <a href="../docs/interactable.html#customInputProvider" class="svelte-194nj6y">Custom Input Provider</a>

                <b class="text-zinc-200 mt-5">Data Binding</b>
                <a href="../docs/cloner.html" class="svelte-194nj6y">Cloner</a>
                <a href="../docs/cloner.html#databinding" class="svelte-194nj6y">Data Binding</a>

                <b class="text-zinc-200 mt-5">Advanced</b>
                <a href="../docs/pipeline.html" class="svelte-194nj6y">Flexalon Pipeline</a>
                <a href="../docs/customLayout.html" class="svelte-194nj6y">Custom Layouts</a>
                <a href="../docs/customAnimators.html" class="svelte-194nj6y">Custom Animators</a>
                <a href="../docs/adapters.html" class="svelte-194nj6y">Adapters</a>

                <a class="text-zinc-200 mt-5 font-bold svelte-194nj6y" href="../docs/api/Flexalon.html">Scripting API</a>
                <div class="mb-20"></div></div></div>
        
        <div class="flex flex-col w-full p-10 lg:ml-[300px]"><div class="doc svelte-194nj6y">

<div class="text-white text-2xl mb-10">Flexalon Template Pack</div>
<p class="text-bold text-xl">The Flexalon Template Pack constains 16 working examples to study from and copy into your scenes.
</p>

<p class="text-bold text-xl"><a href="https://www.flexalon.com/templates">Download the Flexalon Template Pack for FREE
    </a></p>

<img src="../images/templates.png" class="mt-10 mb-10" alt="Flexalon Templates Pack">

<p class="text-bold text-xl">Installation:
</p>

<ol class="list-decimal text-white ml-10 leading-10"><li>Create or open a Unity project (version 2019.4 or newer).</li>
    <li>Add Flexalon 3D Layouts to your project from the package manager. <a href="https://docs.unity3d.com/Manual/upm-ui-import.html" target="_blank" rel="noreferrer">How do I import an asset store package?</a></li>
    <li>Import the FlexalonTemplates package. <a href="https://docs.unity3d.com/Manual/AssetPackagesImport.html" target="_blank" rel="noreferrer">How do I import a local asset packages?</a></li>
    <li>Open the scene FlexalonTemplates/Scenes/FlexalonTemplates.unity.</li>
    <ul class="list-disc ml-10"><li>If you don&#39;t have TextMeshPro in your project, you may be prompted to import TextMeshPro essentials. Import TextMeshPro and reopen the scene.</li></ul>
    <li>Explore the scene! Press play to see the animations and interactables in action.</li></ol>

<p class="text-bold text-xl mt-10">Play Mode Controls:
</p>
<ul class="list-disc text-white ml-10 leading-10"><li>Click on a template to zoom in.</li>
    <li>Press ESC to zoom out.</li>
    <li>Click and drag to rotate the camera.</li>
    <li>Use WASD or arrow keys to move the camera.</li></ul>

<p class="font-bold text-xl mt-10">Template 1: Align Layout</p>
<p>Learn how to use an Align Layout to place objects on floors and walls.</p>
<img src="../images/template_01.png" alt="Template 1">

<p class="font-bold text-xl mt-10">Template 2: Flexible Layout</p>
<p>Learn different possible configurations of a Flexible Layout.</p>
<img src="../images/template_02.png" alt="Template 2">

<p class="font-bold text-xl mt-10">Template 3: Grid Layout</p>
<p>Learn different possible configurations of a Grid Layout.</p>
<img src="../images/template_03.png" alt="Template 3">

<p class="font-bold text-xl mt-10">Template 4: Shape Layout</p>
<p>Learn different possible configurations of a Shape Layout.</p>
<img src="../images/template_04.png" alt="Template 4">

<p class="font-bold text-xl mt-10">Template 5: Circle Layout (Part 1)</p>
<p>Learn different possible configurations of a Circle Layout.</p>
<img src="../images/template_05.png" alt="Template 5">

<p class="font-bold text-xl mt-10">Template 6: Circle Layout (Part 2)</p>
<p>Learn different possible configurations of a Circle Layout.</p>
<img src="../images/template_06.png" alt="Template 6">

<p class="font-bold text-xl mt-10">Template 7: Animating a Curve Layout</p>
<p>Learn two different ways to animate objects along a Curve Layout.</p>
<div class="flex ml-10 m-5 mb-10"><video muted autoplay loop playsinline><source src="https://www.flexalon.com/videos/template_07.mp4"></video></div>

<p class="font-bold text-xl mt-10">Template 8: Drawing Shapes with a Curve Layout</p>
<p>Learn how to draw shapes with a Curve Layout.</p>
<img src="../images/template_08.png" alt="Template 8">

<p class="font-bold text-xl mt-10">Template 9: Dynamic Constraint Targets</p>
<p>Learn how to change a constraint target at runtime to create interesting interactions.</p>
<div class="flex ml-10 m-5 mb-10"><video muted autoplay loop playsinline><source src="https://www.flexalon.com/videos/template_09.mp4"></video></div>

<p class="font-bold text-xl mt-10">Template 10: Bounding Box</p>
<p>Learn how to build a bounding box that surrounds an object of any size without growing thicker.</p>
<img src="../images/template_10.png" alt="Template 10">

<p class="font-bold text-xl mt-10">Template 11: Layout Physics</p>
<p>Learn how to configure rigid bodies to animate with Flexalon Rigid Body Animators.</p>
<div class="flex ml-10 m-5 mb-10"><video muted autoplay loop playsinline><source src="https://www.flexalon.com/videos/template_11.mp4"></video></div>

<p class="font-bold text-xl mt-10">Template 12: Unity Canvas + Flexalon</p>
<p>Learn how Flexalon can be used to arrange and resize a Unity Canvas.</p>
<img src="../images/template_12.png" alt="Template 12">

<p class="font-bold text-xl mt-10">Template 13: Flexalon Interactables</p>
<p>Learn how Flexalon Interactables can be configured to drag objects between layouts.</p>
<div class="flex ml-10 m-5 mb-10"><video muted autoplay loop playsinline><source src="https://www.flexalon.com/videos/template_13.mp4"></video></div>

<p class="font-bold text-xl mt-10">Template 14: Random Flexalon Cloner</p>
<p>Learn how Flexalon Cloner can generate a random sequence of objects into a layout.</p>
<img src="../images/template_14.png" alt="Template 14">

<p class="font-bold text-xl mt-10">Template 15: Data Bound Flexalon Cloner</p>
<p>Learn how Flexalon Cloner can generate text objects based on an input field&#39;s text data.</p>
<img src="../images/template_15.png" alt="Template 15">

<p class="font-bold text-xl mt-10">Template 16: Random Modifier</p>
<p>Learn how Flexalon Random Modifier can modify the results of any layout.</p>
<img src="../images/template_16.png" alt="Template 16"></div></div></div>
</div>


			
			
		</div>
	</body>
</html>
