<?xml version="1.0" encoding="UTF-8"?><package><identifier v="Unsaved Package"/><formatVersion v="1.1.0.202201"/><updaterVersion v="1.1.0.202201"/><fileUID v="{b886da4a-4c14-4104-9480-c8aa37d6c279}"/><versionUID v="0"/><dependencies><dependency><filename v="sbs://pattern_shape.sbs"/><uid v="1311189026"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://trapezoid_transform.sbs"/><uid v="1486830014"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://pbr_base_material.sbs"/><uid v="1482592834"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://gradient_linear_1.sbs"/><uid v="1335474237"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_gaussian_noise.sbs"/><uid v="1486875707"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://pattern_tile_sampler.sbs"/><uid v="1486875900"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://pattern_splatter_circular.sbs"/><uid v="1487668795"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://pattern_tile_generator.sbs"/><uid v="1386931028"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://blend.sbs"/><uid v="1290776863"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://histogram_scan.sbs"/><uid v="1149349207"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://hbao_2.sbs"/><uid v="1297162667"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency></dependencies><content><graph><identifier v="Grass_with_Flowers"/><uid v="1487663959"/><graphtype v="material"/><graphOutputs><graphoutput><identifier v="basecolor"/><uid v="1487664056"/><attributes><label v="Base Color"/></attributes><usages><usage><components v="RGBA"/><name v="baseColor"/></usage></usages><group v="Material"/></graphoutput><graphoutput><identifier v="normal"/><uid v="1487664058"/><attributes><label v="Normal"/></attributes><usages><usage><components v="RGBA"/><name v="normal"/></usage></usages><group v="Material"/></graphoutput><graphoutput><identifier v="roughness"/><uid v="1487664060"/><attributes><label v="Roughness"/></attributes><usages><usage><components v="RGBA"/><name v="roughness"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput><graphoutput><identifier v="height"/><uid v="1487664064"/><attributes><label v="Height"/></attributes><usages><usage><components v="RGBA"/><name v="height"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput><graphoutput><identifier v="ambientOcclusion"/><uid v="1487664066"/><attributes><label v="Ambient Occlusion"/></attributes><usages><usage><components v="RGBA"/><name v="ambientOcclusion"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput></graphOutputs><compNodes><compNode><uid v="1487664034"/><GUILayout><gpos v="46.4301758 -341.757538 1"/></GUILayout><compOutputs><compOutput><uid v="1482708448"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487664048"/><connRefOutput v="1482628326"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.440051019 0.440051019 0.440051019 0.5"/></paramValue></parameter><parameter><name v="levelinlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.182397962 0.182397962 0.182397962 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487664035"/><GUILayout><gpos v="-848 -496 0"/></GUILayout><compOutputs><compOutput><uid v="1482709066"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487715000"/><connRefOutput v="1487715001"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.151785716 0.151785716 0.151785716 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487664036"/><GUILayout><gpos v="-2690.3269 -869.411438 0"/></GUILayout><compOutputs><compOutput><uid v="1482576333"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///shape?dependency=1311189026"/><parameters><parameter><name v="Pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="16"/></paramValue></parameter><parameter><name v="Size"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.74000001"/></paramValue></parameter><parameter><name v="Pattern_Specific"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.829999983"/></paramValue></parameter><parameter><name v="Size_xy"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.199999988 1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1482576333"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487664037"/><GUILayout><gpos v="-2306.46655 -869.848572 0"/></GUILayout><compOutputs><compOutput><uid v="1482590705"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="source"/><connRef v="1487664052"/><connRefOutput v="1482590232"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="maskrectangle"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.199999988 0.779999971 0 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487664038"/><GUILayout><gpos v="-2564.84106 -869.848572 0"/></GUILayout><compOutputs><compOutput><uid v="1482591428"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input"/><connRef v="1487664036"/><connRefOutput v="1482576333"/></connection></connections><compImplementation><compInstance><path v="pkg:///trapezoid_transform_grayscale?dependency=1486830014"/><parameters><parameter><name v="top_stretch"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.620000005"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1482591428"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487664039"/><GUILayout><gpos v="46.4301758 -229.585663 1"/></GUILayout><compOutputs><compOutput><uid v="1482708448"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487664046"/><connRefOutput v="1482628326"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.377574384 0.377574384 0.377574384 0.5"/></paramValue></parameter><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.557397962 0.557397962 0.557397962 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487664040"/><GUILayout><gpos v="240 -464 0"/></GUILayout><compOutputs><compOutput><uid v="1482593503"/><comptype v="1"/></compOutput><compOutput><uid v="1482593504"/><comptype v="1"/></compOutput><compOutput><uid v="1482593505"/><comptype v="1"/></compOutput><compOutput><uid v="1482593506"/><comptype v="1"/></compOutput><compOutput><uid v="1482593507"/><comptype v="2"/></compOutput><compOutput><uid v="1482593508"/><comptype v="2"/></compOutput><compOutput><uid v="1482593509"/><comptype v="2"/></compOutput><compOutput><uid v="1482593510"/><comptype v="2"/></compOutput><compOutput><uid v="1482593511"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="normal_input"/><connRef v="1487664050"/><connRefOutput v="1359211384"/></connection><connection><identifier v="ambientOcclusion_input"/><connRef v="1487664039"/><connRefOutput v="1482708448"/></connection><connection><identifier v="roughness_input"/><connRef v="1487664034"/><connRefOutput v="1482708448"/></connection><connection><identifier v="metallic_input"/><connRef v="1487734084"/><connRefOutput v="1486906083"/></connection><connection><identifier v="height_input"/><connRef v="1487683955"/><connRefOutput v="1487683956"/></connection><connection><identifier v="basecolor_input"/><connRef v="1487692859"/><connRefOutput v="1487692860"/></connection></connections><compImplementation><compInstance><path v="pkg:///pbr_base_material?dependency=1482592834"/><parameters><parameter><name v="user_basecolor"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="user_normal"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="user_roughness"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="user_metallic"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="user_ao"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="user_height"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="height_position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.519999981"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1482593503"/><identifier v="diffuse"/></outputBridging><outputBridging><uid v="1482593504"/><identifier v="basecolor"/></outputBridging><outputBridging><uid v="1482593505"/><identifier v="normal"/></outputBridging><outputBridging><uid v="1482593506"/><identifier v="specular"/></outputBridging><outputBridging><uid v="1482593507"/><identifier v="glossiness"/></outputBridging><outputBridging><uid v="1482593508"/><identifier v="roughness"/></outputBridging><outputBridging><uid v="1482593509"/><identifier v="metallic"/></outputBridging><outputBridging><uid v="1482593510"/><identifier v="height"/></outputBridging><outputBridging><uid v="1482593511"/><identifier v="ambientOcclusion"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487664041"/><GUILayout><gpos v="-1008 -912 0"/></GUILayout><compOutputs><compOutput><uid v="1482625016"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487664051"/><connRefOutput v="1482609709"/></connection></connections><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1482625039"/><paramsArrayCells><paramsArrayCell><uid v="1482625052"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.286956519 0.182096973 0.0550556034 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1482625053"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.875527442"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.650328338 0.908695638 0.507178962 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1482625213"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.0354818478"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.168359771 0.352173924 0.114661269 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1482625257"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.386075944"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.41995281 0.682608724 0.170652181 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1482625330"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.630801678"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.660464287 0.813043475 0.207987875 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487693664"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.0126582272"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.0372851379 0.256521732 0.0391915254 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1487664042"/><GUILayout><gpos v="-2416 -752 0"/></GUILayout><compOutputs><compOutput><uid v="1482591041"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///gradient_linear_1?dependency=1335474237"/><parameters><parameter><name v="rotation"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1482591041"/><identifier v="Simple_Gradient"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487664044"/><GUILayout><gpos v="-2288 -752 0"/></GUILayout><compOutputs><compOutput><uid v="1482590975"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="Input_1"/><connRef v="1487664042"/><connRefOutput v="1482591041"/></connection></connections><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1149349207"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.610000014"/></paramValue></parameter><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0700000003"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1482590975"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487664045"/><GUILayout><gpos v="-2018.67749 -869.84845 0"/></GUILayout><compOutputs><compOutput><uid v="1482608790"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487664053"/></connection></connections><compImplementation><compFilter><filter v="blur"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.91999996"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487664046"/><GUILayout><gpos v="-144 -227.644348 0"/></GUILayout><compOutputs><compOutput><uid v="1482628326"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487664035"/><connRefOutput v="1482709066"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.478316337 0.478316337 0.478316337 0.5"/></paramValue></parameter><parameter><name v="levelinlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.12627551 0.12627551 0.12627551 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487664047"/><GUILayout><gpos v="-1488 -639.070374 0"/></GUILayout><compOutputs><compOutput><uid v="1482708240"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487678600"/><connRefOutput v="1487678601"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.53188777 0.53188777 0.53188777 0.5"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487664048"/><GUILayout><gpos v="-144 -344.000183 0"/></GUILayout><compOutputs><compOutput><uid v="1482628326"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487678714"/><connRefOutput v="1487678715"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.478316337 0.478316337 0.478316337 0.5"/></paramValue></parameter><parameter><name v="levelinlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.12627551 0.12627551 0.12627551 0"/></paramValue></parameter><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.235969394 0.235969394 0.235969394 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487664049"/><GUILayout><gpos v="-1946.23352 -740.756226 0"/></GUILayout><compOutputs><compOutput><uid v="1482609943"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///gaussian_noise?dependency=1486875707"/><parameters><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueInt32 v="9"/></paramValue></parameter><parameter><name v="disorder"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.099999994"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1482609943"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487664050"/><GUILayout><gpos v="48 -576.356384 0"/></GUILayout><compOutputs><compOutput><uid v="1359211384"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487683955"/><connRefOutput v="1487683956"/></connection></connections><compImplementation><compFilter><filter v="normal"/><parameters><parameter><name v="input2alpha"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="3"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487664051"/><GUILayout><gpos v="-1794.0686 -752 0"/></GUILayout><compOutputs><compOutput><uid v="1482609709"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="pattern_input"/><connRef v="1487664045"/><connRefOutput v="1482608790"/></connection><connection><identifier v="rotation_map_input"/><connRef v="1487664049"/><connRefOutput v="1482609943"/></connection></connections><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1486875900"/><parameters><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="75"/></paramValue></parameter><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="size"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="6 6.73999977"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="6.46000004"/></paramValue></parameter><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="75"/></paramValue></parameter><parameter><name v="size_random"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.329999983 0.319999993"/></paramValue></parameter><parameter><name v="rotation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.920000017"/></paramValue></parameter><parameter><name v="non_square_expansion"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="pattern_input_number"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.799999952"/></paramValue></parameter><parameter><name v="rotation_map_multiplier"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.569999993"/></paramValue></parameter><parameter><name v="rotation_vector_map_multiplier"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.340000004"/></paramValue></parameter><parameter><name v="color_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="color_parametrization_mode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="scale_map_multiplier"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0599999987"/></paramValue></parameter><parameter><name v="scale_vector_map_multiplier"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.519999981"/></paramValue></parameter><parameter><name v="mask_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.229999989"/></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="10"/></paramValue></parameter><parameter><name v="vector_map_displacement"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.969999969"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1482609709"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487664052"/><GUILayout><gpos v="-2436.04346 -869.848572 0"/></GUILayout><compOutputs><compOutput><uid v="1482590232"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487664038"/><connRefOutput v="1482591428"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.149234697 0.149234697 0.149234697 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487664053"/><GUILayout><gpos v="-2146.67773 -869.84845 0"/></GUILayout><compOutputs><compOutput><uid v="1482591088"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="destination"/><connRef v="1487664037"/><connRefOutput v="1482590705"/></connection><connection><identifier v="source"/><connRef v="1487664044"/><connRefOutput v="1482590975"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="maskrectangle"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0 1 0 0"/></paramValue></parameter><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487664054"/><GUILayout><gpos v="-176 -816 0"/></GUILayout><compOutputs><compOutput><uid v="1486907756"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487686292"/><connRefOutput v="1487686293"/></connection></connections><compImplementation><compFilter><filter v="hsl"/><parameters><parameter><name v="hue"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.50999999"/></paramValue></parameter><parameter><name v="saturation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.469999999"/></paramValue></parameter><parameter><name v="luminosity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.48999998"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487664055"/><GUILayout><gpos v="464 -688 0"/></GUILayout><connections><connection><identifier v="inputNodeOutput"/><connRef v="1487664040"/><connRefOutput v="1482593504"/></connection></connections><compImplementation><compOutputBridge><output v="1487664056"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1487664057"/><GUILayout><gpos v="464 -573.257263 0"/></GUILayout><connections><connection><identifier v="inputNodeOutput"/><connRef v="1487664040"/><connRefOutput v="1482593505"/></connection></connections><compImplementation><compOutputBridge><output v="1487664058"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1487664059"/><GUILayout><gpos v="464 -462.514465 0"/></GUILayout><connections><connection><identifier v="inputNodeOutput"/><connRef v="1487664040"/><connRefOutput v="1482593508"/></connection></connections><compImplementation><compOutputBridge><output v="1487664060"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1487664063"/><GUILayout><gpos v="465.569824 -348.627686 0"/></GUILayout><connections><connection><identifier v="inputNodeOutput"/><connRef v="1487664040"/><connRefOutput v="1482593510"/></connection></connections><compImplementation><compOutputBridge><output v="1487664064"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1487664065"/><GUILayout><gpos v="464 -240 0"/></GUILayout><connections><connection><identifier v="inputNodeOutput"/><connRef v="1488297733"/><connRefOutput v="1488297734"/></connection></connections><compImplementation><compOutputBridge><output v="1487664066"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1487664355"/><GUILayout><gpos v="-2634.00659 -345.405365 0"/></GUILayout><compOutputs><compOutput><uid v="1487664356"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///shape?dependency=1311189026"/><parameters><parameter><name v="Pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="15"/></paramValue></parameter><parameter><name v="Size_xy"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.719999969 1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487664356"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487667974"/><GUILayout><gpos v="-2506.00659 -345.405365 0"/></GUILayout><compOutputs><compOutput><uid v="1487667975"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487664355"/><connRefOutput v="1487664356"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.40487805 0.40487805 0.40487805 0.5"/></paramValue></parameter><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.391581625 0.391581625 0.391581625 1"/></paramValue></parameter><parameter><name v="levelinlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.3125 0.3125 0.3125 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487668230"/><GUILayout><gpos v="-2378.00659 -345.405365 0"/></GUILayout><compOutputs><compOutput><uid v="1487668231"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487667974"/></connection></connections><compImplementation><compFilter><filter v="transformation"/><parameters><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.0760999992 -0.0188999996"/></paramValue></parameter><parameter><name v="matrix22"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.939899981 -0.341500014 0.341500014 0.939899981"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487668290"/><GUILayout><gpos v="-2398.69849 -223.613007 0"/></GUILayout><compOutputs><compOutput><uid v="1487668231"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487667974"/></connection></connections><compImplementation><compFilter><filter v="transformation"/><parameters><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="-0.0780000016 -0.0256999992"/></paramValue></parameter><parameter><name v="matrix22"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.909699976 0.414999992 -0.414999992 0.909699976"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487668328"/><GUILayout><gpos v="-2250.00659 -347.085388 0"/></GUILayout><compOutputs><compOutput><uid v="1487668329"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="source"/><connRef v="1487668230"/><connRefOutput v="1487668231"/></connection><connection><identifier v="destination"/><connRef v="1487668290"/><connRefOutput v="1487668231"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487669406"/><GUILayout><gpos v="-1776 -336 0"/></GUILayout><compOutputs><compOutput><uid v="1487669407"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="pattern_input_1"/><connRef v="1487672266"/><connRefOutput v="1487672267"/></connection><connection><identifier v="background"/><connRef v="1487670287"/><connRefOutput v="1487670288"/></connection></connections><compImplementation><compInstance><path v="pkg:///splatter_circular?dependency=1487668795"/><parameters><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="pattern_scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.620000005"/></paramValue></parameter><parameter><name v="pattern_size"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.839999974 1"/></paramValue></parameter><parameter><name v="pattern_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="5"/></paramValue></parameter><parameter><name v="radius"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.140000001"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487669407"/><identifier v="Splatter_Circular"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487672266"/><GUILayout><gpos v="-2139.89722 -347.085388 0"/></GUILayout><compOutputs><compOutput><uid v="1487672267"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487668328"/></connection></connections><compImplementation><compFilter><filter v="blur"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="6.35999966"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487678600"/><GUILayout><gpos v="-1621.40344 -637.18927 0"/></GUILayout><compOutputs><compOutput><uid v="1487678601"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="destination"/><connRef v="1487664051"/><connRefOutput v="1482609709"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487678714"/><GUILayout><gpos v="-264.808746 -343.88678 0"/></GUILayout><compOutputs><compOutput><uid v="1487678715"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="source"/><connRef v="1487683955"/><connRefOutput v="1487683956"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1487669862"/><GUILayout><gpos v="-2462.69849 -80 0"/></GUILayout><compOutputs><compOutput><uid v="1487667975"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487664355"/><connRefOutput v="1487664356"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.40487805 0.40487805 0.40487805 0.5"/></paramValue></parameter><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.391581625 0.391581625 0.391581625 1"/></paramValue></parameter><parameter><name v="levelinlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.3125 0.3125 0.3125 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487669871"/><GUILayout><gpos v="-2334.69849 -80 0"/></GUILayout><compOutputs><compOutput><uid v="1487669872"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487669862"/></connection></connections><compImplementation><compFilter><filter v="transformation"/><parameters><parameter><name v="matrix22"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="1 0 0 1.33290005"/></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0 -0.0401000008"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487669977"/><GUILayout><gpos v="-2208.75977 -80 0"/></GUILayout><compOutputs><compOutput><uid v="1487669978"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="source"/><connRef v="1487669871"/><connRefOutput v="1487669872"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="maskrectangle"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0 1 0.179999992 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487670043"/><GUILayout><gpos v="-2082.93701 -80 0"/></GUILayout><compOutputs><compOutput><uid v="1487670044"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487669977"/></connection></connections><compImplementation><compFilter><filter v="transformation"/><parameters><parameter><name v="matrix22"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="1.65289998 0 0 1.7622"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487670141"/><GUILayout><gpos v="-1648 -336 0"/></GUILayout><compOutputs><compOutput><uid v="1487670142"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="source"/><connRef v="1487669406"/><connRefOutput v="1487669407"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="maskrectangle"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.0899999961 0.839999974 0.149999991 0.859999955"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487670287"/><GUILayout><gpos v="-1936 -208 0"/></GUILayout><compOutputs><compOutput><uid v="1487670288"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487670043"/><connRefOutput v="1487670044"/></connection></connections><compImplementation><compFilter><filter v="blur"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="9.35999966"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487690789"/><GUILayout><gpos v="-880 -912 0"/></GUILayout><compOutputs><compOutput><uid v="1487690790"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="destination"/><connRef v="1487664041"/><connRefOutput v="1482625016"/></connection><connection><identifier v="source"/><connRef v="1487690855"/><connRefOutput v="1482625016"/></connection><connection><identifier v="opacity"/><connRef v="1487712394"/><connRefOutput v="1487712395"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487690855"/><GUILayout><gpos v="-1232 -784 0"/></GUILayout><compOutputs><compOutput><uid v="1482625016"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487712185"/><connRefOutput v="1487677951"/></connection></connections><compImplementation><compFilter><filter v="gradient"/><parameters><parameter><name v="colorswitch"/><paramValue><constantValueBool v="1"/></paramValue></parameter></parameters><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1482625039"/><paramsArrayCells><paramsArrayCell><uid v="1487691015"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0 0 0 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487691016"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.297820807"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="1 1 1 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487691017"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.142857119"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.890776694 0.890776694 0.890776694 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1487710304"/><GUILayout><gpos v="-1328 -336 0"/></GUILayout><compOutputs><compOutput><uid v="1487710305"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487712185"/><connRefOutput v="1487677951"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.631377578 0.631377578 0.631377578 0.5"/></paramValue></parameter><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.215561226 0.215561226 0.215561226 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487712185"/><GUILayout><gpos v="-1520 -336 0"/></GUILayout><compOutputs><compOutput><uid v="1487677951"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="pattern_input_1"/><connRef v="1487670141"/><connRefOutput v="1487670142"/></connection></connections><compImplementation><compInstance><path v="pkg:///tile_generator?dependency=1386931028"/><parameters><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.280000001"/></paramValue></parameter><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="position_offset_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.859999955"/></paramValue></parameter><parameter><name v="offset_random_seed"/><relativeTo v="0"/><paramValue><constantValueInt32 v="271"/></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.179999992 0.429999977"/></paramValue></parameter><parameter><name v="global_offset"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.579999983 0.449999988"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.419999987"/></paramValue></parameter><parameter><name v="pattern_rotation_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.599999964"/></paramValue></parameter><parameter><name v="pattern_symmetry_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487677951"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487712394"/><GUILayout><gpos v="-1104 -784 0"/></GUILayout><compOutputs><compOutput><uid v="1487712395"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487712185"/><connRefOutput v="1487677951"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.578431308 0.578431308 0.578431308 0.5"/></paramValue></parameter><parameter><name v="levelinlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.108418368 0.108418368 0.108418368 0"/></paramValue></parameter><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.169642851 0.169642851 0.169642851 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487714410"/><GUILayout><gpos v="-144 -464 0"/></GUILayout><compOutputs><compOutput><uid v="1487714411"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="destination"/><connRef v="1487664035"/><connRefOutput v="1482709066"/></connection><connection><identifier v="source"/><connRef v="1487710304"/><connRefOutput v="1487710305"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.459999979"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487715000"/><GUILayout><gpos v="-1040 -496 0"/></GUILayout><compOutputs><compOutput><uid v="1487715001"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="source"/><connRef v="1487664047"/><connRefOutput v="1482708240"/></connection><connection><identifier v="destination"/><connRef v="1487710304"/><connRefOutput v="1487710305"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487734084"/><GUILayout><gpos v="48 -464 0"/></GUILayout><compOutputs><compOutput><uid v="1486906083"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487714410"/><connRefOutput v="1487714411"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelouthigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.679846942 0.679846942 0.679846942 1"/></paramValue></parameter><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.459302336 0.459302336 0.459302336 0.5"/></paramValue></parameter><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.672193885 0.672193885 0.672193885 1"/></paramValue></parameter><parameter><name v="levelinlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.304846942 0.304846942 0.304846942 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487682472"/><GUILayout><gpos v="-1456 -144 0"/></GUILayout><compOutputs><compOutput><uid v="1487677951"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="pattern_input_1"/><connRef v="1487670141"/><connRefOutput v="1487670142"/></connection></connections><compImplementation><compInstance><path v="pkg:///tile_generator?dependency=1386931028"/><parameters><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.539999962"/></paramValue></parameter><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.419999987"/></paramValue></parameter><parameter><name v="pattern_rotation_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.599999964"/></paramValue></parameter><parameter><name v="pattern_symmetry_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="rotation_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.659999967"/></paramValue></parameter><parameter><name v="rotation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.51111114"/></paramValue></parameter><parameter><name v="randomseed"/><relativeTo v="2"/><paramValue><constantValueInt32 v="8"/></paramValue></parameter><parameter><name v="pattern_input_number"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="position_offset"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="position_offset_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.98999995"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487677951"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487682483"/><GUILayout><gpos v="-831.486633 -144 0"/></GUILayout><compOutputs><compOutput><uid v="1487682484"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="source"/><connRef v="1487690789"/><connRefOutput v="1487690790"/></connection><connection><identifier v="destination"/><connRef v="1487684658"/><connRefOutput v="1487684659"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487682523"/><GUILayout><gpos v="-1119.48657 -144 0"/></GUILayout><compOutputs><compOutput><uid v="1487682524"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487683024"/><connRefOutput v="1487683025"/></connection></connections><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1487682745"/><paramsArrayCells><paramsArrayCell><uid v="1487682746"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0 0 0 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487682747"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="1 1 1 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1487683024"/><GUILayout><gpos v="-1230.32776 -144 0"/></GUILayout><compOutputs><compOutput><uid v="1487683025"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487683417"/><connRefOutput v="1487683418"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.71811223 0.71811223 0.71811223 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487683417"/><GUILayout><gpos v="-1343.48657 -144 0"/></GUILayout><compOutputs><compOutput><uid v="1487683418"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487682472"/><connRefOutput v="1487677951"/></connection></connections><compImplementation><compFilter><filter v="transformation"/><parameters><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.272799999 0.356900007"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487683616"/><GUILayout><gpos v="-688 -16 0"/></GUILayout><compOutputs><compOutput><uid v="1487683617"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="source"/><connRef v="1487682483"/><connRefOutput v="1487682484"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487683955"/><GUILayout><gpos v="-140.798706 -125.88681 0"/></GUILayout><compOutputs><compOutput><uid v="1487683956"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487683616"/></connection></connections><compImplementation><compFilter><filter v="grayscaleconversion"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1487684658"/><GUILayout><gpos v="-1007.99347 -144 0"/></GUILayout><compOutputs><compOutput><uid v="1487684659"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="Background"/><connRef v="1487682523"/><connRefOutput v="1487682524"/></connection></connections><compImplementation><compInstance><path v="pkg:///color?dependency=1290776863"/><parameters/><outputBridgings><outputBridging><uid v="1487684659"/><identifier v="Color"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487685985"/><GUILayout><gpos v="-702.444275 -848 0"/></GUILayout><compOutputs><compOutput><uid v="1487685986"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487710304"/></connection></connections><compImplementation><compFilter><filter v="gradient"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1487686292"/><GUILayout><gpos v="-304 -816 0"/></GUILayout><compOutputs><compOutput><uid v="1487686293"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="destination"/><connRef v="1487683616"/><connRefOutput v="1487683617"/></connection><connection><identifier v="source"/><connRef v="1487686337"/><connRefOutput v="1487686338"/></connection><connection><identifier v="opacity"/><connRef v="1487686376"/><connRefOutput v="1487686377"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1487686337"/><GUILayout><gpos v="-510.444275 -976 0"/></GUILayout><compOutputs><compOutput><uid v="1487686338"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="uniform"/><parameters><parameter><name v="outputcolor"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.913725555 0.454224646 0.909801006 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487686376"/><GUILayout><gpos v="-570.674011 -848 0"/></GUILayout><compOutputs><compOutput><uid v="1487686377"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487685985"/></connection></connections><compImplementation><compFilter><filter v="grayscaleconversion"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1487692240"/><GUILayout><gpos v="-1456 80 501"/></GUILayout><compOutputs><compOutput><uid v="1487677951"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="pattern_input_1"/><connRef v="1487669977"/><connRefOutput v="1487669978"/></connection></connections><compImplementation><compInstance><path v="pkg:///tile_generator?dependency=1386931028"/><parameters><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.269999981"/></paramValue></parameter><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.419999987"/></paramValue></parameter><parameter><name v="pattern_rotation_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.599999964"/></paramValue></parameter><parameter><name v="pattern_symmetry_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="rotation_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.659999967"/></paramValue></parameter><parameter><name v="rotation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.51111114"/></paramValue></parameter><parameter><name v="randomseed"/><relativeTo v="2"/><paramValue><constantValueInt32 v="8"/></paramValue></parameter><parameter><name v="pattern_input_number"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="position_offset"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="position_offset_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.98999995"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487677951"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487692247"/><GUILayout><gpos v="-1341.83643 80 501"/></GUILayout><compOutputs><compOutput><uid v="1487683418"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487692240"/><connRefOutput v="1487677951"/></connection></connections><compImplementation><compFilter><filter v="transformation"/><parameters><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.272799999 0.356900007"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487692374"/><GUILayout><gpos v="-1342.81213 190.187866 502"/></GUILayout><compOutputs><compOutput><uid v="1487677951"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="pattern_input_1"/><connRef v="1487669977"/><connRefOutput v="1487669978"/></connection></connections><compImplementation><compInstance><path v="pkg:///tile_generator?dependency=1386931028"/><parameters><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.149999991"/></paramValue></parameter><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="position_offset_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.859999955"/></paramValue></parameter><parameter><name v="offset_random_seed"/><relativeTo v="0"/><paramValue><constantValueInt32 v="271"/></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.179999992 0.429999977"/></paramValue></parameter><parameter><name v="global_offset"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.579999983 0.449999988"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.419999987"/></paramValue></parameter><parameter><name v="pattern_rotation_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.599999964"/></paramValue></parameter><parameter><name v="pattern_symmetry_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487677951"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487692470"/><GUILayout><gpos v="-688 272 0"/></GUILayout><compOutputs><compOutput><uid v="1487692471"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="source"/><connRef v="1487692247"/><connRefOutput v="1487683418"/></connection><connection><identifier v="destination"/><connRef v="1487692374"/><connRefOutput v="1487677951"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487692617"/><GUILayout><gpos v="-688 144 501"/></GUILayout><compOutputs><compOutput><uid v="1487686338"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="uniform"/><parameters><parameter><name v="outputcolor"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="1 0.973451614 0 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487692859"/><GUILayout><gpos v="48 -720 0"/></GUILayout><compOutputs><compOutput><uid v="1487692860"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="destination"/><connRef v="1487664054"/><connRefOutput v="1486907756"/></connection><connection><identifier v="opacity"/><connRef v="1487692470"/><connRefOutput v="1487692471"/></connection><connection><identifier v="source"/><connRef v="1487692617"/><connRefOutput v="1487686338"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1488297733"/><GUILayout><gpos v="336 -240 0"/></GUILayout><compOutputs><compOutput><uid v="1488297734"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input"/><connRef v="1487664040"/><connRefOutput v="1482593511"/></connection></connections><compImplementation><compInstance><path v="pkg:///hbao?dependency=1297162667"/><parameters><parameter><name v="height_depth"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0299999993"/></paramValue></parameter><parameter><name v="samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="16"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1488297734"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode></compNodes><baseParameters/><options><option><name v="defaultParentSize"/><value v="11x11"/></option><option><name v="export/fromGraph/autoExport"/><value v="false"/></option><option><name v="export/fromGraph/destination"/><value v="C:/Users/<USER>/Desktop"/></option><option><name v="export/fromGraph/extension"/><value v="png"/></option><option><name v="export/fromGraph/outputs/ambientOcclusion"/><value v="true"/></option><option><name v="export/fromGraph/outputs/basecolor"/><value v="true"/></option><option><name v="export/fromGraph/outputs/height"/><value v="true"/></option><option><name v="export/fromGraph/outputs/normal"/><value v="true"/></option><option><name v="export/fromGraph/outputs/roughness"/><value v="true"/></option><option><name v="export/fromGraph/outputsColorspace/ambientOcclusion"/><value v="Raw"/></option><option><name v="export/fromGraph/outputsColorspace/basecolor"/><value v="sRGB"/></option><option><name v="export/fromGraph/outputsColorspace/height"/><value v="Raw"/></option><option><name v="export/fromGraph/outputsColorspace/normal"/><value v="Raw"/></option><option><name v="export/fromGraph/outputsColorspace/roughness"/><value v="Raw"/></option><option><name v="export/fromGraph/pattern"/><value v="$(graph)_$(identifier)"/></option></options><root><rootOutputs><rootOutput><output v="1487664056"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1487664058"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1487664060"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1487664064"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1487664066"/><format v="0"/><usertag v=""/></rootOutput></rootOutputs></root></graph></content></package>
