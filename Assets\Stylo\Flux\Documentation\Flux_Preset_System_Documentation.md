# Flux Preset System Documentation

## Overview

The Flux Preset System provides a comprehensive solution for saving, loading, and sharing datamoshing effect configurations. Built on Unity's ScriptableObject architecture, it offers seamless integration with both Built-in Render Pipeline and URP workflows.

---

## 🎯 **Key Features**

### **1. Complete Parameter Capture**

- **All 15+ parameters** including new advanced datamoshing features
- **Cross-pipeline compatibility** (Built-in RP and URP)
- **Metadata support** (name, category, description)
- **Version control friendly** ScriptableObject assets

### **2. Intuitive User Interface**

- **Integrated inspector UI** for both Runtime and Volume components
- **One-click preset application** with undo support
- **Save current settings** as new presets
- **Categorized organization** for easy browsing

### **3. Asset Management**

- **Project-wide sharing** of preset assets
- **Folder organization** by category
- **Search and filtering** capabilities
- **Default preset creation** for common effects

---

## 📁 **File Structure**

```
Assets/Stylo Flux Effect/
├── FluxPreset.cs                    # Core preset ScriptableObject
├── FluxPresetManager.cs             # Utility management class
├── Editor/
│   ├── FluxEffectEditor.cs          # Runtime component editor
│   ├── FluxVolumeComponentEditor.cs # URP volume component editor
│   └── FluxPresetEditor.cs          # Preset asset editor
└── Presets/                         # Default preset storage
    ├── VHS Datamosh.asset
    ├── Digital Glitch.asset
    ├── Subtle Compression.asset
    └── Satellite Feed Loss.asset
```

---

## 🚀 **Getting Started**

### **Step 1: Create Default Presets**

```
Tools → Stylo → Flux → Create Default Presets
```

This creates 4 starter presets in `Assets/Stylo Flux Effect/Presets/`

### **Step 2: Using Presets**

#### **For Runtime Flux Effect (Built-in RP):**

1. Select your Flux Effect component
2. In the inspector, expand "Flux Presets"
3. Choose a preset from the dropdown
4. Click "Apply Preset"

#### **For URP Volume Component:**

1. Select your Volume or Volume Profile
2. Locate the Flux Effect override
3. Expand "Flux Presets" at the top
4. Choose and apply your desired preset

### **Step 3: Creating Custom Presets**

1. Configure your Flux effect parameters
2. Click "Save Current as Preset"
3. Enter a name and category
4. The preset is saved as a project asset

---

## 🎨 **Default Presets**

### **VHS Datamosh**

- **Category**: VHS
- **Description**: Classic VHS-style datamoshing with temporal trails
- **Key Features**: Motion corruption, error accumulation, chroma bleeding
- **Use Case**: Retro video effects, nostalgic aesthetics

### **Digital Glitch**

- **Category**: Digital
- **Description**: Modern digital glitch art with feedback loops
- **Key Features**: Multi-scale corruption, feedback loops, DCT artifacts
- **Use Case**: Cyberpunk aesthetics, digital art, music videos

### **Subtle Compression**

- **Category**: Subtle
- **Description**: Light compression artifacts for realistic degradation
- **Key Features**: Minimal DCT corruption, light chroma separation
- **Use Case**: Realistic video quality simulation, broadcast effects

### **Satellite Feed Loss**

- **Category**: Cinematic
- **Description**: Simulates satellite transmission corruption
- **Key Features**: Heavy motion corruption, error accumulation, large blocks
- **Use Case**: Sci-fi scenes, communication breakdown effects

---

## 🔧 **Advanced Usage**

### **Preset Categories**

Organize presets by category for better management:

- **VHS**: Retro video effects
- **Digital**: Modern glitch art
- **Cinematic**: Film and TV effects
- **Subtle**: Realistic compression
- **Custom**: User-created presets

### **Programmatic Access**

```csharp
// Get all presets
var presets = FluxPresetManager.GetAllPresets();

// Find specific preset
var vhsPreset = FluxPresetManager.FindPresetByName("VHS Datamosh");

// Apply to Runtime component
vhsPreset.ApplyToRuntime(fluxEffect);

// Apply to URP component
vhsPreset.ApplyToURP(urpFluxEffect);
```

### **Custom Preset Creation**

```csharp
// Create new preset asset
var newPreset = FluxPresetManager.CreatePresetAsset("My Custom Effect");

// Configure preset
newPreset.presetName = "Custom Glitch";
newPreset.category = "Experimental";
newPreset.description = "My unique datamosh effect";

// Capture from existing effect
newPreset.CaptureFromRuntime(myFluxEffect);

// Save changes
EditorUtility.SetDirty(newPreset);
AssetDatabase.SaveAssets();
```

---

## 🎛️ **Parameter Coverage**

### **Core Parameters**

- Effect Intensity
- Color Crunch
- Downscaling
- Block Size
- Oversharpening
- Don't Crunch Skybox
- Only Stenciled

### **Basic Datamoshing**

- Base Noise
- Base Reroll Speed
- Length Influence

### **Advanced Datamoshing**

- Keyframe Reset Rate
- Motion Vector Corruption
- Error Accumulation
- DCT Corruption

### **Enhanced Corruption**

- Corruption Mask (Texture2D)
- Chroma Corruption
- Glitch Transition
- Feedback Intensity
- Multi-Scale Corruption

### **Debug Options**

- Visualize Motion Vectors

---

## 🔄 **Workflow Integration**

### **Version Control**

- Preset assets are stored as `.asset` files
- Fully compatible with Git, Perforce, etc.
- Share presets across team members
- Track preset changes over time

### **Build Pipeline**

- Presets can be included in builds via Resources folder
- Runtime preset loading supported
- Minimal performance impact

### **Cross-Project Sharing**

1. Export preset assets as Unity packages
2. Share individual `.asset` files
3. Copy presets between projects
4. Maintain consistent effects across projects

---

## 🛠️ **Menu Commands**

### **Stylo Menu**

- `Stylo → Flux → Create Default Presets`
- `Stylo → Flux → Refresh Preset Cache`

### **Assets Menu**

- `Assets → Create → Stylo → Flux Preset`

### **Context Menu**

- Right-click in Project window → `Create → Stylo → Flux Preset`

---

## 🎯 **Best Practices**

### **Naming Conventions**

- Use descriptive names: "VHS Datamosh" vs "Preset1"
- Include intensity level: "Heavy Glitch", "Subtle Compression"
- Specify use case: "Cinematic Impact", "UI Safe Glitch"

### **Organization**

- Group by category in folders
- Use consistent naming patterns
- Document preset purposes
- Create preset collections for projects

### **Performance**

- Test presets on target hardware
- Consider mobile-friendly variants
- Document performance characteristics
- Use appropriate block sizes for platform

---

## 🔍 **Troubleshooting**

### **Preset Not Appearing**

- Check preset is in project
- Refresh preset cache via menu
- Verify preset asset is not corrupted

### **Parameters Not Applying**

- Ensure correct pipeline (Built-in vs URP)
- Check for component/volume presence
- Verify preset compatibility

### **Performance Issues**

- Review preset parameter values
- Test on target platform
- Consider creating optimized variants

---

## 🚀 **Future Enhancements**

The preset system is designed for extensibility:

- **Preset interpolation** between multiple presets
- **Animation curve support** for parameter transitions
- **Conditional presets** based on scene conditions
- **Cloud sharing** integration
- **Preset validation** and compatibility checking

---

**The Flux Preset System transforms datamoshing workflow from parameter tweaking to creative exploration, enabling artists to focus on visual storytelling rather than technical configuration.**
