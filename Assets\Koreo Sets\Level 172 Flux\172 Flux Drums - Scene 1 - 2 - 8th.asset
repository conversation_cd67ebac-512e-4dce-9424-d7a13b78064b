%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -1223223985, guid: aa81bbec85a3f423193272a7c6154986, type: 3}
  m_Name: 172 Flux Drums - Scene 1 - 2 - 8th
  m_EditorClassIdentifier: 
  mEventID: 8th
  mEventList:
  - mStartSample: 0
    mEndSample: 0
  - mStartSample: 15383
    mEndSample: 15383
  - mStartSample: 30767
    mEndSample: 30767
  - mStartSample: 46151
    mEndSample: 46151
  - mStartSample: 61534
    mEndSample: 61534
  - mStartSample: 76918
    mEndSample: 76918
  - mStartSample: 92302
    mEndSample: 92302
  - mStartSample: 107686
    mEndSample: 107686
  - mStartSample: 123069
    mEndSample: 123069
  - mStartSample: 138452
    mEndSample: 138452
  - mStartSample: 153836
    mEndSample: 153836
  - mStartSample: 169220
    mEndSample: 169220
  - mStartSample: 184603
    mEndSample: 184603
  - mStartSample: 199987
    mEndSample: 199987
  - mStartSample: 215371
    mEndSample: 215371
  - mStartSample: 230755
    mEndSample: 230755
  - mStartSample: 246139
    mEndSample: 246139
  - mStartSample: 261522
    mEndSample: 261522
  - mStartSample: 276906
    mEndSample: 276906
  - mStartSample: 292290
    mEndSample: 292290
  - mStartSample: 307673
    mEndSample: 307673
  - mStartSample: 323057
    mEndSample: 323057
  - mStartSample: 338441
    mEndSample: 338441
  - mStartSample: 353825
    mEndSample: 353825
  - mStartSample: 369209
    mEndSample: 369209
  - mStartSample: 384592
    mEndSample: 384592
  - mStartSample: 399976
    mEndSample: 399976
  - mStartSample: 415360
    mEndSample: 415360
  - mStartSample: 430743
    mEndSample: 430743
  - mStartSample: 446127
    mEndSample: 446127
  - mStartSample: 461511
    mEndSample: 461511
  - mStartSample: 476895
    mEndSample: 476895
  _SerializedPayloadTypes: []
  _AssetPayloads: []
  _AssetPayloadIdxs: 
  _ColorPayloads: []
  _ColorPayloadIdxs: 
  _CurvePayloads: []
  _CurvePayloadIdxs: 
  _FloatPayloads: []
  _FloatPayloadIdxs: 
  _GradientPayloads: []
  _GradientPayloadIdxs: 
  _IntPayloads: []
  _IntPayloadIdxs: 
  _SpectrumPayloads: []
  _SpectrumPayloadIdxs: 
  _TextPayloads: []
  _TextPayloadIdxs: 
