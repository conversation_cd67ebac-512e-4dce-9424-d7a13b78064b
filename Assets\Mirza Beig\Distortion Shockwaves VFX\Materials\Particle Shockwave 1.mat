%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-2752676988557032495
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0353a89b1f911e48b9e16bdc9f2e058, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 7
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Particle Shockwave 1
  m_Shader: {fileID: 4800000, guid: 044a951a8cf50eb45a09f6c9dc24a512, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords:
  - _RECEIVE_SHADOWS_OFF
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BaseMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AlphaClip: 0
    - _AlphaCutoff: 0.5
    - _AlphaToMask: 0
    - _Alpha_Mask_Power: 0.5
    - _Animation: 1
    - _Blend: 0
    - _BlendModePreserveSpecular: 1
    - _BumpScale: 1
    - _ClearCoatMask: 0
    - _ClearCoatSmoothness: 0
    - _Cull: 2
    - _Cutoff: 0.5
    - _Debug: 0
    - _DetailAlbedoMapScale: 1
    - _DetailNormalMapScale: 1
    - _Distortion: 0.1
    - _DistortionDepthFade: 1
    - _DistortionDepthFadePower: 2
    - _DistortionNormalStrength: 1
    - _Distortion_Mask_Power: 1
    - _Distortion_Max: 1
    - _DstBlend: 0
    - _DstBlendAlpha: 0
    - _EnvironmentReflections: 1
    - _Float0: 0
    - _Float1: 1
    - _GlossMapScale: 0
    - _Glossiness: 0
    - _GlossyReflections: 0
    - _InnerRadialDistortionMaskAlphaPower: 0.23
    - _InnerRadialDistortionMaskHardness: 0.5
    - _InnerRadialDistortionMaskPower: 2
    - _InnerRadialDistortionMaskRadius: 1
    - _InnerRadialDistortionMaskasAlpha: 0
    - _InnerRadialDistortionMaskasAlphaPower: 1
    - _Inner_Feather: 0.5
    - _Inner_Radius: 1
    - _Inner_Remap_Max: 0.5
    - _Inner_Remap_Min: 0.3
    - _Inner_Size: 0
    - _Metallic: 0
    - _Minimum: -1
    - _NormalizeDistortion: 1
    - _NormalizedDistortion: 0
    - _OcclusionStrength: 1
    - _OuterRadialDistortionMaskPower: 2
    - _OuterRadialDistortionMaskRemapMax: 1
    - _OuterRadialDistortionMaskRemapMin: 0
    - _Outer_Feather: 0.25
    - _Outer_Remap_Max: 0.2
    - _Outer_Remap_Min: 0
    - _Parallax: 0.005
    - _ParticleAnimation: 2
    - _ParticleRandomize: 0.2412162
    - _Particle_Animation: 1
    - _Power: 1
    - _QueueControl: 0
    - _QueueOffset: 0
    - _RadialAlphaMaskEdgeFade: 0
    - _RadialAlphaMaskFeather: 0.1
    - _RadialAlphaMaskPower: 0.1
    - _RadialAlphaMaskRemapMax: 0.085
    - _Radial_Alpha_Mask_Edge_Fade: 0.1
    - _Radial_Alpha_Mask_Max: 0.1
    - _Radial_Alpha_Mask_Power: 1
    - _Radial_Distortion_Inner_Mask_Alpha_Power: 0.01
    - _Radial_Distortion_Inner_Mask_Feather: 0.5
    - _Radial_Distortion_Inner_Mask_Hardness: 1
    - _Radial_Distortion_Inner_Mask_Power: 1
    - _Radial_Distortion_Inner_Mask_Radius: 1
    - _Radial_Distortion_Mask_Power: 1.2
    - _Radial_Distortion_Mask_Power_1: 1
    - _Radial_Distortion_Mask_Power_2: 1
    - _Radial_Distortion_Mask_Power_3: 1
    - _Radial_Mask_Power: 5
    - _ReceiveShadows: 0
    - _ShockwaveNormalStrength: 0.1
    - _Smoothness: 0.5
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _Speed: 0
    - _Spherize: 0
    - _SrcBlend: 1
    - _SrcBlendAlpha: 1
    - _Surface: 0
    - _TessEdgeLength: 16
    - _TessMax: 25
    - _TessMaxDisp: 25
    - _TessMin: 10
    - _TessPhongStrength: 0.5
    - _TessValue: 16
    - _Tiliing: 3
    - _Tiling: 2
    - _WaveSmoothness: 1
    - _WaveSmoothstep: 0
    - _WorkflowMode: 1
    - _ZWrite: 1
    m_Colors:
    - _BaseColor: {r: 1, g: 1, b: 1, a: 1}
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _Color0: {r: 1, g: 1, b: 1, a: 1}
    - _Colour: {r: 1, g: 1, b: 1, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _SpecColor: {r: 0.19999996, g: 0.19999996, b: 0.19999996, a: 1}
    - _Vector0: {r: 0, g: 0, b: 0, a: 0}
  m_BuildTextureStacks: []
