%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7be522ae007ba5a4f87ffaaf2e793f4c, type: 3}
  m_Name: Enhanced Effects Demo
  m_EditorClassIdentifier: 
  presetName: Enhanced Effects Demo
  description: Showcases Flux's unique enhanced features - camera motion sensitivity, pixel flow, trail smoothness, and artistic effects. Minimal authentic datamoshing to highlight modern enhancements.
  category: Enhanced
  effectIntensity: 0.8
  colorCrunch: 0.3
  downscaling: 8
  blockSize: 1
  oversharpening: 0.3
  dontCrunchSkybox: 1
  onlyStenciled: 0
  reprojectBaseNoise: 0.05
  reprojectBaseRerollSpeed: 3
  reprojectLengthInfluence: 0.2
  cameraMotionAmplification: 5
  cameraMotionThreshold: 0.005
  cameraMotionInfluence: 8
  cameraMotionSmoothing: 0.3
  pixelFlowIntensity: 8
  trailSmoothness: 0.9
  motionPersistence: 0.8
  flowGradient: 4
  temporalAccumulation: 0.7
  keyframeResetRate: 0
  motionVectorCorruption: 0.2
  errorAccumulation: 0.1
  dctCorruption: 0
  corruptionMask: {fileID: 0}
  chromaCorruption: 0.1
  glitchTransition: 0.5
  feedbackIntensity: 0.3
  multiScaleCorruption: 0.6
  visualizeMotionVectors: 0
