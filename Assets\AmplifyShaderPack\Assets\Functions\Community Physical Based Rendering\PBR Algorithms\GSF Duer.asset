%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: GSF Duer
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor v1.9.4.4\n// Available at the
    Unity Asset Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=19404\nNode;AmplifyShaderEditor.CommentaryNode;35;-1280,1920;Inherit;False;969.9723;823.165;Math;9;217;87;417;416;264;339;261;338;420;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;420;-1170.751,2432;Inherit;False;404.9814;282.1353;Original
    Formula;2;419;418;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;267;-2334.863,1920;Inherit;False;1024.329;575.2021;Dot
    Products;9;347;300;350;346;332;325;320;421;422;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;338;-1232,1984;Inherit;False;194;142;NdotH;1;337;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;261;-992,1984;Inherit;False;223.9009;257.2463;Power
    of 4;1;259;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;232;-1278.799,1568;Inherit;False;785.7505;291.7751;GSF
    Duer Node;1;46;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;339;-992,2256;Inherit;False;202;136;HdotH;1;340;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;46;-896,1648;Inherit;False;382.2368;148.2119;;;0,0,0,1;The
    Duer GSF produces similar results as the Ashikhmin-Shirley, but is more suited
    towards Isotropic BRDFs, or very slightly Anisotropic BRDF -Jorden Stevens;0;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;259;-976,2032;Inherit;True;4;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.StickyNoteNode;264;-736,1984;Inherit;False;220.5312;125.9782;;;0,0,0,1;Hard
    coding Power with multiple can be a lot cheaper than use of power node;0;0\nNode;AmplifyShaderEditor.GetLocalVarNode;337;-1216,2032;Inherit;False;332;NdotH;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.StickyNoteNode;351;-1280,1376;Inherit;False;558.8756;147.9872;Credits;;0,0,0,0.5019608;Method
    by Jordan Stevens$Link: https://www.jordanstevenstechart.com/physically-based-rendering$$Node
    Made by Tiber Legacy$Special Thanks to Dawie;0;0\nNode;AmplifyShaderEditor.FunctionInput;320;-2048,2080;Inherit;False;NdotH;1;1;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;325;-1888,2000;Inherit;False;Custom
    Dot Products;True;0;2;1;In 0;In 1;Object;-1;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;332;-1600,2000;Inherit;True;NdotH;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;346;-2048,2320;Inherit;False;HdotH;1;2;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;350;-1600,2240;Inherit;True;HdotH;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;300;-2272,2000;Inherit;False;Normal;3;0;False;1;0;FLOAT3;0,0,1;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitch;347;-1888,2240;Inherit;False;Option;False;0;2;1;In
    0;In 1;Instance;325;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;340;-976,2304;Inherit;False;350;HdotH;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.PowerNode;418;-992,2480;Inherit;True;True;2;0;FLOAT;0;False;1;FLOAT;-4;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;419;-1152,2496;Inherit;False;Constant;_Pow4;Pow
    4;0;0;Create;True;0;0;0;False;0;False;4;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;416;-752,2096;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;417;-752,2304;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;87;-704,2304;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionNode;421;-2112,2000;Inherit;False;NdotH;-1;;3;fadb6690f179c1448859861f76ebb061;7,27,1,373,0,451,0,463,0,460,0,461,0,452,0;6;230;FLOAT3;0,0,1;False;443;FLOAT3;0,0,0;False;462;FLOAT3;0,0,0;False;454;FLOAT3;0,0,0;False;453;FLOAT3;0,0,0;False;417;FLOAT;1E-37;False;1;FLOAT;109\nNode;AmplifyShaderEditor.FunctionNode;422;-2112,2240;Inherit;False;HdotH;-1;;4;539961ce417869d4abdd54ee29639f41;5,393,0,443,0,445,0,121,0,447,0;5;440;FLOAT3;0,0,0;False;439;FLOAT3;0,0,0;False;130;FLOAT3;0,0,0;False;446;FLOAT3;0,0,0;False;422;FLOAT;1E-37;False;1;FLOAT;296\nNode;AmplifyShaderEditor.FunctionOutput;217;-544,2304;Inherit;True;True;-1;Out;0;False;1;0;FLOAT;0;False;1;FLOAT;0\nWireConnection;259;0;337;0\nWireConnection;259;1;337;0\nWireConnection;259;2;337;0\nWireConnection;259;3;337;0\nWireConnection;325;0;421;109\nWireConnection;325;1;320;0\nWireConnection;332;0;325;0\nWireConnection;350;0;347;0\nWireConnection;347;0;422;296\nWireConnection;347;1;346;0\nWireConnection;418;0;337;0\nWireConnection;418;1;419;0\nWireConnection;416;0;259;0\nWireConnection;417;0;416;0\nWireConnection;87;0;340;0\nWireConnection;87;1;417;0\nWireConnection;421;230;300;0\nWireConnection;217;0;87;0\nASEEND*/\n//CHKSM=CF63E85047F09D8193A3FFC143A5F9DBCA888ADF"
  m_functionName: 
  m_description: 'GSF Duer Node

'
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 0
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_customEdited: 0
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 0
  m_headerStyle: 2
  m_headerColor: {r: 0.7529412, g: 0.6784314, b: 0, a: 1}
  m_customNodeCategory: Amplify Shader Pack
  m_previewPosition: 0
  m_hidden: 0
  m_url: 
