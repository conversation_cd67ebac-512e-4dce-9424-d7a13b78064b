using UnityEngine;
using UnityEditor;
using BTR;

namespace BTR
{
    [CustomEditor(typeof(MeshPrefabPlacer))]
    public class MeshPrefabPlacerEditor : UnityEditor.Editor
    {
        // Cache for optimization
        private SerializedProperty prefabToPlaceProp;
        private SerializedProperty targetMeshObjectProp;

        private void OnEnable()
        {
            // Cache serialized properties (more efficient than accessing directly)
            prefabToPlaceProp = serializedObject.FindProperty("prefabToPlace");
            targetMeshObjectProp = serializedObject.FindProperty("targetMeshObject");
        }

        public override void OnInspectorGUI()
        {
            // CRITICAL: Always update the serializedObject at the start
            serializedObject.Update();

            try
            {
                // Draw properties without the default inspector which could be inefficient
                EditorGUILayout.PropertyField(targetMeshObjectProp);
                EditorGUILayout.PropertyField(prefabToPlaceProp);

                // Draw remaining properties excluding the ones we've already handled
                DrawPropertiesExcluding(serializedObject, new string[] { "m_Script", "prefabToPlace", "targetMeshObject" });

                // CRITICAL: Always apply modified properties
                serializedObject.ApplyModifiedProperties();

                // Simple buttons without complex validation logic
                GUILayout.Space(10);

                if (GUILayout.Button("Generate Prefabs"))
                {
                    MeshPrefabPlacer placer = (MeshPrefabPlacer)target;
                    if (placer != null)
                    {
                        placer.GeneratePrefabs();
                    }
                }

                if (GUILayout.Button("Clear Placed Prefabs"))
                {
                    MeshPrefabPlacer placer = (MeshPrefabPlacer)target;
                    if (placer != null)
                    {
                        placer.ClearPlacedPrefabs();
                    }
                }
            }
            catch (System.Exception e)
            {
                // Catch and display any errors that might cause editor hangs
                EditorGUILayout.HelpBox("Error in inspector: " + e.Message, MessageType.Error);
                Debug.LogException(e);
            }
        }
    }
}