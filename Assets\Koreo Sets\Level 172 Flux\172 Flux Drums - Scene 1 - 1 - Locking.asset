%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -1223223985, guid: aa81bbec85a3f423193272a7c6154986, type: 3}
  m_Name: 172 Flux Drums - Scene 1 - 1 - Locking
  m_EditorClassIdentifier: 
  mEventID: Locking
  mEventList:
  - mStartSample: 0
    mEndSample: 0
  - mStartSample: 30767
    mEndSample: 30767
  - mStartSample: 61534
    mEndSample: 61534
  - mStartSample: 92302
    mEndSample: 92302
  - mStartSample: 123069
    mEndSample: 123069
  - mStartSample: 153837
    mEndSample: 153837
  - mStartSample: 184604
    mEndSample: 184604
  - mStartSample: 215372
    mEndSample: 215372
  _SerializedPayloadTypes: []
  _AssetPayloads: []
  _AssetPayloadIdxs: 
  _ColorPayloads: []
  _ColorPayloadIdxs: 
  _CurvePayloads: []
  _CurvePayloadIdxs: 
  _FloatPayloads: []
  _FloatPayloadIdxs: 
  _GradientPayloads: []
  _GradientPayloadIdxs: 
  _IntPayloads: []
  _IntPayloadIdxs: 
  _SpectrumPayloads: []
  _SpectrumPayloadIdxs: 
  _TextPayloads: []
  _TextPayloadIdxs: 
