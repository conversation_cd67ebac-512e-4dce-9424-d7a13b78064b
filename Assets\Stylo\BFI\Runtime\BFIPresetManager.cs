#if URP_INSTALLED
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.Rendering;

namespace Stylo.BFI.Universal
{
    public static class BFIPresetManager
    {
        private static List<BFIPreset> _cachedPresets;
        
        /// <summary>
        /// Get all BFI presets in the project
        /// </summary>
        /// <returns>List of all BFI presets</returns>
        public static List<BFIPreset> GetAllPresets()
        {
            if (_cachedPresets == null)
            {
                RefreshPresetCache();
            }
            return _cachedPresets ?? new List<BFIPreset>();
        }
        
        /// <summary>
        /// Refresh the preset cache by scanning for all BFI presets in the project
        /// </summary>
        public static void RefreshPresetCache()
        {
            _cachedPresets = new List<BFIPreset>();
            
#if UNITY_EDITOR
            string[] guids = UnityEditor.AssetDatabase.FindAssets("t:BFIPreset");
            foreach (string guid in guids)
            {
                string path = UnityEditor.AssetDatabase.GUIDToAssetPath(guid);
                BFIPreset preset = UnityEditor.AssetDatabase.LoadAssetAtPath<BFIPreset>(path);
                if (preset != null)
                {
                    _cachedPresets.Add(preset);
                }
            }
#endif
        }
        
        /// <summary>
        /// Get a preset by name
        /// </summary>
        /// <param name="presetName">Name of the preset to find</param>
        /// <returns>The preset if found, null otherwise</returns>
        public static BFIPreset GetPresetByName(string presetName)
        {
            var presets = GetAllPresets();
            return presets.FirstOrDefault(p => p.presetName == presetName);
        }
        
        /// <summary>
        /// Apply a preset to the current volume stack
        /// </summary>
        /// <param name="preset">The preset to apply</param>
        /// <returns>True if successfully applied, false otherwise</returns>
        public static bool ApplyPresetToCurrentStack(BFIPreset preset)
        {
            if (preset == null) return false;
            
            var stack = VolumeManager.instance.stack;
            var bfiEffect = stack.GetComponent<BFIEffect>();
            
            if (bfiEffect == null) return false;
            
            preset.ApplyToEffect(bfiEffect);
            return true;
        }
        
        /// <summary>
        /// Create and save default presets for all BFI modes
        /// </summary>
        public static void CreateDefaultPresets()
        {
#if UNITY_EDITOR
            string presetFolder = "Assets/Stylo/BFI/Presets";
            
            // Ensure the presets folder exists
            if (!UnityEditor.AssetDatabase.IsValidFolder(presetFolder))
            {
                UnityEditor.AssetDatabase.CreateFolder("Assets/Stylo/BFI", "Presets");
            }
            
            // Create default presets for each BFI mode
            var modes = System.Enum.GetValues(typeof(BFIEffect.BFIMode)).Cast<BFIEffect.BFIMode>();
            
            foreach (var mode in modes)
            {
                string presetPath = $"{presetFolder}/BFI_{mode}_Default.asset";
                
                // Skip if preset already exists
                if (UnityEditor.AssetDatabase.LoadAssetAtPath<BFIPreset>(presetPath) != null)
                    continue;
                
                var preset = BFIPreset.CreateDefaultPreset(mode);
                UnityEditor.AssetDatabase.CreateAsset(preset, presetPath);
            }
            
            // Create additional specialized presets
            CreateSpecializedPresets(presetFolder);
            
            UnityEditor.AssetDatabase.SaveAssets();
            UnityEditor.AssetDatabase.Refresh();
            RefreshPresetCache();
#endif
        }
        
#if UNITY_EDITOR
        private static void CreateSpecializedPresets(string presetFolder)
        {
            // Gaming Preset - Optimized for competitive gaming
            var gamingPreset = BFIPreset.CreateDefaultPreset(BFIEffect.BFIMode.DoubleFrameRate);
            gamingPreset.presetName = "Gaming Optimized";
            gamingPreset.description = "Optimized for competitive gaming with minimal flicker and good motion clarity.";
            gamingPreset.motionAdaptive = true;
            gamingPreset.motionSensitivity = 2f;
            gamingPreset.flickerReductionMode = BFIEffect.FlickerReduction.High;
            gamingPreset.temporalSmoothing = 0.3f;
            UnityEditor.AssetDatabase.CreateAsset(gamingPreset, $"{presetFolder}/BFI_Gaming_Optimized.asset");
            
            // Cinema Preset - Optimized for movie watching
            var cinemaPreset = BFIPreset.CreateDefaultPreset(BFIEffect.BFIMode.TripleFrameRate);
            cinemaPreset.presetName = "Cinema Experience";
            cinemaPreset.description = "Optimized for cinematic content with smooth motion and reduced flicker.";
            cinemaPreset.flickerReductionMode = BFIEffect.FlickerReduction.High;
            cinemaPreset.temporalSmoothing = 0.4f;
            cinemaPreset.edgePreservation = 0.9f;
            cinemaPreset.brightnessCompensation = 2f;
            UnityEditor.AssetDatabase.CreateAsset(cinemaPreset, $"{presetFolder}/BFI_Cinema_Experience.asset");
            
            // Performance Preset - Minimal overhead
            var performancePreset = BFIPreset.CreateDefaultPreset(BFIEffect.BFIMode.DoubleFrameRate);
            performancePreset.presetName = "Performance Mode";
            performancePreset.description = "Lightweight BFI with minimal performance impact.";
            performancePreset.flickerReductionMode = BFIEffect.FlickerReduction.Low;
            performancePreset.temporalSmoothing = 0.1f;
            performancePreset.motionAdaptive = false;
            performancePreset.subpixelPrecision = 0.5f;
            UnityEditor.AssetDatabase.CreateAsset(performancePreset, $"{presetFolder}/BFI_Performance_Mode.asset");
            
            // Debug Preset - For testing and development
            var debugPreset = BFIPreset.CreateDefaultPreset(BFIEffect.BFIMode.DoubleFrameRate);
            debugPreset.presetName = "Debug Visualization";
            debugPreset.description = "Debug mode showing BFI timing and motion data visualization.";
            debugPreset.debugVisualization = true;
            debugPreset.intensity = 0.7f;
            debugPreset.flickerReductionMode = BFIEffect.FlickerReduction.None;
            UnityEditor.AssetDatabase.CreateAsset(debugPreset, $"{presetFolder}/BFI_Debug_Visualization.asset");
        }
#endif
        
        /// <summary>
        /// Get preset names for UI dropdown
        /// </summary>
        /// <returns>Array of preset names</returns>
        public static string[] GetPresetNames()
        {
            var presets = GetAllPresets();
            return presets.Select(p => p.presetName).ToArray();
        }
        
        /// <summary>
        /// Get recommended preset for a specific display refresh rate
        /// </summary>
        /// <param name="refreshRate">Display refresh rate in Hz</param>
        /// <returns>Recommended preset for the refresh rate</returns>
        public static BFIPreset GetRecommendedPresetForRefreshRate(int refreshRate)
        {
            BFIEffect.BFIMode recommendedMode;
            
            if (refreshRate >= 240)
                recommendedMode = BFIEffect.BFIMode.QuadFrameRate;
            else if (refreshRate >= 180)
                recommendedMode = BFIEffect.BFIMode.TripleFrameRate;
            else if (refreshRate >= 120)
                recommendedMode = BFIEffect.BFIMode.DoubleFrameRate;
            else
                recommendedMode = BFIEffect.BFIMode.Off;
            
            // Try to find a default preset for the recommended mode
            var presets = GetAllPresets();
            var defaultPreset = presets.FirstOrDefault(p => 
                p.mode == recommendedMode && p.presetName.Contains("Default"));
            
            return defaultPreset ?? presets.FirstOrDefault(p => p.mode == recommendedMode);
        }
    }
}
#endif
