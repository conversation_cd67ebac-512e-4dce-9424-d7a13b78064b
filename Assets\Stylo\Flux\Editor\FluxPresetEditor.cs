#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using Stylo.Flux;
#if URP_INSTALLED
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
#endif

namespace Stylo.Flux.Editor
{
    [CustomEditor(typeof(FluxPreset))]
    public class FluxPresetEditor : UnityEditor.Editor
    {
        private bool showCoreParameters = true;
        private bool showBasicDatamoshing = true;
        private bool showAdvancedDatamoshing = true;
        private bool showEnhancedCorruption = true;
        private bool showDebugOptions = false;

        public override void OnInspectorGUI()
        {
            FluxPreset preset = (FluxPreset)target;

            EditorGUILayout.Space(5);

            // Header with preset info
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("Flux Preset", EditorStyles.boldLabel);
            EditorGUILayout.LabelField($"Category: {preset.category}");
            if (!string.IsNullOrEmpty(preset.description))
            {
                EditorGUILayout.LabelField("Description:", EditorStyles.miniLabel);
                EditorGUILayout.LabelField(preset.description, EditorStyles.wordWrappedMiniLabel);
            }
            EditorGUILayout.EndVertical();

            EditorGUILayout.Space(10);

            // Apply to Scene button
            if (GUILayout.Button("Apply to Active Flux Effect in Scene", GUILayout.Height(30)))
            {
                ApplyPresetToScene(preset);
            }

            EditorGUILayout.Space(10);

            // Preset Information
            EditorGUILayout.LabelField("Preset Information", EditorStyles.boldLabel);
            preset.presetName = EditorGUILayout.TextField("Preset Name", preset.presetName);
            preset.category = EditorGUILayout.TextField("Category", preset.category);
            preset.description = EditorGUILayout.TextArea(preset.description, GUILayout.Height(60));

            EditorGUILayout.Space(10);

            // Core Parameters
            showCoreParameters = EditorGUILayout.BeginFoldoutHeaderGroup(showCoreParameters, "Core Parameters");
            if (showCoreParameters)
            {
                EditorGUILayout.BeginVertical("box");
                preset.effectIntensity = EditorGUILayout.Slider("Effect Intensity", preset.effectIntensity, 0f, 1f);
                preset.colorCrunch = EditorGUILayout.Slider("Color Crunch", preset.colorCrunch, 0f, 1f);
                preset.downscaling = EditorGUILayout.IntSlider("Downscaling", preset.downscaling, 1, 10);
                preset.blockSize = (FluxPreset.FluxBlockSize)EditorGUILayout.EnumPopup("Block Size", preset.blockSize);
                preset.oversharpening = EditorGUILayout.Slider("Oversharpening", preset.oversharpening, 0f, 10f);
                preset.dontCrunchSkybox = EditorGUILayout.Toggle("Don't Crunch Skybox", preset.dontCrunchSkybox);
                preset.onlyStenciled = EditorGUILayout.Toggle("Only Stenciled", preset.onlyStenciled);
                EditorGUILayout.EndVertical();
            }
            EditorGUILayout.EndFoldoutHeaderGroup();

            // Basic Datamoshing
            showBasicDatamoshing = EditorGUILayout.BeginFoldoutHeaderGroup(showBasicDatamoshing, "Basic Datamoshing");
            if (showBasicDatamoshing)
            {
                EditorGUILayout.BeginVertical("box");
                preset.reprojectBaseNoise = EditorGUILayout.Slider("Base Noise", preset.reprojectBaseNoise, 0f, 1f);
                preset.reprojectBaseRerollSpeed = EditorGUILayout.Slider("Base Reroll Speed", preset.reprojectBaseRerollSpeed, 0f, 20f);
                preset.reprojectLengthInfluence = EditorGUILayout.Slider("Length Influence", preset.reprojectLengthInfluence, 0f, 5f);
                EditorGUILayout.EndVertical();
            }
            EditorGUILayout.EndFoldoutHeaderGroup();

            // Advanced Datamoshing
            showAdvancedDatamoshing = EditorGUILayout.BeginFoldoutHeaderGroup(showAdvancedDatamoshing, "Advanced Datamoshing");
            if (showAdvancedDatamoshing)
            {
                EditorGUILayout.BeginVertical("box");
                preset.keyframeResetRate = EditorGUILayout.Slider("Keyframe Reset Rate", preset.keyframeResetRate, 0f, 1f);
                preset.motionVectorCorruption = EditorGUILayout.Slider("Motion Vector Corruption", preset.motionVectorCorruption, 0f, 2f);
                preset.errorAccumulation = EditorGUILayout.Slider("Error Accumulation", preset.errorAccumulation, 0f, 1f);
                preset.dctCorruption = EditorGUILayout.Slider("DCT Corruption", preset.dctCorruption, 0f, 1f);
                EditorGUILayout.EndVertical();
            }
            EditorGUILayout.EndFoldoutHeaderGroup();

            // Enhanced Corruption
            showEnhancedCorruption = EditorGUILayout.BeginFoldoutHeaderGroup(showEnhancedCorruption, "Enhanced Corruption");
            if (showEnhancedCorruption)
            {
                EditorGUILayout.BeginVertical("box");
                preset.corruptionMask = (Texture2D)EditorGUILayout.ObjectField("Corruption Mask", preset.corruptionMask, typeof(Texture2D), false);
                preset.chromaCorruption = EditorGUILayout.Slider("Chroma Corruption", preset.chromaCorruption, 0f, 1f);
                preset.glitchTransition = EditorGUILayout.Slider("Glitch Transition", preset.glitchTransition, 0f, 1f);
                preset.feedbackIntensity = EditorGUILayout.Slider("Feedback Intensity", preset.feedbackIntensity, 0f, 0.8f);
                preset.multiScaleCorruption = EditorGUILayout.Slider("Multi-Scale Corruption", preset.multiScaleCorruption, 0f, 1f);
                EditorGUILayout.EndVertical();
            }
            EditorGUILayout.EndFoldoutHeaderGroup();

            // Debug Options
            showDebugOptions = EditorGUILayout.BeginFoldoutHeaderGroup(showDebugOptions, "Debug Options");
            if (showDebugOptions)
            {
                EditorGUILayout.BeginVertical("box");
                preset.visualizeMotionVectors = EditorGUILayout.Toggle("Visualize Motion Vectors", preset.visualizeMotionVectors);
                EditorGUILayout.EndVertical();
            }
            EditorGUILayout.EndFoldoutHeaderGroup();

            // Save changes
            if (GUI.changed)
            {
                EditorUtility.SetDirty(preset);
            }
        }

        private void ApplyPresetToScene(FluxPreset preset)
        {
#if URP_INSTALLED
            // Try to find URP Volume with Flux Effect
            Volume[] volumes = FindObjectsByType<Volume>(FindObjectsSortMode.None);
            foreach (var volume in volumes)
            {
                if (volume.profile != null && volume.profile.TryGet<Stylo.Flux.Universal.FluxEffect>(out var urpFlux))
                {
                    Undo.RecordObject(volume.profile, "Apply Flux Preset");
                    preset.ApplyToURP(urpFlux);
                    EditorUtility.SetDirty(volume.profile);
                    EditorUtility.DisplayDialog("Preset Applied",
                        $"Preset '{preset.GetDisplayName()}' applied to URP Volume Flux Effect.", "OK");
                    return;
                }
            }
#endif

            EditorUtility.DisplayDialog("No Flux Effect Found",
                "No active Flux Effect found in the scene. Please add a Flux Effect component or Volume Profile with URP.", "OK");
        }

        [MenuItem("Stylo/Flux/Create Default Presets")]
        public static void CreateDefaultPresetsMenuItem()
        {
            FluxPresetManager.CreateDefaultPresets();
            EditorUtility.DisplayDialog("Default Presets Created",
                "Default Flux presets have been created in Assets/Stylo/Flux/Presets/", "OK");
        }

        [MenuItem("Stylo/Flux/Refresh Preset Cache")]
        public static void RefreshPresetCacheMenuItem()
        {
            FluxPresetManager.RefreshPresetCache();
            EditorUtility.DisplayDialog("Cache Refreshed",
                "Flux preset cache has been refreshed.", "OK");
        }

        [MenuItem("Assets/Create/Stylo/Flux Preset", false, 81)]
        public static void CreateFluxPresetAsset()
        {
            FluxPresetManager.CreatePresetAsset("New Flux Preset");
        }
    }
}
#endif
