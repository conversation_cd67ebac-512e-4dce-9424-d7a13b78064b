using System;
using UnityEngine;

namespace ExtendedImportSettings
{
    [Serializable]
    public class SimpleUVGeneratorData : PerMeshDataBase
    {
        public UVChannel UVChannel;
        public UVProjectionType UVProjectionType;
        public bool KeepAspectRatio;
    }

    [Serializable]
    public class SimpleUVGeneratorModule : PerMeshModuleBase<SimpleUVGeneratorData>
    {
        protected override void OnPostprocessModelPerMesh(Mesh mesh, SimpleUVGeneratorData data,
            GameObject meshGameObject, GameObject rootGameObject)
        {
            Vector3[] vertices = mesh.vertices;
            Vector2[] uvs = new Vector2[vertices.Length];

            switch (data.UVProjectionType)
            {
                case UVProjectionType.PlanarXY:
                case UVProjectionType.PlanarXZ:
                case UVProjectionType.PlanarZY:
                    uvs = GeneratePlanarUV(mesh, data.UVProjectionType, data.KeepAspectRatio);
                    break;
                case UVProjectionType.Spherical:
                    uvs = GenerateSphericalUV(mesh, data.KeepAspectRatio);
                    break;
                case UVProjectionType.Cubic:
                    uvs = GenerateCubicUV(mesh, data.KeepAspectRatio);
                    break;
            }

            var uvIndexes = data.UVChannel.ToIndexes();
            foreach (var uvIndex in uvIndexes)
                mesh.SetUVs(uvIndex, uvs);
        }
        
        private Vector2[] GeneratePlanarUV(Mesh mesh, UVProjectionType uvProjectionType, bool keepAspectRatio)
        {
            Vector3[] vertices = mesh.vertices;
            Vector2[] uvs = new Vector2[vertices.Length];

            Bounds bounds = mesh.bounds;
            Vector3 center = bounds.center;
            Vector3 size = bounds.size;

            size.x = Mathf.Max(size.x, 0.0001f);
            size.y = Mathf.Max(size.y, 0.0001f);
            size.z = Mathf.Max(size.z, 0.0001f);

            float scaleX = keepAspectRatio ? Mathf.Max(size.x, size.y, size.z) : size.x;
            float scaleY = keepAspectRatio ? scaleX : size.y;
            float scaleZ = keepAspectRatio ? scaleX : size.z;

            for (int i = 0; i < vertices.Length; i++)
            {
                Vector3 v = vertices[i] - center;

                switch (uvProjectionType)
                {
                    case UVProjectionType.PlanarXY:
                        uvs[i] = new Vector2(v.x / scaleX + 0.5f, v.y / scaleY + 0.5f);
                        break;
                    case UVProjectionType.PlanarXZ:
                        uvs[i] = new Vector2(v.x / scaleX + 0.5f, v.z / scaleZ + 0.5f);
                        break;
                    case UVProjectionType.PlanarZY:
                        uvs[i] = new Vector2(v.z / scaleZ + 0.5f, v.y / scaleY + 0.5f);
                        break;
                }
            }

            return uvs;
        }

        private Vector2[] GenerateSphericalUV(Mesh mesh, bool keepAspectRatio)
        {
            Vector3[] vertices = mesh.vertices;
            Vector2[] uvs = new Vector2[vertices.Length];

            Bounds bounds = mesh.bounds;
            Vector3 center = bounds.center;
            Vector3 size = bounds.size;

            float scaleX = keepAspectRatio ? Mathf.Max(size.x, size.y, size.z) : size.x;
            float scaleY = keepAspectRatio ? scaleX : size.y;
            float scaleZ = keepAspectRatio ? scaleX : size.z;

            for (int i = 0; i < vertices.Length; i++)
            {
                Vector3 v = vertices[i] - center;
                v = new Vector3(v.x / scaleX, v.y / scaleY, v.z / scaleZ);
                v.Normalize();

                float u = 0.5f + Mathf.Atan2(v.z, v.x) / (2 * Mathf.PI);
                float vCoord = 0.5f - Mathf.Asin(v.y) / Mathf.PI;

                uvs[i] = new Vector2(u, vCoord);
            }

            return uvs;
        }

        private Vector2[] GenerateCubicUV(Mesh mesh, bool keepAspectRatio)
        {
            Vector3[] vertices = mesh.vertices;
            Vector3[] normals = mesh.normals;
            Vector2[] uvs = new Vector2[vertices.Length];

            Bounds bounds = mesh.bounds;
            Vector3 center = bounds.center;
            Vector3 size = bounds.size;

            float maxSize = Mathf.Max(size.x, size.y, size.z);
            float scaleX = keepAspectRatio ? maxSize : size.x;
            float scaleY = keepAspectRatio ? maxSize : size.y;
            float scaleZ = keepAspectRatio ? maxSize : size.z;

            for (int i = 0; i < vertices.Length; i++)
            {
                Vector3 v = vertices[i] - center;
                Vector3 normal = normals[i].normalized;

                if (Mathf.Abs(normal.y) > Mathf.Abs(normal.x) && Mathf.Abs(normal.y) > Mathf.Abs(normal.z))
                {
                    uvs[i] = new Vector2(v.x / scaleX + 0.5f, v.z / scaleZ + 0.5f);
                }
                else if (Mathf.Abs(normal.x) > Mathf.Abs(normal.z))
                {
                    uvs[i] = new Vector2(v.z / scaleZ + 0.5f, v.y / scaleY + 0.5f);
                }
                else
                {
                    uvs[i] = new Vector2(v.x / scaleX + 0.5f, v.y / scaleY + 0.5f);
                }
            }

            return uvs;
        }
    }
}