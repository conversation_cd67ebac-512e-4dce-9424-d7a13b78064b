using System;
using UnityEngine;

namespace Stylo.Cadance
{
    /// <summary>
    /// Spectrum payload for Cadance events containing FFT frequency spectrum data.
    /// Compatible with Koreographer's SpectrumPayload.
    /// </summary>
    [Serializable]
    public class SpectrumPayload : IPayload
    {
        [SerializeField] private float[] spectrumData;
        [SerializeField] private SpectrumInfo spectrumDataInfo;

        /// <summary>
        /// Gets or sets the spectrum data array.
        /// </summary>
        public float[] SpectrumData
        {
            get => spectrumData;
            set => spectrumData = value;
        }

        /// <summary>
        /// Gets or sets the spectrum information.
        /// </summary>
        public SpectrumInfo SpectrumDataInfo
        {
            get => spectrumDataInfo;
            set => spectrumDataInfo = value;
        }

        /// <summary>
        /// Gets the number of spectrum entries (bins).
        /// </summary>
        public int SpectrumEntryCount => spectrumData?.Length ?? 0;

        /// <summary>
        /// Creates a new SpectrumPayload with the specified data.
        /// </summary>
        /// <param name="spectrum">The spectrum data array</param>
        /// <param name="info">The spectrum information</param>
        public SpectrumPayload(float[] spectrum = null, SpectrumInfo info = default)
        {
            spectrumData = spectrum ?? new float[0];
            spectrumDataInfo = info;
        }

        /// <summary>
        /// Gets spectrum data interpolated at a specific delta position.
        /// </summary>
        /// <param name="spectrum">Output array to fill with spectrum values</param>
        /// <param name="delta">Delta position for interpolation (0.0 to 1.0)</param>
        /// <param name="maxBinCount">Maximum number of bins to return</param>
        public void GetSpectrumAtDelta(ref float[] spectrum, float delta, int maxBinCount)
        {
            if (spectrumData == null || spectrumData.Length == 0)
            {
                if (spectrum == null || spectrum.Length != maxBinCount)
                    spectrum = new float[maxBinCount];
                Array.Clear(spectrum, 0, maxBinCount);
                return;
            }

            // Ensure output array is correct size
            if (spectrum == null || spectrum.Length != maxBinCount)
                spectrum = new float[maxBinCount];

            // If we have multiple spectrum entries, interpolate based on delta
            int sourceLength = spectrumData.Length;

            if (maxBinCount >= sourceLength)
            {
                // Copy all data and pad with zeros
                Array.Copy(spectrumData, spectrum, sourceLength);
                for (int i = sourceLength; i < maxBinCount; i++)
                {
                    spectrum[i] = 0f;
                }
            }
            else
            {
                // Downsample by averaging bins
                float binRatio = (float)sourceLength / maxBinCount;

                for (int i = 0; i < maxBinCount; i++)
                {
                    float startIndex = i * binRatio;
                    float endIndex = (i + 1) * binRatio;

                    int startBin = Mathf.FloorToInt(startIndex);
                    int endBin = Mathf.CeilToInt(endIndex);

                    float sum = 0f;
                    int count = 0;

                    for (int j = startBin; j < endBin && j < sourceLength; j++)
                    {
                        sum += spectrumData[j];
                        count++;
                    }

                    spectrum[i] = count > 0 ? sum / count : 0f;
                }
            }
        }

        /// <summary>
        /// Gets spectrum data at a specific sample time with interpolation.
        /// </summary>
        /// <param name="sampleTime">The sample time</param>
        /// <param name="spectrum">Output array to fill with spectrum values</param>
        /// <param name="maxBinCount">Maximum number of bins to return</param>
        public void GetSpectrumAtSampleTime(int sampleTime, ref float[] spectrum, int maxBinCount)
        {
            // For single spectrum data, delta is not relevant
            GetSpectrumAtDelta(ref spectrum, 0f, maxBinCount);
        }

        /// <summary>
        /// Returns a copy of this payload.
        /// </summary>
        /// <returns>A copy of the SpectrumPayload</returns>
        public IPayload GetCopy()
        {
            float[] copiedData = null;
            if (spectrumData != null)
            {
                copiedData = new float[spectrumData.Length];
                Array.Copy(spectrumData, copiedData, spectrumData.Length);
            }

            return new SpectrumPayload(copiedData, spectrumDataInfo);
        }

        /// <summary>
        /// Gets the friendly name for editor display.
        /// </summary>
        /// <returns>The friendly name</returns>
        public string GetFriendlyName()
        {
            return "Spectrum";
        }

        /// <summary>
        /// Gets the display width for editor visualization.
        /// </summary>
        /// <returns>The display width in pixels</returns>
        public float GetDisplayWidth()
        {
            return 100f; // Default width for spectrum visualization
        }

        /// <summary>
        /// Returns a string representation of the spectrum payload.
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"SpectrumPayload: {SpectrumEntryCount} bins, {spectrumDataInfo.sampleRate}Hz";
        }
    }

    /// <summary>
    /// Information about spectrum data.
    /// </summary>
    [Serializable]
    public struct SpectrumInfo
    {
        /// <summary>
        /// The sample rate of the audio data.
        /// </summary>
        public int sampleRate;

        /// <summary>
        /// The number of samples used for the FFT analysis.
        /// </summary>
        public int fftSize;

        /// <summary>
        /// The window function used for FFT analysis.
        /// </summary>
        public FFTWindow windowFunction;

        /// <summary>
        /// The frequency resolution (Hz per bin).
        /// </summary>
        public float frequencyResolution;

        /// <summary>
        /// Creates a new SpectrumInfo.
        /// </summary>
        /// <param name="sampleRate">The sample rate</param>
        /// <param name="fftSize">The FFT size</param>
        /// <param name="windowFunction">The window function</param>
        public SpectrumInfo(int sampleRate, int fftSize, FFTWindow windowFunction = FFTWindow.BlackmanHarris)
        {
            this.sampleRate = sampleRate;
            this.fftSize = fftSize;
            this.windowFunction = windowFunction;
            this.frequencyResolution = (float)sampleRate / fftSize;
        }
    }

    /// <summary>
    /// Extension methods for SpectrumPayload events.
    /// </summary>
    public static class SpectrumPayloadEventExtensions
    {
        /// <summary>
        /// Determines if the event has a SpectrumPayload.
        /// </summary>
        /// <param name="evt">The CadanceEvent</param>
        /// <returns>True if the event has a SpectrumPayload</returns>
        public static bool HasSpectrumPayload(this CadanceEvent evt)
        {
            return evt.Payload is SpectrumPayload;
        }

        /// <summary>
        /// Gets the SpectrumPayload from the event.
        /// </summary>
        /// <param name="evt">The CadanceEvent</param>
        /// <returns>The SpectrumPayload or null if not present</returns>
        public static SpectrumPayload GetSpectrumPayload(this CadanceEvent evt)
        {
            return evt.Payload as SpectrumPayload;
        }

        /// <summary>
        /// Gets spectrum data from the event at a specific delta.
        /// </summary>
        /// <param name="evt">The CadanceEvent</param>
        /// <param name="spectrum">Output array to fill</param>
        /// <param name="delta">Delta position for interpolation</param>
        /// <param name="maxBinCount">Maximum number of bins</param>
        public static void GetSpectrumAtDelta(this CadanceEvent evt, ref float[] spectrum, float delta, int maxBinCount)
        {
            var spectrumPayload = evt.GetSpectrumPayload();
            if (spectrumPayload != null)
            {
                spectrumPayload.GetSpectrumAtDelta(ref spectrum, delta, maxBinCount);
            }
            else
            {
                if (spectrum == null || spectrum.Length != maxBinCount)
                    spectrum = new float[maxBinCount];
                Array.Clear(spectrum, 0, maxBinCount);
            }
        }
    }
}
