%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e78934177bebe0545ab566bb787b0282, type: 3}
  m_Name: Readme
  m_EditorClassIdentifier: 
  Icon: {fileID: 2800000, guid: f4cd3ab4f6b5e094ab495c7ccbec7652, type: 3}
  Title: Noise Simple
  Description:
    Heading: 
    Text: This sample performs texture sampling using noise modified texture coordinates.
    LinkText: 
    Url: 
  PropertiesHeader:
    Heading: Properties
    Text: 
    LinkText: 
    Url: 
  Properties:
  - Heading: Main Texture
    Text: Texture to be sampled.
    LinkText: 
    Url: 
  - Heading: Size
    Text: Noise tiling amount.
    LinkText: 
    Url: 
  - Heading: Strength
    Text: Overall noise strength value.
    LinkText: 
    Url: 
  AdditionalProperties: []
  AdditionalScripts: []
  LoadedLayout: 0
  RPType: 3
