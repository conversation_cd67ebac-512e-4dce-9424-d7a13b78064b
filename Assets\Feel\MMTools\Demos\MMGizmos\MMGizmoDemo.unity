%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 9
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.212, g: 0.227, b: 0.259, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 0
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 2100000, guid: ********************************, type: 2}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_IndirectSpecularColor: {r: 0.10123622, g: 0.4964872, b: 0.72947276, a: 1}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &3
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 11
  m_GIWorkflowMode: 1
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 1
    m_EnableRealtimeLightmaps: 0
  m_LightmapEditorSettings:
    serializedVersion: 12
    m_Resolution: 2
    m_BakeResolution: 40
    m_AtlasSize: 1024
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_ExtractAmbientOcclusion: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_FinalGather: 0
    m_FinalGatherFiltering: 1
    m_FinalGatherRayCount: 256
    m_ReflectionCompression: 2
    m_MixedBakeMode: 2
    m_BakeBackend: 1
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 512
    m_PVRBounces: 2
    m_PVREnvironmentSampleCount: 256
    m_PVREnvironmentReferencePointCount: 2048
    m_PVRFilteringMode: 1
    m_PVRDenoiserTypeDirect: 1
    m_PVRDenoiserTypeIndirect: 1
    m_PVRDenoiserTypeAO: 1
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVREnvironmentMIS: 1
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ExportTrainingData: 0
    m_TrainingDataDestination: TrainingData
    m_LightProbeSampleCountMultiplier: 4
  m_LightingDataAsset: {fileID: 0}
  m_UseShadowmask: 1
--- !u!196 &4
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 2
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    accuratePlacement: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!1 &47096448
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 47096450}
  - component: {fileID: 47096449}
  m_Layer: 0
  m_Name: LineForward
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &47096449
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 47096448}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 99ca21bfff9408f44bf12916ef8f4363, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  DisplayGizmo: 1
  GizmoType: 2
  DisplayMode: 0
  GizmoColor: {r: 1, g: 0.76862746, b: 0, a: 1}
  PositionMode: 13
  PositionTexture: {fileID: 0}
  TextureSize: {x: 1, y: 1}
  PositionSize: 0.5
  ColliderRenderType: 0
  ViewDistance: 20
  GizmoOffset: {x: 0, y: 0, z: 0}
  LockX: 0
  LockedX: 0
  LockY: 0
  LockedY: 0
  LockZ: 0
  LockedZ: 0
  DisplayText: 1
  TextMode: 0
  TextToDisplay: Some Text
  TextOffset: {x: -0.5, y: 0.5, z: 0}
  TextFontStyle: 0
  TextSize: 12
  TextColor: {r: 1, g: 0.76862746, b: 0, a: 1}
  TextBackgroundColor: {r: 0, g: 0, b: 0, a: 0.3}
  TextPadding: {x: 5, y: 0, z: 5, w: 0}
  TextMaxDistance: 14
  TargetProperty:
    TargetObject: {fileID: 0}
    TargetComponent: {fileID: 0}
    TargetScriptableObject: {fileID: 0}
    TargetPropertyName: 
--- !u!4 &47096450
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 47096448}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 4, y: 8, z: -0.021}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 25
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &145927696
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 145927698}
  - component: {fileID: 145927697}
  m_Layer: 0
  m_Name: LineUp
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &145927697
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 145927696}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 99ca21bfff9408f44bf12916ef8f4363, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  DisplayGizmo: 1
  GizmoType: 2
  DisplayMode: 0
  GizmoColor: {r: 1, g: 0.76862746, b: 0, a: 1}
  PositionMode: 12
  PositionTexture: {fileID: 0}
  TextureSize: {x: 1, y: 1}
  PositionSize: 0.5
  ColliderRenderType: 0
  ViewDistance: 20
  GizmoOffset: {x: 0, y: 0, z: 0}
  LockX: 0
  LockedX: 0
  LockY: 0
  LockedY: 0
  LockZ: 0
  LockedZ: 0
  DisplayText: 1
  TextMode: 0
  TextToDisplay: Some Text
  TextOffset: {x: -0.5, y: 0.5, z: 0}
  TextFontStyle: 0
  TextSize: 12
  TextColor: {r: 1, g: 0.76862746, b: 0, a: 1}
  TextBackgroundColor: {r: 0, g: 0, b: 0, a: 0.3}
  TextPadding: {x: 5, y: 0, z: 5, w: 0}
  TextMaxDistance: 14
  TargetProperty:
    TargetObject: {fileID: 0}
    TargetComponent: {fileID: 0}
    TargetScriptableObject: {fileID: 0}
    TargetPropertyName: 
--- !u!4 &145927698
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 145927696}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 2, y: 8, z: -0.021}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 24
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &216846111
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 216846113}
  - component: {fileID: 216846112}
  m_Layer: 0
  m_Name: PositionWireCube
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &216846112
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 216846111}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 99ca21bfff9408f44bf12916ef8f4363, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  DisplayGizmo: 1
  GizmoType: 2
  DisplayMode: 0
  GizmoColor: {r: 1, g: 0.76862746, b: 0, a: 1}
  PositionMode: 2
  PositionTexture: {fileID: 0}
  TextureSize: {x: 1, y: 1}
  PositionSize: 0.2
  ColliderRenderType: 0
  ViewDistance: 20
  GizmoOffset: {x: 0, y: 0, z: 0}
  LockX: 0
  LockedX: 0
  LockY: 0
  LockedY: 0
  LockZ: 0
  LockedZ: 0
  DisplayText: 1
  TextMode: 0
  TextToDisplay: Some Text
  TextOffset: {x: -0.5, y: 0.5, z: 0}
  TextFontStyle: 0
  TextSize: 12
  TextColor: {r: 1, g: 0.76862746, b: 0, a: 1}
  TextBackgroundColor: {r: 0, g: 0, b: 0, a: 0.3}
  TextPadding: {x: 5, y: 0, z: 5, w: 0}
  TextMaxDistance: 14
  TargetProperty:
    TargetObject: {fileID: 0}
    TargetComponent: {fileID: 0}
    TargetScriptableObject: {fileID: 0}
    TargetPropertyName: 
--- !u!4 &216846113
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 216846111}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 10, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 26
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &558744042
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 558744044}
  - component: {fileID: 558744043}
  m_Layer: 0
  m_Name: PositionPoint
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &558744043
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 558744042}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 99ca21bfff9408f44bf12916ef8f4363, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  DisplayGizmo: 1
  GizmoType: 2
  DisplayMode: 0
  GizmoColor: {r: 1, g: 0.76862746, b: 0, a: 1}
  PositionMode: 0
  PositionTexture: {fileID: 0}
  TextureSize: {x: 1, y: 1}
  PositionSize: 0.2
  ColliderRenderType: 0
  ViewDistance: 20
  GizmoOffset: {x: 0, y: 0, z: 0}
  LockX: 0
  LockedX: 0
  LockY: 0
  LockedY: 0
  LockZ: 0
  LockedZ: 0
  DisplayText: 1
  TextMode: 2
  TextToDisplay: fghfghfg hfgh
  TextOffset: {x: -0.5, y: 0.5, z: 0}
  TextFontStyle: 0
  TextSize: 12
  TextColor: {r: 1, g: 0.76862746, b: 0, a: 1}
  TextBackgroundColor: {r: 0, g: 0, b: 0, a: 0.29803923}
  TextPadding: {x: 5, y: 0, z: 5, w: 0}
  TextMaxDistance: 14
  TargetProperty:
    TargetObject: {fileID: 0}
    TargetComponent: {fileID: 0}
    TargetScriptableObject: {fileID: 0}
    TargetPropertyName: 
--- !u!4 &558744044
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 558744042}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -2, y: 4, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 12
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &589520517
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 589520519}
  - component: {fileID: 589520518}
  m_Layer: 0
  m_Name: Directional Light
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!108 &589520518
Light:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 589520517}
  m_Enabled: 1
  serializedVersion: 10
  m_Type: 1
  m_Shape: 0
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_Intensity: 1
  m_Range: 10
  m_SpotAngle: 30
  m_InnerSpotAngle: 21.80208
  m_CookieSize: 10
  m_Shadows:
    m_Type: 0
    m_Resolution: -1
    m_CustomResolution: -1
    m_Strength: 1
    m_Bias: 0.05
    m_NormalBias: 0.4
    m_NearPlane: 0.2
    m_CullingMatrixOverride:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    m_UseCullingMatrixOverride: 0
  m_Cookie: {fileID: 0}
  m_DrawHalo: 0
  m_Flare: {fileID: 0}
  m_RenderMode: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingLayerMask: 1
  m_Lightmapping: 4
  m_LightShadowCasterMode: 0
  m_AreaSize: {x: 1, y: 1}
  m_BounceIntensity: 1
  m_ColorTemperature: 6570
  m_UseColorTemperature: 0
  m_BoundingSphereOverride: {x: 0, y: 0, z: 0, w: 0}
  m_UseBoundingSphereOverride: 0
  m_ShadowRadius: 0
  m_ShadowAngle: 0
--- !u!4 &589520519
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 589520517}
  m_LocalRotation: {x: 0.41084635, y: 0.21241921, z: -0.09905268, w: 0.88106304}
  m_LocalPosition: {x: 0, y: 100, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 50, y: 27.11, z: 0}
--- !u!1 &682261405
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 682261408}
  - component: {fileID: 682261407}
  - component: {fileID: 682261406}
  m_Layer: 0
  m_Name: SphereWire
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!135 &682261406
SphereCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 682261405}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.5
  m_Center: {x: 0, y: 0, z: 0}
--- !u!114 &682261407
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 682261405}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 99ca21bfff9408f44bf12916ef8f4363, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  DisplayGizmo: 1
  GizmoType: 1
  DisplayMode: 0
  GizmoColor: {r: 1, g: 0.76862746, b: 0, a: 1}
  PositionMode: 0
  PositionTexture: {fileID: 0}
  TextureSize: {x: 1, y: 1}
  PositionSize: 0.2
  ColliderRenderType: 1
  ViewDistance: 20
  GizmoOffset: {x: 0, y: 0, z: 0}
  LockX: 0
  LockedX: 0
  LockY: 0
  LockedY: 0
  LockZ: 0
  LockedZ: 0
  DisplayText: 1
  TextMode: 0
  TextToDisplay: Some Text
  TextOffset: {x: -0.5, y: 0.75, z: 0}
  TextFontStyle: 0
  TextSize: 12
  TextColor: {r: 1, g: 0.76862746, b: 0, a: 1}
  TextBackgroundColor: {r: 0, g: 0, b: 0, a: 0.3}
  TextPadding: {x: 5, y: 0, z: 5, w: 0}
  TextMaxDistance: 14
  TargetProperty:
    TargetObject: {fileID: 0}
    TargetComponent: {fileID: 0}
    TargetScriptableObject: {fileID: 0}
    TargetPropertyName: 
--- !u!4 &682261408
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 682261405}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 2, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &741973222
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 741973227}
  - component: {fileID: 741973226}
  - component: {fileID: 741973225}
  - component: {fileID: 741973224}
  - component: {fileID: 741973223}
  - component: {fileID: 741973228}
  m_Layer: 0
  m_Name: Cube
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &741973223
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 741973222}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7f009572b4d918346b24337ef83f88c1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Rotating: 1
  RotationSpace: 1
  UpdateMode: 0
  RotationSpeed: {x: 100, y: 50, z: 10}
  Orbiting: 0
  AdditiveOrbitRotation: 0
  OrbitCenterTransform: {fileID: 0}
  OrbitCenterOffset: {x: 0, y: 0, z: 0}
  OrbitRotationAxis: {x: 0, y: 1, z: 0}
  OrbitRotationSpeed: 10
  OrbitRadius: 3
  OrbitCorrectionSpeed: 10
  DrawGizmos: 1
  OrbitPlaneColor: {r: 54, g: 169, b: 225, a: 0.02}
  OrbitLineColor: {r: 225, g: 225, b: 225, a: 0.1}
  _orbitCenter: {x: 0, y: 0, z: 0}
  _worldRotationAxis: {x: 0, y: 0, z: 0}
  _snappedPosition: {x: 0, y: 0, z: 0}
  _radius: {x: 0, y: 0, z: 0}
--- !u!65 &741973224
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 741973222}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &741973225
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 741973222}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10303, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!33 &741973226
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 741973222}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &741973227
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 741973222}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 9, y: 4, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 21
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &741973228
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 741973222}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 347013242dca7c44fb5001793033ea59, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  UpdateMode: 0
  PositionActive: 1
  RotationActive: 0
  ScaleActive: 0
  PositionWiggleProperties:
    WigglePermitted: 1
    WiggleType: 3
    UseUnscaledTime: 0
    StartWigglingAutomatically: 1
    SmoothPingPong: 1
    UseSpeedCurve: 0
    SpeedCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    FrequencyMin: 0
    FrequencyMax: 1
    AmplitudeMin: {x: -2, y: -2, z: -2}
    AmplitudeMax: {x: 2, y: 2, z: 2}
    RelativeAmplitude: 1
    UniformValues: 0
    Curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    RemapCurveZeroMin: {x: 0, y: 0, z: 0}
    RemapCurveZeroMax: {x: 0, y: 0, z: 0}
    RemapCurveOneMin: {x: 1, y: 1, z: 1}
    RemapCurveOneMax: {x: 1, y: 1, z: 1}
    RelativeCurveAmplitude: 1
    CurvePingPong: 0
    PauseMin: 0
    PauseMax: 0
    LimitedTime: 0
    LimitedTimeTotal: 0
    LimitedTimeFalloff:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: -1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: -1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    LimitedTimeResetValue: 1
    LimitedTimeLeft: 0
    NoiseFrequencyMin: {x: 0, y: 0, z: 0}
    NoiseFrequencyMax: {x: 1, y: 1, z: 1}
    NoiseShiftMin: {x: 0, y: 0, z: 0}
    NoiseShiftMax: {x: 0, y: 0, z: 0}
  RotationWiggleProperties:
    WigglePermitted: 1
    WiggleType: 1
    UseUnscaledTime: 0
    StartWigglingAutomatically: 1
    SmoothPingPong: 1
    UseSpeedCurve: 0
    SpeedCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    FrequencyMin: 0
    FrequencyMax: 1
    AmplitudeMin: {x: 0, y: 0, z: 0}
    AmplitudeMax: {x: 1, y: 1, z: 1}
    RelativeAmplitude: 1
    UniformValues: 0
    Curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    RemapCurveZeroMin: {x: 0, y: 0, z: 0}
    RemapCurveZeroMax: {x: 0, y: 0, z: 0}
    RemapCurveOneMin: {x: 1, y: 1, z: 1}
    RemapCurveOneMax: {x: 1, y: 1, z: 1}
    RelativeCurveAmplitude: 1
    CurvePingPong: 0
    PauseMin: 0
    PauseMax: 0
    LimitedTime: 0
    LimitedTimeTotal: 0
    LimitedTimeFalloff:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: -1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: -1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    LimitedTimeResetValue: 1
    LimitedTimeLeft: 0
    NoiseFrequencyMin: {x: 0, y: 0, z: 0}
    NoiseFrequencyMax: {x: 1, y: 1, z: 1}
    NoiseShiftMin: {x: 0, y: 0, z: 0}
    NoiseShiftMax: {x: 0, y: 0, z: 0}
  ScaleWiggleProperties:
    WigglePermitted: 1
    WiggleType: 1
    UseUnscaledTime: 0
    StartWigglingAutomatically: 1
    SmoothPingPong: 1
    UseSpeedCurve: 0
    SpeedCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    FrequencyMin: 0
    FrequencyMax: 1
    AmplitudeMin: {x: 0, y: 0, z: 0}
    AmplitudeMax: {x: 1, y: 1, z: 1}
    RelativeAmplitude: 1
    UniformValues: 0
    Curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    RemapCurveZeroMin: {x: 0, y: 0, z: 0}
    RemapCurveZeroMax: {x: 0, y: 0, z: 0}
    RemapCurveOneMin: {x: 1, y: 1, z: 1}
    RemapCurveOneMax: {x: 1, y: 1, z: 1}
    RelativeCurveAmplitude: 1
    CurvePingPong: 0
    PauseMin: 0
    PauseMax: 0
    LimitedTime: 0
    LimitedTimeTotal: 0
    LimitedTimeFalloff:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: -1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: -1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    LimitedTimeResetValue: 1
    LimitedTimeLeft: 0
    NoiseFrequencyMin: {x: 0, y: 0, z: 0}
    NoiseFrequencyMax: {x: 1, y: 1, z: 1}
    NoiseShiftMin: {x: 0, y: 0, z: 0}
    NoiseShiftMax: {x: 0, y: 0, z: 0}
  DebugWiggleDuration: 2
--- !u!1 &777917621
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 777917623}
  - component: {fileID: 777917622}
  m_Layer: 0
  m_Name: LineArrows
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &777917622
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 777917621}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 99ca21bfff9408f44bf12916ef8f4363, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  DisplayGizmo: 1
  GizmoType: 2
  DisplayMode: 0
  GizmoColor: {r: 1, g: 0.76862746, b: 0, a: 1}
  PositionMode: 10
  PositionTexture: {fileID: 0}
  TextureSize: {x: 1, y: 1}
  PositionSize: 0.5
  ColliderRenderType: 0
  ViewDistance: 20
  GizmoOffset: {x: 0, y: 0, z: 0}
  LockX: 0
  LockedX: 0
  LockY: 0
  LockedY: 0
  LockZ: 0
  LockedZ: 0
  DisplayText: 1
  TextMode: 0
  TextToDisplay: Some Text
  TextOffset: {x: -0.5, y: 0.5, z: 0}
  TextFontStyle: 0
  TextSize: 12
  TextColor: {r: 1, g: 0.76862746, b: 0, a: 1}
  TextBackgroundColor: {r: 0, g: 0, b: 0, a: 0.3}
  TextPadding: {x: 5, y: 0, z: 5, w: 0}
  TextMaxDistance: 14
  TargetProperty:
    TargetObject: {fileID: 0}
    TargetComponent: {fileID: 0}
    TargetScriptableObject: {fileID: 0}
    TargetPropertyName: 
--- !u!4 &777917623
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 777917621}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -2, y: 8, z: -0.021}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 22
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &904791022
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 904791025}
  - component: {fileID: 904791024}
  - component: {fileID: 904791023}
  m_Layer: 0
  m_Name: BoxCollider2DFull
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!61 &904791023
BoxCollider2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 904791022}
  m_Enabled: 1
  m_Density: 1
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_UsedByEffector: 0
  m_UsedByComposite: 0
  m_Offset: {x: 0, y: 0}
  m_SpriteTilingProperty:
    border: {x: 0, y: 0, z: 0, w: 0}
    pivot: {x: 0, y: 0}
    oldSize: {x: 0, y: 0}
    newSize: {x: 0, y: 0}
    adaptiveTilingThreshold: 0
    drawMode: 0
    adaptiveTiling: 0
  m_AutoTiling: 0
  serializedVersion: 2
  m_Size: {x: 1, y: 1}
  m_EdgeRadius: 0
--- !u!114 &904791024
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 904791022}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 99ca21bfff9408f44bf12916ef8f4363, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  DisplayGizmo: 1
  GizmoType: 1
  DisplayMode: 0
  GizmoColor: {r: 1, g: 0.76862746, b: 0, a: 0.69803923}
  PositionMode: 0
  PositionTexture: {fileID: 0}
  TextureSize: {x: 1, y: 1}
  PositionSize: 0.2
  ColliderRenderType: 0
  ViewDistance: 20
  GizmoOffset: {x: 0, y: 0, z: 0}
  LockX: 0
  LockedX: 0
  LockY: 0
  LockedY: 0
  LockZ: 0
  LockedZ: 0
  DisplayText: 1
  TextMode: 0
  TextToDisplay: Some Text
  TextOffset: {x: -0.5, y: 0.75, z: 0}
  TextFontStyle: 0
  TextSize: 12
  TextColor: {r: 1, g: 0.76862746, b: 0, a: 1}
  TextBackgroundColor: {r: 0, g: 0, b: 0, a: 0.3}
  TextPadding: {x: 5, y: 0, z: 5, w: 0}
  TextMaxDistance: 14
  TargetProperty:
    TargetObject: {fileID: 0}
    TargetComponent: {fileID: 0}
    TargetScriptableObject: {fileID: 0}
    TargetPropertyName: 
--- !u!4 &904791025
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 904791022}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 2, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 6
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &979508401
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 979508404}
  - component: {fileID: 979508403}
  - component: {fileID: 979508402}
  m_Layer: 0
  m_Name: CircleCollider2DWire
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!58 &979508402
CircleCollider2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 979508401}
  m_Enabled: 1
  m_Density: 1
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_UsedByEffector: 0
  m_UsedByComposite: 0
  m_Offset: {x: 0, y: 0}
  serializedVersion: 2
  m_Radius: 0.5
--- !u!114 &979508403
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 979508401}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 99ca21bfff9408f44bf12916ef8f4363, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  DisplayGizmo: 1
  GizmoType: 1
  DisplayMode: 0
  GizmoColor: {r: 1, g: 0.76862746, b: 0, a: 1}
  PositionMode: 0
  PositionTexture: {fileID: 0}
  TextureSize: {x: 1, y: 1}
  PositionSize: 0.2
  ColliderRenderType: 1
  ViewDistance: 20
  GizmoOffset: {x: 0, y: 0, z: 0}
  LockX: 0
  LockedX: 0
  LockY: 0
  LockedY: 0
  LockZ: 0
  LockedZ: 0
  DisplayText: 1
  TextMode: 0
  TextToDisplay: Some Text
  TextOffset: {x: -0.5, y: 0.75, z: 0}
  TextFontStyle: 0
  TextSize: 12
  TextColor: {r: 1, g: 0.76862746, b: 0, a: 1}
  TextBackgroundColor: {r: 0, g: 0, b: 0, a: 0.3}
  TextPadding: {x: 5, y: 0, z: 5, w: 0}
  TextMaxDistance: 14
  TargetProperty:
    TargetObject: {fileID: 0}
    TargetComponent: {fileID: 0}
    TargetScriptableObject: {fileID: 0}
    TargetPropertyName: 
--- !u!4 &979508404
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 979508401}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -2, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 9
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &985391527
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 985391529}
  - component: {fileID: 985391528}
  m_Layer: 0
  m_Name: PositionSphere
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &985391528
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 985391527}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 99ca21bfff9408f44bf12916ef8f4363, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  DisplayGizmo: 1
  GizmoType: 2
  DisplayMode: 0
  GizmoColor: {r: 1, g: 0.76862746, b: 0, a: 1}
  PositionMode: 3
  PositionTexture: {fileID: 0}
  TextureSize: {x: 1, y: 1}
  PositionSize: 0.2
  ColliderRenderType: 0
  ViewDistance: 20
  GizmoOffset: {x: 0, y: 0, z: 0}
  LockX: 0
  LockedX: 0
  LockY: 0
  LockedY: 0
  LockZ: 0
  LockedZ: 0
  DisplayText: 1
  TextMode: 0
  TextToDisplay: Some Text
  TextOffset: {x: -0.5, y: 0.5, z: 0}
  TextFontStyle: 0
  TextSize: 12
  TextColor: {r: 1, g: 0.76862746, b: 0, a: 1}
  TextBackgroundColor: {r: 0, g: 0, b: 0, a: 0.3}
  TextPadding: {x: 5, y: 0, z: 5, w: 0}
  TextMaxDistance: 14
  TargetProperty:
    TargetObject: {fileID: 0}
    TargetComponent: {fileID: 0}
    TargetScriptableObject: {fileID: 0}
    TargetPropertyName: 
--- !u!4 &985391529
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 985391527}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 2, y: 4, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 18
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1008775278
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1008775280}
  - component: {fileID: 1008775279}
  m_Layer: 0
  m_Name: PositionArrowForward
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1008775279
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1008775278}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 99ca21bfff9408f44bf12916ef8f4363, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  DisplayGizmo: 1
  GizmoType: 2
  DisplayMode: 0
  GizmoColor: {r: 1, g: 0.76862746, b: 0, a: 1}
  PositionMode: 9
  PositionTexture: {fileID: 0}
  TextureSize: {x: 1, y: 1}
  PositionSize: 0.5
  ColliderRenderType: 0
  ViewDistance: 20
  GizmoOffset: {x: 0, y: 0, z: 0}
  LockX: 0
  LockedX: 0
  LockY: 0
  LockedY: 0
  LockZ: 0
  LockedZ: 0
  DisplayText: 1
  TextMode: 0
  TextToDisplay: Some Text
  TextOffset: {x: -0.5, y: 0.5, z: 0}
  TextFontStyle: 0
  TextSize: 12
  TextColor: {r: 1, g: 0.76862746, b: 0, a: 1}
  TextBackgroundColor: {r: 0, g: 0, b: 0, a: 0.3}
  TextPadding: {x: 5, y: 0, z: 5, w: 0}
  TextMaxDistance: 14
  TargetProperty:
    TargetObject: {fileID: 0}
    TargetComponent: {fileID: 0}
    TargetScriptableObject: {fileID: 0}
    TargetPropertyName: 
--- !u!4 &1008775280
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1008775278}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 4, y: 6, z: -0.021}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 17
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1064219801
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1064219804}
  - component: {fileID: 1064219803}
  - component: {fileID: 1064219802}
  m_Layer: 0
  m_Name: BoxColliderWire
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!65 &1064219802
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1064219801}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 0.85, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!114 &1064219803
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1064219801}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 99ca21bfff9408f44bf12916ef8f4363, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  DisplayGizmo: 1
  GizmoType: 1
  DisplayMode: 0
  GizmoColor: {r: 1, g: 0.76862746, b: 0, a: 1}
  PositionMode: 0
  PositionTexture: {fileID: 0}
  TextureSize: {x: 1, y: 1}
  PositionSize: 0.2
  ColliderRenderType: 1
  ViewDistance: 20
  GizmoOffset: {x: 0, y: 0, z: 0}
  LockX: 0
  LockedX: 0
  LockY: 0
  LockedY: 0
  LockZ: 0
  LockedZ: 0
  DisplayText: 1
  TextMode: 0
  TextToDisplay: Some Text
  TextOffset: {x: -0.5, y: 0.75, z: 0}
  TextFontStyle: 0
  TextSize: 12
  TextColor: {r: 1, g: 0.76862746, b: 0, a: 1}
  TextBackgroundColor: {r: 0, g: 0, b: 0, a: 0.3}
  TextPadding: {x: 5, y: 0, z: 5, w: 0}
  TextMaxDistance: 14
  TargetProperty:
    TargetObject: {fileID: 0}
    TargetComponent: {fileID: 0}
    TargetScriptableObject: {fileID: 0}
    TargetPropertyName: 
--- !u!4 &1064219804
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1064219801}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 4, y: 0, z: 0}
  m_LocalScale: {x: 1.96, y: 0.9, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1084074544
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1084074547}
  - component: {fileID: 1084074546}
  - component: {fileID: 1084074545}
  m_Layer: 0
  m_Name: MeshColliderFull
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!64 &1084074545
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1084074544}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 4
  m_Convex: 0
  m_CookingOptions: 30
  m_Mesh: {fileID: -5654033164078409323, guid: 5c0669bd66f517143b7f3a11cbd9c17e, type: 3}
--- !u!114 &1084074546
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1084074544}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 99ca21bfff9408f44bf12916ef8f4363, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  DisplayGizmo: 1
  GizmoType: 1
  DisplayMode: 0
  GizmoColor: {r: 1, g: 0.76862746, b: 0, a: 0.69803923}
  PositionMode: 0
  PositionTexture: {fileID: 0}
  TextureSize: {x: 1, y: 1}
  PositionSize: 0.2
  ColliderRenderType: 0
  ViewDistance: 20
  GizmoOffset: {x: 0, y: 0, z: 0}
  LockX: 0
  LockedX: 0
  LockY: 0
  LockedY: 0
  LockZ: 0
  LockedZ: 0
  DisplayText: 1
  TextMode: 0
  TextToDisplay: Some Text
  TextOffset: {x: 0, y: 1.25, z: 0}
  TextFontStyle: 0
  TextSize: 12
  TextColor: {r: 1, g: 0.76862746, b: 0, a: 1}
  TextBackgroundColor: {r: 0, g: 0, b: 0, a: 0.3}
  TextPadding: {x: 5, y: 0, z: 5, w: 0}
  TextMaxDistance: 14
  TargetProperty:
    TargetObject: {fileID: 0}
    TargetComponent: {fileID: 0}
    TargetScriptableObject: {fileID: 0}
    TargetPropertyName: 
--- !u!4 &1084074547
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1084074544}
  m_LocalRotation: {x: 0.8534144, y: 0.521233, z: -0, w: 0}
  m_LocalPosition: {x: 6, y: 1.5, z: 0}
  m_LocalScale: {x: 50, y: 50, z: 50}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 10
  m_LocalEulerAnglesHint: {x: 180, y: 0, z: -62.83}
--- !u!1 &1180641674
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1180641676}
  - component: {fileID: 1180641675}
  m_Layer: 0
  m_Name: PositionTexture
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1180641675
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1180641674}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 99ca21bfff9408f44bf12916ef8f4363, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  DisplayGizmo: 1
  GizmoType: 2
  DisplayMode: 0
  GizmoColor: {r: 1, g: 0.76862746, b: 0, a: 1}
  PositionMode: 5
  PositionTexture: {fileID: 2800000, guid: ddf3b2cb509cb234bb7b7f83fc04364d, type: 3}
  TextureSize: {x: 50, y: 50}
  PositionSize: 0.2
  ColliderRenderType: 0
  ViewDistance: 20
  GizmoOffset: {x: 0, y: 0, z: 0}
  LockX: 0
  LockedX: 0
  LockY: 0
  LockedY: 0
  LockZ: 0
  LockedZ: 0
  DisplayText: 1
  TextMode: 0
  TextToDisplay: Some Text
  TextOffset: {x: -0.5, y: 0.5, z: 0}
  TextFontStyle: 0
  TextSize: 12
  TextColor: {r: 1, g: 0.76862746, b: 0, a: 1}
  TextBackgroundColor: {r: 0, g: 0, b: 0, a: 0.3}
  TextPadding: {x: 5, y: 0, z: 5, w: 0}
  TextMaxDistance: 14
  TargetProperty:
    TargetObject: {fileID: 0}
    TargetComponent: {fileID: 0}
    TargetScriptableObject: {fileID: 0}
    TargetPropertyName: 
--- !u!4 &1180641676
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1180641674}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 4, y: 4, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 19
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1212881641
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1212881644}
  - component: {fileID: 1212881643}
  - component: {fileID: 1212881642}
  m_Layer: 0
  m_Name: BoxCollider2DWire
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!61 &1212881642
BoxCollider2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1212881641}
  m_Enabled: 1
  m_Density: 1
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_UsedByEffector: 0
  m_UsedByComposite: 0
  m_Offset: {x: 0, y: 0}
  m_SpriteTilingProperty:
    border: {x: 0, y: 0, z: 0, w: 0}
    pivot: {x: 0, y: 0}
    oldSize: {x: 0, y: 0}
    newSize: {x: 0, y: 0}
    adaptiveTilingThreshold: 0
    drawMode: 0
    adaptiveTiling: 0
  m_AutoTiling: 0
  serializedVersion: 2
  m_Size: {x: 1, y: 1}
  m_EdgeRadius: 0
--- !u!114 &1212881643
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1212881641}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 99ca21bfff9408f44bf12916ef8f4363, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  DisplayGizmo: 1
  GizmoType: 1
  DisplayMode: 0
  GizmoColor: {r: 1, g: 0.76862746, b: 0, a: 1}
  PositionMode: 0
  PositionTexture: {fileID: 0}
  TextureSize: {x: 1, y: 1}
  PositionSize: 0.2
  ColliderRenderType: 1
  ViewDistance: 20
  GizmoOffset: {x: 0, y: 0, z: 0}
  LockX: 0
  LockedX: 0
  LockY: 0
  LockedY: 0
  LockZ: 0
  LockedZ: 0
  DisplayText: 1
  TextMode: 0
  TextToDisplay: Some Text
  TextOffset: {x: -0.5, y: 0.75, z: 0}
  TextFontStyle: 0
  TextSize: 12
  TextColor: {r: 1, g: 0.76862746, b: 0, a: 1}
  TextBackgroundColor: {r: 0, g: 0, b: 0, a: 0.3}
  TextPadding: {x: 5, y: 0, z: 5, w: 0}
  TextMaxDistance: 14
  TargetProperty:
    TargetObject: {fileID: 0}
    TargetComponent: {fileID: 0}
    TargetScriptableObject: {fileID: 0}
    TargetPropertyName: 
--- !u!4 &1212881644
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1212881641}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 7
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1355728018
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1355728020}
  - component: {fileID: 1355728019}
  m_Layer: 0
  m_Name: PositionCube
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1355728019
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1355728018}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 99ca21bfff9408f44bf12916ef8f4363, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  DisplayGizmo: 1
  GizmoType: 2
  DisplayMode: 0
  GizmoColor: {r: 1, g: 0.76862746, b: 0, a: 1}
  PositionMode: 1
  PositionTexture: {fileID: 0}
  TextureSize: {x: 1, y: 1}
  PositionSize: 0.2
  ColliderRenderType: 0
  ViewDistance: 20
  GizmoOffset: {x: 0, y: 0, z: 0}
  LockX: 0
  LockedX: 0
  LockY: 0
  LockedY: 0
  LockZ: 0
  LockedZ: 0
  DisplayText: 1
  TextMode: 0
  TextToDisplay: Some Text
  TextOffset: {x: -0.5, y: 0.5, z: 0}
  TextFontStyle: 0
  TextSize: 12
  TextColor: {r: 1, g: 0.76862746, b: 0, a: 1}
  TextBackgroundColor: {r: 0, g: 0, b: 0, a: 0.3}
  TextPadding: {x: 5, y: 0, z: 5, w: 0}
  TextMaxDistance: 14
  TargetProperty:
    TargetObject: {fileID: 0}
    TargetComponent: {fileID: 0}
    TargetScriptableObject: {fileID: 0}
    TargetPropertyName: 
--- !u!4 &1355728020
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1355728018}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 4, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 13
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1424603338
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1424603340}
  - component: {fileID: 1424603339}
  m_Layer: 0
  m_Name: PositionArrowUp
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1424603339
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1424603338}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 99ca21bfff9408f44bf12916ef8f4363, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  DisplayGizmo: 1
  GizmoType: 2
  DisplayMode: 0
  GizmoColor: {r: 1, g: 0.76862746, b: 0, a: 1}
  PositionMode: 8
  PositionTexture: {fileID: 0}
  TextureSize: {x: 1, y: 1}
  PositionSize: 0.5
  ColliderRenderType: 0
  ViewDistance: 20
  GizmoOffset: {x: 0, y: 0, z: 0}
  LockX: 0
  LockedX: 0
  LockY: 0
  LockedY: 0
  LockZ: 0
  LockedZ: 0
  DisplayText: 1
  TextMode: 0
  TextToDisplay: Some Text
  TextOffset: {x: -0.5, y: 0.5, z: 0}
  TextFontStyle: 0
  TextSize: 12
  TextColor: {r: 1, g: 0.76862746, b: 0, a: 1}
  TextBackgroundColor: {r: 0, g: 0, b: 0, a: 0.3}
  TextPadding: {x: 5, y: 0, z: 5, w: 0}
  TextMaxDistance: 14
  TargetProperty:
    TargetObject: {fileID: 0}
    TargetComponent: {fileID: 0}
    TargetScriptableObject: {fileID: 0}
    TargetPropertyName: 
--- !u!4 &1424603340
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1424603338}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 2, y: 6, z: -0.021}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 16
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1504946546
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1504946548}
  - component: {fileID: 1504946547}
  m_Layer: 0
  m_Name: JustText
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1504946547
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1504946546}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 99ca21bfff9408f44bf12916ef8f4363, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  DisplayGizmo: 1
  GizmoType: 0
  DisplayMode: 0
  GizmoColor: {r: 1, g: 0.76862746, b: 0, a: 1}
  PositionMode: 4
  PositionTexture: {fileID: 0}
  TextureSize: {x: 1, y: 1}
  PositionSize: 0.2
  ColliderRenderType: 0
  ViewDistance: 20
  GizmoOffset: {x: 0, y: 0, z: 0}
  LockX: 0
  LockedX: 0
  LockY: 0
  LockedY: 0
  LockZ: 0
  LockedZ: 0
  DisplayText: 1
  TextMode: 1
  TextToDisplay: Just Text
  TextOffset: {x: -0.5, y: 0.5, z: 0}
  TextFontStyle: 0
  TextSize: 12
  TextColor: {r: 1, g: 0.76862746, b: 0, a: 1}
  TextBackgroundColor: {r: 0, g: 0, b: 0, a: 0.3}
  TextPadding: {x: 5, y: 0, z: 5, w: 0}
  TextMaxDistance: 14
  TargetProperty:
    TargetObject: {fileID: 0}
    TargetComponent: {fileID: 0}
    TargetScriptableObject: {fileID: 0}
    TargetPropertyName: 
--- !u!4 &1504946548
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1504946546}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 4, y: 10, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 28
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1589810572
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1589810575}
  - component: {fileID: 1589810574}
  - component: {fileID: 1589810573}
  m_Layer: 0
  m_Name: BoxColliderFull
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!65 &1589810573
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1589810572}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 0.85, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!114 &1589810574
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1589810572}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 99ca21bfff9408f44bf12916ef8f4363, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  DisplayGizmo: 1
  GizmoType: 1
  DisplayMode: 0
  GizmoColor: {r: 1, g: 0.76862746, b: 0, a: 0.69803923}
  PositionMode: 0
  PositionTexture: {fileID: 0}
  TextureSize: {x: 1, y: 1}
  PositionSize: 0.2
  ColliderRenderType: 0
  ViewDistance: 20
  GizmoOffset: {x: 0, y: 0, z: 0}
  LockX: 0
  LockedX: 0
  LockY: 0
  LockedY: 0
  LockZ: 0
  LockedZ: 0
  DisplayText: 1
  TextMode: 0
  TextToDisplay: Some Text
  TextOffset: {x: -0.5, y: 0.75, z: 0}
  TextFontStyle: 0
  TextSize: 12
  TextColor: {r: 1, g: 0.76862746, b: 0, a: 1}
  TextBackgroundColor: {r: 0, g: 0, b: 0, a: 0.3}
  TextPadding: {x: 5, y: 0, z: 5, w: 0}
  TextMaxDistance: 14
  TargetProperty:
    TargetObject: {fileID: 0}
    TargetComponent: {fileID: 0}
    TargetScriptableObject: {fileID: 0}
    TargetPropertyName: 
--- !u!4 &1589810575
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1589810572}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 4, y: 2, z: 0}
  m_LocalScale: {x: 1.96, y: 0.9, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1596901027
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1596901030}
  - component: {fileID: 1596901029}
  - component: {fileID: 1596901028}
  m_Layer: 0
  m_Name: MeshColliderWire
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!64 &1596901028
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1596901027}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 4
  m_Convex: 0
  m_CookingOptions: 30
  m_Mesh: {fileID: -5654033164078409323, guid: 5c0669bd66f517143b7f3a11cbd9c17e, type: 3}
--- !u!114 &1596901029
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1596901027}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 99ca21bfff9408f44bf12916ef8f4363, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  DisplayGizmo: 1
  GizmoType: 1
  DisplayMode: 0
  GizmoColor: {r: 1, g: 0.76862746, b: 0, a: 1}
  PositionMode: 0
  PositionTexture: {fileID: 0}
  TextureSize: {x: 1, y: 1}
  PositionSize: 0.2
  ColliderRenderType: 1
  ViewDistance: 20
  GizmoOffset: {x: 0, y: 0, z: 0}
  LockX: 0
  LockedX: 0
  LockY: 0
  LockedY: 0
  LockZ: 0
  LockedZ: 0
  DisplayText: 1
  TextMode: 0
  TextToDisplay: Some Text
  TextOffset: {x: 0, y: 1.25, z: 0}
  TextFontStyle: 0
  TextSize: 12
  TextColor: {r: 1, g: 0.76862746, b: 0, a: 1}
  TextBackgroundColor: {r: 0, g: 0, b: 0, a: 0.3}
  TextPadding: {x: 5, y: 0, z: 5, w: 0}
  TextMaxDistance: 14
  TargetProperty:
    TargetObject: {fileID: 0}
    TargetComponent: {fileID: 0}
    TargetScriptableObject: {fileID: 0}
    TargetPropertyName: 
--- !u!4 &1596901030
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1596901027}
  m_LocalRotation: {x: 0.8534144, y: 0.521233, z: -0, w: 0}
  m_LocalPosition: {x: 6, y: -0.5, z: 0}
  m_LocalScale: {x: 50, y: 50, z: 50}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 11
  m_LocalEulerAnglesHint: {x: 180, y: 0, z: -62.83}
--- !u!1 &1630882199
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1630882202}
  - component: {fileID: 1630882201}
  - component: {fileID: 1630882200}
  m_Layer: 0
  m_Name: CircleCollider2DFull
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!58 &1630882200
CircleCollider2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1630882199}
  m_Enabled: 1
  m_Density: 1
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_UsedByEffector: 0
  m_UsedByComposite: 0
  m_Offset: {x: 0, y: 0}
  serializedVersion: 2
  m_Radius: 0.5
--- !u!114 &1630882201
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1630882199}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 99ca21bfff9408f44bf12916ef8f4363, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  DisplayGizmo: 1
  GizmoType: 1
  DisplayMode: 0
  GizmoColor: {r: 1, g: 0.76862746, b: 0, a: 0.69803923}
  PositionMode: 0
  PositionTexture: {fileID: 0}
  TextureSize: {x: 1, y: 1}
  PositionSize: 0.2
  ColliderRenderType: 0
  ViewDistance: 20
  GizmoOffset: {x: 0, y: 0, z: 0}
  LockX: 0
  LockedX: 0
  LockY: 0
  LockedY: 0
  LockZ: 0
  LockedZ: 0
  DisplayText: 1
  TextMode: 0
  TextToDisplay: Some Text
  TextOffset: {x: -0.5, y: 0.75, z: 0}
  TextFontStyle: 0
  TextSize: 12
  TextColor: {r: 1, g: 0.76862746, b: 0, a: 1}
  TextBackgroundColor: {r: 0, g: 0, b: 0, a: 0.3}
  TextPadding: {x: 5, y: 0, z: 5, w: 0}
  TextMaxDistance: 14
  TargetProperty:
    TargetObject: {fileID: 0}
    TargetComponent: {fileID: 0}
    TargetScriptableObject: {fileID: 0}
    TargetPropertyName: 
--- !u!4 &1630882202
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1630882199}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -2, y: 2, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 8
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1734817153
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1734817155}
  - component: {fileID: 1734817154}
  m_Layer: 0
  m_Name: PositionWireSphere
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1734817154
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1734817153}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 99ca21bfff9408f44bf12916ef8f4363, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  DisplayGizmo: 1
  GizmoType: 2
  DisplayMode: 0
  GizmoColor: {r: 1, g: 0.76862746, b: 0, a: 1}
  PositionMode: 4
  PositionTexture: {fileID: 0}
  TextureSize: {x: 1, y: 1}
  PositionSize: 0.2
  ColliderRenderType: 0
  ViewDistance: 20
  GizmoOffset: {x: 0, y: 0, z: 0}
  LockX: 0
  LockedX: 0
  LockY: 0
  LockedY: 0
  LockZ: 0
  LockedZ: 0
  DisplayText: 1
  TextMode: 0
  TextToDisplay: Some Text
  TextOffset: {x: -0.5, y: 0.5, z: 0}
  TextFontStyle: 0
  TextSize: 12
  TextColor: {r: 1, g: 0.76862746, b: 0, a: 1}
  TextBackgroundColor: {r: 0, g: 0, b: 0, a: 0.3}
  TextPadding: {x: 5, y: 0, z: 5, w: 0}
  TextMaxDistance: 14
  TargetProperty:
    TargetObject: {fileID: 0}
    TargetComponent: {fileID: 0}
    TargetScriptableObject: {fileID: 0}
    TargetPropertyName: 
--- !u!4 &1734817155
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1734817153}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 2, y: 10, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 27
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1799972279
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1799972281}
  - component: {fileID: 1799972280}
  m_Layer: 0
  m_Name: PositionArrows
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1799972280
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1799972279}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 99ca21bfff9408f44bf12916ef8f4363, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  DisplayGizmo: 1
  GizmoType: 2
  DisplayMode: 0
  GizmoColor: {r: 1, g: 0.76862746, b: 0, a: 1}
  PositionMode: 6
  PositionTexture: {fileID: 0}
  TextureSize: {x: 1, y: 1}
  PositionSize: 0.5
  ColliderRenderType: 0
  ViewDistance: 20
  GizmoOffset: {x: 0, y: 0, z: 0}
  LockX: 0
  LockedX: 0
  LockY: 0
  LockedY: 0
  LockZ: 0
  LockedZ: 0
  DisplayText: 1
  TextMode: 0
  TextToDisplay: Some Text
  TextOffset: {x: -0.5, y: 0.5, z: 0}
  TextFontStyle: 0
  TextSize: 12
  TextColor: {r: 1, g: 0.76862746, b: 0, a: 1}
  TextBackgroundColor: {r: 0, g: 0, b: 0, a: 0.3}
  TextPadding: {x: 5, y: 0, z: 5, w: 0}
  TextMaxDistance: 14
  TargetProperty:
    TargetObject: {fileID: 0}
    TargetComponent: {fileID: 0}
    TargetScriptableObject: {fileID: 0}
    TargetPropertyName: 
--- !u!4 &1799972281
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1799972279}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -2, y: 6, z: -0.021}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 14
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1805161126
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1805161129}
  - component: {fileID: 1805161128}
  - component: {fileID: 1805161127}
  m_Layer: 0
  m_Name: Main Camera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!81 &1805161127
AudioListener:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1805161126}
  m_Enabled: 1
--- !u!20 &1805161128
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1805161126}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 1
  m_BackGroundColor: {r: 0.19215687, g: 0.3019608, b: 0.4745098, a: 0}
  m_projectionMatrixMode: 1
  m_GateFitMode: 2
  m_FOVAxisMode: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_FocalLength: 50
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.3
  far clip plane: 1000
  field of view: 60
  orthographic: 0
  orthographic size: 5
  m_Depth: -1
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 1
  m_AllowMSAA: 1
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 0
  m_OcclusionCulling: 1
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!4 &1805161129
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1805161126}
  m_LocalRotation: {x: 0, y: 0.12983403, z: 0, w: 0.9915358}
  m_LocalPosition: {x: 0.27, y: 3.657, z: -10.32}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 14.92, z: 0}
--- !u!1 &1899115485
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1899115487}
  - component: {fileID: 1899115486}
  m_Layer: 0
  m_Name: PropertyGizmo
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1899115486
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899115485}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 99ca21bfff9408f44bf12916ef8f4363, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  DisplayGizmo: 1
  GizmoType: 2
  DisplayMode: 0
  GizmoColor: {r: 0.5149194, g: 1, b: 0, a: 1}
  PositionMode: 1
  PositionTexture: {fileID: 2800000, guid: ddf3b2cb509cb234bb7b7f83fc04364d, type: 3}
  TextureSize: {x: 50, y: 50}
  PositionSize: 0.2
  ColliderRenderType: 0
  ViewDistance: 20
  GizmoOffset: {x: 0, y: 0, z: 0}
  LockX: 0
  LockedX: 0
  LockY: 0
  LockedY: 0
  LockZ: 1
  LockedZ: 0
  DisplayText: 1
  TextMode: 5
  TextToDisplay: Some Text
  TextOffset: {x: -0.5, y: 0.5, z: 0}
  TextFontStyle: 0
  TextSize: 12
  TextColor: {r: 1, g: 0.76862746, b: 0, a: 1}
  TextBackgroundColor: {r: 0, g: 0, b: 0, a: 0.3}
  TextPadding: {x: 5, y: 0, z: 5, w: 0}
  TextMaxDistance: 14
  TargetProperty:
    TargetObject: {fileID: 741973222}
    TargetComponent: {fileID: 741973227}
    TargetScriptableObject: {fileID: 0}
    TargetPropertyName: position
--- !u!4 &1899115487
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1899115485}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 6, y: 4, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 20
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1900779672
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1900779674}
  - component: {fileID: 1900779673}
  m_Layer: 0
  m_Name: LineRight
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1900779673
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1900779672}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 99ca21bfff9408f44bf12916ef8f4363, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  DisplayGizmo: 1
  GizmoType: 2
  DisplayMode: 0
  GizmoColor: {r: 1, g: 0.76862746, b: 0, a: 1}
  PositionMode: 11
  PositionTexture: {fileID: 0}
  TextureSize: {x: 1, y: 1}
  PositionSize: 0.5
  ColliderRenderType: 0
  ViewDistance: 20
  GizmoOffset: {x: 0, y: 0, z: 0}
  LockX: 0
  LockedX: 0
  LockY: 0
  LockedY: 0
  LockZ: 0
  LockedZ: 0
  DisplayText: 1
  TextMode: 0
  TextToDisplay: Some Text
  TextOffset: {x: -0.5, y: 0.5, z: 0}
  TextFontStyle: 0
  TextSize: 12
  TextColor: {r: 1, g: 0.76862746, b: 0, a: 1}
  TextBackgroundColor: {r: 0, g: 0, b: 0, a: 0.3}
  TextPadding: {x: 5, y: 0, z: 5, w: 0}
  TextMaxDistance: 14
  TargetProperty:
    TargetObject: {fileID: 0}
    TargetComponent: {fileID: 0}
    TargetScriptableObject: {fileID: 0}
    TargetPropertyName: 
--- !u!4 &1900779674
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1900779672}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 8, z: -0.338}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 23
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2004486147
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2004486150}
  - component: {fileID: 2004486149}
  - component: {fileID: 2004486148}
  m_Layer: 0
  m_Name: SphereFull
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!135 &2004486148
SphereCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2004486147}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.5
  m_Center: {x: 0, y: 0, z: 0}
--- !u!114 &2004486149
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2004486147}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 99ca21bfff9408f44bf12916ef8f4363, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  DisplayGizmo: 1
  GizmoType: 1
  DisplayMode: 0
  GizmoColor: {r: 1, g: 0.76862746, b: 0, a: 0.69803923}
  PositionMode: 0
  PositionTexture: {fileID: 0}
  TextureSize: {x: 1, y: 1}
  PositionSize: 0.2
  ColliderRenderType: 0
  ViewDistance: 20
  GizmoOffset: {x: 0, y: 0, z: 0}
  LockX: 0
  LockedX: 0
  LockY: 0
  LockedY: 0
  LockZ: 0
  LockedZ: 0
  DisplayText: 1
  TextMode: 0
  TextToDisplay: Some Text
  TextOffset: {x: -0.5, y: 0.75, z: 0}
  TextFontStyle: 0
  TextSize: 12
  TextColor: {r: 1, g: 0.76862746, b: 0, a: 1}
  TextBackgroundColor: {r: 0, g: 0, b: 0, a: 0.3}
  TextPadding: {x: 5, y: 0, z: 5, w: 0}
  TextMaxDistance: 14
  TargetProperty:
    TargetObject: {fileID: 0}
    TargetComponent: {fileID: 0}
    TargetScriptableObject: {fileID: 0}
    TargetPropertyName: 
--- !u!4 &2004486150
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2004486147}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 2, y: 2, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 5
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2063234037
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2063234039}
  - component: {fileID: 2063234038}
  m_Layer: 0
  m_Name: PositionArrowRight
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &2063234038
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2063234037}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 99ca21bfff9408f44bf12916ef8f4363, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  DisplayGizmo: 1
  GizmoType: 2
  DisplayMode: 0
  GizmoColor: {r: 1, g: 0.76862746, b: 0, a: 1}
  PositionMode: 7
  PositionTexture: {fileID: 0}
  TextureSize: {x: 1, y: 1}
  PositionSize: 0.5
  ColliderRenderType: 0
  ViewDistance: 20
  GizmoOffset: {x: 0, y: 0, z: 0}
  LockX: 0
  LockedX: 0
  LockY: 0
  LockedY: 0
  LockZ: 0
  LockedZ: 0
  DisplayText: 1
  TextMode: 0
  TextToDisplay: Some Text
  TextOffset: {x: -0.5, y: 0.5, z: 0}
  TextFontStyle: 0
  TextSize: 12
  TextColor: {r: 1, g: 0.76862746, b: 0, a: 1}
  TextBackgroundColor: {r: 0, g: 0, b: 0, a: 0.3}
  TextPadding: {x: 5, y: 0, z: 5, w: 0}
  TextMaxDistance: 14
  TargetProperty:
    TargetObject: {fileID: 0}
    TargetComponent: {fileID: 0}
    TargetScriptableObject: {fileID: 0}
    TargetPropertyName: 
--- !u!4 &2063234039
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2063234037}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 6, z: -0.021}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 15
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
