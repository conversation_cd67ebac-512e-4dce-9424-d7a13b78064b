/* COLOR VARIABLES*/
:root {
    --bg-color: #000;
    --highlight-light: #fff;
    --highlight-dark: #21a6f0;
    --accent-dim: #111;
    --accent-super-dim: #080808;
    --font-color: #999;
    --hover-color: #fff;
  }

  body {
    color: var(--font-color);
    font-family: "Roboto", sans-serif;
    line-height: 1.5;
    font-size: 16px;
    -ms-text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%;
    word-wrap: break-word;
    background-color: var(--bg-color);
  }

  /* HIGHLIGHT COLOR */

  button,
  a {
    color: var(--highlight-dark);
    cursor: pointer;
  }

  button:hover,
  button:focus,
  a:hover,
  a:focus {
    color: var(--hover-color);
    text-decoration: none;
  }

  .toc .nav > li.active > a {
    color: var(--highlight-dark);
  }

  .toc .nav > li.active > a:hover,
  .toc .nav > li.active > a:focus {
    color: var(--hover-color);
  }

  .pagination > .active > a {
    background-color: var(--bg-color);
    border-color: var(--bg-color);
  }

  .pagination > .active > a,
  .pagination > .active > a:focus,
  .pagination > .active > a:hover,
  .pagination > .active > span,
  .pagination > .active > span:focus,
  .pagination > .active > span:hover {
    background-color: var(--highlight-light);
    border-color: var(--highlight-light);
  }

  /* HEADINGS */

  h1 {
    font-weight: 600;
    font-size: 32px;
  }

  h2 {
    font-weight: 600;
    font-size: 24px;
    line-height: 1.8;
  }

  h3 {
    font-weight: 600;
    font-size: 20px;
    line-height: 1.8;
  }

  h5 {
    font-size: 14px;
    padding: 10px 0px;
  }

  article h1,
  article h2,
  article h3,
  article h4 {
    margin-top: 35px;
    margin-bottom: 15px;
  }

  article h4 {
    padding-bottom: 8px;
    border-bottom: 2px solid #222;
  }

  /* NAVBAR */

  .navbar-brand > img {
    color: var(--font-color);
  }

  .navbar {
    border: none;
  }

  .subnav {
    border-top: 1px solid #222;
    background-color: var(--bg-color);
  }

  .navbar-inverse {
    background-color: var(--bg-color);
    z-index: 100;
  }

  .navbar-inverse .navbar-nav > li > a,
  .navbar-inverse .navbar-text {
    color: var(--font-color);
    background-color: var(--bg-color);
    border-bottom: 3px solid transparent;
    padding-bottom: 12px;
    /* transition: 350ms; */
  }

  .navbar-inverse .navbar-nav > li > a:focus,
  .navbar-inverse .navbar-nav > li > a:hover {
    color: var(--font-color);
    background-color: var(--bg-color);
    border-bottom: 3px solid var(--bg-color);
  }

  .navbar-inverse .navbar-nav > .active > a,
  .navbar-inverse .navbar-nav > .active > a:focus,
  .navbar-inverse .navbar-nav > .active > a:hover {
    color: var(--font-color);
    background-color: var(--bg-color);
    border-bottom: 3px solid var(--bg-color);
  }

  .navbar-form .form-control {
    border: 0;
    border-radius: 4px;
  }

  /* NAVBAR TOGGLED (small screens) */

  .navbar-inverse .navbar-collapse, .navbar-inverse .navbar-form {
    border: none;
  }
  .navbar-inverse .navbar-toggle {
    /* box-shadow: var(--card-box-shadow); */
    border: none;
  }

  .navbar-inverse .navbar-toggle:focus,
  .navbar-inverse .navbar-toggle:hover {
    background-color: var(--highlight-dark);
  }

  /* SIDEBAR */

  .toc .level1 > li {
    font-weight: 400;
  }

  .toc .nav > li > a {
    color: var(--font-color);
  }

  .sidefilter {
    background-color: var(--bg-color);
    border-left: none;
    border-right: none;
  }

  .sidefilter {
    background-color: var(--bg-color);
    border-left: none;
    border-right: none;
  }

  .toc-filter {
    background-color: inherit;
    padding: 5px;
    margin: 0;
    /* box-shadow: var(--card-box-shadow); */
    /* transition:var(--transition); */
  }

  .toc-filter > input {
    color: white;
  }

  .toc-filter:hover {
    background-color: var(--accent-super-dim);
  }

  .toc-filter > .filter-icon {
    display: none;
  }

  .sidetoc > .toc {
    background-color: var(--bg-color);
    overflow-x: hidden;
  }

  .sidetoc {
    background-color: var(--bg-color);
    border: none;
  }

  /* ALERTS */

  .alert {
    padding: 0px 0px 5px 0px;
    color: inherit;
    background-color: inherit;
    border: none;
    /* box-shadow: var(--card-box-shadow); */
  }

  .alert > p {
    margin-bottom: 0;
    padding: 5px 10px;
  }

  .alert > ul {
    margin-bottom: 0;
    padding: 5px 40px;
  }

  .alert > h5 {
    padding: 10px 15px;
    margin-top: 0;
    text-transform: uppercase;
    font-weight: bold;
    border-radius: 4px 4px 0 0;
  }

  .alert-info > h5 {
    color: #1976d2;
    border-bottom: 4px solid #1976d2;
    background-color: #e3f2fd;
  }

  .alert-warning > h5 {
    color: #f57f17;
    border-bottom: 4px solid #f57f17;
    background-color: #fff3e0;
  }

  .alert-danger > h5 {
    color: #d32f2f;
    border-bottom: 4px solid #d32f2f;
    background-color: #ffebee;
  }

  /* CODE HIGHLIGHT */
  pre {
    padding: 9.5px;
    margin: 0 0 10px;
    font-size: 13px;
    word-break: break-all;
    word-wrap: break-word;
    background-color: #222;
    border-radius: 4px;
    border: none;
    /* box-shadow: var(--card-box-shadow); */
  }

  /* STYLE FOR IMAGES */

  .article .small-image {
    margin-top: 15px;
    /* box-shadow: var(--card-box-shadow); */
    max-width: 350px;
  }

  .article .medium-image {
    margin-top: 15px;
    /* box-shadow: var(--card-box-shadow); */
    max-width: 550px;
  }

  .article .large-image {
    margin-top: 15px;
    /* box-shadow: var(--card-box-shadow); */
    max-width: 700px;
  }

  input {
    background-color: #222;
    border: 1px solid #333;
    color: var(--font-color);
    border-radius: 4px;
    padding: 5px;
  }

  .form-control {
    background-color: #222;
    border: 1px solid #333;
    color: var(--font-color);
    border-radius: 4px;
    padding: 5px;
    padding-left: 20px;
    padding-right: 20px;
  }

  .toc .nav > li > a:hover, .toc .nav > li > a:focus {
    color: var(--hover-color);
  }

  .table-striped>tbody>tr:nth-of-type(odd) {
    background-color: inherit;
  }

  .hljs {
    color: #32BBB0;
  }

  .hljs-title {
    color: #DCDC9D;
  }

  .hljs-params {
    color: #7CDCFE;
  }

  .hljs-literal {
    color: #3F9CD6;
  }

  .table-bordered {
    border: 1px solid #333;
  }

  .table-bordered>tbody>tr>td, .table-bordered>tbody>tr>th, .table-bordered>tfoot>tr>td, .table-bordered>tfoot>tr>th, .table-bordered>thead>tr>td, .table-bordered>thead>tr>th {
    border: 1px solid #333;
  }

  .affix ul > li.active > a, .affix ul > li.active > a:before {
    color: var(--highlight-dark);
  }

  .navbar-brand {
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

  .footer {
    background-color: var(--bg-color);
    color: var(--font-color);
    padding: 15px;
    border-top: 1px solid #333;
  }

  .container {
    width: 100%;
    max-width: 1600px;
  }

  .sidenav {
    background-color: var(--bg-color);
    color: var(--font-color);
    border: none;
  }