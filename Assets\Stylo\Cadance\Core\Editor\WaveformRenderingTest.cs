using UnityEngine;
using UnityEditor;
using FMODUnity;
using System.Linq;

namespace Stylo.Cadance
{
    /// <summary>
    /// Test tool for verifying waveform rendering fixes in Cadance Editor.
    /// Tests waveform generation, scaling, and zoom responsiveness.
    /// </summary>
    public static class WaveformRenderingTest
    {
        [MenuItem("Stylo/Cadance/Tools/🎵 Test Waveform Rendering")]
        public static void TestWaveformRendering()
        {
            Debug.Log("=== 🎵 WAVEFORM RENDERING TEST ===");

            bool allTestsPassed = true;

            // Test 1: Find AudioClips for testing
            Debug.Log("1️⃣ Finding AudioClips for testing...");
            string[] audioGuids = AssetDatabase.FindAssets("t:AudioClip");

            if (audioGuids.Length == 0)
            {
                Debug.LogError("❌ No AudioClips found in project for testing");
                EditorUtility.DisplayDialog("Waveform Test", "No AudioClips found in project.\n\nImport some audio files first.", "OK");
                return;
            }

            Debug.Log($"✅ Found {audioGuids.Length} AudioClips for testing");

            // Test 2: Test waveform generation methods
            Debug.Log("2️⃣ Testing waveform generation methods...");

            for (int i = 0; i < Mathf.Min(3, audioGuids.Length); i++)
            {
                string path = AssetDatabase.GUIDToAssetPath(audioGuids[i]);
                var testClip = AssetDatabase.LoadAssetAtPath<AudioClip>(path);

                if (testClip != null)
                {
                    Debug.Log($"🧪 Testing with: {testClip.name} ({testClip.length:F2}s, {testClip.samples} samples)");

                    // Test direct Unity method
                    bool directSuccess = TestDirectWaveformGeneration(testClip);

                    // Test FMOD method
                    bool fmodSuccess = TestFMODWaveformGeneration(testClip);

                    if (!directSuccess && !fmodSuccess)
                    {
                        Debug.LogWarning($"⚠️ Both methods failed for {testClip.name}");
                        allTestsPassed = false;
                    }
                    else
                    {
                        Debug.Log($"✅ At least one method succeeded for {testClip.name}");
                    }
                }
            }

            // Test 3: Test Cadance Editor integration
            Debug.Log("3️⃣ Testing Cadance Editor integration...");
            bool editorIntegrationSuccess = TestCadanceEditorIntegration();

            if (!editorIntegrationSuccess)
            {
                allTestsPassed = false;
            }

            // Test 4: Test zoom and scaling
            Debug.Log("4️⃣ Testing zoom and scaling calculations...");
            bool scalingSuccess = TestWaveformScaling();

            if (!scalingSuccess)
            {
                allTestsPassed = false;
            }

            // Final Results
            string result = allTestsPassed ? "✅ ALL TESTS PASSED" : "❌ SOME TESTS FAILED";
            Debug.Log($"=== Waveform Test Results: {result} ===");

            string summary = $"Waveform Rendering Test Results:\n\n" +
                           $"📁 AudioClips Found: {audioGuids.Length}\n" +
                           $"🔧 Generation Methods: {(allTestsPassed ? "Working" : "Issues Found")}\n" +
                           $"🎵 Editor Integration: {(editorIntegrationSuccess ? "✅ Working" : "❌ Failed")}\n" +
                           $"📏 Scaling/Zoom: {(scalingSuccess ? "✅ Working" : "❌ Failed")}\n\n" +
                           $"Status: {result}\n\n" +
                           $"💡 Next Steps:\n" +
                           $"1. Open Cadance Editor\n" +
                           $"2. Load a CadanceAsset with AudioClip\n" +
                           $"3. Verify waveform displays correctly\n" +
                           $"4. Test zoom in/out functionality";

            EditorUtility.DisplayDialog("Waveform Rendering Test", summary, "OK");
        }

        private static bool TestDirectWaveformGeneration(AudioClip clip)
        {
            try
            {
                Debug.Log($"   🔧 Testing direct Unity waveform generation...");

                int sampleCount = clip.samples * clip.channels;
                float[] samples = new float[sampleCount];

                if (clip.GetData(samples, 0))
                {
                    // Test downsampling
                    int targetSamples = 1024;
                    float[] waveform = new float[targetSamples];
                    int step = Mathf.Max(1, sampleCount / targetSamples);

                    for (int i = 0; i < targetSamples; i++)
                    {
                        float maxAmplitude = 0f;
                        int start = i * step;
                        int end = Mathf.Min(start + step, sampleCount);

                        for (int j = start; j < end; j += clip.channels)
                        {
                            float sampleSum = 0f;
                            for (int ch = 0; ch < clip.channels; ch++)
                            {
                                if (j + ch < sampleCount)
                                    sampleSum += samples[j + ch];
                            }
                            float avgSample = sampleSum / clip.channels;
                            maxAmplitude = Mathf.Max(maxAmplitude, Mathf.Abs(avgSample));
                        }

                        waveform[i] = maxAmplitude;
                    }

                    // Check if waveform has valid data
                    bool hasData = false;
                    for (int i = 0; i < waveform.Length; i++)
                    {
                        if (waveform[i] > 0.001f)
                        {
                            hasData = true;
                            break;
                        }
                    }

                    if (hasData)
                    {
                        Debug.Log($"   ✅ Direct method successful: {waveform.Length} samples with valid data");
                        return true;
                    }
                    else
                    {
                        Debug.LogWarning($"   ⚠️ Direct method generated empty waveform");
                        return false;
                    }
                }
                else
                {
                    Debug.LogWarning($"   ⚠️ Direct method failed: Could not get audio data");
                    return false;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"   ❌ Direct method error: {ex.Message}");
                return false;
            }
        }

        private static bool TestFMODWaveformGeneration(AudioClip clip)
        {
            try
            {
                Debug.Log($"   🎵 Testing FMOD waveform generation...");

                float[] waveform = FMODWaveformGenerator.GenerateWaveformForAudioClip(clip, 1024);

                if (waveform != null && waveform.Length > 0)
                {
                    // Check if waveform has valid data
                    bool hasData = false;
                    for (int i = 0; i < waveform.Length; i++)
                    {
                        if (waveform[i] > 0.001f)
                        {
                            hasData = true;
                            break;
                        }
                    }

                    if (hasData)
                    {
                        Debug.Log($"   ✅ FMOD method successful: {waveform.Length} samples with valid data");
                        return true;
                    }
                    else
                    {
                        Debug.LogWarning($"   ⚠️ FMOD method generated empty waveform");
                        return false;
                    }
                }
                else
                {
                    Debug.LogWarning($"   ⚠️ FMOD method failed: No waveform data returned");
                    return false;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"   ❌ FMOD method error: {ex.Message}");
                return false;
            }
        }

        private static bool TestCadanceEditorIntegration()
        {
            try
            {
                Debug.Log($"   🎵 Testing Cadance Editor integration...");

                // Check if CadanceEditorWindow exists and has required methods
                var editorType = System.Type.GetType("Stylo.Cadance.CadanceEditorWindow");
                if (editorType == null)
                {
                    Debug.LogError($"   ❌ CadanceEditorWindow type not found");
                    return false;
                }

                var methods = editorType.GetMethods(System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                var requiredMethods = new string[] { "DrawWaveform", "UpdateWaveformData", "GenerateWaveformDirect" };
                var missingMethods = new System.Collections.Generic.List<string>();

                foreach (var methodName in requiredMethods)
                {
                    if (!methods.Any(m => m.Name == methodName))
                    {
                        missingMethods.Add(methodName);
                    }
                }

                if (missingMethods.Count == 0)
                {
                    Debug.Log($"   ✅ All required methods found in CadanceEditorWindow");
                    return true;
                }
                else
                {
                    Debug.LogError($"   ❌ Missing methods: {string.Join(", ", missingMethods)}");
                    return false;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"   ❌ Editor integration test error: {ex.Message}");
                return false;
            }
        }

        private static bool TestWaveformScaling()
        {
            try
            {
                Debug.Log($"   📏 Testing waveform scaling calculations...");

                // Test scaling calculations
                float duration = 10f; // 10 second audio
                float timelineWidth = 800f; // 800 pixel timeline
                float zoom = 1f;

                float pixelsPerSecond = timelineWidth / duration * zoom;
                Debug.Log($"   📊 Base calculation: {pixelsPerSecond} pixels/second");

                // Test zoom scaling
                zoom = 2f;
                float zoomedPixelsPerSecond = timelineWidth / duration * zoom;
                Debug.Log($"   📊 2x zoom: {zoomedPixelsPerSecond} pixels/second");

                // Test time to pixel conversion
                float testTime = 5f; // 5 seconds
                float pixelPosition = testTime * zoomedPixelsPerSecond;
                Debug.Log($"   📊 Time {testTime}s -> Pixel {pixelPosition}");

                // Test pixel to time conversion
                float backToTime = pixelPosition / zoomedPixelsPerSecond;
                Debug.Log($"   📊 Pixel {pixelPosition} -> Time {backToTime}s");

                if (Mathf.Abs(backToTime - testTime) < 0.001f)
                {
                    Debug.Log($"   ✅ Scaling calculations are accurate");
                    return true;
                }
                else
                {
                    Debug.LogError($"   ❌ Scaling calculation error: {testTime} != {backToTime}");
                    return false;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"   ❌ Scaling test error: {ex.Message}");
                return false;
            }
        }

        [MenuItem("Stylo/Cadance/Tools/📊 Open Cadance Editor for Waveform Test")]
        public static void OpenCadanceEditorForTest()
        {
            Debug.Log("=== 📊 OPENING CADANCE EDITOR FOR WAVEFORM TEST ===");

            try
            {
                // Try to get the Cadance Editor window type
                var editorType = System.Type.GetType("Stylo.Cadance.CadanceEditorWindow");
                if (editorType != null)
                {
                    var window = EditorWindow.GetWindow(editorType, false, "Cadance Editor - Waveform Test");
                    if (window != null)
                    {
                        Debug.Log("✅ Cadance Editor opened for waveform testing");

                        // Check for test assets
                        string[] cadanceGuids = AssetDatabase.FindAssets("t:CadanceAsset");
                        string[] audioGuids = AssetDatabase.FindAssets("t:AudioClip");

                        string instructions = $"Cadance Editor opened for waveform testing!\n\n" +
                                            $"📁 Found {cadanceGuids.Length} CadanceAssets\n" +
                                            $"🎵 Found {audioGuids.Length} AudioClips\n\n" +
                                            $"Test Instructions:\n" +
                                            $"1. Open a CadanceAsset with linked AudioClip\n" +
                                            $"2. Verify waveform displays clearly (no corruption)\n" +
                                            $"3. Test zoom in/out with mouse wheel\n" +
                                            $"4. Verify waveform scales with timeline\n" +
                                            $"5. Check tempo grid alignment\n\n" +
                                            $"Expected: Clean waveform that scales properly!";

                        EditorUtility.DisplayDialog("Waveform Test Ready", instructions, "Start Testing");
                    }
                    else
                    {
                        Debug.LogError("❌ Failed to create Cadance Editor window");
                    }
                }
                else
                {
                    Debug.LogError("❌ CadanceEditorWindow type not found");
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"❌ Failed to open Cadance Editor: {ex.Message}");
            }
        }
    }
}
