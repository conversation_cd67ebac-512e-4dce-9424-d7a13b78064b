using System.Collections.Generic;
using System.Linq;
using UnityEditor;
using UnityEngine;

namespace ExtendedImportSettings
{
    [System.Serializable]
    public class PerMeshDataBase
    {
        public bool IsUsing = true;
    }
    
    [System.Serializable]
    public abstract class PerMeshModuleBase<TData> : ExtendedImportModule where TData: PerMeshDataBase, new()
    {
        public List<string> NamePerMesh;
        public List<TData> DataPerMesh;
        public TData DataGlobal;
        
        private List<Mesh> _meshes;
        
        public override void OnEnableGUI(object assetImporter)
        {
            _meshes = ModuleUtilities.GetModelMeshes(assetImporter);
        }
        
        public override void OnDisableGUI(object assetImporter)
        {
            _meshes.Clear();
            _meshes = null;
        }

        protected virtual void OnInspectorGUIPerMesh(TData data, int index, SerializedProperty moduleSerializedProperty,
            object assetImporter)
        {
            if (index == -1)
            {
                SerializedProperty elementSerializedProperty = moduleSerializedProperty.FindPropertyRelative("DataGlobal");
                ModuleUtilities.DrawPropertyField(typeof(TData), elementSerializedProperty, "IsUsing");
            }
            else
            {
                SerializedProperty arraySerializedProperty = moduleSerializedProperty.FindPropertyRelative("DataPerMesh");
                if (arraySerializedProperty != null && index < arraySerializedProperty.arraySize)
                {
                    SerializedProperty elementSerializedProperty = arraySerializedProperty.GetArrayElementAtIndex(index);
                    ModuleUtilities.DrawPropertyField(typeof(TData), elementSerializedProperty, "IsUsing");
                }
            }
        }
        
        public override void OnInspectorGUI(SerializedProperty moduleSerializedProperty, object assetImporter)
        {
            if (_meshes == null || _meshes.Count == 0)
                return;

            bool isPerMesh = !DataGlobal.IsUsing;
            isPerMesh = EditorGUILayout.Toggle("Per Mesh", isPerMesh);
            DataGlobal.IsUsing = !isPerMesh;
            
            if (isPerMesh)
            {
                if (NamePerMesh == null)
                {
                    NamePerMesh = new List<string>();
                    DataPerMesh = new List<TData>();
                }
                
                for (int i = NamePerMesh.Count - 1; i >= 0; i--)
                {
                    if (_meshes.All(mesh => mesh.name != NamePerMesh[i]))
                    {
                        NamePerMesh.RemoveAt(i);
                        DataPerMesh.RemoveAt(i);
                    }
                }

                for (var i = 0; i < _meshes.Count; i++)
                {
                    var meshName = _meshes[i].name;
                    if (!NamePerMesh.Contains(meshName))
                    {
                        NamePerMesh.Insert(i, meshName);
                        DataPerMesh.Insert(i, new TData());
                    }
                
                    EditorGUILayout.BeginHorizontal();
                    DataPerMesh[i].IsUsing = EditorGUILayout.Toggle(DataPerMesh[i].IsUsing, GUILayout.Width(16));
                    var wasEnable = GUI.enabled;
                    GUI.enabled = false;
                    EditorGUILayout.ObjectField(_meshes[i], typeof(Mesh), false);
                    GUI.enabled = wasEnable;
                    EditorGUILayout.EndHorizontal();
                    OnInspectorGUIPerMesh(DataPerMesh[i], i, moduleSerializedProperty, assetImporter);
                    EditorGUILayout.Space();
                }
            }
            else
            {
                OnInspectorGUIPerMesh(DataGlobal, -1, moduleSerializedProperty, assetImporter);
            }
        }
        
        protected void Execute(GameObject gameObject, out int count)
        {
            var meshFilters = gameObject.GetComponentsInChildren<MeshFilter>();
            var skinnedMeshes = gameObject.GetComponentsInChildren<SkinnedMeshRenderer>();
            var isPerMesh = !DataGlobal.IsUsing;

            Dictionary<Component, Mesh> meshes = new ();
            for (var i = 0; i < meshFilters.Length; i++)
            {
                var mesh = meshFilters[i].sharedMesh;
                if (mesh == null)
                    continue;
                meshes.Add(meshFilters[i], mesh);
            }
            
            for (var i = 0; i < skinnedMeshes.Length; i++)
            {
                var mesh = skinnedMeshes[i].sharedMesh;
                if (mesh == null)
                    continue;
                meshes.Add(skinnedMeshes[i], mesh);
            }

            count = 0;
            foreach (var mesh in meshes)
            {
                count++;
                if (isPerMesh)
                {
                    var index = NamePerMesh.IndexOf(mesh.Value.name);
                    if (index != -1 && DataPerMesh[index].IsUsing)
                        OnPostprocessModelPerMesh(mesh.Value,DataPerMesh[index], mesh.Key.gameObject, gameObject);
                }
                else
                {
                    OnPostprocessModelPerMesh(mesh.Value, DataGlobal, mesh.Key.gameObject, gameObject);
                }
            }
        }

        public override void OnPostprocessModel(GameObject gameObject)
        {
            Execute(gameObject, out var meshCount);
        }
        
        protected abstract void OnPostprocessModelPerMesh(Mesh mesh, TData data,
            GameObject meshGameObject, GameObject rootGameObject);
    }
}