%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &1002498400835686
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4173073848065700}
  m_Layer: 0
  m_Name: BMedusaBigTentacle_1_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4173073848065700
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1002498400835686}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00022835647, y: -1.7013883e-12, z: 0.000000007450581, w: 1}
  m_LocalPosition: {x: -0, y: 8.881784e-17, z: -0.21457219}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4029686301358206}
  m_Father: {fileID: 4433906129938410}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1021247636083130
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4119037796251514}
  m_Layer: 0
  m_Name: BMedusaBigTentacle_6_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4119037796251514
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1021247636083130}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0076925354, y: -1.147386e-10, z: 8.8265685e-13, w: 0.99997044}
  m_LocalPosition: {x: 3.0443204e-11, y: 0, z: -0.26354817}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4465872659551170}
  m_Father: {fileID: 4253242480666078}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1023069578205144
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4916249086574084}
  m_Layer: 0
  m_Name: BMedusaSmallTentacle_6_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4916249086574084
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1023069578205144}
  serializedVersion: 2
  m_LocalRotation: {x: 0.007298687, y: 3.523471e-33, z: -2.5717397e-35, w: 0.99997336}
  m_LocalPosition: {x: -0, y: 1.7763569e-16, z: -0.15702073}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4765463346945718}
  m_Father: {fileID: 4705594881460506}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1038957955854794
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4446627823584482}
  m_Layer: 0
  m_Name: BMedusaHead_1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4446627823584482
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1038957955854794}
  serializedVersion: 2
  m_LocalRotation: {x: -0.45358548, y: 1.10997765e-16, z: 2.3340477e-18, w: 0.89121276}
  m_LocalPosition: {x: 6.414572e-19, y: 3.3306692e-17, z: -0.13746744}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4093775943049514}
  m_Father: {fileID: 4967518121876084}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1065548424644192
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4967518121876084}
  - component: {fileID: 114895685674720242}
  m_Layer: 0
  m_Name: BMedusaHead
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4967518121876084
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1065548424644192}
  serializedVersion: 2
  m_LocalRotation: {x: 0.15767717, y: -0.6893025, z: 0.15767717, w: 0.6893025}
  m_LocalPosition: {x: 0.09792989, y: 0.047975674, z: -0.010494943}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4446627823584482}
  - {fileID: 4852971330691566}
  - {fileID: 4539490278012388}
  m_Father: {fileID: 4516784778058484}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &114895685674720242
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1065548424644192}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 61574e354ba34a141ae2cc1228f5eda3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  UseCollision: 0
  CollisionSpace: 1
  CollisionMode: 0
  CheapCollision: 0
  DynamicWorldCollidersInclusion: 0
  InclusionRadius: 1
  IgnoreMeshColliders: 1
  IncludedColliders: []
  IncludedColliders2D: []
  CollidersType: 0
  CollideWithOtherTails: 0
  CollideWithDisabledColliders: 1
  CollisionSlippery: 1
  ReflectCollision: 0
  CollidersScaleCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  CollidersScaleMul: 6.5
  CollisionsAutoCurve: 0.5
  IgnoredColliders: []
  IgnoredColliders2D: []
  CollidersSameLayer: 1
  CollidersAddRigidbody: 1
  RigidbodyMass: 1
  CollidersLayer: 0
  UseSlitheryCurve: 0
  SlitheryCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.75
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseCurlingCurve: 0
  CurlingCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.3
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseSpringCurve: 0
  SpringCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseSlipperyCurve: 0
  SlipperyCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UsePosSpeedCurve: 0
  PosCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseRotSpeedCurve: 0
  RotCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.9
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UsePartialBlend: 0
  BlendCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.95
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.45
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseIK: 0
  IK:
    IKWeight: 0
    IKTargetPosition: {x: 0, y: 0, z: 0}
    IKTargetRotation: {x: 0, y: 0, z: 0, w: 0}
    LastLocalDirection: {x: 0, y: 0, z: 0}
    LocalDirection: {x: 0, y: 0, z: 0}
    IKBones: []
    ContinousSolving: 0
    SyncWithAnimator: 0
    ReactionQuality: 0
    Smoothing: 0
    StretchToTarget: 0
    StretchCurve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    Use2D: 0
    Invert: 0
  IKTarget: {fileID: 0}
  IKAutoWeights: 1
  IKBaseReactionWeight: 0.65
  IKReactionWeightCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.25
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  IKAutoAngleLimits: 1
  IKAutoAngleLimit: 40
  IKContinousSolve: 0
  IkInvertOrder: 0
  IKBlend: 1
  IKAnimatorBlend: 0.5
  IKReactionQuality: 2
  IKSmoothing: 0
  IKStretchToTarget: 0
  IKStretchCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  IKLimitSettings: []
  IKSelectiveChain: 0
  RotationOffset: {x: 0, y: 0, z: 0, w: 1}
  Curving: {x: 0, y: 0, z: 0, w: 1}
  UseCurvingCurve: 0
  CurvCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.75
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  LengthMultiplier: 1
  UseLengthMulCurve: 0
  LengthMulCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseGravityCurve: 0
  GravityCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.65
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  Gravity: {x: 0, y: 0, z: 0}
  UseWaving: 1
  CosinusAdd: 0
  FixedCycle: 0.1
  WavingSpeed: 2
  WavingRange: 0.5
  WavingAxis: {x: 1, y: 0.2, z: 0.2}
  WavingType: 0
  AlternateWave: 2
  UseWind: 0
  WindEffectPower: 1
  WindTurbulencePower: 1
  WindWorldNoisePower: 0.5
  WindEffect: {x: 0, y: 0, z: 0}
  fDebug: 0
  _gizmosEditorStartPreview: {fileID: 0}
  _gizmospesp: {fileID: 0}
  _gizmosEditorEndPreview: {fileID: 0}
  _gizmospeep: {fileID: 0}
  _editor_Title: Tail Animator 2
  _editor_IsInspectorViewingColliders: 0
  _editor_IsInspectorViewingIncludedColliders: 0
  _editor_animatorViewedCounter: 1
  TailSegments: []
  GhostParent:
    ProceduralPosition: {x: 0, y: 0, z: 0}
    ProceduralPositionWeightBlended: {x: 0, y: 0, z: 0}
    TrueTargetRotation: {x: 0, y: 0, z: 0, w: 1}
    PosRefRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosReferenceRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosition: {x: 0, y: 0, z: 0}
    BlendValue: 1
    BoneDimensionsScaled: {x: 0, y: 0, z: 0}
    BoneLengthScaled: 0
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalPositionInRoot: {x: 0, y: 0, z: 0}
    InitialLocalRotationInRoot: {x: 0, y: 0, z: 0, w: 1}
    LocalOffset: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: 0, y: 0, z: 0, w: 1}
    ColliderRadius: 1
    CollisionContactFlag: 0
    CollisionContactRelevancy: -1
    VelocityHelper: {x: 0, y: 0, z: 0}
    QVelocityHelper: {x: 0, y: 0, z: 0, w: 1}
    PreviousPush: {x: 0, y: 0, z: 0}
    Curving: {x: 0, y: 0, z: 0, w: 1}
    Gravity: {x: 0, y: 0, z: 0}
    GravityLookOffset: {x: 0, y: 0, z: 0}
    LengthMultiplier: 1
    PositionSpeed: 1
    RotationSpeed: 1
    Springiness: 0
    Slithery: 1
    Curling: 0.5
    Slippery: 1
    LastKeyframeLocalRotation: {x: 0, y: 0, z: 0, w: 0}
    LastKeyframeLocalPosition: {x: 0, y: 0, z: 0}
  GhostChild:
    ProceduralPosition: {x: 0, y: 0, z: 0}
    ProceduralPositionWeightBlended: {x: 0, y: 0, z: 0}
    TrueTargetRotation: {x: 0, y: 0, z: 0, w: 1}
    PosRefRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosReferenceRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosition: {x: 0, y: 0, z: 0}
    BlendValue: 1
    BoneDimensionsScaled: {x: 0, y: 0, z: 0}
    BoneLengthScaled: 0
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalPositionInRoot: {x: 0, y: 0, z: 0}
    InitialLocalRotationInRoot: {x: 0, y: 0, z: 0, w: 1}
    LocalOffset: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: 0, y: 0, z: 0, w: 1}
    ColliderRadius: 1
    CollisionContactFlag: 0
    CollisionContactRelevancy: -1
    VelocityHelper: {x: 0, y: 0, z: 0}
    QVelocityHelper: {x: 0, y: 0, z: 0, w: 1}
    PreviousPush: {x: 0, y: 0, z: 0}
    Curving: {x: 0, y: 0, z: 0, w: 1}
    Gravity: {x: 0, y: 0, z: 0}
    GravityLookOffset: {x: 0, y: 0, z: 0}
    LengthMultiplier: 1
    PositionSpeed: 1
    RotationSpeed: 1
    Springiness: 0
    Slithery: 1
    Curling: 0.5
    Slippery: 1
    LastKeyframeLocalRotation: {x: 0, y: 0, z: 0, w: 0}
    LastKeyframeLocalPosition: {x: 0, y: 0, z: 0}
  UseMaxDistance: 0
  DistanceFrom: {fileID: 0}
  _distanceFrom_Auto: {fileID: 0}
  MaximumDistance: 35
  MaxOutDistanceFactor: 0
  DistanceWithoutY: 0
  DistanceMeasurePoint: {x: 0, y: 0, z: 0}
  FadeDuration: 0.75
  Deflection: 0
  DeflectionStartAngle: 10
  DeflectionSmooth: 0
  DeflectionFalloff:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  DeflectOnlyCollisions: 1
  _Editor_Category: 0
  _Editor_FeaturesCategory: 0
  DrawGizmos: 1
  StartBone: {fileID: 4967518121876084}
  EndBone: {fileID: 4093775943049514}
  EndBoneJointOffset: {x: 0, y: 0, z: 0}
  _TransformsGhostChain:
  - {fileID: 4967518121876084}
  - {fileID: 4446627823584482}
  - {fileID: 4093775943049514}
  _GhostChainInitCount: 3
  UpdateRate: 0
  AnimatePhysics: 0
  InterpolateRate: 0
  Prewarm: 0
  Slithery: 1
  Curling: 0.75
  Springiness: 0
  MaxStretching: 1
  AngleLimit: 181
  AngleLimitAxis: {x: 0, y: 0, z: 0}
  LimitAxisRange: {x: 0, y: 0}
  LimitSmoothing: 0.5
  MotionInfluence: 1
  MotionInfluenceInY: 1
  IncludeParent: 0
  UnifyBendiness: 0
  ReactionSpeed: 0.3
  Sustain: 0
  RotationRelevancy: 0.8
  SmoothingStyle: 0
  TimeScale: 1
  DeltaType: 4
  UpdateAsLast: 0
  DetectZeroKeyframes: 1
  StartAfterTPose: 1
  OptimizeWithMesh: {fileID: 0}
  OptimizeWithMeshes: []
  TailAnimatorAmount: 1
  DetachChildren: 0
  Axis2D: 0
  Tangle: 0
  AnimateRoll: 0
  OverrideKeyframeAnimation: 0
--- !u!1 &1098653190230948
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4433906129938410}
  - component: {fileID: 114299217047002064}
  m_Layer: 0
  m_Name: BMedusaBigTentacle_3_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4433906129938410
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1098653190230948}
  serializedVersion: 2
  m_LocalRotation: {x: -0.769424, y: -2.836568e-16, z: -3.416929e-16, w: 0.63873833}
  m_LocalPosition: {x: -0.18518268, y: -0.11469058, z: -0.21089275}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4173073848065700}
  m_Father: {fileID: 4114351381275056}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &114299217047002064
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1098653190230948}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 61574e354ba34a141ae2cc1228f5eda3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  UseCollision: 0
  CollisionSpace: 1
  CollisionMode: 0
  CheapCollision: 0
  DynamicWorldCollidersInclusion: 0
  InclusionRadius: 1
  IgnoreMeshColliders: 1
  IncludedColliders: []
  IncludedColliders2D: []
  CollidersType: 0
  CollideWithOtherTails: 0
  CollideWithDisabledColliders: 1
  CollisionSlippery: 1
  ReflectCollision: 0
  CollidersScaleCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  CollidersScaleMul: 6.5
  CollisionsAutoCurve: 0.5
  IgnoredColliders: []
  IgnoredColliders2D: []
  CollidersSameLayer: 1
  CollidersAddRigidbody: 1
  RigidbodyMass: 1
  CollidersLayer: 0
  UseSlitheryCurve: 0
  SlitheryCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.75
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseCurlingCurve: 0
  CurlingCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.3
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseSpringCurve: 0
  SpringCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseSlipperyCurve: 0
  SlipperyCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UsePosSpeedCurve: 0
  PosCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseRotSpeedCurve: 0
  RotCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.9
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UsePartialBlend: 0
  BlendCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.95
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.45
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseIK: 0
  IK:
    IKWeight: 0
    IKTargetPosition: {x: 0, y: 0, z: 0}
    IKTargetRotation: {x: 0, y: 0, z: 0, w: 0}
    LastLocalDirection: {x: 0, y: 0, z: 0}
    LocalDirection: {x: 0, y: 0, z: 0}
    IKBones: []
    ContinousSolving: 0
    SyncWithAnimator: 0
    ReactionQuality: 0
    Smoothing: 0
    StretchToTarget: 0
    StretchCurve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    Use2D: 0
    Invert: 0
  IKTarget: {fileID: 0}
  IKAutoWeights: 1
  IKBaseReactionWeight: 0.65
  IKReactionWeightCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.25
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  IKAutoAngleLimits: 1
  IKAutoAngleLimit: 40
  IKContinousSolve: 0
  IkInvertOrder: 0
  IKBlend: 1
  IKAnimatorBlend: 0.5
  IKReactionQuality: 2
  IKSmoothing: 0
  IKStretchToTarget: 0
  IKStretchCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  IKLimitSettings: []
  IKSelectiveChain: 0
  RotationOffset: {x: 0, y: 0, z: 0, w: 1}
  Curving: {x: 0, y: 0, z: 0, w: 1}
  UseCurvingCurve: 0
  CurvCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.75
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  LengthMultiplier: 1
  UseLengthMulCurve: 0
  LengthMulCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseGravityCurve: 0
  GravityCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.65
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  Gravity: {x: 0, y: 0, z: 0}
  UseWaving: 1
  CosinusAdd: 0
  FixedCycle: 0
  WavingSpeed: 2.2
  WavingRange: 0.5
  WavingAxis: {x: 1, y: 0.2, z: 0.2}
  WavingType: 0
  AlternateWave: 1
  UseWind: 0
  WindEffectPower: 1
  WindTurbulencePower: 1
  WindWorldNoisePower: 0.5
  WindEffect: {x: 0, y: 0, z: 0}
  fDebug: 0
  _gizmosEditorStartPreview: {fileID: 0}
  _gizmospesp: {fileID: 0}
  _gizmosEditorEndPreview: {fileID: 0}
  _gizmospeep: {fileID: 0}
  _editor_Title: Tail Animator 2
  _editor_IsInspectorViewingColliders: 1
  _editor_IsInspectorViewingIncludedColliders: 1
  _editor_animatorViewedCounter: 1
  TailSegments: []
  GhostParent:
    ProceduralPosition: {x: 0, y: 0, z: 0}
    ProceduralPositionWeightBlended: {x: 0, y: 0, z: 0}
    TrueTargetRotation: {x: 0, y: 0, z: 0, w: 1}
    PosRefRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosReferenceRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosition: {x: 0, y: 0, z: 0}
    BlendValue: 1
    BoneDimensionsScaled: {x: 0, y: 0, z: 0}
    BoneLengthScaled: 0
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalPositionInRoot: {x: 0, y: 0, z: 0}
    InitialLocalRotationInRoot: {x: 0, y: 0, z: 0, w: 1}
    LocalOffset: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: 0, y: 0, z: 0, w: 1}
    ColliderRadius: 1
    CollisionContactFlag: 0
    CollisionContactRelevancy: -1
    VelocityHelper: {x: 0, y: 0, z: 0}
    QVelocityHelper: {x: 0, y: 0, z: 0, w: 1}
    PreviousPush: {x: 0, y: 0, z: 0}
    Curving: {x: 0, y: 0, z: 0, w: 1}
    Gravity: {x: 0, y: 0, z: 0}
    GravityLookOffset: {x: 0, y: 0, z: 0}
    LengthMultiplier: 1
    PositionSpeed: 1
    RotationSpeed: 1
    Springiness: 0
    Slithery: 1
    Curling: 0.5
    Slippery: 1
    LastKeyframeLocalRotation: {x: 0, y: 0, z: 0, w: 0}
    LastKeyframeLocalPosition: {x: 0, y: 0, z: 0}
  GhostChild:
    ProceduralPosition: {x: 0, y: 0, z: 0}
    ProceduralPositionWeightBlended: {x: 0, y: 0, z: 0}
    TrueTargetRotation: {x: 0, y: 0, z: 0, w: 1}
    PosRefRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosReferenceRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosition: {x: 0, y: 0, z: 0}
    BlendValue: 1
    BoneDimensionsScaled: {x: 0, y: 0, z: 0}
    BoneLengthScaled: 0
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalPositionInRoot: {x: 0, y: 0, z: 0}
    InitialLocalRotationInRoot: {x: 0, y: 0, z: 0, w: 1}
    LocalOffset: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: 0, y: 0, z: 0, w: 1}
    ColliderRadius: 1
    CollisionContactFlag: 0
    CollisionContactRelevancy: -1
    VelocityHelper: {x: 0, y: 0, z: 0}
    QVelocityHelper: {x: 0, y: 0, z: 0, w: 1}
    PreviousPush: {x: 0, y: 0, z: 0}
    Curving: {x: 0, y: 0, z: 0, w: 1}
    Gravity: {x: 0, y: 0, z: 0}
    GravityLookOffset: {x: 0, y: 0, z: 0}
    LengthMultiplier: 1
    PositionSpeed: 1
    RotationSpeed: 1
    Springiness: 0
    Slithery: 1
    Curling: 0.5
    Slippery: 1
    LastKeyframeLocalRotation: {x: 0, y: 0, z: 0, w: 0}
    LastKeyframeLocalPosition: {x: 0, y: 0, z: 0}
  UseMaxDistance: 0
  DistanceFrom: {fileID: 0}
  _distanceFrom_Auto: {fileID: 0}
  MaximumDistance: 35
  MaxOutDistanceFactor: 0
  DistanceWithoutY: 0
  DistanceMeasurePoint: {x: 0, y: 0, z: 0}
  FadeDuration: 0.75
  Deflection: 0
  DeflectionStartAngle: 10
  DeflectionSmooth: 0
  DeflectionFalloff:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  DeflectOnlyCollisions: 1
  _Editor_Category: 0
  _Editor_FeaturesCategory: 0
  DrawGizmos: 1
  StartBone: {fileID: 4433906129938410}
  EndBone: {fileID: 0}
  EndBoneJointOffset: {x: 0, y: 0, z: 0}
  _TransformsGhostChain:
  - {fileID: 4433906129938410}
  - {fileID: 4173073848065700}
  - {fileID: 4029686301358206}
  - {fileID: 4499118396483252}
  - {fileID: 4128911775619874}
  - {fileID: 4397892140108248}
  - {fileID: 4649201082156680}
  - {fileID: 4747344628455634}
  - {fileID: 4623121953854166}
  _GhostChainInitCount: 9
  UpdateRate: 0
  AnimatePhysics: 0
  InterpolateRate: 0
  Prewarm: 0
  Slithery: 1.2
  Curling: 0.6
  Springiness: 0
  MaxStretching: 1
  AngleLimit: 181
  AngleLimitAxis: {x: 0, y: 0, z: 0}
  LimitAxisRange: {x: 0, y: 0}
  LimitSmoothing: 0.5
  MotionInfluence: 1
  MotionInfluenceInY: 1
  IncludeParent: 0
  UnifyBendiness: 0
  ReactionSpeed: 0.32
  Sustain: 0
  RotationRelevancy: 0.112
  SmoothingStyle: 0
  TimeScale: 1
  DeltaType: 4
  UpdateAsLast: 1
  DetectZeroKeyframes: 1
  StartAfterTPose: 1
  OptimizeWithMesh: {fileID: 0}
  OptimizeWithMeshes: []
  TailAnimatorAmount: 1
  DetachChildren: 0
  Axis2D: 0
  Tangle: 0
  AnimateRoll: 1
  OverrideKeyframeAnimation: 0
--- !u!1 &1107888954870694
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4362552420222478}
  - component: {fileID: 114162226758843942}
  m_Layer: 0
  m_Name: BMedusaSmallTentacle_3_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4362552420222478
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1107888954870694}
  serializedVersion: 2
  m_LocalRotation: {x: 4.1355242e-18, y: 0.8216741, z: 0.5699576, w: 1.0147121e-16}
  m_LocalPosition: {x: 0.04175864, y: -0.106956236, z: -0.10950682}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4710811522703924}
  m_Father: {fileID: 4791223082621518}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &114162226758843942
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1107888954870694}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 61574e354ba34a141ae2cc1228f5eda3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  UseCollision: 0
  CollisionSpace: 1
  CollisionMode: 0
  CheapCollision: 0
  DynamicWorldCollidersInclusion: 0
  InclusionRadius: 1
  IgnoreMeshColliders: 1
  IncludedColliders: []
  IncludedColliders2D: []
  CollidersType: 0
  CollideWithOtherTails: 0
  CollideWithDisabledColliders: 1
  CollisionSlippery: 1
  ReflectCollision: 0
  CollidersScaleCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  CollidersScaleMul: 6.5
  CollisionsAutoCurve: 0.5
  IgnoredColliders: []
  IgnoredColliders2D: []
  CollidersSameLayer: 1
  CollidersAddRigidbody: 1
  RigidbodyMass: 1
  CollidersLayer: 0
  UseSlitheryCurve: 0
  SlitheryCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.75
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseCurlingCurve: 0
  CurlingCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.3
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseSpringCurve: 0
  SpringCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseSlipperyCurve: 0
  SlipperyCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UsePosSpeedCurve: 0
  PosCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseRotSpeedCurve: 0
  RotCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.9
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UsePartialBlend: 0
  BlendCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.95
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.45
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseIK: 0
  IK:
    IKWeight: 0
    IKTargetPosition: {x: 0, y: 0, z: 0}
    IKTargetRotation: {x: 0, y: 0, z: 0, w: 0}
    LastLocalDirection: {x: 0, y: 0, z: 0}
    LocalDirection: {x: 0, y: 0, z: 0}
    IKBones: []
    ContinousSolving: 0
    SyncWithAnimator: 0
    ReactionQuality: 0
    Smoothing: 0
    StretchToTarget: 0
    StretchCurve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    Use2D: 0
    Invert: 0
  IKTarget: {fileID: 0}
  IKAutoWeights: 1
  IKBaseReactionWeight: 0.65
  IKReactionWeightCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.25
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  IKAutoAngleLimits: 1
  IKAutoAngleLimit: 40
  IKContinousSolve: 0
  IkInvertOrder: 0
  IKBlend: 1
  IKAnimatorBlend: 0.5
  IKReactionQuality: 2
  IKSmoothing: 0
  IKStretchToTarget: 0
  IKStretchCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  IKLimitSettings: []
  IKSelectiveChain: 0
  RotationOffset: {x: 0, y: 0, z: 0, w: 1}
  Curving: {x: 0, y: 0, z: 0, w: 1}
  UseCurvingCurve: 0
  CurvCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.75
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  LengthMultiplier: 1
  UseLengthMulCurve: 0
  LengthMulCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseGravityCurve: 0
  GravityCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.65
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  Gravity: {x: 0, y: 0, z: 0}
  UseWaving: 1
  CosinusAdd: 1
  FixedCycle: 0
  WavingSpeed: 2.2
  WavingRange: 0.5
  WavingAxis: {x: 1, y: 0.2, z: 0.2}
  WavingType: 0
  AlternateWave: 1
  UseWind: 0
  WindEffectPower: 1
  WindTurbulencePower: 1
  WindWorldNoisePower: 0.5
  WindEffect: {x: 0, y: 0, z: 0}
  fDebug: 0
  _gizmosEditorStartPreview: {fileID: 0}
  _gizmospesp: {fileID: 0}
  _gizmosEditorEndPreview: {fileID: 0}
  _gizmospeep: {fileID: 0}
  _editor_Title: Tail Animator 2
  _editor_IsInspectorViewingColliders: 1
  _editor_IsInspectorViewingIncludedColliders: 1
  _editor_animatorViewedCounter: 1
  TailSegments: []
  GhostParent:
    ProceduralPosition: {x: 0, y: 0, z: 0}
    ProceduralPositionWeightBlended: {x: 0, y: 0, z: 0}
    TrueTargetRotation: {x: 0, y: 0, z: 0, w: 1}
    PosRefRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosReferenceRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosition: {x: 0, y: 0, z: 0}
    BlendValue: 1
    BoneDimensionsScaled: {x: 0, y: 0, z: 0}
    BoneLengthScaled: 0
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalPositionInRoot: {x: 0, y: 0, z: 0}
    InitialLocalRotationInRoot: {x: 0, y: 0, z: 0, w: 1}
    LocalOffset: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: 0, y: 0, z: 0, w: 1}
    ColliderRadius: 1
    CollisionContactFlag: 0
    CollisionContactRelevancy: -1
    VelocityHelper: {x: 0, y: 0, z: 0}
    QVelocityHelper: {x: 0, y: 0, z: 0, w: 1}
    PreviousPush: {x: 0, y: 0, z: 0}
    Curving: {x: 0, y: 0, z: 0, w: 1}
    Gravity: {x: 0, y: 0, z: 0}
    GravityLookOffset: {x: 0, y: 0, z: 0}
    LengthMultiplier: 1
    PositionSpeed: 1
    RotationSpeed: 1
    Springiness: 0
    Slithery: 1
    Curling: 0.5
    Slippery: 1
    LastKeyframeLocalRotation: {x: 0, y: 0, z: 0, w: 0}
    LastKeyframeLocalPosition: {x: 0, y: 0, z: 0}
  GhostChild:
    ProceduralPosition: {x: 0, y: 0, z: 0}
    ProceduralPositionWeightBlended: {x: 0, y: 0, z: 0}
    TrueTargetRotation: {x: 0, y: 0, z: 0, w: 1}
    PosRefRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosReferenceRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosition: {x: 0, y: 0, z: 0}
    BlendValue: 1
    BoneDimensionsScaled: {x: 0, y: 0, z: 0}
    BoneLengthScaled: 0
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalPositionInRoot: {x: 0, y: 0, z: 0}
    InitialLocalRotationInRoot: {x: 0, y: 0, z: 0, w: 1}
    LocalOffset: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: 0, y: 0, z: 0, w: 1}
    ColliderRadius: 1
    CollisionContactFlag: 0
    CollisionContactRelevancy: -1
    VelocityHelper: {x: 0, y: 0, z: 0}
    QVelocityHelper: {x: 0, y: 0, z: 0, w: 1}
    PreviousPush: {x: 0, y: 0, z: 0}
    Curving: {x: 0, y: 0, z: 0, w: 1}
    Gravity: {x: 0, y: 0, z: 0}
    GravityLookOffset: {x: 0, y: 0, z: 0}
    LengthMultiplier: 1
    PositionSpeed: 1
    RotationSpeed: 1
    Springiness: 0
    Slithery: 1
    Curling: 0.5
    Slippery: 1
    LastKeyframeLocalRotation: {x: 0, y: 0, z: 0, w: 0}
    LastKeyframeLocalPosition: {x: 0, y: 0, z: 0}
  UseMaxDistance: 0
  DistanceFrom: {fileID: 0}
  _distanceFrom_Auto: {fileID: 0}
  MaximumDistance: 35
  MaxOutDistanceFactor: 0
  DistanceWithoutY: 0
  DistanceMeasurePoint: {x: 0, y: 0, z: 0}
  FadeDuration: 0.75
  Deflection: 0
  DeflectionStartAngle: 10
  DeflectionSmooth: 0
  DeflectionFalloff:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  DeflectOnlyCollisions: 1
  _Editor_Category: 0
  _Editor_FeaturesCategory: 0
  DrawGizmos: 1
  StartBone: {fileID: 4362552420222478}
  EndBone: {fileID: 0}
  EndBoneJointOffset: {x: 0, y: 0, z: 0}
  _TransformsGhostChain:
  - {fileID: 4362552420222478}
  - {fileID: 4710811522703924}
  - {fileID: 4553558501434468}
  - {fileID: 4625922807059234}
  - {fileID: 4705594881460506}
  - {fileID: 4916249086574084}
  - {fileID: 4765463346945718}
  _GhostChainInitCount: 7
  UpdateRate: 0
  AnimatePhysics: 0
  InterpolateRate: 0
  Prewarm: 0
  Slithery: 1.2
  Curling: 0.6
  Springiness: 0
  MaxStretching: 1
  AngleLimit: 181
  AngleLimitAxis: {x: 0, y: 0, z: 0}
  LimitAxisRange: {x: 0, y: 0}
  LimitSmoothing: 0.5
  MotionInfluence: 1
  MotionInfluenceInY: 1
  IncludeParent: 0
  UnifyBendiness: 0
  ReactionSpeed: 0.32
  Sustain: 0
  RotationRelevancy: 0.112
  SmoothingStyle: 0
  TimeScale: 1
  DeltaType: 4
  UpdateAsLast: 1
  DetectZeroKeyframes: 1
  StartAfterTPose: 1
  OptimizeWithMesh: {fileID: 0}
  OptimizeWithMeshes: []
  TailAnimatorAmount: 1
  DetachChildren: 0
  Axis2D: 0
  Tangle: 0
  AnimateRoll: 1
  OverrideKeyframeAnimation: 0
--- !u!1 &1116159454858732
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4256239339913762}
  m_Layer: 0
  m_Name: BMedusaHead_2_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4256239339913762
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1116159454858732}
  serializedVersion: 2
  m_LocalRotation: {x: -1.9428903e-16, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 9.9920076e-17, z: -0.22696303}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4948753678086262}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1142552543224466
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4710033502307542}
  m_Layer: 0
  m_Name: BMedusaSmallTentacle_6_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4710033502307542
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1142552543224466}
  serializedVersion: 2
  m_LocalRotation: {x: 0.007298687, y: -6.0848308e-33, z: 4.4412458e-35, w: 0.99997336}
  m_LocalPosition: {x: -0, y: 2.220446e-16, z: -0.15702073}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4488331731804064}
  m_Father: {fileID: 4115987048860412}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1152474975592548
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4465872659551170}
  m_Layer: 0
  m_Name: BMedusaBigTentacle_7_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4465872659551170
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1152474975592548}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0038167106, y: 5.693271e-11, z: 2.1729725e-13, w: 0.9999927}
  m_LocalPosition: {x: -5.551115e-15, y: -8.881784e-17, z: -0.26763394}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4390319561557988}
  m_Father: {fileID: 4119037796251514}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1177381134001104
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4539490278012388}
  - component: {fileID: 114003112911359528}
  m_Layer: 0
  m_Name: BMedusaSmallTentacle
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4539490278012388
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1177381134001104}
  serializedVersion: 2
  m_LocalRotation: {x: -0.59342265, y: 0.60426295, z: 0.3672414, w: 0.38451216}
  m_LocalPosition: {x: 0.08847722, y: -0.13280639, z: -0.09045798}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4404810430758760}
  m_Father: {fileID: 4967518121876084}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &114003112911359528
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1177381134001104}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 61574e354ba34a141ae2cc1228f5eda3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  UseCollision: 0
  CollisionSpace: 1
  CollisionMode: 0
  CheapCollision: 0
  DynamicWorldCollidersInclusion: 0
  InclusionRadius: 1
  IgnoreMeshColliders: 1
  IncludedColliders: []
  IncludedColliders2D: []
  CollidersType: 0
  CollideWithOtherTails: 0
  CollideWithDisabledColliders: 1
  CollisionSlippery: 1
  ReflectCollision: 0
  CollidersScaleCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  CollidersScaleMul: 6.5
  CollisionsAutoCurve: 0.5
  IgnoredColliders: []
  IgnoredColliders2D: []
  CollidersSameLayer: 1
  CollidersAddRigidbody: 1
  RigidbodyMass: 1
  CollidersLayer: 0
  UseSlitheryCurve: 0
  SlitheryCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.75
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseCurlingCurve: 0
  CurlingCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.3
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseSpringCurve: 0
  SpringCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseSlipperyCurve: 0
  SlipperyCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UsePosSpeedCurve: 0
  PosCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseRotSpeedCurve: 0
  RotCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.9
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UsePartialBlend: 0
  BlendCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.95
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.45
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseIK: 0
  IK:
    IKWeight: 0
    IKTargetPosition: {x: 0, y: 0, z: 0}
    IKTargetRotation: {x: 0, y: 0, z: 0, w: 0}
    LastLocalDirection: {x: 0, y: 0, z: 0}
    LocalDirection: {x: 0, y: 0, z: 0}
    IKBones: []
    ContinousSolving: 0
    SyncWithAnimator: 0
    ReactionQuality: 0
    Smoothing: 0
    StretchToTarget: 0
    StretchCurve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    Use2D: 0
    Invert: 0
  IKTarget: {fileID: 0}
  IKAutoWeights: 1
  IKBaseReactionWeight: 0.65
  IKReactionWeightCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.25
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  IKAutoAngleLimits: 1
  IKAutoAngleLimit: 40
  IKContinousSolve: 0
  IkInvertOrder: 0
  IKBlend: 1
  IKAnimatorBlend: 0.5
  IKReactionQuality: 2
  IKSmoothing: 0
  IKStretchToTarget: 0
  IKStretchCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  IKLimitSettings: []
  IKSelectiveChain: 0
  RotationOffset: {x: 0, y: 0, z: 0, w: 1}
  Curving: {x: 0, y: 0, z: 0, w: 1}
  UseCurvingCurve: 0
  CurvCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.75
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  LengthMultiplier: 1
  UseLengthMulCurve: 0
  LengthMulCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseGravityCurve: 0
  GravityCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.65
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  Gravity: {x: 0, y: 0, z: 0}
  UseWaving: 1
  CosinusAdd: 1
  FixedCycle: 0
  WavingSpeed: 2.2
  WavingRange: 0.5
  WavingAxis: {x: 1, y: 0.2, z: 0.2}
  WavingType: 0
  AlternateWave: 1
  UseWind: 0
  WindEffectPower: 1
  WindTurbulencePower: 1
  WindWorldNoisePower: 0.5
  WindEffect: {x: 0, y: 0, z: 0}
  fDebug: 0
  _gizmosEditorStartPreview: {fileID: 0}
  _gizmospesp: {fileID: 0}
  _gizmosEditorEndPreview: {fileID: 0}
  _gizmospeep: {fileID: 0}
  _editor_Title: Tail Animator 2
  _editor_IsInspectorViewingColliders: 1
  _editor_IsInspectorViewingIncludedColliders: 1
  _editor_animatorViewedCounter: 1
  TailSegments: []
  GhostParent:
    ProceduralPosition: {x: 0, y: 0, z: 0}
    ProceduralPositionWeightBlended: {x: 0, y: 0, z: 0}
    TrueTargetRotation: {x: 0, y: 0, z: 0, w: 1}
    PosRefRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosReferenceRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosition: {x: 0, y: 0, z: 0}
    BlendValue: 1
    BoneDimensionsScaled: {x: 0, y: 0, z: 0}
    BoneLengthScaled: 0
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalPositionInRoot: {x: 0, y: 0, z: 0}
    InitialLocalRotationInRoot: {x: 0, y: 0, z: 0, w: 1}
    LocalOffset: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: 0, y: 0, z: 0, w: 1}
    ColliderRadius: 1
    CollisionContactFlag: 0
    CollisionContactRelevancy: -1
    VelocityHelper: {x: 0, y: 0, z: 0}
    QVelocityHelper: {x: 0, y: 0, z: 0, w: 1}
    PreviousPush: {x: 0, y: 0, z: 0}
    Curving: {x: 0, y: 0, z: 0, w: 1}
    Gravity: {x: 0, y: 0, z: 0}
    GravityLookOffset: {x: 0, y: 0, z: 0}
    LengthMultiplier: 1
    PositionSpeed: 1
    RotationSpeed: 1
    Springiness: 0
    Slithery: 1
    Curling: 0.5
    Slippery: 1
    LastKeyframeLocalRotation: {x: 0, y: 0, z: 0, w: 0}
    LastKeyframeLocalPosition: {x: 0, y: 0, z: 0}
  GhostChild:
    ProceduralPosition: {x: 0, y: 0, z: 0}
    ProceduralPositionWeightBlended: {x: 0, y: 0, z: 0}
    TrueTargetRotation: {x: 0, y: 0, z: 0, w: 1}
    PosRefRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosReferenceRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosition: {x: 0, y: 0, z: 0}
    BlendValue: 1
    BoneDimensionsScaled: {x: 0, y: 0, z: 0}
    BoneLengthScaled: 0
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalPositionInRoot: {x: 0, y: 0, z: 0}
    InitialLocalRotationInRoot: {x: 0, y: 0, z: 0, w: 1}
    LocalOffset: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: 0, y: 0, z: 0, w: 1}
    ColliderRadius: 1
    CollisionContactFlag: 0
    CollisionContactRelevancy: -1
    VelocityHelper: {x: 0, y: 0, z: 0}
    QVelocityHelper: {x: 0, y: 0, z: 0, w: 1}
    PreviousPush: {x: 0, y: 0, z: 0}
    Curving: {x: 0, y: 0, z: 0, w: 1}
    Gravity: {x: 0, y: 0, z: 0}
    GravityLookOffset: {x: 0, y: 0, z: 0}
    LengthMultiplier: 1
    PositionSpeed: 1
    RotationSpeed: 1
    Springiness: 0
    Slithery: 1
    Curling: 0.5
    Slippery: 1
    LastKeyframeLocalRotation: {x: 0, y: 0, z: 0, w: 0}
    LastKeyframeLocalPosition: {x: 0, y: 0, z: 0}
  UseMaxDistance: 0
  DistanceFrom: {fileID: 0}
  _distanceFrom_Auto: {fileID: 0}
  MaximumDistance: 35
  MaxOutDistanceFactor: 0
  DistanceWithoutY: 0
  DistanceMeasurePoint: {x: 0, y: 0, z: 0}
  FadeDuration: 0.75
  Deflection: 0
  DeflectionStartAngle: 10
  DeflectionSmooth: 0
  DeflectionFalloff:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  DeflectOnlyCollisions: 1
  _Editor_Category: 0
  _Editor_FeaturesCategory: 0
  DrawGizmos: 1
  StartBone: {fileID: 4539490278012388}
  EndBone: {fileID: 0}
  EndBoneJointOffset: {x: 0, y: 0, z: 0}
  _TransformsGhostChain:
  - {fileID: 4539490278012388}
  - {fileID: 4404810430758760}
  - {fileID: 4568984149630552}
  - {fileID: 4932931450908010}
  - {fileID: 4947156887212692}
  - {fileID: 4281372711745850}
  - {fileID: 4771379987283930}
  _GhostChainInitCount: 7
  UpdateRate: 0
  AnimatePhysics: 0
  InterpolateRate: 0
  Prewarm: 0
  Slithery: 1.2
  Curling: 0.6
  Springiness: 0
  MaxStretching: 1
  AngleLimit: 181
  AngleLimitAxis: {x: 0, y: 0, z: 0}
  LimitAxisRange: {x: 0, y: 0}
  LimitSmoothing: 0.5
  MotionInfluence: 1
  MotionInfluenceInY: 1
  IncludeParent: 0
  UnifyBendiness: 0
  ReactionSpeed: 0.32
  Sustain: 0
  RotationRelevancy: 0.112
  SmoothingStyle: 0
  TimeScale: 1
  DeltaType: 4
  UpdateAsLast: 1
  DetectZeroKeyframes: 1
  StartAfterTPose: 1
  OptimizeWithMesh: {fileID: 0}
  OptimizeWithMeshes: []
  TailAnimatorAmount: 1
  DetachChildren: 0
  Axis2D: 0
  Tangle: 0
  AnimateRoll: 1
  OverrideKeyframeAnimation: 0
--- !u!1 &1177736009973772
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4893238969152924}
  m_Layer: 0
  m_Name: BMedusaSmallTentacle_7_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4893238969152924
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1177736009973772}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: -0.17491166}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4794533554389354}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1180968146452896
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4369423742016470}
  - component: {fileID: 114763916633808708}
  m_Layer: 0
  m_Name: BMedusaBigTentacle_1_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4369423742016470
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1180968146452896}
  serializedVersion: 2
  m_LocalRotation: {x: 3.0846754e-18, y: 0.8157288, z: 0.5784346, w: 1.01508594e-16}
  m_LocalPosition: {x: -0.19893794, y: -0.14224112, z: -0.19355847}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4275359106047306}
  m_Father: {fileID: 4791223082621518}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &114763916633808708
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1180968146452896}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 61574e354ba34a141ae2cc1228f5eda3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  UseCollision: 0
  CollisionSpace: 1
  CollisionMode: 0
  CheapCollision: 0
  DynamicWorldCollidersInclusion: 0
  InclusionRadius: 1
  IgnoreMeshColliders: 1
  IncludedColliders: []
  IncludedColliders2D: []
  CollidersType: 0
  CollideWithOtherTails: 0
  CollideWithDisabledColliders: 1
  CollisionSlippery: 1
  ReflectCollision: 0
  CollidersScaleCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  CollidersScaleMul: 6.5
  CollisionsAutoCurve: 0.5
  IgnoredColliders: []
  IgnoredColliders2D: []
  CollidersSameLayer: 1
  CollidersAddRigidbody: 1
  RigidbodyMass: 1
  CollidersLayer: 0
  UseSlitheryCurve: 0
  SlitheryCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.75
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseCurlingCurve: 0
  CurlingCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.3
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseSpringCurve: 0
  SpringCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseSlipperyCurve: 0
  SlipperyCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UsePosSpeedCurve: 0
  PosCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseRotSpeedCurve: 0
  RotCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.9
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UsePartialBlend: 0
  BlendCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.95
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.45
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseIK: 0
  IK:
    IKWeight: 0
    IKTargetPosition: {x: 0, y: 0, z: 0}
    IKTargetRotation: {x: 0, y: 0, z: 0, w: 0}
    LastLocalDirection: {x: 0, y: 0, z: 0}
    LocalDirection: {x: 0, y: 0, z: 0}
    IKBones: []
    ContinousSolving: 0
    SyncWithAnimator: 0
    ReactionQuality: 0
    Smoothing: 0
    StretchToTarget: 0
    StretchCurve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    Use2D: 0
    Invert: 0
  IKTarget: {fileID: 0}
  IKAutoWeights: 1
  IKBaseReactionWeight: 0.65
  IKReactionWeightCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.25
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  IKAutoAngleLimits: 1
  IKAutoAngleLimit: 40
  IKContinousSolve: 0
  IkInvertOrder: 0
  IKBlend: 1
  IKAnimatorBlend: 0.5
  IKReactionQuality: 2
  IKSmoothing: 0
  IKStretchToTarget: 0
  IKStretchCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  IKLimitSettings: []
  IKSelectiveChain: 0
  RotationOffset: {x: 0, y: 0, z: 0, w: 1}
  Curving: {x: 0, y: 0, z: 0, w: 1}
  UseCurvingCurve: 0
  CurvCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.75
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  LengthMultiplier: 1
  UseLengthMulCurve: 0
  LengthMulCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseGravityCurve: 0
  GravityCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.65
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  Gravity: {x: 0, y: 0, z: 0}
  UseWaving: 1
  CosinusAdd: 1
  FixedCycle: 0
  WavingSpeed: 2.2
  WavingRange: 0.5
  WavingAxis: {x: 1, y: 0.2, z: 0.2}
  WavingType: 0
  AlternateWave: 1
  UseWind: 0
  WindEffectPower: 1
  WindTurbulencePower: 1
  WindWorldNoisePower: 0.5
  WindEffect: {x: 0, y: 0, z: 0}
  fDebug: 0
  _gizmosEditorStartPreview: {fileID: 0}
  _gizmospesp: {fileID: 0}
  _gizmosEditorEndPreview: {fileID: 0}
  _gizmospeep: {fileID: 0}
  _editor_Title: Tail Animator 2
  _editor_IsInspectorViewingColliders: 0
  _editor_IsInspectorViewingIncludedColliders: 0
  _editor_animatorViewedCounter: 1
  TailSegments: []
  GhostParent:
    ProceduralPosition: {x: 0, y: 0, z: 0}
    ProceduralPositionWeightBlended: {x: 0, y: 0, z: 0}
    TrueTargetRotation: {x: 0, y: 0, z: 0, w: 1}
    PosRefRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosReferenceRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosition: {x: 0, y: 0, z: 0}
    BlendValue: 1
    BoneDimensionsScaled: {x: 0, y: 0, z: 0}
    BoneLengthScaled: 0
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalPositionInRoot: {x: 0, y: 0, z: 0}
    InitialLocalRotationInRoot: {x: 0, y: 0, z: 0, w: 1}
    LocalOffset: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: 0, y: 0, z: 0, w: 1}
    ColliderRadius: 1
    CollisionContactFlag: 0
    CollisionContactRelevancy: -1
    VelocityHelper: {x: 0, y: 0, z: 0}
    QVelocityHelper: {x: 0, y: 0, z: 0, w: 1}
    PreviousPush: {x: 0, y: 0, z: 0}
    Curving: {x: 0, y: 0, z: 0, w: 1}
    Gravity: {x: 0, y: 0, z: 0}
    GravityLookOffset: {x: 0, y: 0, z: 0}
    LengthMultiplier: 1
    PositionSpeed: 1
    RotationSpeed: 1
    Springiness: 0
    Slithery: 1
    Curling: 0.5
    Slippery: 1
    LastKeyframeLocalRotation: {x: 0, y: 0, z: 0, w: 0}
    LastKeyframeLocalPosition: {x: 0, y: 0, z: 0}
  GhostChild:
    ProceduralPosition: {x: 0, y: 0, z: 0}
    ProceduralPositionWeightBlended: {x: 0, y: 0, z: 0}
    TrueTargetRotation: {x: 0, y: 0, z: 0, w: 1}
    PosRefRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosReferenceRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosition: {x: 0, y: 0, z: 0}
    BlendValue: 1
    BoneDimensionsScaled: {x: 0, y: 0, z: 0}
    BoneLengthScaled: 0
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalPositionInRoot: {x: 0, y: 0, z: 0}
    InitialLocalRotationInRoot: {x: 0, y: 0, z: 0, w: 1}
    LocalOffset: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: 0, y: 0, z: 0, w: 1}
    ColliderRadius: 1
    CollisionContactFlag: 0
    CollisionContactRelevancy: -1
    VelocityHelper: {x: 0, y: 0, z: 0}
    QVelocityHelper: {x: 0, y: 0, z: 0, w: 1}
    PreviousPush: {x: 0, y: 0, z: 0}
    Curving: {x: 0, y: 0, z: 0, w: 1}
    Gravity: {x: 0, y: 0, z: 0}
    GravityLookOffset: {x: 0, y: 0, z: 0}
    LengthMultiplier: 1
    PositionSpeed: 1
    RotationSpeed: 1
    Springiness: 0
    Slithery: 1
    Curling: 0.5
    Slippery: 1
    LastKeyframeLocalRotation: {x: 0, y: 0, z: 0, w: 0}
    LastKeyframeLocalPosition: {x: 0, y: 0, z: 0}
  UseMaxDistance: 0
  DistanceFrom: {fileID: 0}
  _distanceFrom_Auto: {fileID: 0}
  MaximumDistance: 35
  MaxOutDistanceFactor: 0
  DistanceWithoutY: 0
  DistanceMeasurePoint: {x: 0, y: 0, z: 0}
  FadeDuration: 0.75
  Deflection: 0
  DeflectionStartAngle: 10
  DeflectionSmooth: 0
  DeflectionFalloff:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  DeflectOnlyCollisions: 1
  _Editor_Category: 0
  _Editor_FeaturesCategory: 0
  DrawGizmos: 1
  StartBone: {fileID: 4369423742016470}
  EndBone: {fileID: 0}
  EndBoneJointOffset: {x: 0, y: 0, z: 0}
  _TransformsGhostChain:
  - {fileID: 4369423742016470}
  - {fileID: 4275359106047306}
  - {fileID: 4598062211917050}
  - {fileID: 4770854467462028}
  - {fileID: 4665063114609876}
  - {fileID: 4253242480666078}
  - {fileID: 4119037796251514}
  - {fileID: 4465872659551170}
  - {fileID: 4390319561557988}
  _GhostChainInitCount: 9
  UpdateRate: 0
  AnimatePhysics: 0
  InterpolateRate: 0
  Prewarm: 0
  Slithery: 1.2
  Curling: 0.6
  Springiness: 0
  MaxStretching: 1
  AngleLimit: 181
  AngleLimitAxis: {x: 0, y: 0, z: 0}
  LimitAxisRange: {x: 0, y: 0}
  LimitSmoothing: 0.5
  MotionInfluence: 1
  MotionInfluenceInY: 1
  IncludeParent: 0
  UnifyBendiness: 0
  ReactionSpeed: 0.32
  Sustain: 0
  RotationRelevancy: 0.112
  SmoothingStyle: 0
  TimeScale: 1
  DeltaType: 4
  UpdateAsLast: 1
  DetectZeroKeyframes: 1
  StartAfterTPose: 1
  OptimizeWithMesh: {fileID: 0}
  OptimizeWithMeshes: []
  TailAnimatorAmount: 1
  DetachChildren: 0
  Axis2D: 0
  Tangle: 0
  AnimateRoll: 1
  OverrideKeyframeAnimation: 0
--- !u!1 &1182300189763478
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4550558008891874}
  m_Layer: 0
  m_Name: BMedusaBigTentacle_1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4550558008891874
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1182300189763478}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00022835647, y: -1.7013883e-12, z: 0.000000007450581, w: 1}
  m_LocalPosition: {x: -4.440892e-17, y: 1.7763569e-16, z: -0.21457219}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4468834799086354}
  m_Father: {fileID: 4852971330691566}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1183273993097082
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4008515803486860}
  m_Layer: 0
  m_Name: BMedusaBigTentacle_3_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4008515803486860
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1183273993097082}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0155279, y: -2.3149002e-10, z: 3.5949872e-12, w: 0.9998795}
  m_LocalPosition: {x: -4.440892e-17, y: 8.881784e-17, z: -0.19405498}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4804820510424768}
  m_Father: {fileID: 4724247564744192}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1184367077515744
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4496943030920412}
  - component: {fileID: 137284067982799548}
  m_Layer: 0
  m_Name: FedusaMesh
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4496943030920412
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1184367077515744}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4262243184209816}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137284067982799548
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1184367077515744}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4967518121876084}
  - {fileID: 4446627823584482}
  - {fileID: 4093775943049514}
  - {fileID: 4544918222211998}
  - {fileID: 4123346571995812}
  - {fileID: 4502739371238246}
  - {fileID: 4791223082621518}
  - {fileID: 4711285722647462}
  - {fileID: 4585172350752336}
  - {fileID: 4114351381275056}
  - {fileID: 4948753678086262}
  - {fileID: 4256239339913762}
  - {fileID: 4362552420222478}
  - {fileID: 4710811522703924}
  - {fileID: 4553558501434468}
  - {fileID: 4625922807059234}
  - {fileID: 4705594881460506}
  - {fileID: 4916249086574084}
  - {fileID: 4765463346945718}
  - {fileID: 4022440963597994}
  - {fileID: 4825973450039334}
  - {fileID: 4650322623395200}
  - {fileID: 4161613562273456}
  - {fileID: 4115987048860412}
  - {fileID: 4710033502307542}
  - {fileID: 4488331731804064}
  - {fileID: 4539490278012388}
  - {fileID: 4404810430758760}
  - {fileID: 4568984149630552}
  - {fileID: 4932931450908010}
  - {fileID: 4947156887212692}
  - {fileID: 4281372711745850}
  - {fileID: 4771379987283930}
  - {fileID: 4348709109149050}
  - {fileID: 4858884341418190}
  - {fileID: 4618795026672700}
  - {fileID: 4682986588889634}
  - {fileID: 4023926564130414}
  - {fileID: 4794533554389354}
  - {fileID: 4893238969152924}
  - {fileID: 4369423742016470}
  - {fileID: 4275359106047306}
  - {fileID: 4598062211917050}
  - {fileID: 4770854467462028}
  - {fileID: 4665063114609876}
  - {fileID: 4253242480666078}
  - {fileID: 4119037796251514}
  - {fileID: 4465872659551170}
  - {fileID: 4390319561557988}
  - {fileID: 4433906129938410}
  - {fileID: 4173073848065700}
  - {fileID: 4029686301358206}
  - {fileID: 4499118396483252}
  - {fileID: 4128911775619874}
  - {fileID: 4397892140108248}
  - {fileID: 4649201082156680}
  - {fileID: 4747344628455634}
  - {fileID: 4623121953854166}
  - {fileID: 4852971330691566}
  - {fileID: 4550558008891874}
  - {fileID: 4468834799086354}
  - {fileID: 4627020987916408}
  - {fileID: 4422272615871404}
  - {fileID: 4858749781824216}
  - {fileID: 4223421973644560}
  - {fileID: 4133831262160342}
  - {fileID: 4267755260373218}
  - {fileID: 4245220811890572}
  - {fileID: 4093497776261874}
  - {fileID: 4724247564744192}
  - {fileID: 4008515803486860}
  - {fileID: 4804820510424768}
  - {fileID: 4368197746673206}
  - {fileID: 4023000906293916}
  - {fileID: 4802801646766158}
  - {fileID: 4446104239766824}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4114351381275056}
  m_AABB:
    m_Center: {x: 0.020630747, y: -0.70081604, z: 0.16922122}
    m_Extent: {x: 0.57520497, y: 1.1958946, z: 0.6383083}
  m_DirtyAABB: 0
--- !u!1 &1191544191228950
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4348709109149050}
  - component: {fileID: 114414538138042154}
  m_Layer: 0
  m_Name: BMedusaSmallTentacle_1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4348709109149050
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1191544191228950}
  serializedVersion: 2
  m_LocalRotation: {x: -0.76276934, y: 0, z: 0, w: 0.64667064}
  m_LocalPosition: {x: 0.12676731, y: -0.08682271, z: -0.10090851}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4858884341418190}
  m_Father: {fileID: 4114351381275056}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &114414538138042154
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1191544191228950}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 61574e354ba34a141ae2cc1228f5eda3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  UseCollision: 0
  CollisionSpace: 1
  CollisionMode: 0
  CheapCollision: 0
  DynamicWorldCollidersInclusion: 0
  InclusionRadius: 1
  IgnoreMeshColliders: 1
  IncludedColliders: []
  IncludedColliders2D: []
  CollidersType: 0
  CollideWithOtherTails: 0
  CollideWithDisabledColliders: 1
  CollisionSlippery: 1
  ReflectCollision: 0
  CollidersScaleCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  CollidersScaleMul: 6.5
  CollisionsAutoCurve: 0.5
  IgnoredColliders: []
  IgnoredColliders2D: []
  CollidersSameLayer: 1
  CollidersAddRigidbody: 1
  RigidbodyMass: 1
  CollidersLayer: 0
  UseSlitheryCurve: 0
  SlitheryCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.75
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseCurlingCurve: 0
  CurlingCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.3
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseSpringCurve: 0
  SpringCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseSlipperyCurve: 0
  SlipperyCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UsePosSpeedCurve: 0
  PosCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseRotSpeedCurve: 0
  RotCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.9
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UsePartialBlend: 0
  BlendCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.95
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.45
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseIK: 0
  IK:
    IKWeight: 0
    IKTargetPosition: {x: 0, y: 0, z: 0}
    IKTargetRotation: {x: 0, y: 0, z: 0, w: 0}
    LastLocalDirection: {x: 0, y: 0, z: 0}
    LocalDirection: {x: 0, y: 0, z: 0}
    IKBones: []
    ContinousSolving: 0
    SyncWithAnimator: 0
    ReactionQuality: 0
    Smoothing: 0
    StretchToTarget: 0
    StretchCurve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    Use2D: 0
    Invert: 0
  IKTarget: {fileID: 0}
  IKAutoWeights: 1
  IKBaseReactionWeight: 0.65
  IKReactionWeightCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.25
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  IKAutoAngleLimits: 1
  IKAutoAngleLimit: 40
  IKContinousSolve: 0
  IkInvertOrder: 0
  IKBlend: 1
  IKAnimatorBlend: 0.5
  IKReactionQuality: 2
  IKSmoothing: 0
  IKStretchToTarget: 0
  IKStretchCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  IKLimitSettings: []
  IKSelectiveChain: 0
  RotationOffset: {x: 0, y: 0, z: 0, w: 1}
  Curving: {x: 0, y: 0, z: 0, w: 1}
  UseCurvingCurve: 0
  CurvCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.75
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  LengthMultiplier: 1
  UseLengthMulCurve: 0
  LengthMulCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseGravityCurve: 0
  GravityCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.65
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  Gravity: {x: 0, y: 0, z: 0}
  UseWaving: 1
  CosinusAdd: 1
  FixedCycle: 0
  WavingSpeed: 2.2
  WavingRange: 0.5
  WavingAxis: {x: 1, y: 0.2, z: 0.2}
  WavingType: 0
  AlternateWave: 1
  UseWind: 0
  WindEffectPower: 1
  WindTurbulencePower: 1
  WindWorldNoisePower: 0.5
  WindEffect: {x: 0, y: 0, z: 0}
  fDebug: 0
  _gizmosEditorStartPreview: {fileID: 0}
  _gizmospesp: {fileID: 0}
  _gizmosEditorEndPreview: {fileID: 0}
  _gizmospeep: {fileID: 0}
  _editor_Title: Tail Animator 2
  _editor_IsInspectorViewingColliders: 0
  _editor_IsInspectorViewingIncludedColliders: 0
  _editor_animatorViewedCounter: 0
  TailSegments: []
  GhostParent:
    ProceduralPosition: {x: 0, y: 0, z: 0}
    ProceduralPositionWeightBlended: {x: 0, y: 0, z: 0}
    TrueTargetRotation: {x: 0, y: 0, z: 0, w: 1}
    PosRefRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosReferenceRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosition: {x: 0, y: 0, z: 0}
    BlendValue: 1
    BoneDimensionsScaled: {x: 0, y: 0, z: 0}
    BoneLengthScaled: 0
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalPositionInRoot: {x: 0, y: 0, z: 0}
    InitialLocalRotationInRoot: {x: 0, y: 0, z: 0, w: 1}
    LocalOffset: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: 0, y: 0, z: 0, w: 1}
    ColliderRadius: 1
    CollisionContactFlag: 0
    CollisionContactRelevancy: -1
    VelocityHelper: {x: 0, y: 0, z: 0}
    QVelocityHelper: {x: 0, y: 0, z: 0, w: 1}
    PreviousPush: {x: 0, y: 0, z: 0}
    Curving: {x: 0, y: 0, z: 0, w: 1}
    Gravity: {x: 0, y: 0, z: 0}
    GravityLookOffset: {x: 0, y: 0, z: 0}
    LengthMultiplier: 1
    PositionSpeed: 1
    RotationSpeed: 1
    Springiness: 0
    Slithery: 1
    Curling: 0.5
    Slippery: 1
    LastKeyframeLocalRotation: {x: 0, y: 0, z: 0, w: 0}
    LastKeyframeLocalPosition: {x: 0, y: 0, z: 0}
  GhostChild:
    ProceduralPosition: {x: 0, y: 0, z: 0}
    ProceduralPositionWeightBlended: {x: 0, y: 0, z: 0}
    TrueTargetRotation: {x: 0, y: 0, z: 0, w: 1}
    PosRefRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosReferenceRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosition: {x: 0, y: 0, z: 0}
    BlendValue: 1
    BoneDimensionsScaled: {x: 0, y: 0, z: 0}
    BoneLengthScaled: 0
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalPositionInRoot: {x: 0, y: 0, z: 0}
    InitialLocalRotationInRoot: {x: 0, y: 0, z: 0, w: 1}
    LocalOffset: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: 0, y: 0, z: 0, w: 1}
    ColliderRadius: 1
    CollisionContactFlag: 0
    CollisionContactRelevancy: -1
    VelocityHelper: {x: 0, y: 0, z: 0}
    QVelocityHelper: {x: 0, y: 0, z: 0, w: 1}
    PreviousPush: {x: 0, y: 0, z: 0}
    Curving: {x: 0, y: 0, z: 0, w: 1}
    Gravity: {x: 0, y: 0, z: 0}
    GravityLookOffset: {x: 0, y: 0, z: 0}
    LengthMultiplier: 1
    PositionSpeed: 1
    RotationSpeed: 1
    Springiness: 0
    Slithery: 1
    Curling: 0.5
    Slippery: 1
    LastKeyframeLocalRotation: {x: 0, y: 0, z: 0, w: 0}
    LastKeyframeLocalPosition: {x: 0, y: 0, z: 0}
  UseMaxDistance: 0
  DistanceFrom: {fileID: 0}
  _distanceFrom_Auto: {fileID: 0}
  MaximumDistance: 35
  MaxOutDistanceFactor: 0
  DistanceWithoutY: 0
  DistanceMeasurePoint: {x: 0, y: 0, z: 0}
  FadeDuration: 0.75
  Deflection: 0
  DeflectionStartAngle: 10
  DeflectionSmooth: 0
  DeflectionFalloff:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  DeflectOnlyCollisions: 1
  _Editor_Category: 0
  _Editor_FeaturesCategory: 0
  DrawGizmos: 1
  StartBone: {fileID: 4348709109149050}
  EndBone: {fileID: 0}
  EndBoneJointOffset: {x: 0, y: 0, z: 0}
  _TransformsGhostChain:
  - {fileID: 4348709109149050}
  - {fileID: 4858884341418190}
  - {fileID: 4618795026672700}
  - {fileID: 4682986588889634}
  - {fileID: 4023926564130414}
  - {fileID: 4794533554389354}
  - {fileID: 4893238969152924}
  _GhostChainInitCount: 7
  UpdateRate: 0
  AnimatePhysics: 0
  InterpolateRate: 0
  Prewarm: 0
  Slithery: 1.2
  Curling: 0.6
  Springiness: 0
  MaxStretching: 1
  AngleLimit: 181
  AngleLimitAxis: {x: 0, y: 0, z: 0}
  LimitAxisRange: {x: 0, y: 0}
  LimitSmoothing: 0.5
  MotionInfluence: 1
  MotionInfluenceInY: 1
  IncludeParent: 0
  UnifyBendiness: 0
  ReactionSpeed: 0.32
  Sustain: 0
  RotationRelevancy: 0.112
  SmoothingStyle: 0
  TimeScale: 1
  DeltaType: 4
  UpdateAsLast: 1
  DetectZeroKeyframes: 1
  StartAfterTPose: 1
  OptimizeWithMesh: {fileID: 0}
  OptimizeWithMeshes: []
  TailAnimatorAmount: 1
  DetachChildren: 0
  Axis2D: 0
  Tangle: 0
  AnimateRoll: 1
  OverrideKeyframeAnimation: 0
--- !u!1 &1201132959775206
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4710811522703924}
  m_Layer: 0
  m_Name: BMedusaSmallTentacle_2_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4710811522703924
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1201132959775206}
  serializedVersion: 2
  m_LocalRotation: {x: -0.014418579, y: -9.598948e-33, z: 2.4516048e-32, w: 0.99989605}
  m_LocalPosition: {x: -0, y: 2.6645354e-16, z: -0.13282073}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4553558501434468}
  m_Father: {fileID: 4362552420222478}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1208290431652140
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4281372711745850}
  m_Layer: 0
  m_Name: BMedusaSmallTentacle_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4281372711745850
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1208290431652140}
  serializedVersion: 2
  m_LocalRotation: {x: 0.007298687, y: -4.6063668e-32, z: 4.964133e-32, w: 0.99997336}
  m_LocalPosition: {x: -0, y: 1.8873792e-16, z: -0.15702073}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4771379987283930}
  m_Father: {fileID: 4947156887212692}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1212785151472032
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4422272615871404}
  m_Layer: 0
  m_Name: BMedusaBigTentacle_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4422272615871404
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1212785151472032}
  serializedVersion: 2
  m_LocalRotation: {x: -0.009355862, y: 1.3952317e-10, z: 1.3054166e-12, w: 0.99995625}
  m_LocalPosition: {x: -0, y: 1.3322677e-16, z: -0.19329609}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4858749781824216}
  m_Father: {fileID: 4627020987916408}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1217026740095690
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4618795026672700}
  m_Layer: 0
  m_Name: BMedusaSmallTentacle_3_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4618795026672700
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1217026740095690}
  serializedVersion: 2
  m_LocalRotation: {x: -3.1086245e-15, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: -3.5527137e-16, z: -0.16595688}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4682986588889634}
  m_Father: {fileID: 4858884341418190}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1273824709406176
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4404810430758760}
  m_Layer: 0
  m_Name: BMedusaSmallTentacle_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4404810430758760
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1273824709406176}
  serializedVersion: 2
  m_LocalRotation: {x: -0.014418579, y: -4.2306653e-32, z: 4.8698868e-32, w: 0.99989605}
  m_LocalPosition: {x: -0, y: 2.4424907e-16, z: -0.13282073}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4568984149630552}
  m_Father: {fileID: 4539490278012388}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1275114946607516
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4553558501434468}
  m_Layer: 0
  m_Name: BMedusaSmallTentacle_3_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4553558501434468
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1275114946607516}
  serializedVersion: 2
  m_LocalRotation: {x: -3.1086245e-15, y: 1.0295743e-33, z: -2.4651903e-32, w: 1}
  m_LocalPosition: {x: -0, y: -3.1086244e-16, z: -0.16595688}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4625922807059234}
  m_Father: {fileID: 4710811522703924}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1296629068450854
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4771379987283930}
  m_Layer: 0
  m_Name: BMedusaSmallTentacle_7
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4771379987283930
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1296629068450854}
  serializedVersion: 2
  m_LocalRotation: {x: -1.1188966e-16, y: -2.4651903e-32, z: 4.9303807e-32, w: 1}
  m_LocalPosition: {x: -4.440892e-17, y: 0, z: -0.17491166}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4281372711745850}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1297664926636316
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4665063114609876}
  m_Layer: 0
  m_Name: BMedusaBigTentacle_4_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4665063114609876
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1297664926636316}
  serializedVersion: 2
  m_LocalRotation: {x: -0.009355862, y: 1.3952317e-10, z: 1.3054166e-12, w: 0.99995625}
  m_LocalPosition: {x: -0, y: 4.440892e-17, z: -0.19329609}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4253242480666078}
  m_Father: {fileID: 4770854467462028}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1304671195454648
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4029686301358206}
  m_Layer: 0
  m_Name: BMedusaBigTentacle_2_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4029686301358206
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1304671195454648}
  serializedVersion: 2
  m_LocalRotation: {x: -0.010465642, y: 1.5596713e-10, z: 1.6323856e-12, w: 0.9999452}
  m_LocalPosition: {x: -0, y: 4.440892e-17, z: -0.22861564}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4499118396483252}
  m_Father: {fileID: 4173073848065700}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1317554087072422
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4804820510424768}
  m_Layer: 0
  m_Name: BMedusaBigTentacle_4_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4804820510424768
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1317554087072422}
  serializedVersion: 2
  m_LocalRotation: {x: -0.009355862, y: 1.3952317e-10, z: 1.3054166e-12, w: 0.99995625}
  m_LocalPosition: {x: -0, y: 4.440892e-17, z: -0.19329609}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4368197746673206}
  m_Father: {fileID: 4008515803486860}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1320388444687420
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4948753678086262}
  m_Layer: 0
  m_Name: BMedusaHead_1_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4948753678086262
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1320388444687420}
  serializedVersion: 2
  m_LocalRotation: {x: -0.34962875, y: 0, z: -0, w: 0.93688834}
  m_LocalPosition: {x: -0, y: 1.110223e-17, z: -0.17085285}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4256239339913762}
  m_Father: {fileID: 4114351381275056}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1333500288876158
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4858884341418190}
  m_Layer: 0
  m_Name: BMedusaSmallTentacle_2_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4858884341418190
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1333500288876158}
  serializedVersion: 2
  m_LocalRotation: {x: -0.014418579, y: 0, z: -0, w: 0.99989605}
  m_LocalPosition: {x: -0, y: 2.6645354e-16, z: -0.13282073}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4618795026672700}
  m_Father: {fileID: 4348709109149050}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1360635262755238
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4023926564130414}
  m_Layer: 0
  m_Name: BMedusaSmallTentacle_5_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4023926564130414
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1360635262755238}
  serializedVersion: 2
  m_LocalRotation: {x: -6.661338e-16, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 3.996803e-16, z: -0.18638234}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4794533554389354}
  m_Father: {fileID: 4682986588889634}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1376840829068060
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4705594881460506}
  m_Layer: 0
  m_Name: BMedusaSmallTentacle_5_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4705594881460506
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1376840829068060}
  serializedVersion: 2
  m_LocalRotation: {x: -6.661338e-16, y: -1.8399818e-32, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 3.5527137e-16, z: -0.18638234}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4916249086574084}
  m_Father: {fileID: 4625922807059234}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1377151728243846
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4397892140108248}
  m_Layer: 0
  m_Name: BMedusaBigTentacle_5_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4397892140108248
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1377151728243846}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0038758817, y: 5.7806614e-11, z: 2.2405328e-13, w: 0.9999925}
  m_LocalPosition: {x: -2.220446e-17, y: 8.881784e-17, z: -0.20429476}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4649201082156680}
  m_Father: {fileID: 4128911775619874}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1420496011864694
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4023000906293916}
  m_Layer: 0
  m_Name: BMedusaBigTentacle_6_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4023000906293916
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1420496011864694}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0076925354, y: -1.147386e-10, z: 8.8265685e-13, w: 0.99997044}
  m_LocalPosition: {x: 3.044316e-11, y: 0, z: -0.26354817}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4802801646766158}
  m_Father: {fileID: 4368197746673206}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1420574968451670
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4724247564744192}
  m_Layer: 0
  m_Name: BMedusaBigTentacle_2_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4724247564744192
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1420574968451670}
  serializedVersion: 2
  m_LocalRotation: {x: -0.010465642, y: 1.5596713e-10, z: 1.6323856e-12, w: 0.9999452}
  m_LocalPosition: {x: -0, y: 0, z: -0.22861564}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4008515803486860}
  m_Father: {fileID: 4093497776261874}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1424609456878154
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4791223082621518}
  - component: {fileID: 114694808781987386}
  m_Layer: 0
  m_Name: BMedusaHead_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4791223082621518
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1424609456878154}
  serializedVersion: 2
  m_LocalRotation: {x: 7.0434337e-17, y: 0.9864963, z: -0.16378362, w: 5.0376626e-17}
  m_LocalPosition: {x: -0, y: 0.033534747, z: 0.10234868}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4711285722647462}
  - {fileID: 4369423742016470}
  - {fileID: 4362552420222478}
  m_Father: {fileID: 4516784778058484}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &114694808781987386
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1424609456878154}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 61574e354ba34a141ae2cc1228f5eda3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  UseCollision: 0
  CollisionSpace: 1
  CollisionMode: 0
  CheapCollision: 0
  DynamicWorldCollidersInclusion: 0
  InclusionRadius: 1
  IgnoreMeshColliders: 1
  IncludedColliders: []
  IncludedColliders2D: []
  CollidersType: 0
  CollideWithOtherTails: 0
  CollideWithDisabledColliders: 1
  CollisionSlippery: 1
  ReflectCollision: 0
  CollidersScaleCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  CollidersScaleMul: 6.5
  CollisionsAutoCurve: 0.5
  IgnoredColliders: []
  IgnoredColliders2D: []
  CollidersSameLayer: 1
  CollidersAddRigidbody: 1
  RigidbodyMass: 1
  CollidersLayer: 0
  UseSlitheryCurve: 0
  SlitheryCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.75
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseCurlingCurve: 0
  CurlingCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.3
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseSpringCurve: 0
  SpringCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseSlipperyCurve: 0
  SlipperyCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UsePosSpeedCurve: 0
  PosCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseRotSpeedCurve: 0
  RotCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.9
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UsePartialBlend: 0
  BlendCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.95
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.45
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseIK: 0
  IK:
    IKWeight: 0
    IKTargetPosition: {x: 0, y: 0, z: 0}
    IKTargetRotation: {x: 0, y: 0, z: 0, w: 0}
    LastLocalDirection: {x: 0, y: 0, z: 0}
    LocalDirection: {x: 0, y: 0, z: 0}
    IKBones: []
    ContinousSolving: 0
    SyncWithAnimator: 0
    ReactionQuality: 0
    Smoothing: 0
    StretchToTarget: 0
    StretchCurve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    Use2D: 0
    Invert: 0
  IKTarget: {fileID: 0}
  IKAutoWeights: 1
  IKBaseReactionWeight: 0.65
  IKReactionWeightCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.25
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  IKAutoAngleLimits: 1
  IKAutoAngleLimit: 40
  IKContinousSolve: 0
  IkInvertOrder: 0
  IKBlend: 1
  IKAnimatorBlend: 0.5
  IKReactionQuality: 2
  IKSmoothing: 0
  IKStretchToTarget: 0
  IKStretchCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  IKLimitSettings: []
  IKSelectiveChain: 0
  RotationOffset: {x: 0, y: 0, z: 0, w: 1}
  Curving: {x: 0, y: 0, z: 0, w: 1}
  UseCurvingCurve: 0
  CurvCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.75
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  LengthMultiplier: 1
  UseLengthMulCurve: 0
  LengthMulCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseGravityCurve: 0
  GravityCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.65
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  Gravity: {x: 0, y: 0, z: 0}
  UseWaving: 1
  CosinusAdd: 0
  FixedCycle: 0.1
  WavingSpeed: 2
  WavingRange: 0.5
  WavingAxis: {x: 1, y: 0.2, z: 0.2}
  WavingType: 0
  AlternateWave: 2
  UseWind: 0
  WindEffectPower: 1
  WindTurbulencePower: 1
  WindWorldNoisePower: 0.5
  WindEffect: {x: 0, y: 0, z: 0}
  fDebug: 0
  _gizmosEditorStartPreview: {fileID: 0}
  _gizmospesp: {fileID: 0}
  _gizmosEditorEndPreview: {fileID: 0}
  _gizmospeep: {fileID: 0}
  _editor_Title: Tail Animator 2
  _editor_IsInspectorViewingColliders: 0
  _editor_IsInspectorViewingIncludedColliders: 0
  _editor_animatorViewedCounter: 1
  TailSegments: []
  GhostParent:
    ProceduralPosition: {x: 0, y: 0, z: 0}
    ProceduralPositionWeightBlended: {x: 0, y: 0, z: 0}
    TrueTargetRotation: {x: 0, y: 0, z: 0, w: 1}
    PosRefRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosReferenceRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosition: {x: 0, y: 0, z: 0}
    BlendValue: 1
    BoneDimensionsScaled: {x: 0, y: 0, z: 0}
    BoneLengthScaled: 0
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalPositionInRoot: {x: 0, y: 0, z: 0}
    InitialLocalRotationInRoot: {x: 0, y: 0, z: 0, w: 1}
    LocalOffset: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: 0, y: 0, z: 0, w: 1}
    ColliderRadius: 1
    CollisionContactFlag: 0
    CollisionContactRelevancy: -1
    VelocityHelper: {x: 0, y: 0, z: 0}
    QVelocityHelper: {x: 0, y: 0, z: 0, w: 1}
    PreviousPush: {x: 0, y: 0, z: 0}
    Curving: {x: 0, y: 0, z: 0, w: 1}
    Gravity: {x: 0, y: 0, z: 0}
    GravityLookOffset: {x: 0, y: 0, z: 0}
    LengthMultiplier: 1
    PositionSpeed: 1
    RotationSpeed: 1
    Springiness: 0
    Slithery: 1
    Curling: 0.5
    Slippery: 1
    LastKeyframeLocalRotation: {x: 0, y: 0, z: 0, w: 0}
    LastKeyframeLocalPosition: {x: 0, y: 0, z: 0}
  GhostChild:
    ProceduralPosition: {x: 0, y: 0, z: 0}
    ProceduralPositionWeightBlended: {x: 0, y: 0, z: 0}
    TrueTargetRotation: {x: 0, y: 0, z: 0, w: 1}
    PosRefRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosReferenceRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosition: {x: 0, y: 0, z: 0}
    BlendValue: 1
    BoneDimensionsScaled: {x: 0, y: 0, z: 0}
    BoneLengthScaled: 0
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalPositionInRoot: {x: 0, y: 0, z: 0}
    InitialLocalRotationInRoot: {x: 0, y: 0, z: 0, w: 1}
    LocalOffset: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: 0, y: 0, z: 0, w: 1}
    ColliderRadius: 1
    CollisionContactFlag: 0
    CollisionContactRelevancy: -1
    VelocityHelper: {x: 0, y: 0, z: 0}
    QVelocityHelper: {x: 0, y: 0, z: 0, w: 1}
    PreviousPush: {x: 0, y: 0, z: 0}
    Curving: {x: 0, y: 0, z: 0, w: 1}
    Gravity: {x: 0, y: 0, z: 0}
    GravityLookOffset: {x: 0, y: 0, z: 0}
    LengthMultiplier: 1
    PositionSpeed: 1
    RotationSpeed: 1
    Springiness: 0
    Slithery: 1
    Curling: 0.5
    Slippery: 1
    LastKeyframeLocalRotation: {x: 0, y: 0, z: 0, w: 0}
    LastKeyframeLocalPosition: {x: 0, y: 0, z: 0}
  UseMaxDistance: 0
  DistanceFrom: {fileID: 0}
  _distanceFrom_Auto: {fileID: 0}
  MaximumDistance: 35
  MaxOutDistanceFactor: 0
  DistanceWithoutY: 0
  DistanceMeasurePoint: {x: 0, y: 0, z: 0}
  FadeDuration: 0.75
  Deflection: 0
  DeflectionStartAngle: 10
  DeflectionSmooth: 0
  DeflectionFalloff:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  DeflectOnlyCollisions: 1
  _Editor_Category: 0
  _Editor_FeaturesCategory: 0
  DrawGizmos: 1
  StartBone: {fileID: 4791223082621518}
  EndBone: {fileID: 0}
  EndBoneJointOffset: {x: 0, y: 0, z: 0}
  _TransformsGhostChain:
  - {fileID: 4791223082621518}
  - {fileID: 4711285722647462}
  - {fileID: 4585172350752336}
  _GhostChainInitCount: 3
  UpdateRate: 0
  AnimatePhysics: 0
  InterpolateRate: 0
  Prewarm: 0
  Slithery: 1
  Curling: 0.75
  Springiness: 0
  MaxStretching: 1
  AngleLimit: 181
  AngleLimitAxis: {x: 0, y: 0, z: 0}
  LimitAxisRange: {x: 0, y: 0}
  LimitSmoothing: 0.5
  MotionInfluence: 1
  MotionInfluenceInY: 1
  IncludeParent: 0
  UnifyBendiness: 0
  ReactionSpeed: 0.3
  Sustain: 0
  RotationRelevancy: 0.8
  SmoothingStyle: 0
  TimeScale: 1
  DeltaType: 4
  UpdateAsLast: 0
  DetectZeroKeyframes: 1
  StartAfterTPose: 1
  OptimizeWithMesh: {fileID: 0}
  OptimizeWithMeshes: []
  TailAnimatorAmount: 1
  DetachChildren: 0
  Axis2D: 0
  Tangle: 0
  AnimateRoll: 0
  OverrideKeyframeAnimation: 0
--- !u!1 &1428776656259484
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4267755260373218}
  m_Layer: 0
  m_Name: BMedusaBigTentacle_8
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4267755260373218
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1428776656259484}
  serializedVersion: 2
  m_LocalRotation: {x: -3.330669e-16, y: -9.2262594e-17, z: -2.1897706e-17, w: 1}
  m_LocalPosition: {x: -0, y: 6.6613384e-17, z: -0.20020889}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4133831262160342}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1448458367524670
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4747344628455634}
  m_Layer: 0
  m_Name: BMedusaBigTentacle_7_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4747344628455634
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1448458367524670}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0038167106, y: 5.693271e-11, z: 2.1729725e-13, w: 0.9999927}
  m_LocalPosition: {x: -5.5289106e-15, y: -8.881784e-17, z: -0.26763394}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4623121953854166}
  m_Father: {fileID: 4649201082156680}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1456242873765976
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4245220811890572}
  - component: {fileID: 114401764802151184}
  m_Layer: 0
  m_Name: BMedusaBigTentacle
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4245220811890572
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1456242873765976}
  serializedVersion: 2
  m_LocalRotation: {x: 0.5999848, y: 0.60300714, z: 0.3692999, w: -0.37419003}
  m_LocalPosition: {x: -0.24595182, y: -0.15553026, z: -0.13672353}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4093497776261874}
  m_Father: {fileID: 4544918222211998}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &114401764802151184
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1456242873765976}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 61574e354ba34a141ae2cc1228f5eda3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  UseCollision: 0
  CollisionSpace: 1
  CollisionMode: 0
  CheapCollision: 0
  DynamicWorldCollidersInclusion: 0
  InclusionRadius: 1
  IgnoreMeshColliders: 1
  IncludedColliders: []
  IncludedColliders2D: []
  CollidersType: 0
  CollideWithOtherTails: 0
  CollideWithDisabledColliders: 1
  CollisionSlippery: 1
  ReflectCollision: 0
  CollidersScaleCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  CollidersScaleMul: 6.5
  CollisionsAutoCurve: 0.5
  IgnoredColliders: []
  IgnoredColliders2D: []
  CollidersSameLayer: 1
  CollidersAddRigidbody: 1
  RigidbodyMass: 1
  CollidersLayer: 0
  UseSlitheryCurve: 0
  SlitheryCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.75
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseCurlingCurve: 0
  CurlingCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.3
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseSpringCurve: 0
  SpringCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseSlipperyCurve: 0
  SlipperyCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UsePosSpeedCurve: 0
  PosCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseRotSpeedCurve: 0
  RotCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.9
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UsePartialBlend: 0
  BlendCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.95
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.45
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseIK: 0
  IK:
    IKWeight: 0
    IKTargetPosition: {x: 0, y: 0, z: 0}
    IKTargetRotation: {x: 0, y: 0, z: 0, w: 0}
    LastLocalDirection: {x: 0, y: 0, z: 0}
    LocalDirection: {x: 0, y: 0, z: 0}
    IKBones: []
    ContinousSolving: 0
    SyncWithAnimator: 0
    ReactionQuality: 0
    Smoothing: 0
    StretchToTarget: 0
    StretchCurve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    Use2D: 0
    Invert: 0
  IKTarget: {fileID: 0}
  IKAutoWeights: 1
  IKBaseReactionWeight: 0.65
  IKReactionWeightCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.25
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  IKAutoAngleLimits: 1
  IKAutoAngleLimit: 40
  IKContinousSolve: 0
  IkInvertOrder: 0
  IKBlend: 1
  IKAnimatorBlend: 0.5
  IKReactionQuality: 2
  IKSmoothing: 0
  IKStretchToTarget: 0
  IKStretchCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  IKLimitSettings: []
  IKSelectiveChain: 0
  RotationOffset: {x: 0, y: 0, z: 0, w: 1}
  Curving: {x: 0, y: 0, z: 0, w: 1}
  UseCurvingCurve: 0
  CurvCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.75
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  LengthMultiplier: 1
  UseLengthMulCurve: 0
  LengthMulCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseGravityCurve: 0
  GravityCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.65
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  Gravity: {x: 0, y: 0, z: 0}
  UseWaving: 1
  CosinusAdd: 1
  FixedCycle: 0
  WavingSpeed: 2.2
  WavingRange: 0.5
  WavingAxis: {x: 1, y: 0.2, z: 0.2}
  WavingType: 0
  AlternateWave: 1
  UseWind: 0
  WindEffectPower: 1
  WindTurbulencePower: 1
  WindWorldNoisePower: 0.5
  WindEffect: {x: 0, y: 0, z: 0}
  fDebug: 0
  _gizmosEditorStartPreview: {fileID: 0}
  _gizmospesp: {fileID: 0}
  _gizmosEditorEndPreview: {fileID: 0}
  _gizmospeep: {fileID: 0}
  _editor_Title: Tail Animator 2
  _editor_IsInspectorViewingColliders: 1
  _editor_IsInspectorViewingIncludedColliders: 1
  _editor_animatorViewedCounter: 0
  TailSegments: []
  GhostParent:
    ProceduralPosition: {x: 0, y: 0, z: 0}
    ProceduralPositionWeightBlended: {x: 0, y: 0, z: 0}
    TrueTargetRotation: {x: 0, y: 0, z: 0, w: 1}
    PosRefRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosReferenceRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosition: {x: 0, y: 0, z: 0}
    BlendValue: 1
    BoneDimensionsScaled: {x: 0, y: 0, z: 0}
    BoneLengthScaled: 0
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalPositionInRoot: {x: 0, y: 0, z: 0}
    InitialLocalRotationInRoot: {x: 0, y: 0, z: 0, w: 1}
    LocalOffset: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: 0, y: 0, z: 0, w: 1}
    ColliderRadius: 1
    CollisionContactFlag: 0
    CollisionContactRelevancy: -1
    VelocityHelper: {x: 0, y: 0, z: 0}
    QVelocityHelper: {x: 0, y: 0, z: 0, w: 1}
    PreviousPush: {x: 0, y: 0, z: 0}
    Curving: {x: 0, y: 0, z: 0, w: 1}
    Gravity: {x: 0, y: 0, z: 0}
    GravityLookOffset: {x: 0, y: 0, z: 0}
    LengthMultiplier: 1
    PositionSpeed: 1
    RotationSpeed: 1
    Springiness: 0
    Slithery: 1
    Curling: 0.5
    Slippery: 1
    LastKeyframeLocalRotation: {x: 0, y: 0, z: 0, w: 0}
    LastKeyframeLocalPosition: {x: 0, y: 0, z: 0}
  GhostChild:
    ProceduralPosition: {x: 0, y: 0, z: 0}
    ProceduralPositionWeightBlended: {x: 0, y: 0, z: 0}
    TrueTargetRotation: {x: 0, y: 0, z: 0, w: 1}
    PosRefRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosReferenceRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosition: {x: 0, y: 0, z: 0}
    BlendValue: 1
    BoneDimensionsScaled: {x: 0, y: 0, z: 0}
    BoneLengthScaled: 0
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalPositionInRoot: {x: 0, y: 0, z: 0}
    InitialLocalRotationInRoot: {x: 0, y: 0, z: 0, w: 1}
    LocalOffset: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: 0, y: 0, z: 0, w: 1}
    ColliderRadius: 1
    CollisionContactFlag: 0
    CollisionContactRelevancy: -1
    VelocityHelper: {x: 0, y: 0, z: 0}
    QVelocityHelper: {x: 0, y: 0, z: 0, w: 1}
    PreviousPush: {x: 0, y: 0, z: 0}
    Curving: {x: 0, y: 0, z: 0, w: 1}
    Gravity: {x: 0, y: 0, z: 0}
    GravityLookOffset: {x: 0, y: 0, z: 0}
    LengthMultiplier: 1
    PositionSpeed: 1
    RotationSpeed: 1
    Springiness: 0
    Slithery: 1
    Curling: 0.5
    Slippery: 1
    LastKeyframeLocalRotation: {x: 0, y: 0, z: 0, w: 0}
    LastKeyframeLocalPosition: {x: 0, y: 0, z: 0}
  UseMaxDistance: 0
  DistanceFrom: {fileID: 0}
  _distanceFrom_Auto: {fileID: 0}
  MaximumDistance: 35
  MaxOutDistanceFactor: 0
  DistanceWithoutY: 0
  DistanceMeasurePoint: {x: 0, y: 0, z: 0}
  FadeDuration: 0.75
  Deflection: 0
  DeflectionStartAngle: 10
  DeflectionSmooth: 0
  DeflectionFalloff:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  DeflectOnlyCollisions: 1
  _Editor_Category: 0
  _Editor_FeaturesCategory: 0
  DrawGizmos: 1
  StartBone: {fileID: 4245220811890572}
  EndBone: {fileID: 0}
  EndBoneJointOffset: {x: 0, y: 0, z: 0}
  _TransformsGhostChain:
  - {fileID: 4245220811890572}
  - {fileID: 4093497776261874}
  - {fileID: 4724247564744192}
  - {fileID: 4008515803486860}
  - {fileID: 4804820510424768}
  - {fileID: 4368197746673206}
  - {fileID: 4023000906293916}
  - {fileID: 4802801646766158}
  - {fileID: 4446104239766824}
  _GhostChainInitCount: 9
  UpdateRate: 0
  AnimatePhysics: 0
  InterpolateRate: 0
  Prewarm: 0
  Slithery: 1.2
  Curling: 0.6
  Springiness: 0
  MaxStretching: 1
  AngleLimit: 181
  AngleLimitAxis: {x: 0, y: 0, z: 0}
  LimitAxisRange: {x: 0, y: 0}
  LimitSmoothing: 0.5
  MotionInfluence: 1
  MotionInfluenceInY: 1
  IncludeParent: 0
  UnifyBendiness: 0
  ReactionSpeed: 0.32
  Sustain: 0
  RotationRelevancy: 0.112
  SmoothingStyle: 0
  TimeScale: 1
  DeltaType: 4
  UpdateAsLast: 1
  DetectZeroKeyframes: 1
  StartAfterTPose: 1
  OptimizeWithMesh: {fileID: 0}
  OptimizeWithMeshes: []
  TailAnimatorAmount: 1
  DetachChildren: 0
  Axis2D: 0
  Tangle: 0
  AnimateRoll: 1
  OverrideKeyframeAnimation: 0
--- !u!1 &1470115923409188
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4627020987916408}
  m_Layer: 0
  m_Name: BMedusaBigTentacle_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4627020987916408
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1470115923409188}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0155279, y: -2.3149002e-10, z: 3.5949872e-12, w: 0.9998795}
  m_LocalPosition: {x: -0, y: 1.5543122e-16, z: -0.19405498}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4422272615871404}
  m_Father: {fileID: 4468834799086354}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1482863116101974
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4262243184209816}
  m_Layer: 0
  m_Name: Fedusa
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4262243184209816
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1482863116101974}
  serializedVersion: 2
  m_LocalRotation: {x: 0.7071068, y: 0, z: 0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4516784778058484}
  - {fileID: 4496943030920412}
  m_Father: {fileID: 4056310940266686}
  m_LocalEulerAnglesHint: {x: 90, y: 0, z: 0}
--- !u!1 &1516381896977036
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4794533554389354}
  m_Layer: 0
  m_Name: BMedusaSmallTentacle_6_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4794533554389354
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1516381896977036}
  serializedVersion: 2
  m_LocalRotation: {x: 0.007298687, y: 0, z: -0, w: 0.99997336}
  m_LocalPosition: {x: -0, y: 1.7763569e-16, z: -0.15702073}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4893238969152924}
  m_Father: {fileID: 4023926564130414}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1525546650302132
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4123346571995812}
  m_Layer: 0
  m_Name: BMedusaHead_1_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4123346571995812
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1525546650302132}
  serializedVersion: 2
  m_LocalRotation: {x: -0.43225062, y: -1.110069e-16, z: 1.8496013e-18, w: 0.90175354}
  m_LocalPosition: {x: -3.800658e-19, y: 6.6613384e-17, z: -0.13363601}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4502739371238246}
  m_Father: {fileID: 4544918222211998}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1531920107313614
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4585172350752336}
  m_Layer: 0
  m_Name: BMedusaHead_2_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4585172350752336
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1531920107313614}
  serializedVersion: 2
  m_LocalRotation: {x: -2.220446e-16, y: -2.0258813e-17, z: -8.471867e-18, w: 1}
  m_LocalPosition: {x: -5.0409187e-17, y: -4.440892e-17, z: -0.21730311}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4711285722647462}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1539432206618318
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4056310940266686}
  - component: {fileID: 114505298714526368}
  - component: {fileID: 135997336013015586}
  m_Layer: 0
  m_Name: Forward Corrector
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4056310940266686
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1539432206618318}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 6.66, y: 5.05, z: 1.35}
  m_LocalScale: {x: 2, y: 2, z: 2}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4262243184209816}
  m_Father: {fileID: 4192922582611828}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &114505298714526368
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1539432206618318}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 43491a0ac6e9bb04cb722d4ed6d70d5e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  MainSpeed: 0.5
  RangeValue: {x: 1, y: 1, z: 1}
  RangeMul: 3
  AddYSin: 13.5
  AddYSinTimeSpeed: 0.49
  RotateForwardSpeed: 9
--- !u!135 &135997336013015586
SphereCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1539432206618318}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Radius: 0.5
  m_Center: {x: 0, y: 0, z: -0.12}
--- !u!1 &1557435166302882
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4947156887212692}
  m_Layer: 0
  m_Name: BMedusaSmallTentacle_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4947156887212692
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1557435166302882}
  serializedVersion: 2
  m_LocalRotation: {x: -6.661338e-16, y: 8.98903e-32, z: 4.9303807e-32, w: 1}
  m_LocalPosition: {x: -0, y: 3.5527137e-16, z: -0.18638234}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4281372711745850}
  m_Father: {fileID: 4932931450908010}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1558548821843992
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4625922807059234}
  m_Layer: 0
  m_Name: BMedusaSmallTentacle_4_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4625922807059234
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1558548821843992}
  serializedVersion: 2
  m_LocalRotation: {x: 3.330669e-16, y: 4.5582274e-32, z: -0, w: 1}
  m_LocalPosition: {x: -5.551115e-18, y: 2.220446e-16, z: -0.2068078}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4705594881460506}
  m_Father: {fileID: 4553558501434468}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1566454484762452
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4502739371238246}
  m_Layer: 0
  m_Name: BMedusaHead_2_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4502739371238246
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1566454484762452}
  serializedVersion: 2
  m_LocalRotation: {x: -5.7752325e-32, y: -2.018007e-16, z: -9.262983e-17, w: 1}
  m_LocalPosition: {x: -1.2208286e-17, y: -4.440892e-17, z: -0.28652194}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4123346571995812}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1575117524384684
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4161613562273456}
  m_Layer: 0
  m_Name: BMedusaSmallTentacle_4_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4161613562273456
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1575117524384684}
  serializedVersion: 2
  m_LocalRotation: {x: 2.220446e-16, y: 6.187527e-34, z: -1.540744e-33, w: 1}
  m_LocalPosition: {x: 4.440892e-17, y: 2.3314685e-16, z: -0.2068078}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4115987048860412}
  m_Father: {fileID: 4650322623395200}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1585951750145308
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4022440963597994}
  - component: {fileID: 114684671791889264}
  m_Layer: 0
  m_Name: BMedusaSmallTentacle_2_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4022440963597994
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1585951750145308}
  serializedVersion: 2
  m_LocalRotation: {x: 0.59607834, y: 0.6067985, z: 0.3630366, w: -0.38038218}
  m_LocalPosition: {x: 0.086014256, y: -0.12294366, z: -0.085483454}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4825973450039334}
  m_Father: {fileID: 4544918222211998}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &114684671791889264
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1585951750145308}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 61574e354ba34a141ae2cc1228f5eda3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  UseCollision: 0
  CollisionSpace: 1
  CollisionMode: 0
  CheapCollision: 0
  DynamicWorldCollidersInclusion: 0
  InclusionRadius: 1
  IgnoreMeshColliders: 1
  IncludedColliders: []
  IncludedColliders2D: []
  CollidersType: 0
  CollideWithOtherTails: 0
  CollideWithDisabledColliders: 1
  CollisionSlippery: 1
  ReflectCollision: 0
  CollidersScaleCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  CollidersScaleMul: 6.5
  CollisionsAutoCurve: 0.5
  IgnoredColliders: []
  IgnoredColliders2D: []
  CollidersSameLayer: 1
  CollidersAddRigidbody: 1
  RigidbodyMass: 1
  CollidersLayer: 0
  UseSlitheryCurve: 0
  SlitheryCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.75
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseCurlingCurve: 0
  CurlingCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.3
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseSpringCurve: 0
  SpringCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseSlipperyCurve: 0
  SlipperyCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UsePosSpeedCurve: 0
  PosCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseRotSpeedCurve: 0
  RotCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.9
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UsePartialBlend: 0
  BlendCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.95
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.45
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseIK: 0
  IK:
    IKWeight: 0
    IKTargetPosition: {x: 0, y: 0, z: 0}
    IKTargetRotation: {x: 0, y: 0, z: 0, w: 0}
    LastLocalDirection: {x: 0, y: 0, z: 0}
    LocalDirection: {x: 0, y: 0, z: 0}
    IKBones: []
    ContinousSolving: 0
    SyncWithAnimator: 0
    ReactionQuality: 0
    Smoothing: 0
    StretchToTarget: 0
    StretchCurve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    Use2D: 0
    Invert: 0
  IKTarget: {fileID: 0}
  IKAutoWeights: 1
  IKBaseReactionWeight: 0.65
  IKReactionWeightCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.25
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  IKAutoAngleLimits: 1
  IKAutoAngleLimit: 40
  IKContinousSolve: 0
  IkInvertOrder: 0
  IKBlend: 1
  IKAnimatorBlend: 0.5
  IKReactionQuality: 2
  IKSmoothing: 0
  IKStretchToTarget: 0
  IKStretchCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  IKLimitSettings: []
  IKSelectiveChain: 0
  RotationOffset: {x: 0, y: 0, z: 0, w: 1}
  Curving: {x: 0, y: 0, z: 0, w: 1}
  UseCurvingCurve: 0
  CurvCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.75
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  LengthMultiplier: 1
  UseLengthMulCurve: 0
  LengthMulCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseGravityCurve: 0
  GravityCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.65
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  Gravity: {x: 0, y: 0, z: 0}
  UseWaving: 1
  CosinusAdd: 1
  FixedCycle: 0
  WavingSpeed: 2.2
  WavingRange: 0.5
  WavingAxis: {x: 1, y: 0.2, z: 0.2}
  WavingType: 0
  AlternateWave: 1
  UseWind: 0
  WindEffectPower: 1
  WindTurbulencePower: 1
  WindWorldNoisePower: 0.5
  WindEffect: {x: 0, y: 0, z: 0}
  fDebug: 0
  _gizmosEditorStartPreview: {fileID: 0}
  _gizmospesp: {fileID: 0}
  _gizmosEditorEndPreview: {fileID: 0}
  _gizmospeep: {fileID: 0}
  _editor_Title: Tail Animator 2
  _editor_IsInspectorViewingColliders: 0
  _editor_IsInspectorViewingIncludedColliders: 0
  _editor_animatorViewedCounter: 1
  TailSegments: []
  GhostParent:
    ProceduralPosition: {x: 0, y: 0, z: 0}
    ProceduralPositionWeightBlended: {x: 0, y: 0, z: 0}
    TrueTargetRotation: {x: 0, y: 0, z: 0, w: 1}
    PosRefRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosReferenceRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosition: {x: 0, y: 0, z: 0}
    BlendValue: 1
    BoneDimensionsScaled: {x: 0, y: 0, z: 0}
    BoneLengthScaled: 0
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalPositionInRoot: {x: 0, y: 0, z: 0}
    InitialLocalRotationInRoot: {x: 0, y: 0, z: 0, w: 1}
    LocalOffset: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: 0, y: 0, z: 0, w: 1}
    ColliderRadius: 1
    CollisionContactFlag: 0
    CollisionContactRelevancy: -1
    VelocityHelper: {x: 0, y: 0, z: 0}
    QVelocityHelper: {x: 0, y: 0, z: 0, w: 1}
    PreviousPush: {x: 0, y: 0, z: 0}
    Curving: {x: 0, y: 0, z: 0, w: 1}
    Gravity: {x: 0, y: 0, z: 0}
    GravityLookOffset: {x: 0, y: 0, z: 0}
    LengthMultiplier: 1
    PositionSpeed: 1
    RotationSpeed: 1
    Springiness: 0
    Slithery: 1
    Curling: 0.5
    Slippery: 1
    LastKeyframeLocalRotation: {x: 0, y: 0, z: 0, w: 0}
    LastKeyframeLocalPosition: {x: 0, y: 0, z: 0}
  GhostChild:
    ProceduralPosition: {x: 0, y: 0, z: 0}
    ProceduralPositionWeightBlended: {x: 0, y: 0, z: 0}
    TrueTargetRotation: {x: 0, y: 0, z: 0, w: 1}
    PosRefRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosReferenceRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosition: {x: 0, y: 0, z: 0}
    BlendValue: 1
    BoneDimensionsScaled: {x: 0, y: 0, z: 0}
    BoneLengthScaled: 0
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalPositionInRoot: {x: 0, y: 0, z: 0}
    InitialLocalRotationInRoot: {x: 0, y: 0, z: 0, w: 1}
    LocalOffset: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: 0, y: 0, z: 0, w: 1}
    ColliderRadius: 1
    CollisionContactFlag: 0
    CollisionContactRelevancy: -1
    VelocityHelper: {x: 0, y: 0, z: 0}
    QVelocityHelper: {x: 0, y: 0, z: 0, w: 1}
    PreviousPush: {x: 0, y: 0, z: 0}
    Curving: {x: 0, y: 0, z: 0, w: 1}
    Gravity: {x: 0, y: 0, z: 0}
    GravityLookOffset: {x: 0, y: 0, z: 0}
    LengthMultiplier: 1
    PositionSpeed: 1
    RotationSpeed: 1
    Springiness: 0
    Slithery: 1
    Curling: 0.5
    Slippery: 1
    LastKeyframeLocalRotation: {x: 0, y: 0, z: 0, w: 0}
    LastKeyframeLocalPosition: {x: 0, y: 0, z: 0}
  UseMaxDistance: 0
  DistanceFrom: {fileID: 0}
  _distanceFrom_Auto: {fileID: 0}
  MaximumDistance: 35
  MaxOutDistanceFactor: 0
  DistanceWithoutY: 0
  DistanceMeasurePoint: {x: 0, y: 0, z: 0}
  FadeDuration: 0.75
  Deflection: 0
  DeflectionStartAngle: 10
  DeflectionSmooth: 0
  DeflectionFalloff:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  DeflectOnlyCollisions: 1
  _Editor_Category: 0
  _Editor_FeaturesCategory: 0
  DrawGizmos: 1
  StartBone: {fileID: 4022440963597994}
  EndBone: {fileID: 0}
  EndBoneJointOffset: {x: 0, y: 0, z: 0}
  _TransformsGhostChain:
  - {fileID: 4022440963597994}
  - {fileID: 4825973450039334}
  - {fileID: 4650322623395200}
  - {fileID: 4161613562273456}
  - {fileID: 4115987048860412}
  - {fileID: 4710033502307542}
  - {fileID: 4488331731804064}
  _GhostChainInitCount: 7
  UpdateRate: 0
  AnimatePhysics: 0
  InterpolateRate: 0
  Prewarm: 0
  Slithery: 1.2
  Curling: 0.6
  Springiness: 0
  MaxStretching: 1
  AngleLimit: 181
  AngleLimitAxis: {x: 0, y: 0, z: 0}
  LimitAxisRange: {x: 0, y: 0}
  LimitSmoothing: 0.5
  MotionInfluence: 1
  MotionInfluenceInY: 1
  IncludeParent: 0
  UnifyBendiness: 0
  ReactionSpeed: 0.32
  Sustain: 0
  RotationRelevancy: 0.112
  SmoothingStyle: 0
  TimeScale: 1
  DeltaType: 4
  UpdateAsLast: 1
  DetectZeroKeyframes: 1
  StartAfterTPose: 1
  OptimizeWithMesh: {fileID: 0}
  OptimizeWithMeshes: []
  TailAnimatorAmount: 1
  DetachChildren: 0
  Axis2D: 0
  Tangle: 0
  AnimateRoll: 1
  OverrideKeyframeAnimation: 0
--- !u!1 &1590430973716402
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4115987048860412}
  m_Layer: 0
  m_Name: BMedusaSmallTentacle_5_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4115987048860412
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1590430973716402}
  serializedVersion: 2
  m_LocalRotation: {x: -6.661338e-16, y: 3.9706256e-33, z: -1.540744e-33, w: 1}
  m_LocalPosition: {x: -0, y: 3.7747584e-16, z: -0.18638234}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4710033502307542}
  m_Father: {fileID: 4161613562273456}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1591360365260080
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4093775943049514}
  m_Layer: 0
  m_Name: BMedusaHead_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4093775943049514
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1591360365260080}
  serializedVersion: 2
  m_LocalRotation: {x: 8.326673e-17, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: 6.7718875e-17, y: -2.220446e-17, z: -0.27119774}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4446627823584482}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1619186925458672
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4825973450039334}
  m_Layer: 0
  m_Name: BMedusaSmallTentacle_2_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4825973450039334
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1619186925458672}
  serializedVersion: 2
  m_LocalRotation: {x: -0.014418579, y: 4.555045e-34, z: -1.5343358e-33, w: 0.99989605}
  m_LocalPosition: {x: -0, y: 1.7763569e-16, z: -0.13282073}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4650322623395200}
  m_Father: {fileID: 4022440963597994}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1643311089120814
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4093497776261874}
  m_Layer: 0
  m_Name: BMedusaBigTentacle_1_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4093497776261874
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1643311089120814}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00022835647, y: -1.7013883e-12, z: 0.000000007450581, w: 1}
  m_LocalPosition: {x: -0, y: 8.881784e-17, z: -0.21457219}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4724247564744192}
  m_Father: {fileID: 4245220811890572}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1651867777036010
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4623121953854166}
  m_Layer: 0
  m_Name: BMedusaBigTentacle_8_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4623121953854166
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1651867777036010}
  serializedVersion: 2
  m_LocalRotation: {x: -3.330669e-16, y: -9.2262594e-17, z: -2.1897706e-17, w: 1}
  m_LocalPosition: {x: -0, y: 4.440892e-17, z: -0.20020889}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4747344628455634}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1661058781208572
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4544918222211998}
  - component: {fileID: 114464548249188672}
  m_Layer: 0
  m_Name: BMedusaHead_2_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4544918222211998
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1661058781208572}
  serializedVersion: 2
  m_LocalRotation: {x: 0.16245987, y: 0.688191, z: -0.16245987, w: 0.688191}
  m_LocalPosition: {x: -0.10270586, y: 0.039437987, z: -0.010494943}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4123346571995812}
  - {fileID: 4245220811890572}
  - {fileID: 4022440963597994}
  m_Father: {fileID: 4516784778058484}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &114464548249188672
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1661058781208572}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 61574e354ba34a141ae2cc1228f5eda3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  UseCollision: 0
  CollisionSpace: 1
  CollisionMode: 0
  CheapCollision: 0
  DynamicWorldCollidersInclusion: 0
  InclusionRadius: 1
  IgnoreMeshColliders: 1
  IncludedColliders: []
  IncludedColliders2D: []
  CollidersType: 0
  CollideWithOtherTails: 0
  CollideWithDisabledColliders: 1
  CollisionSlippery: 1
  ReflectCollision: 0
  CollidersScaleCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  CollidersScaleMul: 6.5
  CollisionsAutoCurve: 0.5
  IgnoredColliders: []
  IgnoredColliders2D: []
  CollidersSameLayer: 1
  CollidersAddRigidbody: 1
  RigidbodyMass: 1
  CollidersLayer: 0
  UseSlitheryCurve: 0
  SlitheryCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.75
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseCurlingCurve: 0
  CurlingCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.3
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseSpringCurve: 0
  SpringCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseSlipperyCurve: 0
  SlipperyCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UsePosSpeedCurve: 0
  PosCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseRotSpeedCurve: 0
  RotCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.9
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UsePartialBlend: 0
  BlendCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.95
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.45
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseIK: 0
  IK:
    IKWeight: 0
    IKTargetPosition: {x: 0, y: 0, z: 0}
    IKTargetRotation: {x: 0, y: 0, z: 0, w: 0}
    LastLocalDirection: {x: 0, y: 0, z: 0}
    LocalDirection: {x: 0, y: 0, z: 0}
    IKBones: []
    ContinousSolving: 0
    SyncWithAnimator: 0
    ReactionQuality: 0
    Smoothing: 0
    StretchToTarget: 0
    StretchCurve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    Use2D: 0
    Invert: 0
  IKTarget: {fileID: 0}
  IKAutoWeights: 1
  IKBaseReactionWeight: 0.65
  IKReactionWeightCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.25
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  IKAutoAngleLimits: 1
  IKAutoAngleLimit: 40
  IKContinousSolve: 0
  IkInvertOrder: 0
  IKBlend: 1
  IKAnimatorBlend: 0.5
  IKReactionQuality: 2
  IKSmoothing: 0
  IKStretchToTarget: 0
  IKStretchCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  IKLimitSettings: []
  IKSelectiveChain: 0
  RotationOffset: {x: 0, y: 0, z: 0, w: 1}
  Curving: {x: 0, y: 0, z: 0, w: 1}
  UseCurvingCurve: 0
  CurvCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.75
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  LengthMultiplier: 1
  UseLengthMulCurve: 0
  LengthMulCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseGravityCurve: 0
  GravityCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.65
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  Gravity: {x: 0, y: 0, z: 0}
  UseWaving: 1
  CosinusAdd: 0
  FixedCycle: 0.1
  WavingSpeed: 2
  WavingRange: 0.5
  WavingAxis: {x: 1, y: 0.2, z: 0.2}
  WavingType: 0
  AlternateWave: 2
  UseWind: 0
  WindEffectPower: 1
  WindTurbulencePower: 1
  WindWorldNoisePower: 0.5
  WindEffect: {x: 0, y: 0, z: 0}
  fDebug: 0
  _gizmosEditorStartPreview: {fileID: 0}
  _gizmospesp: {fileID: 0}
  _gizmosEditorEndPreview: {fileID: 0}
  _gizmospeep: {fileID: 0}
  _editor_Title: Tail Animator 2
  _editor_IsInspectorViewingColliders: 0
  _editor_IsInspectorViewingIncludedColliders: 0
  _editor_animatorViewedCounter: 1
  TailSegments: []
  GhostParent:
    ProceduralPosition: {x: 0, y: 0, z: 0}
    ProceduralPositionWeightBlended: {x: 0, y: 0, z: 0}
    TrueTargetRotation: {x: 0, y: 0, z: 0, w: 1}
    PosRefRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosReferenceRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosition: {x: 0, y: 0, z: 0}
    BlendValue: 1
    BoneDimensionsScaled: {x: 0, y: 0, z: 0}
    BoneLengthScaled: 0
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalPositionInRoot: {x: 0, y: 0, z: 0}
    InitialLocalRotationInRoot: {x: 0, y: 0, z: 0, w: 1}
    LocalOffset: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: 0, y: 0, z: 0, w: 1}
    ColliderRadius: 1
    CollisionContactFlag: 0
    CollisionContactRelevancy: -1
    VelocityHelper: {x: 0, y: 0, z: 0}
    QVelocityHelper: {x: 0, y: 0, z: 0, w: 1}
    PreviousPush: {x: 0, y: 0, z: 0}
    Curving: {x: 0, y: 0, z: 0, w: 1}
    Gravity: {x: 0, y: 0, z: 0}
    GravityLookOffset: {x: 0, y: 0, z: 0}
    LengthMultiplier: 1
    PositionSpeed: 1
    RotationSpeed: 1
    Springiness: 0
    Slithery: 1
    Curling: 0.5
    Slippery: 1
    LastKeyframeLocalRotation: {x: 0, y: 0, z: 0, w: 0}
    LastKeyframeLocalPosition: {x: 0, y: 0, z: 0}
  GhostChild:
    ProceduralPosition: {x: 0, y: 0, z: 0}
    ProceduralPositionWeightBlended: {x: 0, y: 0, z: 0}
    TrueTargetRotation: {x: 0, y: 0, z: 0, w: 1}
    PosRefRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosReferenceRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosition: {x: 0, y: 0, z: 0}
    BlendValue: 1
    BoneDimensionsScaled: {x: 0, y: 0, z: 0}
    BoneLengthScaled: 0
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalPositionInRoot: {x: 0, y: 0, z: 0}
    InitialLocalRotationInRoot: {x: 0, y: 0, z: 0, w: 1}
    LocalOffset: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: 0, y: 0, z: 0, w: 1}
    ColliderRadius: 1
    CollisionContactFlag: 0
    CollisionContactRelevancy: -1
    VelocityHelper: {x: 0, y: 0, z: 0}
    QVelocityHelper: {x: 0, y: 0, z: 0, w: 1}
    PreviousPush: {x: 0, y: 0, z: 0}
    Curving: {x: 0, y: 0, z: 0, w: 1}
    Gravity: {x: 0, y: 0, z: 0}
    GravityLookOffset: {x: 0, y: 0, z: 0}
    LengthMultiplier: 1
    PositionSpeed: 1
    RotationSpeed: 1
    Springiness: 0
    Slithery: 1
    Curling: 0.5
    Slippery: 1
    LastKeyframeLocalRotation: {x: 0, y: 0, z: 0, w: 0}
    LastKeyframeLocalPosition: {x: 0, y: 0, z: 0}
  UseMaxDistance: 0
  DistanceFrom: {fileID: 0}
  _distanceFrom_Auto: {fileID: 0}
  MaximumDistance: 35
  MaxOutDistanceFactor: 0
  DistanceWithoutY: 0
  DistanceMeasurePoint: {x: 0, y: 0, z: 0}
  FadeDuration: 0.75
  Deflection: 0
  DeflectionStartAngle: 10
  DeflectionSmooth: 0
  DeflectionFalloff:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  DeflectOnlyCollisions: 1
  _Editor_Category: 0
  _Editor_FeaturesCategory: 0
  DrawGizmos: 1
  StartBone: {fileID: 4544918222211998}
  EndBone: {fileID: 0}
  EndBoneJointOffset: {x: 0, y: 0, z: 0}
  _TransformsGhostChain:
  - {fileID: 4544918222211998}
  - {fileID: 4123346571995812}
  - {fileID: 4502739371238246}
  _GhostChainInitCount: 3
  UpdateRate: 0
  AnimatePhysics: 0
  InterpolateRate: 0
  Prewarm: 0
  Slithery: 1
  Curling: 0.75
  Springiness: 0
  MaxStretching: 1
  AngleLimit: 181
  AngleLimitAxis: {x: 0, y: 0, z: 0}
  LimitAxisRange: {x: 0, y: 0}
  LimitSmoothing: 0.5
  MotionInfluence: 1
  MotionInfluenceInY: 1
  IncludeParent: 0
  UnifyBendiness: 0
  ReactionSpeed: 0.3
  Sustain: 0
  RotationRelevancy: 0.8
  SmoothingStyle: 0
  TimeScale: 1
  DeltaType: 4
  UpdateAsLast: 0
  DetectZeroKeyframes: 1
  StartAfterTPose: 1
  OptimizeWithMesh: {fileID: 0}
  OptimizeWithMeshes: []
  TailAnimatorAmount: 1
  DetachChildren: 0
  Axis2D: 0
  Tangle: 0
  AnimateRoll: 0
  OverrideKeyframeAnimation: 0
--- !u!1 &1669010897294854
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4932931450908010}
  m_Layer: 0
  m_Name: BMedusaSmallTentacle_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4932931450908010
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1669010897294854}
  serializedVersion: 2
  m_LocalRotation: {x: 3.330669e-16, y: 9.5688485e-32, z: -4.9303807e-32, w: 1}
  m_LocalPosition: {x: -4.440892e-17, y: 2.553513e-16, z: -0.2068078}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4947156887212692}
  m_Father: {fileID: 4568984149630552}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1676661583205532
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4499118396483252}
  m_Layer: 0
  m_Name: BMedusaBigTentacle_3_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4499118396483252
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1676661583205532}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0155279, y: -2.3149002e-10, z: 3.5949872e-12, w: 0.9998795}
  m_LocalPosition: {x: -2.220446e-17, y: 8.881784e-17, z: -0.19405498}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4128911775619874}
  m_Father: {fileID: 4029686301358206}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1702603981368668
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4852971330691566}
  - component: {fileID: 114834842024758952}
  m_Layer: 0
  m_Name: BMedusaBigTentacle_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4852971330691566
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1702603981368668}
  serializedVersion: 2
  m_LocalRotation: {x: -0.59737206, y: 0.60042816, z: 0.37347823, w: 0.37834728}
  m_LocalPosition: {x: -0.1328338, y: -0.1879167, z: -0.19028248}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4550558008891874}
  m_Father: {fileID: 4967518121876084}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &114834842024758952
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1702603981368668}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 61574e354ba34a141ae2cc1228f5eda3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  UseCollision: 0
  CollisionSpace: 1
  CollisionMode: 0
  CheapCollision: 0
  DynamicWorldCollidersInclusion: 0
  InclusionRadius: 1
  IgnoreMeshColliders: 1
  IncludedColliders: []
  IncludedColliders2D: []
  CollidersType: 0
  CollideWithOtherTails: 0
  CollideWithDisabledColliders: 1
  CollisionSlippery: 1
  ReflectCollision: 0
  CollidersScaleCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  CollidersScaleMul: 6.5
  CollisionsAutoCurve: 0.5
  IgnoredColliders: []
  IgnoredColliders2D: []
  CollidersSameLayer: 1
  CollidersAddRigidbody: 1
  RigidbodyMass: 1
  CollidersLayer: 0
  UseSlitheryCurve: 0
  SlitheryCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.75
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseCurlingCurve: 0
  CurlingCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.3
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseSpringCurve: 0
  SpringCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseSlipperyCurve: 0
  SlipperyCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UsePosSpeedCurve: 0
  PosCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseRotSpeedCurve: 0
  RotCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.9
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UsePartialBlend: 0
  BlendCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.95
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.45
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseIK: 0
  IK:
    IKWeight: 0
    IKTargetPosition: {x: 0, y: 0, z: 0}
    IKTargetRotation: {x: 0, y: 0, z: 0, w: 0}
    LastLocalDirection: {x: 0, y: 0, z: 0}
    LocalDirection: {x: 0, y: 0, z: 0}
    IKBones: []
    ContinousSolving: 0
    SyncWithAnimator: 0
    ReactionQuality: 0
    Smoothing: 0
    StretchToTarget: 0
    StretchCurve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    Use2D: 0
    Invert: 0
  IKTarget: {fileID: 0}
  IKAutoWeights: 1
  IKBaseReactionWeight: 0.65
  IKReactionWeightCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.25
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  IKAutoAngleLimits: 1
  IKAutoAngleLimit: 40
  IKContinousSolve: 0
  IkInvertOrder: 0
  IKBlend: 1
  IKAnimatorBlend: 0.5
  IKReactionQuality: 2
  IKSmoothing: 0
  IKStretchToTarget: 0
  IKStretchCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  IKLimitSettings: []
  IKSelectiveChain: 0
  RotationOffset: {x: 0, y: 0, z: 0, w: 1}
  Curving: {x: 0, y: 0, z: 0, w: 1}
  UseCurvingCurve: 0
  CurvCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.75
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  LengthMultiplier: 1
  UseLengthMulCurve: 0
  LengthMulCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseGravityCurve: 0
  GravityCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.65
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  Gravity: {x: 0, y: 0, z: 0}
  UseWaving: 1
  CosinusAdd: 1
  FixedCycle: 0
  WavingSpeed: 2.2
  WavingRange: 0.5
  WavingAxis: {x: 1, y: 0.2, z: 0.2}
  WavingType: 0
  AlternateWave: 1
  UseWind: 0
  WindEffectPower: 1
  WindTurbulencePower: 1
  WindWorldNoisePower: 0.5
  WindEffect: {x: 0, y: 0, z: 0}
  fDebug: 0
  _gizmosEditorStartPreview: {fileID: 0}
  _gizmospesp: {fileID: 0}
  _gizmosEditorEndPreview: {fileID: 0}
  _gizmospeep: {fileID: 0}
  _editor_Title: Tail Animator 2
  _editor_IsInspectorViewingColliders: 0
  _editor_IsInspectorViewingIncludedColliders: 0
  _editor_animatorViewedCounter: 1
  TailSegments: []
  GhostParent:
    ProceduralPosition: {x: 0, y: 0, z: 0}
    ProceduralPositionWeightBlended: {x: 0, y: 0, z: 0}
    TrueTargetRotation: {x: 0, y: 0, z: 0, w: 1}
    PosRefRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosReferenceRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosition: {x: 0, y: 0, z: 0}
    BlendValue: 1
    BoneDimensionsScaled: {x: 0, y: 0, z: 0}
    BoneLengthScaled: 0
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalPositionInRoot: {x: 0, y: 0, z: 0}
    InitialLocalRotationInRoot: {x: 0, y: 0, z: 0, w: 1}
    LocalOffset: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: 0, y: 0, z: 0, w: 1}
    ColliderRadius: 1
    CollisionContactFlag: 0
    CollisionContactRelevancy: -1
    VelocityHelper: {x: 0, y: 0, z: 0}
    QVelocityHelper: {x: 0, y: 0, z: 0, w: 1}
    PreviousPush: {x: 0, y: 0, z: 0}
    Curving: {x: 0, y: 0, z: 0, w: 1}
    Gravity: {x: 0, y: 0, z: 0}
    GravityLookOffset: {x: 0, y: 0, z: 0}
    LengthMultiplier: 1
    PositionSpeed: 1
    RotationSpeed: 1
    Springiness: 0
    Slithery: 1
    Curling: 0.5
    Slippery: 1
    LastKeyframeLocalRotation: {x: 0, y: 0, z: 0, w: 0}
    LastKeyframeLocalPosition: {x: 0, y: 0, z: 0}
  GhostChild:
    ProceduralPosition: {x: 0, y: 0, z: 0}
    ProceduralPositionWeightBlended: {x: 0, y: 0, z: 0}
    TrueTargetRotation: {x: 0, y: 0, z: 0, w: 1}
    PosRefRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosReferenceRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosition: {x: 0, y: 0, z: 0}
    BlendValue: 1
    BoneDimensionsScaled: {x: 0, y: 0, z: 0}
    BoneLengthScaled: 0
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalPositionInRoot: {x: 0, y: 0, z: 0}
    InitialLocalRotationInRoot: {x: 0, y: 0, z: 0, w: 1}
    LocalOffset: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: 0, y: 0, z: 0, w: 1}
    ColliderRadius: 1
    CollisionContactFlag: 0
    CollisionContactRelevancy: -1
    VelocityHelper: {x: 0, y: 0, z: 0}
    QVelocityHelper: {x: 0, y: 0, z: 0, w: 1}
    PreviousPush: {x: 0, y: 0, z: 0}
    Curving: {x: 0, y: 0, z: 0, w: 1}
    Gravity: {x: 0, y: 0, z: 0}
    GravityLookOffset: {x: 0, y: 0, z: 0}
    LengthMultiplier: 1
    PositionSpeed: 1
    RotationSpeed: 1
    Springiness: 0
    Slithery: 1
    Curling: 0.5
    Slippery: 1
    LastKeyframeLocalRotation: {x: 0, y: 0, z: 0, w: 0}
    LastKeyframeLocalPosition: {x: 0, y: 0, z: 0}
  UseMaxDistance: 0
  DistanceFrom: {fileID: 0}
  _distanceFrom_Auto: {fileID: 0}
  MaximumDistance: 35
  MaxOutDistanceFactor: 0
  DistanceWithoutY: 0
  DistanceMeasurePoint: {x: 0, y: 0, z: 0}
  FadeDuration: 0.75
  Deflection: 0
  DeflectionStartAngle: 10
  DeflectionSmooth: 0
  DeflectionFalloff:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  DeflectOnlyCollisions: 1
  _Editor_Category: 1
  _Editor_FeaturesCategory: 0
  DrawGizmos: 1
  StartBone: {fileID: 4852971330691566}
  EndBone: {fileID: 0}
  EndBoneJointOffset: {x: 0, y: 0, z: 0}
  _TransformsGhostChain:
  - {fileID: 4852971330691566}
  - {fileID: 4550558008891874}
  - {fileID: 4468834799086354}
  - {fileID: 4627020987916408}
  - {fileID: 4422272615871404}
  - {fileID: 4858749781824216}
  - {fileID: 4223421973644560}
  - {fileID: 4133831262160342}
  - {fileID: 4267755260373218}
  _GhostChainInitCount: 9
  UpdateRate: 0
  AnimatePhysics: 0
  InterpolateRate: 0
  Prewarm: 0
  Slithery: 1.2
  Curling: 0.6
  Springiness: 0
  MaxStretching: 1
  AngleLimit: 181
  AngleLimitAxis: {x: 0, y: 0, z: 0}
  LimitAxisRange: {x: 0, y: 0}
  LimitSmoothing: 0.5
  MotionInfluence: 1
  MotionInfluenceInY: 1
  IncludeParent: 0
  UnifyBendiness: 0
  ReactionSpeed: 0.32
  Sustain: 0
  RotationRelevancy: 0.112
  SmoothingStyle: 0
  TimeScale: 1
  DeltaType: 4
  UpdateAsLast: 1
  DetectZeroKeyframes: 1
  StartAfterTPose: 1
  OptimizeWithMesh: {fileID: 0}
  OptimizeWithMeshes: []
  TailAnimatorAmount: 1
  DetachChildren: 0
  Axis2D: 0
  Tangle: 0
  AnimateRoll: 1
  OverrideKeyframeAnimation: 0
--- !u!1 &1711079790281660
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4568984149630552}
  m_Layer: 0
  m_Name: BMedusaSmallTentacle_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4568984149630552
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1711079790281660}
  serializedVersion: 2
  m_LocalRotation: {x: -3.1086245e-15, y: 1.5661434e-31, z: 0, w: 1}
  m_LocalPosition: {x: -0, y: -3.330669e-16, z: -0.16595688}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4932931450908010}
  m_Father: {fileID: 4404810430758760}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1729446579006494
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4770854467462028}
  m_Layer: 0
  m_Name: BMedusaBigTentacle_3_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4770854467462028
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1729446579006494}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0155279, y: -2.3149002e-10, z: 3.5949872e-12, w: 0.9998795}
  m_LocalPosition: {x: -2.220446e-17, y: 8.881784e-17, z: -0.19405498}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4665063114609876}
  m_Father: {fileID: 4598062211917050}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1730193897187000
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4390319561557988}
  m_Layer: 0
  m_Name: BMedusaBigTentacle_8_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4390319561557988
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1730193897187000}
  serializedVersion: 2
  m_LocalRotation: {x: -3.330669e-16, y: -9.2262594e-17, z: -2.1897704e-17, w: 1}
  m_LocalPosition: {x: -0, y: 8.881784e-17, z: -0.20020889}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4465872659551170}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1744831629762348
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4128911775619874}
  m_Layer: 0
  m_Name: BMedusaBigTentacle_4_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4128911775619874
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1744831629762348}
  serializedVersion: 2
  m_LocalRotation: {x: -0.009355862, y: 1.3952317e-10, z: 1.3054166e-12, w: 0.99995625}
  m_LocalPosition: {x: -2.220446e-17, y: 4.440892e-17, z: -0.19329609}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4397892140108248}
  m_Father: {fileID: 4499118396483252}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1752691883884512
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4765463346945718}
  m_Layer: 0
  m_Name: BMedusaSmallTentacle_7_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4765463346945718
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1752691883884512}
  serializedVersion: 2
  m_LocalRotation: {x: -1.110223e-16, y: -1.3096324e-32, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: -0.17491166}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4916249086574084}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1763823763172766
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4516784778058484}
  m_Layer: 0
  m_Name: BMedusaRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4516784778058484
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1763823763172766}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: -0.10665428, z: 0.010494943}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4967518121876084}
  - {fileID: 4544918222211998}
  - {fileID: 4791223082621518}
  - {fileID: 4114351381275056}
  m_Father: {fileID: 4262243184209816}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1784643133566050
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4488331731804064}
  m_Layer: 0
  m_Name: BMedusaSmallTentacle_7_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4488331731804064
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1784643133566050}
  serializedVersion: 2
  m_LocalRotation: {x: -1.1188966e-16, y: -0, z: 3.081488e-33, w: 1}
  m_LocalPosition: {x: -0, y: 1.110223e-17, z: -0.17491166}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4710033502307542}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1788027904520950
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4468834799086354}
  m_Layer: 0
  m_Name: BMedusaBigTentacle_2_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4468834799086354
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1788027904520950}
  serializedVersion: 2
  m_LocalRotation: {x: -0.010465642, y: 1.5596713e-10, z: 1.6323856e-12, w: 0.9999452}
  m_LocalPosition: {x: -0, y: 0, z: -0.22861564}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4627020987916408}
  m_Father: {fileID: 4550558008891874}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1791487226061744
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4682986588889634}
  m_Layer: 0
  m_Name: BMedusaSmallTentacle_4_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4682986588889634
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1791487226061744}
  serializedVersion: 2
  m_LocalRotation: {x: 2.220446e-16, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 2.6645354e-16, z: -0.2068078}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4023926564130414}
  m_Father: {fileID: 4618795026672700}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1793320584440450
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4368197746673206}
  m_Layer: 0
  m_Name: BMedusaBigTentacle_5_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4368197746673206
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1793320584440450}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0038758817, y: 5.7806614e-11, z: 2.2405328e-13, w: 0.9999925}
  m_LocalPosition: {x: -0, y: 8.881784e-17, z: -0.20429476}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4023000906293916}
  m_Father: {fileID: 4804820510424768}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1824647884053356
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4598062211917050}
  m_Layer: 0
  m_Name: BMedusaBigTentacle_2_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4598062211917050
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1824647884053356}
  serializedVersion: 2
  m_LocalRotation: {x: -0.010465642, y: 1.5596713e-10, z: 1.6323856e-12, w: 0.9999452}
  m_LocalPosition: {x: -0, y: 4.440892e-17, z: -0.22861564}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4770854467462028}
  m_Father: {fileID: 4275359106047306}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1833961382239024
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4275359106047306}
  m_Layer: 0
  m_Name: BMedusaBigTentacle_1_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4275359106047306
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1833961382239024}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00022835647, y: -1.7013883e-12, z: 0.000000007450581, w: 1}
  m_LocalPosition: {x: -2.220446e-17, y: 1.3322677e-16, z: -0.21457219}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4598062211917050}
  m_Father: {fileID: 4369423742016470}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1850014397998950
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4192922582611828}
  m_Layer: 0
  m_Name: PR_TailAnimatorDemo_UnderwaterMedusa
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4192922582611828
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1850014397998950}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4056310940266686}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1876019572761046
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4649201082156680}
  m_Layer: 0
  m_Name: BMedusaBigTentacle_6_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4649201082156680
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1876019572761046}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0076925354, y: -1.147386e-10, z: 8.8265685e-13, w: 0.99997044}
  m_LocalPosition: {x: 3.0443204e-11, y: 4.440892e-17, z: -0.26354817}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4747344628455634}
  m_Father: {fileID: 4397892140108248}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1884769565706888
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4802801646766158}
  m_Layer: 0
  m_Name: BMedusaBigTentacle_7_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4802801646766158
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1884769565706888}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0038167106, y: 5.693271e-11, z: 2.1729725e-13, w: 0.9999927}
  m_LocalPosition: {x: -5.5955242e-15, y: -8.881784e-17, z: -0.26763394}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4446104239766824}
  m_Father: {fileID: 4023000906293916}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1909071759933814
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4253242480666078}
  m_Layer: 0
  m_Name: BMedusaBigTentacle_5_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4253242480666078
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1909071759933814}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0038758817, y: 5.7806614e-11, z: 2.2405328e-13, w: 0.9999925}
  m_LocalPosition: {x: -0, y: 8.881784e-17, z: -0.20429476}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4119037796251514}
  m_Father: {fileID: 4665063114609876}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1924518094968950
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4446104239766824}
  m_Layer: 0
  m_Name: BMedusaBigTentacle_8_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4446104239766824
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1924518094968950}
  serializedVersion: 2
  m_LocalRotation: {x: -3.330669e-16, y: -9.2262594e-17, z: -2.1897706e-17, w: 1}
  m_LocalPosition: {x: 4.440892e-17, y: 0, z: -0.20020889}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4802801646766158}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1927824237693864
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4711285722647462}
  m_Layer: 0
  m_Name: BMedusaHead_1_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4711285722647462
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1927824237693864}
  serializedVersion: 2
  m_LocalRotation: {x: -0.3546742, y: -1.117256e-16, z: -1.2627948e-16, w: 0.93499}
  m_LocalPosition: {x: 6.1955795e-18, y: 4.440892e-17, z: -0.15792081}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4585172350752336}
  m_Father: {fileID: 4791223082621518}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1946628145303676
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4858749781824216}
  m_Layer: 0
  m_Name: BMedusaBigTentacle_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4858749781824216
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1946628145303676}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0038758817, y: 5.7806614e-11, z: 2.2405328e-13, w: 0.9999925}
  m_LocalPosition: {x: -0, y: 6.6613384e-17, z: -0.20429476}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4223421973644560}
  m_Father: {fileID: 4422272615871404}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1956804818039484
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4650322623395200}
  m_Layer: 0
  m_Name: BMedusaSmallTentacle_3_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4650322623395200
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1956804818039484}
  serializedVersion: 2
  m_LocalRotation: {x: -3.2196468e-15, y: -1.6236426e-33, z: 3.081488e-33, w: 1}
  m_LocalPosition: {x: -4.440892e-17, y: -3.8857806e-16, z: -0.16595688}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4161613562273456}
  m_Father: {fileID: 4825973450039334}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1958323547270966
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4133831262160342}
  m_Layer: 0
  m_Name: BMedusaBigTentacle_7
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4133831262160342
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1958323547270966}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0038167106, y: 5.693271e-11, z: 2.1729725e-13, w: 0.9999927}
  m_LocalPosition: {x: -5.551115e-15, y: -1.3322677e-16, z: -0.26763394}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4267755260373218}
  m_Father: {fileID: 4223421973644560}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1985046914751494
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4223421973644560}
  m_Layer: 0
  m_Name: BMedusaBigTentacle_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4223421973644560
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1985046914751494}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0076925354, y: -1.147386e-10, z: 8.8265685e-13, w: 0.99997044}
  m_LocalPosition: {x: 3.044325e-11, y: 0, z: -0.26354817}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4133831262160342}
  m_Father: {fileID: 4858749781824216}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1999796837207052
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4114351381275056}
  - component: {fileID: 114589226184517618}
  m_Layer: 0
  m_Name: BMedusaHead_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4114351381275056
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1999796837207052}
  serializedVersion: 2
  m_LocalRotation: {x: 0.096455544, y: 0, z: -0, w: 0.9953373}
  m_LocalPosition: {x: -0, y: 0.033534747, z: -0.10906603}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4948753678086262}
  - {fileID: 4433906129938410}
  - {fileID: 4348709109149050}
  m_Father: {fileID: 4516784778058484}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &114589226184517618
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1999796837207052}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 61574e354ba34a141ae2cc1228f5eda3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  UseCollision: 0
  CollisionSpace: 1
  CollisionMode: 0
  CheapCollision: 0
  DynamicWorldCollidersInclusion: 0
  InclusionRadius: 1
  IgnoreMeshColliders: 1
  IncludedColliders: []
  IncludedColliders2D: []
  CollidersType: 0
  CollideWithOtherTails: 0
  CollideWithDisabledColliders: 1
  CollisionSlippery: 1
  ReflectCollision: 0
  CollidersScaleCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  CollidersScaleMul: 6.5
  CollisionsAutoCurve: 0.5
  IgnoredColliders: []
  IgnoredColliders2D: []
  CollidersSameLayer: 1
  CollidersAddRigidbody: 1
  RigidbodyMass: 1
  CollidersLayer: 0
  UseSlitheryCurve: 0
  SlitheryCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.75
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseCurlingCurve: 0
  CurlingCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.3
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseSpringCurve: 0
  SpringCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseSlipperyCurve: 0
  SlipperyCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UsePosSpeedCurve: 0
  PosCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseRotSpeedCurve: 0
  RotCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.9
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UsePartialBlend: 0
  BlendCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.95
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.45
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseIK: 0
  IK:
    IKWeight: 0
    IKTargetPosition: {x: 0, y: 0, z: 0}
    IKTargetRotation: {x: 0, y: 0, z: 0, w: 0}
    LastLocalDirection: {x: 0, y: 0, z: 0}
    LocalDirection: {x: 0, y: 0, z: 0}
    IKBones: []
    ContinousSolving: 0
    SyncWithAnimator: 0
    ReactionQuality: 0
    Smoothing: 0
    StretchToTarget: 0
    StretchCurve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    Use2D: 0
    Invert: 0
  IKTarget: {fileID: 0}
  IKAutoWeights: 1
  IKBaseReactionWeight: 0.65
  IKReactionWeightCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.25
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  IKAutoAngleLimits: 1
  IKAutoAngleLimit: 40
  IKContinousSolve: 0
  IkInvertOrder: 0
  IKBlend: 1
  IKAnimatorBlend: 0.5
  IKReactionQuality: 2
  IKSmoothing: 0
  IKStretchToTarget: 0
  IKStretchCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  IKLimitSettings: []
  IKSelectiveChain: 0
  RotationOffset: {x: 0, y: 0, z: 0, w: 1}
  Curving: {x: 0, y: 0, z: 0, w: 1}
  UseCurvingCurve: 0
  CurvCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.75
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  LengthMultiplier: 1
  UseLengthMulCurve: 0
  LengthMulCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseGravityCurve: 0
  GravityCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.65
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  Gravity: {x: 0, y: 0, z: 0}
  UseWaving: 1
  CosinusAdd: 0
  FixedCycle: 0.1
  WavingSpeed: 2
  WavingRange: 0.5
  WavingAxis: {x: 1, y: 0.2, z: 0.2}
  WavingType: 0
  AlternateWave: 2
  UseWind: 0
  WindEffectPower: 1
  WindTurbulencePower: 1
  WindWorldNoisePower: 0.5
  WindEffect: {x: 0, y: 0, z: 0}
  fDebug: 0
  _gizmosEditorStartPreview: {fileID: 0}
  _gizmospesp: {fileID: 0}
  _gizmosEditorEndPreview: {fileID: 0}
  _gizmospeep: {fileID: 0}
  _editor_Title: Tail Animator 2
  _editor_IsInspectorViewingColliders: 0
  _editor_IsInspectorViewingIncludedColliders: 0
  _editor_animatorViewedCounter: 1
  TailSegments: []
  GhostParent:
    ProceduralPosition: {x: 0, y: 0, z: 0}
    ProceduralPositionWeightBlended: {x: 0, y: 0, z: 0}
    TrueTargetRotation: {x: 0, y: 0, z: 0, w: 1}
    PosRefRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosReferenceRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosition: {x: 0, y: 0, z: 0}
    BlendValue: 1
    BoneDimensionsScaled: {x: 0, y: 0, z: 0}
    BoneLengthScaled: 0
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalPositionInRoot: {x: 0, y: 0, z: 0}
    InitialLocalRotationInRoot: {x: 0, y: 0, z: 0, w: 1}
    LocalOffset: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: 0, y: 0, z: 0, w: 1}
    ColliderRadius: 1
    CollisionContactFlag: 0
    CollisionContactRelevancy: -1
    VelocityHelper: {x: 0, y: 0, z: 0}
    QVelocityHelper: {x: 0, y: 0, z: 0, w: 1}
    PreviousPush: {x: 0, y: 0, z: 0}
    Curving: {x: 0, y: 0, z: 0, w: 1}
    Gravity: {x: 0, y: 0, z: 0}
    GravityLookOffset: {x: 0, y: 0, z: 0}
    LengthMultiplier: 1
    PositionSpeed: 1
    RotationSpeed: 1
    Springiness: 0
    Slithery: 1
    Curling: 0.5
    Slippery: 1
    LastKeyframeLocalRotation: {x: 0, y: 0, z: 0, w: 0}
    LastKeyframeLocalPosition: {x: 0, y: 0, z: 0}
  GhostChild:
    ProceduralPosition: {x: 0, y: 0, z: 0}
    ProceduralPositionWeightBlended: {x: 0, y: 0, z: 0}
    TrueTargetRotation: {x: 0, y: 0, z: 0, w: 1}
    PosRefRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosReferenceRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosition: {x: 0, y: 0, z: 0}
    BlendValue: 1
    BoneDimensionsScaled: {x: 0, y: 0, z: 0}
    BoneLengthScaled: 0
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalPositionInRoot: {x: 0, y: 0, z: 0}
    InitialLocalRotationInRoot: {x: 0, y: 0, z: 0, w: 1}
    LocalOffset: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: 0, y: 0, z: 0, w: 1}
    ColliderRadius: 1
    CollisionContactFlag: 0
    CollisionContactRelevancy: -1
    VelocityHelper: {x: 0, y: 0, z: 0}
    QVelocityHelper: {x: 0, y: 0, z: 0, w: 1}
    PreviousPush: {x: 0, y: 0, z: 0}
    Curving: {x: 0, y: 0, z: 0, w: 1}
    Gravity: {x: 0, y: 0, z: 0}
    GravityLookOffset: {x: 0, y: 0, z: 0}
    LengthMultiplier: 1
    PositionSpeed: 1
    RotationSpeed: 1
    Springiness: 0
    Slithery: 1
    Curling: 0.5
    Slippery: 1
    LastKeyframeLocalRotation: {x: 0, y: 0, z: 0, w: 0}
    LastKeyframeLocalPosition: {x: 0, y: 0, z: 0}
  UseMaxDistance: 0
  DistanceFrom: {fileID: 0}
  _distanceFrom_Auto: {fileID: 0}
  MaximumDistance: 35
  MaxOutDistanceFactor: 0
  DistanceWithoutY: 0
  DistanceMeasurePoint: {x: 0, y: 0, z: 0}
  FadeDuration: 0.75
  Deflection: 0
  DeflectionStartAngle: 10
  DeflectionSmooth: 0
  DeflectionFalloff:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  DeflectOnlyCollisions: 1
  _Editor_Category: 0
  _Editor_FeaturesCategory: 0
  DrawGizmos: 1
  StartBone: {fileID: 4114351381275056}
  EndBone: {fileID: 0}
  EndBoneJointOffset: {x: 0, y: 0, z: 0}
  _TransformsGhostChain:
  - {fileID: 4114351381275056}
  - {fileID: 4948753678086262}
  - {fileID: 4256239339913762}
  _GhostChainInitCount: 3
  UpdateRate: 0
  AnimatePhysics: 0
  InterpolateRate: 0
  Prewarm: 0
  Slithery: 1
  Curling: 0.75
  Springiness: 0
  MaxStretching: 1
  AngleLimit: 181
  AngleLimitAxis: {x: 0, y: 0, z: 0}
  LimitAxisRange: {x: 0, y: 0}
  LimitSmoothing: 0.5
  MotionInfluence: 1
  MotionInfluenceInY: 1
  IncludeParent: 0
  UnifyBendiness: 0
  ReactionSpeed: 0.3
  Sustain: 0
  RotationRelevancy: 0.8
  SmoothingStyle: 0
  TimeScale: 1
  DeltaType: 4
  UpdateAsLast: 0
  DetectZeroKeyframes: 1
  StartAfterTPose: 1
  OptimizeWithMesh: {fileID: 0}
  OptimizeWithMeshes: []
  TailAnimatorAmount: 1
  DetachChildren: 0
  Axis2D: 0
  Tangle: 0
  AnimateRoll: 0
  OverrideKeyframeAnimation: 0
