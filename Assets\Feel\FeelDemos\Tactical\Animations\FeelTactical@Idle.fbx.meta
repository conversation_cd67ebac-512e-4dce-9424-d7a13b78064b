fileFormatVersion: 2
guid: ae6eb209df497b846a8d698d0d2c07c2
ModelImporter:
  serializedVersion: 19301
  internalIDToNameTable:
  - first:
      74: -203655887218126122
    second: Tactical@Idle
  externalObjects: {}
  materials:
    materialImportMode: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: Tactical@Idle
      takeName: mmntns.com
      internalID: 0
      firstFrame: 0
      lastFrame: 60
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 0
      loopBlendOrientation: 1
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 1
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    fileIdsGeneration: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    meshOptimizationFlags: -1
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human:
    - boneName: Hips
      humanName: Hips
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: ThighL
      humanName: LeftUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: ThighR
      humanName: RightUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: ShinL
      humanName: LeftLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: ShinR
      humanName: RightLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: FootL
      humanName: LeftFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: FootR
      humanName: RightFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Spine
      humanName: Spine
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Chest
      humanName: Chest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Neck
      humanName: Neck
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Head
      humanName: Head
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: ShoulderL
      humanName: LeftShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: ShoulderR
      humanName: RightShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: UpperArmL
      humanName: LeftUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: UpperArmR
      humanName: RightUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: ForeArmL
      humanName: LeftLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: ForeArmR
      humanName: RightLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandL
      humanName: LeftHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: HandR
      humanName: RightHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: ToeL
      humanName: LeftToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: ToeR
      humanName: RightToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Thumb01L
      humanName: Left Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Thumb02L
      humanName: Left Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Thumb03L
      humanName: Left Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Index01L
      humanName: Left Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Index02L
      humanName: Left Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Index03L
      humanName: Left Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Middle01L
      humanName: Left Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Middle02L
      humanName: Left Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Middle03L
      humanName: Left Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Ring01L
      humanName: Left Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Ring02L
      humanName: Left Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Ring03L
      humanName: Left Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Pinky01L
      humanName: Left Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Pinky02L
      humanName: Left Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Pinky03L
      humanName: Left Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Thumb01R
      humanName: Right Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Thumb02R
      humanName: Right Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Thumb03R
      humanName: Right Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Index01R
      humanName: Right Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Index02R
      humanName: Right Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Index03R
      humanName: Right Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Middle01R
      humanName: Right Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Middle02R
      humanName: Right Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Middle03R
      humanName: Right Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Ring01R
      humanName: Right Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Ring02R
      humanName: Right Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Ring03R
      humanName: Right Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Pinky01R
      humanName: Right Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Pinky02R
      humanName: Right Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Pinky03R
      humanName: Right Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    skeleton:
    - name: FBI@T-Pose(Clone)
      parentName: 
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Armature
      parentName: FBI@T-Pose(Clone)
      position: {x: 0.00000009157762, y: 0.028590163, z: -0.0136364335}
      rotation: {x: -0.7071067, y: -0.000000030323196, z: 0.000000028441063, w: 0.7071068}
      scale: {x: 99.99999, y: 99.99999, z: 99.99999}
    - name: Hips
      parentName: Armature
      position: {x: -2.6645365e-17, y: 0.0004663294, z: 0.009672329}
      rotation: {x: 0.8136232, y: 0, z: 0, w: 0.5813926}
      scale: {x: 1, y: 1, z: 1.0000001}
    - name: Spine
      parentName: Hips
      position: {x: 8.2620236e-17, y: 0.0017740728, z: 3.632158e-10}
      rotation: {x: -0.120176755, y: -0.0000000073379147, z: -0.000000040921215, w: 0.99275255}
      scale: {x: 1, y: 0.9999999, z: 0.99999994}
    - name: Chest
      parentName: Spine
      position: {x: -4.3950928e-17, y: 0.0015970987, z: 0}
      rotation: {x: -0.09394412, y: 6.161361e-35, z: 3.3235815e-36, w: 0.9955775}
      scale: {x: 1, y: 1, z: 1}
    - name: Neck
      parentName: Chest
      position: {x: 7.917067e-12, y: 0.003936316, z: -0.000031845495}
      rotation: {x: 0.1416969, y: 1.0642834e-34, z: 1.44573e-36, w: 0.9899101}
      scale: {x: 1, y: 0.99999994, z: 0.99999994}
    - name: Head
      parentName: Neck
      position: {x: 6.4392934e-17, y: 0.0009061008, z: 2.371962e-10}
      rotation: {x: -0.071768135, y: -1.0626672e-34, z: 6.038427e-36, w: 0.9974213}
      scale: {x: 1, y: 1.0000001, z: 1.0000001}
    - name: HeadEnd
      parentName: Head
      position: {x: 1.3322676e-17, y: 0.0020713496, z: 3.7252902e-11}
      rotation: {x: -0.020752221, y: 3.587499e-19, z: -7.446461e-21, w: 0.99978465}
      scale: {x: 1, y: 1, z: 1}
    - name: HeadEnd_end
      parentName: HeadEnd
      position: {x: -0, y: 0.0022671984, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: ShoulderR
      parentName: Chest
      position: {x: 0.000116892676, y: 0.003514681, z: 0.0005582076}
      rotation: {x: -0.5920387, y: -0.4108488, z: -0.44439572, w: 0.53217095}
      scale: {x: 0.9999997, y: 0.9999998, z: 0.9999995}
    - name: UpperArmR
      parentName: ShoulderR
      position: {x: 0.00009909787, y: 0.002443949, z: -0.000013490875}
      rotation: {x: 0.12899937, y: 0.7189969, z: -0.060219355, w: 0.68027663}
      scale: {x: 1.0000001, y: 1.0000005, z: 1}
    - name: ForeArmR
      parentName: UpperArmR
      position: {x: 4.1094608e-10, y: 0.002531569, z: 9.3132255e-12}
      rotation: {x: -0.0023226982, y: 0.11205292, z: -0.0029264672, w: 0.99369526}
      scale: {x: 0.99999994, y: 1, z: 1.0000004}
    - name: HandR
      parentName: ForeArmR
      position: {x: -0.000016202248, y: 0.0025250197, z: 0.0000100051}
      rotation: {x: -0.02045654, y: 0.83206856, z: -0.028009582, w: 0.5535873}
      scale: {x: 0.9999999, y: 0.99999994, z: 1}
    - name: Index01R
      parentName: HandR
      position: {x: -0.00028561708, y: 0.0010322109, z: -0.00011712358}
      rotation: {x: -0.019820483, y: 0.12885262, z: 0.030848498, w: 0.99098563}
      scale: {x: 0.99999994, y: 0.99999994, z: 0.99999994}
    - name: Index02R
      parentName: Index01R
      position: {x: -2.6077032e-10, y: 0.00040617693, z: -3.5390257e-10}
      rotation: {x: -0.000000010792177, y: 0.0066332556, z: 4.695864e-10, w: 0.999978}
      scale: {x: 1, y: 1.0000001, z: 1.0000001}
    - name: Index03R
      parentName: Index02R
      position: {x: -5.2154064e-10, y: 0.0004093155, z: -1.862645e-10}
      rotation: {x: -0.020588508, y: 0.026258707, z: -0.034315087, w: 0.9988539}
      scale: {x: 0.9999999, y: 0.99999994, z: 0.99999994}
    - name: Index04R
      parentName: Index03R
      position: {x: 9.313225e-11, y: 0.00024769592, z: -4.0978193e-10}
      rotation: {x: 0.038630538, y: 0.012175905, z: 0.07039292, w: 0.9966967}
      scale: {x: 1.0000001, y: 1.0000001, z: 1.0000002}
    - name: Index04R_end
      parentName: Index04R
      position: {x: -0, y: 0.0002456306, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Thumb01R
      parentName: HandR
      position: {x: -0.00038163192, y: 0.00026059122, z: -0.00017677115}
      rotation: {x: 0.013060134, y: -0.35298258, z: 0.38920477, w: 0.85073644}
      scale: {x: 0.99999994, y: 1.0000001, z: 1.0000001}
    - name: Thumb02R
      parentName: Thumb01R
      position: {x: 0.0000000010803342, y: 0.00031308478, z: 4.6566126e-11}
      rotation: {x: 0.020050624, y: 0.68878764, z: 0.011442896, w: 0.7245955}
      scale: {x: 0.9999998, y: 0.99999994, z: 1}
    - name: Thumb03R
      parentName: Thumb02R
      position: {x: 6.705522e-10, y: 0.0004540968, z: -9.313226e-10}
      rotation: {x: 0.003495491, y: 0.3699681, z: -0.040029127, w: 0.9281751}
      scale: {x: 1, y: 1, z: 1}
    - name: Thumb04R
      parentName: Thumb03R
      position: {x: -8.5681673e-10, y: 0.00022753484, z: 1.4901161e-10}
      rotation: {x: -0.015391988, y: -0.12483281, z: 0.043355327, w: 0.99111056}
      scale: {x: 0.99999994, y: 0.99999994, z: 0.99999994}
    - name: Thumb04R_end
      parentName: Thumb04R
      position: {x: -0, y: 0.00026071083, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Middle01R
      parentName: HandR
      position: {x: -0.00007389575, y: 0.0011141185, z: -0.00003536609}
      rotation: {x: -0.02008675, y: 0.13742724, z: 0.030675711, w: 0.989833}
      scale: {x: 0.99999994, y: 0.9999998, z: 1}
    - name: Middle02R
      parentName: Middle01R
      position: {x: 4.749745e-10, y: 0.00038510642, z: -4.1443854e-10}
      rotation: {x: -0.000000041605414, y: -0.016972406, z: 0.00000003011201, w: 0.999856}
      scale: {x: 1, y: 0.9999999, z: 0.9999999}
    - name: Middle03R
      parentName: Middle02R
      position: {x: -2.3283064e-10, y: 0.00041840455, z: 9.313225e-11}
      rotation: {x: -0.03618261, y: 0.013476939, z: -0.018897653, w: 0.99907565}
      scale: {x: 0.99999994, y: 0.99999994, z: 1}
    - name: Middle04R
      parentName: Middle03R
      position: {x: 1.2107193e-10, y: 0.00023727534, z: 1.862645e-10}
      rotation: {x: 0.06738001, y: -0.008986994, z: 0.035576165, w: 0.99705243}
      scale: {x: 1, y: 1.0000001, z: 1}
    - name: Middle04R_end
      parentName: Middle04R
      position: {x: -0, y: 0.00027335365, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Ring01R
      parentName: HandR
      position: {x: 0.000116048155, y: 0.001017896, z: 0.00012267179}
      rotation: {x: -0.018645858, y: 0.0914769, z: 0.031572435, w: 0.9951319}
      scale: {x: 0.99999994, y: 1, z: 0.99999994}
    - name: Ring02R
      parentName: Ring01R
      position: {x: 9.685754e-10, y: 0.00039699386, z: -1.9557773e-10}
      rotation: {x: -0.000000008297126, y: -0.015424286, z: -0.000000029891304, w: 0.9998811}
      scale: {x: 1, y: 0.9999999, z: 1}
    - name: Ring03R
      parentName: Ring02R
      position: {x: 1.6763806e-10, y: 0.00037663954, z: 2.1420418e-10}
      rotation: {x: -0.0077162404, y: 0.034301005, z: -0.0075252103, w: 0.99935347}
      scale: {x: 1, y: 1, z: 1}
    - name: Ring04R
      parentName: Ring03R
      position: {x: -2.0809238e-11, y: 0.00028894277, z: 1.7695129e-10}
      rotation: {x: 0.02118189, y: -0.012224153, z: 0.021590257, w: 0.99946773}
      scale: {x: 1.0000001, y: 0.99999994, z: 1.0000001}
    - name: Ring04R_end
      parentName: Ring04R
      position: {x: -0, y: 0.00016022517, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Pinky01R
      parentName: HandR
      position: {x: 0.00029777887, y: 0.0010284443, z: 0.00022380528}
      rotation: {x: -0.017703185, y: 0.06198399, z: 0.032110367, w: 0.9974034}
      scale: {x: 0.9999999, y: 1, z: 0.99999994}
    - name: Pinky02R
      parentName: Pinky01R
      position: {x: -1.4901161e-10, y: 0.0003140823, z: -5.587935e-10}
      rotation: {x: -0.000000049445333, y: 0.074548125, z: 0.00000010001789, w: 0.9972174}
      scale: {x: 1, y: 1.0000001, z: 1}
    - name: Pinky03R
      parentName: Pinky02R
      position: {x: 1.8626451e-11, y: 0.0002910918, z: 4.656613e-10}
      rotation: {x: -0.033065077, y: -0.0001870319, z: -0.023792349, w: 0.99916995}
      scale: {x: 1, y: 0.99999994, z: 1}
    - name: Pinky04R
      parentName: Pinky03R
      position: {x: 2.6077032e-10, y: 0.00018836086, z: -2.7939676e-10}
      rotation: {x: 0.09533585, y: -0.011481738, z: 0.06691291, w: 0.99312735}
      scale: {x: 1, y: 0.99999994, z: 1}
    - name: Pinky04R_end
      parentName: Pinky04R
      position: {x: -0, y: 0.00018440654, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: ShoulderL
      parentName: Chest
      position: {x: -0.00011689303, y: 0.003514681, z: 0.00055820757}
      rotation: {x: -0.59203905, y: 0.41084844, z: 0.4443951, w: 0.5321713}
      scale: {x: 0.9999999, y: 0.99999994, z: 0.99999994}
    - name: UpperArmL
      parentName: ShoulderL
      position: {x: -0.00009909766, y: 0.002443949, z: -0.000013490378}
      rotation: {x: 0.12899972, y: -0.71899647, z: 0.060219966, w: 0.68027693}
      scale: {x: 1.0000001, y: 1.0000002, z: 1.0000001}
    - name: ForeArmL
      parentName: UpperArmL
      position: {x: 8.259667e-10, y: 0.0025315671, z: 4.6566126e-11}
      rotation: {x: -0.0023226994, y: -0.11205292, z: 0.0029264484, w: 0.99369526}
      scale: {x: 1, y: 0.9999999, z: 1}
    - name: HandL
      parentName: ForeArmL
      position: {x: 0.000016198079, y: 0.002525019, z: 0.000010006055}
      rotation: {x: -0.020456545, y: -0.8320689, z: 0.028009588, w: 0.5535868}
      scale: {x: 1, y: 0.99999976, z: 1}
    - name: Index01L
      parentName: HandL
      position: {x: 0.00028561734, y: 0.0010322096, z: -0.0001171242}
      rotation: {x: -0.019820502, y: -0.12885264, z: -0.030848494, w: 0.99098563}
      scale: {x: 0.99999994, y: 1, z: 0.99999994}
    - name: Index02L
      parentName: Index01L
      position: {x: 0.0000000013038516, y: 0.00040617745, z: -4.2840836e-10}
      rotation: {x: -3.952603e-12, y: -0.0066341185, z: -5.9578614e-10, w: 0.999978}
      scale: {x: 1, y: 1.0000001, z: 0.99999994}
    - name: Index03L
      parentName: Index02L
      position: {x: -1.862645e-10, y: 0.00040931537, z: -8.1956386e-10}
      rotation: {x: -0.020588629, y: -0.026258683, z: 0.03431502, w: 0.9988539}
      scale: {x: 1.0000001, y: 0.99999994, z: 1}
    - name: Index04L
      parentName: Index03L
      position: {x: -3.352761e-10, y: 0.00024769554, z: 1.11758706e-10}
      rotation: {x: 0.038630538, y: -0.012175905, z: -0.07039292, w: 0.9966967}
      scale: {x: 1, y: 0.99999994, z: 0.9999999}
    - name: Index04L_end
      parentName: Index04L
      position: {x: -0, y: 0.0002456306, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Thumb01L
      parentName: HandL
      position: {x: 0.00038163183, y: 0.00026058956, z: -0.00017677188}
      rotation: {x: 0.0130616715, y: 0.352983, z: -0.3892019, w: 0.8507375}
      scale: {x: 1, y: 1, z: 0.99999994}
    - name: Thumb02L
      parentName: Thumb01L
      position: {x: 2.9802322e-10, y: 0.00031308504, z: 2.6077032e-10}
      rotation: {x: 0.020050302, y: -0.68878764, z: -0.011443233, w: 0.7245955}
      scale: {x: 1, y: 1, z: 1}
    - name: Thumb03L
      parentName: Thumb02L
      position: {x: 5.122274e-10, y: 0.00045409694, z: -6.1467287e-10}
      rotation: {x: 0.00349541, y: -0.36996827, z: 0.040017705, w: 0.9281755}
      scale: {x: 1, y: 1.0000001, z: 1}
    - name: Thumb04L
      parentName: Thumb03L
      position: {x: -3.352761e-10, y: 0.00022753536, z: -2.9802322e-10}
      rotation: {x: -0.015391965, y: 0.12483287, z: -0.043355346, w: 0.99111056}
      scale: {x: 1, y: 0.99999994, z: 1}
    - name: Thumb04L_end
      parentName: Thumb04L
      position: {x: -0, y: 0.00026071083, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Middle01L
      parentName: HandL
      position: {x: 0.00007389569, y: 0.0011141173, z: -0.000035366473}
      rotation: {x: -0.02008676, y: -0.1374272, z: -0.030675706, w: 0.989833}
      scale: {x: 1, y: 0.99999994, z: 0.9999999}
    - name: Middle02L
      parentName: Middle01L
      position: {x: -8.0559404e-10, y: 0.000385107, z: 0.0000000011688098}
      rotation: {x: -0.000000021922647, y: 0.016972352, z: -6.394985e-10, w: 0.999856}
      scale: {x: 1, y: 1, z: 1}
    - name: Middle03L
      parentName: Middle02L
      position: {x: 4.0978193e-10, y: 0.0004184044, z: 5.587935e-10}
      rotation: {x: -0.036182676, y: -0.013476853, z: 0.018897567, w: 0.99907565}
      scale: {x: 0.9999999, y: 1, z: 1}
    - name: Middle04L
      parentName: Middle03L
      position: {x: -7.264316e-10, y: 0.00023727375, z: 4.4703483e-10}
      rotation: {x: 0.06738001, y: 0.008986994, z: -0.035576165, w: 0.99705243}
      scale: {x: 0.9999999, y: 1, z: 0.99999994}
    - name: Middle04L_end
      parentName: Middle04L
      position: {x: -0, y: 0.00027335365, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Ring01L
      parentName: HandL
      position: {x: -0.00011604821, y: 0.0010178946, z: 0.00012267102}
      rotation: {x: -0.018645868, y: -0.09147686, z: -0.03157243, w: 0.9951319}
      scale: {x: 1.0000001, y: 1.0000001, z: 0.99999994}
    - name: Ring02L
      parentName: Ring01L
      position: {x: -2.1420418e-10, y: 0.00039699508, z: 6.7986544e-10}
      rotation: {x: -0.000000008297126, y: 0.015424286, z: 0.000000029891304, w: 0.9998811}
      scale: {x: 1, y: 0.99999994, z: 1}
    - name: Ring03L
      parentName: Ring02L
      position: {x: 3.9115547e-10, y: 0.0003766391, z: -8.1025064e-10}
      rotation: {x: -0.0077162385, y: -0.03430101, z: 0.0075252703, w: 0.99935347}
      scale: {x: 0.9999999, y: 0.99999994, z: 1.0000001}
    - name: Ring04L
      parentName: Ring03L
      position: {x: -1.2194505e-10, y: 0.00028894332, z: -5.9604643e-10}
      rotation: {x: 0.02118189, y: 0.012224153, z: -0.021590257, w: 0.99946773}
      scale: {x: 1.0000002, y: 1.0000001, z: 1.0000001}
    - name: Ring04L_end
      parentName: Ring04L
      position: {x: -0, y: 0.00016022517, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Pinky01L
      parentName: HandL
      position: {x: -0.00029777834, y: 0.0010284436, z: 0.00022380566}
      rotation: {x: -0.017703213, y: -0.061983917, z: -0.032110397, w: 0.9974034}
      scale: {x: 1, y: 1, z: 0.99999994}
    - name: Pinky02L
      parentName: Pinky01L
      position: {x: -4.842877e-10, y: 0.00031408298, z: 4.656613e-10}
      rotation: {x: -0.0000000072818205, y: -0.07454819, z: -0.000000035205424, w: 0.9972174}
      scale: {x: 0.9999999, y: 0.99999994, z: 1}
    - name: Pinky03L
      parentName: Pinky02L
      position: {x: -2.9802322e-10, y: 0.00029109116, z: -1.3038516e-10}
      rotation: {x: -0.03306208, y: 0.0001870095, z: 0.023790067, w: 0.9991702}
      scale: {x: 0.99999994, y: 0.9999999, z: 0.9999999}
    - name: Pinky04L
      parentName: Pinky03L
      position: {x: 5.5879353e-11, y: 0.00018835907, z: -7.4505804e-11}
      rotation: {x: 0.09533585, y: 0.011481738, z: -0.06691291, w: 0.99312735}
      scale: {x: 0.99999994, y: 0.99999994, z: 0.9999999}
    - name: Pinky04L_end
      parentName: Pinky04L
      position: {x: -0, y: 0.00018440654, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: ThighR
      parentName: Hips
      position: {x: 0.00091688416, y: 0.00047229955, z: 0.0004797937}
      rotation: {x: 0.98856306, y: 0.007757289, z: -0.0041892384, w: 0.15055023}
      scale: {x: 0.9999995, y: 0.9999999, z: 0.9999997}
    - name: ShinR
      parentName: ThighR
      position: {x: 8.3018675e-11, y: 0.0040789396, z: -1.3969838e-11}
      rotation: {x: 0.004052175, y: -0.0028137213, z: 0.0152760595, w: 0.9998712}
      scale: {x: 1, y: 1.0000001, z: 0.99999994}
    - name: FootR
      parentName: ShinR
      position: {x: 6.984919e-11, y: 0.0048280326, z: 9.3132255e-12}
      rotation: {x: -0.38521805, y: -0.08794435, z: -0.00051841536, w: 0.9186254}
      scale: {x: 1, y: 1.0000001, z: 0.99999994}
    - name: ToeR
      parentName: FootR
      position: {x: 2.712477e-10, y: 0.001702583, z: -7.916242e-11}
      rotation: {x: 0.006153901, y: 0.9165084, z: -0.39497915, w: -0.06297645}
      scale: {x: 0.99999994, y: 1.0000005, z: 0.99999994}
    - name: ToeREnd
      parentName: ToeR
      position: {x: 8.731149e-11, y: 0.0008828195, z: -1.6298145e-11}
      rotation: {x: 0.0006853406, y: -0.13332559, z: -0.0061928765, w: 0.99105275}
      scale: {x: 0.99999994, y: 0.99999994, z: 0.99999994}
    - name: ToeREnd_end
      parentName: ToeREnd
      position: {x: -0, y: 0.0014088419, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: ThighL
      parentName: Hips
      position: {x: -0.000916884, y: 0.0004722996, z: 0.00047979414}
      rotation: {x: 0.9886036, y: -0.0077585448, z: 0.0041871476, w: 0.1502838}
      scale: {x: 0.9999994, y: 0.99999994, z: 0.9999996}
    - name: ShinL
      parentName: ThighL
      position: {x: 7.4869604e-11, y: 0.0040789405, z: 9.3132255e-12}
      rotation: {x: 0.0035542853, y: 0.0028175644, z: -0.015280476, w: 0.999873}
      scale: {x: 0.99999994, y: 1, z: 1}
    - name: FootL
      parentName: ShinL
      position: {x: -7.21775e-11, y: 0.0048280316, z: -6.519258e-11}
      rotation: {x: -0.38500923, y: 0.08794298, z: 0.0005407761, w: 0.91871303}
      scale: {x: 1, y: 1.0000001, z: 1}
    - name: ToeL
      parentName: FootL
      position: {x: -2.3457686e-10, y: 0.0017025834, z: -9.313225e-11}
      rotation: {x: -0.0061540147, y: 0.9165083, z: -0.39497927, w: 0.06297651}
      scale: {x: 0.99999994, y: 1.0000006, z: 0.99999994}
    - name: ToeL_end
      parentName: ToeL
      position: {x: -0, y: 0.0008828196, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Body
      parentName: FBI@T-Pose(Clone)
      position: {x: -0, y: 2.4124253, z: -0.29125553}
      rotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071067}
      scale: {x: 34.603848, y: 34.603848, z: 34.603848}
    - name: Glasses
      parentName: Body
      position: {x: -0.00002461353, y: -0.011452343, z: -0.016487064}
      rotation: {x: 0.7071069, y: 0, z: -0, w: 0.7071067}
      scale: {x: 0.01536218, y: 0.01536218, z: 0.01536218}
    - name: Eyes
      parentName: Body
      position: {x: -0, y: -0.00020233452, z: 0.00034770145}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Eyebrows
      parentName: Body
      position: {x: -0, y: -0.008416844, z: -0.06961862}
      rotation: {x: 0.00000008146034, y: 0, z: -0, w: 1}
      scale: {x: 2.8898523, y: 2.8898523, z: 2.8898523}
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 1
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {fileID: 9000000, guid: 5c442f9e667c3ac4089fc4321d655698,
    type: 3}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 3
  humanoidOversampling: 1
  avatarSetup: 2
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
AssetOrigin:
  serializedVersion: 1
  productId: 183370
  packageName: Feel
  packageVersion: 5.5
  assetPath: Assets/Feel/FeelDemos/Tactical/Animations/<EMAIL>
  uploadId: 759320
