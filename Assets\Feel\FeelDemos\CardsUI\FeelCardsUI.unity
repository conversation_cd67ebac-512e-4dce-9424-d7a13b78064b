%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 9
  m_Fog: 1
  m_FogColor: {r: 0, g: 0, b: 0, a: 0}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.2, g: 0.2, b: 0.2, a: 1}
  m_AmbientEquatorColor: {r: 0.2, g: 0.2, b: 0.2, a: 1}
  m_AmbientGroundColor: {r: 0.2, g: 0.2, b: 0.2, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 3
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_IndirectSpecularColor: {r: 0, g: 0, b: 0, a: 1}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &4
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 12
  m_GIWorkflowMode: 1
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 1
    m_EnableRealtimeLightmaps: 0
  m_LightmapEditorSettings:
    serializedVersion: 12
    m_Resolution: 1
    m_BakeResolution: 50
    m_AtlasSize: 1024
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 0
    m_CompAOExponentDirect: 0
    m_ExtractAmbientOcclusion: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 0
    m_FinalGather: 0
    m_FinalGatherFiltering: 1
    m_FinalGatherRayCount: 1024
    m_ReflectionCompression: 2
    m_MixedBakeMode: 1
    m_BakeBackend: 0
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 500
    m_PVRBounces: 2
    m_PVREnvironmentSampleCount: 500
    m_PVREnvironmentReferencePointCount: 2048
    m_PVRFilteringMode: 2
    m_PVRDenoiserTypeDirect: 0
    m_PVRDenoiserTypeIndirect: 0
    m_PVRDenoiserTypeAO: 0
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVREnvironmentMIS: 0
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ExportTrainingData: 0
    m_TrainingDataDestination: TrainingData
    m_LightProbeSampleCountMultiplier: 4
  m_LightingDataAsset: {fileID: 0}
  m_LightingSettings: {fileID: 4890085278179872738, guid: c109e1b1cadedb64686484f1dcb6de6a,
    type: 2}
--- !u!196 &5
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 3
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666666
    manualTileSize: 0
    tileSize: 256
    buildHeightMesh: 0
    maxJobWorkers: 0
    preserveTilesOutsideBounds: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!1 &4684824
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4684827}
  - component: {fileID: 4684826}
  m_Layer: 0
  m_Name: EventSystem
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &4684826
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4684824}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 76c392e42b5098c458856cdf6ecaaaa1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_FirstSelected: {fileID: 0}
  m_sendNavigationEvents: 1
  m_DragThreshold: 10
--- !u!4 &4684827
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4684824}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -147.91467, y: 44.754505, z: 38.78494}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &146825166 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 427014296157349933, guid: ef1f777facc2a024c8bc6aecad8c85c1,
    type: 3}
  m_PrefabInstance: {fileID: 331905144}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6da43522623d4704e979466dc7650b65, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1001 &182199741
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 394108091, guid: a5ed96966948b2348b46dab87f87bbcf, type: 3}
      propertyPath: MobileText
      value: Tap on the cards
      objectReference: {fileID: 0}
    - target: {fileID: 394108091, guid: a5ed96966948b2348b46dab87f87bbcf, type: 3}
      propertyPath: DesktopText
      value: Click on the cards
      objectReference: {fileID: 0}
    - target: {fileID: 3209280001990082502, guid: a5ed96966948b2348b46dab87f87bbcf,
        type: 3}
      propertyPath: managedReferences[*******************].DestinationSceneName
      value: FeelBrass
      objectReference: {fileID: 0}
    - target: {fileID: 8516721323258378249, guid: a5ed96966948b2348b46dab87f87bbcf,
        type: 3}
      propertyPath: m_Pivot.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8516721323258378249, guid: a5ed96966948b2348b46dab87f87bbcf,
        type: 3}
      propertyPath: m_Pivot.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8516721323258378249, guid: a5ed96966948b2348b46dab87f87bbcf,
        type: 3}
      propertyPath: m_RootOrder
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 8516721323258378249, guid: a5ed96966948b2348b46dab87f87bbcf,
        type: 3}
      propertyPath: m_AnchorMax.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8516721323258378249, guid: a5ed96966948b2348b46dab87f87bbcf,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8516721323258378249, guid: a5ed96966948b2348b46dab87f87bbcf,
        type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8516721323258378249, guid: a5ed96966948b2348b46dab87f87bbcf,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8516721323258378249, guid: a5ed96966948b2348b46dab87f87bbcf,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8516721323258378249, guid: a5ed96966948b2348b46dab87f87bbcf,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8516721323258378249, guid: a5ed96966948b2348b46dab87f87bbcf,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8516721323258378249, guid: a5ed96966948b2348b46dab87f87bbcf,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8516721323258378249, guid: a5ed96966948b2348b46dab87f87bbcf,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8516721323258378249, guid: a5ed96966948b2348b46dab87f87bbcf,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8516721323258378249, guid: a5ed96966948b2348b46dab87f87bbcf,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8516721323258378249, guid: a5ed96966948b2348b46dab87f87bbcf,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8516721323258378249, guid: a5ed96966948b2348b46dab87f87bbcf,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8516721323258378249, guid: a5ed96966948b2348b46dab87f87bbcf,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8516721323258378249, guid: a5ed96966948b2348b46dab87f87bbcf,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8516721323258378249, guid: a5ed96966948b2348b46dab87f87bbcf,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8516721323258378249, guid: a5ed96966948b2348b46dab87f87bbcf,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8516721323258378249, guid: a5ed96966948b2348b46dab87f87bbcf,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8516721323258378253, guid: a5ed96966948b2348b46dab87f87bbcf,
        type: 3}
      propertyPath: m_Name
      value: NextDemoCanvas
      objectReference: {fileID: 0}
    - target: {fileID: 8516721323668150470, guid: a5ed96966948b2348b46dab87f87bbcf,
        type: 3}
      propertyPath: DestinationSceneName
      value: FeelBrass
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: a5ed96966948b2348b46dab87f87bbcf, type: 3}
--- !u!1 &251850590
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 251850594}
  - component: {fileID: 251850593}
  - component: {fileID: 251850592}
  - component: {fileID: 251850591}
  - component: {fileID: 251850597}
  - component: {fileID: 251850596}
  - component: {fileID: 251850595}
  m_Layer: 0
  m_Name: PostProcessing
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &251850591
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 251850590}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 04189ce672fd8c349b61eb900b1dc0ec, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  ChannelMode: 0
  Channel: 0
  MMChannelDefinition: {fileID: 0}
  ShakeDuration: 0.2
  PlayOnAwake: 0
  PermanentShake: 0
  Interruptible: 1
  AlwaysResetTargetValuesAfterShake: 0
  OnlyUseShakerValues: 0
  CooldownBetweenShakes: 0
  Shaking: 0
  ForwardDirection: 1
  TimescaleMode: 0
  RelativeValues: 1
  ShakeIntensity:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 0.5
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  RemapIntensityZero: 0
  RemapIntensityOne: 10
  ShakeThreshold:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 0.5
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  RemapThresholdZero: 0
  RemapThresholdOne: 0
--- !u!114 &251850592
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 251850590}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 81cf12de3b3c98d489779a3382046725, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  ChannelMode: 0
  Channel: 0
  MMChannelDefinition: {fileID: 0}
  ShakeDuration: 0.2
  PlayOnAwake: 0
  PermanentShake: 0
  Interruptible: 1
  AlwaysResetTargetValuesAfterShake: 0
  OnlyUseShakerValues: 0
  CooldownBetweenShakes: 0
  Shaking: 0
  ForwardDirection: 1
  TimescaleMode: 0
  RelativeIntensity: 1
  ShakeIntensity:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 0.5
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  RemapIntensityZero: 0
  RemapIntensityOne: 0.1
  InterpolateColor: 0
  ColorCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 0.05
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 0.95
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  RemapColorZero: 0
  RemapColorOne: 1
  TargetColor: {r: 1, g: 0, b: 0, a: 1}
--- !u!114 &251850593
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 251850590}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8b9a305e18de0c04dbd257a21cd47087, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  sharedProfile: {fileID: 11400000, guid: ca84483515897314ab34ade6c928d2ca, type: 2}
  isGlobal: 1
  blendDistance: 0
  weight: 1
  priority: 0
--- !u!4 &251850594
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 251850590}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -149.58199, y: 47.303776, z: 73.82589}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &251850595
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 251850590}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7efc6a4a233d87b4fbc72cda82ca4a31, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  ChannelMode: 0
  Channel: 0
  MMChannelDefinition: {fileID: 0}
  ShakeDuration: 0.8
  PlayOnAwake: 0
  PermanentShake: 0
  Interruptible: 1
  AlwaysResetTargetValuesAfterShake: 0
  OnlyUseShakerValues: 0
  CooldownBetweenShakes: 0
  Shaking: 0
  ForwardDirection: 1
  TimescaleMode: 0
  RelativeIntensity: 0
  ShakeIntensity:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 0.2
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 0.25
      value: -1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 0.35
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 0.4
      value: -0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 0.6
      value: 0.3
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 0.65
      value: -0.3
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 0.8
      value: 0.1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 0.85
      value: -0.1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  RemapIntensityZero: 0
  RemapIntensityOne: 50
--- !u!114 &251850596
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 251850590}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 416f266b197306a49975dadec6e05526, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  ChannelMode: 0
  Channel: 0
  MMChannelDefinition: {fileID: 0}
  ShakeDuration: 0.8
  PlayOnAwake: 0
  PermanentShake: 0
  Interruptible: 1
  AlwaysResetTargetValuesAfterShake: 0
  OnlyUseShakerValues: 0
  CooldownBetweenShakes: 0
  Shaking: 0
  ForwardDirection: 1
  TimescaleMode: 0
  RelativeValues: 1
  ShakePostExposure:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 0.5
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  RemapPostExposureZero: 0
  RemapPostExposureOne: 1
  ShakeHueShift:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 0.5
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  RemapHueShiftZero: 0
  RemapHueShiftOne: 180
  ShakeSaturation:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 0.5
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  RemapSaturationZero: 0
  RemapSaturationOne: 100
  ShakeContrast:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 0.5
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  RemapContrastZero: 0
  RemapContrastOne: 100
  ShakeColorFilter: 0
  ColorFilterGradient:
    serializedVersion: 2
    key0: {r: 1, g: 1, b: 1, a: 1}
    key1: {r: 1, g: 1, b: 1, a: 1}
    key2: {r: 0, g: 0, b: 0, a: 0}
    key3: {r: 0, g: 0, b: 0, a: 0}
    key4: {r: 0, g: 0, b: 0, a: 0}
    key5: {r: 0, g: 0, b: 0, a: 0}
    key6: {r: 0, g: 0, b: 0, a: 0}
    key7: {r: 0, g: 0, b: 0, a: 0}
    ctime0: 0
    ctime1: 65535
    ctime2: 0
    ctime3: 0
    ctime4: 0
    ctime5: 0
    ctime6: 0
    ctime7: 0
    atime0: 0
    atime1: 65535
    atime2: 0
    atime3: 0
    atime4: 0
    atime5: 0
    atime6: 0
    atime7: 0
    m_Mode: 0
    m_ColorSpace: -1
    m_NumColorKeys: 2
    m_NumAlphaKeys: 2
--- !u!114 &251850597
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 251850590}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1297b688774526f418895a2f09176619, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  ChannelMode: 0
  Channel: 0
  MMChannelDefinition: {fileID: 0}
  ShakeDuration: 0.2
  PlayOnAwake: 0
  PermanentShake: 0
  Interruptible: 1
  AlwaysResetTargetValuesAfterShake: 0
  OnlyUseShakerValues: 0
  CooldownBetweenShakes: 0
  Shaking: 0
  ForwardDirection: 1
  TimescaleMode: 0
  RelativeIntensity: 0
  ShakeIntensity:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 0.5
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  RemapIntensityZero: 0
  RemapIntensityOne: 1
--- !u!1001 &331905144
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 2084567609}
    m_Modifications:
    - target: {fileID: 6341985375202562952, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: TargetGameObject
      value: 
      objectReference: {fileID: 1703243937}
    - target: {fileID: 7465677149413967465, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_textInfo.lineCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149413967465, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_textInfo.pageCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149413967465, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_textInfo.wordCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149413967465, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_textInfo.characterCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185876, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_Name
      value: CardsUI_Card_Lady
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185876, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_RootOrder
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_AnchorMax.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_AnchorMin.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 720
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 1024
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_LocalScale.x
      value: -0.8
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_LocalScale.y
      value: 0.8
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.9961947
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0.08715578
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 1062.9994
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -207.99973
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 10
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150045463348, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_textInfo.lineCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150045463348, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_textInfo.pageCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150045463348, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_textInfo.wordCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150045463348, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_textInfo.characterCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150062058218, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150168234784, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_textInfo.lineCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150168234784, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_textInfo.pageCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150168234784, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_textInfo.wordCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150168234784, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_textInfo.spaceCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150168234784, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_textInfo.characterCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150184380004, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_textInfo.lineCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150184380004, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_textInfo.pageCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150184380004, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_textInfo.wordCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150184380004, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_textInfo.spaceCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150184380004, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_textInfo.characterCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150323484100, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_textInfo.lineCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150323484100, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_textInfo.pageCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150323484100, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_textInfo.wordCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150323484100, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_textInfo.spaceCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150323484100, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_textInfo.characterCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150507819257, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150527117204, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_textInfo.lineCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150527117204, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_textInfo.pageCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150527117204, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_textInfo.wordCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150527117204, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_textInfo.spaceCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150527117204, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_textInfo.characterCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150731463991, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_textInfo.lineCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150731463991, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_textInfo.pageCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150731463991, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_textInfo.wordCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150731463991, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_textInfo.characterCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150923398953, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_textInfo.lineCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150923398953, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_textInfo.pageCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150923398953, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_textInfo.wordCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150923398953, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_textInfo.characterCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150988669718, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_textInfo.lineCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150988669718, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_textInfo.pageCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150988669718, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_textInfo.wordCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150988669718, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: m_textInfo.characterCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8787838387538572279, guid: ef1f777facc2a024c8bc6aecad8c85c1,
        type: 3}
      propertyPath: TargetGameObject
      value: 
      objectReference: {fileID: 1703243937}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: ef1f777facc2a024c8bc6aecad8c85c1, type: 3}
--- !u!224 &331905145 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 7465677149715185877, guid: ef1f777facc2a024c8bc6aecad8c85c1,
    type: 3}
  m_PrefabInstance: {fileID: 331905144}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &332578521 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 427014296157349933, guid: 7fc69917d0d06bc45b69968dd837d42b,
    type: 3}
  m_PrefabInstance: {fileID: 402199376}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6da43522623d4704e979466dc7650b65, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1001 &402199376
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 2084567609}
    m_Modifications:
    - target: {fileID: 6341985375202562952, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: TargetGameObject
      value: 
      objectReference: {fileID: 1703243937}
    - target: {fileID: 7465677149413967465, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_textInfo.lineCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149413967465, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_textInfo.pageCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149413967465, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_textInfo.wordCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149413967465, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_textInfo.characterCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185876, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_Name
      value: CardsUI_Card_Stormeye
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185876, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_RootOrder
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_AnchorMax.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_AnchorMin.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 720
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 1024
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_LocalScale.x
      value: -0.8
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_LocalScale.y
      value: 0.8
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.9990483
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0.043619405
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 1177.9994
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -222.99973
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: -5
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150045463348, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_textInfo.lineCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150045463348, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_textInfo.pageCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150045463348, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_textInfo.wordCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150045463348, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_textInfo.characterCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150062058218, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150168234784, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_textInfo.lineCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150168234784, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_textInfo.pageCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150168234784, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_textInfo.wordCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150168234784, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_textInfo.spaceCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150168234784, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_textInfo.characterCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150184380004, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_textInfo.lineCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150184380004, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_textInfo.pageCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150184380004, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_textInfo.wordCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150184380004, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_textInfo.spaceCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150184380004, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_textInfo.characterCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150323484100, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_textInfo.lineCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150323484100, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_textInfo.pageCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150323484100, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_textInfo.wordCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150323484100, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_textInfo.spaceCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150323484100, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_textInfo.characterCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150507819257, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150527117204, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_textInfo.lineCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150527117204, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_textInfo.pageCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150527117204, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_textInfo.wordCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150527117204, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_textInfo.spaceCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150527117204, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_textInfo.characterCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150731463991, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_textInfo.lineCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150731463991, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_textInfo.pageCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150731463991, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_textInfo.wordCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150731463991, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_textInfo.characterCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150923398953, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_textInfo.lineCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150923398953, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_textInfo.pageCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150923398953, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_textInfo.wordCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150923398953, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_textInfo.characterCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150988669718, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_textInfo.lineCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150988669718, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_textInfo.pageCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150988669718, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_textInfo.wordCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150988669718, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: m_textInfo.characterCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8787838387538572279, guid: 7fc69917d0d06bc45b69968dd837d42b,
        type: 3}
      propertyPath: TargetGameObject
      value: 
      objectReference: {fileID: 1703243937}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 7fc69917d0d06bc45b69968dd837d42b, type: 3}
--- !u!224 &402199377 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 7465677149715185877, guid: 7fc69917d0d06bc45b69968dd837d42b,
    type: 3}
  m_PrefabInstance: {fileID: 402199376}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &484690340
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 484690342}
  - component: {fileID: 484690341}
  m_Layer: 0
  m_Name: MMFPSUnlock
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &484690341
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 484690340}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1ecb76331b2602f4ba888267530cbfce, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  TargetFPS: 300
  VSyncCount: 0
--- !u!4 &484690342
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 484690340}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0.00081371516, y: -0.0011363812, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &601855540
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 601855543}
  - component: {fileID: 601855542}
  - component: {fileID: 601855541}
  m_Layer: 0
  m_Name: AudioListener
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &601855541
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 601855540}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0e8aa3525151bb343b66b059b6095946, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!81 &601855542
AudioListener:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 601855540}
  m_Enabled: 1
--- !u!4 &601855543
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 601855540}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &617180822
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 617180824}
  - component: {fileID: 617180823}
  m_Layer: 0
  m_Name: DemoPackageTester
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &617180823
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 617180822}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1ac950cd485569740affc32a280665ea, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  RequiresPostProcessing: 1
  RequiresTMP: 1
  RequiresCinemachine: 1
--- !u!4 &617180824
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 617180822}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &702963716 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 7465677149715185876, guid: 7fc69917d0d06bc45b69968dd837d42b,
    type: 3}
  m_PrefabInstance: {fileID: 402199376}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &752334677
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 752334678}
  - component: {fileID: 752334680}
  - component: {fileID: 752334679}
  m_Layer: 5
  m_Name: Text (TMP)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &752334678
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 752334677}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1703243938}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &752334679
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 752334677}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: STACK
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 2f490f72b6ed6ae4d8b31cee2cac3de7, type: 2}
  m_sharedMaterial: {fileID: -6263097307693789204, guid: 2f490f72b6ed6ae4d8b31cee2cac3de7,
    type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4282927176
  m_fontColor: {r: 0.2830189, g: 0.2830189, b: 0.2830189, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: 0
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 50
  m_fontSizeBase: 50
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 1
  m_VerticalAlignment: 256
  m_textAlignment: 514
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_enableWordWrapping: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 1
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 1
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &752334680
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 752334677}
  m_CullTransparentMesh: 0
--- !u!114 &773926780 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 427014296157349933, guid: 092b85f5f05bc0f4bb97bab8327f367e,
    type: 3}
  m_PrefabInstance: {fileID: 1051631846}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6da43522623d4704e979466dc7650b65, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &886102147
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 886102149}
  - component: {fileID: 886102148}
  m_Layer: 0
  m_Name: MMSoundManager
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &886102148
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 886102147}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 614edb33c38bcf949846d5675f29f9fb, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  AutomaticallyUnparentOnAwake: 1
  settingsSo: {fileID: 11400000, guid: 07ea3ff88e1ecb84d91f58aa804badc6, type: 2}
  AudioSourcePoolSize: 10
  PoolCanExpand: 1
--- !u!4 &886102149
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 886102147}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -142.98137, y: 41.857143, z: 97.578835}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &950042007 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 7465677149715185876, guid: 092b85f5f05bc0f4bb97bab8327f367e,
    type: 3}
  m_PrefabInstance: {fileID: 1051631846}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1044802375
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1044802376}
  - component: {fileID: 1044802380}
  - component: {fileID: 1044802379}
  - component: {fileID: 1044802378}
  - component: {fileID: 1044802377}
  m_Layer: 5
  m_Name: MMFlash
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1044802376
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1044802375}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 224062016678292244}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1044802377
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1044802375}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5463d50a0cd9b2449a50f538278303d2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  ChannelMode: 0
  Channel: 0
  MMChannelDefinition: {fileID: 0}
  FlashID: 0
  Interruptable: 0
  FlashInTween:
    MMTweenDefinitionType: 0
    MMTweenCurve: 0
    Curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    Initialized: 0
  FlashOutTween:
    MMTweenDefinitionType: 0
    MMTweenCurve: 0
    Curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    Initialized: 0
  DebugSettings:
    ChannelMode: 0
    Channel: 0
    MMChannelDefinition: {fileID: 0}
    FlashColor: {r: 1, g: 1, b: 1, a: 1}
    FlashDuration: 0.2
    FlashAlpha: 1
    FlashID: 0
  DebugTestButton: 0
--- !u!225 &1044802378
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1044802375}
  m_Enabled: 1
  m_Alpha: 0
  m_Interactable: 0
  m_BlocksRaycasts: 0
  m_IgnoreParentGroups: 0
--- !u!114 &1044802379
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1044802375}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1044802380
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1044802375}
  m_CullTransparentMesh: 0
--- !u!1001 &1051631846
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 2084567609}
    m_Modifications:
    - target: {fileID: 427014296157349933, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: CanPlayWhileAlreadyPlaying
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6341985375202562952, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: TargetGameObject
      value: 
      objectReference: {fileID: 1703243937}
    - target: {fileID: 7465677149413967465, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_textInfo.lineCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149413967465, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_textInfo.pageCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149413967465, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_textInfo.wordCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149413967465, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_textInfo.characterCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185876, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_Name
      value: CardsUI_Card_Fireproof
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185876, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_AnchorMax.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_AnchorMin.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 720
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 1024
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_LocalScale.x
      value: -0.8
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_LocalScale.y
      value: 0.8
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.9848078
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0.17364816
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 973.99976
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -224.99971
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 20
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150045463348, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_textInfo.lineCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150045463348, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_textInfo.pageCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150045463348, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_textInfo.wordCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150045463348, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_textInfo.characterCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150062058218, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150168234784, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_textInfo.lineCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150168234784, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_textInfo.pageCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150168234784, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_textInfo.wordCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150168234784, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_textInfo.spaceCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150168234784, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_textInfo.characterCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150184380004, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_textInfo.lineCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150184380004, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_textInfo.pageCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150184380004, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_textInfo.wordCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150184380004, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_textInfo.spaceCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150184380004, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_textInfo.characterCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150323484100, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_textInfo.lineCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150323484100, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_textInfo.pageCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150323484100, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_textInfo.wordCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150323484100, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_textInfo.spaceCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150323484100, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_textInfo.characterCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150507819257, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150527117204, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_textInfo.lineCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150527117204, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_textInfo.pageCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150527117204, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_textInfo.wordCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150527117204, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_textInfo.spaceCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150527117204, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_textInfo.characterCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150731463991, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_textInfo.lineCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150731463991, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_textInfo.pageCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150731463991, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_textInfo.wordCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150731463991, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_textInfo.characterCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150923398953, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_textInfo.lineCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150923398953, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_textInfo.pageCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150923398953, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_textInfo.wordCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150923398953, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_textInfo.characterCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150988669718, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_textInfo.lineCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150988669718, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_textInfo.pageCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150988669718, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_textInfo.wordCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150988669718, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: m_textInfo.characterCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8787838387538572279, guid: 092b85f5f05bc0f4bb97bab8327f367e,
        type: 3}
      propertyPath: TargetGameObject
      value: 
      objectReference: {fileID: 1703243937}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 092b85f5f05bc0f4bb97bab8327f367e, type: 3}
--- !u!224 &1051631847 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 7465677149715185877, guid: 092b85f5f05bc0f4bb97bab8327f367e,
    type: 3}
  m_PrefabInstance: {fileID: 1051631846}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1456155674 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 7465677149715185876, guid: ef1f777facc2a024c8bc6aecad8c85c1,
    type: 3}
  m_PrefabInstance: {fileID: 331905144}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1504572916
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1504572917}
  - component: {fileID: 1504572919}
  - component: {fileID: 1504572918}
  m_Layer: 5
  m_Name: CanvasBg
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1504572917
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1504572916}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 224062016678292244}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0, y: 0.5}
--- !u!114 &1504572918
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1504572916}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 0.76862746, b: 0, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: aab7a2a17dd28b04899e817f7db9a189, type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1504572919
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1504572916}
  m_CullTransparentMesh: 0
--- !u!1001 &1623701838
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 2084567609}
    m_Modifications:
    - target: {fileID: 6341985375202562952, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: TargetGameObject
      value: 
      objectReference: {fileID: 1703243937}
    - target: {fileID: 7465677149413967465, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_textInfo.lineCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149413967465, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_textInfo.pageCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149413967465, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_textInfo.wordCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149413967465, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_textInfo.characterCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185876, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_Name
      value: CardsUI_Card_Sumerian
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185876, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_AnchorMax.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_AnchorMin.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 720
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 1024
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_LocalScale.x
      value: -0.8
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_LocalScale.y
      value: 0.8
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.9659258
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0.2588191
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 928
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -272
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677149715185877, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 30
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150045463348, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_textInfo.lineCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150045463348, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_textInfo.pageCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150045463348, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_textInfo.wordCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150045463348, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_textInfo.characterCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150062058218, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150168234784, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_textInfo.lineCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150168234784, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_textInfo.pageCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150168234784, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_textInfo.wordCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150168234784, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_textInfo.spaceCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150168234784, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_textInfo.characterCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150184380004, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_textInfo.lineCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150184380004, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_textInfo.pageCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150184380004, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_textInfo.wordCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150184380004, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_textInfo.spaceCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150184380004, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_textInfo.characterCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150323484100, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_textInfo.lineCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150323484100, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_textInfo.pageCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150323484100, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_textInfo.wordCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150323484100, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_textInfo.spaceCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150323484100, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_textInfo.characterCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150507819257, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150527117204, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_textInfo.lineCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150527117204, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_textInfo.pageCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150527117204, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_textInfo.wordCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150527117204, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_textInfo.spaceCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150527117204, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_textInfo.characterCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150731463991, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_textInfo.lineCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150731463991, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_textInfo.pageCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150731463991, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_textInfo.wordCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150731463991, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_textInfo.characterCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150923398953, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_textInfo.lineCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150923398953, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_textInfo.pageCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150923398953, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_textInfo.wordCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150923398953, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_textInfo.characterCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150988669718, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_textInfo.lineCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150988669718, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_textInfo.pageCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150988669718, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_textInfo.wordCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7465677150988669718, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: m_textInfo.characterCount
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8787838387538572279, guid: b3f6473bddc357f499dbcb5855925796,
        type: 3}
      propertyPath: TargetGameObject
      value: 
      objectReference: {fileID: 1703243937}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: b3f6473bddc357f499dbcb5855925796, type: 3}
--- !u!224 &1623701839 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 7465677149715185877, guid: b3f6473bddc357f499dbcb5855925796,
    type: 3}
  m_PrefabInstance: {fileID: 1623701838}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1703243937
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1703243938}
  - component: {fileID: 1703243940}
  - component: {fileID: 1703243939}
  - component: {fileID: 1703243941}
  - component: {fileID: 1703243942}
  m_Layer: 5
  m_Name: StackButton
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!224 &1703243938
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1703243937}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 752334678}
  m_Father: {fileID: 224062016678292244}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 1, y: 1}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: -75, y: -75}
  m_SizeDelta: {x: 440.97144, y: 150.55396}
  m_Pivot: {x: 1, y: 1}
--- !u!114 &1703243939
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1703243937}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 1282028943509491997, guid: 72f7a32ac9c7676409eb438757bc6b4a,
    type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1703243940
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1703243937}
  m_CullTransparentMesh: 0
--- !u!114 &1703243941
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1703243937}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4e29b1a8efbd4b44bb3f3716e73f07ff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 1703243939}
  m_OnClick:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 1703243942}
        m_TargetAssemblyTypeName: 
        m_MethodName: Stack
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
--- !u!114 &1703243942
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1703243937}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c17806bb2fe50e149aa8b52db844e7ce, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  StackFeedback: {fileID: **********}
  BlockerFeedbacks:
  - {fileID: 1884460090}
  - {fileID: 773926780}
  - {fileID: 146825166}
  - {fileID: 332578521}
--- !u!1 &1713032064
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1713032065}
  - component: {fileID: 1713032066}
  - component: {fileID: 1713032068}
  - component: {fileID: 1713032067}
  m_Layer: 5
  m_Name: DealerButton
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1713032065
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1713032064}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 224062016678292244}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1713032066
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1713032064}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4e29b1a8efbd4b44bb3f3716e73f07ff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 0
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 1713032067}
  m_OnClick:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: **********}
        m_TargetAssemblyTypeName: 
        m_MethodName: PlayFeedbacks
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
--- !u!114 &1713032067
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1713032064}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 0}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1713032068
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1713032064}
  m_CullTransparentMesh: 0
--- !u!1 &1817957845
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1817957848}
  - component: {fileID: **********}
  m_Layer: 0
  m_Name: StackFeedback
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &**********
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1817957845}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6da43522623d4704e979466dc7650b65, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Feedbacks:
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  InitializationMode: 2
  AutoInitialization: 1
  SafeMode: 3
  Direction: 0
  AutoChangeDirectionOnEnd: 1
  AutoPlayOnStart: 0
  AutoPlayOnEnable: 0
  ForceTimescaleMode: 0
  ForcedTimescaleMode: 1
  DurationMultiplier: 1
  RandomizeDuration: 0
  RandomDurationMultiplier: {x: 0.5, y: 1.5}
  DisplayFullDurationDetails: 0
  PlayerTimescaleMode: 1
  OnlyPlayIfWithinRange: 0
  RangeCenter: {fileID: 0}
  RangeDistance: 5
  UseRangeFalloff: 0
  RangeFalloff:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  RemapRangeFalloff: {x: 0, y: 1}
  IgnoreRangeEvents: 0
  CooldownDuration: 0
  InitialDelay: 0
  CanPlay: 1
  CanPlayWhileAlreadyPlaying: 0
  ChanceToPlay: 100
  FeedbacksIntensity: 1
  Events:
    TriggerMMFeedbacksEvents: 0
    TriggerUnityEvents: 1
    OnPlay:
      m_PersistentCalls:
        m_Calls: []
    OnPause:
      m_PersistentCalls:
        m_Calls: []
    OnResume:
      m_PersistentCalls:
        m_Calls: []
    OnRevert:
      m_PersistentCalls:
        m_Calls: []
    OnComplete:
      m_PersistentCalls:
        m_Calls: []
    OnRestoreInitialValues:
      m_PersistentCalls:
        m_Calls: []
    OnSkipToTheEnd:
      m_PersistentCalls:
        m_Calls: []
  DebugActive: 0
  FeedbacksList:
  - rid: 0
  - rid: 1
  - rid: 2
  - rid: 3
  - rid: 4
  - rid: 5
  - rid: 6
  - rid: 7
  - rid: 8
  - rid: 9
  - rid: 10
  - rid: 11
  - rid: 12
  - rid: 13
  KeepPlayModeChanges: 0
  PerformanceMode: 0
  StopFeedbacksOnDisable: 0
  PlayCount: 0
  references:
    version: 2
    RefIds:
    - rid: 0
      type: {class: MMF_Position, ns: MoreMountains.Feedbacks, asm: MoreMountains.Tools}
      data:
        Active: 1
        UniqueID: 261211702
        Label: Position Sumerian
        ChannelMode: 0
        Channel: 0
        MMChannelDefinition: {fileID: 0}
        Chance: 100
        DisplayColor: {r: 0, g: 0, b: 0, a: 1}
        Timing:
          TimescaleMode: 0
          ExcludeFromHoldingPauses: 0
          ContributeToTotalDuration: 1
          InitialDelay: 0
          CooldownDuration: 0
          InterruptsOnStop: 1
          NumberOfRepeats: 0
          RepeatForever: 0
          DelayBetweenRepeats: 1
          PlayCount: 0
          LimitPlayCount: 0
          MaxPlayCount: 3
          SetPlayCountToZeroOnReset: 0
          MMFeedbacksDirectionCondition: 0
          PlayDirection: 0
          ConstantIntensity: 0
          UseIntensityInterval: 0
          IntensityIntervalMin: 0
          IntensityIntervalMax: 0
          Sequence: {fileID: 0}
          TrackID: 0
          Quantized: 0
          TargetBPM: 120
        AutomatedTargetAcquisition:
          Mode: 0
          ChildIndex: 0
        RandomizeOutput: 0
        RandomMultiplier: {x: 0.8, y: 1}
        RandomizeDuration: 0
        RandomDurationMultiplier: {x: 0.5, y: 2}
        UseRange: 0
        RangeDistance: 5
        UseRangeFalloff: 0
        RangeFalloff:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        RemapRangeFalloff: {x: 0, y: 1}
        Owner: {fileID: **********}
        DebugActive: 0
        AnimatePositionTarget: {fileID: 1876896487}
        Mode: 0
        Space: 2
        RandomizeRemap: 0
        AnimatePositionDuration: 0.2
        AnimatePositionTween:
          MMTweenDefinitionType: 1
          MMTweenCurve: 4
          Curve:
            serializedVersion: 2
            m_Curve:
            - serializedVersion: 3
              time: 0
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 0.7413168
              value: 1.1071914
              inSlope: 0.89233255
              outSlope: 0.89233255
              tangentMode: 0
              weightedMode: 0
              inWeight: 0.33333334
              outWeight: 0.33333334
            - serializedVersion: 3
              time: 0.879726
              value: 0.90986913
              inSlope: -1.5172535
              outSlope: -1.5172535
              tangentMode: 0
              weightedMode: 0
              inWeight: 0.33333334
              outWeight: 0.33333334
            - serializedVersion: 3
              time: 1
              value: 1
              inSlope: -5.39372
              outSlope: -5.39372
              tangentMode: 0
              weightedMode: 0
              inWeight: 0.13907161
              outWeight: 0
            m_PreInfinity: 2
            m_PostInfinity: 2
            m_RotationOrder: 4
          Initialized: 1
        RemapCurveZero: 0
        RemapCurveZeroAlt: 0
        RemapCurveOne: 1
        RemapCurveOneAlt: 1
        AnimateX: 0
        AnimatePositionTweenX:
          MMTweenDefinitionType: 1
          MMTweenCurve: 4
          Curve:
            serializedVersion: 2
            m_Curve:
            - serializedVersion: 3
              time: 0
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 0.3
              value: 1
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 0.6
              value: -1
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 1
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            m_PreInfinity: 2
            m_PostInfinity: 2
            m_RotationOrder: 4
          Initialized: 1
        AnimateY: 0
        AnimatePositionTweenY:
          MMTweenDefinitionType: 1
          MMTweenCurve: 4
          Curve:
            serializedVersion: 2
            m_Curve:
            - serializedVersion: 3
              time: 0
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 0.3
              value: 1
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 0.6
              value: -1
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 1
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            m_PreInfinity: 2
            m_PostInfinity: 2
            m_RotationOrder: 4
          Initialized: 1
        AnimateZ: 0
        AnimatePositionTweenZ:
          MMTweenDefinitionType: 1
          MMTweenCurve: 4
          Curve:
            serializedVersion: 2
            m_Curve:
            - serializedVersion: 3
              time: 0
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 0.3
              value: 1
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 0.6
              value: -1
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 1
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            m_PreInfinity: 2
            m_PostInfinity: 2
            m_RotationOrder: 4
          Initialized: 1
        AllowAdditivePlays: 0
        RelativePosition: 0
        DeterminePositionsOnPlay: 0
        InitialPosition: {x: 0, y: 0, z: 0}
        DestinationPosition: {x: -1140, y: -38, z: 0}
        InitialPositionTransform: {fileID: 0}
        DestinationPositionTransform: {fileID: 0}
        AnimatePositionCurveX:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 0.3
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 0.6
            value: -1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        AnimatePositionCurveY:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 0.3
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 0.6
            value: -1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        AnimatePositionCurveZ:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 0.3
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 0.6
            value: -1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        AnimatePositionCurve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 0.7413168
            value: 1.1071914
            inSlope: 0.89233255
            outSlope: 0.89233255
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 0.879726
            value: 0.90986913
            inSlope: -1.5172535
            outSlope: -1.5172535
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: -5.39372
            outSlope: -5.39372
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.13907161
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
    - rid: 1
      type: {class: MMF_Rotation, ns: MoreMountains.Feedbacks, asm: MoreMountains.Tools}
      data:
        Active: 1
        UniqueID: 1161115961
        Label: Rotation Sumerian
        ChannelMode: 0
        Channel: 0
        MMChannelDefinition: {fileID: 0}
        Chance: 100
        DisplayColor: {r: 0, g: 0, b: 0, a: 1}
        Timing:
          TimescaleMode: 0
          ExcludeFromHoldingPauses: 0
          ContributeToTotalDuration: 1
          InitialDelay: 0
          CooldownDuration: 0
          InterruptsOnStop: 1
          NumberOfRepeats: 0
          RepeatForever: 0
          DelayBetweenRepeats: 1
          PlayCount: 0
          LimitPlayCount: 0
          MaxPlayCount: 3
          SetPlayCountToZeroOnReset: 0
          MMFeedbacksDirectionCondition: 0
          PlayDirection: 0
          ConstantIntensity: 0
          UseIntensityInterval: 0
          IntensityIntervalMin: 0
          IntensityIntervalMax: 0
          Sequence: {fileID: 0}
          TrackID: 0
          Quantized: 0
          TargetBPM: 120
        AutomatedTargetAcquisition:
          Mode: 0
          ChildIndex: 0
        RandomizeOutput: 0
        RandomMultiplier: {x: 0.8, y: 1}
        RandomizeDuration: 0
        RandomDurationMultiplier: {x: 0.5, y: 2}
        UseRange: 0
        RangeDistance: 5
        UseRangeFalloff: 0
        RangeFalloff:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        RemapRangeFalloff: {x: 0, y: 1}
        Owner: {fileID: **********}
        DebugActive: 0
        AnimateRotationTarget: {fileID: 1623701839}
        Mode: 2
        RotationSpace: 0
        AnimateRotationDuration: 0.2
        RemapCurveZero: 0
        RemapCurveOne: 0
        AnimateX: 1
        AnimateRotationTweenX:
          MMTweenDefinitionType: 1
          MMTweenCurve: 4
          Curve:
            serializedVersion: 2
            m_Curve:
            - serializedVersion: 3
              time: 0
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 0.88772655
              value: 1.1154296
              inSlope: 0.5001836
              outSlope: 0.5001836
              tangentMode: 0
              weightedMode: 0
              inWeight: 0.33333334
              outWeight: 0.33333334
            - serializedVersion: 3
              time: 1
              value: 1
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            m_PreInfinity: 2
            m_PostInfinity: 2
            m_RotationOrder: 4
          Initialized: 1
        AnimateY: 1
        AnimateRotationTweenY:
          MMTweenDefinitionType: 1
          MMTweenCurve: 4
          Curve:
            serializedVersion: 2
            m_Curve:
            - serializedVersion: 3
              time: 0
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 0.9080805
              value: 1.1004071
              inSlope: 0.41706467
              outSlope: 0.41706467
              tangentMode: 0
              weightedMode: 0
              inWeight: 0.33333334
              outWeight: 0.33333334
            - serializedVersion: 3
              time: 1
              value: 1
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            m_PreInfinity: 2
            m_PostInfinity: 2
            m_RotationOrder: 4
          Initialized: 1
        AnimateZ: 1
        AnimateRotationTweenZ:
          MMTweenDefinitionType: 1
          MMTweenCurve: 4
          Curve:
            serializedVersion: 2
            m_Curve:
            - serializedVersion: 3
              time: 0
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 1
              value: 1
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            m_PreInfinity: 2
            m_PostInfinity: 2
            m_RotationOrder: 4
          Initialized: 1
        AllowAdditivePlays: 0
        DetermineRotationOnPlay: 1
        ToDestinationSpace: 0
        DestinationAngles: {x: 0, y: 0, z: 0}
        ToDestinationTween:
          MMTweenDefinitionType: 1
          MMTweenCurve: 10
          Curve:
            serializedVersion: 2
            m_Curve:
            - serializedVersion: 3
              time: 0
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 1
              value: 1
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            m_PreInfinity: 2
            m_PostInfinity: 2
            m_RotationOrder: 4
          Initialized: 1
        AnimateRotationX:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 0.88772655
            value: 1.1154296
            inSlope: 0.5001836
            outSlope: 0.5001836
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        AnimateRotationY:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 0.9080805
            value: 1.1004071
            inSlope: 0.41706467
            outSlope: 0.41706467
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        AnimateRotationZ:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        ToDestinationCurve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
    - rid: 2
      type: {class: MMF_Position, ns: MoreMountains.Feedbacks, asm: MoreMountains.Tools}
      data:
        Active: 1
        UniqueID: 1045870999
        Label: Position Fireproof
        ChannelMode: 0
        Channel: 0
        MMChannelDefinition: {fileID: 0}
        Chance: 100
        DisplayColor: {r: 0, g: 0, b: 0, a: 1}
        Timing:
          TimescaleMode: 0
          ExcludeFromHoldingPauses: 0
          ContributeToTotalDuration: 1
          InitialDelay: 0.05
          CooldownDuration: 0
          InterruptsOnStop: 1
          NumberOfRepeats: 0
          RepeatForever: 0
          DelayBetweenRepeats: 1
          PlayCount: 0
          LimitPlayCount: 0
          MaxPlayCount: 3
          SetPlayCountToZeroOnReset: 0
          MMFeedbacksDirectionCondition: 0
          PlayDirection: 0
          ConstantIntensity: 0
          UseIntensityInterval: 0
          IntensityIntervalMin: 0
          IntensityIntervalMax: 0
          Sequence: {fileID: 0}
          TrackID: 0
          Quantized: 0
          TargetBPM: 120
        AutomatedTargetAcquisition:
          Mode: 0
          ChildIndex: 0
        RandomizeOutput: 0
        RandomMultiplier: {x: 0.8, y: 1}
        RandomizeDuration: 0
        RandomDurationMultiplier: {x: 0.5, y: 2}
        UseRange: 0
        RangeDistance: 5
        UseRangeFalloff: 0
        RangeFalloff:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        RemapRangeFalloff: {x: 0, y: 1}
        Owner: {fileID: **********}
        DebugActive: 0
        AnimatePositionTarget: {fileID: 950042007}
        Mode: 0
        Space: 2
        RandomizeRemap: 0
        AnimatePositionDuration: 0.2
        AnimatePositionTween:
          MMTweenDefinitionType: 1
          MMTweenCurve: 4
          Curve:
            serializedVersion: 2
            m_Curve:
            - serializedVersion: 3
              time: 0
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 0.77232265
              value: 1.0781349
              inSlope: 0.89233255
              outSlope: 0.89233255
              tangentMode: 0
              weightedMode: 0
              inWeight: 0.33333334
              outWeight: 0.33333334
            - serializedVersion: 3
              time: 0.87911564
              value: 0.88807195
              inSlope: -1.5172535
              outSlope: -1.5172535
              tangentMode: 0
              weightedMode: 0
              inWeight: 0.33333334
              outWeight: 0.33333334
            - serializedVersion: 3
              time: 1
              value: 1
              inSlope: -5.39372
              outSlope: -5.39372
              tangentMode: 0
              weightedMode: 0
              inWeight: 0.13907161
              outWeight: 0
            m_PreInfinity: 2
            m_PostInfinity: 2
            m_RotationOrder: 4
          Initialized: 1
        RemapCurveZero: 0
        RemapCurveZeroAlt: 0
        RemapCurveOne: 1
        RemapCurveOneAlt: 1
        AnimateX: 0
        AnimatePositionTweenX:
          MMTweenDefinitionType: 1
          MMTweenCurve: 4
          Curve:
            serializedVersion: 2
            m_Curve:
            - serializedVersion: 3
              time: 0
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 0.3
              value: 1
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 0.6
              value: -1
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 1
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            m_PreInfinity: 2
            m_PostInfinity: 2
            m_RotationOrder: 4
          Initialized: 1
        AnimateY: 0
        AnimatePositionTweenY:
          MMTweenDefinitionType: 1
          MMTweenCurve: 4
          Curve:
            serializedVersion: 2
            m_Curve:
            - serializedVersion: 3
              time: 0
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 0.3
              value: 1
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 0.6
              value: -1
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 1
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            m_PreInfinity: 2
            m_PostInfinity: 2
            m_RotationOrder: 4
          Initialized: 1
        AnimateZ: 0
        AnimatePositionTweenZ:
          MMTweenDefinitionType: 1
          MMTweenCurve: 4
          Curve:
            serializedVersion: 2
            m_Curve:
            - serializedVersion: 3
              time: 0
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 0.3
              value: 1
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 0.6
              value: -1
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 1
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            m_PreInfinity: 2
            m_PostInfinity: 2
            m_RotationOrder: 4
          Initialized: 1
        AllowAdditivePlays: 0
        RelativePosition: 0
        DeterminePositionsOnPlay: 0
        InitialPosition: {x: 0, y: 0, z: 0}
        DestinationPosition: {x: -380, y: -38, z: 0}
        InitialPositionTransform: {fileID: 0}
        DestinationPositionTransform: {fileID: 0}
        AnimatePositionCurveX:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 0.3
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 0.6
            value: -1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        AnimatePositionCurveY:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 0.3
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 0.6
            value: -1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        AnimatePositionCurveZ:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 0.3
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 0.6
            value: -1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        AnimatePositionCurve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 0.77232265
            value: 1.0781349
            inSlope: 0.89233255
            outSlope: 0.89233255
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 0.87911564
            value: 0.88807195
            inSlope: -1.5172535
            outSlope: -1.5172535
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: -5.39372
            outSlope: -5.39372
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.13907161
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
    - rid: 3
      type: {class: MMF_Rotation, ns: MoreMountains.Feedbacks, asm: MoreMountains.Tools}
      data:
        Active: 1
        UniqueID: 1700484265
        Label: Rotation Fireproof
        ChannelMode: 0
        Channel: 0
        MMChannelDefinition: {fileID: 0}
        Chance: 100
        DisplayColor: {r: 0, g: 0, b: 0, a: 1}
        Timing:
          TimescaleMode: 0
          ExcludeFromHoldingPauses: 0
          ContributeToTotalDuration: 1
          InitialDelay: 0
          CooldownDuration: 0
          InterruptsOnStop: 1
          NumberOfRepeats: 0
          RepeatForever: 0
          DelayBetweenRepeats: 1
          PlayCount: 0
          LimitPlayCount: 0
          MaxPlayCount: 3
          SetPlayCountToZeroOnReset: 0
          MMFeedbacksDirectionCondition: 0
          PlayDirection: 0
          ConstantIntensity: 0
          UseIntensityInterval: 0
          IntensityIntervalMin: 0
          IntensityIntervalMax: 0
          Sequence: {fileID: 0}
          TrackID: 0
          Quantized: 0
          TargetBPM: 120
        AutomatedTargetAcquisition:
          Mode: 0
          ChildIndex: 0
        RandomizeOutput: 0
        RandomMultiplier: {x: 0.8, y: 1}
        RandomizeDuration: 0
        RandomDurationMultiplier: {x: 0.5, y: 2}
        UseRange: 0
        RangeDistance: 5
        UseRangeFalloff: 0
        RangeFalloff:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        RemapRangeFalloff: {x: 0, y: 1}
        Owner: {fileID: **********}
        DebugActive: 0
        AnimateRotationTarget: {fileID: 1051631847}
        Mode: 2
        RotationSpace: 0
        AnimateRotationDuration: 0.2
        RemapCurveZero: 0
        RemapCurveOne: 0
        AnimateX: 1
        AnimateRotationTweenX:
          MMTweenDefinitionType: 1
          MMTweenCurve: 4
          Curve:
            serializedVersion: 2
            m_Curve:
            - serializedVersion: 3
              time: 0
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 0.88772655
              value: 1.1154296
              inSlope: 0.5001836
              outSlope: 0.5001836
              tangentMode: 0
              weightedMode: 0
              inWeight: 0.33333334
              outWeight: 0.33333334
            - serializedVersion: 3
              time: 1
              value: 1
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            m_PreInfinity: 2
            m_PostInfinity: 2
            m_RotationOrder: 4
          Initialized: 1
        AnimateY: 1
        AnimateRotationTweenY:
          MMTweenDefinitionType: 1
          MMTweenCurve: 4
          Curve:
            serializedVersion: 2
            m_Curve:
            - serializedVersion: 3
              time: 0
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 0.9080805
              value: 1.1004071
              inSlope: 0.41706467
              outSlope: 0.41706467
              tangentMode: 0
              weightedMode: 0
              inWeight: 0.33333334
              outWeight: 0.33333334
            - serializedVersion: 3
              time: 1
              value: 1
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            m_PreInfinity: 2
            m_PostInfinity: 2
            m_RotationOrder: 4
          Initialized: 1
        AnimateZ: 1
        AnimateRotationTweenZ:
          MMTweenDefinitionType: 1
          MMTweenCurve: 4
          Curve:
            serializedVersion: 2
            m_Curve:
            - serializedVersion: 3
              time: 0
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 1
              value: 1
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            m_PreInfinity: 2
            m_PostInfinity: 2
            m_RotationOrder: 4
          Initialized: 1
        AllowAdditivePlays: 0
        DetermineRotationOnPlay: 1
        ToDestinationSpace: 0
        DestinationAngles: {x: 0, y: 0, z: 0}
        ToDestinationTween:
          MMTweenDefinitionType: 1
          MMTweenCurve: 10
          Curve:
            serializedVersion: 2
            m_Curve:
            - serializedVersion: 3
              time: 0
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 1
              value: 1
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            m_PreInfinity: 2
            m_PostInfinity: 2
            m_RotationOrder: 4
          Initialized: 1
        AnimateRotationX:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 0.88772655
            value: 1.1154296
            inSlope: 0.5001836
            outSlope: 0.5001836
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        AnimateRotationY:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 0.9080805
            value: 1.1004071
            inSlope: 0.41706467
            outSlope: 0.41706467
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        AnimateRotationZ:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        ToDestinationCurve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
    - rid: 4
      type: {class: MMF_Position, ns: MoreMountains.Feedbacks, asm: MoreMountains.Tools}
      data:
        Active: 1
        UniqueID: 1542871251
        Label: Position Lady
        ChannelMode: 0
        Channel: 0
        MMChannelDefinition: {fileID: 0}
        Chance: 100
        DisplayColor: {r: 0, g: 0, b: 0, a: 1}
        Timing:
          TimescaleMode: 0
          ExcludeFromHoldingPauses: 0
          ContributeToTotalDuration: 1
          InitialDelay: 0.1
          CooldownDuration: 0
          InterruptsOnStop: 1
          NumberOfRepeats: 0
          RepeatForever: 0
          DelayBetweenRepeats: 1
          PlayCount: 0
          LimitPlayCount: 0
          MaxPlayCount: 3
          SetPlayCountToZeroOnReset: 0
          MMFeedbacksDirectionCondition: 0
          PlayDirection: 0
          ConstantIntensity: 0
          UseIntensityInterval: 0
          IntensityIntervalMin: 0
          IntensityIntervalMax: 0
          Sequence: {fileID: 0}
          TrackID: 0
          Quantized: 0
          TargetBPM: 120
        AutomatedTargetAcquisition:
          Mode: 0
          ChildIndex: 0
        RandomizeOutput: 0
        RandomMultiplier: {x: 0.8, y: 1}
        RandomizeDuration: 0
        RandomDurationMultiplier: {x: 0.5, y: 2}
        UseRange: 0
        RangeDistance: 5
        UseRangeFalloff: 0
        RangeFalloff:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        RemapRangeFalloff: {x: 0, y: 1}
        Owner: {fileID: **********}
        DebugActive: 0
        AnimatePositionTarget: {fileID: 1456155674}
        Mode: 0
        Space: 2
        RandomizeRemap: 0
        AnimatePositionDuration: 0.2
        AnimatePositionTween:
          MMTweenDefinitionType: 1
          MMTweenCurve: 4
          Curve:
            serializedVersion: 2
            m_Curve:
            - serializedVersion: 3
              time: 0
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 0.7765951
              value: 1.1180862
              inSlope: 0.89233255
              outSlope: 0.89233255
              tangentMode: 0
              weightedMode: 0
              inWeight: 0.33333334
              outWeight: 0.33333334
            - serializedVersion: 3
              time: 0.90328556
              value: 0.8626508
              inSlope: -1.5172535
              outSlope: -1.5172535
              tangentMode: 0
              weightedMode: 0
              inWeight: 0.33333334
              outWeight: 0.33333334
            - serializedVersion: 3
              time: 1
              value: 1
              inSlope: -5.39372
              outSlope: -5.39372
              tangentMode: 0
              weightedMode: 0
              inWeight: 0.13907161
              outWeight: 0
            m_PreInfinity: 2
            m_PostInfinity: 2
            m_RotationOrder: 4
          Initialized: 1
        RemapCurveZero: 0
        RemapCurveZeroAlt: 0
        RemapCurveOne: 1
        RemapCurveOneAlt: 1
        AnimateX: 0
        AnimatePositionTweenX:
          MMTweenDefinitionType: 1
          MMTweenCurve: 4
          Curve:
            serializedVersion: 2
            m_Curve:
            - serializedVersion: 3
              time: 0
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 0.3
              value: 1
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 0.6
              value: -1
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 1
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            m_PreInfinity: 2
            m_PostInfinity: 2
            m_RotationOrder: 4
          Initialized: 1
        AnimateY: 0
        AnimatePositionTweenY:
          MMTweenDefinitionType: 1
          MMTweenCurve: 4
          Curve:
            serializedVersion: 2
            m_Curve:
            - serializedVersion: 3
              time: 0
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 0.3
              value: 1
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 0.6
              value: -1
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 1
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            m_PreInfinity: 2
            m_PostInfinity: 2
            m_RotationOrder: 4
          Initialized: 1
        AnimateZ: 0
        AnimatePositionTweenZ:
          MMTweenDefinitionType: 1
          MMTweenCurve: 4
          Curve:
            serializedVersion: 2
            m_Curve:
            - serializedVersion: 3
              time: 0
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 0.3
              value: 1
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 0.6
              value: -1
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 1
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            m_PreInfinity: 2
            m_PostInfinity: 2
            m_RotationOrder: 4
          Initialized: 1
        AllowAdditivePlays: 0
        RelativePosition: 0
        DeterminePositionsOnPlay: 0
        InitialPosition: {x: 0, y: 0, z: 0}
        DestinationPosition: {x: 380, y: -38, z: 0}
        InitialPositionTransform: {fileID: 0}
        DestinationPositionTransform: {fileID: 0}
        AnimatePositionCurveX:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 0.3
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 0.6
            value: -1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        AnimatePositionCurveY:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 0.3
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 0.6
            value: -1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        AnimatePositionCurveZ:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 0.3
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 0.6
            value: -1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        AnimatePositionCurve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 0.7765951
            value: 1.1180862
            inSlope: 0.89233255
            outSlope: 0.89233255
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 0.90328556
            value: 0.8626508
            inSlope: -1.5172535
            outSlope: -1.5172535
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: -5.39372
            outSlope: -5.39372
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.13907161
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
    - rid: 5
      type: {class: MMF_Rotation, ns: MoreMountains.Feedbacks, asm: MoreMountains.Tools}
      data:
        Active: 1
        UniqueID: 358296796
        Label: Rotation Lady
        ChannelMode: 0
        Channel: 0
        MMChannelDefinition: {fileID: 0}
        Chance: 100
        DisplayColor: {r: 0, g: 0, b: 0, a: 1}
        Timing:
          TimescaleMode: 0
          ExcludeFromHoldingPauses: 0
          ContributeToTotalDuration: 1
          InitialDelay: 0
          CooldownDuration: 0
          InterruptsOnStop: 1
          NumberOfRepeats: 0
          RepeatForever: 0
          DelayBetweenRepeats: 1
          PlayCount: 0
          LimitPlayCount: 0
          MaxPlayCount: 3
          SetPlayCountToZeroOnReset: 0
          MMFeedbacksDirectionCondition: 0
          PlayDirection: 0
          ConstantIntensity: 0
          UseIntensityInterval: 0
          IntensityIntervalMin: 0
          IntensityIntervalMax: 0
          Sequence: {fileID: 0}
          TrackID: 0
          Quantized: 0
          TargetBPM: 120
        AutomatedTargetAcquisition:
          Mode: 0
          ChildIndex: 0
        RandomizeOutput: 0
        RandomMultiplier: {x: 0.8, y: 1}
        RandomizeDuration: 0
        RandomDurationMultiplier: {x: 0.5, y: 2}
        UseRange: 0
        RangeDistance: 5
        UseRangeFalloff: 0
        RangeFalloff:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        RemapRangeFalloff: {x: 0, y: 1}
        Owner: {fileID: **********}
        DebugActive: 0
        AnimateRotationTarget: {fileID: 331905145}
        Mode: 2
        RotationSpace: 0
        AnimateRotationDuration: 0.2
        RemapCurveZero: 0
        RemapCurveOne: 0
        AnimateX: 1
        AnimateRotationTweenX:
          MMTweenDefinitionType: 1
          MMTweenCurve: 4
          Curve:
            serializedVersion: 2
            m_Curve:
            - serializedVersion: 3
              time: 0
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 0.88772655
              value: 1.1154296
              inSlope: 0.5001836
              outSlope: 0.5001836
              tangentMode: 0
              weightedMode: 0
              inWeight: 0.33333334
              outWeight: 0.33333334
            - serializedVersion: 3
              time: 1
              value: 1
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            m_PreInfinity: 2
            m_PostInfinity: 2
            m_RotationOrder: 4
          Initialized: 1
        AnimateY: 1
        AnimateRotationTweenY:
          MMTweenDefinitionType: 1
          MMTweenCurve: 4
          Curve:
            serializedVersion: 2
            m_Curve:
            - serializedVersion: 3
              time: 0
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 0.9080805
              value: 1.1004071
              inSlope: 0.41706467
              outSlope: 0.41706467
              tangentMode: 0
              weightedMode: 0
              inWeight: 0.33333334
              outWeight: 0.33333334
            - serializedVersion: 3
              time: 1
              value: 1
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            m_PreInfinity: 2
            m_PostInfinity: 2
            m_RotationOrder: 4
          Initialized: 1
        AnimateZ: 1
        AnimateRotationTweenZ:
          MMTweenDefinitionType: 1
          MMTweenCurve: 4
          Curve:
            serializedVersion: 2
            m_Curve:
            - serializedVersion: 3
              time: 0
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 1
              value: 1
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            m_PreInfinity: 2
            m_PostInfinity: 2
            m_RotationOrder: 4
          Initialized: 1
        AllowAdditivePlays: 0
        DetermineRotationOnPlay: 1
        ToDestinationSpace: 0
        DestinationAngles: {x: 0, y: 0, z: 0}
        ToDestinationTween:
          MMTweenDefinitionType: 1
          MMTweenCurve: 10
          Curve:
            serializedVersion: 2
            m_Curve:
            - serializedVersion: 3
              time: 0
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 1
              value: 1
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            m_PreInfinity: 2
            m_PostInfinity: 2
            m_RotationOrder: 4
          Initialized: 1
        AnimateRotationX:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 0.88772655
            value: 1.1154296
            inSlope: 0.5001836
            outSlope: 0.5001836
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        AnimateRotationY:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 0.9080805
            value: 1.1004071
            inSlope: 0.41706467
            outSlope: 0.41706467
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        AnimateRotationZ:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        ToDestinationCurve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
    - rid: 6
      type: {class: MMF_Position, ns: MoreMountains.Feedbacks, asm: MoreMountains.Tools}
      data:
        Active: 1
        UniqueID: -89927973
        Label: Position Stormeye
        ChannelMode: 0
        Channel: 0
        MMChannelDefinition: {fileID: 0}
        Chance: 100
        DisplayColor: {r: 0, g: 0, b: 0, a: 1}
        Timing:
          TimescaleMode: 0
          ExcludeFromHoldingPauses: 0
          ContributeToTotalDuration: 1
          InitialDelay: 0.15
          CooldownDuration: 0
          InterruptsOnStop: 1
          NumberOfRepeats: 0
          RepeatForever: 0
          DelayBetweenRepeats: 1
          PlayCount: 0
          LimitPlayCount: 0
          MaxPlayCount: 3
          SetPlayCountToZeroOnReset: 0
          MMFeedbacksDirectionCondition: 0
          PlayDirection: 0
          ConstantIntensity: 0
          UseIntensityInterval: 0
          IntensityIntervalMin: 0
          IntensityIntervalMax: 0
          Sequence: {fileID: 0}
          TrackID: 0
          Quantized: 0
          TargetBPM: 120
        AutomatedTargetAcquisition:
          Mode: 0
          ChildIndex: 0
        RandomizeOutput: 0
        RandomMultiplier: {x: 0.8, y: 1}
        RandomizeDuration: 0
        RandomDurationMultiplier: {x: 0.5, y: 2}
        UseRange: 0
        RangeDistance: 5
        UseRangeFalloff: 0
        RangeFalloff:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        RemapRangeFalloff: {x: 0, y: 1}
        Owner: {fileID: **********}
        DebugActive: 0
        AnimatePositionTarget: {fileID: 702963716}
        Mode: 0
        Space: 2
        RandomizeRemap: 0
        AnimatePositionDuration: 0.2
        AnimatePositionTween:
          MMTweenDefinitionType: 1
          MMTweenCurve: 4
          Curve:
            serializedVersion: 2
            m_Curve:
            - serializedVersion: 3
              time: 0
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 0.7952719
              value: 1.0890297
              inSlope: 0.89233255
              outSlope: 0.89233255
              tangentMode: 0
              weightedMode: 0
              inWeight: 0.33333334
              outWeight: 0.33333334
            - serializedVersion: 3
              time: 0.90951115
              value: 0.9243955
              inSlope: -1.5172535
              outSlope: -1.5172535
              tangentMode: 0
              weightedMode: 0
              inWeight: 0.33333334
              outWeight: 0.33333334
            - serializedVersion: 3
              time: 1
              value: 1
              inSlope: -5.39372
              outSlope: -5.39372
              tangentMode: 0
              weightedMode: 0
              inWeight: 0.13907161
              outWeight: 0
            m_PreInfinity: 2
            m_PostInfinity: 2
            m_RotationOrder: 4
          Initialized: 1
        RemapCurveZero: 0
        RemapCurveZeroAlt: 0
        RemapCurveOne: 1
        RemapCurveOneAlt: 1
        AnimateX: 0
        AnimatePositionTweenX:
          MMTweenDefinitionType: 1
          MMTweenCurve: 4
          Curve:
            serializedVersion: 2
            m_Curve:
            - serializedVersion: 3
              time: 0
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 0.3
              value: 1
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 0.6
              value: -1
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 1
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            m_PreInfinity: 2
            m_PostInfinity: 2
            m_RotationOrder: 4
          Initialized: 1
        AnimateY: 0
        AnimatePositionTweenY:
          MMTweenDefinitionType: 1
          MMTweenCurve: 4
          Curve:
            serializedVersion: 2
            m_Curve:
            - serializedVersion: 3
              time: 0
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 0.3
              value: 1
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 0.6
              value: -1
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 1
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            m_PreInfinity: 2
            m_PostInfinity: 2
            m_RotationOrder: 4
          Initialized: 1
        AnimateZ: 0
        AnimatePositionTweenZ:
          MMTweenDefinitionType: 1
          MMTweenCurve: 4
          Curve:
            serializedVersion: 2
            m_Curve:
            - serializedVersion: 3
              time: 0
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 0.3
              value: 1
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 0.6
              value: -1
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 1
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            m_PreInfinity: 2
            m_PostInfinity: 2
            m_RotationOrder: 4
          Initialized: 1
        AllowAdditivePlays: 0
        RelativePosition: 0
        DeterminePositionsOnPlay: 0
        InitialPosition: {x: 0, y: 0, z: 0}
        DestinationPosition: {x: 1140, y: -38, z: 0}
        InitialPositionTransform: {fileID: 0}
        DestinationPositionTransform: {fileID: 0}
        AnimatePositionCurveX:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 0.3
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 0.6
            value: -1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        AnimatePositionCurveY:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 0.3
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 0.6
            value: -1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        AnimatePositionCurveZ:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 0.3
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 0.6
            value: -1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        AnimatePositionCurve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 0.7952719
            value: 1.0890297
            inSlope: 0.89233255
            outSlope: 0.89233255
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 0.90951115
            value: 0.9243955
            inSlope: -1.5172535
            outSlope: -1.5172535
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: -5.39372
            outSlope: -5.39372
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.13907161
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
    - rid: 7
      type: {class: MMF_Rotation, ns: MoreMountains.Feedbacks, asm: MoreMountains.Tools}
      data:
        Active: 1
        UniqueID: 785301874
        Label: Rotation Stormeye
        ChannelMode: 0
        Channel: 0
        MMChannelDefinition: {fileID: 0}
        Chance: 100
        DisplayColor: {r: 0, g: 0, b: 0, a: 1}
        Timing:
          TimescaleMode: 0
          ExcludeFromHoldingPauses: 0
          ContributeToTotalDuration: 1
          InitialDelay: 0
          CooldownDuration: 0
          InterruptsOnStop: 1
          NumberOfRepeats: 0
          RepeatForever: 0
          DelayBetweenRepeats: 1
          PlayCount: 0
          LimitPlayCount: 0
          MaxPlayCount: 3
          SetPlayCountToZeroOnReset: 0
          MMFeedbacksDirectionCondition: 0
          PlayDirection: 0
          ConstantIntensity: 0
          UseIntensityInterval: 0
          IntensityIntervalMin: 0
          IntensityIntervalMax: 0
          Sequence: {fileID: 0}
          TrackID: 0
          Quantized: 0
          TargetBPM: 120
        AutomatedTargetAcquisition:
          Mode: 0
          ChildIndex: 0
        RandomizeOutput: 0
        RandomMultiplier: {x: 0.8, y: 1}
        RandomizeDuration: 0
        RandomDurationMultiplier: {x: 0.5, y: 2}
        UseRange: 0
        RangeDistance: 5
        UseRangeFalloff: 0
        RangeFalloff:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        RemapRangeFalloff: {x: 0, y: 1}
        Owner: {fileID: **********}
        DebugActive: 0
        AnimateRotationTarget: {fileID: 402199377}
        Mode: 2
        RotationSpace: 0
        AnimateRotationDuration: 0.2
        RemapCurveZero: 0
        RemapCurveOne: 0
        AnimateX: 1
        AnimateRotationTweenX:
          MMTweenDefinitionType: 1
          MMTweenCurve: 4
          Curve:
            serializedVersion: 2
            m_Curve:
            - serializedVersion: 3
              time: 0
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 0.88772655
              value: 1.1154296
              inSlope: 0.5001836
              outSlope: 0.5001836
              tangentMode: 0
              weightedMode: 0
              inWeight: 0.33333334
              outWeight: 0.33333334
            - serializedVersion: 3
              time: 1
              value: 1
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            m_PreInfinity: 2
            m_PostInfinity: 2
            m_RotationOrder: 4
          Initialized: 1
        AnimateY: 1
        AnimateRotationTweenY:
          MMTweenDefinitionType: 1
          MMTweenCurve: 4
          Curve:
            serializedVersion: 2
            m_Curve:
            - serializedVersion: 3
              time: 0
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 0.9080805
              value: 1.1004071
              inSlope: 0.41706467
              outSlope: 0.41706467
              tangentMode: 0
              weightedMode: 0
              inWeight: 0.33333334
              outWeight: 0.33333334
            - serializedVersion: 3
              time: 1
              value: 1
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            m_PreInfinity: 2
            m_PostInfinity: 2
            m_RotationOrder: 4
          Initialized: 1
        AnimateZ: 1
        AnimateRotationTweenZ:
          MMTweenDefinitionType: 1
          MMTweenCurve: 4
          Curve:
            serializedVersion: 2
            m_Curve:
            - serializedVersion: 3
              time: 0
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 1
              value: 1
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            m_PreInfinity: 2
            m_PostInfinity: 2
            m_RotationOrder: 4
          Initialized: 1
        AllowAdditivePlays: 0
        DetermineRotationOnPlay: 1
        ToDestinationSpace: 0
        DestinationAngles: {x: 0, y: 0, z: 0}
        ToDestinationTween:
          MMTweenDefinitionType: 1
          MMTweenCurve: 10
          Curve:
            serializedVersion: 2
            m_Curve:
            - serializedVersion: 3
              time: 0
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 1
              value: 1
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            m_PreInfinity: 2
            m_PostInfinity: 2
            m_RotationOrder: 4
          Initialized: 1
        AnimateRotationX:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 0.88772655
            value: 1.1154296
            inSlope: 0.5001836
            outSlope: 0.5001836
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        AnimateRotationY:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 0.9080805
            value: 1.1004071
            inSlope: 0.41706467
            outSlope: 0.41706467
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        AnimateRotationZ:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        ToDestinationCurve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
    - rid: 8
      type: {class: MMF_MMSoundManagerSound, ns: MoreMountains.Feedbacks, asm: MoreMountains.Tools}
      data:
        Active: 1
        UniqueID: 338160500
        Label: MMSoundManager Sound
        ChannelMode: 0
        Channel: 0
        MMChannelDefinition: {fileID: 0}
        Chance: 100
        DisplayColor: {r: 0, g: 0, b: 0, a: 1}
        Timing:
          TimescaleMode: 0
          ExcludeFromHoldingPauses: 0
          ContributeToTotalDuration: 1
          InitialDelay: 0
          CooldownDuration: 0
          InterruptsOnStop: 1
          NumberOfRepeats: 0
          RepeatForever: 0
          DelayBetweenRepeats: 1
          PlayCount: 0
          LimitPlayCount: 0
          MaxPlayCount: 3
          SetPlayCountToZeroOnReset: 0
          MMFeedbacksDirectionCondition: 0
          PlayDirection: 0
          ConstantIntensity: 0
          UseIntensityInterval: 0
          IntensityIntervalMin: 0
          IntensityIntervalMax: 0
          Sequence: {fileID: 0}
          TrackID: 0
          Quantized: 0
          TargetBPM: 120
        AutomatedTargetAcquisition:
          Mode: 0
          ChildIndex: 0
        RandomizeOutput: 0
        RandomMultiplier: {x: 0.8, y: 1}
        RandomizeDuration: 0
        RandomDurationMultiplier: {x: 0.5, y: 2}
        UseRange: 0
        RangeDistance: 5
        UseRangeFalloff: 0
        RangeFalloff:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        RemapRangeFalloff: {x: 0, y: 1}
        Owner: {fileID: **********}
        DebugActive: 0
        Sfx: {fileID: 8300000, guid: 86aa66a501648034f8ec69ba5d404514, type: 3}
        RandomSfx: []
        SequentialOrder: 0
        SequentialOrderHoldLast: 0
        SequentialOrderHoldCooldownDuration: 2
        RandomUnique: 0
        SoundDataSO: {fileID: 0}
        MinVolume: 0.5
        MaxVolume: 0.5
        MinPitch: 1
        MaxPitch: 1
        MmSoundManagerTrack: 0
        ID: 0
        AudioGroup: {fileID: 0}
        RecycleAudioSource: {fileID: 0}
        Loop: 0
        Persistent: 0
        DoNotPlayIfClipAlreadyPlaying: 0
        StopSoundOnFeedbackStop: 0
        Fade: 0
        FadeInitialVolume: 0
        FadeDuration: 1
        FadeTween:
          MMTweenDefinitionType: 0
          MMTweenCurve: 9
          Curve:
            serializedVersion: 2
            m_Curve:
            - serializedVersion: 3
              time: 0
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 1
              value: 1
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            m_PreInfinity: 2
            m_PostInfinity: 2
            m_RotationOrder: 4
          Initialized: 0
        SoloSingleTrack: 0
        SoloAllTracks: 0
        AutoUnSoloOnEnd: 0
        PanStereo: 0
        SpatialBlend: 0
        AttachToTransform: {fileID: 0}
        BypassEffects: 0
        BypassListenerEffects: 0
        BypassReverbZones: 0
        Priority: 0
        ReverbZoneMix: 0
        PlaybackTime: {x: 0, y: 0}
        PlaybackDuration: {x: 0, y: 0}
        DopplerLevel: 0
        Spread: 0
        RolloffMode: 0
        MinDistance: 0
        MaxDistance: 0
        UseCustomRolloffCurve: 0
        CustomRolloffCurve:
          serializedVersion: 2
          m_Curve: []
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        UseSpatialBlendCurve: 0
        SpatialBlendCurve:
          serializedVersion: 2
          m_Curve: []
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        UseReverbZoneMixCurve: 0
        ReverbZoneMixCurve:
          serializedVersion: 2
          m_Curve: []
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        UseSpreadCurve: 0
        SpreadCurve:
          serializedVersion: 2
          m_Curve: []
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        DrawGizmos: 0
        GizmosCenter: {fileID: 0}
        MinDistanceColor: {r: 0.37254903, g: 0.61960787, b: 0.627451, a: 1}
        MaxDistanceColor: {r: 1, g: 0.27058825, b: 0, a: 1}
        TestPlayButton:
          ButtonText: Debug Play Sound
        TestStopButton:
          ButtonText: Debug Stop Sound
        ResetSequentialIndexButton:
          ButtonText: Reset Sequential Index
    - rid: 9
      type: {class: MMF_ChromaticAberration, ns: MoreMountains.FeedbacksForThirdParty,
        asm: MoreMountains.Tools}
      data:
        Active: 1
        UniqueID: 1865738373
        Label: Chromatic Aberration
        ChannelMode: 0
        Channel: 0
        MMChannelDefinition: {fileID: 0}
        Chance: 100
        DisplayColor: {r: 0, g: 0, b: 0, a: 1}
        Timing:
          TimescaleMode: 0
          ExcludeFromHoldingPauses: 0
          ContributeToTotalDuration: 1
          InitialDelay: 0
          CooldownDuration: 0
          InterruptsOnStop: 1
          NumberOfRepeats: 0
          RepeatForever: 0
          DelayBetweenRepeats: 1
          PlayCount: 0
          LimitPlayCount: 0
          MaxPlayCount: 3
          SetPlayCountToZeroOnReset: 0
          MMFeedbacksDirectionCondition: 0
          PlayDirection: 0
          ConstantIntensity: 0
          UseIntensityInterval: 0
          IntensityIntervalMin: 0
          IntensityIntervalMax: 0
          Sequence: {fileID: 0}
          TrackID: 0
          Quantized: 0
          TargetBPM: 120
        AutomatedTargetAcquisition:
          Mode: 0
          ChildIndex: 0
        RandomizeOutput: 0
        RandomMultiplier: {x: 0.8, y: 1}
        RandomizeDuration: 0
        RandomDurationMultiplier: {x: 0.5, y: 2}
        UseRange: 0
        RangeDistance: 5
        UseRangeFalloff: 0
        RangeFalloff:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        RemapRangeFalloff: {x: 0, y: 1}
        Owner: {fileID: **********}
        DebugActive: 0
        Duration: 0.2
        ResetShakerValuesAfterShake: 1
        ResetTargetValuesAfterShake: 1
        RemapIntensityZero: 0
        RemapIntensityOne: 1
        Intensity:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 0.5
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        Amplitude: 1
        RelativeIntensity: 0
    - rid: 10
      type: {class: MMF_SetActive, ns: MoreMountains.Feedbacks, asm: MoreMountains.Tools}
      data:
        Active: 1
        UniqueID: -1220209040
        Label: Set Active - Toggle Dealer Button
        ChannelMode: 0
        Channel: 0
        MMChannelDefinition: {fileID: 0}
        Chance: 100
        DisplayColor: {r: 0, g: 0, b: 0, a: 1}
        Timing:
          TimescaleMode: 0
          ExcludeFromHoldingPauses: 0
          ContributeToTotalDuration: 1
          InitialDelay: 0
          CooldownDuration: 0
          InterruptsOnStop: 1
          NumberOfRepeats: 0
          RepeatForever: 0
          DelayBetweenRepeats: 1
          PlayCount: 0
          LimitPlayCount: 0
          MaxPlayCount: 3
          SetPlayCountToZeroOnReset: 0
          MMFeedbacksDirectionCondition: 0
          PlayDirection: 0
          ConstantIntensity: 0
          UseIntensityInterval: 0
          IntensityIntervalMin: 0
          IntensityIntervalMax: 0
          Sequence: {fileID: 0}
          TrackID: 0
          Quantized: 0
          TargetBPM: 120
        AutomatedTargetAcquisition:
          Mode: 0
          ChildIndex: 0
        RandomizeOutput: 0
        RandomMultiplier: {x: 0.8, y: 1}
        RandomizeDuration: 0
        RandomDurationMultiplier: {x: 0.5, y: 2}
        UseRange: 0
        RangeDistance: 5
        UseRangeFalloff: 0
        RangeFalloff:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        RemapRangeFalloff: {x: 0, y: 1}
        Owner: {fileID: **********}
        DebugActive: 0
        TargetGameObject: {fileID: 1713032064}
        ExtraTargetGameObjects: []
        SetStateOnInit: 0
        StateOnInit: 1
        SetStateOnPlay: 1
        StateOnPlay: 2
        SetStateOnStop: 0
        StateOnStop: 1
        SetStateOnReset: 0
        StateOnReset: 1
        SetStateOnSkip: 0
        StateOnSkip: 1
        SetStateOnPlayerComplete: 0
        StateOnPlayerComplete: 1
    - rid: 11
      type: {class: MMF_SetActive, ns: MoreMountains.Feedbacks, asm: MoreMountains.Tools}
      data:
        Active: 1
        UniqueID: -566676223
        Label: Set Active - Stack Button
        ChannelMode: 0
        Channel: 0
        MMChannelDefinition: {fileID: 0}
        Chance: 100
        DisplayColor: {r: 0, g: 0, b: 0, a: 1}
        Timing:
          TimescaleMode: 0
          ExcludeFromHoldingPauses: 0
          ContributeToTotalDuration: 1
          InitialDelay: 0
          CooldownDuration: 0
          InterruptsOnStop: 1
          NumberOfRepeats: 0
          RepeatForever: 0
          DelayBetweenRepeats: 1
          PlayCount: 0
          LimitPlayCount: 0
          MaxPlayCount: 3
          SetPlayCountToZeroOnReset: 0
          MMFeedbacksDirectionCondition: 1
          PlayDirection: 0
          ConstantIntensity: 0
          UseIntensityInterval: 0
          IntensityIntervalMin: 0
          IntensityIntervalMax: 0
          Sequence: {fileID: 0}
          TrackID: 0
          Quantized: 0
          TargetBPM: 120
        AutomatedTargetAcquisition:
          Mode: 0
          ChildIndex: 0
        RandomizeOutput: 0
        RandomMultiplier: {x: 0.8, y: 1}
        RandomizeDuration: 0
        RandomDurationMultiplier: {x: 0.5, y: 2}
        UseRange: 0
        RangeDistance: 5
        UseRangeFalloff: 0
        RangeFalloff:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        RemapRangeFalloff: {x: 0, y: 1}
        Owner: {fileID: **********}
        DebugActive: 0
        TargetGameObject: {fileID: 1703243937}
        ExtraTargetGameObjects: []
        SetStateOnInit: 0
        StateOnInit: 1
        SetStateOnPlay: 1
        StateOnPlay: 0
        SetStateOnStop: 0
        StateOnStop: 1
        SetStateOnReset: 0
        StateOnReset: 1
        SetStateOnSkip: 0
        StateOnSkip: 1
        SetStateOnPlayerComplete: 0
        StateOnPlayerComplete: 1
    - rid: 12
      type: {class: MMF_Pause, ns: MoreMountains.Feedbacks, asm: MoreMountains.Tools}
      data:
        Active: 1
        UniqueID: -687112231
        Label: Pause
        ChannelMode: 0
        Channel: 0
        MMChannelDefinition: {fileID: 0}
        Chance: 100
        DisplayColor: {r: 0, g: 0, b: 0, a: 1}
        Timing:
          TimescaleMode: 0
          ExcludeFromHoldingPauses: 0
          ContributeToTotalDuration: 1
          InitialDelay: 0
          CooldownDuration: 0
          InterruptsOnStop: 1
          NumberOfRepeats: 0
          RepeatForever: 0
          DelayBetweenRepeats: 1
          PlayCount: 0
          LimitPlayCount: 0
          MaxPlayCount: 3
          SetPlayCountToZeroOnReset: 0
          MMFeedbacksDirectionCondition: 2
          PlayDirection: 0
          ConstantIntensity: 0
          UseIntensityInterval: 0
          IntensityIntervalMin: 0
          IntensityIntervalMax: 0
          Sequence: {fileID: 0}
          TrackID: 0
          Quantized: 0
          TargetBPM: 120
        AutomatedTargetAcquisition:
          Mode: 0
          ChildIndex: 0
        RandomizeOutput: 0
        RandomMultiplier: {x: 0.8, y: 1}
        RandomizeDuration: 0
        RandomDurationMultiplier: {x: 0.5, y: 2}
        UseRange: 0
        RangeDistance: 5
        UseRangeFalloff: 0
        RangeFalloff:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        RemapRangeFalloff: {x: 0, y: 1}
        Owner: {fileID: **********}
        DebugActive: 0
        PauseDuration: 1.5
        RandomizePauseDuration: 0
        MinPauseDuration: 1
        MaxPauseDuration: 3
        RandomizeOnEachPlay: 1
        ScriptDriven: 0
        AutoResume: 0
        AutoResumeAfter: 0.25
    - rid: 13
      type: {class: MMF_Events, ns: MoreMountains.Feedbacks, asm: MoreMountains.Tools}
      data:
        Active: 1
        UniqueID: -515249494
        Label: Events - Reset Cards
        ChannelMode: 0
        Channel: 0
        MMChannelDefinition: {fileID: 0}
        Chance: 100
        DisplayColor: {r: 0, g: 0, b: 0, a: 1}
        Timing:
          TimescaleMode: 0
          ExcludeFromHoldingPauses: 0
          ContributeToTotalDuration: 1
          InitialDelay: 0
          CooldownDuration: 0
          InterruptsOnStop: 1
          NumberOfRepeats: 0
          RepeatForever: 0
          DelayBetweenRepeats: 1
          PlayCount: 0
          LimitPlayCount: 0
          MaxPlayCount: 3
          SetPlayCountToZeroOnReset: 0
          MMFeedbacksDirectionCondition: 2
          PlayDirection: 0
          ConstantIntensity: 0
          UseIntensityInterval: 0
          IntensityIntervalMin: 0
          IntensityIntervalMax: 0
          Sequence: {fileID: 0}
          TrackID: 0
          Quantized: 0
          TargetBPM: 120
        AutomatedTargetAcquisition:
          Mode: 0
          ChildIndex: 0
        RandomizeOutput: 0
        RandomMultiplier: {x: 0.8, y: 1}
        RandomizeDuration: 0
        RandomDurationMultiplier: {x: 0.5, y: 2}
        UseRange: 0
        RangeDistance: 5
        UseRangeFalloff: 0
        RangeFalloff:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        RemapRangeFalloff: {x: 0, y: 1}
        Owner: {fileID: **********}
        DebugActive: 0
        PlayEvents:
          m_PersistentCalls:
            m_Calls:
            - m_Target: {fileID: 1884460090}
              m_TargetAssemblyTypeName: 
              m_MethodName: PlayFeedbacksOnlyIfReversed
              m_Mode: 1
              m_Arguments:
                m_ObjectArgument: {fileID: 0}
                m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
                m_IntArgument: 0
                m_FloatArgument: 0
                m_StringArgument: 
                m_BoolArgument: 0
              m_CallState: 2
            - m_Target: {fileID: 773926780}
              m_TargetAssemblyTypeName: 
              m_MethodName: PlayFeedbacksOnlyIfReversed
              m_Mode: 1
              m_Arguments:
                m_ObjectArgument: {fileID: 0}
                m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
                m_IntArgument: 0
                m_FloatArgument: 0
                m_StringArgument: 
                m_BoolArgument: 0
              m_CallState: 2
            - m_Target: {fileID: 146825166}
              m_TargetAssemblyTypeName: 
              m_MethodName: PlayFeedbacksOnlyIfReversed
              m_Mode: 1
              m_Arguments:
                m_ObjectArgument: {fileID: 0}
                m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
                m_IntArgument: 0
                m_FloatArgument: 0
                m_StringArgument: 
                m_BoolArgument: 0
              m_CallState: 2
            - m_Target: {fileID: 332578521}
              m_TargetAssemblyTypeName: 
              m_MethodName: PlayFeedbacksOnlyIfReversed
              m_Mode: 1
              m_Arguments:
                m_ObjectArgument: {fileID: 0}
                m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
                m_IntArgument: 0
                m_FloatArgument: 0
                m_StringArgument: 
                m_BoolArgument: 0
              m_CallState: 2
        StopEvents:
          m_PersistentCalls:
            m_Calls: []
        InitializationEvents:
          m_PersistentCalls:
            m_Calls: []
        ResetEvents:
          m_PersistentCalls:
            m_Calls: []
--- !u!4 &1817957848
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1817957845}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -142.4, y: 40.91, z: 71.4}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1876896487 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 7465677149715185876, guid: b3f6473bddc357f499dbcb5855925796,
    type: 3}
  m_PrefabInstance: {fileID: 1623701838}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &1884460090 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 427014296157349933, guid: b3f6473bddc357f499dbcb5855925796,
    type: 3}
  m_PrefabInstance: {fileID: 1623701838}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6da43522623d4704e979466dc7650b65, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &2084567608
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2084567609}
  - component: {fileID: 2084567611}
  - component: {fileID: 2084567610}
  m_Layer: 5
  m_Name: Container
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2084567609
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2084567608}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.8, y: 0.8, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1623701839}
  - {fileID: 1051631847}
  - {fileID: 331905145}
  - {fileID: 402199377}
  m_Father: {fileID: 224062016678292244}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 100, y: 100}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &2084567610
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2084567608}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6bf8e915527fa424a8b17dc168fe20eb, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  ChannelMode: 0
  Channel: 0
  MMChannelDefinition: {fileID: 0}
  CooldownBetweenShakes: 0
--- !u!114 &2084567611
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2084567608}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 347013242dca7c44fb5001793033ea59, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  UpdateMode: 0
  PositionActive: 1
  RotationActive: 0
  ScaleActive: 0
  PositionWiggleProperties:
    WigglePermitted: 0
    WiggleType: 1
    UseUnscaledTime: 0
    StartWigglingAutomatically: 1
    SmoothPingPong: 1
    UseSpeedCurve: 0
    SpeedCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    FrequencyMin: 0.05
    FrequencyMax: 0.05
    AmplitudeMin: {x: -10, y: -10, z: -10}
    AmplitudeMax: {x: 10, y: 10, z: 10}
    RelativeAmplitude: 1
    UniformValues: 0
    ForceVectorLength: 0
    ForcedVectorLength: 1
    Curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    RemapCurveZeroMin: {x: 0, y: 0, z: 0}
    RemapCurveZeroMax: {x: 0, y: 0, z: 0}
    RemapCurveOneMin: {x: 1, y: 1, z: 1}
    RemapCurveOneMax: {x: 1, y: 1, z: 1}
    RelativeCurveAmplitude: 1
    CurvePingPong: 0
    PauseMin: 0
    PauseMax: 0
    LimitedTime: 0
    LimitedTimeTotal: 0
    LimitedTimeFalloff:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    LimitedTimeResetValue: 1
    LimitedTimeLeft: 0
    NoiseFrequencyMin: {x: 0, y: 0, z: 0}
    NoiseFrequencyMax: {x: 1, y: 1, z: 1}
    NoiseShiftMin: {x: 0, y: 0, z: 0}
    NoiseShiftMax: {x: 0, y: 0, z: 0}
  RotationWiggleProperties:
    WigglePermitted: 1
    WiggleType: 1
    UseUnscaledTime: 0
    StartWigglingAutomatically: 1
    SmoothPingPong: 1
    UseSpeedCurve: 0
    SpeedCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    FrequencyMin: 0
    FrequencyMax: 1
    AmplitudeMin: {x: 0, y: 0, z: 0}
    AmplitudeMax: {x: 1, y: 1, z: 1}
    RelativeAmplitude: 1
    UniformValues: 0
    ForceVectorLength: 0
    ForcedVectorLength: 1
    Curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    RemapCurveZeroMin: {x: 0, y: 0, z: 0}
    RemapCurveZeroMax: {x: 0, y: 0, z: 0}
    RemapCurveOneMin: {x: 1, y: 1, z: 1}
    RemapCurveOneMax: {x: 1, y: 1, z: 1}
    RelativeCurveAmplitude: 1
    CurvePingPong: 0
    PauseMin: 0
    PauseMax: 0
    LimitedTime: 0
    LimitedTimeTotal: 0
    LimitedTimeFalloff:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    LimitedTimeResetValue: 1
    LimitedTimeLeft: 0
    NoiseFrequencyMin: {x: 0, y: 0, z: 0}
    NoiseFrequencyMax: {x: 1, y: 1, z: 1}
    NoiseShiftMin: {x: 0, y: 0, z: 0}
    NoiseShiftMax: {x: 0, y: 0, z: 0}
  ScaleWiggleProperties:
    WigglePermitted: 1
    WiggleType: 1
    UseUnscaledTime: 0
    StartWigglingAutomatically: 1
    SmoothPingPong: 1
    UseSpeedCurve: 0
    SpeedCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    FrequencyMin: 0
    FrequencyMax: 1
    AmplitudeMin: {x: 0, y: 0, z: 0}
    AmplitudeMax: {x: 1, y: 1, z: 1}
    RelativeAmplitude: 1
    UniformValues: 0
    ForceVectorLength: 0
    ForcedVectorLength: 1
    Curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    RemapCurveZeroMin: {x: 0, y: 0, z: 0}
    RemapCurveZeroMax: {x: 0, y: 0, z: 0}
    RemapCurveOneMin: {x: 1, y: 1, z: 1}
    RemapCurveOneMax: {x: 1, y: 1, z: 1}
    RelativeCurveAmplitude: 1
    CurvePingPong: 0
    PauseMin: 0
    PauseMax: 0
    LimitedTime: 0
    LimitedTimeTotal: 0
    LimitedTimeFalloff:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    LimitedTimeResetValue: 1
    LimitedTimeLeft: 0
    NoiseFrequencyMin: {x: 0, y: 0, z: 0}
    NoiseFrequencyMax: {x: 1, y: 1, z: 1}
    NoiseShiftMin: {x: 0, y: 0, z: 0}
    NoiseShiftMax: {x: 0, y: 0, z: 0}
  DebugWiggleDuration: 2
--- !u!1 &1530778140380286
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 224062016678292244}
  - component: {fileID: 223305196734712864}
  - component: {fileID: 114817721475771694}
  - component: {fileID: 114562784829046912}
  m_Layer: 5
  m_Name: Canvas
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &1619623234221280
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4242223709885388}
  - component: {fileID: 20353287653893070}
  - component: {fileID: 124369366022169824}
  - component: {fileID: 1619623234221281}
  - component: {fileID: 1619623234221282}
  m_Layer: 0
  m_Name: UICamera
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1619623234221281
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1619623234221280}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 948f4100a11a5c24981795d21301da5c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  volumeTrigger: {fileID: 4242223709885388}
  volumeLayer:
    serializedVersion: 2
    m_Bits: 4294967295
  stopNaNPropagation: 1
  finalBlitToCameraTarget: 0
  antialiasingMode: 1
  temporalAntialiasing:
    jitterSpread: 0.75
    sharpness: 0.25
    stationaryBlending: 0.95
    motionBlending: 0.85
  subpixelMorphologicalAntialiasing:
    quality: 2
  fastApproximateAntialiasing:
    fastMode: 0
    keepAlpha: 0
  fog:
    enabled: 1
    excludeSkybox: 1
  debugLayer:
    lightMeter:
      width: 512
      height: 256
      showCurves: 1
    histogram:
      width: 512
      height: 256
      channel: 3
    waveform:
      exposure: 0.12
      height: 256
    vectorscope:
      size: 256
      exposure: 0.12
    overlaySettings:
      linearDepth: 0
      motionColorIntensity: 4
      motionGridSize: 64
      colorBlindnessType: 0
      colorBlindnessStrength: 1
  m_Resources: {fileID: 11400000, guid: d82512f9c8e5d4a4d938b575d47f88d4, type: 2}
  m_ShowToolkit: 0
  m_ShowCustomSorter: 0
  breakBeforeColorGrading: 0
  m_BeforeTransparentBundles: []
  m_BeforeStackBundles: []
  m_AfterStackBundles: []
--- !u!114 &1619623234221282
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1619623234221280}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 72ece51f2901e7445ab60da3685d6b5f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_ShowDebugText: 0
  m_ShowCameraFrustum: 1
  m_IgnoreTimeScale: 0
  m_WorldUpOverride: {fileID: 0}
  m_UpdateMethod: 2
  m_BlendUpdateMethod: 1
  m_DefaultBlend:
    m_Style: 1
    m_Time: 2
    m_CustomCurve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
  m_CustomBlends: {fileID: 0}
  m_CameraCutEvent:
    m_PersistentCalls:
      m_Calls: []
  m_CameraActivatedEvent:
    m_PersistentCalls:
      m_Calls: []
--- !u!4 &4242223709885388
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1619623234221280}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -142.4, y: 40.91, z: -28.6}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 224062016678292244}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!20 &20353287653893070
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1619623234221280}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 1
  m_BackGroundColor: {r: 0.19215687, g: 0.3019608, b: 0.4745098, a: 0.019607844}
  m_projectionMatrixMode: 1
  m_GateFitMode: 2
  m_FOVAxisMode: 0
  m_Iso: 200
  m_ShutterSpeed: 0.005
  m_Aperture: 16
  m_FocusDistance: 10
  m_FocalLength: 50
  m_BladeCount: 5
  m_Curvature: {x: 2, y: 11}
  m_BarrelClipping: 0.25
  m_Anamorphism: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.3
  far clip plane: 1000
  field of view: 60
  orthographic: 1
  orthographic size: 10
  m_Depth: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 32
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 1
  m_AllowMSAA: 1
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 0
  m_OcclusionCulling: 1
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!114 &114562784829046912
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1530778140380286}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc42784cf147c0c48a680349fa168899, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 0
--- !u!114 &114817721475771694
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1530778140380286}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0cd44c1031e13a943bb63640046fad76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UiScaleMode: 1
  m_ReferencePixelsPerUnit: 100
  m_ScaleFactor: 1
  m_ReferenceResolution: {x: 2560, y: 1440}
  m_ScreenMatchMode: 1
  m_MatchWidthOrHeight: 0
  m_PhysicalUnit: 3
  m_FallbackScreenDPI: 96
  m_DefaultSpriteDPI: 96
  m_DynamicPixelsPerUnit: 1
  m_PresetInfoIsWorld: 0
--- !u!124 &124369366022169824
Behaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1619623234221280}
  m_Enabled: 1
--- !u!223 &223305196734712864
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1530778140380286}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 1
  m_Camera: {fileID: 20353287653893070}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_VertexColorAlwaysGammaSpace: 0
  m_AdditionalShaderChannelsFlag: 25
  m_UpdateRectTransformForStandalone: 0
  m_SortingLayerID: 0
  m_SortingOrder: 2
  m_TargetDisplay: 0
--- !u!224 &224062016678292244
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1530778140380286}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0, y: 0, z: 0}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1504572917}
  - {fileID: 2084567609}
  - {fileID: 1713032065}
  - {fileID: 1703243938}
  - {fileID: 1044802376}
  m_Father: {fileID: 4242223709885388}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0, y: 0}
--- !u!1660057539 &9223372036854775807
SceneRoots:
  m_ObjectHideFlags: 0
  m_Roots:
  - {fileID: 617180824}
  - {fileID: 886102149}
  - {fileID: 182199741}
  - {fileID: 4242223709885388}
  - {fileID: 251850594}
  - {fileID: 601855543}
  - {fileID: 4684827}
  - {fileID: 484690342}
  - {fileID: 1817957848}
