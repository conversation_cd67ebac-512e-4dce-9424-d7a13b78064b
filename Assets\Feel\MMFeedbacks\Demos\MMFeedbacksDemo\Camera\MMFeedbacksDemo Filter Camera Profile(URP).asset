%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-7474256355635030544
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5485954d14dfb9a4c8ead8edb0ded5b1, type: 3}
  m_Name: LiftGammaGain
  m_EditorClassIdentifier: 
  active: 1
  lift:
    m_OverrideState: 1
    m_Value: {x: 1, y: 1, z: 1, w: 0}
  gamma:
    m_OverrideState: 1
    m_Value: {x: 1, y: 1, z: 1, w: 0}
  gain:
    m_OverrideState: 1
    m_Value: {x: 1, y: 1, z: 1, w: 0}
--- !u!114 &-6629001093191209093
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 81180773991d8724ab7f2d216912b564, type: 3}
  m_Name: ChromaticAberration
  m_EditorClassIdentifier: 
  active: 1
  intensity:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &-5902885961058659040
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 899c54efeace73346a0a16faa3afe726, type: 3}
  m_Name: Vignette
  m_EditorClassIdentifier: 
  active: 1
  color:
    m_OverrideState: 1
    m_Value: {r: 0, g: 0, b: 0, a: 1}
  center:
    m_OverrideState: 1
    m_Value: {x: 0.5, y: 0.5}
  intensity:
    m_OverrideState: 1
    m_Value: 0.457
  smoothness:
    m_OverrideState: 1
    m_Value: 0.2
  rounded:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &-5512120271668119425
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 97c23e3b12dc18c42a140437e53d3951, type: 3}
  m_Name: Tonemapping
  m_EditorClassIdentifier: 
  active: 1
  mode:
    m_OverrideState: 1
    m_Value: 0
  neutralHDRRangeReductionMode:
    m_OverrideState: 0
    m_Value: 2
  acesPreset:
    m_OverrideState: 0
    m_Value: 3
  hueShiftAmount:
    m_OverrideState: 0
    m_Value: 0
  detectPaperWhite:
    m_OverrideState: 0
    m_Value: 0
  paperWhite:
    m_OverrideState: 0
    m_Value: 300
  detectBrightnessLimits:
    m_OverrideState: 0
    m_Value: 1
  minNits:
    m_OverrideState: 0
    m_Value: 0.005
  maxNits:
    m_OverrideState: 0
    m_Value: 1000
--- !u!114 &-5431587598214616624
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3eb4b772797da9440885e8bd939e9560, type: 3}
  m_Name: ColorCurves
  m_EditorClassIdentifier: 
  active: 1
  master:
    m_OverrideState: 1
    m_Value:
      <length>k__BackingField: 2
      m_Loop: 0
      m_ZeroValue: 0
      m_Range: 1
      m_Curve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 1
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 1
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  red:
    m_OverrideState: 1
    m_Value:
      <length>k__BackingField: 2
      m_Loop: 0
      m_ZeroValue: 0
      m_Range: 1
      m_Curve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 1
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 1
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  green:
    m_OverrideState: 1
    m_Value:
      <length>k__BackingField: 2
      m_Loop: 0
      m_ZeroValue: 0
      m_Range: 1
      m_Curve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 1
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 1
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  blue:
    m_OverrideState: 1
    m_Value:
      <length>k__BackingField: 2
      m_Loop: 0
      m_ZeroValue: 0
      m_Range: 1
      m_Curve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 1
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 1
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  hueVsHue:
    m_OverrideState: 1
    m_Value:
      <length>k__BackingField: 0
      m_Loop: 0
      m_ZeroValue: 0
      m_Range: 1
      m_Curve:
        serializedVersion: 2
        m_Curve: []
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  hueVsSat:
    m_OverrideState: 1
    m_Value:
      <length>k__BackingField: 0
      m_Loop: 0
      m_ZeroValue: 0
      m_Range: 1
      m_Curve:
        serializedVersion: 2
        m_Curve: []
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  satVsSat:
    m_OverrideState: 1
    m_Value:
      <length>k__BackingField: 0
      m_Loop: 0
      m_ZeroValue: 0
      m_Range: 1
      m_Curve:
        serializedVersion: 2
        m_Curve: []
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  lumVsSat:
    m_OverrideState: 1
    m_Value:
      <length>k__BackingField: 0
      m_Loop: 0
      m_ZeroValue: 0
      m_Range: 1
      m_Curve:
        serializedVersion: 2
        m_Curve: []
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
--- !u!114 &-3721513375283081566
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cdfbdbb87d3286943a057f7791b43141, type: 3}
  m_Name: ChannelMixer
  m_EditorClassIdentifier: 
  active: 1
  redOutRedIn:
    m_OverrideState: 1
    m_Value: 100
  redOutGreenIn:
    m_OverrideState: 1
    m_Value: 0
  redOutBlueIn:
    m_OverrideState: 1
    m_Value: 0
  greenOutRedIn:
    m_OverrideState: 1
    m_Value: 0
  greenOutGreenIn:
    m_OverrideState: 1
    m_Value: 100
  greenOutBlueIn:
    m_OverrideState: 1
    m_Value: 0
  blueOutRedIn:
    m_OverrideState: 1
    m_Value: 0
  blueOutGreenIn:
    m_OverrideState: 1
    m_Value: 0
  blueOutBlueIn:
    m_OverrideState: 1
    m_Value: 100
--- !u!114 &-3030343540557805223
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 221518ef91623a7438a71fef23660601, type: 3}
  m_Name: WhiteBalance
  m_EditorClassIdentifier: 
  active: 1
  temperature:
    m_OverrideState: 1
    m_Value: 0
  tint:
    m_OverrideState: 1
    m_Value: 0
--- !u!114 &-1818775727065305760
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c01700fd266d6914ababb731e09af2eb, type: 3}
  m_Name: DepthOfField
  m_EditorClassIdentifier: 
  active: 1
  mode:
    m_OverrideState: 1
    m_Value: 2
  gaussianStart:
    m_OverrideState: 0
    m_Value: 10
  gaussianEnd:
    m_OverrideState: 0
    m_Value: 30
  gaussianMaxRadius:
    m_OverrideState: 0
    m_Value: 1
  highQualitySampling:
    m_OverrideState: 0
    m_Value: 0
  focusDistance:
    m_OverrideState: 1
    m_Value: 5.37
  aperture:
    m_OverrideState: 1
    m_Value: 2.5
  focalLength:
    m_OverrideState: 1
    m_Value: 78
  bladeCount:
    m_OverrideState: 0
    m_Value: 5
  bladeCurvature:
    m_OverrideState: 0
    m_Value: 1
  bladeRotation:
    m_OverrideState: 0
    m_Value: 0
--- !u!114 &-1766022084085301757
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ccf1aba9553839d41ae37dd52e9ebcce, type: 3}
  m_Name: MotionBlur
  m_EditorClassIdentifier: 
  active: 1
  mode:
    m_OverrideState: 0
    m_Value: 0
  quality:
    m_OverrideState: 0
    m_Value: 0
  intensity:
    m_OverrideState: 0
    m_Value: 0.75
  clamp:
    m_OverrideState: 0
    m_Value: 0.05
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d7fd9488000d3734a9e00ee676215985, type: 3}
  m_Name: MMFeedbacksDemo Filter Camera Profile(URP)
  m_EditorClassIdentifier: 
  components:
  - {fileID: -5902885961058659040}
  - {fileID: 2834118435614672052}
  - {fileID: -6629001093191209093}
  - {fileID: -5512120271668119425}
  - {fileID: -3030343540557805223}
  - {fileID: 1859374552297085189}
  - {fileID: -3721513375283081566}
  - {fileID: -7474256355635030544}
  - {fileID: -5431587598214616624}
  - {fileID: -1818775727065305760}
  - {fileID: 569892535135060064}
  - {fileID: -1766022084085301757}
--- !u!114 &569892535135060064
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c5e1dc532bcb41949b58bc4f2abfbb7e, type: 3}
  m_Name: LensDistortion
  m_EditorClassIdentifier: 
  active: 1
  intensity:
    m_OverrideState: 1
    m_Value: 0.212
  xMultiplier:
    m_OverrideState: 1
    m_Value: 1
  yMultiplier:
    m_OverrideState: 1
    m_Value: 1
  center:
    m_OverrideState: 1
    m_Value: {x: 0, y: 0}
  scale:
    m_OverrideState: 1
    m_Value: 1
--- !u!114 &1859374552297085189
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 66f335fb1ffd8684294ad653bf1c7564, type: 3}
  m_Name: ColorAdjustments
  m_EditorClassIdentifier: 
  active: 1
  postExposure:
    m_OverrideState: 1
    m_Value: -0.5
  contrast:
    m_OverrideState: 1
    m_Value: 100
  colorFilter:
    m_OverrideState: 1
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  hueShift:
    m_OverrideState: 1
    m_Value: 0
  saturation:
    m_OverrideState: 1
    m_Value: -100
--- !u!114 &2834118435614672052
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0b2db86121404754db890f4c8dfe81b2, type: 3}
  m_Name: Bloom
  m_EditorClassIdentifier: 
  active: 1
  skipIterations:
    m_OverrideState: 0
    m_Value: 1
  threshold:
    m_OverrideState: 1
    m_Value: 1
  intensity:
    m_OverrideState: 1
    m_Value: 3.2
  scatter:
    m_OverrideState: 1
    m_Value: 0.39000002
  clamp:
    m_OverrideState: 1
    m_Value: 65471.57
  tint:
    m_OverrideState: 1
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  highQualityFiltering:
    m_OverrideState: 1
    m_Value: 1
  downscale:
    m_OverrideState: 0
    m_Value: 0
  maxIterations:
    m_OverrideState: 0
    m_Value: 6
  dirtTexture:
    m_OverrideState: 1
    m_Value: {fileID: 0}
    dimension: 1
  dirtIntensity:
    m_OverrideState: 1
    m_Value: 0
