<?xml version="1.0" encoding="UTF-8"?><package><identifier v="Unsaved Package"/><formatVersion v="1.1.0.202302"/><updaterVersion v="1.1.0.202302"/><fileUID v="{b48e64e1-38b9-4678-af87-c32194b2b904}"/><versionUID v="0"/><dependencies><dependency><filename v="sbs://hbao_2.sbs"/><uid v="1297162667"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://non_uniform_blur.sbs"/><uid v="1290776959"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://pattern_tile_sampler.sbs"/><uid v="1449399972"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://auto_levels.sbs"/><uid v="1406270631"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_perlin_noise.sbs"/><uid v="1407332030"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://histogram_scan.sbs"/><uid v="1449404462"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://blur_hq.sbs"/><uid v="1299236171"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://invert.sbs"/><uid v="1368115694"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://highpass.sbs"/><uid v="1406245499"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://rgba_split.sbs"/><uid v="1406393461"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://curvature_smooth.sbs"/><uid v="1449590280"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://quantize.sbs"/><uid v="1406322980"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_crystal_1.sbs"/><uid v="1407271237"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://pattern_shape.sbs"/><uid v="1311189026"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://gradient_linear_2.sbs"/><uid v="1406282768"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://waveform_1.sbs"/><uid v="1353873470"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_anisotropic_noise.sbs"/><uid v="1406350129"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency></dependencies><content><graph><identifier v="roots"/><uid v="1406245786"/><graphOutputs><graphoutput><identifier v="basecolor_1"/><uid v="1406259860"/><attributes><label v="Base Color"/></attributes><usages><usage><components v="RGBA"/><name v="baseColor"/></usage></usages><group v="Material"/></graphoutput><graphoutput><identifier v="normal_1"/><uid v="1406259862"/><attributes><label v="Normal"/></attributes><usages><usage><components v="RGBA"/><name v="normal"/></usage></usages><group v="Material"/></graphoutput><graphoutput><identifier v="roughness_1"/><uid v="1406259864"/><attributes><label v="Roughness"/></attributes><usages><usage><components v="RGBA"/><name v="roughness"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput><graphoutput><identifier v="metallic_1"/><uid v="1406259866"/><attributes><label v="Metallic"/></attributes><usages><usage><components v="RGBA"/><name v="metallic"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput><graphoutput><identifier v="height_1"/><uid v="1406259868"/><attributes><label v="Height"/></attributes><usages><usage><components v="RGBA"/><name v="height"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput><graphoutput><identifier v="AO"/><uid v="1406259870"/><usages><usage><components v="RGBA"/><name v="ambientOcclusion"/></usage></usages><channels v="2"/></graphoutput></graphOutputs><compNodes><compNode><uid v="1406259852"/><connections><connection><identifier v="input1"/><connRef v="1406259856"/><connRefOutput v="1355424890"/></connection></connections><GUILayout><gpos v="6128.00146 -1104.00024 0"/></GUILayout><compOutputs><compOutput><uid v="1355431358"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="normal"/><parameters><parameter><name v="input2alpha"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="inversedy"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="10"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1406259853"/><GUILayout><gpos v="6128.00098 -1584.00024 0"/></GUILayout><compOutputs><compOutput><uid v="1355432809"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="uniform"/><parameters><parameter><name v="colorswitch"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="outputcolor"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.420000017 0.420000017 0.420000017 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1406259855"/><GUILayout><gpos v="6128.00098 -1424.00037 0"/></GUILayout><compOutputs><compOutput><uid v="1355432809"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="uniform"/><parameters><parameter><name v="colorswitch"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="outputcolor"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.552941203 0.552941203 0.552941203 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1406259856"/><connections><connection><identifier v="destination"/><connRef v="1406348348"/><connRefOutput v="1406348349"/></connection></connections><GUILayout><gpos v="5904.00098 -944.000244 0"/></GUILayout><compOutputs><compOutput><uid v="1355424890"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1406259857"/><connections><connection><identifier v="input"/><connRef v="1406259856"/><connRefOutput v="1355424890"/></connection></connections><GUILayout><gpos v="6128 -1253.33337 0"/></GUILayout><compOutputs><compOutput><uid v="1355424961"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///hbao?dependency=1297162667"/><parameters><parameter><name v="height_depth"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.129999995"/></paramValue></parameter><parameter><name v="samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="16"/></paramValue></parameter><parameter><name v="surface_size"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="158.369995"/></paramValue></parameter><parameter><name v="height_depth_cm"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="72.4000015"/></paramValue></parameter><parameter><name v="radius"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0199999996"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1355424961"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406259858"/><GUILayout><gpos v="6128.00098 -784.000244 0"/></GUILayout><compOutputs><compOutput><uid v="1355432809"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="uniform"/><parameters><parameter><name v="colorswitch"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1406259859"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1406399345"/><connRefOutput v="1406399346"/></connection></connections><GUILayout><gpos v="6352.00146 -1584.00024 0"/></GUILayout><compImplementation><compOutputBridge><output v="1406259860"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1406259861"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1406259852"/><connRefOutput v="1355431358"/></connection></connections><GUILayout><gpos v="6352.00049 -1104.00024 0"/></GUILayout><compImplementation><compOutputBridge><output v="1406259862"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1406259863"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1406415779"/><connRefOutput v="1406397263"/></connection></connections><GUILayout><gpos v="6352.00146 -1424.00024 0"/></GUILayout><compImplementation><compOutputBridge><output v="1406259864"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1406259865"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1406259858"/><connRefOutput v="1355432809"/></connection></connections><GUILayout><gpos v="6352.00049 -784.000244 0"/></GUILayout><compImplementation><compOutputBridge><output v="1406259866"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1406259867"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1406259856"/><connRefOutput v="1355424890"/></connection></connections><GUILayout><gpos v="6352.00146 -944.000244 0"/></GUILayout><compImplementation><compOutputBridge><output v="1406259868"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1406259869"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1406259857"/><connRefOutput v="1355424961"/></connection></connections><GUILayout><gpos v="6352.00049 -1264.00037 0"/></GUILayout><compImplementation><compOutputBridge><output v="1406259870"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1406262464"/><GUILayout><gpos v="-4816 354.222534 0"/></GUILayout><compOutputs><compOutput><uid v="1406262465"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///waveform_1?dependency=1353873470"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1024"/></paramValue></parameter><parameter><name v="SizeMax"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.299999982"/></paramValue></parameter><parameter><name v="WaveNumber"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="Noise"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="Pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="9"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1406262465"/><identifier v="Waveform"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406262506"/><connections><connection><identifier v="input1"/><connRef v="1406262464"/><connRefOutput v="1406262465"/></connection></connections><GUILayout><gpos v="-4656 354.222534 0"/></GUILayout><compOutputs><compOutput><uid v="1406262507"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="transformation"/><parameters><parameter><name v="matrix22"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.0167999994 0.999800026 -0.999800026 0.0167999994"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1406264574"/><connections><connection><identifier v="Source"/><connRef v="1406262506"/><connRefOutput v="1406262507"/></connection><connection><identifier v="Effect"/><connRef v="1406262506"/><connRefOutput v="1406262507"/></connection></connections><GUILayout><gpos v="-4400 336 0"/></GUILayout><compOutputs><compOutput><uid v="1406264575"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///non_uniform_blur_grayscale?dependency=1290776959"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="16"/></paramValue></parameter><parameter><name v="Blades"/><relativeTo v="0"/><paramValue><constantValueInt32 v="9"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1406264575"/><identifier v="Non_Uniform_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406268487"/><connections><connection><identifier v="pattern_input"/><connRef v="1406288318"/><connRefOutput v="1406268839"/></connection></connections><GUILayout><gpos v="-2277.33325 -528 0"/></GUILayout><compOutputs><compOutput><uid v="1406268488"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1449399972"/><parameters><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="7"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="12.0500002"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.659999967"/></paramValue></parameter><parameter><name v="color_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.870000005"/></paramValue></parameter><parameter><name v="rotation_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0500000007"/></paramValue></parameter><parameter><name v="rotation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0833333358"/></paramValue></parameter><parameter><name v="size_random"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.389999986 0.119999997"/></paramValue></parameter><parameter><name v="size"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="1.03999996 1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1406268488"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406268680"/><connections><connection><identifier v="pattern_input"/><connRef v="1406279782"/><connRefOutput v="1406279783"/></connection><connection><identifier v="mask_map_input"/><connRef v="1406279782"/><connRefOutput v="1406279783"/></connection></connections><GUILayout><gpos v="-3440 144 0"/></GUILayout><compOutputs><compOutput><uid v="1406268488"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1449399972"/><parameters><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="50"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="13.7799997"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.589999974"/></paramValue></parameter><parameter><name v="color_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.839999974"/></paramValue></parameter><parameter><name v="rotation_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.00999999978"/></paramValue></parameter><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="42"/></paramValue></parameter><parameter><name v="displacement_angle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0111111114"/></paramValue></parameter><parameter><name v="rotation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.180222228"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1406268488"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406268838"/><connections><connection><identifier v="source"/><connRef v="1406311250"/><connRefOutput v="1406275336"/></connection><connection><identifier v="destination"/><connRef v="1406264574"/><connRefOutput v="1406264575"/></connection></connections><GUILayout><gpos v="-3056 176 0"/></GUILayout><compOutputs><compOutput><uid v="1406268839"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.649999976"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1406271181"/><connections><connection><identifier v="Input"/><connRef v="1406268487"/><connRefOutput v="1406268488"/></connection></connections><GUILayout><gpos v="-2085.33325 -528 0"/></GUILayout><compOutputs><compOutput><uid v="1406271182"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1406270631"/><parameters/><outputBridgings><outputBridging><uid v="1406271182"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406271690"/><GUILayout><gpos v="-2277.33325 -304 0"/></GUILayout><compOutputs><compOutput><uid v="1406271691"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///perlin_noise?dependency=1407332030"/><parameters><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1406271691"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406271812"/><connections><connection><identifier v="input1"/><connRef v="1406271690"/><connRefOutput v="1406271691"/></connection></connections><GUILayout><gpos v="-2117.33325 -304 0"/></GUILayout><compOutputs><compOutput><uid v="1406271813"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blur"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="20"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1406275335"/><connections><connection><identifier v="inputintensity"/><connRef v="1406271812"/><connRefOutput v="1406271813"/></connection><connection><identifier v="input1"/><connRef v="1406390000"/><connRefOutput v="1406390001"/></connection></connections><GUILayout><gpos v="-1733.33325 -528 0"/></GUILayout><compOutputs><compOutput><uid v="1406275336"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="directionalwarp"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="100"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1406279782"/><connections><connection><identifier v="destination"/><connRef v="1406279849"/><connRefOutput v="1406279850"/></connection><connection><identifier v="source"/><connRef v="1406282851"/><connRefOutput v="1406282852"/></connection></connections><GUILayout><gpos v="-4080 208 0"/></GUILayout><compOutputs><compOutput><uid v="1406279783"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1406279849"/><connections><connection><identifier v="Input_1"/><connRef v="1406264574"/><connRefOutput v="1406264575"/></connection></connections><GUILayout><gpos v="-4240 208 0"/></GUILayout><compOutputs><compOutput><uid v="1406279850"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1449404462"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.519999981"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1406279850"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406282851"/><GUILayout><gpos v="-4400 208 0"/></GUILayout><compOutputs><compOutput><uid v="1406282852"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///gradient_linear_2?dependency=1406282768"/><parameters/><outputBridgings><outputBridging><uid v="1406282852"/><identifier v="Simple_Gradient_2"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406288295"/><connections><connection><identifier v="mask_map_input"/><connRef v="1406279782"/><connRefOutput v="1406279783"/></connection><connection><identifier v="pattern_input"/><connRef v="1406350750"/><connRefOutput v="1406350751"/></connection></connections><GUILayout><gpos v="-3632 -144 0"/></GUILayout><compOutputs><compOutput><uid v="1406268488"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1449399972"/><parameters><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="5.44999981"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.589999974"/></paramValue></parameter><parameter><name v="color_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.839999974"/></paramValue></parameter><parameter><name v="rotation_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.00999999978"/></paramValue></parameter><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="41"/></paramValue></parameter><parameter><name v="rotation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.182722226"/></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.459999979"/></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.349999994"/></paramValue></parameter><parameter><name v="size"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="1 0.439999998"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="64"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1406268488"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406288318"/><connections><connection><identifier v="destination"/><connRef v="1406268838"/><connRefOutput v="1406268839"/></connection><connection><identifier v="source"/><connRef v="1406288295"/><connRefOutput v="1406268488"/></connection></connections><GUILayout><gpos v="-2864 -144 0"/></GUILayout><compOutputs><compOutput><uid v="1406268839"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0399999991"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1406311250"/><connections><connection><identifier v="input1"/><connRef v="1406268680"/><connRefOutput v="1406268488"/></connection><connection><identifier v="inputintensity"/><connRef v="1406311261"/><connRefOutput v="1406271691"/></connection></connections><GUILayout><gpos v="-3312 158.506363 0"/></GUILayout><compOutputs><compOutput><uid v="1406275336"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="directionalwarp"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="9.42999935"/></paramValue></parameter><parameter><name v="warpangle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0944444463"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1406311261"/><GUILayout><gpos v="-3856 671.650574 0"/></GUILayout><compOutputs><compOutput><uid v="1406271691"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///perlin_noise?dependency=1407332030"/><parameters><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueInt32 v="16"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1406271691"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406328013"/><GUILayout><gpos v="-2229.70117 -766.302185 0"/></GUILayout><compOutputs><compOutput><uid v="1406328014"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///crystal_1?dependency=1407271237"/><parameters><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueInt32 v="7"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1406328014"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406328214"/><connections><connection><identifier v="Source"/><connRef v="1406328013"/><connRefOutput v="1406328014"/></connection></connections><GUILayout><gpos v="-2069.70117 -769.946716 0"/></GUILayout><compOutputs><compOutput><uid v="1406328215"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1299236171"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="3.21000004"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1406328215"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406343881"/><connections><connection><identifier v="inputintensity"/><connRef v="1406271812"/><connRefOutput v="1406271813"/></connection><connection><identifier v="input1"/><connRef v="1406410100"/><connRefOutput v="1406410101"/></connection></connections><GUILayout><gpos v="-1616 -1697.07983 0"/></GUILayout><compOutputs><compOutput><uid v="1406275336"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="directionalwarp"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="100"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1406343892"/><connections><connection><identifier v="inputgradient"/><connRef v="1406357153"/><connRefOutput v="1406328215"/></connection><connection><identifier v="input1"/><connRef v="1406427325"/><connRefOutput v="1406275336"/></connection></connections><GUILayout><gpos v="-1296 -1696.07935 0"/></GUILayout><compOutputs><compOutput><uid v="1406328199"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="warp"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.729999959"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1406343995"/><connections><connection><identifier v="Effect"/><connRef v="1406344343"/><connRefOutput v="1406344344"/></connection><connection><identifier v="Source"/><connRef v="1406353390"/><connRefOutput v="1406353391"/></connection></connections><GUILayout><gpos v="-336 -912 0"/></GUILayout><compOutputs><compOutput><uid v="1406343996"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///non_uniform_blur_grayscale?dependency=1290776959"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="16"/></paramValue></parameter><parameter><name v="Blades"/><relativeTo v="0"/><paramValue><constantValueInt32 v="9"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.42999995"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1406343996"/><identifier v="Non_Uniform_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406344337"/><connections><connection><identifier v="destination"/><connRef v="1406343995"/><connRefOutput v="1406343996"/></connection><connection><identifier v="source"/><connRef v="1406592243"/><connRefOutput v="1406592244"/></connection></connections><GUILayout><gpos v="-162.151489 -912 0"/></GUILayout><compOutputs><compOutput><uid v="1406344338"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.170000002"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1406344343"/><connections><connection><identifier v="Input_1"/><connRef v="1406592243"/><connRefOutput v="1406592244"/></connection></connections><GUILayout><gpos v="-1040 -1680 0"/></GUILayout><compOutputs><compOutput><uid v="1406344344"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1449404462"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.995000005"/></paramValue></parameter><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1406344344"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406353390"/><connections><connection><identifier v="input1"/><connRef v="1406283432"/><connRefOutput v="1406275336"/></connection></connections><GUILayout><gpos v="-1669.33325 -752 0"/></GUILayout><compOutputs><compOutput><uid v="1406353391"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelouthigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.876146793 0.876146793 0.876146793 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1406357153"/><connections><connection><identifier v="Source"/><connRef v="1406328013"/><connRefOutput v="1406328014"/></connection></connections><GUILayout><gpos v="-1392 -1473.07983 0"/></GUILayout><compOutputs><compOutput><uid v="1406328215"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1299236171"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="9.63000011"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1406328215"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406383446"/><connections><connection><identifier v="destination"/><connRef v="1406383447"/><connRefOutput v="1406317367"/></connection></connections><GUILayout><gpos v="-2320 -1781.98169 0"/></GUILayout><compOutputs><compOutput><uid v="1406317367"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.319999993"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1406383447"/><connections><connection><identifier v="destination"/><connRef v="1406383448"/><connRefOutput v="1406268839"/></connection></connections><GUILayout><gpos v="-2480 -1779.83179 0"/></GUILayout><compOutputs><compOutput><uid v="1406317367"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.189999998"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1406383448"/><connections><connection><identifier v="destination"/><connRef v="1406383449"/><connRefOutput v="1406268839"/></connection><connection><identifier v="source"/><connRef v="1406288295"/><connRefOutput v="1406268488"/></connection></connections><GUILayout><gpos v="-2653.12012 -1779.83179 0"/></GUILayout><compOutputs><compOutput><uid v="1406268839"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.409999996"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1406383449"/><connections><connection><identifier v="source"/><connRef v="1406311250"/><connRefOutput v="1406275336"/></connection><connection><identifier v="destination"/><connRef v="1406264574"/><connRefOutput v="1406264575"/></connection></connections><GUILayout><gpos v="-2832 -1779.83179 0"/></GUILayout><compOutputs><compOutput><uid v="1406268839"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.429999977"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1406389970"/><connections><connection><identifier v="pattern_input"/><connRef v="1406288318"/><connRefOutput v="1406268839"/></connection></connections><GUILayout><gpos v="-2277.33325 -16 0"/></GUILayout><compOutputs><compOutput><uid v="1406268488"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1449399972"/><parameters><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="25"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="7.78999996"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.659999967"/></paramValue></parameter><parameter><name v="color_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.870000005"/></paramValue></parameter><parameter><name v="rotation_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0299999993"/></paramValue></parameter><parameter><name v="rotation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0833333358"/></paramValue></parameter><parameter><name v="size_random"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.479999989 0.119999997"/></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.439999998"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1406268488"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406390000"/><connections><connection><identifier v="destination"/><connRef v="1406271181"/><connRefOutput v="1406271182"/></connection><connection><identifier v="source"/><connRef v="1406390024"/><connRefOutput v="1406390025"/></connection></connections><GUILayout><gpos v="-1861.33325 -272 0"/></GUILayout><compOutputs><compOutput><uid v="1406390001"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1406390024"/><connections><connection><identifier v="input1"/><connRef v="1406389970"/><connRefOutput v="1406268488"/></connection></connections><GUILayout><gpos v="-2053.33325 -16 0"/></GUILayout><compOutputs><compOutput><uid v="1406390025"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelouthigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.769113123 0.769113123 0.769113123 1"/></paramValue></parameter><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0 0 0 0.5"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1406404627"/><connections><connection><identifier v="Input"/><connRef v="1406344337"/><connRefOutput v="1406344338"/></connection></connections><GUILayout><gpos v="-2.15148926 -912 0"/></GUILayout><compOutputs><compOutput><uid v="1406404628"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1406270631"/><parameters/><outputBridgings><outputBridging><uid v="1406404628"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406410032"/><connections><connection><identifier v="mask_map_input"/><connRef v="1406279782"/><connRefOutput v="1406279783"/></connection><connection><identifier v="pattern_input"/><connRef v="1406383446"/><connRefOutput v="1406317367"/></connection></connections><GUILayout><gpos v="-2000 -1694.36975 0"/></GUILayout><compOutputs><compOutput><uid v="1406268488"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1449399972"/><parameters><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="20"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="3.98000002"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.319999993"/></paramValue></parameter><parameter><name v="color_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.119999997"/></paramValue></parameter><parameter><name v="rotation_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0599999987"/></paramValue></parameter><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="152"/></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.479999989"/></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.569999993"/></paramValue></parameter><parameter><name v="mask_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.959999979"/></paramValue></parameter><parameter><name v="rotation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0583333336"/></paramValue></parameter><parameter><name v="color_parametrization_mode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="color_parametrization_multiplier"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.75"/></paramValue></parameter><parameter><name v="size"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.839999974 2.1099999"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1406268488"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406410100"/><connections><connection><identifier v="source"/><connRef v="1406410118"/><connRefOutput v="1406410119"/></connection></connections><GUILayout><gpos v="-1744 -1889.07983 0"/></GUILayout><compOutputs><compOutput><uid v="1406410101"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="5"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1406410118"/><connections><connection><identifier v="input1"/><connRef v="1406410032"/><connRefOutput v="1406268488"/></connection></connections><GUILayout><gpos v="-1822 -1910.41321 -1"/><docked v="1"/><dockDistance v="96 192"/></GUILayout><compOutputs><compOutput><uid v="1406410119"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelouthigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.399082571 0.399082571 0.399082571 1"/></paramValue></parameter><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.270428002 0.270428002 0.270428002 0.5"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1406427325"/><connections><connection><identifier v="input1"/><connRef v="1406343881"/><connRefOutput v="1406275336"/></connection><connection><identifier v="inputintensity"/><connRef v="1406427338"/><connRefOutput v="1406271691"/></connection></connections><GUILayout><gpos v="-1456 -1697.07983 0"/></GUILayout><compOutputs><compOutput><uid v="1406275336"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="directionalwarp"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="3.57999992"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1406427338"/><GUILayout><gpos v="-1616 -1441.07983 0"/></GUILayout><compOutputs><compOutput><uid v="1406271691"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///perlin_noise?dependency=1407332030"/><parameters><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueInt32 v="13"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1406271691"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406429150"/><connections><connection><identifier v="input"/><connRef v="1406245505"/><connRefOutput v="1406245506"/></connection></connections><GUILayout><gpos v="592 -3472 0"/></GUILayout><compOutputs><compOutput><uid v="1355424961"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///hbao?dependency=1297162667"/><parameters><parameter><name v="height_depth"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.219999999"/></paramValue></parameter><parameter><name v="samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="16"/></paramValue></parameter><parameter><name v="surface_size"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="158.369995"/></paramValue></parameter><parameter><name v="height_depth_cm"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="72.4000015"/></paramValue></parameter><parameter><name v="radius"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.189999998"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1355424961"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406433650"/><connections><connection><identifier v="Input_1"/><connRef v="1406429150"/><connRefOutput v="1355424961"/></connection></connections><GUILayout><gpos v="752 -3472 0"/></GUILayout><compOutputs><compOutput><uid v="1406433651"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1449404462"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.25"/></paramValue></parameter><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.639999986"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1406433651"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406433829"/><connections><connection><identifier v="input1"/><connRef v="1406323687"/><connRefOutput v="1406323024"/></connection></connections><GUILayout><gpos v="976 -3888 0"/></GUILayout><compOutputs><compOutput><uid v="1406433830"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1406433838"/><paramsArrayCells><paramsArrayCell><uid v="1406433961"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.443137288 0.426245213 0.409644395 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1406433965"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.318492651"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.309803933 0.296915233 0.285505593 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1406434070"/><connections><connection><identifier v="input"/><connRef v="1406353390"/><connRefOutput v="1406353391"/></connection></connections><GUILayout><gpos v="432 -3728 0"/></GUILayout><compOutputs><compOutput><uid v="1355424961"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///hbao?dependency=1297162667"/><parameters><parameter><name v="height_depth"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.439999998"/></paramValue></parameter><parameter><name v="samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="16"/></paramValue></parameter><parameter><name v="surface_size"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="158.369995"/></paramValue></parameter><parameter><name v="height_depth_cm"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="72.4000015"/></paramValue></parameter><parameter><name v="radius"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.189999998"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1355424961"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406435085"/><connections><connection><identifier v="input1"/><connRef v="1406434070"/><connRefOutput v="1355424961"/></connection></connections><GUILayout><gpos v="1264 -3888 0"/></GUILayout><compOutputs><compOutput><uid v="1406433830"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1406433838"/><paramsArrayCells><paramsArrayCell><uid v="1406435161"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.739669442 0.477665991 0.124763526 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1406435165"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.00421940908"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.36470589 0.318391651 0.256009251 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1494634135"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.692355394"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.466942161 0.296749175 0.0675097182 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1406243840"/><connections><connection><identifier v="input1"/><connRef v="1406323023"/><connRefOutput v="1406323024"/></connection></connections><GUILayout><gpos v="976 -3728 0"/></GUILayout><compOutputs><compOutput><uid v="1406433830"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1406433838"/><paramsArrayCells><paramsArrayCell><uid v="1406273301"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.00843881816"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.21960786 0.191858187 0.154481128 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1406273302"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="1 0.572222114 0 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1406243915"/><connections><connection><identifier v="Input_1"/><connRef v="1406249893"/><connRefOutput v="1406249894"/></connection></connections><GUILayout><gpos v="752 -3600 0"/></GUILayout><compOutputs><compOutput><uid v="1406243916"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1449404462"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.310000002"/></paramValue></parameter><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.75"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1406243916"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406244021"/><connections><connection><identifier v="source"/><connRef v="1406433829"/><connRefOutput v="1406433830"/></connection><connection><identifier v="destination"/><connRef v="1406243840"/><connRefOutput v="1406433830"/></connection><connection><identifier v="opacity"/><connRef v="1406249893"/><connRefOutput v="1406249894"/></connection></connections><GUILayout><gpos v="1200 -3728 0"/></GUILayout><compOutputs><compOutput><uid v="1406244022"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1406245505"/><connections><connection><identifier v="Source"/><connRef v="1406353390"/><connRefOutput v="1406353391"/></connection></connections><GUILayout><gpos v="432 -3472 0"/></GUILayout><compOutputs><compOutput><uid v="1406245506"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///highpass_grayscale?dependency=1406245499"/><parameters><parameter><name v="Radius"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="47.8699989"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1406245506"/><identifier v="Highpass"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406249893"/><connections><connection><identifier v="Source"/><connRef v="1406434070"/><connRefOutput v="1355424961"/></connection></connections><GUILayout><gpos v="592 -3600 0"/></GUILayout><compOutputs><compOutput><uid v="1406249894"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1299236171"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1406249894"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406251928"/><connections><connection><identifier v="destination"/><connRef v="1406251967"/><connRefOutput v="1406433651"/></connection><connection><identifier v="source"/><connRef v="1406276936"/><connRefOutput v="1406243916"/></connection></connections><GUILayout><gpos v="1200 -3568 0"/></GUILayout><compOutputs><compOutput><uid v="1406251929"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1406251965"/><connections><connection><identifier v="Source"/><connRef v="1406353390"/><connRefOutput v="1406353391"/></connection></connections><GUILayout><gpos v="528 -3280 0"/></GUILayout><compOutputs><compOutput><uid v="1406245506"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///highpass_grayscale?dependency=1406245499"/><parameters/><outputBridgings><outputBridging><uid v="1406245506"/><identifier v="Highpass"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406251966"/><connections><connection><identifier v="input"/><connRef v="1406251965"/><connRefOutput v="1406245506"/></connection></connections><GUILayout><gpos v="688 -3280 0"/></GUILayout><compOutputs><compOutput><uid v="1355424961"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///hbao?dependency=1297162667"/><parameters><parameter><name v="height_depth"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="16"/></paramValue></parameter><parameter><name v="surface_size"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="158.369995"/></paramValue></parameter><parameter><name v="height_depth_cm"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="72.4000015"/></paramValue></parameter><parameter><name v="radius"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0299999993"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1355424961"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406251967"/><connections><connection><identifier v="Input_1"/><connRef v="1406251966"/><connRefOutput v="1355424961"/></connection></connections><GUILayout><gpos v="848 -3280 0"/></GUILayout><compOutputs><compOutput><uid v="1406433651"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1449404462"/><parameters><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.639999986"/></paramValue></parameter><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.00999999978"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1406433651"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406270846"/><connections><connection><identifier v="destination"/><connRef v="1406244021"/><connRefOutput v="1406244022"/></connection><connection><identifier v="opacity"/><connRef v="1406243915"/><connRefOutput v="1406243916"/></connection><connection><identifier v="source"/><connRef v="1406435085"/><connRefOutput v="1406433830"/></connection></connections><GUILayout><gpos v="1360 -3728 0"/></GUILayout><compOutputs><compOutput><uid v="1406270847"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.550000012"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1406273222"/><connections><connection><identifier v="destination"/><connRef v="1406270846"/><connRefOutput v="1406270847"/></connection><connection><identifier v="source"/><connRef v="1406273236"/><connRefOutput v="1406273237"/></connection></connections><GUILayout><gpos v="1520 -3600 0"/></GUILayout><compOutputs><compOutput><uid v="1406273223"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.119999997"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1406273236"/><connections><connection><identifier v="input1"/><connRef v="1406316900"/><connRefOutput v="1406316901"/></connection></connections><GUILayout><gpos v="1360 -3585.15771 0"/></GUILayout><compOutputs><compOutput><uid v="1406273237"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1406273334"/><connections><connection><identifier v="Input_1"/><connRef v="1406273452"/><connRefOutput v="1406273453"/></connection></connections><GUILayout><gpos v="976 -3152 0"/></GUILayout><compOutputs><compOutput><uid v="1406433651"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1449404462"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0399999991"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1406433651"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406273452"/><connections><connection><identifier v="Source"/><connRef v="1406251966"/><connRefOutput v="1355424961"/></connection></connections><GUILayout><gpos v="848 -3152 0"/></GUILayout><compOutputs><compOutput><uid v="1406273453"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1299236171"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.25"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1406273453"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406273483"/><connections><connection><identifier v="destination"/><connRef v="1406273222"/><connRefOutput v="1406273223"/></connection><connection><identifier v="source"/><connRef v="1406276749"/><connRefOutput v="1406433830"/></connection><connection><identifier v="opacity"/><connRef v="1406273334"/><connRefOutput v="1406433651"/></connection></connections><GUILayout><gpos v="1712 -3600 0"/></GUILayout><compOutputs><compOutput><uid v="1406273484"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.280000001"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1406276749"/><connections><connection><identifier v="input1"/><connRef v="1406273334"/><connRefOutput v="1406433651"/></connection></connections><GUILayout><gpos v="1232 -3184 0"/></GUILayout><compOutputs><compOutput><uid v="1406433830"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1406433838"/><paramsArrayCells><paramsArrayCell><uid v="1406273301"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.00843881816"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.21960786 0.191858187 0.154481128 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1406273302"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.298039228 0.278119087 0.251287967 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1406276936"/><connections><connection><identifier v="Input_1"/><connRef v="1406249893"/><connRefOutput v="1406249894"/></connection></connections><GUILayout><gpos v="976 -3568 0"/></GUILayout><compOutputs><compOutput><uid v="1406243916"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1449404462"/><parameters><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.75"/></paramValue></parameter><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0500000007"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1406243916"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406286998"/><connections><connection><identifier v="opacity"/><connRef v="1406344343"/><connRefOutput v="1406344344"/></connection><connection><identifier v="source"/><connRef v="1406287046"/><connRefOutput v="1406433830"/></connection><connection><identifier v="destination"/><connRef v="1406273483"/><connRefOutput v="1406273484"/></connection></connections><GUILayout><gpos v="1936 -3600 0"/></GUILayout><compOutputs><compOutput><uid v="1406286999"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1406287027"/><connections><connection><identifier v="input"/><connRef v="1406404627"/><connRefOutput v="1406404628"/></connection></connections><GUILayout><gpos v="1456 -3216 0"/></GUILayout><compOutputs><compOutput><uid v="1355424961"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///hbao?dependency=1297162667"/><parameters><parameter><name v="height_depth"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.219999999"/></paramValue></parameter><parameter><name v="samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="16"/></paramValue></parameter><parameter><name v="surface_size"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="158.369995"/></paramValue></parameter><parameter><name v="height_depth_cm"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="72.4000015"/></paramValue></parameter><parameter><name v="radius"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.189999998"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1355424961"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406287046"/><connections><connection><identifier v="input1"/><connRef v="1406323638"/><connRefOutput v="1406323024"/></connection></connections><GUILayout><gpos v="1712 -3216 0"/></GUILayout><compOutputs><compOutput><uid v="1406433830"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1406433838"/><paramsArrayCells><paramsArrayCell><uid v="1406273301"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.00632911362"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.762620687 1 0.102409661 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1406273302"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.0258845631 0.0413223132 0.00796574727 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1406348348"/><connections><connection><identifier v="Input"/><connRef v="1406481452"/><connRefOutput v="1406481453"/></connection></connections><GUILayout><gpos v="464 -912 0"/></GUILayout><compOutputs><compOutput><uid v="1406348349"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1406270631"/><parameters/><outputBridgings><outputBridging><uid v="1406348349"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406393437"/><connections><connection><identifier v="input1"/><connRef v="1406404627"/><connRefOutput v="1406404628"/></connection></connections><GUILayout><gpos v="496 -2704 0"/></GUILayout><compOutputs><compOutput><uid v="1406393438"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="normal"/><parameters><parameter><name v="input2alpha"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="inversedy"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="20"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1406393470"/><connections><connection><identifier v="RGBA"/><connRef v="1406393437"/><connRefOutput v="1406393438"/></connection></connections><GUILayout><gpos v="656 -2693.33325 0"/></GUILayout><compOutputs><compOutput><uid v="1406393471"/><comptype v="2"/></compOutput><compOutput><uid v="1406393472"/><comptype v="2"/></compOutput><compOutput><uid v="1406393473"/><comptype v="2"/></compOutput><compOutput><uid v="1406393474"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///rgba_split?dependency=1406393461"/><parameters/><outputBridgings><outputBridging><uid v="1406393471"/><identifier v="R"/></outputBridging><outputBridging><uid v="1406393472"/><identifier v="G"/></outputBridging><outputBridging><uid v="1406393473"/><identifier v="B"/></outputBridging><outputBridging><uid v="1406393474"/><identifier v="A"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406394262"/><connections><connection><identifier v="input"/><connRef v="1406393437"/><connRefOutput v="1406393438"/></connection></connections><GUILayout><gpos v="656 -2896 0"/></GUILayout><compOutputs><compOutput><uid v="1406394263"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///curvature_smooth?dependency=1449590280"/><parameters><parameter><name v="normal_format"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1406394263"/><identifier v="height"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406397046"/><connections><connection><identifier v="Input_1"/><connRef v="1406394262"/><connRefOutput v="1406394263"/></connection></connections><GUILayout><gpos v="816 -2896 0"/></GUILayout><compOutputs><compOutput><uid v="1406397047"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1449404462"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.370000005"/></paramValue></parameter><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.719999969"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1406397047"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406397262"/><connections><connection><identifier v="source"/><connRef v="1406397280"/><connRefOutput v="1406397281"/></connection><connection><identifier v="destination"/><connRef v="1406323412"/><connRefOutput v="1406286999"/></connection></connections><GUILayout><gpos v="2576 -3600 0"/></GUILayout><compOutputs><compOutput><uid v="1406397263"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0399999991"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1406397280"/><connections><connection><identifier v="input1"/><connRef v="1406397046"/><connRefOutput v="1406397047"/></connection></connections><GUILayout><gpos v="2498 -3621.33325 -1"/><docked v="1"/><dockDistance v="1152 -480"/></GUILayout><compOutputs><compOutput><uid v="1406397281"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1406399345"/><connections><connection><identifier v="destination"/><connRef v="1406448999"/><connRefOutput v="1406449000"/></connection></connections><GUILayout><gpos v="3696 -3600 0"/></GUILayout><compOutputs><compOutput><uid v="1406399346"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1406448999"/><connections><connection><identifier v="input1"/><connRef v="1406342981"/><connRefOutput v="1406342982"/></connection></connections><GUILayout><gpos v="3536 -3600 0"/></GUILayout><compOutputs><compOutput><uid v="1406449000"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.487767577 0.487767577 0.487767577 0.5"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1406481452"/><connections><connection><identifier v="input1"/><connRef v="1406404627"/><connRefOutput v="1406404628"/></connection></connections><GUILayout><gpos v="272 -912 0"/></GUILayout><compOutputs><compOutput><uid v="1406481453"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.503058076 0.503058076 0.503058076 0.5"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1406283432"/><connections><connection><identifier v="input1"/><connRef v="1406275335"/><connRefOutput v="1406275336"/></connection><connection><identifier v="inputintensity"/><connRef v="1406328214"/><connRefOutput v="1406328215"/></connection></connections><GUILayout><gpos v="-1861.33325 -752 0"/></GUILayout><compOutputs><compOutput><uid v="1406275336"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="directionalwarp"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="6.73999977"/></paramValue></parameter><parameter><name v="warpangle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.108333334"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1406316900"/><connections><connection><identifier v="Source"/><connRef v="1406251928"/><connRefOutput v="1406251929"/></connection></connections><GUILayout><gpos v="1360 -3472 0"/></GUILayout><compOutputs><compOutput><uid v="1406316901"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///invert_grayscale?dependency=1368115694"/><parameters/><outputBridgings><outputBridging><uid v="1406316901"/><identifier v="Invert_Grayscale"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406323023"/><connections><connection><identifier v="Input"/><connRef v="1406434070"/><connRefOutput v="1355424961"/></connection></connections><GUILayout><gpos v="720 -3743.97803 0"/></GUILayout><compOutputs><compOutput><uid v="1406323024"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///quantize_grayscale?dependency=1406322980"/><parameters><parameter><name v="Quantize"/><relativeTo v="0"/><paramValue><constantValueInt32 v="5"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1406323024"/><identifier v="Quantize"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406323165"/><connections><connection><identifier v="Input_1"/><connRef v="1406393470"/><connRefOutput v="1406393473"/></connection></connections><GUILayout><gpos v="1936 -3728 0"/></GUILayout><compOutputs><compOutput><uid v="1406323166"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1449404462"/><parameters><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.680000007"/></paramValue></parameter><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0399999991"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1406323166"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406323392"/><connections><connection><identifier v="Source"/><connRef v="1406323165"/><connRefOutput v="1406323166"/></connection></connections><GUILayout><gpos v="2096 -3728 0"/></GUILayout><compOutputs><compOutput><uid v="1406323393"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///invert_grayscale?dependency=1368115694"/><parameters/><outputBridgings><outputBridging><uid v="1406323393"/><identifier v="Invert_Grayscale"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406323404"/><connections><connection><identifier v="input1"/><connRef v="1406323165"/><connRefOutput v="1406323166"/></connection></connections><GUILayout><gpos v="2288 -3728 0"/></GUILayout><compOutputs><compOutput><uid v="1406323405"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1406323412"/><connections><connection><identifier v="destination"/><connRef v="1406286998"/><connRefOutput v="1406286999"/></connection><connection><identifier v="opacity"/><connRef v="1406323392"/><connRefOutput v="1406323393"/></connection><connection><identifier v="source"/><connRef v="1406323404"/><connRefOutput v="1406323405"/></connection></connections><GUILayout><gpos v="2320 -3600 0"/></GUILayout><compOutputs><compOutput><uid v="1406286999"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.179999992"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1406323638"/><connections><connection><identifier v="Input"/><connRef v="1406287027"/><connRefOutput v="1355424961"/></connection></connections><GUILayout><gpos v="1584 -3216 0"/></GUILayout><compOutputs><compOutput><uid v="1406323024"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///quantize_grayscale?dependency=1406322980"/><parameters><parameter><name v="Quantize"/><relativeTo v="0"/><paramValue><constantValueInt32 v="5"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1406323024"/><identifier v="Quantize"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406323687"/><connections><connection><identifier v="Input"/><connRef v="1406433650"/><connRefOutput v="1406433651"/></connection></connections><GUILayout><gpos v="784 -3920 0"/></GUILayout><compOutputs><compOutput><uid v="1406323024"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///quantize_grayscale?dependency=1406322980"/><parameters/><outputBridgings><outputBridging><uid v="1406323024"/><identifier v="Quantize"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406350122"/><connections><connection><identifier v="inputintensity"/><connRef v="1406350383"/><connRefOutput v="1406350384"/></connection><connection><identifier v="input1"/><connRef v="1406350411"/><connRefOutput v="1406350412"/></connection></connections><GUILayout><gpos v="-4112 -208 0"/></GUILayout><compOutputs><compOutput><uid v="1406350123"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="directionalwarp"/><parameters><parameter><name v="warpangle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.249972224"/></paramValue></parameter><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="20"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1406350356"/><GUILayout><gpos v="-4464 -112 0"/></GUILayout><compOutputs><compOutput><uid v="1406350357"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///anisotropic_noise?dependency=1406350129"/><parameters><parameter><name v="rotate"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="Y_Amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="23"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1406350357"/><identifier v="Anisotropic_Noise"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406350383"/><connections><connection><identifier v="Source"/><connRef v="1406350356"/><connRefOutput v="1406350357"/></connection></connections><GUILayout><gpos v="-4304 -112 0"/></GUILayout><compOutputs><compOutput><uid v="1406350384"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1299236171"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="2.11999989"/></paramValue></parameter><parameter><name v="Quality"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1406350384"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406350411"/><GUILayout><gpos v="-4304 -240 0"/></GUILayout><compOutputs><compOutput><uid v="1406350412"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///shape?dependency=1311189026"/><parameters><parameter><name v="Pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="Size_xy"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="1 0.0899999961"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1406350412"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406350750"/><connections><connection><identifier v="destination"/><connRef v="1406279782"/><connRefOutput v="1406279783"/></connection><connection><identifier v="source"/><connRef v="1406350122"/><connRefOutput v="1406350123"/></connection></connections><GUILayout><gpos v="-3920 -208 0"/></GUILayout><compOutputs><compOutput><uid v="1406350751"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.219999999"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1406412284"/><connections><connection><identifier v="input"/><connRef v="1406348348"/><connRefOutput v="1406348349"/></connection></connections><GUILayout><gpos v="1552 -1088.24829 0"/></GUILayout><compOutputs><compOutput><uid v="1355424961"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///hbao?dependency=1297162667"/><parameters><parameter><name v="height_depth"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.129999995"/></paramValue></parameter><parameter><name v="samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="16"/></paramValue></parameter><parameter><name v="surface_size"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="158.369995"/></paramValue></parameter><parameter><name v="height_depth_cm"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="72.4000015"/></paramValue></parameter><parameter><name v="radius"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0199999996"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1355424961"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406413192"/><connections><connection><identifier v="input1"/><connRef v="1406413204"/><connRefOutput v="1406413205"/></connection></connections><GUILayout><gpos v="1776 -1104 0"/></GUILayout><compOutputs><compOutput><uid v="1406413193"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.166666672 0.166666672 0.166666672 0.5"/></paramValue></parameter><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.466360867 0.466360867 0.466360867 0"/></paramValue></parameter><parameter><name v="levelouthigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.888379216 0.888379216 0.888379216 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1406413204"/><connections><connection><identifier v="Source"/><connRef v="1406412284"/><connRefOutput v="1355424961"/></connection></connections><GUILayout><gpos v="1698 -1104 -1"/><docked v="1"/><dockDistance v="96 -79.751709"/></GUILayout><compOutputs><compOutput><uid v="1406413205"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///invert_grayscale?dependency=1368115694"/><parameters/><outputBridgings><outputBridging><uid v="1406413205"/><identifier v="Invert_Grayscale"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406413542"/><connections><connection><identifier v="destination"/><connRef v="1406413192"/><connRefOutput v="1406413193"/></connection><connection><identifier v="source"/><connRef v="1406323392"/><connRefOutput v="1406323393"/></connection></connections><GUILayout><gpos v="1951.62708 -1104 0"/></GUILayout><compOutputs><compOutput><uid v="1406413543"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0700000003"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1406415779"/><connections><connection><identifier v="source"/><connRef v="1406397046"/><connRefOutput v="1406397047"/></connection><connection><identifier v="destination"/><connRef v="1406413542"/><connRefOutput v="1406413543"/></connection></connections><GUILayout><gpos v="2145.50854 -1104 501"/></GUILayout><compOutputs><compOutput><uid v="1406397263"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.170000002"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1406324282"/><connections><connection><identifier v="destination"/><connRef v="1406397262"/><connRefOutput v="1406397263"/></connection><connection><identifier v="opacity"/><connRef v="1406339916"/><connRefOutput v="1406339917"/></connection><connection><identifier v="source"/><connRef v="1406339948"/><connRefOutput v="1406339949"/></connection></connections><GUILayout><gpos v="2864 -3600 0"/></GUILayout><compOutputs><compOutput><uid v="1406324283"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1406324593"/><connections><connection><identifier v="input"/><connRef v="1406348348"/><connRefOutput v="1406348349"/></connection></connections><GUILayout><gpos v="2320 -3408 501"/></GUILayout><compOutputs><compOutput><uid v="1355424961"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///hbao?dependency=1297162667"/><parameters><parameter><name v="height_depth"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="16"/></paramValue></parameter><parameter><name v="surface_size"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="158.369995"/></paramValue></parameter><parameter><name v="height_depth_cm"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="72.4000015"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1355424961"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406339854"/><connections><connection><identifier v="Input"/><connRef v="1406324593"/><connRefOutput v="1355424961"/></connection></connections><GUILayout><gpos v="2480 -3408 0"/></GUILayout><compOutputs><compOutput><uid v="1406339855"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///quantize_grayscale?dependency=1406322980"/><parameters><parameter><name v="Quantize"/><relativeTo v="0"/><paramValue><constantValueInt32 v="12"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1406339855"/><identifier v="Quantize"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406339916"/><connections><connection><identifier v="Input_1"/><connRef v="1406339854"/><connRefOutput v="1406339855"/></connection></connections><GUILayout><gpos v="2640 -3408 0"/></GUILayout><compOutputs><compOutput><uid v="1406339917"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1449404462"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.159999996"/></paramValue></parameter><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.389999986"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1406339917"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406339948"/><connections><connection><identifier v="input1"/><connRef v="1406339916"/><connRefOutput v="1406339917"/></connection></connections><GUILayout><gpos v="2786 -3621.33325 -1"/><docked v="1"/><dockDistance v="112 -96"/></GUILayout><compOutputs><compOutput><uid v="1406339949"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1406339956"/><paramsArrayCells><paramsArrayCell><uid v="1406339957"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0 0 0 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1406339958"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.376470625 0.330305636 0.205213383 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1406340178"/><connections><connection><identifier v="Input_1"/><connRef v="1406340200"/><connRefOutput v="1406339855"/></connection></connections><GUILayout><gpos v="2640 -3280 501"/></GUILayout><compOutputs><compOutput><uid v="1406339917"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1449404462"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.979999959"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1406339917"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406340200"/><connections><connection><identifier v="Input"/><connRef v="1406324593"/><connRefOutput v="1355424961"/></connection></connections><GUILayout><gpos v="2480 -3280 0"/></GUILayout><compOutputs><compOutput><uid v="1406339855"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///quantize_grayscale?dependency=1406322980"/><parameters><parameter><name v="Quantize"/><relativeTo v="0"/><paramValue><constantValueInt32 v="42"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1406339855"/><identifier v="Quantize"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406340222"/><connections><connection><identifier v="destination"/><connRef v="1406340178"/><connRefOutput v="1406339917"/></connection><connection><identifier v="source"/><connRef v="1406323392"/><connRefOutput v="1406323393"/></connection></connections><GUILayout><gpos v="2832 -3280 0"/></GUILayout><compOutputs><compOutput><uid v="1406340223"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1406340251"/><connections><connection><identifier v="source"/><connRef v="1406340252"/><connRefOutput v="1406339949"/></connection><connection><identifier v="destination"/><connRef v="1406324282"/><connRefOutput v="1406324283"/></connection><connection><identifier v="opacity"/><connRef v="1406340369"/><connRefOutput v="1406397047"/></connection></connections><GUILayout><gpos v="3120 -3600 0"/></GUILayout><compOutputs><compOutput><uid v="1406324283"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.629999995"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1406340252"/><connections><connection><identifier v="input1"/><connRef v="1406340369"/><connRefOutput v="1406397047"/></connection></connections><GUILayout><gpos v="3042 -3621.33325 -1"/><docked v="1"/><dockDistance v="112 -96"/></GUILayout><compOutputs><compOutput><uid v="1406339949"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1406339956"/><paramsArrayCells><paramsArrayCell><uid v="1406339957"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0 0 0 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1406339958"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.165167257 0.235294133 0.218829557 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1406340369"/><connections><connection><identifier v="Input_1"/><connRef v="1406394262"/><connRefOutput v="1406394263"/></connection></connections><GUILayout><gpos v="2992 -3408 501"/></GUILayout><compOutputs><compOutput><uid v="1406397047"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1449404462"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.299999982"/></paramValue></parameter><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.969999969"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1406397047"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406342817"/><connections><connection><identifier v="Source"/><connRef v="1406394262"/><connRefOutput v="1406394263"/></connection></connections><GUILayout><gpos v="2941.31348 -3773.2002 0"/></GUILayout><compOutputs><compOutput><uid v="1406342818"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///highpass_grayscale?dependency=1406245499"/><parameters/><outputBridgings><outputBridging><uid v="1406342818"/><identifier v="Highpass"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406342832"/><connections><connection><identifier v="Input_1"/><connRef v="1406342817"/><connRefOutput v="1406342818"/></connection></connections><GUILayout><gpos v="3101.31348 -3773.2002 0"/></GUILayout><compOutputs><compOutput><uid v="1406342833"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1449404462"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.370000005"/></paramValue></parameter><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.819999993"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1406342833"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1406342981"/><connections><connection><identifier v="destination"/><connRef v="1406340251"/><connRefOutput v="1406324283"/></connection><connection><identifier v="source"/><connRef v="1406342997"/><connRefOutput v="1406342998"/></connection><connection><identifier v="opacity"/><connRef v="1406342832"/><connRefOutput v="1406342833"/></connection></connections><GUILayout><gpos v="3312 -3600 0"/></GUILayout><compOutputs><compOutput><uid v="1406342982"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1406342997"/><connections><connection><identifier v="input1"/><connRef v="1406342832"/><connRefOutput v="1406342833"/></connection></connections><GUILayout><gpos v="3261.31348 -3774.23706 0"/></GUILayout><compOutputs><compOutput><uid v="1406342998"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1406343033"/><paramsArrayCells><paramsArrayCell><uid v="1406343034"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0 0 0 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1406343035"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.0999699011 0.38429752 0.0787115321 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1406592243"/><connections><connection><identifier v="input1"/><connRef v="1406343892"/><connRefOutput v="1406328199"/></connection></connections><GUILayout><gpos v="-1168 -1680 0"/></GUILayout><compOutputs><compOutput><uid v="1406592244"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.484709471 0.484709471 0.484709471 0.5"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode></compNodes><baseParameters/><options><option><name v="defaultParentSize"/><value v="11x11"/></option><option><name v="export/fromGraph/autoExport"/><value v="true"/></option><option><name v="export/fromGraph/destination"/><value v="D:/work/Annery/stylized material/stylized material/images/roots"/></option><option><name v="export/fromGraph/extension"/><value v="png"/></option><option><name v="export/fromGraph/outputs/AO"/><value v="true"/></option><option><name v="export/fromGraph/outputs/basecolor_1"/><value v="true"/></option><option><name v="export/fromGraph/outputs/height_1"/><value v="true"/></option><option><name v="export/fromGraph/outputs/metallic_1"/><value v="true"/></option><option><name v="export/fromGraph/outputs/normal_1"/><value v="true"/></option><option><name v="export/fromGraph/outputs/roughness_1"/><value v="true"/></option><option><name v="export/fromGraph/outputsColorspace/AO"/><value v="Raw"/></option><option><name v="export/fromGraph/outputsColorspace/basecolor_1"/><value v="sRGB"/></option><option><name v="export/fromGraph/outputsColorspace/height_1"/><value v="Raw"/></option><option><name v="export/fromGraph/outputsColorspace/metallic_1"/><value v="Raw"/></option><option><name v="export/fromGraph/outputsColorspace/normal_1"/><value v="Raw"/></option><option><name v="export/fromGraph/outputsColorspace/roughness_1"/><value v="Raw"/></option><option><name v="export/fromGraph/pattern"/><value v="$(graph)_$(identifier)"/></option></options><root><rootOutputs><rootOutput><output v="1406259860"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1406259862"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1406259864"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1406259866"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1406259868"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1406259870"/><format v="0"/><usertag v=""/></rootOutput></rootOutputs></root></graph></content></package>
