using System;
using System.Reflection;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using UnityEditor;
using UnityEngine;
using Object = UnityEngine.Object;

namespace ExtendedImportSettings
{
    public static class ExtendedImportUtilities
    {
        private static JsonSerializerSettings _jsonSerializerSettings;

        public static JsonSerializerSettings JsonSerializerSettings
        {
            get
            {
                if (_jsonSerializerSettings == null)
                {
                    _jsonSerializerSettings = new JsonSerializerSettings
                    {
                        ContractResolver = new PublicFieldsOnlyContractResolver(),
                    };
                }

                return _jsonSerializerSettings;
            }
        }

        public static FieldInfo[] GetFields(Type type)
        {
            return type.GetFields(BindingFlags.Public | BindingFlags.Instance);
        }
        
        public static TModule LoadModule<TModule>(AssetImporter assetImporter) where TModule: ExtendedImportModule
        {
            var name = typeof(TModule).Name;
            var index = GetExtraUserProperty(assetImporter, name, out string json);
            var existed = index > -1;

            if (!existed)
                return null;
            
            json = json.Replace($"\"{name}\":", "");
            var data = JsonConvert.DeserializeObject<TModule>(json);
            //var data = JsonUtility.FromJson<X>(json);
            if (!data.IsActive)
                return null;
            
            return data;
        }
        
        public static bool TryLoadModule<TModule>(AssetImporter assetImporter, out TModule model) where TModule: ExtendedImportModule
        {
            model = LoadModule<TModule>(assetImporter);
            return model != null;
        }
        
        public static int GetExtraUserProperty(Object target, string fieldName, out string data)
        {
            var extraUserProperties = ((ModelImporter)target).extraUserProperties;
            if (extraUserProperties == null || extraUserProperties.Length == 0)
            {
                data = null;
                return -1;
            }

            for (var i = 0; i < extraUserProperties.Length; i++)
            {
                if (extraUserProperties[i].Contains(fieldName))
                {
                    data = extraUserProperties[i];
                    return i;
                }
            }

            data = null;
            return -1;
        }
        
        public static string InsertSpaces(string input)
        {
            return Regex.Replace(input, @"([A-Z])", " $1").Trim();
        }
        
        public static void OpenScript(string scriptName)
        {
            string[] guids = AssetDatabase.FindAssets(scriptName + " t:Script");
            if (guids.Length > 0)
            {
                string path = AssetDatabase.GUIDToAssetPath(guids[0]);
                Object scriptAsset = AssetDatabase.LoadAssetAtPath<Object>(path);
                AssetDatabase.OpenAsset(scriptAsset);
            }
            else
            {
                Debug.LogError("Script " + scriptName + " not found!");
            }
        }
    }
}