#if URP_INSTALLED
using System;
using UnityEngine;

namespace Stylo.BFI.Universal
{
    [CreateAssetMenu(fileName = "New BFI Preset", menuName = "Stylo/BFI/BFI Preset")]
    public class BFIPreset : ScriptableObject
    {
        [Header("Preset Information")]
        public string presetName = "New BFI Preset";
        [TextArea(3, 5)]
        public string description = "Custom BFI configuration";
        
        [Header("BFI Settings")]
        public BFIEffect.BFIMode mode = BFIEffect.BFIMode.DoubleFrameRate;
        public float intensity = 1f;
        
        [Header("Brightness & Compensation")]
        public float brightnessCompensation = 1.5f;
        public float manualBrightness = 1f;
        public float gammaCorrection = 1f;
        
        [Header("Timing & Synchronization")]
        public float blackFrameDuration = 0.5f;
        public float phaseOffset = 0f;
        public bool syncToRefreshRate = true;
        
        [Header("Flicker Reduction")]
        public BFIEffect.FlickerReduction flickerReductionMode = BFIEffect.FlickerReduction.Medium;
        public float temporalSmoothing = 0.2f;
        public float edgePreservation = 0.8f;
        
        [Header("Motion Adaptive")]
        public bool motionAdaptive = false;
        public float motionSensitivity = 1f;
        public float motionThreshold = 0.01f;
        public float adaptationSpeed = 2f;
        
        [Header("Advanced Settings")]
        public float ditheringStrength = 0.1f;
        public float subpixelPrecision = 1f;
        public bool debugVisualization = false;

        /// <summary>
        /// Apply this preset to a BFI Effect volume component
        /// </summary>
        /// <param name="bfiEffect">The BFI Effect to apply settings to</param>
        public void ApplyToEffect(BFIEffect bfiEffect)
        {
            if (bfiEffect == null) return;

            // Master Controls
            bfiEffect.Mode.value = mode;
            bfiEffect.Intensity.value = intensity;
            
            // Brightness & Compensation
            bfiEffect.BrightnessCompensation.value = brightnessCompensation;
            bfiEffect.ManualBrightness.value = manualBrightness;
            bfiEffect.GammaCorrection.value = gammaCorrection;
            
            // Timing & Synchronization
            bfiEffect.BlackFrameDuration.value = blackFrameDuration;
            bfiEffect.PhaseOffset.value = phaseOffset;
            bfiEffect.SyncToRefreshRate.value = syncToRefreshRate;
            
            // Flicker Reduction
            bfiEffect.FlickerReductionMode.value = flickerReductionMode;
            bfiEffect.TemporalSmoothing.value = temporalSmoothing;
            bfiEffect.EdgePreservation.value = edgePreservation;
            
            // Motion Adaptive
            bfiEffect.MotionAdaptive.value = motionAdaptive;
            bfiEffect.MotionSensitivity.value = motionSensitivity;
            bfiEffect.MotionThreshold.value = motionThreshold;
            bfiEffect.AdaptationSpeed.value = adaptationSpeed;
            
            // Advanced Settings
            bfiEffect.DitheringStrength.value = ditheringStrength;
            bfiEffect.SubpixelPrecision.value = subpixelPrecision;
            bfiEffect.DebugVisualization.value = debugVisualization;
        }

        /// <summary>
        /// Load settings from a BFI Effect volume component
        /// </summary>
        /// <param name="bfiEffect">The BFI Effect to load settings from</param>
        public void LoadFromEffect(BFIEffect bfiEffect)
        {
            if (bfiEffect == null) return;

            // Master Controls
            mode = bfiEffect.Mode.value;
            intensity = bfiEffect.Intensity.value;
            
            // Brightness & Compensation
            brightnessCompensation = bfiEffect.BrightnessCompensation.value;
            manualBrightness = bfiEffect.ManualBrightness.value;
            gammaCorrection = bfiEffect.GammaCorrection.value;
            
            // Timing & Synchronization
            blackFrameDuration = bfiEffect.BlackFrameDuration.value;
            phaseOffset = bfiEffect.PhaseOffset.value;
            syncToRefreshRate = bfiEffect.SyncToRefreshRate.value;
            
            // Flicker Reduction
            flickerReductionMode = bfiEffect.FlickerReductionMode.value;
            temporalSmoothing = bfiEffect.TemporalSmoothing.value;
            edgePreservation = bfiEffect.EdgePreservation.value;
            
            // Motion Adaptive
            motionAdaptive = bfiEffect.MotionAdaptive.value;
            motionSensitivity = bfiEffect.MotionSensitivity.value;
            motionThreshold = bfiEffect.MotionThreshold.value;
            adaptationSpeed = bfiEffect.AdaptationSpeed.value;
            
            // Advanced Settings
            ditheringStrength = bfiEffect.DitheringStrength.value;
            subpixelPrecision = bfiEffect.SubpixelPrecision.value;
            debugVisualization = bfiEffect.DebugVisualization.value;
        }

        /// <summary>
        /// Create a preset with default settings for the specified BFI mode
        /// </summary>
        /// <param name="bfiMode">The BFI mode to create defaults for</param>
        /// <returns>A new BFI preset with appropriate defaults</returns>
        public static BFIPreset CreateDefaultPreset(BFIEffect.BFIMode bfiMode)
        {
            var preset = CreateInstance<BFIPreset>();
            
            preset.mode = bfiMode;
            preset.presetName = $"BFI {bfiMode} Default";
            
            switch (bfiMode)
            {
                case BFIEffect.BFIMode.DoubleFrameRate:
                    preset.description = "Standard 2x BFI for 120Hz displays. Good balance of motion clarity and compatibility.";
                    preset.brightnessCompensation = 1.8f;
                    preset.flickerReductionMode = BFIEffect.FlickerReduction.Medium;
                    break;
                    
                case BFIEffect.BFIMode.TripleFrameRate:
                    preset.description = "3x BFI for 180Hz displays. Enhanced motion clarity with higher brightness compensation.";
                    preset.brightnessCompensation = 2.2f;
                    preset.flickerReductionMode = BFIEffect.FlickerReduction.High;
                    break;
                    
                case BFIEffect.BFIMode.QuadFrameRate:
                    preset.description = "4x BFI for 240Hz displays. Maximum motion clarity for high-end displays.";
                    preset.brightnessCompensation = 2.8f;
                    preset.flickerReductionMode = BFIEffect.FlickerReduction.High;
                    preset.temporalSmoothing = 0.3f;
                    break;
                    
                default:
                    preset.description = "BFI disabled.";
                    preset.intensity = 0f;
                    break;
            }
            
            return preset;
        }
    }
}
#endif
