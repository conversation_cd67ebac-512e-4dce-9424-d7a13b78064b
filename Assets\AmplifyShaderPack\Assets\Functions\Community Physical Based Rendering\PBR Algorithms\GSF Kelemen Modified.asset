%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: GSF Kelemen Modified
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor v1.9.4.4\n// Available at the
    Unity Asset Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=19404\nNode;AmplifyShaderEditor.CommentaryNode;32;-1920,1408;Inherit;False;1659.915;798.8145;Math;19;235;236;396;242;474;234;233;395;225;121;238;190;120;189;468;393;391;470;471;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;397;-3007.25,1408;Inherit;False;1026.987;569.1089;Dot
    Products;9;434;401;400;403;402;300;458;475;476;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;471;-862.9596,1472;Inherit;False;184.7379;127.1656;NdotL;1;310;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;470;-1165.54,1472;Inherit;False;187.5781;132.8458;NdotV;1;311;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;232;-1920,1040;Inherit;False;770.6545;291.9468;GSF
    Kelemen Modified Node;1;50;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;50;-1584,1088;Inherit;False;390.4673;170.4649;;;0,0,0,1;This
    is a modified form of the Kelemen Approximation of Cook-Torrance$$It has been
    modified to produce the Keleman GSF distributed by roughness -Jorden Stevens;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;391;-1408,1600;Inherit;False;224.5932;114.7734;Roughness;;0,0,0,1;;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;393;-1376,1984;Inherit;False;261.9292;189.818;PI
    Inv Approx;;0,0,0,1;Near:$0.797884560802865$$Exact:$sqrt(2 / Pi)$$PI Inv Approx$0.7978846$;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;468;-944,1888;Inherit;False;272.866;113.9399;Roughness
    Approxmation;;0,0,0,1;for Functions/Methods that share similar Roughness Calculation;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;469;-1920,832;Inherit;False;511.7001;170.1489;Credits;;0,0,0,0.5019608;Method
    by Jordan Stevens$Link: https://www.jordanstevenstechart.com/physically-based-rendering$$Node
    Made by Tiber Legacy$Special Thanks to Dawie;0;0\nNode;AmplifyShaderEditor.OneMinusNode;189;-944,1728;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;120;-944,1616;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;190;-768,1616;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;238;-1152,1728;Inherit;False;3;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;310;-848,1520;Inherit;False;300;NdotL;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;311;-1152,1520;Inherit;False;458;NdotV;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;458;-2256,1504;Inherit;True;NdotV;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;300;-2256,1728;Inherit;True;NdotL;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;402;-2688,1584;Inherit;False;NdotV;1;2;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;403;-2688,1808;Inherit;False;NdotL;1;1;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;401;-2528,1728;Inherit;False;Option;False;0;2;1;In
    0;In 1;Instance;400;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;434;-2944,1504;Inherit;False;Normal;3;0;False;1;0;FLOAT3;0,0,1;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitch;400;-2528,1504;Inherit;False;Custom
    Dot Products;True;0;2;1;In 0;In 1;Object;-1;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;121;-640,1520;Inherit;False;3;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;395;-1376,1856;Inherit;False;PI
    Inv Approx;False;0;3;-1;Near;Exact;Custom;Object;-1;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SqrtOpNode;233;-1520,1936;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleDivideOpNode;234;-1680,1936;Inherit;False;2;0;FLOAT;2;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;474;-1552,2032;Inherit;False;PI
    Inv;1;3;False;1;0;FLOAT;0.7978846;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;242;-1344,1728;Inherit;False;Roughness;1;4;False;1;0;FLOAT;0.5;False;1;FLOAT;0\nNode;AmplifyShaderEditor.PiNode;236;-1872,1984;Inherit;False;1;0;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;235;-1616,1856;Inherit;False;Constant;_ApproxSqrt2PI1;_Approx.
    Sqrt(2/PI);0;0;Create;True;0;0;0;False;0;False;0.7978846;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionNode;475;-2752,1504;Inherit;False;NdotV;-1;;3;65be4ec269a534f4e8867af2f999aaa9;4,27,1,377,0,452,0,451,0;3;230;FLOAT3;0,0,1;False;443;FLOAT3;0,0,0;False;415;FLOAT;1E-37;False;1;FLOAT;23\nNode;AmplifyShaderEditor.FunctionNode;476;-2752,1728;Inherit;False;NdotL;-1;;4;6d7ee1aa8c79587448475fa2b37affc1;5,27,1,381,0,460,0,461,0,463,0;4;230;FLOAT3;0,0,1;False;462;FLOAT3;0,0,0;False;454;FLOAT3;0,0,0;False;416;FLOAT;1E-37;False;1;FLOAT;111\nNode;AmplifyShaderEditor.FunctionOutput;225;-464,1520;Inherit;True;True;-1;Out;0;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;396;-944,1808;Inherit;False;False;-1;Roughness
    Approxmation;1;False;1;0;FLOAT;0;False;1;FLOAT;0\nWireConnection;189;0;238;0\nWireConnection;120;0;311;0\nWireConnection;120;1;238;0\nWireConnection;190;0;120;0\nWireConnection;190;1;189;0\nWireConnection;238;0;242;0\nWireConnection;238;1;242;0\nWireConnection;238;2;395;0\nWireConnection;458;0;400;0\nWireConnection;300;0;401;0\nWireConnection;401;0;476;111\nWireConnection;401;1;403;0\nWireConnection;400;0;475;23\nWireConnection;400;1;402;0\nWireConnection;121;0;310;0\nWireConnection;121;1;190;0\nWireConnection;121;2;190;0\nWireConnection;395;0;235;0\nWireConnection;395;1;233;0\nWireConnection;395;2;474;0\nWireConnection;233;0;234;0\nWireConnection;234;1;236;0\nWireConnection;475;230;434;0\nWireConnection;476;230;434;0\nWireConnection;225;0;121;0\nWireConnection;396;0;238;0\nASEEND*/\n//CHKSM=AE7FA64E0A2CDD8AC400BBF97150714ACB8C8833"
  m_functionName: 
  m_description: 'GSF Kelemen Modified Node

'
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 0
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_customEdited: 0
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 0
  m_headerStyle: 2
  m_headerColor: {r: 0.7529412, g: 0.6784314, b: 0, a: 1}
  m_customNodeCategory: Amplify Shader Pack
  m_previewPosition: 0
  m_hidden: 0
  m_url: 
