# Real-Time Spectrum Analyzer Guide

## Overview

The Cadance Real-Time Spectrum Analyzer provides live frequency analysis and automatic event creation during audio playback. This system replaces the complex offline analysis workflow with a streamlined real-time approach.

## Features

### Visual Spectrum Analysis

- **Real-time frequency visualization** - Live spectrum bars that update during playback
- **Customizable frequency range** - Set min/max Hz for focused analysis (default: 60Hz-8000Hz)
- **Adjustable visualization** - Control bar count, height, and colors

### Automatic Event Creation

- **Threshold-based triggering** - Events created when frequency peaks exceed threshold
- **Duplicate prevention** - Configurable minimum time gaps between events
- **Loop-aware** - Prevents duplicate events during looped playback
- **Magnitude payload** - Events include frequency magnitude as FloatPayload

### FMOD Integration

- **Works with disabled Unity audio** - Uses FMOD DSP for spectrum analysis
- **Real-time FFT processing** - Leverages FMOD's high-performance audio pipeline
- **Timeline synchronization** - Perfect sync with Cadance Editor timeline

## Usage

### Basic Setup

1. **Open Cadance Editor** - Window > Stylo > Cadance > Cadance Editor
2. **Load audio asset** - Assign a CadanceAsset with AudioClip
3. **Select target track** - Choose the track where events will be created
4. **Enable spectrum analyzer** - Click "Spectrum" button in toolbar

### Configuration

#### Frequency Range

```
Min Frequency: 60 Hz (default) - Lower bound for analysis
Max Frequency: 8000 Hz (default) - Upper bound for analysis
```

#### Event Creation

```
Threshold: 0.1 (default) - Amplitude level for event triggering
Min Event Gap: 0.1s (default) - Minimum time between events
Auto Events: Enabled/Disabled - Toggle automatic event creation
```

#### Visualization

```
Spectrum Bars: 64 (default) - Number of frequency bars to display
Height: 100px (default) - Visualization panel height
```

### Workflow

1. **Configure settings** - Set frequency range and threshold
2. **Start playback** - Press play button to begin audio
3. **Monitor visualization** - Watch real-time frequency analysis
4. **Review events** - Check automatically created events on timeline
5. **Adjust parameters** - Fine-tune threshold and frequency range as needed

## Technical Details

### FMOD DSP Integration

- Uses `DSP_TYPE.FFT` for real-time spectrum analysis
- 1024-sample FFT window with Blackman-Harris windowing
- Automatic channel mixing for multi-channel audio

### Event Creation Logic

```csharp
// Frequency magnitude calculation
float magnitude = FMODEditorAudioSystem.GetMagnitudeInRange(minFreq, maxFreq, sampleRate);

// Threshold check
if (magnitude >= threshold && ShouldCreateEvent(samplePosition, timePosition))
{
    var eventPayload = new FloatPayload(magnitude);
    var cadanceEvent = new CadanceEvent(targetTrack.EventID, samplePosition, eventPayload);
    targetTrack.AddEvent(cadanceEvent);
}
```

### Duplicate Prevention

- **Sample-based tracking** - Prevents events at identical sample positions
- **Time-based gaps** - Enforces minimum time between events
- **Loop detection** - Maintains event history across loop cycles

## Comparison with Legacy System

### Old Offline Analysis

- Complex multi-step workflow
- Separate analysis window
- Batch processing of entire audio file
- Multiple analysis types (RMS, Spectrum, Beat)
- Professional-grade but complex configuration

### New Real-Time Analyzer

- **Simplified workflow** - Integrated into main editor
- **Live feedback** - See results immediately during playback
- **Focused functionality** - Spectrum analysis with automatic events
- **User-friendly** - Minimal configuration required
- **Performance optimized** - Real-time processing with FMOD

## Best Practices

### Frequency Range Selection

- **Music analysis**: 60Hz - 8000Hz (default)
- **Bass-focused**: 20Hz - 250Hz
- **Vocal range**: 300Hz - 3000Hz
- **High frequencies**: 2000Hz - 16000Hz

### Threshold Tuning

- **Start high** (0.3-0.5) to capture only prominent peaks
- **Gradually lower** to capture more subtle events
- **Monitor event density** - avoid creating too many events

### Event Management

- **Use descriptive track names** - e.g., "Bass_Peaks", "Vocal_Hits"
- **Review generated events** - Remove unwanted events manually
- **Combine with manual events** - Mix automatic and manual event placement

## Troubleshooting

### No Spectrum Visualization

**Symptoms:** Spectrum analyzer shows "No spectrum data available" message
**Causes & Solutions:**

- **FMOD not playing:** Ensure audio is playing through FMOD (not Unity AudioSource fallback)
- **Unity audio disabled:** This is expected - FMOD integration required for spectrum analysis
- **FMOD system not initialized:** Check FMOD settings and ensure FMOD is properly configured
- **AudioClip not loaded:** Verify AudioClip is properly assigned to CadanceAsset

**Status Messages:**

- `"FMOD Spectrum: Available"` = Full functionality enabled
- `"FMOD Spectrum: Not Available"` = Visualization-only mode (no event creation)
- `"Analysis: Running (Visualization Only)"` = Analyzer active but no spectrum data

### No Events Created

**Symptoms:** Spectrum visualization works but no events appear on timeline
**Causes & Solutions:**

- **No track selected:** Select a track in the Cadance Editor
- **Threshold too high:** Lower the threshold value (try 0.1-0.3)
- **Auto Events disabled:** Enable "Auto Events" checkbox
- **Frequency range mismatch:** Adjust frequency range to match audio content
- **FMOD spectrum not available:** Event creation requires FMOD spectrum data

### Too Many Events

**Symptoms:** Events created too frequently, cluttering timeline
**Solutions:**

- **Increase threshold:** Higher values (0.4-0.8) capture only prominent peaks
- **Increase minimum event gap:** Set to 0.2-0.5 seconds for sparse events
- **Narrow frequency range:** Focus on specific frequency bands (e.g., 60-250Hz for bass)

### FMOD Audio Not Playing

**Symptoms:** Audio plays through Unity AudioSource instead of FMOD
**Causes & Solutions:**

- **FMOD initialization failed:** Check FMOD settings in Project Settings
- **Audio file format:** Ensure audio file is compatible with FMOD
- **FMOD banks not loaded:** Verify FMOD Studio banks are properly loaded
- **Unity audio not disabled:** Confirm Unity audio is disabled in project settings

### Performance Issues

**Symptoms:** Editor becomes slow or unresponsive during analysis
**Solutions:**

- **Reduce spectrum bars:** Lower from 64 to 32 or 16 bars
- **Close other windows:** Minimize other heavy editor windows
- **Reduce FFT size:** Contact support for advanced configuration options
- **Disable visualization:** Keep only event creation enabled if visualization not needed

### Common Error Messages

**"Failed to enable FMOD spectrum analysis"**

- FMOD audio system not initialized or no active channel
- Start audio playback first, then enable spectrum analyzer

**"No active FMOD channel"**

- Audio is playing through Unity AudioSource instead of FMOD
- Check FMOD configuration and audio file compatibility

**"Spectrum analysis not available"**

- Normal when using Unity AudioSource fallback
- Analyzer will work in visualization-only mode

## Integration with Existing Workflow

The real-time spectrum analyzer integrates seamlessly with existing Cadance workflows:

- **Compatible with all payload types** - Generated FloatPayload events work with existing systems
- **Timeline integration** - Events appear immediately on timeline
- **Undo/Redo support** - Event creation respects editor undo system
- **Export compatibility** - Generated events export with CadanceAsset

## Future Enhancements

Potential improvements for future versions:

- **Multi-band analysis** - Separate bass/mid/treble analyzers
- **Onset detection** - AI-powered beat and rhythm detection
- **Spectrogram view** - Time-frequency visualization
- **Custom event types** - User-defined event categories
- **MIDI export** - Convert events to MIDI for external tools

---

_For technical support or feature requests, refer to the main Cadance documentation._
