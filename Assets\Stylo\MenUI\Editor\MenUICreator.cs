using UnityEngine;
using UnityEngine.UI;
using UnityEditor;
using TMPro;
using System.IO;
using Object = UnityEngine.Object;

namespace Stylo.MenUI.Editor
{
    /// <summary>
    /// Editor tool to create complete MenUI system with integrated settings panels.
    /// Creates everything as one unified pause menu system.
    /// </summary>
    public static class MenUICreator
    {
        [MenuItem("Stylo/Create MenUI System")]
        public static void CreateCompletePauseMenuUI()
        {
            // Check if MenUI system already exists
            if (Object.FindObjectOfType<MenUISystem>() != null)
            {
                if (!EditorUtility.DisplayDialog("MenUI System Exists",
                    "A MenUI System already exists in the scene. Do you want to create another one?",
                    "Yes", "Cancel"))
                {
                    return;
                }
            }

            // Create the complete MenUI system with integrated settings
            GameObject menUIRoot = CreateMenUIRoot();
            GameObject pauseMenu = CreatePauseMenu(menUIRoot);
            GameObject settingsPanel = CreateCompleteSettingsPanel(menUIRoot);

            // Setup MenUISystem component with proper references
            MenUISystem menUISystem = SetupMenUISystem(menUIRoot, pauseMenu, settingsPanel);

            // Build complete settings UI with all components
            SettingsPanelManager settingsManager = settingsPanel.GetComponent<SettingsPanelManager>();
            MenUIUIBuilder.BuildCompleteSettingsUI(settingsPanel, settingsManager);

            // Apply proper fonts to all text components
            MenUIUIBuilder.ApplyFontsToAllText(menUIRoot);

            // Select the created object
            Selection.activeGameObject = menUIRoot;

            EditorUtility.DisplayDialog("MenUI System Created!",
                "Complete MenUI System created successfully!\n\n" +
                "✅ Pause menu with Resume, Settings, Exit\n" +
                "✅ Complete settings system (5 categories)\n" +
                "✅ Graphics, Audio, Controls, Gameplay, Debug\n" +
                "✅ All UI elements and references configured\n" +
                "✅ BTR system integration (FMOD, Epoch, URP)\n\n" +
                "Ready to use! Test with pause input or editor controls.",
                "OK");

            Debug.Log("MenUI System created: Complete pause menu with integrated settings ready!");
        }

        /// <summary>
        /// Loads the best available font for the UI. Tries multiple font options in order of preference.
        /// </summary>
        /// <returns>A TMP_FontAsset or null if none found</returns>
        private static TMP_FontAsset LoadBestAvailableFont()
        {
            // Try to load fonts in order of preference
            string[] fontPaths = {
                "Fonts & Materials/LiberationSans SDF",          // Default TMP font
                "Feel/MMTools/Demos/MMTween/Fonts/Lato/SDF/Lato SDF",  // Lato font from Feel
                "Plugins/AllIn13DShader/Demo/Fonts/PoetsenOne-Regular SDF", // PoetsenOne font
                "Fonts/WipEout-Fonts-master/WO3 SDF",           // WipEout style - cyberpunk
                "Fonts/WipEout-Fonts-master/Fusion SDF",        // Fusion style - clean cyberpunk
                "Fonts/square_sans_serif_7 SDF",                // Square sans serif - tech look
                "Fonts/GlacialIndifference-Regular SDF"         // Clean modern font
            };

            foreach (string fontPath in fontPaths)
            {
                TMP_FontAsset font = Resources.Load<TMP_FontAsset>(fontPath);
                if (font != null)
                {
                    Debug.Log($"MenUICreator: Loaded font '{font.name}' from '{fontPath}'");
                    return font;
                }
            }

            // Try to get the default font from TMP settings
            TMP_Settings tmpSettings = TMP_Settings.instance;
            if (tmpSettings != null && tmpSettings.defaultFontAsset != null)
            {
                Debug.Log($"MenUICreator: Using TMP default font '{tmpSettings.defaultFontAsset.name}'");
                return tmpSettings.defaultFontAsset;
            }

            Debug.LogWarning("MenUICreator: No fonts found, TextMeshPro will use its built-in default");
            return null;
        }

        private static GameObject CreateMenUIRoot()
        {
            // Create root Canvas
            GameObject canvasGO = new GameObject("MenUI System");
            Canvas canvas = canvasGO.AddComponent<Canvas>();
            canvas.renderMode = RenderMode.ScreenSpaceOverlay;
            canvas.sortingOrder = 100;

            CanvasScaler scaler = canvasGO.AddComponent<CanvasScaler>();
            scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
            scaler.referenceResolution = new Vector2(1920, 1080);
            scaler.screenMatchMode = CanvasScaler.ScreenMatchMode.MatchWidthOrHeight;
            scaler.matchWidthOrHeight = 0.5f;

            canvasGO.AddComponent<GraphicRaycaster>();

            return canvasGO;
        }

        private static GameObject CreatePauseMenu(GameObject parent)
        {
            // Create pause menu panel
            GameObject pausePanel = CreateUIPanel("Pause Menu Panel", parent);
            SetupPanelBackground(pausePanel, new Color(0, 0, 0, 0.8f));

            // Create vertical layout for buttons
            GameObject buttonContainer = CreateUIPanel("Button Container", pausePanel);
            VerticalLayoutGroup vlg = buttonContainer.AddComponent<VerticalLayoutGroup>();
            vlg.spacing = 20f;
            vlg.padding = new RectOffset(50, 50, 50, 50);
            vlg.childAlignment = TextAnchor.MiddleCenter;
            vlg.childControlWidth = true;
            vlg.childControlHeight = false;
            vlg.childForceExpandWidth = true;
            vlg.childForceExpandHeight = false;

            // Set container size
            RectTransform containerRect = buttonContainer.GetComponent<RectTransform>();
            containerRect.anchorMin = new Vector2(0.3f, 0.3f);
            containerRect.anchorMax = new Vector2(0.7f, 0.7f);
            containerRect.offsetMin = Vector2.zero;
            containerRect.offsetMax = Vector2.zero;

            // Create buttons
            CreateMenuButton("Resume", buttonContainer);
            CreateMenuButton("Settings", buttonContainer);
            CreateMenuButton("Exit", buttonContainer);

            return pausePanel;
        }

        private static GameObject CreateCompleteSettingsPanel(GameObject parent)
        {
            // Create main settings panel
            GameObject settingsPanel = CreateUIPanel("Settings Panel", parent);
            SetupPanelBackground(settingsPanel, new Color(0, 0, 0, 0.9f));
            settingsPanel.SetActive(false); // Hidden by default

            // Add SettingsPanelManager component
            SettingsPanelManager manager = settingsPanel.AddComponent<SettingsPanelManager>();

            // Create header with title and back button
            GameObject header = CreateSettingsHeader(settingsPanel);

            // Create category tabs container
            GameObject categoryContainer = CreateCategoryTabs(settingsPanel);

            // Create content area
            GameObject contentArea = CreateContentArea(settingsPanel);

            // Create all settings panels
            CreateGraphicsSettingsPanel(contentArea);
            CreateAudioSettingsPanel(contentArea);
            CreateControlsSettingsPanel(contentArea);
            CreateGameplaySettingsPanel(contentArea);
            CreateDebugSettingsPanel(contentArea);

            // Create footer with Apply/Reset buttons
            GameObject footer = CreateSettingsFooter(settingsPanel);

            // Setup SettingsPanelManager references
            SetupSettingsPanelManagerReferences(manager, settingsPanel, categoryContainer, contentArea, footer);

            return settingsPanel;
        }

        private static GameObject CreateUIPanel(string name, GameObject parent)
        {
            GameObject panel = new GameObject(name);
            panel.transform.SetParent(parent.transform, false);

            RectTransform rect = panel.AddComponent<RectTransform>();
            rect.anchorMin = Vector2.zero;
            rect.anchorMax = Vector2.one;
            rect.offsetMin = Vector2.zero;
            rect.offsetMax = Vector2.zero;

            return panel;
        }

        private static void SetupPanelBackground(GameObject panel, Color color)
        {
            Image bg = panel.AddComponent<Image>();
            bg.color = color;
        }

        private static GameObject CreateMenuButton(string text, GameObject parent)
        {
            GameObject buttonGO = new GameObject(text + " Button");
            buttonGO.transform.SetParent(parent.transform, false);

            // Add Image component for background
            Image bg = buttonGO.AddComponent<Image>();
            bg.color = new Color(0.2f, 0.2f, 0.2f, 0.8f);

            // Add Button component
            Button button = buttonGO.AddComponent<Button>();

            // Create text child
            GameObject textGO = new GameObject("Text");
            textGO.transform.SetParent(buttonGO.transform, false);

            TextMeshProUGUI textComp = textGO.AddComponent<TextMeshProUGUI>();
            textComp.text = text;
            textComp.fontSize = 24;
            textComp.color = Color.white;
            textComp.alignment = TextAlignmentOptions.Center;
            textComp.fontStyle = FontStyles.Bold;

            // Load and assign a proper font
            TMP_FontAsset font = LoadBestAvailableFont();
            if (font != null)
            {
                textComp.font = font;
            }

            // Setup text rect
            RectTransform textRect = textGO.GetComponent<RectTransform>();
            textRect.anchorMin = Vector2.zero;
            textRect.anchorMax = Vector2.one;
            textRect.offsetMin = Vector2.zero;
            textRect.offsetMax = Vector2.zero;

            // Setup button rect
            RectTransform buttonRect = buttonGO.GetComponent<RectTransform>();
            buttonRect.sizeDelta = new Vector2(200, 50);

            // Setup button colors
            ColorBlock colors = button.colors;
            colors.normalColor = new Color(0.2f, 0.2f, 0.2f, 0.8f);
            colors.highlightedColor = new Color(0.3f, 0.3f, 0.3f, 0.8f);
            colors.pressedColor = new Color(0.1f, 0.1f, 0.1f, 0.8f);
            button.colors = colors;

            return buttonGO;
        }

        private static GameObject CreateSettingsHeader(GameObject parent)
        {
            GameObject header = CreateUIPanel("Header", parent);
            RectTransform headerRect = header.GetComponent<RectTransform>();
            headerRect.anchorMin = new Vector2(0, 0.9f);
            headerRect.anchorMax = new Vector2(1, 1);

            // Create title
            GameObject titleGO = new GameObject("Title");
            titleGO.transform.SetParent(header.transform, false);

            TextMeshProUGUI title = titleGO.AddComponent<TextMeshProUGUI>();
            title.text = "Settings";
            title.fontSize = 36;
            title.color = Color.white;
            title.alignment = TextAlignmentOptions.Center;
            title.fontStyle = FontStyles.Bold;

            // Load and assign a proper font
            TMP_FontAsset font = LoadBestAvailableFont();
            if (font != null)
            {
                title.font = font;
            }

            RectTransform titleRect = titleGO.GetComponent<RectTransform>();
            titleRect.anchorMin = new Vector2(0.1f, 0);
            titleRect.anchorMax = new Vector2(0.9f, 1);
            titleRect.offsetMin = Vector2.zero;
            titleRect.offsetMax = Vector2.zero;

            // Create back button
            GameObject backButton = CreateMenuButton("Back", header);
            RectTransform backRect = backButton.GetComponent<RectTransform>();
            backRect.anchorMin = new Vector2(0.85f, 0.2f);
            backRect.anchorMax = new Vector2(0.98f, 0.8f);
            backRect.offsetMin = Vector2.zero;
            backRect.offsetMax = Vector2.zero;

            return header;
        }

        private static GameObject CreateCategoryTabs(GameObject parent)
        {
            GameObject categoryContainer = CreateUIPanel("Category Tabs", parent);
            RectTransform categoryRect = categoryContainer.GetComponent<RectTransform>();
            categoryRect.anchorMin = new Vector2(0, 0.8f);
            categoryRect.anchorMax = new Vector2(1, 0.9f);

            // Add horizontal layout
            HorizontalLayoutGroup hlg = categoryContainer.AddComponent<HorizontalLayoutGroup>();
            hlg.spacing = 10f;
            hlg.padding = new RectOffset(20, 20, 10, 10);
            hlg.childAlignment = TextAnchor.MiddleCenter;
            hlg.childControlWidth = true;
            hlg.childControlHeight = true;
            hlg.childForceExpandWidth = true;
            hlg.childForceExpandHeight = true;

            // Create category buttons
            CreateCategoryButton("Graphics", categoryContainer);
            CreateCategoryButton("Audio", categoryContainer);
            CreateCategoryButton("Controls", categoryContainer);
            CreateCategoryButton("Gameplay", categoryContainer);
            CreateCategoryButton("Debug", categoryContainer);

            return categoryContainer;
        }

        private static GameObject CreateCategoryButton(string categoryName, GameObject parent)
        {
            GameObject button = CreateMenuButton(categoryName, parent);

            // Adjust for category styling
            Button buttonComp = button.GetComponent<Button>();
            ColorBlock colors = buttonComp.colors;
            colors.normalColor = new Color(0.3f, 0.3f, 0.3f, 0.8f);
            colors.highlightedColor = new Color(0.4f, 0.4f, 0.4f, 0.8f);
            colors.selectedColor = new Color(0, 1f, 1f, 0.8f); // Cyan for selected
            buttonComp.colors = colors;

            return button;
        }

        private static GameObject CreateContentArea(GameObject parent)
        {
            GameObject contentArea = CreateUIPanel("Content Area", parent);
            RectTransform contentRect = contentArea.GetComponent<RectTransform>();
            contentRect.anchorMin = new Vector2(0, 0.1f);
            contentRect.anchorMax = new Vector2(1, 0.8f);

            return contentArea;
        }

        private static GameObject CreateSettingsFooter(GameObject parent)
        {
            GameObject footer = CreateUIPanel("Footer", parent);
            RectTransform footerRect = footer.GetComponent<RectTransform>();
            footerRect.anchorMin = new Vector2(0, 0);
            footerRect.anchorMax = new Vector2(1, 0.1f);

            // Add horizontal layout for buttons
            HorizontalLayoutGroup hlg = footer.AddComponent<HorizontalLayoutGroup>();
            hlg.spacing = 20f;
            hlg.padding = new RectOffset(50, 50, 20, 20);
            hlg.childAlignment = TextAnchor.MiddleCenter;
            hlg.childControlWidth = false;
            hlg.childControlHeight = true;
            hlg.childForceExpandWidth = false;
            hlg.childForceExpandHeight = true;

            // Create Apply and Reset buttons
            CreateMenuButton("Apply", footer);
            CreateMenuButton("Reset", footer);

            return footer;
        }

        private static void CreateGraphicsSettingsPanel(GameObject parent)
        {
            GameObject panel = CreateUIPanel("Graphics Settings Panel", parent);
            panel.SetActive(false); // Hidden by default

            // Add GraphicsSettingsPanel component
            GraphicsSettingsPanel graphicsPanel = panel.AddComponent<GraphicsSettingsPanel>();

            // Create scroll view for graphics settings
            CreateSettingsScrollView(panel, "Graphics Settings Content");
        }

        private static void CreateAudioSettingsPanel(GameObject parent)
        {
            GameObject panel = CreateUIPanel("Audio Settings Panel", parent);
            panel.SetActive(false); // Hidden by default

            // Add AudioSettingsPanel component
            AudioSettingsPanel audioPanel = panel.AddComponent<AudioSettingsPanel>();

            // Create scroll view for audio settings
            CreateSettingsScrollView(panel, "Audio Settings Content");
        }

        private static void CreateControlsSettingsPanel(GameObject parent)
        {
            GameObject panel = CreateUIPanel("Controls Settings Panel", parent);
            panel.SetActive(false); // Hidden by default

            // Add ControlsSettingsPanel component
            ControlsSettingsPanel controlsPanel = panel.AddComponent<ControlsSettingsPanel>();

            // Create scroll view for controls settings
            CreateSettingsScrollView(panel, "Controls Settings Content");
        }

        private static void CreateGameplaySettingsPanel(GameObject parent)
        {
            GameObject panel = CreateUIPanel("Gameplay Settings Panel", parent);
            panel.SetActive(false); // Hidden by default

            // Add GameplaySettingsPanel component
            GameplaySettingsPanel gameplayPanel = panel.AddComponent<GameplaySettingsPanel>();

            // Create scroll view for gameplay settings
            CreateSettingsScrollView(panel, "Gameplay Settings Content");
        }

        private static void CreateDebugSettingsPanel(GameObject parent)
        {
            GameObject panel = CreateUIPanel("Debug Settings Panel", parent);
            panel.SetActive(false); // Hidden by default

            // Add DebugSettingsPanel component
            DebugSettingsPanel debugPanel = panel.AddComponent<DebugSettingsPanel>();

            // Create scroll view for debug settings
            CreateSettingsScrollView(panel, "Debug Settings Content");
        }

        private static GameObject CreateSettingsScrollView(GameObject parent, string contentName)
        {
            // Create scroll view
            GameObject scrollView = new GameObject("Scroll View");
            scrollView.transform.SetParent(parent.transform, false);

            RectTransform scrollRect = scrollView.AddComponent<RectTransform>();
            scrollRect.anchorMin = Vector2.zero;
            scrollRect.anchorMax = Vector2.one;
            scrollRect.offsetMin = Vector2.zero;
            scrollRect.offsetMax = Vector2.zero;

            // Add ScrollRect component
            ScrollRect scroll = scrollView.AddComponent<ScrollRect>();
            scroll.horizontal = false;
            scroll.vertical = true;
            scroll.movementType = ScrollRect.MovementType.Clamped;
            scroll.scrollSensitivity = 20f;

            // Create viewport
            GameObject viewport = new GameObject("Viewport");
            viewport.transform.SetParent(scrollView.transform, false);

            RectTransform viewportRect = viewport.AddComponent<RectTransform>();
            viewportRect.anchorMin = Vector2.zero;
            viewportRect.anchorMax = Vector2.one;
            viewportRect.offsetMin = Vector2.zero;
            viewportRect.offsetMax = Vector2.zero;

            viewport.AddComponent<Image>().color = new Color(0, 0, 0, 0.1f);
            viewport.AddComponent<Mask>().showMaskGraphic = false;

            // Create content
            GameObject content = new GameObject(contentName);
            content.transform.SetParent(viewport.transform, false);

            RectTransform contentRect = content.AddComponent<RectTransform>();
            contentRect.anchorMin = new Vector2(0, 1);
            contentRect.anchorMax = new Vector2(1, 1);
            contentRect.pivot = new Vector2(0.5f, 1);
            contentRect.sizeDelta = new Vector2(0, 1000); // Initial height

            // Add ContentSizeFitter
            ContentSizeFitter fitter = content.AddComponent<ContentSizeFitter>();
            fitter.verticalFit = ContentSizeFitter.FitMode.PreferredSize;

            // Add VerticalLayoutGroup
            VerticalLayoutGroup vlg = content.AddComponent<VerticalLayoutGroup>();
            vlg.spacing = 10f;
            vlg.padding = new RectOffset(20, 20, 20, 20);
            vlg.childAlignment = TextAnchor.UpperCenter;
            vlg.childControlWidth = true;
            vlg.childControlHeight = false;
            vlg.childForceExpandWidth = true;
            vlg.childForceExpandHeight = false;

            // Setup ScrollRect references
            scroll.viewport = viewportRect;
            scroll.content = contentRect;

            return content;
        }

        private static MenUISystem SetupMenUISystem(GameObject root, GameObject pauseMenu, GameObject settingsPanel)
        {
            // Add MenUISystem component
            MenUISystem menUISystem = root.AddComponent<MenUISystem>();

            // Setup references using SerializedObject for proper inspector assignment
            SerializedObject serializedMenUI = new SerializedObject(menUISystem);

            // Set pause menu panel reference
            SerializedProperty pauseMenuProp = serializedMenUI.FindProperty("pauseMenuPanel");
            if (pauseMenuProp != null)
            {
                pauseMenuProp.objectReferenceValue = pauseMenu;
            }

            // Set settings panel manager reference
            SettingsPanelManager settingsManager = settingsPanel.GetComponent<SettingsPanelManager>();
            SerializedProperty settingsManagerProp = serializedMenUI.FindProperty("settingsPanelManager");
            if (settingsManagerProp != null)
            {
                settingsManagerProp.objectReferenceValue = settingsManager;
            }

            // Apply the changes
            serializedMenUI.ApplyModifiedProperties();

            Debug.Log("MenUISystem component configured with proper references.");
            return menUISystem;
        }

        private static void SetupSettingsPanelManagerReferences(SettingsPanelManager manager, GameObject settingsPanel,
            GameObject categoryContainer, GameObject contentArea, GameObject footer)
        {
            // Setup basic references
            // Note: Detailed UI element references would need to be assigned manually or via SerializedObject
            Debug.Log("SettingsPanelManager component added. Please assign UI element references in the inspector.");
        }


    }
}
