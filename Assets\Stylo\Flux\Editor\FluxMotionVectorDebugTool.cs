#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using Stylo.Flux.Universal;

namespace Stylo.Flux.Editor
{
    /// <summary>
    /// Debug tool to diagnose motion vector and reprojection issues
    /// </summary>
    public class FluxMotionVectorDebugTool : EditorWindow
    {
        private string diagnosticResults = "Click 'Diagnose Motion Vector System' to run analysis...";
        private Vector2 scrollPosition;

        [MenuItem("Tools/Flux/Motion Vector Debug Tool", false, 400)]
        public static void ShowWindow()
        {
            GetWindow<FluxMotionVectorDebugTool>("Flux Motion Vector Debug");
        }

        private void OnGUI()
        {
            EditorGUILayout.LabelField("Flux Motion Vector Debug Tool", EditorStyles.boldLabel);
            EditorGUILayout.Space();

            if (GUILayout.Button("Diagnose Motion Vector System"))
            {
                DiagnoseMotionVectorSystem();
            }

            EditorGUILayout.Space();

            if (GUILayout.Button("Enable Motion Vector Visualization"))
            {
                EnableMotionVectorVisualization();
            }

            if (GUILayout.Button("Disable Motion Vector Visualization"))
            {
                DisableMotionVectorVisualization();
            }

            EditorGUILayout.Space();

            if (GUILayout.Button("Force Enable Reprojection"))
            {
                ForceEnableReprojection();
            }

            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Diagnostic Results:", EditorStyles.boldLabel);

            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition, GUILayout.Height(300));
            EditorGUILayout.TextArea(diagnosticResults, GUILayout.ExpandHeight(true));
            EditorGUILayout.EndScrollView();

            EditorGUILayout.Space();
            if (GUILayout.Button("Open Console Window"))
            {
                EditorWindow.GetWindow(System.Type.GetType("UnityEditor.ConsoleWindow,UnityEditor.dll"));
            }
        }

        private void DiagnoseMotionVectorSystem()
        {
            var results = new System.Text.StringBuilder();
            results.AppendLine("=== UNITY 6 URP MOTION VECTOR SYSTEM DIAGNOSIS ===");
            results.AppendLine();

            // Check Unity version and URP setup
            results.AppendLine($"📊 Unity Version: {Application.unityVersion}");

            // Check URP setup
            var renderPipelineAsset = UnityEngine.Rendering.GraphicsSettings.defaultRenderPipeline;
            if (renderPipelineAsset == null)
            {
                results.AppendLine("❌ No URP Render Pipeline Asset found! Using Built-in Render Pipeline.");
                results.AppendLine("💡 Go to Edit → Project Settings → Graphics and assign a URP asset.");
                diagnosticResults = results.ToString();
                Debug.LogError("❌ No URP Render Pipeline Asset found!");
                return;
            }
            results.AppendLine($"✅ URP Render Pipeline Asset: {renderPipelineAsset.name}");

            // Find active Volume with FluxEffect
            var volumes = FindObjectsByType<Volume>(FindObjectsSortMode.None);
            FluxEffect? fluxEffect = null;
            Volume? activeVolume = null;

            foreach (var volume in volumes)
            {
                if (volume.profile != null && volume.profile.TryGet<FluxEffect>(out fluxEffect))
                {
                    activeVolume = volume;
                    break;
                }
            }

            if (fluxEffect == null)
            {
                results.AppendLine("❌ No active FluxEffect found in any Volume!");
                results.AppendLine("💡 Create a Volume with a Volume Profile containing a Flux Effect component.");
                diagnosticResults = results.ToString();
                Debug.LogError("❌ No active FluxEffect found in any Volume!");
                return;
            }

            results.AppendLine($"✅ Found FluxEffect in Volume: {activeVolume?.name ?? "Unknown"}");

            // Check DoReprojection conditions
            float reprojectPercent = fluxEffect.ReprojectBaseNoise.value * fluxEffect.EffectIntensity.value;
            float reprojectLengthInfluence = fluxEffect.ReprojectLengthInfluence.value * fluxEffect.EffectIntensity.value;
            float motionAmplification = fluxEffect.MotionAmplification.value;
            bool visualizeMotionVectors = fluxEffect.VisualizeMotionVectors.value;

            results.AppendLine();
            results.AppendLine("📊 DoReprojection Conditions:");
            results.AppendLine($"   - reprojectPercent: {reprojectPercent} (Base Noise: {fluxEffect.ReprojectBaseNoise.value} × Effect Intensity: {fluxEffect.EffectIntensity.value})");
            results.AppendLine($"   - reprojectLengthInfluence: {reprojectLengthInfluence} (Length Influence: {fluxEffect.ReprojectLengthInfluence.value} × Effect Intensity: {fluxEffect.EffectIntensity.value})");
            results.AppendLine($"   - motionAmplification: {motionAmplification}");
            results.AppendLine($"   - visualizeMotionVectors: {visualizeMotionVectors}");

            bool doReprojection = (reprojectPercent > 0f || reprojectLengthInfluence > 0f || motionAmplification > 0f || visualizeMotionVectors);

            results.AppendLine();
            if (doReprojection)
            {
                results.AppendLine("✅ DoReprojection = TRUE - Motion vectors should be enabled");
            }
            else
            {
                results.AppendLine("❌ DoReprojection = FALSE - Motion vectors are DISABLED!");
                results.AppendLine("💡 To enable motion vectors, set one of these > 0:");
                results.AppendLine("   - Base Noise (Reprojection Percent)");
                results.AppendLine("   - Length Influence");
                results.AppendLine("   - Motion Amplification");
                results.AppendLine("   - Or enable 'Visualize Motion Vectors'");
            }

            // Check camera setup - CRITICAL for Unity 6
            var cameras = FindObjectsByType<Camera>(FindObjectsSortMode.None);
            results.AppendLine();
            results.AppendLine("📊 Camera Analysis:");

            foreach (var camera in cameras)
            {
                bool isSceneView = camera.cameraType == CameraType.SceneView;
                bool isMainCamera = camera == Camera.main;

                results.AppendLine($"   - Camera: {camera.name}");
                results.AppendLine($"     Type: {camera.cameraType} {(isSceneView ? "❌ EXCLUDED from motion vectors" : "✅ Eligible")}");
                results.AppendLine($"     Main Camera: {isMainCamera}");

                var cameraData = camera.GetUniversalAdditionalCameraData();
                if (cameraData != null)
                {
                    results.AppendLine($"     Render Type: {cameraData.renderType}");
                    results.AppendLine($"     Render Post Processing: {cameraData.renderPostProcessing}");
                }

                if (isSceneView)
                {
                    results.AppendLine("⚠️  Scene View cameras are EXCLUDED from motion vector generation in Unity 6!");
                    results.AppendLine("💡 Test motion vectors in GAME VIEW during Play Mode, not Scene View!");
                }
            }

            // Check individual parameter values
            results.AppendLine();
            results.AppendLine("📊 Consolidated Motion & Trailing Parameters:");
            results.AppendLine($"   - Motion Amplification: {fluxEffect.MotionAmplification.value}");
            results.AppendLine($"   - Motion Threshold: {fluxEffect.MotionThreshold.value}");
            results.AppendLine($"   - Camera Object Motion Balance: {fluxEffect.CameraObjectMotionBalance.value}");
            results.AppendLine($"   - Motion Smoothing: {fluxEffect.MotionSmoothing.value}");
            results.AppendLine($"   - Trail Intensity: {fluxEffect.TrailIntensity.value}");
            results.AppendLine($"   - Trail Smoothness: {fluxEffect.TrailSmoothness.value}");
            results.AppendLine($"   - Trail Persistence: {fluxEffect.TrailPersistence.value}");
            results.AppendLine($"   - Flow Spread: {fluxEffect.FlowSpread.value}");

            // Unity 6 specific checks
            results.AppendLine();
            results.AppendLine("📊 Unity 6 Render Graph Checks:");
            results.AppendLine($"   - Application.isPlaying: {Application.isPlaying} {(Application.isPlaying ? "✅" : "❌ Enter Play Mode to test")}");

            if (Application.isPlaying)
            {
                results.AppendLine("✅ In Play Mode - Motion vectors can be generated");
                results.AppendLine("💡 Move camera in GAME VIEW (not Scene View) to test motion vectors");
            }
            else
            {
                results.AppendLine("⚠️  Not in Play Mode - Motion vectors only work during Play Mode");
            }

            results.AppendLine("=== END DIAGNOSIS ===");
            results.AppendLine();
            results.AppendLine("📋 NEXT STEPS:");
            results.AppendLine("1. Ensure you're testing in GAME VIEW during Play Mode");
            results.AppendLine("2. If DoReprojection = FALSE, click 'Force Enable Reprojection'");
            results.AppendLine("3. Enable 'Motion Vector Visualization' to see motion vectors");
            results.AppendLine("4. Move camera with WASD + Mouse in Game View");

            // Update the window display
            diagnosticResults = results.ToString();

            // Also output to console
            Debug.Log(diagnosticResults);

            // Force repaint the window
            Repaint();
        }

        private void EnableMotionVectorVisualization()
        {
            var volumes = FindObjectsByType<Volume>(FindObjectsSortMode.None);

            foreach (var volume in volumes)
            {
                if (volume.profile != null && volume.profile.TryGet<FluxEffect>(out var fluxEffect))
                {
                    fluxEffect.VisualizeMotionVectors.value = true;
                    fluxEffect.VisualizeMotionVectors.overrideState = true;
                    Debug.Log("✅ Motion Vector Visualization ENABLED");
                    EditorUtility.SetDirty(volume.profile);
                    return;
                }
            }

            Debug.LogError("❌ No FluxEffect found to enable motion vector visualization");
        }

        private void DisableMotionVectorVisualization()
        {
            var volumes = FindObjectsByType<Volume>(FindObjectsSortMode.None);

            foreach (var volume in volumes)
            {
                if (volume.profile != null && volume.profile.TryGet<FluxEffect>(out var fluxEffect))
                {
                    fluxEffect.VisualizeMotionVectors.value = false;
                    Debug.Log("✅ Motion Vector Visualization DISABLED");
                    EditorUtility.SetDirty(volume.profile);
                    return;
                }
            }
        }

        private void ForceEnableReprojection()
        {
            var volumes = FindObjectsByType<Volume>(FindObjectsSortMode.None);

            foreach (var volume in volumes)
            {
                if (volume.profile != null && volume.profile.TryGet<FluxEffect>(out var fluxEffect))
                {
                    // Force enable reprojection by setting minimum required values
                    if (fluxEffect.ReprojectBaseNoise.value <= 0f &&
                        fluxEffect.ReprojectLengthInfluence.value <= 0f &&
                        fluxEffect.MotionAmplification.value <= 0f)
                    {
                        fluxEffect.ReprojectBaseNoise.value = 0.01f;
                        fluxEffect.ReprojectBaseNoise.overrideState = true;
                        Debug.Log("✅ Forced reprojection ON by setting Base Noise = 0.01");
                    }
                    else
                    {
                        Debug.Log("✅ Reprojection already enabled");
                    }

                    EditorUtility.SetDirty(volume.profile);
                    return;
                }
            }

            Debug.LogError("❌ No FluxEffect found to force enable reprojection");
        }
    }
}
#endif
