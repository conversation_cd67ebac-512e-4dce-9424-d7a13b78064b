<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<link rel="icon" href="../images/favicon.svg" />
		<link rel="icon" href="../images/favicon.ico" />
		<meta name="viewport" content="width=device-width, height=device-height, initial-scale=1">
		<meta property="og:type" content="website">
		<meta http-equiv="content-security-policy" content="">
		<link href="../_app/immutable/assets/_layout.21aea584.css" rel="stylesheet">
		<link href="../_app/immutable/assets/_layout.0154a58a.css" rel="stylesheet">
		
		
		
		
		
		
		
		
		
		
		
		
		<title>Adapters</title><!-- HEAD_svelte-i5aht6_START --><meta property="og:title" content="Adapters"><meta name="description" property="og:description" content="Flexalon lets Unity developers arrange objects in 3D layouts that are precise and easy to adjust. Use built-in interactions and animations to create powerful experiences."><meta name="image" property="og:image" content="../images/flexalon-1200x630.png"><meta name="twitter:card" content="summary_large_image"><meta name="twitter:title" content="Adapters"><meta name="twitter:site" content="@alon_farchy"><meta name="twitter:description" content="Flexalon lets Unity developers arrange objects in 3D layouts that are precise and easy to adjust. Use built-in interactions and animations to create powerful experiences."><meta name="twitter:image" content="../images/flexalon-1200x630.png"><!-- HEAD_svelte-i5aht6_END -->
	</head>
	<body>
		<div>


<div class="text-white font-body p-4 sticky top-0 bg-black flex gap-5 w-full text-gray-200 z-20 border-b-2 border-[#21a6f0]"><button class="lg:hidden"><svg width="24" height="24"><path d="M5 6h14M5 12h14M5 18h14" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"></path></svg></button>
    <a href="https://www.flexalon.com/" class="font-logo upper text-lg text-[#21a6f0] lg:pl-5 svelte-194nj6y">Flexalon Layouts</a></div>

<div class="w-full max-w-[1600px] lg:pl-10"><div class="flex w-full min-h-full text-zinc-400 relative"><div class="h-full fixed bg-black z-10 max-lg:hidden"><div class="flex flex-col w-[300px] gap-2 border-[#21a6f0] border-r-2 bg-black min-h-0 h-full pt-10 pb-36 overflow-auto max-lg:pl-10"><b class="text-zinc-200">Getting Started</b>
                <a href="../docs.html" class="svelte-194nj6y">Installation</a>
                <a href="../docs/coreConcepts.html" class="svelte-194nj6y">Core Concepts</a>
                <a href="../docs/ui.html" class="svelte-194nj6y">Flexalon UI <span class="ml-2 text-xs font-bold text-pink-300">(New in v4.0!)</span></a>
                <a href="../docs/templates.html" class="svelte-194nj6y">Template Pack</a>
                <a href="../docs/challenges.html" class="svelte-194nj6y">Challenges!</a>

                <a class="text-zinc-200 mt-5 font-bold svelte-194nj6y" href="../docs/flexalonObject.html">Flexalon Objects</a>
                <a href="../docs/flexalonObject.html#size" class="svelte-194nj6y">Width, Height, and Depth</a>
                <a href="../docs/flexalonObject.html#minmax" class="svelte-194nj6y">Min / Max Size<span class="ml-2 text-xs font-bold text-purple-400">(v4.1)</span></a>
                <a href="../docs/flexalonObject.html#margins" class="svelte-194nj6y">Margins and Padding</a>
                <a href="../docs/flexalonObject.html#offset" class="svelte-194nj6y">Offset, Scale, and Rotation</a>

                <b class="text-zinc-200 mt-5">Layouts</b>
                <a href="../docs/flexibleLayout.html" class="svelte-194nj6y">Flexible Layout</a>
                <a href="../docs/gridLayout.html" class="svelte-194nj6y">Grid Layout</a>
                <a href="../docs/circleLayout.html" class="svelte-194nj6y">Circle / Spiral Layout</a>
                <a href="../docs/curveLayout.html" class="svelte-194nj6y">Curve Layout</a>
                <a href="../docs/randomLayout.html" class="svelte-194nj6y">Random Layout</a>
                <a href="../docs/alignLayout.html" class="svelte-194nj6y">Align Layout</a>
                <a href="../docs/shapeLayout.html" class="svelte-194nj6y">Shape Layout</a>
                <a href="../docs/constraints.html" class="svelte-194nj6y">Constraints</a>
                <a href="../docs/randomModifier.html" class="svelte-194nj6y">Random Modifier</a>

                <a class="text-zinc-200 mt-5 font-bold svelte-194nj6y" href="../docs/animators.html">Animators</a>
                <a href="../docs/animators.html#curve" class="svelte-194nj6y">Curve Animator</a>
                <a href="../docs/animators.html#lerp" class="svelte-194nj6y">Lerp Animator</a>
                <a href="../docs/animators.html#rb" class="svelte-194nj6y">Rigid Body Animator</a>
                <a href="../docs/animators.html#layouts" class="svelte-194nj6y">Animating Layouts</a>

                <b class="text-zinc-200 mt-5">Click &amp; Drag</b>
                <a href="../docs/interactable.html" class="svelte-194nj6y">Interactable</a>
                <a href="../docs/interactable.html#dragTarget" class="svelte-194nj6y">Drag Target</a>
                <a href="../docs/xr.html" class="svelte-194nj6y">XR Interactions <span class="ml-2 text-xs font-bold text-blue-300">(v3.2)</span></a>
                <a href="../docs/interactable.html#customInputProvider" class="svelte-194nj6y">Custom Input Provider</a>

                <b class="text-zinc-200 mt-5">Data Binding</b>
                <a href="../docs/cloner.html" class="svelte-194nj6y">Cloner</a>
                <a href="../docs/cloner.html#databinding" class="svelte-194nj6y">Data Binding</a>

                <b class="text-zinc-200 mt-5">Advanced</b>
                <a href="../docs/pipeline.html" class="svelte-194nj6y">Flexalon Pipeline</a>
                <a href="../docs/customLayout.html" class="svelte-194nj6y">Custom Layouts</a>
                <a href="../docs/customAnimators.html" class="svelte-194nj6y">Custom Animators</a>
                <a href="../docs/adapters.html" class="svelte-194nj6y">Adapters</a>

                <a class="text-zinc-200 mt-5 font-bold svelte-194nj6y" href="../docs/api/Flexalon.html">Scripting API</a>
                <div class="mb-20"></div></div></div>
        
        <div class="flex flex-col w-full p-10 lg:ml-[300px]"><div class="doc svelte-194nj6y">

<div class="text-white text-2xl mb-10">Adapters</div>

<p>An Adapter determines two things:
</p>
<p class="ml-10">1. How external Unity components like MeshRenderer, TMP_Text, and Colliders affect gameObjects using SizeType.ComponentSize.
</p>
<p class="ml-10">2. How layout results will modify those a gameObject&#39;s components and localScale.
</p>

<p>Flexalon has built-in adapters for:</p>

<table><tr><td>TextMeshPro.TMP_Text</td>
        <td>The component size is the size of the text. The RectTransform
            is resized to fit the text. The gameObject scale is set to the Scale value of the FlexalonObject.
        </td></tr>
    <tr><td>RectTransform</td>
        <td>The component size is the size of the rectTransform.
            The gameObject scale is set to the Scale value of the FlexalonObject.
        </td></tr>
    <tr><td>MeshRenderer</td>
        <td>The component size is the size of the renderer local bounds.
            The gameObject is scaled uniformly where possible.
        </td></tr>
    <tr><td>SpriteRenderer</td>
        <td>The component size is the size of the sprite.
            The gameObject is scaled uniformly, depth scale is 1.
        </td></tr>
    <tr><td>Collider</td>
        <td>The component size is the size of the collider local bounds, which depends on the type of collider.
            The gameObject is scaled uniformly where possible.
        </td></tr>
    <tr><td>Collider2D</td>
        <td>The component size is the size of the collider local bounds, which depends on the type of collider.
            The gameObject is scaled uniformly where possible, depth scale is 1.
        </td></tr>
    <tr><td>Canvas (UGUI)</td>
        <td>Non-root canvases or canvases with renderMode set to World Space are adapted as RectTransforms.
            Root canvas sizes are never modified, since they are controlled by Unity.
        </td></tr>
    <tr><td>Image (UGUI)</td>
        <td>If only one axis is set to Component size, it is set to the aspect ratio of the sprite.
            The gameObject scale is set to the Scale value of the FlexalonObject.
        </td></tr></table>

<p class="text-xl mt-10">Implementing a custom adapter</p>
<p>To provide your own sizing strategy, implement Adapter and call:</p>
<p style="font-family:'Courier New', Courier, monospace">Flexalon.GetOrCreateNode(gameObject).SetAdapter(yourObject);
</p>

<p class="text-xl mt-10">Measure</p>
<p style="font-family:'Courier New', Courier, monospace">Bounds <b>Measure</b>(FlexalonNode node, Vector3 size, Vector3 min, Vector3 max);
</p>
<table><tr><td>node</td>
        <td>The node to be measured.</td></tr>
    <tr><td>size</td>
        <td>The size set by the Flexalon Object Component. Update any axis set to SizeType.Component.</td></tr>
    <tr><td>min</td>
        <td>The minimum set by the Flexalon Object Component. Ensure the returned bounds fit in min/max.</td></tr>
    <tr><td>max</td>
        <td>The maximum set by the Flexalon Object Component. Ensure the returned bounds fit in min/max.</td></tr>
    <tr><td>Return Value</td>
        <td>The measured size to use in layout.</td></tr></table>

<p class="mt-10">Call node.GetSizeType() to determine which axes should be adjusted.
    Return a bounds that includes the passed in size and the auto size.
</p>

<p class="text-xl mt-10">UpdateSize</p>
<p style="font-family:'Courier New', Courier, monospace">Vector3 <b>UpdateSize</b>(FlexalonNode node);
</p>
<table><tr><td>node</td>
        <td>The node to be sized.</td></tr>
    <tr><td>size</td>
        <td>The final layout size.</td></tr>
    <tr><td>Return Value</td>
        <td>The desired scale of this gameObject.</td></tr></table>

<p class="mt-10">Update components on this node&#39;s gameObject to match the passed in size.
    Do not adjust the Transform component.
    Return what the gameObject&#39;s scale should be in local space.
</p></div></div></div>
</div>


			
			
		</div>
	</body>
</html>
