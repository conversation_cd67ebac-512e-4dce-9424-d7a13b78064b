%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 9
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.212, g: 0.227, b: 0.259, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 0
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 2100000, guid: ********************************, type: 2}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_IndirectSpecularColor: {r: 0.99999946, g: 0.5420191, b: 0, a: 1}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &3
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 11
  m_GIWorkflowMode: 0
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 1
    m_EnableRealtimeLightmaps: 1
  m_LightmapEditorSettings:
    serializedVersion: 12
    m_Resolution: 2
    m_BakeResolution: 40
    m_AtlasSize: 1024
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_ExtractAmbientOcclusion: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_FinalGather: 0
    m_FinalGatherFiltering: 1
    m_FinalGatherRayCount: 256
    m_ReflectionCompression: 2
    m_MixedBakeMode: 2
    m_BakeBackend: 1
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 500
    m_PVRBounces: 2
    m_PVREnvironmentSampleCount: 500
    m_PVREnvironmentReferencePointCount: 2048
    m_PVRFilteringMode: 2
    m_PVRDenoiserTypeDirect: 0
    m_PVRDenoiserTypeIndirect: 0
    m_PVRDenoiserTypeAO: 0
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVREnvironmentMIS: 0
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ExportTrainingData: 0
    m_TrainingDataDestination: TrainingData
    m_LightProbeSampleCountMultiplier: 4
  m_LightingDataAsset: {fileID: 0}
  m_UseShadowmask: 1
--- !u!196 &4
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 2
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    accuratePlacement: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!1 &20807434
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 20807435}
  m_Layer: 0
  m_Name: Mid
  m_TagString: Untagged
  m_Icon: {fileID: 5132851093641282708, guid: 0000000000000000d000000000000000, type: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &20807435
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 20807434}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 6.46, y: 3.9199998, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 413556091}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &33538731
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 33538732}
  m_Layer: 0
  m_Name: Control3
  m_TagString: Untagged
  m_Icon: {fileID: 2974397684917235467, guid: 0000000000000000d000000000000000, type: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &33538732
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 33538731}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 6.13, y: 4.98, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 413556091}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &56892210
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 56892211}
  m_Layer: 0
  m_Name: Mid
  m_TagString: Untagged
  m_Icon: {fileID: 5132851093641282708, guid: 0000000000000000d000000000000000, type: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &56892211
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 56892210}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 14.25, y: 3.34, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 2137754276}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &124904463
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 124904466}
  - component: {fileID: 124904465}
  - component: {fileID: 124904464}
  m_Layer: 0
  m_Name: Main Camera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!81 &124904464
AudioListener:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 124904463}
  m_Enabled: 1
--- !u!20 &124904465
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 124904463}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 1
  m_BackGroundColor: {r: 0.31132078, g: 0.31132078, b: 0.31132078, a: 0}
  m_projectionMatrixMode: 1
  m_GateFitMode: 2
  m_FOVAxisMode: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_FocalLength: 50
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.3
  far clip plane: 1000
  field of view: 60
  orthographic: 0
  orthographic size: 5
  m_Depth: -1
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 1
  m_AllowMSAA: 1
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 0
  m_OcclusionCulling: 1
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!4 &124904466
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 124904463}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 1, z: -10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &239464957
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 239464958}
  - component: {fileID: 239464960}
  m_Layer: 0
  m_Name: End
  m_TagString: Untagged
  m_Icon: {fileID: 5132851093641282708, guid: 0000000000000000d000000000000000, type: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &239464958
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 239464957}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 17.08, y: 1.54, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1675030887}
  m_Father: {fileID: 2137754276}
  m_RootOrder: 6
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &239464960
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 239464957}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 347013242dca7c44fb5001793033ea59, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  UpdateMode: 0
  PositionActive: 1
  RotationActive: 0
  ScaleActive: 0
  PositionWiggleProperties:
    WigglePermitted: 1
    WiggleType: 3
    UseUnscaledTime: 0
    StartWigglingAutomatically: 1
    SmoothPingPong: 1
    UseSpeedCurve: 0
    SpeedCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    FrequencyMin: 1
    FrequencyMax: 2
    AmplitudeMin: {x: -1, y: -1, z: -1}
    AmplitudeMax: {x: 1, y: 1, z: 1}
    RelativeAmplitude: 1
    Curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    RemapCurveZeroMin: {x: 0, y: 0, z: 0}
    RemapCurveZeroMax: {x: 0, y: 0, z: 0}
    RemapCurveOneMin: {x: 1, y: 1, z: 1}
    RemapCurveOneMax: {x: 1, y: 1, z: 1}
    RelativeCurveAmplitude: 1
    CurvePingPong: 0
    PauseMin: 0
    PauseMax: 0
    LimitedTime: 0
    LimitedTimeTotal: 0
    LimitedTimeFalloff:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    LimitedTimeResetValue: 1
    LimitedTimeLeft: 0
    NoiseFrequencyMin: {x: 0, y: 0, z: 0}
    NoiseFrequencyMax: {x: 1, y: 1, z: 1}
    NoiseShiftMin: {x: 0, y: 0, z: 0}
    NoiseShiftMax: {x: 0, y: 0, z: 0}
  RotationWiggleProperties:
    WigglePermitted: 1
    WiggleType: 1
    UseUnscaledTime: 0
    StartWigglingAutomatically: 1
    SmoothPingPong: 1
    UseSpeedCurve: 0
    SpeedCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    FrequencyMin: 0
    FrequencyMax: 1
    AmplitudeMin: {x: 0, y: 0, z: 0}
    AmplitudeMax: {x: 1, y: 1, z: 1}
    RelativeAmplitude: 1
    Curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    RemapCurveZeroMin: {x: 0, y: 0, z: 0}
    RemapCurveZeroMax: {x: 0, y: 0, z: 0}
    RemapCurveOneMin: {x: 1, y: 1, z: 1}
    RemapCurveOneMax: {x: 1, y: 1, z: 1}
    RelativeCurveAmplitude: 1
    CurvePingPong: 0
    PauseMin: 0
    PauseMax: 0
    LimitedTime: 0
    LimitedTimeTotal: 0
    LimitedTimeFalloff:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    LimitedTimeResetValue: 1
    LimitedTimeLeft: 0
    NoiseFrequencyMin: {x: 0, y: 0, z: 0}
    NoiseFrequencyMax: {x: 1, y: 1, z: 1}
    NoiseShiftMin: {x: 0, y: 0, z: 0}
    NoiseShiftMax: {x: 0, y: 0, z: 0}
  ScaleWiggleProperties:
    WigglePermitted: 1
    WiggleType: 1
    UseUnscaledTime: 0
    StartWigglingAutomatically: 1
    SmoothPingPong: 1
    UseSpeedCurve: 0
    SpeedCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    FrequencyMin: 0
    FrequencyMax: 1
    AmplitudeMin: {x: 0, y: 0, z: 0}
    AmplitudeMax: {x: 1, y: 1, z: 1}
    RelativeAmplitude: 1
    Curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    RemapCurveZeroMin: {x: 0, y: 0, z: 0}
    RemapCurveZeroMax: {x: 0, y: 0, z: 0}
    RemapCurveOneMin: {x: 1, y: 1, z: 1}
    RemapCurveOneMax: {x: 1, y: 1, z: 1}
    RelativeCurveAmplitude: 1
    CurvePingPong: 0
    PauseMin: 0
    PauseMax: 0
    LimitedTime: 0
    LimitedTimeTotal: 0
    LimitedTimeFalloff:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    LimitedTimeResetValue: 1
    LimitedTimeLeft: 0
    NoiseFrequencyMin: {x: 0, y: 0, z: 0}
    NoiseFrequencyMax: {x: 1, y: 1, z: 1}
    NoiseShiftMin: {x: 0, y: 0, z: 0}
    NoiseShiftMax: {x: 0, y: 0, z: 0}
  DebugWiggleDuration: 2
--- !u!1 &320325106
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 320325108}
  m_Layer: 0
  m_Name: Mid
  m_TagString: Untagged
  m_Icon: {fileID: 5132851093641282708, guid: 0000000000000000d000000000000000, type: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &320325108
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 320325106}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 6.46, y: 3.9199998, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 2062585004}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &367080838
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 367080839}
  m_Layer: 0
  m_Name: Control4
  m_TagString: Untagged
  m_Icon: {fileID: 2974397684917235467, guid: 0000000000000000d000000000000000, type: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &367080839
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 367080838}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 5.39, y: 5.29, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 413556091}
  m_RootOrder: 5
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &413556088
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 413556091}
  - component: {fileID: 413556090}
  - component: {fileID: 413556089}
  m_Layer: 0
  m_Name: MMBezierLineRenderer - RightArm - NoWiggle
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!114 &413556089
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 413556088}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 177926c7a52cdb247b4ea692ea51aa68, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  AdjustmentHandles:
  - {fileID: 843146713}
  - {fileID: 1720615899}
  - {fileID: 1888501706}
  - {fileID: 20807435}
  - {fileID: 33538732}
  - {fileID: 367080839}
  - {fileID: 1080195249}
  NumberOfSegments: 50
  SortingLayerName: Default
  NumberOfCurves: 2
--- !u!120 &413556090
LineRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 413556088}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 0
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 0
  m_RayTracingMode: 0
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Positions:
  - {x: -0.8699999, y: -1.1199999, z: 0}
  - {x: -1.0397282, y: -1.1862053, z: 0}
  - {x: -1.2013031, y: -1.2465348, z: 0}
  - {x: -1.354943, y: -1.3011107, z: 0}
  - {x: -1.5008662, y: -1.3500546, z: 0}
  - {x: -1.6392912, y: -1.3934888, z: 0}
  - {x: -1.7704359, y: -1.4315348, z: 0}
  - {x: -1.8945189, y: -1.4643148, z: 0}
  - {x: -2.0117583, y: -1.4919504, z: 0}
  - {x: -2.1223722, y: -1.5145633, z: 0}
  - {x: -2.2265794, y: -1.5322762, z: 0}
  - {x: -2.3245971, y: -1.5452102, z: 0}
  - {x: -2.4166455, y: -1.5534878, z: 0}
  - {x: -2.502941, y: -1.5572304, z: 0}
  - {x: -2.5837026, y: -1.5565597, z: 0}
  - {x: -2.6591487, y: -1.5515983, z: 0}
  - {x: -2.7294974, y: -1.5424676, z: 0}
  - {x: -2.7949667, y: -1.5292894, z: 0}
  - {x: -2.8557756, y: -1.512186, z: 0}
  - {x: -2.9121418, y: -1.491279, z: 0}
  - {x: -2.9642837, y: -1.4666902, z: 0}
  - {x: -3.0124195, y: -1.438542, z: 0}
  - {x: -3.0567677, y: -1.406956, z: 0}
  - {x: -3.0975468, y: -1.3720539, z: 0}
  - {x: -3.1349747, y: -1.3339578, z: 0}
  - {x: -3.1692693, y: -1.2927893, z: 0}
  - {x: -3.2006493, y: -1.2486706, z: 0}
  - {x: -3.2293336, y: -1.2017236, z: 0}
  - {x: -3.2555392, y: -1.1520698, z: 0}
  - {x: -3.2794852, y: -1.0998315, z: 0}
  - {x: -3.3013902, y: -1.0451306, z: 0}
  - {x: -3.3214712, y: -0.9880887, z: 0}
  - {x: -3.3399477, y: -0.9288279, z: 0}
  - {x: -3.357037, y: -0.86747, z: 0}
  - {x: -3.3729587, y: -0.8041367, z: 0}
  - {x: -3.38793, y: -0.73895025, z: 0}
  - {x: -3.4021697, y: -0.67203254, z: 0}
  - {x: -3.4158952, y: -0.60350513, z: 0}
  - {x: -3.4293256, y: -0.5334901, z: 0}
  - {x: -3.4426794, y: -0.4621094, z: 0}
  - {x: -3.456174, y: -0.38948452, z: 0}
  - {x: -3.4700284, y: -0.31573787, z: 0}
  - {x: -3.4844606, y: -0.24099119, z: 0}
  - {x: -3.4996886, y: -0.16536628, z: 0}
  - {x: -3.5159314, y: -0.088985056, z: 0}
  - {x: -3.5334067, y: -0.011969402, z: 0}
  - {x: -3.5523329, y: 0.065559015, z: 0}
  - {x: -3.5729282, y: 0.14347786, z: 0}
  - {x: -3.5954115, y: 0.22166547, z: 0}
  - {x: -3.62, y: 0.29999995, z: 0}
  - {x: -3.62, y: 0.29999995, z: 0}
  - {x: -3.640715, y: 0.3639565, z: 0}
  - {x: -3.6624444, y: 0.42601278, z: 0}
  - {x: -3.6851795, y: 0.48614284, z: 0}
  - {x: -3.7089093, y: 0.5443206, z: 0}
  - {x: -3.7336257, y: 0.6005202, z: 0}
  - {x: -3.7593174, y: 0.6547154, z: 0}
  - {x: -3.7859766, y: 0.70688045, z: 0}
  - {x: -3.813592, y: 0.75698924, z: 0}
  - {x: -3.842154, y: 0.80501556, z: 0}
  - {x: -3.8716543, y: 0.85093373, z: 0}
  - {x: -3.9020817, y: 0.89471745, z: 0}
  - {x: -3.9334278, y: 0.93634105, z: 0}
  - {x: -3.9656816, y: 0.97577816, z: 0}
  - {x: -3.998834, y: 1.013003, z: 0}
  - {x: -4.032875, y: 1.0479895, z: 0}
  - {x: -4.0677958, y: 1.0807116, z: 0}
  - {x: -4.1035857, y: 1.1111432, z: 0}
  - {x: -4.140236, y: 1.1392586, z: 0}
  - {x: -4.177736, y: 1.1650316, z: 0}
  - {x: -4.2160764, y: 1.188436, z: 0}
  - {x: -4.255247, y: 1.209446, z: 0}
  - {x: -4.2952394, y: 1.2280358, z: 0}
  - {x: -4.336044, y: 1.2441791, z: 0}
  - {x: -4.37765, y: 1.25785, z: 0}
  - {x: -4.4200473, y: 1.2690225, z: 0}
  - {x: -4.463227, y: 1.2776703, z: 0}
  - {x: -4.5071797, y: 1.2837678, z: 0}
  - {x: -4.551895, y: 1.2872888, z: 0}
  - {x: -4.597364, y: 1.2882072, z: 0}
  - {x: -4.643576, y: 1.2864972, z: 0}
  - {x: -4.690522, y: 1.2821327, z: 0}
  - {x: -4.7381926, y: 1.2750876, z: 0}
  - {x: -4.786577, y: 1.2653363, z: 0}
  - {x: -4.835667, y: 1.2528522, z: 0}
  - {x: -4.885452, y: 1.2376094, z: 0}
  - {x: -4.9359226, y: 1.2195823, z: 0}
  - {x: -4.987068, y: 1.1987445, z: 0}
  - {x: -5.0388803, y: 1.1750703, z: 0}
  - {x: -5.0913486, y: 1.1485335, z: 0}
  - {x: -5.1444635, y: 1.1191078, z: 0}
  - {x: -5.1982155, y: 1.0867678, z: 0}
  - {x: -5.252595, y: 1.051487, z: 0}
  - {x: -5.3075914, y: 1.0132396, z: 0}
  - {x: -5.3631964, y: 0.9719998, z: 0}
  - {x: -5.4193993, y: 0.9277412, z: 0}
  - {x: -5.476191, y: 0.88043785, z: 0}
  - {x: -5.5335617, y: 0.830064, z: 0}
  - {x: -5.5915017, y: 0.7765935, z: 0}
  - {x: -5.65, y: 0.72000027, z: 0}
  m_Parameters:
    serializedVersion: 3
    widthMultiplier: 0.5
    widthCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    colorGradient:
      serializedVersion: 2
      key0: {r: 0.8207547, g: 0.8207547, b: 0.8207547, a: 1}
      key1: {r: 1, g: 1, b: 1, a: 1}
      key2: {r: 1, g: 1, b: 1, a: 0}
      key3: {r: 0, g: 0, b: 0, a: 0}
      key4: {r: 0, g: 0, b: 0, a: 0}
      key5: {r: 0, g: 0, b: 0, a: 0}
      key6: {r: 0, g: 0, b: 0, a: 0}
      key7: {r: 0, g: 0, b: 0, a: 0}
      ctime0: 0
      ctime1: 6554
      ctime2: 65535
      ctime3: 0
      ctime4: 0
      ctime5: 0
      ctime6: 0
      ctime7: 0
      atime0: 0
      atime1: 65535
      atime2: 0
      atime3: 0
      atime4: 0
      atime5: 0
      atime6: 0
      atime7: 0
      m_Mode: 0
      m_NumColorKeys: 3
      m_NumAlphaKeys: 2
    numCornerVertices: 0
    numCapVertices: 0
    alignment: 0
    textureMode: 0
    shadowBias: 0
    generateLightingData: 0
  m_UseWorldSpace: 1
  m_Loop: 0
--- !u!4 &413556091
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 413556088}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -10.08, y: -3.62, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 843146713}
  - {fileID: 1720615899}
  - {fileID: 1888501706}
  - {fileID: 20807435}
  - {fileID: 33538732}
  - {fileID: 367080839}
  - {fileID: 1080195249}
  m_Father: {fileID: 0}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &454120794
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 454120795}
  - component: {fileID: 454120797}
  m_Layer: 0
  m_Name: Control3
  m_TagString: Untagged
  m_Icon: {fileID: 2974397684917235467, guid: 0000000000000000d000000000000000, type: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &454120795
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 454120794}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 16.32, y: 3.65, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 2137754276}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &454120797
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 454120794}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 347013242dca7c44fb5001793033ea59, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  UpdateMode: 0
  PositionActive: 1
  RotationActive: 0
  ScaleActive: 0
  PositionWiggleProperties:
    WigglePermitted: 1
    WiggleType: 3
    UseUnscaledTime: 0
    StartWigglingAutomatically: 1
    SmoothPingPong: 1
    UseSpeedCurve: 0
    SpeedCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    FrequencyMin: 1
    FrequencyMax: 2
    AmplitudeMin: {x: -1, y: -1, z: -1}
    AmplitudeMax: {x: 1, y: 1, z: 1}
    RelativeAmplitude: 1
    Curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    RemapCurveZeroMin: {x: 0, y: 0, z: 0}
    RemapCurveZeroMax: {x: 0, y: 0, z: 0}
    RemapCurveOneMin: {x: 1, y: 1, z: 1}
    RemapCurveOneMax: {x: 1, y: 1, z: 1}
    RelativeCurveAmplitude: 1
    CurvePingPong: 0
    PauseMin: 0
    PauseMax: 0
    LimitedTime: 0
    LimitedTimeTotal: 0
    LimitedTimeFalloff:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    LimitedTimeResetValue: 1
    LimitedTimeLeft: 0
    NoiseFrequencyMin: {x: 0, y: 0, z: 0}
    NoiseFrequencyMax: {x: 1, y: 1, z: 1}
    NoiseShiftMin: {x: 0, y: 0, z: 0}
    NoiseShiftMax: {x: 0, y: 0, z: 0}
  RotationWiggleProperties:
    WigglePermitted: 1
    WiggleType: 1
    UseUnscaledTime: 0
    StartWigglingAutomatically: 1
    SmoothPingPong: 1
    UseSpeedCurve: 0
    SpeedCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    FrequencyMin: 0
    FrequencyMax: 1
    AmplitudeMin: {x: 0, y: 0, z: 0}
    AmplitudeMax: {x: 1, y: 1, z: 1}
    RelativeAmplitude: 1
    Curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    RemapCurveZeroMin: {x: 0, y: 0, z: 0}
    RemapCurveZeroMax: {x: 0, y: 0, z: 0}
    RemapCurveOneMin: {x: 1, y: 1, z: 1}
    RemapCurveOneMax: {x: 1, y: 1, z: 1}
    RelativeCurveAmplitude: 1
    CurvePingPong: 0
    PauseMin: 0
    PauseMax: 0
    LimitedTime: 0
    LimitedTimeTotal: 0
    LimitedTimeFalloff:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    LimitedTimeResetValue: 1
    LimitedTimeLeft: 0
    NoiseFrequencyMin: {x: 0, y: 0, z: 0}
    NoiseFrequencyMax: {x: 1, y: 1, z: 1}
    NoiseShiftMin: {x: 0, y: 0, z: 0}
    NoiseShiftMax: {x: 0, y: 0, z: 0}
  ScaleWiggleProperties:
    WigglePermitted: 1
    WiggleType: 1
    UseUnscaledTime: 0
    StartWigglingAutomatically: 1
    SmoothPingPong: 1
    UseSpeedCurve: 0
    SpeedCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    FrequencyMin: 0
    FrequencyMax: 1
    AmplitudeMin: {x: 0, y: 0, z: 0}
    AmplitudeMax: {x: 1, y: 1, z: 1}
    RelativeAmplitude: 1
    Curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    RemapCurveZeroMin: {x: 0, y: 0, z: 0}
    RemapCurveZeroMax: {x: 0, y: 0, z: 0}
    RemapCurveOneMin: {x: 1, y: 1, z: 1}
    RemapCurveOneMax: {x: 1, y: 1, z: 1}
    RelativeCurveAmplitude: 1
    CurvePingPong: 0
    PauseMin: 0
    PauseMax: 0
    LimitedTime: 0
    LimitedTimeTotal: 0
    LimitedTimeFalloff:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    LimitedTimeResetValue: 1
    LimitedTimeLeft: 0
    NoiseFrequencyMin: {x: 0, y: 0, z: 0}
    NoiseFrequencyMax: {x: 1, y: 1, z: 1}
    NoiseShiftMin: {x: 0, y: 0, z: 0}
    NoiseShiftMax: {x: 0, y: 0, z: 0}
  DebugWiggleDuration: 2
--- !u!1 &523521494
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 523521495}
  m_Layer: 0
  m_Name: Control3
  m_TagString: Untagged
  m_Icon: {fileID: 2974397684917235467, guid: 0000000000000000d000000000000000, type: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &523521495
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 523521494}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 16.32, y: 3.65, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 665220345}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &590311554
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 590311556}
  - component: {fileID: 590311557}
  m_Layer: 0
  m_Name: Control4
  m_TagString: Untagged
  m_Icon: {fileID: 2974397684917235467, guid: 0000000000000000d000000000000000, type: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &590311556
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 590311554}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 5.39, y: 5.29, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 2062585004}
  m_RootOrder: 5
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &590311557
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 590311554}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 347013242dca7c44fb5001793033ea59, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  UpdateMode: 0
  PositionActive: 1
  RotationActive: 0
  ScaleActive: 0
  PositionWiggleProperties:
    WigglePermitted: 1
    WiggleType: 3
    UseUnscaledTime: 0
    StartWigglingAutomatically: 1
    SmoothPingPong: 1
    UseSpeedCurve: 0
    SpeedCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    FrequencyMin: 1
    FrequencyMax: 2
    AmplitudeMin: {x: -1, y: -1, z: -1}
    AmplitudeMax: {x: 1, y: 1, z: 1}
    RelativeAmplitude: 1
    Curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    RemapCurveZeroMin: {x: 0, y: 0, z: 0}
    RemapCurveZeroMax: {x: 0, y: 0, z: 0}
    RemapCurveOneMin: {x: 1, y: 1, z: 1}
    RemapCurveOneMax: {x: 1, y: 1, z: 1}
    RelativeCurveAmplitude: 1
    CurvePingPong: 0
    PauseMin: 0
    PauseMax: 0
    LimitedTime: 0
    LimitedTimeTotal: 0
    LimitedTimeFalloff:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    LimitedTimeResetValue: 1
    LimitedTimeLeft: 0
    NoiseFrequencyMin: {x: 0, y: 0, z: 0}
    NoiseFrequencyMax: {x: 1, y: 1, z: 1}
    NoiseShiftMin: {x: 0, y: 0, z: 0}
    NoiseShiftMax: {x: 0, y: 0, z: 0}
  RotationWiggleProperties:
    WigglePermitted: 1
    WiggleType: 1
    UseUnscaledTime: 0
    StartWigglingAutomatically: 1
    SmoothPingPong: 1
    UseSpeedCurve: 0
    SpeedCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    FrequencyMin: 0
    FrequencyMax: 1
    AmplitudeMin: {x: 0, y: 0, z: 0}
    AmplitudeMax: {x: 1, y: 1, z: 1}
    RelativeAmplitude: 1
    Curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    RemapCurveZeroMin: {x: 0, y: 0, z: 0}
    RemapCurveZeroMax: {x: 0, y: 0, z: 0}
    RemapCurveOneMin: {x: 1, y: 1, z: 1}
    RemapCurveOneMax: {x: 1, y: 1, z: 1}
    RelativeCurveAmplitude: 1
    CurvePingPong: 0
    PauseMin: 0
    PauseMax: 0
    LimitedTime: 0
    LimitedTimeTotal: 0
    LimitedTimeFalloff:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    LimitedTimeResetValue: 1
    LimitedTimeLeft: 0
    NoiseFrequencyMin: {x: 0, y: 0, z: 0}
    NoiseFrequencyMax: {x: 1, y: 1, z: 1}
    NoiseShiftMin: {x: 0, y: 0, z: 0}
    NoiseShiftMax: {x: 0, y: 0, z: 0}
  ScaleWiggleProperties:
    WigglePermitted: 1
    WiggleType: 1
    UseUnscaledTime: 0
    StartWigglingAutomatically: 1
    SmoothPingPong: 1
    UseSpeedCurve: 0
    SpeedCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    FrequencyMin: 0
    FrequencyMax: 1
    AmplitudeMin: {x: 0, y: 0, z: 0}
    AmplitudeMax: {x: 1, y: 1, z: 1}
    RelativeAmplitude: 1
    Curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    RemapCurveZeroMin: {x: 0, y: 0, z: 0}
    RemapCurveZeroMax: {x: 0, y: 0, z: 0}
    RemapCurveOneMin: {x: 1, y: 1, z: 1}
    RemapCurveOneMax: {x: 1, y: 1, z: 1}
    RelativeCurveAmplitude: 1
    CurvePingPong: 0
    PauseMin: 0
    PauseMax: 0
    LimitedTime: 0
    LimitedTimeTotal: 0
    LimitedTimeFalloff:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    LimitedTimeResetValue: 1
    LimitedTimeLeft: 0
    NoiseFrequencyMin: {x: 0, y: 0, z: 0}
    NoiseFrequencyMax: {x: 1, y: 1, z: 1}
    NoiseShiftMin: {x: 0, y: 0, z: 0}
    NoiseShiftMax: {x: 0, y: 0, z: 0}
  DebugWiggleDuration: 2
--- !u!1 &622112409
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 622112410}
  - component: {fileID: 622112411}
  m_Layer: 0
  m_Name: MMBezierLineRendererDemoDudeHand
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &622112410
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 622112409}
  m_LocalRotation: {x: -0, y: -0, z: 0.97291106, w: 0.23117997}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: -1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1080195249}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 153.26701}
--- !u!212 &622112411
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 622112409}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Sprite: {fileID: 21300000, guid: 3eb4421e0ac10aa488e44538963f00e5, type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 1.28, y: 1.28}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!1 &629939769
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 629939770}
  m_Layer: 0
  m_Name: Control4
  m_TagString: Untagged
  m_Icon: {fileID: 2974397684917235467, guid: 0000000000000000d000000000000000, type: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &629939770
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 629939769}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 15.37, y: 1.11, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 665220345}
  m_RootOrder: 5
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &665220342
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 665220345}
  - component: {fileID: 665220344}
  - component: {fileID: 665220343}
  m_Layer: 0
  m_Name: MMBezierLineRenderer - LeftArm - NoWiggle
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!114 &665220343
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 665220342}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 177926c7a52cdb247b4ea692ea51aa68, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  AdjustmentHandles:
  - {fileID: 1511050799}
  - {fileID: 1521069615}
  - {fileID: 1515801002}
  - {fileID: 1383356089}
  - {fileID: 523521495}
  - {fileID: 629939770}
  - {fileID: 705777285}
  NumberOfSegments: 50
  SortingLayerName: Default
  NumberOfCurves: 2
--- !u!120 &665220344
LineRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 665220342}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 0
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 0
  m_RayTracingMode: 0
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Positions:
  - {x: 1.0100002, y: -1.0699999, z: 0}
  - {x: 1.1036494, y: -1.16211, z: 0}
  - {x: 1.194853, y: -1.245164, z: 0}
  - {x: 1.2836899, y: -1.3194524, z: 0}
  - {x: 1.3702382, y: -1.3852651, z: 0}
  - {x: 1.454577, y: -1.4428927, z: 0}
  - {x: 1.5367843, y: -1.4926248, z: 0}
  - {x: 1.6169392, y: -1.5347521, z: 0}
  - {x: 1.6951196, y: -1.5695646, z: 0}
  - {x: 1.7714044, y: -1.5973521, z: 0}
  - {x: 1.8458723, y: -1.6184057, z: 0}
  - {x: 1.9186013, y: -1.6330147, z: 0}
  - {x: 1.9896708, y: -1.64147, z: 0}
  - {x: 2.0591583, y: -1.6440613, z: 0}
  - {x: 2.1271434, y: -1.6410786, z: 0}
  - {x: 2.1937037, y: -1.6328125, z: 0}
  - {x: 2.258918, y: -1.6195534, z: 0}
  - {x: 2.322865, y: -1.6015906, z: 0}
  - {x: 2.3856235, y: -1.5792153, z: 0}
  - {x: 2.4472718, y: -1.5527171, z: 0}
  - {x: 2.5078878, y: -1.522386, z: 0}
  - {x: 2.5675511, y: -1.4885128, z: 0}
  - {x: 2.6263397, y: -1.4513874, z: 0}
  - {x: 2.6843326, y: -1.4113001, z: 0}
  - {x: 2.7416077, y: -1.3685408, z: 0}
  - {x: 2.7982433, y: -1.3233998, z: 0}
  - {x: 2.8543186, y: -1.2761673, z: 0}
  - {x: 2.9099123, y: -1.2271338, z: 0}
  - {x: 2.9651024, y: -1.1765887, z: 0}
  - {x: 3.0199678, y: -1.124823, z: 0}
  - {x: 3.0745866, y: -1.0721265, z: 0}
  - {x: 3.1290376, y: -1.0187895, z: 0}
  - {x: 3.1833994, y: -0.9651021, z: 0}
  - {x: 3.2377505, y: -0.9113545, z: 0}
  - {x: 3.2921696, y: -0.8578368, z: 0}
  - {x: 3.346735, y: -0.8048395, z: 0}
  - {x: 3.4015255, y: -0.75265247, z: 0}
  - {x: 3.456619, y: -0.70156604, z: 0}
  - {x: 3.5120945, y: -0.65187037, z: 0}
  - {x: 3.5680308, y: -0.60385567, z: 0}
  - {x: 3.6245062, y: -0.55781186, z: 0}
  - {x: 3.6815991, y: -0.51402956, z: 0}
  - {x: 3.739388, y: -0.4727987, z: 0}
  - {x: 3.7979515, y: -0.43440953, z: 0}
  - {x: 3.8573685, y: -0.39915222, z: 0}
  - {x: 3.9177172, y: -0.36731696, z: 0}
  - {x: 3.9790761, y: -0.33919385, z: 0}
  - {x: 4.041524, y: -0.31507322, z: 0}
  - {x: 4.1051393, y: -0.29524523, z: 0}
  - {x: 4.17, y: -0.27999997, z: 0}
  - {x: 4.17, y: -0.27999997, z: 0}
  - {x: 4.29301, y: -0.26453194, z: 0}
  - {x: 4.4087615, y: -0.2558891, z: 0}
  - {x: 4.517547, y: -0.25377467, z: 0}
  - {x: 4.6196537, y: -0.25789177, z: 0}
  - {x: 4.7153726, y: -0.2679437, z: 0}
  - {x: 4.8049927, y: -0.2836336, z: 0}
  - {x: 4.8888044, y: -0.30466464, z: 0}
  - {x: 4.9670973, y: -0.33073997, z: 0}
  - {x: 5.04016, y: -0.36156285, z: 0}
  - {x: 5.108283, y: -0.3968364, z: 0}
  - {x: 5.171756, y: -0.4362638, z: 0}
  - {x: 5.230869, y: -0.4795484, z: 0}
  - {x: 5.285911, y: -0.5263931, z: 0}
  - {x: 5.337172, y: -0.57650137, z: 0}
  - {x: 5.3849416, y: -0.6295762, z: 0}
  - {x: 5.4295096, y: -0.68532073, z: 0}
  - {x: 5.4711647, y: -0.7434384, z: 0}
  - {x: 5.510198, y: -0.8036322, z: 0}
  - {x: 5.546899, y: -0.86560535, z: 0}
  - {x: 5.5815554, y: -0.92906094, z: 0}
  - {x: 5.61446, y: -0.99370235, z: 0}
  - {x: 5.6459007, y: -1.0592327, z: 0}
  - {x: 5.6761675, y: -1.1253552, z: 0}
  - {x: 5.70555, y: -1.191773, z: 0}
  - {x: 5.734338, y: -1.2581892, z: 0}
  - {x: 5.7628193, y: -1.324307, z: 0}
  - {x: 5.7912874, y: -1.3898296, z: 0}
  - {x: 5.8200293, y: -1.4544606, z: 0}
  - {x: 5.849335, y: -1.5179026, z: 0}
  - {x: 5.879494, y: -1.5798588, z: 0}
  - {x: 5.910797, y: -1.6400326, z: 0}
  - {x: 5.943533, y: -1.698127, z: 0}
  - {x: 5.977991, y: -1.7538457, z: 0}
  - {x: 6.0144625, y: -1.8068917, z: 0}
  - {x: 6.053236, y: -1.8569677, z: 0}
  - {x: 6.0946016, y: -1.9037775, z: 0}
  - {x: 6.138848, y: -1.9470236, z: 0}
  - {x: 6.186265, y: -1.9864097, z: 0}
  - {x: 6.2371435, y: -2.021639, z: 0}
  - {x: 6.2917724, y: -2.0524144, z: 0}
  - {x: 6.3504415, y: -2.0784392, z: 0}
  - {x: 6.4134398, y: -2.0994167, z: 0}
  - {x: 6.481058, y: -2.1150498, z: 0}
  - {x: 6.553586, y: -2.1250424, z: 0}
  - {x: 6.6313124, y: -2.1290967, z: 0}
  - {x: 6.714527, y: -2.1269164, z: 0}
  - {x: 6.80352, y: -2.1182048, z: 0}
  - {x: 6.8985815, y: -2.1026652, z: 0}
  - {x: 7, y: -2.08, z: 0}
  m_Parameters:
    serializedVersion: 3
    widthMultiplier: 0.5
    widthCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    colorGradient:
      serializedVersion: 2
      key0: {r: 0.8207547, g: 0.8207547, b: 0.8207547, a: 1}
      key1: {r: 1, g: 1, b: 1, a: 1}
      key2: {r: 1, g: 1, b: 1, a: 0}
      key3: {r: 0, g: 0, b: 0, a: 0}
      key4: {r: 0, g: 0, b: 0, a: 0}
      key5: {r: 0, g: 0, b: 0, a: 0}
      key6: {r: 0, g: 0, b: 0, a: 0}
      key7: {r: 0, g: 0, b: 0, a: 0}
      ctime0: 0
      ctime1: 6554
      ctime2: 65535
      ctime3: 0
      ctime4: 0
      ctime5: 0
      ctime6: 0
      ctime7: 0
      atime0: 0
      atime1: 65535
      atime2: 0
      atime3: 0
      atime4: 0
      atime5: 0
      atime6: 0
      atime7: 0
      m_Mode: 0
      m_NumColorKeys: 3
      m_NumAlphaKeys: 2
    numCornerVertices: 0
    numCapVertices: 0
    alignment: 0
    textureMode: 0
    shadowBias: 0
    generateLightingData: 0
  m_UseWorldSpace: 1
  m_Loop: 0
--- !u!4 &665220345
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 665220342}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -10.08, y: -3.62, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1511050799}
  - {fileID: 1521069615}
  - {fileID: 1515801002}
  - {fileID: 1383356089}
  - {fileID: 523521495}
  - {fileID: 629939770}
  - {fileID: 705777285}
  m_Father: {fileID: 0}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &705777284
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 705777285}
  m_Layer: 0
  m_Name: End
  m_TagString: Untagged
  m_Icon: {fileID: 5132851093641282708, guid: 0000000000000000d000000000000000, type: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &705777285
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 705777284}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 17.08, y: 1.54, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 775867012}
  m_Father: {fileID: 665220345}
  m_RootOrder: 6
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &741245294
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 741245295}
  - component: {fileID: 741245297}
  m_Layer: 0
  m_Name: Control1
  m_TagString: Untagged
  m_Icon: {fileID: 2974397684917235467, guid: 0000000000000000d000000000000000, type: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &741245295
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 741245294}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 12.64, y: 0.97, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 2137754276}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &741245297
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 741245294}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 347013242dca7c44fb5001793033ea59, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  UpdateMode: 0
  PositionActive: 1
  RotationActive: 0
  ScaleActive: 0
  PositionWiggleProperties:
    WigglePermitted: 1
    WiggleType: 3
    UseUnscaledTime: 0
    StartWigglingAutomatically: 1
    SmoothPingPong: 1
    UseSpeedCurve: 0
    SpeedCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    FrequencyMin: 1
    FrequencyMax: 2
    AmplitudeMin: {x: -1, y: -1, z: -1}
    AmplitudeMax: {x: 1, y: 1, z: 1}
    RelativeAmplitude: 1
    Curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    RemapCurveZeroMin: {x: 0, y: 0, z: 0}
    RemapCurveZeroMax: {x: 0, y: 0, z: 0}
    RemapCurveOneMin: {x: 1, y: 1, z: 1}
    RemapCurveOneMax: {x: 1, y: 1, z: 1}
    RelativeCurveAmplitude: 1
    CurvePingPong: 0
    PauseMin: 0
    PauseMax: 0
    LimitedTime: 0
    LimitedTimeTotal: 0
    LimitedTimeFalloff:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    LimitedTimeResetValue: 1
    LimitedTimeLeft: 0
    NoiseFrequencyMin: {x: 0, y: 0, z: 0}
    NoiseFrequencyMax: {x: 1, y: 1, z: 1}
    NoiseShiftMin: {x: 0, y: 0, z: 0}
    NoiseShiftMax: {x: 0, y: 0, z: 0}
  RotationWiggleProperties:
    WigglePermitted: 1
    WiggleType: 1
    UseUnscaledTime: 0
    StartWigglingAutomatically: 1
    SmoothPingPong: 1
    UseSpeedCurve: 0
    SpeedCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    FrequencyMin: 0
    FrequencyMax: 1
    AmplitudeMin: {x: 0, y: 0, z: 0}
    AmplitudeMax: {x: 1, y: 1, z: 1}
    RelativeAmplitude: 1
    Curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    RemapCurveZeroMin: {x: 0, y: 0, z: 0}
    RemapCurveZeroMax: {x: 0, y: 0, z: 0}
    RemapCurveOneMin: {x: 1, y: 1, z: 1}
    RemapCurveOneMax: {x: 1, y: 1, z: 1}
    RelativeCurveAmplitude: 1
    CurvePingPong: 0
    PauseMin: 0
    PauseMax: 0
    LimitedTime: 0
    LimitedTimeTotal: 0
    LimitedTimeFalloff:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    LimitedTimeResetValue: 1
    LimitedTimeLeft: 0
    NoiseFrequencyMin: {x: 0, y: 0, z: 0}
    NoiseFrequencyMax: {x: 1, y: 1, z: 1}
    NoiseShiftMin: {x: 0, y: 0, z: 0}
    NoiseShiftMax: {x: 0, y: 0, z: 0}
  ScaleWiggleProperties:
    WigglePermitted: 1
    WiggleType: 1
    UseUnscaledTime: 0
    StartWigglingAutomatically: 1
    SmoothPingPong: 1
    UseSpeedCurve: 0
    SpeedCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    FrequencyMin: 0
    FrequencyMax: 1
    AmplitudeMin: {x: 0, y: 0, z: 0}
    AmplitudeMax: {x: 1, y: 1, z: 1}
    RelativeAmplitude: 1
    Curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    RemapCurveZeroMin: {x: 0, y: 0, z: 0}
    RemapCurveZeroMax: {x: 0, y: 0, z: 0}
    RemapCurveOneMin: {x: 1, y: 1, z: 1}
    RemapCurveOneMax: {x: 1, y: 1, z: 1}
    RelativeCurveAmplitude: 1
    CurvePingPong: 0
    PauseMin: 0
    PauseMax: 0
    LimitedTime: 0
    LimitedTimeTotal: 0
    LimitedTimeFalloff:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    LimitedTimeResetValue: 1
    LimitedTimeLeft: 0
    NoiseFrequencyMin: {x: 0, y: 0, z: 0}
    NoiseFrequencyMax: {x: 1, y: 1, z: 1}
    NoiseShiftMin: {x: 0, y: 0, z: 0}
    NoiseShiftMax: {x: 0, y: 0, z: 0}
  DebugWiggleDuration: 2
--- !u!1 &741898272
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 741898273}
  - component: {fileID: 741898274}
  m_Layer: 0
  m_Name: MMBezierLineRendererDemoDudeHand
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &741898273
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 741898272}
  m_LocalRotation: {x: -0, y: -0, z: 0.97291106, w: 0.23117997}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: -1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1480235752}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 153.26701}
--- !u!212 &741898274
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 741898272}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Sprite: {fileID: 21300000, guid: 3eb4421e0ac10aa488e44538963f00e5, type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 1.28, y: 1.28}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!1 &775867011
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 775867012}
  - component: {fileID: 775867013}
  m_Layer: 0
  m_Name: MMBezierLineRendererDemoDudeHand
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &775867012
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 775867011}
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 705777285}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: -90}
--- !u!212 &775867013
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 775867011}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Sprite: {fileID: 21300000, guid: 3eb4421e0ac10aa488e44538963f00e5, type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 1.28, y: 1.28}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!1 &776746534
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 776746535}
  - component: {fileID: 776746537}
  m_Layer: 0
  m_Name: Control2
  m_TagString: Untagged
  m_Icon: {fileID: 2974397684917235467, guid: 0000000000000000d000000000000000, type: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &776746535
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 776746534}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 6.88, y: 2.64, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 2062585004}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &776746537
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 776746534}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 347013242dca7c44fb5001793033ea59, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  UpdateMode: 0
  PositionActive: 1
  RotationActive: 0
  ScaleActive: 0
  PositionWiggleProperties:
    WigglePermitted: 1
    WiggleType: 3
    UseUnscaledTime: 0
    StartWigglingAutomatically: 1
    SmoothPingPong: 1
    UseSpeedCurve: 0
    SpeedCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    FrequencyMin: 1
    FrequencyMax: 2
    AmplitudeMin: {x: -1, y: -1, z: -1}
    AmplitudeMax: {x: 1, y: 1, z: 1}
    RelativeAmplitude: 1
    Curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    RemapCurveZeroMin: {x: 0, y: 0, z: 0}
    RemapCurveZeroMax: {x: 0, y: 0, z: 0}
    RemapCurveOneMin: {x: 1, y: 1, z: 1}
    RemapCurveOneMax: {x: 1, y: 1, z: 1}
    RelativeCurveAmplitude: 1
    CurvePingPong: 0
    PauseMin: 0
    PauseMax: 0
    LimitedTime: 0
    LimitedTimeTotal: 0
    LimitedTimeFalloff:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    LimitedTimeResetValue: 1
    LimitedTimeLeft: 0
    NoiseFrequencyMin: {x: 0, y: 0, z: 0}
    NoiseFrequencyMax: {x: 1, y: 1, z: 1}
    NoiseShiftMin: {x: 0, y: 0, z: 0}
    NoiseShiftMax: {x: 0, y: 0, z: 0}
  RotationWiggleProperties:
    WigglePermitted: 1
    WiggleType: 1
    UseUnscaledTime: 0
    StartWigglingAutomatically: 1
    SmoothPingPong: 1
    UseSpeedCurve: 0
    SpeedCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    FrequencyMin: 0
    FrequencyMax: 1
    AmplitudeMin: {x: 0, y: 0, z: 0}
    AmplitudeMax: {x: 1, y: 1, z: 1}
    RelativeAmplitude: 1
    Curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    RemapCurveZeroMin: {x: 0, y: 0, z: 0}
    RemapCurveZeroMax: {x: 0, y: 0, z: 0}
    RemapCurveOneMin: {x: 1, y: 1, z: 1}
    RemapCurveOneMax: {x: 1, y: 1, z: 1}
    RelativeCurveAmplitude: 1
    CurvePingPong: 0
    PauseMin: 0
    PauseMax: 0
    LimitedTime: 0
    LimitedTimeTotal: 0
    LimitedTimeFalloff:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    LimitedTimeResetValue: 1
    LimitedTimeLeft: 0
    NoiseFrequencyMin: {x: 0, y: 0, z: 0}
    NoiseFrequencyMax: {x: 1, y: 1, z: 1}
    NoiseShiftMin: {x: 0, y: 0, z: 0}
    NoiseShiftMax: {x: 0, y: 0, z: 0}
  ScaleWiggleProperties:
    WigglePermitted: 1
    WiggleType: 1
    UseUnscaledTime: 0
    StartWigglingAutomatically: 1
    SmoothPingPong: 1
    UseSpeedCurve: 0
    SpeedCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    FrequencyMin: 0
    FrequencyMax: 1
    AmplitudeMin: {x: 0, y: 0, z: 0}
    AmplitudeMax: {x: 1, y: 1, z: 1}
    RelativeAmplitude: 1
    Curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    RemapCurveZeroMin: {x: 0, y: 0, z: 0}
    RemapCurveZeroMax: {x: 0, y: 0, z: 0}
    RemapCurveOneMin: {x: 1, y: 1, z: 1}
    RemapCurveOneMax: {x: 1, y: 1, z: 1}
    RelativeCurveAmplitude: 1
    CurvePingPong: 0
    PauseMin: 0
    PauseMax: 0
    LimitedTime: 0
    LimitedTimeTotal: 0
    LimitedTimeFalloff:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    LimitedTimeResetValue: 1
    LimitedTimeLeft: 0
    NoiseFrequencyMin: {x: 0, y: 0, z: 0}
    NoiseFrequencyMax: {x: 1, y: 1, z: 1}
    NoiseShiftMin: {x: 0, y: 0, z: 0}
    NoiseShiftMax: {x: 0, y: 0, z: 0}
  DebugWiggleDuration: 2
--- !u!1 &843146712
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 843146713}
  m_Layer: 0
  m_Name: Start
  m_TagString: Untagged
  m_Icon: {fileID: 5132851093641282708, guid: 0000000000000000d000000000000000, type: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &843146713
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 843146712}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 9.21, y: 2.5, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 413556091}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &941127169
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 941127171}
  - component: {fileID: 941127172}
  m_Layer: 0
  m_Name: Control3
  m_TagString: Untagged
  m_Icon: {fileID: 2974397684917235467, guid: 0000000000000000d000000000000000, type: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &941127171
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 941127169}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 6.13, y: 4.98, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 2062585004}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &941127172
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 941127169}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 347013242dca7c44fb5001793033ea59, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  UpdateMode: 0
  PositionActive: 1
  RotationActive: 0
  ScaleActive: 0
  PositionWiggleProperties:
    WigglePermitted: 1
    WiggleType: 3
    UseUnscaledTime: 0
    StartWigglingAutomatically: 1
    SmoothPingPong: 1
    UseSpeedCurve: 0
    SpeedCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    FrequencyMin: 1
    FrequencyMax: 2
    AmplitudeMin: {x: -1, y: -1, z: -1}
    AmplitudeMax: {x: 1, y: 1, z: 1}
    RelativeAmplitude: 1
    Curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    RemapCurveZeroMin: {x: 0, y: 0, z: 0}
    RemapCurveZeroMax: {x: 0, y: 0, z: 0}
    RemapCurveOneMin: {x: 1, y: 1, z: 1}
    RemapCurveOneMax: {x: 1, y: 1, z: 1}
    RelativeCurveAmplitude: 1
    CurvePingPong: 0
    PauseMin: 0
    PauseMax: 0
    LimitedTime: 0
    LimitedTimeTotal: 0
    LimitedTimeFalloff:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    LimitedTimeResetValue: 1
    LimitedTimeLeft: 0
    NoiseFrequencyMin: {x: 0, y: 0, z: 0}
    NoiseFrequencyMax: {x: 1, y: 1, z: 1}
    NoiseShiftMin: {x: 0, y: 0, z: 0}
    NoiseShiftMax: {x: 0, y: 0, z: 0}
  RotationWiggleProperties:
    WigglePermitted: 1
    WiggleType: 1
    UseUnscaledTime: 0
    StartWigglingAutomatically: 1
    SmoothPingPong: 1
    UseSpeedCurve: 0
    SpeedCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    FrequencyMin: 0
    FrequencyMax: 1
    AmplitudeMin: {x: 0, y: 0, z: 0}
    AmplitudeMax: {x: 1, y: 1, z: 1}
    RelativeAmplitude: 1
    Curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    RemapCurveZeroMin: {x: 0, y: 0, z: 0}
    RemapCurveZeroMax: {x: 0, y: 0, z: 0}
    RemapCurveOneMin: {x: 1, y: 1, z: 1}
    RemapCurveOneMax: {x: 1, y: 1, z: 1}
    RelativeCurveAmplitude: 1
    CurvePingPong: 0
    PauseMin: 0
    PauseMax: 0
    LimitedTime: 0
    LimitedTimeTotal: 0
    LimitedTimeFalloff:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    LimitedTimeResetValue: 1
    LimitedTimeLeft: 0
    NoiseFrequencyMin: {x: 0, y: 0, z: 0}
    NoiseFrequencyMax: {x: 1, y: 1, z: 1}
    NoiseShiftMin: {x: 0, y: 0, z: 0}
    NoiseShiftMax: {x: 0, y: 0, z: 0}
  ScaleWiggleProperties:
    WigglePermitted: 1
    WiggleType: 1
    UseUnscaledTime: 0
    StartWigglingAutomatically: 1
    SmoothPingPong: 1
    UseSpeedCurve: 0
    SpeedCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    FrequencyMin: 0
    FrequencyMax: 1
    AmplitudeMin: {x: 0, y: 0, z: 0}
    AmplitudeMax: {x: 1, y: 1, z: 1}
    RelativeAmplitude: 1
    Curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    RemapCurveZeroMin: {x: 0, y: 0, z: 0}
    RemapCurveZeroMax: {x: 0, y: 0, z: 0}
    RemapCurveOneMin: {x: 1, y: 1, z: 1}
    RemapCurveOneMax: {x: 1, y: 1, z: 1}
    RelativeCurveAmplitude: 1
    CurvePingPong: 0
    PauseMin: 0
    PauseMax: 0
    LimitedTime: 0
    LimitedTimeTotal: 0
    LimitedTimeFalloff:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    LimitedTimeResetValue: 1
    LimitedTimeLeft: 0
    NoiseFrequencyMin: {x: 0, y: 0, z: 0}
    NoiseFrequencyMax: {x: 1, y: 1, z: 1}
    NoiseShiftMin: {x: 0, y: 0, z: 0}
    NoiseShiftMax: {x: 0, y: 0, z: 0}
  DebugWiggleDuration: 2
--- !u!1 &1080195248
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1080195249}
  m_Layer: 0
  m_Name: End
  m_TagString: Untagged
  m_Icon: {fileID: 5132851093641282708, guid: 0000000000000000d000000000000000, type: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1080195249
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1080195248}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 4.43, y: 4.34, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 622112410}
  m_Father: {fileID: 413556091}
  m_RootOrder: 6
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1085712392
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1085712394}
  - component: {fileID: 1085712393}
  m_Layer: 0
  m_Name: MMBezierLineRendererDemoDude
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!212 &1085712393
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1085712392}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Sprite: {fileID: 21300000, guid: 0e4040556b24d6040826b165baa18a21, type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 5.12, y: 5.12}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!4 &1085712394
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1085712392}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 5
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1383356088
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1383356089}
  m_Layer: 0
  m_Name: Mid
  m_TagString: Untagged
  m_Icon: {fileID: 5132851093641282708, guid: 0000000000000000d000000000000000, type: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1383356089
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1383356088}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 14.25, y: 3.34, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 665220345}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1435426885
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1435426886}
  - component: {fileID: 1435426888}
  m_Layer: 0
  m_Name: Control4
  m_TagString: Untagged
  m_Icon: {fileID: 2974397684917235467, guid: 0000000000000000d000000000000000, type: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1435426886
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1435426885}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 15.37, y: 1.11, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 2137754276}
  m_RootOrder: 5
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1435426888
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1435426885}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 347013242dca7c44fb5001793033ea59, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  UpdateMode: 0
  PositionActive: 1
  RotationActive: 0
  ScaleActive: 0
  PositionWiggleProperties:
    WigglePermitted: 1
    WiggleType: 3
    UseUnscaledTime: 0
    StartWigglingAutomatically: 1
    SmoothPingPong: 1
    UseSpeedCurve: 0
    SpeedCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    FrequencyMin: 1
    FrequencyMax: 2
    AmplitudeMin: {x: -1, y: -1, z: -1}
    AmplitudeMax: {x: 1, y: 1, z: 1}
    RelativeAmplitude: 1
    Curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    RemapCurveZeroMin: {x: 0, y: 0, z: 0}
    RemapCurveZeroMax: {x: 0, y: 0, z: 0}
    RemapCurveOneMin: {x: 1, y: 1, z: 1}
    RemapCurveOneMax: {x: 1, y: 1, z: 1}
    RelativeCurveAmplitude: 1
    CurvePingPong: 0
    PauseMin: 0
    PauseMax: 0
    LimitedTime: 0
    LimitedTimeTotal: 0
    LimitedTimeFalloff:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    LimitedTimeResetValue: 1
    LimitedTimeLeft: 0
    NoiseFrequencyMin: {x: 0, y: 0, z: 0}
    NoiseFrequencyMax: {x: 1, y: 1, z: 1}
    NoiseShiftMin: {x: 0, y: 0, z: 0}
    NoiseShiftMax: {x: 0, y: 0, z: 0}
  RotationWiggleProperties:
    WigglePermitted: 1
    WiggleType: 1
    UseUnscaledTime: 0
    StartWigglingAutomatically: 1
    SmoothPingPong: 1
    UseSpeedCurve: 0
    SpeedCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    FrequencyMin: 0
    FrequencyMax: 1
    AmplitudeMin: {x: 0, y: 0, z: 0}
    AmplitudeMax: {x: 1, y: 1, z: 1}
    RelativeAmplitude: 1
    Curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    RemapCurveZeroMin: {x: 0, y: 0, z: 0}
    RemapCurveZeroMax: {x: 0, y: 0, z: 0}
    RemapCurveOneMin: {x: 1, y: 1, z: 1}
    RemapCurveOneMax: {x: 1, y: 1, z: 1}
    RelativeCurveAmplitude: 1
    CurvePingPong: 0
    PauseMin: 0
    PauseMax: 0
    LimitedTime: 0
    LimitedTimeTotal: 0
    LimitedTimeFalloff:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    LimitedTimeResetValue: 1
    LimitedTimeLeft: 0
    NoiseFrequencyMin: {x: 0, y: 0, z: 0}
    NoiseFrequencyMax: {x: 1, y: 1, z: 1}
    NoiseShiftMin: {x: 0, y: 0, z: 0}
    NoiseShiftMax: {x: 0, y: 0, z: 0}
  ScaleWiggleProperties:
    WigglePermitted: 1
    WiggleType: 1
    UseUnscaledTime: 0
    StartWigglingAutomatically: 1
    SmoothPingPong: 1
    UseSpeedCurve: 0
    SpeedCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    FrequencyMin: 0
    FrequencyMax: 1
    AmplitudeMin: {x: 0, y: 0, z: 0}
    AmplitudeMax: {x: 1, y: 1, z: 1}
    RelativeAmplitude: 1
    Curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    RemapCurveZeroMin: {x: 0, y: 0, z: 0}
    RemapCurveZeroMax: {x: 0, y: 0, z: 0}
    RemapCurveOneMin: {x: 1, y: 1, z: 1}
    RemapCurveOneMax: {x: 1, y: 1, z: 1}
    RelativeCurveAmplitude: 1
    CurvePingPong: 0
    PauseMin: 0
    PauseMax: 0
    LimitedTime: 0
    LimitedTimeTotal: 0
    LimitedTimeFalloff:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    LimitedTimeResetValue: 1
    LimitedTimeLeft: 0
    NoiseFrequencyMin: {x: 0, y: 0, z: 0}
    NoiseFrequencyMax: {x: 1, y: 1, z: 1}
    NoiseShiftMin: {x: 0, y: 0, z: 0}
    NoiseShiftMax: {x: 0, y: 0, z: 0}
  DebugWiggleDuration: 2
--- !u!1 &1480235751
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1480235752}
  - component: {fileID: 1480235754}
  m_Layer: 0
  m_Name: End
  m_TagString: Untagged
  m_Icon: {fileID: 5132851093641282708, guid: 0000000000000000d000000000000000, type: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1480235752
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1480235751}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 4.43, y: 4.34, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 741898273}
  m_Father: {fileID: 2062585004}
  m_RootOrder: 6
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1480235754
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1480235751}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 347013242dca7c44fb5001793033ea59, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  UpdateMode: 0
  PositionActive: 1
  RotationActive: 0
  ScaleActive: 0
  PositionWiggleProperties:
    WigglePermitted: 1
    WiggleType: 3
    UseUnscaledTime: 0
    StartWigglingAutomatically: 1
    SmoothPingPong: 1
    UseSpeedCurve: 0
    SpeedCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    FrequencyMin: 1
    FrequencyMax: 2
    AmplitudeMin: {x: -1, y: -1, z: -1}
    AmplitudeMax: {x: 1, y: 1, z: 1}
    RelativeAmplitude: 1
    Curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    RemapCurveZeroMin: {x: 0, y: 0, z: 0}
    RemapCurveZeroMax: {x: 0, y: 0, z: 0}
    RemapCurveOneMin: {x: 1, y: 1, z: 1}
    RemapCurveOneMax: {x: 1, y: 1, z: 1}
    RelativeCurveAmplitude: 1
    CurvePingPong: 0
    PauseMin: 0
    PauseMax: 0
    LimitedTime: 0
    LimitedTimeTotal: 0
    LimitedTimeFalloff:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    LimitedTimeResetValue: 1
    LimitedTimeLeft: 0
    NoiseFrequencyMin: {x: 0, y: 0, z: 0}
    NoiseFrequencyMax: {x: 1, y: 1, z: 1}
    NoiseShiftMin: {x: 0, y: 0, z: 0}
    NoiseShiftMax: {x: 0, y: 0, z: 0}
  RotationWiggleProperties:
    WigglePermitted: 1
    WiggleType: 1
    UseUnscaledTime: 0
    StartWigglingAutomatically: 1
    SmoothPingPong: 1
    UseSpeedCurve: 0
    SpeedCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    FrequencyMin: 0
    FrequencyMax: 1
    AmplitudeMin: {x: 0, y: 0, z: 0}
    AmplitudeMax: {x: 1, y: 1, z: 1}
    RelativeAmplitude: 1
    Curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    RemapCurveZeroMin: {x: 0, y: 0, z: 0}
    RemapCurveZeroMax: {x: 0, y: 0, z: 0}
    RemapCurveOneMin: {x: 1, y: 1, z: 1}
    RemapCurveOneMax: {x: 1, y: 1, z: 1}
    RelativeCurveAmplitude: 1
    CurvePingPong: 0
    PauseMin: 0
    PauseMax: 0
    LimitedTime: 0
    LimitedTimeTotal: 0
    LimitedTimeFalloff:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    LimitedTimeResetValue: 1
    LimitedTimeLeft: 0
    NoiseFrequencyMin: {x: 0, y: 0, z: 0}
    NoiseFrequencyMax: {x: 1, y: 1, z: 1}
    NoiseShiftMin: {x: 0, y: 0, z: 0}
    NoiseShiftMax: {x: 0, y: 0, z: 0}
  ScaleWiggleProperties:
    WigglePermitted: 1
    WiggleType: 1
    UseUnscaledTime: 0
    StartWigglingAutomatically: 1
    SmoothPingPong: 1
    UseSpeedCurve: 0
    SpeedCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    FrequencyMin: 0
    FrequencyMax: 1
    AmplitudeMin: {x: 0, y: 0, z: 0}
    AmplitudeMax: {x: 1, y: 1, z: 1}
    RelativeAmplitude: 1
    Curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    RemapCurveZeroMin: {x: 0, y: 0, z: 0}
    RemapCurveZeroMax: {x: 0, y: 0, z: 0}
    RemapCurveOneMin: {x: 1, y: 1, z: 1}
    RemapCurveOneMax: {x: 1, y: 1, z: 1}
    RelativeCurveAmplitude: 1
    CurvePingPong: 0
    PauseMin: 0
    PauseMax: 0
    LimitedTime: 0
    LimitedTimeTotal: 0
    LimitedTimeFalloff:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    LimitedTimeResetValue: 1
    LimitedTimeLeft: 0
    NoiseFrequencyMin: {x: 0, y: 0, z: 0}
    NoiseFrequencyMax: {x: 1, y: 1, z: 1}
    NoiseShiftMin: {x: 0, y: 0, z: 0}
    NoiseShiftMax: {x: 0, y: 0, z: 0}
  DebugWiggleDuration: 2
--- !u!1 &1511050798
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1511050799}
  m_Layer: 0
  m_Name: Start
  m_TagString: Untagged
  m_Icon: {fileID: 5132851093641282708, guid: 0000000000000000d000000000000000, type: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1511050799
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1511050798}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 11.09, y: 2.55, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 665220345}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1515801001
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1515801002}
  m_Layer: 0
  m_Name: Control2
  m_TagString: Untagged
  m_Icon: {fileID: 2974397684917235467, guid: 0000000000000000d000000000000000, type: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1515801002
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1515801001}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 13.18, y: 3.13, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 665220345}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1521069614
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1521069615}
  m_Layer: 0
  m_Name: Control1
  m_TagString: Untagged
  m_Icon: {fileID: 2974397684917235467, guid: 0000000000000000d000000000000000, type: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1521069615
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1521069614}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 12.64, y: 0.97, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 665220345}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1675030886
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1675030887}
  - component: {fileID: 1675030888}
  m_Layer: 0
  m_Name: MMBezierLineRendererDemoDudeHand
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1675030887
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1675030886}
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 239464958}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: -90}
--- !u!212 &1675030888
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1675030886}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Sprite: {fileID: 21300000, guid: 3eb4421e0ac10aa488e44538963f00e5, type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 1.28, y: 1.28}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!1 &1720615898
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1720615899}
  m_Layer: 0
  m_Name: Control1
  m_TagString: Untagged
  m_Icon: {fileID: 2974397684917235467, guid: 0000000000000000d000000000000000, type: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1720615899
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1720615898}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 6.37, y: 1.3699999, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 413556091}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1734772539
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1734772541}
  m_Layer: 0
  m_Name: Start
  m_TagString: Untagged
  m_Icon: {fileID: 5132851093641282708, guid: 0000000000000000d000000000000000, type: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1734772541
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1734772539}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 9.21, y: 2.5, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 2062585004}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1818202471
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1818202472}
  m_Layer: 0
  m_Name: Start
  m_TagString: Untagged
  m_Icon: {fileID: 5132851093641282708, guid: 0000000000000000d000000000000000, type: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1818202472
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1818202471}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 11.09, y: 2.55, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 2137754276}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1859827273
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1859827274}
  - component: {fileID: 1859827276}
  m_Layer: 0
  m_Name: Control1
  m_TagString: Untagged
  m_Icon: {fileID: 2974397684917235467, guid: 0000000000000000d000000000000000, type: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1859827274
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1859827273}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 6.37, y: 1.3699999, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 2062585004}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1859827276
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1859827273}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 347013242dca7c44fb5001793033ea59, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  UpdateMode: 0
  PositionActive: 1
  RotationActive: 0
  ScaleActive: 0
  PositionWiggleProperties:
    WigglePermitted: 1
    WiggleType: 3
    UseUnscaledTime: 0
    StartWigglingAutomatically: 1
    SmoothPingPong: 1
    UseSpeedCurve: 0
    SpeedCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    FrequencyMin: 1
    FrequencyMax: 2
    AmplitudeMin: {x: -1, y: -1, z: -1}
    AmplitudeMax: {x: 1, y: 1, z: 1}
    RelativeAmplitude: 1
    Curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    RemapCurveZeroMin: {x: 0, y: 0, z: 0}
    RemapCurveZeroMax: {x: 0, y: 0, z: 0}
    RemapCurveOneMin: {x: 1, y: 1, z: 1}
    RemapCurveOneMax: {x: 1, y: 1, z: 1}
    RelativeCurveAmplitude: 1
    CurvePingPong: 0
    PauseMin: 0
    PauseMax: 0
    LimitedTime: 0
    LimitedTimeTotal: 0
    LimitedTimeFalloff:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    LimitedTimeResetValue: 1
    LimitedTimeLeft: 0
    NoiseFrequencyMin: {x: 0, y: 0, z: 0}
    NoiseFrequencyMax: {x: 1, y: 1, z: 1}
    NoiseShiftMin: {x: 0, y: 0, z: 0}
    NoiseShiftMax: {x: 0, y: 0, z: 0}
  RotationWiggleProperties:
    WigglePermitted: 1
    WiggleType: 1
    UseUnscaledTime: 0
    StartWigglingAutomatically: 1
    SmoothPingPong: 1
    UseSpeedCurve: 0
    SpeedCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    FrequencyMin: 0
    FrequencyMax: 1
    AmplitudeMin: {x: 0, y: 0, z: 0}
    AmplitudeMax: {x: 1, y: 1, z: 1}
    RelativeAmplitude: 1
    Curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    RemapCurveZeroMin: {x: 0, y: 0, z: 0}
    RemapCurveZeroMax: {x: 0, y: 0, z: 0}
    RemapCurveOneMin: {x: 1, y: 1, z: 1}
    RemapCurveOneMax: {x: 1, y: 1, z: 1}
    RelativeCurveAmplitude: 1
    CurvePingPong: 0
    PauseMin: 0
    PauseMax: 0
    LimitedTime: 0
    LimitedTimeTotal: 0
    LimitedTimeFalloff:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    LimitedTimeResetValue: 1
    LimitedTimeLeft: 0
    NoiseFrequencyMin: {x: 0, y: 0, z: 0}
    NoiseFrequencyMax: {x: 1, y: 1, z: 1}
    NoiseShiftMin: {x: 0, y: 0, z: 0}
    NoiseShiftMax: {x: 0, y: 0, z: 0}
  ScaleWiggleProperties:
    WigglePermitted: 1
    WiggleType: 1
    UseUnscaledTime: 0
    StartWigglingAutomatically: 1
    SmoothPingPong: 1
    UseSpeedCurve: 0
    SpeedCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    FrequencyMin: 0
    FrequencyMax: 1
    AmplitudeMin: {x: 0, y: 0, z: 0}
    AmplitudeMax: {x: 1, y: 1, z: 1}
    RelativeAmplitude: 1
    Curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    RemapCurveZeroMin: {x: 0, y: 0, z: 0}
    RemapCurveZeroMax: {x: 0, y: 0, z: 0}
    RemapCurveOneMin: {x: 1, y: 1, z: 1}
    RemapCurveOneMax: {x: 1, y: 1, z: 1}
    RelativeCurveAmplitude: 1
    CurvePingPong: 0
    PauseMin: 0
    PauseMax: 0
    LimitedTime: 0
    LimitedTimeTotal: 0
    LimitedTimeFalloff:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    LimitedTimeResetValue: 1
    LimitedTimeLeft: 0
    NoiseFrequencyMin: {x: 0, y: 0, z: 0}
    NoiseFrequencyMax: {x: 1, y: 1, z: 1}
    NoiseShiftMin: {x: 0, y: 0, z: 0}
    NoiseShiftMax: {x: 0, y: 0, z: 0}
  DebugWiggleDuration: 2
--- !u!1 &1888501705
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1888501706}
  m_Layer: 0
  m_Name: Control2
  m_TagString: Untagged
  m_Icon: {fileID: 2974397684917235467, guid: 0000000000000000d000000000000000, type: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1888501706
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1888501705}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 6.88, y: 2.64, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 413556091}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2062585001
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2062585004}
  - component: {fileID: 2062585003}
  - component: {fileID: 2062585002}
  m_Layer: 0
  m_Name: MMBezierLineRenderer - RightArm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &2062585002
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2062585001}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 177926c7a52cdb247b4ea692ea51aa68, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  AdjustmentHandles:
  - {fileID: 1734772541}
  - {fileID: 1859827274}
  - {fileID: 776746535}
  - {fileID: 320325108}
  - {fileID: 941127171}
  - {fileID: 590311556}
  - {fileID: 1480235752}
  NumberOfSegments: 50
  SortingLayerName: Default
  NumberOfCurves: 2
--- !u!120 &2062585003
LineRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2062585001}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 0
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 0
  m_RayTracingMode: 0
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Positions:
  - {x: -0.8699999, y: -1.1199999, z: 0}
  - {x: -1.0397282, y: -1.1862053, z: 0}
  - {x: -1.2013031, y: -1.2465348, z: 0}
  - {x: -1.354943, y: -1.3011107, z: 0}
  - {x: -1.5008662, y: -1.3500546, z: 0}
  - {x: -1.6392912, y: -1.3934888, z: 0}
  - {x: -1.7704359, y: -1.4315348, z: 0}
  - {x: -1.8945189, y: -1.4643148, z: 0}
  - {x: -2.0117583, y: -1.4919504, z: 0}
  - {x: -2.1223722, y: -1.5145633, z: 0}
  - {x: -2.2265794, y: -1.5322762, z: 0}
  - {x: -2.3245971, y: -1.5452102, z: 0}
  - {x: -2.4166455, y: -1.5534878, z: 0}
  - {x: -2.502941, y: -1.5572304, z: 0}
  - {x: -2.5837026, y: -1.5565597, z: 0}
  - {x: -2.6591487, y: -1.5515983, z: 0}
  - {x: -2.7294974, y: -1.5424676, z: 0}
  - {x: -2.7949667, y: -1.5292894, z: 0}
  - {x: -2.8557756, y: -1.512186, z: 0}
  - {x: -2.9121418, y: -1.491279, z: 0}
  - {x: -2.9642837, y: -1.4666902, z: 0}
  - {x: -3.0124195, y: -1.438542, z: 0}
  - {x: -3.0567677, y: -1.406956, z: 0}
  - {x: -3.0975468, y: -1.3720539, z: 0}
  - {x: -3.1349747, y: -1.3339578, z: 0}
  - {x: -3.1692693, y: -1.2927893, z: 0}
  - {x: -3.2006493, y: -1.2486706, z: 0}
  - {x: -3.2293336, y: -1.2017236, z: 0}
  - {x: -3.2555392, y: -1.1520698, z: 0}
  - {x: -3.2794852, y: -1.0998315, z: 0}
  - {x: -3.3013902, y: -1.0451306, z: 0}
  - {x: -3.3214712, y: -0.9880887, z: 0}
  - {x: -3.3399477, y: -0.9288279, z: 0}
  - {x: -3.357037, y: -0.86747, z: 0}
  - {x: -3.3729587, y: -0.8041367, z: 0}
  - {x: -3.38793, y: -0.73895025, z: 0}
  - {x: -3.4021697, y: -0.67203254, z: 0}
  - {x: -3.4158952, y: -0.60350513, z: 0}
  - {x: -3.4293256, y: -0.5334901, z: 0}
  - {x: -3.4426794, y: -0.4621094, z: 0}
  - {x: -3.456174, y: -0.38948452, z: 0}
  - {x: -3.4700284, y: -0.31573787, z: 0}
  - {x: -3.4844606, y: -0.24099119, z: 0}
  - {x: -3.4996886, y: -0.16536628, z: 0}
  - {x: -3.5159314, y: -0.088985056, z: 0}
  - {x: -3.5334067, y: -0.011969402, z: 0}
  - {x: -3.5523329, y: 0.065559015, z: 0}
  - {x: -3.5729282, y: 0.14347786, z: 0}
  - {x: -3.5954115, y: 0.22166547, z: 0}
  - {x: -3.62, y: 0.29999995, z: 0}
  - {x: -3.62, y: 0.29999995, z: 0}
  - {x: -3.640715, y: 0.3639565, z: 0}
  - {x: -3.6624444, y: 0.42601278, z: 0}
  - {x: -3.6851795, y: 0.48614284, z: 0}
  - {x: -3.7089093, y: 0.5443206, z: 0}
  - {x: -3.7336257, y: 0.6005202, z: 0}
  - {x: -3.7593174, y: 0.6547154, z: 0}
  - {x: -3.7859766, y: 0.70688045, z: 0}
  - {x: -3.813592, y: 0.75698924, z: 0}
  - {x: -3.842154, y: 0.80501556, z: 0}
  - {x: -3.8716543, y: 0.85093373, z: 0}
  - {x: -3.9020817, y: 0.89471745, z: 0}
  - {x: -3.9334278, y: 0.93634105, z: 0}
  - {x: -3.9656816, y: 0.97577816, z: 0}
  - {x: -3.998834, y: 1.013003, z: 0}
  - {x: -4.032875, y: 1.0479895, z: 0}
  - {x: -4.0677958, y: 1.0807116, z: 0}
  - {x: -4.1035857, y: 1.1111432, z: 0}
  - {x: -4.140236, y: 1.1392586, z: 0}
  - {x: -4.177736, y: 1.1650316, z: 0}
  - {x: -4.2160764, y: 1.188436, z: 0}
  - {x: -4.255247, y: 1.209446, z: 0}
  - {x: -4.2952394, y: 1.2280358, z: 0}
  - {x: -4.336044, y: 1.2441791, z: 0}
  - {x: -4.37765, y: 1.25785, z: 0}
  - {x: -4.4200473, y: 1.2690225, z: 0}
  - {x: -4.463227, y: 1.2776703, z: 0}
  - {x: -4.5071797, y: 1.2837678, z: 0}
  - {x: -4.551895, y: 1.2872888, z: 0}
  - {x: -4.597364, y: 1.2882072, z: 0}
  - {x: -4.643576, y: 1.2864972, z: 0}
  - {x: -4.690522, y: 1.2821327, z: 0}
  - {x: -4.7381926, y: 1.2750876, z: 0}
  - {x: -4.786577, y: 1.2653363, z: 0}
  - {x: -4.835667, y: 1.2528522, z: 0}
  - {x: -4.885452, y: 1.2376094, z: 0}
  - {x: -4.9359226, y: 1.2195823, z: 0}
  - {x: -4.987068, y: 1.1987445, z: 0}
  - {x: -5.0388803, y: 1.1750703, z: 0}
  - {x: -5.0913486, y: 1.1485335, z: 0}
  - {x: -5.1444635, y: 1.1191078, z: 0}
  - {x: -5.1982155, y: 1.0867678, z: 0}
  - {x: -5.252595, y: 1.051487, z: 0}
  - {x: -5.3075914, y: 1.0132396, z: 0}
  - {x: -5.3631964, y: 0.9719998, z: 0}
  - {x: -5.4193993, y: 0.9277412, z: 0}
  - {x: -5.476191, y: 0.88043785, z: 0}
  - {x: -5.5335617, y: 0.830064, z: 0}
  - {x: -5.5915017, y: 0.7765935, z: 0}
  - {x: -5.65, y: 0.72000027, z: 0}
  m_Parameters:
    serializedVersion: 3
    widthMultiplier: 0.5
    widthCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    colorGradient:
      serializedVersion: 2
      key0: {r: 0.8207547, g: 0.8207547, b: 0.8207547, a: 1}
      key1: {r: 1, g: 1, b: 1, a: 1}
      key2: {r: 1, g: 1, b: 1, a: 0}
      key3: {r: 0, g: 0, b: 0, a: 0}
      key4: {r: 0, g: 0, b: 0, a: 0}
      key5: {r: 0, g: 0, b: 0, a: 0}
      key6: {r: 0, g: 0, b: 0, a: 0}
      key7: {r: 0, g: 0, b: 0, a: 0}
      ctime0: 0
      ctime1: 6554
      ctime2: 65535
      ctime3: 0
      ctime4: 0
      ctime5: 0
      ctime6: 0
      ctime7: 0
      atime0: 0
      atime1: 65535
      atime2: 0
      atime3: 0
      atime4: 0
      atime5: 0
      atime6: 0
      atime7: 0
      m_Mode: 0
      m_NumColorKeys: 3
      m_NumAlphaKeys: 2
    numCornerVertices: 0
    numCapVertices: 0
    alignment: 0
    textureMode: 0
    shadowBias: 0
    generateLightingData: 0
  m_UseWorldSpace: 1
  m_Loop: 0
--- !u!4 &2062585004
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2062585001}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -10.08, y: -3.62, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1734772541}
  - {fileID: 1859827274}
  - {fileID: 776746535}
  - {fileID: 320325108}
  - {fileID: 941127171}
  - {fileID: 590311556}
  - {fileID: 1480235752}
  m_Father: {fileID: 0}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2085962822
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2085962823}
  - component: {fileID: 2085962825}
  m_Layer: 0
  m_Name: Control2
  m_TagString: Untagged
  m_Icon: {fileID: 2974397684917235467, guid: 0000000000000000d000000000000000, type: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2085962823
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2085962822}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 13.18, y: 3.13, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 2137754276}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2085962825
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2085962822}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 347013242dca7c44fb5001793033ea59, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  UpdateMode: 0
  PositionActive: 1
  RotationActive: 0
  ScaleActive: 0
  PositionWiggleProperties:
    WigglePermitted: 1
    WiggleType: 3
    UseUnscaledTime: 0
    StartWigglingAutomatically: 1
    SmoothPingPong: 1
    UseSpeedCurve: 0
    SpeedCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    FrequencyMin: 1
    FrequencyMax: 2
    AmplitudeMin: {x: -1, y: -1, z: -1}
    AmplitudeMax: {x: 1, y: 1, z: 1}
    RelativeAmplitude: 1
    Curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    RemapCurveZeroMin: {x: 0, y: 0, z: 0}
    RemapCurveZeroMax: {x: 0, y: 0, z: 0}
    RemapCurveOneMin: {x: 1, y: 1, z: 1}
    RemapCurveOneMax: {x: 1, y: 1, z: 1}
    RelativeCurveAmplitude: 1
    CurvePingPong: 0
    PauseMin: 0
    PauseMax: 0
    LimitedTime: 0
    LimitedTimeTotal: 0
    LimitedTimeFalloff:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    LimitedTimeResetValue: 1
    LimitedTimeLeft: 0
    NoiseFrequencyMin: {x: 0, y: 0, z: 0}
    NoiseFrequencyMax: {x: 1, y: 1, z: 1}
    NoiseShiftMin: {x: 0, y: 0, z: 0}
    NoiseShiftMax: {x: 0, y: 0, z: 0}
  RotationWiggleProperties:
    WigglePermitted: 1
    WiggleType: 1
    UseUnscaledTime: 0
    StartWigglingAutomatically: 1
    SmoothPingPong: 1
    UseSpeedCurve: 0
    SpeedCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    FrequencyMin: 0
    FrequencyMax: 1
    AmplitudeMin: {x: 0, y: 0, z: 0}
    AmplitudeMax: {x: 1, y: 1, z: 1}
    RelativeAmplitude: 1
    Curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    RemapCurveZeroMin: {x: 0, y: 0, z: 0}
    RemapCurveZeroMax: {x: 0, y: 0, z: 0}
    RemapCurveOneMin: {x: 1, y: 1, z: 1}
    RemapCurveOneMax: {x: 1, y: 1, z: 1}
    RelativeCurveAmplitude: 1
    CurvePingPong: 0
    PauseMin: 0
    PauseMax: 0
    LimitedTime: 0
    LimitedTimeTotal: 0
    LimitedTimeFalloff:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    LimitedTimeResetValue: 1
    LimitedTimeLeft: 0
    NoiseFrequencyMin: {x: 0, y: 0, z: 0}
    NoiseFrequencyMax: {x: 1, y: 1, z: 1}
    NoiseShiftMin: {x: 0, y: 0, z: 0}
    NoiseShiftMax: {x: 0, y: 0, z: 0}
  ScaleWiggleProperties:
    WigglePermitted: 1
    WiggleType: 1
    UseUnscaledTime: 0
    StartWigglingAutomatically: 1
    SmoothPingPong: 1
    UseSpeedCurve: 0
    SpeedCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    FrequencyMin: 0
    FrequencyMax: 1
    AmplitudeMin: {x: 0, y: 0, z: 0}
    AmplitudeMax: {x: 1, y: 1, z: 1}
    RelativeAmplitude: 1
    Curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    RemapCurveZeroMin: {x: 0, y: 0, z: 0}
    RemapCurveZeroMax: {x: 0, y: 0, z: 0}
    RemapCurveOneMin: {x: 1, y: 1, z: 1}
    RemapCurveOneMax: {x: 1, y: 1, z: 1}
    RelativeCurveAmplitude: 1
    CurvePingPong: 0
    PauseMin: 0
    PauseMax: 0
    LimitedTime: 0
    LimitedTimeTotal: 0
    LimitedTimeFalloff:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    LimitedTimeResetValue: 1
    LimitedTimeLeft: 0
    NoiseFrequencyMin: {x: 0, y: 0, z: 0}
    NoiseFrequencyMax: {x: 1, y: 1, z: 1}
    NoiseShiftMin: {x: 0, y: 0, z: 0}
    NoiseShiftMax: {x: 0, y: 0, z: 0}
  DebugWiggleDuration: 2
--- !u!1 &2137754273
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2137754276}
  - component: {fileID: 2137754275}
  - component: {fileID: 2137754274}
  m_Layer: 0
  m_Name: MMBezierLineRenderer - LeftArm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &2137754274
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2137754273}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 177926c7a52cdb247b4ea692ea51aa68, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  AdjustmentHandles:
  - {fileID: 1818202472}
  - {fileID: 741245295}
  - {fileID: 2085962823}
  - {fileID: 56892211}
  - {fileID: 454120795}
  - {fileID: 1435426886}
  - {fileID: 239464958}
  NumberOfSegments: 50
  SortingLayerName: Default
  NumberOfCurves: 2
--- !u!120 &2137754275
LineRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2137754273}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 0
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 0
  m_RayTracingMode: 0
  m_RenderingLayerMask: 4294967295
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Positions:
  - {x: 1.0100002, y: -1.0699999, z: 0}
  - {x: 1.1036494, y: -1.16211, z: 0}
  - {x: 1.194853, y: -1.245164, z: 0}
  - {x: 1.2836899, y: -1.3194524, z: 0}
  - {x: 1.3702382, y: -1.3852651, z: 0}
  - {x: 1.454577, y: -1.4428927, z: 0}
  - {x: 1.5367843, y: -1.4926248, z: 0}
  - {x: 1.6169392, y: -1.5347521, z: 0}
  - {x: 1.6951196, y: -1.5695646, z: 0}
  - {x: 1.7714044, y: -1.5973521, z: 0}
  - {x: 1.8458723, y: -1.6184057, z: 0}
  - {x: 1.9186013, y: -1.6330147, z: 0}
  - {x: 1.9896708, y: -1.64147, z: 0}
  - {x: 2.0591583, y: -1.6440613, z: 0}
  - {x: 2.1271434, y: -1.6410786, z: 0}
  - {x: 2.1937037, y: -1.6328125, z: 0}
  - {x: 2.258918, y: -1.6195534, z: 0}
  - {x: 2.322865, y: -1.6015906, z: 0}
  - {x: 2.3856235, y: -1.5792153, z: 0}
  - {x: 2.4472718, y: -1.5527171, z: 0}
  - {x: 2.5078878, y: -1.522386, z: 0}
  - {x: 2.5675511, y: -1.4885128, z: 0}
  - {x: 2.6263397, y: -1.4513874, z: 0}
  - {x: 2.6843326, y: -1.4113001, z: 0}
  - {x: 2.7416077, y: -1.3685408, z: 0}
  - {x: 2.7982433, y: -1.3233998, z: 0}
  - {x: 2.8543186, y: -1.2761673, z: 0}
  - {x: 2.9099123, y: -1.2271338, z: 0}
  - {x: 2.9651024, y: -1.1765887, z: 0}
  - {x: 3.0199678, y: -1.124823, z: 0}
  - {x: 3.0745866, y: -1.0721265, z: 0}
  - {x: 3.1290376, y: -1.0187895, z: 0}
  - {x: 3.1833994, y: -0.9651021, z: 0}
  - {x: 3.2377505, y: -0.9113545, z: 0}
  - {x: 3.2921696, y: -0.8578368, z: 0}
  - {x: 3.346735, y: -0.8048395, z: 0}
  - {x: 3.4015255, y: -0.75265247, z: 0}
  - {x: 3.456619, y: -0.70156604, z: 0}
  - {x: 3.5120945, y: -0.65187037, z: 0}
  - {x: 3.5680308, y: -0.60385567, z: 0}
  - {x: 3.6245062, y: -0.55781186, z: 0}
  - {x: 3.6815991, y: -0.51402956, z: 0}
  - {x: 3.739388, y: -0.4727987, z: 0}
  - {x: 3.7979515, y: -0.43440953, z: 0}
  - {x: 3.8573685, y: -0.39915222, z: 0}
  - {x: 3.9177172, y: -0.36731696, z: 0}
  - {x: 3.9790761, y: -0.33919385, z: 0}
  - {x: 4.041524, y: -0.31507322, z: 0}
  - {x: 4.1051393, y: -0.29524523, z: 0}
  - {x: 4.17, y: -0.27999997, z: 0}
  - {x: 4.17, y: -0.27999997, z: 0}
  - {x: 4.29301, y: -0.26453194, z: 0}
  - {x: 4.4087615, y: -0.2558891, z: 0}
  - {x: 4.517547, y: -0.25377467, z: 0}
  - {x: 4.6196537, y: -0.25789177, z: 0}
  - {x: 4.7153726, y: -0.2679437, z: 0}
  - {x: 4.8049927, y: -0.2836336, z: 0}
  - {x: 4.8888044, y: -0.30466464, z: 0}
  - {x: 4.9670973, y: -0.33073997, z: 0}
  - {x: 5.04016, y: -0.36156285, z: 0}
  - {x: 5.108283, y: -0.3968364, z: 0}
  - {x: 5.171756, y: -0.4362638, z: 0}
  - {x: 5.230869, y: -0.4795484, z: 0}
  - {x: 5.285911, y: -0.5263931, z: 0}
  - {x: 5.337172, y: -0.57650137, z: 0}
  - {x: 5.3849416, y: -0.6295762, z: 0}
  - {x: 5.4295096, y: -0.68532073, z: 0}
  - {x: 5.4711647, y: -0.7434384, z: 0}
  - {x: 5.510198, y: -0.8036322, z: 0}
  - {x: 5.546899, y: -0.86560535, z: 0}
  - {x: 5.5815554, y: -0.92906094, z: 0}
  - {x: 5.61446, y: -0.99370235, z: 0}
  - {x: 5.6459007, y: -1.0592327, z: 0}
  - {x: 5.6761675, y: -1.1253552, z: 0}
  - {x: 5.70555, y: -1.191773, z: 0}
  - {x: 5.734338, y: -1.2581892, z: 0}
  - {x: 5.7628193, y: -1.324307, z: 0}
  - {x: 5.7912874, y: -1.3898296, z: 0}
  - {x: 5.8200293, y: -1.4544606, z: 0}
  - {x: 5.849335, y: -1.5179026, z: 0}
  - {x: 5.879494, y: -1.5798588, z: 0}
  - {x: 5.910797, y: -1.6400326, z: 0}
  - {x: 5.943533, y: -1.698127, z: 0}
  - {x: 5.977991, y: -1.7538457, z: 0}
  - {x: 6.0144625, y: -1.8068917, z: 0}
  - {x: 6.053236, y: -1.8569677, z: 0}
  - {x: 6.0946016, y: -1.9037775, z: 0}
  - {x: 6.138848, y: -1.9470236, z: 0}
  - {x: 6.186265, y: -1.9864097, z: 0}
  - {x: 6.2371435, y: -2.021639, z: 0}
  - {x: 6.2917724, y: -2.0524144, z: 0}
  - {x: 6.3504415, y: -2.0784392, z: 0}
  - {x: 6.4134398, y: -2.0994167, z: 0}
  - {x: 6.481058, y: -2.1150498, z: 0}
  - {x: 6.553586, y: -2.1250424, z: 0}
  - {x: 6.6313124, y: -2.1290967, z: 0}
  - {x: 6.714527, y: -2.1269164, z: 0}
  - {x: 6.80352, y: -2.1182048, z: 0}
  - {x: 6.8985815, y: -2.1026652, z: 0}
  - {x: 7, y: -2.08, z: 0}
  m_Parameters:
    serializedVersion: 3
    widthMultiplier: 0.5
    widthCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    colorGradient:
      serializedVersion: 2
      key0: {r: 0.8207547, g: 0.8207547, b: 0.8207547, a: 1}
      key1: {r: 1, g: 1, b: 1, a: 1}
      key2: {r: 1, g: 1, b: 1, a: 0}
      key3: {r: 0, g: 0, b: 0, a: 0}
      key4: {r: 0, g: 0, b: 0, a: 0}
      key5: {r: 0, g: 0, b: 0, a: 0}
      key6: {r: 0, g: 0, b: 0, a: 0}
      key7: {r: 0, g: 0, b: 0, a: 0}
      ctime0: 0
      ctime1: 6554
      ctime2: 65535
      ctime3: 0
      ctime4: 0
      ctime5: 0
      ctime6: 0
      ctime7: 0
      atime0: 0
      atime1: 65535
      atime2: 0
      atime3: 0
      atime4: 0
      atime5: 0
      atime6: 0
      atime7: 0
      m_Mode: 0
      m_NumColorKeys: 3
      m_NumAlphaKeys: 2
    numCornerVertices: 0
    numCapVertices: 0
    alignment: 0
    textureMode: 0
    shadowBias: 0
    generateLightingData: 0
  m_UseWorldSpace: 1
  m_Loop: 0
--- !u!4 &2137754276
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2137754273}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -10.08, y: -3.62, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1818202472}
  - {fileID: 741245295}
  - {fileID: 2085962823}
  - {fileID: 56892211}
  - {fileID: 454120795}
  - {fileID: 1435426886}
  - {fileID: 239464958}
  m_Father: {fileID: 0}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
