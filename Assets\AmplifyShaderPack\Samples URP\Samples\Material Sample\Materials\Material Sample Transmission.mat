%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-246189247257170916
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: aa486462e6be1764e89c788ba30e61f7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_DiffusionProfileReferences:
  - {fileID: 11400000, guid: c10ffcee6480e8b4b933e4ed138dfd40, type: 2}
  m_MaterialReferences: []
--- !u!21 &2100000
Material:
  serializedVersion: 6
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Material Sample Transmission
  m_Shader: {fileID: 4800000, guid: d991ffef415f4b9298fd9995cf41b23f, type: 3}
  m_ShaderKeywords: 
  m_LightmapFlags: 2
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: 3000
  stringTagMap:
    MotionVector: User
  disabledShaderPasses:
  - TransparentBackface
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BaseColor:
        m_Texture: {fileID: 2800000, guid: 0f2b32c905e3472db19df9a0a6fb3413, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 2800000, guid: b36d82dce1944357b33e03f7e2975b06, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: 359c4962c0a3407eb969c1d9eeb6426b, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalMap:
        m_Texture: {fileID: 2800000, guid: cd843373d1f443fc9d46351a814d44cf, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SmoothnessMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecularMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TransmissionMaskMap:
        m_Texture: {fileID: 2800000, guid: d2fd9f8b51654f87b55f9b4a55249b1d, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _texcoord:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Floats:
    - _AddPrecomputedVelocity: 1
    - _AlphaCutoff: 0.5
    - _AlphaCutoffEnable: 0
    - _AlphaDstBlend: 0
    - _AlphaSrcBlend: 1
    - _AlphaToMask: 0
    - _AlphaToMaskInspectorValue: 0
    - _BlendMode: 0
    - _Brightness: 1
    - _BumpScale: 1
    - _Cull: 2
    - _CullMode: 2
    - _CullModeForward: 2
    - _Cutoff: 0.5
    - _DetailNormalMapScale: 1
    - _DiffusionProfileHash: 2.686939
    - _DoubleSidedEnable: 0
    - _DoubleSidedGIMode: 0
    - _DoubleSidedNormalMode: 2
    - _DstBlend: 0
    - _EnableBlendModePreserveSpecularLighting: 1
    - _EnableFogOnTransparent: 1
    - _EnvironmentReflections: 1
    - _Gloss: 0.532
    - _GlossMapScale: 1
    - _Glossiness: 0.5
    - _GlossyReflections: 1
    - _Metallic: 0.048
    - _MetallicStrength: 0
    - _Mode: 0
    - _NormalStrength: 1
    - _OcclusionStrength: 1
    - _OcclusionStrengthAO: 0.502
    - _OpaqueCullMode: 2
    - _Parallax: 0.02
    - _QueueControl: 0
    - _QueueOffset: 0
    - _ReceiveShadows: 1
    - _ReceivesSSR: 1
    - _ReceivesSSRTransparent: 0
    - _RenderQueueType: 1
    - _RequireSplitLighting: 0
    - _SmoothnessStrength: 0
    - _SmoothnessTextureChannel: 0
    - _SpecularColorIOR: 0
    - _SpecularColorWeight: 1
    - _SpecularHighlights: 1
    - _SpecularStrength: 0.04
    - _SrcBlend: 1
    - _StencilRef: 0
    - _StencilRefDepth: 8
    - _StencilRefDistortionVec: 4
    - _StencilRefGBuffer: 10
    - _StencilRefMV: 40
    - _StencilWriteMask: 6
    - _StencilWriteMaskDepth: 8
    - _StencilWriteMaskDistortionVec: 4
    - _StencilWriteMaskGBuffer: 14
    - _StencilWriteMaskMV: 40
    - _SupportDecals: 1
    - _SurfaceType: 0
    - _TransmissionMaskFeather: 0.264
    - _TransmissionMaskStrength: 0.543
    - _TransmissionShadow: 0.731
    - _TransmissionStrength: 5.61
    - _TransparentBackfaceEnable: 0
    - _TransparentCullMode: 2
    - _TransparentSortPriority: 0
    - _TransparentWritingMotionVec: 0
    - _TransparentZWrite: 0
    - _UVSec: 0
    - _UseShadowThreshold: 0
    - _ZTestDepthEqualForOpaque: 3
    - _ZTestGBuffer: 4
    - _ZTestTransparent: 4
    - _ZWrite: 1
    - __dirty: 0
    m_Colors:
    - _BaseColor: {r: 1, g: 1, b: 1, a: 0}
    - _Color: {r: 1, g: 1, b: 1, a: 0}
    - _DiffusionProfileHash_Asset: {r: -3.9004634e+28, g: -0.00000043306784, b: -8.828139e+27, a: 7.923471}
    - _DoubleSidedConstants: {r: 1, g: 1, b: -1, a: 0}
    - _EmissionColor: {r: 1, g: 1, b: 1, a: 1}
    - _MainUVs: {r: 1, g: 1, b: 0, a: 0}
    - _SpecularColor: {r: 1, g: 1, b: 1, a: 0}
    - _Transmission: {r: 1, g: 0, b: 0.03393936, a: 0}
    - _TransmissionColor: {r: 1, g: 0.105143145, b: 0, a: 1}
  m_BuildTextureStacks: []
