%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &4069684926287188033
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4069684926287188044}
  - component: {fileID: 4069684926287188035}
  - component: {fileID: 4069684926287188034}
  m_Layer: 0
  m_Name: VisualizerBlock
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4069684926287188044
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4069684926287188033}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -14, y: 0, z: -4}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 4069684926323646899}
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &4069684926287188035
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4069684926287188033}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 50e0d22617aa66044ba0c746fe1d8054, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  ControlPositionX: 0
  PositionX: 0
  ControlPositionY: 0
  PositionY: 0
  ControlPositionZ: 0
  PositionZ: 0
  ControlLocalPositionX: 0
  LocalPositionX: 0
  ControlLocalPositionY: 0
  LocalPositionY: 0
  ControlLocalPositionZ: 0
  LocalPositionZ: 0
  ControlRotationX: 0
  RotationX: 0
  ControlRotationY: 0
  RotationY: 0
  ControlRotationZ: 0
  RotationZ: 0
  ControlLocalRotationX: 0
  LocalRotationX: 0
  ControlLocalRotationY: 0
  LocalRotationY: 0
  ControlLocalRotationZ: 0
  LocalRotationZ: 0
  ControlScaleX: 0
  ScaleX: 0
  ControlScaleY: 1
  ScaleY: 0
  ControlScaleZ: 0
  ScaleZ: 0
--- !u!114 &4069684926287188034
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4069684926287188033}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4520ff861e339d42bae57b95686b14b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  TargetObject: {fileID: 4069684926287188035}
  ControlMode: 3
  AddToInitialValue: 0
  UseUnscaledTime: 1
  RevertToInitialValueAfterEnd: 1
  DrivenLevel: 0
  Curve:
    MMTweenDefinitionType: 0
    MMTweenCurve: 4
    Curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
  MinValue: 0
  MaxValue: 5
  Duration: 1
  PingPongPauseDuration: 0
  Amplitude: {x: 0, y: 5}
  Frequency: {x: 1, y: 1}
  Shift: {x: 0, y: 1}
  RemapNoiseValues: 0
  RemapNoiseZero: 0
  RemapNoiseOne: 1
  OneTimeDuration: 1
  OneTimeAmplitude: 1
  OneTimeRemapMin: 0
  OneTimeRemapMax: 1
  OneTimeCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 0.5
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  DisableAfterOneTime: 0
  DisableGameObjectAfterOneTime: 0
  OneTimeButton: 0
  ToDestinationDuration: 1
  ToDestinationValue: 1
  ToDestinationCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 0.5
      value: 0.6
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  DisableAfterToDestination: 0
  ToDestinationButton: 0
  AudioAnalyzer: {fileID: 0}
  AudioAnalyzerMode: 1
  BeatID: 0
  NormalizedLevelID: 0
  AudioAnalyzerMultiplier: 10
  InitialValue: 0
  CurrentValue: 0
  CurrentValueNormalized: 0
  PingPong: 0
  AttributeNames:
  - <Undefined Attribute>
  - PositionX
  - PositionY
  - PositionZ
  - LocalPositionX
  - LocalPositionY
  - LocalPositionZ
  - RotationX
  - RotationY
  - RotationZ
  - LocalRotationX
  - LocalRotationY
  - LocalRotationZ
  - ScaleX
  - ScaleY
  - ScaleZ
  PropertyName: ScaleY
  ChoiceIndex: 14
--- !u!1 &4069684926323646903
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4069684926323646899}
  - component: {fileID: 4069684926323646898}
  - component: {fileID: 4069684926323646897}
  - component: {fileID: 4069684926323646896}
  m_Layer: 0
  m_Name: Cube
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4069684926323646899
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4069684926323646903}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0.5, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 4069684926287188044}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &4069684926323646898
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4069684926323646903}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &4069684926323646897
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4069684926323646903}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 8713b5054e09e104a9016ff787798af1, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!65 &4069684926323646896
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4069684926323646903}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
