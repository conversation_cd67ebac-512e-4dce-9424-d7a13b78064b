using System;
using System.Collections.Generic;
using System.Reflection;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;

namespace ExtendedImportSettings
{
    public class PublicFieldsOnlyContractResolver : DefaultContractResolver
    {
        protected override IList<JsonProperty> CreateProperties(Type type, MemberSerialization memberSerialization)
        {
            var fields = type.GetFields(BindingFlags.Public | BindingFlags.Instance);
            List<JsonProperty> properties = new List<JsonProperty>();

            foreach (var field in fields)
            {
                var property = CreateProperty(field, memberSerialization);
                properties.Add(property);
            }

            return properties;
        }
    }
}