using UnityEngine;

namespace ExtendedImportSettings
{
    public class PerMeshTemplateData : PerMeshDataBase
    {
        public string Parameter;
    }

    [System.Serializable]
    public class PerMeshTemplateModule : PerMeshModuleBase<PerMeshTemplateData>
    {
        //All parameters are moved to the PerMeshTemplateData class.
        //Here you can place general parameters for the entire model.
        
        protected override void OnPostprocessModelPerMesh(Mesh mesh, PerMeshTemplateData data,
            GameObject meshGameObject, GameObject rootGameObject)
        {
            //Here, all necessary operations with the mesh are performed.
        }
    }
}