%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: PBR Core
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor v1.9.6.2\n// Available at the
    Unity Asset Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=19602\nNode;AmplifyShaderEditor.CommentaryNode;700;-1248,3744;Inherit;False;2838.317;2659.731;INDIRECT
    LIGHTING;2;551;550;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;197;-2752,-2320;Inherit;False;2744.866;1012.537;Alpha;46;158;159;156;260;259;143;126;133;117;118;116;141;119;142;154;166;144;145;146;147;148;149;172;167;165;164;196;110;152;173;189;188;186;187;184;153;102;101;99;98;95;93;92;91;195;1171;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;550;-1184,5248;Inherit;False;2327.253;1051.619;LightProbe
    Contribution;31;891;651;608;650;793;62;646;666;644;641;676;681;680;679;677;678;675;674;667;622;610;607;606;605;601;682;561;556;1174;1296;1297;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;551;-1200,3792;Inherit;False;2698.913;1429.802;Reflection
    Probe Contribution;47;766;775;652;569;584;774;576;771;772;768;574;668;60;764;765;600;59;648;599;598;645;597;595;594;647;653;579;578;577;567;566;565;770;769;575;767;61;560;558;557;794;804;805;1172;1173;958;957;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;467;1664,-496;Inherit;False;2555.863;1200.595;Jordan
    PBR Light Model;55;0;31;900;902;899;901;911;896;1109;908;782;784;790;789;1108;1107;1106;1105;1104;1103;1102;1101;1100;783;785;744;739;659;740;658;786;76;787;77;788;404;204;449;202;67;66;883;24;914;913;912;898;897;895;905;910;909;894;893;892;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;239;2080,1552;Inherit;False;1896.406;1351.646;NORMAL
    DISTRIBUTION;74;1241;262;1008;1221;1224;1260;1259;1258;1257;1256;1255;1254;1253;1252;1223;1251;1250;1249;1248;1247;1246;1245;1244;1243;1227;1225;1242;1187;1188;1189;1190;1192;1240;1239;1238;1237;1236;1005;1006;1232;995;1233;1208;1235;1234;1007;1004;1003;991;1002;200;1206;992;863;869;865;864;862;866;867;240;870;871;872;873;874;875;860;858;857;853;1182;419;1291;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;219;-3312,-1200;Inherit;False;1592.971;729.9622;UVs;16;1;4;3;5;795;6;534;724;723;536;535;532;531;530;533;2;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;214;1664,768;Inherit;False;1338.011;619.8185;FRESNEL
    TERM;13;201;215;1111;1114;1112;503;1110;505;205;878;877;504;1295;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;212;-3328,-432;Inherit;False;1303.589;1365.432;Dot
    Products;65;967;1164;968;1142;1141;1126;1125;975;974;655;882;1162;1139;1121;210;880;966;1118;1117;1161;1170;1168;1166;1157;1155;1158;1156;1154;1153;1150;1152;1151;1149;1148;1147;1145;1144;1143;1138;1137;1136;1135;1119;1129;1130;1131;1132;1120;1128;1127;1123;879;1115;1116;208;973;972;969;962;970;963;964;965;971;1175;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;195;-1792,-2256;Inherit;False;580;194.8782;Sharpens
    the alpha to the width of a single pixel;6;194;180;179;178;177;193;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;13;-1248,1552;Inherit;False;3240.618;2162.966;GEOMETRIC
    SHADOWING FUNCTION;152;65;1280;1281;1279;927;1277;20;18;1275;1274;1273;1270;1278;1269;1268;1267;1266;1265;932;1060;1028;1099;1098;1097;1096;1095;1094;1093;1092;1091;1090;1089;1088;1087;1086;1085;1084;1083;1082;1081;1080;1079;1078;1077;1076;211;1075;1074;1073;1072;1071;1070;1069;1068;1067;1066;1065;1064;1063;1062;1061;846;1059;838;837;1058;1057;1056;1055;1054;1052;1050;1049;1048;1047;1046;1045;1044;1043;1042;1041;1040;1039;1038;1037;1036;1035;1034;1032;1031;1030;1029;1027;1026;1025;1024;978;977;976;961;209;1023;1022;817;819;822;808;960;959;830;829;828;821;824;823;820;818;825;826;816;813;811;812;810;362;841;839;840;254;845;844;842;843;852;851;850;849;848;847;836;835;833;832;361;255;213;807;806;809;1210;1209;933;;0.02830189,0.02830189,0.02830189,1;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;80;-1632,-384;Inherit;False;198;100;BaseColor
    Map;;0,0,0,1;_MainTex;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;81;-1616,-32;Inherit;False;200;100;Normal
    Map;;0,0,0,1;_BumpMap;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;82;-1600,288;Inherit;False;197;100;Specular
    Map;;0,0,0,1;_SpecularMap;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;85;480,-640;Inherit;False;150;100;Base
    Color;;0,0,0,1;_BaseColor;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;86;432,-224;Inherit;False;256;104;Brightness;;0,0,0,1;_Brightness;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;87;-608,112;Inherit;False;261;117;Normal
    Strength;;0,0,0,1;_NormalStrength;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;91;-400,-2192;Inherit;False;321.4203;115.3568;Alpha;;0,0,0,1;Defines
    material's alpha value. Used for transparency and/or alpha clip. Expected range
    0 - 1.$;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;92;-2384,-1824;Inherit;False;338.1846;103.4977;;;0,0,0,1;The
    result of this dot product is white when the faces are pointed toward the camera,
    and black when they're pointed away.;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;93;-400,-1904;Inherit;False;319.5595;104.7868;Alpha
    Clip Threshold;;0,0,0,1;Fragments with an alpha below this value are discarded.
    Expected range 0 - 1.$;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;95;-944,-1776;Inherit;False;252.0156;102.7698;_Cutoff;;0,0,0,1;HDRP
    Hidden in Inspector for GUI;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;98;-976,-1440;Inherit;False;256.5154;100;_AlphaCutoffShadow;;0,0,0,1;HDRP
    Hidden in Inspector for GUI;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;99;-400,-1520;Inherit;False;319.1143;106.021;Alpha
    Clip Threshold Shadow;;0,0,0,1;URP HDRP;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;101;-1680,-1856;Inherit;False;312.8433;102;Alpha
    Cutoff Bias;;0,0,0,1;Fragments with an alpha below this value are discarded.
    Expected range 0 - 1.$;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;102;-1248,-1536;Inherit;False;250.5098;100;Clip
    Glancing Angle;;0,0,0,1;Opacity Mask Glancing Angles;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;153;-1312,-1856;Inherit;False;267.7893;120.4925;;;0.009433985,0.009433985,0.009433985,1;Set
    UNITY_PASS_SHADOWCASTER to False so shadows do not get included in clip for glancing
    angles;0;0\nNode;AmplifyShaderEditor.ClipNode;184;-1168,-2272;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;187;-1216,-2144;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;193;-1520,-2224;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleDivideOpNode;177;-1456,-2176;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;178;-1328,-2176;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0.5;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FWidthOpNode;179;-1776,-2160;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMaxOpNode;180;-1600,-2160;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0.0001;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;194;-1504,-2208;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SaturateNode;186;-976,-2272;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;188;-1216,-2048;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;189;-1360,-2048;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;173;-1984,-2256;Inherit;False;79;Alpha;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;152;-784,-2112;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitchByPipeline;110;-624,-2272;Inherit;False;4;0;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;196;-976,-2192;Inherit;False;79;Alpha;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.DdyOpNode;164;-2512,-1712;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.DdxOpNode;165;-2512,-1632;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.NormalizeNode;167;-2208,-1712;Inherit;False;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.DotProductOpNode;149;-2048,-1712;Inherit;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.AbsOpNode;148;-1920,-1712;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;147;-1792,-1712;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;146;-1632,-1728;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;145;-1488,-1728;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;144;-1488,-1648;Inherit;False;Constant;_Float1;Float
    1;18;0;Create;True;0;0;0;False;0;False;1;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.CrossProductOpNode;166;-2384,-1712;Inherit;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitchByPipeline;154;-784,-1984;Inherit;False;4;0;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.LerpOp;142;-960,-2112;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitchByPipeline;119;-656,-1600;Inherit;False;4;0;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;141;-1152,-2112;Inherit;False;Constant;_Constant;Constant;15;0;Create;True;1;;0;0;False;0;False;1;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;118;-976,-1600;Half;False;Property;_AlphaCutoffBiasShadow;Alpha
    Cutoff Bias Shadow;3;0;Create;False;1;;0;0;False;0;False;0.5;0.9;0.01;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;117;-976,-1520;Half;False;Property;_AlphaCutoffShadow;AlphaCutoffShadow;4;1;[HideInInspector];Create;False;0;0;0;False;0;False;0.5;0.851;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.StaticSwitch;143;-1328,-1728;Inherit;False;Property;_Keyword1;Keyword
    0;16;0;Create;True;0;0;0;False;0;False;0;0;0;False;UNITY_PASS_SHADOWCASTER;Toggle;2;Key0;Key1;Fetch;False;True;All;9;1;FLOAT;0;False;0;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;259;-688,-2160;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;260;-704,-2240;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;33;-1088,-288;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;375;-1088,-288;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.SwizzleNode;39;-704,-384;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.StickyNoteNode;399;-688,496;Inherit;False;397.9531;114.4154;Metallic
    Strength;;0,0,0,1;_MetallicStrength$$value 0 being dielectric (non-metallic)
    & 1 full metal;0;0\nNode;AmplifyShaderEditor.WireNode;40;-1088,336;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.StickyNoteNode;416;-32,912;Inherit;False;286.7477;102;Smoothness
    Strength;;0,0,0,1;_SmoothnessStrength;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;419;2160,2240;Inherit;False;305.4297;137.1641;Anisotropic;;0,0,0,1;Use
    the float to set the direction of the anisotropy effect. Negative values make
    the effect vertical, and positive values make the effect horizontal.  This stretches
    the anisotropic highlights in the given direction.;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;424;-992,1216;Inherit;False;242.2029;132.5147;;;0,0,0,1;Use
    Min Node instead of Multiply so result is whichever result is darker. This will
    help prevent AO from getting too dark in blending. ;0;0\nNode;AmplifyShaderEditor.SwizzleNode;55;-720,288;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;379;-1088,656;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.SamplerNode;10;-1008,-32;Inherit;True;Property;_TextureSample14;Texture
    Sample 14;34;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;6;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT3;5\nNode;AmplifyShaderEditor.SamplerNode;35;-1008,-384;Inherit;True;Property;_TextureSample13;Texture
    Sample 13;33;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;6;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT3;5\nNode;AmplifyShaderEditor.SamplerNode;49;-1024,288;Inherit;True;Property;_TextureSample19;Texture
    Sample 17;37;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;6;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT3;5\nNode;AmplifyShaderEditor.UnpackScaleNormalNode;12;-384,-32;Inherit;False;Tangent;2;0;FLOAT4;0,0,0,0;False;1;FLOAT;1;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.StickyNoteNode;504;1744,1184;Inherit;False;248.1001;100;IOR
    fresnel;;0,0,0,1;Range 1-4;0;0\nNode;AmplifyShaderEditor.TexCoordVertexDataNode;2;-3008,-1152;Inherit;False;0;2;0;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.TexCoordVertexDataNode;533;-3232,-944;Inherit;False;0;2;0;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.TexCoordVertexDataNode;530;-3232,-832;Inherit;False;1;2;0;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.TexCoordVertexDataNode;531;-3232,-720;Inherit;False;2;2;0;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.TexCoordVertexDataNode;532;-3232,-608;Inherit;False;3;2;0;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.CustomExpressionNode;535;-2976,-1024;Inherit;False;if(m_switch
    ==0)$\treturn m_UV0@$else if(m_switch ==1)$\treturn m_UV1@$else if(m_switch ==2)$\treturn
    m_UV2@$else if(m_switch ==3)$\treturn m_UV3@$else$return float2(0,0)@;2;Create;5;True;m_switch;FLOAT;0;In;;Inherit;False;True;m_UV0;FLOAT2;0,0;In;;Inherit;False;True;m_UV1;FLOAT2;0,0;In;;Inherit;False;True;m_UV2;FLOAT2;0,0;In;;Inherit;False;True;m_UV3;FLOAT2;0,0;In;;Inherit;False;float2
    switch UV Mode;True;False;0;;False;5;0;FLOAT;0;False;1;FLOAT2;0,0;False;2;FLOAT2;0,0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.FunctionSwitch;536;-2736,-1152;Inherit;False;Enable
    UV Mode;True;0;2;5;In 0;In 1;Object;-1;10;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.StickyNoteNode;541;-192,-624;Inherit;False;184.5881;105.1591;Perceptual
    Weights;;0,0,0,1;Grayscale using a dot product with the perceptual weights.;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;542;-480,-224;Inherit;False;356.4791;128.7434;Saturation;;0.1792453,0.1792453,0.1792453,1;_Saturation$$Blend
    between the grayscale and the original version using the Saturation as the Alpha
    value.;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;556;-848,5552;Inherit;False;288.7295;102;Lambert
    Half;;0,0,0,1;Diffuse Lighting is set with Lamber Half vs Lambert to reduce the
    rear of objects looking flat;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;557;-112,4864;Inherit;False;256.5498;104.5558;Indirect
    Specular ;;0,0,0,1;_IndirectSpecular$Range 0-1;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;558;-752,4896;Inherit;False;260;106;Indirect
    Specular Smoothness;;0,0,0,1;_IndirectSpecularSmoothness$Range 0-1;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;560;-1024,3840;Inherit;False;212.6338;100;Indirect
    Specular Color;;0,0,0,1;_IndirectSpecularColor;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;561;-320,5520;Inherit;False;252.2357;100;Indirect
    Diffuse;;0,0,0,1;_IndirectDiffuse$Range 0-1;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;682;-480,5968;Inherit;False;324.9874;101.5352;Lightmap
    UVs;;0,0,0,1;Static (UV1) unity_LightmapST$Dynamic (UV2) unity_DynamicLightmapST;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;696;-512,1392;Inherit;False;259.4089;104.333;Smoothness
    Strength;;0,0,0,1;_OcclusionStrengthAO;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;699;112,1216;Inherit;False;142.434;100.3531;;;0.1037736,0.09741012,0.09741012,1;Saturate
    AO to prevent Nan in SRP;0;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;64;-528,288;Inherit;False;Specular
    Map;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.StickyNoteNode;723;-2672,-816;Inherit;False;193;129;Main
    UVs;;0,0,0,1;_MainUVs;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;724;-3248,-1136;Inherit;False;196;102;UV
    Mode;;0,0,0,1;_UVMode;0;0\nNode;AmplifyShaderEditor.RangedFloatNode;11;-608,32;Half;False;Property;_NormalStrength;Normal
    Strength;18;0;Create;False;1;;0;0;False;0;False;1;1;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;545;128,-384;Inherit;False;Enable
    Color Saturation;True;1;2;6;In 0;In 1;Object;-1;10;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;734;-96,-352;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.Vector3Node;540;-448,-512;Inherit;False;Constant;_PerceptualWeights;Perceptual
    Weights;0;0;Create;True;0;0;0;False;0;False;0.2126729,0.7151522,0.072175;0,0,0;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.DotProductOpNode;537;-192,-512;Inherit;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;736;-256,-384;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;733;-272,-352;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;538;-32,-320;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;735;-80,-320;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.OneMinusNode;544;-208,-304;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;543;-480,-304;Inherit;False;Property;_Saturation;Saturation;6;0;Create;False;0;0;0;False;0;False;0;0;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;79;-704,-272;Inherit;False;Alpha;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;48;736,-416;Inherit;False;3;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RangedFloatNode;45;432,-304;Half;False;Property;_Brightness;Brightness;7;0;Create;False;1;;0;0;False;0;False;1;1;0;2;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;745;-80,560;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;742;96,496;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;743;256,496;Inherit;False;MetallicHalf;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;753;-496,816;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.LerpOp;750;-256,768;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;752;-656,784;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;688;-656,784;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;414;256,768;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;413;-32,832;Half;False;Property;_SmoothnessStrength;Smoothness
    Strength;15;0;Create;False;0;0;0;False;0;False;0.5;0;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;741;-224,496;Inherit;False;Constant;_Float0;Float
    0;30;0;Create;True;0;0;0;False;0;False;0.5;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;796;-1104,-928;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;797;-1104,-928;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;798;-1104,-928;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;799;-1104,-928;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;800;-1088,-384;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;801;-1088,-32;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;802;-1088,288;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;803;-1088,640;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;6;-2096,-1008;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.VertexToFragmentNode;795;-1952,-1008;Inherit;False;False;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;5;-2272,-1152;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SwizzleNode;3;-2432,-1072;Inherit;False;FLOAT2;0;1;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SwizzleNode;4;-2432,-992;Inherit;False;FLOAT2;2;3;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.StickyNoteNode;804;448,5056;Inherit;False;236.4668;103.2;Bug
    #220;;0.8773585,0.0289694,0.0289694,1;Indirect Specular Light Node Breaking in
    URP Forward+$$;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;805;-48,4160;Inherit;False;236.4668;103.2;Bug
    #310;;0.8773585,0.0289694,0.0289694,1;URP Unlit breaking with Light Attenuation
    x Light Color$$$;0;0\nNode;AmplifyShaderEditor.RangedFloatNode;534;-3168,-1024;Inherit;False;Property;_UVMode;UV
    Mode;10;1;[Enum];Create;False;0;4;UV0;0;UV1;1;UV2;2;UV3;3;0;False;0;False;0;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.Vector4Node;1;-2672,-992;Inherit;False;Property;_MainUVs;Main
    UVs;11;0;Create;False;0;0;0;False;0;False;1,1,0,0;1,1,0,0;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.RangedFloatNode;126;-1264,-1616;Half;False;Property;_GlancingClipMode;Enable
    Clip Glancing Angle;0;2;[Header];[ToggleUI];Create;False;1;ALPHA;0;0;False;0;False;0;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;116;-944,-1856;Inherit;False;Property;_Cutoff;Cutoff;1;1;[HideInInspector];Create;False;1;;0;0;False;0;False;0;0.9;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;133;-1680,-1936;Half;False;Property;_AlphaCutoffBias;Alpha
    Cutoff Bias;2;0;Create;False;1;;0;0;False;0;False;0.5;0.9;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.TexturePropertyNode;28;-1360,-384;Inherit;True;Property;_MainTex;BaseColor
    Map;8;2;[Header];[SingleLineTexture];Create;False;1;SURFACE INPUTS;0;0;False;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.TexturePropertyNode;9;-1344,-32;Inherit;True;Property;_BumpMap;Normal
    Map;17;2;[Normal];[SingleLineTexture];Create;False;1;;0;0;False;1;Space(10);False;None;None;True;bump;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.TexturePropertyNode;42;-1344,288;Inherit;True;Property;_SpecularMap;Specular
    Map;9;1;[SingleLineTexture];Create;False;0;0;0;False;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.StickyNoteNode;885;-1632,640;Inherit;False;257.5952;261.6046;Main
    Mask Map;;0,0,0,1;_MainMaskMap$$MSO$R = Metallic$G = Smoothness $B = Occlusion$$MRO$R
    = Metallic$G = Roughness $B = Occlusion;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;889;-480,992;Inherit;False;301.0374;131.9005;Main
    Mask Type;;0,0,0,1;_MainMaskType$$Converts Map from MSO to MRO$If no map is added
    use MSO only;0;0\nNode;AmplifyShaderEditor.RangedFloatNode;431;-512,1312;Half;False;Property;_OcclusionStrengthAO;Occlusion
    Strength;16;0;Create;False;0;0;0;False;0;False;0;0;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;671;-224,1312;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;691;-640,1168;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.VertexColorNode;426;-736,1216;Inherit;False;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.LerpOp;428;-48,1136;Inherit;False;3;0;FLOAT;1;False;1;FLOAT;1;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;690;-656,1136;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SamplerNode;473;-1024,640;Inherit;True;Property;_TextureSample16;Texture
    Sample 16;36;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;6;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT3;5\nNode;AmplifyShaderEditor.OneMinusNode;751;-448,832;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;887;-480,912;Inherit;False;Property;_MainMaskType;Main
    Mask Type;12;1;[Enum];Create;False;0;2;MSO;0;MRO;1;0;False;0;False;0;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.TexturePropertyNode;884;-1360,640;Inherit;True;Property;_MainMaskMap;Main
    Mask Map;13;1;[SingleLineTexture];Create;False;1;;0;0;False;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.StickyNoteNode;892;2624,176;Inherit;False;275;138;Shadow
    Color;;0,0,0,0.5019608;_ShadowColor$$Alpha is used as to either allow the Base
    Color into the shadow or not;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;893;2944,512;Inherit;False;230;100;Enable
    Shadow Color;;0,0,0,1;_ShadowColorEnable;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;894;3040,192;Inherit;False;186;100;Occlusion;;0,0,0,0.5019608;It
    should still override Shadow Color;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;909;3680,-320;Inherit;False;244.4735;115.7456;Enable
    Shadow Color;;0,0,0,1;;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;910;3488,-96;Inherit;False;422.2702;192.3879;Shadow
    Color \"Blending\";;0,0,0,0.5019608;unfortunately due to jordan's Light Model
    Design/Calculations, \"Coloring\" Shadows is not possible as the color is basically
    removed in the process, so the current solution is used or the light model is
    reworked to work with shadow colors (which could require a complete rewrite,
    at that point would be better to just use a different light model);0;0\nNode;AmplifyShaderEditor.LerpOp;905;3472,-240;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;895;3232,-16;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;897;2896,-96;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;898;3072,-16;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;912;3424,-128;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;913;3392,-16;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;914;3392,-160;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.StickyNoteNode;933;-1232,2192;Inherit;False;325;194;Roughness;;0,0,0,0.5019608;Roughness
    from texture and other calculations should be used for Shadows instead of a flat
    value (Shadow Strength moved as a result), where the shadows could end up looking
    the same regardless of smoothness/roughness Texture Map (which is not desired);0;0\nNode;AmplifyShaderEditor.ColorNode;24;2624,-16;Inherit;False;Property;_ShadowColor;Shadow
    Color;25;1;[HDR];Create;False;0;0;0;False;0;False;0.3113208,0.3113208,0.3113208,0;1,1,1,0;True;True;0;6;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT3;5\nNode;AmplifyShaderEditor.ColorNode;37;480,-528;Half;False;Property;_BaseColor;Base
    Color;5;1;[Header];Create;False;1;COLOR;0;0;False;0;False;1,1,1,0;1,1,1,0;True;False;0;6;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT3;5\nNode;AmplifyShaderEditor.RegisterLocalVarNode;63;928,-416;Inherit;False;BaseColor
    Map;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMinOpNode;425;-464,1152;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;400;-256,656;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;694;-656,816;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SaturateNode;434;112,1136;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;435;288,1136;Inherit;False;Occlusion;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;66;1728,-432;Inherit;False;63;BaseColor
    Map;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;67;1728,-352;Inherit;False;64;Specular
    Map;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;202;1760,-272;Inherit;False;200;Specular;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;449;1952,-352;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;204;1728,-192;Inherit;False;201;Fresnel
    Term;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;404;1760,-112;Inherit;False;403;Metallic;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;788;2000,-256;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;77;1728,-32;Inherit;False;417;Smoothness
    ;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;787;2016,-240;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;76;1760,48;Inherit;False;65;Shadow
    ;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;786;2032,-224;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;658;1728,128;Inherit;False;600;Indirect
    Specular;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;740;1696,224;Inherit;False;63;BaseColor
    Map;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;659;1696,304;Inherit;False;644;Indirect
    Diffuse;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;739;1952,224;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;744;1728,384;Inherit;False;743;MetallicHalf;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;785;2096,224;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;783;2096,-176;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1100;1936,352;Inherit;False;208;_NdotL;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1101;1936,432;Inherit;False;210;_NdotV;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1102;1936,512;Inherit;False;972;_LdotH;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1103;2128,-160;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1104;2144,-144;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1105;2160,-128;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1106;2160,512;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1107;2144,416;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1108;2128,352;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;789;1984,-272;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;790;1968,-304;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;784;2048,112;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;782;2064,-192;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitch;908;3680,-432;Inherit;False;Enable
    Shadow Color;True;1;2;7;In 0;In 1;Object;-1;10;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1109;2864,80;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;896;3040,112;Inherit;False;435;Occlusion;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;911;2624,-96;Inherit;False;63;BaseColor
    Map;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.OneMinusNode;901;3056,336;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;899;3232,336;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;902;3424,336;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;900;2880,336;Inherit;False;65;Shadow
    ;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;31;2944,432;Inherit;False;Property;_ShadowColorEnable;Enable
    Shadow Color;24;2;[Header];[ToggleUI];Create;False;1;SHADOW COLOR;0;0;False;1;Space(5);False;0;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;14;-96,-32;Inherit;False;Normal
    Map;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;1160;96,64;Inherit;False;WorldNormalTangent;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1171;-2256,-1600;Inherit;False;1115;_ViewDir;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WorldPosInputsNode;172;-2720,-1712;Inherit;False;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.StickyNoteNode;1175;-3312,352;Inherit;False;516.533;397.6427;Dot
    Product List;;0,0,0,0.5019608;Inorder as they Appear here.$$HdotH - dot( Half
    Vector, Half Vector )$//Anistropic Dot Products$HdotX - dot( Half Vector, World
    Tangent)$HdotY - dot( Half Vector, World Bitangent)$$//Standard Dot Products
    (HdotH is as well)$LdotH - dot( Light Dir, Half Vector )$LdotV - dot( Light Dir,
    View Dir )$NdotL - dot( Normal, Light Dir )$NdotV - dot( Normal, View Dir )$NdotH
    - dot( Normal, Half Vector )$RdotV - dot( Light Reflect Dir, View Dir )$VdotH
    - dot( View Dir, Half Vector )$$*Half Vector = (Light Dir + View Dir)$*for the
    Anistropic Dot Products consider HdotX for the vertical Anisotropic and HdotY
    for the Horizontal one;0;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;971;-2320,-32;Inherit;False;_HdotY;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionNode;965;-2560,64;Inherit;False;LdotH;-1;;61185;0364555185933b049a6a96d60ccefd1a;4,385,0,460,1,451,1,461,0;4;443;FLOAT3;0,0,0;False;462;FLOAT3;0,0,0;False;454;FLOAT3;0,0,0;False;419;FLOAT;1E-37;False;1;FLOAT;112\nNode;AmplifyShaderEditor.FunctionNode;964;-2560,-32;Inherit;False;HdotY;-1;;61186;1c82aeabcbdbf7041b87a9a5449f37a1;5,453,1,462,1,463,0,454,0,465,0;5;445;FLOAT3;0,0,0;False;464;FLOAT3;0,0,0;False;456;FLOAT3;0,0,0;False;455;FLOAT3;0,0,0;False;466;FLOAT3;0,0,0;False;1;FLOAT;300\nNode;AmplifyShaderEditor.FunctionNode;963;-2560,-128;Inherit;False;HdotX;-1;;61187;e4a7c169e85b67f408600ac15b2fd149;5,443,1,444,1,121,0,445,0,446,0;5;440;FLOAT3;0,0,0;False;439;FLOAT3;0,0,0;False;130;FLOAT3;0,0,0;False;441;FLOAT3;0,0,0;False;447;FLOAT3;0,0,0;False;1;FLOAT;299\nNode;AmplifyShaderEditor.RegisterLocalVarNode;970;-2320,-128;Inherit;False;_HdotX;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionNode;962;-2560,-224;Inherit;False;HdotH;-1;;61188;539961ce417869d4abdd54ee29639f41;5,393,0,443,1,445,1,121,0,447,0;5;440;FLOAT3;0,0,0;False;439;FLOAT3;0,0,0;False;130;FLOAT3;0,0,0;False;446;FLOAT3;0,0,0;False;422;FLOAT;1E-37;False;1;FLOAT;296\nNode;AmplifyShaderEditor.RegisterLocalVarNode;969;-2320,-224;Inherit;False;_HdotH;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;972;-2320,64;Inherit;False;_LdotH;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;973;-2320,160;Inherit;False;_LdotV;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;208;-2320,256;Inherit;False;_NdotL;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;1116;-2560,-304;Inherit;False;_LightDir;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;1115;-2560,-384;Inherit;False;_ViewDir;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionNode;879;-2560,256;Inherit;False;NdotL;-1;;61189;6d7ee1aa8c79587448475fa2b37affc1;5,27,1,381,0,460,1,461,0,463,1;4;230;FLOAT3;0,0,1;False;462;FLOAT3;0,0,0;False;454;FLOAT3;0,0,0;False;416;FLOAT;1E-37;False;1;FLOAT;111\nNode;AmplifyShaderEditor.WireNode;1123;-2704,352;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1127;-2704,144;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1128;-2704,48;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1120;-2704,-48;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1132;-2720,-160;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1131;-2720,-160;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1130;-2720,-160;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1129;-2720,-160;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1119;-2720,-160;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1135;-2720,-160;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1136;-2720,-160;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1137;-2720,-160;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1138;-2704,256;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1143;-2704,160;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1144;-2720,160;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1145;-2720,160;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1147;-2720,160;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1148;-2720,160;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1149;-2720,-128;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1151;-2704,32;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1152;-2720,96;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1150;-2704,-48;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1153;-2720,96;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1154;-2720,96;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1156;-2720,96;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1158;-2736,-224;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1155;-2720,-240;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1157;-2720,-320;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1166;-2704,352;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1168;-2720,336;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1170;-2720,336;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1161;-3008,256;Inherit;False;1160;WorldNormalTangent;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.ViewDirInputsCoordNode;1117;-2944,-224;Inherit;False;World;True;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.WorldSpaceLightDirHlpNode;1118;-2976,96;Inherit;False;True;1;0;FLOAT;0;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.FunctionNode;966;-2560,160;Inherit;False;LdotV;-1;;61190;955d8291c53b8884e9aea1cb0845e13a;4,389,0,460,1,451,1,461,0;4;443;FLOAT3;0,0,0;False;462;FLOAT3;0,0,0;False;454;FLOAT3;0,0,0;False;420;FLOAT;1E-37;False;1;FLOAT;113\nNode;AmplifyShaderEditor.FunctionNode;880;-2560,352;Inherit;False;NdotV;-1;;61191;65be4ec269a534f4e8867af2f999aaa9;4,27,1,377,0,452,1,451,1;3;230;FLOAT3;0,0,1;False;443;FLOAT3;0,0,0;False;415;FLOAT;1E-37;False;1;FLOAT;23\nNode;AmplifyShaderEditor.FunctionNode;883;2192,-432;Inherit;False;PBR
    Light Model;-1;;61195;2a2ee1a57fbb6b24d8f10c234027eef7;6,704,1,665,1,387,1,388,1,630,1,781,0;15;782;FLOAT3;1,1,1;False;51;FLOAT3;0,1,1;False;80;FLOAT3;0,1,0;False;351;FLOAT3;0,0,1;False;491;FLOAT;1;False;634;FLOAT;1;False;712;FLOAT;0.75;False;708;FLOAT;0.5;False;710;FLOAT;0.5;False;490;FLOAT;1;False;773;FLOAT3;1,1,1;False;775;FLOAT3;1,1,1;False;381;FLOAT;0;False;380;FLOAT;0;False;631;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.StickyNoteNode;1182;3200,1856;Inherit;False;494.0259;262.8664;List
    of Dot Products Used for NDFs;;0,0,0,0.5019608;in order as they appear in the
    inputs of the NDFs.$$-NdotH for All but Bling-Phong$$-RdotV for Bling-Phong$$-HdotX
    & HdotY for Both TrowBridge Reitz & Ward Anisotropic$$-NdotL & NdotV for Ward
    Anisotropic$$read \"Dot Products List\" notes for details per Dot Product;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;1209;-1232,2400;Inherit;False;331.3958;296.8188;List
    Dot Products used For GSFs;;0,0,0,0.5019608;in order as they appear in the inputs
    of the GSFs (All GSF Groups included).$$-NdotL & NdotV for All but Deur$$-LdotH
    for Ashikhmin Shirley$$-NdotH for Cook Torrance & Duer$$-VdotH for Cook Torrance,
    Kelemen, Kurt$$-HdotH for Deur$$read \"Dot Products List\" notes for details
    per Dot Product;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;1210;-224,2800;Inherit;False;478;365.1208;Nan
    or extreme values (Fixed now);;0,0,0,0.5019608;some GSFs are outputing harsh/Extreme
    values that leading to \"unpleasant\" blending with shadow color at 'Low/Zero
    Roughness'.$$list of current GSFs with this issue:$- GGX$- Schlick Beckman$-
    Schlick GGX$- Schlick$- Smith Beckman$- Smith GGX$$this can be easily with the
    Step node in editor ( EX: step(1, GSF) ), tho this could be fixed similar like
    the NDFs by adding a Max of .0001 or a 'Non Zero' value.$$Note: Above Issues
    have been resolved, this note can kept or deleted as needed;0;0\nNode;AmplifyShaderEditor.FunctionNode;809;-800,1968;Inherit;False;GSF
    Duer;-1;;61203;c9b881f11d80e6545b8c3a0298f7ef0b;2,325,1,347,1;3;300;FLOAT3;0,0,1;False;320;FLOAT;0;False;346;FLOAT;0;False;1;FLOAT;217\nNode;AmplifyShaderEditor.FunctionNode;806;-800,1616;Inherit;False;GSF
    Ashikhmin Premoze;-1;;61206;64f9784c6240efd4f81eb8291d478ea8;2,340,1,339,1;3;373;FLOAT3;0,0,1;False;342;FLOAT;0;False;341;FLOAT;0;False;1;FLOAT;216\nNode;AmplifyShaderEditor.FunctionNode;807;-800,1712;Inherit;False;GSF
    Ashikhmin Shirley;-1;;61209;dbf6de06038356c4ea88032d7ee8fef0;3,270,1,313,1,271,1;4;304;FLOAT3;0,0,1;False;273;FLOAT;0;False;272;FLOAT;0;False;315;FLOAT;0;False;1;FLOAT;215\nNode;AmplifyShaderEditor.FunctionSwitch;213;-256,1616;Inherit;False;GEOMETRIC
    SHADOWING FUNCTION A;False;6;10;1;GSF Ashikhmin Premoze;GSF Ashikhmin Shirley;GSF
    CookTorrance;GSF Duer;GSF GGX;GSF Implicit;GSF Kelemen Modified;GSF Kelemen;GSF
    Kurt;GSF Neumann;Object;-1;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;255;192,1616;Inherit;False;GEOMETRIC
    SHADOWING FUNCTION;False;0;2;0;Model Group A;Model Group B;Object;-1;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;361;144,1712;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionNode;832;-800,2928;Inherit;False;GSF
    Schlick GGX;-1;;61213;fbea8e7af23c86b49a04cab2025971bc;2,106,1,107,1;4;70;FLOAT3;0,0,1;False;100;FLOAT;0;False;99;FLOAT;0;False;23;FLOAT;0.8;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionNode;833;-800,3056;Inherit;False;GSF
    Schlick;-1;;61216;140896541c8985d47bbf7c502df6a48b;4,35,0,121,1,122,1,39,0;5;85;FLOAT3;0,0,1;False;115;FLOAT;0;False;114;FLOAT;0;False;150;FLOAT;0.7978846;False;23;FLOAT;0.5;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionNode;835;-800,3184;Inherit;False;GSF
    Smith Beckman;-1;;61219;293fdf85bd5693d4d98d6dcc4aa80874;2,142,1,143,1;4;106;FLOAT3;0,0,1;False;136;FLOAT;0;False;135;FLOAT;0;False;17;FLOAT;0.5;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionNode;836;-800,3312;Inherit;False;GSF
    Smith GGX;-1;;61222;f6f676824eb163f43b7e1693d93e3426;2,52,1,51,1;4;55;FLOAT3;0,0,1;False;49;FLOAT;0;False;48;FLOAT;0;False;24;FLOAT;0.5;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;847;-384,3408;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;848;-384,3280;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;849;-384,3152;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;850;-384,2992;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;851;-384,2880;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;852;-400,2768;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;843;-304,2736;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;842;-320,2720;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;844;-288,2752;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;845;-272,2768;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;254;-224,2576;Inherit;False;GEOMETRIC
    SHADOWING FUNCTION B;False;0;7;2;GSF Schlick Beckman;GSF Schlick GGX;GSF Schlick;GSF
    Smith Beckman;GSF Smith GGX;GSF Walter et all;GSF Ward;Object;-1;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;840;-352,2688;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;839;-368,2672;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;841;-336,2704;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;362;144,2576;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionNode;810;-800,2192;Inherit;False;GSF
    Implicit;-1;;61225;9370c6f58d8077849aae76c2eb73268f;2,270,1,271,1;3;304;FLOAT3;0,0,1;False;273;FLOAT;0;False;272;FLOAT;0;False;1;FLOAT;214\nNode;AmplifyShaderEditor.FunctionNode;812;-800,2288;Inherit;False;GSF
    Kelemen Modified;-1;;61228;4e99cc23c5577484280ad36854b59092;3,395,1,400,1,401,1;5;434;FLOAT3;0,0,1;False;403;FLOAT;0;False;402;FLOAT;0;False;474;FLOAT;0.7978846;False;242;FLOAT;0.5;False;2;FLOAT;225;FLOAT;396\nNode;AmplifyShaderEditor.FunctionNode;811;-800,2416;Inherit;False;GSF
    Kelemen;-1;;61231;5679e1af793ab674487193329a5d7b6f;3,360,1,361,1,367,1;4;394;FLOAT3;0,0,1;False;354;FLOAT;0;False;353;FLOAT;0;False;369;FLOAT;0;False;1;FLOAT;221\nNode;AmplifyShaderEditor.FunctionNode;813;-800,2544;Inherit;False;GSF
    Kurt;-1;;61235;930acf53f9145a447b2eafc720aaa062;3,487,1,488,1,500,1;5;451;FLOAT3;0,0,1;False;481;FLOAT;0;False;480;FLOAT;0;False;501;FLOAT;0;False;226;FLOAT;0.5;False;1;FLOAT;222\nNode;AmplifyShaderEditor.FunctionNode;816;-800,2688;Inherit;False;GSF
    Neumann;-1;;61239;2f2bc48abcbe0be448d32bc9de9f9aca;2,269,1,270,1;3;303;FLOAT3;0,0,1;False;272;FLOAT;0;False;271;FLOAT;0;False;1;FLOAT;218\nNode;AmplifyShaderEditor.WireNode;826;-432,1824;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;825;-416,1840;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;818;-400,1856;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;820;-384,1872;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;823;-368,1888;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;824;-352,1904;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;821;-384,2608;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;828;-464,2176;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;829;-464,2032;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;830;-448,1808;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;959;-480,1952;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;960;-464,1776;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionNode;808;-800,1824;Inherit;False;GSF
    CookTorrance;-1;;61242;01c858a2790221c449099255c56941a5;4,277,1,271,1,272,1,337,1;5;305;FLOAT3;0,0,1;False;274;FLOAT;0;False;273;FLOAT;0;False;333;FLOAT;0;False;275;FLOAT;0;False;1;FLOAT;224\nNode;AmplifyShaderEditor.WireNode;822;-400,2512;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;819;-416,2384;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;817;-416,2272;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1022;-864,1712;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1023;-864,1824;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;209;-1072,1616;Inherit;False;208;_NdotL;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;961;-1072,1776;Inherit;False;972;_LdotH;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;976;-1072,1856;Inherit;False;655;_NdotH;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;977;-1072,1936;Inherit;False;975;_VdotH;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;978;-1072,2016;Inherit;False;969;_HdotH;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1024;-864,2064;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1025;-864,2096;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1026;-864,1968;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1027;-864,2176;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1029;-864,2304;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1030;-864,2288;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1031;-864,2416;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1032;-864,2432;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1034;-864,2480;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1035;-864,2528;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1036;-864,2560;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1037;-864,2592;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1038;-864,2624;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1039;-864,2704;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1040;-864,2688;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1041;-864,2784;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1042;-864,2800;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1043;-864,2864;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1044;-864,2912;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1045;-864,2944;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1046;-864,2976;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1047;-864,3040;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1048;-864,3104;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1049;-864,3072;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1050;-864,3168;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1052;-864,3232;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1054;-864,3296;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1055;-864,3328;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1056;-864,3440;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1057;-864,3472;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1058;-864,3408;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionNode;837;-800,3440;Inherit;False;GSF
    Walter et all;-1;;61247;af42cbe5d68814f42ae2707b017437aa;2,127,1,128,1;4;91;FLOAT3;0,0,1;False;121;FLOAT;0;False;120;FLOAT;0;False;19;FLOAT;0.5;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionNode;838;-800,3568;Inherit;False;GSF
    Ward;-1;;61250;9b06d9053d687e944aac26458cec86c3;2,269,1,268,1;3;302;FLOAT3;0,0,1;False;271;FLOAT;0;False;270;FLOAT;0;False;1;FLOAT;223\nNode;AmplifyShaderEditor.WireNode;1059;-864,3568;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;846;-384,3536;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1061;-864,3360;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1062;-864,3200;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1063;-864,1696;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1064;-864,1696;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1065;-864,1696;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1066;-864,1696;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1067;-864,1696;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1068;-864,1696;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1069;-864,1696;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1070;-864,1696;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1071;-864,1696;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1072;-864,1696;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1073;-864,1696;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1074;-864,1696;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1075;-864,1696;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;211;-1072,1680;Inherit;False;210;_NdotV;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1076;-864,1760;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1077;-864,1760;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1078;-864,1760;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1079;-864,1760;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1080;-864,1760;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1081;-864,1760;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1082;-864,1760;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1083;-864,1760;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1084;-864,1760;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1085;-864,1760;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1086;-864,1760;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1087;-864,1760;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1088;-864,1760;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1089;-864,1760;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1090;-864,2000;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1091;-864,2000;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1092;-864,2192;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1093;-864,2192;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1094;-864,2192;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1095;-864,2192;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1096;-864,2192;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1097;-864,2192;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1098;-864,2192;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1099;-864,2192;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1028;-864,2224;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1060;-864,3600;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionNode;853;2656,1600;Inherit;False;NDF
    Beckman;-1;;61253;422c6f2c53b892546af586d7039d40df;1,334,1;3;346;FLOAT3;0,0,1;False;329;FLOAT;0;False;226;FLOAT;0.5;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionNode;857;2656,1888;Inherit;False;NDF
    Gaussian;-1;;61261;e40225b06d48ab3408c3212cc8475240;1,400,1;3;365;FLOAT3;0,0,1;False;398;FLOAT;0;False;226;FLOAT;0.35;False;1;FLOAT;213\nNode;AmplifyShaderEditor.FunctionNode;860;2656,2256;Inherit;False;NDF
    Trowbridge Reitz;-1;;61265;8344da91481494d4b8cb7fb96347da42;1,526,1;3;491;FLOAT3;0,0,1;False;524;FLOAT;0;False;290;FLOAT;0.1;False;1;FLOAT;273\nNode;AmplifyShaderEditor.WireNode;875;3104,1824;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;874;3088,1808;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;873;3072,1792;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;872;3056,1776;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;871;3040,1760;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;870;3024,1744;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;867;2992,1808;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;866;3008,1888;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;862;3072,2320;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;864;3056,2064;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;865;3024,1968;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;863;3056,2224;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;992;2560,1888;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1206;2560,2064;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;200;3664,1600;Inherit;True;Specular;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1002;2320,1680;Inherit;False;974;_RdotV;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1234;2560,2128;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1235;2560,2096;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1208;2560,2240;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1233;2560,2336;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;995;2560,2384;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1005;2560,2448;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1236;2560,1904;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1237;2560,1904;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1238;2560,1984;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1239;2560,1984;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1240;2560,2064;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1192;2560,1664;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1190;2560,1664;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1189;2560,1664;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1188;2560,1664;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1187;2560,1664;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1242;2560,1696;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1225;2560,1744;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1227;2560,1792;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1243;2560,2208;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1244;2560,2480;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1245;2592,2320;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1246;2592,2048;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1247;2592,1968;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1248;2592,1872;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1249;2592,1776;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1250;2592,1680;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1251;2592,2224;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1252;2592,2544;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1253;2592,2720;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1254;2592,2720;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1255;2592,2656;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1256;2592,2656;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1257;2592,2576;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1258;2592,2576;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1259;2592,2576;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1260;2592,2576;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;262;2224,2160;Inherit;False;Property;_NDFAnistropic;NDF
    Anistropic;20;1;[Header];Create;True;1;NORMAL DISTRIBUTION;0;0;False;0;False;-2;0.75;-20;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;869;2992,1680;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1223;2400,2640;Inherit;False;1222;Gloss;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1224;2400,2560;Inherit;False;730;NDF
    Rough;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;605;-16,5344;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;610;-16,5792;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;667;352,5344;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;674;352,5440;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;675;352,5536;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;678;288,5600;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;677;288,5792;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;679;256,5504;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;680;272,5808;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitchByPipeline;641;544,5344;Inherit;False;4;0;FLOAT3;0,0,0;False;3;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;767;608,4048;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;575;656,3968;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;769;784,4032;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;770;784,4032;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.ScaleAndOffsetNode;565;-400,4560;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;10;False;2;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.Exp2OpNode;566;-192,4560;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.PowerNode;567;-48,4480;Inherit;False;False;2;0;FLOAT;0;False;1;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;577;1104,4672;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;578;1056,4480;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;579;1056,4688;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;653;-464,4640;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.LerpOp;594;208,4688;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SwitchBySRPVersionNode;597;448,4784;Inherit;False;9;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;3;FLOAT3;0,0,0;False;4;FLOAT3;0,0,0;False;5;FLOAT3;0,0,0;False;6;FLOAT3;0,0,0;False;7;FLOAT3;0,0,0;False;8;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitchByPipeline;598;752,4688;Inherit;False;4;0;FLOAT3;0,0,0;False;3;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;599;672,4720;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;648;672,4720;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;600;1248,4672;Inherit;False;Indirect
    Specular;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;765;-48,4368;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitchByPipeline;764;160,4320;Inherit;False;4;0;FLOAT3;0,0,0;False;3;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;668;-640,5024;Inherit;False;435;Occlusion;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;574;448,4432;Inherit;False;3;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;768;608,4416;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;772;784,4464;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;771;784,4464;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitchByPipeline;576;848,4432;Inherit;False;4;0;FLOAT3;0,0,0;False;3;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;774;-48,4272;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;652;-464,4128;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;775;784,4464;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.CustomExpressionNode;766;-288,4368;Inherit;False;Light
    light = GetMainLight()@$return Color = light.color@;3;Create;1;True;Color;FLOAT3;0,0,0;Out;;Inherit;False;Get
    Main Light Color Node;True;False;0;;False;1;0;FLOAT3;0,0,0;False;2;FLOAT3;0;FLOAT3;1\nNode;AmplifyShaderEditor.GetLocalVarNode;666;144,5408;Inherit;False;435;Occlusion;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.LightColorNode;569;-256,4240;Inherit;False;0;3;COLOR;0;FLOAT3;1;FLOAT;2\nNode;AmplifyShaderEditor.RangedFloatNode;60;-752,4816;Half;False;Property;_IndirectSpecularSmoothness;Indirect
    Specular Smoothness;28;0;Create;False;1;;0;0;False;0;False;1;0;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;59;-112,4784;Half;False;Property;_IndirectSpecular;Indirect
    Specular ;27;0;Create;False;0;0;0;False;0;False;0.85;0.422;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.Vector3Node;645;224,4976;Inherit;False;Constant;_Vector4;Vector
    2;0;0;Create;True;0;0;0;False;0;False;0,0,0;0,0,0;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.RangedFloatNode;595;32,4672;Float;False;Constant;_Float6;Float
    5;18;0;Create;True;0;0;0;False;0;False;1;1;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;62;-320,5440;Float;False;Property;_IndirectDiffuse;Indirect
    Diffuse;29;0;Create;False;0;0;0;False;0;False;0;0;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.BakedGINode;607;-480,5792;Inherit;False;True;5;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT2;0,0;False;3;FLOAT2;0,0;False;4;FLOAT4;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.ColorNode;61;-576,3840;Inherit;False;Property;_IndirectSpecularColor;Indirect
    Specular Color;26;2;[HDR];[Header];Create;False;1;INDIRECT LIGHTING;0;0;False;0;False;1,0.9568627,0.8392157,0;1,1,1,0;True;False;0;6;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT3;5\nNode;AmplifyShaderEditor.WireNode;957;304,3888;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;958;336,3936;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;584;-256,4480;Inherit;False;655;_NdotH;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionNode;794;-288,3968;Inherit;False;SRP
    Additional Light;-1;;61277;6c86746ad131a0a408ca599df5f40861;8,212,0,6,2,9,1,23,0,24,0,142,0,168,0,154,0;6;2;FLOAT3;0,0,0;False;11;FLOAT3;0,0,0;False;15;FLOAT3;0,0,0;False;14;FLOAT3;0,0,0;False;18;FLOAT;0.5;False;32;FLOAT4;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1172;-576,4000;Inherit;False;1160;WorldNormalTangent;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1173;-768,4736;Inherit;False;1160;WorldNormalTangent;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WorldPosInputsNode;608;-736,5792;Inherit;False;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.FunctionNode;793;-480,5696;Inherit;False;SRP
    Additional Light;-1;;61278;6c86746ad131a0a408ca599df5f40861;8,212,0,6,1,9,1,23,1,24,0,142,0,168,0,154,0;6;2;FLOAT3;0,0,0;False;11;FLOAT3;0,0,0;False;15;FLOAT3;0,0,0;False;14;FLOAT3;0,0,0;False;18;FLOAT;0.5;False;32;FLOAT4;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1174;-800,5696;Inherit;False;1160;WorldNormalTangent;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionNode;650;-736,5936;Inherit;False;Lightmap
    UV;-1;;61279;1940f027d0458684eb0ad486f669d7d5;1,1,0;0;1;FLOAT2;0\nNode;AmplifyShaderEditor.FunctionNode;651;-736,6016;Inherit;False;Lightmap
    UV;-1;;61280;1940f027d0458684eb0ad486f669d7d5;1,1,1;0;1;FLOAT2;0\nNode;AmplifyShaderEditor.ScreenPosInputsNode;891;-768,6096;Inherit;False;0;False;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SimpleAddOpNode;601;-208,5696;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;606;-16,5696;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;681;224,5712;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;676;256,5680;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.IndirectDiffuseLighting;646;-320,5344;Inherit;False;Tangent;1;0;FLOAT3;0,0,1;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;622;-512,5344;Inherit;False;14;Normal
    Map;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.IndirectSpecularLight;647;-384,4720;Inherit;False;World;3;0;FLOAT3;0,0,1;False;1;FLOAT;0.5;False;2;FLOAT;1;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1261;-1135.386,-980.4782;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;1262;-1136,-976;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;1263;-1136,-976;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;1264;-1136,-976;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.FunctionNode;878;2048,1088;Inherit;False;Schlick
    IOR Fresnel;-1;;61281;4e5b8e7e69de2624b8c2eb0fc8a0add3;1,149,1;2;78;FLOAT;0;False;6;FLOAT;1.5;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionNode;877;2048,1008;Inherit;False;Gaussian
    Fresnel;-1;;61283;bcd454558bbff74468287c1228c31794;1,148,1;2;79;FLOAT;0;False;128;FLOAT3;0,0,1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionNode;1212;2048,848;Inherit;False;Diffuse
    Fresnel;-1;;61285;02dfec30984d37f4089b77decb5f975c;2,76,1,90,1;4;89;FLOAT;0;False;93;FLOAT3;0,0,1;False;35;FLOAT;0.5;False;78;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1112;2288,960;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1111;2256,944;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;201;2752,848;Inherit;True;Fresnel
    Term;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1114;2288,1088;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;503;1744,1104;Inherit;False;Property;_fresnelIOR;fresnel
    IOR;19;1;[Header];Create;True;1;FRESNEL TERM;0;0;False;0;False;1.5;1.5;1;4;0;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1110;1840,1008;Inherit;False;972;_LdotH;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;505;1808,928;Inherit;False;417;Smoothness
    ;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;205;1840,848;Inherit;False;655;_NdotH;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;932;-1072,2112;Inherit;False;730;NDF
    Rough;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;403;-80,656;Inherit;False;Metallic;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;398;-544,624;Inherit;False;Property;_MetallicStrength;Metallic
    Strength;14;0;Create;False;0;0;0;False;0;False;0.15;0;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;53;416,848;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;47;576,928;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;1222;736,848;Inherit;False;Gloss;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;417;736,768;Inherit;False;Smoothness
    ;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionNode;1265;-800,2064;Inherit;False;GSF
    GGX;-1;;61290;6b233c3831e4ea84f9de7e1c9c1f5324;2,145,1,144,1;4;108;FLOAT3;0,0,1;False;138;FLOAT;0;False;137;FLOAT;0;False;18;FLOAT;0.5;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionNode;1266;-800,2800;Inherit;False;GSF
    Schlick Beckman;-1;;61293;6eb064b8976b7954c84f0daef24518b5;3,114,1,115,1,2,0;5;78;FLOAT3;0,0,1;False;108;FLOAT;0;False;107;FLOAT;0;False;142;FLOAT;0.7978846;False;26;FLOAT;0.5;False;1;FLOAT;0\nNode;AmplifyShaderEditor.StickyNoteNode;1267;528,1904;Inherit;False;256.6381;135;Shadow
    Offset;;0,0,0,0.5019608;_ShadowOffset$$-1 for Reduced shadow$0 for Normal Shadow$1
    for Extended Shadow;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;1268;240,1824;Inherit;False;253.2332;129.3191;Shadow
    Strength;;0,0,0,0.5019608;_ShadowStrength $$'One Minus'-ed for perceptual effect,
    remember The 'Shadow' is the black/Zero;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;1269;864,1872;Inherit;False;256.681;100;Shadow
    Falloff;;0,0,0,1;_ShadowFalloff;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;1278;1312,1728;Inherit;False;266.3429;144.3734;Enable
    Shadow Color;;0,0,0,0.5019608;added here as regardless of the GSF used, these
    variables here have no effect on the end result UNLESS Shadow color is enabled$;0;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;65;1616,1616;Inherit;True;Shadow
    ;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1281;656,1680;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1280;640,1648;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1282;3376,-400;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1283;3408,-352;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionNode;1284;2656,1696;Inherit;False;NDF
    BlinnPhong;-1;;61296;e7fd032cc98224d45916029fbc1b6605;4,169,0,39,1,125,1,158,1;5;92;FLOAT3;0,0,1;False;123;FLOAT;0;False;159;FLOAT;0;False;14;FLOAT;0.98;False;15;FLOAT;36;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionNode;1285;2656,1792;Inherit;False;NDF
    BlinnPhong;-1;;61299;e7fd032cc98224d45916029fbc1b6605;4,169,0,39,0,125,1,158,1;5;92;FLOAT3;0,0,1;False;123;FLOAT;0;False;159;FLOAT;0;False;14;FLOAT;0.98;False;15;FLOAT;36;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1006;2560,2416;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionNode;858;2656,1984;Inherit;False;NDF
    GGX;-1;;61306;910ccb9a63165124b96e6b7cde61f457;2,468,0,537,1;3;502;FLOAT3;0,0,1;False;535;FLOAT;0;False;290;FLOAT;0.5;False;1;FLOAT;272\nNode;AmplifyShaderEditor.WireNode;1287;2560,2000;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1288;2560,1664;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionNode;1290;2656,2352;Inherit;False;NDF
    Ward Anisotropic;-1;;61322;1ab576a6940ee30489e517acfe250a47;5,241,1,242,1,243,1,249,1,250,1;8;224;FLOAT3;0,0,1;False;173;FLOAT;0;False;176;FLOAT;0;False;172;FLOAT;0;False;235;FLOAT;0;False;256;FLOAT;0;False;54;FLOAT;5;False;48;FLOAT;0.5;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;210;-2320,352;Inherit;False;_NdotV;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1241;2560,2160;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1232;2560,2400;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1121;-2704,432;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1139;-2704,464;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1162;-2704,448;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionNode;882;-2560,464;Inherit;False;NdotH;-1;;61328;fadb6690f179c1448859861f76ebb061;7,27,1,373,0,451,1,463,1,460,1,461,0,452,0;6;230;FLOAT3;0,0,1;False;443;FLOAT3;0,0,0;False;462;FLOAT3;0,0,0;False;454;FLOAT3;0,0,0;False;453;FLOAT3;0,0,0;False;417;FLOAT;1E-37;False;1;FLOAT;109\nNode;AmplifyShaderEditor.RegisterLocalVarNode;655;-2320,464;Inherit;False;_NdotH;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;974;-2320,608;Inherit;False;_RdotV;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;975;-2320,736;Inherit;False;_VdotH;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1125;-2704,736;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1126;-2704,608;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1141;-2704,752;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1142;-2704,624;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionNode;968;-2576,736;Inherit;False;VdotH;-1;;61329;0538bd8fc12afb44aa34ce2aa4cd3663;5,363,0,451,1,460,1,452,0,461,0;5;443;FLOAT3;0,0,0;False;462;FLOAT3;0,0,0;False;454;FLOAT3;0,0,0;False;453;FLOAT3;0,0,0;False;418;FLOAT;1E-37;False;1;FLOAT;108\nNode;AmplifyShaderEditor.WireNode;1164;-2704,592;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionNode;967;-2624,608;Inherit;False;RdotV;-1;;61330;57d3b3f789fb88e41aad491f72c6ab43;6,27,1,401,0,454,1,463,1,464,0,466,1;5;230;FLOAT3;0,0,1;False;446;FLOAT3;0,0,0;False;465;FLOAT3;0,0,0;False;457;FLOAT3;0,0,0;False;421;FLOAT;1E-37;False;3;FLOAT;298;FLOAT3;430;FLOAT3;433\nNode;AmplifyShaderEditor.OneMinusNode;1270;512,1744;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;1273;864,1696;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;1274;704,1696;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SaturateNode;1275;1008,1696;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;18;224,1744;Half;False;Property;_ShadowStrength;Shadow
    Strength;21;1;[Header];Create;False;1;GEOMETRIC SHADOWING;0;0;False;0;False;0.1;0.239;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;20;528,1824;Half;False;Property;_ShadowOffset;Shadow
    Offset;22;0;Create;False;1;;0;0;False;0;False;-0.05;0.295;-1;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.PowerNode;1277;1152,1696;Inherit;False;False;2;0;FLOAT;0;False;1;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;927;848,1792;Inherit;False;Property;_ShadowFalloff;Shadow
    Falloff;23;0;Create;True;0;0;0;False;0;False;1;1;1;10;0;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;1279;1312,1616;Inherit;False;Enable
    Shadow Color;True;1;2;7;In 0;In 1;Instance;908;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1008;2320,2080;Inherit;False;210;_NdotV;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;240;3200,1600;Inherit;False;NORMAL
    DISTRIBUTION FUNCTION;False;5;8;3;NDF Beckman;NDF Phong;NDF BlinnPhong;NDF Gaussian;NDF
    GGX;NDF Trowbridge Reitz Anisotropic;NDF Trowbridge Reitz;NDF Ward Anisotropic;Object;-1;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;991;2320,1600;Inherit;False;655;_NdotH;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1003;2320,1840;Inherit;False;970;_HdotX;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1004;2320,1920;Inherit;False;971;_HdotY;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1007;2320,2000;Inherit;False;208;_NdotL;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionNode;1291;2672,2080;Inherit;False;NDF
    Trowbridge Reitz Anisotropic;-1;;61331;110a56f1435e8fc418d170fb6d934ea6;3,291,1,334,1,341,1;6;322;FLOAT3;0,0,1;False;266;FLOAT;0;False;336;FLOAT;0;False;343;FLOAT;0;False;15;FLOAT;0.9;False;19;FLOAT;0.5;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1221;2368,2720;Inherit;False;417;Smoothness
    ;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;730;928,928;Inherit;False;NDF
    Rough;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;1292;752,928;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WorldNormalVector;1159;-96,64;Inherit;False;True;1;0;FLOAT3;0,0,1;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.WireNode;1293;2336,976;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1294;2336,1184;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionNode;1295;2048,1200;Inherit;False;Schlick
    Fresnel Approx;-1;;61335;795f71e3795c9fc4f856e860f8cfba61;0;1;5;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;215;2400,848;Inherit;False;FRESNEL
    TERM;False;1;4;4;Diffuse Fresnel;Gaussian Fresnel;Schlick IOR Fresnel;Schlick
    Approx Fresnel;Object;-1;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;644;928,5344;Inherit;False;Indirect
    Diffuse;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.Exposure;1296;640,5504;Inherit;False;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;1297;784,5344;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionOutput;156;-400,-2272;Inherit;False;False;-1;Alpha;1;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;159;-400,-1984;Inherit;False;False;-1;Alpha
    Clip Threshold;2;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;158;-400,-1600;Inherit;False;False;-1;Alpha
    Clip Threshold Shadow;3;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;0;3968,-432;Inherit;False;True;-1;BaseColor;0;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nWireConnection;184;0;178;0\nWireConnection;184;1;173;0\nWireConnection;184;2;187;0\nWireConnection;187;0;188;0\nWireConnection;193;0;173;0\nWireConnection;177;0;194;0\nWireConnection;177;1;180;0\nWireConnection;178;0;177;0\nWireConnection;179;0;173;0\nWireConnection;180;0;179;0\nWireConnection;194;0;193;0\nWireConnection;186;0;184;0\nWireConnection;188;0;189;0\nWireConnection;189;0;133;0\nWireConnection;152;0;196;0\nWireConnection;152;1;142;0\nWireConnection;110;0;186;0\nWireConnection;110;3;260;0\nWireConnection;110;1;259;0\nWireConnection;110;2;152;0\nWireConnection;164;0;172;0\nWireConnection;165;0;172;0\nWireConnection;167;0;166;0\nWireConnection;149;0;167;0\nWireConnection;149;1;1171;0\nWireConnection;148;0;149;0\nWireConnection;147;0;148;0\nWireConnection;146;0;147;0\nWireConnection;146;1;147;0\nWireConnection;145;0;146;0\nWireConnection;166;0;164;0\nWireConnection;166;1;165;0\nWireConnection;154;0;142;0\nWireConnection;154;3;142;0\nWireConnection;154;1;133;0\nWireConnection;154;2;116;0\nWireConnection;142;0;141;0\nWireConnection;142;1;143;0\nWireConnection;142;2;126;0\nWireConnection;119;0;118;0\nWireConnection;119;3;118;0\nWireConnection;119;1;118;0\nWireConnection;119;2;117;0\nWireConnection;143;1;145;0\nWireConnection;143;0;144;0\nWireConnection;259;0;196;0\nWireConnection;260;0;186;0\nWireConnection;33;0;28;1\nWireConnection;375;0;28;1\nWireConnection;39;0;35;0\nWireConnection;40;0;33;0\nWireConnection;55;0;49;0\nWireConnection;379;0;375;0\nWireConnection;10;0;9;0\nWireConnection;10;1;801;0\nWireConnection;10;7;9;1\nWireConnection;35;0;28;0\nWireConnection;35;1;800;0\nWireConnection;35;7;28;1\nWireConnection;49;0;42;0\nWireConnection;49;1;802;0\nWireConnection;49;7;40;0\nWireConnection;12;0;10;0\nWireConnection;12;1;11;0\nWireConnection;535;0;534;0\nWireConnection;535;1;533;0\nWireConnection;535;2;530;0\nWireConnection;535;3;531;0\nWireConnection;535;4;532;0\nWireConnection;536;0;2;0\nWireConnection;536;1;535;0\nWireConnection;64;0;55;0\nWireConnection;545;0;39;0\nWireConnection;545;1;538;0\nWireConnection;734;0;39;0\nWireConnection;537;0;540;0\nWireConnection;537;1;736;0\nWireConnection;736;0;733;0\nWireConnection;733;0;39;0\nWireConnection;538;0;537;0\nWireConnection;538;1;735;0\nWireConnection;538;2;544;0\nWireConnection;735;0;734;0\nWireConnection;544;0;543;0\nWireConnection;79;0;35;4\nWireConnection;48;0;37;0\nWireConnection;48;1;545;0\nWireConnection;48;2;45;0\nWireConnection;745;0;741;0\nWireConnection;745;1;400;0\nWireConnection;742;0;741;0\nWireConnection;742;1;745;0\nWireConnection;743;0;742;0\nWireConnection;753;0;752;0\nWireConnection;750;0;688;0\nWireConnection;750;1;751;0\nWireConnection;750;2;887;0\nWireConnection;752;0;473;2\nWireConnection;688;0;473;2\nWireConnection;414;0;750;0\nWireConnection;414;1;413;0\nWireConnection;796;0;1261;0\nWireConnection;797;0;1264;0\nWireConnection;798;0;1262;0\nWireConnection;799;0;1263;0\nWireConnection;800;0;797;0\nWireConnection;801;0;796;0\nWireConnection;802;0;798;0\nWireConnection;803;0;799;0\nWireConnection;6;0;5;0\nWireConnection;6;1;4;0\nWireConnection;795;0;6;0\nWireConnection;5;0;536;0\nWireConnection;5;1;3;0\nWireConnection;3;0;1;0\nWireConnection;4;0;1;0\nWireConnection;671;0;431;0\nWireConnection;691;0;690;0\nWireConnection;428;1;425;0\nWireConnection;428;2;671;0\nWireConnection;690;0;694;0\nWireConnection;473;0;884;0\nWireConnection;473;1;803;0\nWireConnection;473;7;379;0\nWireConnection;751;0;753;0\nWireConnection;905;0;1283;0\nWireConnection;905;1;914;0\nWireConnection;905;2;912;0\nWireConnection;895;0;898;0\nWireConnection;895;1;896;0\nWireConnection;897;0;911;0\nWireConnection;897;1;24;5\nWireConnection;898;0;897;0\nWireConnection;898;1;24;5\nWireConnection;898;2;1109;0\nWireConnection;912;0;902;0\nWireConnection;913;0;895;0\nWireConnection;914;0;913;0\nWireConnection;63;0;48;0\nWireConnection;425;0;691;0\nWireConnection;425;1;426;4\nWireConnection;400;0;398;0\nWireConnection;400;1;473;1\nWireConnection;694;0;473;3\nWireConnection;434;0;428;0\nWireConnection;435;0;434;0\nWireConnection;449;0;67;0\nWireConnection;788;0;404;0\nWireConnection;787;0;77;0\nWireConnection;786;0;76;0\nWireConnection;739;0;740;0\nWireConnection;739;1;659;0\nWireConnection;739;2;744;0\nWireConnection;785;0;739;0\nWireConnection;783;0;785;0\nWireConnection;1103;0;1108;0\nWireConnection;1104;0;1107;0\nWireConnection;1105;0;1106;0\nWireConnection;1106;0;1102;0\nWireConnection;1107;0;1101;0\nWireConnection;1108;0;1100;0\nWireConnection;789;0;204;0\nWireConnection;790;0;202;0\nWireConnection;784;0;658;0\nWireConnection;782;0;784;0\nWireConnection;908;0;883;0\nWireConnection;908;1;905;0\nWireConnection;1109;0;24;4\nWireConnection;901;0;900;0\nWireConnection;899;0;901;0\nWireConnection;899;1;31;0\nWireConnection;902;0;899;0\nWireConnection;14;0;12;0\nWireConnection;1160;0;1159;0\nWireConnection;971;0;964;300\nWireConnection;965;443;1128;0\nWireConnection;965;462;1118;0\nWireConnection;964;445;1120;0\nWireConnection;964;464;1151;0\nWireConnection;963;440;1119;0\nWireConnection;963;439;1150;0\nWireConnection;970;0;963;299\nWireConnection;962;440;1117;0\nWireConnection;962;439;1149;0\nWireConnection;969;0;962;296\nWireConnection;972;0;965;112\nWireConnection;973;0;966;113\nWireConnection;208;0;879;111\nWireConnection;1116;0;1155;0\nWireConnection;1115;0;1157;0\nWireConnection;879;230;1161;0\nWireConnection;879;462;1138;0\nWireConnection;1123;0;1132;0\nWireConnection;1127;0;1131;0\nWireConnection;1128;0;1130;0\nWireConnection;1120;0;1129;0\nWireConnection;1132;0;1117;0\nWireConnection;1131;0;1117;0\nWireConnection;1130;0;1117;0\nWireConnection;1129;0;1117;0\nWireConnection;1119;0;1117;0\nWireConnection;1135;0;1117;0\nWireConnection;1136;0;1117;0\nWireConnection;1137;0;1117;0\nWireConnection;1138;0;1144;0\nWireConnection;1143;0;1118;0\nWireConnection;1144;0;1118;0\nWireConnection;1145;0;1118;0\nWireConnection;1147;0;1118;0\nWireConnection;1148;0;1118;0\nWireConnection;1149;0;1154;0\nWireConnection;1151;0;1153;0\nWireConnection;1152;0;1118;0\nWireConnection;1150;0;1152;0\nWireConnection;1153;0;1118;0\nWireConnection;1154;0;1118;0\nWireConnection;1156;0;1118;0\nWireConnection;1158;0;1117;0\nWireConnection;1155;0;1156;0\nWireConnection;1157;0;1158;0\nWireConnection;1166;0;1161;0\nWireConnection;1168;0;1161;0\nWireConnection;1170;0;1161;0\nWireConnection;966;443;1127;0\nWireConnection;966;462;1143;0\nWireConnection;880;230;1166;0\nWireConnection;880;443;1123;0\nWireConnection;883;51;66;0\nWireConnection;883;80;449;0\nWireConnection;883;491;790;0\nWireConnection;883;634;789;0\nWireConnection;883;712;788;0\nWireConnection;883;708;787;0\nWireConnection;883;490;786;0\nWireConnection;883;773;782;0\nWireConnection;883;775;783;0\nWireConnection;883;381;1103;0\nWireConnection;883;380;1104;0\nWireConnection;883;631;1105;0\nWireConnection;809;320;1026;0\nWireConnection;809;346;978;0\nWireConnection;806;342;209;0\nWireConnection;806;341;211;0\nWireConnection;807;273;1022;0\nWireConnection;807;272;211;0\nWireConnection;807;315;961;0\nWireConnection;213;0;806;216\nWireConnection;213;1;807;215\nWireConnection;213;2;960;0\nWireConnection;213;3;830;0\nWireConnection;213;4;826;0\nWireConnection;213;5;825;0\nWireConnection;213;6;818;0\nWireConnection;213;7;820;0\nWireConnection;213;8;823;0\nWireConnection;213;9;824;0\nWireConnection;255;0;213;0\nWireConnection;255;1;361;0\nWireConnection;361;0;362;0\nWireConnection;832;100;1044;0\nWireConnection;832;99;1045;0\nWireConnection;832;23;1046;0\nWireConnection;833;115;1047;0\nWireConnection;833;114;1049;0\nWireConnection;833;23;1048;0\nWireConnection;835;136;1050;0\nWireConnection;835;135;1062;0\nWireConnection;835;17;1052;0\nWireConnection;836;49;1054;0\nWireConnection;836;48;1061;0\nWireConnection;836;24;1055;0\nWireConnection;847;0;837;0\nWireConnection;848;0;836;0\nWireConnection;849;0;835;0\nWireConnection;850;0;833;0\nWireConnection;851;0;832;0\nWireConnection;852;0;1266;0\nWireConnection;843;0;848;0\nWireConnection;842;0;849;0\nWireConnection;844;0;847;0\nWireConnection;845;0;846;0\nWireConnection;254;0;839;0\nWireConnection;254;1;840;0\nWireConnection;254;2;841;0\nWireConnection;254;3;842;0\nWireConnection;254;4;843;0\nWireConnection;254;5;844;0\nWireConnection;254;6;845;0\nWireConnection;840;0;851;0\nWireConnection;839;0;852;0\nWireConnection;841;0;850;0\nWireConnection;362;0;254;0\nWireConnection;810;273;1027;0\nWireConnection;810;272;1028;0\nWireConnection;812;403;1030;0\nWireConnection;812;402;1029;0\nWireConnection;812;242;1092;0\nWireConnection;811;354;1031;0\nWireConnection;811;353;1032;0\nWireConnection;811;369;1034;0\nWireConnection;813;481;1035;0\nWireConnection;813;480;1036;0\nWireConnection;813;501;1037;0\nWireConnection;813;226;1038;0\nWireConnection;816;272;1040;0\nWireConnection;816;271;1039;0\nWireConnection;826;0;829;0\nWireConnection;825;0;828;0\nWireConnection;818;0;817;0\nWireConnection;820;0;819;0\nWireConnection;823;0;822;0\nWireConnection;824;0;821;0\nWireConnection;821;0;816;218\nWireConnection;828;0;810;214\nWireConnection;829;0;1265;0\nWireConnection;830;0;959;0\nWireConnection;959;0;809;217\nWireConnection;960;0;808;224\nWireConnection;808;274;1023;0\nWireConnection;808;273;1076;0\nWireConnection;808;333;976;0\nWireConnection;808;275;977;0\nWireConnection;822;0;813;222\nWireConnection;819;0;811;221\nWireConnection;817;0;812;225\nWireConnection;1022;0;209;0\nWireConnection;1023;0;209;0\nWireConnection;1024;0;1063;0\nWireConnection;1025;0;1077;0\nWireConnection;1026;0;976;0\nWireConnection;1027;0;1064;0\nWireConnection;1029;0;1079;0\nWireConnection;1030;0;1065;0\nWireConnection;1031;0;1066;0\nWireConnection;1032;0;1080;0\nWireConnection;1034;0;1090;0\nWireConnection;1035;0;1067;0\nWireConnection;1036;0;1081;0\nWireConnection;1037;0;1091;0\nWireConnection;1038;0;1093;0\nWireConnection;1039;0;1082;0\nWireConnection;1040;0;1068;0\nWireConnection;1041;0;1069;0\nWireConnection;1042;0;1083;0\nWireConnection;1043;0;1094;0\nWireConnection;1044;0;1070;0\nWireConnection;1045;0;1084;0\nWireConnection;1046;0;1095;0\nWireConnection;1047;0;1071;0\nWireConnection;1048;0;1096;0\nWireConnection;1049;0;1085;0\nWireConnection;1050;0;1072;0\nWireConnection;1052;0;1097;0\nWireConnection;1054;0;1073;0\nWireConnection;1055;0;1098;0\nWireConnection;1056;0;1086;0\nWireConnection;1057;0;1099;0\nWireConnection;1058;0;1074;0\nWireConnection;837;121;1058;0\nWireConnection;837;120;1056;0\nWireConnection;837;19;1057;0\nWireConnection;838;271;1059;0\nWireConnection;838;270;1060;0\nWireConnection;1059;0;1075;0\nWireConnection;846;0;838;223\nWireConnection;1061;0;1088;0\nWireConnection;1062;0;1089;0\nWireConnection;1063;0;209;0\nWireConnection;1064;0;209;0\nWireConnection;1065;0;209;0\nWireConnection;1066;0;209;0\nWireConnection;1067;0;209;0\nWireConnection;1068;0;209;0\nWireConnection;1069;0;209;0\nWireConnection;1070;0;209;0\nWireConnection;1071;0;209;0\nWireConnection;1072;0;209;0\nWireConnection;1073;0;209;0\nWireConnection;1074;0;209;0\nWireConnection;1075;0;209;0\nWireConnection;1076;0;211;0\nWireConnection;1077;0;211;0\nWireConnection;1078;0;211;0\nWireConnection;1079;0;211;0\nWireConnection;1080;0;211;0\nWireConnection;1081;0;211;0\nWireConnection;1082;0;211;0\nWireConnection;1083;0;211;0\nWireConnection;1084;0;211;0\nWireConnection;1085;0;211;0\nWireConnection;1086;0;211;0\nWireConnection;1087;0;211;0\nWireConnection;1088;0;211;0\nWireConnection;1089;0;211;0\nWireConnection;1090;0;977;0\nWireConnection;1091;0;977;0\nWireConnection;1092;0;932;0\nWireConnection;1093;0;932;0\nWireConnection;1094;0;932;0\nWireConnection;1095;0;932;0\nWireConnection;1096;0;932;0\nWireConnection;1097;0;932;0\nWireConnection;1098;0;932;0\nWireConnection;1099;0;932;0\nWireConnection;1028;0;1078;0\nWireConnection;1060;0;1087;0\nWireConnection;853;329;991;0\nWireConnection;853;226;1250;0\nWireConnection;857;398;992;0\nWireConnection;857;226;1247;0\nWireConnection;860;524;1208;0\nWireConnection;860;290;1245;0\nWireConnection;875;0;862;0\nWireConnection;874;0;863;0\nWireConnection;873;0;864;0\nWireConnection;872;0;865;0\nWireConnection;871;0;866;0\nWireConnection;870;0;867;0\nWireConnection;867;0;1285;0\nWireConnection;866;0;857;213\nWireConnection;862;0;1290;0\nWireConnection;864;0;1291;0\nWireConnection;865;0;858;272\nWireConnection;863;0;860;273\nWireConnection;992;0;1188;0\nWireConnection;1206;0;1190;0\nWireConnection;200;0;240;0\nWireConnection;1234;0;1238;0\nWireConnection;1235;0;1236;0\nWireConnection;1208;0;1192;0\nWireConnection;1233;0;1240;0\nWireConnection;995;0;1189;0\nWireConnection;1005;0;1239;0\nWireConnection;1236;0;1003;0\nWireConnection;1237;0;1003;0\nWireConnection;1238;0;1004;0\nWireConnection;1239;0;1004;0\nWireConnection;1240;0;1007;0\nWireConnection;1192;0;991;0\nWireConnection;1190;0;991;0\nWireConnection;1189;0;991;0\nWireConnection;1188;0;991;0\nWireConnection;1187;0;991;0\nWireConnection;1242;0;1187;0\nWireConnection;1225;0;1002;0\nWireConnection;1227;0;1225;0\nWireConnection;1243;0;262;0\nWireConnection;1244;0;1243;0\nWireConnection;1245;0;1257;0\nWireConnection;1246;0;1258;0\nWireConnection;1247;0;1259;0\nWireConnection;1248;0;1255;0\nWireConnection;1249;0;1256;0\nWireConnection;1250;0;1260;0\nWireConnection;1251;0;1254;0\nWireConnection;1252;0;1253;0\nWireConnection;1253;0;1221;0\nWireConnection;1254;0;1221;0\nWireConnection;1255;0;1223;0\nWireConnection;1256;0;1223;0\nWireConnection;1257;0;1224;0\nWireConnection;1258;0;1224;0\nWireConnection;1259;0;1224;0\nWireConnection;1260;0;1224;0\nWireConnection;869;0;1284;0\nWireConnection;605;0;646;0\nWireConnection;605;1;62;0\nWireConnection;610;0;607;0\nWireConnection;610;1;62;0\nWireConnection;667;0;605;0\nWireConnection;667;1;666;0\nWireConnection;674;0;679;0\nWireConnection;674;1;666;0\nWireConnection;675;0;678;0\nWireConnection;675;1;666;0\nWireConnection;678;0;677;0\nWireConnection;677;0;680;0\nWireConnection;679;0;676;0\nWireConnection;680;0;610;0\nWireConnection;641;0;667;0\nWireConnection;641;3;667;0\nWireConnection;641;1;674;0\nWireConnection;641;2;675;0\nWireConnection;767;0;768;0\nWireConnection;575;0;794;0\nWireConnection;575;1;767;0\nWireConnection;769;0;575;0\nWireConnection;770;0;575;0\nWireConnection;565;0;653;0\nWireConnection;566;0;565;0\nWireConnection;567;0;584;0\nWireConnection;567;1;566;0\nWireConnection;577;0;579;0\nWireConnection;577;1;598;0\nWireConnection;578;0;576;0\nWireConnection;579;0;578;0\nWireConnection;653;0;60;0\nWireConnection;594;0;595;0\nWireConnection;594;1;647;0\nWireConnection;594;2;59;0\nWireConnection;597;0;594;0\nWireConnection;597;1;594;0\nWireConnection;597;2;594;0\nWireConnection;597;3;594;0\nWireConnection;597;4;594;0\nWireConnection;597;5;645;0\nWireConnection;597;6;645;0\nWireConnection;597;7;645;0\nWireConnection;597;8;645;0\nWireConnection;598;0;594;0\nWireConnection;598;3;648;0\nWireConnection;598;1;597;0\nWireConnection;598;2;599;0\nWireConnection;599;0;594;0\nWireConnection;648;0;594;0\nWireConnection;600;0;577;0\nWireConnection;765;0;584;0\nWireConnection;765;1;766;1\nWireConnection;764;0;774;0\nWireConnection;764;3;774;0\nWireConnection;764;1;765;0\nWireConnection;764;2;774;0\nWireConnection;574;0;958;0\nWireConnection;574;1;764;0\nWireConnection;574;2;567;0\nWireConnection;768;0;574;0\nWireConnection;772;0;770;0\nWireConnection;771;0;769;0\nWireConnection;576;0;574;0\nWireConnection;576;3;775;0\nWireConnection;576;1;771;0\nWireConnection;576;2;772;0\nWireConnection;774;0;584;0\nWireConnection;774;1;569;1\nWireConnection;652;0;60;0\nWireConnection;775;0;574;0\nWireConnection;607;0;608;0\nWireConnection;607;1;1174;0\nWireConnection;607;2;650;0\nWireConnection;607;3;651;0\nWireConnection;607;4;891;0\nWireConnection;957;0;61;0\nWireConnection;958;0;957;0\nWireConnection;794;11;1172;0\nWireConnection;794;14;61;0\nWireConnection;794;18;652;0\nWireConnection;793;11;1174;0\nWireConnection;601;0;793;0\nWireConnection;601;1;607;0\nWireConnection;606;0;601;0\nWireConnection;606;1;62;0\nWireConnection;681;0;606;0\nWireConnection;676;0;681;0\nWireConnection;646;0;622;0\nWireConnection;647;0;1173;0\nWireConnection;647;1;60;0\nWireConnection;647;2;668;0\nWireConnection;1261;0;795;0\nWireConnection;1262;0;795;0\nWireConnection;1263;0;795;0\nWireConnection;1264;0;795;0\nWireConnection;878;78;1110;0\nWireConnection;878;6;503;0\nWireConnection;877;79;1110;0\nWireConnection;1212;89;205;0\nWireConnection;1212;35;505;0\nWireConnection;1112;0;1114;0\nWireConnection;1111;0;877;0\nWireConnection;201;0;215;0\nWireConnection;1114;0;878;0\nWireConnection;403;0;400;0\nWireConnection;53;0;414;0\nWireConnection;53;1;414;0\nWireConnection;47;0;53;0\nWireConnection;1222;0;53;0\nWireConnection;417;0;414;0\nWireConnection;1265;138;1024;0\nWireConnection;1265;137;1025;0\nWireConnection;1265;18;932;0\nWireConnection;1266;108;1041;0\nWireConnection;1266;107;1042;0\nWireConnection;1266;26;1043;0\nWireConnection;65;0;1279;0\nWireConnection;1281;0;1280;0\nWireConnection;1280;0;255;0\nWireConnection;1282;0;883;0\nWireConnection;1283;0;1282;0\nWireConnection;1284;123;1242;0\nWireConnection;1284;14;1249;0\nWireConnection;1285;159;1227;0\nWireConnection;1285;14;1248;0\nWireConnection;1006;0;1237;0\nWireConnection;858;535;1287;0\nWireConnection;858;290;1246;0\nWireConnection;1287;0;1288;0\nWireConnection;1288;0;991;0\nWireConnection;1290;173;1233;0\nWireConnection;1290;176;995;0\nWireConnection;1290;172;1232;0\nWireConnection;1290;235;1006;0\nWireConnection;1290;256;1005;0\nWireConnection;1290;54;1244;0\nWireConnection;1290;48;1252;0\nWireConnection;210;0;880;23\nWireConnection;1241;0;1008;0\nWireConnection;1232;0;1241;0\nWireConnection;1121;0;1135;0\nWireConnection;1139;0;1145;0\nWireConnection;1162;0;1168;0\nWireConnection;882;230;1162;0\nWireConnection;882;443;1121;0\nWireConnection;882;462;1139;0\nWireConnection;655;0;882;109\nWireConnection;974;0;967;298\nWireConnection;975;0;968;108\nWireConnection;1125;0;1136;0\nWireConnection;1126;0;1137;0\nWireConnection;1141;0;1147;0\nWireConnection;1142;0;1148;0\nWireConnection;968;443;1125;0\nWireConnection;968;462;1141;0\nWireConnection;1164;0;1170;0\nWireConnection;967;230;1164;0\nWireConnection;967;446;1126;0\nWireConnection;967;465;1142;0\nWireConnection;1270;0;18;0\nWireConnection;1273;0;1274;0\nWireConnection;1273;1;20;0\nWireConnection;1274;0;1281;0\nWireConnection;1274;1;1270;0\nWireConnection;1275;0;1273;0\nWireConnection;1277;0;1275;0\nWireConnection;1277;1;927;0\nWireConnection;1279;0;255;0\nWireConnection;1279;1;1277;0\nWireConnection;240;0;853;0\nWireConnection;240;1;869;0\nWireConnection;240;2;870;0\nWireConnection;240;3;871;0\nWireConnection;240;4;872;0\nWireConnection;240;5;873;0\nWireConnection;240;6;874;0\nWireConnection;240;7;875;0\nWireConnection;1291;266;1206;0\nWireConnection;1291;336;1235;0\nWireConnection;1291;343;1234;0\nWireConnection;1291;15;262;0\nWireConnection;1291;19;1251;0\nWireConnection;730;0;1292;0\nWireConnection;1292;0;47;0\nWireConnection;1292;1;47;0\nWireConnection;1159;0;12;0\nWireConnection;1293;0;1294;0\nWireConnection;1294;0;1295;0\nWireConnection;1295;5;1110;0\nWireConnection;215;0;1212;0\nWireConnection;215;1;1111;0\nWireConnection;215;2;1112;0\nWireConnection;215;3;1293;0\nWireConnection;644;0;1297;0\nWireConnection;1297;0;641;0\nWireConnection;1297;1;1296;0\nWireConnection;156;0;110;0\nWireConnection;159;0;154;0\nWireConnection;158;0;119;0\nWireConnection;0;0;908;0\nASEEND*/\n//CHKSM=C64491B27F073192B34A904194EA9D2945C61D60"
  m_functionName: 
  m_description: 'PBR Core Node


    Based on Jordan Stevens functions for Physical
    Based Rendering'
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 0
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_customEdited: 0
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 0
  m_headerStyle: 2
  m_headerColor: {r: 0.7647059, g: 0.30588236, b: 0, a: 1}
  m_customNodeCategory: Amplify Shader Pack
  m_previewPosition: 0
  m_hidden: 0
  m_url: 
