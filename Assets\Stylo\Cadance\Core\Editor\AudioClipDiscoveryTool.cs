using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Stylo.Cadance;

namespace Stylo.Cadance.Editor
{
    /// <summary>
    /// Tool to discover and assign AudioClip references to CadanceAssets based on source clip names.
    /// </summary>
    public class AudioClipDiscoveryTool : EditorWindow
    {
        private Vector2 scrollPosition;
        private List<AudioClipMapping> mappings = new List<AudioClipMapping>();
        private bool discoveryComplete = false;
        private int totalCadanceAssets = 0;
        private int resolvedMappings = 0;
        
        [MenuItem("Stylo/Cadance/Tools/AudioClip Discovery Tool", false, 102)]
        public static void ShowWindow()
        {
            GetWindow<AudioClipDiscoveryTool>("AudioClip Discovery Tool");
        }
        
        private void OnGUI()
        {
            EditorGUILayout.LabelField("AudioClip Discovery Tool", EditorStyles.boldLabel);
            EditorGUILayout.HelpBox("This tool finds Unity AudioClip assets that match the source clip names in CadanceAssets and allows automatic assignment.", MessageType.Info);
            
            EditorGUILayout.Space();
            
            if (GUILayout.Button("Discover AudioClip Mappings"))
            {
                DiscoverMappings();
            }
            
            EditorGUILayout.Space();
            
            if (discoveryComplete)
            {
                EditorGUILayout.LabelField($"Discovery Results: {resolvedMappings}/{totalCadanceAssets} CadanceAssets can be resolved", EditorStyles.boldLabel);
                
                if (resolvedMappings > 0)
                {
                    EditorGUILayout.Space();
                    
                    if (GUILayout.Button("Apply All Mappings"))
                    {
                        ApplyAllMappings();
                    }
                    
                    EditorGUILayout.Space();
                }
                
                scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);
                
                foreach (var mapping in mappings)
                {
                    DrawMapping(mapping);
                }
                
                EditorGUILayout.EndScrollView();
            }
        }
        
        private void DiscoverMappings()
        {
            mappings.Clear();
            discoveryComplete = false;
            totalCadanceAssets = 0;
            resolvedMappings = 0;
            
            // Find all CadanceAssets
            string[] cadanceGuids = AssetDatabase.FindAssets("t:CadanceAsset");
            totalCadanceAssets = cadanceGuids.Length;
            
            Debug.Log($"[AudioClip Discovery] Found {totalCadanceAssets} CadanceAssets to process");
            
            // Find all AudioClips in the project
            string[] audioClipGuids = AssetDatabase.FindAssets("t:AudioClip");
            var audioClipDatabase = new Dictionary<string, AudioClip>();
            
            foreach (string guid in audioClipGuids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                var audioClip = AssetDatabase.LoadAssetAtPath<AudioClip>(path);
                if (audioClip != null)
                {
                    // Store by filename without extension for matching
                    string filename = Path.GetFileNameWithoutExtension(audioClip.name);
                    if (!audioClipDatabase.ContainsKey(filename))
                    {
                        audioClipDatabase[filename] = audioClip;
                    }
                }
            }
            
            Debug.Log($"[AudioClip Discovery] Built database of {audioClipDatabase.Count} AudioClips");
            
            // Process each CadanceAsset
            foreach (string guid in cadanceGuids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                var cadanceAsset = AssetDatabase.LoadAssetAtPath<CadanceAsset>(path);
                
                if (cadanceAsset != null)
                {
                    var mapping = new AudioClipMapping
                    {
                        cadanceAsset = cadanceAsset,
                        cadanceAssetPath = path,
                        sourceClipName = cadanceAsset.SourceClipName,
                        currentAudioClip = cadanceAsset.SourceClip
                    };
                    
                    // Try to find matching AudioClip
                    if (!string.IsNullOrEmpty(cadanceAsset.SourceClipName))
                    {
                        // Try exact match first
                        if (audioClipDatabase.TryGetValue(cadanceAsset.SourceClipName, out AudioClip exactMatch))
                        {
                            mapping.suggestedAudioClip = exactMatch;
                            mapping.matchType = MatchType.Exact;
                            resolvedMappings++;
                        }
                        else
                        {
                            // Try partial matches
                            var partialMatches = audioClipDatabase.Where(kvp => 
                                kvp.Key.Contains(cadanceAsset.SourceClipName) || 
                                cadanceAsset.SourceClipName.Contains(kvp.Key)
                            ).ToList();
                            
                            if (partialMatches.Count > 0)
                            {
                                mapping.suggestedAudioClip = partialMatches.First().Value;
                                mapping.matchType = MatchType.Partial;
                                mapping.alternativeMatches = partialMatches.Select(kvp => kvp.Value).ToList();
                                resolvedMappings++;
                            }
                            else
                            {
                                mapping.matchType = MatchType.None;
                            }
                        }
                    }
                    else
                    {
                        mapping.matchType = MatchType.NoSourceName;
                    }
                    
                    mappings.Add(mapping);
                }
            }
            
            discoveryComplete = true;
            Debug.Log($"[AudioClip Discovery] Complete. Resolved {resolvedMappings}/{totalCadanceAssets} mappings");
        }
        
        private void DrawMapping(AudioClipMapping mapping)
        {
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            
            // Header with match status
            string statusIcon = GetMatchIcon(mapping.matchType);
            EditorGUILayout.LabelField($"{statusIcon} {mapping.cadanceAsset.name}", EditorStyles.boldLabel);
            
            // CadanceAsset info
            EditorGUILayout.LabelField($"Path: {mapping.cadanceAssetPath}", EditorStyles.miniLabel);
            EditorGUILayout.LabelField($"Source Clip Name: {mapping.sourceClipName}", EditorStyles.miniLabel);
            
            // Current AudioClip
            EditorGUI.BeginDisabledGroup(true);
            EditorGUILayout.ObjectField("Current AudioClip", mapping.currentAudioClip, typeof(AudioClip), false);
            EditorGUI.EndDisabledGroup();
            
            // Suggested AudioClip
            if (mapping.suggestedAudioClip != null)
            {
                EditorGUILayout.LabelField($"Match Type: {mapping.matchType}", EditorStyles.miniLabel);
                
                mapping.suggestedAudioClip = (AudioClip)EditorGUILayout.ObjectField("Suggested AudioClip", mapping.suggestedAudioClip, typeof(AudioClip), false);
                
                EditorGUILayout.BeginHorizontal();
                
                if (GUILayout.Button("Apply Mapping"))
                {
                    ApplyMapping(mapping);
                }
                
                if (mapping.alternativeMatches != null && mapping.alternativeMatches.Count > 1)
                {
                    if (GUILayout.Button("Show Alternatives"))
                    {
                        ShowAlternativeMatches(mapping);
                    }
                }
                
                EditorGUILayout.EndHorizontal();
            }
            else
            {
                EditorGUILayout.LabelField("No matching AudioClip found", EditorStyles.miniLabel);
                
                // Manual assignment
                mapping.suggestedAudioClip = (AudioClip)EditorGUILayout.ObjectField("Manual Assignment", mapping.suggestedAudioClip, typeof(AudioClip), false);
                
                if (mapping.suggestedAudioClip != null)
                {
                    if (GUILayout.Button("Apply Manual Assignment"))
                    {
                        ApplyMapping(mapping);
                    }
                }
            }
            
            EditorGUILayout.EndVertical();
            EditorGUILayout.Space();
        }
        
        private void ApplyMapping(AudioClipMapping mapping)
        {
            if (mapping.suggestedAudioClip != null && mapping.cadanceAsset != null)
            {
                mapping.cadanceAsset.SourceClip = mapping.suggestedAudioClip;
                EditorUtility.SetDirty(mapping.cadanceAsset);
                mapping.currentAudioClip = mapping.suggestedAudioClip;
                
                Debug.Log($"[AudioClip Discovery] Applied mapping: {mapping.cadanceAsset.name} -> {mapping.suggestedAudioClip.name}");
            }
        }
        
        private void ApplyAllMappings()
        {
            int appliedCount = 0;
            
            foreach (var mapping in mappings)
            {
                if (mapping.suggestedAudioClip != null && mapping.cadanceAsset != null && mapping.currentAudioClip == null)
                {
                    ApplyMapping(mapping);
                    appliedCount++;
                }
            }
            
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
            
            Debug.Log($"[AudioClip Discovery] Applied {appliedCount} mappings");
            EditorUtility.DisplayDialog("AudioClip Discovery", $"Applied {appliedCount} AudioClip mappings successfully!", "OK");
        }
        
        private void ShowAlternativeMatches(AudioClipMapping mapping)
        {
            if (mapping.alternativeMatches == null || mapping.alternativeMatches.Count == 0) return;
            
            GenericMenu menu = new GenericMenu();
            
            foreach (var alternative in mapping.alternativeMatches)
            {
                menu.AddItem(new GUIContent(alternative.name), false, () => {
                    mapping.suggestedAudioClip = alternative;
                    Repaint();
                });
            }
            
            menu.ShowAsContext();
        }
        
        private string GetMatchIcon(MatchType matchType)
        {
            switch (matchType)
            {
                case MatchType.Exact: return "✅";
                case MatchType.Partial: return "⚠️";
                case MatchType.None: return "❌";
                case MatchType.NoSourceName: return "❓";
                default: return "?";
            }
        }
    }
    
    public enum MatchType
    {
        Exact,
        Partial,
        None,
        NoSourceName
    }
    
    public class AudioClipMapping
    {
        public CadanceAsset cadanceAsset;
        public string cadanceAssetPath;
        public string sourceClipName;
        public AudioClip currentAudioClip;
        public AudioClip suggestedAudioClip;
        public MatchType matchType;
        public List<AudioClip> alternativeMatches;
    }
}
