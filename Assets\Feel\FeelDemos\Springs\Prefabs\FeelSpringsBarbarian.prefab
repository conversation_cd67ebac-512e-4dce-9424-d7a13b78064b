%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &179822598583166227
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2703071787956424704}
  - component: {fileID: 5715691900134983876}
  - component: {fileID: 3840194315038740251}
  - component: {fileID: 4658580690964711303}
  m_Layer: 0
  m_Name: Barbarian
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2703071787956424704
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 179822598583166227}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7350535469641195996}
  - {fileID: 9184586011331847864}
  m_Father: {fileID: 9011976935958460729}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!95 &5715691900134983876
Animator:
  serializedVersion: 5
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 179822598583166227}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: 870fdb8f8364af4408c9ae7eb1a234f8, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!114 &3840194315038740251
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 179822598583166227}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9c77c6d59898ea34989b338214db7de1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  MinimumRandomRange: 0
  MaximumRandomRange: 1
  AnimationLayerID: 0
  OffsetOnStart: 1
  OffsetOnEnable: 0
  DisableAfterOffset: 1
--- !u!114 &4658580690964711303
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 179822598583166227}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c6a00a894837bd744b10fbeb03d1287b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Target: {fileID: 0}
  TimeScaleMode: 1
  ChannelMode: 1
  Channel: 0
  MMChannelDefinition: {fileID: 11400000, guid: 86637982991ef464497bf77bd49b9f4c,
    type: 2}
  FloatSpring:
    Damping: 0.4
    Frequency: 6
    ClampSettings:
      ClampMin: 0
      ClampMinValue: 0
      ClampMinInitial: 0
      ClampMinBounce: 0
      ClampMax: 0
      ClampMaxValue: 10
      ClampMaxInitial: 0
      ClampMaxBounce: 0
    SpringDebug:
      CurrentValue: 0
      TargetValue: 0
    UnifiedSpring: 0
    CurrentValueDisplay: 0
    TargetValueDisplay: 0
    VelocityDisplay: 0
  MoveToRandomValue: {x: -2, y: 2}
  BumpAmountRandomValue: {x: 20, y: 100}
  TestMoveToValue: 2
  MoveToToolbar: 0
  TestBumpAmount: 75
  BumpToToolbar: 0
  OtherControlsToToolbar: 0
--- !u!1 &254890610873914053
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6448741691784823248}
  m_Layer: 0
  m_Name: Spine2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6448741691784823248
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 254890610873914053}
  serializedVersion: 2
  m_LocalRotation: {x: -0.083741024, y: -0.0009555449, z: -0.0048032, w: 0.9964756}
  m_LocalPosition: {x: -3.013265e-11, y: 0.006337032, z: -0.00011147443}
  m_LocalScale: {x: 0.99999994, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 159989199989898306}
  - {fileID: 3893268375968472389}
  - {fileID: 2046961029356303982}
  m_Father: {fileID: 6710152009144201313}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &821701823524657173
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8180635984569551132}
  - component: {fileID: 8680259112994876487}
  m_Layer: 0
  m_Name: BumpFeedback
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8180635984569551132
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 821701823524657173}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 989563652246574382}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &8680259112994876487
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 821701823524657173}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6da43522623d4704e979466dc7650b65, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Feedbacks: []
  InitializationMode: 2
  AutoInitialization: 1
  SafeMode: 3
  Direction: 0
  AutoChangeDirectionOnEnd: 0
  AutoPlayOnStart: 0
  AutoPlayOnEnable: 0
  ForceTimescaleMode: 0
  ForcedTimescaleMode: 1
  DurationMultiplier: 1
  RandomizeDuration: 0
  RandomDurationMultiplier: {x: 0.5, y: 1.5}
  DisplayFullDurationDetails: 0
  PlayerTimescaleMode: 1
  OnlyPlayIfWithinRange: 0
  RangeCenter: {fileID: 0}
  RangeDistance: 5
  UseRangeFalloff: 0
  RangeFalloff:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  RemapRangeFalloff: {x: 0, y: 1}
  IgnoreRangeEvents: 0
  CooldownDuration: 0
  InitialDelay: 0
  CanPlay: 1
  CanPlayWhileAlreadyPlaying: 1
  ChanceToPlay: 100
  FeedbacksIntensity: 1
  Events:
    TriggerMMFeedbacksEvents: 0
    TriggerUnityEvents: 1
    OnPlay:
      m_PersistentCalls:
        m_Calls: []
    OnPause:
      m_PersistentCalls:
        m_Calls: []
    OnResume:
      m_PersistentCalls:
        m_Calls: []
    OnRevert:
      m_PersistentCalls:
        m_Calls: []
    OnComplete:
      m_PersistentCalls:
        m_Calls: []
    OnRestoreInitialValues:
      m_PersistentCalls:
        m_Calls: []
    OnSkipToTheEnd:
      m_PersistentCalls:
        m_Calls: []
    OnInitializationComplete:
      m_PersistentCalls:
        m_Calls: []
    OnEnable:
      m_PersistentCalls:
        m_Calls: []
    OnDisable:
      m_PersistentCalls:
        m_Calls: []
  DebugActive: 0
  FeedbacksList:
  - rid: 1779414967430938723
  KeepPlayModeChanges: 0
  PerformanceMode: 0
  StopFeedbacksOnDisable: 0
  PlayCount: 0
  references:
    version: 2
    RefIds:
    - rid: 1779414967430938723
      type: {class: MMF_Events, ns: MoreMountains.Feedbacks, asm: MoreMountains.Tools}
      data:
        Active: 1
        UniqueID: 1098704421
        Label: Unity Events
        ChannelMode: 0
        Channel: 0
        MMChannelDefinition: {fileID: 0}
        Chance: 100
        DisplayColor: {r: 0, g: 0, b: 0, a: 1}
        Timing:
          TimescaleMode: 0
          ExcludeFromHoldingPauses: 0
          ContributeToTotalDuration: 1
          InitialDelay: 0
          CooldownDuration: 0
          InterruptsOnStop: 1
          NumberOfRepeats: 0
          RepeatForever: 0
          DelayBetweenRepeats: 1
          PlayCount: 0
          LimitPlayCount: 0
          MaxPlayCount: 3
          SetPlayCountToZeroOnReset: 0
          MMFeedbacksDirectionCondition: 0
          PlayDirection: 0
          ConstantIntensity: 0
          UseIntensityInterval: 0
          IntensityIntervalMin: 0
          IntensityIntervalMax: 0
          Sequence: {fileID: 0}
          TrackID: 0
          Quantized: 0
          TargetBPM: 120
        AutomatedTargetAcquisition:
          Mode: 0
          ChildIndex: 0
        RandomizeOutput: 0
        RandomMultiplier: {x: 0.8, y: 1}
        RandomizeDuration: 0
        RandomDurationMultiplier: {x: 0.5, y: 2}
        UseRange: 0
        RangeDistance: 5
        UseRangeFalloff: 0
        RangeFalloff:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        RemapRangeFalloff: {x: 0, y: 1}
        AutomaticShakerSetupButton:
          ButtonText: 
        Owner: {fileID: 8680259112994876487}
        DebugActive: 0
        PlayEvents:
          m_PersistentCalls:
            m_Calls:
            - m_Target: {fileID: 6276478178186640553}
              m_TargetAssemblyTypeName: MoreMountains.Feedbacks.MMSpringFloat, MoreMountains.Tools
              m_MethodName: BumpRandom
              m_Mode: 1
              m_Arguments:
                m_ObjectArgument: {fileID: 0}
                m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
                m_IntArgument: 0
                m_FloatArgument: 0
                m_StringArgument: 
                m_BoolArgument: 0
              m_CallState: 2
        StopEvents:
          m_PersistentCalls:
            m_Calls: []
        InitializationEvents:
          m_PersistentCalls:
            m_Calls: []
        ResetEvents:
          m_PersistentCalls:
            m_Calls: []
--- !u!1 &1096917697970610639
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3758768483434237904}
  - component: {fileID: 2247537714666434388}
  - component: {fileID: 5156354470931725500}
  m_Layer: 0
  m_Name: Horn
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3758768483434237904
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1096917697970610639}
  serializedVersion: 2
  m_LocalRotation: {x: 0.000000005587935, y: 1.1641531e-10, z: -3.9290168e-10, w: 1}
  m_LocalPosition: {x: -0, y: -2.9802322e-10, z: 0.0000000016298145}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7933312363712594881}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &2247537714666434388
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1096917697970610639}
  m_Mesh: {fileID: 1629092342830211672, guid: 5c0669bd66f517143b7f3a11cbd9c17e, type: 3}
--- !u!23 &5156354470931725500
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1096917697970610639}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 20729848b1209504ebb9c98d05470832, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &1313385098477994309
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7414774537373631883}
  - component: {fileID: 6276478178186640553}
  - component: {fileID: 4147204758188181400}
  - component: {fileID: 763879633520640491}
  m_Layer: 0
  m_Name: SquashAndStretch
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7414774537373631883
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1313385098477994309}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 9011976935958460729}
  m_Father: {fileID: 989563652246574382}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &6276478178186640553
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1313385098477994309}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7e08cd56fd2838f45bb3577428403529, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Target: {fileID: 0}
  TimeScaleMode: 1
  ChannelMode: 0
  Channel: 0
  MMChannelDefinition: {fileID: 0}
  FloatSpring:
    Damping: 0.1
    Frequency: 4
    ClampSettings:
      ClampMin: 0
      ClampMinValue: 0
      ClampMinInitial: 0
      ClampMinBounce: 0
      ClampMax: 0
      ClampMaxValue: 10
      ClampMaxInitial: 0
      ClampMaxBounce: 0
    SpringDebug:
      CurrentValue: 0
      TargetValue: 0
    UnifiedSpring: 0
    CurrentValueDisplay: 0
    TargetValueDisplay: 0
    VelocityDisplay: 0
  MoveToRandomValue: {x: -2, y: 2}
  BumpAmountRandomValue: {x: 10, y: 20}
  TestMoveToValue: 2
  MoveToToolbar: 0
  TestBumpAmount: -20
  BumpToToolbar: 0
  OtherControlsToToolbar: 0
  Axis: 3
--- !u!114 &4147204758188181400
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1313385098477994309}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fcada72f36389ee498301db873e7cd1a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Target: {fileID: 0}
  TimeScaleMode: 1
  ChannelMode: 0
  Channel: 0
  MMChannelDefinition: {fileID: 0}
  SpringVector3:
    SeparateAxis: 0
    UnifiedSpring:
      Damping: 0.4
      Frequency: 6
      ClampSettings:
        ClampMin: 0
        ClampMinValue: 0
        ClampMinInitial: 0
        ClampMinBounce: 0
        ClampMax: 0
        ClampMaxValue: 10
        ClampMaxInitial: 0
        ClampMaxBounce: 0
      SpringDebug:
        CurrentValue: 0
        TargetValue: 0
      UnifiedSpring: 1
      CurrentValueDisplay: 0
      TargetValueDisplay: 0
      VelocityDisplay: 0
    SpringX:
      Damping: 0.4
      Frequency: 6
      ClampSettings:
        ClampMin: 0
        ClampMinValue: 0
        ClampMinInitial: 0
        ClampMinBounce: 0
        ClampMax: 0
        ClampMaxValue: 10
        ClampMaxInitial: 0
        ClampMaxBounce: 0
      SpringDebug:
        CurrentValue: 0
        TargetValue: 0
      UnifiedSpring: 0
      CurrentValueDisplay: 0
      TargetValueDisplay: 0
      VelocityDisplay: 0
    SpringY:
      Damping: 0.4
      Frequency: 6
      ClampSettings:
        ClampMin: 0
        ClampMinValue: 0
        ClampMinInitial: 0
        ClampMinBounce: 0
        ClampMax: 0
        ClampMaxValue: 10
        ClampMaxInitial: 0
        ClampMaxBounce: 0
      SpringDebug:
        CurrentValue: 0
        TargetValue: 0
      UnifiedSpring: 0
      CurrentValueDisplay: 0
      TargetValueDisplay: 0
      VelocityDisplay: 0
    SpringZ:
      Damping: 0.4
      Frequency: 6
      ClampSettings:
        ClampMin: 0
        ClampMinValue: 0
        ClampMinInitial: 0
        ClampMinBounce: 0
        ClampMax: 0
        ClampMaxValue: 10
        ClampMaxInitial: 0
        ClampMaxBounce: 0
      SpringDebug:
        CurrentValue: 0
        TargetValue: 0
      UnifiedSpring: 0
      CurrentValueDisplay: 0
      TargetValueDisplay: 0
      VelocityDisplay: 0
  MoveToRandomValueMin: {x: -2, y: -2, z: -2}
  MoveToRandomValueMax: {x: 2, y: 2, z: 2}
  BumpAmountRandomValueMin: {x: -20, y: 20, z: -20}
  BumpAmountRandomValueMax: {x: 20, y: 20, z: 20}
  TestMoveToValue: {x: 2, y: 2, z: 2}
  MoveToToolbar: 0
  TestBumpAmount: {x: 75, y: 100, z: 50}
  BumpToToolbar: 0
  OtherControlsToToolbar: 0
  Space: 0
--- !u!114 &763879633520640491
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1313385098477994309}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 77e19943fc6c12249aa90e2f613265ad, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Target: {fileID: 0}
  TimeScaleMode: 1
  ChannelMode: 0
  Channel: 0
  MMChannelDefinition: {fileID: 0}
  SpringVector3:
    SeparateAxis: 0
    UnifiedSpring:
      Damping: 0.4
      Frequency: 6
      ClampSettings:
        ClampMin: 0
        ClampMinValue: 0
        ClampMinInitial: 0
        ClampMinBounce: 0
        ClampMax: 0
        ClampMaxValue: 10
        ClampMaxInitial: 0
        ClampMaxBounce: 0
      SpringDebug:
        CurrentValue: 0
        TargetValue: 0
      UnifiedSpring: 1
      CurrentValueDisplay: 0
      TargetValueDisplay: 0
      VelocityDisplay: 0
    SpringX:
      Damping: 0.4
      Frequency: 6
      ClampSettings:
        ClampMin: 0
        ClampMinValue: 0
        ClampMinInitial: 0
        ClampMinBounce: 0
        ClampMax: 0
        ClampMaxValue: 10
        ClampMaxInitial: 0
        ClampMaxBounce: 0
      SpringDebug:
        CurrentValue: 0
        TargetValue: 0
      UnifiedSpring: 0
      CurrentValueDisplay: 0
      TargetValueDisplay: 0
      VelocityDisplay: 0
    SpringY:
      Damping: 0.4
      Frequency: 6
      ClampSettings:
        ClampMin: 0
        ClampMinValue: 0
        ClampMinInitial: 0
        ClampMinBounce: 0
        ClampMax: 0
        ClampMaxValue: 10
        ClampMaxInitial: 0
        ClampMaxBounce: 0
      SpringDebug:
        CurrentValue: 0
        TargetValue: 0
      UnifiedSpring: 0
      CurrentValueDisplay: 0
      TargetValueDisplay: 0
      VelocityDisplay: 0
    SpringZ:
      Damping: 0.4
      Frequency: 6
      ClampSettings:
        ClampMin: 0
        ClampMinValue: 0
        ClampMinInitial: 0
        ClampMinBounce: 0
        ClampMax: 0
        ClampMaxValue: 10
        ClampMaxInitial: 0
        ClampMaxBounce: 0
      SpringDebug:
        CurrentValue: 0
        TargetValue: 0
      UnifiedSpring: 0
      CurrentValueDisplay: 0
      TargetValueDisplay: 0
      VelocityDisplay: 0
  MoveToRandomValueMin: {x: -2, y: -2, z: -2}
  MoveToRandomValueMax: {x: 2, y: 2, z: 2}
  BumpAmountRandomValueMin: {x: -20, y: 20, z: -20}
  BumpAmountRandomValueMax: {x: 20, y: 20, z: 20}
  TestMoveToValue: {x: 2, y: 2, z: 2}
  MoveToToolbar: 0
  TestBumpAmount: {x: 75, y: 100, z: 50}
  BumpToToolbar: 0
  OtherControlsToToolbar: 0
  Space: 1
--- !u!1 &1439932170198048796
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6710152009144201313}
  m_Layer: 0
  m_Name: Spine1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6710152009144201313
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1439932170198048796}
  serializedVersion: 2
  m_LocalRotation: {x: 0.52535033, y: -0.4732939, z: 0.5253502, w: 0.47329402}
  m_LocalPosition: {x: -0.006644067, y: 0.00055199966, z: -0.0000000010752316}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6448741691784823248}
  - {fileID: 8742905626117344227}
  - {fileID: 6143234244476452233}
  m_Father: {fileID: 9091176427198875475}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1447301689752048967
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4444381413050081090}
  m_Layer: 0
  m_Name: Head
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4444381413050081090
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1447301689752048967}
  serializedVersion: 2
  m_LocalRotation: {x: -0.05560565, y: -0.00000011907341, z: 0.0000000066058767, w: 0.99845284}
  m_LocalPosition: {x: -1.7353159e-11, y: 0.003251105, z: 9.713403e-12}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7933312363712594881}
  m_Father: {fileID: 159989199989898306}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1458378853640103279
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 159989199989898306}
  m_Layer: 0
  m_Name: Neck
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &159989199989898306
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1458378853640103279}
  serializedVersion: 2
  m_LocalRotation: {x: 0.06658877, y: 0.00000011881817, z: -0.000000007753371, w: 0.9977805}
  m_LocalPosition: {x: -2.9685907e-11, y: 0.0049987007, z: 4.667072e-11}
  m_LocalScale: {x: 0.99999994, y: 0.99999994, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4444381413050081090}
  m_Father: {fileID: 6448741691784823248}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1807332811245169552
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8799311143667136152}
  m_Layer: 0
  m_Name: forearm.R
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8799311143667136152
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1807332811245169552}
  serializedVersion: 2
  m_LocalRotation: {x: 0.012385438, y: 0.033361364, z: -0.18670169, w: 0.98177195}
  m_LocalPosition: {x: -4.4587067e-10, y: 0.0029962005, z: 1.3038516e-10}
  m_LocalScale: {x: 0.99999994, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3558756004602860473}
  m_Father: {fileID: 943871142983233734}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2137277572707752317
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4247256853780524853}
  m_Layer: 0
  m_Name: shin.L
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4247256853780524853
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2137277572707752317}
  serializedVersion: 2
  m_LocalRotation: {x: 0.372336, y: -0.013690298, z: -0.023617411, w: 0.92769647}
  m_LocalPosition: {x: 9.150244e-10, y: 0.0027616709, z: -3.4924594e-11}
  m_LocalScale: {x: 0.99999994, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3760746117907811551}
  m_Father: {fileID: 8742905626117344227}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2174754133586757635
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1909721956282768540}
  m_Layer: 0
  m_Name: Hips
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1909721956282768540
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2174754133586757635}
  serializedVersion: 2
  m_LocalRotation: {x: 0.7071069, y: -4.3297796e-17, z: -0.7071067, w: -4.329781e-17}
  m_LocalPosition: {x: -0.0057083587, y: 0.00055199966, z: -8.5603274e-10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 53132730025419800}
  - {fileID: 5427677393536985795}
  m_Father: {fileID: 9091176427198875475}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2610866975120619353
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9046493099410736913}
  m_Layer: 0
  m_Name: toe.L
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9046493099410736913
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2610866975120619353}
  serializedVersion: 2
  m_LocalRotation: {x: -0.000000029683203, y: 0.9907687, z: -0.13556333, w: -0.00000013033728}
  m_LocalPosition: {x: -5.973561e-11, y: 0.0025500336, z: 4.773028e-11}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3760746117907811551}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2796694883780573442
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5687797213681456353}
  - component: {fileID: 7151212636818091981}
  - component: {fileID: 1504697694298901365}
  m_Layer: 0
  m_Name: Axe1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5687797213681456353
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2796694883780573442}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0.7071068, z: 0, w: 0.7071068}
  m_LocalPosition: {x: 0.0004, y: -0.0004, z: 0.004}
  m_LocalScale: {x: 0.5, y: 0.5, z: 0.5}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6354106187788528312}
  m_LocalEulerAnglesHint: {x: 0, y: 90, z: 0}
--- !u!33 &7151212636818091981
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2796694883780573442}
  m_Mesh: {fileID: -5654033164078409323, guid: 5c0669bd66f517143b7f3a11cbd9c17e, type: 3}
--- !u!23 &1504697694298901365
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2796694883780573442}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: -1738910134290924054, guid: 5c0669bd66f517143b7f3a11cbd9c17e, type: 3}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &3005571129702610467
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5427677393536985795}
  m_Layer: 0
  m_Name: pelvis.R
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5427677393536985795
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3005571129702610467}
  serializedVersion: 2
  m_LocalRotation: {x: 0.06744761, y: 0.12816519, z: 0.6346243, w: 0.7591288}
  m_LocalPosition: {x: 1.7886136e-17, y: 2.9802322e-10, z: -8.881784e-18}
  m_LocalScale: {x: 1.0932609, y: 1.0932604, z: 1.0932608}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1909721956282768540}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3136519201946301115
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 53132730025419800}
  m_Layer: 0
  m_Name: pelvis.L
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &53132730025419800
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3136519201946301115}
  serializedVersion: 2
  m_LocalRotation: {x: 0.06744762, y: -0.12816529, z: -0.6346244, w: 0.7591287}
  m_LocalPosition: {x: 1.7886136e-17, y: 2.9802322e-10, z: -8.881784e-18}
  m_LocalScale: {x: 1.0932609, y: 1.093261, z: 1.0932608}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1909721956282768540}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3437313740837796939
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3893268375968472389}
  m_Layer: 0
  m_Name: shoulder.L
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3893268375968472389
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3437313740837796939}
  serializedVersion: 2
  m_LocalRotation: {x: -0.53245175, y: 0.45336503, z: 0.4635006, w: 0.5441714}
  m_LocalPosition: {x: -0.0012289189, y: 0.0044502676, z: 0.00078612217}
  m_LocalScale: {x: 1.038816, y: 1.0388157, z: 1.0388159}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4871661452960679300}
  m_Father: {fileID: 6448741691784823248}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3590604145256488034
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7933312363712594881}
  - component: {fileID: 6994337788199226533}
  - component: {fileID: 20315182920541326}
  m_Layer: 0
  m_Name: HelmetBody
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7933312363712594881
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3590604145256488034}
  serializedVersion: 2
  m_LocalRotation: {x: -0.72744083, y: -0.0000774981, z: 0.009705661, w: 0.68610185}
  m_LocalPosition: {x: -0.0009, y: 0.0023, z: -0}
  m_LocalScale: {x: 1.1, y: 1.1, z: 1.1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3804235455050890046}
  - {fileID: 3758768483434237904}
  - {fileID: 2852425430542177666}
  m_Father: {fileID: 4444381413050081090}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &6994337788199226533
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3590604145256488034}
  m_Mesh: {fileID: 3623846107799637885, guid: 5c0669bd66f517143b7f3a11cbd9c17e, type: 3}
--- !u!23 &20315182920541326
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3590604145256488034}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 8b000dda6e5033841a878fe092a16c32, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &3644135525482154014
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4280743042552247397}
  m_Layer: 0
  m_Name: foot.R
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4280743042552247397
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3644135525482154014}
  serializedVersion: 2
  m_LocalRotation: {x: -0.78840274, y: 0.0013634749, z: -0.017084325, w: 0.6149206}
  m_LocalPosition: {x: -1.9470463e-10, y: 0.002264711, z: -9.3132255e-12}
  m_LocalScale: {x: 0.99999994, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3914246223808136832}
  m_Father: {fileID: 4945329780020109011}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4149414131508171539
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2046961029356303982}
  m_Layer: 0
  m_Name: shoulder.R
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2046961029356303982
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4149414131508171539}
  serializedVersion: 2
  m_LocalRotation: {x: -0.5324517, y: -0.4533651, z: -0.4635003, w: 0.54417163}
  m_LocalPosition: {x: 0.0012289194, y: 0.004450269, z: 0.0007861215}
  m_LocalScale: {x: 1.0388161, y: 1.0388161, z: 1.038816}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 943871142983233734}
  m_Father: {fileID: 6448741691784823248}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4363425685211870516
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8824808082363890012}
  - component: {fileID: 4363281669774100735}
  - component: {fileID: 2862054025981116939}
  m_Layer: 0
  m_Name: Axe2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8824808082363890012
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4363425685211870516}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0.7071068, z: 0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: -0.0004, z: 0.004}
  m_LocalScale: {x: 0.5, y: 0.5, z: 0.5}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3558756004602860473}
  m_LocalEulerAnglesHint: {x: 0, y: 90, z: 0}
--- !u!33 &4363281669774100735
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4363425685211870516}
  m_Mesh: {fileID: 30401544605609000, guid: 5c0669bd66f517143b7f3a11cbd9c17e, type: 3}
--- !u!23 &2862054025981116939
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4363425685211870516}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: -5259558859319161221, guid: 5c0669bd66f517143b7f3a11cbd9c17e, type: 3}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &4505752789142751824
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9091176427198875475}
  m_Layer: 0
  m_Name: Root
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9091176427198875475
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4505752789142751824}
  serializedVersion: 2
  m_LocalRotation: {x: 7.850462e-17, y: 0.7071067, z: -7.8504615e-17, w: 0.7071068}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1909721956282768540}
  - {fileID: 6710152009144201313}
  m_Father: {fileID: 7350535469641195996}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5086218880000307286
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2852425430542177666}
  - component: {fileID: 2176733390694224876}
  - component: {fileID: 7087992932158486536}
  m_Layer: 0
  m_Name: HornRing
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2852425430542177666
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5086218880000307286}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0000000026958495, y: 2.5273592e-10, z: -4.4228787e-10, w: 1}
  m_LocalPosition: {x: -0, y: -2.9802322e-10, z: 0.0000000016298145}
  m_LocalScale: {x: 1.3818612, y: 1.3818612, z: 1.3818613}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7933312363712594881}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &2176733390694224876
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5086218880000307286}
  m_Mesh: {fileID: 8832755778838038794, guid: 5c0669bd66f517143b7f3a11cbd9c17e, type: 3}
--- !u!23 &7087992932158486536
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5086218880000307286}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 4237604609199654394, guid: 5c0669bd66f517143b7f3a11cbd9c17e, type: 3}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &5244209524161266759
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 989563652246574382}
  - component: {fileID: 7429578384151552757}
  m_Layer: 0
  m_Name: FeelSpringsBarbarian
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &989563652246574382
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5244209524161266759}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7414774537373631883}
  - {fileID: 7665928146807215912}
  - {fileID: 8180635984569551132}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!65 &7429578384151552757
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5244209524161266759}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 1
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.1, y: 0.1, z: 0.18}
  m_Center: {x: 0, y: 0.05, z: 0}
--- !u!1 &5249514072387507915
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9184586011331847864}
  - component: {fileID: 3323628693149010345}
  - component: {fileID: 3884096725511753384}
  - component: {fileID: 2781782197733923640}
  m_Layer: 0
  m_Name: Body
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9184586011331847864
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5249514072387507915}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071067, y: -0.000000030908616, z: 0.000000030908623, w: 0.7071068}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 100, y: 100, z: 100}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2703071787956424704}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &3323628693149010345
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5249514072387507915}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 8bccc2efd7c974a43b3f23c9dc1b5f22, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 2959467708922500008, guid: 5c0669bd66f517143b7f3a11cbd9c17e, type: 3}
  m_Bones:
  - {fileID: 9091176427198875475}
  - {fileID: 6710152009144201313}
  - {fileID: 8742905626117344227}
  - {fileID: 4247256853780524853}
  - {fileID: 3760746117907811551}
  - {fileID: 9046493099410736913}
  - {fileID: 6448741691784823248}
  - {fileID: 3893268375968472389}
  - {fileID: 4871661452960679300}
  - {fileID: 1879787553996844401}
  - {fileID: 6354106187788528312}
  - {fileID: 159989199989898306}
  - {fileID: 4444381413050081090}
  - {fileID: 2046961029356303982}
  - {fileID: 943871142983233734}
  - {fileID: 8799311143667136152}
  - {fileID: 3558756004602860473}
  - {fileID: 6143234244476452233}
  - {fileID: 4945329780020109011}
  - {fileID: 4280743042552247397}
  - {fileID: 3914246223808136832}
  - {fileID: 1909721956282768540}
  - {fileID: 53132730025419800}
  - {fileID: 5427677393536985795}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 9091176427198875475}
  m_AABB:
    m_Center: {x: -0.017674554, y: 0.0007363381, z: 0.00007583108}
    m_Extent: {x: 0.019980509, y: 0.014602819, z: 0.021799073}
  m_DirtyAABB: 0
--- !u!114 &3884096725511753384
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5249514072387507915}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8ad986206bb726846a202cfdedd282dc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  TargetType: 0
  TargetRenderer: {fileID: 3323628693149010345}
  TargetMaterialID: 0
  TargetImage: {fileID: 0}
  UseMaterialForRendering: 0
  TargetRawImage: {fileID: 0}
  TargetText: {fileID: 0}
  CacheMaterial: 1
  CreateMaterialInstance: 0
  TargetPropertyName: _EmissionForce
  PropertyType: 1
  X: 0
  Y: 0
  Z: 0
  W: 0
  ColorMode: 0
  ColorRamp:
    serializedVersion: 2
    key0: {r: 1, g: 1, b: 1, a: 1}
    key1: {r: 1, g: 1, b: 1, a: 1}
    key2: {r: 0, g: 0, b: 0, a: 0}
    key3: {r: 0, g: 0, b: 0, a: 0}
    key4: {r: 0, g: 0, b: 0, a: 0}
    key5: {r: 0, g: 0, b: 0, a: 0}
    key6: {r: 0, g: 0, b: 0, a: 0}
    key7: {r: 0, g: 0, b: 0, a: 0}
    ctime0: 0
    ctime1: 65535
    ctime2: 0
    ctime3: 0
    ctime4: 0
    ctime5: 0
    ctime6: 0
    ctime7: 0
    atime0: 0
    atime1: 65535
    atime2: 0
    atime3: 0
    atime4: 0
    atime5: 0
    atime6: 0
    atime7: 0
    m_Mode: 0
    m_ColorSpace: -1
    m_NumColorKeys: 2
    m_NumAlphaKeys: 2
  FromColor: {r: 0, g: 0, b: 0, a: 1}
  ToColor: {r: 1, g: 1, b: 1, a: 1}
  ControlMode: 5
  AddToInitialValue: 0
  UseUnscaledTime: 1
  RevertToInitialValueAfterEnd: 1
  UseMaterialPropertyBlocks: 0
  SpriteRendererTextureProperty: _MainTex
  SafeMode: 0
  Curve:
    MMTweenDefinitionType: 0
    MMTweenCurve: 0
    Curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    Initialized: 0
  MinValue: 0
  MaxValue: 5
  Duration: 1
  PingPongPauseDuration: 1
  LoopCurve:
    MMTweenDefinitionType: 0
    MMTweenCurve: 0
    Curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    Initialized: 0
  LoopStartValue: 0
  LoopEndValue: 5
  LoopDuration: 1
  LoopPauseDuration: 1
  DrivenLevel: 0
  Amplitude: {x: 0, y: 5}
  Frequency: {x: 1, y: 1}
  Shift: {x: 0, y: 1}
  RemapNoiseValues: 0
  RemapNoiseZero: 0
  RemapNoiseOne: 1
  OneTimeDuration: 1
  OneTimeAmplitude: 1
  OneTimeRemapMin: 0
  OneTimeRemapMax: 1
  OneTimeCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 0.5
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  OneTimeButton: 0
  DisableAfterOneTime: 0
  DisableGameObjectAfterOneTime: 0
  GetInitialValueOnOneTime: 0
  AudioAnalyzer: {fileID: 0}
  BeatID: 0
  AudioAnalyzerMultiplier: 1
  AudioAnalyzerOffset: 0
  AudioAnalyzerLerp: 60
  ToDestinationValue: 1
  ToDestinationDuration: 1
  ToDestinationCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 0.5
      value: 0.6
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  ToDestinationButton: 0
  DisableAfterToDestination: 0
  InitialValue: 0
  CurrentValue: 0
  CurrentValueNormalized: 0
  InitialColor: {r: 0, g: 0, b: 0, a: 0}
  PropertyID: 0
  PropertyFound: 0
  TargetMaterial: {fileID: 0}
  PingPong: 0
  LoopTime: 0
--- !u!114 &2781782197733923640
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5249514072387507915}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e157e875e121b174d903add782257618, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Target: {fileID: 3884096725511753384}
  TimeScaleMode: 1
  ChannelMode: 1
  Channel: 0
  MMChannelDefinition: {fileID: 11400000, guid: 9d5ac922c10fc194d832e790fa6200cc,
    type: 2}
  FloatSpring:
    Damping: 0.4
    Frequency: 6
    ClampSettings:
      ClampMin: 0
      ClampMinValue: 0
      ClampMinInitial: 0
      ClampMinBounce: 0
      ClampMax: 0
      ClampMaxValue: 10
      ClampMaxInitial: 0
      ClampMaxBounce: 0
    SpringDebug:
      CurrentValue: 0
      TargetValue: 0
    UnifiedSpring: 0
    CurrentValueDisplay: 0
    TargetValueDisplay: 0
    VelocityDisplay: 0
  MoveToRandomValue: {x: -2, y: 2}
  BumpAmountRandomValue: {x: 20, y: 100}
  TestMoveToValue: 2
  MoveToToolbar: 0
  TestBumpAmount: 75
  BumpToToolbar: 0
  OtherControlsToToolbar: 0
--- !u!1 &5290491763301312129
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9011976935958460729}
  m_Layer: 0
  m_Name: ModelContainer
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9011976935958460729
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5290491763301312129}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.030000001, y: 0.030000001, z: 0.030000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2703071787956424704}
  m_Father: {fileID: 7414774537373631883}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5459337854215817119
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1879787553996844401}
  m_Layer: 0
  m_Name: forearm.L
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1879787553996844401
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5459337854215817119}
  serializedVersion: 2
  m_LocalRotation: {x: 0.01238675, y: -0.033360623, z: 0.18671122, w: 0.9817701}
  m_LocalPosition: {x: -3.2014214e-10, y: 0.0029962019, z: 9.313225e-11}
  m_LocalScale: {x: 0.99999994, y: 0.9999999, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6354106187788528312}
  m_Father: {fileID: 4871661452960679300}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5527673617666273902
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3914246223808136832}
  m_Layer: 0
  m_Name: toe.R
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3914246223808136832
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5527673617666273902}
  serializedVersion: 2
  m_LocalRotation: {x: 0.000000029821386, y: 0.9907684, z: -0.13556573, w: 0.00000013031836}
  m_LocalPosition: {x: -5.932088e-10, y: 0.00254999, z: 1.2223608e-10}
  m_LocalScale: {x: 1, y: 1.0000004, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4280743042552247397}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5842302032279235595
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4945329780020109011}
  m_Layer: 0
  m_Name: shin.R
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4945329780020109011
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5842302032279235595}
  serializedVersion: 2
  m_LocalRotation: {x: 0.3723331, y: 0.013690396, z: 0.023617428, w: 0.92769766}
  m_LocalPosition: {x: 2.0605512e-10, y: 0.0027616706, z: 1.1874363e-10}
  m_LocalScale: {x: 0.99999994, y: 1, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4280743042552247397}
  m_Father: {fileID: 6143234244476452233}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5982734523937191478
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3760746117907811551}
  m_Layer: 0
  m_Name: foot.L
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3760746117907811551
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5982734523937191478}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7884056, y: -0.0013634062, z: 0.017084332, w: 0.614917}
  m_LocalPosition: {x: -4.8334187e-10, y: 0.002264713, z: -6.519258e-11}
  m_LocalScale: {x: 0.99999994, y: 0.99999994, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 9046493099410736913}
  m_Father: {fileID: 4247256853780524853}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6128270610742171048
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4871661452960679300}
  m_Layer: 0
  m_Name: upper_arm.L
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4871661452960679300
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6128270610742171048}
  serializedVersion: 2
  m_LocalRotation: {x: -0.120397225, y: -0.68839544, z: 0.25414288, w: 0.6686013}
  m_LocalPosition: {x: -0.00049671216, y: 0.009052149, z: -0.0023860903}
  m_LocalScale: {x: 0.99999994, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1879787553996844401}
  m_Father: {fileID: 3893268375968472389}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6659113880476171809
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6143234244476452233}
  m_Layer: 0
  m_Name: thigh.R
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6143234244476452233
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6659113880476171809}
  serializedVersion: 2
  m_LocalRotation: {x: 0.98124915, y: 0.01150783, z: -0.005541865, w: 0.19232002}
  m_LocalPosition: {x: 0.0049802503, y: -0.0010752982, z: 0.0008373951}
  m_LocalScale: {x: 1, y: 1.000001, z: 1.0000091}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4945329780020109011}
  m_Father: {fileID: 6710152009144201313}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6711110922082783416
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8742905626117344227}
  m_Layer: 0
  m_Name: thigh.L
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8742905626117344227
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6711110922082783416}
  serializedVersion: 2
  m_LocalRotation: {x: 0.981249, y: -0.011507733, z: 0.0055416054, w: 0.19232108}
  m_LocalPosition: {x: -0.0049802503, y: -0.0010752972, z: 0.0008373985}
  m_LocalScale: {x: 1, y: 0.9999993, z: 0.99999213}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4247256853780524853}
  m_Father: {fileID: 6710152009144201313}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7721908957556437621
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 943871142983233734}
  m_Layer: 0
  m_Name: upper_arm.R
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &943871142983233734
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7721908957556437621}
  serializedVersion: 2
  m_LocalRotation: {x: -0.12039725, y: 0.68839544, z: -0.25414282, w: 0.6686013}
  m_LocalPosition: {x: 0.0004967118, y: 0.009052149, z: -0.0023860857}
  m_LocalScale: {x: 0.99999994, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8799311143667136152}
  m_Father: {fileID: 2046961029356303982}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7895578275571615548
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1723446931294378392}
  - component: {fileID: 3006143133298671725}
  - component: {fileID: 7267623468395464979}
  m_Layer: 0
  m_Name: Model
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1723446931294378392
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7895578275571615548}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: -0.0064, z: 0}
  m_LocalScale: {x: 0.1, y: 0.007, z: 0.1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7665928146807215912}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &3006143133298671725
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7895578275571615548}
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &7267623468395464979
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7895578275571615548}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: -1580535799172915004, guid: 2b0c4bbc795c1cd458a0e991993226f3, type: 3}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &8145738667287533545
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6354106187788528312}
  m_Layer: 0
  m_Name: Hand.L
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6354106187788528312
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8145738667287533545}
  serializedVersion: 2
  m_LocalRotation: {x: -0.21624516, y: 0.9755092, z: 0.00028211597, w: -0.04024649}
  m_LocalPosition: {x: -5.315087e-11, y: 0.0027799564, z: 1.4784746e-10}
  m_LocalScale: {x: 1.0575902, y: 1.05759, z: 1.0575902}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5687797213681456353}
  m_Father: {fileID: 1879787553996844401}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8411616136048268515
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3558756004602860473}
  m_Layer: 0
  m_Name: Hand.R
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3558756004602860473
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8411616136048268515}
  serializedVersion: 2
  m_LocalRotation: {x: 0.21624509, y: 0.9755092, z: 0.00028210488, w: 0.04024635}
  m_LocalPosition: {x: -9.8079905e-11, y: 0.002779957, z: 5.5879353e-11}
  m_LocalScale: {x: 1.0575901, y: 1.05759, z: 1.0575901}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8824808082363890012}
  m_Father: {fileID: 8799311143667136152}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8509387165957796045
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7665928146807215912}
  m_Layer: 0
  m_Name: Base
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7665928146807215912
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8509387165957796045}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1723446931294378392}
  m_Father: {fileID: 989563652246574382}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8897168645623790992
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3804235455050890046}
  - component: {fileID: 3437948490615384372}
  - component: {fileID: 415126239608537168}
  m_Layer: 0
  m_Name: HelmetRim
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3804235455050890046
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8897168645623790992}
  serializedVersion: 2
  m_LocalRotation: {x: 0.000000005587935, y: 1.1641531e-10, z: -3.9290168e-10, w: 1}
  m_LocalPosition: {x: -0, y: -2.9802322e-10, z: 0.0000000016298145}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7933312363712594881}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &3437948490615384372
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8897168645623790992}
  m_Mesh: {fileID: 8364061120063493113, guid: 5c0669bd66f517143b7f3a11cbd9c17e, type: 3}
--- !u!23 &415126239608537168
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8897168645623790992}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 20729848b1209504ebb9c98d05470832, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &9109395179953454086
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7350535469641195996}
  m_Layer: 0
  m_Name: Armature
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7350535469641195996
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9109395179953454086}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071067, y: 5.302452e-33, z: 5.302451e-33, w: 0.7071068}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 100, y: 100, z: 100}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 9091176427198875475}
  m_Father: {fileID: 2703071787956424704}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
