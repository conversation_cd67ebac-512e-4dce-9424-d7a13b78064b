.mm-controls { /* debug controls panel */
    margin-top: 0px;
    margin-left: 0px;
    padding: 10px;
    padding-left: 8px;
    padding-right: 6px;
}

.mm-controls Button { /* debug controls button */
    flex-grow: 1;
    padding: 2px;
}

.mm-controls-label { /* debug controls label */
    -unity-font-style: bold;
    margin-left: 3px;
}

.mm-playmode-button-active { /* active playmode button */
    background-color: rgba(244, 52, 0, 1);
    color: rgba(32, 32, 32, 1);
}

.mm-script-driven-pause-label{ /* script-driven pause label */
    background-color: rgba(42, 167, 208, 1);
    -unity-font-style: bold;
    color: rgba(32, 32, 32, 1);
    -unity-text-align: middle-center;
    padding: 5px;
    margin: 3px;
    transition-duration: 0.5s;
}

.mm-script-driven-pause-label-blink{ /* script-driven pause label blink */
    background-color: rgba(117, 221, 255, 1);
}

.mm-bottom-bar { /* bottom bar */
    border-color: rgba(255, 255, 255, 0.3);
    background-color: rgba(0, 0, 0, 0.3);
    border-width: 1px;
    border-left-width: 0px;
    border-right-width: 0px;
    border-top-width: 0px;
    padding-top: 5px;
    padding-bottom: 5px;
    padding-left: 8px;
    padding-right: 6px;
    margin-top: 0px;
}

.mm-add-feedback-search-field { /* add feedback search field */
    width: 45%;
    transition: width 0.1s ease-in-out; 
}

.mm-add-feedback-search-field-placeholder {
    width: 20%
}

.mm-add-feedback-search-field Label {
    display: none;
    background-color: yellow;
}

.mm-add-feedback-search-field-placeholder TextInput { /* add feedback search field placeholder */
    color: rgba(255, 255, 255, 0.3);
}

.mm-search-add-feedback-button { /* search add feedback button */
    width: 60%;
    margin-left: 40%;
    -unity-text-align: middle-left;
}

.mm-search-results-row {
    flex-direction: column;
}

.mm-search-results-row Button:focus {
    background-color: rgba(83,191,255,0.3);
}