<?xml version="1.0" encoding="UTF-8"?><package><identifier v="Unsaved Package"/><formatVersion v="1.1.0.202302"/><updaterVersion v="1.1.0.202302"/><fileUID v="{84b9ec4e-af4c-4574-952a-e3d014b1549e}"/><versionUID v="0"/><dependencies><dependency><filename v="sbs://pattern_tile_sampler.sbs"/><uid v="1449399972"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://edge_detect.sbs"/><uid v="1407202483"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_crystal_1.sbs"/><uid v="1407271237"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://blur_hq.sbs"/><uid v="1299236171"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://flood_fill_2.sbs"/><uid v="1323881949"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://slope_blur.sbs"/><uid v="1449402040"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_perlin_noise.sbs"/><uid v="1407332030"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://pattern_polygon_2.sbs"/><uid v="1487918781"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://hbao_2.sbs"/><uid v="1297162667"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://curvature_smooth.sbs"/><uid v="1449590280"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://height_blend.sbs"/><uid v="1449623608"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_clouds_1.sbs"/><uid v="1482602979"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://pbr_base_material.sbs"/><uid v="1482592834"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://histogram_scan.sbs"/><uid v="1449404462"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://splatter.sbs"/><uid v="1487921414"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_voronoi.sbs"/><uid v="1440766644"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://pattern_gaussian_1.sbs"/><uid v="1487922179"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://histogram_range.sbs"/><uid v="1335474785"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_cells_3.sbs"/><uid v="1487079844"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency></dependencies><content><graph><identifier v="cliff_with_sand"/><uid v="1487420008"/><graphtype v="material"/><graphOutputs><graphoutput><identifier v="basecolor"/><uid v="1213284336"/><attributes><label v="Base Color"/></attributes><usages><usage><components v="RGBA"/><name v="baseColor"/></usage></usages><group v="Material"/></graphoutput><graphoutput><identifier v="normal"/><uid v="1213284338"/><attributes><label v="Normal"/></attributes><usages><usage><components v="RGBA"/><name v="normal"/></usage></usages><group v="Material"/></graphoutput><graphoutput><identifier v="roughness"/><uid v="1213284340"/><attributes><label v="Roughness"/></attributes><usages><usage><components v="RGBA"/><name v="roughness"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput><graphoutput><identifier v="metallic"/><uid v="1213284342"/><attributes><label v="Metallic"/></attributes><usages><usage><components v="RGBA"/><name v="metallic"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput><graphoutput><identifier v="height"/><uid v="1279137031"/><attributes><label v="Height"/></attributes><usages><usage><components v="RGBA"/><name v="height"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput><graphoutput><identifier v="ambientocclusion"/><uid v="1359211721"/><attributes><label v="Ambient Occlusion"/></attributes><usages><usage><components v="RGBA"/><name v="ambientOcclusion"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput></graphOutputs><compNodes><compNode><uid v="1213284337"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1487719592"/><connRefOutput v="1487719593"/></connection></connections><GUILayout><gpos v="528 -240 0"/></GUILayout><compImplementation><compOutputBridge><output v="1213284336"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1213284339"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1487865549"/><connRefOutput v="1487865550"/></connection></connections><GUILayout><gpos v="528 -80 0"/></GUILayout><compImplementation><compOutputBridge><output v="1213284338"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1213284341"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1359211391"/><connRefOutput v="1359211392"/></connection></connections><GUILayout><gpos v="528 80 0"/></GUILayout><compImplementation><compOutputBridge><output v="1213284340"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1213284343"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1359211407"/><connRefOutput v="1359211408"/></connection></connections><GUILayout><gpos v="528 240 0"/></GUILayout><compImplementation><compOutputBridge><output v="1213284342"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1279137030"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1487437633"/><connRefOutput v="1487437634"/></connection></connections><GUILayout><gpos v="528 944 0"/></GUILayout><compImplementation><compOutputBridge><output v="1279137031"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1359211383"/><connections><connection><identifier v="input1"/><connRef v="1487437633"/><connRefOutput v="1487437634"/></connection></connections><GUILayout><gpos v="-880 64.7343674 0"/></GUILayout><compOutputs><compOutput><uid v="1359211384"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="normal"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="10"/></paramValue></parameter><parameter><name v="input2alpha"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1359211391"/><GUILayout><gpos v="368 80 0"/></GUILayout><compOutputs><compOutput><uid v="1359211392"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="uniform"/><parameters><parameter><name v="colorswitch"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="outputcolor"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.25 0.25 0.25 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1359211407"/><GUILayout><gpos v="368 240 0"/></GUILayout><compOutputs><compOutput><uid v="1359211408"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="uniform"/><parameters><parameter><name v="colorswitch"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1359211720"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1487854935"/><connRefOutput v="1487854936"/></connection></connections><GUILayout><gpos v="528 784 0"/></GUILayout><compImplementation><compOutputBridge><output v="1359211721"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1487424389"/><GUILayout><gpos v="-5058.25 -176 0"/></GUILayout><compOutputs><compOutput><uid v="1487424390"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1449399972"/><parameters><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="8"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="8"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="6.82999992"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.370000005"/></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="6.32999992"/></paramValue></parameter><parameter><name v="color_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.629999995"/></paramValue></parameter><parameter><name v="rotation_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.289999992"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487424390"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487424666"/><connections><connection><identifier v="input"/><connRef v="1487424909"/><connRefOutput v="1487424910"/></connection></connections><GUILayout><gpos v="-4564.38916 -176 0"/></GUILayout><compOutputs><compOutput><uid v="1487424667"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///edge_detect?dependency=1407202483"/><parameters><parameter><name v="edge_width"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="3.6099999"/></paramValue></parameter><parameter><name v="edge_roundness"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487424667"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487424909"/><connections><connection><identifier v="input1"/><connRef v="1487424389"/><connRefOutput v="1487424390"/></connection><connection><identifier v="inputintensity"/><connRef v="1487432349"/><connRefOutput v="1487432350"/></connection></connections><GUILayout><gpos v="-4739.54297 -176 0"/></GUILayout><compOutputs><compOutput><uid v="1487424910"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="directionalwarp"/><parameters><parameter><name v="warpangle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.162055552"/></paramValue></parameter><parameter><name v="randomseed"/><relativeTo v="2"/><paramValue><constantValueInt32 v="10"/></paramValue></parameter><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="20"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487424943"/><GUILayout><gpos v="-5012.38916 48 0"/></GUILayout><compOutputs><compOutput><uid v="1487424944"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///crystal_1?dependency=1407271237"/><parameters><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueInt32 v="7"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487424944"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487425265"/><connections><connection><identifier v="Source"/><connRef v="1487424666"/><connRefOutput v="1487424667"/></connection></connections><GUILayout><gpos v="-4434.94922 -176 0"/></GUILayout><compOutputs><compOutput><uid v="1487425266"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1299236171"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="4.17999983"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487425266"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487432349"/><connections><connection><identifier v="Source"/><connRef v="1487424943"/><connRefOutput v="1487424944"/></connection></connections><GUILayout><gpos v="-4852.38916 48 0"/></GUILayout><compOutputs><compOutput><uid v="1487432350"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1299236171"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="3.06999993"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487432350"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487436181"/><connections><connection><identifier v="mask"/><connRef v="1487425265"/><connRefOutput v="1487425266"/></connection></connections><GUILayout><gpos v="-4148.38916 -159.07608 0"/></GUILayout><compOutputs><compOutput><uid v="1487436182"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters><parameter><name v="colorswitch"/><paramValue><constantValueBool v="0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487442222"/><connections><connection><identifier v="mask"/><connRef v="1487425265"/><connRefOutput v="1487425266"/></connection></connections><GUILayout><gpos v="-4340.38916 48.6608887 0"/></GUILayout><compOutputs><compOutput><uid v="1487442223"/><comptype v="1"/></compOutput><compOutput><uid v="1487442224"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill?dependency=1323881949"/><parameters/><outputBridgings><outputBridging><uid v="1487442223"/><identifier v="output"/></outputBridging><outputBridging><uid v="1487442224"/><identifier v="referenceMap"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487442502"/><connections><connection><identifier v="input"/><connRef v="1487442222"/><connRefOutput v="1487442223"/></connection></connections><GUILayout><gpos v="-4195.22461 65.050415 0"/></GUILayout><compOutputs><compOutput><uid v="1487442503"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487442503"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487442713"/><connections><connection><identifier v="source"/><connRef v="1487442502"/><connRefOutput v="1487442503"/></connection><connection><identifier v="mask"/><connRef v="1487425265"/><connRefOutput v="1487425266"/></connection></connections><GUILayout><gpos v="-4052.38916 51.6407776 0"/></GUILayout><compOutputs><compOutput><uid v="1487442714"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1487442832"/><connections><connection><identifier v="input"/><connRef v="1487442833"/><connRefOutput v="1487442223"/></connection></connections><GUILayout><gpos v="-4195.22461 272 0"/></GUILayout><compOutputs><compOutput><uid v="1487442503"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="angle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.305555552"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487442503"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487442833"/><connections><connection><identifier v="mask"/><connRef v="1487425265"/><connRefOutput v="1487425266"/></connection></connections><GUILayout><gpos v="-4340.38916 272 0"/></GUILayout><compOutputs><compOutput><uid v="1487442223"/><comptype v="1"/></compOutput><compOutput><uid v="1487442224"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill?dependency=1323881949"/><parameters/><outputBridgings><outputBridging><uid v="1487442223"/><identifier v="output"/></outputBridging><outputBridging><uid v="1487442224"/><identifier v="referenceMap"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487442834"/><connections><connection><identifier v="source"/><connRef v="1487442832"/><connRefOutput v="1487442503"/></connection><connection><identifier v="mask"/><connRef v="1487425265"/><connRefOutput v="1487425266"/></connection></connections><GUILayout><gpos v="-4052.38916 272 0"/></GUILayout><compOutputs><compOutput><uid v="1487442714"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1487428995"/><connections><connection><identifier v="source"/><connRef v="1487428996"/><connRefOutput v="1487442503"/></connection><connection><identifier v="mask"/><connRef v="1487425265"/><connRefOutput v="1487425266"/></connection></connections><GUILayout><gpos v="-4037.55347 496 0"/></GUILayout><compOutputs><compOutput><uid v="1487442714"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1487428996"/><connections><connection><identifier v="input"/><connRef v="1487428997"/><connRefOutput v="1487442223"/></connection></connections><GUILayout><gpos v="-4180.38916 496 0"/></GUILayout><compOutputs><compOutput><uid v="1487442503"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="angle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.36944443"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487442503"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487428997"/><connections><connection><identifier v="mask"/><connRef v="1487425265"/><connRefOutput v="1487425266"/></connection></connections><GUILayout><gpos v="-4325.55322 496 0"/></GUILayout><compOutputs><compOutput><uid v="1487442223"/><comptype v="1"/></compOutput><compOutput><uid v="1487442224"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill?dependency=1323881949"/><parameters/><outputBridgings><outputBridging><uid v="1487442223"/><identifier v="output"/></outputBridging><outputBridging><uid v="1487442224"/><identifier v="referenceMap"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487429361"/><connections><connection><identifier v="destination"/><connRef v="1487436181"/><connRefOutput v="1487436182"/></connection><connection><identifier v="source"/><connRef v="1487436402"/><connRefOutput v="1487436403"/></connection></connections><GUILayout><gpos v="-3778.46289 -158.102829 0"/></GUILayout><compOutputs><compOutput><uid v="1487429362"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487436402"/><connections><connection><identifier v="input1"/><connRef v="1487442713"/><connRefOutput v="1487442714"/></connection></connections><GUILayout><gpos v="-3924.38916 48 0"/></GUILayout><compOutputs><compOutput><uid v="1487436403"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.569164276 0.569164276 0.569164276 0"/></paramValue></parameter><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.557034194 0.557034194 0.557034194 0.5"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487436540"/><connections><connection><identifier v="destination"/><connRef v="1487429361"/><connRefOutput v="1487429362"/></connection><connection><identifier v="source"/><connRef v="1487436721"/><connRefOutput v="1487436722"/></connection></connections><GUILayout><gpos v="-3636.38916 144 0"/></GUILayout><compOutputs><compOutput><uid v="1487436541"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487436721"/><connections><connection><identifier v="input1"/><connRef v="1487442834"/><connRefOutput v="1487442714"/></connection></connections><GUILayout><gpos v="-3924.38916 272 0"/></GUILayout><compOutputs><compOutput><uid v="1487436722"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.240634009 0.240634009 0.240634009 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487436869"/><connections><connection><identifier v="destination"/><connRef v="1487436540"/><connRefOutput v="1487436541"/></connection><connection><identifier v="source"/><connRef v="1487437073"/><connRefOutput v="1487437074"/></connection></connections><GUILayout><gpos v="-3444.38916 304 0"/></GUILayout><compOutputs><compOutput><uid v="1487436870"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487437073"/><connections><connection><identifier v="input1"/><connRef v="1487428995"/><connRefOutput v="1487442714"/></connection></connections><GUILayout><gpos v="-3892.38916 496 0"/></GUILayout><compOutputs><compOutput><uid v="1487437074"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.427953899 0.427953899 0.427953899 0.5"/></paramValue></parameter><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.246397689 0.246397689 0.246397689 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487437633"/><connections><connection><identifier v="input1"/><connRef v="1487713718"/><connRefOutput v="1487713719"/></connection></connections><GUILayout><gpos v="-1602.77124 208 0"/></GUILayout><compOutputs><compOutput><uid v="1487437634"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.750950575 0.750950575 0.750950575 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487497686"/><connections><connection><identifier v="Source"/><connRef v="1487436869"/><connRefOutput v="1487436870"/></connection><connection><identifier v="Effect"/><connRef v="1487498286"/><connRefOutput v="1487498287"/></connection></connections><GUILayout><gpos v="-3266.46289 400 0"/></GUILayout><compOutputs><compOutput><uid v="1487497687"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///slope_blur_grayscale_2?dependency=1449402040"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.449999988"/></paramValue></parameter><parameter><name v="mode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="32"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487497687"/><identifier v="Slope_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487498286"/><GUILayout><gpos v="-3428.63428 592 0"/></GUILayout><compOutputs><compOutput><uid v="1487498287"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///perlin_noise?dependency=1407332030"/><parameters><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueInt32 v="14"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487498287"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487499217"/><connections><connection><identifier v="destination"/><connRef v="1487436869"/><connRefOutput v="1487436870"/></connection><connection><identifier v="source"/><connRef v="1487499314"/><connRefOutput v="1487499315"/></connection></connections><GUILayout><gpos v="-2945.87598 208 0"/></GUILayout><compOutputs><compOutput><uid v="1487499218"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.859999955"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487499314"/><connections><connection><identifier v="input1"/><connRef v="1487497686"/><connRefOutput v="1487497687"/></connection></connections><GUILayout><gpos v="-3106.46289 400 0"/></GUILayout><compOutputs><compOutput><uid v="1487499315"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="randomseed"/><relativeTo v="2"/><paramValue><constantValueInt32 v="10"/></paramValue></parameter><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.678707242 0.678707242 0.678707242 0.5"/></paramValue></parameter><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.18871595 0.18871595 0.18871595 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487690913"/><connections><connection><identifier v="input1"/><connRef v="1487499217"/><connRefOutput v="1487499218"/></connection><connection><identifier v="inputintensity"/><connRef v="1487499217"/><connRefOutput v="1487499218"/></connection></connections><GUILayout><gpos v="-2800 208 0"/></GUILayout><compOutputs><compOutput><uid v="1487690914"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="directionalwarp"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="10.4499998"/></paramValue></parameter><parameter><name v="warpangle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.159999996"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487691403"/><connections><connection><identifier v="pattern_input"/><connRef v="1487692175"/><connRefOutput v="1487692176"/></connection></connections><GUILayout><gpos v="-2800 784 0"/></GUILayout><compOutputs><compOutput><uid v="1487691404"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1449399972"/><parameters><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="size_random"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.5 0.5"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.62"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.519999981"/></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="7.40999985"/></paramValue></parameter><parameter><name v="mask_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.870000005"/></paramValue></parameter><parameter><name v="rotation_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.589999974"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487691404"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487692175"/><GUILayout><gpos v="-2960 816 0"/></GUILayout><compOutputs><compOutput><uid v="1487692176"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///polygon_2?dependency=1487918781"/><parameters><parameter><name v="Scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.50999999"/></paramValue></parameter><parameter><name v="Curve"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="Gradient"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.140000001"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487692176"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487693524"/><connections><connection><identifier v="destination"/><connRef v="1487690913"/><connRefOutput v="1487690914"/></connection><connection><identifier v="source"/><connRef v="1487942395"/><connRefOutput v="1487084810"/></connection></connections><GUILayout><gpos v="-2448 208 0"/></GUILayout><compOutputs><compOutput><uid v="1487693525"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0199999996"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487694525"/><connections><connection><identifier v="input1"/><connRef v="1487693524"/><connRefOutput v="1487693525"/></connection></connections><GUILayout><gpos v="-1328 -240 0"/></GUILayout><compOutputs><compOutput><uid v="1487694526"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1487694620"/><paramsArrayCells><paramsArrayCell><uid v="1487889618"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.172995776"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.365217388 0.260996133 0.123154692 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487889619"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.45550096"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.6652174 0.484687209 0.324873596 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487889620"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.70588237 0.580392182 0.345098048 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1487702288"/><connections><connection><identifier v="source"/><connRef v="1487694525"/><connRefOutput v="1487694526"/></connection></connections><GUILayout><gpos v="-1200 -240 0"/></GUILayout><compOutputs><compOutput><uid v="1487702289"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1487703978"/><connections><connection><identifier v="input"/><connRef v="1487437633"/><connRefOutput v="1487437634"/></connection></connections><GUILayout><gpos v="-1200 208 0"/></GUILayout><compOutputs><compOutput><uid v="1487703979"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///hbao?dependency=1297162667"/><parameters><parameter><name v="height_depth"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.229999989"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487703979"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487704933"/><connections><connection><identifier v="input1"/><connRef v="1487703978"/><connRefOutput v="1487703979"/></connection></connections><GUILayout><gpos v="-1008 144 0"/></GUILayout><compOutputs><compOutput><uid v="1487704934"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1487704959"/><paramsArrayCells><paramsArrayCell><uid v="1487704967"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.768627465 0.870588243 0.937254906 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487704968"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.295652181 0.292214364 0.292214364 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1487705099"/><connections><connection><identifier v="source"/><connRef v="1487704933"/><connRefOutput v="1487704934"/></connection><connection><identifier v="destination"/><connRef v="1487717578"/><connRefOutput v="1487717579"/></connection></connections><GUILayout><gpos v="-929.765991 -240 0"/></GUILayout><compOutputs><compOutput><uid v="1487705100"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="9"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.5"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487706181"/><connections><connection><identifier v="input"/><connRef v="1359211383"/></connection></connections><GUILayout><gpos v="-720 208 0"/></GUILayout><compOutputs><compOutput><uid v="1487706182"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///curvature_smooth?dependency=1449590280"/><parameters/><outputBridgings><outputBridging><uid v="1487706182"/><identifier v="height"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487706200"/><connections><connection><identifier v="input1"/><connRef v="1487706181"/><connRefOutput v="1487706182"/></connection></connections><GUILayout><gpos v="-592 208 0"/></GUILayout><compOutputs><compOutput><uid v="1487706201"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1487706227"/><paramsArrayCells><paramsArrayCell><uid v="1487706228"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.541124344"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0 0 0 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487706229"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.592826962"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.713043451 0.685285926 0.567947388 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1487706483"/><connections><connection><identifier v="destination"/><connRef v="1487705099"/><connRefOutput v="1487705100"/></connection><connection><identifier v="source"/><connRef v="1487706200"/><connRefOutput v="1487706201"/></connection></connections><GUILayout><gpos v="-752 -240 0"/></GUILayout><compOutputs><compOutput><uid v="1487706484"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.219999999"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487713718"/><connections><connection><identifier v="height_bottom"/><connRef v="1487693524"/><connRefOutput v="1487693525"/></connection><connection><identifier v="height_top"/><connRef v="1487859259"/><connRefOutput v="1482613460"/></connection></connections><GUILayout><gpos v="-1904 208 0"/></GUILayout><compOutputs><compOutput><uid v="1487713719"/><comptype v="2"/></compOutput><compOutput><uid v="1487713720"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///height_blend?dependency=1449623608"/><parameters><parameter><name v="height_position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.239999995"/></paramValue></parameter><parameter><name v="contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="opacity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.919999957"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487713719"/><identifier v="blended_height"/></outputBridging><outputBridging><uid v="1487713720"/><identifier v="height_mask"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487717578"/><connections><connection><identifier v="destination"/><connRef v="1487702288"/><connRefOutput v="1487702289"/></connection><connection><identifier v="opacity"/><connRef v="1487713718"/><connRefOutput v="1487713720"/></connection><connection><identifier v="source"/><connRef v="1487859270"/><connRefOutput v="1482612071"/></connection></connections><GUILayout><gpos v="-1072 -240 0"/></GUILayout><compOutputs><compOutput><uid v="1487717579"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.849999964"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487719302"/><connections><connection><identifier v="input1"/><connRef v="1487691403"/><connRefOutput v="1487691404"/></connection></connections><GUILayout><gpos v="-2640 816 0"/></GUILayout><compOutputs><compOutput><uid v="1487719303"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1487719320"/><paramsArrayCells><paramsArrayCell><uid v="1487719378"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.0443115234"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.819607854 0.75686276 0.75686276 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487719383"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="1 1 1 0"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1487719592"/><connections><connection><identifier v="destination"/><connRef v="1487706483"/><connRefOutput v="1487706484"/></connection><connection><identifier v="source"/><connRef v="1487719302"/><connRefOutput v="1487719303"/></connection></connections><GUILayout><gpos v="-605.011353 -240 0"/></GUILayout><compOutputs><compOutput><uid v="1487719593"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.319999993"/></paramValue></parameter><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487859252"/><connections><connection><identifier v="input"/><connRef v="1487859254"/><connRefOutput v="1359211384"/></connection></connections><GUILayout><gpos v="-3312 1520 0"/></GUILayout><compOutputs><compOutput><uid v="1482605187"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///curvature_smooth?dependency=1449590280"/><parameters/><outputBridgings><outputBridging><uid v="1482605187"/><identifier v="height"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487859253"/><GUILayout><gpos v="-3714.32422 2096 0"/></GUILayout><compOutputs><compOutput><uid v="1482603323"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///clouds_1?dependency=1482602979"/><parameters/><outputBridgings><outputBridging><uid v="1482603323"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487859254"/><connections><connection><identifier v="input1"/><connRef v="1487859272"/><connRefOutput v="1482609242"/></connection></connections><GUILayout><gpos v="-3472 1616 0"/></GUILayout><compOutputs><compOutput><uid v="1359211384"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="normal"/><parameters><parameter><name v="input2alpha"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="3"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487859255"/><connections><connection><identifier v="input1"/><connRef v="1487859269"/><connRefOutput v="1482608101"/></connection></connections><GUILayout><gpos v="-3824 1744 0"/></GUILayout><compOutputs><compOutput><uid v="1482607100"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters><parameter><name v="colorswitch"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter></parameters><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1482607108"/><paramsArrayCells><paramsArrayCell><uid v="1482607109"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.406950086"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0 0 0 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1482607110"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.716706991"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="1 1 1 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1482607111"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.820388377 0.820388377 0.820388377 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1487859256"/><connections><connection><identifier v="input1"/><connRef v="1487859268"/><connRefOutput v="1482602557"/></connection><connection><identifier v="inputgradient"/><connRef v="1487859253"/><connRefOutput v="1482603323"/></connection></connections><GUILayout><gpos v="-3549.44116 1936 0"/></GUILayout><compOutputs><compOutput><uid v="1482602957"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="warp"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.00999999978"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487859257"/><connections><connection><identifier v="input1"/><connRef v="1487859266"/><connRefOutput v="1482605259"/></connection></connections><GUILayout><gpos v="-2608 1296 0"/></GUILayout><compOutputs><compOutput><uid v="1482609151"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="hsl"/><parameters><parameter><name v="luminosity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.519999981"/></paramValue></parameter><parameter><name v="hue"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487859258"/><GUILayout><gpos v="-4034.32422 1936 0"/></GUILayout><compOutputs><compOutput><uid v="1482597662"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///polygon_2?dependency=1487918781"/><parameters/><outputBridgings><outputBridging><uid v="1482597662"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487859259"/><connections><connection><identifier v="ambientOcclusion_input"/><connRef v="1487859262"/><connRefOutput v="1482613803"/></connection><connection><identifier v="roughness_input"/><connRef v="1487859273"/><connRefOutput v="1482605684"/></connection><connection><identifier v="height_input"/><connRef v="1487859263"/><connRefOutput v="1482608809"/></connection><connection><identifier v="normal_input"/><connRef v="1487859261"/><connRefOutput v="1359211384"/></connection><connection><identifier v="basecolor_input"/><connRef v="1487859270"/><connRefOutput v="1482612071"/></connection></connections><GUILayout><gpos v="-2320 1904 0"/></GUILayout><compOutputs><compOutput><uid v="1482613453"/><comptype v="1"/></compOutput><compOutput><uid v="1482613454"/><comptype v="1"/></compOutput><compOutput><uid v="1482613455"/><comptype v="1"/></compOutput><compOutput><uid v="1482613456"/><comptype v="1"/></compOutput><compOutput><uid v="1482613457"/><comptype v="2"/></compOutput><compOutput><uid v="1482613458"/><comptype v="2"/></compOutput><compOutput><uid v="1482613459"/><comptype v="2"/></compOutput><compOutput><uid v="1482613460"/><comptype v="2"/></compOutput><compOutput><uid v="1482613461"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///pbr_base_material?dependency=1482592834"/><parameters><parameter><name v="user_basecolor"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="user_normal"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="user_roughness"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="user_ao"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="user_height"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="metallic"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="height_position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.629999995"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1482613453"/><identifier v="diffuse"/></outputBridging><outputBridging><uid v="1482613454"/><identifier v="basecolor"/></outputBridging><outputBridging><uid v="1482613455"/><identifier v="normal"/></outputBridging><outputBridging><uid v="1482613456"/><identifier v="specular"/></outputBridging><outputBridging><uid v="1482613457"/><identifier v="glossiness"/></outputBridging><outputBridging><uid v="1482613458"/><identifier v="roughness"/></outputBridging><outputBridging><uid v="1482613459"/><identifier v="metallic"/></outputBridging><outputBridging><uid v="1482613460"/><identifier v="height"/></outputBridging><outputBridging><uid v="1482613461"/><identifier v="ambientOcclusion"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487859260"/><connections><connection><identifier v="input1"/><connRef v="1487859252"/><connRefOutput v="1482605187"/></connection></connections><GUILayout><gpos v="-3102.27783 1488 0"/></GUILayout><compOutputs><compOutput><uid v="1482605259"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters><parameter><name v="colorswitch"/><paramValue><constantValueBool v="1"/></paramValue></parameter></parameters><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1482605286"/><paramsArrayCells><paramsArrayCell><uid v="1482612759"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.257383972"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.339130431 0.286409199 0.096612744 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1482612760"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.811240196"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.0673660263 0.178260863 0.175367922 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1487859261"/><connections><connection><identifier v="input1"/><connRef v="1487859263"/><connRefOutput v="1482608809"/></connection></connections><GUILayout><gpos v="-3024 1680 0"/></GUILayout><compOutputs><compOutput><uid v="1359211384"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="normal"/><parameters><parameter><name v="input2alpha"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="10"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487859262"/><connections><connection><identifier v="input1"/><connRef v="1487859265"/><connRefOutput v="1482603561"/></connection></connections><GUILayout><gpos v="-2896 2128 0"/></GUILayout><compOutputs><compOutput><uid v="1482613803"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.409438789 0.409438789 0.409438789 0.5"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487859263"/><connections><connection><identifier v="destination"/><connRef v="1487859264"/><connRefOutput v="1482603561"/></connection></connections><GUILayout><gpos v="-3184 1885.02649 0"/></GUILayout><compOutputs><compOutput><uid v="1482608809"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0799999982"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487859264"/><connections><connection><identifier v="Input_1"/><connRef v="1487859256"/><connRefOutput v="1482602957"/></connection></connections><GUILayout><gpos v="-3408 1936 0"/></GUILayout><compOutputs><compOutput><uid v="1482603561"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1449404462"/><parameters><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.269999981"/></paramValue></parameter><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.419999987"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1482603561"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487859265"/><connections><connection><identifier v="Input_1"/><connRef v="1487859263"/><connRefOutput v="1482608809"/></connection></connections><GUILayout><gpos v="-3024 2128 0"/></GUILayout><compOutputs><compOutput><uid v="1482603561"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1449404462"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.610000014"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1482603561"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487859266"/><connections><connection><identifier v="input1"/><connRef v="1487859274"/><connRefOutput v="1482605187"/></connection></connections><GUILayout><gpos v="-2736 1296 0"/></GUILayout><compOutputs><compOutput><uid v="1482605259"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1482605286"/><paramsArrayCells><paramsArrayCell><uid v="1482605302"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.856521726 0.651701331 0.308746189 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1482605303"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.856521726 0.64139539 0.318705767 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1482605388"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.398734123"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.691304326 0.461055845 0.257229507 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1487859267"/><connections><connection><identifier v="Source"/><connRef v="1487859258"/><connRefOutput v="1482597662"/></connection></connections><GUILayout><gpos v="-3874.32422 1936 0"/></GUILayout><compOutputs><compOutput><uid v="1482598498"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///splatter?dependency=1487921414"/><parameters><parameter><name v="Pattern_size_width"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1085.13"/></paramValue></parameter><parameter><name v="Pattern_size_height"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="358.139984"/></paramValue></parameter><parameter><name v="Rotation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="25.1199989"/></paramValue></parameter><parameter><name v="Disorder"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="51.1599998"/></paramValue></parameter><parameter><name v="Size_Var"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="100"/></paramValue></parameter><parameter><name v="Lum_Var"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.719999969"/></paramValue></parameter><parameter><name v="Gain"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.899999976"/></paramValue></parameter><parameter><name v="Disorder_Angle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="171.62999"/></paramValue></parameter><parameter><name v="Pan_Y"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="-8.13999939"/></paramValue></parameter><parameter><name v="Pan_X"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="6.04999971"/></paramValue></parameter><parameter><name v="Grid_Number"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1482598498"/><identifier v="Splatter_Grayscale"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487859268"/><connections><connection><identifier v="input1"/><connRef v="1487859267"/><connRefOutput v="1482598498"/></connection><connection><identifier v="inputgradient"/><connRef v="1487859271"/><connRefOutput v="1482602676"/></connection></connections><GUILayout><gpos v="-3714.32422 1936 0"/></GUILayout><compOutputs><compOutput><uid v="1482602557"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="warp"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.839999974"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487859269"/><GUILayout><gpos v="-3984 1744 0"/></GUILayout><compOutputs><compOutput><uid v="1482608101"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///voronoi?dependency=1440766644"/><parameters><parameter><name v="distortion_intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0799999982"/></paramValue></parameter><parameter><name v="distance_scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.170000002"/></paramValue></parameter><parameter><name v="rounded_curve"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0599999987"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="21.9499989"/></paramValue></parameter><parameter><name v="distortion_scale_multiplier"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.65999997"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1482608101"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487859270"/><connections><connection><identifier v="destination"/><connRef v="1487859257"/><connRefOutput v="1482609151"/></connection><connection><identifier v="source"/><connRef v="1487859260"/><connRefOutput v="1482605259"/></connection></connections><GUILayout><gpos v="-2704 1616 0"/></GUILayout><compOutputs><compOutput><uid v="1482612071"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0700000003"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487859271"/><GUILayout><gpos v="-4034.32422 2113.00439 0"/></GUILayout><compOutputs><compOutput><uid v="1482602676"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///gaussian_1?dependency=1487922179"/><parameters><parameter><name v="Tiling"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1482602676"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487859272"/><connections><connection><identifier v="input1"/><connRef v="1487859255"/><connRefOutput v="1482607100"/></connection></connections><GUILayout><gpos v="-3682.00708 1744 0"/></GUILayout><compOutputs><compOutput><uid v="1482609242"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.0497448966 0.0497448966 0.0497448966 0.5"/></paramValue></parameter><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.011479592 0.011479592 0.011479592 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487859273"/><connections><connection><identifier v="input"/><connRef v="1487859263"/><connRefOutput v="1482608809"/></connection></connections><GUILayout><gpos v="-2864 1808 0"/></GUILayout><compOutputs><compOutput><uid v="1482605684"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_range?dependency=1335474785"/><parameters><parameter><name v="range"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.239999995"/></paramValue></parameter><parameter><name v="position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.419999987"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1482605684"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487859274"/><connections><connection><identifier v="input"/><connRef v="1487859261"/><connRefOutput v="1359211384"/></connection></connections><GUILayout><gpos v="-2864 1296 0"/></GUILayout><compOutputs><compOutput><uid v="1482605187"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///curvature_smooth?dependency=1449590280"/><parameters/><outputBridgings><outputBridging><uid v="1482605187"/><identifier v="height"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487865549"/><connections><connection><identifier v="source"/><connRef v="1359211383"/><connRefOutput v="1359211384"/></connection><connection><identifier v="destination"/><connRef v="1487859259"/><connRefOutput v="1482613455"/></connection></connections><GUILayout><gpos v="-162.807098 -32 0"/></GUILayout><compOutputs><compOutput><uid v="1487865550"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1487854935"/><connections><connection><identifier v="source"/><connRef v="1487703978"/><connRefOutput v="1487703979"/></connection></connections><GUILayout><gpos v="-224.270203 528 0"/></GUILayout><compOutputs><compOutput><uid v="1487854936"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1487942395"/><connections><connection><identifier v="destination"/><connRef v="1487942400"/><connRefOutput v="1487084560"/></connection></connections><GUILayout><gpos v="-3130.66675 -688.000122 0"/></GUILayout><compOutputs><compOutput><uid v="1487084810"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487942397"/><connections><connection><identifier v="Effect"/><connRef v="1487942399"/><connRefOutput v="1487072766"/></connection><connection><identifier v="Source"/><connRef v="1487954504"/><connRefOutput v="1487954505"/></connection></connections><GUILayout><gpos v="-3642.66675 -688.000122 0"/></GUILayout><compOutputs><compOutput><uid v="1487072348"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///slope_blur_grayscale_2?dependency=1449402040"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="32"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="2.30999994"/></paramValue></parameter><parameter><name v="mode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487072348"/><identifier v="Slope_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487942398"/><GUILayout><gpos v="-3834.66675 -752.000122 0"/></GUILayout><compOutputs><compOutput><uid v="1487079897"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///cells_3?dependency=1487079844"/><parameters><parameter><name v="hardness"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487079897"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487942399"/><GUILayout><gpos v="-3826.15015 -582.92395 0"/></GUILayout><compOutputs><compOutput><uid v="1487072766"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///perlin_noise?dependency=1407332030"/><parameters><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueInt32 v="15"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487072766"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487942400"/><connections><connection><identifier v="Input_1"/><connRef v="1487942401"/><connRefOutput v="1487083334"/></connection></connections><GUILayout><gpos v="-3290.66675 -688.000122 0"/></GUILayout><compOutputs><compOutput><uid v="1487084560"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1449404462"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.179999992"/></paramValue></parameter><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.399999976"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487084560"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487942401"/><connections><connection><identifier v="Source"/><connRef v="1487942397"/><connRefOutput v="1487072348"/></connection></connections><GUILayout><gpos v="-3450.66675 -688.000122 0"/></GUILayout><compOutputs><compOutput><uid v="1487083334"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1299236171"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.45000005"/></paramValue></parameter><parameter><name v="Quality"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487083334"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487954504"/><GUILayout><gpos v="-4112 -624 0"/></GUILayout><compOutputs><compOutput><uid v="1487954505"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///cells_3?dependency=1487079844"/><parameters><parameter><name v="hardness"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.769999981"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueInt32 v="19"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487954505"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode></compNodes><baseParameters/><options><option><name v="defaultParentSize"/><value v="11x11"/></option></options><root><rootOutputs><rootOutput><output v="1213284336"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1213284338"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1213284340"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1213284342"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1279137031"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1359211721"/><format v="0"/><usertag v=""/></rootOutput></rootOutputs></root></graph></content></package>
