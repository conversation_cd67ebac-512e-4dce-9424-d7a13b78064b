# Phase 3: Component Migration Guide

## 🎯 **Overview**

**Objective:** Replace Koreographer components with Cadance equivalents in scenes and prefabs  
**Status:** 🔄 In Progress  
**Risk Level:** Medium (scene integrity)  

---

## 📋 **Component Migration Mapping**

### **Core Components**

| Koreographer Component | Cadance Equivalent | Migration Notes |
|------------------------|-------------------|-----------------|
| `Koreographer` | `Cadance` | Main singleton - requires CadanceAsset |
| `SimpleMusicPlayer` | `AudioSourceCadancePlayer` | Audio playback component |
| `AudioSourceVisor` | `AudioSourceCadancePlayer` | Audio integration |
| `KoreographedEventEmitter` | `CadancedEventEmitter` | Event emission |
| `KoreographerEventRegistry` | `CadanceEventRegistry` | Already migrated in Phase 2 |
| `KoreographerDomainReloadHandler` | `CadanceDomainReloadHandler` | Already migrated in Phase 2 |
| `EpochKoreographyHandler` | `EpochCadanceHandler` | Already migrated in Phase 2 |

### **Script Components (Already Migrated in Phase 2)**

| Component | Status | Notes |
|-----------|--------|-------|
| `KoreoVFXTrigger` | ✅ Migrated | Updated to use Cadance in Phase 2 |
| `MusicSyncedCombatBehavior` | ✅ Migrated | Updated to use Cadance in Phase 2 |
| Custom scripts using Koreographer | ✅ Migrated | All 8 core scripts updated |

---

## 🎯 **Target Scenes**

### **Primary Scenes:**
1. **Ouroboros - Base.unity** - Main base scene
2. **Ouroboros - Scene 1.unity** - First level scene

### **Expected Components:**
Based on Phase 1 inventory:
- SimpleMusicPlayer (on MusicManager GameObject)
- AudioSourceVisor (audio integration)
- KoreographedEventEmitter (various GameObjects)
- EpochKoreographyHandler (time integration) - ✅ Already migrated

---

## 🔧 **Migration Strategy**

### **Step 3.1: Pre-Migration Backup** ✅ Complete
- Backup branches created
- Scene files backed up

### **Step 3.2: Component Scanning** 🔄 In Progress
- Use CadanceComponentMigrationTool to scan scenes
- Identify all Koreographer components
- Generate migration report

### **Step 3.3: Component Replacement** ⏳ Pending
- Replace components one by one
- Preserve component settings where possible
- Update asset references

### **Step 3.4: Asset Reference Updates** ⏳ Pending
- Update Koreography asset references to CadanceAsset
- Verify audio clip assignments
- Check event ID configurations

### **Step 3.5: Scene Validation** ⏳ Pending
- Test scene loading
- Verify component functionality
- Check for missing references

---

## ⚠️ **Migration Risks & Mitigation**

### **High Risk Areas:**
1. **Scene Corruption** - Mitigated by backup strategy
2. **Missing References** - Careful asset mapping required
3. **Component Settings Loss** - Manual verification needed

### **Mitigation Strategies:**
1. **Incremental Migration** - One scene at a time
2. **Immediate Testing** - Test after each component
3. **Rollback Plan** - Git backup branches ready

---

## 🔍 **Verification Checklist**

### **Per Component:**
- [ ] Component replaced successfully
- [ ] Settings preserved
- [ ] References intact
- [ ] No compilation errors

### **Per Scene:**
- [ ] Scene loads without errors
- [ ] All GameObjects active
- [ ] Audio systems functional
- [ ] Event systems working

### **Overall System:**
- [ ] Musical timing preserved
- [ ] Event registration working
- [ ] FMOD integration intact
- [ ] Epoch time system functional

---

## 📊 **Progress Tracking**

**Current Status:**
- ✅ **Phase 2 Complete** - All scripts migrated
- 🔄 **Phase 3 Started** - Component migration in progress
- ⏳ **Scene scanning** - Using migration tool

**Next Steps:**
1. Complete scene component scanning
2. Generate detailed migration report
3. Begin careful component replacement
4. Test each scene after migration

---

## 🚀 **Success Criteria**

**Phase 3 Complete When:**
- All Koreographer components replaced with Cadance equivalents
- All scenes load and function correctly
- Musical timing systems work as expected
- Zero compilation errors
- All references properly updated

**Estimated Completion:** 3-4 hours from start
