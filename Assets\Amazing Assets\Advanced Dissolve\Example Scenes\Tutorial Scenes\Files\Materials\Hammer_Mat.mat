%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Hammer_Mat
  m_Shader: {fileID: 4800000, guid: dcda956a6808f8c4e84b1460ad3ac6eb, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords:
  - _AD_CUTOUT_GEOMETRIC_TYPE_CAPSULE
  - _AD_CUTOUT_STANDARD_SOURCE_CUSTOM_MAP
  - _AD_EDGE_BASE_SOURCE_ALL
  - _AD_STATE_ENABLED
  - _METALLICSPECGLOSSMAP
  - _NORMALMAP
  - _OCCLUSIONMAP
  m_InvalidKeywords:
  - _AD_CUTOUT_GEOMETRIC_COUNT_ONE
  - _AD_CUTOUT_STANDARD_SOURCE_MAPS_MAPPING_TYPE_DEFAULT
  - _AD_EDGE_ADDITIONAL_COLOR_NONE
  - _AD_EDGE_UV_DISTORTION_SOURCE_DEFAULT
  - _AD_GLOBAL_CONTROL_ID_NONE
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: 2000
  stringTagMap:
    RenderType: Opaque
  disabledShaderPasses:
  - DistortionVectors
  - MOTIONVECTORS
  - TransparentDepthPrepass
  - TransparentDepthPostpass
  - TransparentBackface
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _AdvancedDissolveCutoutMap1:
        m_Texture: {fileID: 2800000, guid: 1d6cb8ff93eb8aa4a8481cd36cb330d7, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _AdvancedDissolveCutoutMap2:
        m_Texture: {fileID: 2800000, guid: c51bcac81abbcb6468fd10d9017a502b, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _AdvancedDissolveCutoutMap3:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _AdvancedDissolveCutoutSimpleMap1:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _AdvancedDissolveCutoutSimpleMap2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _AdvancedDissolveCutoutSimpleMap3:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _AdvancedDissolveCutoutStandardMap1:
        m_Texture: {fileID: 2800000, guid: 15156bfb2d3680b42b640ccf7d864400, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _AdvancedDissolveCutoutStandardMap2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _AdvancedDissolveCutoutStandardMap3:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _AdvancedDissolveEdgeAdditionalColorMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _AdvancedDissolveEdgeMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _AdvancedDissolveEdgeUVDistortionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _AnisotropyMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BaseColorMap:
        m_Texture: {fileID: 2800000, guid: fdfe9455148e4a0448c6aa349eec27cf, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BaseMap:
        m_Texture: {fileID: 2800000, guid: 9b76eda3033941740b52592121766abc, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BentNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BentNormalMapOS:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 2800000, guid: 714b2feb0dd2e634785138fffa34f04c, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _CoatMaskMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DistortionVectorMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissiveColorMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _HeightMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _IridescenceMaskMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _IridescenceThicknessMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: 9b76eda3033941740b52592121766abc, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MaskMap:
        m_Texture: {fileID: 2800000, guid: 8bc17817b680ca846a217b4c1048180d, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 2800000, guid: aefccd7f765af7244bb7ea12f7a828a7, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalMap:
        m_Texture: {fileID: 2800000, guid: a48d12de2849af14e8fa09b5f6bc90ad, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalMapOS:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 2800000, guid: aefccd7f765af7244bb7ea12f7a828a7, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecularColorMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SubsurfaceMaskMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TangentMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TangentMapOS:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ThicknessMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TransmittanceColorMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AORemapMax: 1
    - _AORemapMin: 0
    - _ATDistance: 1
    - _AddPrecomputedVelocity: 0
    - _AdvancedDissolveCutoutBaseInvert: 0
    - _AdvancedDissolveCutoutClip: 0
    - _AdvancedDissolveCutoutGeometric1Height: 10.295027
    - _AdvancedDissolveCutoutGeometric1Radius: 0.5
    - _AdvancedDissolveCutoutGeometric2Height: 1
    - _AdvancedDissolveCutoutGeometric2Radius: 0.5
    - _AdvancedDissolveCutoutGeometric3Height: 1
    - _AdvancedDissolveCutoutGeometric3Radius: 0.75
    - _AdvancedDissolveCutoutGeometric4Height: 1
    - _AdvancedDissolveCutoutGeometric4Radius: 1
    - _AdvancedDissolveCutoutGeometricInvert: 1
    - _AdvancedDissolveCutoutGeometricNoise: 0.25
    - _AdvancedDissolveCutoutGeometricXYZAxis: 2
    - _AdvancedDissolveCutoutGeometricXYZRollout: 0
    - _AdvancedDissolveCutoutGeometricXYZSpace: 0
    - _AdvancedDissolveCutoutGeometricXYZStyle: 0
    - _AdvancedDissolveCutoutMap1Channel: 3
    - _AdvancedDissolveCutoutMap1Intensity: 1
    - _AdvancedDissolveCutoutMap1Invert: 0
    - _AdvancedDissolveCutoutMap2Channel: 3
    - _AdvancedDissolveCutoutMap2Intensity: 1
    - _AdvancedDissolveCutoutMap2Invert: 0
    - _AdvancedDissolveCutoutMap3Channel: 3
    - _AdvancedDissolveCutoutMap3Intensity: 1
    - _AdvancedDissolveCutoutMap3Invert: 0
    - _AdvancedDissolveCutoutMapsBlendType: 0
    - _AdvancedDissolveCutoutParametric1Height: 5.2687554
    - _AdvancedDissolveCutoutParametric1Radius: 1.3545165
    - _AdvancedDissolveCutoutParametric2Height: 1
    - _AdvancedDissolveCutoutParametric2Radius: 1
    - _AdvancedDissolveCutoutParametric3Height: 1
    - _AdvancedDissolveCutoutParametric3Radius: 1
    - _AdvancedDissolveCutoutParametric4Height: 1
    - _AdvancedDissolveCutoutParametric4Radius: 1
    - _AdvancedDissolveCutoutParametricInvert: 0
    - _AdvancedDissolveCutoutParametricNoise: 0
    - _AdvancedDissolveCutoutParametricXYZAxis: 0
    - _AdvancedDissolveCutoutParametricXYZRollout: 0
    - _AdvancedDissolveCutoutParametricXYZSpace: 0
    - _AdvancedDissolveCutoutParametricXYZType: 0
    - _AdvancedDissolveCutoutScreenSpaceUVScale: 0
    - _AdvancedDissolveCutoutSimpleBaseInvert: 0
    - _AdvancedDissolveCutoutSimpleClip: 0.25
    - _AdvancedDissolveCutoutSimpleMap1Channel: 3
    - _AdvancedDissolveCutoutSimpleMap1Intensity: 1
    - _AdvancedDissolveCutoutSimpleMap1Invert: 0
    - _AdvancedDissolveCutoutSimpleMap2Channel: 3
    - _AdvancedDissolveCutoutSimpleMap2Intensity: 1
    - _AdvancedDissolveCutoutSimpleMap2Invert: 0
    - _AdvancedDissolveCutoutSimpleMap3Channel: 3
    - _AdvancedDissolveCutoutSimpleMap3Intensity: 1
    - _AdvancedDissolveCutoutSimpleMap3Invert: 0
    - _AdvancedDissolveCutoutSimpleMapsBlendType: 0
    - _AdvancedDissolveCutoutSimpleMapsScreenSpaceUVScale: 0
    - _AdvancedDissolveCutoutSimpleMapsTriplanarMappingSpace: 0
    - _AdvancedDissolveCutoutStandardBaseInvert: 0
    - _AdvancedDissolveCutoutStandardClip: 0
    - _AdvancedDissolveCutoutStandardMap1Channel: 3
    - _AdvancedDissolveCutoutStandardMap1Intensity: 1
    - _AdvancedDissolveCutoutStandardMap1Invert: 0
    - _AdvancedDissolveCutoutStandardMap2Channel: 3
    - _AdvancedDissolveCutoutStandardMap2Intensity: 1
    - _AdvancedDissolveCutoutStandardMap2Invert: 0
    - _AdvancedDissolveCutoutStandardMap3Channel: 3
    - _AdvancedDissolveCutoutStandardMap3Intensity: 1
    - _AdvancedDissolveCutoutStandardMap3Invert: 0
    - _AdvancedDissolveCutoutStandardMapsBlendType: 0
    - _AdvancedDissolveCutoutStandardMapsScreenSpaceUVScale: 0
    - _AdvancedDissolveCutoutStandardMapsTriplanarMappingSpace: 0
    - _AdvancedDissolveCutoutTriplanarMappingSpace: 0
    - _AdvancedDissolveDynamicMask2Height: 1
    - _AdvancedDissolveDynamicMask2Radius: 0.5
    - _AdvancedDissolveDynamicMask3Height: 1
    - _AdvancedDissolveDynamicMask3Radius: 0
    - _AdvancedDissolveDynamicMask4Height: 1
    - _AdvancedDissolveDynamicMask4Radius: 0
    - _AdvancedDissolveDynamicMaskAxis: 0
    - _AdvancedDissolveDynamicMaskAxisPosition: 0
    - _AdvancedDissolveDynamicMaskAxisRollout: 0
    - _AdvancedDissolveDynamicMaskAxisSpace: 0
    - _AdvancedDissolveDynamicMaskAxisType: 0
    - _AdvancedDissolveDynamicMaskHeight: 1
    - _AdvancedDissolveDynamicMaskInvert: 1
    - _AdvancedDissolveDynamicMaskNoise: 0.25
    - _AdvancedDissolveDynamicMaskRadius: 1
    - _AdvancedDissolveEdgeAdditionalColorAlphaOffset: 0
    - _AdvancedDissolveEdgeAdditionalColorClipInterpolation: 0
    - _AdvancedDissolveEdgeAdditionalColorMapMipmap: 1
    - _AdvancedDissolveEdgeAdditionalColorMapReverse: 0
    - _AdvancedDissolveEdgeAdditionalColorPhaseOffset: 0
    - _AdvancedDissolveEdgeAdditionalColorTransparency: 1
    - _AdvancedDissolveEdgeAlphaOffset: 0
    - _AdvancedDissolveEdgeBaseColorTransparency: 1
    - _AdvancedDissolveEdgeBaseShape: 2
    - _AdvancedDissolveEdgeBaseWidthGeometric: 0.1
    - _AdvancedDissolveEdgeBaseWidthParametric: 0.1
    - _AdvancedDissolveEdgeBaseWidthSimple: 0.1
    - _AdvancedDissolveEdgeBaseWidthStandard: 0.1
    - _AdvancedDissolveEdgeColor2Transparency: 1
    - _AdvancedDissolveEdgeColorTransparency: 1
    - _AdvancedDissolveEdgeGIMetaPassMultiplier: 1
    - _AdvancedDissolveEdgeIsDynamic: 0
    - _AdvancedDissolveEdgeMapMipmap: 1
    - _AdvancedDissolveEdgeMapReverse: 0
    - _AdvancedDissolveEdgePhaseOffset: 0
    - _AdvancedDissolveEdgeShape: 2
    - _AdvancedDissolveEdgeUVDistortionStrength: 0
    - _AdvancedDissolveEdgeWidth: 0.285
    - _AdvancedDissolveKeywordCutoutGeometricCount: 0
    - _AdvancedDissolveKeywordCutoutGeometricType: 5
    - _AdvancedDissolveKeywordCutoutMappingType: 1
    - _AdvancedDissolveKeywordCutoutParametricCount: 0
    - _AdvancedDissolveKeywordCutoutParametricType: 6
    - _AdvancedDissolveKeywordCutoutSimpleMapsMappingType: 0
    - _AdvancedDissolveKeywordCutoutSimpleSource: 2
    - _AdvancedDissolveKeywordCutoutSource: 1
    - _AdvancedDissolveKeywordCutoutStandardSource: 2
    - _AdvancedDissolveKeywordCutoutStandardSourceMapsMappingType: 0
    - _AdvancedDissolveKeywordDynamicMaskCount: 1
    - _AdvancedDissolveKeywordDynamicMaskType: 1
    - _AdvancedDissolveKeywordEdgeAdditionalColorSource: 0
    - _AdvancedDissolveKeywordEdgeBaseSource: 3
    - _AdvancedDissolveKeywordEdgeBlendColor: 0
    - _AdvancedDissolveKeywordEdgeUVDistortionSource: 0
    - _AdvancedDissolveKeywordGlobalControlID: 0
    - _AdvancedDissolveKeywordState: 1
    - _AdvancedDissolveMetaPassMultiplier: 1
    - _AlbedoAffectEmissive: 0
    - _AlphaClip: 0
    - _AlphaCutoff: 0.5
    - _AlphaCutoffEnable: 0
    - _AlphaCutoffPostpass: 0.5
    - _AlphaCutoffPrepass: 0.5
    - _AlphaCutoffShadow: 0.5
    - _AlphaDstBlend: 0
    - _AlphaSrcBlend: 1
    - _AlphaToMask: 0
    - _Anisotropy: 0
    - _Blend: 0
    - _BlendMode: 0
    - _BlendModePreserveSpecular: 1
    - _BumpScale: 1
    - _ClearCoatMask: 0
    - _ClearCoatSmoothness: 0
    - _CoatMask: 0
    - _Cull: 2
    - _CullMode: 2
    - _CullModeForward: 2
    - _Cutoff: 0.5
    - _DepthOffsetEnable: 0
    - _DetailAlbedoMapScale: 1
    - _DetailAlbedoScale: 1
    - _DetailNormalMapScale: 1
    - _DetailNormalScale: 1
    - _DetailSmoothnessScale: 1
    - _DiffusionProfile: 0
    - _DiffusionProfileHash: 0
    - _DisplacementLockObjectScale: 1
    - _DisplacementLockTilingScale: 1
    - _DisplacementMode: 0
    - _DistortionBlendMode: 0
    - _DistortionBlurBlendMode: 0
    - _DistortionBlurDstBlend: 1
    - _DistortionBlurRemapMax: 1
    - _DistortionBlurRemapMin: 0
    - _DistortionBlurScale: 1
    - _DistortionBlurSrcBlend: 1
    - _DistortionDepthTest: 1
    - _DistortionDstBlend: 1
    - _DistortionEnable: 0
    - _DistortionScale: 1
    - _DistortionSrcBlend: 1
    - _DistortionVectorBias: -1
    - _DistortionVectorScale: 2
    - _DoubleSidedEnable: 0
    - _DoubleSidedNormalMode: 1
    - _Drag: 1
    - _DstBlend: 0
    - _DstBlendAlpha: 0
    - _EmissiveColorMode: 1
    - _EmissiveExposureWeight: 1
    - _EmissiveIntensity: 0
    - _EmissiveIntensityUnit: 0
    - _EnableBlendModePreserveSpecularLighting: 1
    - _EnableFogOnTransparent: 1
    - _EnableGeometricSpecularAA: 0
    - _EnableMotionVectorForVertexAnimation: 0
    - _EnableSpecularOcclusion: 0
    - _EnableWind: 0
    - _EnergyConservingSpecularColor: 1
    - _EnvironmentReflections: 1
    - _GlossMapScale: 1
    - _Glossiness: 0.5
    - _GlossyReflections: 1
    - _HdrpVersion: 2
    - _HeightAmplitude: 0.02
    - _HeightCenter: 0.5
    - _HeightMapParametrization: 0
    - _HeightMax: 1
    - _HeightMin: -1
    - _HeightOffset: 0
    - _HeightPoMAmplitude: 2
    - _HeightTessAmplitude: 2
    - _HeightTessCenter: 0.5
    - _InitialBend: 1
    - _InvTilingScale: 1
    - _Ior: 1
    - _IridescenceMask: 1
    - _IridescenceThickness: 1
    - _LinkDetailsWithBase: 1
    - _MaterialID: 1
    - _Metallic: 1
    - _Mode: 0
    - _NormalMapSpace: 0
    - _NormalScale: 1
    - _OcclusionStrength: 1
    - _PPDLodThreshold: 5
    - _PPDMaxSamples: 15
    - _PPDMinSamples: 5
    - _PPDPrimitiveLength: 1
    - _PPDPrimitiveWidth: 1
    - _Parallax: 0.02
    - _PreRefractionPass: 0
    - _QueueOffset: 0
    - _ReceiveShadows: 1
    - _ReceivesSSR: 1
    - _RefractionMode: 0
    - _RefractionModel: 0
    - _SSRefractionProjectionModel: 0
    - _ShiverDirectionality: 0.5
    - _ShiverDrag: 0.2
    - _Smoothness: 1
    - _SmoothnessRemapMax: 1
    - _SmoothnessRemapMin: 0
    - _SmoothnessTextureChannel: 0
    - _SpecularAAScreenSpaceVariance: 0.1
    - _SpecularAAThreshold: 0.2
    - _SpecularHighlights: 1
    - _SpecularOcclusionMode: 1
    - _SrcBlend: 1
    - _SrcBlendAlpha: 1
    - _StencilRef: 0
    - _StencilRefDepth: 8
    - _StencilRefDistortionVec: 4
    - _StencilRefGBuffer: 10
    - _StencilRefMV: 40
    - _StencilWriteMask: 6
    - _StencilWriteMaskDepth: 8
    - _StencilWriteMaskDistortionVec: 4
    - _StencilWriteMaskGBuffer: 14
    - _StencilWriteMaskMV: 40
    - _Stiffness: 1
    - _SubsurfaceMask: 1
    - _SupportDBuffer: 1
    - _SupportDecals: 1
    - _Surface: 0
    - _SurfaceType: 0
    - _TexWorldScale: 1
    - _TexWorldScaleEmissive: 1
    - _Thickness: 1
    - _ThicknessMultiplier: 1
    - _TransmissionEnable: 1
    - _TransparentBackfaceEnable: 0
    - _TransparentCullMode: 2
    - _TransparentDepthPostpassEnable: 0
    - _TransparentDepthPrepassEnable: 0
    - _TransparentSortPriority: 0
    - _TransparentWritingMotionVec: 0
    - _TransparentZWrite: 0
    - _UVBase: 0
    - _UVDetail: 0
    - _UVEmissive: 0
    - _UVSec: 0
    - _UseEmissiveIntensity: 0
    - _UseShadowThreshold: 0
    - _WorkflowMode: 1
    - _ZTestDepthEqualForOpaque: 3
    - _ZTestGBuffer: 4
    - _ZTestModeDistortion: 4
    - _ZTestTransparent: 4
    - _ZWrite: 1
    m_Colors:
    - _AdvancedDissolveBakedKeywords: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutGeometric1Normal: {r: -0.7160739, g: -0.028946025, b: 0.6974241, a: 0}
    - _AdvancedDissolveCutoutGeometric1Position: {r: 7.72, g: 1.69, b: -5.5, a: 0}
    - _AdvancedDissolveCutoutGeometric2Normal: {r: 1, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutGeometric2Position: {r: -1.078, g: 1.472, b: 0.129, a: 0}
    - _AdvancedDissolveCutoutGeometric3Normal: {r: 1, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutGeometric3Position: {r: -1.59, g: 0.468, b: 1.471, a: 0}
    - _AdvancedDissolveCutoutGeometric4Normal: {r: 1, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutGeometric4Position: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutGeometricXYZPosition: {r: 0, g: 0, b: 1.22, a: 0}
    - _AdvancedDissolveCutoutMap1_Offset: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutMap1_Scroll: {r: 0, g: 0.5, b: 0, a: 0}
    - _AdvancedDissolveCutoutMap1_Tiling: {r: 1, g: 1, b: 1, a: 0}
    - _AdvancedDissolveCutoutMap1_Triplanar_Tiling: {r: 1, g: 1, b: 1, a: 1}
    - _AdvancedDissolveCutoutMap2_Offset: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutMap2_Scroll: {r: 0, g: 0.5, b: 0, a: 0}
    - _AdvancedDissolveCutoutMap2_Tiling: {r: 1, g: 1, b: 1, a: 0}
    - _AdvancedDissolveCutoutMap2_Triplanar_Tiling: {r: 1, g: 1, b: 1, a: 1}
    - _AdvancedDissolveCutoutMap3_Offset: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutMap3_Scroll: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutMap3_Tiling: {r: 1, g: 1, b: 1, a: 0}
    - _AdvancedDissolveCutoutMap3_Triplanar_Tiling: {r: 1, g: 1, b: 1, a: 1}
    - _AdvancedDissolveCutoutParametric1Normal: {r: 0.41310218, g: -0.7040947, b: 0.5775787, a: 0}
    - _AdvancedDissolveCutoutParametric1Position: {r: -0.2, g: 2.39, b: -2.68, a: 0}
    - _AdvancedDissolveCutoutParametric2Normal: {r: 1, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutParametric2Position: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutParametric3Normal: {r: 1, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutParametric3Position: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutParametric4Normal: {r: 1, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutParametric4Position: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutParametricXYZPosition: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutSimpleMap1Offset: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutSimpleMap1Scroll: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutSimpleMap1Tiling: {r: 1, g: 1, b: 1, a: 0}
    - _AdvancedDissolveCutoutSimpleMap2Offset: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutSimpleMap2Scroll: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutSimpleMap2Tiling: {r: 1, g: 1, b: 1, a: 0}
    - _AdvancedDissolveCutoutSimpleMap3Offset: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutSimpleMap3Scroll: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutSimpleMap3Tiling: {r: 1, g: 1, b: 1, a: 0}
    - _AdvancedDissolveCutoutStandardMap1Offset: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutStandardMap1Scroll: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutStandardMap1Tiling: {r: 1, g: 1, b: 1, a: 0}
    - _AdvancedDissolveCutoutStandardMap2Offset: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutStandardMap2Scroll: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutStandardMap2Tiling: {r: 1, g: 1, b: 1, a: 0}
    - _AdvancedDissolveCutoutStandardMap3Offset: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutStandardMap3Scroll: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveCutoutStandardMap3Tiling: {r: 1, g: 1, b: 1, a: 0}
    - _AdvancedDissolveDynamicMask2Normal: {r: 0, g: 1, b: 0, a: 0}
    - _AdvancedDissolveDynamicMask2Position: {r: 0.225, g: 0.274, b: 0.961, a: 0}
    - _AdvancedDissolveDynamicMask3Normal: {r: 1, g: 0, b: 0, a: 0}
    - _AdvancedDissolveDynamicMask3Position: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveDynamicMask4Normal: {r: 1, g: 0, b: 0, a: 0}
    - _AdvancedDissolveDynamicMask4Position: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveDynamicMaskNormal: {r: -0.5575174, g: -0.61124897, b: -0.5617378, a: 0}
    - _AdvancedDissolveDynamicMaskPosition: {r: 0.43, g: 0.65, b: -1.01, a: 0}
    - _AdvancedDissolveEdgeAdditionalColor: {r: 1, g: 0, b: 0, a: 1}
    - _AdvancedDissolveEdgeAdditionalColorIntensity: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveEdgeAdditionalColorMapOffset: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveEdgeAdditionalColorMapScroll: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveEdgeAdditionalColorMapTiling: {r: 1, g: 1, b: 0, a: 0}
    - _AdvancedDissolveEdgeBaseColor: {r: 0.8148468, g: 0.21586053, b: 0.015208514, a: 1}
    - _AdvancedDissolveEdgeBaseColorIntensity: {r: 4, g: 53.59815, b: 0, a: 0}
    - _AdvancedDissolveEdgeColor: {r: 0.015224886, g: 0.48570853, b: 0.81761277, a: 1}
    - _AdvancedDissolveEdgeColor2: {r: 1, g: 0, b: 0, a: 1}
    - _AdvancedDissolveEdgeColor2Intensity: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveEdgeColorIntensity: {r: 11, g: 59873.14, b: 0, a: 0}
    - _AdvancedDissolveEdgeMap_Offset: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveEdgeMap_Scroll: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveEdgeMap_Tiling: {r: 1, g: 1, b: 0, a: 0}
    - _AdvancedDissolveEdgeUVDistortionMapOffset: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveEdgeUVDistortionMapScroll: {r: 0, g: 0, b: 0, a: 0}
    - _AdvancedDissolveEdgeUVDistortionMapTiling: {r: 1, g: 1, b: 0, a: 0}
    - _AdvancedDissolveMetaPassObjectPosition: {r: 0, g: 0, b: 0, a: 0}
    - _BaseColor: {r: 1, g: 1, b: 1, a: 1}
    - _BaseColorMap_MipInfo: {r: 0, g: 0, b: 0, a: 0}
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _CurvedWorldBendSettings: {r: 0, g: 0, b: 0, a: 0}
    - _DiffusionProfileAsset: {r: 0, g: 0, b: 0, a: 0}
    - _DoubleSidedConstants: {r: 1, g: 1, b: -1, a: 0}
    - _EmissionColor: {r: 1, g: 1, b: 1, a: 1}
    - _EmissiveColor: {r: 0, g: 0, b: 0, a: 1}
    - _EmissiveColorLDR: {r: 0, g: 0, b: 0, a: 1}
    - _InvPrimScale: {r: 1, g: 1, b: 0, a: 0}
    - _IridescenceThicknessRemap: {r: 0, g: 1, b: 0, a: 0}
    - _SpecColor: {r: 0.2, g: 0.2, b: 0.2, a: 1}
    - _SpecularColor: {r: 1, g: 1, b: 1, a: 1}
    - _ThicknessRemap: {r: 0, g: 1, b: 0, a: 0}
    - _TransmittanceColor: {r: 1, g: 1, b: 1, a: 1}
    - _UVDetailsMappingMask: {r: 1, g: 0, b: 0, a: 0}
    - _UVMappingMask: {r: 1, g: 0, b: 0, a: 0}
    - _UVMappingMaskEmissive: {r: 1, g: 0, b: 0, a: 0}
  m_BuildTextureStacks: []
--- !u!114 &93942427365883542
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: da692e001514ec24dbc4cca1949ff7e8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 2
