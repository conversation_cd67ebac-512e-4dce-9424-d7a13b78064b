%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Screen RGB Small
  m_Shader: {fileID: 4800000, guid: b2d1613db98b404695e94352a295e34e, type: 3}
  m_ValidKeywords: []
  m_InvalidKeywords:
  - _ADD_PRECOMPUTED_VELOCITY
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap:
    MotionVector: User
  disabledShaderPasses:
  - TransparentBackface
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _Base:
        m_Texture: {fileID: 2800000, guid: 109f6beb94e9454981d4600a7be9d87b, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NoiseMap:
        m_Texture: {fileID: 2800000, guid: f801b5d60f6341cd885f97f23afdf8d8, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NoiseMap1:
        m_Texture: {fileID: 2800000, guid: d63752d68809404caadcb720622cb7f9, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NoiseTexture:
        m_Texture: {fileID: 2800000, guid: b888983e73ae97d47a2ddcbf23bc4ccf, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NoiseTexture1:
        m_Texture: {fileID: 2800000, guid: 80ee511a5808d8d499a8ca850b8a2129, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Overlay:
        m_Texture: {fileID: 2800000, guid: 9680987116f4406785c50870a510900b, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _RGB:
        m_Texture: {fileID: 2800000, guid: ae2fc962c2d555344ae50444519518a3, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Texture0:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TextureSample1:
        m_Texture: {fileID: 2800000, guid: 9580df54da1e4d87b0f89f2e36519caf, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _newyorkcity:
        m_Texture: {fileID: 2800000, guid: 109f6beb94e9454981d4600a7be9d87b, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _newyorkcity1:
        m_Texture: {fileID: 2800000, guid: 9580df54da1e4d87b0f89f2e36519caf, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _texcoord:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AddPrecomputedVelocity: 1
    - _AlphaCutoff: 0.5
    - _AlphaCutoffEnable: 0
    - _AlphaDstBlend: 0
    - _AlphaSquaresSize: 5.21
    - _AlphaSrcBlend: 1
    - _BlendMode: 0
    - _CullMode: 2
    - _CullModeForward: 2
    - _DoubleSidedEnable: 0
    - _DoubleSidedNormalMode: 2
    - _DstBlend: 0
    - _EnableFogOnTransparent: 1
    - _EnvironmentReflections: 1
    - _Float0: 1
    - _Float2: 2.31
    - _Float3: 8.77
    - _Float4: 0.19
    - _GlitchAmount: 0.2683511
    - _GlitchAmount1: 0.806
    - _HorizontalGlitchAmount: 5
    - _HorizontalGlitchAmount1: 7.61
    - _Intensity: 3.83
    - _Intensity1: 0
    - _NoiseMapStrength: 0.17
    - _NoiseMapStrength1: 0.24829493
    - _NoiseThreshold: 0.36407146
    - _NoiseThreshold1: 0.791
    - _NoiseTiling: 0.09458872
    - _NoiseTiling1: 0.544
    - _Perturbation: 3
    - _Perturbation1: 5.14
    - _QueueControl: 0
    - _QueueOffset: 0
    - _ReceiveShadows: 1
    - _ReceivesSSR: 1
    - _RectSize: 1
    - _RectSize1: 1.71
    - _RenderQueueType: 1
    - _RequireSplitLighting: 0
    - _Smoothness: 0.732
    - _Specular: 0.05
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _StencilRef: 0
    - _StencilRefDepth: 8
    - _StencilRefDistortionVec: 4
    - _StencilRefGBuffer: 10
    - _StencilRefMV: 40
    - _StencilWriteMask: 6
    - _StencilWriteMaskDepth: 8
    - _StencilWriteMaskDistortionVec: 4
    - _StencilWriteMaskGBuffer: 14
    - _StencilWriteMaskMV: 40
    - _SurfaceType: 0
    - _TimeScale: 0.01
    - _TimeScale1: 33
    - _TransparentBackfaceEnable: 0
    - _TransparentCullMode: 2
    - _TransparentSortPriority: 0
    - _TransparentZWrite: 1
    - _UseShadowThreshold: 0
    - _VerticalGlitchAmount: 7
    - _VerticalGlitchAmount1: 3.25
    - _ZTestDepthEqualForOpaque: 3
    - _ZTestGBuffer: 4
    - _ZTestTransparent: 4
    - _ZWrite: 1
    - __dirty: 0
    m_Colors:
    - _Color0: {r: 0.29003114, g: 0.5338787, b: 2.4594646, a: 0}
    - _DoubleSidedConstants: {r: 1, g: 1, b: -1, a: 0}
    - _EmissionColor: {r: 1, g: 1, b: 1, a: 1}
    - _NoiseMapPannerSpeed: {r: 0.2, g: 0.1, b: 0, a: 0}
    - _NoiseMapPannerSpeed1: {r: 1.61, g: -0.43, b: 0, a: 0}
    - _NoiseMapSize: {r: 1, g: 1, b: 0, a: 0}
    - _NoiseMapSize1: {r: 1, g: 1, b: 0, a: 0}
    - _RingPannerSpeed: {r: 0.02, g: -0.05, b: 0, a: 0}
    - _RingPannerSpeed1: {r: 0.1, g: -1.27, b: 0, a: 0}
    - _Tint: {r: 3.9187057, g: 8.101142, b: 10.789165, a: 1}
    - _Vector1: {r: 350, g: 350, b: 0, a: 0}
  m_BuildTextureStacks: []
