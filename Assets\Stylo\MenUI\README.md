# Simple MenUI - Streamlined Pause Menu System

A simple, reliable UGUI-based pause menu system that just works. No over-engineering, no complex initialization - just direct, reliable functionality.

## Features

- **Simple UGUI Implementation**: Uses standard Unity UI components
- **Cyberpunk Aesthetic**: Dark theme with cyan accents and glow effects
- **Configurable Overlay Background**: Transparent overlay behind pause menu with adjustable color/transparency
- **Editor Testing Controls**: Context menu buttons to show/hide pause menu in editor mode
- **Time Management Integration**: Pauses all systems via TimeManager.Instance
- **Input System Integration**: Uses existing DefaultControls input actions
- **GameManager Compatible**: Drop-in replacement for previous MenUI system

## Quick Setup

### Option 1: Automatic Setup (Recommended)

1. In Unity Editor, go to `Stylo → MenUI → Create Pause Menu UI`
2. This will automatically create the complete UI structure
3. Assign the created MenUI System to GameManager's pauseMenuManager field

### Option 2: Manual Setup

1. **Create Canvas** (if not exists):

   - Render Mode: Screen Space - Overlay
   - Sort Order: 100

2. **Create MenUI System**:

   - Right-click Canvas → Create Empty → Name: "MenUI System"
   - Add Component: MenUISystem

3. **Create Pause Panel**:

   - Right-click MenUI System → UI → Panel → Name: "PauseMenuPanel"
   - Set to full screen stretch
   - Background color: Dark semi-transparent

4. **Create Buttons**:

   - Create 3 buttons under PauseMenuPanel:
     - Right-click PauseMenuPanel → UI → Button - TextMeshPro
     - Name them: "ResumeButton", "SettingsButton", "ExitButton"
     - Set button texts to: "RESUME", "SETTINGS", "EXIT"
     - **Important**: Use TextMeshPro buttons for proper text rendering

5. **Configure MenUISystem**:
   - Assign all UI references in the inspector
   - Link to GameManager's pauseMenuManager field

## Architecture

### Core Components

- **MenUISystem**: Main controller handling pause logic and UI
- **MenUISetupHelper**: Utility for programmatic UI creation
- **TimeManager Integration**: Uses existing Epoch time system
- **Input System**: Leverages existing DefaultControls

### Input Bindings

The system uses the existing UI action map's Pause action:

- **Keyboard**: Escape key
- **Controller**: Menu/Start button

### Time Management

Properly pauses all game systems:

```csharp
// Pause
TimeManager.Instance.PauseTime();  // Epoch time system
Time.timeScale = 0f;               // Unity time scale
AudioListener.pause = true;        // Audio system

// Resume
TimeManager.Instance.ResumeTime(); // Epoch time system
Time.timeScale = previousScale;    // Unity time scale
AudioListener.pause = false;       // Audio system
```

## GameManager Integration

The MenUISystem provides the same interface as the previous implementation:

```csharp
public class GameManager : MonoBehaviour
{
    [SerializeField] private MenUISystem pauseMenuManager;

    private void HandlePlayerDeath()
    {
        pauseMenuManager.ShowPause(); // Show pause menu
    }

    private void HandleGameRestart()
    {
        pauseMenuManager.HidePause(); // Hide pause menu
    }
}
```

## Styling

### Cyberpunk Theme

- **Background**: Dark blue-black with transparency
- **Buttons**: Blue gradient with cyan highlights
- **Text**: Cyan color with bold cyberpunk fonts
- **Effects**: Subtle glow and shadow effects

### Font Configuration

The system automatically tries to load cyberpunk-style fonts in this order:

1. **WO3 SDF** - WipEout style (most cyberpunk)
2. **Fusion SDF** - Clean cyberpunk look
3. **square_sans_serif_7 SDF** - Tech/digital style
4. **GlacialIndifference-Regular SDF** - Clean modern
5. **LiberationSans SDF** - Default fallback

**Manual Font Assignment**:

- Select TextMeshPro text components in buttons
- In Font Asset field, choose from available SDF fonts
- Recommended: WO3 SDF or Fusion SDF for best cyberpunk look

### Customization

Modify colors and styling in the MenUISetupHelper.ApplyCyberpunkStyling() method or directly in the inspector.

## Configuration Options

### MenUISystem Inspector Settings

- **Enable Debug Mode**: Logs pause/resume actions
- **Pause Audio**: Whether to pause AudioListener
- **Enable Overlay**: Show/hide the background overlay
- **Overlay Color**: Color and transparency of the background overlay

### Button Callbacks

- **Resume**: Hides pause menu and resumes game
- **Settings**: Placeholder for future settings menu
- **Exit**: Quits application (or stops play mode in editor)

### Editor Testing Controls

Right-click on MenUISystem component in inspector to access:

- **Show Pause Menu (Editor)**: Display pause menu in editor mode for testing
- **Hide Pause Menu (Editor)**: Hide pause menu in editor mode
- **Toggle Pause Menu (Editor)**: Toggle pause menu visibility in editor
- **Update Overlay Color**: Apply overlay color changes in real-time
- **Recreate Overlay Panel**: Rebuild overlay panel if needed

## Troubleshooting

### Common Issues

1. **Compilation Errors**: Ensure MenUISystem.cs is in the correct namespace
2. **UI Not Showing**: Check Canvas sort order and MenUISystem configuration
3. **Input Not Working**: Verify DefaultControls input actions are properly set up
4. **Time Not Pausing**: Ensure TimeManager.Instance is available in scene
5. **Mouse Interaction Not Working**: See Mouse/Touch Interaction Issues below
6. **Menu Visible in Editor**: See Editor Mode Visibility Issues below

### Mouse/Touch Interaction Issues

**Problem**: Buttons don't respond to mouse clicks or touch input

**Solutions**:

1. **Check EventSystem**: Ensure an EventSystem exists in the scene

   - Look for "EventSystem" GameObject in hierarchy
   - Should have EventSystem and InputSystemUIInputModule components
   - Use `Stylo → MenUI → Validate System` to check

2. **Check GraphicRaycaster**: Canvas must have GraphicRaycaster component

   - Select the Canvas containing MenUI System
   - Ensure GraphicRaycaster component is present
   - Automatic setup adds this, but manual setup may miss it

3. **Check Canvas Settings**:

   - Render Mode: Screen Space - Overlay (recommended)
   - Sort Order: 100 or higher (above other UI)
   - Canvas Scaler: Scale With Screen Size (for proper scaling)

4. **Validate Setup**: Use `Stylo → MenUI → Validate System` for comprehensive checks

### Editor Mode Visibility Issues

**Problem**: Pause menu visible in editor mode when it shouldn't be

**Solutions**:

1. **Check hideInEditorMode Setting**: In MenUISystem inspector, ensure "Hide In Editor Mode" is enabled
2. **Manual Deactivation**: If automatic hiding fails, manually deactivate PauseMenuPanel in editor
3. **Proper Setup**: Use automatic setup which creates panel deactivated by default

**Best Practice**: The pause menu panel should be deactivated by default, not just hidden via Canvas

### Input System Issues

**Problem**: Pause input (Escape key) not working

**Solutions**:

1. **Check Input Actions**: Verify DefaultControls.inputactions has UI.Pause action
2. **Check Bindings**: Ensure Pause action has proper keyboard/controller bindings
3. **Check Input Module**: EventSystem needs InputSystemUIInputModule for new Input System

### Debug Mode

Enable debug mode in MenUISystem to see detailed logs of pause/resume operations and component validation.

## GameManager Integration

The SimpleMenUISystem provides a clean API for game integration:

- `ShowPause()` - Show pause menu and pause all systems
- `HidePause()` - Hide pause menu and resume all systems
- `TogglePause()` - Toggle pause state
- `ShowSettings()` - Show settings menu
- `HideSettings()` - Hide settings menu

GameManager integration is seamless - just assign the SimpleMenUISystem to the pauseMenuManager field.

## Settings System

The MenUI system now includes a comprehensive tabbed settings interface with the following features:

### Settings Categories

- **Graphics Settings**: Quality presets, resolution, fullscreen, VSync, anti-aliasing, HDR, shadows, post-processing
- **Audio Settings**: Master/Music/SFX/Ambient/Voice volumes, FMOD integration, audio quality, speaker modes
- **Controls Settings**: Mouse/controller sensitivity, key bindings, input preferences, deadzone settings
- **Gameplay Settings**: Difficulty, accessibility options, HUD settings, auto-save, performance options

### Quick Setup

1. **Automatic Setup** (Recommended):

   ```
   Stylo → MenUI → Create Complete Settings UI
   ```

   This creates the entire tabbed settings interface automatically.

2. **Use Simple MenUI System** (Recommended):
   - Use `Stylo → MenUI → Create Simple MenUI System` instead
   - This creates a streamlined, reliable settings system
   - No complex setup or initialization issues

### Settings Persistence

Settings are automatically saved and loaded using:

- **JSON Files**: Stored in `Application.persistentDataPath/GameSettings.json`
- **PlayerPrefs**: Fallback option for individual settings
- **Automatic Saving**: On application pause/focus loss/quit

### Settings Panel Components

- **SimpleMenUISystem**: Streamlined controller for pause menu and settings
- **Graphics Settings Panel**: Ready for graphics and rendering options
- **Audio Settings Panel**: Ready for audio volumes and FMOD integration
- **Controls Settings Panel**: Ready for input sensitivity and key bindings
- **Gameplay Settings Panel**: Ready for game-specific and accessibility options
- **Debug Settings Panel**: Ready for developer tools and debug options

### Integration with Existing Systems

- **FMOD Audio**: Direct bus volume control and real-time audio settings
- **Unity Quality Settings**: Automatic application of graphics presets
- **Input System**: Key rebinding with new Input System support
- **URP Integration**: Render scale, MSAA, and pipeline-specific settings

### Usage Example

```csharp
// Show settings from pause menu (Simple MenUI System)
simpleMenUISystem.ShowSettings();

// Show specific settings category
simpleMenUISystem.ShowSettingsCategory(graphicsPanel);

// Toggle pause state
simpleMenUISystem.TogglePause();
```

### Customization

- **Category Icons**: Assign sprites to `SettingsCategory.categoryIcon`
- **Color Themes**: Modify colors in `SimpleMenUICreator` editor script
- **Additional Categories**: Create new panels inheriting from base settings pattern
- **Custom Settings**: Add new options to existing panel classes

## Future Enhancements

- Settings menu implementation
- Controller navigation improvements
- Animation transitions
- Sound effects
- Additional pause menu options
