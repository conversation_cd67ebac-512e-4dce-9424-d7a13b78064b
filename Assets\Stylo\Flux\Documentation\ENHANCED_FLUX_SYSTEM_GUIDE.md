# Enhanced Flux System - Complete Usage Guide

## Overview

This guide covers the enhanced Flux datamoshing system with consolidated parameters, adaptive quality scaling, and mobile optimizations implemented in Unity 6.

## 🚀 Quick Start

### Basic Setup
1. Add Flux Renderer Feature to your URP Renderer
2. Create a Volume Profile with Flux Effect
3. Start with a preset: "VHS Datamosh" or "Digital Glitch"
4. Adjust Effect Intensity to taste (0.3-0.7 recommended)

### Essential Parameters
- **Effect Intensity**: Master control (0-1) - affects all other parameters
- **Motion Amplification**: How strongly motion affects the effect (2-5 recommended)
- **Trail Intensity**: Primary control for pixel trailing (1-3 recommended)
- **Block Size**: Visual style - 4x4 fast, 8x8 classic, 16x16 dramatic

## 📋 Parameter Reference

### Master Controls
| Parameter | Range | Description | Performance Impact |
|-----------|-------|-------------|-------------------|
| **Effect Intensity** | 0-1 | Master control affecting all parameters | Low |
| **Only Stenciled** | Bool | Apply effect only to marked objects | Low |

### Motion Processing Pipeline
The new consolidated motion system replaces previous overlapping parameters:

| Parameter | Range | Description | Replaces |
|-----------|-------|-------------|----------|
| **Motion Amplification** | 0-10 | Master motion sensitivity | Camera Motion Amplification, Pixel Flow Intensity |
| **Motion Threshold** | 0-0.1 | Minimum motion to trigger effects | Camera Motion Threshold |
| **Camera vs Object Motion Balance** | 0-1 | 0=objects only, 1=camera only | Camera Motion Influence |
| **Motion Smoothing** | 0-1 | Reduces jittery motion | Camera Motion Smoothing |

### Pixel Trailing System
Simplified and more powerful trail controls:

| Parameter | Range | Description | Notes |
|-----------|-------|-------------|-------|
| **Trail Intensity** | 0-5 | Primary trail strength | Replaces Pixel Flow Intensity |
| **Trail Smoothness** | 0-1 | Blocky (0) vs smooth (1) trails | Visual style choice |
| **Trail Persistence** | 0-1 | How long trails last | Replaces Motion Persistence + Temporal Accumulation |
| **Flow Spread** | 0-5 | Organic flow patterns around motion | Replaces Flow Gradient |

### Core Datamoshing
Always-active compression simulation:

| Parameter | Range | Description | Performance Impact |
|-----------|-------|-------------|-------------------|
| **Color Crunch** | 0-1 | JPEG-style color quantization | Low |
| **Downscaling** | 1-10 | Resolution division before processing | High |
| **Block Size** | 2x2-32x32 | Compression block size | Very High |
| **Base Noise** | 0-1 | Random reprojection chance | Medium |
| **Length Influence** | 0-5 | Motion vector influence on reprojection | Medium |

### Enhanced Corruption Features
Advanced glitch effects:

| Parameter | Range | Description | Quality Level |
|-----------|-------|-------------|---------------|
| **Keyframe Reset Rate** | 0-1 | I-frame simulation frequency | All |
| **Motion Vector Corruption** | 0-2 | Motion vector noise | Medium+ |
| **Error Accumulation** | 0-1 | Temporal error buildup | Medium+ |
| **DCT Corruption** | 0-1 | Frequency coefficient corruption | High+ |
| **Chroma Corruption** | 0-1 | RGB channel separation | Medium+ |
| **Multi-Scale Corruption** | 0-1 | Multiple corruption scales | High+ |

### JPEG Compression Simulation
Authentic compression artifacts:

| Parameter | Range | Description | Mobile |
|-----------|-------|-------------|--------|
| **JPEG Quality** | 1-100 | Compression quality (lower = more artifacts) | ✓ |
| **Luminance Quantization** | 0-2 | Brightness channel quantization | ✓ |
| **Chrominance Quantization** | 0-2 | Color channel quantization | ✓ |
| **Chroma Subsampling** | Bool | 4:2:0 chroma subsampling | ✓ |
| **Ringing Artifacts** | 0-1 | Edge ringing simulation | Limited |
| **Mosquito Noise** | 0-1 | High-frequency noise around edges | Limited |

## 🎯 Parameter Interactions

### Motion Requirements
- **Trail effects require Motion Amplification > 0**
- **Motion effects require Base Noise OR Length Influence > 0**
- **Camera motion works best with Balance = 0.3-0.7**

### Performance Relationships
- **Large blocks (16x16, 32x32) + Low downscaling = Performance issues**
- **High Effect Intensity + Many corruption features = GPU intensive**
- **Mobile automatically reduces quality and disables expensive features**

### Common Conflicts
❌ **Trail Intensity > 0 but Motion Amplification = 0** → No trails will show
❌ **Motion features enabled but no reprojection** → Limited motion response  
❌ **32x32 blocks with downscaling < 5** → Severe performance impact
❌ **JPEG Quality < 10 with Effect Intensity > 0.8** → Visual artifacts

## 🔧 Quality & Performance

### Adaptive Quality System
The system automatically adjusts quality based on performance:

| Quality Level | Frame Time Target | Adjustments |
|---------------|------------------|-------------|
| **Low** | > 33ms (< 30 FPS) | 50% effects, smaller blocks, more downscaling |
| **Medium** | 16-33ms (30-60 FPS) | 75% effects, moderate adjustments |
| **High** | 8-16ms (60-120 FPS) | 100% effects, no adjustments |
| **Ultra** | < 8ms (> 120 FPS) | 120% effects, enhanced quality |

### Mobile Optimizations
On mobile platforms:
- Motion vectors disabled (too expensive)
- Reduced sample counts in trail processing
- 2-scale corruption instead of 3-scale
- Aggressive downscaling multipliers
- Frame skipping below 30 FPS
- Smaller block size limits

### Performance Tiers
| Tier | Hardware | Recommended Settings |
|------|----------|---------------------|
| **Mobile** | Phones, tablets | 4x4 blocks, downscaling 6+, limited corruption |
| **Console** | PS5, Xbox Series | 8x8 blocks, downscaling 4+, moderate corruption |
| **PC** | Desktop GPUs | 16x16 blocks, downscaling 2+, full corruption |
| **High-End** | RTX 4080+ | 32x32 blocks, downscaling 1+, maximum corruption |

## 📱 Platform-Specific Guidelines

### Mobile (iOS/Android)
```
Block Size: 2x2 or 4x4 only
Downscaling: 8-10
Effect Intensity: 0.3-0.6
Trail Intensity: 0.5-1.5
Corruption features: Minimal
Motion vectors: Disabled
```

### Console (PlayStation/Xbox)
```
Block Size: 4x4 to 8x8
Downscaling: 4-6
Effect Intensity: 0.4-0.8
Trail Intensity: 1.0-2.5
Corruption features: Moderate
Motion vectors: Enabled
```

### PC (Desktop)
```
Block Size: 8x8 to 16x16
Downscaling: 2-4
Effect Intensity: 0.5-1.0
Trail Intensity: 1.5-4.0
Corruption features: Full
Motion vectors: Enabled
```

## 🎨 Artistic Guidelines

### VHS Aesthetic
```
Block Size: 8x8
Color Crunch: 0.6
Keyframe Reset: 0.05
Motion Vector Corruption: 1.2
Error Accumulation: 0.6
Chroma Corruption: 0.4
```

### Digital Glitch
```
Block Size: 4x4
Feedback Intensity: 0.6
Multi-Scale Corruption: 0.8
DCT Corruption: 0.5
Trail Intensity: 2.0
Flow Spread: 3.0
```

### Satellite Corruption
```
Block Size: 16x16
Motion Vector Corruption: 2.0
Error Accumulation: 0.8
Multi-Scale Corruption: 0.6
Keyframe Reset: 0.02
```

### Subtle Compression
```
Block Size: 2x2
JPEG Quality: 40
Color Crunch: 0.3
Ringing Artifacts: 0.3
Mosquito Noise: 0.2
```

## 🛠️ Troubleshooting

### No Effect Visible
1. Check Effect Intensity > 0
2. Verify Motion Amplification > 0 for trails
3. Enable Base Noise or Length Influence for motion
4. Ensure camera has motion vectors enabled (not mobile)

### Poor Performance
1. Reduce Block Size (16x16 → 8x8 → 4x4)
2. Increase Downscaling (double the value)
3. Lower Effect Intensity
4. Disable expensive corruption features
5. Use adaptive quality (enabled by default)

### Black Screen/Artifacts
1. Check depth texture availability
2. Verify URP renderer has motion vectors enabled
3. Ensure MSAA compatibility in settings
4. Check for shader compilation errors

### Motion Effects Not Working
1. Verify Motion Amplification > 0
2. Enable Base Noise (0.1+) or Length Influence (0.1+)
3. Check motion vectors are available (not Scene View)
4. Ensure camera is moving or objects have motion

## 🔍 Validation & Auto-Fix

The system includes automatic validation:

### Parameter Validation
```csharp
// Check for parameter conflicts
string warnings = fluxEffect.ValidateParameters();
if (!string.IsNullOrEmpty(warnings))
    Debug.LogWarning(warnings);
```

### Auto-Fix Common Issues
```csharp
// Automatically fix common parameter conflicts
string fixes = fluxEffect.AutoFixParameters();
if (!string.IsNullOrEmpty(fixes))
    Debug.Log("Auto-fixed: " + fixes);
```

### Preset Validation
```csharp
// Validate preset performance and conflicts
var result = FluxPresetManager.ValidatePreset(preset);
if (result.hasIssues)
{
    Debug.LogWarning("Preset issues: " + string.Join(", ", result.warnings));
}
```

## 🚨 Breaking Changes from Previous Version

### Removed Parameters
- `CameraMotionAmplification` → Use `MotionAmplification`
- `CameraMotionThreshold` → Use `MotionThreshold`  
- `CameraMotionInfluence` → Use `CameraObjectMotionBalance`
- `CameraMotionSmoothing` → Use `MotionSmoothing`
- `PixelFlowIntensity` → Use `TrailIntensity`
- `MotionPersistence` → Use `TrailPersistence`
- `FlowGradient` → Use `FlowSpread`
- `TemporalAccumulation` → Integrated into `TrailPersistence`

### Behavior Changes
- Motion processing is now unified and non-conflicting
- Mobile platforms automatically disable motion vectors
- Shader variants reduced from 1024 to 160
- Adaptive quality enabled by default
- Parameter validation runs automatically

### Migration Guide
1. Update presets to use new parameter names
2. Adjust values - new parameters have stronger effects
3. Test on target platforms with new mobile optimizations
4. Use validation tools to check for issues

## 📖 API Reference

### FluxEffect Methods
```csharp
// Parameter validation
string ValidateParameters()
string AutoFixParameters()

// Quality control
bool IsActive()
bool IsTileCompatible()
```

### FluxPresetManager Methods
```csharp
// Preset management
List<FluxPreset> GetAllPresets()
List<FluxPreset> GetPresetsByPerformanceTier(PerformanceTier tier)
FluxPreset FindPresetByName(string name)

// Validation
PresetValidationResult ValidatePreset(FluxPreset preset)
string AutoFixPreset(FluxPreset preset)
PerformanceTier GetPresetPerformanceTier(FluxPreset preset)
```

### Performance Monitoring
```csharp
// Adaptive quality system automatically monitors:
// - Frame time history (60 frames)
// - Quality level adjustment
// - Mobile platform detection
// - Parameter scaling based on performance
```

---

For technical support and advanced usage, refer to the individual parameter documentation files in the Documentation folder.