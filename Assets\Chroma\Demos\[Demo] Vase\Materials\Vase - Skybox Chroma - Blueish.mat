%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!28 &-8444485339891319028
Texture2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: z__GradientTex{"mode":0,"colorKeys":[{"color":{"r":1.0,"g":0.8666666746139526,"b":0.5764706134796143,"a":1.0},"time":0.34065765142440798},{"color":{"r":0.3450980484485626,"g":0.8549019694328308,"b":0.8549019694328308,"a":1.0},"time":0.5659266114234924},{"color":{"r":0.10980392247438431,"g":0.6352941393852234,"b":0.7333333492279053,"a":1.0},"time":0.8351720571517944},{"color":{"r":0.0,"g":0.3686274588108063,"b":0.48627451062202456,"a":1.0},"time":1.0}],"alphaKeys":[{"alpha":1.0,"time":0.0},{"alpha":1.0,"time":1.0}]}
  m_ImageContentsHash:
    serializedVersion: 2
    Hash: d8f128f9b827e3a322a354637c4112e7
  m_ForcedFallbackFormat: 4
  m_DownscaleFallback: 0
  m_IsAlphaChannelOptional: 0
  serializedVersion: 2
  m_Width: 1024
  m_Height: 1
  m_CompleteImageSize: 8192
  m_MipsStripped: 0
  m_TextureFormat: 17
  m_MipCount: 1
  m_IsReadable: 1
  m_IsPreProcessed: 0
  m_IgnoreMasterTextureLimit: 0
  m_StreamingMipmaps: 0
  m_StreamingMipmapsPriority: 0
  m_VTOnly: 0
  m_AlphaIsTransparency: 0
  m_ImageCount: 1
  m_TextureDimension: 2
  m_TextureSettings:
    serializedVersion: 2
    m_FilterMode: 1
    m_Aniso: 1
    m_MipBias: 0
    m_WrapU: 1
    m_WrapV: 1
    m_WrapW: 1
  m_LightmapFormat: 0
  m_ColorSpace: 0
  m_PlatformBlob: 
  image data: 8192
  _typelessdata: 003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003c003cef3a9d38003cfd3bef3a9e38003cf73bef3aa038003cf13bef3aa338003cec3bef3aa538003ce63bee3aa838003ce03bee3aaa38003cda3bee3aad38003cd43bee3aaf38003cce3bee3ab238003cc93bee3ab438003cc33bee3ab738003cbd3bee3ab938003cb73bee3abc38003cb13bee3abe38003cac3bed3ac138003ca63bed3ac338003ca03bed3ac538003c9a3bed3ac838003c943bed3aca38003c8e3bed3acd38003c893bed3acf38003c833bed3ad238003c7d3bed3ad438003c773bec3ad738003c713bec3ad938003c6c3bec3adc38003c663bec3ade38003c603bec3ae138003c5a3bec3ae338003c543bec3ae638003c4e3bec3ae838003c493bec3aeb38003c433bec3aed38003c3d3beb3af038003c373beb3af238003c313beb3af438003c2c3beb3af738003c263beb3af938003c203beb3afc38003c1a3beb3afe38003c143beb3a0139003c0e3beb3a0339003c093bea3a0639003c033bea3a0839003cfd3aea3a0b39003cf73aea3a0d39003cf13aea3a1039003cec3aea3a1239003ce63aea3a1539003ce03aea3a1739003cda3aea3a1a39003cd43aea3a1c39003cce3ae93a1f39003cc93ae93a2139003cc33ae93a2339003cbd3ae93a2639003cb73ae93a2839003cb13ae93a2b39003cab3ae93a2d39003ca63ae93a3039003ca03ae93a3239003c9a3ae93a3539003c943ae83a3739003c8e3ae83a3a39003c893ae83a3c39003c833ae83a3f39003c7d3ae83a4139003c773ae83a4439003c713ae83a4639003c6b3ae83a4939003c663ae83a4b39003c603ae73a4e39003c5a3ae73a5039003c543ae73a5239003c4e3ae73a5539003c493ae73a5739003c433ae73a5a39003c3d3ae73a5c39003c373ae73a5f39003c313ae73a6139003c2b3ae73a6439003c263ae63a6639003c203ae63a6939003c1a3ae63a6b39003c143ae63a6e39003c0e3ae63a7039003c093ae63a7339003c033ae63a7539003cfd39e63a7839003cf739e63a7a39003cf139e53a7d39003ceb39e53a7f39003ce639e53a8239003ce039e53a8439003cda39e53a8639003cd439e53a8939003cce39e53a8b39003cc839e53a8e39003cc339e53a9039003cbd39e53a9339003cb739e43a9539003cb139e43a9839003cab39e43a9a39003ca639e43a9d39003ca039e43a9f39003c9a39e43aa239003c9439e43aa439003c8e39e43aa739003c8839e43aa939003c8339e33aac39003c7d39e33aae39003c7739e33ab139003c7139e33ab339003c6b39e33ab539003c6639e33ab839003c6039e33aba39003c5a39e33abd39003c5439e33abf39003c4e39e33ac239003c4839e23ac439003c4339e23ac739003c3d39e23ac939003c3739e23acc39003c3139e23ace39003c2b39e23ad139003c2639e23ad339003c2039e23ad639003c1a39e23ad839003c1439e13adb39003c0e39e13add39003c0839e13ae039003c0339e13ae239003cfd38e13ae439003cf738e13ae739003cf138e13ae939003ceb38e13aec39003ce638e13aee39003ce038e13af139003cda38e03af339003cd438e03af639003cce38e03af839003cc838e03afb39003cc338e03afd39003cbd38e03a003a003cb738e03a023a003cb138e03a053a003cab38e03a073a003ca538e03a0a3a003ca038df3a0c3a003c9a38df3a0f3a003c9438df3a113a003c8e38df3a143a003c8838df3a163a003c8338df3a183a003c7d38df3a1b3a003c7738df3a1d3a003c7138df3a203a003c6b38de3a223a003c6538de3a253a003c6038de3a273a003c5a38de3a2a3a003c5438de3a2c3a003c4e38de3a2f3a003c4838de3a313a003c4338de3a343a003c3d38de3a363a003c3738de3a393a003c3138dd3a3b3a003c2b38dd3a3e3a003c2538dd3a403a003c2038dd3a433a003c1a38dd3a453a003c1438dd3a473a003c0e38dd3a4a3a003c0838dd3a4c3a003c0338dd3a4f3a003cf937dc3a513a003cee37dc3a543a003ce237dc3a563a003cd737dc3a593a003ccb37dc3a5b3a003cbf37dc3a5e3a003cb437dc3a603a003ca837dc3a633a003c9c37dc3a653a003c9137dc3a683a003c8537db3a6a3a003c7937db3a6d3a003c6e37db3a6f3a003c6237db3a723a003c5637db3a743a003c4b37db3a763a003c3f37db3a793a003c3437db3a7b3a003c2837db3a7e3a003c1c37da3a803a003c1137da3a833a003c0537da3a853a003cf936da3a883a003cee36da3a8a3a003ce236da3a8d3a003cd636da3a8f3a003ccb36da3a923a003cbf36da3a943a003cb436da3a973a003ca836d93a993a003c9c36d93a9c3a003c9136d93a9e3a003c8536d93aa13a003c7936d93aa33a003c6e36d93aa53a003c6236d93aa83a003c5636d93aaa3a003c4b36d93aad3a003c3f36d93aaf3a003c3336d83ab23a003c2836d83ab43a003c1c36d83ab73a003c1136d83ab93a003c0536d83abc3a003cf935d83abe3a003cee35d83ac13a003ce235d83ac33a003cd635d83ac63a003ccb35d73ac83a003cbf35d73acb3a003cb335d73acd3a003ca835d73ad03a003c9c35d73ad23a003c9035d73ad53a003c8535d73ad73a003c8235d53ad63a003c7e35d33ad53a003c7b35d23ad43a003c7735d03ad33a003c7435cf3ad23a003c7035cd3ad13a003c6d35cb3ad03a003c6935ca3ad03a003c6635c83acf3a003c6235c63ace3a003c5f35c53acd3a003c5b35c33acc3a003c5835c23acb3a003c5435c03aca3a003c5135be3ac93a003c4d35bd3ac83a003c4a35bb3ac73a003c4635b93ac73a003c4335b83ac63a003c3f35b63ac53a003c3c35b43ac43a003c3835b33ac33a003c3535b13ac23a003c3135b03ac13a003c2e35ae3ac03a003c2a35ac3abf3a003c2735ab3abe3a003c2335a93abd3a003c2035a73abd3a003c1c35a63abc3a003c1935a43abb3a003c1535a23aba3a003c1235a13ab93a003c0e359f3ab83a003c0b359e3ab73a003c07359c3ab63a003c04359a3ab53a003c0035993ab43a003cfd34973ab43a003cf934953ab33a003cf634943ab23a003cf234923ab13a003cef34913ab03a003ceb348f3aaf3a003ce8348d3aae3a003ce4348c3aad3a003ce1348a3aac3a003cdd34883aab3a003cda34873aaa3a003cd634853aaa3a003cd334833aa93a003ccf34823aa83a003ccc34803aa73a003cc8347f3aa63a003cc5347d3aa53a003cc1347b3aa43a003cbe347a3aa33a003cba34783aa23a003cb734763aa13a003cb334753aa13a003cb034733aa03a003cac34723a9f3a003ca934703a9e3a003ca5346e3a9d3a003ca2346d3a9c3a003c9e346b3a9b3a003c9b34693a9a3a003c9734683a993a003c9434663a983a003c9034643a983a003c8d34633a973a003c8934613a963a003c8634603a953a003c82345e3a943a003c7f345c3a933a003c7b345b3a923a003c7834593a913a003c7434573a903a003c7134563a8f3a003c6d34543a8e3a003c6a34523a8e3a003c6634513a8d3a003c63344f3a8c3a003c5f344e3a8b3a003c5c344c3a8a3a003c58344a3a893a003c5534493a883a003c5134473a873a003c4e34453a863a003c4a34443a853a003c4734423a853a003c4334413a843a003c40343f3a833a003c3c343d3a823a003c39343c3a813a003c35343a3a803a003c3234383a7f3a003c2e34373a7e3a003c2b34353a7d3a003c2734333a7c3a003c2434323a7b3a003c2034303a7b3a003c1d342f3a7a3a003c19342d3a793a003c16342b3a783a003c12342a3a773a003c0f34283a763a003c0b34263a753a003c0834253a743a003c0434233a733a003c0134213a723a003cfb33203a723a003cf4331e3a713a003ced331d3a703a003ce6331b3a6f3a003cdf33193a6e3a003cd833183a6d3a003cd133163a6c3a003cca33143a6b3a003cc333133a6a3a003cbc33113a693a003cb533103a693a003cae330e3a683a003ca7330c3a673a003ca0330b3a663a003c9933093a653a003c9233073a643a003c8b33063a633a003c8433043a623a003c7d33023a613a003c7633013a603a003c6f33ff395f3a003c6833fe395f3a003c6133fc395e3a003c5a33fa395d3a003c5333f9395c3a003c4c33f7395b3a003c4533f5395a3a003c3e33f439593a003c3733f239583a003c3033f139573a003c2933ef39563a003c2233ed39563a003c1b33ec39553a003c1433ea39543a003c0d33e839533a003c0633e739523a003cff32e539513a003cf832e339503a003cf132e2394f3a003cea32e0394e3a003ce332df394d3a003cdc32dd394c3a003cd532db394c3a003cce32da394b3a003cc732d8394a3a003cc032d639493a003cb932d539483a003cb232d339473a003cab32d139463a003ca432d039453a003c9d32ce39443a003c9632cd39433a003c8f32cb39433a003c8832c939423a003c8132c839413a003c7a32c639403a003c7332c4393f3a003c6c32c3393e3a003c6532c1393d3a003c5e32c0393c3a003c5732be393b3a003c5032bc393a3a003c4932bb393a3a003c4232b939393a003c3b32b739383a003c3432b639373a003c2d32b439363a003c2632b239353a003c1f32b139343a003c1832af39333a003c1132ae39323a003c0a32ac39313a003c0332aa39303a003cfc31a939303a003cf531a7392f3a003cee31a5392e3a003ce731a4392d3a003ce031a2392c3a003cd931a0392b3a003cd2319f392a3a003ccb319d39293a003cc4319c39283a003cbd319a39273a003cb6319839273a003caf319739263a003ca8319539253a003ca1319339243a003c9a319239233a003c93319039223a003c8c318f39213a003c85318d39203a003c7e318b391f3a003c77318a391e3a003c703188391d3a003c693186391d3a003c623185391c3a003c5b3183391b3a003c543181391a3a003c4d318039193a003c46317e39183a003c3f317d39173a003c38317b39163a003c31317939153a003c2a317839143a003c23317639143a003c1c317439133a003c15317339123a003c0e317139113a003c07317039103a003c00316e390f3a003cf9306c390e3a003cf2306b390d3a003ceb3069390c3a003ce43067390b3a003cdd3066390b3a003cd63064390a3a003ccf306239093a003cc8306139083a003cc1305f39073a003cba305e39063a003cb3305c39053a003cac305a39043a003ca5305939033a003c9e305739023a003c97305539013a003c90305439013a003c89305239003a003c82305039ff39003c7b304f39fe39003c74304d39fd39003c6d304c39fc39003c66304a39fb39003c5f304839fa39003c58304739f939003c51304539f839003c4a304339f839003c43304239f739003c3c304039f639003c35303f39f539003c2e303d39f439003c27303b39f339003c20303a39f239003c19303839f139003c12303639f039003c0b303539ef39003c04303339ee39003cfa2f3139ee39003cec2f3039ed39003cde2f2e39ec39003cd02f2d39eb39003cc22f2b39ea39003cb42f2939e939003ca62f2839e839003c982f2639e739003c8a2f2439e639003c7c2f2339e539003c6e2f2139e539003c602f2039e439003c522f1e39e339003c442f1c39e239003c362f1b39e139003c282f1939e039003c1a2f1739df39003c0c2f1639de39003c002f1339dc39003cf62e1039d939003ceb2e0d39d639003ce02e0939d339003cd62e0639d039003ccb2e0339cd39003cc02e0039ca39003cb62efc38c739003cab2ef938c439003ca02ef638c139003c962ef338be39003c8b2eef38bb39003c802eec38b839003c762ee938b539003c6b2ee638b239003c602ee238af39003c562edf38ac39003c4b2edc38a939003c402ed938a639003c362ed638a339003c2b2ed238a039003c202ecf389d39003c162ecc389a39003c0b2ec9389739003c002ec5389439003cf62dc2389139003ceb2dbf388e39003ce02dbc388b39003cd62db8388839003ccb2db5388539003cc02db2388239003cb62daf387f39003cab2dab387c39003ca02da8387939003c962da5387639003c8b2da2387339003c802d9e387039003c762d9b386d39003c6b2d98386a39003c602d95386739003c562d92386439003c4b2d8e386139003c402d8b385e39003c362d88385b39003c2b2d85385839003c202d81385539003c162d7e385239003c0b2d7b384f39003c002d78384c39003cf62c74384939003ceb2c71384639003ce02c6e384339003cd62c6b384039003ccb2c67383d39003cc02c64383a39003cb62c61383739003cab2c5e383439003ca02c5a383139003c962c57382e39003c8b2c54382b39003c802c51382839003c762c4e382539003c6b2c4a382239003c602c47381f39003c562c44381c39003c4b2c41381939003c402c3d381639003c362c3a381339003c2b2c37381039003c202c34380d39003c162c30380a39003c0b2c2d380739003c002c2a380439003ceb2b27380139003cd62b2338fe38003cc02b2038fb38003cab2b1d38f838003c962b1a38f538003c802b1638f238003c6b2b1338ef38003c562b1038ec38003c402b0d38e938003c2b2b0938e638003c162b0638e338003c002b0338e038003ceb2a0038dd38003cd62af937da38003cc02af337d738003cab2aec37d438003c962ae637d138003c802adf37ce38003c6b2ad937cb38003c562ad237c838003c402acc37c538003c2b2ac537c238003c162abf37bf38003c002ab837bc38003ceb29b237b938003cd629ab37b638003cc029a537b338003cab299e37b038003c96299837ad38003c80299137aa38003c6b298b37a738003c56298437a438003c40297e37a138003c2b2978379e38003c162971379b38003c00296b379838003ceb2864379538003cd6285e379238003cc02857378f38003cab2851378c38003c96284a378938003c802844378638003c6b283d378338003c562837378038003c402830377d38003c2b282a377a38003c162823377738003c00281d377438003cd62716377138003cab2710376e38003c802709376b38003c562703376838003c2b27fc366538003c0027f6366238003cd626ef365f38003cab26e9365c38003c8026e3365938003c5626dc365638003c2b26d6365338003c0026cf365038003cd625c9364d38003cab25c2364a38003c8025bc364738003c5625b5364438003c2b25af364138003c0025a8363e38003cd624a2363b38003cab249b363838003c802495363538003c56248e363238003c2b2488362f38003c002481362c38003cab237b362938003c562374362638003c00236e362338003cab2267362038003c562261361d38003c00225a361a38003cab2154361738003c56214e361438003c002147361138003cab2041360e38003c56203a360b38003c002034360838003c561f2d360538003cab1e27360238003c001e2036fe37003c561d1a36f837003cab1c1336f237003c001c0d36ec37003cab1a0636e637003c56190036e037003c0018f935da37003c5615f335d437003c5611ec35ce37003c0000e635c837003c
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!114 &-3923895880809708522
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0353a89b1f911e48b9e16bdc9f2e058, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 5
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Vase - Skybox Chroma - Blueish
  m_Shader: {fileID: 4800000, guid: 85e646ae6ff735241a05a66922d89b7b, type: 3}
  m_ValidKeywords: []
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BaseMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Gradient:
        m_Texture: {fileID: -8444485339891319028}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Intensity:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AlphaClip: 0
    - _Blend: 0
    - _BumpScale: 1
    - _ClearCoatMask: 0
    - _ClearCoatSmoothness: 0
    - _Cull: 2
    - _Cutoff: 0.5
    - _DetailAlbedoMapScale: 1
    - _DetailNormalMapScale: 1
    - _DirectionPitch: 0
    - _DirectionYaw: 0
    - _DstBlend: 0
    - _EnvironmentReflections: 1
    - _GlossMapScale: 0
    - _Glossiness: 0
    - _GlossyReflections: 0
    - _Metallic: 0
    - _OcclusionStrength: 1
    - _Parallax: 0.005
    - _QueueOffset: 0
    - _ReceiveShadows: 1
    - _Smoothness: 0.5
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _Surface: 0
    - _WorkflowMode: 1
    - _ZWrite: 1
    m_Colors:
    - _BaseColor: {r: 1, g: 1, b: 1, a: 1}
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _SpecColor: {r: 0.19999996, g: 0.19999996, b: 0.19999996, a: 1}
  m_BuildTextureStacks: []
