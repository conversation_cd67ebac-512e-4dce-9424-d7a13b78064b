%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Studio_CS_Ghost_URP_CS_Advanced Ghost - Lock On Cube
  m_Shader: {fileID: 4800000, guid: 03cbdd32a8958fe459d7b5edaae49ff7, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords: []
  m_InvalidKeywords:
  - _DUPLICATEDETAILS_ON
  - _ENABLESPECULARLIGHT_ON
  - _INVERT_ON
  - _MAPCONTRIBUTION_NONE
  - _UVSCREENPROJECTION_UVPROJECTION
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 1
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMap:
        m_Texture: {fileID: 2800000, guid: 8c38d2e72ca280f4783343ec9c4418f7, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AlphaCutoff: 0.5
    - _AmplitudeSpeed: 5
    - _BumpScale: 0.3
    - _ContrastDetailMap: 1
    - _DesaturateBackground: 0
    - _DetailScale: 1
    - _DuplicateDetails: 1
    - _EnableSpecularLight: 1
    - _FresnelBias: 0.072
    - _FresnelIntensity: 61.7
    - _FresnelPower: 9.28
    - _Glossiness: 0.447
    - _Invert: 1
    - _MapContribution: 0
    - _MaxValueAmplitude: 1
    - _MinValueAmplitude: 0
    - _Opacity: 1
    - _Refraction: 0.87
    - _RotationAngle: 2.13
    - _RotationSpeed: 0.18
    - _SelfIllumination: 2.29
    - _ShadowOpacity: 0.298
    - _SpreadDetailMap: 0
    - _TexturesScale: 1
    - _TranslationSpeed: 0.09
    - _UVScreenProjection: 0
    - _XRayBias: 0.04
    - _XRayFresnelIntensity: 1
    - _XRayFresnelPower: 1
    - __dirty: 1
    m_Colors:
    - _Color: {r: 0, g: 0, b: 0, a: 1}
    - _EmissionColor: {r: 1, g: 1, b: 1, a: 1}
    - _FillColorBackground: {r: 0, g: 0, b: 0, a: 0}
    - _FresnelColor: {r: 0.6933962, g: 1, b: 0.9814353, a: 1}
    - _XRayColor: {r: 0, g: 1, b: 0.98039216, a: 0.1019608}
  m_BuildTextureStacks: []
