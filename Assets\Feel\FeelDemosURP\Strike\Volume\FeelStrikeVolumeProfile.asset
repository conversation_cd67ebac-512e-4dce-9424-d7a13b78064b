%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-7893295128165547882
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0b2db86121404754db890f4c8dfe81b2, type: 3}
  m_Name: Bloom
  m_EditorClassIdentifier: 
  active: 1
  m_AdvancedMode: 0
  threshold:
    m_OverrideState: 1
    m_Value: 1
    min: 0
  intensity:
    m_OverrideState: 1
    m_Value: 2.5
    min: 0
  scatter:
    m_OverrideState: 0
    m_Value: 0.7
    min: 0
    max: 1
  clamp:
    m_OverrideState: 0
    m_Value: 65472
    min: 0
  tint:
    m_OverrideState: 0
    m_Value: {r: 1, g: 1, b: 1, a: 1}
    hdr: 0
    showAlpha: 0
    showEyeDropper: 1
  highQualityFiltering:
    m_OverrideState: 0
    m_Value: 0
  dirtTexture:
    m_OverrideState: 0
    m_Value: {fileID: 0}
  dirtIntensity:
    m_OverrideState: 0
    m_Value: 0
    min: 0
--- !u!114 &-7081212252951009005
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fb60a22f311433c4c962b888d1393f88, type: 3}
  m_Name: PaniniProjection
  m_EditorClassIdentifier: 
  active: 1
  m_AdvancedMode: 0
  distance:
    m_OverrideState: 1
    m_Value: 0
    min: 0
    max: 1
  cropToFit:
    m_OverrideState: 1
    m_Value: 1
    min: 0
    max: 1
--- !u!114 &-7011558710299706105
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 899c54efeace73346a0a16faa3afe726, type: 3}
  m_Name: Vignette
  m_EditorClassIdentifier: 
  active: 1
  m_AdvancedMode: 0
  color:
    m_OverrideState: 0
    m_Value: {r: 0, g: 0, b: 0, a: 1}
    hdr: 0
    showAlpha: 0
    showEyeDropper: 1
  center:
    m_OverrideState: 0
    m_Value: {x: 0.5, y: 0.5}
  intensity:
    m_OverrideState: 1
    m_Value: 0.331
    min: 0
    max: 1
  smoothness:
    m_OverrideState: 1
    m_Value: 0.4
    min: 0.01
    max: 1
  rounded:
    m_OverrideState: 0
    m_Value: 0
--- !u!114 &-5689738791633590597
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 81180773991d8724ab7f2d216912b564, type: 3}
  m_Name: ChromaticAberration
  m_EditorClassIdentifier: 
  active: 1
  m_AdvancedMode: 0
  intensity:
    m_OverrideState: 1
    m_Value: 0
    min: 0
    max: 1
--- !u!114 &-4803825905945671550
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 29fa0085f50d5e54f8144f766051a691, type: 3}
  m_Name: FilmGrain
  m_EditorClassIdentifier: 
  active: 1
  m_AdvancedMode: 0
  type:
    m_OverrideState: 1
    m_Value: 8
  intensity:
    m_OverrideState: 1
    m_Value: 0
    min: 0
    max: 1
  response:
    m_OverrideState: 1
    m_Value: 0.8
    min: 0
    max: 1
  texture:
    m_OverrideState: 1
    m_Value: {fileID: 0}
--- !u!114 &-4665665722927919062
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 66f335fb1ffd8684294ad653bf1c7564, type: 3}
  m_Name: ColorAdjustments
  m_EditorClassIdentifier: 
  active: 1
  m_AdvancedMode: 0
  postExposure:
    m_OverrideState: 1
    m_Value: 0
  contrast:
    m_OverrideState: 1
    m_Value: 20.1
    min: -100
    max: 100
  colorFilter:
    m_OverrideState: 1
    m_Value: {r: 1, g: 1, b: 1, a: 1}
    hdr: 1
    showAlpha: 0
    showEyeDropper: 1
  hueShift:
    m_OverrideState: 1
    m_Value: 0
    min: -180
    max: 180
  saturation:
    m_OverrideState: 1
    m_Value: 0
    min: -100
    max: 100
--- !u!114 &-4547279462740394649
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c5e1dc532bcb41949b58bc4f2abfbb7e, type: 3}
  m_Name: LensDistortion
  m_EditorClassIdentifier: 
  active: 1
  m_AdvancedMode: 0
  intensity:
    m_OverrideState: 1
    m_Value: 0
    min: -1
    max: 1
  xMultiplier:
    m_OverrideState: 1
    m_Value: 1
    min: 0
    max: 1
  yMultiplier:
    m_OverrideState: 1
    m_Value: 1
    min: 0
    max: 1
  center:
    m_OverrideState: 1
    m_Value: {x: 0.5, y: 0.5}
  scale:
    m_OverrideState: 1
    m_Value: 1
    min: 0.01
    max: 5
--- !u!114 &-2448478148110283005
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ccf1aba9553839d41ae37dd52e9ebcce, type: 3}
  m_Name: MotionBlur
  m_EditorClassIdentifier: 
  active: 1
  m_AdvancedMode: 0
  mode:
    m_OverrideState: 1
    m_Value: 0
  quality:
    m_OverrideState: 1
    m_Value: 2
  intensity:
    m_OverrideState: 1
    m_Value: 0.262
    min: 0
    max: 1
  clamp:
    m_OverrideState: 1
    m_Value: 0.05
    min: 0
    max: 0.2
--- !u!114 &-208047447922490061
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 221518ef91623a7438a71fef23660601, type: 3}
  m_Name: WhiteBalance
  m_EditorClassIdentifier: 
  active: 1
  m_AdvancedMode: 0
  temperature:
    m_OverrideState: 1
    m_Value: 7
    min: -100
    max: 100
  tint:
    m_OverrideState: 1
    m_Value: 0
    min: -100
    max: 100
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d7fd9488000d3734a9e00ee676215985, type: 3}
  m_Name: FeelStrikeVolumeProfile
  m_EditorClassIdentifier: 
  components:
  - {fileID: 849379129802519247}
  - {fileID: -7893295128165547882}
  - {fileID: -7011558710299706105}
  - {fileID: -5689738791633590597}
  - {fileID: -4665665722927919062}
  - {fileID: 7896825853726250482}
  - {fileID: -4547279462740394649}
  - {fileID: -2448478148110283005}
  - {fileID: -7081212252951009005}
  - {fileID: -208047447922490061}
  - {fileID: 6883507797812651821}
  - {fileID: -4803825905945671550}
--- !u!114 &849379129802519247
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 97c23e3b12dc18c42a140437e53d3951, type: 3}
  m_Name: Tonemapping
  m_EditorClassIdentifier: 
  active: 1
  m_AdvancedMode: 0
  mode:
    m_OverrideState: 1
    m_Value: 2
--- !u!114 &6883507797812651821
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cdfbdbb87d3286943a057f7791b43141, type: 3}
  m_Name: ChannelMixer
  m_EditorClassIdentifier: 
  active: 1
  m_AdvancedMode: 0
  redOutRedIn:
    m_OverrideState: 1
    m_Value: 100
    min: -200
    max: 200
  redOutGreenIn:
    m_OverrideState: 1
    m_Value: 0
    min: -200
    max: 200
  redOutBlueIn:
    m_OverrideState: 1
    m_Value: 0
    min: -200
    max: 200
  greenOutRedIn:
    m_OverrideState: 1
    m_Value: 0
    min: -200
    max: 200
  greenOutGreenIn:
    m_OverrideState: 1
    m_Value: 100
    min: -200
    max: 200
  greenOutBlueIn:
    m_OverrideState: 1
    m_Value: 0
    min: -200
    max: 200
  blueOutRedIn:
    m_OverrideState: 1
    m_Value: 0
    min: -200
    max: 200
  blueOutGreenIn:
    m_OverrideState: 1
    m_Value: 0
    min: -200
    max: 200
  blueOutBlueIn:
    m_OverrideState: 1
    m_Value: 100
    min: -200
    max: 200
--- !u!114 &7896825853726250482
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c01700fd266d6914ababb731e09af2eb, type: 3}
  m_Name: DepthOfField
  m_EditorClassIdentifier: 
  active: 1
  m_AdvancedMode: 0
  mode:
    m_OverrideState: 1
    m_Value: 2
  gaussianStart:
    m_OverrideState: 1
    m_Value: 10
    min: 0
  gaussianEnd:
    m_OverrideState: 1
    m_Value: 30
    min: 0
  gaussianMaxRadius:
    m_OverrideState: 1
    m_Value: 1
    min: 0.5
    max: 1.5
  highQualitySampling:
    m_OverrideState: 1
    m_Value: 0
  focusDistance:
    m_OverrideState: 1
    m_Value: 5.37
    min: 0.1
  aperture:
    m_OverrideState: 1
    m_Value: 3.56
    min: 1
    max: 32
  focalLength:
    m_OverrideState: 1
    m_Value: 100.8
    min: 1
    max: 300
  bladeCount:
    m_OverrideState: 1
    m_Value: 5
    min: 3
    max: 9
  bladeCurvature:
    m_OverrideState: 1
    m_Value: 1
    min: 0
    max: 1
  bladeRotation:
    m_OverrideState: 1
    m_Value: 0
    min: -180
    max: 180
