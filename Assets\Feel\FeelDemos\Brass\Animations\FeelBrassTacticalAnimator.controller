%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!206 &-4545143397978411216
BlendTree:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Blend Tree
  m_Childs:
  - serializedVersion: 2
    m_Motion: {fileID: -203655887218126122, guid: 05aa64df2cb150746a26399e4a1da0d6,
      type: 3}
    m_Threshold: 0
    m_Position: {x: 0, y: 0.5}
    m_TimeScale: 1
    m_CycleOffset: 0
    m_DirectBlendParameter: Random
    m_Mirror: 0
  - serializedVersion: 2
    m_Motion: {fileID: -203655887218126122, guid: a6c92ba70ca7df84f9d14161f8aef225,
      type: 3}
    m_Threshold: 0.14285715
    m_Position: {x: -0.5, y: 0}
    m_TimeScale: 1
    m_CycleOffset: 0
    m_DirectBlendParameter: Random
    m_Mirror: 0
  - serializedVersion: 2
    m_Motion: {fileID: -203655887218126122, guid: 4c7c15907bce1894f986f47e617bae66,
      type: 3}
    m_Threshold: 0.2857143
    m_Position: {x: 0.5, y: 0}
    m_TimeScale: 1
    m_CycleOffset: 0
    m_DirectBlendParameter: Random
    m_Mirror: 0
  - serializedVersion: 2
    m_Motion: {fileID: -203655887218126122, guid: a1a98e920cffd3d4691097eacbb1c5a6,
      type: 3}
    m_Threshold: 0.42857143
    m_Position: {x: -0.25, y: 0.25}
    m_TimeScale: 1
    m_CycleOffset: 0
    m_DirectBlendParameter: Random
    m_Mirror: 0
  - serializedVersion: 2
    m_Motion: {fileID: -203655887218126122, guid: 29a771460488ac746aeed0cdc3e2ed33,
      type: 3}
    m_Threshold: 0.5714286
    m_Position: {x: 0.25, y: 0.25}
    m_TimeScale: 1
    m_CycleOffset: 0
    m_DirectBlendParameter: Random
    m_Mirror: 0
  - serializedVersion: 2
    m_Motion: {fileID: -203655887218126122, guid: beb938024f06dfa40b6704096803b652,
      type: 3}
    m_Threshold: 0.71428573
    m_Position: {x: -0.25, y: -0.25}
    m_TimeScale: 1
    m_CycleOffset: 0
    m_DirectBlendParameter: Random
    m_Mirror: 0
  - serializedVersion: 2
    m_Motion: {fileID: -203655887218126122, guid: 864b1f262a74c38469a8d8a6159122d0,
      type: 3}
    m_Threshold: 0.85714287
    m_Position: {x: 0.25, y: -0.25}
    m_TimeScale: 1
    m_CycleOffset: 0
    m_DirectBlendParameter: Random
    m_Mirror: 0
  - serializedVersion: 2
    m_Motion: {fileID: -203655887218126122, guid: ad34f1272b7300e409f8e4de0cffd039,
      type: 3}
    m_Threshold: 1
    m_Position: {x: 0, y: -0.5}
    m_TimeScale: 1
    m_CycleOffset: 0
    m_DirectBlendParameter: Random
    m_Mirror: 0
  m_BlendParameter: RemappedLateralSpeedNormalized
  m_BlendParameterY: RemappedForwardSpeedNormalized
  m_MinThreshold: 0
  m_MaxThreshold: 1
  m_UseAutomaticThresholds: 1
  m_NormalizedBlendValues: 0
  m_BlendType: 3
--- !u!1101 &-4198847755270212729
AnimatorStateTransition:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  m_Conditions:
  - m_ConditionMode: 1
    m_ConditionEvent: Crouching
    m_EventTreshold: 0
  m_DstStateMachine: {fileID: 0}
  m_DstState: {fileID: 7482645143960757924}
  m_Solo: 0
  m_Mute: 0
  m_IsExit: 0
  serializedVersion: 3
  m_TransitionDuration: 0.1
  m_TransitionOffset: 0
  m_ExitTime: 0
  m_HasExitTime: 0
  m_HasFixedDuration: 1
  m_InterruptionSource: 0
  m_OrderedInterruption: 1
  m_CanTransitionToSelf: 1
--- !u!1102 &-3772444578010579154
AnimatorState:
  serializedVersion: 5
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: SpecialDanceMove
  m_Speed: 1.5
  m_CycleOffset: 0
  m_Transitions:
  - {fileID: 3597076210737789128}
  m_StateMachineBehaviours: []
  m_Position: {x: 50, y: 50, z: 0}
  m_IKOnFeet: 0
  m_WriteDefaultValues: 1
  m_Mirror: 0
  m_SpeedParameterActive: 0
  m_MirrorParameterActive: 0
  m_CycleOffsetParameterActive: 0
  m_TimeParameterActive: 0
  m_Motion: {fileID: -4791504522942941401, guid: d9dd7ddf844bda743a03b85e7550a9d8,
    type: 3}
  m_Tag: 
  m_SpeedParameter: 
  m_MirrorParameter: 
  m_CycleOffsetParameter: 
  m_TimeParameter: 
--- !u!1101 &-3591147841593793717
AnimatorStateTransition:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  m_Conditions:
  - m_ConditionMode: 2
    m_ConditionEvent: Walking
    m_EventTreshold: 0
  - m_ConditionMode: 2
    m_ConditionEvent: Running
    m_EventTreshold: 0
  - m_ConditionMode: 2
    m_ConditionEvent: Crawling
    m_EventTreshold: 0
  m_DstStateMachine: {fileID: 0}
  m_DstState: {fileID: -446130921896828856}
  m_Solo: 0
  m_Mute: 0
  m_IsExit: 0
  serializedVersion: 3
  m_TransitionDuration: 0.1
  m_TransitionOffset: 0
  m_ExitTime: 0
  m_HasExitTime: 0
  m_HasFixedDuration: 1
  m_InterruptionSource: 0
  m_OrderedInterruption: 1
  m_CanTransitionToSelf: 1
--- !u!1102 &-2348609720350248392
AnimatorState:
  serializedVersion: 5
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Dancing
  m_Speed: 1
  m_CycleOffset: 0
  m_Transitions: []
  m_StateMachineBehaviours: []
  m_Position: {x: 50, y: 50, z: 0}
  m_IKOnFeet: 0
  m_WriteDefaultValues: 1
  m_Mirror: 0
  m_SpeedParameterActive: 0
  m_MirrorParameterActive: 0
  m_CycleOffsetParameterActive: 0
  m_TimeParameterActive: 0
  m_Motion: {fileID: -203655887218126122, guid: b21300540b7458749bf916d12e31fd4b,
    type: 3}
  m_Tag: 
  m_SpeedParameter: 
  m_MirrorParameter: 
  m_CycleOffsetParameter: 
  m_TimeParameter: 
--- !u!1101 &-2005053873667759480
AnimatorStateTransition:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  m_Conditions:
  - m_ConditionMode: 2
    m_ConditionEvent: Crouching
    m_EventTreshold: 0
  - m_ConditionMode: 1
    m_ConditionEvent: Idle
    m_EventTreshold: 0
  m_DstStateMachine: {fileID: 0}
  m_DstState: {fileID: -446130921896828856}
  m_Solo: 0
  m_Mute: 0
  m_IsExit: 0
  serializedVersion: 3
  m_TransitionDuration: 0.1
  m_TransitionOffset: 0
  m_ExitTime: 0
  m_HasExitTime: 0
  m_HasFixedDuration: 1
  m_InterruptionSource: 0
  m_OrderedInterruption: 1
  m_CanTransitionToSelf: 1
--- !u!1102 &-446130921896828856
AnimatorState:
  serializedVersion: 5
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: T-Pose@Rifle Aiming Idle
  m_Speed: 1
  m_CycleOffset: 0
  m_Transitions:
  - {fileID: 7908471762261929911}
  - {fileID: -4198847755270212729}
  - {fileID: 3309000310792004042}
  m_StateMachineBehaviours: []
  m_Position: {x: 50, y: 50, z: 0}
  m_IKOnFeet: 0
  m_WriteDefaultValues: 1
  m_Mirror: 0
  m_SpeedParameterActive: 0
  m_MirrorParameterActive: 0
  m_CycleOffsetParameterActive: 0
  m_TimeParameterActive: 0
  m_Motion: {fileID: -203655887218126122, guid: ae6eb209df497b846a8d698d0d2c07c2,
    type: 3}
  m_Tag: 
  m_SpeedParameter: 
  m_MirrorParameter: 
  m_CycleOffsetParameter: 
  m_TimeParameter: 
--- !u!91 &9100000
AnimatorController:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: FeelBrassTacticalAnimator
  serializedVersion: 5
  m_AnimatorParameters:
  - m_Name: SpecialDanceMove
    m_Type: 9
    m_DefaultFloat: 0
    m_DefaultInt: 0
    m_DefaultBool: 0
    m_Controller: {fileID: 0}
  m_AnimatorLayers:
  - serializedVersion: 5
    m_Name: Base Layer
    m_StateMachine: {fileID: 5661380733464169219}
    m_Mask: {fileID: 0}
    m_Motions: []
    m_Behaviours: []
    m_BlendingMode: 0
    m_SyncedLayerIndex: -1
    m_DefaultWeight: 0
    m_IKPass: 1
    m_SyncedLayerAffectsTiming: 0
    m_Controller: {fileID: 9100000}
--- !u!1101 &763260403751245823
AnimatorStateTransition:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  m_Conditions:
  - m_ConditionMode: 1
    m_ConditionEvent: Crawling
    m_EventTreshold: 0
  m_DstStateMachine: {fileID: 0}
  m_DstState: {fileID: 4018941785848006698}
  m_Solo: 0
  m_Mute: 0
  m_IsExit: 0
  serializedVersion: 3
  m_TransitionDuration: 0.1
  m_TransitionOffset: 0
  m_ExitTime: 0
  m_HasExitTime: 0
  m_HasFixedDuration: 1
  m_InterruptionSource: 0
  m_OrderedInterruption: 1
  m_CanTransitionToSelf: 1
--- !u!1101 &1296975666757328168
AnimatorStateTransition:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  m_Conditions:
  - m_ConditionMode: 2
    m_ConditionEvent: Running
    m_EventTreshold: 0
  m_DstStateMachine: {fileID: 0}
  m_DstState: {fileID: 2175879143978310236}
  m_Solo: 0
  m_Mute: 0
  m_IsExit: 0
  serializedVersion: 3
  m_TransitionDuration: 0.2
  m_TransitionOffset: 0
  m_ExitTime: 0
  m_HasExitTime: 0
  m_HasFixedDuration: 1
  m_InterruptionSource: 0
  m_OrderedInterruption: 1
  m_CanTransitionToSelf: 1
--- !u!1102 &2175879143978310236
AnimatorState:
  serializedVersion: 5
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Walk
  m_Speed: 2.5
  m_CycleOffset: 0
  m_Transitions:
  - {fileID: 4823733338993744450}
  - {fileID: 763260403751245823}
  - {fileID: 2706039316299719163}
  m_StateMachineBehaviours: []
  m_Position: {x: 50, y: 50, z: 0}
  m_IKOnFeet: 0
  m_WriteDefaultValues: 1
  m_Mirror: 0
  m_SpeedParameterActive: 1
  m_MirrorParameterActive: 0
  m_CycleOffsetParameterActive: 0
  m_TimeParameterActive: 0
  m_Motion: {fileID: -4545143397978411216}
  m_Tag: 
  m_SpeedParameter: RemappedSpeedNormalized
  m_MirrorParameter: 
  m_CycleOffsetParameter: 
  m_TimeParameter: 
--- !u!1101 &2267410970865588648
AnimatorStateTransition:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  m_Conditions:
  - m_ConditionMode: 1
    m_ConditionEvent: Crawling
    m_EventTreshold: 0
  m_DstStateMachine: {fileID: 0}
  m_DstState: {fileID: 4018941785848006698}
  m_Solo: 0
  m_Mute: 0
  m_IsExit: 0
  serializedVersion: 3
  m_TransitionDuration: 0.1
  m_TransitionOffset: 0
  m_ExitTime: 0
  m_HasExitTime: 0
  m_HasFixedDuration: 1
  m_InterruptionSource: 0
  m_OrderedInterruption: 1
  m_CanTransitionToSelf: 1
--- !u!1101 &2344911264550650765
AnimatorStateTransition:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  m_Conditions:
  - m_ConditionMode: 2
    m_ConditionEvent: Crawling
    m_EventTreshold: 0
  m_DstStateMachine: {fileID: 0}
  m_DstState: {fileID: -446130921896828856}
  m_Solo: 0
  m_Mute: 0
  m_IsExit: 0
  serializedVersion: 3
  m_TransitionDuration: 0.1
  m_TransitionOffset: 0
  m_ExitTime: 0
  m_HasExitTime: 0
  m_HasFixedDuration: 1
  m_InterruptionSource: 0
  m_OrderedInterruption: 1
  m_CanTransitionToSelf: 1
--- !u!1101 &2706039316299719163
AnimatorStateTransition:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  m_Conditions:
  - m_ConditionMode: 1
    m_ConditionEvent: Running
    m_EventTreshold: 0
  - m_ConditionMode: 3
    m_ConditionEvent: Speed
    m_EventTreshold: 3
  m_DstStateMachine: {fileID: 0}
  m_DstState: {fileID: 5121355656053269990}
  m_Solo: 0
  m_Mute: 0
  m_IsExit: 0
  serializedVersion: 3
  m_TransitionDuration: 0.2
  m_TransitionOffset: 0
  m_ExitTime: 0
  m_HasExitTime: 0
  m_HasFixedDuration: 1
  m_InterruptionSource: 0
  m_OrderedInterruption: 1
  m_CanTransitionToSelf: 1
--- !u!1101 &3309000310792004042
AnimatorStateTransition:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  m_Conditions:
  - m_ConditionMode: 1
    m_ConditionEvent: Crawling
    m_EventTreshold: 0
  m_DstStateMachine: {fileID: 0}
  m_DstState: {fileID: 4018941785848006698}
  m_Solo: 0
  m_Mute: 0
  m_IsExit: 0
  serializedVersion: 3
  m_TransitionDuration: 0.1
  m_TransitionOffset: 0
  m_ExitTime: 0
  m_HasExitTime: 0
  m_HasFixedDuration: 1
  m_InterruptionSource: 0
  m_OrderedInterruption: 1
  m_CanTransitionToSelf: 1
--- !u!1101 &3597076210737789128
AnimatorStateTransition:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  m_Conditions: []
  m_DstStateMachine: {fileID: 0}
  m_DstState: {fileID: -2348609720350248392}
  m_Solo: 0
  m_Mute: 0
  m_IsExit: 0
  serializedVersion: 3
  m_TransitionDuration: 0.2
  m_TransitionOffset: 0
  m_ExitTime: 0.8
  m_HasExitTime: 1
  m_HasFixedDuration: 1
  m_InterruptionSource: 0
  m_OrderedInterruption: 1
  m_CanTransitionToSelf: 1
--- !u!1102 &4018941785848006698
AnimatorState:
  serializedVersion: 5
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Crawl
  m_Speed: 1
  m_CycleOffset: 0
  m_Transitions:
  - {fileID: 7839999874738908634}
  - {fileID: 2344911264550650765}
  m_StateMachineBehaviours: []
  m_Position: {x: 50, y: 50, z: 0}
  m_IKOnFeet: 0
  m_WriteDefaultValues: 1
  m_Mirror: 0
  m_SpeedParameterActive: 0
  m_MirrorParameterActive: 0
  m_CycleOffsetParameterActive: 0
  m_TimeParameterActive: 0
  m_Motion: {fileID: 5128702909174175801}
  m_Tag: 
  m_SpeedParameter: 
  m_MirrorParameter: 
  m_CycleOffsetParameter: 
  m_TimeParameter: 
--- !u!1101 &4552654992489527290
AnimatorStateTransition:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  m_Conditions:
  - m_ConditionMode: 1
    m_ConditionEvent: SpecialDanceMove
    m_EventTreshold: 0
  m_DstStateMachine: {fileID: 0}
  m_DstState: {fileID: -3772444578010579154}
  m_Solo: 0
  m_Mute: 0
  m_IsExit: 0
  serializedVersion: 3
  m_TransitionDuration: 0.3
  m_TransitionOffset: 0
  m_ExitTime: 0.75
  m_HasExitTime: 0
  m_HasFixedDuration: 1
  m_InterruptionSource: 0
  m_OrderedInterruption: 1
  m_CanTransitionToSelf: 1
--- !u!206 &4765653412708922389
BlendTree:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Blend Tree
  m_Childs:
  - serializedVersion: 2
    m_Motion: {fileID: -203655887218126122, guid: 77573758953579f41814389478000149,
      type: 3}
    m_Threshold: 0
    m_Position: {x: 0, y: 1}
    m_TimeScale: 1
    m_CycleOffset: 0
    m_DirectBlendParameter: Random
    m_Mirror: 0
  - serializedVersion: 2
    m_Motion: {fileID: -203655887218126122, guid: fd7028aeeb58c224da0ad6ec72697de7,
      type: 3}
    m_Threshold: 0.14285715
    m_Position: {x: -1, y: 0}
    m_TimeScale: 1
    m_CycleOffset: 0
    m_DirectBlendParameter: Random
    m_Mirror: 0
  - serializedVersion: 2
    m_Motion: {fileID: -203655887218126122, guid: 16635f2efef7a774686586be68c0339f,
      type: 3}
    m_Threshold: 0.2857143
    m_Position: {x: 1, y: 0}
    m_TimeScale: 1
    m_CycleOffset: 0
    m_DirectBlendParameter: Random
    m_Mirror: 0
  - serializedVersion: 2
    m_Motion: {fileID: -203655887218126122, guid: 36b84d6f331bbae4095a5e37453bf57b,
      type: 3}
    m_Threshold: 0.42857143
    m_Position: {x: -0.5, y: 0.5}
    m_TimeScale: 1
    m_CycleOffset: 0
    m_DirectBlendParameter: Random
    m_Mirror: 0
  - serializedVersion: 2
    m_Motion: {fileID: -203655887218126122, guid: 42637ecb7d2e22e4f8b98388a3604069,
      type: 3}
    m_Threshold: 0.5714286
    m_Position: {x: 0.5, y: 0.5}
    m_TimeScale: 1
    m_CycleOffset: 0
    m_DirectBlendParameter: Random
    m_Mirror: 0
  - serializedVersion: 2
    m_Motion: {fileID: -203655887218126122, guid: 10fd4749bd313af4da73f8a491e76a98,
      type: 3}
    m_Threshold: 0.71428573
    m_Position: {x: -0.5, y: -0.5}
    m_TimeScale: 1
    m_CycleOffset: 0
    m_DirectBlendParameter: Random
    m_Mirror: 0
  - serializedVersion: 2
    m_Motion: {fileID: -203655887218126122, guid: 4d8c2a981ecf7ce4aa85cd739960bd38,
      type: 3}
    m_Threshold: 0.85714287
    m_Position: {x: 0.5, y: -0.5}
    m_TimeScale: 1
    m_CycleOffset: 0
    m_DirectBlendParameter: Random
    m_Mirror: 0
  - serializedVersion: 2
    m_Motion: {fileID: -203655887218126122, guid: 0ad4aa4288b106f428baef9fc2d561fb,
      type: 3}
    m_Threshold: 1
    m_Position: {x: 0, y: -1}
    m_TimeScale: 1
    m_CycleOffset: 0
    m_DirectBlendParameter: Random
    m_Mirror: 0
  m_BlendParameter: RemappedLateralSpeedNormalized
  m_BlendParameterY: RemappedForwardSpeedNormalized
  m_MinThreshold: 0
  m_MaxThreshold: 1
  m_UseAutomaticThresholds: 1
  m_NormalizedBlendValues: 0
  m_BlendType: 3
--- !u!1101 &4823733338993744450
AnimatorStateTransition:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  m_Conditions:
  - m_ConditionMode: 2
    m_ConditionEvent: Walking
    m_EventTreshold: 0
  - m_ConditionMode: 2
    m_ConditionEvent: Running
    m_EventTreshold: 0
  - m_ConditionMode: 2
    m_ConditionEvent: Crawling
    m_EventTreshold: 0
  m_DstStateMachine: {fileID: 0}
  m_DstState: {fileID: -446130921896828856}
  m_Solo: 0
  m_Mute: 0
  m_IsExit: 0
  serializedVersion: 3
  m_TransitionDuration: 0.1
  m_TransitionOffset: 0
  m_ExitTime: 0
  m_HasExitTime: 0
  m_HasFixedDuration: 1
  m_InterruptionSource: 0
  m_OrderedInterruption: 1
  m_CanTransitionToSelf: 1
--- !u!1102 &5121355656053269990
AnimatorState:
  serializedVersion: 5
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Run
  m_Speed: 1
  m_CycleOffset: 0
  m_Transitions:
  - {fileID: 1296975666757328168}
  - {fileID: 9103883218025818653}
  m_StateMachineBehaviours: []
  m_Position: {x: 50, y: 50, z: 0}
  m_IKOnFeet: 0
  m_WriteDefaultValues: 1
  m_Mirror: 0
  m_SpeedParameterActive: 0
  m_MirrorParameterActive: 0
  m_CycleOffsetParameterActive: 0
  m_TimeParameterActive: 0
  m_Motion: {fileID: 4765653412708922389}
  m_Tag: 
  m_SpeedParameter: 
  m_MirrorParameter: 
  m_CycleOffsetParameter: 
  m_TimeParameter: 
--- !u!206 &5128702909174175801
BlendTree:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Blend Tree
  m_Childs:
  - serializedVersion: 2
    m_Motion: {fileID: -203655887218126122, guid: 8d9ce1fb4e81a9c45b6eff4ec0a975e2,
      type: 3}
    m_Threshold: 0
    m_Position: {x: 0, y: 1}
    m_TimeScale: 1
    m_CycleOffset: 0
    m_DirectBlendParameter: Random
    m_Mirror: 0
  - serializedVersion: 2
    m_Motion: {fileID: -203655887218126122, guid: 7cc26ec8e1f6cde43addc1a5004a27e7,
      type: 3}
    m_Threshold: 0.14285715
    m_Position: {x: -1, y: 0}
    m_TimeScale: 1
    m_CycleOffset: 0
    m_DirectBlendParameter: Random
    m_Mirror: 0
  - serializedVersion: 2
    m_Motion: {fileID: -203655887218126122, guid: ddfcfae442ceafb4aaeab25deb34a4bb,
      type: 3}
    m_Threshold: 0.2857143
    m_Position: {x: 1, y: 0}
    m_TimeScale: 1
    m_CycleOffset: 0
    m_DirectBlendParameter: Random
    m_Mirror: 0
  - serializedVersion: 2
    m_Motion: {fileID: -203655887218126122, guid: 0d724d1a734e56940a7e95a8dddf5b2f,
      type: 3}
    m_Threshold: 0.42857143
    m_Position: {x: -0.5, y: 0.5}
    m_TimeScale: 1
    m_CycleOffset: 0
    m_DirectBlendParameter: Random
    m_Mirror: 0
  - serializedVersion: 2
    m_Motion: {fileID: -203655887218126122, guid: c956bc82a5467c0408853fe4edf37abe,
      type: 3}
    m_Threshold: 0.5714286
    m_Position: {x: 0.5, y: 0.5}
    m_TimeScale: 1
    m_CycleOffset: 0
    m_DirectBlendParameter: Random
    m_Mirror: 0
  - serializedVersion: 2
    m_Motion: {fileID: -203655887218126122, guid: cf2081f29f246a54792378f56a428241,
      type: 3}
    m_Threshold: 0.71428573
    m_Position: {x: -0.5, y: -0.5}
    m_TimeScale: 1
    m_CycleOffset: 0
    m_DirectBlendParameter: Random
    m_Mirror: 0
  - serializedVersion: 2
    m_Motion: {fileID: -203655887218126122, guid: 55dc443b4d717ca4694e8d61054e14e1,
      type: 3}
    m_Threshold: 0.85714287
    m_Position: {x: 0.5, y: -0.5}
    m_TimeScale: 1
    m_CycleOffset: 0
    m_DirectBlendParameter: Random
    m_Mirror: 0
  - serializedVersion: 2
    m_Motion: {fileID: -203655887218126122, guid: 2f0bed83e47840940ab2a0e7c970f632,
      type: 3}
    m_Threshold: 1
    m_Position: {x: 0, y: -1}
    m_TimeScale: 1
    m_CycleOffset: 0
    m_DirectBlendParameter: Random
    m_Mirror: 0
  m_BlendParameter: RelativeLateralSpeedNormalized
  m_BlendParameterY: RelativeForwardSpeedNormalized
  m_MinThreshold: 0
  m_MaxThreshold: 1
  m_UseAutomaticThresholds: 1
  m_NormalizedBlendValues: 0
  m_BlendType: 2
--- !u!1107 &5661380733464169219
AnimatorStateMachine:
  serializedVersion: 5
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Base Layer
  m_ChildStates:
  - serializedVersion: 1
    m_State: {fileID: -2348609720350248392}
    m_Position: {x: 660, y: -90, z: 0}
  - serializedVersion: 1
    m_State: {fileID: -3772444578010579154}
    m_Position: {x: 660, y: 20, z: 0}
  m_ChildStateMachines: []
  m_AnyStateTransitions:
  - {fileID: 4552654992489527290}
  m_EntryTransitions: []
  m_StateMachineTransitions: {}
  m_StateMachineBehaviours: []
  m_AnyStatePosition: {x: 360, y: 20, z: 0}
  m_EntryPosition: {x: 400, y: -100, z: 0}
  m_ExitPosition: {x: 940, y: 170, z: 0}
  m_ParentStateMachinePosition: {x: 800, y: 20, z: 0}
  m_DefaultState: {fileID: -2348609720350248392}
--- !u!1102 &7482645143960757924
AnimatorState:
  serializedVersion: 5
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: T-Pose@Crouch Idle
  m_Speed: 1
  m_CycleOffset: 0
  m_Transitions:
  - {fileID: -2005053873667759480}
  - {fileID: 8886525869639430356}
  m_StateMachineBehaviours: []
  m_Position: {x: 50, y: 50, z: 0}
  m_IKOnFeet: 0
  m_WriteDefaultValues: 1
  m_Mirror: 0
  m_SpeedParameterActive: 0
  m_MirrorParameterActive: 0
  m_CycleOffsetParameterActive: 0
  m_TimeParameterActive: 0
  m_Motion: {fileID: -203655887218126122, guid: 6812966174de35b419942d48a639808f,
    type: 3}
  m_Tag: 
  m_SpeedParameter: 
  m_MirrorParameter: 
  m_CycleOffsetParameter: 
  m_TimeParameter: 
--- !u!1101 &7839999874738908634
AnimatorStateTransition:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  m_Conditions:
  - m_ConditionMode: 1
    m_ConditionEvent: Crouching
    m_EventTreshold: 0
  m_DstStateMachine: {fileID: 0}
  m_DstState: {fileID: 7482645143960757924}
  m_Solo: 0
  m_Mute: 0
  m_IsExit: 0
  serializedVersion: 3
  m_TransitionDuration: 0.1
  m_TransitionOffset: 0
  m_ExitTime: 0
  m_HasExitTime: 0
  m_HasFixedDuration: 1
  m_InterruptionSource: 0
  m_OrderedInterruption: 1
  m_CanTransitionToSelf: 1
--- !u!1101 &7908471762261929911
AnimatorStateTransition:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  m_Conditions:
  - m_ConditionMode: 2
    m_ConditionEvent: Crouching
    m_EventTreshold: 0
  - m_ConditionMode: 2
    m_ConditionEvent: Crawling
    m_EventTreshold: 0
  - m_ConditionMode: 3
    m_ConditionEvent: Speed
    m_EventTreshold: 0.05
  m_DstStateMachine: {fileID: 0}
  m_DstState: {fileID: 2175879143978310236}
  m_Solo: 0
  m_Mute: 0
  m_IsExit: 0
  serializedVersion: 3
  m_TransitionDuration: 0.1
  m_TransitionOffset: 0
  m_ExitTime: 0
  m_HasExitTime: 0
  m_HasFixedDuration: 1
  m_InterruptionSource: 0
  m_OrderedInterruption: 1
  m_CanTransitionToSelf: 1
--- !u!1101 &8886525869639430356
AnimatorStateTransition:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  m_Conditions:
  - m_ConditionMode: 1
    m_ConditionEvent: Crawling
    m_EventTreshold: 0
  m_DstStateMachine: {fileID: 0}
  m_DstState: {fileID: 4018941785848006698}
  m_Solo: 0
  m_Mute: 0
  m_IsExit: 0
  serializedVersion: 3
  m_TransitionDuration: 0.1
  m_TransitionOffset: 0
  m_ExitTime: 0
  m_HasExitTime: 0
  m_HasFixedDuration: 1
  m_InterruptionSource: 0
  m_OrderedInterruption: 1
  m_CanTransitionToSelf: 1
--- !u!1101 &9103883218025818653
AnimatorStateTransition:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 
  m_Conditions:
  - m_ConditionMode: 4
    m_ConditionEvent: Speed
    m_EventTreshold: 3
  m_DstStateMachine: {fileID: 0}
  m_DstState: {fileID: 2175879143978310236}
  m_Solo: 0
  m_Mute: 0
  m_IsExit: 0
  serializedVersion: 3
  m_TransitionDuration: 0.2
  m_TransitionOffset: 0
  m_ExitTime: 0
  m_HasExitTime: 0
  m_HasFixedDuration: 1
  m_InterruptionSource: 0
  m_OrderedInterruption: 1
  m_CanTransitionToSelf: 1
