Shader "Custom/TyRetroChromeSimplified"
{
  Properties
  {
    [Header(Chrome Colors)]
    [HDR]_Color1 ("Primary Color", Color) = (0.0, 0.5, 2.0, 1)
    [HDR]_Color2 ("Secondary Color", Color) = (2.0, 0.3, 1.5, 1)
    [HDR]_Color3 ("Accent Color", Color) = (2.0, 0.1, 0.5, 1)
    _DarkColor ("Dark Color", Color) = (0.02, 0.0, 0.08, 1)
    
    [Header(Surface Properties)]
    _Smoothness ("Smoothness", Range(0, 1)) = 0.95
    _Metallic ("Metallic", Range(0, 1)) = 1.0
    
    [Header(Effect Controls)]
    _ReflectionStrength ("Reflection Strength", Range(1, 10)) = 5.0
    [PowerSlider(4)]_FresnelPower ("Fresnel Power", Range(0.1, 20)) = 5
    _FresnelIntensity ("Fresnel Intensity", Range(0.1, 5)) = 1.0
    [PowerSlider(2)]_Threshold ("Color Threshold", Range(0, 0.3)) = 0.05
    _ColorBlend ("Color Blend Sharpness", Range(0.01, 5)) = 1.0
    _ColorShift ("Color Shift Speed", Range(0, 2)) = 0.5
    _Contrast ("Contrast", Range(1, 10)) = 4
  }

  SubShader
  {
    Tags 
    { 
      "RenderType" = "Opaque"
      "RenderPipeline" = "UniversalPipeline"
      "Queue" = "Geometry"
      "UniversalMaterialType" = "Lit"
    }

    HLSLINCLUDE
    #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
    #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Lighting.hlsl"

    CBUFFER_START(UnityPerMaterial)
      float4 _Color1;
      float4 _Color2;
      float4 _Color3;
      float4 _DarkColor;
      float _Smoothness;
      float _Metallic;
      float _ReflectionStrength;
      float _FresnelPower;
      float _FresnelIntensity;
      float _ColorBlend;
      float _ColorShift;
      float _Threshold;
      float _Contrast;
    CBUFFER_END

    float CalculateFresnel(float3 normalWS, float3 viewDirWS)
    {
      float NdotV = 1.0 - saturate(dot(normalWS, viewDirWS));
      return pow(NdotV, _FresnelPower) * _FresnelIntensity;
    }

    float3 ApplyChrome(float3 baseColor, float3 normalWS, float3 viewDirWS, float fresnel)
    {
      // Calculate view-dependent factors for stronger color variation
      float NdotV = saturate(dot(normalWS, viewDirWS));
      float viewFactor = pow(1 - NdotV, max(1, _ColorBlend * 2));
      
      // Enhanced normal-based variation
      float3 worldUp = float3(0, 1, 0);
      float3 worldRight = float3(1, 0, 0);
      float upDot = dot(normalWS, worldUp);
      float rightDot = dot(normalWS, worldRight);
      float normalFactor = (upDot * rightDot + 0.5) * _ColorBlend;
      
      // Enhanced time-based color shift
      float timeShift = _Time.y * _ColorShift * 2.0;
      
      // Create more dramatic color transitions
      float3 color1 = lerp(_DarkColor.rgb * _Color1.rgb, _Color1.rgb, fresnel * 0.5);
      float3 color2 = lerp(_DarkColor.rgb * _Color2.rgb * 2, _Color2.rgb * 1.5, fresnel * 0.7);
      float3 color3 = lerp(_DarkColor.rgb * _Color3.rgb, _Color3.rgb, fresnel * 0.5);
      
      // Combine factors for color selection
      float blendFactor = viewFactor + normalFactor + timeShift;
      
      // Create overlapping color transitions
      float t = frac(blendFactor);
      float t1 = smoothstep(0.0, 0.5, t) * (1 - smoothstep(0.3, 0.8, t));
      float t2 = smoothstep(0.2, 0.7, t) * (1 - smoothstep(0.5, 1.0, t));
      float t3 = smoothstep(0.4, 0.9, t) * (1 - smoothstep(0.7, 1.2, t));
      
      // Blend colors with overlapping
      float3 finalColor = color1 * t1 + color2 * t2 + color3 * t3;
      
      // Ensure dark color is always present
      float colorStrength = max(max(t1, t2), t3);
      finalColor = lerp(_DarkColor.rgb, finalColor, colorStrength);
      
      // Apply fresnel brightening with dampening
      float fresnelDampen = lerp(0.3, 0.7, _FresnelIntensity); // Reduce maximum fresnel brightness
      finalColor = lerp(finalColor, finalColor * (1 + fresnel * fresnelDampen), _FresnelIntensity);
      
      // Metallic affects color saturation and reflection balance
      float3 metallicColor = lerp(finalColor, baseColor * finalColor, _Metallic * 0.5);
      
      // Apply reflection influence with metallic and fresnel
      float reflectionLuminance = dot(baseColor * _ReflectionStrength * (1 + _Metallic + fresnel), float3(0.299, 0.587, 0.114));
      float threshold = smoothstep(_Threshold - 0.2, _Threshold + 0.2, reflectionLuminance);
      
      // Apply contrast and reflection with metallic and smoothness influence
      float3 contrastedColor = pow(abs(baseColor), _Contrast) * _ReflectionStrength * (1 + _Metallic);
      float smoothnessInfluence = lerp(0.5, 1.2, _Smoothness); // Reduced maximum smoothness boost
      metallicColor = lerp(metallicColor, metallicColor * contrastedColor * smoothnessInfluence, _ReflectionStrength * 0.2 * _Metallic);
      
      // Ensure dark color influence in shadows
      float3 darkInfluence = lerp(_DarkColor.rgb * 0.5, metallicColor, threshold);
      return darkInfluence;
    }
    ENDHLSL

    Pass
    {
      Name "ForwardLit"
      Tags { "LightMode" = "UniversalForward" }

      HLSLPROGRAM
      #pragma vertex vert
      #pragma fragment frag
      
      #pragma multi_compile _ _MAIN_LIGHT_SHADOWS
      #pragma multi_compile _ _MAIN_LIGHT_SHADOWS_CASCADE
      #pragma multi_compile_fragment _ _REFLECTION_PROBE_BLENDING
      #pragma multi_compile_fragment _ _REFLECTION_PROBE_BOX_PROJECTION
      #pragma multi_compile_fragment _ _SHADOWS_SOFT
      #pragma multi_compile_instancing
      
      #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
      
      struct Attributes
      {
        float4 positionOS : POSITION;
        float3 normalOS : NORMAL;
        UNITY_VERTEX_INPUT_INSTANCE_ID
      };

      struct Varyings
      {
        float4 positionCS : SV_POSITION;
        float3 positionWS : TEXCOORD0;
        float3 normalWS : TEXCOORD1;
        float3 viewDirWS : TEXCOORD2;
        UNITY_VERTEX_INPUT_INSTANCE_ID
        UNITY_VERTEX_OUTPUT_STEREO
      };

      Varyings vert(Attributes input)
      {
        Varyings output = (Varyings)0;
        UNITY_SETUP_INSTANCE_ID(input);
        UNITY_TRANSFER_INSTANCE_ID(input, output);
        UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(output);
        
        VertexPositionInputs vertexInput = GetVertexPositionInputs(input.positionOS.xyz);
        VertexNormalInputs normalInput = GetVertexNormalInputs(input.normalOS);
        
        output.positionCS = vertexInput.positionCS;
        output.positionWS = vertexInput.positionWS;
        output.normalWS = normalInput.normalWS;
        output.viewDirWS = GetWorldSpaceViewDir(vertexInput.positionWS);
        
        return output;
      }

      float4 frag(Varyings input) : SV_Target
      {
        // Setup instancing
        UNITY_SETUP_INSTANCE_ID(input);
        UNITY_SETUP_STEREO_EYE_INDEX_POST_VERTEX(input);
        
        float3 normalWS = normalize(input.normalWS);
        float3 viewDirWS = normalize(input.viewDirWS);
        
        // Calculate reflection with dampened strength
        float3 reflectVector = reflect(-viewDirWS, normalWS);
        float3 reflection = GlossyEnvironmentReflection(reflectVector, 0, _Smoothness);
        float smoothnessBoost = lerp(0.5, 1.5, _Smoothness); // Reduced maximum reflection boost
        reflection *= _ReflectionStrength * _ReflectionStrength * (1 + _Metallic * 0.7) * smoothnessBoost;
        
        // Calculate fresnel
        float fresnel = CalculateFresnel(normalWS, viewDirWS);
        
        // Apply chrome effect with multiple colors
        float3 chromeColor = ApplyChrome(reflection, normalWS, viewDirWS, fresnel);
        
        // Add specular highlight with tinted and dampened reflection
        float3 lightDir = normalize(_MainLightPosition.xyz);
        float3 halfDir = normalize(lightDir + viewDirWS);
        float specPower = lerp(8, 128, _Smoothness); // Reduced maximum specular power
        float specular = pow(saturate(dot(normalWS, halfDir)), specPower);
        
        // Calculate specular color based on the current chrome color
        float3 specColor = chromeColor * 0.5; // Tint specular with current color
        specColor = lerp(specColor * 0.3, specColor, _Metallic);
        float specularIntensity = lerp(0.3, 1.0, _Smoothness); // Reduced specular intensity range
        
        // Add dampened specular
        chromeColor += specular * specColor * _ReflectionStrength * (0.5 + _Metallic * 0.3) * specularIntensity;
        
        // Clamp maximum brightness
        float maxBrightness = 2.0;
        chromeColor = min(chromeColor, maxBrightness);
        
        return float4(chromeColor, 1);
      }
      ENDHLSL
    }
    
    Pass
    {
      Name "ShadowCaster"
      Tags{"LightMode" = "ShadowCaster"}

      ZWrite On
      ZTest LEqual
      ColorMask 0
      Cull Back

      HLSLPROGRAM
      #pragma vertex ShadowPassVertex
      #pragma fragment ShadowPassFragment
      #pragma multi_compile_instancing
      
      #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
      #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Lighting.hlsl"
      
      float3 _LightDirection;
      
      struct Attributes
      {
        float4 positionOS : POSITION;
        float3 normalOS : NORMAL;
        UNITY_VERTEX_INPUT_INSTANCE_ID
      };
      
      struct Varyings
      {
        float4 positionCS : SV_POSITION;
        UNITY_VERTEX_INPUT_INSTANCE_ID
        UNITY_VERTEX_OUTPUT_STEREO
      };
      
      float4 GetShadowPositionHClip(Attributes input)
      {
        float3 positionWS = TransformObjectToWorld(input.positionOS.xyz);
        float3 normalWS = TransformObjectToWorldNormal(input.normalOS);
        float4 positionCS = TransformWorldToHClip(ApplyShadowBias(positionWS, normalWS, _LightDirection));
        
        #if UNITY_REVERSED_Z
          positionCS.z = min(positionCS.z, UNITY_NEAR_CLIP_VALUE);
        #else
          positionCS.z = max(positionCS.z, UNITY_NEAR_CLIP_VALUE);
        #endif
        
        return positionCS;
      }
      
      Varyings ShadowPassVertex(Attributes input)
      {
        Varyings output;
        UNITY_SETUP_INSTANCE_ID(input);
        UNITY_TRANSFER_INSTANCE_ID(input, output);
        UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(output);
        output.positionCS = GetShadowPositionHClip(input);
        return output;
      }
      
      half4 ShadowPassFragment(Varyings input) : SV_TARGET
      {
        return 0;
      }
      ENDHLSL
    }

    Pass
    {
      Name "DepthOnly"
      Tags{"LightMode" = "DepthOnly"}

      ZWrite On
      ColorMask 0
      Cull Back

      HLSLPROGRAM
      #pragma vertex DepthOnlyVertex
      #pragma fragment DepthOnlyFragment
      #pragma multi_compile_instancing
      
      #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"

      struct Attributes
      {
        float4 position : POSITION;
        UNITY_VERTEX_INPUT_INSTANCE_ID
      };

      struct Varyings
      {
        float4 positionCS : SV_POSITION;
        UNITY_VERTEX_INPUT_INSTANCE_ID
        UNITY_VERTEX_OUTPUT_STEREO
      };

      Varyings DepthOnlyVertex(Attributes input)
      {
        Varyings output = (Varyings)0;
        UNITY_SETUP_INSTANCE_ID(input);
        UNITY_TRANSFER_INSTANCE_ID(input, output);
        UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(output);
        output.positionCS = TransformObjectToHClip(input.position.xyz);
        return output;
      }

      half4 DepthOnlyFragment(Varyings input) : SV_TARGET
      {
        return 0;
      }
      ENDHLSL
    }
  }
  FallBack "Hidden/Universal Render Pipeline/FallbackError"
} 