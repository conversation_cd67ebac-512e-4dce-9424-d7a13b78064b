%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &1060871491264068
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4500688365064020}
  - component: {fileID: 33794166296483756}
  - component: {fileID: 23125268577445020}
  - component: {fileID: 135280058657112932}
  - component: {fileID: 114360699673101352}
  m_Layer: 0
  m_Name: PR_TailAnimatorDemo_SphereMonster
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4500688365064020
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1060871491264068}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4814156698578472}
  - {fileID: 4265440575917502}
  - {fileID: 4808372848937036}
  - {fileID: 4316102901522140}
  - {fileID: 4408487124684998}
  - {fileID: 4827492547854588}
  - {fileID: 4489378341808832}
  - {fileID: 4373050835076546}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &33794166296483756
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1060871491264068}
  m_Mesh: {fileID: 10207, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &23125268577445020
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1060871491264068}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!135 &135280058657112932
SphereCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1060871491264068}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Radius: 0.5
  m_Center: {x: 0, y: 0, z: 0}
--- !u!114 &114360699673101352
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1060871491264068}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 43491a0ac6e9bb04cb722d4ed6d70d5e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  MainSpeed: 1
  RangeValue: {x: 1, y: 1, z: 1}
  RangeMul: 5
  AddYSin: 1
  AddYSinTimeSpeed: 1
  RotateForwardSpeed: 10
--- !u!1 &1098000185742978
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4827492547854588}
  - component: {fileID: 114605717504848982}
  m_Layer: 0
  m_Name: Tail 1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4827492547854588
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1098000185742978}
  serializedVersion: 2
  m_LocalRotation: {x: 0.6532824, y: 0.27059773, z: -0.65328056, w: 0.2705985}
  m_LocalPosition: {x: -0.2814285, y: -0.28142858, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4500688365064020}
  m_LocalEulerAnglesHint: {x: -90, y: 0, z: 0}
--- !u!114 &114605717504848982
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1098000185742978}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 61574e354ba34a141ae2cc1228f5eda3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  UseCollision: 0
  CollisionSpace: 1
  CollisionMode: 0
  CheapCollision: 0
  DynamicWorldCollidersInclusion: 0
  InclusionRadius: 1
  IgnoreMeshColliders: 1
  IncludedColliders: []
  IncludedColliders2D: []
  CollidersType: 0
  CollideWithOtherTails: 0
  CollideWithDisabledColliders: 1
  CollisionSlippery: 1
  ReflectCollision: 0
  CollidersScaleCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  CollidersScaleMul: 6.5
  CollisionsAutoCurve: 0.5
  IgnoredColliders: []
  IgnoredColliders2D: []
  CollidersSameLayer: 1
  CollidersAddRigidbody: 1
  RigidbodyMass: 1
  CollidersLayer: 0
  UseSlitheryCurve: 0
  SlitheryCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.75
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseCurlingCurve: 0
  CurlingCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.3
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseSpringCurve: 0
  SpringCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseSlipperyCurve: 0
  SlipperyCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UsePosSpeedCurve: 0
  PosCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseRotSpeedCurve: 0
  RotCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.9
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UsePartialBlend: 0
  BlendCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.95
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.45
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseIK: 0
  IK:
    IKWeight: 0
    IKTargetPosition: {x: 0, y: 0, z: 0}
    IKTargetRotation: {x: 0, y: 0, z: 0, w: 0}
    LastLocalDirection: {x: 0, y: 0, z: 0}
    LocalDirection: {x: 0, y: 0, z: 0}
    IKBones: []
    ContinousSolving: 0
    SyncWithAnimator: 0
    ReactionQuality: 0
    Smoothing: 0
    StretchToTarget: 0
    StretchCurve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    Use2D: 0
    Invert: 0
  IKTarget: {fileID: 0}
  IKAutoWeights: 1
  IKBaseReactionWeight: 0.65
  IKReactionWeightCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.25
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  IKAutoAngleLimits: 1
  IKAutoAngleLimit: 40
  IKContinousSolve: 0
  IkInvertOrder: 0
  IKBlend: 1
  IKAnimatorBlend: 0.85
  IKReactionQuality: 2
  IKSmoothing: 0.2
  IKStretchToTarget: 0
  IKStretchCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  IKLimitSettings: []
  IKSelectiveChain: 0
  RotationOffset: {x: 0, y: 0, z: 0, w: 1}
  Curving: {x: 0, y: 0, z: 0, w: 1}
  UseCurvingCurve: 0
  CurvCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.75
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  LengthMultiplier: 1
  UseLengthMulCurve: 0
  LengthMulCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseGravityCurve: 0
  GravityCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.65
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  Gravity: {x: 0, y: 0, z: 0}
  UseWaving: 1
  CosinusAdd: 1
  FixedCycle: 0
  WavingSpeed: 3.25
  WavingRange: 0.4
  WavingAxis: {x: 0, y: 1, z: 0}
  WavingType: 0
  AlternateWave: 1
  UseWind: 0
  WindEffectPower: 1
  WindTurbulencePower: 1
  WindWorldNoisePower: 0.5
  WindEffect: {x: 0, y: 0, z: 0}
  fDebug: 0
  _gizmosEditorStartPreview: {fileID: 0}
  _gizmospesp: {fileID: 0}
  _gizmosEditorEndPreview: {fileID: 0}
  _gizmospeep: {fileID: 0}
  _editor_Title: Tail Animator 2
  _editor_IsInspectorViewingColliders: 0
  _editor_IsInspectorViewingIncludedColliders: 0
  _editor_animatorViewedCounter: 1
  TailSegments: []
  GhostParent:
    ProceduralPosition: {x: 0, y: 0, z: 0}
    ProceduralPositionWeightBlended: {x: 0, y: 0, z: 0}
    TrueTargetRotation: {x: 0, y: 0, z: 0, w: 1}
    PosRefRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosReferenceRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosition: {x: 0, y: 0, z: 0}
    BlendValue: 1
    BoneDimensionsScaled: {x: 0, y: 0, z: 0}
    BoneLengthScaled: 0
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalPositionInRoot: {x: 0, y: 0, z: 0}
    InitialLocalRotationInRoot: {x: 0, y: 0, z: 0, w: 1}
    LocalOffset: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: 0, y: 0, z: 0, w: 1}
    ColliderRadius: 1
    CollisionContactFlag: 0
    CollisionContactRelevancy: -1
    VelocityHelper: {x: 0, y: 0, z: 0}
    QVelocityHelper: {x: 0, y: 0, z: 0, w: 1}
    PreviousPush: {x: 0, y: 0, z: 0}
    Curving: {x: 0, y: 0, z: 0, w: 1}
    Gravity: {x: 0, y: 0, z: 0}
    GravityLookOffset: {x: 0, y: 0, z: 0}
    LengthMultiplier: 1
    PositionSpeed: 1
    RotationSpeed: 1
    Springiness: 0
    Slithery: 1
    Curling: 0.5
    Slippery: 1
    LastKeyframeLocalRotation: {x: 0, y: 0, z: 0, w: 0}
    LastKeyframeLocalPosition: {x: 0, y: 0, z: 0}
  GhostChild:
    ProceduralPosition: {x: 0, y: 0, z: 0}
    ProceduralPositionWeightBlended: {x: 0, y: 0, z: 0}
    TrueTargetRotation: {x: 0, y: 0, z: 0, w: 1}
    PosRefRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosReferenceRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosition: {x: 0, y: 0, z: 0}
    BlendValue: 1
    BoneDimensionsScaled: {x: 0, y: 0, z: 0}
    BoneLengthScaled: 0
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalPositionInRoot: {x: 0, y: 0, z: 0}
    InitialLocalRotationInRoot: {x: 0, y: 0, z: 0, w: 1}
    LocalOffset: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: 0, y: 0, z: 0, w: 1}
    ColliderRadius: 1
    CollisionContactFlag: 0
    CollisionContactRelevancy: -1
    VelocityHelper: {x: 0, y: 0, z: 0}
    QVelocityHelper: {x: 0, y: 0, z: 0, w: 1}
    PreviousPush: {x: 0, y: 0, z: 0}
    Curving: {x: 0, y: 0, z: 0, w: 1}
    Gravity: {x: 0, y: 0, z: 0}
    GravityLookOffset: {x: 0, y: 0, z: 0}
    LengthMultiplier: 1
    PositionSpeed: 1
    RotationSpeed: 1
    Springiness: 0
    Slithery: 1
    Curling: 0.5
    Slippery: 1
    LastKeyframeLocalRotation: {x: 0, y: 0, z: 0, w: 0}
    LastKeyframeLocalPosition: {x: 0, y: 0, z: 0}
  UseMaxDistance: 0
  DistanceFrom: {fileID: 0}
  _distanceFrom_Auto: {fileID: 0}
  MaximumDistance: 35
  MaxOutDistanceFactor: 0
  DistanceWithoutY: 0
  DistanceMeasurePoint: {x: 0, y: 0, z: 0}
  FadeDuration: 0.75
  Deflection: 0
  DeflectionStartAngle: 10
  DeflectionSmooth: 0
  DeflectionFalloff:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0.1
      outSlope: 0.1
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 5
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  DeflectOnlyCollisions: 1
  _Editor_Category: 0
  _Editor_FeaturesCategory: 0
  DrawGizmos: 1
  StartBone: {fileID: 0}
  EndBone: {fileID: 0}
  EndBoneJointOffset: {x: 0, y: 0, z: 0}
  _TransformsGhostChain: []
  _GhostChainInitCount: -1
  UpdateRate: 0
  AnimatePhysics: 0
  InterpolateRate: 1
  Prewarm: 0
  Slithery: 1
  Curling: 0.5
  Springiness: 0
  MaxStretching: 0.375
  AngleLimit: 181
  AngleLimitAxis: {x: 0, y: 0, z: 0}
  LimitAxisRange: {x: 0, y: 0}
  LimitSmoothing: 0.5
  MotionInfluence: 1
  MotionInfluenceInY: 1
  IncludeParent: 1
  UnifyBendiness: 0
  ReactionSpeed: 0.9
  Sustain: 0
  RotationRelevancy: 1
  SmoothingStyle: 1
  TimeScale: 1
  DeltaType: 4
  UpdateAsLast: 1
  DetectZeroKeyframes: 1
  StartAfterTPose: 1
  OptimizeWithMesh: {fileID: 0}
  OptimizeWithMeshes: []
  TailAnimatorAmount: 1
  DetachChildren: 0
  Axis2D: 0
  Tangle: 0
  AnimateRoll: 0
  OverrideKeyframeAnimation: 0
--- !u!1 &1123132789430898
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4408487124684998}
  - component: {fileID: 114430649747312766}
  m_Layer: 0
  m_Name: Tail 1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4408487124684998
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1123132789430898}
  serializedVersion: 2
  m_LocalRotation: {x: 0.27059832, y: 0.6532806, z: -0.27059755, w: 0.6532825}
  m_LocalPosition: {x: 0.2814286, y: -0.28142834, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4500688365064020}
  m_LocalEulerAnglesHint: {x: -90, y: 0, z: 0}
--- !u!114 &114430649747312766
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1123132789430898}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 61574e354ba34a141ae2cc1228f5eda3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  UseCollision: 0
  CollisionSpace: 1
  CollisionMode: 0
  CheapCollision: 0
  DynamicWorldCollidersInclusion: 0
  InclusionRadius: 1
  IgnoreMeshColliders: 1
  IncludedColliders: []
  IncludedColliders2D: []
  CollidersType: 0
  CollideWithOtherTails: 0
  CollideWithDisabledColliders: 1
  CollisionSlippery: 1
  ReflectCollision: 0
  CollidersScaleCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  CollidersScaleMul: 6.5
  CollisionsAutoCurve: 0.5
  IgnoredColliders: []
  IgnoredColliders2D: []
  CollidersSameLayer: 1
  CollidersAddRigidbody: 1
  RigidbodyMass: 1
  CollidersLayer: 0
  UseSlitheryCurve: 0
  SlitheryCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.75
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseCurlingCurve: 0
  CurlingCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.3
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseSpringCurve: 0
  SpringCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseSlipperyCurve: 0
  SlipperyCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UsePosSpeedCurve: 0
  PosCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseRotSpeedCurve: 0
  RotCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.9
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UsePartialBlend: 0
  BlendCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.95
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.45
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseIK: 0
  IK:
    IKWeight: 0
    IKTargetPosition: {x: 0, y: 0, z: 0}
    IKTargetRotation: {x: 0, y: 0, z: 0, w: 0}
    LastLocalDirection: {x: 0, y: 0, z: 0}
    LocalDirection: {x: 0, y: 0, z: 0}
    IKBones: []
    ContinousSolving: 0
    SyncWithAnimator: 0
    ReactionQuality: 0
    Smoothing: 0
    StretchToTarget: 0
    StretchCurve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    Use2D: 0
    Invert: 0
  IKTarget: {fileID: 0}
  IKAutoWeights: 1
  IKBaseReactionWeight: 0.65
  IKReactionWeightCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.25
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  IKAutoAngleLimits: 1
  IKAutoAngleLimit: 40
  IKContinousSolve: 0
  IkInvertOrder: 0
  IKBlend: 1
  IKAnimatorBlend: 0.85
  IKReactionQuality: 2
  IKSmoothing: 0.2
  IKStretchToTarget: 0
  IKStretchCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  IKLimitSettings: []
  IKSelectiveChain: 0
  RotationOffset: {x: 0, y: 0, z: 0, w: 1}
  Curving: {x: 0, y: 0, z: 0, w: 1}
  UseCurvingCurve: 0
  CurvCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.75
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  LengthMultiplier: 1
  UseLengthMulCurve: 0
  LengthMulCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseGravityCurve: 0
  GravityCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.65
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  Gravity: {x: 0, y: 0, z: 0}
  UseWaving: 1
  CosinusAdd: 1
  FixedCycle: 0
  WavingSpeed: 3.25
  WavingRange: 0.4
  WavingAxis: {x: 0, y: 1, z: 0}
  WavingType: 0
  AlternateWave: 1
  UseWind: 0
  WindEffectPower: 1
  WindTurbulencePower: 1
  WindWorldNoisePower: 0.5
  WindEffect: {x: 0, y: 0, z: 0}
  fDebug: 0
  _gizmosEditorStartPreview: {fileID: 0}
  _gizmospesp: {fileID: 0}
  _gizmosEditorEndPreview: {fileID: 0}
  _gizmospeep: {fileID: 0}
  _editor_Title: Tail Animator 2
  _editor_IsInspectorViewingColliders: 0
  _editor_IsInspectorViewingIncludedColliders: 0
  _editor_animatorViewedCounter: 1
  TailSegments: []
  GhostParent:
    ProceduralPosition: {x: 0, y: 0, z: 0}
    ProceduralPositionWeightBlended: {x: 0, y: 0, z: 0}
    TrueTargetRotation: {x: 0, y: 0, z: 0, w: 1}
    PosRefRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosReferenceRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosition: {x: 0, y: 0, z: 0}
    BlendValue: 1
    BoneDimensionsScaled: {x: 0, y: 0, z: 0}
    BoneLengthScaled: 0
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalPositionInRoot: {x: 0, y: 0, z: 0}
    InitialLocalRotationInRoot: {x: 0, y: 0, z: 0, w: 1}
    LocalOffset: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: 0, y: 0, z: 0, w: 1}
    ColliderRadius: 1
    CollisionContactFlag: 0
    CollisionContactRelevancy: -1
    VelocityHelper: {x: 0, y: 0, z: 0}
    QVelocityHelper: {x: 0, y: 0, z: 0, w: 1}
    PreviousPush: {x: 0, y: 0, z: 0}
    Curving: {x: 0, y: 0, z: 0, w: 1}
    Gravity: {x: 0, y: 0, z: 0}
    GravityLookOffset: {x: 0, y: 0, z: 0}
    LengthMultiplier: 1
    PositionSpeed: 1
    RotationSpeed: 1
    Springiness: 0
    Slithery: 1
    Curling: 0.5
    Slippery: 1
    LastKeyframeLocalRotation: {x: 0, y: 0, z: 0, w: 0}
    LastKeyframeLocalPosition: {x: 0, y: 0, z: 0}
  GhostChild:
    ProceduralPosition: {x: 0, y: 0, z: 0}
    ProceduralPositionWeightBlended: {x: 0, y: 0, z: 0}
    TrueTargetRotation: {x: 0, y: 0, z: 0, w: 1}
    PosRefRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosReferenceRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosition: {x: 0, y: 0, z: 0}
    BlendValue: 1
    BoneDimensionsScaled: {x: 0, y: 0, z: 0}
    BoneLengthScaled: 0
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalPositionInRoot: {x: 0, y: 0, z: 0}
    InitialLocalRotationInRoot: {x: 0, y: 0, z: 0, w: 1}
    LocalOffset: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: 0, y: 0, z: 0, w: 1}
    ColliderRadius: 1
    CollisionContactFlag: 0
    CollisionContactRelevancy: -1
    VelocityHelper: {x: 0, y: 0, z: 0}
    QVelocityHelper: {x: 0, y: 0, z: 0, w: 1}
    PreviousPush: {x: 0, y: 0, z: 0}
    Curving: {x: 0, y: 0, z: 0, w: 1}
    Gravity: {x: 0, y: 0, z: 0}
    GravityLookOffset: {x: 0, y: 0, z: 0}
    LengthMultiplier: 1
    PositionSpeed: 1
    RotationSpeed: 1
    Springiness: 0
    Slithery: 1
    Curling: 0.5
    Slippery: 1
    LastKeyframeLocalRotation: {x: 0, y: 0, z: 0, w: 0}
    LastKeyframeLocalPosition: {x: 0, y: 0, z: 0}
  UseMaxDistance: 0
  DistanceFrom: {fileID: 0}
  _distanceFrom_Auto: {fileID: 0}
  MaximumDistance: 35
  MaxOutDistanceFactor: 0
  DistanceWithoutY: 0
  DistanceMeasurePoint: {x: 0, y: 0, z: 0}
  FadeDuration: 0.75
  Deflection: 0
  DeflectionStartAngle: 10
  DeflectionSmooth: 0
  DeflectionFalloff:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0.1
      outSlope: 0.1
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 5
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  DeflectOnlyCollisions: 1
  _Editor_Category: 0
  _Editor_FeaturesCategory: 0
  DrawGizmos: 1
  StartBone: {fileID: 0}
  EndBone: {fileID: 0}
  EndBoneJointOffset: {x: 0, y: 0, z: 0}
  _TransformsGhostChain: []
  _GhostChainInitCount: -1
  UpdateRate: 0
  AnimatePhysics: 0
  InterpolateRate: 1
  Prewarm: 0
  Slithery: 1
  Curling: 0.5
  Springiness: 0
  MaxStretching: 0.375
  AngleLimit: 181
  AngleLimitAxis: {x: 0, y: 0, z: 0}
  LimitAxisRange: {x: 0, y: 0}
  LimitSmoothing: 0.5
  MotionInfluence: 1
  MotionInfluenceInY: 1
  IncludeParent: 1
  UnifyBendiness: 0
  ReactionSpeed: 0.9
  Sustain: 0
  RotationRelevancy: 1
  SmoothingStyle: 1
  TimeScale: 1
  DeltaType: 4
  UpdateAsLast: 1
  DetectZeroKeyframes: 1
  StartAfterTPose: 1
  OptimizeWithMesh: {fileID: 0}
  OptimizeWithMeshes: []
  TailAnimatorAmount: 1
  DetachChildren: 0
  Axis2D: 0
  Tangle: 0
  AnimateRoll: 0
  OverrideKeyframeAnimation: 0
--- !u!1 &1346142044517616
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4316102901522140}
  - component: {fileID: 114679039289851918}
  m_Layer: 0
  m_Name: Tail 1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4316102901522140
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1346142044517616}
  serializedVersion: 2
  m_LocalRotation: {x: 0.5000007, y: 0.4999993, z: -0.49999925, w: 0.50000083}
  m_LocalPosition: {x: 0, y: -0.398, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4500688365064020}
  m_LocalEulerAnglesHint: {x: -90, y: 0, z: 0}
--- !u!114 &114679039289851918
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1346142044517616}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 61574e354ba34a141ae2cc1228f5eda3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  UseCollision: 0
  CollisionSpace: 1
  CollisionMode: 0
  CheapCollision: 0
  DynamicWorldCollidersInclusion: 0
  InclusionRadius: 1
  IgnoreMeshColliders: 1
  IncludedColliders: []
  IncludedColliders2D: []
  CollidersType: 0
  CollideWithOtherTails: 0
  CollideWithDisabledColliders: 1
  CollisionSlippery: 1
  ReflectCollision: 0
  CollidersScaleCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  CollidersScaleMul: 6.5
  CollisionsAutoCurve: 0.5
  IgnoredColliders: []
  IgnoredColliders2D: []
  CollidersSameLayer: 1
  CollidersAddRigidbody: 1
  RigidbodyMass: 1
  CollidersLayer: 0
  UseSlitheryCurve: 0
  SlitheryCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.75
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseCurlingCurve: 0
  CurlingCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.3
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseSpringCurve: 0
  SpringCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseSlipperyCurve: 0
  SlipperyCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UsePosSpeedCurve: 0
  PosCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseRotSpeedCurve: 0
  RotCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.9
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UsePartialBlend: 0
  BlendCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.95
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.45
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseIK: 0
  IK:
    IKWeight: 0
    IKTargetPosition: {x: 0, y: 0, z: 0}
    IKTargetRotation: {x: 0, y: 0, z: 0, w: 0}
    LastLocalDirection: {x: 0, y: 0, z: 0}
    LocalDirection: {x: 0, y: 0, z: 0}
    IKBones: []
    ContinousSolving: 0
    SyncWithAnimator: 0
    ReactionQuality: 0
    Smoothing: 0
    StretchToTarget: 0
    StretchCurve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    Use2D: 0
    Invert: 0
  IKTarget: {fileID: 0}
  IKAutoWeights: 1
  IKBaseReactionWeight: 0.65
  IKReactionWeightCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.25
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  IKAutoAngleLimits: 1
  IKAutoAngleLimit: 40
  IKContinousSolve: 0
  IkInvertOrder: 0
  IKBlend: 1
  IKAnimatorBlend: 0.85
  IKReactionQuality: 2
  IKSmoothing: 0.2
  IKStretchToTarget: 0
  IKStretchCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  IKLimitSettings: []
  IKSelectiveChain: 0
  RotationOffset: {x: 0, y: 0, z: 0, w: 1}
  Curving: {x: 0, y: 0, z: 0, w: 1}
  UseCurvingCurve: 0
  CurvCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.75
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  LengthMultiplier: 1
  UseLengthMulCurve: 0
  LengthMulCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseGravityCurve: 0
  GravityCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.65
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  Gravity: {x: 0, y: 0, z: 0}
  UseWaving: 1
  CosinusAdd: 1
  FixedCycle: 0
  WavingSpeed: 3.25
  WavingRange: 0.4
  WavingAxis: {x: 0, y: 1, z: 0}
  WavingType: 0
  AlternateWave: 1
  UseWind: 0
  WindEffectPower: 1
  WindTurbulencePower: 1
  WindWorldNoisePower: 0.5
  WindEffect: {x: 0, y: 0, z: 0}
  fDebug: 0
  _gizmosEditorStartPreview: {fileID: 0}
  _gizmospesp: {fileID: 0}
  _gizmosEditorEndPreview: {fileID: 0}
  _gizmospeep: {fileID: 0}
  _editor_Title: Tail Animator 2
  _editor_IsInspectorViewingColliders: 0
  _editor_IsInspectorViewingIncludedColliders: 0
  _editor_animatorViewedCounter: 1
  TailSegments: []
  GhostParent:
    ProceduralPosition: {x: 0, y: 0, z: 0}
    ProceduralPositionWeightBlended: {x: 0, y: 0, z: 0}
    TrueTargetRotation: {x: 0, y: 0, z: 0, w: 1}
    PosRefRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosReferenceRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosition: {x: 0, y: 0, z: 0}
    BlendValue: 1
    BoneDimensionsScaled: {x: 0, y: 0, z: 0}
    BoneLengthScaled: 0
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalPositionInRoot: {x: 0, y: 0, z: 0}
    InitialLocalRotationInRoot: {x: 0, y: 0, z: 0, w: 1}
    LocalOffset: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: 0, y: 0, z: 0, w: 1}
    ColliderRadius: 1
    CollisionContactFlag: 0
    CollisionContactRelevancy: -1
    VelocityHelper: {x: 0, y: 0, z: 0}
    QVelocityHelper: {x: 0, y: 0, z: 0, w: 1}
    PreviousPush: {x: 0, y: 0, z: 0}
    Curving: {x: 0, y: 0, z: 0, w: 1}
    Gravity: {x: 0, y: 0, z: 0}
    GravityLookOffset: {x: 0, y: 0, z: 0}
    LengthMultiplier: 1
    PositionSpeed: 1
    RotationSpeed: 1
    Springiness: 0
    Slithery: 1
    Curling: 0.5
    Slippery: 1
    LastKeyframeLocalRotation: {x: 0, y: 0, z: 0, w: 0}
    LastKeyframeLocalPosition: {x: 0, y: 0, z: 0}
  GhostChild:
    ProceduralPosition: {x: 0, y: 0, z: 0}
    ProceduralPositionWeightBlended: {x: 0, y: 0, z: 0}
    TrueTargetRotation: {x: 0, y: 0, z: 0, w: 1}
    PosRefRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosReferenceRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosition: {x: 0, y: 0, z: 0}
    BlendValue: 1
    BoneDimensionsScaled: {x: 0, y: 0, z: 0}
    BoneLengthScaled: 0
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalPositionInRoot: {x: 0, y: 0, z: 0}
    InitialLocalRotationInRoot: {x: 0, y: 0, z: 0, w: 1}
    LocalOffset: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: 0, y: 0, z: 0, w: 1}
    ColliderRadius: 1
    CollisionContactFlag: 0
    CollisionContactRelevancy: -1
    VelocityHelper: {x: 0, y: 0, z: 0}
    QVelocityHelper: {x: 0, y: 0, z: 0, w: 1}
    PreviousPush: {x: 0, y: 0, z: 0}
    Curving: {x: 0, y: 0, z: 0, w: 1}
    Gravity: {x: 0, y: 0, z: 0}
    GravityLookOffset: {x: 0, y: 0, z: 0}
    LengthMultiplier: 1
    PositionSpeed: 1
    RotationSpeed: 1
    Springiness: 0
    Slithery: 1
    Curling: 0.5
    Slippery: 1
    LastKeyframeLocalRotation: {x: 0, y: 0, z: 0, w: 0}
    LastKeyframeLocalPosition: {x: 0, y: 0, z: 0}
  UseMaxDistance: 0
  DistanceFrom: {fileID: 0}
  _distanceFrom_Auto: {fileID: 0}
  MaximumDistance: 35
  MaxOutDistanceFactor: 0
  DistanceWithoutY: 0
  DistanceMeasurePoint: {x: 0, y: 0, z: 0}
  FadeDuration: 0.75
  Deflection: 0
  DeflectionStartAngle: 10
  DeflectionSmooth: 0
  DeflectionFalloff:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0.1
      outSlope: 0.1
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 5
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  DeflectOnlyCollisions: 1
  _Editor_Category: 0
  _Editor_FeaturesCategory: 0
  DrawGizmos: 1
  StartBone: {fileID: 0}
  EndBone: {fileID: 0}
  EndBoneJointOffset: {x: 0, y: 0, z: 0}
  _TransformsGhostChain: []
  _GhostChainInitCount: -1
  UpdateRate: 0
  AnimatePhysics: 0
  InterpolateRate: 1
  Prewarm: 0
  Slithery: 1
  Curling: 0.5
  Springiness: 0
  MaxStretching: 0.375
  AngleLimit: 181
  AngleLimitAxis: {x: 0, y: 0, z: 0}
  LimitAxisRange: {x: 0, y: 0}
  LimitSmoothing: 0.5
  MotionInfluence: 1
  MotionInfluenceInY: 1
  IncludeParent: 1
  UnifyBendiness: 0
  ReactionSpeed: 0.9
  Sustain: 0
  RotationRelevancy: 1
  SmoothingStyle: 1
  TimeScale: 1
  DeltaType: 4
  UpdateAsLast: 1
  DetectZeroKeyframes: 1
  StartAfterTPose: 1
  OptimizeWithMesh: {fileID: 0}
  OptimizeWithMeshes: []
  TailAnimatorAmount: 1
  DetachChildren: 0
  Axis2D: 0
  Tangle: 0
  AnimateRoll: 0
  OverrideKeyframeAnimation: 0
--- !u!1 &1714975381893214
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4265440575917502}
  - component: {fileID: 114692163574913326}
  m_Layer: 0
  m_Name: Tail 1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4265440575917502
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1714975381893214}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: 0.7071058, z: -0, w: 0.7071078}
  m_LocalPosition: {x: 0.39800003, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4500688365064020}
  m_LocalEulerAnglesHint: {x: -90, y: 0, z: 0}
--- !u!114 &114692163574913326
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1714975381893214}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 61574e354ba34a141ae2cc1228f5eda3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  UseCollision: 0
  CollisionSpace: 1
  CollisionMode: 0
  CheapCollision: 0
  DynamicWorldCollidersInclusion: 0
  InclusionRadius: 1
  IgnoreMeshColliders: 1
  IncludedColliders: []
  IncludedColliders2D: []
  CollidersType: 0
  CollideWithOtherTails: 0
  CollideWithDisabledColliders: 1
  CollisionSlippery: 1
  ReflectCollision: 0
  CollidersScaleCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  CollidersScaleMul: 6.5
  CollisionsAutoCurve: 0.5
  IgnoredColliders: []
  IgnoredColliders2D: []
  CollidersSameLayer: 1
  CollidersAddRigidbody: 1
  RigidbodyMass: 1
  CollidersLayer: 0
  UseSlitheryCurve: 0
  SlitheryCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.75
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseCurlingCurve: 0
  CurlingCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.3
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseSpringCurve: 0
  SpringCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseSlipperyCurve: 0
  SlipperyCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UsePosSpeedCurve: 0
  PosCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseRotSpeedCurve: 0
  RotCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.9
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UsePartialBlend: 0
  BlendCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.95
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.45
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseIK: 0
  IK:
    IKWeight: 0
    IKTargetPosition: {x: 0, y: 0, z: 0}
    IKTargetRotation: {x: 0, y: 0, z: 0, w: 0}
    LastLocalDirection: {x: 0, y: 0, z: 0}
    LocalDirection: {x: 0, y: 0, z: 0}
    IKBones: []
    ContinousSolving: 0
    SyncWithAnimator: 0
    ReactionQuality: 0
    Smoothing: 0
    StretchToTarget: 0
    StretchCurve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    Use2D: 0
    Invert: 0
  IKTarget: {fileID: 0}
  IKAutoWeights: 1
  IKBaseReactionWeight: 0.65
  IKReactionWeightCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.25
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  IKAutoAngleLimits: 1
  IKAutoAngleLimit: 40
  IKContinousSolve: 0
  IkInvertOrder: 0
  IKBlend: 1
  IKAnimatorBlend: 0.85
  IKReactionQuality: 2
  IKSmoothing: 0.2
  IKStretchToTarget: 0
  IKStretchCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  IKLimitSettings: []
  IKSelectiveChain: 0
  RotationOffset: {x: 0, y: 0, z: 0, w: 1}
  Curving: {x: 0, y: 0, z: 0, w: 1}
  UseCurvingCurve: 0
  CurvCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.75
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  LengthMultiplier: 1
  UseLengthMulCurve: 0
  LengthMulCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseGravityCurve: 0
  GravityCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.65
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  Gravity: {x: 0, y: 0, z: 0}
  UseWaving: 1
  CosinusAdd: 1
  FixedCycle: 0
  WavingSpeed: 3.25
  WavingRange: 0.4
  WavingAxis: {x: 0, y: 1, z: 0}
  WavingType: 0
  AlternateWave: 1
  UseWind: 0
  WindEffectPower: 1
  WindTurbulencePower: 1
  WindWorldNoisePower: 0.5
  WindEffect: {x: 0, y: 0, z: 0}
  fDebug: 0
  _gizmosEditorStartPreview: {fileID: 0}
  _gizmospesp: {fileID: 0}
  _gizmosEditorEndPreview: {fileID: 0}
  _gizmospeep: {fileID: 0}
  _editor_Title: Tail Animator 2
  _editor_IsInspectorViewingColliders: 0
  _editor_IsInspectorViewingIncludedColliders: 0
  _editor_animatorViewedCounter: 1
  TailSegments: []
  GhostParent:
    ProceduralPosition: {x: 0, y: 0, z: 0}
    ProceduralPositionWeightBlended: {x: 0, y: 0, z: 0}
    TrueTargetRotation: {x: 0, y: 0, z: 0, w: 1}
    PosRefRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosReferenceRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosition: {x: 0, y: 0, z: 0}
    BlendValue: 1
    BoneDimensionsScaled: {x: 0, y: 0, z: 0}
    BoneLengthScaled: 0
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalPositionInRoot: {x: 0, y: 0, z: 0}
    InitialLocalRotationInRoot: {x: 0, y: 0, z: 0, w: 1}
    LocalOffset: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: 0, y: 0, z: 0, w: 1}
    ColliderRadius: 1
    CollisionContactFlag: 0
    CollisionContactRelevancy: -1
    VelocityHelper: {x: 0, y: 0, z: 0}
    QVelocityHelper: {x: 0, y: 0, z: 0, w: 1}
    PreviousPush: {x: 0, y: 0, z: 0}
    Curving: {x: 0, y: 0, z: 0, w: 1}
    Gravity: {x: 0, y: 0, z: 0}
    GravityLookOffset: {x: 0, y: 0, z: 0}
    LengthMultiplier: 1
    PositionSpeed: 1
    RotationSpeed: 1
    Springiness: 0
    Slithery: 1
    Curling: 0.5
    Slippery: 1
    LastKeyframeLocalRotation: {x: 0, y: 0, z: 0, w: 0}
    LastKeyframeLocalPosition: {x: 0, y: 0, z: 0}
  GhostChild:
    ProceduralPosition: {x: 0, y: 0, z: 0}
    ProceduralPositionWeightBlended: {x: 0, y: 0, z: 0}
    TrueTargetRotation: {x: 0, y: 0, z: 0, w: 1}
    PosRefRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosReferenceRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosition: {x: 0, y: 0, z: 0}
    BlendValue: 1
    BoneDimensionsScaled: {x: 0, y: 0, z: 0}
    BoneLengthScaled: 0
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalPositionInRoot: {x: 0, y: 0, z: 0}
    InitialLocalRotationInRoot: {x: 0, y: 0, z: 0, w: 1}
    LocalOffset: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: 0, y: 0, z: 0, w: 1}
    ColliderRadius: 1
    CollisionContactFlag: 0
    CollisionContactRelevancy: -1
    VelocityHelper: {x: 0, y: 0, z: 0}
    QVelocityHelper: {x: 0, y: 0, z: 0, w: 1}
    PreviousPush: {x: 0, y: 0, z: 0}
    Curving: {x: 0, y: 0, z: 0, w: 1}
    Gravity: {x: 0, y: 0, z: 0}
    GravityLookOffset: {x: 0, y: 0, z: 0}
    LengthMultiplier: 1
    PositionSpeed: 1
    RotationSpeed: 1
    Springiness: 0
    Slithery: 1
    Curling: 0.5
    Slippery: 1
    LastKeyframeLocalRotation: {x: 0, y: 0, z: 0, w: 0}
    LastKeyframeLocalPosition: {x: 0, y: 0, z: 0}
  UseMaxDistance: 0
  DistanceFrom: {fileID: 0}
  _distanceFrom_Auto: {fileID: 0}
  MaximumDistance: 35
  MaxOutDistanceFactor: 0
  DistanceWithoutY: 0
  DistanceMeasurePoint: {x: 0, y: 0, z: 0}
  FadeDuration: 0.75
  Deflection: 0
  DeflectionStartAngle: 10
  DeflectionSmooth: 0
  DeflectionFalloff:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0.1
      outSlope: 0.1
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 5
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  DeflectOnlyCollisions: 1
  _Editor_Category: 0
  _Editor_FeaturesCategory: 0
  DrawGizmos: 1
  StartBone: {fileID: 0}
  EndBone: {fileID: 0}
  EndBoneJointOffset: {x: 0, y: 0, z: 0}
  _TransformsGhostChain: []
  _GhostChainInitCount: -1
  UpdateRate: 0
  AnimatePhysics: 0
  InterpolateRate: 1
  Prewarm: 0
  Slithery: 1
  Curling: 0.5
  Springiness: 0
  MaxStretching: 0.375
  AngleLimit: 181
  AngleLimitAxis: {x: 0, y: 0, z: 0}
  LimitAxisRange: {x: 0, y: 0}
  LimitSmoothing: 0.5
  MotionInfluence: 1
  MotionInfluenceInY: 1
  IncludeParent: 1
  UnifyBendiness: 0
  ReactionSpeed: 0.9
  Sustain: 0
  RotationRelevancy: 1
  SmoothingStyle: 1
  TimeScale: 1
  DeltaType: 4
  UpdateAsLast: 1
  DetectZeroKeyframes: 1
  StartAfterTPose: 1
  OptimizeWithMesh: {fileID: 0}
  OptimizeWithMeshes: []
  TailAnimatorAmount: 1
  DetachChildren: 0
  Axis2D: 0
  Tangle: 0
  AnimateRoll: 0
  OverrideKeyframeAnimation: 0
--- !u!1 &1747890445726472
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4808372848937036}
  - component: {fileID: 114162676352585136}
  m_Layer: 0
  m_Name: Tail 1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4808372848937036
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1747890445726472}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071078, y: -0, z: 0.7071058, w: 0}
  m_LocalPosition: {x: -0.398, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4500688365064020}
  m_LocalEulerAnglesHint: {x: -90, y: 0, z: 0}
--- !u!114 &114162676352585136
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1747890445726472}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 61574e354ba34a141ae2cc1228f5eda3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  UseCollision: 0
  CollisionSpace: 1
  CollisionMode: 0
  CheapCollision: 0
  DynamicWorldCollidersInclusion: 0
  InclusionRadius: 1
  IgnoreMeshColliders: 1
  IncludedColliders: []
  IncludedColliders2D: []
  CollidersType: 0
  CollideWithOtherTails: 0
  CollideWithDisabledColliders: 1
  CollisionSlippery: 1
  ReflectCollision: 0
  CollidersScaleCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  CollidersScaleMul: 6.5
  CollisionsAutoCurve: 0.5
  IgnoredColliders: []
  IgnoredColliders2D: []
  CollidersSameLayer: 1
  CollidersAddRigidbody: 1
  RigidbodyMass: 1
  CollidersLayer: 0
  UseSlitheryCurve: 0
  SlitheryCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.75
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseCurlingCurve: 0
  CurlingCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.3
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseSpringCurve: 0
  SpringCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseSlipperyCurve: 0
  SlipperyCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UsePosSpeedCurve: 0
  PosCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseRotSpeedCurve: 0
  RotCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.9
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UsePartialBlend: 0
  BlendCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.95
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.45
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseIK: 0
  IK:
    IKWeight: 0
    IKTargetPosition: {x: 0, y: 0, z: 0}
    IKTargetRotation: {x: 0, y: 0, z: 0, w: 0}
    LastLocalDirection: {x: 0, y: 0, z: 0}
    LocalDirection: {x: 0, y: 0, z: 0}
    IKBones: []
    ContinousSolving: 0
    SyncWithAnimator: 0
    ReactionQuality: 0
    Smoothing: 0
    StretchToTarget: 0
    StretchCurve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    Use2D: 0
    Invert: 0
  IKTarget: {fileID: 0}
  IKAutoWeights: 1
  IKBaseReactionWeight: 0.65
  IKReactionWeightCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.25
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  IKAutoAngleLimits: 1
  IKAutoAngleLimit: 40
  IKContinousSolve: 0
  IkInvertOrder: 0
  IKBlend: 1
  IKAnimatorBlend: 0.85
  IKReactionQuality: 2
  IKSmoothing: 0.2
  IKStretchToTarget: 0
  IKStretchCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  IKLimitSettings: []
  IKSelectiveChain: 0
  RotationOffset: {x: 0, y: 0, z: 0, w: 1}
  Curving: {x: 0, y: 0, z: 0, w: 1}
  UseCurvingCurve: 0
  CurvCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.75
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  LengthMultiplier: 1
  UseLengthMulCurve: 0
  LengthMulCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseGravityCurve: 0
  GravityCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.65
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  Gravity: {x: 0, y: 0, z: 0}
  UseWaving: 1
  CosinusAdd: 1
  FixedCycle: 0
  WavingSpeed: 3.25
  WavingRange: 0.4
  WavingAxis: {x: 0, y: 1, z: 0}
  WavingType: 0
  AlternateWave: 1
  UseWind: 0
  WindEffectPower: 1
  WindTurbulencePower: 1
  WindWorldNoisePower: 0.5
  WindEffect: {x: 0, y: 0, z: 0}
  fDebug: 0
  _gizmosEditorStartPreview: {fileID: 0}
  _gizmospesp: {fileID: 0}
  _gizmosEditorEndPreview: {fileID: 0}
  _gizmospeep: {fileID: 0}
  _editor_Title: Tail Animator 2
  _editor_IsInspectorViewingColliders: 0
  _editor_IsInspectorViewingIncludedColliders: 0
  _editor_animatorViewedCounter: 1
  TailSegments: []
  GhostParent:
    ProceduralPosition: {x: 0, y: 0, z: 0}
    ProceduralPositionWeightBlended: {x: 0, y: 0, z: 0}
    TrueTargetRotation: {x: 0, y: 0, z: 0, w: 1}
    PosRefRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosReferenceRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosition: {x: 0, y: 0, z: 0}
    BlendValue: 1
    BoneDimensionsScaled: {x: 0, y: 0, z: 0}
    BoneLengthScaled: 0
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalPositionInRoot: {x: 0, y: 0, z: 0}
    InitialLocalRotationInRoot: {x: 0, y: 0, z: 0, w: 1}
    LocalOffset: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: 0, y: 0, z: 0, w: 1}
    ColliderRadius: 1
    CollisionContactFlag: 0
    CollisionContactRelevancy: -1
    VelocityHelper: {x: 0, y: 0, z: 0}
    QVelocityHelper: {x: 0, y: 0, z: 0, w: 1}
    PreviousPush: {x: 0, y: 0, z: 0}
    Curving: {x: 0, y: 0, z: 0, w: 1}
    Gravity: {x: 0, y: 0, z: 0}
    GravityLookOffset: {x: 0, y: 0, z: 0}
    LengthMultiplier: 1
    PositionSpeed: 1
    RotationSpeed: 1
    Springiness: 0
    Slithery: 1
    Curling: 0.5
    Slippery: 1
    LastKeyframeLocalRotation: {x: 0, y: 0, z: 0, w: 0}
    LastKeyframeLocalPosition: {x: 0, y: 0, z: 0}
  GhostChild:
    ProceduralPosition: {x: 0, y: 0, z: 0}
    ProceduralPositionWeightBlended: {x: 0, y: 0, z: 0}
    TrueTargetRotation: {x: 0, y: 0, z: 0, w: 1}
    PosRefRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosReferenceRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosition: {x: 0, y: 0, z: 0}
    BlendValue: 1
    BoneDimensionsScaled: {x: 0, y: 0, z: 0}
    BoneLengthScaled: 0
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalPositionInRoot: {x: 0, y: 0, z: 0}
    InitialLocalRotationInRoot: {x: 0, y: 0, z: 0, w: 1}
    LocalOffset: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: 0, y: 0, z: 0, w: 1}
    ColliderRadius: 1
    CollisionContactFlag: 0
    CollisionContactRelevancy: -1
    VelocityHelper: {x: 0, y: 0, z: 0}
    QVelocityHelper: {x: 0, y: 0, z: 0, w: 1}
    PreviousPush: {x: 0, y: 0, z: 0}
    Curving: {x: 0, y: 0, z: 0, w: 1}
    Gravity: {x: 0, y: 0, z: 0}
    GravityLookOffset: {x: 0, y: 0, z: 0}
    LengthMultiplier: 1
    PositionSpeed: 1
    RotationSpeed: 1
    Springiness: 0
    Slithery: 1
    Curling: 0.5
    Slippery: 1
    LastKeyframeLocalRotation: {x: 0, y: 0, z: 0, w: 0}
    LastKeyframeLocalPosition: {x: 0, y: 0, z: 0}
  UseMaxDistance: 0
  DistanceFrom: {fileID: 0}
  _distanceFrom_Auto: {fileID: 0}
  MaximumDistance: 35
  MaxOutDistanceFactor: 0
  DistanceWithoutY: 0
  DistanceMeasurePoint: {x: 0, y: 0, z: 0}
  FadeDuration: 0.75
  Deflection: 0
  DeflectionStartAngle: 10
  DeflectionSmooth: 0
  DeflectionFalloff:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0.1
      outSlope: 0.1
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 5
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  DeflectOnlyCollisions: 1
  _Editor_Category: 0
  _Editor_FeaturesCategory: 0
  DrawGizmos: 1
  StartBone: {fileID: 0}
  EndBone: {fileID: 0}
  EndBoneJointOffset: {x: 0, y: 0, z: 0}
  _TransformsGhostChain: []
  _GhostChainInitCount: -1
  UpdateRate: 0
  AnimatePhysics: 0
  InterpolateRate: 1
  Prewarm: 0
  Slithery: 1
  Curling: 0.5
  Springiness: 0
  MaxStretching: 0.375
  AngleLimit: 181
  AngleLimitAxis: {x: 0, y: 0, z: 0}
  LimitAxisRange: {x: 0, y: 0}
  LimitSmoothing: 0.5
  MotionInfluence: 1
  MotionInfluenceInY: 1
  IncludeParent: 1
  UnifyBendiness: 0
  ReactionSpeed: 0.9
  Sustain: 0
  RotationRelevancy: 1
  SmoothingStyle: 1
  TimeScale: 1
  DeltaType: 4
  UpdateAsLast: 1
  DetectZeroKeyframes: 1
  StartAfterTPose: 1
  OptimizeWithMesh: {fileID: 0}
  OptimizeWithMeshes: []
  TailAnimatorAmount: 1
  DetachChildren: 0
  Axis2D: 0
  Tangle: 0
  AnimateRoll: 0
  OverrideKeyframeAnimation: 0
--- !u!1 &1815379819013494
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4373050835076546}
  - component: {fileID: 114104804815419904}
  m_Layer: 0
  m_Name: Tail 1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4373050835076546
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1815379819013494}
  serializedVersion: 2
  m_LocalRotation: {x: -0.2705984, y: 0.65328056, z: 0.2705977, w: 0.6532824}
  m_LocalPosition: {x: 0.2814285, y: 0.28142858, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4500688365064020}
  m_LocalEulerAnglesHint: {x: -90, y: 0, z: 0}
--- !u!114 &114104804815419904
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1815379819013494}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 61574e354ba34a141ae2cc1228f5eda3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  UseCollision: 0
  CollisionSpace: 1
  CollisionMode: 0
  CheapCollision: 0
  DynamicWorldCollidersInclusion: 0
  InclusionRadius: 1
  IgnoreMeshColliders: 1
  IncludedColliders: []
  IncludedColliders2D: []
  CollidersType: 0
  CollideWithOtherTails: 0
  CollideWithDisabledColliders: 1
  CollisionSlippery: 1
  ReflectCollision: 0
  CollidersScaleCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  CollidersScaleMul: 6.5
  CollisionsAutoCurve: 0.5
  IgnoredColliders: []
  IgnoredColliders2D: []
  CollidersSameLayer: 1
  CollidersAddRigidbody: 1
  RigidbodyMass: 1
  CollidersLayer: 0
  UseSlitheryCurve: 0
  SlitheryCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.75
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseCurlingCurve: 0
  CurlingCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.3
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseSpringCurve: 0
  SpringCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseSlipperyCurve: 0
  SlipperyCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UsePosSpeedCurve: 0
  PosCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseRotSpeedCurve: 0
  RotCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.9
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UsePartialBlend: 0
  BlendCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.95
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.45
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseIK: 0
  IK:
    IKWeight: 0
    IKTargetPosition: {x: 0, y: 0, z: 0}
    IKTargetRotation: {x: 0, y: 0, z: 0, w: 0}
    LastLocalDirection: {x: 0, y: 0, z: 0}
    LocalDirection: {x: 0, y: 0, z: 0}
    IKBones: []
    ContinousSolving: 0
    SyncWithAnimator: 0
    ReactionQuality: 0
    Smoothing: 0
    StretchToTarget: 0
    StretchCurve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    Use2D: 0
    Invert: 0
  IKTarget: {fileID: 0}
  IKAutoWeights: 1
  IKBaseReactionWeight: 0.65
  IKReactionWeightCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.25
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  IKAutoAngleLimits: 1
  IKAutoAngleLimit: 40
  IKContinousSolve: 0
  IkInvertOrder: 0
  IKBlend: 1
  IKAnimatorBlend: 0.85
  IKReactionQuality: 2
  IKSmoothing: 0.2
  IKStretchToTarget: 0
  IKStretchCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  IKLimitSettings: []
  IKSelectiveChain: 0
  RotationOffset: {x: 0, y: 0, z: 0, w: 1}
  Curving: {x: 0, y: 0, z: 0, w: 1}
  UseCurvingCurve: 0
  CurvCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.75
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  LengthMultiplier: 1
  UseLengthMulCurve: 0
  LengthMulCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseGravityCurve: 0
  GravityCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.65
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  Gravity: {x: 0, y: 0, z: 0}
  UseWaving: 1
  CosinusAdd: 1
  FixedCycle: 0
  WavingSpeed: 3.25
  WavingRange: 0.4
  WavingAxis: {x: 0, y: 1, z: 0}
  WavingType: 0
  AlternateWave: 1
  UseWind: 0
  WindEffectPower: 1
  WindTurbulencePower: 1
  WindWorldNoisePower: 0.5
  WindEffect: {x: 0, y: 0, z: 0}
  fDebug: 0
  _gizmosEditorStartPreview: {fileID: 0}
  _gizmospesp: {fileID: 0}
  _gizmosEditorEndPreview: {fileID: 0}
  _gizmospeep: {fileID: 0}
  _editor_Title: Tail Animator 2
  _editor_IsInspectorViewingColliders: 0
  _editor_IsInspectorViewingIncludedColliders: 0
  _editor_animatorViewedCounter: 1
  TailSegments: []
  GhostParent:
    ProceduralPosition: {x: 0, y: 0, z: 0}
    ProceduralPositionWeightBlended: {x: 0, y: 0, z: 0}
    TrueTargetRotation: {x: 0, y: 0, z: 0, w: 1}
    PosRefRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosReferenceRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosition: {x: 0, y: 0, z: 0}
    BlendValue: 1
    BoneDimensionsScaled: {x: 0, y: 0, z: 0}
    BoneLengthScaled: 0
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalPositionInRoot: {x: 0, y: 0, z: 0}
    InitialLocalRotationInRoot: {x: 0, y: 0, z: 0, w: 1}
    LocalOffset: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: 0, y: 0, z: 0, w: 1}
    ColliderRadius: 1
    CollisionContactFlag: 0
    CollisionContactRelevancy: -1
    VelocityHelper: {x: 0, y: 0, z: 0}
    QVelocityHelper: {x: 0, y: 0, z: 0, w: 1}
    PreviousPush: {x: 0, y: 0, z: 0}
    Curving: {x: 0, y: 0, z: 0, w: 1}
    Gravity: {x: 0, y: 0, z: 0}
    GravityLookOffset: {x: 0, y: 0, z: 0}
    LengthMultiplier: 1
    PositionSpeed: 1
    RotationSpeed: 1
    Springiness: 0
    Slithery: 1
    Curling: 0.5
    Slippery: 1
    LastKeyframeLocalRotation: {x: 0, y: 0, z: 0, w: 0}
    LastKeyframeLocalPosition: {x: 0, y: 0, z: 0}
  GhostChild:
    ProceduralPosition: {x: 0, y: 0, z: 0}
    ProceduralPositionWeightBlended: {x: 0, y: 0, z: 0}
    TrueTargetRotation: {x: 0, y: 0, z: 0, w: 1}
    PosRefRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosReferenceRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosition: {x: 0, y: 0, z: 0}
    BlendValue: 1
    BoneDimensionsScaled: {x: 0, y: 0, z: 0}
    BoneLengthScaled: 0
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalPositionInRoot: {x: 0, y: 0, z: 0}
    InitialLocalRotationInRoot: {x: 0, y: 0, z: 0, w: 1}
    LocalOffset: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: 0, y: 0, z: 0, w: 1}
    ColliderRadius: 1
    CollisionContactFlag: 0
    CollisionContactRelevancy: -1
    VelocityHelper: {x: 0, y: 0, z: 0}
    QVelocityHelper: {x: 0, y: 0, z: 0, w: 1}
    PreviousPush: {x: 0, y: 0, z: 0}
    Curving: {x: 0, y: 0, z: 0, w: 1}
    Gravity: {x: 0, y: 0, z: 0}
    GravityLookOffset: {x: 0, y: 0, z: 0}
    LengthMultiplier: 1
    PositionSpeed: 1
    RotationSpeed: 1
    Springiness: 0
    Slithery: 1
    Curling: 0.5
    Slippery: 1
    LastKeyframeLocalRotation: {x: 0, y: 0, z: 0, w: 0}
    LastKeyframeLocalPosition: {x: 0, y: 0, z: 0}
  UseMaxDistance: 0
  DistanceFrom: {fileID: 0}
  _distanceFrom_Auto: {fileID: 0}
  MaximumDistance: 35
  MaxOutDistanceFactor: 0
  DistanceWithoutY: 0
  DistanceMeasurePoint: {x: 0, y: 0, z: 0}
  FadeDuration: 0.75
  Deflection: 0
  DeflectionStartAngle: 10
  DeflectionSmooth: 0
  DeflectionFalloff:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0.1
      outSlope: 0.1
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 5
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  DeflectOnlyCollisions: 1
  _Editor_Category: 0
  _Editor_FeaturesCategory: 0
  DrawGizmos: 1
  StartBone: {fileID: 0}
  EndBone: {fileID: 0}
  EndBoneJointOffset: {x: 0, y: 0, z: 0}
  _TransformsGhostChain: []
  _GhostChainInitCount: -1
  UpdateRate: 0
  AnimatePhysics: 0
  InterpolateRate: 1
  Prewarm: 0
  Slithery: 1
  Curling: 0.5
  Springiness: 0
  MaxStretching: 0.375
  AngleLimit: 181
  AngleLimitAxis: {x: 0, y: 0, z: 0}
  LimitAxisRange: {x: 0, y: 0}
  LimitSmoothing: 0.5
  MotionInfluence: 1
  MotionInfluenceInY: 1
  IncludeParent: 1
  UnifyBendiness: 0
  ReactionSpeed: 0.9
  Sustain: 0
  RotationRelevancy: 1
  SmoothingStyle: 1
  TimeScale: 1
  DeltaType: 4
  UpdateAsLast: 1
  DetectZeroKeyframes: 1
  StartAfterTPose: 1
  OptimizeWithMesh: {fileID: 0}
  OptimizeWithMeshes: []
  TailAnimatorAmount: 1
  DetachChildren: 0
  Axis2D: 0
  Tangle: 0
  AnimateRoll: 0
  OverrideKeyframeAnimation: 0
--- !u!1 &1965571971659114
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4814156698578472}
  - component: {fileID: 114858218688350706}
  m_Layer: 0
  m_Name: Tail 1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4814156698578472
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1965571971659114}
  serializedVersion: 2
  m_LocalRotation: {x: -0.5000008, y: 0.49999928, z: 0.49999928, w: 0.5000008}
  m_LocalPosition: {x: 0, y: 0.398, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4500688365064020}
  m_LocalEulerAnglesHint: {x: -90, y: 0, z: 0}
--- !u!114 &114858218688350706
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1965571971659114}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 61574e354ba34a141ae2cc1228f5eda3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  UseCollision: 0
  CollisionSpace: 1
  CollisionMode: 0
  CheapCollision: 0
  DynamicWorldCollidersInclusion: 0
  InclusionRadius: 1
  IgnoreMeshColliders: 1
  IncludedColliders: []
  IncludedColliders2D: []
  CollidersType: 0
  CollideWithOtherTails: 0
  CollideWithDisabledColliders: 1
  CollisionSlippery: 1
  ReflectCollision: 0
  CollidersScaleCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  CollidersScaleMul: 6.5
  CollisionsAutoCurve: 0.5
  IgnoredColliders: []
  IgnoredColliders2D: []
  CollidersSameLayer: 1
  CollidersAddRigidbody: 1
  RigidbodyMass: 1
  CollidersLayer: 0
  UseSlitheryCurve: 0
  SlitheryCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.75
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseCurlingCurve: 0
  CurlingCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.3
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseSpringCurve: 0
  SpringCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseSlipperyCurve: 0
  SlipperyCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UsePosSpeedCurve: 0
  PosCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseRotSpeedCurve: 0
  RotCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.9
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UsePartialBlend: 0
  BlendCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.95
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.45
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseIK: 0
  IK:
    IKWeight: 0
    IKTargetPosition: {x: 0, y: 0, z: 0}
    IKTargetRotation: {x: 0, y: 0, z: 0, w: 0}
    LastLocalDirection: {x: 0, y: 0, z: 0}
    LocalDirection: {x: 0, y: 0, z: 0}
    IKBones: []
    ContinousSolving: 0
    SyncWithAnimator: 0
    ReactionQuality: 0
    Smoothing: 0
    StretchToTarget: 0
    StretchCurve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    Use2D: 0
    Invert: 0
  IKTarget: {fileID: 0}
  IKAutoWeights: 1
  IKBaseReactionWeight: 0.65
  IKReactionWeightCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.25
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  IKAutoAngleLimits: 1
  IKAutoAngleLimit: 40
  IKContinousSolve: 0
  IkInvertOrder: 0
  IKBlend: 1
  IKAnimatorBlend: 0.85
  IKReactionQuality: 2
  IKSmoothing: 0.2
  IKStretchToTarget: 0
  IKStretchCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  IKLimitSettings: []
  IKSelectiveChain: 0
  RotationOffset: {x: 0, y: 0, z: 0, w: 1}
  Curving: {x: 0, y: 0, z: 0, w: 1}
  UseCurvingCurve: 0
  CurvCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.75
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  LengthMultiplier: 1
  UseLengthMulCurve: 0
  LengthMulCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseGravityCurve: 0
  GravityCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.65
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  Gravity: {x: 0, y: 0, z: 0}
  UseWaving: 1
  CosinusAdd: 1
  FixedCycle: 0
  WavingSpeed: 3.25
  WavingRange: 0.4
  WavingAxis: {x: 0, y: 1, z: 0}
  WavingType: 0
  AlternateWave: 1
  UseWind: 0
  WindEffectPower: 1
  WindTurbulencePower: 1
  WindWorldNoisePower: 0.5
  WindEffect: {x: 0, y: 0, z: 0}
  fDebug: 0
  _gizmosEditorStartPreview: {fileID: 0}
  _gizmospesp: {fileID: 0}
  _gizmosEditorEndPreview: {fileID: 0}
  _gizmospeep: {fileID: 0}
  _editor_Title: Tail Animator 2
  _editor_IsInspectorViewingColliders: 0
  _editor_IsInspectorViewingIncludedColliders: 0
  _editor_animatorViewedCounter: 1
  TailSegments: []
  GhostParent:
    ProceduralPosition: {x: 0, y: 0, z: 0}
    ProceduralPositionWeightBlended: {x: 0, y: 0, z: 0}
    TrueTargetRotation: {x: 0, y: 0, z: 0, w: 1}
    PosRefRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosReferenceRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosition: {x: 0, y: 0, z: 0}
    BlendValue: 1
    BoneDimensionsScaled: {x: 0, y: 0, z: 0}
    BoneLengthScaled: 0
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalPositionInRoot: {x: 0, y: 0, z: 0}
    InitialLocalRotationInRoot: {x: 0, y: 0, z: 0, w: 1}
    LocalOffset: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: 0, y: 0, z: 0, w: 1}
    ColliderRadius: 1
    CollisionContactFlag: 0
    CollisionContactRelevancy: -1
    VelocityHelper: {x: 0, y: 0, z: 0}
    QVelocityHelper: {x: 0, y: 0, z: 0, w: 1}
    PreviousPush: {x: 0, y: 0, z: 0}
    Curving: {x: 0, y: 0, z: 0, w: 1}
    Gravity: {x: 0, y: 0, z: 0}
    GravityLookOffset: {x: 0, y: 0, z: 0}
    LengthMultiplier: 1
    PositionSpeed: 1
    RotationSpeed: 1
    Springiness: 0
    Slithery: 1
    Curling: 0.5
    Slippery: 1
    LastKeyframeLocalRotation: {x: 0, y: 0, z: 0, w: 0}
    LastKeyframeLocalPosition: {x: 0, y: 0, z: 0}
  GhostChild:
    ProceduralPosition: {x: 0, y: 0, z: 0}
    ProceduralPositionWeightBlended: {x: 0, y: 0, z: 0}
    TrueTargetRotation: {x: 0, y: 0, z: 0, w: 1}
    PosRefRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosReferenceRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosition: {x: 0, y: 0, z: 0}
    BlendValue: 1
    BoneDimensionsScaled: {x: 0, y: 0, z: 0}
    BoneLengthScaled: 0
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalPositionInRoot: {x: 0, y: 0, z: 0}
    InitialLocalRotationInRoot: {x: 0, y: 0, z: 0, w: 1}
    LocalOffset: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: 0, y: 0, z: 0, w: 1}
    ColliderRadius: 1
    CollisionContactFlag: 0
    CollisionContactRelevancy: -1
    VelocityHelper: {x: 0, y: 0, z: 0}
    QVelocityHelper: {x: 0, y: 0, z: 0, w: 1}
    PreviousPush: {x: 0, y: 0, z: 0}
    Curving: {x: 0, y: 0, z: 0, w: 1}
    Gravity: {x: 0, y: 0, z: 0}
    GravityLookOffset: {x: 0, y: 0, z: 0}
    LengthMultiplier: 1
    PositionSpeed: 1
    RotationSpeed: 1
    Springiness: 0
    Slithery: 1
    Curling: 0.5
    Slippery: 1
    LastKeyframeLocalRotation: {x: 0, y: 0, z: 0, w: 0}
    LastKeyframeLocalPosition: {x: 0, y: 0, z: 0}
  UseMaxDistance: 0
  DistanceFrom: {fileID: 0}
  _distanceFrom_Auto: {fileID: 0}
  MaximumDistance: 35
  MaxOutDistanceFactor: 0
  DistanceWithoutY: 0
  DistanceMeasurePoint: {x: 0, y: 0, z: 0}
  FadeDuration: 0.75
  Deflection: 0
  DeflectionStartAngle: 10
  DeflectionSmooth: 0
  DeflectionFalloff:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0.1
      outSlope: 0.1
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 5
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  DeflectOnlyCollisions: 1
  _Editor_Category: 0
  _Editor_FeaturesCategory: 0
  DrawGizmos: 1
  StartBone: {fileID: 0}
  EndBone: {fileID: 0}
  EndBoneJointOffset: {x: 0, y: 0, z: 0}
  _TransformsGhostChain: []
  _GhostChainInitCount: -1
  UpdateRate: 0
  AnimatePhysics: 0
  InterpolateRate: 1
  Prewarm: 0
  Slithery: 1
  Curling: 0.5
  Springiness: 0
  MaxStretching: 0.375
  AngleLimit: 181
  AngleLimitAxis: {x: 0, y: 0, z: 0}
  LimitAxisRange: {x: 0, y: 0}
  LimitSmoothing: 0.5
  MotionInfluence: 1
  MotionInfluenceInY: 1
  IncludeParent: 1
  UnifyBendiness: 0
  ReactionSpeed: 0.9
  Sustain: 0
  RotationRelevancy: 1
  SmoothingStyle: 1
  TimeScale: 1
  DeltaType: 4
  UpdateAsLast: 1
  DetectZeroKeyframes: 1
  StartAfterTPose: 1
  OptimizeWithMesh: {fileID: 0}
  OptimizeWithMeshes: []
  TailAnimatorAmount: 1
  DetachChildren: 0
  Axis2D: 0
  Tangle: 0
  AnimateRoll: 0
  OverrideKeyframeAnimation: 0
--- !u!1 &1991071170923560
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4489378341808832}
  - component: {fileID: 114551527330688670}
  m_Layer: 0
  m_Name: Tail 1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4489378341808832
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1991071170923560}
  serializedVersion: 2
  m_LocalRotation: {x: -0.65328246, y: 0.27059767, z: 0.6532806, w: 0.27059838}
  m_LocalPosition: {x: -0.28142852, y: 0.28142858, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4500688365064020}
  m_LocalEulerAnglesHint: {x: -90, y: 0, z: 0}
--- !u!114 &114551527330688670
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1991071170923560}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 61574e354ba34a141ae2cc1228f5eda3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  UseCollision: 0
  CollisionSpace: 1
  CollisionMode: 0
  CheapCollision: 0
  DynamicWorldCollidersInclusion: 0
  InclusionRadius: 1
  IgnoreMeshColliders: 1
  IncludedColliders: []
  IncludedColliders2D: []
  CollidersType: 0
  CollideWithOtherTails: 0
  CollideWithDisabledColliders: 1
  CollisionSlippery: 1
  ReflectCollision: 0
  CollidersScaleCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  CollidersScaleMul: 6.5
  CollisionsAutoCurve: 0.5
  IgnoredColliders: []
  IgnoredColliders2D: []
  CollidersSameLayer: 1
  CollidersAddRigidbody: 1
  RigidbodyMass: 1
  CollidersLayer: 0
  UseSlitheryCurve: 0
  SlitheryCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.75
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseCurlingCurve: 0
  CurlingCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.3
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseSpringCurve: 0
  SpringCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseSlipperyCurve: 0
  SlipperyCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UsePosSpeedCurve: 0
  PosCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseRotSpeedCurve: 0
  RotCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.9
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UsePartialBlend: 0
  BlendCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.95
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.45
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseIK: 0
  IK:
    IKWeight: 0
    IKTargetPosition: {x: 0, y: 0, z: 0}
    IKTargetRotation: {x: 0, y: 0, z: 0, w: 0}
    LastLocalDirection: {x: 0, y: 0, z: 0}
    LocalDirection: {x: 0, y: 0, z: 0}
    IKBones: []
    ContinousSolving: 0
    SyncWithAnimator: 0
    ReactionQuality: 0
    Smoothing: 0
    StretchToTarget: 0
    StretchCurve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    Use2D: 0
    Invert: 0
  IKTarget: {fileID: 0}
  IKAutoWeights: 1
  IKBaseReactionWeight: 0.65
  IKReactionWeightCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.25
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  IKAutoAngleLimits: 1
  IKAutoAngleLimit: 40
  IKContinousSolve: 0
  IkInvertOrder: 0
  IKBlend: 1
  IKAnimatorBlend: 0.85
  IKReactionQuality: 2
  IKSmoothing: 0.2
  IKStretchToTarget: 0
  IKStretchCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  IKLimitSettings: []
  IKSelectiveChain: 0
  RotationOffset: {x: 0, y: 0, z: 0, w: 1}
  Curving: {x: 0, y: 0, z: 0, w: 1}
  UseCurvingCurve: 0
  CurvCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.75
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  LengthMultiplier: 1
  UseLengthMulCurve: 0
  LengthMulCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseGravityCurve: 0
  GravityCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.65
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  Gravity: {x: 0, y: 0, z: 0}
  UseWaving: 1
  CosinusAdd: 1
  FixedCycle: 0
  WavingSpeed: 3.25
  WavingRange: 0.4
  WavingAxis: {x: 0, y: 1, z: 0}
  WavingType: 0
  AlternateWave: 1
  UseWind: 0
  WindEffectPower: 1
  WindTurbulencePower: 1
  WindWorldNoisePower: 0.5
  WindEffect: {x: 0, y: 0, z: 0}
  fDebug: 0
  _gizmosEditorStartPreview: {fileID: 0}
  _gizmospesp: {fileID: 0}
  _gizmosEditorEndPreview: {fileID: 0}
  _gizmospeep: {fileID: 0}
  _editor_Title: Tail Animator 2
  _editor_IsInspectorViewingColliders: 0
  _editor_IsInspectorViewingIncludedColliders: 0
  _editor_animatorViewedCounter: 1
  TailSegments: []
  GhostParent:
    ProceduralPosition: {x: 0, y: 0, z: 0}
    ProceduralPositionWeightBlended: {x: 0, y: 0, z: 0}
    TrueTargetRotation: {x: 0, y: 0, z: 0, w: 1}
    PosRefRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosReferenceRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosition: {x: 0, y: 0, z: 0}
    BlendValue: 1
    BoneDimensionsScaled: {x: 0, y: 0, z: 0}
    BoneLengthScaled: 0
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalPositionInRoot: {x: 0, y: 0, z: 0}
    InitialLocalRotationInRoot: {x: 0, y: 0, z: 0, w: 1}
    LocalOffset: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: 0, y: 0, z: 0, w: 1}
    ColliderRadius: 1
    CollisionContactFlag: 0
    CollisionContactRelevancy: -1
    VelocityHelper: {x: 0, y: 0, z: 0}
    QVelocityHelper: {x: 0, y: 0, z: 0, w: 1}
    PreviousPush: {x: 0, y: 0, z: 0}
    Curving: {x: 0, y: 0, z: 0, w: 1}
    Gravity: {x: 0, y: 0, z: 0}
    GravityLookOffset: {x: 0, y: 0, z: 0}
    LengthMultiplier: 1
    PositionSpeed: 1
    RotationSpeed: 1
    Springiness: 0
    Slithery: 1
    Curling: 0.5
    Slippery: 1
    LastKeyframeLocalRotation: {x: 0, y: 0, z: 0, w: 0}
    LastKeyframeLocalPosition: {x: 0, y: 0, z: 0}
  GhostChild:
    ProceduralPosition: {x: 0, y: 0, z: 0}
    ProceduralPositionWeightBlended: {x: 0, y: 0, z: 0}
    TrueTargetRotation: {x: 0, y: 0, z: 0, w: 1}
    PosRefRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosReferenceRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosition: {x: 0, y: 0, z: 0}
    BlendValue: 1
    BoneDimensionsScaled: {x: 0, y: 0, z: 0}
    BoneLengthScaled: 0
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalPositionInRoot: {x: 0, y: 0, z: 0}
    InitialLocalRotationInRoot: {x: 0, y: 0, z: 0, w: 1}
    LocalOffset: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: 0, y: 0, z: 0, w: 1}
    ColliderRadius: 1
    CollisionContactFlag: 0
    CollisionContactRelevancy: -1
    VelocityHelper: {x: 0, y: 0, z: 0}
    QVelocityHelper: {x: 0, y: 0, z: 0, w: 1}
    PreviousPush: {x: 0, y: 0, z: 0}
    Curving: {x: 0, y: 0, z: 0, w: 1}
    Gravity: {x: 0, y: 0, z: 0}
    GravityLookOffset: {x: 0, y: 0, z: 0}
    LengthMultiplier: 1
    PositionSpeed: 1
    RotationSpeed: 1
    Springiness: 0
    Slithery: 1
    Curling: 0.5
    Slippery: 1
    LastKeyframeLocalRotation: {x: 0, y: 0, z: 0, w: 0}
    LastKeyframeLocalPosition: {x: 0, y: 0, z: 0}
  UseMaxDistance: 0
  DistanceFrom: {fileID: 0}
  _distanceFrom_Auto: {fileID: 0}
  MaximumDistance: 35
  MaxOutDistanceFactor: 0
  DistanceWithoutY: 0
  DistanceMeasurePoint: {x: 0, y: 0, z: 0}
  FadeDuration: 0.75
  Deflection: 0
  DeflectionStartAngle: 10
  DeflectionSmooth: 0
  DeflectionFalloff:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0.1
      outSlope: 0.1
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 5
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  DeflectOnlyCollisions: 1
  _Editor_Category: 0
  _Editor_FeaturesCategory: 0
  DrawGizmos: 1
  StartBone: {fileID: 0}
  EndBone: {fileID: 0}
  EndBoneJointOffset: {x: 0, y: 0, z: 0}
  _TransformsGhostChain: []
  _GhostChainInitCount: -1
  UpdateRate: 0
  AnimatePhysics: 0
  InterpolateRate: 1
  Prewarm: 0
  Slithery: 1
  Curling: 0.5
  Springiness: 0
  MaxStretching: 0.375
  AngleLimit: 181
  AngleLimitAxis: {x: 0, y: 0, z: 0}
  LimitAxisRange: {x: 0, y: 0}
  LimitSmoothing: 0.5
  MotionInfluence: 1
  MotionInfluenceInY: 1
  IncludeParent: 1
  UnifyBendiness: 0
  ReactionSpeed: 0.9
  Sustain: 0
  RotationRelevancy: 1
  SmoothingStyle: 1
  TimeScale: 1
  DeltaType: 4
  UpdateAsLast: 1
  DetectZeroKeyframes: 1
  StartAfterTPose: 1
  OptimizeWithMesh: {fileID: 0}
  OptimizeWithMeshes: []
  TailAnimatorAmount: 1
  DetachChildren: 0
  Axis2D: 0
  Tangle: 0
  AnimateRoll: 0
  OverrideKeyframeAnimation: 0
