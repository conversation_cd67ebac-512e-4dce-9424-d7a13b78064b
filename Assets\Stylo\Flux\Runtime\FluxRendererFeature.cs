#if URP_INSTALLED
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Experimental.Rendering;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using UnityEngine.Rendering.RenderGraphModule;

namespace Stylo.Flux.Universal
{
    public class FluxRendererFeature : ScriptableRendererFeature
    {
        private class FluxRenderPass : ScriptableRenderPass
        {
            // Adaptive Quality System
            private float[] frameTimeHistory = new float[60]; // 1 second at 60fps
            private int frameTimeIndex = 0;
            private float targetFrameTime = 1f / 60f; // 60fps target
            private float lastQualityAdjustTime = 0f;
            private float qualityAdjustCooldown = 2f; // Wait 2 seconds between adjustments
            
            public enum QualityLevel
            {
                Low = 0,    // Mobile/weak hardware
                Medium = 1, // Console/mid-range PC
                High = 2,   // High-end PC
                Ultra = 3   // Top-tier hardware
            }
            
            private QualityLevel currentQuality = QualityLevel.Medium;
            private QualityLevel targetQuality = QualityLevel.Medium;
            // PassData structure for Unity 6 Render Graph
            private class FluxPassData
            {
                // Input textures
                internal TextureHandle sourceTexture;
                internal TextureHandle motionVectorTexture;
                internal TextureHandle depthTexture;
                internal TextureHandle previousFrameTexture;

                // Output texture
                internal TextureHandle outputTexture;

                // Material and parameters
                internal Material fluxMaterial;
                internal int passIndex;
                internal Vector4 screenTexelSize;
                internal Vector4 downscaledTexelSize;
                internal float colorCrunch;
                internal float sharpening;
                internal float reprojectPercent;
                internal float reprojectSpeed;
                internal float reprojectLengthInfluence;
                internal float keyframeResetRate;
                internal float motionVectorCorruption;
                internal float errorAccumulation;
                internal float dctCorruption;
                internal float chromaCorruption;
                internal float glitchTransition;
                internal float feedbackIntensity;
                internal float multiScaleCorruption;
                internal Texture corruptionMask;
                internal bool doReprojection;
                internal bool onlyStenciled;
                internal bool visualizeMotionVectors;

                // Consolidated motion parameters
                internal float motionAmplification;
                internal float motionThreshold;
                internal float cameraObjectMotionBalance;
                internal float motionSmoothing;

                // Pixel flow and trailing parameters
                internal float trailIntensity;
                internal float trailSmoothness;
                internal float trailPersistence;
                internal float flowSpread;

                // New JPEG compression parameters
                internal float jpegQuality;
                internal float luminanceQuantization;
                internal float chrominanceQuantization;
                internal bool chromaSubsampling;
                internal float ringingArtifacts;
                internal float mosquitoNoise;
                internal float edgeSensitivity;


                // Old enhanced motion processing parameters removed - replaced by Unity6 Guide

                // Brightness Control parameters
                internal float noiseTransparency;
                internal float maxNoiseBrightness;
                internal float brightnessThreshold;
                internal float brightAreaMasking;

                // Debug parameters
                internal bool debugCompressionArtifacts;

                // Mesh for fullscreen triangle
                internal Mesh fullscreenTriangle;
            }

            public FluxEffect v;

            float colorCrunch;
            int downscaling;
            FluxEffect._BlockSize blockSize;
            float reprojectPercent;
            float reprojectLengthInfluence;
            float keyframeResetRate;
            float motionVectorCorruption;
            float errorAccumulation;
            float dctCorruption;
            float chromaCorruption;
            float glitchTransition;
            float feedbackIntensity;
            float multiScaleCorruption;

            // Consolidated motion parameters
            float motionAmplification;
            float motionThreshold;
            float cameraObjectMotionBalance;
            float motionSmoothing;

            // Pixel flow and trailing parameters
            float trailIntensity;
            float trailSmoothness;
            float trailPersistence;
            float flowSpread;

            // New JPEG compression parameters
            float jpegQuality;
            float luminanceQuantization;
            float chrominanceQuantization;
            bool chromaSubsampling;
            float ringingArtifacts;
            float mosquitoNoise;
            float edgeSensitivity;


            // Old enhanced motion processing parameters removed - replaced by Unity6 Guide



            void CheckParameters()
            {
                // Update adaptive quality based on performance
                UpdateAdaptiveQuality();
                
                // Apply quality scaling to parameters
                float qualityScale = GetQualityScale();
                
                colorCrunch = v.ColorCrunch.value * v.EffectIntensity.value;
                downscaling = Mathf.Max(1, Mathf.CeilToInt(v.Downscaling.value * v.EffectIntensity.value * GetDownscalingQualityMultiplier()));
                blockSize = GetQualityAdjustedBlockSize();
                reprojectPercent = v.ReprojectBaseNoise.value * v.EffectIntensity.value;
                reprojectLengthInfluence = v.ReprojectLengthInfluence.value * v.EffectIntensity.value;
                keyframeResetRate = v.KeyframeResetRate.value;
                motionVectorCorruption = v.MotionVectorCorruption.value * qualityScale;
                errorAccumulation = v.ErrorAccumulation.value * qualityScale;
                dctCorruption = v.DCTCorruption.value * qualityScale;
                chromaCorruption = v.ChromaCorruption.value * qualityScale;
                glitchTransition = v.GlitchTransition.value;
                feedbackIntensity = v.FeedbackIntensity.value * qualityScale;
                multiScaleCorruption = v.MultiScaleCorruption.value * qualityScale;

                // Consolidated motion parameters
                motionAmplification = v.MotionAmplification.value;
                motionThreshold = v.MotionThreshold.value;
                cameraObjectMotionBalance = v.CameraObjectMotionBalance.value;
                motionSmoothing = v.MotionSmoothing.value;

                // Pixel flow and trailing parameters (quality scaled)
                trailIntensity = v.TrailIntensity.value * qualityScale;
                trailSmoothness = v.TrailSmoothness.value;
                trailPersistence = v.TrailPersistence.value * qualityScale;
                flowSpread = v.FlowSpread.value * qualityScale;

                // New JPEG compression parameters
                jpegQuality = v.JPEGQuality.value;
                luminanceQuantization = v.LuminanceQuantization.value;
                chrominanceQuantization = v.ChrominanceQuantization.value;
                chromaSubsampling = v.ChromaSubsampling.value;
                ringingArtifacts = v.RingingArtifacts.value * qualityScale;
                mosquitoNoise = v.MosquitoNoise.value * qualityScale;
                edgeSensitivity = v.EdgeSensitivity.value;
            }
            
            void UpdateAdaptiveQuality()
            {
                // Record current frame time
                frameTimeHistory[frameTimeIndex] = Time.unscaledDeltaTime;
                frameTimeIndex = (frameTimeIndex + 1) % frameTimeHistory.Length;
                
                // Don't adjust too frequently
                if (Time.unscaledTime - lastQualityAdjustTime < qualityAdjustCooldown)
                    return;
                
                // Calculate average frame time over last second
                float averageFrameTime = 0f;
                for (int i = 0; i < frameTimeHistory.Length; i++)
                    averageFrameTime += frameTimeHistory[i];
                averageFrameTime /= frameTimeHistory.Length;
                
                // Determine target quality based on performance
                QualityLevel newTargetQuality = currentQuality;
                
                if (averageFrameTime > targetFrameTime * 1.5f) // If we're significantly above target
                {
                    // Reduce quality
                    if (currentQuality > QualityLevel.Low)
                        newTargetQuality = currentQuality - 1;
                }
                else if (averageFrameTime < targetFrameTime * 0.8f) // If we have headroom
                {
                    // Increase quality
                    if (currentQuality < QualityLevel.Ultra)
                        newTargetQuality = currentQuality + 1;
                }
                
                if (newTargetQuality != currentQuality)
                {
                    currentQuality = newTargetQuality;
                    lastQualityAdjustTime = Time.unscaledTime;
                    
                    if (Application.isEditor)
                        UnityEngine.Debug.Log($"[Flux] Adaptive Quality: {currentQuality} (avg frame time: {averageFrameTime * 1000:F1}ms)");
                }
            }
            
            float GetQualityScale()
            {
                // Mobile gets more aggressive scaling
                if (IsMobilePlatform())
                {
                    switch (currentQuality)
                    {
                        case QualityLevel.Low: return 0.3f;    // Very reduced effects
                        case QualityLevel.Medium: return 0.5f;  // Half effects
                        case QualityLevel.High: return 0.7f;    // Reduced effects
                        case QualityLevel.Ultra: return 0.9f;   // Near full effects
                        default: return 0.5f;
                    }
                }
                
                // Desktop/console standard scaling
                switch (currentQuality)
                {
                    case QualityLevel.Low: return 0.5f;
                    case QualityLevel.Medium: return 0.75f;
                    case QualityLevel.High: return 1.0f;
                    case QualityLevel.Ultra: return 1.2f;
                    default: return 1.0f;
                }
            }
            
            float GetDownscalingQualityMultiplier()
            {
                // Mobile needs much more aggressive downscaling
                if (IsMobilePlatform())
                {
                    switch (currentQuality)
                    {
                        case QualityLevel.Low: return 2.5f;    // Very aggressive downscaling
                        case QualityLevel.Medium: return 2.0f; // Aggressive downscaling  
                        case QualityLevel.High: return 1.7f;   // Moderate downscaling
                        case QualityLevel.Ultra: return 1.4f;  // Mild downscaling
                        default: return 2.0f;
                    }
                }
                
                // Desktop/console standard scaling
                switch (currentQuality)
                {
                    case QualityLevel.Low: return 1.5f; // More aggressive downscaling
                    case QualityLevel.Medium: return 1.2f;
                    case QualityLevel.High: return 1.0f;
                    case QualityLevel.Ultra: return 0.8f; // Less downscaling for quality
                    default: return 1.0f;
                }
            }
            
            FluxEffect._BlockSize GetQualityAdjustedBlockSize()
            {
                var baseBlockSize = (FluxEffect._BlockSize)Mathf.RoundToInt((int)v.BlockSize.value * v.EffectIntensity.value);
                
                // Mobile-first optimization: Force smaller blocks on mobile platforms
                if (IsMobilePlatform())
                {
                    // Mobile devices should never use large blocks
                    if (baseBlockSize == FluxEffect._BlockSize._32x32) return FluxEffect._BlockSize._8x8;
                    if (baseBlockSize == FluxEffect._BlockSize._16x16) return FluxEffect._BlockSize._4x4;
                    return baseBlockSize;
                }
                
                // Desktop/console quality adjustment
                switch (currentQuality)
                {
                    case QualityLevel.Low:
                        // Force smaller blocks for better performance
                        if (baseBlockSize == FluxEffect._BlockSize._32x32) return FluxEffect._BlockSize._16x16;
                        if (baseBlockSize == FluxEffect._BlockSize._16x16) return FluxEffect._BlockSize._8x8;
                        break;
                    case QualityLevel.Ultra:
                        // Allow larger blocks for more dramatic effects if performance allows
                        break;
                }
                
                return baseBlockSize;
            }

            bool DoOnlyStenciled => v.OnlyStenciled.value && !v.VisualizeMotionVectors.value;
            bool DoReprojection
            {
                get
                {
                    bool hasReprojectionParams = (reprojectPercent > 0f || reprojectLengthInfluence > 0f || motionAmplification > 0f || v.VisualizeMotionVectors.value);
                    bool notSceneView = !cameraData.isSceneViewCamera;
                    bool result = hasReprojectionParams && notSceneView;

                    // DEBUG: Log reprojection status for debugging motion vector issues
                    if (v.VisualizeMotionVectors.value)
                    {
                        UnityEngine.Debug.Log($"[Flux] DoReprojection = {result} (reprojectPercent={reprojectPercent}, lengthInfluence={reprojectLengthInfluence}, motionAmplification={motionAmplification}, visualize={v.VisualizeMotionVectors.value}, isSceneView={cameraData.isSceneViewCamera})");
                    }

                    return result;
                }
            }

            // Unity 6 Render Graph version that takes camera data as parameter
            bool GetDoReprojection(UniversalCameraData cameraData) => (reprojectPercent > 0f || reprojectLengthInfluence > 0f || motionAmplification > 0f || v.VisualizeMotionVectors.value) && !cameraData.isSceneViewCamera;

            Material mat;

            public void Setup(Shader shader, ScriptableRenderer renderer, RenderingData renderingData)
            {
                if (mat == null) mat = CoreUtils.CreateEngineMaterial(shader);

                // Unity6 Guide materials no longer needed - integrated directly into Flux shader
                // Force recompilation to clear cached errors

            }

            public void Cleanup()
            {
                CoreUtils.Destroy(mat);

                // Unity 6 Render Graph cleanup
                if (prevFrameRTHandle != null)
                {
                    prevFrameRTHandle.Release();
                    prevFrameRTHandle = null;
                }
            }

            private void FetchVolumeComponent()
            {
                if (v == null)
                    v = VolumeManager.instance.stack.GetComponent<FluxEffect>();
            }

            RenderTargetIdentifier source;
            RenderTargetIdentifier sourceDepthStencil;
            CameraData cameraData;
            public override void OnCameraSetup(CommandBuffer cmd, ref RenderingData renderingData)
            {
                FetchVolumeComponent();

#if UNITY_2022_1_OR_NEWER
                source = renderingData.cameraData.renderer.cameraColorTargetHandle; //implicit conversion, hopefully will exist for forseable future
                sourceDepthStencil = renderingData.cameraData.renderer.cameraColorTargetHandle.rt.depth > 0 ? renderingData.cameraData.renderer.cameraColorTargetHandle : renderingData.cameraData.renderer.cameraDepthTargetHandle;
#else
                    source = renderingData.cameraData.renderer.cameraColorTarget;
                    sourceDepthStencil = renderingData.cameraData.renderer.cameraDepthTarget != new RenderTargetIdentifier(BuiltinRenderTextureType.CameraTarget)/*(basically -1)*/ ? renderingData.cameraData.renderer.cameraDepthTarget : renderingData.cameraData.renderer.cameraColorTarget;
#endif

                cameraData = renderingData.cameraData;

                // Mobile optimization: Adjust render pass event based on platform
                if (IsMobilePlatform())
                {
                    // On mobile, render after transparents to reduce bandwidth on TBDR
                    renderPassEvent = RenderPassEvent.AfterRenderingTransparents;
                }
                else
                {
                    // On desktop/console, before post-processing for better integration
                    renderPassEvent = RenderPassEvent.BeforeRenderingPostProcessing;
                }

                // Configure input requirements based on features used
                ScriptableRenderPassInput inputRequirements = ScriptableRenderPassInput.Color;

                // Mobile optimization: Only request expensive inputs when absolutely necessary
                bool needsMotionVectors = DoReprojection && !IsMobilePlatform(); // Disable motion vectors on mobile
                bool needsDepthTexture = DoOnlyStenciled || (colorCrunch > 0f && !v.DontCrunchSkybox.value);
                
                if (needsMotionVectors)
                    inputRequirements |= ScriptableRenderPassInput.Motion;

                if (needsDepthTexture)
                    inputRequirements |= ScriptableRenderPassInput.Depth;

                ConfigureInput(inputRequirements);
            }
            
            private bool IsMobilePlatform()
            {
                return Application.isMobilePlatform || 
                       SystemInfo.graphicsDeviceType == UnityEngine.Rendering.GraphicsDeviceType.OpenGLES2 ||
                       SystemInfo.graphicsDeviceType == UnityEngine.Rendering.GraphicsDeviceType.OpenGLES3;
            }

            public override void Configure(CommandBuffer cmd, RenderTextureDescriptor cameraTextureDescriptor)
            {
                if (mat == null) return;

                FetchVolumeComponent();

                if (!v.IsActive()) return;

                CheckParameters();
                UpdateMaterialProperties();
                UpdateShaderKeywords();
            }

            void UpdateMaterialProperties()
            {
                var width = cameraData.cameraTargetDescriptor.width;
                var height = cameraData.cameraTargetDescriptor.height;

                downscaledWidth = Mathf.FloorToInt(width / downscaling / 2f) * 2;
                downscaledHeight = Mathf.FloorToInt(height / downscaling / 2f) * 2;

                // Cache values for render graph
                screenTexelSize = new Vector4(1f / width, 1f / height, width, height);
                downscaledTexelSize = new Vector4(1f / downscaledWidth, 1f / downscaledHeight, downscaledWidth, downscaledHeight);

                mat.SetVector("_Screen_TexelSize", screenTexelSize);
                mat.SetVector("_Downscaled_TexelSize", downscaledTexelSize);

                mat.SetFloat("_ColorCrunch", colorCrunch);
                mat.SetFloat("_Sharpening", v.Oversharpening.value);

                mat.SetFloat("_ReprojectPercent", reprojectPercent);
                mat.SetFloat("_ReprojectSpeed", v.ReprojectBaseRerollSpeed.value);
                mat.SetFloat("_ReprojectLengthInfluence", reprojectLengthInfluence);
                mat.SetFloat("_KeyframeResetRate", keyframeResetRate);
                mat.SetFloat("_MotionVectorCorruption", motionVectorCorruption);
                mat.SetFloat("_ErrorAccumulation", errorAccumulation);
                mat.SetFloat("_DCTCorruption", dctCorruption);
                mat.SetFloat("_ChromaCorruption", chromaCorruption);
                mat.SetFloat("_GlitchTransition", glitchTransition);
                mat.SetFloat("_FeedbackIntensity", feedbackIntensity);
                mat.SetFloat("_MultiScaleCorruption", multiScaleCorruption);

                // Consolidated motion parameters
                mat.SetFloat("_MotionAmplification", motionAmplification);
                mat.SetFloat("_MotionThreshold", motionThreshold);
                mat.SetFloat("_CameraObjectMotionBalance", cameraObjectMotionBalance);
                mat.SetFloat("_MotionSmoothing", motionSmoothing);

                // Pixel flow and trailing parameters
                mat.SetFloat("_TrailIntensity", trailIntensity);
                mat.SetFloat("_TrailSmoothness", trailSmoothness);
                mat.SetFloat("_TrailPersistence", trailPersistence);
                mat.SetFloat("_FlowSpread", flowSpread);

                // New JPEG compression parameters
                mat.SetFloat("_JPEGQuality", jpegQuality);
                mat.SetFloat("_LuminanceQuantization", luminanceQuantization);
                mat.SetFloat("_ChrominanceQuantization", chrominanceQuantization);
                mat.SetFloat("_RingingArtifacts", ringingArtifacts);
                mat.SetFloat("_MosquitoNoise", mosquitoNoise);
                mat.SetFloat("_EdgeSensitivity", edgeSensitivity);

                // Old Unity6 Render Graph Enhancement parameters removed

                if (v.CorruptionMask.value != null)
                {
                    mat.SetTexture("_CorruptionMask", v.CorruptionMask.value);
                    mat.EnableKeyword("_CORRUPTIONMASK");
                }
                else
                {
                    mat.DisableKeyword("_CORRUPTIONMASK");
                }
            }

            // Unity 6 Render Graph version of UpdateMaterialProperties
            void UpdateMaterialProperties(UniversalCameraData cameraData)
            {
                var width = cameraData.cameraTargetDescriptor.width;
                var height = cameraData.cameraTargetDescriptor.height;

                downscaledWidth = Mathf.FloorToInt(width / downscaling / 2f) * 2;
                downscaledHeight = Mathf.FloorToInt(height / downscaling / 2f) * 2;

                // Cache values for render graph
                screenTexelSize = new Vector4(1f / width, 1f / height, width, height);
                downscaledTexelSize = new Vector4(1f / downscaledWidth, 1f / downscaledHeight, downscaledWidth, downscaledHeight);
            }

            // OPTIMIZED KEYWORD SYSTEM - Fewer variants, better performance
            string[] keywords = new string[4]; // Reduced from 7 to 4
            void UpdateShaderKeywords()
            {
                // Core multi_compile keywords (always set)
                keywords[0] = blockSize == FluxEffect._BlockSize._2x2 ? "BLOCK_SIZE_2" :
                             blockSize == FluxEffect._BlockSize._4x4 ? "BLOCK_SIZE_4" :
                             blockSize == FluxEffect._BlockSize._8x8 ? "BLOCK_SIZE_8" :
                             blockSize == FluxEffect._BlockSize._16x16 ? "BLOCK_SIZE_16" :
                             blockSize == FluxEffect._BlockSize._32x32 ? "BLOCK_SIZE_32" : "BLOCK_SIZE_4";
                             
                keywords[1] = DoReprojection ? "REPROJECTION" : "";
                keywords[2] = (ringingArtifacts > 0.001f || mosquitoNoise > 0.001f) ? "COMPRESSION_ARTIFACTS" : "";
                keywords[3] = ""; // Reserved for future use
                
                // Set core keywords
                mat.shaderKeywords = keywords;
                
                // Handle shader_feature keywords separately (conditional compilation)
                if (!v.DontCrunchSkybox.value)
                    mat.EnableKeyword("COLOR_CRUNCH_SKYBOX");
                else
                    mat.DisableKeyword("COLOR_CRUNCH_SKYBOX");
                    
                if (chromaSubsampling)
                    mat.EnableKeyword("CHROMA_SUBSAMPLING");
                else
                    mat.DisableKeyword("CHROMA_SUBSAMPLING");
                    
                if (v.VisualizeMotionVectors.value)
                    mat.EnableKeyword("VIZ_MOTION_VECTORS");
                else
                    mat.DisableKeyword("VIZ_MOTION_VECTORS");
            }

            // Unity 6 Render Graph version with optimized keywords
            void UpdateShaderKeywords(bool doReprojection)
            {
                // Core multi_compile keywords
                keywords[0] = blockSize == FluxEffect._BlockSize._2x2 ? "BLOCK_SIZE_2" :
                             blockSize == FluxEffect._BlockSize._4x4 ? "BLOCK_SIZE_4" :
                             blockSize == FluxEffect._BlockSize._8x8 ? "BLOCK_SIZE_8" :
                             blockSize == FluxEffect._BlockSize._16x16 ? "BLOCK_SIZE_16" :
                             blockSize == FluxEffect._BlockSize._32x32 ? "BLOCK_SIZE_32" : "BLOCK_SIZE_4";
                             
                keywords[1] = doReprojection ? "REPROJECTION" : "";
                keywords[2] = (ringingArtifacts > 0.001f || mosquitoNoise > 0.001f) ? "COMPRESSION_ARTIFACTS" : "";
                keywords[3] = ""; // Reserved
                
                mat.shaderKeywords = keywords;
                
                // Shader feature keywords
                if (!v.DontCrunchSkybox.value)
                    mat.EnableKeyword("COLOR_CRUNCH_SKYBOX");
                else
                    mat.DisableKeyword("COLOR_CRUNCH_SKYBOX");
                    
                if (chromaSubsampling)
                    mat.EnableKeyword("CHROMA_SUBSAMPLING");
                else
                    mat.DisableKeyword("CHROMA_SUBSAMPLING");
                    
                if (v.VisualizeMotionVectors.value)
                    mat.EnableKeyword("VIZ_MOTION_VECTORS");
                else
                    mat.DisableKeyword("VIZ_MOTION_VECTORS");
            }

            static class Pass
            {
                public const int Downscale = 0;
                public const int Encode = 1;
                public const int Decode = 2;
                public const int UpscalePull = 3;
                public const int UpscalePullStenciled = 4;
                public const int CopyToPrev = 5;
            }
            // Legacy fields for compatibility mode
            RenderTexture prevScreenTex;
            int prevWidth = -1;
            int prevHeight = -1;

            // Unity 6 Render Graph fields
            private RTHandle prevFrameRTHandle;

            // Cached properties for render graph
            private Vector4 screenTexelSize;
            private Vector4 downscaledTexelSize;
            private int downscaledWidth;
            private int downscaledHeight;
            public override void Execute(ScriptableRenderContext context, ref RenderingData renderingData)
            {
                if (mat == null)
                {
                    Debug.LogError("Flux material has not been correctly initialized...");
                    return;
                }
                if (!v.IsActive()) return;

                // Skip Flux effect entirely for Scene View cameras
                if (cameraData.isSceneViewCamera) return;
                
                // Mobile optimization: Skip expensive effects if framerate is too low
                if (IsMobilePlatform() && Time.unscaledDeltaTime > 1f / 30f) // Below 30 FPS
                {
                    return; // Skip this frame to recover performance
                }
                
                var cmd = CommandBufferPool.Get("Flux");

                // Have to refetch color target here because post-processing will use texture B instead of A and in OnCameraSetup() it always returns A. So events like AfterRenderingPostProcessing start working.
#if UNITY_2022_1_OR_NEWER
                source = renderingData.cameraData.renderer.cameraColorTargetHandle;
#else
                    source = renderingData.cameraData.renderer.cameraColorTarget;
#endif

                var width = cameraData.cameraTargetDescriptor.width;
                var height = cameraData.cameraTargetDescriptor.height;

                if (prevWidth != width || prevHeight != height)
                {
                    prevWidth = width;
                    prevHeight = height;
                    if (prevScreenTex != null) RenderTexture.ReleaseTemporary(prevScreenTex);
                    prevScreenTex = RenderTexture.GetTemporary(prevWidth, prevHeight, 0, GraphicsFormat.R32G32B32A32_SFloat);
                    prevScreenTex.name = "_PrevScreen RT";
                }

                {
                    int widthDownscaled = Mathf.FloorToInt(width / downscaling / 2f) * 2;
                    int heightDownscaled = Mathf.FloorToInt(height / downscaling / 2f) * 2;

                    var downscaledTex = Shader.PropertyToID("_FluxScreenDownscaled");
                    cmd.GetTemporaryRT(downscaledTex, widthDownscaled, heightDownscaled, 0, FilterMode.Bilinear, GraphicsFormat.R32G32B32A32_SFloat);

                    RenderWith(source, downscaledTex, cmd, mat, Pass.Downscale);

                    var blocksTex = Shader.PropertyToID("_FluxBlocks");
                    cmd.GetTemporaryRT(blocksTex, widthDownscaled, heightDownscaled, 0, FilterMode.Bilinear, GraphicsFormat.R32G32B32A32_SFloat);

                    RenderWith(downscaledTex, blocksTex, cmd, mat, Pass.Encode);
                    RenderWith(blocksTex, downscaledTex, cmd, mat, Pass.Decode);

                    cmd.SetGlobalTexture("_PrevScreen", prevScreenTex);
                    RenderWith(downscaledTex, source, cmd, mat, DoOnlyStenciled ? Pass.UpscalePullStenciled : Pass.UpscalePull, rebindStencil: true);

                    if (DoReprojection)
                        RenderWith(source, prevScreenTex, cmd, mat, Pass.CopyToPrev);

                    cmd.ReleaseTemporaryRT(downscaledTex);
                    cmd.ReleaseTemporaryRT(blocksTex);
                }

                context.ExecuteCommandBuffer(cmd);
                CommandBufferPool.Release(cmd);
            }

            // Unity 6 Render Graph implementation
            // Performance benefits: Automatic resource management, pass merging on mobile TBDR, optimized memory usage
            public override void RecordRenderGraph(RenderGraph renderGraph, ContextContainer frameData)
            {
                if (mat == null)
                {
                    Debug.LogError("Flux material has not been correctly initialized...");
                    return;
                }

                FetchVolumeComponent();
                if (v == null) return;
                if (!v.IsActive()) return;

                var resourceData = frameData.Get<UniversalResourceData>();
                var cameraData = frameData.Get<UniversalCameraData>();

                // Skip Flux effect entirely for Scene View cameras
                if (cameraData.isSceneViewCamera) return;

                CheckParameters();
                UpdateMaterialProperties(cameraData);

                // Import persistent texture for temporal reprojection
                bool doReprojection = GetDoReprojection(cameraData);
                UpdateShaderKeywords(doReprojection);

                // Skip if rendering to back buffer
                if (resourceData.isActiveTargetBackBuffer)
                    return;

                // CRITICAL FIX: Ensure previous frame texture is always available when reprojection is enabled
                TextureHandle prevFrameTexture = TextureHandle.nullHandle;
                if (doReprojection)
                {
                    // Get texture descriptor for current frame
                    var desc = renderGraph.GetTextureDesc(resourceData.activeColorTexture);

                    // TEXTURE FORMAT FIX: Force R32G32B32A32_SFloat to match JPG Bitcrunch exactly
                    // JPG Bitcrunch always uses this format for previous frame texture
                    GraphicsFormat previousFrameFormat = GraphicsFormat.R32G32B32A32_SFloat;

                    // Debug logging for format verification - disabled to reduce console spam
                    /*if (Application.isEditor && desc.format != previousFrameFormat && Time.frameCount % 60 == 0)
                    {
                        Debug.LogWarning($"[Flux] Camera color format {desc.format} differs from JPG Bitcrunch standard {previousFrameFormat}. " +
                                        "Using JPG Bitcrunch format for previous frame texture to ensure consistency.");
                    }*/

                    // Create or update persistent texture BEFORE it's needed with correct format
                    UpdatePersistentTexture(desc.width, desc.height, previousFrameFormat);

                    // Import the persistent texture (now guaranteed to exist)
                    prevFrameTexture = renderGraph.ImportTexture(prevFrameRTHandle);

                    // Debug logging to verify fix - disabled to reduce console spam
                    /*if (Application.isEditor && Time.frameCount % 60 == 0) // Log every 60 frames to avoid spam
                    {
                        Debug.Log($"[Flux] Previous frame texture available: {prevFrameTexture.IsValid()}, " +
                                 $"RTHandle: {prevFrameRTHandle != null}, " +
                                 $"Size: {desc.width}x{desc.height}, Format: {previousFrameFormat}");
                    }*/
                }

                // Phase 2: Complete multi-pass implementation
                // Pass 1: Downscale
                TextureHandle downscaledTexture = AddDownscalePass(renderGraph, resourceData.activeColorTexture, doReprojection);

                // Pass 2: Encode - Apply JPEG-style block compression
                TextureHandle encodedTexture = AddEncodePass(renderGraph, downscaledTexture, resourceData.activeDepthTexture, doReprojection);

                // Pass 3: Decode - Decompress blocks with artifacts
                TextureHandle decodedTexture = AddDecodePass(renderGraph, encodedTexture, doReprojection);

                // Pass 3.5: Unity6 Datamosh Guide Processing (Integrated into Upscale)
                TextureHandle processedTexture = decodedTexture;
                // Unity6 Guide is now integrated directly into the Upscale pass via shader keywords

                // Pass 4: Upscale - Return to full resolution with sharpening and reprojection
                // CRITICAL FIX: Only pass motion vectors when reprojection is actually enabled AND not on mobile
                TextureHandle motionVectorTexture = (doReprojection && !IsMobilePlatform()) ? resourceData.motionVectorColor : TextureHandle.nullHandle;
                AddUpscalePass(renderGraph, processedTexture, resourceData.activeColorTexture,
                              motionVectorTexture, prevFrameTexture, resourceData.activeDepthTexture, doReprojection);

                // Pass 5: Copy to Previous - Store frame for temporal reprojection (if enabled)
                if (doReprojection)
                    AddCopyToPrevPass(renderGraph, resourceData.activeColorTexture, doReprojection);
            }

            // Unity6 Datamosh Guide - REMOVED: Now integrated directly into Upscale pass
            // This function is no longer used to avoid global state modification errors


            // Unity 6 Render Graph pass implementations
            private TextureHandle AddDownscalePass(RenderGraph renderGraph, TextureHandle sourceTexture, bool doReprojection)
            {
                using (var builder = renderGraph.AddRasterRenderPass<FluxPassData>("Flux Downscale", out var passData))
                {
                    // Create output texture with downscaled dimensions
                    var desc = renderGraph.GetTextureDesc(sourceTexture);
                    desc.width = downscaledWidth;
                    desc.height = downscaledHeight;
                    desc.depthBufferBits = 0;
                    desc.format = GraphicsFormat.R32G32B32A32_SFloat;
                    desc.name = "Flux_Downscaled";
                    TextureHandle outputTexture = renderGraph.CreateTexture(desc);

                    // Configure pass data
                    passData.sourceTexture = sourceTexture;
                    passData.outputTexture = outputTexture;
                    passData.motionVectorTexture = TextureHandle.nullHandle; // Downscale pass doesn't need motion vectors
                    passData.fluxMaterial = mat;
                    passData.passIndex = Pass.Downscale;
                    passData.screenTexelSize = screenTexelSize;
                    passData.downscaledTexelSize = downscaledTexelSize;
                    passData.colorCrunch = colorCrunch;
                    passData.sharpening = v.Oversharpening.value;
                    passData.reprojectPercent = reprojectPercent;
                    passData.reprojectSpeed = v.ReprojectBaseRerollSpeed.value;
                    passData.reprojectLengthInfluence = reprojectLengthInfluence;
                    passData.keyframeResetRate = keyframeResetRate;
                    passData.motionVectorCorruption = motionVectorCorruption;
                    passData.errorAccumulation = errorAccumulation;
                    passData.dctCorruption = dctCorruption;
                    passData.chromaCorruption = chromaCorruption;
                    passData.glitchTransition = glitchTransition;
                    passData.feedbackIntensity = feedbackIntensity;
                    passData.multiScaleCorruption = multiScaleCorruption;
                    passData.corruptionMask = v.CorruptionMask.value;
                    passData.doReprojection = false; // Downscale pass doesn't do reprojection
                    passData.onlyStenciled = DoOnlyStenciled;
                    passData.visualizeMotionVectors = v.VisualizeMotionVectors.value;

                    // Consolidated motion parameters
                    passData.motionAmplification = motionAmplification;
                    passData.motionThreshold = motionThreshold;
                    passData.cameraObjectMotionBalance = cameraObjectMotionBalance;
                    passData.motionSmoothing = motionSmoothing;

                    // Pixel flow and trailing parameters
                    passData.trailIntensity = trailIntensity;
                    passData.trailSmoothness = trailSmoothness;
                    passData.trailPersistence = trailPersistence;
                    passData.flowSpread = flowSpread;

                    // New JPEG compression parameters
                    passData.jpegQuality = jpegQuality;
                    passData.luminanceQuantization = luminanceQuantization;
                    passData.chrominanceQuantization = chrominanceQuantization;
                    passData.chromaSubsampling = chromaSubsampling;
                    passData.ringingArtifacts = ringingArtifacts;
                    passData.mosquitoNoise = mosquitoNoise;
                    passData.edgeSensitivity = edgeSensitivity;


                    // Brightness Control parameters
                    passData.noiseTransparency = v.NoiseTransparency.value;
                    passData.maxNoiseBrightness = v.MaxNoiseBrightness.value;
                    passData.brightnessThreshold = v.BrightnessThreshold.value;
                    passData.brightAreaMasking = v.BrightAreaMasking.value;

                    // Debug parameters
                    passData.debugCompressionArtifacts = v.DebugCompressionArtifacts.value;

                    // Old Unity6 Render Graph Enhancement parameters removed

                    passData.fullscreenTriangle = FullscreenTriangle;

                    // Declare inputs/outputs
                    builder.UseTexture(passData.sourceTexture, AccessFlags.Read);
                    builder.SetRenderAttachment(passData.outputTexture, 0, AccessFlags.Write);

                    // Enable render graph optimizations
                    builder.AllowPassCulling(false); // Keep pass for effect consistency

                    // Set execution function
                    builder.SetRenderFunc((FluxPassData data, RasterGraphContext context) =>
                        ExecuteFluxPass(data, context));

                    return outputTexture;
                }
            }

            private TextureHandle AddEncodePass(RenderGraph renderGraph, TextureHandle sourceTexture, TextureHandle depthTexture, bool doReprojection)
            {
                using (var builder = renderGraph.AddRasterRenderPass<FluxPassData>("Flux Encode", out var passData))
                {
                    // Create output texture with same dimensions as input (downscaled)
                    var desc = renderGraph.GetTextureDesc(sourceTexture);
                    desc.name = "Flux_Encoded";
                    TextureHandle outputTexture = renderGraph.CreateTexture(desc);

                    // Configure pass data
                    passData.sourceTexture = sourceTexture;
                    passData.outputTexture = outputTexture;
                    passData.motionVectorTexture = TextureHandle.nullHandle; // Encode pass doesn't need motion vectors

                    // MSAA FIX: Only pass depth texture if it's not multisampled to avoid binding issues
                    if (depthTexture.IsValid())
                    {
                        var depthDesc = renderGraph.GetTextureDesc(depthTexture);
                        if (depthDesc.msaaSamples == MSAASamples.None)
                        {
                            passData.depthTexture = depthTexture;
                        }
                        else
                        {
                            passData.depthTexture = TextureHandle.nullHandle; // Don't pass multisampled depth
                        }
                    }
                    else
                    {
                        passData.depthTexture = TextureHandle.nullHandle;
                    }

                    passData.fluxMaterial = mat;
                    passData.passIndex = Pass.Encode;
                    passData.screenTexelSize = screenTexelSize;
                    passData.downscaledTexelSize = downscaledTexelSize;
                    passData.colorCrunch = colorCrunch;
                    passData.sharpening = v.Oversharpening.value;
                    passData.reprojectPercent = reprojectPercent;
                    passData.reprojectSpeed = v.ReprojectBaseRerollSpeed.value;
                    passData.reprojectLengthInfluence = reprojectLengthInfluence;
                    passData.keyframeResetRate = keyframeResetRate;
                    passData.motionVectorCorruption = motionVectorCorruption;
                    passData.errorAccumulation = errorAccumulation;
                    passData.dctCorruption = dctCorruption;
                    passData.chromaCorruption = chromaCorruption;
                    passData.glitchTransition = glitchTransition;
                    passData.feedbackIntensity = feedbackIntensity;
                    passData.multiScaleCorruption = multiScaleCorruption;
                    passData.corruptionMask = v.CorruptionMask.value;
                    passData.doReprojection = false; // Encode pass doesn't do reprojection
                    passData.onlyStenciled = DoOnlyStenciled;
                    passData.visualizeMotionVectors = v.VisualizeMotionVectors.value;

                    // Consolidated motion parameters
                    passData.motionAmplification = motionAmplification;
                    passData.motionThreshold = motionThreshold;
                    passData.cameraObjectMotionBalance = cameraObjectMotionBalance;
                    passData.motionSmoothing = motionSmoothing;

                    // Pixel flow and trailing parameters
                    passData.trailIntensity = trailIntensity;
                    passData.trailSmoothness = trailSmoothness;
                    passData.trailPersistence = trailPersistence;
                    passData.flowSpread = flowSpread;

                    // New JPEG compression parameters
                    passData.jpegQuality = jpegQuality;
                    passData.luminanceQuantization = luminanceQuantization;
                    passData.chrominanceQuantization = chrominanceQuantization;
                    passData.chromaSubsampling = chromaSubsampling;
                    passData.ringingArtifacts = ringingArtifacts;
                    passData.mosquitoNoise = mosquitoNoise;
                    passData.edgeSensitivity = edgeSensitivity;


                    // Brightness Control parameters
                    passData.noiseTransparency = v.NoiseTransparency.value;
                    passData.maxNoiseBrightness = v.MaxNoiseBrightness.value;
                    passData.brightnessThreshold = v.BrightnessThreshold.value;
                    passData.brightAreaMasking = v.BrightAreaMasking.value;

                    // Debug parameters
                    passData.debugCompressionArtifacts = v.DebugCompressionArtifacts.value;

                    // Old Unity6 Render Graph Enhancement parameters removed

                    passData.fullscreenTriangle = FullscreenTriangle;

                    // Declare inputs/outputs
                    builder.UseTexture(passData.sourceTexture, AccessFlags.Read);

                    // MSAA FIX: Only bind depth texture if it's not multisampled to avoid sampler warnings
                    if (passData.depthTexture.IsValid())
                    {
                        var depthDesc = renderGraph.GetTextureDesc(passData.depthTexture);
                        if (depthDesc.msaaSamples == MSAASamples.None)
                        {
                            builder.UseTexture(passData.depthTexture, AccessFlags.Read);
                        }
                        // Skip multisampled depth textures to prevent "multisampled texture bound to non-multisampled sampler" error
                    }

                    builder.SetRenderAttachment(passData.outputTexture, 0, AccessFlags.Write);

                    // Enable render graph optimizations
                    builder.AllowPassCulling(false); // Keep pass for effect consistency

                    // Set execution function
                    builder.SetRenderFunc((FluxPassData data, RasterGraphContext context) =>
                        ExecuteFluxPass(data, context));

                    return outputTexture;
                }
            }

            private TextureHandle AddDecodePass(RenderGraph renderGraph, TextureHandle sourceTexture, bool doReprojection)
            {
                using (var builder = renderGraph.AddRasterRenderPass<FluxPassData>("Flux Decode", out var passData))
                {
                    // Create output texture with same dimensions as input (downscaled)
                    var desc = renderGraph.GetTextureDesc(sourceTexture);
                    desc.name = "Flux_Decoded";
                    TextureHandle outputTexture = renderGraph.CreateTexture(desc);

                    // Configure pass data
                    passData.sourceTexture = sourceTexture;
                    passData.outputTexture = outputTexture;
                    passData.motionVectorTexture = TextureHandle.nullHandle; // Decode pass doesn't need motion vectors
                    passData.fluxMaterial = mat;
                    passData.passIndex = Pass.Decode;
                    passData.screenTexelSize = screenTexelSize;
                    passData.downscaledTexelSize = downscaledTexelSize;
                    passData.colorCrunch = colorCrunch;
                    passData.sharpening = v.Oversharpening.value;
                    passData.reprojectPercent = reprojectPercent;
                    passData.reprojectSpeed = v.ReprojectBaseRerollSpeed.value;
                    passData.reprojectLengthInfluence = reprojectLengthInfluence;
                    passData.keyframeResetRate = keyframeResetRate;
                    passData.motionVectorCorruption = motionVectorCorruption;
                    passData.errorAccumulation = errorAccumulation;
                    passData.dctCorruption = dctCorruption;
                    passData.chromaCorruption = chromaCorruption;
                    passData.glitchTransition = glitchTransition;
                    passData.feedbackIntensity = feedbackIntensity;
                    passData.multiScaleCorruption = multiScaleCorruption;
                    passData.corruptionMask = v.CorruptionMask.value;
                    passData.doReprojection = false; // Decode pass doesn't do reprojection
                    passData.onlyStenciled = DoOnlyStenciled;
                    passData.visualizeMotionVectors = v.VisualizeMotionVectors.value;

                    // Consolidated motion parameters
                    passData.motionAmplification = motionAmplification;
                    passData.motionThreshold = motionThreshold;
                    passData.cameraObjectMotionBalance = cameraObjectMotionBalance;
                    passData.motionSmoothing = motionSmoothing;

                    // Pixel flow and trailing parameters
                    passData.trailIntensity = trailIntensity;
                    passData.trailSmoothness = trailSmoothness;
                    passData.trailPersistence = trailPersistence;
                    passData.flowSpread = flowSpread;

                    // New JPEG compression parameters
                    passData.jpegQuality = jpegQuality;
                    passData.luminanceQuantization = luminanceQuantization;
                    passData.chrominanceQuantization = chrominanceQuantization;
                    passData.chromaSubsampling = chromaSubsampling;
                    passData.ringingArtifacts = ringingArtifacts;
                    passData.mosquitoNoise = mosquitoNoise;
                    passData.edgeSensitivity = edgeSensitivity;


                    // Brightness Control parameters
                    passData.noiseTransparency = v.NoiseTransparency.value;
                    passData.maxNoiseBrightness = v.MaxNoiseBrightness.value;
                    passData.brightnessThreshold = v.BrightnessThreshold.value;
                    passData.brightAreaMasking = v.BrightAreaMasking.value;

                    // Debug parameters
                    passData.debugCompressionArtifacts = v.DebugCompressionArtifacts.value;

                    // Old Unity6 Render Graph Enhancement parameters removed

                    passData.fullscreenTriangle = FullscreenTriangle;

                    // Declare inputs/outputs
                    builder.UseTexture(passData.sourceTexture, AccessFlags.Read);
                    builder.SetRenderAttachment(passData.outputTexture, 0, AccessFlags.Write);

                    // Enable render graph optimizations
                    builder.AllowPassCulling(false); // Keep pass for effect consistency

                    // Set execution function
                    builder.SetRenderFunc((FluxPassData data, RasterGraphContext context) =>
                        ExecuteFluxPass(data, context));

                    return outputTexture;
                }
            }

            private void AddUpscalePass(RenderGraph renderGraph, TextureHandle sourceTexture, TextureHandle targetTexture,
                                       TextureHandle motionVectorTexture, TextureHandle previousFrameTexture, TextureHandle depthTexture, bool doReprojection)
            {
                using (var builder = renderGraph.AddRasterRenderPass<FluxPassData>("Flux Upscale", out var passData))
                {
                    // Configure pass data
                    passData.sourceTexture = sourceTexture;
                    passData.outputTexture = targetTexture;
                    passData.motionVectorTexture = motionVectorTexture;
                    passData.previousFrameTexture = previousFrameTexture;

                    // MSAA FIX: Handle depth texture based on MSAA and stencil requirements
                    if (depthTexture.IsValid())
                    {
                        var depthDesc = renderGraph.GetTextureDesc(depthTexture);
                        if (DoOnlyStenciled)
                        {
                            // For stencil testing, we need the depth texture regardless of MSAA for attachment
                            passData.depthTexture = depthTexture;
                        }
                        else if (depthDesc.msaaSamples == MSAASamples.None)
                        {
                            // For non-stencil mode, only pass non-multisampled depth textures
                            passData.depthTexture = depthTexture;
                        }
                        else
                        {
                            // Skip multisampled depth textures in non-stencil mode
                            passData.depthTexture = TextureHandle.nullHandle;
                        }
                    }
                    else
                    {
                        passData.depthTexture = TextureHandle.nullHandle;
                    }

                    passData.fluxMaterial = mat;
                    passData.passIndex = DoOnlyStenciled ? Pass.UpscalePullStenciled : Pass.UpscalePull;
                    passData.screenTexelSize = screenTexelSize;
                    passData.downscaledTexelSize = downscaledTexelSize;
                    passData.colorCrunch = colorCrunch;
                    passData.sharpening = v.Oversharpening.value;
                    passData.reprojectPercent = reprojectPercent;
                    passData.reprojectSpeed = v.ReprojectBaseRerollSpeed.value;
                    passData.reprojectLengthInfluence = reprojectLengthInfluence;
                    passData.keyframeResetRate = keyframeResetRate;
                    passData.motionVectorCorruption = motionVectorCorruption;
                    passData.errorAccumulation = errorAccumulation;
                    passData.dctCorruption = dctCorruption;
                    passData.chromaCorruption = chromaCorruption;
                    passData.glitchTransition = glitchTransition;
                    passData.feedbackIntensity = feedbackIntensity;
                    passData.multiScaleCorruption = multiScaleCorruption;
                    passData.corruptionMask = v.CorruptionMask.value;
                    passData.doReprojection = doReprojection;
                    passData.onlyStenciled = DoOnlyStenciled;
                    passData.visualizeMotionVectors = v.VisualizeMotionVectors.value;

                    // Consolidated motion parameters
                    passData.motionAmplification = motionAmplification;
                    passData.motionThreshold = motionThreshold;
                    passData.cameraObjectMotionBalance = cameraObjectMotionBalance;
                    passData.motionSmoothing = motionSmoothing;

                    // Pixel flow and trailing parameters
                    passData.trailIntensity = trailIntensity;
                    passData.trailSmoothness = trailSmoothness;
                    passData.trailPersistence = trailPersistence;
                    passData.flowSpread = flowSpread;

                    // New JPEG compression parameters
                    passData.jpegQuality = jpegQuality;
                    passData.luminanceQuantization = luminanceQuantization;
                    passData.chrominanceQuantization = chrominanceQuantization;
                    passData.chromaSubsampling = chromaSubsampling;
                    passData.ringingArtifacts = ringingArtifacts;
                    passData.mosquitoNoise = mosquitoNoise;
                    passData.edgeSensitivity = edgeSensitivity;




                    passData.fullscreenTriangle = FullscreenTriangle;

                    // Declare inputs/outputs
                    builder.UseTexture(passData.sourceTexture, AccessFlags.Read);
                    if (passData.motionVectorTexture.IsValid())
                        builder.UseTexture(passData.motionVectorTexture, AccessFlags.Read);
                    if (passData.previousFrameTexture.IsValid())
                        builder.UseTexture(passData.previousFrameTexture, AccessFlags.Read);

                    builder.SetRenderAttachment(passData.outputTexture, 0, AccessFlags.Write);

                    // UNITY 6 RENDER GRAPH STENCIL FIX:
                    // For stencil testing to work, we need to attach the active depth buffer (not camera depth texture)
                    // as the depth attachment. This enables GPU stencil testing hardware for shader stencil blocks.
                    if (passData.depthTexture.IsValid())
                    {
                        if (passData.onlyStenciled)
                        {
                            // Attach active depth buffer as depth attachment for stencil testing
                            // This is the actual depth buffer that contains stencil data from geometry passes
                            builder.SetRenderAttachmentDepth(passData.depthTexture, AccessFlags.Read);
                        }
                        else
                        {
                            // Normal mode: use depth texture as shader input only
                            // Note: Don't bind multisampled depth textures as regular texture inputs to avoid MSAA sampler warnings
                            var depthDesc = renderGraph.GetTextureDesc(passData.depthTexture);
                            if (depthDesc.msaaSamples == MSAASamples.None)
                            {
                                builder.UseTexture(passData.depthTexture, AccessFlags.Read);
                            }
                            // Skip binding multisampled depth textures as regular inputs to prevent MSAA sampler errors
                        }
                    }

                    // Enable render graph optimizations
                    builder.AllowPassCulling(false); // Keep pass for effect consistency

                    // Set execution function
                    builder.SetRenderFunc((FluxPassData data, RasterGraphContext context) =>
                        ExecuteFluxPass(data, context));
                }
            }

            private void AddCopyToPrevPass(RenderGraph renderGraph, TextureHandle sourceTexture, bool doReprojection)
            {
                using (var builder = renderGraph.AddRasterRenderPass<FluxPassData>("Flux Copy To Previous", out var passData))
                {
                    // Update persistent texture dimensions if needed
                    var desc = renderGraph.GetTextureDesc(sourceTexture);

                    // TEXTURE FORMAT FIX: Use JPG Bitcrunch format for consistency
                    GraphicsFormat previousFrameFormat = GraphicsFormat.R32G32B32A32_SFloat;
                    UpdatePersistentTexture(desc.width, desc.height, previousFrameFormat);

                    // Import the persistent texture as output
                    TextureHandle prevFrameOutput = renderGraph.ImportTexture(prevFrameRTHandle);

                    // Configure pass data
                    passData.sourceTexture = sourceTexture;
                    passData.outputTexture = prevFrameOutput;
                    passData.motionVectorTexture = TextureHandle.nullHandle; // CopyToPrev pass doesn't need motion vectors
                    passData.fluxMaterial = mat;
                    passData.passIndex = Pass.CopyToPrev;
                    passData.screenTexelSize = screenTexelSize;
                    passData.downscaledTexelSize = downscaledTexelSize;
                    passData.colorCrunch = colorCrunch;
                    passData.sharpening = v.Oversharpening.value;
                    passData.reprojectPercent = reprojectPercent;
                    passData.reprojectSpeed = v.ReprojectBaseRerollSpeed.value;
                    passData.reprojectLengthInfluence = reprojectLengthInfluence;
                    passData.keyframeResetRate = keyframeResetRate;
                    passData.motionVectorCorruption = motionVectorCorruption;
                    passData.errorAccumulation = errorAccumulation;
                    passData.dctCorruption = dctCorruption;
                    passData.chromaCorruption = chromaCorruption;
                    passData.glitchTransition = glitchTransition;
                    passData.feedbackIntensity = feedbackIntensity;
                    passData.multiScaleCorruption = multiScaleCorruption;
                    passData.corruptionMask = v.CorruptionMask.value;
                    passData.doReprojection = false; // CopyToPrev pass doesn't do reprojection
                    passData.onlyStenciled = DoOnlyStenciled;
                    passData.visualizeMotionVectors = v.VisualizeMotionVectors.value;

                    // Consolidated motion parameters
                    passData.motionAmplification = motionAmplification;
                    passData.motionThreshold = motionThreshold;
                    passData.cameraObjectMotionBalance = cameraObjectMotionBalance;
                    passData.motionSmoothing = motionSmoothing;

                    // Pixel flow and trailing parameters
                    passData.trailIntensity = trailIntensity;
                    passData.trailSmoothness = trailSmoothness;
                    passData.trailPersistence = trailPersistence;
                    passData.flowSpread = flowSpread;

                    // New JPEG compression parameters
                    passData.jpegQuality = jpegQuality;
                    passData.luminanceQuantization = luminanceQuantization;
                    passData.chrominanceQuantization = chrominanceQuantization;
                    passData.chromaSubsampling = chromaSubsampling;
                    passData.ringingArtifacts = ringingArtifacts;
                    passData.mosquitoNoise = mosquitoNoise;
                    passData.edgeSensitivity = edgeSensitivity;


                    // Brightness Control parameters
                    passData.noiseTransparency = v.NoiseTransparency.value;
                    passData.maxNoiseBrightness = v.MaxNoiseBrightness.value;
                    passData.brightnessThreshold = v.BrightnessThreshold.value;
                    passData.brightAreaMasking = v.BrightAreaMasking.value;

                    // Debug parameters
                    passData.debugCompressionArtifacts = v.DebugCompressionArtifacts.value;

                    // Old Unity6 Render Graph Enhancement parameters removed

                    passData.fullscreenTriangle = FullscreenTriangle;

                    // Declare inputs/outputs
                    builder.UseTexture(passData.sourceTexture, AccessFlags.Read);
                    builder.SetRenderAttachment(passData.outputTexture, 0, AccessFlags.Write);

                    // Enable render graph optimizations
                    builder.AllowPassCulling(false); // Keep pass for temporal consistency

                    // Set execution function
                    builder.SetRenderFunc((FluxPassData data, RasterGraphContext context) =>
                        ExecuteFluxPass(data, context));
                }
            }

            // Unity6 Datamosh Guide Execution Function - REMOVED
            // Unity6 Guide is now integrated directly into ExecuteFluxPass

            // Execution function for render graph passes
            static void ExecuteFluxPass(FluxPassData data, RasterGraphContext context)
            {
                // Set material properties
                data.fluxMaterial.SetVector("_Screen_TexelSize", data.screenTexelSize);
                data.fluxMaterial.SetVector("_Downscaled_TexelSize", data.downscaledTexelSize);
                data.fluxMaterial.SetFloat("_ColorCrunch", data.colorCrunch);
                data.fluxMaterial.SetFloat("_Sharpening", data.sharpening);
                data.fluxMaterial.SetFloat("_ReprojectPercent", data.reprojectPercent);
                data.fluxMaterial.SetFloat("_ReprojectSpeed", data.reprojectSpeed);
                data.fluxMaterial.SetFloat("_ReprojectLengthInfluence", data.reprojectLengthInfluence);
                data.fluxMaterial.SetFloat("_KeyframeResetRate", data.keyframeResetRate);
                data.fluxMaterial.SetFloat("_MotionVectorCorruption", data.motionVectorCorruption);
                data.fluxMaterial.SetFloat("_ErrorAccumulation", data.errorAccumulation);
                data.fluxMaterial.SetFloat("_DCTCorruption", data.dctCorruption);
                data.fluxMaterial.SetFloat("_ChromaCorruption", data.chromaCorruption);
                data.fluxMaterial.SetFloat("_GlitchTransition", data.glitchTransition);
                data.fluxMaterial.SetFloat("_FeedbackIntensity", data.feedbackIntensity);
                data.fluxMaterial.SetFloat("_MultiScaleCorruption", data.multiScaleCorruption);

                // Consolidated motion parameters
                data.fluxMaterial.SetFloat("_MotionAmplification", data.motionAmplification);
                data.fluxMaterial.SetFloat("_MotionThreshold", data.motionThreshold);
                data.fluxMaterial.SetFloat("_CameraObjectMotionBalance", data.cameraObjectMotionBalance);
                data.fluxMaterial.SetFloat("_MotionSmoothing", data.motionSmoothing);

                // Pixel flow and trailing parameters
                data.fluxMaterial.SetFloat("_TrailIntensity", data.trailIntensity);
                data.fluxMaterial.SetFloat("_TrailSmoothness", data.trailSmoothness);
                data.fluxMaterial.SetFloat("_TrailPersistence", data.trailPersistence);
                data.fluxMaterial.SetFloat("_FlowSpread", data.flowSpread);

                // New JPEG compression parameters
                data.fluxMaterial.SetFloat("_JPEGQuality", data.jpegQuality);
                data.fluxMaterial.SetFloat("_LuminanceQuantization", data.luminanceQuantization);
                data.fluxMaterial.SetFloat("_ChrominanceQuantization", data.chrominanceQuantization);
                data.fluxMaterial.SetFloat("_RingingArtifacts", data.ringingArtifacts);
                data.fluxMaterial.SetFloat("_MosquitoNoise", data.mosquitoNoise);
                data.fluxMaterial.SetFloat("_EdgeSensitivity", data.edgeSensitivity);

                // Brightness Control parameters
                data.fluxMaterial.SetFloat("_NoiseTransparency", data.noiseTransparency);
                data.fluxMaterial.SetFloat("_MaxNoiseBrightness", data.maxNoiseBrightness);
                data.fluxMaterial.SetFloat("_BrightnessThreshold", data.brightnessThreshold);
                data.fluxMaterial.SetFloat("_BrightAreaMasking", data.brightAreaMasking);

                // Debug parameters
                data.fluxMaterial.SetFloat("_DebugCompressionArtifacts", data.debugCompressionArtifacts ? 1.0f : 0.0f);

                // Compression Artifacts keyword management
                if (data.ringingArtifacts > 0.001f || data.mosquitoNoise > 0.001f)
                {
                    data.fluxMaterial.EnableKeyword("COMPRESSION_ARTIFACTS");
                }
                else
                {
                    data.fluxMaterial.DisableKeyword("COMPRESSION_ARTIFACTS");
                }

                // Enhanced Visual Effects are always available
                data.fluxMaterial.EnableKeyword("ENHANCED_VISUAL_EFFECTS");

                if (data.corruptionMask != null)
                {
                    data.fluxMaterial.SetTexture("_CorruptionMask", data.corruptionMask);
                    data.fluxMaterial.EnableKeyword("_CORRUPTIONMASK");
                }
                else
                {
                    data.fluxMaterial.DisableKeyword("_CORRUPTIONMASK");
                }

                // Set input textures - validate handles before use with additional safety checks
                if (data.sourceTexture.IsValid())
                {
                    try
                    {
                        data.fluxMaterial.SetTexture("_Input", data.sourceTexture);
                    }
                    catch (System.Exception)
                    {
                        // Silently handle invalid texture handle conversion
                    }
                }

                // CRITICAL FIX: Unity 6 Render Graph requires explicit motion vector binding
                // Unlike legacy render pipeline, Render Graph doesn't automatically bind motion vectors
                // We need to explicitly bind the motion vector texture when reprojection is enabled

                if (data.doReprojection && data.motionVectorTexture.IsValid())
                {
                    try
                    {
                        // Unity 6 Render Graph: Explicit motion vector texture binding is required
                        data.fluxMaterial.SetTexture("_MotionVectorTexture", data.motionVectorTexture);
                    }
                    catch (System.Exception ex)
                    {
                        UnityEngine.Debug.LogWarning($"[Flux] Failed to bind motion vector texture: {ex.Message}");
                    }
                }
                else if (data.doReprojection && !data.motionVectorTexture.IsValid())
                {
                    UnityEngine.Debug.LogWarning("[Flux] Motion vectors requested but texture not available. Check if ScriptableRenderPassInput.Motion is properly configured.");
                }

                if (data.previousFrameTexture.IsValid())
                {
                    try
                    {
                        data.fluxMaterial.SetTexture("_PrevScreen", data.previousFrameTexture);
                    }
                    catch (System.Exception)
                    {
                        // Silently handle invalid texture handle conversion
                    }
                }

                // DEPTH TEXTURE FIX: Always ensure depth texture is bound to prevent black materials
                // For stencil testing, the depth attachment is handled by SetRenderAttachmentDepth, not texture binding
                if (data.depthTexture.IsValid() && !data.onlyStenciled)
                {
                    try
                    {
                        data.fluxMaterial.SetTexture("_CameraDepthTexture", data.depthTexture);
                    }
                    catch (System.Exception)
                    {
                        // If depth texture binding fails, disable depth-based features to prevent black materials
                        data.fluxMaterial.SetFloat("_ColorCrunch", 0.0f);
                    }
                }
                else if (!data.onlyStenciled)
                {
                    // If no depth texture is available, disable depth-dependent features
                    data.fluxMaterial.SetFloat("_ColorCrunch", 0.0f);
                }

                // Draw fullscreen triangle
                context.cmd.DrawMesh(data.fullscreenTriangle, Matrix4x4.identity,
                                    data.fluxMaterial, 0, data.passIndex);
            }

            // Update persistent texture for temporal reprojection
            private void UpdatePersistentTexture(int width, int height, GraphicsFormat format)
            {
                bool wasNull = prevFrameRTHandle == null;

                if (prevFrameRTHandle == null ||
                    prevFrameRTHandle.rt.width != width ||
                    prevFrameRTHandle.rt.height != height ||
                    prevFrameRTHandle.rt.graphicsFormat != format)
                {
                    prevFrameRTHandle?.Release();
                    prevFrameRTHandle = RTHandles.Alloc(width, height, colorFormat: format, name: "Flux_PrevFrame");

                    // Clear the texture on first creation to ensure consistent behavior
                    if (wasNull)
                    {
                        var cmd = CommandBufferPool.Get("Flux Clear PrevFrame");
                        cmd.SetRenderTarget(prevFrameRTHandle);
                        cmd.ClearRenderTarget(false, true, Color.black);
                        Graphics.ExecuteCommandBuffer(cmd);
                        CommandBufferPool.Release(cmd);
                    }
                }
            }

            Mesh fullscreenTriangle;
            Mesh FullscreenTriangle
            {
                get
                {
                    if (fullscreenTriangle != null) return fullscreenTriangle;
                    fullscreenTriangle = new Mesh { name = "Fullscreen Triangle" };
                    fullscreenTriangle.SetVertices(new List<Vector3> { new Vector3(-1f, -1f, 0f), new Vector3(-1f, 3f, 0f), new Vector3(3f, -1f, 0f) });
                    fullscreenTriangle.SetIndices(new[] { 0, 1, 2 }, MeshTopology.Triangles, 0, false);
                    fullscreenTriangle.UploadMeshData(false);
                    return fullscreenTriangle;
                }
            }
            public void RenderWith(RenderTargetIdentifier source, RenderTargetIdentifier destination, CommandBuffer cmd, Material material, int pass, bool rebindStencil = false)
            {
                cmd.SetGlobalTexture("_Input", source);
                // Why we rebind stencil: gist.github.com/ScottJDaley/6cddf0c8995ed61cac7088e22c983de1?permalink_comment_id=4976348#gistcomment-4976348
                if (rebindStencil)
                    cmd.SetRenderTarget(destination, sourceDepthStencil);
                else
                    cmd.SetRenderTarget(destination);
                cmd.DrawMesh(FullscreenTriangle, Matrix4x4.identity, material, 0, pass);
            }
            public void Render(RenderTargetIdentifier destination, CommandBuffer cmd, Material material, int pass)
            {
                cmd.SetRenderTarget(destination);
                cmd.DrawMesh(FullscreenTriangle, Matrix4x4.identity, material, 0, pass);
            }

            // End of FluxRenderPass class
        }

        [Space(15), SerializeField, Header("You can now add Flux to your Post Process Volume.")]
        Shader shader;
        FluxRenderPass renderPass;

        public override void Create()
        {
            if (!isActive)
            {
                renderPass?.Cleanup();
                renderPass = null;
                return;
            }
            name = "Flux";
            renderPass = new FluxRenderPass();
        }

        void OnDisable()
        {
            renderPass?.Cleanup();
        }

        protected override void Dispose(bool disposing)
        {
            renderPass?.Cleanup();
            renderPass = null;
        }

        public override void AddRenderPasses(ScriptableRenderer renderer, ref RenderingData renderingData)
        {
            shader = Shader.Find("Hidden/Universal Render Pipeline/Flux");
            if (shader == null)
            {
                Debug.LogWarning("Flux shader was not found. Please ensure it compiles correctly");
                return;
            }

            // Temporarily remove post-processing dependency to test render graph conflicts
            // if (renderingData.cameraData.postProcessEnabled)
            {
                renderPass.Setup(shader, renderer, renderingData);
                renderer.EnqueuePass(renderPass);
            }
        }
    }
}
#endif