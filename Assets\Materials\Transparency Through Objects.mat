%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Transparency Through Objects
  m_Shader: {fileID: 4800000, guid: 88e9264ba3f8ff64fbbcb07a4957e5d3, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords:
  - _TRANSPARENT_ON
  m_InvalidKeywords:
  - _DUPLICATEDETAILS_ON
  - _MAPCONTRIBUTION_NONE
  - _UVSCREENPROJECTION_UVPROJECTION
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 1
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMap:
        m_Texture: {fileID: 2800000, guid: 8c38d2e72ca280f4783343ec9c4418f7, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AlphaCutoff: 0.5
    - _AlphaTestBrightness: 2
    - _AmplitudeSpeed: 1
    - _BumpScale: 0.3
    - _ContrastDetailMap: 1
    - _DesaturateBackground: 0
    - _DetailScale: 1
    - _DuplicateDetails: 1
    - _EnableSpecularLight: 0
    - _FresnelBias: 0
    - _FresnelIntensity: 4
    - _FresnelPower: 4
    - _Glossiness: 0.75
    - _Invert: 0
    - _MapContribution: 0
    - _MaxValueAmplitude: 2
    - _MinValueAmplitude: 1
    - _Opacity: 0.548
    - _Refraction: 1.1
    - _RotationAngle: 0
    - _RotationSpeed: 0
    - _SelfIllumination: 5.19
    - _ShadowOpacity: 0
    - _SpreadDetailMap: 0
    - _TexturesScale: 1
    - _TranslationSpeed: 0
    - _TransparentMode: 1
    - _UVScreenProjection: 0
    - _UseAlphaTest: 0
    - _XRayBias: 0.04
    - _XRayFresnelIntensity: 1
    - _XRayFresnelPower: 1
    m_Colors:
    - _Color: {r: 0.8679245, g: 0.741011, b: 0.741011, a: 0.39607844}
    - _EmissionColor: {r: 1, g: 1, b: 1, a: 1}
    - _FillColorBackground: {r: 0, g: 0, b: 0, a: 0}
    - _FresnelColor: {r: 0.6933962, g: 1, b: 0.9814353, a: 1}
    - _XRayColor: {r: 0, g: 1, b: 0.980212, a: 0.1019608}
  m_BuildTextureStacks: []
  m_AllowLocking: 1
