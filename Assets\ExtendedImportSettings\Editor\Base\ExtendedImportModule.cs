using UnityEditor;
using UnityEngine;

namespace ExtendedImportSettings
{
    public abstract class ExtendedImportModule
    {
        [HideInInspector] public bool IsActive;

        public virtual void OnEnableGUI(object assetImporter)
        {
            
        }
        
        public virtual void OnDisableGUI(object assetImporter)
        {
            
        }
        
        public virtual void OnInspectorG<PERSON>(SerializedProperty moduleSerializedProperty, object assetImporter)
        {
            ModuleUtilities.DrawPropertyField(GetType(), moduleSerializedProperty, "IsActive");
        }

        public abstract void OnPostprocessModel(GameObject gameObject);
    }
}
