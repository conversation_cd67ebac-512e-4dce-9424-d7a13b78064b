﻿#if UNITY_EDITOR
using UnityEditor;
using UnityEngine;

namespace FIMSpace.FOptimizing
{
    /// <summary>
    /// FM: Class to solve many troubles with different unity versions to use not shared (unique) settings
    /// in all cases (isolated scene prefab mode / creating prefabs from scene etc.)
    /// Class with methods supporting versions before unity 2018.3
    /// </summary>
    public static partial class Optimizers_LODTransport
    {

#if UNITY_2018_3_OR_NEWER  ///////////////////////////////////////////////
#else //ELSE


        public static Object GetPrefab(GameObject target)
        {
            Object prefab = null;

            if (target.gameObject)
            {
                if (PrefabUtility.GetPrefabObject(target.gameObject)) // If it is project asset prefab
                    prefab = PrefabUtility.GetPrefabObject(target.gameObject);
                else // If it is instance of prefab
                {
                    prefab = PrefabUtility.GetPrefabParent(target.gameObject);
                }
            }
            else
                Debug.LogError("[OPTIMIZERS EDITOR] No Game Object inside lods controller!");

            if (prefab)
                if (!AssetDatabase.Contains(prefab))
                    return null; // It's not in assets database?

            return prefab;
        }


        public static GameObject GetProjectPrefabSimple(Object reference)
        {
            GameObject prefab = null;
            if (reference) prefab = PrefabUtility.GetPrefabParent(reference) as GameObject;
            if (prefab == null) prefab = PrefabUtility.GetPrefabObject(reference) as GameObject;
            return prefab;
        }

        public static GameObject GetPrefabRootObject(Object reference)
        {
            GameObject prefab = null;
            GameObject prefabO = reference as GameObject;
            if (prefabO) prefab = PrefabUtility.FindPrefabRoot(prefabO);

            return prefab;
        }

        public static string GetProjectPrefabPath(Object reference)
        {
            string path = string.Empty;
            if (reference) path = AssetDatabase.GetAssetPath(PrefabUtility.GetPrefabObject(reference));
            return path;
        }


#endif  ///////////////////////////////////////////////

    }
}
#endif
