<?xml version="1.0" encoding="UTF-8"?><package><identifier v="Unsaved Package"/><formatVersion v="1.1.0.202201"/><updaterVersion v="1.1.0.202201"/><fileUID v="{c90869d1-d014-4126-a741-9fb2faa4e4d9}"/><versionUID v="0"/><dependencies><dependency><filename v="sbs://pattern_polygon_2.sbs"/><uid v="1487936962"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://splatter.sbs"/><uid v="1487937180"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://pattern_gaussian_1.sbs"/><uid v="1487937587"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_clouds_1.sbs"/><uid v="1487938400"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://histogram_scan.sbs"/><uid v="1487938912"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://white_noise.sbs"/><uid v="1146345729"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://blur_hq.sbs"/><uid v="1299236171"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://curvature_smooth.sbs"/><uid v="1487940552"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://histogram_range.sbs"/><uid v="1487941862"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_gaussian_spots_1.sbs"/><uid v="1487942298"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://contrast_luminosity.sbs"/><uid v="1146607502"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency></dependencies><content><graph><identifier v="soft_sand"/><uid v="1487929992"/><graphtype v="material"/><graphOutputs><graphoutput><identifier v="basecolor"/><uid v="1213284336"/><attributes><label v="Base Color"/></attributes><usages><usage><components v="RGBA"/><name v="baseColor"/></usage></usages><group v="Material"/></graphoutput><graphoutput><identifier v="normal"/><uid v="1213284338"/><attributes><label v="Normal"/></attributes><usages><usage><components v="RGBA"/><name v="normal"/></usage></usages><group v="Material"/></graphoutput><graphoutput><identifier v="roughness"/><uid v="1213284340"/><attributes><label v="Roughness"/></attributes><usages><usage><components v="RGBA"/><name v="roughness"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput><graphoutput><identifier v="metallic"/><uid v="1213284342"/><attributes><label v="Metallic"/></attributes><usages><usage><components v="RGBA"/><name v="metallic"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput><graphoutput><identifier v="height"/><uid v="1279137031"/><attributes><label v="Height"/></attributes><usages><usage><components v="RGBA"/><name v="height"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput><graphoutput><identifier v="ambientocclusion"/><uid v="1359211721"/><attributes><label v="Ambient Occlusion"/></attributes><usages><usage><components v="RGBA"/><name v="ambientOcclusion"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput></graphOutputs><compNodes><compNode><uid v="1213284337"/><GUILayout><gpos v="144 -240 0"/></GUILayout><connections><connection><identifier v="inputNodeOutput"/><connRef v="1487944038"/><connRefOutput v="1487944039"/></connection></connections><compImplementation><compOutputBridge><output v="1213284336"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1213284339"/><GUILayout><gpos v="144 -80 0"/></GUILayout><connections><connection><identifier v="inputNodeOutput"/><connRef v="1359211383"/><connRefOutput v="1359211384"/></connection></connections><compImplementation><compOutputBridge><output v="1213284338"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1213284341"/><GUILayout><gpos v="144 80 0"/></GUILayout><connections><connection><identifier v="inputNodeOutput"/><connRef v="1487941885"/><connRefOutput v="1487941886"/></connection></connections><compImplementation><compOutputBridge><output v="1213284340"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1213284343"/><GUILayout><gpos v="144 240 0"/></GUILayout><connections><connection><identifier v="inputNodeOutput"/><connRef v="1359211407"/><connRefOutput v="1359211408"/></connection></connections><compImplementation><compOutputBridge><output v="1213284342"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1279137030"/><GUILayout><gpos v="144 560 0"/></GUILayout><connections><connection><identifier v="inputNodeOutput"/><connRef v="1487944285"/><connRefOutput v="1487944286"/></connection></connections><compImplementation><compOutputBridge><output v="1279137031"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1359211383"/><GUILayout><gpos v="-208 -80 0"/></GUILayout><compOutputs><compOutput><uid v="1359211384"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487944285"/><connRefOutput v="1487944286"/></connection></connections><compImplementation><compFilter><filter v="normal"/><parameters><parameter><name v="input2alpha"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1359211407"/><GUILayout><gpos v="-208 240 0"/></GUILayout><compOutputs><compOutput><uid v="1359211408"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="uniform"/><parameters><parameter><name v="colorswitch"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1359211719"/><GUILayout><gpos v="-208 400 0"/></GUILayout><compOutputs><compOutput><uid v="1359211408"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="uniform"/><parameters><parameter><name v="colorswitch"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="outputcolor"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="1 1 1 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1359211720"/><GUILayout><gpos v="144 400 0"/></GUILayout><connections><connection><identifier v="inputNodeOutput"/><connRef v="1359211719"/><connRefOutput v="1359211408"/></connection></connections><compImplementation><compOutputBridge><output v="1359211721"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1487937170"/><GUILayout><gpos v="-1936 80 0"/></GUILayout><compOutputs><compOutput><uid v="1487937171"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///polygon_2?dependency=1487936962"/><parameters/><outputBridgings><outputBridging><uid v="1487937171"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487937355"/><GUILayout><gpos v="-1808 80 0"/></GUILayout><compOutputs><compOutput><uid v="1487937356"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="Source"/><connRef v="1487937170"/><connRefOutput v="1487937171"/></connection></connections><compImplementation><compInstance><path v="pkg:///splatter?dependency=1487937180"/><parameters><parameter><name v="Pattern_size_width"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1939.76001"/></paramValue></parameter><parameter><name v="Pattern_size_height"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="220.880005"/></paramValue></parameter><parameter><name v="Rotation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="24.5799999"/></paramValue></parameter><parameter><name v="Disorder"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="37.75"/></paramValue></parameter><parameter><name v="Grid_Number"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="Disorder_Angle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="141.690002"/></paramValue></parameter><parameter><name v="Size_Var"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="100"/></paramValue></parameter><parameter><name v="Lum_Var"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.479999989"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487937356"/><identifier v="Splatter_Grayscale"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487937575"/><GUILayout><gpos v="-1648 80 0"/></GUILayout><compOutputs><compOutput><uid v="1487937576"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487937355"/><connRefOutput v="1487937356"/></connection><connection><identifier v="inputgradient"/><connRef v="1487937661"/><connRefOutput v="1487937662"/></connection></connections><compImplementation><compFilter><filter v="warp"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.75"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487937661"/><GUILayout><gpos v="-1872 208 0"/></GUILayout><compOutputs><compOutput><uid v="1487937662"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///gaussian_1?dependency=1487937587"/><parameters><parameter><name v="Tiling"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487937662"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487938375"/><GUILayout><gpos v="-1520 80 0"/></GUILayout><compOutputs><compOutput><uid v="1487938376"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487937575"/></connection><connection><identifier v="inputgradient"/><connRef v="1487943896"/><connRefOutput v="1487943897"/></connection></connections><compImplementation><compFilter><filter v="warp"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.00300000003"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487938743"/><GUILayout><gpos v="-1744 240 0"/></GUILayout><compOutputs><compOutput><uid v="1487938744"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///clouds_1?dependency=1487938400"/><parameters><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueInt32 v="8"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487938744"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487938963"/><GUILayout><gpos v="-1392 80 0"/></GUILayout><compOutputs><compOutput><uid v="1487938964"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="Input_1"/><connRef v="1487938375"/></connection></connections><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1487938912"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.359999985"/></paramValue></parameter><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.209999993"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487938964"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487939054"/><GUILayout><gpos v="-1232 80 0"/></GUILayout><compOutputs><compOutput><uid v="1487939055"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="destination"/><connRef v="1487938963"/><connRefOutput v="1487938964"/></connection><connection><identifier v="source"/><connRef v="1487940319"/><connRefOutput v="1487940320"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0299999993"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487940319"/><GUILayout><gpos v="-1392 -48 0"/></GUILayout><compOutputs><compOutput><uid v="1487940320"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///white_noise?dependency=1146345729"/><parameters/><outputBridgings><outputBridging><uid v="1487940320"/><identifier v="White_Noise"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487940372"/><GUILayout><gpos v="-927.601563 80 0"/></GUILayout><compOutputs><compOutput><uid v="1487940373"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="Source"/><connRef v="1487943470"/><connRefOutput v="1487943471"/></connection></connections><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1299236171"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.100000001"/></paramValue></parameter><parameter><name v="Quality"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487940373"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487941326"/><GUILayout><gpos v="-192.733459 -400 0"/></GUILayout><compOutputs><compOutput><uid v="1487941327"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input"/><connRef v="1359211383"/><connRefOutput v="1359211384"/></connection></connections><compImplementation><compInstance><path v="pkg:///curvature_smooth?dependency=1487940552"/><parameters/><outputBridgings><outputBridging><uid v="1487941327"/><identifier v="height"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487941349"/><GUILayout><gpos v="-80 -400 0"/></GUILayout><compOutputs><compOutput><uid v="1487941350"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487941326"/><connRefOutput v="1487941327"/></connection></connections><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1487941406"/><paramsArrayCells><paramsArrayCell><uid v="1487945123"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.37148568"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.368627459 0.294117659 0.164705887 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487945124"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.533466578"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.78039217 0.646717846 0.466666639 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487945125"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.640217662"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.796078444 0.70588237 0.568627477 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487945126"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.996491253"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.909803927 0.839215696 0.662745118 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487945456"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.455696195"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.508695662 0.404360175 0.245475233 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1487941885"/><GUILayout><gpos v="-208 80 0"/></GUILayout><compOutputs><compOutput><uid v="1487941886"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input"/><connRef v="1487944285"/><connRefOutput v="1487944286"/></connection></connections><compImplementation><compInstance><path v="pkg:///histogram_range?dependency=1487941862"/><parameters><parameter><name v="range"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.730000019"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487941886"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487942497"/><GUILayout><gpos v="-1360 -304 0"/></GUILayout><compOutputs><compOutput><uid v="1487942498"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///gaussian_spots_1?dependency=1487942298"/><parameters><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487942498"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487942503"/><GUILayout><gpos v="-1232 -304 0"/></GUILayout><compOutputs><compOutput><uid v="1487942504"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487942497"/><connRefOutput v="1487942498"/></connection></connections><compImplementation><compFilter><filter v="gradient"/><parameters><parameter><name v="colorswitch"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter></parameters><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1487942538"/><paramsArrayCells><paramsArrayCell><uid v="1487942539"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.608391643"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0 0 0 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487942540"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.688811183"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="1 1 1 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487942541"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.867132843"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.534782588 0.534782588 0.534782588 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1487943073"/><GUILayout><gpos v="-1104 -304 0"/></GUILayout><compOutputs><compOutput><uid v="1487943074"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487942503"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.0207547173 0.0207547173 0.0207547173 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487943470"/><GUILayout><gpos v="-1072 80 0"/></GUILayout><compOutputs><compOutput><uid v="1487943471"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="destination"/><connRef v="1487939054"/><connRefOutput v="1487939055"/></connection><connection><identifier v="source"/><connRef v="1487943073"/><connRefOutput v="1487943074"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0700000003"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487943896"/><GUILayout><gpos v="-1629.61279 240.174469 0"/></GUILayout><compOutputs><compOutput><uid v="1487943897"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487938743"/><connRefOutput v="1487938744"/></connection></connections><compImplementation><compFilter><filter v="blur"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487944038"/><GUILayout><gpos v="31.2665482 -400 0"/></GUILayout><compOutputs><compOutput><uid v="1487944039"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487941349"/><connRefOutput v="1487941350"/></connection></connections><compImplementation><compFilter><filter v="hsl"/><parameters><parameter><name v="saturation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.620000005"/></paramValue></parameter><parameter><name v="luminosity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.529999971"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487944285"/><GUILayout><gpos v="-656 80 0"/></GUILayout><compOutputs><compOutput><uid v="1487944286"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="Input_1"/><connRef v="1487944576"/><connRefOutput v="1487944577"/></connection></connections><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1487938912"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.5"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487944286"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487944576"/><GUILayout><gpos v="-791.800781 80 0"/></GUILayout><compOutputs><compOutput><uid v="1487944577"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="Source"/><connRef v="1487940372"/><connRefOutput v="1487940373"/></connection></connections><compImplementation><compInstance><path v="pkg:///contrast_luminosity_grayscale?dependency=1146607502"/><parameters><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.100000001"/></paramValue></parameter><parameter><name v="Luminosity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="-0.100000001"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487944577"/><identifier v="Contrast_Luminosity"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode></compNodes><baseParameters/><options><option><name v="defaultParentSize"/><value v="11x11"/></option></options><root><rootOutputs><rootOutput><output v="1213284336"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1213284338"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1213284340"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1213284342"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1279137031"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1359211721"/><format v="0"/><usertag v=""/></rootOutput></rootOutputs></root></graph></content></package>
