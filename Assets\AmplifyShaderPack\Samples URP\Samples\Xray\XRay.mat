%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 6
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: XRay
  m_Shader: {fileID: 4800000, guid: 4aefe7d7019e41e2a26ed475bbc7ca15, type: 3}
  m_ShaderKeywords: 
  m_LightmapFlags: 2
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _Albedo:
        m_Texture: {fileID: 2800000, guid: 359c4962c0a3407eb969c1d9eeb6426b, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 2800000, guid: b36d82dce1944357b33e03f7e2975b06, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: 359c4962c0a3407eb969c1d9eeb6426b, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SmoothnessMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TextureSample0:
        m_Texture: {fileID: 2800000, guid: 359c4962c0a3407eb969c1d9eeb6426b, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TextureSample1:
        m_Texture: {fileID: 2800000, guid: 0bebe40e9ebbecc48b8e9cfea982da7e, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _texcoord:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Floats:
    - _ASEOutlineWidth: 0
    - _AlphaCutoff: 0.5
    - _AlphaCutoffBias: 0
    - _Brightness: 1
    - _Cull: 0
    - _Cutoff: 0.5
    - _EnvironmentReflections: 1
    - _FadeTweak: 10
    - _Float0: 0.07
    - _Float1: 1.87
    - _GlossyReflections: 1
    - _MetallicStrength: 0.135
    - _NormalStrength: 1
    - _OcclusionStrengthAO: 0
    - _QueueControl: 0
    - _QueueOffset: 0
    - _ReceiveShadows: 1
    - _SmoothnessStrength: 0.543
    - _SpecularHighlights: 1
    - _XRayBias: 1.5
    - _XRayDistance: 5
    - _XRayFadeTweak: 10
    - _XRayIntensity: 5
    - _XRayOutlineDistance: 5
    - _XRayOutlineThickness: 0
    - _XRayPower: -0.25
    - _XRayScale: 0.75
    - _XRayThickness: 0.001
    - __dirty: 0
    m_Colors:
    - _BaseColor: {r: 1, g: 1, b: 1, a: 0}
    - _EmissionColor: {r: 1, g: 1, b: 1, a: 1}
    - _MainUVs: {r: 1, g: 1, b: 0, a: 0}
    - _XRayColor: {r: 0, g: 0.29803896, b: 1, a: 0.34901962}
  m_BuildTextureStacks: []
