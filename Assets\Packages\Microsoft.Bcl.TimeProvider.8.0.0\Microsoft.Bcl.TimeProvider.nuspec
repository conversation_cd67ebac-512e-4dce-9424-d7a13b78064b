﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2013/05/nuspec.xsd">
  <metadata>
    <id>Microsoft.Bcl.TimeProvider</id>
    <version>8.0.0</version>
    <authors>Microsoft</authors>
    <license type="expression">MIT</license>
    <licenseUrl>https://licenses.nuget.org/MIT</licenseUrl>
    <icon>Icon.png</icon>
    <readme>PACKAGE.md</readme>
    <projectUrl>https://dot.net/</projectUrl>
    <description>Provides support for system time abstraction primitives for .NET Framework and .NET Standard.

Commonly Used Types:
System.TimeProvider
System.ITimer</description>
    <releaseNotes>https://go.microsoft.com/fwlink/?LinkID=799421</releaseNotes>
    <copyright>© Microsoft Corporation. All rights reserved.</copyright>
    <serviceable>true</serviceable>
    <repository type="git" url="https://github.com/dotnet/runtime" commit="5535e31a712343a63f5d7d796cd874e563e5ac14" />
    <dependencies>
      <group targetFramework=".NETFramework4.6.2">
        <dependency id="Microsoft.Bcl.AsyncInterfaces" version="6.0.0" exclude="Build,Analyzers" />
        <dependency id="System.ValueTuple" version="4.5.0" exclude="Build,Analyzers" />
      </group>
      <group targetFramework="net8.0" />
      <group targetFramework=".NETStandard2.0">
        <dependency id="Microsoft.Bcl.AsyncInterfaces" version="6.0.0" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
</package>