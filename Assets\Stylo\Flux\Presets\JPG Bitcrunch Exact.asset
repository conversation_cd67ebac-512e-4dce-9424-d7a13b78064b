%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7be522ae007ba5a4f87ffaaf2e793f4c, type: 3}
  m_Name: JPG Bitcrunch Exact
  m_EditorClassIdentifier: 
  presetName: JPG Bitcrunch Exact
  description: Exact 1:1 recreation of JPG Bitcrunch with authentic datamoshing enabled. Uses working parameter values that produce visible trailing/smearing effects like the original.
  category: True Datamosh
  effectIntensity: 1
  colorCrunch: 0.528
  downscaling: 7
  blockSize: 2
  oversharpening: 0.738
  dontCrunchSkybox: 0
  onlyStenciled: 0
  reprojectBaseNoise: 0.15
  reprojectBaseRerollSpeed: 3
  reprojectLengthInfluence: 1.2
  cameraMotionAmplification: 0
  cameraMotionThreshold: 0.001
  cameraMotionInfluence: 0
  cameraMotionSmoothing: 0.1
  pixelFlowIntensity: 0
  trailSmoothness: 0
  motionPersistence: 0
  flowGradient: 0
  temporalAccumulation: 0
  keyframeResetRate: 0
  motionVectorCorruption: 0
  errorAccumulation: 0
  dctCorruption: 0
  corruptionMask: {fileID: 0}
  chromaCorruption: 0
  glitchTransition: 0
  feedbackIntensity: 0
  multiScaleCorruption: 0
  jpegQuality: 100
  luminanceQuantization: 0
  chrominanceQuantization: 0
  chromaSubsampling: 0
  ringingArtifacts: 0
  mosquitoNoise: 0
  edgeSensitivity: 0.5

  visualizeMotionVectors: 0
