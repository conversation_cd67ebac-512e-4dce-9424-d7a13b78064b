%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Hologram Simple
  m_Shader: {fileID: 4800000, guid: b03d170494a643e5948fbce1b8f07225, type: 3}
  m_ValidKeywords: []
  m_InvalidKeywords: []
  m_LightmapFlags: 0
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - <noninit>:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _RimNormalMap:
        m_Texture: {fileID: 2800000, guid: 2fb1b14b4e2147e4a580f44624c73725, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _texcoord:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - <noninit>: 1
    - _AlphaCutoff: 0.5
    - _Cull: 2
    - _EnvironmentReflections: 1
    - _Intensity: 4.282353
    - _MaskClipValue: 0
    - _Opacity: 0.4
    - _QueueControl: 0
    - _QueueOffset: 0
    - _ReceiveShadows: 1
    - _RimPower: 4.13
    - _ScanLines: 6
    - _SpecularHighlights: 1
    - _Speed: 64
    - __dirty: 0
    m_Colors:
    - <noninit>: {r: 1, g: 1, b: 1, a: 1}
    - _EmissionColor: {r: 1, g: 1, b: 1, a: 1}
    - _Hologramcolor: {r: 0.8901961, g: 0, b: 0, a: 0}
  m_BuildTextureStacks: []
