%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &1269092366527885050
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2866507148317891817}
  - component: {fileID: 6529348300587673311}
  - component: {fileID: 693676131708033574}
  - component: {fileID: 3614434946855222847}
  m_Layer: 0
  m_Name: ExplodudeCrate (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2866507148317891817
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1269092366527885050}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0.0025, z: 0}
  m_LocalScale: {x: 0.5, y: 0.5, z: 0.5}
  m_Children: []
  m_Father: {fileID: 40694071891407375}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &6529348300587673311
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1269092366527885050}
  m_Mesh: {fileID: -5495902117074765545, guid: c4201b7f5edf70a4e9272a77e04c947c, type: 3}
--- !u!23 &693676131708033574
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1269092366527885050}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 00977836d35ea9c44ba9f73300203ff2, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!114 &3614434946855222847
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1269092366527885050}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 347013242dca7c44fb5001793033ea59, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  UpdateMode: 0
  PositionActive: 0
  RotationActive: 0
  ScaleActive: 0
  PositionWiggleProperties:
    WigglePermitted: 1
    WiggleType: 1
    UseUnscaledTime: 0
    StartWigglingAutomatically: 1
    SmoothPingPong: 1
    UseSpeedCurve: 0
    SpeedCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    FrequencyMin: 0
    FrequencyMax: 1
    AmplitudeMin: {x: 0, y: 0, z: 0}
    AmplitudeMax: {x: 1, y: 1, z: 1}
    RelativeAmplitude: 1
    Curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    RemapCurveZeroMin: {x: 0, y: 0, z: 0}
    RemapCurveZeroMax: {x: 0, y: 0, z: 0}
    RemapCurveOneMin: {x: 1, y: 1, z: 1}
    RemapCurveOneMax: {x: 1, y: 1, z: 1}
    RelativeCurveAmplitude: 1
    CurvePingPong: 0
    PauseMin: 0
    PauseMax: 0
    LimitedTime: 0
    LimitedTimeTotal: 0
    LimitedTimeFalloff:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    LimitedTimeResetValue: 1
    LimitedTimeLeft: 0
    NoiseFrequencyMin: {x: 0, y: 0, z: 0}
    NoiseFrequencyMax: {x: 1, y: 1, z: 1}
    NoiseShiftMin: {x: 0, y: 0, z: 0}
    NoiseShiftMax: {x: 0, y: 0, z: 0}
  RotationWiggleProperties:
    WigglePermitted: 1
    WiggleType: 2
    UseUnscaledTime: 0
    StartWigglingAutomatically: 1
    SmoothPingPong: 1
    UseSpeedCurve: 1
    SpeedCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    FrequencyMin: 0.5
    FrequencyMax: 0.5
    AmplitudeMin: {x: 0, y: 0, z: 0}
    AmplitudeMax: {x: 0, y: 0, z: 90}
    RelativeAmplitude: 1
    Curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    RemapCurveZeroMin: {x: 0, y: 0, z: 0}
    RemapCurveZeroMax: {x: 0, y: 0, z: 0}
    RemapCurveOneMin: {x: 1, y: 1, z: 1}
    RemapCurveOneMax: {x: 1, y: 1, z: 1}
    RelativeCurveAmplitude: 1
    CurvePingPong: 0
    PauseMin: 0.5
    PauseMax: 0.5
    LimitedTime: 0
    LimitedTimeTotal: 0
    LimitedTimeFalloff:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    LimitedTimeResetValue: 1
    LimitedTimeLeft: 0
    NoiseFrequencyMin: {x: 0, y: 0, z: 0}
    NoiseFrequencyMax: {x: 1, y: 1, z: 1}
    NoiseShiftMin: {x: 0, y: 0, z: 0}
    NoiseShiftMax: {x: 0, y: 0, z: 0}
  ScaleWiggleProperties:
    WigglePermitted: 1
    WiggleType: 1
    UseUnscaledTime: 0
    StartWigglingAutomatically: 1
    SmoothPingPong: 1
    UseSpeedCurve: 0
    SpeedCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    FrequencyMin: 0
    FrequencyMax: 1
    AmplitudeMin: {x: 0, y: 0, z: 0}
    AmplitudeMax: {x: 1, y: 1, z: 1}
    RelativeAmplitude: 1
    Curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    RemapCurveZeroMin: {x: 0, y: 0, z: 0}
    RemapCurveZeroMax: {x: 0, y: 0, z: 0}
    RemapCurveOneMin: {x: 1, y: 1, z: 1}
    RemapCurveOneMax: {x: 1, y: 1, z: 1}
    RelativeCurveAmplitude: 1
    CurvePingPong: 0
    PauseMin: 0
    PauseMax: 0
    LimitedTime: 0
    LimitedTimeTotal: 0
    LimitedTimeFalloff:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    LimitedTimeResetValue: 1
    LimitedTimeLeft: 0
    NoiseFrequencyMin: {x: 0, y: 0, z: 0}
    NoiseFrequencyMax: {x: 1, y: 1, z: 1}
    NoiseShiftMin: {x: 0, y: 0, z: 0}
    NoiseShiftMax: {x: 0, y: 0, z: 0}
  DebugWiggleDuration: 2
--- !u!1 &5265625615195376083
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 40694071891407375}
  - component: {fileID: 2367100571923349812}
  - component: {fileID: 4976850178260999481}
  m_Layer: 0
  m_Name: FeelBounceTinyRock
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &40694071891407375
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5265625615195376083}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 2866507148317891817}
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!65 &2367100571923349812
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5265625615195376083}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 0.005, y: 0.005, z: 0.005}
  m_Center: {x: 0, y: 0.0025, z: 0}
--- !u!54 &4976850178260999481
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5265625615195376083}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
