﻿BEGIN_OPTIONS
	Workflow "Unlit"
	Alpha "Blend"
END_OPTIONS

BEGIN_PASS("All")
	Cull [_SGT_Cull]
END_PASS

BEGIN_PROPERTIES
	[Enum(UnityEngine.Rendering.CullMode)] _SGT_Cull ("Cull", Int) = 2
	[NoScaleOffset]_SGT_DepthTex("Depth Tex", 2D) = "white" {}

	[Header(NEAR FADE)]
	[Toggle(_SGT_NEAR)] _SGT_Near ("	Enable", Float) = 0
	_SGT_NearRangeRecip("	Near Range Recip", Float) = 1
	[NoScaleOffset]_SGT_NearTex("	Near Tex", 2D) = "white" {}

	[Header(DETAIL)]
	[Toggle(_SGT_DETAIL)] _SGT_Detail ("	Enable", Float) = 0
	_SGT_DetailStrength("	Detail Strength", Float) = 5
	_SGT_DetailTiling("	Detail Tiling", Float) = 1
	[NoScaleOffset]_SGT_DetailTex("	Detail Tex (A)", 2D) = "white" {}

	[Header(LIGHTING)]
	[Toggle(_SGT_LIGHTING)] _SGT_Lighting ("	Enable", Float) = 0
	[HDR][Gamma]_SGT_AmbientColor("	Ambient Color", Color) = (0, 0, 0, 0)
	[NoScaleOffset]_SGT_LightingTex("	Lighting Tex", 2D) = "white" {}
END_PROPERTIES

BEGIN_DEFINES
	#pragma shader_feature_local _SGT_NEAR
	#pragma shader_feature_local _SGT_DETAIL
	#pragma shader_feature_local _SGT_LIGHTING
	#pragma multi_compile_local _ _SGT_SOFTNESS
END_DEFINES

BEGIN_CBUFFER
	float4      _SGT_Color; // Auto
	float       _SGT_Brightness; // Auto
	samplerCUBE _SGT_MainTex; // Auto
	sampler2D   _SGT_DepthTex;

	sampler2D _SGT_NearTex;
	float     _SGT_NearRangeRecip;

	sampler2D _SGT_DetailTex;
	float     _SGT_DetailStrength;
	float     _SGT_DetailTiling;

	sampler2D _SGT_LightingTex;
	float3    _SGT_AmbientColor;

	float _SGT_SoftParticlesFactor; // Auto
	
	int _SGT_Cull; // Auto
END_CBUFFER

BEGIN_CODE
	float4 sample2(sampler2D tex, float4 coords, float polar)
	{
		float4 tex1 = tex2D(tex, coords.xy);
		float4 tex2 = tex2D(tex, coords.zw);

		return lerp(tex1, tex2, polar);
	}

	void ModifyVertex(inout VertexData v, inout ExtraV2F e)
	{
		#if __SGT_MATRIX
			float4 wpos   = SGT_O2W(v.vertex);
			float3 wnorm  = normalize(mul((float3x3)UNITY_MATRIX_M, v.normal));
			float3 wcam   = _WorldSpaceCameraPos;
			float2 coord0 = v.texcoord0.xy;

			v.texcoord1.xyz = wpos.xyz - wcam;
			v.texcoord0.xy  = abs(dot(wnorm, normalize(-v.texcoord1.xyz)));

			#if _SGT_DETAIL
				v.texcoord0.y = saturate((abs(coord0.y - 0.5f) - 0.2f) * 30.0f);
				v.texcoord2 = float4(coord0.x * 2.0f, coord0.y, v.vertex.xz * 0.5f) * _SGT_DetailTiling;
			#endif

			#if _SGT_SOFTNESS
				v.texcoord3.z = -SGT_O2V(v.vertex).z;
			#endif
		#endif
	}

	void SurfaceFunction(inout Surface o, inout ShaderData d)
	{
		#if __SGT_OUTPUT
			float4 depth = tex2D(_SGT_DepthTex, d.texcoord0.xx);
			float4 main  = _SGT_Color * texCUBE(_SGT_MainTex, d.localSpaceNormal) * depth;

			main.rgb *= _SGT_Brightness;

			#if _SGT_NEAR
				float2 near01 = length(d.texcoord1.xyz) * _SGT_NearRangeRecip;
				float  near   = tex2D(_SGT_NearTex, near01).a;
				main.a *= near;
			#endif

			#if _SGT_DETAIL
				float detail = sample2(_SGT_DetailTex, d.texcoord2, d.texcoord0.y).w - 0.5f;
				main.a += (1.0f - main.a) * detail * _SGT_DetailStrength * main.a;
			#endif

			#if _SGT_SOFTNESS
				float sceneZ = GetLinearEyeDepth(d.screenUV);
				float partZ = d.texcoord3.z;

				main.a *= smoothstep(0.0f, 1.0f, _SGT_SoftParticlesFactor * (sceneZ - partZ));
			#endif

			float4 finalColor = main;

			#if __SGT_LIGHTANDSHADOW
				#if _SGT_LIGHTING
					finalColor.rgb *= _SGT_AmbientColor;

					float4 lighting = 0.0f;

					for (int i = 0; i < _SGT_LightCount; i++)
					{
						float theta = dot(d.worldSpaceNormal, _SGT_LightDirection[i]) * 0.5f + 0.5f;

						lighting += tex2D(_SGT_LightingTex, theta) * main * _SGT_LightColor[i];
					}

					finalColor += lighting * SGT_ShadowColor(d.worldSpacePosition);
				#else
					finalColor = SGT_ModifyUnlitOutput(finalColor);
				#endif
			#endif

			finalColor.a = saturate(finalColor.a);

			SGT_OutputWithAlpha(o, finalColor);
		#endif
	}
END_CODE