%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e78934177bebe0545ab566bb787b0282, type: 3}
  m_Name: Readme
  m_EditorClassIdentifier: 
  Icon: {fileID: 2800000, guid: 76ea654e31068554db2a5cd7babaef95, type: 3}
  Title: Water Flow
  Description:
    Heading: 
    Text: This sample animates a river plane making use of flow maps.
    LinkText: 
    Url: 
  PropertiesHeader:
    Heading: Let It Flow Properties
    Text: 
    LinkText: 
    Url: 
  Properties:
  - Heading: Size
    Text: Overall scale value for used textture coordinates.
    LinkText: 
    Url: 
  - Heading: Flow Map
    Text: Texture containing flow map. (Ony R and G channels used).
    LinkText: 
    Url: 
  - Heading: Flow Speed
    Text: Value to control flow speed animation.
    LinkText: 
    Url: 
  - Heading: Flow Strength
    Text: Vector value to tweak flow strength for each direction separatly.
    LinkText: 
    Url: 
  - Heading: Normal
    Text: Normals texture to be applied to surface.
    LinkText: 
    Url: 
  - Heading: Normal Scale
    Text: Scale value for Normals texture sampling.
    LinkText: 
    Url: 
  - Heading: Normal Strength
    Text: Value to tweak overall flow strength.
    LinkText: 
    Url: 
  - Heading: Tiling
    Text: Tiling value to be applied to Normals texture sampling.
    LinkText: 
    Url: 
  - Heading: River Color
    Text: Color value to tint river.
    LinkText: 
    Url: 
  - Heading: Fade Distance
    Text: Distance value to set alpha behavior on river/intersection.
    LinkText: 
    Url: 
  - Heading: Smoothness
    Text: Overall smoothness value.
    LinkText: 
    Url: 
  - Heading: Metallic
    Text: Overall metallic value.
    LinkText: 
    Url: 
  AdditionalProperties:
  - BlockHeader:
      Heading: Totem Properties
      Text: 
      LinkText: 
      Url: 
    BlockContent:
    - Heading: Albedo
      Text: Albedo texture to be applied to surface.
      LinkText: 
      Url: 
    - Heading: Tint
      Text: Color value to tint albedo channel.
      LinkText: 
      Url: 
    - Heading: Normal
      Text: Normals texture to be applied to surface.
      LinkText: 
      Url: 
    - Heading: Occlusion
      Text: Occlusion texture to be applied to surface.
      LinkText: 
      Url: 
    - Heading: Specular
      Text: Specular texture to be applied to surface.
      LinkText: 
      Url: 
  AdditionalScripts: []
  LoadedLayout: 0
  RPType: 3
