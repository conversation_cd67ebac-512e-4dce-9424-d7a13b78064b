%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: GSF GGX
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor v1.9.5.1\n// Available at the
    Unity Asset Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=19501\nNode;AmplifyShaderEditor.CommentaryNode;76;-1408,-256;Inherit;False;1868.266;580.3206;Math;18;0;18;79;30;69;24;27;73;78;77;55;71;53;54;22;20;72;177;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;80;-2494.681,-256;Inherit;False;1023.904;543.1509;Dot
    Products;9;108;144;145;130;138;137;129;174;175;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;75;-1408,-560;Inherit;False;647.3301;250.2299;GSF
    GGX Node;1;74;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;77;-1232,-192;Inherit;False;200.5789;133.2894;NdotV;1;170;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;78;-1232,-48;Inherit;False;200.5789;133.2894;NdotL;1;171;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;68;-1408,-736;Inherit;False;553.6827;158.4391;Credits;;0,0,0,0.5019608;Method
    by Jordan Stevens$Link: https://www.jordanstevenstechart.com/physically-based-rendering$$Node
    Made by Tiber Legacy$Special Thanks to Dawie;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;72;-976,-224;Inherit;False;194;100;Vectorized;;0,0,0,0.5019608;to
    simplify and Optimize the Algorithm;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;74;-1152,-512;Inherit;False;320.5612;138.7639;;;0,0,0,1;\"This
    is a refactor of the Walter et al. GSF algorithm by multiplying the GSF by NdotV/NdotV.\"
    - Jordan Stevens;0;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;20;-752,-48;Inherit;False;3;3;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.OneMinusNode;22;-928,0;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleDivideOpNode;54;-160,-176;Inherit;False;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.ScaleNode;53;-752,-176;Inherit;False;2;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;55;96,-176;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;73;-1088,96;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SqrtOpNode;27;-432,96;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;24;-560,96;Inherit;False;3;3;0;FLOAT;0;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.DynamicAppendNode;69;-928,-112;Inherit;False;FLOAT2;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;30;-288,-112;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.StickyNoteNode;79;-1328,176;Inherit;False;204.6;102.6;Roughness;;0,0,0,1;;0;0\nNode;AmplifyShaderEditor.GetLocalVarNode;170;-1200,-144;Inherit;False;129;NdotV;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;171;-1200,0;Inherit;False;130;NdotL;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;129;-1728,-176;Inherit;True;NdotV;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;137;-2176,-96;Inherit;False;NdotV;1;2;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;138;-2176,128;Inherit;False;NdotL;1;1;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;130;-1728,48;Inherit;True;NdotL;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;145;-2016,48;Inherit;False;Option;False;0;2;1;In
    0;In 1;Instance;144;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;144;-2016,-176;Inherit;False;Custom
    Dot Products;True;0;2;1;In 0;In 1;Object;-1;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;108;-2416,-176;Inherit;False;Normal;3;0;False;1;0;FLOAT3;0,0,1;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionInput;18;-1280,96;Inherit;False;Roughness;1;3;False;1;0;FLOAT;0.5;False;1;FLOAT;0\nNode;AmplifyShaderEditor.BreakToComponentsNode;71;-16,-176;Inherit;False;FLOAT2;1;0;FLOAT2;0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.FunctionNode;174;-2240,-176;Inherit;False;NdotV;-1;;3;65be4ec269a534f4e8867af2f999aaa9;4,27,1,377,0,452,0,451,0;3;230;FLOAT3;0,0,1;False;443;FLOAT3;0,0,0;False;415;FLOAT;1E-37;False;1;FLOAT;23\nNode;AmplifyShaderEditor.FunctionNode;175;-2240,48;Inherit;False;NdotL;-1;;4;6d7ee1aa8c79587448475fa2b37affc1;5,27,1,381,0,460,0,461,0,463,0;4;230;FLOAT3;0,0,1;False;462;FLOAT3;0,0,0;False;454;FLOAT3;0,0,0;False;416;FLOAT;1E-37;False;1;FLOAT;111\nNode;AmplifyShaderEditor.StickyNoteNode;177;-880,176;Inherit;False;150;100;Add
    0.1;;0,0,0,0.5019608;To Prevent NaN in Division and Output;0;0\nNode;AmplifyShaderEditor.RangedFloatNode;176;-720,176;Inherit;False;Constant;_0_1;_0_1;0;0;Create;True;0;0;0;False;0;False;0.1;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;0;256,-176;Inherit;True;True;-1;Out;0;False;1;0;FLOAT;0;False;1;FLOAT;0\nWireConnection;20;0;69;0\nWireConnection;20;1;69;0\nWireConnection;20;2;22;0\nWireConnection;22;0;73;0\nWireConnection;54;0;53;0\nWireConnection;54;1;30;0\nWireConnection;53;0;69;0\nWireConnection;55;0;71;0\nWireConnection;55;1;71;1\nWireConnection;73;0;18;0\nWireConnection;73;1;18;0\nWireConnection;27;0;24;0\nWireConnection;24;0;73;0\nWireConnection;24;1;20;0\nWireConnection;24;2;176;0\nWireConnection;69;0;170;0\nWireConnection;69;1;171;0\nWireConnection;30;0;69;0\nWireConnection;30;1;27;0\nWireConnection;129;0;144;0\nWireConnection;130;0;145;0\nWireConnection;145;0;175;111\nWireConnection;145;1;138;0\nWireConnection;144;0;174;23\nWireConnection;144;1;137;0\nWireConnection;71;0;54;0\nWireConnection;174;230;108;0\nWireConnection;175;230;108;0\nWireConnection;0;0;55;0\nASEEND*/\n//CHKSM=721FAB5F06FC73432BF1600909AD0B2378A78B24"
  m_functionName: 
  m_description: 'GSF GGX Node

'
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 0
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_customEdited: 0
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 0
  m_headerStyle: 2
  m_headerColor: {r: 0.7529412, g: 0.6784314, b: 0, a: 1}
  m_customNodeCategory: Amplify Shader Pack
  m_previewPosition: 0
  m_hidden: 0
  m_url: 
