# Component Migration Quick Reference

## 🔄 **Component Replacement Map**

| Remove This | Add This | Notes |
|-------------|----------|-------|
| `Koreographer` | `Cadance` | Main singleton |
| `SimpleMusicPlayer` | `AudioSourceCadancePlayer` | Music playback |
| `AudioSourceVisor` | `AudioSourceCadancePlayer` | Audio integration |
| `KoreographedEventEmitter` | `CadancedEventEmitter` | Event emission |

## 📍 **Target Scenes**

1. `Assets/_Scenes/Levels/Ouroboros - Base.unity`
2. `Assets/_Scenes/Levels/Ouroboros - Scene 1.unity`

## ⚡ **Quick Steps**

1. **Open Scene**
2. **Find Component** (usually on MusicManager)
3. **Note Settings** (screenshot recommended)
4. **Remove Old Component**
5. **Add New Component**
6. **Copy Settings**
7. **Save Scene** (Ctrl+S)
8. **Test** (Enter Play Mode)

## 🚨 **Safety Reminders**

- ✅ **Save after each component**
- ✅ **Test in Play Mode**
- ✅ **Git backup available**
- ⚠️ **CadanceAssets needed in Phase 4**

## 🔍 **Common Locations**

- **MusicManager GameObject** - Main Koreographer components
- **Audio GameObjects** - AudioSourceVisor components  
- **VFX GameObjects** - Event emitters
- **Time Integration** - Already migrated in Phase 2

## ✅ **Verification**

- [ ] No red missing component icons
- [ ] Console shows no errors
- [ ] Play Mode enters successfully
- [ ] All GameObjects still active
