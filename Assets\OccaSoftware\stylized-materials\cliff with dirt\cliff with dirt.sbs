<?xml version="1.0" encoding="UTF-8"?><package><identifier v="Unsaved Package"/><formatVersion v="1.1.0.202201"/><updaterVersion v="1.1.0.202201"/><fileUID v="{84b9ec4e-af4c-4574-952a-e3d014b1549e}"/><versionUID v="0"/><dependencies><dependency><filename v="sbs://pattern_tile_sampler.sbs"/><uid v="1486812999"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://edge_detect.sbs"/><uid v="1487424625"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_crystal_1.sbs"/><uid v="1486811282"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://blur_hq.sbs"/><uid v="1486811589"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://flood_fill_2.sbs"/><uid v="1323881949"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://slope_blur.sbs"/><uid v="1487497056"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_perlin_noise.sbs"/><uid v="1487497977"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://pattern_polygon_2.sbs"/><uid v="1487691967"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_creased.sbs"/><uid v="1487699857"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_clouds_2.sbs"/><uid v="1487700427"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://hbao_2.sbs"/><uid v="1297162667"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://curvature_smooth.sbs"/><uid v="1487705407"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://height_blend.sbs"/><uid v="1487713662"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency></dependencies><content><graph><identifier v="cliff_with_dirt"/><uid v="1487420008"/><graphtype v="material"/><graphOutputs><graphoutput><identifier v="basecolor"/><uid v="1213284336"/><attributes><label v="Base Color"/></attributes><usages><usage><components v="RGBA"/><name v="baseColor"/></usage></usages><group v="Material"/></graphoutput><graphoutput><identifier v="normal"/><uid v="1213284338"/><attributes><label v="Normal"/></attributes><usages><usage><components v="RGBA"/><name v="normal"/></usage></usages><group v="Material"/></graphoutput><graphoutput><identifier v="roughness"/><uid v="1213284340"/><attributes><label v="Roughness"/></attributes><usages><usage><components v="RGBA"/><name v="roughness"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput><graphoutput><identifier v="height"/><uid v="1279137031"/><attributes><label v="Height"/></attributes><usages><usage><components v="RGBA"/><name v="height"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput><graphoutput><identifier v="ambientocclusion"/><uid v="1359211721"/><attributes><label v="Ambient Occlusion"/></attributes><usages><usage><components v="RGBA"/><name v="ambientOcclusion"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput></graphOutputs><compNodes><compNode><uid v="1213284337"/><GUILayout><gpos v="-48 -240 0"/></GUILayout><connections><connection><identifier v="inputNodeOutput"/><connRef v="1487719592"/><connRefOutput v="1487719593"/></connection></connections><compImplementation><compOutputBridge><output v="1213284336"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1213284339"/><GUILayout><gpos v="-48 16 0"/></GUILayout><connections><connection><identifier v="inputNodeOutput"/><connRef v="1359211383"/><connRefOutput v="1359211384"/></connection></connections><compImplementation><compOutputBridge><output v="1213284338"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1213284341"/><GUILayout><gpos v="-29.6098194 192.968246 0"/></GUILayout><connections><connection><identifier v="inputNodeOutput"/><connRef v="1487437633"/><connRefOutput v="1487437634"/></connection></connections><compImplementation><compOutputBridge><output v="1213284340"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1279137030"/><GUILayout><gpos v="-48 848 0"/></GUILayout><connections><connection><identifier v="inputNodeOutput"/><connRef v="1487437633"/><connRefOutput v="1487437634"/></connection></connections><compImplementation><compOutputBridge><output v="1279137031"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1359211383"/><GUILayout><gpos v="-848 16 0"/></GUILayout><compOutputs><compOutput><uid v="1359211384"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487437633"/><connRefOutput v="1487437634"/></connection></connections><compImplementation><compFilter><filter v="normal"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="10"/></paramValue></parameter><parameter><name v="input2alpha"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1359211720"/><GUILayout><gpos v="-48 624 0"/></GUILayout><connections><connection><identifier v="inputNodeOutput"/><connRef v="1487703978"/><connRefOutput v="1487703979"/></connection></connections><compImplementation><compOutputBridge><output v="1359211721"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1487424389"/><GUILayout><gpos v="-4656 -189.135849 0"/></GUILayout><compOutputs><compOutput><uid v="1487424390"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1486812999"/><parameters><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="8"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="9"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="5.01999998"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.370000005"/></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="4.71999979"/></paramValue></parameter><parameter><name v="color_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.629999995"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487424390"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487424666"/><GUILayout><gpos v="-4161.92627 -176 0"/></GUILayout><compOutputs><compOutput><uid v="1487424667"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input"/><connRef v="1487424909"/><connRefOutput v="1487424910"/></connection></connections><compImplementation><compInstance><path v="pkg:///edge_detect?dependency=1487424625"/><parameters><parameter><name v="edge_width"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="3.6099999"/></paramValue></parameter><parameter><name v="edge_roundness"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.34000003"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487424667"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487424909"/><GUILayout><gpos v="-4337.08008 -176 0"/></GUILayout><compOutputs><compOutput><uid v="1487424910"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487424389"/><connRefOutput v="1487424390"/></connection><connection><identifier v="inputintensity"/><connRef v="1487432349"/><connRefOutput v="1487432350"/></connection></connections><compImplementation><compFilter><filter v="directionalwarp"/><parameters><parameter><name v="warpangle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.162055552"/></paramValue></parameter><parameter><name v="randomseed"/><relativeTo v="2"/><paramValue><constantValueInt32 v="10"/></paramValue></parameter><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="20"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487424943"/><GUILayout><gpos v="-4609.92627 48 0"/></GUILayout><compOutputs><compOutput><uid v="1487424944"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///crystal_1?dependency=1486811282"/><parameters><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueInt32 v="7"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487424944"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487425265"/><GUILayout><gpos v="-4033.92627 -176 0"/></GUILayout><compOutputs><compOutput><uid v="1487425266"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="Source"/><connRef v="1487424666"/><connRefOutput v="1487424667"/></connection></connections><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1486811589"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="4.17999983"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487425266"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487432349"/><GUILayout><gpos v="-4449.92627 48 0"/></GUILayout><compOutputs><compOutput><uid v="1487432350"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="Source"/><connRef v="1487424943"/><connRefOutput v="1487424944"/></connection></connections><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1486811589"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="3.06999993"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487432350"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487436181"/><GUILayout><gpos v="-3745.92627 -159.07608 0"/></GUILayout><compOutputs><compOutput><uid v="1487436182"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="mask"/><connRef v="1487425265"/><connRefOutput v="1487425266"/></connection></connections><compImplementation><compFilter><filter v="distance"/><parameters><parameter><name v="colorswitch"/><paramValue><constantValueBool v="0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487442222"/><GUILayout><gpos v="-3935.39233 51.8271027 0"/></GUILayout><compOutputs><compOutput><uid v="1487442223"/><comptype v="1"/></compOutput><compOutput><uid v="1487442224"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="mask"/><connRef v="1487425265"/><connRefOutput v="1487425266"/></connection></connections><compImplementation><compInstance><path v="pkg:///flood_fill?dependency=1323881949"/><parameters/><outputBridgings><outputBridging><uid v="1487442223"/><identifier v="output"/></outputBridging><outputBridging><uid v="1487442224"/><identifier v="referenceMap"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487442502"/><GUILayout><gpos v="-3792 48 0"/></GUILayout><compOutputs><compOutput><uid v="1487442503"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input"/><connRef v="1487442222"/><connRefOutput v="1487442223"/></connection></connections><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487442503"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487442713"/><GUILayout><gpos v="-3649.92627 51.6407776 0"/></GUILayout><compOutputs><compOutput><uid v="1487442714"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="source"/><connRef v="1487442502"/><connRefOutput v="1487442503"/></connection><connection><identifier v="mask"/><connRef v="1487425265"/><connRefOutput v="1487425266"/></connection></connections><compImplementation><compFilter><filter v="distance"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1487442832"/><GUILayout><gpos v="-3792.76196 272 0"/></GUILayout><compOutputs><compOutput><uid v="1487442503"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input"/><connRef v="1487442833"/><connRefOutput v="1487442223"/></connection></connections><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="angle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.305555552"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487442503"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487442833"/><GUILayout><gpos v="-3937.92627 272 0"/></GUILayout><compOutputs><compOutput><uid v="1487442223"/><comptype v="1"/></compOutput><compOutput><uid v="1487442224"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="mask"/><connRef v="1487425265"/><connRefOutput v="1487425266"/></connection></connections><compImplementation><compInstance><path v="pkg:///flood_fill?dependency=1323881949"/><parameters/><outputBridgings><outputBridging><uid v="1487442223"/><identifier v="output"/></outputBridging><outputBridging><uid v="1487442224"/><identifier v="referenceMap"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487442834"/><GUILayout><gpos v="-3649.92627 272 0"/></GUILayout><compOutputs><compOutput><uid v="1487442714"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="source"/><connRef v="1487442832"/><connRefOutput v="1487442503"/></connection><connection><identifier v="mask"/><connRef v="1487425265"/><connRefOutput v="1487425266"/></connection></connections><compImplementation><compFilter><filter v="distance"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1487428995"/><GUILayout><gpos v="-3635.09058 496 0"/></GUILayout><compOutputs><compOutput><uid v="1487442714"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="source"/><connRef v="1487428996"/><connRefOutput v="1487442503"/></connection><connection><identifier v="mask"/><connRef v="1487425265"/><connRefOutput v="1487425266"/></connection></connections><compImplementation><compFilter><filter v="distance"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1487428996"/><GUILayout><gpos v="-3777.92627 496 0"/></GUILayout><compOutputs><compOutput><uid v="1487442503"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input"/><connRef v="1487428997"/><connRefOutput v="1487442223"/></connection></connections><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="angle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.36944443"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487442503"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487428997"/><GUILayout><gpos v="-3923.09058 496 0"/></GUILayout><compOutputs><compOutput><uid v="1487442223"/><comptype v="1"/></compOutput><compOutput><uid v="1487442224"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="mask"/><connRef v="1487425265"/><connRefOutput v="1487425266"/></connection></connections><compImplementation><compInstance><path v="pkg:///flood_fill?dependency=1323881949"/><parameters/><outputBridgings><outputBridging><uid v="1487442223"/><identifier v="output"/></outputBridging><outputBridging><uid v="1487442224"/><identifier v="referenceMap"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487429361"/><GUILayout><gpos v="-3361.92627 -161.088806 0"/></GUILayout><compOutputs><compOutput><uid v="1487429362"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="destination"/><connRef v="1487436181"/><connRefOutput v="1487436182"/></connection><connection><identifier v="source"/><connRef v="1487436402"/><connRefOutput v="1487436403"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487436402"/><GUILayout><gpos v="-3521.92627 48 0"/></GUILayout><compOutputs><compOutput><uid v="1487436403"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487442713"/><connRefOutput v="1487442714"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.569164276 0.569164276 0.569164276 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487436540"/><GUILayout><gpos v="-3233.92627 144 0"/></GUILayout><compOutputs><compOutput><uid v="1487436541"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="destination"/><connRef v="1487429361"/><connRefOutput v="1487429362"/></connection><connection><identifier v="source"/><connRef v="1487436721"/><connRefOutput v="1487436722"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487436721"/><GUILayout><gpos v="-3521.92627 272 0"/></GUILayout><compOutputs><compOutput><uid v="1487436722"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487442834"/><connRefOutput v="1487442714"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.240634009 0.240634009 0.240634009 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487436869"/><GUILayout><gpos v="-3041.92627 304 0"/></GUILayout><compOutputs><compOutput><uid v="1487436870"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="destination"/><connRef v="1487436540"/><connRefOutput v="1487436541"/></connection><connection><identifier v="source"/><connRef v="1487437073"/><connRefOutput v="1487437074"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487437073"/><GUILayout><gpos v="-3489.92627 496 0"/></GUILayout><compOutputs><compOutput><uid v="1487437074"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487428995"/><connRefOutput v="1487442714"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.427953899 0.427953899 0.427953899 0.5"/></paramValue></parameter><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.246397689 0.246397689 0.246397689 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487437633"/><GUILayout><gpos v="-1602.77124 208 0"/></GUILayout><compOutputs><compOutput><uid v="1487437634"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487713718"/><connRefOutput v="1487713719"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1487497686"/><GUILayout><gpos v="-2881.92627 400 0"/></GUILayout><compOutputs><compOutput><uid v="1487497687"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="Source"/><connRef v="1487436869"/><connRefOutput v="1487436870"/></connection><connection><identifier v="Effect"/><connRef v="1487498286"/><connRefOutput v="1487498287"/></connection></connections><compImplementation><compInstance><path v="pkg:///slope_blur_grayscale_2?dependency=1487497056"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="32"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.449999988"/></paramValue></parameter><parameter><name v="mode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487497687"/><identifier v="Slope_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487498286"/><GUILayout><gpos v="-3026.17139 592 0"/></GUILayout><compOutputs><compOutput><uid v="1487498287"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///perlin_noise?dependency=1487497977"/><parameters><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueInt32 v="14"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487498287"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487499217"/><GUILayout><gpos v="-2640 194.456802 0"/></GUILayout><compOutputs><compOutput><uid v="1487499218"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="destination"/><connRef v="1487436869"/><connRefOutput v="1487436870"/></connection><connection><identifier v="source"/><connRef v="1487499314"/><connRefOutput v="1487499315"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487499314"/><GUILayout><gpos v="-2736 400 0"/></GUILayout><compOutputs><compOutput><uid v="1487499315"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487497686"/><connRefOutput v="1487497687"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="randomseed"/><relativeTo v="2"/><paramValue><constantValueInt32 v="10"/></paramValue></parameter><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.5625 0.5625 0.5625 0.5"/></paramValue></parameter><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.18871595 0.18871595 0.18871595 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487690913"/><GUILayout><gpos v="-2448 208 0"/></GUILayout><compOutputs><compOutput><uid v="1487690914"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487499217"/><connRefOutput v="1487499218"/></connection><connection><identifier v="inputintensity"/><connRef v="1487499217"/><connRefOutput v="1487499218"/></connection></connections><compImplementation><compFilter><filter v="directionalwarp"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="10.4499998"/></paramValue></parameter><parameter><name v="warpangle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.159999996"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487691403"/><GUILayout><gpos v="-2608 624 0"/></GUILayout><compOutputs><compOutput><uid v="1487691404"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="pattern_input"/><connRef v="1487692175"/><connRefOutput v="1487692176"/></connection></connections><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1486812999"/><parameters><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="size_random"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.5 0.5"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.62"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.519999981"/></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="7.40999985"/></paramValue></parameter><parameter><name v="mask_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.870000005"/></paramValue></parameter><parameter><name v="rotation_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.589999974"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487691404"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487692175"/><GUILayout><gpos v="-2768 656 0"/></GUILayout><compOutputs><compOutput><uid v="1487692176"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///polygon_2?dependency=1487691967"/><parameters><parameter><name v="Scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.50999999"/></paramValue></parameter><parameter><name v="Curve"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="Gradient"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.140000001"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487692176"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487693524"/><GUILayout><gpos v="-2256 208 0"/></GUILayout><compOutputs><compOutput><uid v="1487693525"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="destination"/><connRef v="1487690913"/><connRefOutput v="1487690914"/></connection><connection><identifier v="source"/><connRef v="1487691403"/><connRefOutput v="1487691404"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0199999996"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487694525"/><GUILayout><gpos v="-1616 -112 0"/></GUILayout><compOutputs><compOutput><uid v="1487694526"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487693524"/><connRefOutput v="1487693525"/></connection></connections><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1487694620"/><paramsArrayCells><paramsArrayCell><uid v="1487695180"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.543478251 0.407718629 0.287537932 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487695181"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.38818568"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.586956501 0.45119679 0.331016183 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487695182"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.953586459"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.639130414 0.491303176 0.360439837 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487695183"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.721518993"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.639130414 0.491303176 0.360439837 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1487699725"/><GUILayout><gpos v="-2544 -528 0"/></GUILayout><compOutputs><compOutput><uid v="1487699726"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///perlin_noise?dependency=1487497977"/><parameters><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487699726"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487699760"/><GUILayout><gpos v="-2384 -496 0"/></GUILayout><compOutputs><compOutput><uid v="1487699761"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="Source"/><connRef v="1487699725"/><connRefOutput v="1487699726"/></connection><connection><identifier v="Effect"/><connRef v="1487700046"/><connRefOutput v="1487700047"/></connection></connections><compImplementation><compInstance><path v="pkg:///slope_blur_grayscale_2?dependency=1487497056"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="32"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487699761"/><identifier v="Slope_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487700046"/><GUILayout><gpos v="-2544 -368 0"/></GUILayout><compOutputs><compOutput><uid v="1487700047"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///creased?dependency=1487699857"/><parameters><parameter><name v="warp_intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487700047"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487700388"/><GUILayout><gpos v="-2256 -496 0"/></GUILayout><compOutputs><compOutput><uid v="1487700389"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="Source"/><connRef v="1487699760"/><connRefOutput v="1487699761"/></connection><connection><identifier v="Effect"/><connRef v="1487700934"/><connRefOutput v="1487700935"/></connection></connections><compImplementation><compInstance><path v="pkg:///slope_blur_grayscale_2?dependency=1487497056"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="32"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="6.23999977"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487700389"/><identifier v="Slope_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487700934"/><GUILayout><gpos v="-2384 -368 0"/></GUILayout><compOutputs><compOutput><uid v="1487700935"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///clouds_2?dependency=1487700427"/><parameters/><outputBridgings><outputBridging><uid v="1487700935"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487701253"/><GUILayout><gpos v="-2128 -496 0"/></GUILayout><compOutputs><compOutput><uid v="1487701254"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="Source"/><connRef v="1487700388"/><connRefOutput v="1487700389"/></connection></connections><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1486811589"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.689999998"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487701254"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487701428"/><GUILayout><gpos v="-2000 -496 0"/></GUILayout><compOutputs><compOutput><uid v="1487701429"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="Source"/><connRef v="1487701253"/><connRefOutput v="1487701254"/></connection><connection><identifier v="Effect"/><connRef v="1487701253"/><connRefOutput v="1487701254"/></connection></connections><compImplementation><compInstance><path v="pkg:///slope_blur_grayscale_2?dependency=1487497056"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="32"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="12.9899998"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487701429"/><identifier v="Slope_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487701488"/><GUILayout><gpos v="-1840 -496 0"/></GUILayout><compOutputs><compOutput><uid v="1487701489"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487701428"/><connRefOutput v="1487701429"/></connection></connections><compImplementation><compFilter><filter v="gradient"/><parameters><parameter><name v="addressingrepeat"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="colorswitch"/><paramValue><constantValueBool v="1"/></paramValue></parameter></parameters><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1487701548"/><paramsArrayCells><paramsArrayCell><uid v="1487701981"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.592156887 0.509803951 0.431372553 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487701982"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.0363128483"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.556862772 0.431372553 0.321568638 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487701983"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.0698323995"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.490196079 0.368627459 0.258823544 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487701984"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.078212291"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.635294139 0.505882382 0.392156869 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487701985"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.108938545"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.494117647 0.376470596 0.274509817 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487701986"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.201117307"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.600000024 0.494117647 0.403921574 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487701987"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.223463684"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.490196079 0.388235301 0.298039228 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487701988"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.237430155"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.576470613 0.482352942 0.396078438 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487701989"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.251396626"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.509803951 0.403921574 0.282352954 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487701990"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.405027926"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.592156887 0.494117647 0.380392164 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487701991"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.513966441"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.494117647 0.392156869 0.282352954 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487701992"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.567039073"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.596078455 0.490196079 0.400000006 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487701993"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.606145263"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.517647088 0.403921574 0.290196091 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487701994"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.656424582"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.619607866 0.501960814 0.368627459 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487701995"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.681564212"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.533333361 0.403921574 0.305882365 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487701996"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.768156409"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.635294139 0.517647088 0.388235301 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487701997"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.793296039"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.490196079 0.380392164 0.262745112 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487701998"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.913407803"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.53725493 0.454901963 0.376470596 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487701999"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.955307245"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.643137276 0.513725519 0.407843143 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487702000"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.986033499"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.650980413 0.533333361 0.423529416 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487702001"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.991620064"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.203921571 0.125490203 0.0509803928 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487702002"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.997206688"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.215686277 0.192156866 0.168627456 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1487702288"/><GUILayout><gpos v="-1410.86414 -176 0"/></GUILayout><compOutputs><compOutput><uid v="1487702289"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="destination"/><connRef v="1487694525"/></connection><connection><identifier v="source"/><connRef v="1487701488"/><connRefOutput v="1487701489"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.200000003"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487703978"/><GUILayout><gpos v="-1168 272 0"/></GUILayout><compOutputs><compOutput><uid v="1487703979"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input"/><connRef v="1487437633"/><connRefOutput v="1487437634"/></connection></connections><compImplementation><compInstance><path v="pkg:///hbao?dependency=1297162667"/><parameters/><outputBridgings><outputBridging><uid v="1487703979"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487704933"/><GUILayout><gpos v="-1026.77124 208 0"/></GUILayout><compOutputs><compOutput><uid v="1487704934"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487703978"/><connRefOutput v="1487703979"/></connection></connections><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1487704959"/><paramsArrayCells><paramsArrayCell><uid v="1487704967"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.768627465 0.870588243 0.937254906 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487704968"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.295652181 0.292214364 0.292214364 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1487705099"/><GUILayout><gpos v="-1040 -144 0"/></GUILayout><compOutputs><compOutput><uid v="1487705100"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="source"/><connRef v="1487704933"/><connRefOutput v="1487704934"/></connection><connection><identifier v="destination"/><connRef v="1487717578"/><connRefOutput v="1487717579"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="9"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.409999996"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487706181"/><GUILayout><gpos v="-752 144 0"/></GUILayout><compOutputs><compOutput><uid v="1487706182"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input"/><connRef v="1359211383"/></connection></connections><compImplementation><compInstance><path v="pkg:///curvature_smooth?dependency=1487705407"/><parameters/><outputBridgings><outputBridging><uid v="1487706182"/><identifier v="height"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487706200"/><GUILayout><gpos v="-624 144 0"/></GUILayout><compOutputs><compOutput><uid v="1487706201"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487706181"/><connRefOutput v="1487706182"/></connection></connections><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1487706227"/><paramsArrayCells><paramsArrayCell><uid v="1487706228"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.566455662"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0 0 0 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487706229"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.607594907"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="1 1 1 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1487706483"/><GUILayout><gpos v="-656 -224.565018 0"/></GUILayout><compOutputs><compOutput><uid v="1487706484"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="destination"/><connRef v="1487705099"/><connRefOutput v="1487705100"/></connection><connection><identifier v="source"/><connRef v="1487706200"/><connRefOutput v="1487706201"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.170000002"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487713718"/><GUILayout><gpos v="-1954.65295 208 0"/></GUILayout><compOutputs><compOutput><uid v="1487713719"/><comptype v="2"/></compOutput><compOutput><uid v="1487713720"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="height_bottom"/><connRef v="1487693524"/><connRefOutput v="1487693525"/></connection><connection><identifier v="height_top"/><connRef v="1487714804"/><connRefOutput v="1487714805"/></connection></connections><compImplementation><compInstance><path v="pkg:///height_blend?dependency=1487713662"/><parameters><parameter><name v="height_position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.179999992"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487713719"/><identifier v="blended_height"/></outputBridging><outputBridging><uid v="1487713720"/><identifier v="height_mask"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487714678"/><GUILayout><gpos v="-2271.58008 912 0"/></GUILayout><compOutputs><compOutput><uid v="1487714679"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///clouds_2?dependency=1487700427"/><parameters/><outputBridgings><outputBridging><uid v="1487714679"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487714685"/><GUILayout><gpos v="-2143.58008 912 0"/></GUILayout><compOutputs><compOutput><uid v="1487714686"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="Source"/><connRef v="1487714678"/><connRefOutput v="1487714679"/></connection></connections><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1486811589"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.11000001"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487714686"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487714719"/><GUILayout><gpos v="-1983.5802 912 0"/></GUILayout><compOutputs><compOutput><uid v="1487714720"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="Source"/><connRef v="1487714685"/><connRefOutput v="1487714686"/></connection><connection><identifier v="Effect"/><connRef v="1487714685"/><connRefOutput v="1487714686"/></connection></connections><compImplementation><compInstance><path v="pkg:///slope_blur_grayscale_2?dependency=1487497056"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="32"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.330000013"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487714720"/><identifier v="Slope_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487714804"/><GUILayout><gpos v="-1823.5802 912 0"/></GUILayout><compOutputs><compOutput><uid v="1487714805"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487714719"/><connRefOutput v="1487714720"/></connection><connection><identifier v="inputintensity"/><connRef v="1487714818"/><connRefOutput v="1487714819"/></connection></connections><compImplementation><compFilter><filter v="directionalwarp"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="29"/></paramValue></parameter><parameter><name v="warpangle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.427777767"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487714818"/><GUILayout><gpos v="-2015.5802 1104 0"/></GUILayout><compOutputs><compOutput><uid v="1487714819"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///creased?dependency=1487699857"/><parameters/><outputBridgings><outputBridging><uid v="1487714819"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487717512"/><GUILayout><gpos v="-1663.5802 976 0"/></GUILayout><compOutputs><compOutput><uid v="1487717513"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487714804"/></connection></connections><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1487717527"/><paramsArrayCells><paramsArrayCell><uid v="1487717538"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.400000006 0.356862754 0.313725501 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487717539"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.44023326"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.501960814 0.443137258 0.372549027 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487717540"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.997084558"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.600000024 0.533333361 0.458823532 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1487717578"/><GUILayout><gpos v="-1232 -80 0"/></GUILayout><compOutputs><compOutput><uid v="1487717579"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="destination"/><connRef v="1487702288"/><connRefOutput v="1487702289"/></connection><connection><identifier v="source"/><connRef v="1487717512"/><connRefOutput v="1487717513"/></connection><connection><identifier v="opacity"/><connRef v="1487713718"/><connRefOutput v="1487713720"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.639999986"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487719302"/><GUILayout><gpos v="-2448 656 0"/></GUILayout><compOutputs><compOutput><uid v="1487719303"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487691403"/><connRefOutput v="1487691404"/></connection></connections><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1487719320"/><paramsArrayCells><paramsArrayCell><uid v="1487719378"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.0443115234"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.819607854 0.75686276 0.75686276 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487719383"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="1 1 1 0"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1487719592"/><GUILayout><gpos v="-352 -224 0"/></GUILayout><compOutputs><compOutput><uid v="1487719593"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="destination"/><connRef v="1487706483"/><connRefOutput v="1487706484"/></connection><connection><identifier v="source"/><connRef v="1487719302"/><connRefOutput v="1487719303"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.319999993"/></paramValue></parameter><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode></compNodes><baseParameters/><options><option><name v="defaultParentSize"/><value v="11x11"/></option><option><name v="export/fromGraph/autoExport"/><value v="false"/></option><option><name v="export/fromGraph/destination"/><value v="D:/work/Annery/stylized material/stylized material/substance designer/final file for Michael/cliff with dirt/cliff with dirt material"/></option><option><name v="export/fromGraph/extension"/><value v="png"/></option><option><name v="export/fromGraph/outputs/ambientocclusion"/><value v="true"/></option><option><name v="export/fromGraph/outputs/basecolor"/><value v="true"/></option><option><name v="export/fromGraph/outputs/height"/><value v="true"/></option><option><name v="export/fromGraph/outputs/normal"/><value v="true"/></option><option><name v="export/fromGraph/outputs/roughness"/><value v="true"/></option><option><name v="export/fromGraph/outputsColorspace/ambientocclusion"/><value v="Raw"/></option><option><name v="export/fromGraph/outputsColorspace/basecolor"/><value v="sRGB"/></option><option><name v="export/fromGraph/outputsColorspace/height"/><value v="Raw"/></option><option><name v="export/fromGraph/outputsColorspace/normal"/><value v="Raw"/></option><option><name v="export/fromGraph/outputsColorspace/roughness"/><value v="Raw"/></option><option><name v="export/fromGraph/pattern"/><value v="$(graph)_$(identifier)"/></option></options><root><rootOutputs><rootOutput><output v="1213284336"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1213284338"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1213284340"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1279137031"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1359211721"/><format v="0"/><usertag v=""/></rootOutput></rootOutputs></root></graph></content></package>
