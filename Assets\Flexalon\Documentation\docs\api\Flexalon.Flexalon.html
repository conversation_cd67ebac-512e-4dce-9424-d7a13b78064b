﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class Flexalon
 | Flexalon 3D Layouts </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class Flexalon
 | Flexalon 3D Layouts ">
    <meta name="generator" content="docfx ">
  
    <link rel="shortcut icon" href="images/favicon.svg">
    <link rel="stylesheet" href="styles/docfx.vendor.css">
    <link rel="stylesheet" href="styles/docfx.css">
    <link rel="stylesheet" href="styles/main.css">
    <meta property="docfx:navrel" content="tochead.html">
    <meta property="docfx:tocrel" content="toc.html">
  
  <meta property="docfx:rel" content="">
  
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="https://www.flexalon.com/">
                <img id="logo" class="svg" src="images/logo_32x32.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first="First" data-prev="Previous" data-next="Next" data-last="Last"></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">

        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="Flexalon.Flexalon">


  <h1 id="Flexalon_Flexalon" data-uid="Flexalon.Flexalon" class="text-break">Class Flexalon
</h1>
  <div class="markdown level0 summary"><p>Singleton class which tracks and updates all FlexalonNodes in the scene.
See <a href="..\../docs/coreConcepts.html">core concepts</a> for more information.</p>
</div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">object</span></div>
    <div class="level1"><span class="xref">UnityEngine.Object</span></div>
    <div class="level2"><span class="xref">UnityEngine.Component</span></div>
    <div class="level3"><span class="xref">UnityEngine.Behaviour</span></div>
    <div class="level4"><span class="xref">UnityEngine.MonoBehaviour</span></div>
    <div class="level5"><span class="xref">Flexalon</span></div>
  </div>
  <h5 id="Flexalon_Flexalon_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">[ExecuteAlways]
[HelpURL(&quot;https://www.flexalon.com/docs/coreConcepts&quot;)]
public class Flexalon : MonoBehaviour</code></pre>
  </div>
  <h3 id="fields">Fields
</h3>
  <h4 id="Flexalon_Flexalon_PreUpdate" data-uid="Flexalon.Flexalon.PreUpdate">PreUpdate</h4>
  <div class="markdown level1 summary"><p>Event invoked before Flexalon updates.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Action PreUpdate</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Action</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="properties">Properties
</h3>
  <a id="Flexalon_Flexalon_InputProvider_" data-uid="Flexalon.Flexalon.InputProvider*"></a>
  <h4 id="Flexalon_Flexalon_InputProvider" data-uid="Flexalon.Flexalon.InputProvider">InputProvider</h4>
  <div class="markdown level1 summary"><p>Override the default InputProvider used by FlexalonInteractables to support other input devices.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public InputProvider InputProvider { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.InputProvider.html">InputProvider</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_Flexalon_Nodes_" data-uid="Flexalon.Flexalon.Nodes*"></a>
  <h4 id="Flexalon_Flexalon_Nodes" data-uid="Flexalon.Flexalon.Nodes">Nodes</h4>
  <div class="markdown level1 summary"><p>Set of nodes representing GameObjects tracked by Flexalon.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public IReadOnlyCollection&lt;FlexalonNode&gt; Nodes { get; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Collections.Generic.IReadOnlyCollection&lt;T&gt;</span>&lt;<a class="xref" href="Flexalon.FlexalonNode.html">FlexalonNode</a>&gt;</td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_Flexalon_RecordFrameChanges_" data-uid="Flexalon.Flexalon.RecordFrameChanges*"></a>
  <h4 id="Flexalon_Flexalon_RecordFrameChanges" data-uid="Flexalon.Flexalon.RecordFrameChanges">RecordFrameChanges</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static bool RecordFrameChanges { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">bool</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_Flexalon_SkipInactiveObjects_" data-uid="Flexalon.Flexalon.SkipInactiveObjects*"></a>
  <h4 id="Flexalon_Flexalon_SkipInactiveObjects" data-uid="Flexalon.Flexalon.SkipInactiveObjects">SkipInactiveObjects</h4>
  <div class="markdown level1 summary"><p>Determines if Flexalon should automatically skip inactive gameObjects in a layout.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool SkipInactiveObjects { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">bool</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_Flexalon_UpdateInEditMode_" data-uid="Flexalon.Flexalon.UpdateInEditMode*"></a>
  <h4 id="Flexalon_Flexalon_UpdateInEditMode" data-uid="Flexalon.Flexalon.UpdateInEditMode">UpdateInEditMode</h4>
  <div class="markdown level1 summary"><p>Determines if Flexalon should automatically update in edit mode.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool UpdateInEditMode { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">bool</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_Flexalon_UpdateInPlayMode_" data-uid="Flexalon.Flexalon.UpdateInPlayMode*"></a>
  <h4 id="Flexalon_Flexalon_UpdateInPlayMode" data-uid="Flexalon.Flexalon.UpdateInPlayMode">UpdateInPlayMode</h4>
  <div class="markdown level1 summary"><p>Determines if Flexalon should automatically update in play mode.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool UpdateInPlayMode { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">bool</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
</h3>
  <a id="Flexalon_Flexalon_AddComponent_" data-uid="Flexalon.Flexalon.AddComponent*"></a>
  <h4 id="Flexalon_Flexalon_AddComponent_UnityEngine_GameObject_System_Type_" data-uid="Flexalon.Flexalon.AddComponent(UnityEngine.GameObject,System.Type)">AddComponent(GameObject, Type)</h4>
  <div class="markdown level1 summary"><p>Helper to ensure undo operation on AddComponent is handled correctly.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Component AddComponent(GameObject go, Type type)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">UnityEngine.GameObject</span></td>
        <td><span class="parametername">go</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">System.Type</span></td>
        <td><span class="parametername">type</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">UnityEngine.Component</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_Flexalon_AddComponent_" data-uid="Flexalon.Flexalon.AddComponent*"></a>
  <h4 id="Flexalon_Flexalon_AddComponent__1_UnityEngine_GameObject_" data-uid="Flexalon.Flexalon.AddComponent``1(UnityEngine.GameObject)">AddComponent&lt;T&gt;(GameObject)</h4>
  <div class="markdown level1 summary"><p>Helper to ensure undo operation on AddComponent is handled correctly.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static T AddComponent&lt;T&gt;(GameObject go) where T : Component</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">UnityEngine.GameObject</span></td>
        <td><span class="parametername">go</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="typeParameters">Type Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="parametername">T</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_Flexalon_ForceUpdate_" data-uid="Flexalon.Flexalon.ForceUpdate*"></a>
  <h4 id="Flexalon_Flexalon_ForceUpdate" data-uid="Flexalon.Flexalon.ForceUpdate">ForceUpdate()</h4>
  <div class="markdown level1 summary"><p>Marks every node and FlexalonComponent as dirty and calls UpdateDirtyNodes.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void ForceUpdate()</code></pre>
  </div>
  <a id="Flexalon_Flexalon_Get_" data-uid="Flexalon.Flexalon.Get*"></a>
  <h4 id="Flexalon_Flexalon_Get" data-uid="Flexalon.Flexalon.Get">Get()</h4>
  <div class="markdown level1 summary"><p>Returns the singleton Flexalon component.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Flexalon Get()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.Flexalon.html">Flexalon</a></td>
        <td><p>The singleton Flexalon component, or null if it doesn't exist.</p>
</td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_Flexalon_GetInputProvider_" data-uid="Flexalon.Flexalon.GetInputProvider*"></a>
  <h4 id="Flexalon_Flexalon_GetInputProvider" data-uid="Flexalon.Flexalon.GetInputProvider">GetInputProvider()</h4>
  <div class="markdown level1 summary"><p>Gets the current InputProvider used by FlexalonInteractables.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static InputProvider GetInputProvider()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.InputProvider.html">InputProvider</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_Flexalon_GetNode_" data-uid="Flexalon.Flexalon.GetNode*"></a>
  <h4 id="Flexalon_Flexalon_GetNode_UnityEngine_GameObject_" data-uid="Flexalon.Flexalon.GetNode(UnityEngine.GameObject)">GetNode(GameObject)</h4>
  <div class="markdown level1 summary"><p>Returns the FlexalonNode associated with the gameObject.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static FlexalonNode GetNode(GameObject go)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">UnityEngine.GameObject</span></td>
        <td><span class="parametername">go</span></td>
        <td><p>The gameObject to get the FlexalonNode for.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.FlexalonNode.html">FlexalonNode</a></td>
        <td><p>The FlexalonNode associated with the gameObject, or null if it doesn't exist.</p>
</td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_Flexalon_GetOrCreate_" data-uid="Flexalon.Flexalon.GetOrCreate*"></a>
  <h4 id="Flexalon_Flexalon_GetOrCreate" data-uid="Flexalon.Flexalon.GetOrCreate">GetOrCreate()</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Flexalon GetOrCreate()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.Flexalon.html">Flexalon</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_Flexalon_GetOrCreateNode_" data-uid="Flexalon.Flexalon.GetOrCreateNode*"></a>
  <h4 id="Flexalon_Flexalon_GetOrCreateNode_UnityEngine_GameObject_" data-uid="Flexalon.Flexalon.GetOrCreateNode(UnityEngine.GameObject)">GetOrCreateNode(GameObject)</h4>
  <div class="markdown level1 summary"><p>Returns the FlexalonNode associated with the gameObject,
or creates it if it doesn't exist.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static FlexalonNode GetOrCreateNode(GameObject go)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">UnityEngine.GameObject</span></td>
        <td><span class="parametername">go</span></td>
        <td><p>The gameObject to get the FlexalonNode for.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.FlexalonNode.html">FlexalonNode</a></td>
        <td><p>The FlexalonNode associated with the gameObject.</p>
</td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_Flexalon_UpdateDirtyNodes_" data-uid="Flexalon.Flexalon.UpdateDirtyNodes*"></a>
  <h4 id="Flexalon_Flexalon_UpdateDirtyNodes" data-uid="Flexalon.Flexalon.UpdateDirtyNodes">UpdateDirtyNodes()</h4>
  <div class="markdown level1 summary"><p>Updates all dirty nodes.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void UpdateDirtyNodes()</code></pre>
  </div>
</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In This Article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      Flexalon 3D Layouts
      
          </div>
        </div>
      </footer>
    </div>

    
    
    
  </body>
</html>
