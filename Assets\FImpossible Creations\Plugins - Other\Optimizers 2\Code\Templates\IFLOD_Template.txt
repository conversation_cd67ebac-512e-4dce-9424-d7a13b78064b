﻿using UnityEngine;

namespace FIMSpace.FOptimizing
{


    #region Scriptable Container for ILODInstance Instance

    // Create your LOD reference file throught 'Right Mouse button' -> Create -> Custom Optimizers -> ScrLOD_#SCRIPTNAME# Reference
    // Then move it to 'Resources/Optimizers/Custom' directory then it will be detected by Scriptable Optimizer
    // ALSO you have to rename this .cs file to 'ScrLOD_#SCRIPTNAME#'
    [CreateAssetMenu(menuName = "Custom Optimizers/ScrLOD_#SCRIPTNAME# Reference - Move it to Resources - Optimizers - Custom")]
    public sealed class ScrLOD_#SCRIPTNAME# : ScrLOD_Base
    {
        [SerializeField]
        private ILODInstance_#SCRIPTNAME# settings;
        public override ILODInstance GetLODInstance() { return settings; }
        public ScrLOD_#SCRIPTNAME#() { settings = new ILODInstance_#SCRIPTNAME#(); }

        public override ScrLOD_Base GetScrLODInstance()
        { return CreateInstance<ScrLOD_#SCRIPTNAME#>(); }


        public override ScrLOD_Base CreateNewScrCopy()
        {
            ScrLOD_#SCRIPTNAME# newA = CreateInstance<ScrLOD_#SCRIPTNAME#>();
            newA.settings = settings.GetCopy() as ILODInstance_#SCRIPTNAME#;
            return newA;
        }

        public override ScriptableLODsController GenerateLODController(Component target, ScriptableOptimizer optimizer)
        {
            #SCRIPTNAME# a = target as #SCRIPTNAME#;
            if (!a) a = target.GetComponentInChildren<#SCRIPTNAME#>();
            if (a) if (!optimizer.ContainsComponent(a))
                {
                    return new ScriptableLODsController(optimizer, a, -1, "#SCRIPTNAME#", this);
                }

            return null;
        }
    }

    #endregion



    // ILODInstance LOD INSTANCE CODE BELOW -----------------------------------------------


    [System.Serializable]
    public sealed class ILODInstance_#SCRIPTNAME# : ILODInstance
    {
        public string HeaderText { get { return "#SCRIPTNAME# LOD Settings"; } }
        public Texture Icon { get { return null; } }
        public bool LockSettings { get { return _Locked; } set { _Locked = value; } }
        [HideInInspector] [SerializeField] private bool _Locked = false;
        public bool SupportingTransitions { get { return true; } }// Will you implement supporting transitions?

        #region Main Settings : Interface Properties

        public int Index { get { return index; } set { index = value; } }
        internal int index = -1;
        public string Name { get { return LODName; } set { LODName = value; } }
        internal string LODName = "";
        public bool CustomEditor { get { return false; } }
        public bool Disable { get { return SetDisabled; } set { SetDisabled = value; } }
        [HideInInspector] public bool SetDisabled = false;
        public bool DrawDisableOption { get { return true; } }
        public bool DrawLowererSlider { get { return false; } }
        public float QualityLowerer { get { return 1f; } set { new System.NotImplementedException(); } }
        public bool SupportVersions { get { return false; } }
        public int DrawingVersion { get { return 1; } set { new System.NotImplementedException(); } }
        public float ToCullDelay { get { return 0f; } }
        public Component TargetComponent { get { return cmp; } }
        [SerializeField] [HideInInspector] private #SCRIPTNAME# cmp;

        #endregion



        [Range(0f, 1f)]
        public float ExampleVariable = 1f;


        public void SetSameValuesAsComponent(Component component)
        {
            if (component == null) return;

            #SCRIPTNAME# typeComponent = component as #SCRIPTNAME#;
            cmp = typeComponent;
            ExampleVariable = typeComponent.Variable;
            // ~YOUR CODE~ \\
        }


        public void InterpolateBetween(ILODInstance a, ILODInstance b, float transitionToB)
        {
            FLOD.DoBaseInterpolation(this, a, b, transitionToB);

            ILODInstance_#SCRIPTNAME# aa = a as ILODInstance_#SCRIPTNAME#;
            ILODInstance_#SCRIPTNAME# bb = b as ILODInstance_#SCRIPTNAME#;

            ExampleVariable = Mathf.Lerp(aa.ExampleVariable, bb.ExampleVariable, transitionToB);
            // ~YOUR CODE~ \\
        }


        public void ApplySettingsToTheComponent(Component component, ILODInstance initialSettings)
        {
            #SCRIPTNAME# comp = component as #SCRIPTNAME#;

            // Reference to initial settings collected when object was starting in playmode
            ILODInstance_#SCRIPTNAME# initials = initialSettings as ILODInstance_#SCRIPTNAME#;


            // Percentage change basing on initial value
            comp.Variable = initials.ExampleVariable * ExampleVariable;
            // ~YOUR CODE~ \\


            // Apply disable / enable component
            FLOD.ApplyEnableDisableState(this, component);
        }


        public void AssignAutoSettingsAsForLODLevel(int lodIndex, int lodCount, Component source)
        {
            #SCRIPTNAME# comp = source as #SCRIPTNAME#;
            if (comp == null) Debug.LogError("[OPTIMIZERS] Given component for reference values is null or is not AudioSource Component!");

            float mul = FLOD.GetValueForLODLevel(1f, 0f, lodIndex - 1, lodCount);

            // ~YOUR OPTIONAL AUTO CODE~ \\
            if (lodIndex > 0) ExampleVariable = 1f - mul;

        }


        public void AssignSettingsAsForCulled(Component component)
        {
            FLOD.AssignDefaultCulledParams(this);
            // ~YOUR OPTIONAL AUTO CODE~ \\
        }


        public void AssignSettingsAsForNearest(Component component)
        {
            FLOD.AssignDefaultNearestParams(this);
            // ~YOUR OPTIONAL AUTO CODE~ \\
        }

        public ILODInstance GetCopy() { return MemberwiseClone() as ILODInstance; }

        public void AssignSettingsAsForHidden(Component component)
        {
            FLOD.AssignDefaultHiddenParams(this);
            // ~YOUR OPTIONAL AUTO CODE~ \\
        }


        // Custom Inspector Features ---------------------------------------------


#if UNITY_EDITOR
        public void AssignToggler(ILODInstance reference)
        { }

        public void DrawTogglers(UnityEditor.SerializedProperty ILODInstanceProp)
        { }

        public void CustomEditorWindow(UnityEditor.SerializedProperty prop)
        { }

        public void DrawVersionSwitch(UnityEditor.SerializedProperty iflodProp, LODsControllerBase lODsControllerBase) 
        { }

        public void CustomEditorWindow(UnityEditor.SerializedProperty iflodProp, LODsControllerBase lODsControllerBase)
        { }
#endif

    }


}

