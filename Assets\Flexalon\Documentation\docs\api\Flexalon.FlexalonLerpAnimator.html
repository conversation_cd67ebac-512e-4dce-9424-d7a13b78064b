﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class FlexalonLerpAnimator
 | Flexalon 3D Layouts </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class FlexalonLerpAnimator
 | Flexalon 3D Layouts ">
    <meta name="generator" content="docfx ">
  
    <link rel="shortcut icon" href="images/favicon.svg">
    <link rel="stylesheet" href="styles/docfx.vendor.css">
    <link rel="stylesheet" href="styles/docfx.css">
    <link rel="stylesheet" href="styles/main.css">
    <meta property="docfx:navrel" content="tochead.html">
    <meta property="docfx:tocrel" content="toc.html">
  
  <meta property="docfx:rel" content="">
  
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="https://www.flexalon.com/">
                <img id="logo" class="svg" src="images/logo_32x32.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first="First" data-prev="Previous" data-next="Next" data-last="Last"></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">

        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="Flexalon.FlexalonLerpAnimator">


  <h1 id="Flexalon_FlexalonLerpAnimator" data-uid="Flexalon.FlexalonLerpAnimator" class="text-break">Class FlexalonLerpAnimator
</h1>
  <div class="markdown level0 summary"><p>The lerp animator constnatly performs a linear interpolation between
the object's current position and its layout position. This is useful
if the layout position is continuously changing.</p>
</div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">object</span></div>
    <div class="level1"><span class="xref">UnityEngine.Object</span></div>
    <div class="level2"><span class="xref">UnityEngine.Component</span></div>
    <div class="level3"><span class="xref">UnityEngine.Behaviour</span></div>
    <div class="level4"><span class="xref">UnityEngine.MonoBehaviour</span></div>
    <div class="level5"><span class="xref">FlexalonLerpAnimator</span></div>
  </div>
  <div class="implements">
    <h5>Implements</h5>
    <div><a class="xref" href="Flexalon.TransformUpdater.html">TransformUpdater</a></div>
  </div>
  <h5 id="Flexalon_FlexalonLerpAnimator_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">[AddComponentMenu(&quot;Flexalon/Flexalon Lerp Animator&quot;)]
[HelpURL(&quot;https://www.flexalon.com/docs/animators&quot;)]
public class FlexalonLerpAnimator : MonoBehaviour, TransformUpdater</code></pre>
  </div>
  <h3 id="properties">Properties
</h3>
  <a id="Flexalon_FlexalonLerpAnimator_AnimateInWorldSpace_" data-uid="Flexalon.FlexalonLerpAnimator.AnimateInWorldSpace*"></a>
  <h4 id="Flexalon_FlexalonLerpAnimator_AnimateInWorldSpace" data-uid="Flexalon.FlexalonLerpAnimator.AnimateInWorldSpace">AnimateInWorldSpace</h4>
  <div class="markdown level1 summary"><p>Determines if the animation should be performed in world space.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool AnimateInWorldSpace { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">bool</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonLerpAnimator_AnimatePosition_" data-uid="Flexalon.FlexalonLerpAnimator.AnimatePosition*"></a>
  <h4 id="Flexalon_FlexalonLerpAnimator_AnimatePosition" data-uid="Flexalon.FlexalonLerpAnimator.AnimatePosition">AnimatePosition</h4>
  <div class="markdown level1 summary"><p>Determines if the position should be animated.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool AnimatePosition { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">bool</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonLerpAnimator_AnimateRotation_" data-uid="Flexalon.FlexalonLerpAnimator.AnimateRotation*"></a>
  <h4 id="Flexalon_FlexalonLerpAnimator_AnimateRotation" data-uid="Flexalon.FlexalonLerpAnimator.AnimateRotation">AnimateRotation</h4>
  <div class="markdown level1 summary"><p>Determines if the rotation should be animated.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool AnimateRotation { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">bool</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonLerpAnimator_AnimateScale_" data-uid="Flexalon.FlexalonLerpAnimator.AnimateScale*"></a>
  <h4 id="Flexalon_FlexalonLerpAnimator_AnimateScale" data-uid="Flexalon.FlexalonLerpAnimator.AnimateScale">AnimateScale</h4>
  <div class="markdown level1 summary"><p>Determines if the rotation should be animated.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool AnimateScale { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">bool</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonLerpAnimator_InterpolationSpeed_" data-uid="Flexalon.FlexalonLerpAnimator.InterpolationSpeed*"></a>
  <h4 id="Flexalon_FlexalonLerpAnimator_InterpolationSpeed" data-uid="Flexalon.FlexalonLerpAnimator.InterpolationSpeed">InterpolationSpeed</h4>
  <div class="markdown level1 summary"><p>Amount the object should be interpolated towards the target at each frame.
This value is multiplied by Time.deltaTime.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float InterpolationSpeed { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">float</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
</h3>
  <a id="Flexalon_FlexalonLerpAnimator_PreUpdate_" data-uid="Flexalon.FlexalonLerpAnimator.PreUpdate*"></a>
  <h4 id="Flexalon_FlexalonLerpAnimator_PreUpdate_Flexalon_FlexalonNode_" data-uid="Flexalon.FlexalonLerpAnimator.PreUpdate(Flexalon.FlexalonNode)">PreUpdate(FlexalonNode)</h4>
  <div class="markdown level1 summary"><p>Called before the layout system starts updating any transforms.
Use this to capture the transform position.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void PreUpdate(FlexalonNode node)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.FlexalonNode.html">FlexalonNode</a></td>
        <td><span class="parametername">node</span></td>
        <td><p>The node being updated.</p>
</td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonLerpAnimator_UpdatePosition_" data-uid="Flexalon.FlexalonLerpAnimator.UpdatePosition*"></a>
  <h4 id="Flexalon_FlexalonLerpAnimator_UpdatePosition_Flexalon_FlexalonNode_UnityEngine_Vector3_" data-uid="Flexalon.FlexalonLerpAnimator.UpdatePosition(Flexalon.FlexalonNode,UnityEngine.Vector3)">UpdatePosition(FlexalonNode, Vector3)</h4>
  <div class="markdown level1 summary"><p>Called to update the position of the object.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool UpdatePosition(FlexalonNode node, Vector3 position)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.FlexalonNode.html">FlexalonNode</a></td>
        <td><span class="parametername">node</span></td>
        <td><p>The node being updated.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><span class="parametername">position</span></td>
        <td><p>The computed local position of the object.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">bool</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonLerpAnimator_UpdateRectSize_" data-uid="Flexalon.FlexalonLerpAnimator.UpdateRectSize*"></a>
  <h4 id="Flexalon_FlexalonLerpAnimator_UpdateRectSize_Flexalon_FlexalonNode_UnityEngine_Vector2_" data-uid="Flexalon.FlexalonLerpAnimator.UpdateRectSize(Flexalon.FlexalonNode,UnityEngine.Vector2)">UpdateRectSize(FlexalonNode, Vector2)</h4>
  <div class="markdown level1 summary"><p>Called to update the rect of the object.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool UpdateRectSize(FlexalonNode node, Vector2 size)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.FlexalonNode.html">FlexalonNode</a></td>
        <td><span class="parametername">node</span></td>
        <td><p>The node being updated.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">UnityEngine.Vector2</span></td>
        <td><span class="parametername">size</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">bool</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonLerpAnimator_UpdateRotation_" data-uid="Flexalon.FlexalonLerpAnimator.UpdateRotation*"></a>
  <h4 id="Flexalon_FlexalonLerpAnimator_UpdateRotation_Flexalon_FlexalonNode_UnityEngine_Quaternion_" data-uid="Flexalon.FlexalonLerpAnimator.UpdateRotation(Flexalon.FlexalonNode,UnityEngine.Quaternion)">UpdateRotation(FlexalonNode, Quaternion)</h4>
  <div class="markdown level1 summary"><p>Called to update the rotation of the object.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool UpdateRotation(FlexalonNode node, Quaternion rotation)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.FlexalonNode.html">FlexalonNode</a></td>
        <td><span class="parametername">node</span></td>
        <td><p>The node being updated.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">UnityEngine.Quaternion</span></td>
        <td><span class="parametername">rotation</span></td>
        <td><p>The computed local rotation of the object.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">bool</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonLerpAnimator_UpdateScale_" data-uid="Flexalon.FlexalonLerpAnimator.UpdateScale*"></a>
  <h4 id="Flexalon_FlexalonLerpAnimator_UpdateScale_Flexalon_FlexalonNode_UnityEngine_Vector3_" data-uid="Flexalon.FlexalonLerpAnimator.UpdateScale(Flexalon.FlexalonNode,UnityEngine.Vector3)">UpdateScale(FlexalonNode, Vector3)</h4>
  <div class="markdown level1 summary"><p>Called to update the scale of the object.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool UpdateScale(FlexalonNode node, Vector3 scale)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.FlexalonNode.html">FlexalonNode</a></td>
        <td><span class="parametername">node</span></td>
        <td><p>The node being updated.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><span class="parametername">scale</span></td>
        <td><p>The computed local scale of the object.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">bool</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="implements">Implements</h3>
  <div>
      <a class="xref" href="Flexalon.TransformUpdater.html">TransformUpdater</a>
  </div>
</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In This Article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      Flexalon 3D Layouts
      
          </div>
        </div>
      </footer>
    </div>

    
    
    
  </body>
</html>
