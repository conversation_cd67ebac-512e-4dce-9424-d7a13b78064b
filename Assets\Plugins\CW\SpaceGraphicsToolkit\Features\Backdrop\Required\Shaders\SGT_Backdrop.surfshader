﻿BEGIN_OPTIONS
	Workflow "Unlit"
	Alpha "Add"
END_OPTIONS

BEGIN_PROPERTIES
	[Header(POWER RGB)]
	[Toggle(_SGT_POWER_RGB)] _SGT_PowerRgb ("	Enable", Float) = 0
	_SGT_ColorInfluence("	Color Influence", Float) = 10

	[Header(PULSE)]
	[Toggle(_SGT_PULSE)] _SGT_Pulse ("	Enable", Float) = 0
	_SGT_PulseMin("	Pulse Min", Range(0,1)) = 0.5
	_SGT_PulseMax("	Pulse Max", Range(0,1)) = 1.0
	_SGT_PulseSpeed("	Pulse Speed", Float) = 100.0
END_PROPERTIES

BEGIN_DEFINES
	#define __SGT_BACKDROP 1
	#pragma shader_feature_local _SGT_POWER_RGB
	#pragma shader_feature_local _SGT_PULSE
	#pragma multi_compile_local _ _SGT_CLAMP_SIZE_MIN
END_DEFINES

BEGIN_CBUFFER
	sampler2D _SGT_MainTex; // Auto
	float4    _SGT_Color; // Auto
	float     _SGT_Brightness; // Auto

	// Power RGB
	float _SGT_ColorInfluence;

	// Pulse
	float _SGT_PulseMin;
	float _SGT_PulseMax;
	float _SGT_PulseSpeed;

	// Clamp Size Min
	float _SGT_ClampSizeMin; // Auto
	float _SGT_ClampSizeScale; // Auto
	float _SGT_Radius; // Auto
END_CBUFFER

BEGIN_BLACKBOARD
	float4 finalColor;
END_BLACKBOARD

BEGIN_CODE
	void ModifyVertex(inout VertexData v, inout ExtraV2F e)
	{
		float3 wcam      = _WorldSpaceCameraPos;
		float3 center    = v.texcoord1.xyz;
		float3 direction = v.vertex.xyz - center.xyz;
		float  size      = length(direction);

		// Normalize
		direction /= size;

		#if _SGT_PULSE
			float pulseSpeed  = v.texcoord0.z % 1.0f;
			float pulseOffset = floor(v.texcoord0.z) * 3.141592654f;
			float pulseAngle  = pulseOffset + _Time.x * pulseSpeed * _SGT_PulseSpeed;
			size *= lerp(_SGT_PulseMin, _SGT_PulseMax, sin(pulseAngle) * 0.5f + 0.5f);
		#endif

		#if _SGT_CLAMP_SIZE_MIN
			float sizeMin = (_SGT_ClampSizeMin * _SGT_Radius) / _ScreenParams.y * _SGT_ClampSizeScale;
			float scale   = saturate(size / sizeMin);
			size /= scale; // Scale up to min size
			v.vertexColor.a *= scale; // Darken by shrunk amount
		#endif

		#if _SGT_PULSE || _SGT_CLAMP_SIZE_MIN
			v.vertex.xyz = center.xyz + direction * size;
		#endif

		v.vertexColor *= _SGT_Color;
	}

	void SurfaceFunction(inout Surface o, inout ShaderData d)
	{
		float4 finalColor = tex2D(_SGT_MainTex, d.texcoord0.xy);

		#if _SGT_POWER_RGB
			finalColor.rgb = pow(abs(finalColor.rgb), float3(1.0f, 1.0f, 1.0f) + (1.0f - d.vertexColor.rgb) * _SGT_ColorInfluence);
		#else
			finalColor *= d.vertexColor;
		#endif

		finalColor.xyz *= _SGT_Brightness;

		finalColor *= saturate(d.vertexColor.a);
		
		d.blackboard.finalColor = finalColor;

		#if __SGT_OUTPUT
			SGT_OutputWithoutAlpha(o, SGT_ModifyUnlitOutput(finalColor));
		#endif
	}
END_CODE