#if URP_INSTALLED
using UnityEngine;
using UnityEditor;
using UnityEngine.Rendering;
using System.Collections.Generic;
using System.Linq;
using Stylo.Flux.Universal;

namespace Stylo.Flux.Editor
{
    /// <summary>
    /// Detects changes to Flux Volume settings during Play Mode and prompts to save them when exiting Play Mode.
    /// This prevents losing carefully tuned Flux settings when exiting Play Mode.
    /// </summary>
    [InitializeOnLoad]
    public class FluxPlayModeChangeDetector
    {
        private static Dictionary<Volume, FluxEffectSnapshot> originalFluxSettings = new Dictionary<Volume, FluxEffectSnapshot>();
        private static bool hasDetectedChanges = false;

        static FluxPlayModeChangeDetector()
        {
            EditorApplication.playModeStateChanged += OnPlayModeStateChanged;
            Debug.Log("[Flux] Play Mode Change Detector initialized");
        }

        private static void OnPlayModeStateChanged(PlayModeStateChange state)
        {
            Debug.Log($"[Flux] Play Mode State Changed: {state}");

            switch (state)
            {
                case PlayModeStateChange.EnteredPlayMode:
                    CaptureOriginalFluxSettings();
                    break;

                case PlayModeStateChange.ExitingPlayMode:
                    DetectAndPromptForChanges();
                    break;

                case PlayModeStateChange.EnteredEditMode:
                    ClearCapturedSettings();
                    break;
            }
        }

        private static void CaptureOriginalFluxSettings()
        {
            originalFluxSettings.Clear();
            hasDetectedChanges = false;

            // Find all Volume components in the scene
            var volumes = Object.FindObjectsByType<Volume>(FindObjectsSortMode.None);
            Debug.Log($"[Flux] Found {volumes.Length} Volume components in scene");

            foreach (var volume in volumes)
            {
                Debug.Log($"[Flux] Checking Volume: {volume.name}, Profile: {(volume.profile != null ? volume.profile.name : "null")}");

                if (volume.profile != null)
                {
                    // Check if this volume has a FluxEffect component
                    if (volume.profile.TryGet<FluxEffect>(out var fluxEffect))
                    {
                        // Capture the current state
                        originalFluxSettings[volume] = new FluxEffectSnapshot(fluxEffect);

                        Debug.Log($"[Flux] ✅ Captured original settings for Volume: {volume.name} (Profile: {volume.profile.name})");
                    }
                    else
                    {
                        Debug.Log($"[Flux] ❌ Volume {volume.name} has no FluxEffect component");
                    }
                }
            }

            Debug.Log($"[Flux] Total Flux volumes captured: {originalFluxSettings.Count}");
        }

        private static void DetectAndPromptForChanges()
        {
            if (originalFluxSettings.Count == 0)
                return;

            var changedVolumes = new List<(Volume volume, FluxEffectSnapshot original, FluxEffectSnapshot current)>();

            // Check each captured volume for changes
            foreach (var kvp in originalFluxSettings)
            {
                var volume = kvp.Key;
                var originalSnapshot = kvp.Value;

                if (volume != null && volume.profile != null)
                {
                    if (volume.profile.TryGet<FluxEffect>(out var currentFluxEffect))
                    {
                        var currentSnapshot = new FluxEffectSnapshot(currentFluxEffect);

                        if (!originalSnapshot.Equals(currentSnapshot))
                        {
                            changedVolumes.Add((volume, originalSnapshot, currentSnapshot));
                        }
                    }
                }
            }

            // If changes detected, prompt user
            if (changedVolumes.Count > 0)
            {
                PromptToSaveChanges(changedVolumes);
            }
        }

        private static void PromptToSaveChanges(List<(Volume volume, FluxEffectSnapshot original, FluxEffectSnapshot current)> changedVolumes)
        {
            string message = $"Flux settings were modified during Play Mode in {changedVolumes.Count} Volume(s):\n\n";

            foreach (var change in changedVolumes)
            {
                message += $"• {change.volume.name} (Profile: {change.volume.profile.name})\n";
            }

            message += "\nDo you want to save these changes to the Volume Profile(s)?";

            int choice = EditorUtility.DisplayDialogComplex(
                "Save Flux Changes?",
                message,
                "Save Changes",
                "Discard Changes",
                "Show Details"
            );

            switch (choice)
            {
                case 0: // Save Changes
                    SaveChangesToProfiles(changedVolumes);
                    break;

                case 1: // Discard Changes
                    Debug.Log("[Flux] Play Mode changes discarded.");
                    break;

                case 2: // Show Details
                    ShowDetailedChanges(changedVolumes);
                    break;
            }
        }

        private static void SaveChangesToProfiles(List<(Volume volume, FluxEffectSnapshot original, FluxEffectSnapshot current)> changedVolumes)
        {
            foreach (var change in changedVolumes)
            {
                if (change.volume.profile.TryGet<FluxEffect>(out var fluxEffect))
                {
                    // Apply the current (modified) settings to the profile
                    change.current.ApplyToFluxEffect(fluxEffect);

                    // Mark the profile as dirty so Unity saves it
                    EditorUtility.SetDirty(change.volume.profile);

                    Debug.Log($"[Flux] Saved changes to Volume Profile: {change.volume.profile.name}");
                }
            }

            // Save assets to disk
            AssetDatabase.SaveAssets();

            EditorUtility.DisplayDialog("Changes Saved",
                $"Flux settings have been saved to {changedVolumes.Count} Volume Profile(s).",
                "OK");
        }

        private static void ShowDetailedChanges(List<(Volume volume, FluxEffectSnapshot original, FluxEffectSnapshot current)> changedVolumes)
        {
            var detailWindow = EditorWindow.GetWindow<FluxChangeDetailsWindow>("Flux Changes");
            detailWindow.SetChanges(changedVolumes);
            detailWindow.Show();
        }

        private static void ClearCapturedSettings()
        {
            originalFluxSettings.Clear();
            hasDetectedChanges = false;
        }

        /// <summary>
        /// Menu item to manually check for Flux changes during Play Mode
        /// </summary>
        [MenuItem("Tools/Flux/Check for Unsaved Changes", false, 100)]
        private static void ManualCheckForChanges()
        {
            if (!Application.isPlaying)
            {
                EditorUtility.DisplayDialog("Not in Play Mode",
                    "This feature only works during Play Mode to detect changes made to Flux settings.",
                    "OK");
                return;
            }

            if (originalFluxSettings.Count == 0)
            {
                EditorUtility.DisplayDialog("No Flux Volumes",
                    "No Flux Volume settings were captured when entering Play Mode.",
                    "OK");
                return;
            }

            DetectAndPromptForChanges();
        }

        /// <summary>
        /// Menu item to save all current Flux settings to profiles
        /// </summary>
        [MenuItem("Tools/Flux/Save All Current Settings", false, 101)]
        private static void SaveAllCurrentSettings()
        {
            if (!Application.isPlaying)
            {
                EditorUtility.DisplayDialog("Not in Play Mode",
                    "This feature only works during Play Mode to save current Flux settings.",
                    "OK");
                return;
            }

            var volumes = Object.FindObjectsByType<Volume>(FindObjectsSortMode.None);
            var fluxVolumes = new List<Volume>();

            foreach (var volume in volumes)
            {
                if (volume.profile != null && volume.profile.TryGet<FluxEffect>(out _))
                {
                    fluxVolumes.Add(volume);
                }
            }

            if (fluxVolumes.Count == 0)
            {
                EditorUtility.DisplayDialog("No Flux Volumes",
                    "No Volumes with Flux Effects found in the current scene.",
                    "OK");
                return;
            }

            foreach (var volume in fluxVolumes)
            {
                if (volume.profile.TryGet<FluxEffect>(out var fluxEffect))
                {
                    var snapshot = new FluxEffectSnapshot(fluxEffect);
                    snapshot.ApplyToFluxEffect(fluxEffect);
                    EditorUtility.SetDirty(volume.profile);
                }
            }

            AssetDatabase.SaveAssets();

            EditorUtility.DisplayDialog("Settings Saved",
                $"Current Flux settings have been saved to {fluxVolumes.Count} Volume Profile(s).",
                "OK");
        }



        /// <summary>
        /// Menu item to test the system initialization
        /// </summary>
        [MenuItem("Tools/Flux/Test System Status", false, 102)]
        private static void TestSystemStatus()
        {
            Debug.Log($"[Flux] System Status:");
            Debug.Log($"[Flux] - Play Mode: {Application.isPlaying}");
            Debug.Log($"[Flux] - Captured Volumes: {originalFluxSettings.Count}");
            Debug.Log($"[Flux] - Has Changes: {hasDetectedChanges}");

            var volumes = Object.FindObjectsByType<Volume>(FindObjectsSortMode.None);
            var fluxVolumes = 0;

            foreach (var volume in volumes)
            {
                if (volume.profile != null && volume.profile.TryGet<FluxEffect>(out _))
                {
                    fluxVolumes++;
                }
            }

            Debug.Log($"[Flux] - Current Flux Volumes in Scene: {fluxVolumes}");

            string detailedStatus = $"=== FLUX PARAMETER SAVING SYSTEM ===\n\n" +
                $"Play Mode: {Application.isPlaying}\n" +
                $"Captured Volumes: {originalFluxSettings.Count}\n" +
                $"Current Flux Volumes: {fluxVolumes}\n" +
                $"Has Changes: {hasDetectedChanges}\n\n";

            if (originalFluxSettings.Count > 0)
            {
                detailedStatus += "✅ System is tracking parameters!\n";
                detailedStatus += "✅ All new parameters included:\n";
                detailedStatus += "  • Mode Selection (Pure Datamosh, Unity6 Guide)\n";
                detailedStatus += "  • Brightness Control (4 parameters)\n";
                detailedStatus += "  • Compression Artifacts (3 parameters)\n";
                detailedStatus += "  • JPEG Quality Control (4 parameters)\n";
                detailedStatus += "  • Unity6 Guide (6 parameters)\n";
                detailedStatus += "  • Debug parameters\n\n";
                detailedStatus += "Modify parameters in Play Mode to test saving!";
            }
            else
            {
                detailedStatus += "❌ No parameters captured.\n";
                detailedStatus += "Enter Play Mode to start tracking.";
            }

            EditorUtility.DisplayDialog("Flux Parameter Saving System", detailedStatus, "OK");
        }
    }


}
#endif
