%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 10
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.212, g: 0.227, b: 0.259, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 0
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &3
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 13
  m_BakeOnSceneLoad: 0
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 1
    m_EnableRealtimeLightmaps: 0
  m_LightmapEditorSettings:
    serializedVersion: 12
    m_Resolution: 2
    m_BakeResolution: 40
    m_AtlasSize: 1024
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_ExtractAmbientOcclusion: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_ReflectionCompression: 2
    m_MixedBakeMode: 2
    m_BakeBackend: 1
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 512
    m_PVRBounces: 2
    m_PVREnvironmentSampleCount: 256
    m_PVREnvironmentReferencePointCount: 2048
    m_PVRFilteringMode: 1
    m_PVRDenoiserTypeDirect: 1
    m_PVRDenoiserTypeIndirect: 1
    m_PVRDenoiserTypeAO: 1
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVREnvironmentMIS: 1
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ExportTrainingData: 0
    m_TrainingDataDestination: TrainingData
    m_LightProbeSampleCountMultiplier: 4
  m_LightingDataAsset: {fileID: 0}
  m_LightingSettings: {fileID: 0}
--- !u!196 &4
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 3
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    buildHeightMesh: 0
    maxJobWorkers: 0
    preserveTilesOutsideBounds: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!1 &62541747
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 62541751}
  - component: {fileID: 62541750}
  - component: {fileID: 62541749}
  - component: {fileID: 62541748}
  m_Layer: 0
  m_Name: FillFlexibleLayout
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &62541748
MonoBehaviour:
  m_ObjectHideFlags: 2
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 62541747}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994c66b6148109b489689ed705d244cd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Parent: {fileID: 0}
  SiblingIndex: 0
  LayoutPosition: {x: 0, y: 0, z: 0}
  LayoutRotation: {x: 0, y: 0, z: 0, w: 1}
  AdapterBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 5.335784, y: 1, z: 0.5000001}
  LayoutBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 5.335784, y: 1, z: 0.5000001}
  RotatedAndScaledBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 5.335784, y: 1, z: 0.5000001}
  ComponentScale: {x: 1, y: 1, z: 1}
  FillSize: {x: 1, y: 1, z: 1}
  ShrinkSize: {x: 999999, y: 999999, z: 999999}
  TargetPosition: {x: 6, y: 0, z: 0}
  TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  TargetScale: {x: 1, y: 1, z: 1}
  TargetRectSize: {x: 0, y: 0, z: 0}
  TransformPosition: {x: 6, y: 0, z: 0}
  TransformRotation: {x: 0, y: 0, z: 0, w: 1}
  TransformScale: {x: 1, y: 1, z: 1}
  TransformRectSize: {x: 10.671568, y: 2}
--- !u!114 &62541749
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 62541747}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 00b0da67f6b3c9a4e84ed47af9da4884, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _version: 4
  _widthType: 0
  _width: 10.671568
  _widthOfParent: 1
  _heightType: 3
  _height: 1
  _heightOfParent: 1
  _depthType: 3
  _depth: 1
  _depthOfParent: 1
  _minWidthType: 0
  _minWidth: 0
  _minWidthOfParent: 0
  _minHeightType: 0
  _minHeight: 0
  _minHeightOfParent: 0
  _minDepthType: 0
  _minDepth: 0
  _minDepthOfParent: 0
  _maxWidthType: 0
  _maxWidth: 1
  _maxWidthOfParent: 1
  _maxHeightType: 0
  _maxHeight: 1
  _maxHeightOfParent: 1
  _maxDepthType: 0
  _maxDepth: 1
  _maxDepthOfParent: 1
  _offset: {x: 0, y: 0, z: 0}
  _scale: {x: 1, y: 1, z: 1}
  _rotation: {x: 0, y: 0, z: 0, w: 1}
  _marginLeft: 0
  _marginRight: 0
  _marginTop: 0
  _marginBottom: 0
  _marginFront: 0
  _marginBack: 0
  _paddingLeft: 0
  _paddingRight: 0
  _paddingTop: 0
  _paddingBottom: 0
  _paddingFront: 0
  _paddingBack: 0
  _skipLayout: 0
--- !u!114 &62541750
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 62541747}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3bcb5ae438b41e3449fdddabaf22c434, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _version: 4
  _direction: 0
  _wrap: 0
  _wrapDirection: 3
  _horizontalAlign: 1
  _verticalAlign: 1
  _depthAlign: 1
  _horizontalInnerAlign: 1
  _verticalInnerAlign: 1
  _depthInnerAlign: 1
  _gapType: 0
  _gap: 0.2
  _wrapGapType: 0
  _wrapGap: 0.2
--- !u!4 &62541751
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 62541747}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 6, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1903162706}
  - {fileID: 1077988681}
  - {fileID: 1971579504}
  - {fileID: 1343192070}
  - {fileID: 834929725}
  - {fileID: 1860966569}
  - {fileID: 1566359689}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &272011451
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 272011452}
  - component: {fileID: 272011455}
  - component: {fileID: 272011454}
  - component: {fileID: 272011453}
  - component: {fileID: 272011456}
  - component: {fileID: 272011457}
  m_Layer: 0
  m_Name: Sphere (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &272011452
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 272011451}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 2.4000003, y: 1.0999999, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2050991704}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!135 &272011453
SphereCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 272011451}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Radius: 0.5
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &272011454
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 272011451}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 3feedb2344ae9cc40b610436dd57e3d0, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &272011455
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 272011451}
  m_Mesh: {fileID: 10207, guid: 0000000000000000e000000000000000, type: 0}
--- !u!114 &272011456
MonoBehaviour:
  m_ObjectHideFlags: 2
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 272011451}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994c66b6148109b489689ed705d244cd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Parent: {fileID: 2050991704}
  SiblingIndex: 4
  LayoutPosition: {x: 2.4000003, y: 1.0999999, z: 0}
  LayoutRotation: {x: 0, y: 0, z: 0, w: 1}
  AdapterBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.5, y: 0.5, z: 0.5}
  LayoutBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.5, y: 0.5, z: 0.5}
  RotatedAndScaledBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.5, y: 0.5, z: 0.5}
  ComponentScale: {x: 1, y: 1, z: 1}
  FillSize: {x: 1, y: 1, z: 1}
  ShrinkSize: {x: 999999, y: 999999, z: 999999}
  TargetPosition: {x: 2.4000003, y: 1.0999999, z: 0}
  TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  TargetScale: {x: 1, y: 1, z: 1}
  TargetRectSize: {x: 0, y: 0, z: 0}
  TransformPosition: {x: 2.4000003, y: 1.0999999, z: 0}
  TransformRotation: {x: 0, y: 0, z: 0, w: 1}
  TransformScale: {x: 1, y: 1, z: 1}
  TransformRectSize: {x: 1, y: 1}
--- !u!114 &272011457
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 272011451}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6122eecc36c8b14478b69adaee2c4d55, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Standard: {fileID: 2100000, guid: d1f3c8ac14b71fc479fe0370087f7d95, type: 2}
  URP: {fileID: 2100000, guid: 3feedb2344ae9cc40b610436dd57e3d0, type: 2}
  HDRP: {fileID: 2100000, guid: f29cbe433e772d04d91757e83b0e7185, type: 2}
--- !u!1 &331720421
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 331720424}
  - component: {fileID: 331720423}
  - component: {fileID: 331720422}
  m_Layer: 0
  m_Name: Main Camera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!81 &331720422
AudioListener:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 331720421}
  m_Enabled: 1
--- !u!20 &331720423
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 331720421}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 1
  m_BackGroundColor: {r: 0.19215687, g: 0.3019608, b: 0.4745098, a: 0}
  m_projectionMatrixMode: 1
  m_GateFitMode: 2
  m_FOVAxisMode: 0
  m_Iso: 200
  m_ShutterSpeed: 0.005
  m_Aperture: 16
  m_FocusDistance: 10
  m_FocalLength: 50
  m_BladeCount: 5
  m_Curvature: {x: 2, y: 11}
  m_BarrelClipping: 0.25
  m_Anamorphism: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.3
  far clip plane: 1000
  field of view: 60
  orthographic: 0
  orthographic size: 5
  m_Depth: -1
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 0
  m_AllowMSAA: 0
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 0
  m_OcclusionCulling: 1
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!4 &331720424
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 331720421}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 1, z: -12.32}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &705464170
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 705464171}
  - component: {fileID: 705464174}
  - component: {fileID: 705464173}
  - component: {fileID: 705464172}
  - component: {fileID: 705464175}
  - component: {fileID: 705464176}
  m_Layer: 0
  m_Name: Cube (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &705464171
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 705464170}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0.6, y: -1.1000001, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2050991704}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!65 &705464172
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 705464170}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &705464173
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 705464170}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 8154b61d528b98f40bcfa5bcc9cca9e5, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &705464174
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 705464170}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!114 &705464175
MonoBehaviour:
  m_ObjectHideFlags: 2
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 705464170}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994c66b6148109b489689ed705d244cd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Parent: {fileID: 2050991704}
  SiblingIndex: 6
  LayoutPosition: {x: -0.6, y: -1.1000001, z: 0}
  LayoutRotation: {x: 0, y: 0, z: 0, w: 1}
  AdapterBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.5, y: 0.5, z: 0.5}
  LayoutBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.5, y: 0.5, z: 0.5}
  RotatedAndScaledBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.5, y: 0.5, z: 0.5}
  ComponentScale: {x: 1, y: 1, z: 1}
  FillSize: {x: 1, y: 1, z: 1}
  ShrinkSize: {x: 999999, y: 999999, z: 999999}
  TargetPosition: {x: -0.6, y: -1.1000001, z: 0}
  TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  TargetScale: {x: 1, y: 1, z: 1}
  TargetRectSize: {x: 0, y: 0, z: 0}
  TransformPosition: {x: -0.6, y: -1.1000001, z: 0}
  TransformRotation: {x: 0, y: 0, z: 0, w: 1}
  TransformScale: {x: 1, y: 1, z: 1}
  TransformRectSize: {x: 1, y: 1}
--- !u!114 &705464176
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 705464170}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6122eecc36c8b14478b69adaee2c4d55, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Standard: {fileID: 2100000, guid: 165bbcb77f5b44048aa4c74551954e33, type: 2}
  URP: {fileID: 2100000, guid: 8154b61d528b98f40bcfa5bcc9cca9e5, type: 2}
  HDRP: {fileID: 2100000, guid: 08fe00e93d6897d41a6674db73f11c81, type: 2}
--- !u!1 &740342063
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 740342064}
  - component: {fileID: 740342067}
  - component: {fileID: 740342066}
  - component: {fileID: 740342065}
  - component: {fileID: 740342068}
  - component: {fileID: 740342069}
  m_Layer: 0
  m_Name: Sphere (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &740342064
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 740342063}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0.59999996, y: -1.1000001, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2050991704}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!135 &740342065
SphereCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 740342063}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Radius: 0.5
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &740342066
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 740342063}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 3feedb2344ae9cc40b610436dd57e3d0, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &740342067
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 740342063}
  m_Mesh: {fileID: 10207, guid: 0000000000000000e000000000000000, type: 0}
--- !u!114 &740342068
MonoBehaviour:
  m_ObjectHideFlags: 2
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 740342063}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994c66b6148109b489689ed705d244cd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Parent: {fileID: 2050991704}
  SiblingIndex: 7
  LayoutPosition: {x: 0.59999996, y: -1.1000001, z: 0}
  LayoutRotation: {x: 0, y: 0, z: 0, w: 1}
  AdapterBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.5, y: 0.5, z: 0.5}
  LayoutBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.5, y: 0.5, z: 0.5}
  RotatedAndScaledBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.5, y: 0.5, z: 0.5}
  ComponentScale: {x: 1, y: 1, z: 1}
  FillSize: {x: 1, y: 1, z: 1}
  ShrinkSize: {x: 999999, y: 999999, z: 999999}
  TargetPosition: {x: 0.59999996, y: -1.1000001, z: 0}
  TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  TargetScale: {x: 1, y: 1, z: 1}
  TargetRectSize: {x: 0, y: 0, z: 0}
  TransformPosition: {x: 0.59999996, y: -1.1000001, z: 0}
  TransformRotation: {x: 0, y: 0, z: 0, w: 1}
  TransformScale: {x: 1, y: 1, z: 1}
  TransformRectSize: {x: 1, y: 1}
--- !u!114 &740342069
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 740342063}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6122eecc36c8b14478b69adaee2c4d55, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Standard: {fileID: 2100000, guid: d1f3c8ac14b71fc479fe0370087f7d95, type: 2}
  URP: {fileID: 2100000, guid: 3feedb2344ae9cc40b610436dd57e3d0, type: 2}
  HDRP: {fileID: 2100000, guid: f29cbe433e772d04d91757e83b0e7185, type: 2}
--- !u!1 &755405168
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 755405169}
  - component: {fileID: 755405172}
  - component: {fileID: 755405171}
  - component: {fileID: 755405170}
  - component: {fileID: 755405173}
  - component: {fileID: 755405174}
  m_Layer: 0
  m_Name: Cylinder (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &755405169
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 755405168}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -1.8000002, y: -1.1000001, z: 0.00000008940697}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2050991704}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!136 &755405170
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 755405168}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.5000001
  m_Height: 2
  m_Direction: 1
  m_Center: {x: 0.000000059604645, y: 0, z: -0.00000008940697}
--- !u!23 &755405171
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 755405168}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 421327a16a2bbfd44a322a684c75142b, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &755405172
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 755405168}
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!114 &755405173
MonoBehaviour:
  m_ObjectHideFlags: 2
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 755405168}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994c66b6148109b489689ed705d244cd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Parent: {fileID: 2050991704}
  SiblingIndex: 5
  LayoutPosition: {x: -1.8000002, y: -1.1000001, z: 0}
  LayoutRotation: {x: 0, y: 0, z: 0, w: 1}
  AdapterBounds:
    m_Center: {x: 0.000000059604645, y: 0, z: -0.00000008940697}
    m_Extent: {x: 0.50000006, y: 1, z: 0.5000001}
  LayoutBounds:
    m_Center: {x: 0.000000059604645, y: 0, z: -0.00000008940697}
    m_Extent: {x: 0.50000006, y: 1, z: 0.5000001}
  RotatedAndScaledBounds:
    m_Center: {x: 0.000000059604645, y: 0, z: -0.00000008940697}
    m_Extent: {x: 0.50000006, y: 1, z: 0.5000001}
  ComponentScale: {x: 1, y: 1, z: 1}
  FillSize: {x: 1.0000001, y: 2, z: 1.0000002}
  ShrinkSize: {x: 999999, y: 999999, z: 999999}
  TargetPosition: {x: -1.8000002, y: -1.1000001, z: 0.00000008940697}
  TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  TargetScale: {x: 1, y: 1, z: 1}
  TargetRectSize: {x: 0, y: 0, z: 0}
  TransformPosition: {x: -1.8000002, y: -1.1000001, z: 0.00000008940697}
  TransformRotation: {x: 0, y: 0, z: 0, w: 1}
  TransformScale: {x: 1, y: 1, z: 1}
  TransformRectSize: {x: 1.0000001, y: 2}
--- !u!114 &755405174
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 755405168}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6122eecc36c8b14478b69adaee2c4d55, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Standard: {fileID: 2100000, guid: 92db8bdc2e8b3c74c8b6ac0824bc84a2, type: 2}
  URP: {fileID: 2100000, guid: 421327a16a2bbfd44a322a684c75142b, type: 2}
  HDRP: {fileID: 2100000, guid: 7b0ab49e7b8a2c94c91d99f6ea1761c2, type: 2}
--- !u!1 &767691250
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 767691251}
  - component: {fileID: 767691254}
  - component: {fileID: 767691253}
  - component: {fileID: 767691252}
  - component: {fileID: 767691255}
  - component: {fileID: 767691256}
  m_Layer: 0
  m_Name: Cylinder (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &767691251
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 767691250}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 1.8, y: -1.1000001, z: 0.00000008940697}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2050991704}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!136 &767691252
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 767691250}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.5000001
  m_Height: 2
  m_Direction: 1
  m_Center: {x: 0.000000059604645, y: 0, z: -0.00000008940697}
--- !u!23 &767691253
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 767691250}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 421327a16a2bbfd44a322a684c75142b, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &767691254
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 767691250}
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!114 &767691255
MonoBehaviour:
  m_ObjectHideFlags: 2
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 767691250}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994c66b6148109b489689ed705d244cd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Parent: {fileID: 2050991704}
  SiblingIndex: 8
  LayoutPosition: {x: 1.8, y: -1.1000001, z: 0}
  LayoutRotation: {x: 0, y: 0, z: 0, w: 1}
  AdapterBounds:
    m_Center: {x: 0.000000059604645, y: 0, z: -0.00000008940697}
    m_Extent: {x: 0.50000006, y: 1, z: 0.5000001}
  LayoutBounds:
    m_Center: {x: 0.000000059604645, y: 0, z: -0.00000008940697}
    m_Extent: {x: 0.50000006, y: 1, z: 0.5000001}
  RotatedAndScaledBounds:
    m_Center: {x: 0.000000059604645, y: 0, z: -0.00000008940697}
    m_Extent: {x: 0.50000006, y: 1, z: 0.5000001}
  ComponentScale: {x: 1, y: 1, z: 1}
  FillSize: {x: 1.0000001, y: 2, z: 1.0000002}
  ShrinkSize: {x: 999999, y: 999999, z: 999999}
  TargetPosition: {x: 1.8, y: -1.1000001, z: 0.00000008940697}
  TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  TargetScale: {x: 1, y: 1, z: 1}
  TargetRectSize: {x: 0, y: 0, z: 0}
  TransformPosition: {x: 1.8, y: -1.1000001, z: 0.00000008940697}
  TransformRotation: {x: 0, y: 0, z: 0, w: 1}
  TransformScale: {x: 1, y: 1, z: 1}
  TransformRectSize: {x: 1.0000001, y: 2}
--- !u!114 &767691256
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 767691250}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6122eecc36c8b14478b69adaee2c4d55, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Standard: {fileID: 2100000, guid: 92db8bdc2e8b3c74c8b6ac0824bc84a2, type: 2}
  URP: {fileID: 2100000, guid: 421327a16a2bbfd44a322a684c75142b, type: 2}
  HDRP: {fileID: 2100000, guid: 7b0ab49e7b8a2c94c91d99f6ea1761c2, type: 2}
--- !u!1 &834929724
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 834929725}
  - component: {fileID: 834929730}
  - component: {fileID: 834929729}
  - component: {fileID: 834929728}
  - component: {fileID: 834929727}
  - component: {fileID: 834929726}
  m_Layer: 0
  m_Name: Cylinder (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &834929725
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 834929724}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 2.4357836, y: 0, z: 0.00000008940697}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 62541751}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &834929726
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 834929724}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6122eecc36c8b14478b69adaee2c4d55, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Standard: {fileID: 2100000, guid: 92db8bdc2e8b3c74c8b6ac0824bc84a2, type: 2}
  URP: {fileID: 2100000, guid: 421327a16a2bbfd44a322a684c75142b, type: 2}
  HDRP: {fileID: 2100000, guid: 7b0ab49e7b8a2c94c91d99f6ea1761c2, type: 2}
--- !u!114 &834929727
MonoBehaviour:
  m_ObjectHideFlags: 2
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 834929724}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994c66b6148109b489689ed705d244cd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Parent: {fileID: 62541751}
  SiblingIndex: 4
  LayoutPosition: {x: 2.4357836, y: 0, z: 0}
  LayoutRotation: {x: 0, y: 0, z: 0, w: 1}
  AdapterBounds:
    m_Center: {x: 0.000000059604645, y: 0, z: -0.00000008940697}
    m_Extent: {x: 0.50000006, y: 1, z: 0.5000001}
  LayoutBounds:
    m_Center: {x: 0.000000059604645, y: 0, z: -0.00000008940697}
    m_Extent: {x: 0.50000006, y: 1, z: 0.5000001}
  RotatedAndScaledBounds:
    m_Center: {x: 0.000000059604645, y: 0, z: -0.00000008940697}
    m_Extent: {x: 0.50000006, y: 1, z: 0.5000001}
  ComponentScale: {x: 1, y: 1, z: 1}
  FillSize: {x: 1.0000001, y: 2, z: 1.0000002}
  ShrinkSize: {x: 999999, y: 999999, z: 999999}
  TargetPosition: {x: 2.4357836, y: 0, z: 0.00000008940697}
  TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  TargetScale: {x: 1, y: 1, z: 1}
  TargetRectSize: {x: 0, y: 0, z: 0}
  TransformPosition: {x: 2.4357836, y: 0, z: 0.00000008940697}
  TransformRotation: {x: 0, y: 0, z: 0, w: 1}
  TransformScale: {x: 1, y: 1, z: 1}
  TransformRectSize: {x: 1.0000001, y: 2}
--- !u!136 &834929728
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 834929724}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.5000001
  m_Height: 2
  m_Direction: 1
  m_Center: {x: 0.000000059604645, y: 0, z: -0.00000008940697}
--- !u!23 &834929729
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 834929724}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 421327a16a2bbfd44a322a684c75142b, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &834929730
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 834929724}
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &972470867
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 972470868}
  - component: {fileID: 972470871}
  - component: {fileID: 972470870}
  - component: {fileID: 972470869}
  - component: {fileID: 972470872}
  - component: {fileID: 972470873}
  m_Layer: 0
  m_Name: Cube (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &972470868
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 972470867}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 1.2000003, y: 1.0999999, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2050991704}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!65 &972470869
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 972470867}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &972470870
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 972470867}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 8154b61d528b98f40bcfa5bcc9cca9e5, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &972470871
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 972470867}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!114 &972470872
MonoBehaviour:
  m_ObjectHideFlags: 2
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 972470867}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994c66b6148109b489689ed705d244cd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Parent: {fileID: 2050991704}
  SiblingIndex: 3
  LayoutPosition: {x: 1.2000003, y: 1.0999999, z: 0}
  LayoutRotation: {x: 0, y: 0, z: 0, w: 1}
  AdapterBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.5, y: 0.5, z: 0.5}
  LayoutBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.5, y: 0.5, z: 0.5}
  RotatedAndScaledBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.5, y: 0.5, z: 0.5}
  ComponentScale: {x: 1, y: 1, z: 1}
  FillSize: {x: 1, y: 1, z: 1}
  ShrinkSize: {x: 999999, y: 999999, z: 999999}
  TargetPosition: {x: 1.2000003, y: 1.0999999, z: 0}
  TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  TargetScale: {x: 1, y: 1, z: 1}
  TargetRectSize: {x: 0, y: 0, z: 0}
  TransformPosition: {x: 1.2000003, y: 1.0999999, z: 0}
  TransformRotation: {x: 0, y: 0, z: 0, w: 1}
  TransformScale: {x: 1, y: 1, z: 1}
  TransformRectSize: {x: 1, y: 1}
--- !u!114 &972470873
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 972470867}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6122eecc36c8b14478b69adaee2c4d55, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Standard: {fileID: 2100000, guid: 165bbcb77f5b44048aa4c74551954e33, type: 2}
  URP: {fileID: 2100000, guid: 8154b61d528b98f40bcfa5bcc9cca9e5, type: 2}
  HDRP: {fileID: 2100000, guid: 08fe00e93d6897d41a6674db73f11c81, type: 2}
--- !u!1 &1077988680
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1077988681}
  - component: {fileID: 1077988686}
  - component: {fileID: 1077988685}
  - component: {fileID: 1077988684}
  - component: {fileID: 1077988683}
  - component: {fileID: 1077988682}
  m_Layer: 0
  m_Name: Cylinder
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1077988681
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1077988680}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -3.6357832, y: 0, z: 0.00000008940697}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 62541751}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1077988682
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1077988680}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6122eecc36c8b14478b69adaee2c4d55, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Standard: {fileID: 2100000, guid: 92db8bdc2e8b3c74c8b6ac0824bc84a2, type: 2}
  URP: {fileID: 2100000, guid: 421327a16a2bbfd44a322a684c75142b, type: 2}
  HDRP: {fileID: 2100000, guid: 7b0ab49e7b8a2c94c91d99f6ea1761c2, type: 2}
--- !u!114 &1077988683
MonoBehaviour:
  m_ObjectHideFlags: 2
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1077988680}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994c66b6148109b489689ed705d244cd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Parent: {fileID: 62541751}
  SiblingIndex: 1
  LayoutPosition: {x: -3.6357832, y: 0, z: 0}
  LayoutRotation: {x: 0, y: 0, z: 0, w: 1}
  AdapterBounds:
    m_Center: {x: 0.000000059604645, y: 0, z: -0.00000008940697}
    m_Extent: {x: 0.50000006, y: 1, z: 0.5000001}
  LayoutBounds:
    m_Center: {x: 0.000000059604645, y: 0, z: -0.00000008940697}
    m_Extent: {x: 0.50000006, y: 1, z: 0.5000001}
  RotatedAndScaledBounds:
    m_Center: {x: 0.000000059604645, y: 0, z: -0.00000008940697}
    m_Extent: {x: 0.50000006, y: 1, z: 0.5000001}
  ComponentScale: {x: 1, y: 1, z: 1}
  FillSize: {x: 1.0000001, y: 2, z: 1.0000002}
  ShrinkSize: {x: 999999, y: 999999, z: 999999}
  TargetPosition: {x: -3.6357832, y: 0, z: 0.00000008940697}
  TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  TargetScale: {x: 1, y: 1, z: 1}
  TargetRectSize: {x: 0, y: 0, z: 0}
  TransformPosition: {x: -3.6357832, y: 0, z: 0.00000008940697}
  TransformRotation: {x: 0, y: 0, z: 0, w: 1}
  TransformScale: {x: 1, y: 1, z: 1}
  TransformRectSize: {x: 1.0000001, y: 2}
--- !u!136 &1077988684
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1077988680}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.5000001
  m_Height: 2
  m_Direction: 1
  m_Center: {x: 0.000000059604645, y: 0, z: -0.00000008940697}
--- !u!23 &1077988685
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1077988680}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 421327a16a2bbfd44a322a684c75142b, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1077988686
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1077988680}
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &**********
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: **********}
  - component: {fileID: **********}
  m_Layer: 0
  m_Name: Flexalon
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &**********
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: **********}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 496811e5d5d9b4bcba367bcf82bb26ff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _updateInEditMode: 1
  _updateInPlayMode: 1
  _skipInactiveObjects: 1
  _inputProvider: {fileID: 0}
--- !u!4 &**********
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: **********}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &**********
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: **********}
  - component: {fileID: **********}
  - component: {fileID: **********}
  m_Layer: 0
  m_Name: Explanation
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &**********
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: **********}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9541d86e2fd84c1d9990edf0852d74ab, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: This is a wrapping flexible layout. Try modifying the width or adding more
    objects to see how the wrapping works.
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 4
  m_fontSizeBase: 4
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 256
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 1
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 0
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  _SortingLayer: 0
  _SortingLayerID: 0
  _SortingOrder: 0
  m_hasFontAssetChanged: 0
  m_renderer: {fileID: **********}
  m_maskType: 0
--- !u!23 &**********
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: **********}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!224 &**********
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: **********}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: -6, y: 4.16}
  m_SizeDelta: {x: 8.91, y: 0.7737}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &1242311008
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1242311009}
  - component: {fileID: 1242311012}
  - component: {fileID: 1242311011}
  - component: {fileID: 1242311010}
  - component: {fileID: 1242311013}
  - component: {fileID: 1242311014}
  m_Layer: 0
  m_Name: Cube
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1242311009
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1242311008}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -2.3999999, y: 1.0999999, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2050991704}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!65 &1242311010
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1242311008}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &1242311011
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1242311008}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 8154b61d528b98f40bcfa5bcc9cca9e5, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1242311012
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1242311008}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!114 &1242311013
MonoBehaviour:
  m_ObjectHideFlags: 2
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1242311008}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994c66b6148109b489689ed705d244cd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Parent: {fileID: 2050991704}
  SiblingIndex: 0
  LayoutPosition: {x: -2.3999999, y: 1.0999999, z: 0}
  LayoutRotation: {x: 0, y: 0, z: 0, w: 1}
  AdapterBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.5, y: 0.5, z: 0.5}
  LayoutBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.5, y: 0.5, z: 0.5}
  RotatedAndScaledBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.5, y: 0.5, z: 0.5}
  ComponentScale: {x: 1, y: 1, z: 1}
  FillSize: {x: 1, y: 1, z: 1}
  ShrinkSize: {x: 999999, y: 999999, z: 999999}
  TargetPosition: {x: -2.3999999, y: 1.0999999, z: 0}
  TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  TargetScale: {x: 1, y: 1, z: 1}
  TargetRectSize: {x: 0, y: 0, z: 0}
  TransformPosition: {x: -2.3999999, y: 1.0999999, z: 0}
  TransformRotation: {x: 0, y: 0, z: 0, w: 1}
  TransformScale: {x: 1, y: 1, z: 1}
  TransformRectSize: {x: 1, y: 1}
--- !u!114 &1242311014
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1242311008}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6122eecc36c8b14478b69adaee2c4d55, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Standard: {fileID: 2100000, guid: 165bbcb77f5b44048aa4c74551954e33, type: 2}
  URP: {fileID: 2100000, guid: 8154b61d528b98f40bcfa5bcc9cca9e5, type: 2}
  HDRP: {fileID: 2100000, guid: 08fe00e93d6897d41a6674db73f11c81, type: 2}
--- !u!1 &1343192069
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1343192070}
  - component: {fileID: 1343192076}
  - component: {fileID: 1343192075}
  - component: {fileID: 1343192074}
  - component: {fileID: 1343192073}
  - component: {fileID: 1343192072}
  - component: {fileID: 1343192071}
  m_Layer: 0
  m_Name: Filling Cube
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1343192070
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1343192069}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0.00000023841858, y: 0, z: 0}
  m_LocalScale: {x: 3.4715667, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 62541751}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1343192071
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1343192069}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 00b0da67f6b3c9a4e84ed47af9da4884, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _version: 4
  _widthType: 1
  _width: 1
  _widthOfParent: 1
  _heightType: 0
  _height: 1
  _heightOfParent: 1
  _depthType: 0
  _depth: 1
  _depthOfParent: 1
  _minWidthType: 0
  _minWidth: 0
  _minWidthOfParent: 0
  _minHeightType: 0
  _minHeight: 0
  _minHeightOfParent: 0
  _minDepthType: 0
  _minDepth: 0
  _minDepthOfParent: 0
  _maxWidthType: 0
  _maxWidth: 1
  _maxWidthOfParent: 1
  _maxHeightType: 0
  _maxHeight: 1
  _maxHeightOfParent: 1
  _maxDepthType: 0
  _maxDepth: 1
  _maxDepthOfParent: 1
  _offset: {x: 0, y: 0, z: 0}
  _scale: {x: 1, y: 1, z: 1}
  _rotation: {x: 0, y: 0, z: 0, w: 1}
  _marginLeft: 0
  _marginRight: 0
  _marginTop: 0
  _marginBottom: 0
  _marginFront: 0
  _marginBack: 0
  _paddingLeft: 0
  _paddingRight: 0
  _paddingTop: 0
  _paddingBottom: 0
  _paddingFront: 0
  _paddingBack: 0
  _skipLayout: 0
--- !u!114 &1343192072
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1343192069}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6122eecc36c8b14478b69adaee2c4d55, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Standard: {fileID: 2100000, guid: 165bbcb77f5b44048aa4c74551954e33, type: 2}
  URP: {fileID: 2100000, guid: 8154b61d528b98f40bcfa5bcc9cca9e5, type: 2}
  HDRP: {fileID: 2100000, guid: 08fe00e93d6897d41a6674db73f11c81, type: 2}
--- !u!114 &1343192073
MonoBehaviour:
  m_ObjectHideFlags: 2
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1343192069}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994c66b6148109b489689ed705d244cd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Parent: {fileID: 62541751}
  SiblingIndex: 3
  LayoutPosition: {x: 0.00000023841858, y: 0, z: 0}
  LayoutRotation: {x: 0, y: 0, z: 0, w: 1}
  AdapterBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 1.7357833, y: 0.5, z: 0.5}
  LayoutBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 1.7357833, y: 0.5, z: 0.5}
  RotatedAndScaledBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 1.7357833, y: 0.5, z: 0.5}
  ComponentScale: {x: 3.4715667, y: 1, z: 1}
  FillSize: {x: 3.4715667, y: 1, z: 1}
  ShrinkSize: {x: 999999, y: 999999, z: 999999}
  TargetPosition: {x: 0.00000023841858, y: 0, z: 0}
  TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  TargetScale: {x: 3.4715667, y: 1, z: 1}
  TargetRectSize: {x: 0, y: 0, z: 0}
  TransformPosition: {x: 0.00000023841858, y: 0, z: 0}
  TransformRotation: {x: 0, y: 0, z: 0, w: 1}
  TransformScale: {x: 3.4715667, y: 1, z: 1}
  TransformRectSize: {x: 3.4715667, y: 1}
--- !u!65 &1343192074
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1343192069}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &1343192075
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1343192069}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 8154b61d528b98f40bcfa5bcc9cca9e5, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1343192076
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1343192069}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1365905927
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1365905929}
  - component: {fileID: 1365905928}
  - component: {fileID: 1365905930}
  m_Layer: 0
  m_Name: Directional Light
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!108 &1365905928
Light:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1365905927}
  m_Enabled: 1
  serializedVersion: 11
  m_Type: 1
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_Intensity: 3.14
  m_Range: 10
  m_SpotAngle: 30
  m_InnerSpotAngle: 21.80208
  m_CookieSize: 10
  m_Shadows:
    m_Type: 2
    m_Resolution: -1
    m_CustomResolution: -1
    m_Strength: 1
    m_Bias: 0.05
    m_NormalBias: 0.4
    m_NearPlane: 0.2
    m_CullingMatrixOverride:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    m_UseCullingMatrixOverride: 0
  m_Cookie: {fileID: 0}
  m_DrawHalo: 0
  m_Flare: {fileID: 0}
  m_RenderMode: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingLayerMask: 1
  m_Lightmapping: 4
  m_LightShadowCasterMode: 2
  m_AreaSize: {x: 1, y: 1}
  m_BounceIntensity: 1
  m_ColorTemperature: 6570
  m_UseColorTemperature: 0
  m_BoundingSphereOverride: {x: 0, y: 0, z: 0, w: 0}
  m_UseBoundingSphereOverride: 0
  m_UseViewFrustumForShadowCasterCull: 1
  m_ForceVisible: 0
  m_ShadowRadius: 0
  m_ShadowAngle: 0
  m_LightUnit: 1
  m_LuxAtDistance: 1
  m_EnableSpotReflector: 1
--- !u!4 &1365905929
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1365905927}
  serializedVersion: 2
  m_LocalRotation: {x: 0.40821788, y: -0.23456968, z: 0.10938163, w: 0.8754261}
  m_LocalPosition: {x: 0, y: 3, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 50, y: -30, z: 0}
--- !u!114 &1365905930
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1365905927}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cb57fb0c2ab17b84e9162c1bdc5285bb, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  StandardIntensity: 3.14
  HDRPIntensity: 100000
--- !u!1 &1566359688
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1566359689}
  - component: {fileID: 1566359694}
  - component: {fileID: 1566359693}
  - component: {fileID: 1566359692}
  - component: {fileID: 1566359691}
  - component: {fileID: 1566359690}
  m_Layer: 0
  m_Name: Cylinder (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1566359689
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1566359688}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 4.8357835, y: 0, z: 0.00000008940697}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 62541751}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1566359690
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1566359688}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6122eecc36c8b14478b69adaee2c4d55, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Standard: {fileID: 2100000, guid: 92db8bdc2e8b3c74c8b6ac0824bc84a2, type: 2}
  URP: {fileID: 2100000, guid: 421327a16a2bbfd44a322a684c75142b, type: 2}
  HDRP: {fileID: 2100000, guid: 7b0ab49e7b8a2c94c91d99f6ea1761c2, type: 2}
--- !u!114 &1566359691
MonoBehaviour:
  m_ObjectHideFlags: 2
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1566359688}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994c66b6148109b489689ed705d244cd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Parent: {fileID: 62541751}
  SiblingIndex: 6
  LayoutPosition: {x: 4.8357835, y: 0, z: 0}
  LayoutRotation: {x: 0, y: 0, z: 0, w: 1}
  AdapterBounds:
    m_Center: {x: 0.000000059604645, y: 0, z: -0.00000008940697}
    m_Extent: {x: 0.50000006, y: 1, z: 0.5000001}
  LayoutBounds:
    m_Center: {x: 0.000000059604645, y: 0, z: -0.00000008940697}
    m_Extent: {x: 0.50000006, y: 1, z: 0.5000001}
  RotatedAndScaledBounds:
    m_Center: {x: 0.000000059604645, y: 0, z: -0.00000008940697}
    m_Extent: {x: 0.50000006, y: 1, z: 0.5000001}
  ComponentScale: {x: 1, y: 1, z: 1}
  FillSize: {x: 1.0000001, y: 2, z: 1.0000002}
  ShrinkSize: {x: 999999, y: 999999, z: 999999}
  TargetPosition: {x: 4.8357835, y: 0, z: 0.00000008940697}
  TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  TargetScale: {x: 1, y: 1, z: 1}
  TargetRectSize: {x: 0, y: 0, z: 0}
  TransformPosition: {x: 4.8357835, y: 0, z: 0.00000008940697}
  TransformRotation: {x: 0, y: 0, z: 0, w: 1}
  TransformScale: {x: 1, y: 1, z: 1}
  TransformRectSize: {x: 1.0000001, y: 2}
--- !u!136 &1566359692
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1566359688}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.5000001
  m_Height: 2
  m_Direction: 1
  m_Center: {x: 0.000000059604645, y: 0, z: -0.00000008940697}
--- !u!23 &1566359693
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1566359688}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 421327a16a2bbfd44a322a684c75142b, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1566359694
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1566359688}
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1847320400
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1847320401}
  - component: {fileID: 1847320404}
  - component: {fileID: 1847320403}
  - component: {fileID: 1847320402}
  - component: {fileID: 1847320405}
  - component: {fileID: 1847320406}
  m_Layer: 0
  m_Name: Sphere
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1847320401
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1847320400}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -1.1999998, y: 1.0999999, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2050991704}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!135 &1847320402
SphereCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1847320400}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Radius: 0.5
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &1847320403
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1847320400}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 3feedb2344ae9cc40b610436dd57e3d0, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1847320404
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1847320400}
  m_Mesh: {fileID: 10207, guid: 0000000000000000e000000000000000, type: 0}
--- !u!114 &1847320405
MonoBehaviour:
  m_ObjectHideFlags: 2
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1847320400}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994c66b6148109b489689ed705d244cd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Parent: {fileID: 2050991704}
  SiblingIndex: 1
  LayoutPosition: {x: -1.1999998, y: 1.0999999, z: 0}
  LayoutRotation: {x: 0, y: 0, z: 0, w: 1}
  AdapterBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.5, y: 0.5, z: 0.5}
  LayoutBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.5, y: 0.5, z: 0.5}
  RotatedAndScaledBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.5, y: 0.5, z: 0.5}
  ComponentScale: {x: 1, y: 1, z: 1}
  FillSize: {x: 1, y: 1, z: 1}
  ShrinkSize: {x: 999999, y: 999999, z: 999999}
  TargetPosition: {x: -1.1999998, y: 1.0999999, z: 0}
  TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  TargetScale: {x: 1, y: 1, z: 1}
  TargetRectSize: {x: 0, y: 0, z: 0}
  TransformPosition: {x: -1.1999998, y: 1.0999999, z: 0}
  TransformRotation: {x: 0, y: 0, z: 0, w: 1}
  TransformScale: {x: 1, y: 1, z: 1}
  TransformRectSize: {x: 1, y: 1}
--- !u!114 &1847320406
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1847320400}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6122eecc36c8b14478b69adaee2c4d55, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Standard: {fileID: 2100000, guid: d1f3c8ac14b71fc479fe0370087f7d95, type: 2}
  URP: {fileID: 2100000, guid: 3feedb2344ae9cc40b610436dd57e3d0, type: 2}
  HDRP: {fileID: 2100000, guid: f29cbe433e772d04d91757e83b0e7185, type: 2}
--- !u!1 &1860966568
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1860966569}
  - component: {fileID: 1860966574}
  - component: {fileID: 1860966573}
  - component: {fileID: 1860966572}
  - component: {fileID: 1860966571}
  - component: {fileID: 1860966570}
  m_Layer: 0
  m_Name: Sphere (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1860966569
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1860966568}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 3.6357837, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 62541751}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1860966570
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1860966568}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6122eecc36c8b14478b69adaee2c4d55, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Standard: {fileID: 2100000, guid: d1f3c8ac14b71fc479fe0370087f7d95, type: 2}
  URP: {fileID: 2100000, guid: 3feedb2344ae9cc40b610436dd57e3d0, type: 2}
  HDRP: {fileID: 2100000, guid: f29cbe433e772d04d91757e83b0e7185, type: 2}
--- !u!114 &1860966571
MonoBehaviour:
  m_ObjectHideFlags: 2
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1860966568}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994c66b6148109b489689ed705d244cd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Parent: {fileID: 62541751}
  SiblingIndex: 5
  LayoutPosition: {x: 3.6357837, y: 0, z: 0}
  LayoutRotation: {x: 0, y: 0, z: 0, w: 1}
  AdapterBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.5, y: 0.5, z: 0.5}
  LayoutBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.5, y: 0.5, z: 0.5}
  RotatedAndScaledBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.5, y: 0.5, z: 0.5}
  ComponentScale: {x: 1, y: 1, z: 1}
  FillSize: {x: 1, y: 1, z: 1}
  ShrinkSize: {x: 999999, y: 999999, z: 999999}
  TargetPosition: {x: 3.6357837, y: 0, z: 0}
  TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  TargetScale: {x: 1, y: 1, z: 1}
  TargetRectSize: {x: 0, y: 0, z: 0}
  TransformPosition: {x: 3.6357837, y: 0, z: 0}
  TransformRotation: {x: 0, y: 0, z: 0, w: 1}
  TransformScale: {x: 1, y: 1, z: 1}
  TransformRectSize: {x: 1, y: 1}
--- !u!135 &1860966572
SphereCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1860966568}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Radius: 0.5
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &1860966573
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1860966568}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 3feedb2344ae9cc40b610436dd57e3d0, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1860966574
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1860966568}
  m_Mesh: {fileID: 10207, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1903162705
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1903162706}
  - component: {fileID: 1903162711}
  - component: {fileID: 1903162710}
  - component: {fileID: 1903162709}
  - component: {fileID: 1903162708}
  - component: {fileID: 1903162707}
  m_Layer: 0
  m_Name: Sphere
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1903162706
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1903162705}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -4.835783, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 62541751}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1903162707
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1903162705}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6122eecc36c8b14478b69adaee2c4d55, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Standard: {fileID: 2100000, guid: d1f3c8ac14b71fc479fe0370087f7d95, type: 2}
  URP: {fileID: 2100000, guid: 3feedb2344ae9cc40b610436dd57e3d0, type: 2}
  HDRP: {fileID: 2100000, guid: f29cbe433e772d04d91757e83b0e7185, type: 2}
--- !u!114 &1903162708
MonoBehaviour:
  m_ObjectHideFlags: 2
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1903162705}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994c66b6148109b489689ed705d244cd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Parent: {fileID: 62541751}
  SiblingIndex: 0
  LayoutPosition: {x: -4.835783, y: 0, z: 0}
  LayoutRotation: {x: 0, y: 0, z: 0, w: 1}
  AdapterBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.5, y: 0.5, z: 0.5}
  LayoutBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.5, y: 0.5, z: 0.5}
  RotatedAndScaledBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.5, y: 0.5, z: 0.5}
  ComponentScale: {x: 1, y: 1, z: 1}
  FillSize: {x: 1, y: 1, z: 1}
  ShrinkSize: {x: 999999, y: 999999, z: 999999}
  TargetPosition: {x: -4.835783, y: 0, z: 0}
  TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  TargetScale: {x: 1, y: 1, z: 1}
  TargetRectSize: {x: 0, y: 0, z: 0}
  TransformPosition: {x: -4.835783, y: 0, z: 0}
  TransformRotation: {x: 0, y: 0, z: 0, w: 1}
  TransformScale: {x: 1, y: 1, z: 1}
  TransformRectSize: {x: 1, y: 1}
--- !u!135 &1903162709
SphereCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1903162705}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Radius: 0.5
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &1903162710
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1903162705}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 3feedb2344ae9cc40b610436dd57e3d0, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1903162711
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1903162705}
  m_Mesh: {fileID: 10207, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1971579503
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1971579504}
  - component: {fileID: 1971579509}
  - component: {fileID: 1971579508}
  - component: {fileID: 1971579507}
  - component: {fileID: 1971579506}
  - component: {fileID: 1971579505}
  m_Layer: 0
  m_Name: Sphere (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1971579504
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1971579503}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -2.4357831, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 62541751}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1971579505
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1971579503}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6122eecc36c8b14478b69adaee2c4d55, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Standard: {fileID: 2100000, guid: d1f3c8ac14b71fc479fe0370087f7d95, type: 2}
  URP: {fileID: 2100000, guid: 3feedb2344ae9cc40b610436dd57e3d0, type: 2}
  HDRP: {fileID: 2100000, guid: f29cbe433e772d04d91757e83b0e7185, type: 2}
--- !u!114 &1971579506
MonoBehaviour:
  m_ObjectHideFlags: 2
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1971579503}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994c66b6148109b489689ed705d244cd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Parent: {fileID: 62541751}
  SiblingIndex: 2
  LayoutPosition: {x: -2.4357831, y: 0, z: 0}
  LayoutRotation: {x: 0, y: 0, z: 0, w: 1}
  AdapterBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.5, y: 0.5, z: 0.5}
  LayoutBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.5, y: 0.5, z: 0.5}
  RotatedAndScaledBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0.5, y: 0.5, z: 0.5}
  ComponentScale: {x: 1, y: 1, z: 1}
  FillSize: {x: 1, y: 1, z: 1}
  ShrinkSize: {x: 999999, y: 999999, z: 999999}
  TargetPosition: {x: -2.4357831, y: 0, z: 0}
  TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  TargetScale: {x: 1, y: 1, z: 1}
  TargetRectSize: {x: 0, y: 0, z: 0}
  TransformPosition: {x: -2.4357831, y: 0, z: 0}
  TransformRotation: {x: 0, y: 0, z: 0, w: 1}
  TransformScale: {x: 1, y: 1, z: 1}
  TransformRectSize: {x: 1, y: 1}
--- !u!135 &1971579507
SphereCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1971579503}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Radius: 0.5
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &1971579508
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1971579503}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 3feedb2344ae9cc40b610436dd57e3d0, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1971579509
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1971579503}
  m_Mesh: {fileID: 10207, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1997495760
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1997495761}
  - component: {fileID: 1997495764}
  - component: {fileID: 1997495763}
  - component: {fileID: 1997495762}
  - component: {fileID: 1997495765}
  - component: {fileID: 1997495766}
  m_Layer: 0
  m_Name: Cylinder
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1997495761
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1997495760}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0.00000017881393, y: 1.0999999, z: 0.00000008940697}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2050991704}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!136 &1997495762
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1997495760}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.5000001
  m_Height: 2
  m_Direction: 1
  m_Center: {x: 0.000000059604645, y: 0, z: -0.00000008940697}
--- !u!23 &1997495763
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1997495760}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 421327a16a2bbfd44a322a684c75142b, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1997495764
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1997495760}
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!114 &1997495765
MonoBehaviour:
  m_ObjectHideFlags: 2
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1997495760}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994c66b6148109b489689ed705d244cd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Parent: {fileID: 2050991704}
  SiblingIndex: 2
  LayoutPosition: {x: 0.00000023841858, y: 1.0999999, z: 0}
  LayoutRotation: {x: 0, y: 0, z: 0, w: 1}
  AdapterBounds:
    m_Center: {x: 0.000000059604645, y: 0, z: -0.00000008940697}
    m_Extent: {x: 0.50000006, y: 1, z: 0.5000001}
  LayoutBounds:
    m_Center: {x: 0.000000059604645, y: 0, z: -0.00000008940697}
    m_Extent: {x: 0.50000006, y: 1, z: 0.5000001}
  RotatedAndScaledBounds:
    m_Center: {x: 0.000000059604645, y: 0, z: -0.00000008940697}
    m_Extent: {x: 0.50000006, y: 1, z: 0.5000001}
  ComponentScale: {x: 1, y: 1, z: 1}
  FillSize: {x: 1.0000001, y: 2, z: 1.0000002}
  ShrinkSize: {x: 999999, y: 999999, z: 999999}
  TargetPosition: {x: 0.00000017881393, y: 1.0999999, z: 0.00000008940697}
  TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  TargetScale: {x: 1, y: 1, z: 1}
  TargetRectSize: {x: 0, y: 0, z: 0}
  TransformPosition: {x: 0.00000017881393, y: 1.0999999, z: 0.00000008940697}
  TransformRotation: {x: 0, y: 0, z: 0, w: 1}
  TransformScale: {x: 1, y: 1, z: 1}
  TransformRectSize: {x: 1.0000001, y: 2}
--- !u!114 &1997495766
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1997495760}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6122eecc36c8b14478b69adaee2c4d55, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Standard: {fileID: 2100000, guid: 92db8bdc2e8b3c74c8b6ac0824bc84a2, type: 2}
  URP: {fileID: 2100000, guid: 421327a16a2bbfd44a322a684c75142b, type: 2}
  HDRP: {fileID: 2100000, guid: 7b0ab49e7b8a2c94c91d99f6ea1761c2, type: 2}
--- !u!1 &2050991701
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2050991704}
  - component: {fileID: 2050991703}
  - component: {fileID: 2050991702}
  - component: {fileID: 2050991705}
  m_Layer: 0
  m_Name: WrappingFlexibleLayout
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &2050991702
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2050991701}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 00b0da67f6b3c9a4e84ed47af9da4884, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _version: 4
  _widthType: 0
  _width: 6.3870664
  _widthOfParent: 1
  _heightType: 3
  _height: 1
  _heightOfParent: 1
  _depthType: 3
  _depth: 1
  _depthOfParent: 1
  _minWidthType: 0
  _minWidth: 0
  _minWidthOfParent: 0
  _minHeightType: 0
  _minHeight: 0
  _minHeightOfParent: 0
  _minDepthType: 0
  _minDepth: 0
  _minDepthOfParent: 0
  _maxWidthType: 0
  _maxWidth: 1
  _maxWidthOfParent: 1
  _maxHeightType: 0
  _maxHeight: 1
  _maxHeightOfParent: 1
  _maxDepthType: 0
  _maxDepth: 1
  _maxDepthOfParent: 1
  _offset: {x: 0, y: 0, z: 0}
  _scale: {x: 1, y: 1, z: 1}
  _rotation: {x: 0, y: 0, z: 0, w: 1}
  _marginLeft: 0
  _marginRight: 0
  _marginTop: 0
  _marginBottom: 0
  _marginFront: 0
  _marginBack: 0
  _paddingLeft: 0
  _paddingRight: 0
  _paddingTop: 0
  _paddingBottom: 0
  _paddingFront: 0
  _paddingBack: 0
  _skipLayout: 0
--- !u!114 &2050991703
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2050991701}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3bcb5ae438b41e3449fdddabaf22c434, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _version: 4
  _direction: 0
  _wrap: 1
  _wrapDirection: 3
  _horizontalAlign: 1
  _verticalAlign: 1
  _depthAlign: 1
  _horizontalInnerAlign: 1
  _verticalInnerAlign: 1
  _depthInnerAlign: 1
  _gapType: 0
  _gap: 0.2
  _wrapGapType: 0
  _wrapGap: 0.2
--- !u!4 &2050991704
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2050991701}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -6, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1242311009}
  - {fileID: 1847320401}
  - {fileID: 1997495761}
  - {fileID: 972470868}
  - {fileID: 272011452}
  - {fileID: 755405169}
  - {fileID: 705464171}
  - {fileID: 740342064}
  - {fileID: 767691251}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2050991705
MonoBehaviour:
  m_ObjectHideFlags: 2
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2050991701}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994c66b6148109b489689ed705d244cd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Parent: {fileID: 0}
  SiblingIndex: 0
  LayoutPosition: {x: 0, y: 0, z: 0}
  LayoutRotation: {x: 0, y: 0, z: 0, w: 1}
  AdapterBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 3.1935332, y: 2.1, z: 0.5000001}
  LayoutBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 3.1935332, y: 2.1, z: 0.5000001}
  RotatedAndScaledBounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 3.1935332, y: 2.1, z: 0.5000001}
  ComponentScale: {x: 1, y: 1, z: 1}
  FillSize: {x: 1, y: 1, z: 1}
  ShrinkSize: {x: 999999, y: 999999, z: 999999}
  TargetPosition: {x: -6, y: 0, z: 0}
  TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  TargetScale: {x: 1, y: 1, z: 1}
  TargetRectSize: {x: 0, y: 0, z: 0}
  TransformPosition: {x: -6, y: 0, z: 0}
  TransformRotation: {x: 0, y: 0, z: 0, w: 1}
  TransformScale: {x: 1, y: 1, z: 1}
  TransformRectSize: {x: 6.3870664, y: 4.2}
--- !u!1 &2140452828
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2140452831}
  - component: {fileID: 2140452830}
  - component: {fileID: 2140452829}
  m_Layer: 0
  m_Name: Explanation 2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &2140452829
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2140452828}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9541d86e2fd84c1d9990edf0852d74ab, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: The red cube in this flexible layout is set to fill the available space.
    Try modifying the width of the flexible layout or adding more items.
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 4
  m_fontSizeBase: 4
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 256
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 1
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 0
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  _SortingLayer: 0
  _SortingLayerID: 0
  _SortingOrder: 0
  m_hasFontAssetChanged: 0
  m_renderer: {fileID: 2140452830}
  m_maskType: 0
--- !u!23 &2140452830
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2140452828}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!224 &2140452831
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2140452828}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 6, y: 4.16}
  m_SizeDelta: {x: 8.91, y: 0.7737}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1660057539 &9223372036854775807
SceneRoots:
  m_ObjectHideFlags: 0
  m_Roots:
  - {fileID: 331720424}
  - {fileID: 1365905929}
  - {fileID: **********}
  - {fileID: **********}
  - {fileID: 2140452831}
  - {fileID: 2050991704}
  - {fileID: 62541751}
