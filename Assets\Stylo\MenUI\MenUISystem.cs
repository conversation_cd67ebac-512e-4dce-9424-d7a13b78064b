using UnityEngine;
using UnityEngine.UI;
using UnityEngine.InputSystem;
using UnityEngine.Events;
using BTR;

namespace Stylo.MenUI
{
    /// <summary>
    /// UGUI-based pause menu system for the MenUI framework.
    /// Provides pause functionality with Resume, Settings, and Exit options.
    /// Integrates with TimeManager for proper time control and existing input system.
    /// </summary>
    public class MenUISystem : MonoBehaviour
    {
        [Header("UI References")]
        [SerializeField] private GameObject pauseMenuPanel;
        [SerializeField] private Button resumeButton;
        [SerializeField] private Button settingsButton;
        [SerializeField] private Button exitButton;
        [SerializeField] private Canvas pauseCanvas;

        [Header("Settings Integration")]
        [SerializeField] private SettingsPanelManager settingsPanelManager;

        [Header("Configuration")]
        [SerializeField] private bool enableDebugMode = true;
        [SerializeField] private bool pauseAudio = true;

        [Header("Overlay Background")]
        [SerializeField] private bool enableOverlay = true;
        [SerializeField] private Color overlayColor = new Color(0f, 0f, 0f, 0.7f);
        [SerializeField] private GameObject overlayPanel;

        // Input system
        private DefaultControls _controls;
        private InputAction _pauseAction;

        // State
        private bool _isPaused = false;
        private float _previousTimeScale = 1f;

        // Events
        [System.NonSerialized]
        public UnityEvent<bool> OnPauseStateChanged = new UnityEvent<bool>();

        #region Unity Lifecycle

        private void Awake()
        {
            InitializeComponents();
            InitializeInput();
            ValidateSetup();
        }

        private void Start()
        {
            // Always hide menu initially
            HidePause();

            // Validate UI interaction components
            ValidateUIInteractionComponents();
        }

        private void SetupOverlayPanel()
        {
            if (overlayPanel == null && enableOverlay)
            {
                // Create overlay panel if it doesn't exist
                overlayPanel = new GameObject("OverlayPanel");
                overlayPanel.transform.SetParent(transform, false);

                // Add RectTransform and configure for full screen
                RectTransform overlayRect = overlayPanel.AddComponent<RectTransform>();
                overlayRect.anchorMin = Vector2.zero;
                overlayRect.anchorMax = Vector2.one;
                overlayRect.sizeDelta = Vector2.zero;
                overlayRect.anchoredPosition = Vector2.zero;

                // Add Image component for the overlay
                Image overlayImage = overlayPanel.AddComponent<Image>();
                overlayImage.color = overlayColor;

                // Set overlay to be behind the pause menu panel
                if (pauseMenuPanel != null)
                {
                    overlayPanel.transform.SetSiblingIndex(pauseMenuPanel.transform.GetSiblingIndex());
                }

                if (enableDebugMode)
                    Debug.Log("MenUISystem: Created overlay panel");
            }

            // Update overlay color if panel exists
            if (overlayPanel != null)
            {
                Image overlayImage = overlayPanel.GetComponent<Image>();
                if (overlayImage != null)
                {
                    overlayImage.color = overlayColor;
                }
            }
        }

        private void ValidateUIInteractionComponents()
        {
            // Check for EventSystem
            if (UnityEngine.EventSystems.EventSystem.current == null)
            {
                Debug.LogWarning("MenUISystem: No EventSystem found! Mouse/touch interaction may not work. Creating one...");
                CreateEventSystem();
            }

            // Check for GraphicRaycaster on Canvas
            if (pauseCanvas != null && pauseCanvas.GetComponent<UnityEngine.UI.GraphicRaycaster>() == null)
            {
                Debug.LogWarning("MenUISystem: No GraphicRaycaster found on Canvas! Adding one...");
                pauseCanvas.gameObject.AddComponent<UnityEngine.UI.GraphicRaycaster>();
            }
        }

        private void CreateEventSystem()
        {
            GameObject eventSystemGO = new GameObject("EventSystem");
            eventSystemGO.AddComponent<UnityEngine.EventSystems.EventSystem>();

#if ENABLE_INPUT_SYSTEM
            eventSystemGO.AddComponent<UnityEngine.InputSystem.UI.InputSystemUIInputModule>();
#else
                eventSystemGO.AddComponent<UnityEngine.EventSystems.StandaloneInputModule>();
#endif

            if (enableDebugMode)
                Debug.Log("MenUISystem: Created EventSystem for UI interaction");
        }

        private void OnEnable()
        {
            if (_controls != null)
            {
                _controls.Enable();
                _pauseAction.performed += OnPauseInput;
            }
        }

        private void OnDisable()
        {
            if (_controls != null)
            {
                _pauseAction.performed -= OnPauseInput;
                _controls.Disable();
            }
        }

        private void OnDestroy()
        {
            _controls?.Dispose();
        }

        #endregion

        #region Initialization

        private void InitializeComponents()
        {
            // Auto-find components if not assigned
            if (pauseMenuPanel == null)
                pauseMenuPanel = transform.Find("PauseMenuPanel")?.gameObject;

            if (pauseCanvas == null)
                pauseCanvas = GetComponentInParent<Canvas>();

            // Auto-find settings panel manager
            if (settingsPanelManager == null)
                settingsPanelManager = GetComponentInChildren<SettingsPanelManager>();

            // Auto-find or create overlay panel
            if (overlayPanel == null)
                overlayPanel = transform.Find("OverlayPanel")?.gameObject;

            // Find buttons if not assigned
            if (resumeButton == null && pauseMenuPanel != null)
                resumeButton = pauseMenuPanel.transform.Find("ResumeButton")?.GetComponent<Button>();

            if (settingsButton == null && pauseMenuPanel != null)
                settingsButton = pauseMenuPanel.transform.Find("SettingsButton")?.GetComponent<Button>();

            if (exitButton == null && pauseMenuPanel != null)
                exitButton = pauseMenuPanel.transform.Find("ExitButton")?.GetComponent<Button>();

            // Setup button callbacks
            if (resumeButton != null)
            {
                resumeButton.onClick.AddListener(OnResumeClicked);
                if (enableDebugMode)
                    Debug.Log($"MenUISystem: Resume button callback added to {resumeButton.name}");
            }

            if (settingsButton != null)
            {
                settingsButton.onClick.AddListener(OnSettingsClicked);
                if (enableDebugMode)
                    Debug.Log($"MenUISystem: Settings button callback added to {settingsButton.name}");
            }

            if (exitButton != null)
            {
                exitButton.onClick.AddListener(OnExitClicked);
                if (enableDebugMode)
                    Debug.Log($"MenUISystem: Exit button callback added to {exitButton.name}");
            }

            // Setup overlay
            SetupOverlayPanel();
        }

        private void InitializeInput()
        {
            _controls = new DefaultControls();
            _pauseAction = _controls.UI.Pause;
        }

        private void ValidateSetup()
        {
            if (pauseMenuPanel == null)
                Debug.LogError("MenUISystem: PauseMenuPanel not found! Assign it in the inspector or ensure it exists as a child named 'PauseMenuPanel'.");

            if (pauseCanvas == null)
                Debug.LogError("MenUISystem: Canvas not found! This component should be on a Canvas or child of a Canvas.");

            if (resumeButton == null)
                Debug.LogWarning("MenUISystem: Resume button not found. Assign it or ensure it exists as 'ResumeButton' under PauseMenuPanel.");

            if (settingsButton == null)
                Debug.LogWarning("MenUISystem: Settings button not found. Assign it or ensure it exists as 'SettingsButton' under PauseMenuPanel.");

            if (exitButton == null)
                Debug.LogWarning("MenUISystem: Exit button not found. Assign it or ensure it exists as 'ExitButton' under PauseMenuPanel.");
        }

        #endregion

        #region Input Handling

        private void OnPauseInput(InputAction.CallbackContext context)
        {
            TogglePause();
        }

        #endregion

        #region Button Callbacks

        private void OnResumeClicked()
        {
            HidePause();
        }

        private void OnSettingsClicked()
        {
            if (enableDebugMode)
                Debug.Log("MenUISystem: Settings button clicked!");

            ShowSettings();
        }

        private void OnExitClicked()
        {
            if (enableDebugMode)
                Debug.Log("MenUISystem: Exit button clicked - Quitting application");

#if UNITY_EDITOR
            UnityEditor.EditorApplication.isPlaying = false;
#else
                Application.Quit();
#endif
        }

        #endregion

        #region Public API (GameManager Interface)

        /// <summary>
        /// Show the pause menu and pause all systems
        /// </summary>
        public void ShowPause()
        {
            if (_isPaused) return;

            _isPaused = true;

            // Show overlay first (behind menu)
            if (overlayPanel != null && enableOverlay)
                overlayPanel.SetActive(true);

            // Show UI
            if (pauseMenuPanel != null)
                pauseMenuPanel.SetActive(true);

            // Only pause systems in play mode
            if (Application.isPlaying)
            {
                PauseAllSystems();
            }

            // Set focus to resume button for controller navigation
            if (resumeButton != null)
                resumeButton.Select();

            // Trigger events
            OnPauseStateChanged?.Invoke(true);

            if (enableDebugMode)
                Debug.Log("MenUISystem: Pause menu shown" + (Application.isPlaying ? " and systems paused" : " (editor mode)"));
        }

        /// <summary>
        /// Hide the pause menu and resume all systems
        /// </summary>
        public void HidePause()
        {
            if (!_isPaused) return;

            _isPaused = false;

            // Hide UI
            if (pauseMenuPanel != null)
                pauseMenuPanel.SetActive(false);

            // Hide overlay
            if (overlayPanel != null)
                overlayPanel.SetActive(false);

            // Only resume systems in play mode
            if (Application.isPlaying)
            {
                ResumeAllSystems();
            }

            // Trigger events
            OnPauseStateChanged?.Invoke(false);

            if (enableDebugMode)
                Debug.Log("MenUISystem: Pause menu hidden" + (Application.isPlaying ? " and systems resumed" : " (editor mode)"));
        }

        /// <summary>
        /// Toggle pause state
        /// </summary>
        public void TogglePause()
        {
            if (_isPaused)
                HidePause();
            else
                ShowPause();
        }

        /// <summary>
        /// Check if the game is currently paused
        /// </summary>
        public bool IsPaused => _isPaused;

        /// <summary>
        /// Show the settings panel
        /// </summary>
        public void ShowSettings()
        {
            if (enableDebugMode)
                Debug.Log("MenUISystem: ShowSettings() called");

            if (settingsPanelManager == null)
            {
                Debug.LogError("MenUISystem: SettingsPanelManager is null! Cannot show settings. Check if the reference is assigned in the inspector.");
                return;
            }

            if (enableDebugMode)
                Debug.Log($"MenUISystem: SettingsPanelManager found: {settingsPanelManager.name}");

            // Hide main pause menu
            if (pauseMenuPanel != null)
            {
                pauseMenuPanel.SetActive(false);
                if (enableDebugMode)
                    Debug.Log("MenUISystem: Pause menu panel hidden");
            }

            // Show settings panel
            settingsPanelManager.ShowSettings();

            if (enableDebugMode)
                Debug.Log("MenUISystem: Settings panel show command sent");
        }

        /// <summary>
        /// Hide the settings panel and return to main pause menu
        /// </summary>
        public void HideSettings()
        {
            if (settingsPanelManager == null)
                return;

            // Hide settings panel
            settingsPanelManager.HideSettings();

            // Show main pause menu if still paused
            if (_isPaused && pauseMenuPanel != null)
                pauseMenuPanel.SetActive(true);

            if (enableDebugMode)
                Debug.Log("MenUISystem: Settings panel hidden, returned to main pause menu");
        }

        #endregion

        #region System Pause/Resume

        private void PauseAllSystems()
        {
            if (enableDebugMode)
                Debug.Log("MenUISystem: Pausing all systems...");

            // 1. Store current Unity time scale and pause Unity systems
            _previousTimeScale = Time.timeScale;
            Time.timeScale = 0f;

            // 2. Pause audio if enabled
            if (pauseAudio)
            {
                AudioListener.pause = true;
                if (enableDebugMode)
                    Debug.Log("MenUISystem: AudioListener paused");
            }

            // 3. Pause Epoch time system via TimeManager
            if (TimeManager.Instance != null)
            {
                TimeManager.Instance.PauseTime();
                if (enableDebugMode)
                    Debug.Log("MenUISystem: Epoch time system paused via TimeManager");
            }
            else
            {
                Debug.LogError("MenUISystem: TimeManager.Instance not found! Epoch time system not paused.");
            }
        }

        private void ResumeAllSystems()
        {
            if (enableDebugMode)
                Debug.Log("MenUISystem: Resuming all systems...");

            // 1. Restore Unity time scale
            Time.timeScale = _previousTimeScale;

            // 2. Resume audio if it was paused
            if (pauseAudio)
            {
                AudioListener.pause = false;
                if (enableDebugMode)
                    Debug.Log("MenUISystem: AudioListener resumed");
            }

            // 3. Resume Epoch time system via TimeManager
            if (TimeManager.Instance != null)
            {
                TimeManager.Instance.ResumeTime();
                if (enableDebugMode)
                    Debug.Log("MenUISystem: Epoch time system resumed via TimeManager");
            }
            else
            {
                Debug.LogError("MenUISystem: TimeManager.Instance not found! Epoch time system not resumed.");
            }
        }

        #endregion

        #region Editor Controls

        /// <summary>
        /// Editor-only method to show pause menu for testing
        /// </summary>
        [ContextMenu("Show Pause Menu (Editor)")]
        public void EditorShowPause()
        {
            if (Application.isPlaying)
            {
                ShowPause();
            }
            else
            {
                // Editor mode - just show UI without system pause
                _isPaused = true;

                if (overlayPanel != null && enableOverlay)
                    overlayPanel.SetActive(true);

                if (pauseMenuPanel != null)
                    pauseMenuPanel.SetActive(true);

                if (enableDebugMode)
                    Debug.Log("MenUISystem: Pause menu shown in editor mode");
            }
        }

        /// <summary>
        /// Editor-only method to hide pause menu for testing
        /// </summary>
        [ContextMenu("Hide Pause Menu (Editor)")]
        public void EditorHidePause()
        {
            if (Application.isPlaying)
            {
                HidePause();
            }
            else
            {
                // Editor mode - just hide UI
                _isPaused = false;

                if (pauseMenuPanel != null)
                    pauseMenuPanel.SetActive(false);

                if (overlayPanel != null)
                    overlayPanel.SetActive(false);

                if (enableDebugMode)
                    Debug.Log("MenUISystem: Pause menu hidden in editor mode");
            }
        }

        /// <summary>
        /// Editor-only method to toggle pause menu for testing
        /// </summary>
        [ContextMenu("Toggle Pause Menu (Editor)")]
        public void EditorTogglePause()
        {
            if (_isPaused)
                EditorHidePause();
            else
                EditorShowPause();
        }

        /// <summary>
        /// Debug method to validate settings panel setup
        /// </summary>
        [ContextMenu("Debug Settings Panel Setup")]
        public void DebugSettingsPanelSetup()
        {
            Debug.Log("=== MenUI Settings Panel Debug ===");
            Debug.Log($"MenUISystem: {(this != null ? "Found" : "NULL")}");
            Debug.Log($"SettingsPanelManager: {(settingsPanelManager != null ? settingsPanelManager.name : "NULL")}");
            Debug.Log($"Pause Menu Panel: {(pauseMenuPanel != null ? pauseMenuPanel.name : "NULL")}");
            Debug.Log($"Settings Button: {(settingsButton != null ? settingsButton.name : "NULL")}");

            if (settingsPanelManager != null)
            {
                // Use reflection to check the settingsPanel field
                var settingsPanelField = typeof(SettingsPanelManager).GetField("settingsPanel",
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                if (settingsPanelField != null)
                {
                    var settingsPanel = settingsPanelField.GetValue(settingsPanelManager) as GameObject;
                    Debug.Log($"Settings Panel GameObject: {(settingsPanel != null ? settingsPanel.name : "NULL")}");
                    if (settingsPanel != null)
                    {
                        Debug.Log($"Settings Panel Active: {settingsPanel.activeInHierarchy}");
                        Debug.Log($"Settings Panel Parent: {(settingsPanel.transform.parent != null ? settingsPanel.transform.parent.name : "NULL")}");
                    }
                }
            }
            Debug.Log("=== End Debug ===");
        }

        /// <summary>
        /// Editor-only method to update overlay color in real-time
        /// </summary>
        [ContextMenu("Update Overlay Color")]
        public void EditorUpdateOverlayColor()
        {
            SetupOverlayPanel();
            if (enableDebugMode)
                Debug.Log($"MenUISystem: Overlay color updated to {overlayColor}");
        }

        /// <summary>
        /// Editor-only method to recreate overlay panel
        /// </summary>
        [ContextMenu("Recreate Overlay Panel")]
        public void EditorRecreateOverlay()
        {
            if (overlayPanel != null)
            {
                if (Application.isPlaying)
                    Destroy(overlayPanel);
                else
                    DestroyImmediate(overlayPanel);
            }

            overlayPanel = null;
            SetupOverlayPanel();

            if (enableDebugMode)
                Debug.Log("MenUISystem: Overlay panel recreated");
        }

        #endregion
    }
}
