%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e78934177bebe0545ab566bb787b0282, type: 3}
  m_Name: Readme
  m_EditorClassIdentifier: 
  Icon: {fileID: 2800000, guid: fe49abb410c53d54cb887b63b918efa8, type: 3}
  Title: Burn Effect
  Description:
    Heading: 
    Text: This sample creates a burnt surface through  a mix of charcoal and embers
      effect.
    LinkText: 
    Url: 
  PropertiesHeader:
    Heading: Properties
    Text: 
    LinkText: 
    Url: 
  Properties:
  - Heading: Albedo Mix
    Text: Controls the strength of albedo texture over the final effect.
    LinkText: 
    Url: 
  - Heading: Charcoal Mix
    Text: Controls the strength of the burnt effect over the albedo texture.
    LinkText: 
    Url: 
  - Heading: Ember Color Tint
    Text: Sets the embers colors.
    LinkText: 
    Url: 
  - Heading: Albedo texture to be used.
    Text: 
    LinkText: 
    Url: 
  - Heading: Normals
    Text: Normal texture to be used.
    LinkText: 
    Url: 
  - Heading: Base Ember
    Text: Sets the strength of the ember sections.
    LinkText: 
    Url: 
  - Heading: Glow Emission Multiplier
    Text: Overall emission strength multiplier.
    LinkText: 
    Url: 
  - Heading: Glow Color Intensity
    Text: Glow color strength multiplier.
    LinkText: 
    Url: 
  - Heading: Burn Offset
    Text: Initial offset used over the Masks texture which determines where to place
      embers.
    LinkText: 
    Url: 
  - Heading: Charcoal Normal Tile
    Text: Amount of tiling applied to Burnt Tile Normals texture.
    LinkText: 
    Url: 
  - Heading: Burn Tilling
    Text: Tiling used over the Masks texture which determines where to place embers.
    LinkText: 
    Url: 
  - Heading: Glow Base Frequency
    Text: Controls the frequency of ember pulsating effect.
    LinkText: 
    Url: 
  - Heading: Glow Override
    Text: Overall glow strength multiplier.
    LinkText: 
    Url: 
  - Heading: Masks
    Text: Mask texture to determine where to use charcoal and ember effects.
    LinkText: 
    Url: 
  - Heading: Burnt Tile Normals
    Text: Burnt tile normals texture to be used.
    LinkText: 
    Url: 
  - Heading: Smoothness
    Text: Overall smoothness strength.
    LinkText: 
    Url: 
  AdditionalProperties: []
  AdditionalScripts: []
  LoadedLayout: 0
  RPType: 3
