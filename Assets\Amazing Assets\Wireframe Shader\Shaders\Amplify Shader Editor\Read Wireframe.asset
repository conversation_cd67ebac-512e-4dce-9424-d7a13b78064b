%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: Read Wireframe
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor\n// Available at the Unity
    Asset Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=18921\n701.6727;90.76363;1618.036;752.2363;321.0181;293.0636;1;True;True\nNode;AmplifyShaderEditor.TexCoordVertexDataNode;2;55,-100.5;Inherit;False;3;3;0;5;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.FunctionInput;6;66.5,212.5;Inherit;False;Diameter;1;2;False;1;0;FLOAT;0.5;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;5;70,39.5;Inherit;False;Thickness;1;0;False;1;0;FLOAT;0.01;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;4;71,123.5;Inherit;False;Smoothness;1;1;False;1;0;FLOAT;0.2;False;1;FLOAT;0\nNode;AmplifyShaderEditor.CustomExpressionNode;3;360,-102.5;Inherit;False;return
    WireframeShaderReadTriangleMassFromUV(uv, thickness, smoothness, diameter)@;1;Create;4;True;uv;FLOAT3;0,0,0;In;;Inherit;False;True;thickness;FLOAT;0;In;;Inherit;False;True;smoothness;FLOAT;0;In;;Inherit;False;True;diameter;FLOAT;0;In;;Inherit;False;AAWireframeShaderReadTriangleMassFromUV;False;False;0;;False;4;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;0;772,-101;Inherit;False;True;-1;Wireframe;0;False;1;0;FLOAT;0;False;1;FLOAT;0\nWireConnection;3;0;2;0\nWireConnection;3;1;5;0\nWireConnection;3;2;4;0\nWireConnection;3;3;6;0\nWireConnection;0;0;3;0\nASEEND*/\n//CHKSM=B03B8FB31B149278C6CA4435101CA2618FDAEAE0"
  m_functionName: 
  m_description: 
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 1
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_customEdited: 0
    m_additionalDirectives:
    - {fileID: 0}
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems:
    - LineType: 0
      LineValue: 
      GUIDToggle: 1
      GUIDValue: 36072c560231e604cb8f0c03c7ff456d
      Origin: 2
  m_nodeCategory: 0
  m_customNodeCategory: Amazing Assets
  m_previewPosition: 0
  m_hidden: 1
  m_url: 
