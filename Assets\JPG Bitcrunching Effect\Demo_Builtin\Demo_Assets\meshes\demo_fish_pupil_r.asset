%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!43 &4300000
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: demo_fish_pupil_r
  serializedVersion: 10
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 240
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 84
    localAABB:
      m_Center: {x: 0.13652396, y: 0.21268936, z: -0.7825612}
      m_Extent: {x: 0.3014574, y: 0.3024394, z: 0.049409598}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose:
  - e00: 1
    e01: 0
    e02: -0
    e03: 0
    e10: 0
    e11: 1
    e12: 0
    e13: 0
    e20: 0
    e21: -0
    e22: 1
    e23: -0
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: 1
    e01: 0
    e02: -0
    e03: -0.011112675
    e10: -0
    e11: 1
    e12: 0
    e13: -0.0011642873
    e20: 0
    e21: 0
    e22: 1
    e23: 0
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: -1.0000002
    e01: 0.00002937313
    e02: 0
    e03: -0.64736646
    e10: 0
    e11: -0.00000011920929
    e12: 1.0000001
    e13: 4.1119193e-16
    e20: 0.00002937313
    e21: 1.0000001
    e22: -0.00000011920929
    e23: 0.00009876619
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: -1.0000002
    e01: -0.00014712971
    e02: 0
    e03: -1.354551
    e10: 0
    e11: -0.00000011920929
    e12: 1.0000001
    e13: 4.976892e-16
    e20: -0.00014712971
    e21: 1.0000001
    e22: -0.00000011920929
    e23: -0.00014031588
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: -1
    e01: -0.00048060168
    e02: 2.910383e-11
    e03: -2.1633656
    e10: -2.910383e-11
    e11: 0
    e12: 1
    e13: 3.9862525e-16
    e20: -0.00048060168
    e21: 0.99999994
    e22: -0.00000011920929
    e23: -0.00086173764
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: -0.9999015
    e01: -0.014035968
    e02: 0
    e03: -2.9928126
    e10: 0
    e11: 0
    e12: 1
    e13: 4.025491e-16
    e20: -0.014035968
    e21: 0.9999015
    e22: 0
    e23: -0.041434355
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: -0.9997916
    e01: 0.020430014
    e02: 0
    e03: -3.8419251
    e10: 0
    e11: -0.00000011920929
    e12: 1.0000001
    e13: 7.983489e-16
    e20: 0.020430014
    e21: 0.99979144
    e22: -0.00000011920929
    e23: 0.7152465
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: -0.9997916
    e01: 0.020430014
    e02: 0
    e03: -4.2017236
    e10: 0
    e11: -0.00000011920929
    e12: 1.0000001
    e13: 7.474209e-16
    e20: 0.020430014
    e21: 0.99979144
    e22: -0.00000011920929
    e23: 0.7152465
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: -0.99992675
    e01: -0.012107229
    e02: 0
    e03: -3.8915958
    e10: 0
    e11: 0
    e12: 1
    e13: 2.9879386e-16
    e20: -0.012107229
    e21: 0.9999267
    e22: 0
    e23: -0.033927903
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: -0.99992675
    e01: -0.012107229
    e02: 0
    e03: -4.279387
    e10: 0
    e11: 0
    e12: 1
    e13: 2.5510348e-16
    e20: -0.012107229
    e21: 0.9999267
    e22: 0
    e23: -0.033927903
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: -0.99921274
    e01: -0.039670877
    e02: 0
    e03: -3.8479002
    e10: 0
    e11: 0
    e12: 1
    e13: -1.8828514e-16
    e20: -0.039670877
    e21: 0.99921274
    e22: 0
    e23: -0.78897315
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: -0.99921274
    e01: -0.039670877
    e02: 0
    e03: -4.2241964
    e10: 0
    e11: 0
    e12: 1
    e13: -2.214354e-16
    e20: -0.039670877
    e21: 0.99921274
    e22: 0
    e23: -0.78897315
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: 0.00000017881393
    e01: -6.617445e-24
    e02: -0.99999994
    e03: -0.72993296
    e10: -1.2246469e-16
    e11: -1
    e12: -1.9852335e-23
    e13: 0.21268936
    e20: -0.99999994
    e21: 1.2246469e-16
    e22: -0.00000011920929
    e23: 0.13652386
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: 0.00000017881393
    e01: -6.617445e-24
    e02: -0.99999994
    e03: -0.78256124
    e10: -1.2246469e-16
    e11: -1
    e12: -1.9852335e-23
    e13: 0.21268936
    e20: -0.99999994
    e21: 1.2246469e-16
    e22: -0.00000011920929
    e23: 0.13652386
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: -0.00000011920929
    e01: -0
    e02: 1.0000001
    e03: -0.729933
    e10: 0
    e11: 1
    e12: 0
    e13: -0.212689
    e20: -1.0000001
    e21: 0
    e22: -0.00000011920929
    e23: 0.136524
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: 0.00000017881393
    e01: 8.881784e-16
    e02: -0.99999994
    e03: 0.782561
    e10: -0.000000021073422
    e11: 1
    e12: -2.6645353e-15
    e13: -0.212689
    e20: 0.99999994
    e21: 0.000000021073422
    e22: 0.00000017881393
    e23: -0.13652411
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB:
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: -0.049409688, y: -0.3024394, z: -0.3014574}
    m_Max: {x: 0.04940951, y: 0.3024394, z: 0.30145735}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 020001000000020000000300020003000400020004000500020005000600020006000700020007000800020008000900020009000a0002000a000b0002000b000c0002000c000d0002000d000e0002000e000f0002000f00100002001000110002001100120002001200130002001300140002001400010017001600150017001800160017001900180017001a00190017001b001a0017001c001b0017001d001c0017001e001d0017001f001e00170020001f001700210020001700220021001700230022001700240023001700250024001700260025001700270026001700280027001700290028001700150029002c002b002a002d002c002a002f002e002b002c002f002b00310030002e002f0031002e003300320030003100330030003500340032003300350032003700360034003500370034003900380036003700390036003b003a00380039003b0038003d003c003a003b003d003a003f003e003c003d003f003c00410040003e003f0041003e004300420040004100430040004500440042004300450042004700460044004500470044004900480046004700490046004b004a00480049004b0048004d004c004a004b004d004a004f004e004c004d004f004c00510050004e004f0051004e00530052005000510053005000
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 84
    m_Channels:
    - stream: 0
      offset: 0
      format: 0
      dimension: 3
    - stream: 0
      offset: 12
      format: 0
      dimension: 3
    - stream: 0
      offset: 24
      format: 0
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 1
      offset: 0
      format: 0
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 2
      offset: 0
      format: 0
      dimension: 4
    - stream: 2
      offset: 16
      format: 10
      dimension: 4
    m_DataSize: 6720
    _typelessdata: 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
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0.13652396, y: 0.21268936, z: -0.7825612}
    m_Extent: {x: 0.3014574, y: 0.3024394, z: 0.049409598}
  m_MeshUsageFlags: 0
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  m_MeshMetrics[0]: 1
  m_MeshMetrics[1]: 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
