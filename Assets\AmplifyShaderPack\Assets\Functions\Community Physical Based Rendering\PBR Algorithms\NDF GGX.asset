%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: NDF GGX
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor v1.9.5.1\n// Available at the
    Unity Asset Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=19501\nNode;AmplifyShaderEditor.CommentaryNode;385;-3200,-1408;Inherit;False;1872.905;605.3022;Math;21;272;473;472;145;395;468;469;142;467;393;290;394;85;89;90;84;86;390;466;566;567;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;474;-4319.73,-1408;Inherit;False;1056.953;385.5613;Dot
    Products;6;502;535;537;526;563;565;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;466;-3136,-1216;Inherit;False;195;144.6666;NdotH;1;380;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;391;-3200,-1760;Inherit;False;771.0603;313.4704;NDF
    GGX Node;1;294;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;294;-2848,-1696;Inherit;False;386.6556;146.9055;;;0,0,0,1;The
    specular highlight of the GGX Algorithm is very tight and hot, while still maintaining
    a smooth distribution across the surface -Jorden Stevens;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;390;-2912,-960;Inherit;False;246.5825;119.3165;Roughness;;0,0,0,1;;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;470;-3200,-1968;Inherit;False;618.1001;171.8;Credits;;0,0,0,0.5019608;Method
    by Jordan Stevens$Link: https://www.jordanstevenstechart.com/physically-based-rendering$$Node
    Made by Tiber Legacy$Special Thanks to Dawie;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;563;-4208,-1216;Inherit;False;194;100;;;0.02830189,0.01268245,0.01268245,1;Light
    Wrapping set to custom .0001;0;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;526;-3520,-1312;Inherit;True;NdotH;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;537;-3808,-1312;Inherit;False;Custom
    Dot Products;True;0;2;1;In 0;In 1;Object;-1;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;90;-2432,-1248;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;89;-2288,-1168;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleDivideOpNode;85;-2128,-1040;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;394;-2608,-1104;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.ReciprocalOpNode;467;-2224,-1344;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;468;-2048,-1344;Inherit;False;PI
    Inverse;False;0;2;-1;Exact;Aprroximate;Object;-1;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;395;-1968,-1040;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;145;-1792,-1344;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;472;-1824,-1264;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;473;-1824,-1040;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;469;-2288,-1264;Inherit;False;Constant;_ApproxPIInverse;Approx.
    PI Inverse;0;0;Create;True;0;0;0;False;0;False;0.3183099;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;290;-2832,-1040;Inherit;False;Roughness;1;2;False;1;0;FLOAT;0.5;False;1;FLOAT;0\nNode;AmplifyShaderEditor.PiNode;142;-2432,-1344;Inherit;False;1;0;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleDivideOpNode;84;-2592,-1248;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;86;-2752,-1248;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;393;-2896,-1168;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;380;-3120,-1168;Inherit;False;526;NdotH;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;535;-3984,-1216;Inherit;False;NdotH;1;1;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;502;-4272,-1312;Inherit;False;Normal;3;0;False;1;0;FLOAT3;0,0,1;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionNode;565;-4080,-1312;Inherit;False;NdotH;-1;;3;fadb6690f179c1448859861f76ebb061;7,27,1,373,2,451,0,463,0,460,0,461,0,452,0;6;230;FLOAT3;0,0,1;False;443;FLOAT3;0,0,0;False;462;FLOAT3;0,0,0;False;454;FLOAT3;0,0,0;False;453;FLOAT3;0,0,0;False;417;FLOAT;0.0001;False;1;FLOAT;109\nNode;AmplifyShaderEditor.StickyNoteNode;566;-2304,-944;Inherit;False;223;100;Max
    .0001;;0,0,0,0.5019608;prevents the Specular dot from \"disappearing\" at 'Low/Zero
    Roughness\";0;0\nNode;AmplifyShaderEditor.SimpleMaxOpNode;567;-2304,-1040;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0.0001;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;272;-1632,-1344;Inherit;True;True;-1;Out;0;True;1;0;FLOAT;0;False;1;FLOAT;0\nWireConnection;526;0;537;0\nWireConnection;537;0;565;109\nWireConnection;537;1;535;0\nWireConnection;90;0;84;0\nWireConnection;90;1;394;0\nWireConnection;89;0;393;0\nWireConnection;89;1;90;0\nWireConnection;85;0;567;0\nWireConnection;85;1;89;0\nWireConnection;394;0;290;0\nWireConnection;394;1;290;0\nWireConnection;467;0;142;0\nWireConnection;468;0;467;0\nWireConnection;468;1;469;0\nWireConnection;395;0;85;0\nWireConnection;395;1;85;0\nWireConnection;145;0;468;0\nWireConnection;145;1;472;0\nWireConnection;472;0;473;0\nWireConnection;473;0;395;0\nWireConnection;84;0;86;0\nWireConnection;84;1;393;0\nWireConnection;86;0;393;0\nWireConnection;393;0;380;0\nWireConnection;393;1;380;0\nWireConnection;565;230;502;0\nWireConnection;567;0;290;0\nWireConnection;272;0;145;0\nASEEND*/\n//CHKSM=A023AB7992A8765F7D9A79E0244EB13602F40D8D"
  m_functionName: 
  m_description: 'NDF GGX Node


    NdotH Light Wrapping set to custom .0001'
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 0
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_customEdited: 0
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 0
  m_headerStyle: 2
  m_headerColor: {r: 0.7529412, g: 0.6784314, b: 0, a: 1}
  m_customNodeCategory: Amplify Shader Pack
  m_previewPosition: 0
  m_hidden: 0
  m_url: 
