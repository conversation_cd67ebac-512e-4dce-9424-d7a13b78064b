<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<link rel="icon" href="../images/favicon.svg" />
		<link rel="icon" href="../images/favicon.ico" />
		<meta name="viewport" content="width=device-width, height=device-height, initial-scale=1">
		<meta property="og:type" content="website">
		<meta http-equiv="content-security-policy" content="">
		<link href="../_app/immutable/assets/_layout.21aea584.css" rel="stylesheet">
		<link href="../_app/immutable/assets/_layout.0154a58a.css" rel="stylesheet">
		
		
		
		
		
		
		
		
		
		
		
		
		<title>Interactable</title><!-- HEAD_svelte-i5aht6_START --><meta property="og:title" content="Interactable"><meta name="description" property="og:description" content="Flexalon lets Unity developers arrange objects in 3D layouts that are precise and easy to adjust. Use built-in interactions and animations to create powerful experiences."><meta name="image" property="og:image" content="../images/flexalon-1200x630.png"><meta name="twitter:card" content="summary_large_image"><meta name="twitter:title" content="Interactable"><meta name="twitter:site" content="@alon_farchy"><meta name="twitter:description" content="Flexalon lets Unity developers arrange objects in 3D layouts that are precise and easy to adjust. Use built-in interactions and animations to create powerful experiences."><meta name="twitter:image" content="../images/flexalon-1200x630.png"><!-- HEAD_svelte-i5aht6_END -->
	</head>
	<body>
		<div>


<div class="text-white font-body p-4 sticky top-0 bg-black flex gap-5 w-full text-gray-200 z-20 border-b-2 border-[#21a6f0]"><button class="lg:hidden"><svg width="24" height="24"><path d="M5 6h14M5 12h14M5 18h14" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"></path></svg></button>
    <a href="https://www.flexalon.com/" class="font-logo upper text-lg text-[#21a6f0] lg:pl-5 svelte-194nj6y">Flexalon Layouts</a></div>

<div class="w-full max-w-[1600px] lg:pl-10"><div class="flex w-full min-h-full text-zinc-400 relative"><div class="h-full fixed bg-black z-10 max-lg:hidden"><div class="flex flex-col w-[300px] gap-2 border-[#21a6f0] border-r-2 bg-black min-h-0 h-full pt-10 pb-36 overflow-auto max-lg:pl-10"><b class="text-zinc-200">Getting Started</b>
                <a href="../docs.html" class="svelte-194nj6y">Installation</a>
                <a href="../docs/coreConcepts.html" class="svelte-194nj6y">Core Concepts</a>
                <a href="../docs/ui.html" class="svelte-194nj6y">Flexalon UI <span class="ml-2 text-xs font-bold text-pink-300">(New in v4.0!)</span></a>
                <a href="../docs/templates.html" class="svelte-194nj6y">Template Pack</a>
                <a href="../docs/challenges.html" class="svelte-194nj6y">Challenges!</a>

                <a class="text-zinc-200 mt-5 font-bold svelte-194nj6y" href="../docs/flexalonObject.html">Flexalon Objects</a>
                <a href="../docs/flexalonObject.html#size" class="svelte-194nj6y">Width, Height, and Depth</a>
                <a href="../docs/flexalonObject.html#minmax" class="svelte-194nj6y">Min / Max Size<span class="ml-2 text-xs font-bold text-purple-400">(v4.1)</span></a>
                <a href="../docs/flexalonObject.html#margins" class="svelte-194nj6y">Margins and Padding</a>
                <a href="../docs/flexalonObject.html#offset" class="svelte-194nj6y">Offset, Scale, and Rotation</a>

                <b class="text-zinc-200 mt-5">Layouts</b>
                <a href="../docs/flexibleLayout.html" class="svelte-194nj6y">Flexible Layout</a>
                <a href="../docs/gridLayout.html" class="svelte-194nj6y">Grid Layout</a>
                <a href="../docs/circleLayout.html" class="svelte-194nj6y">Circle / Spiral Layout</a>
                <a href="../docs/curveLayout.html" class="svelte-194nj6y">Curve Layout</a>
                <a href="../docs/randomLayout.html" class="svelte-194nj6y">Random Layout</a>
                <a href="../docs/alignLayout.html" class="svelte-194nj6y">Align Layout</a>
                <a href="../docs/shapeLayout.html" class="svelte-194nj6y">Shape Layout</a>
                <a href="../docs/constraints.html" class="svelte-194nj6y">Constraints</a>
                <a href="../docs/randomModifier.html" class="svelte-194nj6y">Random Modifier</a>

                <a class="text-zinc-200 mt-5 font-bold svelte-194nj6y" href="../docs/animators.html">Animators</a>
                <a href="../docs/animators.html#curve" class="svelte-194nj6y">Curve Animator</a>
                <a href="../docs/animators.html#lerp" class="svelte-194nj6y">Lerp Animator</a>
                <a href="../docs/animators.html#rb" class="svelte-194nj6y">Rigid Body Animator</a>
                <a href="../docs/animators.html#layouts" class="svelte-194nj6y">Animating Layouts</a>

                <b class="text-zinc-200 mt-5">Click &amp; Drag</b>
                <a href="../docs/interactable.html" class="svelte-194nj6y">Interactable</a>
                <a href="../docs/interactable.html#dragTarget" class="svelte-194nj6y">Drag Target</a>
                <a href="../docs/xr.html" class="svelte-194nj6y">XR Interactions <span class="ml-2 text-xs font-bold text-blue-300">(v3.2)</span></a>
                <a href="../docs/interactable.html#customInputProvider" class="svelte-194nj6y">Custom Input Provider</a>

                <b class="text-zinc-200 mt-5">Data Binding</b>
                <a href="../docs/cloner.html" class="svelte-194nj6y">Cloner</a>
                <a href="../docs/cloner.html#databinding" class="svelte-194nj6y">Data Binding</a>

                <b class="text-zinc-200 mt-5">Advanced</b>
                <a href="../docs/pipeline.html" class="svelte-194nj6y">Flexalon Pipeline</a>
                <a href="../docs/customLayout.html" class="svelte-194nj6y">Custom Layouts</a>
                <a href="../docs/customAnimators.html" class="svelte-194nj6y">Custom Animators</a>
                <a href="../docs/adapters.html" class="svelte-194nj6y">Adapters</a>

                <a class="text-zinc-200 mt-5 font-bold svelte-194nj6y" href="../docs/api/Flexalon.html">Scripting API</a>
                <div class="mb-20"></div></div></div>
        
        <div class="flex flex-col w-full p-10 lg:ml-[300px]"><div class="doc svelte-194nj6y">

<div class="text-white text-2xl mb-10">Interactable</div>

<p>Flexalon supports built-in user interactions for clicking and dragging objects to insert, reorder,
    and remove them from layouts. Interaction works for world objects with a Collider component and UI objects with a Graphic component.
</p>

<p>Setting up an interaction is simple:
</p>
<ul class="list-disc ml-10 leading-8 text-white"><li>Add a <b>Flexalon Interactable</b> component to each object that you want to be clickable or draggable.</li>
    <li>Add a <b>Flexalon Drag Target</b> component to each layout that you to be able to add, remove, or reorder objects.</li>
    <li>Optionally, add a <b>Flexalon Lerp Animator</b> or <b>Flexalon Rigid Body Animator</b> components to make objects under the drag targets reorder smoothly.</li></ul>

<p>For <b>UI interactions</b>, also ensure:
</p>
<ul class="list-disc ml-10 leading-8 text-white"><li>There is an Event System component in the scene.</li>
    <li>There is a Graphics Raycaster component on the Canvas.</li>
    <li>Your interactable objects have a Graphic component with &quot;Raycast Target&quot; checked.</li></ul>

<div class="flex m-5 mb-10"><video muted autoplay loop playsinline><source src="https://www.flexalon.com/videos/doc-interactable.mp4"></video></div>

<p class="text-xl mt-10">Interactable Options</p>
<div class="flex ml-10 m-5 mb-10"><img src="../images/doc-interactable-options.png" alt="Interactable Options"></div>

<table><tr><td>Clickable</td>
        <td>Determines if this object can be clicked and generate click events.</td></tr>
    <tr><td>Max Click Time</td>
        <td>With a mouse or touch input, a click is defined as a press and release.
            The time between press and release must be less than Max Click Time to count as a click.
            A drag interaction cannot start until Max Click Time is exceeded.
        </td></tr>
    <tr><td>Draggable</td>
        <td>Determines if this object can be dragged and generate drag events.</td></tr>
    <tr><td>Interpolation Speed</td>
        <td>Determins how quickly the object moves towards the cursor when dragged.</td></tr>
    <tr><td>Insert Radius <span class="ml-2 text-xs font-bold text-blue-300">(v3.2)</span></td>
        <td>How close this object needs to a drag target&#39;s bounds to be inserted.</td></tr>
    <tr><td>Restriction</td>
        <td><p>Determines how to restrict the object&#39;s drag movement.</p>
            <p>Plane restriction ensures the object moves along a plane, defined by the objects initial position and the Plane Normal property.</p>
            <p>Line restriction ensures the object moves along a line, defined by the object&#39;s initial position and the Line Direction property.</p></td></tr>
    <tr><td>Plane Normal</td>
        <td>Defines the normal of the plane when using a plane restriction.
            If &#39;Local Space&#39; is checked, this normal is rotated by the transform of the layout that the object is in.
        </td></tr>
    <tr><td>Line Direction</td>
        <td>Defines the direction of the line when using a line restriction.
            If &#39;Local Space&#39; is checked, this direction is rotated by the transform of the layout that the object is in.
        </td></tr>
    <tr><td>Hold Offset</td>
        <td>When dragged, this option adds an offset to the dragged object&#39;s position. This can be used to float the object near the layout while it is being dragged.
            If &#39;Local Space&#39; is checked, this offset is rotated and scaled by the transform of the layout that the object is in.
        </td></tr>
    <tr><td>Rotate on Drag</td>
        <td>When dragged, this option adds a rotation to the dragged object. This can be used to tilt the object while it is being dragged.
            If &#39;Local Space&#39; is checked, this rotation will be in the local space of the layout that the object is in.
        </td></tr>
    <tr><td>Hide Cursor</td>
        <td>When checked, Cursor.visible is set to false when the object is dragged.</td></tr>
    <tr><td>Handle <span class="ml-2 text-xs font-bold text-pink-300">(v4.0)</span></td>
        <td>GameObject which should be hit tested for clicking / dragging instead of this object. For world interactions, the handle must have a collider component. For UI interactions, the handle must have a component which has &quot;Raycast Target&quot; checked.</td></tr>
    <tr><td>Bounds</td>
        <td>If set, the object cannot be dragged outside of the bounds collider.</td></tr>
    <tr><td>Layer Mask</td>
        <td>When dragged, limits which Flexalon Drag Targets will accept this object by comparing the Layer Mask to the target GameObject&#39;s layer.</td></tr>
    <tr><td>Clicked (Event)</td>
        <td>Unity Event invoked when the object is pressed and released within MaxClickTime.</td></tr>
    <tr><td>Hover Start (Event)</td>
        <td>Unity Event invoked when the object starts being hovered.</td></tr>
    <tr><td>Hover End (Event)</td>
        <td>Unity Event invoked when the object stops being hovered.</td></tr>
    <tr><td>Select Start (Event)</td>
        <td>Unity Event invoked when the object starts being selected (e.g. press down mouse over object).</td></tr>
    <tr><td>Select End (Event)</td>
        <td>Unity Event invoked when the object stops being selected (e.g. release mouse).</td></tr>
    <tr><td>Drag Start (Event)</td>
        <td>Unity Event invoked when the object starts being dragged.</td></tr>
    <tr><td>Drag End (Event)</td>
        <td>Unity Event invoked when the object stops being dragged.</td></tr></table>

<p class="text-xl mt-10">Event State Machine</p>
<p>This diagram explains how the interactable state machine works, and which events (in blue) will trigger at different transitions.</p>
<img src="../images/doc-interactable-state-machine.png" alt="Interactable State Machine">

<p id="dragTarget" class="text-xl mt-10">Drag Target Options</p>
<div class="flex ml-10 m-5 mb-10"><img src="../images/doc-drag-target-options.png" alt="Drag Target Options"></div>

<table><tr><td class="w-[200px]">Can Remove Objects</td>
        <td>Determines if objects can be dragged out of this target.</td></tr>
    <tr><td>Can Add Objects</td>
        <td>Determines if new objects can be added to this target.</td></tr>
    <tr><td>Min Objects</td>
        <td>The minimum number of objects that should be in this target. Once the minimum is reached, objects cannot be dragged out.</td></tr>
    <tr><td>Max Objects</td>
        <td>The maximum number of objects that this target can hold. Once the maximum is reached, new objects cannot be dragged in. If Max Objects is set to 0, then it is unlimited.</td></tr>
    <tr><td>Margin <span class="m-2 text-xs font-bold text-pink-400">(v3.0)</span></td>
        <td>The bounds of the drag target is determined by the layout size. The margin property increases or decreases the size of the bounds.</td></tr></table>

<p id="customInputProvider" class="text-xl mt-10">Custom Input Provider</p>
<div class="flex ml-10 m-5 mb-10"><img src="../images/doc-singleton.png" alt="Flexalon Options"></div>

<p>By default, Flexalon creates a FlexalonMouseInputProvider which uses the Unity legacy input system to handle mouse and touch interactions.
    If your game uses a different input system, then you can still use Flexalon Interactable by implementing the InputProvider interface.
    You can assign your input provider by:
</p>
<ul class="list-disc ml-10 leading-8 text-white"><li>Assign Flexalon.GetOrCreate().InputProvider at runtime.</li>
    <li>Implement your InputProvider as a MonoBehaviour and assign it to the &#39;Input Provider&#39; field on the Flexalon component.</li></ul>

<p class="text-xl mt-10">InputProvider Interface</p>
<table><tr><td>InputMode <span class="ml-2 text-xs font-bold text-blue-300">(v3.2)</span></td>
        <td><p>Determines if Flexalon Interactable should pick and move the object with the provided ray, or simply listen for positional changes on the ExternalFocusedObject.</p>
            <ul class="list-disc ml-10 leading-8 text-white"><li><b>Raycast: </b>A ray is provided to determine which object is hovered and how it should be moved.</li>
                <li><b>External: </b>Objects are moved by an external system. Only state changes are provided.</li></ul></td></tr>
    <tr><td>Active</td>
        <td>True if the input is active, e.g. button is being held down.</td></tr>
    <tr><td>Ray</td>
        <td>In Rayast Mode, the ray to cast to determine what should be moved / hit.</td></tr>
    <tr><td class="w-[250px]">ExternalFocusedObject <span class="ml-2 text-xs font-bold text-blue-300">(v3.2)</span></td>
        <td>In External Mode, the object that is currently being hovered or selected.</td></tr></table></div></div></div>
</div>


			
			
		</div>
	</body>
</html>
