using UnityEngine;
using Stylo.Epoch;

namespace Stylo.Epoch
{
    /// <summary>
    /// Epoch-Cadance Integration Handler for BTR project.
    /// Provides seamless synchronization between Cadance beat detection and Epoch time manipulation.
    /// Replaces the old ChronosKoreographyHandler with modern Epoch + Cadance integration.
    /// </summary>
    public class EpochCadanceHandler : MonoBehaviour
    {
        [Header("Epoch Integration Configuration")]
        [SerializeField] private string epochClockKey = "Global"; // Match your time control clock key
        [SerializeField] private bool enableDebugLogging = false;

        private EpochGlobalClock epochClock;
        private Stylo.Cadance.Cadance cadance;
        private float initialTimeScale = 1f;
        private float lastTimeScale = 1f;
        private float lastEventDelay = 0f;

        void Start()
        {
            InitializeCadance();
            InitializeEpochClock();

            if (epochClock != null && cadance != null)
            {
                initialTimeScale = epochClock.LocalTimeScale;
                lastTimeScale = initialTimeScale;
                lastEventDelay = cadance.EventDelayInSeconds;

                if (enableDebugLogging)
                {
                    Debug.Log($"[EpochCadanceHandler] Epoch-Cadance integration initialized with clock '{epochClockKey}' (initial scale: {initialTimeScale})");
                }
            }
        }

        void Update()
        {
            if (epochClock == null || cadance == null) return;

            float currentTimeScale = epochClock.LocalTimeScale;
            if (Mathf.Abs(currentTimeScale - lastTimeScale) > 0.001f) // Use small threshold to avoid floating point precision issues
            {
                UpdateCadanceTiming(currentTimeScale);
                lastTimeScale = currentTimeScale;
            }
        }

        /// <summary>
        /// Initialize Cadance component with error handling.
        /// </summary>
        private void InitializeCadance()
        {
            cadance = GetComponent<Stylo.Cadance.Cadance>();
            if (cadance == null)
            {
                Debug.LogError($"[EpochCadanceHandler] Cadance component not found on GameObject '{gameObject.name}'. Please add a Cadance component.");
            }
        }

        /// <summary>
        /// Initialize Epoch clock using EpochTimekeeper lookup.
        /// </summary>
        private void InitializeEpochClock()
        {
            if (!EpochTimekeeper.HasInstance)
            {
                Debug.LogError($"[EpochCadanceHandler] EpochTimekeeper not found in scene. Please add an EpochTimekeeper component.");
                return;
            }

            epochClock = EpochTimekeeper.Instance.GetGlobalClockComponent(epochClockKey);
            if (epochClock == null)
            {
                Debug.LogError($"[EpochCadanceHandler] EpochGlobalClock with key '{epochClockKey}' not found. Please check your clock configuration.");
            }
        }

        /// <summary>
        /// Update Cadance timing based on Epoch time scale changes.
        /// </summary>
        private void UpdateCadanceTiming(float currentTimeScale)
        {
            try
            {
                // Calculate the scale factor relative to initial time scale
                float scaleFactor = currentTimeScale / initialTimeScale;

                // Adjust event delay to maintain beat synchronization
                // When time slows down, events need more delay to stay in sync
                float newEventDelay = lastEventDelay / scaleFactor;
                cadance.EventDelayInSeconds = newEventDelay;

                if (enableDebugLogging)
                {
                    Debug.Log($"[EpochCadanceHandler] Updated Cadance timing - Scale: {currentTimeScale:F2}x, Event Delay: {newEventDelay:F3}s");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[EpochCadanceHandler] Failed to update Cadance timing: {e.Message}");
            }
        }

        /// <summary>
        /// Validate the integration setup for debugging.
        /// </summary>
        [ContextMenu("Validate Epoch-Cadance Integration")]
        public void ValidateIntegration()
        {
            Debug.Log($"[EpochCadanceHandler] Integration Validation:");
            Debug.Log($"  - Cadance: {(cadance != null ? "✓ Found" : "✗ Missing")}");
            Debug.Log($"  - EpochTimekeeper: {(EpochTimekeeper.HasInstance ? "✓ Available" : "✗ Missing")}");
            Debug.Log($"  - EpochClock '{epochClockKey}': {(epochClock != null ? "✓ Found" : "✗ Missing")}");

            if (epochClock != null)
            {
                Debug.Log($"  - Current Time Scale: {epochClock.LocalTimeScale:F2}x");
                Debug.Log($"  - Current Event Delay: {(cadance != null ? cadance.EventDelayInSeconds.ToString("F3") : "N/A")}s");
            }
        }

        /// <summary>
        /// Reset timing to initial values.
        /// </summary>
        [ContextMenu("Reset Timing")]
        public void ResetTiming()
        {
            if (cadance != null)
            {
                cadance.EventDelayInSeconds = 0f;
                lastEventDelay = 0f;
                Debug.Log("[EpochCadanceHandler] Timing reset to defaults");
            }
        }

        /// <summary>
        /// Force update timing based on current time scale.
        /// </summary>
        [ContextMenu("Force Update Timing")]
        public void ForceUpdateTiming()
        {
            if (epochClock != null)
            {
                UpdateCadanceTiming(epochClock.LocalTimeScale);
                Debug.Log("[EpochCadanceHandler] Timing forcefully updated");
            }
        }

        void OnValidate()
        {
            // Ensure epochClockKey is not empty
            if (string.IsNullOrEmpty(epochClockKey))
            {
                epochClockKey = "Global";
            }
        }
    }
}
