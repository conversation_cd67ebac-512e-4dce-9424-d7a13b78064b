%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: MAT_TestObj2SSS
  m_Shader: {fileID: 4800000, guid: 0dde7f0097aeb2541aea684ca0ad0e3c, type: 3}
  m_ValidKeywords:
  - _ALBEDOVERTEXCOLORMODE_REPLACE
  - _ALPHA_CUTOFF_ON
  - _CAST_SHADOWS_ON
  - _COLORRAMPLIGHTINGSTAGE_BEFORELIGHTING
  - _FADE_ON
  - _GREYSCALESTAGE_BEFORELIGHTING
  - _HEIGHTGRADIENTPOSITIONSPACE_LOCAL
  - _HUE_SHIFT_ON
  - _LIGHTMODEL_FAKEGI
  - _MATCAPBLENDMODE_MULTIPLY
  - _MATCAP_ON
  - _OUTLINETYPE_NONE
  - _RECEIVE_SHADOWS_ON
  - _REFLECTIONS_CLASSIC
  - _RIMLIGHTINGSTAGE_BEFORELIGHTING
  - _RIM_LIGHTING_ON
  - _SHADINGMODEL_PBR
  - _SPECULARMODEL_CLASSIC
  - _SUBSURFACE_SCATTERING_ON
  - _TEXTUREBLENDINGMODE_RGB
  - _TEXTUREBLENDINGSOURCE_VERTEXCOLOR
  - _TRIPLANARNORMALSPACE_WORLD
  - _USE_WIND_VERTICAL_MASK
  m_InvalidKeywords:
  - _GLITCHWORLDSPACE_ON
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: 2000
  stringTagMap: {}
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _AOMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BlendingNormalMapB:
        m_Texture: {fileID: 2800000, guid: 6246e3fe4a63c1f4f9acec9623576fc7, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BlendingNormalMapG:
        m_Texture: {fileID: 2800000, guid: 8175cba73bf87af41aeba7bbd2f3c4cd, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BlendingNormalMapWhite:
        m_Texture: {fileID: 2800000, guid: 8175cba73bf87af41aeba7bbd2f3c4cd, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BlendingTextureB:
        m_Texture: {fileID: 2800000, guid: f39690d17007ab242928cd91e5a530a6, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BlendingTextureG:
        m_Texture: {fileID: 2800000, guid: 2af6ba1567607124eb61349fa044375b, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BlendingTextureWhite:
        m_Texture: {fileID: 2800000, guid: 2af6ba1567607124eb61349fa044375b, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ColorRampTex:
        m_Texture: {fileID: 2800000, guid: 99b6592afee2790429156fef1dc9ba44, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DistortTex:
        m_Texture: {fileID: 2800000, guid: fcb69fdadf254834880fe3c443965278, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _FadeTex:
        m_Texture: {fileID: 2800000, guid: fcb69fdadf254834880fe3c443965278, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _GlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MatcapTex:
        m_Texture: {fileID: 2800000, guid: 15843af64ad2270478da0b07bbfb3186, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalMap:
        m_Texture: {fileID: 2800000, guid: 120df76dc2d17ed4cb2d4b8b08292ed9, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SSSMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecularMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TexBlendingMask:
        m_Texture: {fileID: 2800000, guid: 5a775794f8704ba4385b9c92cf9f1224, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ToonRamp:
        m_Texture: {fileID: 2800000, guid: 339398f75cfa2714d93579662d08ec9a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TripalanarTopNormalMap:
        m_Texture: {fileID: 2800000, guid: 120df76dc2d17ed4cb2d4b8b08292ed9, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TripalanarTopTex:
        m_Texture: {fileID: 2800000, guid: a26b9f1a12d33184ab5e89d304e441ad, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TriplanarTopNormalMap:
        m_Texture: {fileID: 2800000, guid: 8175cba73bf87af41aeba7bbd2f3c4cd, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TriplanarTopTex:
        m_Texture: {fileID: 2800000, guid: 2af6ba1567607124eb61349fa044375b, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _VertexDistortionNoiseTex:
        m_Texture: {fileID: 2800000, guid: fcb69fdadf254834880fe3c443965278, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AOContrast: 1
    - _AOMapEnabled: 0
    - _AOMapStrength: 1
    - _AdvancedConfigurationEnabled: 0
    - _AffectedByLightmaps: 0
    - _AlbedoVertexColorMode: 1
    - _AlbedoVertexColorOn: 0
    - _AlphaCutoffOn: 1
    - _AlphaCutoffValue: 0.25
    - _AlphaRoundOn: 0
    - _AnisoShininess: 0.85
    - _Anisotropy: 0.45
    - _BlendDst: 0
    - _BlendSrc: 1
    - _BlendingMaskCutoffB: 0.1
    - _BlendingMaskCutoffG: 0.1
    - _BlendingMaskCutoffWhite: 0.15
    - _BlendingMaskSmoothnessB: 0.4
    - _BlendingMaskSmoothnessG: 0.4
    - _BlendingMaskSmoothnessWhite: 0.4
    - _Brightness: 0
    - _BrightnessLM: 0
    - _BumpScale: 1
    - _CastShadowsOn: 1
    - _ColorMask: 15
    - _ColorRampBlend: 1
    - _ColorRampLightingStage: 0
    - _ColorRampLuminosity: 0
    - _ColorRampOn: 0
    - _ColorRampScrollSpeed: 0
    - _ColorRampTiling: 1
    - _Contrast: 1
    - _ContrastBrightnessOn: 0
    - _ContrastLM: 1
    - _CullingMode: 2
    - _CustomAmbientLightOn: 0
    - _CustomShadowColorOn: 0
    - _Cutoff: 0.5
    - _DepthColoringOn: 0
    - _DepthGlowColorIntensity: 25
    - _DepthGlowDist: 0.2
    - _DepthGlowGlobalIntensity: 2
    - _DepthGlowPower: 25
    - _DetailNormalMapScale: 1
    - _DistortAmount: 0.3
    - _DistortTexXSpeed: 2
    - _DistortTexYSpeed: 2
    - _DitherOn: 0
    - _DitherScale: 1
    - _DstBlend: 0
    - _EmissionEnabled: 0
    - _EmissionSelfGlow: 1
    - _FaceDownCutoff: 0.25
    - _FadeAmount: 0
    - _FadeBurnOn: 0
    - _FadeBurnWidth: 0.015
    - _FadeByCamDistanceOn: 0
    - _FadeOn: 1
    - _FadePower: 1
    - _FadeTransition: 0.4
    - _FogOn: 0
    - _GeneralAlpha: 1
    - _Glitch: 0
    - _GlitchAmount: 0.5
    - _GlitchSpeed: 2.5
    - _GlitchTiling: 5
    - _GlitchWorldSpace: 1
    - _GlossMapOn: 0
    - _GlossMapScale: 1
    - _Glossiness: 0.5
    - _GlossyReflections: 1
    - _GreyScaleStage: 0
    - _Greyscale: 0
    - _GreyscaleBlend: 1
    - _GreyscaleLuminosity: 0
    - _HalfLambertWrap: 1
    - _HandDrawn: 0
    - _HandDrawnAmount: 10
    - _HandDrawnSpeed: 5
    - _HardnessFakeGI: 0.9
    - _HeightGradientOn: 0
    - _HeightGradientPositionSpace: 0
    - _HighlightCutoff: 0.5
    - _HighlightSmoothness: 0.5
    - _Highlights: 0
    - _HighlightsStrength: 1
    - _Hightlights: 0
    - _HightlightsStrength: 1
    - _Hit: 0
    - _HitBlend: 1
    - _HitGlow: 5
    - _Hologram: 0
    - _HologramAccentAlpha: 0.5
    - _HologramAccentFrequency: 2
    - _HologramAccentSpeed: 1
    - _HologramAlpha: 1
    - _HologramBaseAlpha: 0.1
    - _HologramFrequency: 20
    - _HologramLineCenter: 0.5
    - _HologramLineSmoothness: 2
    - _HologramLineSpacing: 2
    - _HologramScrollSpeed: 2
    - _HueBrightness: 1.744
    - _HueBrightnessLM: 1
    - _HueSaturation: 2.33
    - _HueSaturationLM: 1
    - _HueShift: 360
    - _HueShiftEnabled: 1
    - _HueShiftLM: 0
    - _InflateBlend: 1
    - _IntersectionFadeFactor: 1
    - _IntersectionFadeOn: 0
    - _IntersectionGlowOn: 0
    - _LightModel: 5
    - _LightmapColorCorrection: 0
    - _Matcap: 1
    - _MatcapBlend: 1
    - _MatcapBlendMode: 0
    - _MatcapIntensity: 2.05
    - _MaxCameraDistance: 1000
    - _MaxDistanceToFade: 100
    - _MaxFadeDistance: 250
    - _MaxGradientHeight: 0.75
    - _MaxInflate: 0.2
    - _MaxRim: 1
    - _Metallic: 0.387
    - _MetallicMapOn: 0
    - _MinDistanceToFade: 0
    - _MinGradientHeight: 0
    - _MinInflate: 0
    - _MinRim: 0
    - _Mode: 0
    - _NearFade: 0
    - _NormalInfluence: 1.19
    - _NormalMapEnabled: 0
    - _NormalStrength: 1
    - _OcclusionStrength: 1
    - _OutlineMode: 8
    - _OutlineThickness: 1
    - _OutlineType: 0
    - _Parallax: 0.02
    - _Pixelate: 0
    - _PixelateSize: 32
    - _Posterize: 0
    - _PosterizeGamma: 0.75
    - _PosterizeNumColors: 8
    - _RecalculateNormals: 0
    - _ReceiveShadowsOn: 1
    - _Reflections: 1
    - _ReflectionsAtten: 0.25
    - _RenderPreset: 1
    - _RimAttenuation: 0.3
    - _RimLighting: 1
    - _RimLightingStage: 0
    - _SSSAtten: 1
    - _SSSFrontAtten: 0.3
    - _SSSFrontPower: 3
    - _SSSPower: 1
    - _ScaleWithCameraDistance: 0
    - _ScreenSpaceUVOn: 0
    - _ScrollTextureOn: 0
    - _ScrollTextureX: 1
    - _ScrollTextureY: 1
    - _ShadingModel: 1
    - _ShakeBlend: 1
    - _ShakeSpeedMult: 1
    - _Shininess: 16
    - _Smoothness: 0.601
    - _SmoothnessTextureChannel: 0
    - _SpecularAtten: 1
    - _SpecularHighlights: 1
    - _SpecularModel: 1
    - _SpecularToonCutoff: 0.35
    - _SpecularToonSmoothness: 0
    - _SpherizeNormals: 0
    - _SrcBlend: 1
    - _StencilRef: 1
    - _StochasticSampling: 0
    - _StochasticScale: 3.464
    - _StochasticSkew: 0.57735026
    - _SubsurfaceScattering: 1
    - _TextureBlending: 0
    - _TextureBlendingMode: 0
    - _TextureBlendingSource: 0
    - _TimingSeed: 0
    - _ToonCutoff: 0.5
    - _ToonFactor: 0
    - _ToonSmoothness: 0.5
    - _TopNormalStrength: 1
    - _TriplanarMappingOn: 0
    - _TriplanarNormalSpace: 1
    - _TriplanarSharpness: 15
    - _UVDistortion: 0
    - _UVSec: 0
    - _UseCustomTime: 0
    - _UseVerticalMask: 1
    - _VertexColorBlending: 1
    - _VertexDistortionAmount: 0
    - _VertexDistortionOn: 0
    - _VertexInflate: 0
    - _VertexShakeOn: 0
    - _VoxelBlend: 1
    - _VoxelSize: 100
    - _Voxelize: 0
    - _WaveAmount: 7
    - _WaveSpeed: 10
    - _WaveStrength: 7.5
    - _WaveUVOn: 0
    - _WaveX: 0
    - _WaveY: 0.5
    - _WindAttenuation: 1
    - _WindOn: 0
    - _WindVerticalMaskMaxY: 1
    - _WindVerticalMaskMinY: -1
    - _ZTestMode: 4
    - _ZWrite: 1
    m_Colors:
    - _AOColor: {r: 0, g: 0, b: 0, a: 0}
    - _Color: {r: 1, g: 0.50980395, b: 0.5223836, a: 1}
    - _CustomAmbientColor: {r: 0.65, g: 0.65, b: 0.65, a: 1}
    - _DepthGlowColor: {r: 1, g: 0.9869999, b: 0.6, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _FadeBurnColor: {r: 191.74902, g: 11.478628, b: 0, a: 1}
    - _GlitchOffset: {r: -0.5, g: 0, b: 0, a: 0}
    - _GradientHeightColor01: {r: 0.2, g: 0.2, b: 0.2, a: 1}
    - _GradientHeightColor02: {r: 1, g: 1, b: 1, a: 1}
    - _GreyscaleTintColor: {r: 1, g: 1, b: 1, a: 1}
    - _HighlightOffset: {r: 0, g: 0, b: 0, a: 0}
    - _HighlightsColor: {r: 2, g: 2, b: 2, a: 1}
    - _HitColor: {r: 1, g: 1, b: 1, a: 1}
    - _HologramColor: {r: 0.35, g: 0.8, b: 2, a: 1}
    - _HologramLineDirection: {r: 0, g: 1, b: 0, a: 0}
    - _OutlineColor: {r: 1, g: 1, b: 1, a: 1}
    - _RimColor: {r: 1, g: 1, b: 1, a: 1}
    - _RimOffset: {r: 0, g: 0, b: 0, a: 0}
    - _SSSColor: {r: 4.3376827, g: 4.3376827, b: 4.3376827, a: 1}
    - _ShakeMaxDisplacement: {r: 0.1, g: 0.1, b: 0.1, a: 0}
    - _ShakeSpeed: {r: 41, g: 49, b: 45, a: 0}
    - _VertexDistortionNoiseSpeed: {r: 4, g: 4, b: 0, a: 0}
  m_BuildTextureStacks: []
