﻿
////// What does this Lighting settings do? ////// 
If you care about good lighting in unity this tool is for you
In BIRP by default Unity sets gamma correction to light color incorrectly. 
to set it correctly its only possible to do thru script    


////// Can I use this in my assets and projects? ////// 
Yes, however we ask that you change the namespace so you do not conflict with any others.


Use Linear Intensity:
When this is enabled, sets Unity lighting lightsUseLinearIntensity = True
https://docs.unity3d.com/ScriptReference/Rendering.GraphicsSettings-lightsUseLinearIntensity.html


Use Color Temperature:
When this is enabled, sets Unity lighting to use temperature mode for the color of light
Checking this will add a temperature field and change color to filter
https://docs.unity3d.com/ScriptReference/Rendering.GraphicsSettings-lightsUseColorTemperature.html


Note: 
You can change Unity lighting to is original default at any time by unchecking if you wish.

Redistribution and Modification: 
You are welcome to modify and use in anyway you wish, please remember to change your namespace. 


Community Contribution: 
This is a free asset and welcomes community contributions to help continually grow and improve 


Contributors:
We like to say thank you to the following contributors

*************************
Name: DE Environment
Discord:https://discord.gg/W7VThzw
YouTube:     
Twitter: https://twitter.com/DeEnvironment 
Twitch: 
Unity: https://assetstore.unity.com/publishers/50363
*************************

*************************
Name: The MessyCoder
Discord: https://bit.ly/messydiscord
YouTube: https://www.youtube.com/themessycoder   
Twitter: https://twitter.com/themessycoder
Twitch: https://www.twitch.tv/themessycoder
Unity: https://assetstore.unity.com/publishers/81261
*************************