%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 10
  m_Fog: 1
  m_FogColor: {r: 0.14117648, g: 0.15294118, b: 0.16078432, a: 1}
  m_FogMode: 1
  m_FogDensity: 0.09
  m_LinearFogStart: 5
  m_LinearFogEnd: 25
  m_AmbientSkyColor: {r: 0.14751694, g: 0.15180375, b: 0.16037738, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 3
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 10304, guid: 0000000000000000f000000000000000, type: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &3
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 13
  m_BakeOnSceneLoad: 0
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 1
    m_EnableRealtimeLightmaps: 0
  m_LightmapEditorSettings:
    serializedVersion: 12
    m_Resolution: 2
    m_BakeResolution: 40
    m_AtlasSize: 1024
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_ExtractAmbientOcclusion: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_ReflectionCompression: 2
    m_MixedBakeMode: 2
    m_BakeBackend: 1
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 512
    m_PVRBounces: 2
    m_PVREnvironmentSampleCount: 512
    m_PVREnvironmentReferencePointCount: 2048
    m_PVRFilteringMode: 2
    m_PVRDenoiserTypeDirect: 0
    m_PVRDenoiserTypeIndirect: 0
    m_PVRDenoiserTypeAO: 0
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVREnvironmentMIS: 0
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ExportTrainingData: 0
    m_TrainingDataDestination: TrainingData
    m_LightProbeSampleCountMultiplier: 4
  m_LightingDataAsset: {fileID: 0}
  m_LightingSettings: {fileID: 4890085278179872738, guid: 8be7b9f07ebc60443aba89dfb25f8603, type: 2}
--- !u!196 &4
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 3
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    buildHeightMesh: 0
    maxJobWorkers: 0
    preserveTilesOutsideBounds: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!1 &69809304
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 69809309}
  - component: {fileID: 69809308}
  - component: {fileID: 69809307}
  - component: {fileID: 69809310}
  - component: {fileID: 69809311}
  m_Layer: 0
  m_Name: Main Camera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!81 &69809307
AudioListener:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 69809304}
  m_Enabled: 1
--- !u!20 &69809308
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 69809304}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 2
  m_BackGroundColor: {r: 0.13843897, g: 0.15185618, b: 0.16037738, a: 0}
  m_projectionMatrixMode: 1
  m_GateFitMode: 2
  m_FOVAxisMode: 0
  m_Iso: 200
  m_ShutterSpeed: 0.005
  m_Aperture: 16
  m_FocusDistance: 10
  m_FocalLength: 50
  m_BladeCount: 5
  m_Curvature: {x: 2, y: 11}
  m_BarrelClipping: 0.25
  m_Anamorphism: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.3
  far clip plane: 1000
  field of view: 60
  orthographic: 0
  orthographic size: 5
  m_Depth: -1
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 1
  m_AllowMSAA: 1
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 0
  m_OcclusionCulling: 1
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!224 &69809309
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 69809304}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 1}
  m_SizeDelta: {x: 100, y: 100}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &69809310
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 69809304}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ba607cc0cf19c224ea1e74f038ef3184, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  ToFollow: {fileID: 0}
  FollowingOffset: {x: 0, y: 1.5, z: 0}
  FollowingOffsetDirection: {x: 0, y: 0, z: 0}
  DistanceRanges: {x: 5, y: 10}
  RotationRanges: {x: -60, y: 60}
  RotationSensitivity: 10
  RotationSpeed: 1
  HardFollowValue: 0.7
  LockCursor: 0
  SightLayerMask:
    serializedVersion: 2
    m_Bits: 0
  CollisionOffset: 1
  UpdateClock: 1
--- !u!135 &69809311
SphereCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 69809304}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 0
  serializedVersion: 3
  m_Radius: 2.37
  m_Center: {x: 0, y: 0, z: 0}
--- !u!1 &96328323
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 96328325}
  - component: {fileID: 96328324}
  m_Layer: 0
  m_Name: Reflection Probe
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!215 &96328324
ReflectionProbe:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 96328323}
  m_Enabled: 1
  serializedVersion: 2
  m_Type: 0
  m_Mode: 1
  m_RefreshMode: 0
  m_TimeSlicingMode: 0
  m_Resolution: 256
  m_UpdateFrequency: 0
  m_BoxSize: {x: 50, y: 50, z: 50}
  m_BoxOffset: {x: 0, y: 0, z: 0}
  m_NearClip: 0.3
  m_FarClip: 1000
  m_ShadowDistance: 100
  m_ClearFlags: 2
  m_BackGroundColor: {r: 0.22054109, g: 0.24626179, b: 0.26415092, a: 0}
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_IntensityMultiplier: 1
  m_BlendDistance: 1
  m_HDR: 1
  m_BoxProjection: 0
  m_RenderDynamicObjects: 0
  m_UseOcclusionCulling: 1
  m_Importance: 1
  m_CustomBakedTexture: {fileID: 0}
--- !u!4 &96328325
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 96328323}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0.5, y: 3, z: 3.6}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &108715037
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 108715041}
  - component: {fileID: 108715040}
  - component: {fileID: 108715039}
  - component: {fileID: 108715038}
  m_Layer: 0
  m_Name: Floor
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!65 &108715038
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 108715037}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &108715039
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 108715037}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &108715040
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 108715037}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &108715041
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 108715037}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: -3, z: 12.03}
  m_LocalScale: {x: 50, y: 1, z: 50}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &177048327
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 177048331}
  - component: {fileID: 177048330}
  - component: {fileID: 177048329}
  - component: {fileID: 177048328}
  m_Layer: 0
  m_Name: Cube (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!65 &177048328
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 177048327}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &177048329
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 177048327}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &177048330
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 177048327}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &177048331
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 177048327}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 8.3, y: -28.97389, z: 11.3}
  m_LocalScale: {x: 2.3004, y: 15, z: 2.3004}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1945571810}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &*********
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: *********}
  - component: {fileID: 369775800}
  m_Layer: 0
  m_Name: Tail Random
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &369775800
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: *********}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 61574e354ba34a141ae2cc1228f5eda3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  UseCollision: 0
  CollisionSpace: 1
  CollisionMode: 0
  CheapCollision: 0
  DynamicWorldCollidersInclusion: 0
  InclusionRadius: 1
  IgnoreMeshColliders: 1
  IncludedColliders: []
  IncludedColliders2D: []
  CollidersType: 0
  CollideWithOtherTails: 0
  CollideWithDisabledColliders: 1
  CollisionSlippery: 1
  ReflectCollision: 0
  CollidersScaleCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  CollidersScaleMul: 6.5
  CollisionsAutoCurve: 0.5
  IgnoredColliders: []
  IgnoredColliders2D: []
  CollidersSameLayer: 1
  CollidersAddRigidbody: 1
  RigidbodyMass: 1
  CollidersLayer: 0
  UseSlitheryCurve: 0
  SlitheryCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.75
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseCurlingCurve: 0
  CurlingCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.3
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseSpringCurve: 0
  SpringCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseSlipperyCurve: 0
  SlipperyCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UsePosSpeedCurve: 0
  PosCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseRotSpeedCurve: 0
  RotCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.9
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UsePartialBlend: 0
  BlendCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.95
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.45
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseIK: 0
  IK:
    IKWeight: 0
    IKTargetPosition: {x: 0, y: 0, z: 0}
    IKTargetRotation: {x: 0, y: 0, z: 0, w: 0}
    LastLocalDirection: {x: 0, y: 0, z: 0}
    LocalDirection: {x: 0, y: 0, z: 0}
    IKBones: []
    ContinousSolving: 0
    SyncWithAnimator: 0
    ReactionQuality: 0
    Smoothing: 0
    StretchToTarget: 0
    StretchCurve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    Use2D: 0
    Invert: 0
  IKTarget: {fileID: 0}
  IKAutoWeights: 1
  IKBaseReactionWeight: 0.65
  IKReactionWeightCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.25
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  IKAutoAngleLimits: 1
  IKAutoAngleLimit: 40
  IKContinousSolve: 0
  IkInvertOrder: 0
  IKBlend: 1
  IKAnimatorBlend: 0.85
  IKReactionQuality: 2
  IKSmoothing: 0.2
  IKStretchToTarget: 0
  IKStretchCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  IKLimitSettings: []
  IKSelectiveChain: 0
  RotationOffset: {x: 0, y: 0, z: 0, w: 1}
  Curving: {x: 0, y: 0, z: 0, w: 1}
  UseCurvingCurve: 0
  CurvCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.75
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  LengthMultiplier: 1
  UseLengthMulCurve: 0
  LengthMulCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseGravityCurve: 0
  GravityCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.65
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  Gravity: {x: 0, y: 0, z: 0}
  UseWaving: 1
  CosinusAdd: 0
  FixedCycle: 0
  WavingSpeed: 2.5
  WavingRange: 0.4
  WavingAxis: {x: 1, y: 1, z: 1}
  WavingType: 1
  AlternateWave: 1
  UseWind: 0
  WindEffectPower: 1
  WindTurbulencePower: 1
  WindWorldNoisePower: 0.5
  WindEffect: {x: 0, y: 0, z: 0}
  fDebug: 0
  _gizmosEditorStartPreview: {fileID: 0}
  _gizmospesp: {fileID: 0}
  _gizmosEditorEndPreview: {fileID: 0}
  _gizmospeep: {fileID: 0}
  _editor_Title: Tail Animator 2
  _editor_IsInspectorViewingColliders: 0
  _editor_IsInspectorViewingIncludedColliders: 0
  _editor_animatorViewedCounter: 1
  TailSegments: []
  GhostParent:
    ProceduralPosition: {x: 0, y: 0, z: 0}
    ProceduralPositionWeightBlended: {x: 0, y: 0, z: 0}
    TrueTargetRotation: {x: 0, y: 0, z: 0, w: 1}
    PosRefRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosReferenceRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosition: {x: 0, y: 0, z: 0}
    BlendValue: 1
    BoneDimensionsScaled: {x: 0, y: 0, z: 0}
    BoneLengthScaled: 0
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalPositionInRoot: {x: 0, y: 0, z: 0}
    InitialLocalRotationInRoot: {x: 0, y: 0, z: 0, w: 1}
    LocalOffset: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: 0, y: 0, z: 0, w: 1}
    ColliderRadius: 1
    CollisionContactFlag: 0
    CollisionContactRelevancy: -1
    VelocityHelper: {x: 0, y: 0, z: 0}
    QVelocityHelper: {x: 0, y: 0, z: 0, w: 1}
    PreviousPush: {x: 0, y: 0, z: 0}
    Curving: {x: 0, y: 0, z: 0, w: 1}
    Gravity: {x: 0, y: 0, z: 0}
    GravityLookOffset: {x: 0, y: 0, z: 0}
    LengthMultiplier: 1
    PositionSpeed: 1
    RotationSpeed: 1
    Springiness: 0
    Slithery: 1
    Curling: 0.5
    Slippery: 1
    LastKeyframeLocalRotation: {x: 0, y: 0, z: 0, w: 0}
    LastKeyframeLocalPosition: {x: 0, y: 0, z: 0}
  GhostChild:
    ProceduralPosition: {x: 0, y: 0, z: 0}
    ProceduralPositionWeightBlended: {x: 0, y: 0, z: 0}
    TrueTargetRotation: {x: 0, y: 0, z: 0, w: 1}
    PosRefRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosReferenceRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosition: {x: 0, y: 0, z: 0}
    BlendValue: 1
    BoneDimensionsScaled: {x: 0, y: 0, z: 0}
    BoneLengthScaled: 0
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalPositionInRoot: {x: 0, y: 0, z: 0}
    InitialLocalRotationInRoot: {x: 0, y: 0, z: 0, w: 1}
    LocalOffset: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: 0, y: 0, z: 0, w: 1}
    ColliderRadius: 1
    CollisionContactFlag: 0
    CollisionContactRelevancy: -1
    VelocityHelper: {x: 0, y: 0, z: 0}
    QVelocityHelper: {x: 0, y: 0, z: 0, w: 1}
    PreviousPush: {x: 0, y: 0, z: 0}
    Curving: {x: 0, y: 0, z: 0, w: 1}
    Gravity: {x: 0, y: 0, z: 0}
    GravityLookOffset: {x: 0, y: 0, z: 0}
    LengthMultiplier: 1
    PositionSpeed: 1
    RotationSpeed: 1
    Springiness: 0
    Slithery: 1
    Curling: 0.5
    Slippery: 1
    LastKeyframeLocalRotation: {x: 0, y: 0, z: 0, w: 0}
    LastKeyframeLocalPosition: {x: 0, y: 0, z: 0}
  UseMaxDistance: 0
  DistanceFrom: {fileID: 0}
  _distanceFrom_Auto: {fileID: 0}
  MaximumDistance: 35
  MaxOutDistanceFactor: 0
  DistanceWithoutY: 0
  DistanceMeasurePoint: {x: 0, y: 0, z: 0}
  FadeDuration: 0.75
  Deflection: 0
  DeflectionStartAngle: 10
  DeflectionSmooth: 0
  DeflectionFalloff:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0.1
      outSlope: 0.1
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 5
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  DeflectOnlyCollisions: 1
  _Editor_Category: 0
  _Editor_FeaturesCategory: 0
  DrawGizmos: 1
  StartBone: {fileID: 0}
  EndBone: {fileID: 0}
  EndBoneJointOffset: {x: 0, y: 0, z: 0}
  _TransformsGhostChain: []
  _GhostChainInitCount: -1
  UpdateRate: 0
  AnimatePhysics: 0
  InterpolateRate: 1
  Prewarm: 0
  Slithery: 1
  Curling: 0.5
  Springiness: 0
  MaxStretching: 0.375
  AngleLimit: 181
  AngleLimitAxis: {x: 0, y: 0, z: 0}
  LimitAxisRange: {x: 0, y: 0}
  LimitSmoothing: 0.5
  MotionInfluence: 1
  MotionInfluenceInY: 1
  IncludeParent: 1
  UnifyBendiness: 0
  ReactionSpeed: 0.9
  Sustain: 0
  RotationRelevancy: 1
  SmoothingStyle: 1
  TimeScale: 1
  DeltaType: 4
  UpdateAsLast: 1
  DetectZeroKeyframes: 1
  StartAfterTPose: 1
  OptimizeWithMesh: {fileID: 0}
  OptimizeWithMeshes: []
  TailAnimatorAmount: 1
  DetachChildren: 0
  Axis2D: 0
  Tangle: 0
  AnimateRoll: 0
  OverrideKeyframeAnimation: 0
--- !u!4 &*********
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: *********}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: 0, w: 0.7071068}
  m_LocalPosition: {x: 2.96, y: -2.5, z: -1.69}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: -90, y: 0, z: 0}
--- !u!1 &*********
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 576316293}
  - component: {fileID: 576316296}
  - component: {fileID: 576316294}
  m_Layer: 5
  m_Name: Example Info
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &576316293
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: *********}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2001017406}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 113, y: -62.299927}
  m_SizeDelta: {x: 1000, y: 600}
  m_Pivot: {x: 0, y: 1}
--- !u!114 &576316294
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: *********}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5f7201a12d95ffc409449d95f23cf332, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_FontData:
    m_Font: {fileID: 10102, guid: 0000000000000000e000000000000000, type: 0}
    m_FontSize: 45
    m_FontStyle: 1
    m_BestFit: 0
    m_MinSize: 10
    m_MaxSize: 46
    m_Alignment: 0
    m_AlignByGeometry: 0
    m_RichText: 1
    m_HorizontalOverflow: 0
    m_VerticalOverflow: 0
    m_LineSpacing: 1
  m_Text: 'Example of generating procedurally tails in different shape. Algorithm
    is very simple and can be enchanced with custom coding.

    Every time you press
    play there will be generated tails with slightly different look.'
--- !u!222 &576316296
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: *********}
  m_CullTransparentMesh: 0
--- !u!1 &611248103
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 611248104}
  - component: {fileID: 611248107}
  - component: {fileID: 611248106}
  - component: {fileID: 611248105}
  m_Layer: 0
  m_Name: Cube (8)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &611248104
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 611248103}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 16.8, y: -29.07389, z: 9.1}
  m_LocalScale: {x: 2.3004, y: 15, z: 2.3004}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1945571810}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!65 &611248105
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 611248103}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &611248106
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 611248103}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &611248107
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 611248103}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &633614819
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 633614821}
  - component: {fileID: 633614820}
  m_Layer: 0
  m_Name: Directional Light
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!108 &633614820
Light:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 633614819}
  m_Enabled: 1
  serializedVersion: 11
  m_Type: 1
  m_Color: {r: 0.8820755, g: 0.9759214, b: 1, a: 1}
  m_Intensity: 0.2
  m_Range: 10
  m_SpotAngle: 30
  m_InnerSpotAngle: 21.80208
  m_CookieSize: 10
  m_Shadows:
    m_Type: 2
    m_Resolution: -1
    m_CustomResolution: -1
    m_Strength: 1
    m_Bias: 0.05
    m_NormalBias: 0.4
    m_NearPlane: 0.2
    m_CullingMatrixOverride:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    m_UseCullingMatrixOverride: 0
  m_Cookie: {fileID: 0}
  m_DrawHalo: 0
  m_Flare: {fileID: 0}
  m_RenderMode: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingLayerMask: 1
  m_Lightmapping: 4
  m_LightShadowCasterMode: 0
  m_AreaSize: {x: 1, y: 1}
  m_BounceIntensity: 1
  m_ColorTemperature: 6570
  m_UseColorTemperature: 0
  m_BoundingSphereOverride: {x: 0, y: 0, z: 0, w: 0}
  m_UseBoundingSphereOverride: 0
  m_UseViewFrustumForShadowCasterCull: 1
  m_ForceVisible: 0
  m_ShadowRadius: 0
  m_ShadowAngle: 0
  m_LightUnit: 1
  m_LuxAtDistance: 1
  m_EnableSpotReflector: 1
--- !u!4 &633614821
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 633614819}
  serializedVersion: 2
  m_LocalRotation: {x: 0.40821788, y: -0.23456968, z: 0.10938163, w: 0.8754261}
  m_LocalPosition: {x: 0, y: 3, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 50, y: -30, z: 0}
--- !u!1 &651777958
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 651777960}
  - component: {fileID: 651777959}
  m_Layer: 0
  m_Name: Point Light Orange
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!108 &651777959
Light:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 651777958}
  m_Enabled: 1
  serializedVersion: 11
  m_Type: 2
  m_Color: {r: 1, g: 0.5967039, b: 0.23113209, a: 1}
  m_Intensity: 2.25
  m_Range: 18.8
  m_SpotAngle: 30
  m_InnerSpotAngle: 21.80208
  m_CookieSize: 10
  m_Shadows:
    m_Type: 1
    m_Resolution: -1
    m_CustomResolution: -1
    m_Strength: 1
    m_Bias: 0.05
    m_NormalBias: 0.4
    m_NearPlane: 0.2
    m_CullingMatrixOverride:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    m_UseCullingMatrixOverride: 0
  m_Cookie: {fileID: 0}
  m_DrawHalo: 0
  m_Flare: {fileID: 0}
  m_RenderMode: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingLayerMask: 1
  m_Lightmapping: 4
  m_LightShadowCasterMode: 0
  m_AreaSize: {x: 1, y: 1}
  m_BounceIntensity: 1
  m_ColorTemperature: 6570
  m_UseColorTemperature: 0
  m_BoundingSphereOverride: {x: 0, y: 0, z: 0, w: 0}
  m_UseBoundingSphereOverride: 0
  m_UseViewFrustumForShadowCasterCull: 1
  m_ForceVisible: 0
  m_ShadowRadius: 0
  m_ShadowAngle: 0
  m_LightUnit: 1
  m_LuxAtDistance: 1
  m_EnableSpotReflector: 1
--- !u!4 &651777960
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 651777958}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0.48, y: 6.2, z: 3.25}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &655947739
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 655947740}
  m_Layer: 0
  m_Name: --------------------
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &655947740
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 655947739}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 2.2099159, y: 29.37389, z: -4.015108}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &905435057
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 905435058}
  - component: {fileID: 905435061}
  - component: {fileID: 905435060}
  - component: {fileID: 905435059}
  m_Layer: 0
  m_Name: Cube (7)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &905435058
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 905435057}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -20.48, y: -29.07389, z: 20.02}
  m_LocalScale: {x: 2.3004, y: 15, z: 2.3004}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1945571810}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!65 &905435059
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 905435057}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &905435060
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 905435057}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &905435061
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 905435057}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1198396411
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1198396412}
  m_Layer: 0
  m_Name: --------------------
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &1198396412
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1198396411}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 2.2099159, y: 29.37389, z: -4.015108}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1382807689
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1382807690}
  - component: {fileID: 1382807693}
  - component: {fileID: 1382807692}
  - component: {fileID: 1382807691}
  m_Layer: 0
  m_Name: Cube (6)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1382807690
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1382807689}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.68, y: -29.07389, z: 22.7}
  m_LocalScale: {x: 2.3004, y: 15, z: 2.3004}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1945571810}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!65 &1382807691
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1382807689}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &1382807692
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1382807689}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1382807693
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1382807689}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1470713093
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1470713097}
  - component: {fileID: 1470713096}
  - component: {fileID: 1470713095}
  - component: {fileID: 1470713094}
  m_Layer: 0
  m_Name: Cube (4)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!65 &1470713094
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1470713093}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &1470713095
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1470713093}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1470713096
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1470713093}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &1470713097
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1470713093}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -8.6, y: -29.07389, z: 11.7}
  m_LocalScale: {x: 2.3004, y: 15, z: 2.3004}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1945571810}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1599943096
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1599943099}
  - component: {fileID: 1599943097}
  m_Layer: 0
  m_Name: Tail Random
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1599943097
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1599943096}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 61574e354ba34a141ae2cc1228f5eda3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  UseCollision: 0
  CollisionSpace: 1
  CollisionMode: 0
  CheapCollision: 0
  DynamicWorldCollidersInclusion: 0
  InclusionRadius: 1
  IgnoreMeshColliders: 1
  IncludedColliders: []
  IncludedColliders2D: []
  CollidersType: 0
  CollideWithOtherTails: 0
  CollideWithDisabledColliders: 1
  CollisionSlippery: 1
  ReflectCollision: 0
  CollidersScaleCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  CollidersScaleMul: 6.5
  CollisionsAutoCurve: 0.5
  IgnoredColliders: []
  IgnoredColliders2D: []
  CollidersSameLayer: 1
  CollidersAddRigidbody: 1
  RigidbodyMass: 1
  CollidersLayer: 0
  UseSlitheryCurve: 0
  SlitheryCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.75
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseCurlingCurve: 0
  CurlingCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.3
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseSpringCurve: 0
  SpringCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseSlipperyCurve: 0
  SlipperyCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UsePosSpeedCurve: 0
  PosCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseRotSpeedCurve: 0
  RotCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.9
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UsePartialBlend: 0
  BlendCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.95
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.45
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseIK: 0
  IK:
    IKWeight: 0
    IKTargetPosition: {x: 0, y: 0, z: 0}
    IKTargetRotation: {x: 0, y: 0, z: 0, w: 0}
    LastLocalDirection: {x: 0, y: 0, z: 0}
    LocalDirection: {x: 0, y: 0, z: 0}
    IKBones: []
    ContinousSolving: 0
    SyncWithAnimator: 0
    ReactionQuality: 0
    Smoothing: 0
    StretchToTarget: 0
    StretchCurve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    Use2D: 0
    Invert: 0
  IKTarget: {fileID: 0}
  IKAutoWeights: 1
  IKBaseReactionWeight: 0.65
  IKReactionWeightCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.25
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  IKAutoAngleLimits: 1
  IKAutoAngleLimit: 40
  IKContinousSolve: 0
  IkInvertOrder: 0
  IKBlend: 1
  IKAnimatorBlend: 0.85
  IKReactionQuality: 2
  IKSmoothing: 0.2
  IKStretchToTarget: 0
  IKStretchCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  IKLimitSettings: []
  IKSelectiveChain: 0
  RotationOffset: {x: 0, y: 0, z: 0, w: 1}
  Curving: {x: 0, y: 0, z: 0, w: 1}
  UseCurvingCurve: 0
  CurvCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.75
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  LengthMultiplier: 1
  UseLengthMulCurve: 0
  LengthMulCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseGravityCurve: 0
  GravityCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.65
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  Gravity: {x: 0, y: 0, z: 0}
  UseWaving: 1
  CosinusAdd: 0
  FixedCycle: 0
  WavingSpeed: 2.5
  WavingRange: 0.4
  WavingAxis: {x: 1, y: 1, z: 1}
  WavingType: 1
  AlternateWave: 1
  UseWind: 0
  WindEffectPower: 1
  WindTurbulencePower: 1
  WindWorldNoisePower: 0.5
  WindEffect: {x: 0, y: 0, z: 0}
  fDebug: 0
  _gizmosEditorStartPreview: {fileID: 0}
  _gizmospesp: {fileID: 0}
  _gizmosEditorEndPreview: {fileID: 0}
  _gizmospeep: {fileID: 0}
  _editor_Title: Tail Animator 2
  _editor_IsInspectorViewingColliders: 0
  _editor_IsInspectorViewingIncludedColliders: 0
  _editor_animatorViewedCounter: 1
  TailSegments: []
  GhostParent:
    ProceduralPosition: {x: 0, y: 0, z: 0}
    ProceduralPositionWeightBlended: {x: 0, y: 0, z: 0}
    TrueTargetRotation: {x: 0, y: 0, z: 0, w: 1}
    PosRefRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosReferenceRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosition: {x: 0, y: 0, z: 0}
    BlendValue: 1
    BoneDimensionsScaled: {x: 0, y: 0, z: 0}
    BoneLengthScaled: 0
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalPositionInRoot: {x: 0, y: 0, z: 0}
    InitialLocalRotationInRoot: {x: 0, y: 0, z: 0, w: 1}
    LocalOffset: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: 0, y: 0, z: 0, w: 1}
    ColliderRadius: 1
    CollisionContactFlag: 0
    CollisionContactRelevancy: -1
    VelocityHelper: {x: 0, y: 0, z: 0}
    QVelocityHelper: {x: 0, y: 0, z: 0, w: 1}
    PreviousPush: {x: 0, y: 0, z: 0}
    Curving: {x: 0, y: 0, z: 0, w: 1}
    Gravity: {x: 0, y: 0, z: 0}
    GravityLookOffset: {x: 0, y: 0, z: 0}
    LengthMultiplier: 1
    PositionSpeed: 1
    RotationSpeed: 1
    Springiness: 0
    Slithery: 1
    Curling: 0.5
    Slippery: 1
    LastKeyframeLocalRotation: {x: 0, y: 0, z: 0, w: 0}
    LastKeyframeLocalPosition: {x: 0, y: 0, z: 0}
  GhostChild:
    ProceduralPosition: {x: 0, y: 0, z: 0}
    ProceduralPositionWeightBlended: {x: 0, y: 0, z: 0}
    TrueTargetRotation: {x: 0, y: 0, z: 0, w: 1}
    PosRefRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosReferenceRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosition: {x: 0, y: 0, z: 0}
    BlendValue: 1
    BoneDimensionsScaled: {x: 0, y: 0, z: 0}
    BoneLengthScaled: 0
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalPositionInRoot: {x: 0, y: 0, z: 0}
    InitialLocalRotationInRoot: {x: 0, y: 0, z: 0, w: 1}
    LocalOffset: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: 0, y: 0, z: 0, w: 1}
    ColliderRadius: 1
    CollisionContactFlag: 0
    CollisionContactRelevancy: -1
    VelocityHelper: {x: 0, y: 0, z: 0}
    QVelocityHelper: {x: 0, y: 0, z: 0, w: 1}
    PreviousPush: {x: 0, y: 0, z: 0}
    Curving: {x: 0, y: 0, z: 0, w: 1}
    Gravity: {x: 0, y: 0, z: 0}
    GravityLookOffset: {x: 0, y: 0, z: 0}
    LengthMultiplier: 1
    PositionSpeed: 1
    RotationSpeed: 1
    Springiness: 0
    Slithery: 1
    Curling: 0.5
    Slippery: 1
    LastKeyframeLocalRotation: {x: 0, y: 0, z: 0, w: 0}
    LastKeyframeLocalPosition: {x: 0, y: 0, z: 0}
  UseMaxDistance: 0
  DistanceFrom: {fileID: 0}
  _distanceFrom_Auto: {fileID: 0}
  MaximumDistance: 35
  MaxOutDistanceFactor: 0
  DistanceWithoutY: 0
  DistanceMeasurePoint: {x: 0, y: 0, z: 0}
  FadeDuration: 0.75
  Deflection: 0
  DeflectionStartAngle: 10
  DeflectionSmooth: 0
  DeflectionFalloff:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0.1
      outSlope: 0.1
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 5
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  DeflectOnlyCollisions: 1
  _Editor_Category: 0
  _Editor_FeaturesCategory: 0
  DrawGizmos: 1
  StartBone: {fileID: 0}
  EndBone: {fileID: 0}
  EndBoneJointOffset: {x: 0, y: 0, z: 0}
  _TransformsGhostChain: []
  _GhostChainInitCount: -1
  UpdateRate: 0
  AnimatePhysics: 0
  InterpolateRate: 1
  Prewarm: 0
  Slithery: 1
  Curling: 0.5
  Springiness: 0
  MaxStretching: 0.375
  AngleLimit: 181
  AngleLimitAxis: {x: 0, y: 0, z: 0}
  LimitAxisRange: {x: 0, y: 0}
  LimitSmoothing: 0.5
  MotionInfluence: 1
  MotionInfluenceInY: 1
  IncludeParent: 1
  UnifyBendiness: 0
  ReactionSpeed: 0.9
  Sustain: 0
  RotationRelevancy: 1
  SmoothingStyle: 1
  TimeScale: 1
  DeltaType: 4
  UpdateAsLast: 1
  DetectZeroKeyframes: 1
  StartAfterTPose: 1
  OptimizeWithMesh: {fileID: 0}
  OptimizeWithMeshes: []
  TailAnimatorAmount: 1
  DetachChildren: 0
  Axis2D: 0
  Tangle: 0
  AnimateRoll: 0
  OverrideKeyframeAnimation: 0
--- !u!4 &1599943099
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1599943096}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: 0, w: 0.7071068}
  m_LocalPosition: {x: -2.9900002, y: -2.5, z: -1.69}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: -90, y: 0, z: 0}
--- !u!1 &1778829594
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1778829596}
  - component: {fileID: 1778829595}
  m_Layer: 0
  m_Name: Point Light Cyan
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!108 &1778829595
Light:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1778829594}
  m_Enabled: 1
  serializedVersion: 11
  m_Type: 2
  m_Color: {r: 0.514151, g: 0.96396744, b: 1, a: 1}
  m_Intensity: 2.1
  m_Range: 18.8
  m_SpotAngle: 30
  m_InnerSpotAngle: 21.80208
  m_CookieSize: 10
  m_Shadows:
    m_Type: 1
    m_Resolution: -1
    m_CustomResolution: -1
    m_Strength: 1
    m_Bias: 0.05
    m_NormalBias: 0.4
    m_NearPlane: 0.2
    m_CullingMatrixOverride:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    m_UseCullingMatrixOverride: 0
  m_Cookie: {fileID: 0}
  m_DrawHalo: 0
  m_Flare: {fileID: 0}
  m_RenderMode: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingLayerMask: 1
  m_Lightmapping: 4
  m_LightShadowCasterMode: 0
  m_AreaSize: {x: 1, y: 1}
  m_BounceIntensity: 1
  m_ColorTemperature: 6570
  m_UseColorTemperature: 0
  m_BoundingSphereOverride: {x: 0, y: 0, z: 0, w: 0}
  m_UseBoundingSphereOverride: 0
  m_UseViewFrustumForShadowCasterCull: 1
  m_ForceVisible: 0
  m_ShadowRadius: 0
  m_ShadowAngle: 0
  m_LightUnit: 1
  m_LuxAtDistance: 1
  m_EnableSpotReflector: 1
--- !u!4 &1778829596
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1778829594}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -10.3, y: 0.7, z: 3.25}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1859713875
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1859713878}
  - component: {fileID: 1859713876}
  m_Layer: 0
  m_Name: Tail Random
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1859713876
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1859713875}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 61574e354ba34a141ae2cc1228f5eda3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  UseCollision: 0
  CollisionSpace: 1
  CollisionMode: 0
  CheapCollision: 0
  DynamicWorldCollidersInclusion: 0
  InclusionRadius: 1
  IgnoreMeshColliders: 1
  IncludedColliders: []
  IncludedColliders2D: []
  CollidersType: 0
  CollideWithOtherTails: 0
  CollideWithDisabledColliders: 1
  CollisionSlippery: 1
  ReflectCollision: 0
  CollidersScaleCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  CollidersScaleMul: 6.5
  CollisionsAutoCurve: 0.5
  IgnoredColliders: []
  IgnoredColliders2D: []
  CollidersSameLayer: 1
  CollidersAddRigidbody: 1
  RigidbodyMass: 1
  CollidersLayer: 0
  UseSlitheryCurve: 0
  SlitheryCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.75
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseCurlingCurve: 0
  CurlingCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.3
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseSpringCurve: 0
  SpringCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseSlipperyCurve: 0
  SlipperyCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UsePosSpeedCurve: 0
  PosCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.7
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseRotSpeedCurve: 0
  RotCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.9
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UsePartialBlend: 0
  BlendCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.95
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.45
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseIK: 0
  IK:
    IKWeight: 0
    IKTargetPosition: {x: 0, y: 0, z: 0}
    IKTargetRotation: {x: 0, y: 0, z: 0, w: 0}
    LastLocalDirection: {x: 0, y: 0, z: 0}
    LocalDirection: {x: 0, y: 0, z: 0}
    IKBones: []
    ContinousSolving: 0
    SyncWithAnimator: 0
    ReactionQuality: 0
    Smoothing: 0
    StretchToTarget: 0
    StretchCurve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    Use2D: 0
    Invert: 0
  IKTarget: {fileID: 0}
  IKAutoWeights: 1
  IKBaseReactionWeight: 0.65
  IKReactionWeightCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0.25
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  IKAutoAngleLimits: 1
  IKAutoAngleLimit: 40
  IKContinousSolve: 0
  IkInvertOrder: 0
  IKBlend: 1
  IKAnimatorBlend: 0.85
  IKReactionQuality: 2
  IKSmoothing: 0.2
  IKStretchToTarget: 0
  IKStretchCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  IKLimitSettings: []
  IKSelectiveChain: 0
  RotationOffset: {x: 0, y: 0, z: 0, w: 1}
  Curving: {x: 0, y: 0, z: 0, w: 1}
  UseCurvingCurve: 0
  CurvCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.75
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  LengthMultiplier: 1
  UseLengthMulCurve: 0
  LengthMulCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  UseGravityCurve: 0
  GravityCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0.65
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  Gravity: {x: 0, y: 0, z: 0}
  UseWaving: 1
  CosinusAdd: 0
  FixedCycle: 0
  WavingSpeed: 2.5
  WavingRange: 0.4
  WavingAxis: {x: 1, y: 1, z: 1}
  WavingType: 1
  AlternateWave: 1
  UseWind: 0
  WindEffectPower: 1
  WindTurbulencePower: 1
  WindWorldNoisePower: 0.5
  WindEffect: {x: 0, y: 0, z: 0}
  fDebug: 0
  _gizmosEditorStartPreview: {fileID: 0}
  _gizmospesp: {fileID: 0}
  _gizmosEditorEndPreview: {fileID: 0}
  _gizmospeep: {fileID: 0}
  _editor_Title: Tail Animator 2
  _editor_IsInspectorViewingColliders: 0
  _editor_IsInspectorViewingIncludedColliders: 0
  _editor_animatorViewedCounter: 1
  TailSegments: []
  GhostParent:
    ProceduralPosition: {x: 0, y: 0, z: 0}
    ProceduralPositionWeightBlended: {x: 0, y: 0, z: 0}
    TrueTargetRotation: {x: 0, y: 0, z: 0, w: 1}
    PosRefRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosReferenceRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosition: {x: 0, y: 0, z: 0}
    BlendValue: 1
    BoneDimensionsScaled: {x: 0, y: 0, z: 0}
    BoneLengthScaled: 0
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalPositionInRoot: {x: 0, y: 0, z: 0}
    InitialLocalRotationInRoot: {x: 0, y: 0, z: 0, w: 1}
    LocalOffset: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: 0, y: 0, z: 0, w: 1}
    ColliderRadius: 1
    CollisionContactFlag: 0
    CollisionContactRelevancy: -1
    VelocityHelper: {x: 0, y: 0, z: 0}
    QVelocityHelper: {x: 0, y: 0, z: 0, w: 1}
    PreviousPush: {x: 0, y: 0, z: 0}
    Curving: {x: 0, y: 0, z: 0, w: 1}
    Gravity: {x: 0, y: 0, z: 0}
    GravityLookOffset: {x: 0, y: 0, z: 0}
    LengthMultiplier: 1
    PositionSpeed: 1
    RotationSpeed: 1
    Springiness: 0
    Slithery: 1
    Curling: 0.5
    Slippery: 1
    LastKeyframeLocalRotation: {x: 0, y: 0, z: 0, w: 0}
    LastKeyframeLocalPosition: {x: 0, y: 0, z: 0}
  GhostChild:
    ProceduralPosition: {x: 0, y: 0, z: 0}
    ProceduralPositionWeightBlended: {x: 0, y: 0, z: 0}
    TrueTargetRotation: {x: 0, y: 0, z: 0, w: 1}
    PosRefRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosReferenceRotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousPosition: {x: 0, y: 0, z: 0}
    BlendValue: 1
    BoneDimensionsScaled: {x: 0, y: 0, z: 0}
    BoneLengthScaled: 0
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalPositionInRoot: {x: 0, y: 0, z: 0}
    InitialLocalRotationInRoot: {x: 0, y: 0, z: 0, w: 1}
    LocalOffset: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: 0, y: 0, z: 0, w: 1}
    ColliderRadius: 1
    CollisionContactFlag: 0
    CollisionContactRelevancy: -1
    VelocityHelper: {x: 0, y: 0, z: 0}
    QVelocityHelper: {x: 0, y: 0, z: 0, w: 1}
    PreviousPush: {x: 0, y: 0, z: 0}
    Curving: {x: 0, y: 0, z: 0, w: 1}
    Gravity: {x: 0, y: 0, z: 0}
    GravityLookOffset: {x: 0, y: 0, z: 0}
    LengthMultiplier: 1
    PositionSpeed: 1
    RotationSpeed: 1
    Springiness: 0
    Slithery: 1
    Curling: 0.5
    Slippery: 1
    LastKeyframeLocalRotation: {x: 0, y: 0, z: 0, w: 0}
    LastKeyframeLocalPosition: {x: 0, y: 0, z: 0}
  UseMaxDistance: 0
  DistanceFrom: {fileID: 0}
  _distanceFrom_Auto: {fileID: 0}
  MaximumDistance: 35
  MaxOutDistanceFactor: 0
  DistanceWithoutY: 0
  DistanceMeasurePoint: {x: 0, y: 0, z: 0}
  FadeDuration: 0.75
  Deflection: 0
  DeflectionStartAngle: 10
  DeflectionSmooth: 0
  DeflectionFalloff:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0.1
      outSlope: 0.1
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 5
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  DeflectOnlyCollisions: 1
  _Editor_Category: 0
  _Editor_FeaturesCategory: 0
  DrawGizmos: 1
  StartBone: {fileID: 0}
  EndBone: {fileID: 0}
  EndBoneJointOffset: {x: 0, y: 0, z: 0}
  _TransformsGhostChain: []
  _GhostChainInitCount: -1
  UpdateRate: 0
  AnimatePhysics: 0
  InterpolateRate: 1
  Prewarm: 0
  Slithery: 1
  Curling: 0.5
  Springiness: 0
  MaxStretching: 0.375
  AngleLimit: 181
  AngleLimitAxis: {x: 0, y: 0, z: 0}
  LimitAxisRange: {x: 0, y: 0}
  LimitSmoothing: 0.5
  MotionInfluence: 1
  MotionInfluenceInY: 1
  IncludeParent: 1
  UnifyBendiness: 0
  ReactionSpeed: 0.9
  Sustain: 0
  RotationRelevancy: 1
  SmoothingStyle: 1
  TimeScale: 1
  DeltaType: 4
  UpdateAsLast: 1
  DetectZeroKeyframes: 1
  StartAfterTPose: 1
  OptimizeWithMesh: {fileID: 0}
  OptimizeWithMeshes: []
  TailAnimatorAmount: 1
  DetachChildren: 0
  Axis2D: 0
  Tangle: 0
  AnimateRoll: 0
  OverrideKeyframeAnimation: 0
--- !u!4 &1859713878
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1859713875}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: 0, w: 0.7071068}
  m_LocalPosition: {x: 0.31, y: -2.5, z: -1.69}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: -90, y: 0, z: 0}
--- !u!1 &1945571809
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1945571810}
  m_Layer: 0
  m_Name: Scene Background
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1945571810
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1945571809}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 2.2099159, y: 29.37389, z: -4.015108}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 177048331}
  - {fileID: 1470713097}
  - {fileID: 1382807690}
  - {fileID: 905435058}
  - {fileID: 611248104}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2001017402
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2001017406}
  - component: {fileID: 2001017405}
  - component: {fileID: 2001017403}
  m_Layer: 5
  m_Name: Canvas
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &2001017403
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2001017402}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0cd44c1031e13a943bb63640046fad76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UiScaleMode: 1
  m_ReferencePixelsPerUnit: 100
  m_ScaleFactor: 1
  m_ReferenceResolution: {x: 1920, y: 1080}
  m_ScreenMatchMode: 0
  m_MatchWidthOrHeight: 0
  m_PhysicalUnit: 3
  m_FallbackScreenDPI: 96
  m_DefaultSpriteDPI: 96
  m_DynamicPixelsPerUnit: 1
  m_PresetInfoIsWorld: 0
--- !u!223 &2001017405
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2001017402}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 0
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_VertexColorAlwaysGammaSpace: 0
  m_AdditionalShaderChannelsFlag: 0
  m_UpdateRectTransformForStandalone: 0
  m_SortingLayerID: 0
  m_SortingOrder: 0
  m_TargetDisplay: 0
--- !u!224 &2001017406
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2001017402}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0, y: 0, z: 0}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 576316293}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0, y: 0}
--- !u!1660057539 &9223372036854775807
SceneRoots:
  m_ObjectHideFlags: 0
  m_Roots:
  - {fileID: 69809309}
  - {fileID: 633614821}
  - {fileID: 2001017406}
  - {fileID: 1198396412}
  - {fileID: 651777960}
  - {fileID: 1778829596}
  - {fileID: 108715041}
  - {fileID: 1945571810}
  - {fileID: 96328325}
  - {fileID: 655947740}
  - {fileID: 1599943099}
  - {fileID: 1859713878}
  - {fileID: *********}
