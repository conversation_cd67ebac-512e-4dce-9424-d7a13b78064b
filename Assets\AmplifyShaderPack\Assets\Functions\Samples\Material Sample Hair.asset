%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: Material Sample Hair
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor v1.9.3.3\n// Available at the
    Unity Asset Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=19303\nNode;AmplifyShaderEditor.CommentaryNode;746;-1488.617,2926.775;Inherit;False;3076.151;1087.437;Smoothness;23;2;433;751;750;431;752;747;625;716;626;718;627;429;430;428;41;25;435;513;514;515;275;745;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;823;-1904,-1008;Inherit;False;1974.58;832.8761;HairColorationAlongLength;36;810;576;820;572;818;813;569;822;578;557;577;556;558;560;562;814;564;809;574;550;737;555;553;554;551;580;581;565;819;817;812;570;568;567;808;575;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;803;-3776,-752;Inherit;False;771.1495;428.5201;Comment;4;790;787;788;789;;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;802;-3776,-1264;Inherit;False;769.5063;492;PHYSICAL
    MATERIAL;6;798;799;800;801;797;796;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;771;-840.022,2387.86;Inherit;False;825.6564;278.6904;DEPTH
    OFFSET;5;414;408;415;405;417;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;767;-3184,1312;Inherit;False;700.7002;358.7324;VertexColor;4;768;765;769;677;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;745;-945.2704,3435.131;Inherit;False;1138.366;502.3494;Smoothness
    Along Hair Legth;11;730;729;734;733;731;732;739;740;727;726;738;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;593;-3184,1008;Inherit;False;1427.556;273.2209;RootTipGradient;9;736;618;583;582;591;589;590;586;584;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;524;-5056,-2224;Inherit;False;2298.879;913.2667;Dither;24;527;530;525;541;547;534;529;526;545;546;539;528;535;532;543;531;537;544;540;542;536;538;200;8;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;441;-1424,6224;Inherit;False;1857.377;735.4713;Specular;27;467;466;465;464;463;462;461;460;459;458;457;456;455;454;453;452;451;450;449;448;447;446;444;443;442;468;469;;0.06603771,0.06603771,0.06603771,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;470;-5056,-784;Inherit;False;1116.34;520.7225;HDRP
    Depth Prepass Postpass;9;480;481;479;478;477;476;473;472;471;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;199;-4672,-1088;Inherit;False;455.679;100;HDRP
    Hidden _Cutoff;;0,0,0,1;_Cutoff must be set on both Master Node Properties Additional
    Options and Material Inspector;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;200;-3152,-1968;Inherit;False;240.2569;118.0333;Alpha;;0,0,0,1;If
    Alpha value  is lower than Alpha Clip Threshold then Alpha Clip happens;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;257;442.6141,1551.593;Inherit;False;228.6;100;;;0,0,0,1;Saturate
    Occclusion to Prevent Nan in Unity;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;258;-849.0921,4407.922;Inherit;False;271.6077;121.433;SS
    Sampler States Normal Map;;0,0,0,1;Keep Normal SS separated to prevent possible
    GPU crossing in fragment stages of the Mip stream;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;272;-1383.592,14.59186;Inherit;False;150;100;BaseColor
    Map;;0,0,0,1;_MainTex;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;273;-1355.461,4264.953;Inherit;False;150;100;Normal
    Map;;0,0,0,1;_BumpMap;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;274;-1390.295,1687.157;Inherit;False;150;100;Occlusion
    Map;;0,0,0,1;_OcclusionMap;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;275;-1438.617,3229.819;Inherit;False;150;100;Smoothness
    Map;;0,0,0,1;_SmoothnessMap;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;276;-1332.438,4673.365;Inherit;False;150;100;Hair
    Flow Map;;0,0,0,1;_FlowMap;0;0\nNode;AmplifyShaderEditor.WireNode;397;-576.5054,677.1385;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;398;-593.5576,697.8448;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;399;-615.4819,724.6412;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SamplerNode;24;-894.7219,1683.636;Inherit;True;Property;_TextureSample16;Texture
    Sample 16;36;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SaturateNode;144;444.6399,1660.473;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SwizzleNode;27;-573.0966,1682.352;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.StickyNoteNode;469;-1296,6576;Inherit;False;150;100;Specular
    Color;;0,0,0,1;_SpecularColor;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;479;-5008,-432;Inherit;False;639;119;Hidden;;0,0,0,1;in
    ASE template Enable Transparent Depth Prepass and Postpass$$checkbox will show
    in the Rendering.HighDefinition.LightingShaderGraphGUI;0;0\nNode;AmplifyShaderEditor.WireNode;424;-954.6815,1746.721;Inherit;False;1;0;SAMPLER2D;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.WireNode;509;-984.9802,1882.806;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SaturateNode;365;-377.2143,984.4998;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SaturateNode;371;-374.2663,1192.414;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SaturateNode;374;-379.5967,1385.835;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleDivideOpNode;370;-505.2968,1192.413;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleDivideOpNode;362;-508.1125,984.6827;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SwizzleNode;393;-51.51801,1253.582;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SwizzleNode;392;-46.51801,1059.581;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;400;-573.8293,989.5908;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;401;-583.5734,1193;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;402;-604.2797,1359.869;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleDivideOpNode;360;-516.4683,1386.019;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMaxOpNode;373;-741.8342,1216.177;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMaxOpNode;376;-739.7643,1003.555;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.LerpOp;369;125.8285,937.6857;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;361;130.7975,1138.992;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;359;131.4254,1335.629;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RangedFloatNode;363;-896.3553,973.8903;Float;False;Constant;_Float1;Float
    0;0;0;Create;True;0;0;0;False;0;False;1E-05;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMaxOpNode;372;-740.4148,1405.719;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SwizzleNode;391;-44.22027,858.8019;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;505;-1364.033,1994.338;Inherit;False;488;UV_Base;1;0;OBJECT;;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.TextureCoordinatesNode;504;-1408.709,1881.938;Inherit;False;0;-1;2;3;2;SAMPLER2D;;False;0;FLOAT2;1,1;False;1;FLOAT2;0,0;False;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.LerpOp;502;-1146.667,1880.78;Inherit;False;3;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;517;-946.6346,-69.41431;Inherit;False;SSMainTex;-1;True;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.SamplerNode;378;-885.9451,596.1401;Inherit;True;Property;_StrandID;Strand
    ID;7;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.UnpackScaleNormalNode;98;-340.5193,4228.203;Inherit;False;Tangent;2;0;FLOAT4;0,0,0,0;False;1;FLOAT;1;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.SamplerNode;22;-851.1441,4224.423;Inherit;True;Property;_TextureSample14;Texture
    Sample 14;34;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;444;-384,6336;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RangedFloatNode;449;-1072,6336;Inherit;False;Constant;_Float3;Float
    3;36;0;Create;True;0;0;0;False;0;False;1;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;450;-592,6368;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SwizzleNode;468;-1072,6400;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.Vector3Node;447;-1088,6640;Inherit;False;Constant;_Vector2;Vector
    2;54;0;Create;True;0;0;0;False;0;False;0,0,0;0,0,0;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.RangedFloatNode;442;-1072,6784;Inherit;False;Constant;_Float5;Float
    5;36;0;Create;True;0;0;0;False;0;False;0.5;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;455;-1072,6480;Inherit;False;Constant;_Float4;Float
    4;36;0;Create;True;0;0;0;False;0;False;0.3;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.LerpOp;451;-896,6384;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;464;-592,6288;Inherit;False;435;Smoothness_R;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;452;-368,6608;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;466;-576,6544;Inherit;False;435;Smoothness_R;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;467;-464,6688;Inherit;False;107;BaseColorMapAlpha;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;465;-480,6432;Inherit;False;107;BaseColorMapAlpha;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;453;-208,6336;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;448;-208,6592;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;516;-1123.973,4415.385;Inherit;False;488;UV_Base;1;0;OBJECT;;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SaturateNode;730;-167.8755,3645.815;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.LerpOp;734;-321.7921,3644.766;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;733;-613.2027,3646.428;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;731;-459.8026,3704.472;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMinOpNode;732;-597.1081,3726.273;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0.01;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;515;-1181.71,3420.315;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;435;-627.7233,3321.142;Inherit;False;Smoothness_R;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SamplerNode;25;-943.1268,3223.854;Inherit;True;Property;_TextureSample17;Texture
    Sample 17;37;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.LerpOp;729;13.0968,3601.094;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.LerpOp;428;160.9946,3203.619;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;626;194.235,2988.319;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;627;361.9362,3151.6;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SaturateNode;718;504.1539,3153.285;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;750;858.9077,3322.646;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SaturateNode;751;998.358,3322.424;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.LerpOp;354;356.4898,150.7488;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SaturateNode;353;519.731,151.2288;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;758;296.8583,241.9844;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;759;296.7494,1349.076;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;763;263.5289,1662.3;Inherit;False;3;0;FLOAT3;1,1,1;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.OneMinusNode;271;81.95284,1759.186;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;407;-370.7156,2098.044;Inherit;False;107;BaseColorMapAlpha;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;412;-380.0226,2003.194;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;411;-126.1948,2002.788;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SamplerNode;410;-893.2333,1974.686;Inherit;True;Property;_TextureSample3;Texture
    Sample 3;0;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SwizzleNode;420;-574.1488,2165.633;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.StickyNoteNode;769;-2928,1552;Inherit;False;181;102;VertexColor_A;;0.009433985,0.009433985,0.009433985,1;Baked
    Data;0;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;408;-568.9399,2460.872;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.LerpOp;405;-389.4836,2437.86;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;414;-791.022,2478.574;Half;False;Property;_DepthOffset;Depth
    Offset;47;1;[Header];Create;True;1;DEPTH OFFSET;0;0;False;0;False;0.1;-0.1;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;513;-1369.1,3490.334;Inherit;False;490;UV_Mask;1;0;OBJECT;;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.GetLocalVarNode;514;-1369.899,3415.539;Inherit;False;488;UV_Base;1;0;OBJECT;;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.LerpOp;599;1508.214,299.2802;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;598;1363.358,406.9301;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;778;1645.606,-5.394252;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;777;1633.072,15.11684;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SaturateNode;600;1665.323,298.9836;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;704;2140.11,60.70922;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;622;2550.532,-40.83483;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;714;2074.789,-2.535683;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;780;2092.495,15.71365;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;623;1945.594,78.0943;Inherit;False;617;StrandID;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.HSVToRGBNode;707;2281.636,60.48118;Float;False;3;0;FLOAT;0;False;1;FLOAT;2;False;2;FLOAT;0;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.WireNode;784;2499.771,168.1212;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;783;2484.001,197.4087;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.LerpOp;446;-880,6656;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;454;-592,6624;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.StickyNoteNode;791;-2560,-1488;Inherit;False;372.0489;1400.73;;;0,0,0,1;CATEGORY_COLOR$_RootColor$_LengthColor$_TipColor$_RootDistance$_RootFade$_TipDistance$_TipFade$_Brightness$_GoingGrey$_HueVariation$$CATEGORY_SURFACE
    INPUTS$_MainTex$_MainUVs$_Base_AffectUVchannel0$_Base_AffectUVchannel1$_Base_AffectUVchannel2$_Base_AffectUVchannel3$$CATEGORY_NORMAL
    MAP$_BumpMap$_NormalStrength$$CATEGORY_STRAND DIRECTION$_FlowMap$$CATEGORY_HAIR
    STRAND DIRECTION$_HairStrandDirection$$CATEGORY_HAIR DITHER$_EnableDither$_DitherNoiseMap$_DitherSpread$_DitherEdgeMin$_DitherEdgeContrast$$CATEGORY_STRAND
    MAPPING$_EnableHairStandMapping$_StrandIDMap$_TintRedChannel$_BlendRedChannel$_TintGreenChannel$_BlendGreenChannel$_TintBlueChannel$_BlendBlueChannel$$CATEGORY_AMBIENT
    OCCLUSION$_OcclusionMap$_EnableAOUseLightmapUV$_OcclusionStrengthAO$_AmbientOcclusionStrengthBaked$$CATEGORY_DEPTH$_DepthOffset$_DepthOffsetEnableAO$$CATEGORY_SMOOTHNESS$_SmoothnessMap$_MaskUVs$_Mask_AffectUVchannel0$_Mask_AffectUVchannel1$_Mask_AffectUVchannel2$_Mask_AffectUVchannel3$_SmoothnessMin$_SmoothnessMax$_SmoothnessSecondaryOffset$_SmoothnessRoot$_SmoothnessRootFade$_SmoothnessRootLength$_SmoothnessRootDistance$_EnableSmoothnessPerStrand$_SmoothnessPerStrand$$CATEGORY_SPECULAR$_SpecularColor$_Specular$_SpecularShift$_SecondarySpecular$_SecondarySpecularShift$$CATEGORY_HAIR
    TRANSMISSION$_TransmissionColor$_TransmissionRim$$CATEGORY_PHYSICAL MATERIAL$_Radial_Smoothness$_Cuticle_AngleShift$$CATEGORY_STAND$_StrandCountProbe$_StrandShadowBias$;0;0\nNode;AmplifyShaderEditor.RangedFloatNode;90;-156.8215,77.82029;Half;False;Property;_Brightness;Brightness;13;0;Create;False;1;;0;0;False;0;False;1;1.12;0;2;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;624;1952,176;Inherit;False;Property;_HueVariation;Hue
    Variation;15;0;Create;False;0;0;0;False;0;False;0.01;0.01;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.TexturePropertyNode;20;-1182.295,4225.661;Inherit;True;Property;_BumpMap;Normal
    Map;27;3;[Header];[Normal];[SingleLineTexture];Create;False;1;NORMAL MAP;0;0;False;0;False;None;None;False;bump;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.RangedFloatNode;97;-576,4304;Half;False;Property;_NormalStrength;Normal
    Strength;28;0;Create;False;1;;0;0;False;0;False;1;1;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;355;519.1494,226.513;Half;False;Property;_EnableHairStandMapping;Enable;36;2;[Header];[ToggleUI];Create;False;1;STRAND
    MAPPING;0;0;False;0;False;0;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.ColorNode;382;-251.5401,857.7386;Half;False;Property;_TintRedChannel;Tint
    Red Channel;38;0;Create;False;0;0;0;False;0;False;1,1,1,0;1,1,1,0;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.RangedFloatNode;385;-1273.682,1001.628;Half;False;Property;_BlendRedChannel;Blend
    Feather;39;0;Create;False;0;0;0;False;0;False;0.5;1;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.ColorNode;383;-252.6222,1059.835;Half;False;Property;_TintGreenChannel;Tint
    Green Channel;40;0;Create;False;0;0;0;False;0;False;1,1,1,0;1,1,1,0;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.RangedFloatNode;380;-1275.37,1216.061;Half;False;Property;_BlendGreenChannel;Blend
    Feather;41;0;Create;False;0;0;0;False;0;False;0.5;1;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.ColorNode;384;-249.7028,1255.635;Half;False;Property;_TintBlueChannel;Tint
    Blue Channel;42;0;Create;False;0;0;0;False;0;False;1,1,1,0;1,1,1,0;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.RangedFloatNode;381;-1274.839,1404.199;Half;False;Property;_BlendBlueChannel;Blend
    Feather;43;0;Create;False;0;0;0;False;0;False;0.5;1;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.TexturePropertyNode;42;-1226.321,1684.38;Inherit;True;Property;_OcclusionMap;Occlusion
    Map;44;2;[Header];[SingleLineTexture];Create;False;1;AMBIENT OCCLUSION;0;0;False;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.RangedFloatNode;506;-1457.667,2065.655;Half;False;Property;_EnableAOUseLightmapUV;Enable
    AO Use Lightmap UV;45;1;[ToggleUI];Create;False;0;0;0;False;0;False;0;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;138;-214.8944,1755.85;Half;False;Property;_OcclusionStrengthAO;Ambient
    Occlusion Strength;46;0;Create;False;0;0;0;False;0;False;0;0;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;415;-654.1166,2551.551;Half;False;Property;_DepthOffsetEnableAO;Enable
    Depth from AO;48;1;[ToggleUI];Create;False;0;0;0;False;0;False;0;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.TexturePropertyNode;41;-1273.173,3224.402;Inherit;True;Property;_SmoothnessMap;Smoothness
    Map;49;2;[Header];[SingleLineTexture];Create;False;1;SMOOTHNESS;0;0;False;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.RangedFloatNode;429;-157.1034,3135.555;Half;False;Property;_SmoothnessMin;Smoothness
    Min;55;0;Create;False;0;0;0;False;0;False;0.6;0.077;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;430;-157.6661,3215.072;Half;False;Property;_SmoothnessMax;Smoothness
    Max;56;0;Create;False;0;0;0;False;0;False;0.8;0.471;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;727;-290.0088,3560.511;Inherit;False;Property;_SmoothnessRoot;Smoothness
    Root;58;0;Create;False;0;0;0;False;0;False;0.8;0.8;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;740;-895.2704,3720.183;Inherit;False;Property;_SmoothnessRootFade;Smoothness
    Root Fade;59;0;Create;False;0;0;0;False;0;False;0.5;0.5;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;726;-287.7331,3485.379;Inherit;False;Property;_SmoothnessRootLength;Smoothness
    Root Length;60;0;Create;False;0;0;0;False;0;False;0.7;0.7;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;739;-891.8646,3642.48;Inherit;False;Property;_SmoothnessRootDistance;Smoothness
    Root Distance;61;0;Create;False;0;0;0;False;0;False;0.5;0.5;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;752;328.1456,3265.037;Inherit;False;Property;_EnableSmoothnessPerStrand;Enable
    Smoothness Per Strand;62;1;[ToggleUI];Create;False;0;0;0;False;0;False;0;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;625;-159.6197,3057.506;Inherit;False;Property;_SmoothnessPerStrand;Smoothness
    Per Strand;63;0;Create;False;0;0;0;False;0;False;0.01;0.01;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.ColorNode;457;-1296,6416;Half;False;Property;_SpecularColor;Specular
    Color;64;2;[HDR];[Header];Create;False;1;SPECULAR;0;0;False;0;False;0.6039216,0.3137255,0,0;1,1,1,1;False;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.RangedFloatNode;443;-880,6304;Half;False;Property;_Specular;Specular
    Multiplier;65;0;Create;False;0;0;0;False;0;False;1;0;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;459;-512,6768;Half;False;Property;_SpecularShift;Specular
    Shift;66;0;Create;False;0;0;0;False;0;False;0.5;0.5;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;458;-880,6560;Half;False;Property;_SecondarySpecular;Secondary
    Specular Multiplier;67;0;Create;False;0;0;0;False;0;False;1;0;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;456;-512,6848;Half;False;Property;_SecondarySpecularShift;Secondary
    Specular Shift;68;0;Create;False;0;0;0;False;0;False;0.9;0.5;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.ColorNode;416;-834.8745,2164.919;Half;False;Property;_TransmissionColor;Transmission
    Color;69;2;[HDR];[Header];Create;False;1;HAIR TRANSMISSION;0;0;False;0;False;0.8823529,0.8666667,0.627451,0.8823529;0,0,0,0;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.RangedFloatNode;413;-302.4269,2186.751;Half;False;Property;_TransmissionRim;Transmission
    Rim;70;0;Create;False;0;0;0;False;0;False;0;0.2;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.StickyNoteNode;796;-3392,-1136;Inherit;False;309.7031;114.5469;Radial
    Smoothness;;0,0,0,1;Smoothness Controls the internal scattering of light paths
    and the amount of light the hair fiber absorbs.;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;797;-3392,-928;Inherit;False;321.5063;138.7241;Cuticle
    Angle;;0,0,0,1;The angle (in degrees) that the scales on a hair fiber tilt from
    the strand direction. For human hair, this value is usually between 2 to 3 degrees.
    Use this property to \u201Cshift\u201D the highlight.;0;0\nNode;AmplifyShaderEditor.RangedFloatNode;611;864,320;Inherit;False;Property;_GoingGrey;Going
    Grey;14;0;Create;False;0;0;0;False;0;False;0;1;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;774;1152.415,321.6465;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;601;1344,176;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0.125;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;596;1344,272;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0.125;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;608;1168,416;Inherit;False;Constant;_Float0;Float
    0;0;0;Create;True;0;0;0;False;0;False;0.87;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMaxOpNode;604;1520,96;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;779;1632,96;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SwizzleNode;73;-512,0;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SamplerNode;21;-864,0;Inherit;True;Property;_TextureSample13;Texture
    Sample 13;33;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.TexturePropertyNode;17;-1216,0;Inherit;True;Property;_MainTex;BaseColor
    Map;16;2;[Header];[SingleLineTexture];Create;False;1;SURFACE INPUTS;0;0;False;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.GetLocalVarNode;716;-128,2976;Inherit;False;715;StrandIDRandom;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;431;544,3360;Half;False;Property;_SmoothnessSecondaryOffset;Smoothness
    Secondary Offset;57;0;Create;False;0;0;0;False;0;False;0.1;0.077;-1;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.LerpOp;747;656,3216;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.LerpOp;471;-4672,-720;Inherit;False;3;0;FLOAT;1;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;472;-4976,-656;Half;False;Property;_HiddenPostpass;HiddenPostpass;3;1;[HideInInspector];Create;True;0;0;0;False;0;False;1;2.29;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.LerpOp;473;-4672,-560;Inherit;False;3;0;FLOAT;1;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;476;-5008,-592;Half;False;Property;_TransparentDepthPrepassEnable;_TransparentDepthPrepassEnable;0;2;[HideInInspector];[ToggleUI];Create;True;0;0;0;True;0;False;0;1;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;477;-4992,-512;Half;False;Property;_TransparentDepthPostpassEnable;_TransparentDepthPostpassEnable;1;2;[HideInInspector];[ToggleUI];Create;True;0;0;0;True;0;False;0;1;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;478;-4992,-720;Half;False;Property;_HiddenPrepass;HiddenPrepass;2;1;[HideInInspector];Create;True;0;0;0;False;0;False;0;-0.9;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;538;-3152,-2064;Inherit;False;Enable
    Dither;True;1;2;4;None;Active;Object;-1;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.LerpOp;542;-3312,-2000;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.DitheringNode;540;-3552,-1984;Inherit;False;2;True;4;0;FLOAT;0;False;1;SAMPLER2D;;False;2;FLOAT4;0,0,0,0;False;3;SAMPLERSTATE;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;531;-3792,-1984;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.ScreenPosInputsNode;543;-3792,-1888;Inherit;False;1;False;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.LerpOp;535;-3968,-1904;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;1;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.PowerNode;528;-4128,-1904;Inherit;False;True;2;0;FLOAT;0;False;1;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.TFHCRemapNode;539;-4320,-1872;Inherit;False;5;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;1;False;3;FLOAT;-0.2;False;4;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.LerpOp;526;-4128,-1696;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;0.5;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.ScaleAndOffsetNode;529;-4368,-1616;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;1;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;534;-4528,-1616;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;1.5;False;1;FLOAT;0\nNode;AmplifyShaderEditor.AbsOpNode;541;-4656,-1616;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.DotProductOpNode;525;-4784,-1616;Inherit;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.ViewDirInputsCoordNode;530;-5008,-1616;Float;False;World;True;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.NormalVertexDataNode;527;-5008,-1472;Inherit;False;0;5;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.RangedFloatNode;125;-4928,-1168;Inherit;False;Property;_Cutoff;Cutoff;4;1;[HideInInspector];Create;False;1;;0;0;False;0;False;0;0.9;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;129;-5056,-976;Half;False;Property;_AlphaCutoffShadow;Alpha
    Cutoff Shadow;5;1;[HideInInspector];Create;False;0;0;0;False;0;False;1;0.851;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;544;-3488,-1840;Half;False;Property;_EnableDither;Enable;31;2;[Header];[ToggleUI];Create;False;1;HAIR
    DITHER;0;0;False;0;False;1;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.TexturePropertyNode;537;-3856,-2176;Float;True;Property;_DitherNoiseMap;Dither
    Noise;32;2;[NoScaleOffset];[SingleLineTexture];Create;False;0;0;0;False;0;False;None;None;False;black;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.RangedFloatNode;546;-4656,-1872;Half;False;Property;_DitherSpread;Dither
    Spread;33;0;Create;False;0;0;0;False;0;False;0;2.78;0;0.3;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;545;-4432,-1696;Half;False;Property;_DitherEdgeMin;Dither
    Edge Min;34;0;Create;False;0;0;0;False;0;False;0;0.75;0;10;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;547;-4656,-1520;Half;False;Property;_DitherEdgeContrast;Dither
    Edge Contrast;35;0;Create;False;0;0;0;False;0;False;0.09;0.75;0.09;10;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;798;-3712,-1008;Inherit;False;Property;_Cuticle_AngleShift;Cuticle
    Angle Shift;72;0;Create;False;0;0;0;False;0;False;3;3;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.Vector4Node;789;-3728,-704;Inherit;False;Property;_StrandCountProbe;Strand
    Count Probe;73;0;Create;False;0;0;0;False;0;False;0,0,0,0;0,0,0,0;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.RangedFloatNode;788;-3728,-512;Inherit;False;Property;_StrandShadowBias;Strand
    Shadow Bias;74;0;Create;False;0;0;0;False;0;False;0;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;799;-3712,-1216;Inherit;False;Property;_Radial_Smoothness;Radial
    Smoothness;71;1;[Header];Create;False;1;PHYSICAL MATERIAL;0;0;False;0;False;0.7;0.7;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SwizzleNode;808;-816,-960;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;567;-624,-960;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;568;-416,-960;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SaturateNode;570;-288,-960;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;812;-688,-704;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;817;-464,-880;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;819;-128,-896;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SaturateNode;565;-816,-768;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;551;-1456,-592;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMaxOpNode;554;-1552,-688;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0.01;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;553;-1584,-768;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;555;-1392,-704;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;737;-1680,-592;Inherit;False;736;RootTipGradient;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.LerpOp;550;-1232,-768;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SwizzleNode;809;-816,-672;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;564;-624,-672;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;814;-672,-544;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SaturateNode;562;-848,-464;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.LerpOp;560;-1232,-464;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;558;-1392,-384;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;556;-1552,-464;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMaxOpNode;557;-1552,-368;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0.01;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;822;-1456,-272;Inherit;False;736;RootTipGradient;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMaxOpNode;569;-624,-480;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;813;-688,-496;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;818;-464,-656;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;572;-80,-512;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;820;-128,-496;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SwizzleNode;810;-272,-368;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;825;101.3132,-25.09045;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;89;160,-32;Inherit;False;3;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;824;112,-432;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;352;720,-32;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;616;1136,496;Inherit;False;617;StrandID;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;826;112,208;Inherit;False;617;StrandID;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.DotProductOpNode;805;-368,576;Inherit;False;2;0;FLOAT3;0.25,0.25,0.25;False;1;FLOAT3;0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SwizzleNode;390;-544,592;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;617;-240,576;Inherit;False;StrandID;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;613;-80,672;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;2;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;614;-240,672;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0.5;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;715;80,672;Inherit;False;StrandIDRandom;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.LerpOp;595;1808,-48;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;775;1120,0;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;776;1136,32;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.DotProductOpNode;603;1184,96;Inherit;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;511;-1104,208;Inherit;False;488;UV_Base;1;0;OBJECT;;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.GetLocalVarNode;510;-1120,800;Inherit;False;488;UV_Base;1;0;OBJECT;;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;827;-368,400;Inherit;False;Grey;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;828;960,112;Inherit;False;827;Grey;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.ColorNode;576;-528,-368;Inherit;False;Property;_LengthColor;Length
    Color;7;0;Create;False;0;0;0;False;0;False;0.3686275,0.2078431,0.1411765,0;0.3686275,0.2078431,0.1411765,0;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.ColorNode;574;-1072,-672;Inherit;False;Property;_RootColor;Root
    Color;6;1;[Header];Create;False;1;COLOR;0;0;False;0;False;0.1215686,0.04313726,0.01176471,0;0.1215686,0.04313726,0.01176471,0;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.ColorNode;575;-1040,-960;Inherit;False;Property;_TipColor;Tip
    Color;8;0;Create;False;0;0;0;False;0;False;0.8196079,0.6039216,0.3764706,0;0.8196079,0.6039216,0.3764706,0;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.RangedFloatNode;577;-1840,-464;Inherit;False;Property;_RootDistance;Root
    Distance;9;0;Create;False;0;0;0;False;0;False;0.25;0.25;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;578;-1840,-368;Inherit;False;Property;_RootFade;Root
    Fade;10;0;Create;False;0;0;0;False;0;False;0.5;0.5;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;581;-1872,-784;Inherit;False;Property;_TipDistance;Tip
    Distance;11;0;Create;False;0;0;0;False;0;False;0.25;0.25;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;580;-1888,-688;Inherit;False;Property;_TipFade;Tip
    Fade;12;0;Create;False;0;0;0;False;0;False;0.5;0.5;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.Vector3Node;829;-592,400;Inherit;False;Constant;_Grey;Grey;70;0;Create;True;0;0;0;False;0;False;0.6,0.3,0.1;0,0,0;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.TexturePropertyNode;375;-1312,592;Inherit;True;Property;_StrandIDMap;Strand
    ID Map;37;1;[SingleLineTexture];Create;False;0;0;0;False;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.RegisterLocalVarNode;107;-512,112;Inherit;False;BaseColorMapAlpha;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;532;-4384,-1984;Inherit;False;107;BaseColorMapAlpha;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;536;-3568,-2064;Inherit;False;107;BaseColorMapAlpha;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SwizzleNode;163;-480,4672;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SamplerNode;161;-832,4672;Inherit;True;Property;_TextureSample19;Texture
    Sample 17;37;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.GetLocalVarNode;512;-1104,4864;Inherit;False;488;UV_Base;1;0;OBJECT;;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.TexCoordVertexDataNode;589;-3136,1056;Inherit;False;0;2;0;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.VertexColorNode;677;-3136,1376;Inherit;False;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SimpleAddOpNode;584;-2432,1056;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;590;-2912,1056;Inherit;False;2;0;FLOAT2;0,0;False;1;FLOAT2;0.5,0.5;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;591;-2736,1056;Inherit;False;2;2;0;FLOAT2;2,0;False;1;FLOAT2;2,2;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.OneMinusNode;582;-2144,1056;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;583;-2288,1056;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0.5;False;1;FLOAT;0\nNode;AmplifyShaderEditor.DotProductOpNode;586;-2592,1056;Inherit;False;2;0;FLOAT2;0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;618;-2848,1152;Inherit;False;588;Hair
    Flow Map;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;765;-2928,1408;Inherit;False;VertexColor_G;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;768;-2928,1488;Inherit;False;VertexColor_A;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;736;-1984,1056;Inherit;False;RootTipGradient;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.Vector4Node;486;-2704,464;Inherit;False;Property;_MainUVs;Main
    UVs;17;0;Create;False;0;0;0;False;0;False;1,1,0,0;0,0,0,0;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.TexCoordVertexDataNode;840;-2736,336;Inherit;False;0;2;0;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;839;-2288,336;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SwizzleNode;837;-2480,464;Inherit;False;FLOAT2;0;1;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SwizzleNode;838;-2480,544;Inherit;False;FLOAT2;2;3;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;842;-2128,528;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;488;-1936,528;Inherit;False;UV_Base;-1;True;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;588;-272,4672;Inherit;False;Hair
    Flow Map;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.Vector3Node;865;-800,4912;Inherit;False;Property;_HairStrandDirection;Hair
    Strand Direction;30;1;[Header];Create;False;1;HAIR STRAND DIRECTION;0;0;False;0;False;0,-1,0;0,0,0;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.TexturePropertyNode;164;-1136,4672;Inherit;True;Property;_FlowMap;Hair
    Flow Map;29;2;[Header];[SingleLineTexture];Create;False;1;HAIR FLOW MAP;0;0;False;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.TexCoordVertexDataNode;847;-2704,672;Inherit;False;0;2;0;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;845;-2288,672;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.Vector4Node;491;-2672,800;Inherit;False;Property;_MaskUVs;Mask
    UVs;50;0;Create;False;0;0;0;False;0;False;1,1,0,0;0,0,0,0;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SwizzleNode;843;-2464,800;Inherit;False;FLOAT2;0;1;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SwizzleNode;844;-2464,880;Inherit;False;FLOAT2;2;3;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;846;-2112,848;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;490;-1968,848;Inherit;False;UV_Mask;-1;True;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.GetLocalVarNode;738;-576,3840;Inherit;False;736;RootTipGradient;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;461;-144,6768;Inherit;False;False;-1;Specular
    Shift;13;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;462;-144,6848;Inherit;False;False;-1;Secondary
    Specular Shift;16;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;460;-16,6592;Inherit;False;False;-1;Secondary
    Specular Tint;14;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionOutput;463;-16,6336;Inherit;False;False;-1;Specular
    Tint;12;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionOutput;6;19.94309,4228.598;Inherit;False;False;-1;Normal;1;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionOutput;433;1158.17,3321.773;Inherit;False;False;-1;Secondary
    Smoothness;15;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;419;34.00794,2003.839;Inherit;False;False;-1;Transmittance;10;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionOutput;418;4.42743,2191.381;Inherit;False;False;-1;Rim
    Transmittance Intensity;11;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;4;644.9086,1662.047;Inherit;False;False;-1;Occlusion;4;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionOutput;417;-207.3656,2439.645;Inherit;False;False;-1;Depth
    Offset;21;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;2;864,3216;Inherit;False;False;-1;Smoothness;3;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;481;-4496,-720;Inherit;False;False;-1;Alpha
    Clip Threshold Depth Prepass;8;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;480;-4496,-560;Inherit;False;False;-1;Alpha
    Clip Threshold Depth Postpass;9;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;71;-4672,-1168;Inherit;False;False;-1;Alpha
    Clip Threshold;6;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;72;-4672,-976;Inherit;False;False;-1;Alpha
    Clip Threshold Shadow;7;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;801;-3392,-1216;Inherit;False;False;-1;Radial
    Smoothness;17;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;800;-3392,-1008;Inherit;False;False;-1;Cuticle
    Angle Shift;18;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;787;-3408,-704;Inherit;False;False;-1;Strand
    Count Probe;19;False;1;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.FunctionOutput;790;-3408,-512;Inherit;False;False;-1;Strand
    Shadow Bias;20;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;8;-2880,-2064;Inherit;False;False;-1;Alpha;5;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;438;-368,4912;Inherit;False;False;-1;Hair
    Strand Direction;2;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionOutput;1;2752,-32;Inherit;False;True;-1;BaseColor;0;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nWireConnection;397;0;378;1\nWireConnection;398;0;378;2\nWireConnection;399;0;378;3\nWireConnection;24;0;42;0\nWireConnection;24;1;509;0\nWireConnection;24;7;42;1\nWireConnection;144;0;763;0\nWireConnection;27;0;24;0\nWireConnection;424;0;42;0\nWireConnection;509;0;502;0\nWireConnection;365;0;362;0\nWireConnection;371;0;370;0\nWireConnection;374;0;360;0\nWireConnection;370;0;401;0\nWireConnection;370;1;373;0\nWireConnection;362;0;400;0\nWireConnection;362;1;376;0\nWireConnection;393;0;384;0\nWireConnection;392;0;383;0\nWireConnection;400;0;397;0\nWireConnection;401;0;398;0\nWireConnection;402;0;399;0\nWireConnection;360;0;402;0\nWireConnection;360;1;372;0\nWireConnection;373;0;380;0\nWireConnection;373;1;363;0\nWireConnection;376;0;385;0\nWireConnection;376;1;363;0\nWireConnection;369;1;391;0\nWireConnection;369;2;365;0\nWireConnection;361;0;369;0\nWireConnection;361;1;392;0\nWireConnection;361;2;371;0\nWireConnection;359;0;361;0\nWireConnection;359;1;393;0\nWireConnection;359;2;374;0\nWireConnection;372;0;381;0\nWireConnection;372;1;363;0\nWireConnection;391;0;382;0\nWireConnection;502;0;504;0\nWireConnection;502;1;505;0\nWireConnection;502;2;506;0\nWireConnection;517;0;17;1\nWireConnection;378;0;375;0\nWireConnection;378;1;510;0\nWireConnection;378;7;375;1\nWireConnection;98;0;22;0\nWireConnection;98;1;97;0\nWireConnection;22;0;20;0\nWireConnection;22;1;516;0\nWireConnection;22;7;20;1\nWireConnection;444;0;464;0\nWireConnection;444;1;450;0\nWireConnection;450;0;443;0\nWireConnection;450;1;451;0\nWireConnection;468;0;457;0\nWireConnection;451;0;449;0\nWireConnection;451;1;468;0\nWireConnection;451;2;455;0\nWireConnection;452;0;466;0\nWireConnection;452;1;454;0\nWireConnection;453;0;444;0\nWireConnection;453;1;465;0\nWireConnection;448;0;452;0\nWireConnection;448;1;467;0\nWireConnection;730;0;734;0\nWireConnection;734;0;733;0\nWireConnection;734;1;731;0\nWireConnection;734;2;738;0\nWireConnection;733;0;739;0\nWireConnection;731;0;733;0\nWireConnection;731;1;732;0\nWireConnection;732;0;740;0\nWireConnection;515;0;514;0\nWireConnection;515;1;513;0\nWireConnection;435;0;25;1\nWireConnection;25;0;41;0\nWireConnection;25;1;515;0\nWireConnection;25;7;41;1\nWireConnection;729;0;726;0\nWireConnection;729;1;727;0\nWireConnection;729;2;730;0\nWireConnection;428;0;429;0\nWireConnection;428;1;430;0\nWireConnection;428;2;25;1\nWireConnection;626;0;716;0\nWireConnection;626;1;625;0\nWireConnection;627;0;626;0\nWireConnection;627;1;428;0\nWireConnection;718;0;627;0\nWireConnection;750;0;747;0\nWireConnection;750;1;431;0\nWireConnection;751;0;750;0\nWireConnection;354;0;89;0\nWireConnection;354;1;758;0\nWireConnection;354;2;826;0\nWireConnection;353;0;354;0\nWireConnection;758;0;759;0\nWireConnection;759;0;359;0\nWireConnection;763;1;27;0\nWireConnection;763;2;271;0\nWireConnection;271;0;138;0\nWireConnection;412;0;410;1\nWireConnection;412;1;420;0\nWireConnection;411;0;412;0\nWireConnection;411;1;407;0\nWireConnection;410;0;424;0\nWireConnection;410;1;505;0\nWireConnection;410;7;42;1\nWireConnection;420;0;416;0\nWireConnection;408;0;410;2\nWireConnection;408;1;414;0\nWireConnection;405;1;408;0\nWireConnection;405;2;415;0\nWireConnection;599;0;596;0\nWireConnection;599;1;774;0\nWireConnection;599;2;598;0\nWireConnection;598;0;608;0\nWireConnection;598;1;616;0\nWireConnection;778;0;777;0\nWireConnection;777;0;779;0\nWireConnection;600;0;599;0\nWireConnection;704;0;780;0\nWireConnection;704;1;623;0\nWireConnection;622;0;595;0\nWireConnection;622;1;707;0\nWireConnection;622;2;784;0\nWireConnection;714;0;595;0\nWireConnection;780;0;714;0\nWireConnection;707;0;704;0\nWireConnection;707;2;624;0\nWireConnection;784;0;783;0\nWireConnection;783;0;624;0\nWireConnection;446;0;447;0\nWireConnection;446;1;468;0\nWireConnection;446;2;442;0\nWireConnection;454;0;458;0\nWireConnection;454;1;446;0\nWireConnection;774;0;611;0\nWireConnection;601;0;774;0\nWireConnection;596;0;774;0\nWireConnection;604;0;603;0\nWireConnection;604;1;601;0\nWireConnection;779;0;604;0\nWireConnection;73;0;21;0\nWireConnection;21;0;17;0\nWireConnection;21;1;511;0\nWireConnection;21;7;17;1\nWireConnection;747;0;428;0\nWireConnection;747;1;718;0\nWireConnection;747;2;752;0\nWireConnection;471;0;478;0\nWireConnection;471;1;472;0\nWireConnection;471;2;476;0\nWireConnection;473;0;478;0\nWireConnection;473;1;472;0\nWireConnection;473;2;477;0\nWireConnection;538;0;536;0\nWireConnection;538;1;542;0\nWireConnection;542;0;536;0\nWireConnection;542;1;540;0\nWireConnection;542;2;544;0\nWireConnection;540;0;531;0\nWireConnection;540;1;537;0\nWireConnection;540;2;543;0\nWireConnection;540;3;537;1\nWireConnection;531;0;532;0\nWireConnection;531;1;535;0\nWireConnection;535;0;528;0\nWireConnection;535;2;526;0\nWireConnection;528;0;532;0\nWireConnection;528;1;539;0\nWireConnection;539;0;546;0\nWireConnection;526;0;545;0\nWireConnection;526;2;529;0\nWireConnection;529;0;534;0\nWireConnection;529;1;547;0\nWireConnection;534;0;541;0\nWireConnection;541;0;525;0\nWireConnection;525;0;530;0\nWireConnection;525;1;527;0\nWireConnection;808;0;575;0\nWireConnection;567;0;808;0\nWireConnection;567;1;565;0\nWireConnection;568;0;567;0\nWireConnection;568;1;817;0\nWireConnection;570;0;568;0\nWireConnection;812;0;565;0\nWireConnection;817;0;818;0\nWireConnection;819;0;570;0\nWireConnection;565;0;550;0\nWireConnection;551;0;737;0\nWireConnection;554;0;580;0\nWireConnection;553;0;581;0\nWireConnection;555;0;553;0\nWireConnection;555;1;554;0\nWireConnection;550;0;553;0\nWireConnection;550;1;555;0\nWireConnection;550;2;551;0\nWireConnection;809;0;574;0\nWireConnection;564;0;809;0\nWireConnection;564;1;814;0\nWireConnection;814;0;562;0\nWireConnection;562;0;560;0\nWireConnection;560;0;556;0\nWireConnection;560;1;558;0\nWireConnection;560;2;822;0\nWireConnection;558;0;556;0\nWireConnection;558;1;557;0\nWireConnection;556;0;577;0\nWireConnection;557;0;578;0\nWireConnection;569;0;813;0\nWireConnection;569;1;562;0\nWireConnection;813;0;812;0\nWireConnection;818;0;564;0\nWireConnection;572;0;810;0\nWireConnection;572;1;820;0\nWireConnection;572;2;569;0\nWireConnection;820;0;819;0\nWireConnection;810;0;576;0\nWireConnection;825;0;824;0\nWireConnection;89;0;825;0\nWireConnection;89;1;73;0\nWireConnection;89;2;90;0\nWireConnection;824;0;572;0\nWireConnection;352;0;89;0\nWireConnection;352;1;353;0\nWireConnection;352;2;355;0\nWireConnection;805;0;829;0\nWireConnection;805;1;390;0\nWireConnection;390;0;378;0\nWireConnection;617;0;805;0\nWireConnection;613;0;614;0\nWireConnection;614;0;805;0\nWireConnection;715;0;613;0\nWireConnection;595;0;778;0\nWireConnection;595;1;352;0\nWireConnection;595;2;600;0\nWireConnection;775;0;352;0\nWireConnection;776;0;775;0\nWireConnection;603;0;776;0\nWireConnection;603;1;828;0\nWireConnection;827;0;829;0\nWireConnection;107;0;21;4\nWireConnection;163;0;161;0\nWireConnection;161;0;164;0\nWireConnection;161;1;512;0\nWireConnection;161;7;164;1\nWireConnection;584;0;586;0\nWireConnection;590;0;589;0\nWireConnection;591;0;590;0\nWireConnection;582;0;583;0\nWireConnection;583;0;584;0\nWireConnection;586;0;591;0\nWireConnection;586;1;618;0\nWireConnection;765;0;677;2\nWireConnection;768;0;677;4\nWireConnection;736;0;582;0\nWireConnection;839;0;840;0\nWireConnection;839;1;837;0\nWireConnection;837;0;486;0\nWireConnection;838;0;486;0\nWireConnection;842;0;839;0\nWireConnection;842;1;838;0\nWireConnection;488;0;842;0\nWireConnection;588;0;163;0\nWireConnection;845;0;847;0\nWireConnection;845;1;843;0\nWireConnection;843;0;491;0\nWireConnection;844;0;491;0\nWireConnection;846;0;845;0\nWireConnection;846;1;844;0\nWireConnection;490;0;846;0\nWireConnection;461;0;459;0\nWireConnection;462;0;456;0\nWireConnection;460;0;448;0\nWireConnection;463;0;453;0\nWireConnection;6;0;98;0\nWireConnection;433;0;751;0\nWireConnection;419;0;411;0\nWireConnection;418;0;413;0\nWireConnection;4;0;144;0\nWireConnection;417;0;405;0\nWireConnection;2;0;747;0\nWireConnection;481;0;471;0\nWireConnection;480;0;473;0\nWireConnection;71;0;125;0\nWireConnection;72;0;129;0\nWireConnection;801;0;799;0\nWireConnection;800;0;798;0\nWireConnection;787;0;789;0\nWireConnection;790;0;788;0\nWireConnection;8;0;538;0\nWireConnection;438;0;865;0\nWireConnection;1;0;622;0\nASEEND*/\n//CHKSM=A846DCA4C36DE488FFAA5CBBE5EBFB1EE6637AF3"
  m_functionName: 
  m_description: 'Amplify Shader Pack Example

    *HDRP'
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 0
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_customEdited: 0
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 0
  m_headerStyle: 2
  m_headerColor: {r: 1, g: 0.4, b: 0, a: 1}
  m_customNodeCategory: Amplify Shader Pack
  m_previewPosition: 0
  m_hidden: 0
  m_url: 
