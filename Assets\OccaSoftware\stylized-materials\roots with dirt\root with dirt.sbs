<?xml version="1.0" encoding="UTF-8"?><package><identifier v="Unsaved Package"/><formatVersion v="1.1.0.202302"/><updaterVersion v="1.1.0.202302"/><fileUID v="{a9621271-1520-4433-87e5-0ff81383888b}"/><versionUID v="0"/><dependencies><dependency><filename v="sbs://noise_cells_4.sbs"/><uid v="1353814887"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://edge_detect.sbs"/><uid v="1407202483"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_perlin_noise.sbs"/><uid v="1407332030"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://invert.sbs"/><uid v="1368115694"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://bevel.sbs"/><uid v="1353816301"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://height_blend.sbs"/><uid v="1449623608"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://histogram_range.sbs"/><uid v="1353829477"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://hbao_2.sbs"/><uid v="1297162667"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://pattern_tile_sampler.sbs"/><uid v="1449399972"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://blur_hq.sbs"/><uid v="1299236171"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://histogram_scan.sbs"/><uid v="1149349207"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://curvature_smooth.sbs"/><uid v="1449590280"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_grunge_map_014.sbs"/><uid v="1354061253"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://pattern_polygon_2.sbs"/><uid v="1487918781"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://splatter.sbs"/><uid v="1487921414"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://slope_blur.sbs"/><uid v="1449402040"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_dirt_4.sbs"/><uid v="1491035839"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_clouds_2.sbs"/><uid v="1372403415"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_bnw_spots_1.sbs"/><uid v="1490202924"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://pattern_gaussian_1.sbs"/><uid v="1487922179"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency></dependencies><content><graph><identifier v="root_with_dirt"/><uid v="1353811363"/><graphOutputs><graphoutput><identifier v="basecolor"/><uid v="1213284336"/><attributes><label v="Base Color"/></attributes><usages><usage><components v="RGBA"/><name v="baseColor"/></usage></usages><group v="Material"/></graphoutput><graphoutput><identifier v="normal"/><uid v="1213284338"/><attributes><label v="Normal"/></attributes><usages><usage><components v="RGBA"/><name v="normal"/></usage></usages><group v="Material"/></graphoutput><graphoutput><identifier v="roughness"/><uid v="1213284340"/><attributes><label v="Roughness"/></attributes><usages><usage><components v="RGBA"/><name v="roughness"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput><graphoutput><identifier v="metallic"/><uid v="1213284342"/><attributes><label v="Metallic"/></attributes><usages><usage><components v="RGBA"/><name v="metallic"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput><graphoutput><identifier v="height"/><uid v="1279137031"/><attributes><label v="Height"/></attributes><usages><usage><components v="RGBA"/><name v="height"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput><graphoutput><identifier v="AO"/><uid v="1353866730"/><attributes><label v="AO"/></attributes><usages><usage><components v="RGBA"/><name v="ambient"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput></graphOutputs><compNodes><compNode><uid v="1213284337"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1355829575"/><connRefOutput v="1355829576"/></connection></connections><GUILayout><gpos v="208 -208 0"/></GUILayout><compImplementation><compOutputBridge><output v="1213284336"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1213284339"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1353840794"/><connRefOutput v="1353840795"/></connection></connections><GUILayout><gpos v="208 -80 0"/></GUILayout><compImplementation><compOutputBridge><output v="1213284338"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1213284341"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1356398348"/><connRefOutput v="1356398349"/></connection></connections><GUILayout><gpos v="208 80 0"/></GUILayout><compImplementation><compOutputBridge><output v="1213284340"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1213284343"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1353840820"/><connRefOutput v="1353840821"/></connection></connections><GUILayout><gpos v="176 368 0"/></GUILayout><compImplementation><compOutputBridge><output v="1213284342"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1279137030"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1492386683"/><connRefOutput v="1492386684"/></connection></connections><GUILayout><gpos v="191.038788 513.640137 0"/></GUILayout><compImplementation><compOutputBridge><output v="1279137031"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1353839819"/><GUILayout><gpos v="-3888 16 0"/></GUILayout><compOutputs><compOutput><uid v="1353839820"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///cells_4?dependency=1353814887"/><parameters><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1353839820"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1353839839"/><connections><connection><identifier v="input"/><connRef v="1354742782"/><connRefOutput v="1354573520"/></connection></connections><GUILayout><gpos v="-3792 -158.456528 0"/></GUILayout><compOutputs><compOutput><uid v="1353839840"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///edge_detect?dependency=1407202483"/><parameters><parameter><name v="edge_width"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="11.21"/></paramValue></parameter><parameter><name v="edge_roundness"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1353839840"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1353839967"/><connections><connection><identifier v="input"/><connRef v="1354573519"/><connRefOutput v="1354573520"/></connection></connections><GUILayout><gpos v="-3263.79102 752 0"/></GUILayout><compOutputs><compOutput><uid v="1353839840"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///edge_detect?dependency=1407202483"/><parameters><parameter><name v="edge_width"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="4.63000011"/></paramValue></parameter><parameter><name v="edge_roundness"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1353839840"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1353840006"/><connections><connection><identifier v="inputgradient"/><connRef v="1353840018"/><connRefOutput v="1353840019"/></connection><connection><identifier v="input1"/><connRef v="1353839967"/><connRefOutput v="1353839840"/></connection></connections><GUILayout><gpos v="-3120 767.638977 0"/></GUILayout><compOutputs><compOutput><uid v="1353840007"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="warp"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.25"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1353840018"/><GUILayout><gpos v="-3247.99976 895.638977 0"/></GUILayout><compOutputs><compOutput><uid v="1353840019"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///perlin_noise?dependency=1407332030"/><parameters><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueInt32 v="7"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1353840019"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1353840189"/><connections><connection><identifier v="inputgradient"/><connRef v="1353840190"/><connRefOutput v="1353840019"/></connection><connection><identifier v="input1"/><connRef v="1353839839"/><connRefOutput v="1353839840"/></connection></connections><GUILayout><gpos v="-3650.68481 -64.4234467 0"/></GUILayout><compOutputs><compOutput><uid v="1353840007"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="warp"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="3.86999989"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1353840190"/><GUILayout><gpos v="-3728 144 0"/></GUILayout><compOutputs><compOutput><uid v="1353840019"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///perlin_noise?dependency=1407332030"/><parameters><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueInt32 v="5"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1353840019"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1353840210"/><connections><connection><identifier v="Source"/><connRef v="1353840006"/><connRefOutput v="1353840007"/></connection></connections><GUILayout><gpos v="-2992 767.638977 0"/></GUILayout><compOutputs><compOutput><uid v="1353840211"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///invert_grayscale?dependency=1368115694"/><parameters/><outputBridgings><outputBridging><uid v="1353840211"/><identifier v="Invert_Grayscale"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1353840219"/><connections><connection><identifier v="Source"/><connRef v="1353840189"/><connRefOutput v="1353840007"/></connection></connections><GUILayout><gpos v="-3489.47583 -48 0"/></GUILayout><compOutputs><compOutput><uid v="1353840211"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///invert_grayscale?dependency=1368115694"/><parameters/><outputBridgings><outputBridging><uid v="1353840211"/><identifier v="Invert_Grayscale"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1353840227"/><connections><connection><identifier v="input"/><connRef v="1353840210"/><connRefOutput v="1353840211"/></connection></connections><GUILayout><gpos v="-2846.31421 784 0"/></GUILayout><compOutputs><compOutput><uid v="1353840228"/><comptype v="2"/></compOutput><compOutput><uid v="1353840229"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///bevel?dependency=1353816301"/><parameters><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="-0.0399999991"/></paramValue></parameter><parameter><name v="smoothing"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="4.17000008"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1353840228"/><identifier v="height"/></outputBridging><outputBridging><uid v="1353840229"/><identifier v="normal"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1353840772"/><connections><connection><identifier v="height_bottom"/><connRef v="1353851893"/><connRefOutput v="1353851894"/></connection><connection><identifier v="height_top"/><connRef v="1353861773"/><connRefOutput v="1353861774"/></connection></connections><GUILayout><gpos v="-2000 432 0"/></GUILayout><compOutputs><compOutput><uid v="1353840773"/><comptype v="2"/></compOutput><compOutput><uid v="1353840774"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///height_blend?dependency=1449623608"/><parameters><parameter><name v="contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.74000001"/></paramValue></parameter><parameter><name v="height_position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.429999977"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1353840773"/><identifier v="blended_height"/></outputBridging><outputBridging><uid v="1353840774"/><identifier v="height_mask"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1353840794"/><connections><connection><identifier v="input1"/><connRef v="1354003195"/><connRefOutput v="1354003196"/></connection></connections><GUILayout><gpos v="-944 48 0"/></GUILayout><compOutputs><compOutput><uid v="1353840795"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="normal"/><parameters><parameter><name v="input2alpha"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="60"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1353840820"/><GUILayout><gpos v="16 368 0"/></GUILayout><compOutputs><compOutput><uid v="1353840821"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="uniform"/><parameters><parameter><name v="colorswitch"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1353840831"/><GUILayout><gpos v="-432 80 0"/></GUILayout><compOutputs><compOutput><uid v="1353840821"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="uniform"/><parameters><parameter><name v="colorswitch"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="outputcolor"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.883817434 0.883817434 0.883817434 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1353851893"/><connections><connection><identifier v="input"/><connRef v="1354076887"/><connRefOutput v="1354046031"/></connection></connections><GUILayout><gpos v="-2320 798.959167 0"/></GUILayout><compOutputs><compOutput><uid v="1353851894"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_range?dependency=1353829477"/><parameters/><outputBridgings><outputBridging><uid v="1353851894"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1353861773"/><connections><connection><identifier v="input"/><connRef v="1356068145"/><connRefOutput v="1356068146"/></connection></connections><GUILayout><gpos v="-2096 240 0"/></GUILayout><compOutputs><compOutput><uid v="1353861774"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_range?dependency=1353829477"/><parameters><parameter><name v="position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.539999962"/></paramValue></parameter><parameter><name v="range"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.48999998"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1353861774"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1353861964"/><connections><connection><identifier v="input"/><connRef v="1353840219"/><connRefOutput v="1353840211"/></connection></connections><GUILayout><gpos v="-3327.52393 -112 0"/></GUILayout><compOutputs><compOutput><uid v="1353840228"/><comptype v="2"/></compOutput><compOutput><uid v="1353840229"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///bevel?dependency=1353816301"/><parameters><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="-0.0199999996"/></paramValue></parameter><parameter><name v="smoothing"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="4.17000008"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1353840228"/><identifier v="height"/></outputBridging><outputBridging><uid v="1353840229"/><identifier v="normal"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1353866729"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1353868304"/><connRefOutput v="1353868305"/></connection></connections><GUILayout><gpos v="208 720 0"/></GUILayout><compImplementation><compOutputBridge><output v="1353866730"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1353868304"/><connections><connection><identifier v="input"/><connRef v="1353872325"/><connRefOutput v="1353872326"/></connection></connections><GUILayout><gpos v="-336 720 0"/></GUILayout><compOutputs><compOutput><uid v="1353868305"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///hbao?dependency=1297162667"/><parameters><parameter><name v="height_depth"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.149999991"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1353868305"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1353872033"/><connections><connection><identifier v="mask_map_input"/><connRef v="1354002917"/><connRefOutput v="1354002918"/></connection><connection><identifier v="pattern_input"/><connRef v="1491713395"/><connRefOutput v="1487924726"/></connection><connection><identifier v="background_input"/><connRef v="1491713395"/><connRefOutput v="1487924726"/></connection></connections><GUILayout><gpos v="-1808 -354.872528 0"/></GUILayout><compOutputs><compOutput><uid v="1353872034"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1449399972"/><parameters><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.569999993"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="4.25999975"/></paramValue></parameter><parameter><name v="color_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.859999955"/></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.310000002"/></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0599999987"/></paramValue></parameter><parameter><name v="rotation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0250000004"/></paramValue></parameter><parameter><name v="rotation_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.379999995"/></paramValue></parameter><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="53"/></paramValue></parameter><parameter><name v="mask_map_invert"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="39"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1353872034"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1353872306"/><connections><connection><identifier v="input"/><connRef v="1353872033"/><connRefOutput v="1353872034"/></connection></connections><GUILayout><gpos v="-1634.69922 272 0"/></GUILayout><compOutputs><compOutput><uid v="1353872307"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_range?dependency=1353829477"/><parameters><parameter><name v="range"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1353872307"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1353872325"/><connections><connection><identifier v="height_bottom"/><connRef v="1353872306"/><connRefOutput v="1353872307"/></connection><connection><identifier v="height_top"/><connRef v="1355545471"/><connRefOutput v="1355545472"/></connection></connections><GUILayout><gpos v="-1424 272 0"/></GUILayout><compOutputs><compOutput><uid v="1353872326"/><comptype v="2"/></compOutput><compOutput><uid v="1353872327"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///height_blend?dependency=1449623608"/><parameters><parameter><name v="height_position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.479999989"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1353872326"/><identifier v="blended_height"/></outputBridging><outputBridging><uid v="1353872327"/><identifier v="height_mask"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1354002917"/><connections><connection><identifier v="Input_1"/><connRef v="1353840772"/><connRefOutput v="1353840773"/></connection></connections><GUILayout><gpos v="-1921.75256 144 0"/></GUILayout><compOutputs><compOutput><uid v="1354002918"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1149349207"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.75"/></paramValue></parameter><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.74000001"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1354002918"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1354003195"/><connections><connection><identifier v="height_bottom"/><connRef v="1353872325"/><connRefOutput v="1353872326"/></connection></connections><GUILayout><gpos v="-1168 30.534483 0"/></GUILayout><compOutputs><compOutput><uid v="1354003196"/><comptype v="2"/></compOutput><compOutput><uid v="1354003197"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///height_blend?dependency=1449623608"/><parameters><parameter><name v="height_position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="mode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="opacity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.599999964"/></paramValue></parameter><parameter><name v="contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.769999981"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1354003196"/><identifier v="blended_height"/></outputBridging><outputBridging><uid v="1354003197"/><identifier v="height_mask"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1354005322"/><connections><connection><identifier v="input"/><connRef v="1353840794"/><connRefOutput v="1353840795"/></connection></connections><GUILayout><gpos v="-930.059143 -208 0"/></GUILayout><compOutputs><compOutput><uid v="1354005323"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///curvature_smooth?dependency=1449590280"/><parameters/><outputBridgings><outputBridging><uid v="1354005323"/><identifier v="height"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1354005718"/><connections><connection><identifier v="input1"/><connRef v="1354005322"/><connRefOutput v="1354005323"/></connection></connections><GUILayout><gpos v="-656 -304 0"/></GUILayout><compOutputs><compOutput><uid v="1354005719"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1354005733"/><paramsArrayCells><paramsArrayCell><uid v="1491777834"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.260869563 0.12930055 0 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1491777835"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.60447973"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.669421494 0.437460244 0.185502335 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1491777836"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.894117653 0.611764729 0.360784322 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1491777837"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.185106382"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.214876026 0.157736063 0.081549339 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1354006482"/><connections><connection><identifier v="input1"/><connRef v="1354005322"/><connRefOutput v="1354005323"/></connection></connections><GUILayout><gpos v="-656 -432 0"/></GUILayout><compOutputs><compOutput><uid v="1354006483"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1354006492"/><paramsArrayCells><paramsArrayCell><uid v="1491919144"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.318181813 0.172093883 0.0479189456 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1491919145"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.640425563"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.69421488 0.482685685 0.284377187 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1491919146"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.951476812"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.721636057 0.65495342 0.146610513 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1354006538"/><connections><connection><identifier v="destination"/><connRef v="1354005718"/><connRefOutput v="1354005719"/></connection><connection><identifier v="source"/><connRef v="1354006482"/><connRefOutput v="1354006483"/></connection><connection><identifier v="opacity"/><connRef v="1355829087"/><connRefOutput v="1355829088"/></connection></connections><GUILayout><gpos v="-432.885498 -256 0"/></GUILayout><compOutputs><compOutput><uid v="1354006539"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1354045888"/><connections><connection><identifier v="inputintensity"/><connRef v="1353861964"/><connRefOutput v="1353840228"/></connection><connection><identifier v="input1"/><connRef v="1354064338"/><connRefOutput v="1354064339"/></connection></connections><GUILayout><gpos v="-3120 -289.030182 0"/></GUILayout><compOutputs><compOutput><uid v="1354045889"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="directionalwarp"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="80"/></paramValue></parameter><parameter><name v="warpangle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="-0.055555556"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1354046030"/><connections><connection><identifier v="source"/><connRef v="1354045888"/><connRefOutput v="1354045889"/></connection><connection><identifier v="opacity"/><connRef v="1353840219"/><connRefOutput v="1353840211"/></connection><connection><identifier v="destination"/><connRef v="1354914437"/><connRefOutput v="1354914438"/></connection></connections><GUILayout><gpos v="-3024 -48 0"/></GUILayout><compOutputs><compOutput><uid v="1354046031"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.179999992"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1354064338"/><GUILayout><gpos v="-3280 -400 0"/></GUILayout><compOutputs><compOutput><uid v="1354064339"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///grunge_map_014?dependency=1354061253"/><parameters/><outputBridgings><outputBridging><uid v="1354064339"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1354076886"/><GUILayout><gpos v="-2848.84839 656 0"/></GUILayout><compOutputs><compOutput><uid v="1354064339"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///grunge_map_014?dependency=1354061253"/><parameters/><outputBridgings><outputBridging><uid v="1354064339"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1354076887"/><connections><connection><identifier v="source"/><connRef v="1354076888"/><connRefOutput v="1354045889"/></connection><connection><identifier v="opacity"/><connRef v="1353840210"/><connRefOutput v="1353840211"/></connection><connection><identifier v="destination"/><connRef v="1353840227"/><connRefOutput v="1353840228"/></connection></connections><GUILayout><gpos v="-2544 801.413818 0"/></GUILayout><compOutputs><compOutput><uid v="1354046031"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.149999991"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1354076888"/><connections><connection><identifier v="input1"/><connRef v="1354076886"/><connRefOutput v="1354064339"/></connection><connection><identifier v="inputintensity"/><connRef v="1353840210"/><connRefOutput v="1353840211"/></connection></connections><GUILayout><gpos v="-2704 688 0"/></GUILayout><compOutputs><compOutput><uid v="1354045889"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="directionalwarp"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="80"/></paramValue></parameter><parameter><name v="warpangle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="-0.055555556"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1354164204"/><connections><connection><identifier v="input"/><connRef v="1353872325"/><connRefOutput v="1353872326"/></connection></connections><GUILayout><gpos v="-512.885498 -144 0"/></GUILayout><compOutputs><compOutput><uid v="1353868305"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///hbao?dependency=1297162667"/><parameters><parameter><name v="height_depth"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.209999993"/></paramValue></parameter><parameter><name v="radius"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.099999994"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1353868305"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1354201418"/><connections><connection><identifier v="destination"/><connRef v="1354006538"/><connRefOutput v="1354006539"/></connection><connection><identifier v="opacity"/><connRef v="1354201449"/><connRefOutput v="1354201450"/></connection></connections><GUILayout><gpos v="-200.885498 -264 0"/></GUILayout><compOutputs><compOutput><uid v="1354201419"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1354201449"/><connections><connection><identifier v="input1"/><connRef v="1354164204"/><connRefOutput v="1353868305"/></connection></connections><GUILayout><gpos v="-384.885498 -144 0"/></GUILayout><compOutputs><compOutput><uid v="1354201450"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.30933851 0.30933851 0.30933851 0"/></paramValue></parameter><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="1 1 1 0"/></paramValue></parameter><parameter><name v="levelouthigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0 0 0 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1354573482"/><GUILayout><gpos v="-3632 624 0"/></GUILayout><compOutputs><compOutput><uid v="1354573483"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///polygon_2?dependency=1487918781"/><parameters><parameter><name v="Sides"/><relativeTo v="0"/><paramValue><constantValueInt32 v="19"/></paramValue></parameter><parameter><name v="Gradient"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.340000004"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1354573483"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1354573519"/><connections><connection><identifier v="Source"/><connRef v="1354573482"/><connRefOutput v="1354573483"/></connection></connections><GUILayout><gpos v="-3472 624 0"/></GUILayout><compOutputs><compOutput><uid v="1354573520"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///splatter?dependency=1487921414"/><parameters><parameter><name v="Pattern_size_width"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="265.399994"/></paramValue></parameter><parameter><name v="Pattern_size_height"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="535.549988"/></paramValue></parameter><parameter><name v="Lum_Var"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.140000001"/></paramValue></parameter><parameter><name v="Grid_Number"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="Rotation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="46.0699997"/></paramValue></parameter><parameter><name v="Disorder"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="51.1800003"/></paramValue></parameter><parameter><name v="Output_Min"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0399999991"/></paramValue></parameter><parameter><name v="PatternSize"/><relativeTo v="0"/><paramValue><constantValueInt32 v="10"/></paramValue></parameter><parameter><name v="Size_Var"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="53.5499992"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1354573520"/><identifier v="Splatter_Grayscale"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1354742781"/><GUILayout><gpos v="-4093.63428 -112 0"/></GUILayout><compOutputs><compOutput><uid v="1354573483"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///polygon_2?dependency=1487918781"/><parameters><parameter><name v="Sides"/><relativeTo v="0"/><paramValue><constantValueInt32 v="19"/></paramValue></parameter><parameter><name v="Gradient"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.340000004"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1354573483"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1354742782"/><connections><connection><identifier v="Source"/><connRef v="1354742781"/><connRefOutput v="1354573483"/></connection></connections><GUILayout><gpos v="-3933.63428 -112 501"/></GUILayout><compOutputs><compOutput><uid v="1354573520"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///splatter?dependency=1487921414"/><parameters><parameter><name v="Pattern_size_width"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="867.299988"/></paramValue></parameter><parameter><name v="Pattern_size_height"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="635.070007"/></paramValue></parameter><parameter><name v="Lum_Var"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.140000001"/></paramValue></parameter><parameter><name v="Grid_Number"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="Rotation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="138.199997"/></paramValue></parameter><parameter><name v="Disorder"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="51.1800003"/></paramValue></parameter><parameter><name v="Output_Min"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0399999991"/></paramValue></parameter><parameter><name v="PatternSize"/><relativeTo v="0"/><paramValue><constantValueInt32 v="10"/></paramValue></parameter><parameter><name v="Size_Var"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="53.5499992"/></paramValue></parameter><parameter><name v="Rotation_Var"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="73.3600006"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1354573520"/><identifier v="Splatter_Grayscale"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1354914437"/><connections><connection><identifier v="input1"/><connRef v="1353861964"/><connRefOutput v="1353840228"/></connection></connections><GUILayout><gpos v="-3175.76196 -80 0"/></GUILayout><compOutputs><compOutput><uid v="1354914438"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="curve"/><parameters/><paramsArrays><paramsArray><name v="curveluminance"/><uid v="1354916709"/><paramsArrayCells><paramsArrayCell><uid v="1354916710"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat2 v="0 0"/></paramValue></parameter><parameter><name v="left"/><paramValue><constantValueFloat2 v="0 0"/></paramValue></parameter><parameter><name v="isLeftBroken"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="right"/><paramValue><constantValueFloat2 v="0.100000001 0"/></paramValue></parameter><parameter><name v="isRightBroken"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="isLocked"/><paramValue><constantValueBool v="1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1354916711"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat2 v="1 1"/></paramValue></parameter><parameter><name v="left"/><paramValue><constantValueFloat2 v="0.899999976 1"/></paramValue></parameter><parameter><name v="isLeftBroken"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="right"/><paramValue><constantValueFloat2 v="1 1"/></paramValue></parameter><parameter><name v="isRightBroken"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="isLocked"/><paramValue><constantValueBool v="1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1354916712"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat2 v="0.620000005 0.533999979"/></paramValue></parameter><parameter><name v="left"/><paramValue><constantValueFloat2 v="0.479000002 0.521000028"/></paramValue></parameter><parameter><name v="isLeftBroken"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="right"/><paramValue><constantValueFloat2 v="0.760999978 0.546999991"/></paramValue></parameter><parameter><name v="isRightBroken"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="isLocked"/><paramValue><constantValueBool v="1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1354980123"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat2 v="0.324999988 0.192000002"/></paramValue></parameter><parameter><name v="left"/><paramValue><constantValueFloat2 v="0.247999996 0.0160000008"/></paramValue></parameter><parameter><name v="isLeftBroken"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="right"/><paramValue><constantValueFloat2 v="0.40200001 0.368000001"/></paramValue></parameter><parameter><name v="isRightBroken"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="isLocked"/><paramValue><constantValueBool v="1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1355048060"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat2 v="0.769999981 0.769999981"/></paramValue></parameter><parameter><name v="left"/><paramValue><constantValueFloat2 v="0.65200001 0.833000004"/></paramValue></parameter><parameter><name v="isLeftBroken"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="right"/><paramValue><constantValueFloat2 v="0.888000011 0.707000017"/></paramValue></parameter><parameter><name v="isRightBroken"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="isLocked"/><paramValue><constantValueBool v="1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1355404862"/><GUILayout><gpos v="-3120 112 0"/></GUILayout><compOutputs><compOutput><uid v="1355404863"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///polygon_2?dependency=1487918781"/><parameters><parameter><name v="Sides"/><relativeTo v="0"/><paramValue><constantValueInt32 v="32"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1355404863"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1355404867"/><connections><connection><identifier v="source"/><connRef v="1354046030"/><connRefOutput v="1354046031"/></connection><connection><identifier v="destination"/><connRef v="1355404862"/><connRefOutput v="1355404863"/></connection></connections><GUILayout><gpos v="-2896 112 0"/></GUILayout><compOutputs><compOutput><uid v="1355404868"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1355404927"/><connections><connection><identifier v="pattern_input"/><connRef v="1355404867"/><connRefOutput v="1355404868"/></connection></connections><GUILayout><gpos v="-2640 112 0"/></GUILayout><compOutputs><compOutput><uid v="1355404928"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1449399972"/><parameters><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="pattern_rotation_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.140000001"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.94999993"/></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.359999985"/></paramValue></parameter><parameter><name v="rotation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.460388899"/></paramValue></parameter><parameter><name v="rotation_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.50999999"/></paramValue></parameter><parameter><name v="mask_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.119999997"/></paramValue></parameter><parameter><name v="color_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.199999988"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1355404928"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1355545156"/><connections><connection><identifier v="Source"/><connRef v="1355545162"/><connRefOutput v="1355404863"/></connection></connections><GUILayout><gpos v="-2384 464 0"/></GUILayout><compOutputs><compOutput><uid v="1355545157"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///splatter?dependency=1487921414"/><parameters><parameter><name v="Pattern_size_width"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="554.5"/></paramValue></parameter><parameter><name v="Pattern_size_height"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="393.359985"/></paramValue></parameter><parameter><name v="Lum_Var"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.769999981"/></paramValue></parameter><parameter><name v="Disorder"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="100"/></paramValue></parameter><parameter><name v="Rotation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="127.959999"/></paramValue></parameter><parameter><name v="Grid_Number"/><relativeTo v="0"/><paramValue><constantValueInt32 v="5"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1355545157"/><identifier v="Splatter_Grayscale"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1355545162"/><GUILayout><gpos v="-2512 464 0"/></GUILayout><compOutputs><compOutput><uid v="1355404863"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///polygon_2?dependency=1487918781"/><parameters><parameter><name v="Sides"/><relativeTo v="0"/><paramValue><constantValueInt32 v="32"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1355404863"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1355545439"/><connections><connection><identifier v="Source"/><connRef v="1355545156"/><connRefOutput v="1355545157"/></connection></connections><GUILayout><gpos v="-2288 464 0"/></GUILayout><compOutputs><compOutput><uid v="1355545440"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1299236171"/><parameters/><outputBridgings><outputBridging><uid v="1355545440"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1355545471"/><connections><connection><identifier v="height_top"/><connRef v="1353840772"/><connRefOutput v="1353840773"/></connection><connection><identifier v="height_bottom"/><connRef v="1355545740"/><connRefOutput v="1355545741"/></connection></connections><GUILayout><gpos v="-1808 528 0"/></GUILayout><compOutputs><compOutput><uid v="1355545472"/><comptype v="2"/></compOutput><compOutput><uid v="1355545473"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///height_blend?dependency=1449623608"/><parameters><parameter><name v="height_position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1355545472"/><identifier v="blended_height"/></outputBridging><outputBridging><uid v="1355545473"/><identifier v="height_mask"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1355545740"/><connections><connection><identifier v="input"/><connRef v="1355545439"/><connRefOutput v="1355545440"/></connection></connections><GUILayout><gpos v="-2192 464 0"/></GUILayout><compOutputs><compOutput><uid v="1355545741"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_range?dependency=1353829477"/><parameters><parameter><name v="position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.639999986"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1355545741"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1355829087"/><connections><connection><identifier v="input1"/><connRef v="1354002917"/><connRefOutput v="1354002918"/></connection></connections><GUILayout><gpos v="-672.885498 -157.154205 0"/></GUILayout><compOutputs><compOutput><uid v="1355829088"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.612840474 0.612840474 0.612840474 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1355829575"/><connections><connection><identifier v="destination"/><connRef v="1354201418"/><connRefOutput v="1354201419"/></connection><connection><identifier v="source"/><connRef v="1355829585"/><connRefOutput v="1355829586"/></connection></connections><GUILayout><gpos v="-84.885498 -268 0"/></GUILayout><compOutputs><compOutput><uid v="1355829576"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="11"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.25999999"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1355829585"/><connections><connection><identifier v="input1"/><connRef v="1354005322"/><connRefOutput v="1354005323"/></connection></connections><GUILayout><gpos v="-192.885498 -413.154205 0"/></GUILayout><compOutputs><compOutput><uid v="1355829586"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1355953822"/><connections><connection><identifier v="Source"/><connRef v="1355404927"/><connRefOutput v="1355404928"/></connection><connection><identifier v="Effect"/><connRef v="1355999478"/><connRefOutput v="1355999479"/></connection></connections><GUILayout><gpos v="-2430.52417 272 0"/></GUILayout><compOutputs><compOutput><uid v="1355953823"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///slope_blur_grayscale?dependency=1449402040"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="32"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.449999988"/></paramValue></parameter><parameter><name v="mode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1355953823"/><identifier v="Slope_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1355953832"/><GUILayout><gpos v="-2931.66528 336 0"/></GUILayout><compOutputs><compOutput><uid v="1355953833"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///perlin_noise?dependency=1407332030"/><parameters><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueInt32 v="80"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1355953833"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1355969854"/><connections><connection><identifier v="input1"/><connRef v="1355953832"/><connRefOutput v="1355953833"/></connection></connections><GUILayout><gpos v="-2817.18774 336 0"/></GUILayout><compOutputs><compOutput><uid v="1355969855"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.659533083 0.659533083 0.659533083 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1355999478"/><connections><connection><identifier v="Source"/><connRef v="1355969854"/><connRefOutput v="1355969855"/></connection></connections><GUILayout><gpos v="-2704 336 0"/></GUILayout><compOutputs><compOutput><uid v="1355999479"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1299236171"/><parameters><parameter><name v="Quality"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.20999992"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1355999479"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1356068145"/><connections><connection><identifier v="destination"/><connRef v="1355404927"/><connRefOutput v="1355404928"/></connection><connection><identifier v="source"/><connRef v="1355953822"/><connRefOutput v="1355953823"/></connection></connections><GUILayout><gpos v="-2256 125.733231 0"/></GUILayout><compOutputs><compOutput><uid v="1356068146"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.229999989"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1356398309"/><connections><connection><identifier v="input1"/><connRef v="1355545471"/><connRefOutput v="1355545472"/></connection></connections><GUILayout><gpos v="-432 208 0"/></GUILayout><compOutputs><compOutput><uid v="1356398310"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.14202334 0.14202334 0.14202334 0"/></paramValue></parameter><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.527237356 0.527237356 0.527237356 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1356398348"/><connections><connection><identifier v="destination"/><connRef v="1356398309"/><connRefOutput v="1356398310"/></connection><connection><identifier v="source"/><connRef v="1353840831"/><connRefOutput v="1353840821"/></connection><connection><identifier v="opacity"/><connRef v="1356398363"/><connRefOutput v="1356398364"/></connection></connections><GUILayout><gpos v="-240 112 0"/></GUILayout><compOutputs><compOutput><uid v="1356398349"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1356398363"/><connections><connection><identifier v="input1"/><connRef v="1353872033"/><connRefOutput v="1353872034"/></connection></connections><GUILayout><gpos v="-432 319.265106 0"/></GUILayout><compOutputs><compOutput><uid v="1356398364"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.538910508 0.538910508 0.538910508 1"/></paramValue></parameter><parameter><name v="levelinlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.0525291823 0.0525291823 0.0525291823 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1491036014"/><GUILayout><gpos v="-2096 -432 0"/></GUILayout><compOutputs><compOutput><uid v="1491036015"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///dirt_4?dependency=1491035839"/><parameters/><outputBridgings><outputBridging><uid v="1491036015"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1491713390"/><connections><connection><identifier v="input1"/><connRef v="1491713391"/><connRefOutput v="1487921590"/></connection><connection><identifier v="inputgradient"/><connRef v="1491713396"/><connRefOutput v="1487922254"/></connection></connections><GUILayout><gpos v="-3169.21387 -896.722412 0"/></GUILayout><compOutputs><compOutput><uid v="1487921946"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="warp"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1491713391"/><connections><connection><identifier v="Source"/><connRef v="1491713392"/><connRefOutput v="1490129101"/></connection></connections><GUILayout><gpos v="-3361.21387 -896.722412 0"/></GUILayout><compOutputs><compOutput><uid v="1487921590"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///splatter?dependency=1487921414"/><parameters><parameter><name v="Pattern_size_width"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="412.160004"/></paramValue></parameter><parameter><name v="Pattern_size_height"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="229.729996"/></paramValue></parameter><parameter><name v="Rotation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="28.1299992"/></paramValue></parameter><parameter><name v="Disorder"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="5.69000006"/></paramValue></parameter><parameter><name v="PatternSize"/><relativeTo v="0"/><paramValue><constantValueInt32 v="12"/></paramValue></parameter><parameter><name v="Size_Var"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="42.0800018"/></paramValue></parameter><parameter><name v="Lum_Var"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.379999995"/></paramValue></parameter><parameter><name v="Grid_Number"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="Pan_Y"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="47.9699974"/></paramValue></parameter><parameter><name v="Pan_X"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="-26.8299999"/></paramValue></parameter><parameter><name v="Rotation_Var"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="188.440002"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487921590"/><identifier v="Splatter_Grayscale"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1491713392"/><GUILayout><gpos v="-3521.21387 -898.654297 0"/></GUILayout><compOutputs><compOutput><uid v="1490129101"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///bnw_spots_1?dependency=1490202924"/><parameters><parameter><name v="roughness"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.459999979"/></paramValue></parameter><parameter><name v="disorder"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.48999998"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1490129101"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1491713393"/><GUILayout><gpos v="-3137.21387 -688 0"/></GUILayout><compOutputs><compOutput><uid v="1487924312"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///clouds_2?dependency=1372403415"/><parameters><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="disorder"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.359999985"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487924312"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1491713394"/><connections><connection><identifier v="Source"/><connRef v="1491713398"/><connRefOutput v="1487924477"/></connection></connections><GUILayout><gpos v="-2721.21387 -880 0"/></GUILayout><compOutputs><compOutput><uid v="1487924634"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1299236171"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.13"/></paramValue></parameter><parameter><name v="Quality"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487924634"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1491713395"/><connections><connection><identifier v="destination"/><connRef v="1491713394"/><connRefOutput v="1487924634"/></connection></connections><GUILayout><gpos v="-2305.21387 -880 0"/></GUILayout><compOutputs><compOutput><uid v="1487924726"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="9"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.5"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1491713396"/><GUILayout><gpos v="-3379.35718 -722.422852 0"/></GUILayout><compOutputs><compOutput><uid v="1487922254"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///gaussian_1?dependency=1487922179"/><parameters><parameter><name v="Tiling"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487922254"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1491713397"/><connections><connection><identifier v="input1"/><connRef v="1491713390"/><connRefOutput v="1487921946"/></connection><connection><identifier v="inputgradient"/><connRef v="1491713393"/><connRefOutput v="1487924312"/></connection></connections><GUILayout><gpos v="-3024.40015 -880 0"/></GUILayout><compOutputs><compOutput><uid v="1487923791"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="warp"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.239999995"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1491713398"/><connections><connection><identifier v="Input_1"/><connRef v="1491713397"/><connRefOutput v="1487923791"/></connection></connections><GUILayout><gpos v="-2881.21387 -880 0"/></GUILayout><compOutputs><compOutput><uid v="1487924477"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1149349207"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.399999976"/></paramValue></parameter><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.209999993"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487924477"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1492386683"/><connections><connection><identifier v="Input_1"/><connRef v="1353872325"/><connRefOutput v="1353872326"/></connection></connections><GUILayout><gpos v="-80.1131592 520.820068 0"/></GUILayout><compOutputs><compOutput><uid v="1492386684"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1149349207"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.349999994"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1492386684"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode></compNodes><baseParameters><parameter><name v="outputsize"/><relativeTo v="0"/><paramValue><constantValueInt2 v="10 10"/></paramValue></parameter><parameter><name v="format"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter></baseParameters><options><option><name v="export/fromGraph/autoExport"/><value v="false"/></option><option><name v="export/fromGraph/destination"/><value v="D:/Irvin/2016/Pictures/390 Website/speed_modeling/3dex_stylized_groundroots_01/textures"/></option><option><name v="export/fromGraph/extension"/><value v="tga"/></option><option><name v="export/fromGraph/outputs/AO"/><value v="true"/></option><option><name v="export/fromGraph/outputs/basecolor"/><value v="true"/></option><option><name v="export/fromGraph/outputs/height"/><value v="true"/></option><option><name v="export/fromGraph/outputs/metallic"/><value v="true"/></option><option><name v="export/fromGraph/outputs/normal"/><value v="true"/></option><option><name v="export/fromGraph/outputs/roughness"/><value v="true"/></option><option><name v="export/fromGraph/pattern"/><value v="$(graph)_$(identifier)"/></option></options><root><rootOutputs><rootOutput><output v="1213284336"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1213284338"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1213284340"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1213284342"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1279137031"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1353866730"/><format v="0"/><usertag v=""/></rootOutput></rootOutputs></root></graph></content></package>
