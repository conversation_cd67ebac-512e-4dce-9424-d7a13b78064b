using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using Stylo.Cadance.FMOD;
using SonicBloom.Koreo.Players.FMODStudio;

namespace Stylo.Cadance.Editor
{
    /// <summary>
    /// Utility for migrating from FMODEventDescriptionVisor to FMODCadanceVisor.
    /// </summary>
    public class KoreographyMigrationUtility : EditorWindow
    {
        private Vector2 scrollPosition;
        private List<FMODEventDescriptionVisor> foundVisors = new List<FMODEventDescriptionVisor>();
        private bool scanComplete = false;

        [MenuItem("Stylo/Cadance/Migration/Koreography Migration Utility")]
        public static void ShowWindow()
        {
            var window = GetWindow<KoreographyMigrationUtility>("Koreography Migration");
            window.minSize = new Vector2(600, 400);
            window.Show();
        }

        private void OnGUI()
        {
            EditorGUILayout.LabelField("Koreography Migration Utility", EditorStyles.boldLabel);
            EditorGUILayout.Space();

            EditorGUILayout.HelpBox(
                "This utility helps migrate from FMODEventDescriptionVisor (Koreographer) to FMODCadanceVisor (Cadance). " +
                "It will scan your project for existing FMODEventDescriptionVisor components and help you migrate them.",
                MessageType.Info);

            EditorGUILayout.Space();

            if (GUILayout.Button("Scan Project for FMODEventDescriptionVisor Components"))
            {
                ScanForVisors();
            }

            EditorGUILayout.Space();

            if (scanComplete)
            {
                EditorGUILayout.LabelField($"Found {foundVisors.Count} FMODEventDescriptionVisor components:", EditorStyles.boldLabel);

                if (foundVisors.Count == 0)
                {
                    EditorGUILayout.HelpBox("No FMODEventDescriptionVisor components found in the project.", MessageType.Info);
                }
                else
                {
                    scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);

                    foreach (var visor in foundVisors)
                    {
                        if (visor == null) continue;

                        EditorGUILayout.BeginVertical("box");

                        EditorGUILayout.LabelField($"GameObject: {visor.gameObject.name}", EditorStyles.boldLabel);
                        EditorGUILayout.LabelField($"Scene: {visor.gameObject.scene.name}");

                        // Show current configuration
                        var serializedVisor = new SerializedObject(visor);
                        var koreographedEvents = serializedVisor.FindProperty("koreographedEvents");

                        if (koreographedEvents != null && koreographedEvents.arraySize > 0)
                        {
                            EditorGUILayout.LabelField($"Configured Events: {koreographedEvents.arraySize}");

                            EditorGUI.indentLevel++;
                            for (int i = 0; i < koreographedEvents.arraySize; i++)
                            {
                                var eventPair = koreographedEvents.GetArrayElementAtIndex(i);
                                var eventRef = eventPair.FindPropertyRelative("Event");
                                var koreographySet = eventPair.FindPropertyRelative("koreographySet");

                                if (eventRef != null && koreographySet != null)
                                {
                                    string eventPath = GetEventReferencePath(eventRef);
                                    string setName = koreographySet.objectReferenceValue?.name ?? "None";
                                    EditorGUILayout.LabelField($"• {eventPath} → {setName}");
                                }
                            }
                            EditorGUI.indentLevel--;
                        }
                        else
                        {
                            EditorGUILayout.LabelField("No events configured");
                        }

                        EditorGUILayout.Space();

                        EditorGUILayout.BeginHorizontal();

                        if (GUILayout.Button("Select GameObject"))
                        {
                            Selection.activeGameObject = visor.gameObject;
                            EditorGUIUtility.PingObject(visor.gameObject);
                        }

                        if (GUILayout.Button("Migrate to FMODCadanceVisor"))
                        {
                            MigrateVisor(visor);
                        }

                        EditorGUILayout.EndHorizontal();

                        EditorGUILayout.EndVertical();
                        EditorGUILayout.Space();
                    }

                    EditorGUILayout.EndScrollView();

                    EditorGUILayout.Space();

                    if (GUILayout.Button("Migrate All Found Visors"))
                    {
                        MigrateAllVisors();
                    }
                }
            }
        }

        private void ScanForVisors()
        {
            foundVisors.Clear();

            // Find all FMODEventDescriptionVisor components in the project
            var allVisors = FindObjectsOfType<FMODEventDescriptionVisor>(true);
            foundVisors.AddRange(allVisors);

            scanComplete = true;
            Debug.Log($"[KoreographyMigrationUtility] Found {foundVisors.Count} FMODEventDescriptionVisor components");
        }

        private void MigrateVisor(FMODEventDescriptionVisor oldVisor)
        {
            if (oldVisor == null) return;

            try
            {
                GameObject go = oldVisor.gameObject;

                // Add FMODCadanceVisor component
                var newVisor = go.AddComponent<FMODCadanceVisor>();

                // Copy configuration
                var serializedOldVisor = new SerializedObject(oldVisor);
                var serializedNewVisor = new SerializedObject(newVisor);

                // Copy target Koreographer to target Cadance
                var targetKoreographer = serializedOldVisor.FindProperty("targetKoreographer");
                if (targetKoreographer != null && targetKoreographer.objectReferenceValue != null)
                {
                    // Find or create Cadance component
                    var koreographerGO = ((Component)targetKoreographer.objectReferenceValue).gameObject;
                    var cadanceComponent = koreographerGO.GetComponent<Cadance>();
                    if (cadanceComponent == null)
                    {
                        cadanceComponent = koreographerGO.AddComponent<Cadance>();
                    }

                    var targetCadance = serializedNewVisor.FindProperty("targetCadance");
                    targetCadance.objectReferenceValue = cadanceComponent;
                }

                // Copy isMusicPlayer setting
                var oldIsMusicPlayer = serializedOldVisor.FindProperty("isMusicPlayer");
                var newIsMusicPlayer = serializedNewVisor.FindProperty("isMusicPlayer");
                if (oldIsMusicPlayer != null && newIsMusicPlayer != null)
                {
                    newIsMusicPlayer.boolValue = oldIsMusicPlayer.boolValue;
                }

                // Copy events
                var oldEvents = serializedOldVisor.FindProperty("koreographedEvents");
                var newEvents = serializedNewVisor.FindProperty("cadancedEvents");

                if (oldEvents != null && newEvents != null)
                {
                    newEvents.arraySize = oldEvents.arraySize;

                    for (int i = 0; i < oldEvents.arraySize; i++)
                    {
                        var oldEvent = oldEvents.GetArrayElementAtIndex(i);
                        var newEvent = newEvents.GetArrayElementAtIndex(i);

                        // Copy Event reference
                        var oldEventRef = oldEvent.FindPropertyRelative("Event");
                        var newEventRef = newEvent.FindPropertyRelative("Event");
                        if (oldEventRef != null && newEventRef != null)
                        {
                            newEventRef.FindPropertyRelative("Guid").stringValue = oldEventRef.FindPropertyRelative("Guid").stringValue;
                            newEventRef.FindPropertyRelative("Path").stringValue = oldEventRef.FindPropertyRelative("Path").stringValue;
                        }

                        // Copy FMODKoreographySet reference directly
                        var oldKoreographySet = oldEvent.FindPropertyRelative("koreographySet");
                        var newKoreographySet = newEvent.FindPropertyRelative("koreographySet");
                        if (oldKoreographySet != null && newKoreographySet != null)
                        {
                            newKoreographySet.objectReferenceValue = oldKoreographySet.objectReferenceValue;
                        }
                    }
                }

                serializedNewVisor.ApplyModifiedProperties();

                // Mark old visor for removal (but don't remove immediately to allow undo)
                oldVisor.enabled = false;

                Debug.Log($"[KoreographyMigrationUtility] Successfully migrated FMODEventDescriptionVisor on '{go.name}' to FMODCadanceVisor");

                EditorUtility.SetDirty(go);
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[KoreographyMigrationUtility] Failed to migrate visor on '{oldVisor.gameObject.name}': {ex.Message}");
            }
        }

        private void MigrateAllVisors()
        {
            int migratedCount = 0;
            foreach (var visor in foundVisors)
            {
                if (visor != null)
                {
                    MigrateVisor(visor);
                    migratedCount++;
                }
            }

            Debug.Log($"[KoreographyMigrationUtility] Successfully migrated {migratedCount} FMODEventDescriptionVisor components");

            // Refresh the scan
            ScanForVisors();
        }

        private string GetEventReferencePath(SerializedProperty eventRef)
        {
            if (eventRef == null) return "None";

            var pathProperty = eventRef.FindPropertyRelative("Path");
            return pathProperty?.stringValue ?? "Unknown";
        }
    }
}
