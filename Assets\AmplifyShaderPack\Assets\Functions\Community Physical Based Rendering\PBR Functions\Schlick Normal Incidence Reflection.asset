%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: Schlick Normal Incidence Reflection
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor v1.9.4.4\n// Available at the
    Unity Asset Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=19404\nNode;AmplifyShaderEditor.CommentaryNode;70;-2816,0;Inherit;False;2052.194;719.4944;Math;17;44;104;33;105;32;36;230;175;229;228;328;332;333;334;335;336;0;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;237;-3931.447,0;Inherit;False;1036.67;769.1219;Dot
    Products;13;265;286;287;288;301;303;302;297;295;294;337;338;339;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;228;-2784,128;Inherit;False;197.2654;135.1136;NdotL;1;107;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;120;-2816,-368;Inherit;False;721.8379;333.4279;Schlick
    Normal Incidence Reflection Node;1;119;;0.0754717,0.0754717,0.0754717,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;229;-2784,272;Inherit;False;198.2651;139.4407;NdotV;1;106;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;230;-2512,384;Inherit;False;202.9382;139.1137;LdotH;1;108;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;328;-2368,128;Inherit;False;504.0295;233.3165;Schlick
    Fresnel Approx.;3;331;330;329;;0,0,0,0.5019608;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;28;-2816,-544;Inherit;False;576.1084;143.6338;Credits;;0,0,0,0.5019608;Method
    by Jordan Stevens$Link: https://www.jordanstevenstechart.com/physically-based-rendering$$Node
    Made by Tiber Legacy$Special Thanks to Dawie;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;119;-2494.825,-304;Inherit;False;353.5679;189.8585;;;0,0,0,1;;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;175;-2752,528;Inherit;False;220.7993;104.6105;Roughness;;0,0,0,1;;0;0\nNode;AmplifyShaderEditor.DynamicAppendNode;44;-2544,176;Inherit;False;FLOAT2;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.FunctionInput;294;-3584,176;Inherit;False;NdotV;1;3;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;295;-3584,400;Inherit;False;NdotL;1;1;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;297;-3584,624;Inherit;False;LdotH;1;2;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;287;-3136,320;Inherit;True;NdotL;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;286;-3136,96;Inherit;True;NdotV;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;265;-3840,96;Inherit;False;Normal;3;0;False;1;0;FLOAT3;0,0,1;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitch;302;-3424,320;Inherit;False;Option;False;0;2;1;In
    0;In 1;Instance;301;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;303;-3424,544;Inherit;False;Option;False;0;2;1;In
    0;In 1;Instance;301;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;329;-2352,176;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SaturateNode;330;-2176,176;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.GetLocalVarNode;106;-2768,320;Inherit;False;286;NdotV;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;107;-2768,176;Inherit;False;287;NdotL;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;288;-3136,544;Inherit;True;LdotH;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;331;-2016,176;Inherit;False;5;5;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;2;FLOAT2;0,0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;32;-2256,480;Inherit;False;4;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;36;-1920,480;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;104;-2448,608;Inherit;False;Constant;_Two;Two;0;0;Create;True;0;0;0;False;0;False;2;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;33;-2480,528;Inherit;False;Roughness;1;4;False;1;0;FLOAT;0.5;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;105;-2080,544;Inherit;False;Constant;_Half5;Half.5;0;0;Create;True;0;0;0;False;0;False;0.5;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;108;-2496,432;Inherit;False;288;LdotH;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;333;-1776,256;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.OneMinusNode;332;-1776,176;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;334;-1600,176;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.BreakToComponentsNode;335;-1472,176;Inherit;False;FLOAT2;1;0;FLOAT2;0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;336;-1344,176;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionNode;337;-3648,96;Inherit;False;NdotV;-1;;4;65be4ec269a534f4e8867af2f999aaa9;4,27,1,377,0,452,0,451,0;3;230;FLOAT3;0,0,1;False;443;FLOAT3;0,0,0;False;415;FLOAT;1E-37;False;1;FLOAT;23\nNode;AmplifyShaderEditor.FunctionNode;338;-3648,320;Inherit;False;NdotL;-1;;5;6d7ee1aa8c79587448475fa2b37affc1;5,27,1,381,0,460,0,461,0,463,0;4;230;FLOAT3;0,0,1;False;462;FLOAT3;0,0,0;False;454;FLOAT3;0,0,0;False;416;FLOAT;1E-37;False;1;FLOAT;111\nNode;AmplifyShaderEditor.FunctionNode;339;-3648,544;Inherit;False;LdotH;-1;;6;0364555185933b049a6a96d60ccefd1a;4,385,0,460,0,451,0,461,0;4;443;FLOAT3;0,0,0;False;462;FLOAT3;0,0,0;False;454;FLOAT3;0,0,0;False;419;FLOAT;1E-37;False;1;FLOAT;112\nNode;AmplifyShaderEditor.FunctionSwitch;301;-3424,96;Inherit;False;Custom
    Dot Products;True;0;2;1;In 0;In 1;Object;-1;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;0;-1200,176;Inherit;True;True;-1;Out;0;True;1;0;FLOAT;0;False;1;FLOAT;0\nWireConnection;44;0;107;0\nWireConnection;44;1;106;0\nWireConnection;287;0;302;0\nWireConnection;286;0;301;0\nWireConnection;302;0;338;111\nWireConnection;302;1;295;0\nWireConnection;303;0;339;112\nWireConnection;303;1;297;0\nWireConnection;329;0;44;0\nWireConnection;330;0;329;0\nWireConnection;288;0;303;0\nWireConnection;331;0;330;0\nWireConnection;331;1;330;0\nWireConnection;331;2;330;0\nWireConnection;331;3;330;0\nWireConnection;331;4;330;0\nWireConnection;32;0;108;0\nWireConnection;32;1;108;0\nWireConnection;32;2;33;0\nWireConnection;32;3;104;0\nWireConnection;36;0;32;0\nWireConnection;36;1;105;0\nWireConnection;333;0;331;0\nWireConnection;333;1;36;0\nWireConnection;332;0;331;0\nWireConnection;334;0;332;0\nWireConnection;334;1;333;0\nWireConnection;335;0;334;0\nWireConnection;336;0;335;0\nWireConnection;336;1;335;1\nWireConnection;337;230;265;0\nWireConnection;338;230;265;0\nWireConnection;301;0;337;23\nWireConnection;301;1;294;0\nWireConnection;0;0;336;0\nASEEND*/\n//CHKSM=B81234580A19BA923B878B2DA2B7B24F5ADAAE12"
  m_functionName: 
  m_description: 'Schlick Normal Incidence Reflection Node

'
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 0
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_customEdited: 0
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 0
  m_headerStyle: 2
  m_headerColor: {r: 0.7529412, g: 0.6784314, b: 0, a: 1}
  m_customNodeCategory: Amplify Shader Pack
  m_previewPosition: 0
  m_hidden: 0
  m_url: 
