using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.Linq;
using SonicBloom.Koreo;
using Stylo.Cadance;

namespace Stylo.Cadance.Editor
{
    /// <summary>
    /// Tool to compare event data between original Koreography and converted CadanceAsset files.
    /// </summary>
    public class EventDataComparisonTool : EditorWindow
    {
        private Koreography originalKoreography;
        private CadanceAsset convertedCadance;
        private Vector2 scrollPosition;
        private List<EventComparisonResult> comparisonResults = new List<EventComparisonResult>();
        private bool comparisonComplete = false;

        [MenuItem("Stylo/Cadance/Tools/Event Data Comparison Tool", false, 103)]
        public static void ShowWindow()
        {
            GetWindow<EventDataComparisonTool>("Event Data Comparison Tool");
        }

        private void OnGUI()
        {
            EditorGUILayout.LabelField("Event Data Comparison Tool", EditorStyles.boldLabel);
            EditorGUILayout.HelpBox("This tool compares event data between original Koreography and converted CadanceAsset files to verify 100% data preservation.", MessageType.Info);

            EditorGUILayout.Space();

            // Asset selection
            EditorGUILayout.LabelField("Asset Selection", EditorStyles.boldLabel);
            originalKoreography = (Koreography)EditorGUILayout.ObjectField("Original Koreography", originalKoreography, typeof(Koreography), false);
            convertedCadance = (CadanceAsset)EditorGUILayout.ObjectField("Converted CadanceAsset", convertedCadance, typeof(CadanceAsset), false);

            EditorGUILayout.Space();

            EditorGUI.BeginDisabledGroup(originalKoreography == null || convertedCadance == null);
            if (GUILayout.Button("Compare Event Data"))
            {
                CompareEventData();
            }
            EditorGUI.EndDisabledGroup();

            EditorGUILayout.Space();

            if (comparisonComplete)
            {
                DrawComparisonResults();
            }
        }

        private void CompareEventData()
        {
            comparisonResults.Clear();
            comparisonComplete = false;

            Debug.Log($"[Event Comparison] Comparing {originalKoreography.name} with {convertedCadance.name}");

            // Compare basic properties
            var basicComparison = new EventComparisonResult
            {
                category = "Basic Properties",
                trackName = "N/A"
            };

            if (originalKoreography.SourceClipName != convertedCadance.SourceClipName)
            {
                basicComparison.issues.Add($"Source Clip Name mismatch: '{originalKoreography.SourceClipName}' vs '{convertedCadance.SourceClipName}'");
            }

            if (originalKoreography.SampleRate != convertedCadance.SampleRate)
            {
                basicComparison.issues.Add($"Sample Rate mismatch: {originalKoreography.SampleRate} vs {convertedCadance.SampleRate}");
            }

            basicComparison.status = basicComparison.issues.Count == 0 ? ComparisonStatus.Perfect : ComparisonStatus.Issues;
            comparisonResults.Add(basicComparison);

            // Compare tracks
            string[] originalEventIDs = originalKoreography.GetEventIDs();

            foreach (string eventID in originalEventIDs)
            {
                var originalTrack = originalKoreography.GetTrackByID(eventID);
                var convertedTrack = convertedCadance.GetTrackByEventID(eventID);

                var trackComparison = new EventComparisonResult
                {
                    category = "Track",
                    trackName = eventID
                };

                if (convertedTrack == null)
                {
                    trackComparison.status = ComparisonStatus.Missing;
                    trackComparison.issues.Add("Track not found in converted asset");
                }
                else
                {
                    CompareTrackEvents(originalTrack, convertedTrack, trackComparison);
                }

                comparisonResults.Add(trackComparison);
            }

            // Check for extra tracks in converted asset
            foreach (var convertedTrack in convertedCadance.Tracks)
            {
                if (convertedTrack != null && !originalEventIDs.Contains(convertedTrack.EventID))
                {
                    var extraTrackComparison = new EventComparisonResult
                    {
                        category = "Extra Track",
                        trackName = convertedTrack.EventID,
                        status = ComparisonStatus.Extra,
                        issues = { "Track exists in converted asset but not in original" }
                    };
                    comparisonResults.Add(extraTrackComparison);
                }
            }

            comparisonComplete = true;

            // Summary
            int perfectTracks = comparisonResults.Count(r => r.status == ComparisonStatus.Perfect);
            int totalTracks = comparisonResults.Count;
            Debug.Log($"[Event Comparison] Complete. {perfectTracks}/{totalTracks} tracks have perfect data preservation");
        }

        private void CompareTrackEvents(KoreographyTrackBase originalTrack, CadanceTrackBase convertedTrack, EventComparisonResult result)
        {
            var originalEvents = originalTrack.GetAllEvents();
            int originalEventCount = originalEvents.Count;
            int convertedEventCount = convertedTrack.EventCount;

            result.originalEventCount = originalEventCount;
            result.convertedEventCount = convertedEventCount;

            if (originalEventCount != convertedEventCount)
            {
                result.issues.Add($"Event count mismatch: {originalEventCount} vs {convertedEventCount}");
                result.status = ComparisonStatus.Issues;
                return;
            }

            // Compare individual events
            int perfectEvents = 0;
            int eventIssues = 0;

            for (int i = 0; i < originalEventCount && i < convertedEventCount; i++)
            {
                var originalEvent = originalEvents[i];
                var convertedEvent = convertedTrack.GetEventAtIndex(i);

                bool eventPerfect = true;

                // Compare timing
                if (originalEvent.StartSample != convertedEvent.StartSample)
                {
                    result.issues.Add($"Event {i}: StartSample mismatch ({originalEvent.StartSample} vs {convertedEvent.StartSample})");
                    eventPerfect = false;
                }

                if (originalEvent.EndSample != convertedEvent.EndSample)
                {
                    result.issues.Add($"Event {i}: EndSample mismatch ({originalEvent.EndSample} vs {convertedEvent.EndSample})");
                    eventPerfect = false;
                }

                // Compare payload
                if ((originalEvent.Payload != null) != (convertedEvent.Payload != null))
                {
                    result.issues.Add($"Event {i}: Payload presence mismatch");
                    eventPerfect = false;
                }
                else if (originalEvent.Payload != null && convertedEvent.Payload != null)
                {
                    // Compare payload values (this would need to be expanded based on payload types)
                    if (!ComparePayloads(originalEvent, convertedEvent))
                    {
                        result.issues.Add($"Event {i}: Payload value mismatch");
                        eventPerfect = false;
                    }
                }

                if (eventPerfect)
                {
                    perfectEvents++;
                }
                else
                {
                    eventIssues++;
                }
            }

            result.perfectEvents = perfectEvents;
            result.eventIssues = eventIssues;

            if (eventIssues == 0 && result.issues.Count == 0)
            {
                result.status = ComparisonStatus.Perfect;
            }
            else if (eventIssues > 0)
            {
                result.status = ComparisonStatus.Issues;
            }
        }

        private bool ComparePayloads(KoreographyEvent originalEvent, CadanceEvent convertedEvent)
        {
            // This is a simplified comparison - would need to be expanded for all payload types
            try
            {
                // For now, just compare string representations
                string originalPayloadStr = originalEvent.Payload?.ToString();
                string convertedPayloadStr = convertedEvent.Payload?.ToString();

                return originalPayloadStr == convertedPayloadStr;
            }
            catch
            {
                // If we can't compare, assume they're different
                return false;
            }
        }

        private void DrawComparisonResults()
        {
            EditorGUILayout.LabelField("Comparison Results", EditorStyles.boldLabel);

            // Summary
            int perfectCount = comparisonResults.Count(r => r.status == ComparisonStatus.Perfect);
            int issuesCount = comparisonResults.Count(r => r.status == ComparisonStatus.Issues);
            int missingCount = comparisonResults.Count(r => r.status == ComparisonStatus.Missing);
            int extraCount = comparisonResults.Count(r => r.status == ComparisonStatus.Extra);

            EditorGUILayout.LabelField($"Perfect: {perfectCount}, Issues: {issuesCount}, Missing: {missingCount}, Extra: {extraCount}", EditorStyles.boldLabel);

            EditorGUILayout.Space();

            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);

            foreach (var result in comparisonResults)
            {
                DrawComparisonResult(result);
            }

            EditorGUILayout.EndScrollView();

            EditorGUILayout.Space();

            if (GUILayout.Button("Export Results to Console"))
            {
                ExportResultsToConsole();
            }
        }

        private void DrawComparisonResult(EventComparisonResult result)
        {
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);

            string statusIcon = GetStatusIcon(result.status);
            EditorGUILayout.LabelField($"{statusIcon} {result.category}: {result.trackName}", EditorStyles.boldLabel);

            if (result.originalEventCount > 0 || result.convertedEventCount > 0)
            {
                EditorGUILayout.LabelField($"Events: {result.originalEventCount} → {result.convertedEventCount}", EditorStyles.miniLabel);

                if (result.perfectEvents > 0 || result.eventIssues > 0)
                {
                    EditorGUILayout.LabelField($"Perfect Events: {result.perfectEvents}, Issues: {result.eventIssues}", EditorStyles.miniLabel);
                }
            }

            if (result.issues.Count > 0)
            {
                EditorGUILayout.LabelField("Issues:", EditorStyles.miniLabel);
                foreach (string issue in result.issues)
                {
                    EditorGUILayout.LabelField($"  • {issue}", EditorStyles.miniLabel);
                }
            }

            EditorGUILayout.EndVertical();
            EditorGUILayout.Space();
        }

        private string GetStatusIcon(ComparisonStatus status)
        {
            switch (status)
            {
                case ComparisonStatus.Perfect: return "✅";
                case ComparisonStatus.Issues: return "⚠️";
                case ComparisonStatus.Missing: return "❌";
                case ComparisonStatus.Extra: return "➕";
                default: return "?";
            }
        }

        private void ExportResultsToConsole()
        {
            Debug.Log("=== EVENT DATA COMPARISON RESULTS ===");
            Debug.Log($"Original: {originalKoreography.name}");
            Debug.Log($"Converted: {convertedCadance.name}");
            Debug.Log("");

            foreach (var result in comparisonResults)
            {
                Debug.Log($"{GetStatusIcon(result.status)} {result.category}: {result.trackName}");
                if (result.originalEventCount > 0 || result.convertedEventCount > 0)
                {
                    Debug.Log($"  Events: {result.originalEventCount} → {result.convertedEventCount}");
                }
                foreach (string issue in result.issues)
                {
                    Debug.Log($"  Issue: {issue}");
                }
            }

            // Summary statistics
            int perfectCount = comparisonResults.Count(r => r.status == ComparisonStatus.Perfect);
            int totalCount = comparisonResults.Count;
            float successRate = totalCount > 0 ? (float)perfectCount / totalCount * 100f : 0f;

            Debug.Log("");
            Debug.Log($"=== SUMMARY ===");
            Debug.Log($"Perfect Preservation: {perfectCount}/{totalCount} ({successRate:F1}%)");
            Debug.Log($"Data Preservation Quality: {(successRate >= 95f ? "EXCELLENT" : successRate >= 80f ? "GOOD" : "NEEDS IMPROVEMENT")}");
        }
    }

    public enum ComparisonStatus
    {
        Perfect,
        Issues,
        Missing,
        Extra
    }

    public class EventComparisonResult
    {
        public string category;
        public string trackName;
        public ComparisonStatus status;
        public List<string> issues = new List<string>();
        public int originalEventCount;
        public int convertedEventCount;
        public int perfectEvents;
        public int eventIssues;
    }
}
