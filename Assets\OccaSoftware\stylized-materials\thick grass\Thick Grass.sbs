<?xml version="1.0" encoding="UTF-8"?><package><identifier v="Unsaved Package"/><formatVersion v="1.1.0.202201"/><updaterVersion v="1.1.0.202201"/><fileUID v="{2db3f63d-6eb2-4883-ad8f-6a069473405a}"/><versionUID v="0"/><dependencies><dependency><filename v="sbs://pattern_shape.sbs"/><uid v="1311189026"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://trapezoid_transform.sbs"/><uid v="1486830014"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://gradient_linear_1.sbs"/><uid v="1335474237"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_gaussian_noise.sbs"/><uid v="1486875707"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://pattern_tile_sampler.sbs"/><uid v="1486875900"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://histogram_scan.sbs"/><uid v="1149349207"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://pbr_base_material.sbs"/><uid v="1482592834"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://hbao_2.sbs"/><uid v="1297162667"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency></dependencies><content><graph><identifier v="Thick_Grass"/><uid v="1486905551"/><graphtype v="material"/><graphOutputs><graphoutput><identifier v="basecolor"/><uid v="1482724528"/><attributes><label v="Base Color"/></attributes><usages><usage><components v="RGBA"/><name v="baseColor"/></usage></usages><group v="Material"/></graphoutput><graphoutput><identifier v="normal"/><uid v="1482724531"/><attributes><label v="Normal"/></attributes><usages><usage><components v="RGBA"/><name v="normal"/></usage></usages><group v="Material"/></graphoutput><graphoutput><identifier v="roughness"/><uid v="1482724534"/><attributes><label v="Roughness"/></attributes><usages><usage><components v="RGBA"/><name v="roughness"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput><graphoutput><identifier v="height"/><uid v="1482724540"/><attributes><label v="Height"/></attributes><usages><usage><components v="RGBA"/><name v="height"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput><graphoutput><identifier v="ambientOcclusion"/><uid v="1482724543"/><attributes><label v="Ambient Occlusion"/></attributes><usages><usage><components v="RGBA"/><name v="ambientOcclusion"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput></graphOutputs><compNodes><compNode><uid v="1359211383"/><GUILayout><gpos v="-1104 29.6147766 0"/></GUILayout><compOutputs><compOutput><uid v="1359211384"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1482709065"/><connRefOutput v="1482709066"/></connection></connections><compImplementation><compFilter><filter v="normal"/><parameters><parameter><name v="input2alpha"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="3"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1482576332"/><GUILayout><gpos v="-2607.86035 -60.6146431 0"/></GUILayout><compOutputs><compOutput><uid v="1482576333"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///shape?dependency=1311189026"/><parameters><parameter><name v="Pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="16"/></paramValue></parameter><parameter><name v="Size"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.74000001"/></paramValue></parameter><parameter><name v="Pattern_Specific"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.829999983"/></paramValue></parameter><parameter><name v="Size_xy"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.199999988 1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1482576333"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1482590231"/><GUILayout><gpos v="-2353.5769 -61.0516701 0"/></GUILayout><compOutputs><compOutput><uid v="1482590232"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1482591427"/><connRefOutput v="1482591428"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.149234697 0.149234697 0.149234697 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1482590704"/><GUILayout><gpos v="-2224 -61.0516701 0"/></GUILayout><compOutputs><compOutput><uid v="1482590705"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="source"/><connRef v="1482590231"/><connRefOutput v="1482590232"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="maskrectangle"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.199999988 0.779999971 0 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1482590974"/><GUILayout><gpos v="-2224.21118 66.9483337 0"/></GUILayout><compOutputs><compOutput><uid v="1482590975"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="Input_1"/><connRef v="1482591040"/><connRefOutput v="1482591041"/></connection></connections><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1149349207"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.610000014"/></paramValue></parameter><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0700000003"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1482590975"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1482591040"/><GUILayout><gpos v="-2352.21118 66.9483337 0"/></GUILayout><compOutputs><compOutput><uid v="1482591041"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///gradient_linear_1?dependency=1335474237"/><parameters><parameter><name v="rotation"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1482591041"/><identifier v="Simple_Gradient"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1482591087"/><GUILayout><gpos v="-2064.21118 -61.0516663 0"/></GUILayout><compOutputs><compOutput><uid v="1482591088"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="destination"/><connRef v="1482590704"/><connRefOutput v="1482590705"/></connection><connection><identifier v="source"/><connRef v="1482590974"/><connRefOutput v="1482590975"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="maskrectangle"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0 1 0 0"/></paramValue></parameter><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1482591427"/><GUILayout><gpos v="-2482.37451 -61.0516701 0"/></GUILayout><compOutputs><compOutput><uid v="1482591428"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input"/><connRef v="1482576332"/><connRefOutput v="1482576333"/></connection></connections><compImplementation><compInstance><path v="pkg:///trapezoid_transform_grayscale?dependency=1486830014"/><parameters><parameter><name v="top_stretch"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.620000005"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1482591428"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1482593502"/><GUILayout><gpos v="-912 144 0"/></GUILayout><compOutputs><compOutput><uid v="1482593503"/><comptype v="1"/></compOutput><compOutput><uid v="1482593504"/><comptype v="1"/></compOutput><compOutput><uid v="1482593505"/><comptype v="1"/></compOutput><compOutput><uid v="1482593506"/><comptype v="1"/></compOutput><compOutput><uid v="1482593507"/><comptype v="2"/></compOutput><compOutput><uid v="1482593508"/><comptype v="2"/></compOutput><compOutput><uid v="1482593509"/><comptype v="2"/></compOutput><compOutput><uid v="1482593510"/><comptype v="2"/></compOutput><compOutput><uid v="1482593511"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="normal_input"/><connRef v="1359211383"/><connRefOutput v="1359211384"/></connection><connection><identifier v="ambientOcclusion_input"/><connRef v="1482708447"/><connRefOutput v="1482708448"/></connection><connection><identifier v="roughness_input"/><connRef v="1482723953"/><connRefOutput v="1482708448"/></connection><connection><identifier v="height_input"/><connRef v="1486906082"/><connRefOutput v="1486906083"/></connection><connection><identifier v="basecolor_input"/><connRef v="1486907755"/><connRefOutput v="1486907756"/></connection></connections><compImplementation><compInstance><path v="pkg:///pbr_base_material?dependency=1482592834"/><parameters><parameter><name v="user_basecolor"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="user_normal"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="user_roughness"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="user_metallic"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="user_ao"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="user_height"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="height_position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.519999981"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1482593503"/><identifier v="diffuse"/></outputBridging><outputBridging><uid v="1482593504"/><identifier v="basecolor"/></outputBridging><outputBridging><uid v="1482593505"/><identifier v="normal"/></outputBridging><outputBridging><uid v="1482593506"/><identifier v="specular"/></outputBridging><outputBridging><uid v="1482593507"/><identifier v="glossiness"/></outputBridging><outputBridging><uid v="1482593508"/><identifier v="roughness"/></outputBridging><outputBridging><uid v="1482593509"/><identifier v="metallic"/></outputBridging><outputBridging><uid v="1482593510"/><identifier v="height"/></outputBridging><outputBridging><uid v="1482593511"/><identifier v="ambientOcclusion"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1482608789"/><GUILayout><gpos v="-1936.21106 -61.0516663 0"/></GUILayout><compOutputs><compOutput><uid v="1482608790"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1482591087"/></connection></connections><compImplementation><compFilter><filter v="blur"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.91999996"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1482609708"/><GUILayout><gpos v="-1776 -5.33333349 0"/></GUILayout><compOutputs><compOutput><uid v="1482609709"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="pattern_input"/><connRef v="1482608789"/><connRefOutput v="1482608790"/></connection><connection><identifier v="rotation_map_input"/><connRef v="1482609942"/><connRefOutput v="1482609943"/></connection></connections><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1486875900"/><parameters><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="75"/></paramValue></parameter><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="size"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="7.69000006 6.26999998"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="3.75"/></paramValue></parameter><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="75"/></paramValue></parameter><parameter><name v="size_random"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.329999983 0.319999993"/></paramValue></parameter><parameter><name v="rotation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.920000017"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.659999967"/></paramValue></parameter><parameter><name v="rotation_map_multiplier"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.539999962"/></paramValue></parameter><parameter><name v="rotation_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0199999996"/></paramValue></parameter><parameter><name v="non_square_expansion"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="color_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.689999998"/></paramValue></parameter><parameter><name v="pattern_input_number"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1482609709"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1482609942"/><GUILayout><gpos v="-1936 48 0"/></GUILayout><compOutputs><compOutput><uid v="1482609943"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///gaussian_noise?dependency=1486875707"/><parameters><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueInt32 v="9"/></paramValue></parameter><parameter><name v="disorder"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.099999994"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1482609943"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1482628325"/><GUILayout><gpos v="-1297.63147 349.614777 0"/></GUILayout><compOutputs><compOutput><uid v="1482628326"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1482709065"/><connRefOutput v="1482709066"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.478316337 0.478316337 0.478316337 0.5"/></paramValue></parameter><parameter><name v="levelinlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.12627551 0.12627551 0.12627551 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1482628888"/><GUILayout><gpos v="-1296 221.614777 0"/></GUILayout><compOutputs><compOutput><uid v="1482628326"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1482709065"/><connRefOutput v="1482709066"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.478316337 0.478316337 0.478316337 0.5"/></paramValue></parameter><parameter><name v="levelinlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.12627551 0.12627551 0.12627551 0"/></paramValue></parameter><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.235969394 0.235969394 0.235969394 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1482708239"/><GUILayout><gpos v="-1616 48.0000076 0"/></GUILayout><compOutputs><compOutput><uid v="1482708240"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1482609708"/><connRefOutput v="1482609709"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.53188777 0.53188777 0.53188777 0.5"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1482708447"/><GUILayout><gpos v="-1104 365.786652 0"/></GUILayout><compOutputs><compOutput><uid v="1482708448"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1482628325"/><connRefOutput v="1482628326"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.3125 0.3125 0.3125 0.5"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1482709065"/><GUILayout><gpos v="-1488 48 0"/></GUILayout><compOutputs><compOutput><uid v="1482709066"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1482708239"/><connRefOutput v="1482708240"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.151785716 0.151785716 0.151785716 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1482723953"/><GUILayout><gpos v="-1104 253.614777 0"/></GUILayout><compOutputs><compOutput><uid v="1482708448"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1482628888"/><connRefOutput v="1482628326"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.460459173 0.460459173 0.460459173 0.5"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1482724529"/><GUILayout><gpos v="-688 -144 0"/></GUILayout><connections><connection><identifier v="inputNodeOutput"/><connRef v="1482593502"/><connRefOutput v="1482593504"/></connection></connections><compImplementation><compOutputBridge><output v="1482724528"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1482724532"/><GUILayout><gpos v="-688 -29.2572517 0"/></GUILayout><connections><connection><identifier v="inputNodeOutput"/><connRef v="1482593502"/><connRefOutput v="1482593505"/></connection></connections><compImplementation><compOutputBridge><output v="1482724531"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1482724535"/><GUILayout><gpos v="-688 81.4855118 0"/></GUILayout><connections><connection><identifier v="inputNodeOutput"/><connRef v="1482593502"/><connRefOutput v="1482593508"/></connection></connections><compImplementation><compOutputBridge><output v="1482724534"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1482724541"/><GUILayout><gpos v="-688 190.878784 0"/></GUILayout><connections><connection><identifier v="inputNodeOutput"/><connRef v="1482593502"/><connRefOutput v="1482593510"/></connection></connections><compImplementation><compOutputBridge><output v="1482724540"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1482724544"/><GUILayout><gpos v="-685.325195 311.431396 0"/></GUILayout><connections><connection><identifier v="inputNodeOutput"/><connRef v="1488295933"/><connRefOutput v="1488295934"/></connection></connections><compImplementation><compOutputBridge><output v="1482724543"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1486906082"/><GUILayout><gpos v="-1104 144 0"/></GUILayout><compOutputs><compOutput><uid v="1486906083"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1482709065"/><connRefOutput v="1482709066"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelouthigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.679846942 0.679846942 0.679846942 1"/></paramValue></parameter><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.459302336 0.459302336 0.459302336 0.5"/></paramValue></parameter><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.672193885 0.672193885 0.672193885 1"/></paramValue></parameter><parameter><name v="levelinlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.304846942 0.304846942 0.304846942 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1486907755"/><GUILayout><gpos v="-1104 -80 0"/></GUILayout><compOutputs><compOutput><uid v="1486907756"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1486910338"/><connRefOutput v="1482625016"/></connection></connections><compImplementation><compFilter><filter v="hsl"/><parameters><parameter><name v="hue"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.50999999"/></paramValue></parameter><parameter><name v="saturation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.469999999"/></paramValue></parameter><parameter><name v="luminosity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.48999998"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1486910338"/><GUILayout><gpos v="-1296 -80 0"/></GUILayout><compOutputs><compOutput><uid v="1482625016"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1482709065"/><connRefOutput v="1482709066"/></connection></connections><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1482625039"/><paramsArrayCells><paramsArrayCell><uid v="1482625052"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.926086962 0.546007454 0.393048555 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1482625053"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.875527442"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.650328338 0.908695638 0.507178962 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1482625213"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.0780590624"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.168359771 0.352173924 0.114661269 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1482625257"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.386075944"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.41995281 0.682608724 0.170652181 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1482625330"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.630801678"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.660464287 0.813043475 0.207987875 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1488295933"/><GUILayout><gpos v="-795.877808 313.65863 0"/></GUILayout><compOutputs><compOutput><uid v="1488295934"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input"/><connRef v="1482593502"/><connRefOutput v="1482593511"/></connection></connections><compImplementation><compInstance><path v="pkg:///hbao?dependency=1297162667"/><parameters><parameter><name v="height_depth"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0899999961"/></paramValue></parameter><parameter><name v="samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="16"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1488295934"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode></compNodes><baseParameters/><options><option><name v="defaultParentSize"/><value v="11x11"/></option><option><name v="export/fromGraph/autoExport"/><value v="false"/></option><option><name v="export/fromGraph/destination"/><value v="C:/Users/<USER>/Desktop"/></option><option><name v="export/fromGraph/extension"/><value v="png"/></option><option><name v="export/fromGraph/outputs/ambientOcclusion"/><value v="true"/></option><option><name v="export/fromGraph/outputs/basecolor"/><value v="true"/></option><option><name v="export/fromGraph/outputs/height"/><value v="true"/></option><option><name v="export/fromGraph/outputs/normal"/><value v="true"/></option><option><name v="export/fromGraph/outputs/roughness"/><value v="true"/></option><option><name v="export/fromGraph/outputsColorspace/ambientOcclusion"/><value v="Raw"/></option><option><name v="export/fromGraph/outputsColorspace/basecolor"/><value v="sRGB"/></option><option><name v="export/fromGraph/outputsColorspace/height"/><value v="Raw"/></option><option><name v="export/fromGraph/outputsColorspace/normal"/><value v="Raw"/></option><option><name v="export/fromGraph/outputsColorspace/roughness"/><value v="Raw"/></option><option><name v="export/fromGraph/pattern"/><value v="$(graph)_$(identifier)"/></option></options><root><rootOutputs><rootOutput><output v="1482724528"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1482724531"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1482724534"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1482724540"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1482724543"/><format v="0"/><usertag v=""/></rootOutput></rootOutputs></root></graph></content></package>
