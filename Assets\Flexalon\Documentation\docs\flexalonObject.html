<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<link rel="icon" href="../images/favicon.svg" />
		<link rel="icon" href="../images/favicon.ico" />
		<meta name="viewport" content="width=device-width, height=device-height, initial-scale=1">
		<meta property="og:type" content="website">
		<meta http-equiv="content-security-policy" content="">
		<link href="../_app/immutable/assets/_layout.21aea584.css" rel="stylesheet">
		<link href="../_app/immutable/assets/_layout.0154a58a.css" rel="stylesheet">
		
		
		
		
		
		
		
		
		
		
		
		
		<title>Flexalon Objects</title><!-- HEAD_svelte-i5aht6_START --><meta property="og:title" content="Flexalon Objects"><meta name="description" property="og:description" content="Flexalon lets Unity developers arrange objects in 3D layouts that are precise and easy to adjust. Use built-in interactions and animations to create powerful experiences."><meta name="image" property="og:image" content="../images/flexalon-1200x630.png"><meta name="twitter:card" content="summary_large_image"><meta name="twitter:title" content="Flexalon Objects"><meta name="twitter:site" content="@alon_farchy"><meta name="twitter:description" content="Flexalon lets Unity developers arrange objects in 3D layouts that are precise and easy to adjust. Use built-in interactions and animations to create powerful experiences."><meta name="twitter:image" content="../images/flexalon-1200x630.png"><!-- HEAD_svelte-i5aht6_END -->
	</head>
	<body>
		<div>


<div class="text-white font-body p-4 sticky top-0 bg-black flex gap-5 w-full text-gray-200 z-20 border-b-2 border-[#21a6f0]"><button class="lg:hidden"><svg width="24" height="24"><path d="M5 6h14M5 12h14M5 18h14" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"></path></svg></button>
    <a href="https://www.flexalon.com/" class="font-logo upper text-lg text-[#21a6f0] lg:pl-5 svelte-194nj6y">Flexalon Layouts</a></div>

<div class="w-full max-w-[1600px] lg:pl-10"><div class="flex w-full min-h-full text-zinc-400 relative"><div class="h-full fixed bg-black z-10 max-lg:hidden"><div class="flex flex-col w-[300px] gap-2 border-[#21a6f0] border-r-2 bg-black min-h-0 h-full pt-10 pb-36 overflow-auto max-lg:pl-10"><b class="text-zinc-200">Getting Started</b>
                <a href="../docs.html" class="svelte-194nj6y">Installation</a>
                <a href="../docs/coreConcepts.html" class="svelte-194nj6y">Core Concepts</a>
                <a href="../docs/ui.html" class="svelte-194nj6y">Flexalon UI <span class="ml-2 text-xs font-bold text-pink-300">(New in v4.0!)</span></a>
                <a href="../docs/templates.html" class="svelte-194nj6y">Template Pack</a>
                <a href="../docs/challenges.html" class="svelte-194nj6y">Challenges!</a>

                <a class="text-zinc-200 mt-5 font-bold svelte-194nj6y" href="../docs/flexalonObject.html">Flexalon Objects</a>
                <a href="../docs/flexalonObject.html#size" class="svelte-194nj6y">Width, Height, and Depth</a>
                <a href="../docs/flexalonObject.html#minmax" class="svelte-194nj6y">Min / Max Size<span class="ml-2 text-xs font-bold text-purple-400">(v4.1)</span></a>
                <a href="../docs/flexalonObject.html#margins" class="svelte-194nj6y">Margins and Padding</a>
                <a href="../docs/flexalonObject.html#offset" class="svelte-194nj6y">Offset, Scale, and Rotation</a>

                <b class="text-zinc-200 mt-5">Layouts</b>
                <a href="../docs/flexibleLayout.html" class="svelte-194nj6y">Flexible Layout</a>
                <a href="../docs/gridLayout.html" class="svelte-194nj6y">Grid Layout</a>
                <a href="../docs/circleLayout.html" class="svelte-194nj6y">Circle / Spiral Layout</a>
                <a href="../docs/curveLayout.html" class="svelte-194nj6y">Curve Layout</a>
                <a href="../docs/randomLayout.html" class="svelte-194nj6y">Random Layout</a>
                <a href="../docs/alignLayout.html" class="svelte-194nj6y">Align Layout</a>
                <a href="../docs/shapeLayout.html" class="svelte-194nj6y">Shape Layout</a>
                <a href="../docs/constraints.html" class="svelte-194nj6y">Constraints</a>
                <a href="../docs/randomModifier.html" class="svelte-194nj6y">Random Modifier</a>

                <a class="text-zinc-200 mt-5 font-bold svelte-194nj6y" href="../docs/animators.html">Animators</a>
                <a href="../docs/animators.html#curve" class="svelte-194nj6y">Curve Animator</a>
                <a href="../docs/animators.html#lerp" class="svelte-194nj6y">Lerp Animator</a>
                <a href="../docs/animators.html#rb" class="svelte-194nj6y">Rigid Body Animator</a>
                <a href="../docs/animators.html#layouts" class="svelte-194nj6y">Animating Layouts</a>

                <b class="text-zinc-200 mt-5">Click &amp; Drag</b>
                <a href="../docs/interactable.html" class="svelte-194nj6y">Interactable</a>
                <a href="../docs/interactable.html#dragTarget" class="svelte-194nj6y">Drag Target</a>
                <a href="../docs/xr.html" class="svelte-194nj6y">XR Interactions <span class="ml-2 text-xs font-bold text-blue-300">(v3.2)</span></a>
                <a href="../docs/interactable.html#customInputProvider" class="svelte-194nj6y">Custom Input Provider</a>

                <b class="text-zinc-200 mt-5">Data Binding</b>
                <a href="../docs/cloner.html" class="svelte-194nj6y">Cloner</a>
                <a href="../docs/cloner.html#databinding" class="svelte-194nj6y">Data Binding</a>

                <b class="text-zinc-200 mt-5">Advanced</b>
                <a href="../docs/pipeline.html" class="svelte-194nj6y">Flexalon Pipeline</a>
                <a href="../docs/customLayout.html" class="svelte-194nj6y">Custom Layouts</a>
                <a href="../docs/customAnimators.html" class="svelte-194nj6y">Custom Animators</a>
                <a href="../docs/adapters.html" class="svelte-194nj6y">Adapters</a>

                <a class="text-zinc-200 mt-5 font-bold svelte-194nj6y" href="../docs/api/Flexalon.html">Scripting API</a>
                <div class="mb-20"></div></div></div>
        
        <div class="flex flex-col w-full p-10 lg:ml-[300px]"><div class="doc svelte-194nj6y">

<div class="text-white text-2xl mb-10">Flexalon Objects</div>

<p>Flexalon uses a box model to perform layout. Each gameObject has an invisible box which is used by the layout algorithms. If a Flexalon Object Component is added to the gameObject, you can see this box represented by a light blue box:</p>
<div class="flex ml-10 m-5 mb-10"><img src="../images/doc-box.png" alt="Flexalon Box Model"></div>

<p>By default, an object&#39;s box size is based on the size of the Unity components attached to it. For more information on how this works, see the <a href="../docs/adapters.html">Adapter</a> documentation.</p>

<p class="text-xl mt-10">Flexalon Object Component</p>
<p>To control the size of an object, add a Flexalon Object component to it and edit the width, height, or depth properties.</p>
<div class="flex ml-10 m-5 mb-10"><img src="../images/doc-object.png" alt="Flexalon Object Component"></div>

<p id="size" class="text-xl mt-10">Size Options</p>
<table><tr><td>Component</td>
        <td>The size is determined by the <a href="../docs/adapters.html">Adapter</a> and attached Unity components such as MeshRenderer, SpriteRenderer, TMP_Text, RectTransform, and Colliders. An empty GameObject gets a size of 1.</td></tr>
    <tr><td>Fixed</td>
        <td>Specify a fixed size value.</td></tr>
    <tr><td>Fill</td>
        <td>Specify a factor of the space allocated by the parent layout. For example, 0.5 will fill half of the space.</td></tr>
    <tr><td>Layout</td>
        <td>The size determined by the layout&#39;s algorithm.</td></tr></table>

<p class="mt-10">When using Fixed size, spherical handles will appear to let you adjust the size in the editor.</p>
<div class="flex ml-10 m-5 mb-10"><img src="../images/doc-size-handles.png" alt="Flexalon Object Size Handles"></div>

<p id="minmax" class="text-xl mt-10">Min / Max Size Options <span class="ml-2 text-xs font-bold text-purple-400">(v4.1)</span></p>
<table><tr><td>Fixed</td>
        <td>Specify a fixed size value for the minimum or maximum size.</td></tr>
    <tr><td>Fill</td>
        <td>Specify a factor of the size of the parent layout. For example, 0.5 will fill half of the parent.</td></tr>
    <tr><td>None</td>
        <td>The size of the object is unbounded.</td></tr></table>

<p id="shrinking" class="text-xl mt-10">Shrinking <span class="ml-2 text-xs font-bold text-purple-400">(v4.1)</span></p>
<p>Objects with a minimum size other than &#39;None&#39; may be shrunk to fit into the parent layout. For example, if a Grid Layout cells size is 2x2, but the child width is 3, and the min
    width is 0, then its width will be shrunk to 2.
</p>

<p id="margins" class="text-xl mt-10">Margins</p>
<p>Use margins to add additional space around a gameObject which is considered by its parent&#39;s layout.</p>

<p class="text-xl mt-10">Padding</p>
<p>Use padding to add additional space inside the gameObject, which reduces the available space for the children.</p>

<p id="offset" class="text-xl mt-10">Offset</p>
<p>Use offset to add an offset to the final position of the gameObject <i>after</i> layout is complete.</p>

<p class="text-xl mt-10">Rotation</p>
<p>Use rotation to set the rotation of the gameObject <i>before</i> layout runs. This will generate a new size to encapsulate the rotated object.</p>

<p class="text-xl mt-10">Scale</p>
<p>Use rotation to scale the size of the gameObject <i>before</i> layout runs. This will generate a new size to encapsulate the scaled object.</p>

<p class="text-xl mt-10">Skip Layout <span class="ml-2 text-xs font-bold text-purple-400">(v4.1)</span></p>
<p>With this option checked, the child will be skipped by the parent layout, and its position and rotation will not be modified.</p></div></div></div>
</div>


			
			
		</div>
	</body>
</html>
