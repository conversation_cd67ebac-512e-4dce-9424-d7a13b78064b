%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 10
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.45415223, g: 0.5438784, b: 0.7352941, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 3
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 10304, guid: 0000000000000000f000000000000000, type: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &3
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 13
  m_BakeOnSceneLoad: 0
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 0
    m_EnableRealtimeLightmaps: 0
  m_LightmapEditorSettings:
    serializedVersion: 12
    m_Resolution: 0.1
    m_BakeResolution: 40
    m_AtlasSize: 256
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_ExtractAmbientOcclusion: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_ReflectionCompression: 2
    m_MixedBakeMode: 0
    m_BakeBackend: 0
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 500
    m_PVRBounces: 2
    m_PVREnvironmentSampleCount: 500
    m_PVREnvironmentReferencePointCount: 2048
    m_PVRFilteringMode: 2
    m_PVRDenoiserTypeDirect: 0
    m_PVRDenoiserTypeIndirect: 0
    m_PVRDenoiserTypeAO: 0
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVREnvironmentMIS: 0
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ExportTrainingData: 0
    m_TrainingDataDestination: TrainingData
    m_LightProbeSampleCountMultiplier: 4
  m_LightingDataAsset: {fileID: 112000008, guid: 738dab14f22897e49a12eb102bb1a2ff, type: 2}
  m_LightingSettings: {fileID: 4890085278179872738, guid: 2d3905e556ad67143bbe162f60f704be, type: 2}
--- !u!196 &4
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 3
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    buildHeightMesh: 0
    maxJobWorkers: 0
    preserveTilesOutsideBounds: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!1 &106963462
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 106963463}
  m_Layer: 0
  m_Name: ----------------------------------
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &106963463
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 106963462}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 1.5048004, y: -2.4112737, z: 28.424793}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &222741874
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 1050491664777762, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: m_Name
      value: PR_Tentacle (1)
      objectReference: {fileID: 0}
    - target: {fileID: 1050491664777762, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4201504201906128, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: m_RootOrder
      value: 10
      objectReference: {fileID: 0}
    - target: {fileID: 4201504201906128, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: m_LocalPosition.x
      value: 1.62
      objectReference: {fileID: 0}
    - target: {fileID: 4201504201906128, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.64
      objectReference: {fileID: 0}
    - target: {fileID: 4201504201906128, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: m_LocalPosition.z
      value: -2.85
      objectReference: {fileID: 0}
    - target: {fileID: 4201504201906128, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.95218277
      objectReference: {fileID: 0}
    - target: {fileID: 4201504201906128, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4201504201906128, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0.3055291
      objectReference: {fileID: 0}
    - target: {fileID: 4201504201906128, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 114614123583689798, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: Gravity.y
      value: -1.69
      objectReference: {fileID: 0}
    - target: {fileID: 114614123583689798, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: WavingType
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 114614123583689798, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: Sensitivity
      value: 0.613
      objectReference: {fileID: 0}
    - target: {fileID: 114614123583689798, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: Springiness
      value: 0.149
      objectReference: {fileID: 0}
    - target: {fileID: 114614123583689798, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: WavingRange
      value: 0.4
      objectReference: {fileID: 0}
    - target: {fileID: 114614123583689798, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: WavingSpeed
      value: 4
      objectReference: {fileID: 0}
    - target: {fileID: 114614123583689798, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: UseCollision
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114614123583689798, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: WavingAxis.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 114614123583689798, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: WavingAxis.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 114614123583689798, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: MaxStretching
      value: 0.074
      objectReference: {fileID: 0}
    - target: {fileID: 114614123583689798, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: PositionSpeed
      value: 28.9
      objectReference: {fileID: 0}
    - target: {fileID: 114614123583689798, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: RotationSpeed
      value: 18
      objectReference: {fileID: 0}
    - target: {fileID: 114614123583689798, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: CollisionSpace
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114614123583689798, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: CollisionMethod
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114614123583689798, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: CollidersScaleMul
      value: 1.69
      objectReference: {fileID: 0}
    - target: {fileID: 114614123583689798, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: IncludedColliders.Array.size
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114614123583689798, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: 'IncludedColliders.Array.data[0]'
      value: 
      objectReference: {fileID: 469462812}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
--- !u!1 &256013664
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 256013667}
  - component: {fileID: 256013666}
  - component: {fileID: 256013665}
  m_Layer: 0
  m_Name: EventSystem
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &256013665
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 256013664}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4f231c4fb786f3946a6b90b886c48677, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_SendPointerHoverToParent: 1
  m_HorizontalAxis: Horizontal
  m_VerticalAxis: Vertical
  m_SubmitButton: Submit
  m_CancelButton: Cancel
  m_InputActionsPerSecond: 10
  m_RepeatDelay: 0.5
  m_ForceModuleActive: 0
--- !u!114 &256013666
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 256013664}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 76c392e42b5098c458856cdf6ecaaaa1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_FirstSelected: {fileID: 0}
  m_sendNavigationEvents: 1
  m_DragThreshold: 5
--- !u!4 &256013667
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 256013664}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &469462810
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 469462814}
  - component: {fileID: 469462813}
  - component: {fileID: 469462812}
  - component: {fileID: 469462811}
  - component: {fileID: 469462815}
  m_Layer: 5
  m_Name: Floor
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!23 &469462811
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 469462810}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 1405831025}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 1
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &469462812
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 469462810}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!33 &469462813
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 469462810}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &469462814
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 469462810}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 2.0076346, y: -0.64, z: -3.48}
  m_LocalScale: {x: 1000, y: 3, z: 1000}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1717911228}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &469462815
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 469462810}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6604dc3407fb5f64f8fed44b2a0b3b9a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  TextureProperty: _MainTex
  ScaleValues: {x: 1, y: 1}
  EqualDimensions: 0
--- !u!1 &964547473
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 964547477}
  - component: {fileID: 964547476}
  - component: {fileID: 964547475}
  - component: {fileID: 964547474}
  - component: {fileID: 964547482}
  m_Layer: 0
  m_Name: Main Camera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!81 &964547474
AudioListener:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 964547473}
  m_Enabled: 1
--- !u!124 &964547475
Behaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 964547473}
  m_Enabled: 1
--- !u!20 &964547476
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 964547473}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 2
  m_BackGroundColor: {r: 0.6985294, g: 0.6985294, b: 0.6985294, a: 0}
  m_projectionMatrixMode: 1
  m_GateFitMode: 2
  m_FOVAxisMode: 0
  m_Iso: 200
  m_ShutterSpeed: 0.005
  m_Aperture: 16
  m_FocusDistance: 10
  m_FocalLength: 50
  m_BladeCount: 5
  m_Curvature: {x: 2, y: 11}
  m_BarrelClipping: 0.25
  m_Anamorphism: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.3
  far clip plane: 1000
  field of view: 60
  orthographic: 0
  orthographic size: 5
  m_Depth: -1
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 1
  m_AllowMSAA: 1
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 0
  m_OcclusionCulling: 1
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!4 &964547477
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 964547473}
  serializedVersion: 2
  m_LocalRotation: {x: 0.021195149, y: 0.9564496, z: -0.28212234, w: 0.0718461}
  m_LocalPosition: {x: -1.175631, y: 8.542467, z: 8.483481}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &964547482
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 964547473}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2e401366475335a4c85c5431e4624158, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  SpeedMultiplier: 10
  AccelerationSmothnessValue: 10
  RotationSmothnessValue: 10
  MouseSensitivity: 5
--- !u!1 &1106836264
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1106836265}
  - component: {fileID: 1106836267}
  - component: {fileID: 1106836266}
  m_Layer: 5
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1106836265
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1106836264}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1479396080}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 5, y: 0}
  m_SizeDelta: {x: 462.8, y: 208.91}
  m_Pivot: {x: 0, y: 1}
--- !u!114 &1106836266
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1106836264}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5f7201a12d95ffc409449d95f23cf332, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.19607843, g: 0.19607843, b: 0.19607843, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_FontData:
    m_Font: {fileID: 10102, guid: 0000000000000000e000000000000000, type: 0}
    m_FontSize: 14
    m_FontStyle: 1
    m_BestFit: 0
    m_MinSize: 1
    m_MaxSize: 43
    m_Alignment: 0
    m_AlignByGeometry: 0
    m_RichText: 1
    m_HorizontalOverflow: 0
    m_VerticalOverflow: 0
    m_LineSpacing: 1
  m_Text: Tentacle examples which are using perlin noise to generate wave animation,
    instead of very repetitive trigonometric function
--- !u!222 &1106836267
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1106836264}
  m_CullTransparentMesh: 1
--- !u!1001 &1161952298
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 1050491664777762, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: m_Name
      value: PR_Tentacle
      objectReference: {fileID: 0}
    - target: {fileID: 1050491664777762, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4201504201906128, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: m_RootOrder
      value: 8
      objectReference: {fileID: 0}
    - target: {fileID: 4201504201906128, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: m_LocalPosition.x
      value: 1.7
      objectReference: {fileID: 0}
    - target: {fileID: 4201504201906128, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: m_LocalPosition.y
      value: 3.2
      objectReference: {fileID: 0}
    - target: {fileID: 4201504201906128, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: m_LocalPosition.z
      value: 3.1
      objectReference: {fileID: 0}
    - target: {fileID: 4201504201906128, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.21873936
      objectReference: {fileID: 0}
    - target: {fileID: 4201504201906128, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4201504201906128, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0.97578335
      objectReference: {fileID: 0}
    - target: {fileID: 4201504201906128, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4201504201906128, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: -154.73
      objectReference: {fileID: 0}
    - target: {fileID: 114614123583689798, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: WavingRange
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 114614123583689798, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: WavingSpeed
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 114614123583689798, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: WavingAxis.y
      value: 0.6
      objectReference: {fileID: 0}
    - target: {fileID: 114614123583689798, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: WavingAxis.z
      value: 0.3
      objectReference: {fileID: 0}
    - target: {fileID: 114614123583689798, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: AlternateWave
      value: 2
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
--- !u!21 &1405831025
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: MAT_FBasic_Floor (Instance) (Instance)
  m_Shader: {fileID: 46, guid: 0000000000000000f000000000000000, type: 0}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords: []
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 3.17, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 10305, guid: 0000000000000000f000000000000000, type: 0}
        m_Scale: {x: 1000, y: 1000}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _BumpScale: 1
    - _Cutoff: 0.5
    - _DetailNormalMapScale: 1
    - _DstBlend: 0
    - _GlossMapScale: 1
    - _Glossiness: 0
    - _GlossyReflections: 1
    - _Metallic: 0
    - _Mode: 0
    - _OcclusionStrength: 1
    - _Parallax: 0.02
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _UVSec: 0
    - _ZWrite: 1
    m_Colors:
    - _Color: {r: 0.71323526, g: 0.71323526, b: 0.71323526, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
  m_BuildTextureStacks: []
  m_AllowLocking: 1
--- !u!1 &1407205959
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1407205960}
  m_Layer: 0
  m_Name: ----------------------------------
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &1407205960
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1407205959}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 1.5048004, y: -2.4112737, z: 28.424793}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1479396076
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1479396080}
  - component: {fileID: 1479396079}
  - component: {fileID: 1479396078}
  - component: {fileID: 1479396077}
  m_Layer: 5
  m_Name: Canvas
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1479396077
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1479396076}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc42784cf147c0c48a680349fa168899, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 4294967295
--- !u!114 &1479396078
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1479396076}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0cd44c1031e13a943bb63640046fad76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UiScaleMode: 0
  m_ReferencePixelsPerUnit: 100
  m_ScaleFactor: 1
  m_ReferenceResolution: {x: 800, y: 600}
  m_ScreenMatchMode: 0
  m_MatchWidthOrHeight: 0
  m_PhysicalUnit: 3
  m_FallbackScreenDPI: 96
  m_DefaultSpriteDPI: 96
  m_DynamicPixelsPerUnit: 1
  m_PresetInfoIsWorld: 0
--- !u!223 &1479396079
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1479396076}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 0
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_VertexColorAlwaysGammaSpace: 0
  m_AdditionalShaderChannelsFlag: 0
  m_UpdateRectTransformForStandalone: 0
  m_SortingLayerID: 0
  m_SortingOrder: 0
  m_TargetDisplay: 0
--- !u!224 &1479396080
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1479396076}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0, y: 0, z: 0}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1106836265}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0, y: 0}
--- !u!1001 &1564220861
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 1050491664777762, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: m_Name
      value: PR_Tentacle
      objectReference: {fileID: 0}
    - target: {fileID: 1050491664777762, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4201504201906128, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: m_RootOrder
      value: 9
      objectReference: {fileID: 0}
    - target: {fileID: 4201504201906128, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: m_LocalScale.x
      value: 0.5899124
      objectReference: {fileID: 0}
    - target: {fileID: 4201504201906128, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: m_LocalScale.y
      value: 0.5899124
      objectReference: {fileID: 0}
    - target: {fileID: 4201504201906128, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: m_LocalScale.z
      value: 0.5899124
      objectReference: {fileID: 0}
    - target: {fileID: 4201504201906128, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: m_LocalPosition.x
      value: -1.44
      objectReference: {fileID: 0}
    - target: {fileID: 4201504201906128, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: m_LocalPosition.y
      value: 3.2
      objectReference: {fileID: 0}
    - target: {fileID: 4201504201906128, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: m_LocalPosition.z
      value: -0.34
      objectReference: {fileID: 0}
    - target: {fileID: 4201504201906128, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.9715492
      objectReference: {fileID: 0}
    - target: {fileID: 4201504201906128, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4201504201906128, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.23683812
      objectReference: {fileID: 0}
    - target: {fileID: 4201504201906128, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4201504201906128, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 27.4
      objectReference: {fileID: 0}
    - target: {fileID: 114614123583689798, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: WavingRange
      value: 0.68
      objectReference: {fileID: 0}
    - target: {fileID: 114614123583689798, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: WavingSpeed
      value: 2.7
      objectReference: {fileID: 0}
    - target: {fileID: 114614123583689798, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: WavingAxis.x
      value: 0.7
      objectReference: {fileID: 0}
    - target: {fileID: 114614123583689798, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: WavingAxis.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114614123583689798, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: WavingAxis.z
      value: 0.7
      objectReference: {fileID: 0}
    - target: {fileID: 114614123583689798, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: AlternateWave
      value: 1.5
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
--- !u!1 &1717911227
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1717911228}
  m_Layer: 0
  m_Name: Level
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1717911228
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717911227}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -2.0076346, y: -0.87006235, z: 4.1002235}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 469462814}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1927895606
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1927895608}
  - component: {fileID: 1927895607}
  m_Layer: 0
  m_Name: Directional Light
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!108 &1927895607
Light:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1927895606}
  m_Enabled: 1
  serializedVersion: 11
  m_Type: 1
  m_Color: {r: 0.6397059, g: 0.6397059, b: 0.6397059, a: 1}
  m_Intensity: 1.33
  m_Range: 10
  m_SpotAngle: 30
  m_InnerSpotAngle: 21.80208
  m_CookieSize: 10
  m_Shadows:
    m_Type: 2
    m_Resolution: -1
    m_CustomResolution: -1
    m_Strength: 1
    m_Bias: 0.05
    m_NormalBias: 0.4
    m_NearPlane: 0.2
    m_CullingMatrixOverride:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    m_UseCullingMatrixOverride: 0
  m_Cookie: {fileID: 0}
  m_DrawHalo: 0
  m_Flare: {fileID: 0}
  m_RenderMode: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingLayerMask: 1
  m_Lightmapping: 4
  m_LightShadowCasterMode: 0
  m_AreaSize: {x: 1, y: 1}
  m_BounceIntensity: 1
  m_ColorTemperature: 6570
  m_UseColorTemperature: 0
  m_BoundingSphereOverride: {x: 0, y: 0, z: 0, w: 0}
  m_UseBoundingSphereOverride: 0
  m_UseViewFrustumForShadowCasterCull: 1
  m_ForceVisible: 0
  m_ShadowRadius: 0
  m_ShadowAngle: 0
  m_LightUnit: 1
  m_LuxAtDistance: 1
  m_EnableSpotReflector: 1
--- !u!4 &1927895608
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1927895606}
  serializedVersion: 2
  m_LocalRotation: {x: 0.40821788, y: -0.23456968, z: 0.10938163, w: 0.8754261}
  m_LocalPosition: {x: 0, y: 3, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 50, y: -30, z: 0}
--- !u!1001 &1958249401
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 1050491664777762, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4201504201906128, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: m_RootOrder
      value: 7
      objectReference: {fileID: 0}
    - target: {fileID: 4201504201906128, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: m_LocalPosition.x
      value: 2.91
      objectReference: {fileID: 0}
    - target: {fileID: 4201504201906128, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: m_LocalPosition.y
      value: 3.34
      objectReference: {fileID: 0}
    - target: {fileID: 4201504201906128, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: m_LocalPosition.z
      value: -2.01
      objectReference: {fileID: 0}
    - target: {fileID: 4201504201906128, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.9521827
      objectReference: {fileID: 0}
    - target: {fileID: 4201504201906128, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4201504201906128, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0.30552906
      objectReference: {fileID: 0}
    - target: {fileID: 4201504201906128, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 39be8a5703d23364bb2ba86b6686ed83, type: 3}
--- !u!1660057539 &9223372036854775807
SceneRoots:
  m_ObjectHideFlags: 0
  m_Roots:
  - {fileID: 964547477}
  - {fileID: 1927895608}
  - {fileID: 1407205960}
  - {fileID: 256013667}
  - {fileID: 1479396080}
  - {fileID: 1717911228}
  - {fileID: 106963463}
  - {fileID: 1958249401}
  - {fileID: 1161952298}
  - {fileID: 1564220861}
  - {fileID: 222741874}
