#if URP_INSTALLED
using UnityEngine;
using UnityEditor;
using UnityEngine.Rendering;
using System.Collections.Generic;
using System.Reflection;
using Stylo.Flux.Universal;

namespace Stylo.Flux.Editor
{
    /// <summary>
    /// Editor window that shows detailed changes made to Flux settings during Play Mode
    /// </summary>
    public class FluxChangeDetailsWindow : EditorWindow
    {
        private List<(Volume volume, FluxEffectSnapshot original, FluxEffectSnapshot current)> changes = new();
        private Vector2 scrollPosition;
        private GUIStyle? headerStyle;
        private GUIStyle? changedValueStyle;
        private GUIStyle? unchangedValueStyle;

        public void SetChanges(List<(Volume volume, FluxEffectSnapshot original, FluxEffectSnapshot current)> changedVolumes)
        {
            changes = changedVolumes;
        }

        private void OnEnable()
        {
            titleContent = new GUIContent("Flux Changes", "Detailed view of Flux changes made during Play Mode");
            minSize = new Vector2(600, 400);
        }

        private void InitializeStyles()
        {
            if (headerStyle == null)
            {
                headerStyle = new GUIStyle(EditorStyles.boldLabel)
                {
                    fontSize = 14,
                    normal = { textColor = EditorGUIUtility.isProSkin ? Color.white : Color.black }
                };
            }

            if (changedValueStyle == null)
            {
                changedValueStyle = new GUIStyle(EditorStyles.label)
                {
                    normal = { textColor = EditorGUIUtility.isProSkin ? Color.yellow : new Color(0.8f, 0.4f, 0f) }
                };
            }

            if (unchangedValueStyle == null)
            {
                unchangedValueStyle = new GUIStyle(EditorStyles.label)
                {
                    normal = { textColor = EditorGUIUtility.isProSkin ? Color.gray : Color.gray }
                };
            }
        }

        private void OnGUI()
        {
            InitializeStyles();

            if (changes == null || changes.Count == 0)
            {
                EditorGUILayout.HelpBox("No changes detected.", MessageType.Info);
                return;
            }

            EditorGUILayout.BeginVertical();

            // Header
            EditorGUILayout.LabelField("Flux Settings Changes During Play Mode", headerStyle);
            EditorGUILayout.Space(10);

            // Action buttons
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("Save All Changes", GUILayout.Height(30)))
            {
                SaveAllChanges();
                Close();
            }
            if (GUILayout.Button("Discard All Changes", GUILayout.Height(30)))
            {
                Close();
            }
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.Space(10);

            // Scrollable content
            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);

            foreach (var change in changes)
            {
                DrawVolumeChanges(change.volume, change.original, change.current);
                EditorGUILayout.Space(15);
            }

            EditorGUILayout.EndScrollView();
            EditorGUILayout.EndVertical();
        }

        private void DrawVolumeChanges(Volume volume, FluxEffectSnapshot original, FluxEffectSnapshot current)
        {
            EditorGUILayout.BeginVertical("box");

            // Volume header
            EditorGUILayout.LabelField($"Volume: {volume.name}", headerStyle);
            EditorGUILayout.LabelField($"Profile: {volume.profile.name}", EditorStyles.miniLabel);
            EditorGUILayout.Space(5);

            // Compare all properties using reflection
            var fields = typeof(FluxEffectSnapshot).GetFields(BindingFlags.Public | BindingFlags.Instance);

            EditorGUILayout.BeginVertical();
            foreach (var field in fields)
            {
                var originalValue = field.GetValue(original);
                var currentValue = field.GetValue(current);

                bool hasChanged = !originalValue.Equals(currentValue);

                if (hasChanged)
                {
                    EditorGUILayout.BeginHorizontal();

                    // Property name
                    EditorGUILayout.LabelField(GetFriendlyFieldName(field.Name), GUILayout.Width(200));

                    // Original value
                    EditorGUILayout.LabelField($"Was: {FormatValue(originalValue)}", unchangedValueStyle, GUILayout.Width(120));

                    // Arrow
                    EditorGUILayout.LabelField("→", GUILayout.Width(20));

                    // New value
                    EditorGUILayout.LabelField($"Now: {FormatValue(currentValue)}", changedValueStyle);

                    EditorGUILayout.EndHorizontal();
                }
            }
            EditorGUILayout.EndVertical();

            EditorGUILayout.EndVertical();
        }

        private string GetFriendlyFieldName(string fieldName)
        {
            // Convert camelCase to friendly names
            switch (fieldName)
            {
                // Core parameters
                case "effectIntensity": return "Effect Intensity";
                case "effectIntensityOverride": return "Effect Intensity (Override)";
                case "onlyStenciled": return "Only Stenciled";
                case "onlyStenciledOverride": return "Only Stenciled (Override)";

                // Block Encoding
                case "colorCrunch": return "Color Crunch";
                case "colorCrunchOverride": return "Color Crunch (Override)";
                case "downscaling": return "Downscaling";
                case "downscalingOverride": return "Downscaling (Override)";
                case "blockSize": return "Block Size";
                case "blockSizeOverride": return "Block Size (Override)";
                case "oversharpening": return "Oversharpening";
                case "oversharpeningOverride": return "Oversharpening (Override)";
                case "dontCrunchSkybox": return "Don't Crunch Skybox";
                case "dontCrunchSkyboxOverride": return "Don't Crunch Skybox (Override)";

                // Datamoshing Reprojection
                case "reprojectBaseNoise": return "Reproject Base Noise";
                case "reprojectBaseNoiseOverride": return "Reproject Base Noise (Override)";
                case "reprojectBaseRerollSpeed": return "Reproject Reroll Speed";
                case "reprojectBaseRerollSpeedOverride": return "Reproject Reroll Speed (Override)";
                case "reprojectLengthInfluence": return "Reproject Length Influence";
                case "reprojectLengthInfluenceOverride": return "Reproject Length Influence (Override)";

                // Consolidated Motion Processing
                case "motionAmplification": return "Motion Amplification";
                case "motionAmplificationOverride": return "Motion Amplification (Override)";
                case "motionThreshold": return "Motion Threshold";
                case "motionThresholdOverride": return "Motion Threshold (Override)";
                case "cameraObjectMotionBalance": return "Camera Object Motion Balance";
                case "cameraObjectMotionBalanceOverride": return "Camera Object Motion Balance (Override)";
                case "motionSmoothing": return "Motion Smoothing";
                case "motionSmoothingOverride": return "Motion Smoothing (Override)";

                // Pixel Flow & Trailing
                case "trailIntensity": return "Trail Intensity";
                case "trailIntensityOverride": return "Trail Intensity (Override)";
                case "trailSmoothness": return "Trail Smoothness";
                case "trailSmoothnessOverride": return "Trail Smoothness (Override)";
                case "trailPersistence": return "Trail Persistence";
                case "trailPersistenceOverride": return "Trail Persistence (Override)";
                case "flowSpread": return "Flow Spread";
                case "flowSpreadOverride": return "Flow Spread (Override)";

                // Advanced Datamoshing
                case "keyframeResetRate": return "Keyframe Reset Rate";
                case "keyframeResetRateOverride": return "Keyframe Reset Rate (Override)";
                case "motionVectorCorruption": return "Motion Vector Corruption";
                case "motionVectorCorruptionOverride": return "Motion Vector Corruption (Override)";
                case "errorAccumulation": return "Error Accumulation";
                case "errorAccumulationOverride": return "Error Accumulation (Override)";
                case "dctCorruption": return "DCT Corruption";
                case "dctCorruptionOverride": return "DCT Corruption (Override)";

                // Enhanced Corruption
                case "corruptionMask": return "Corruption Mask";
                case "corruptionMaskOverride": return "Corruption Mask (Override)";
                case "chromaCorruption": return "Chroma Corruption";
                case "chromaCorruptionOverride": return "Chroma Corruption (Override)";
                case "glitchTransition": return "Glitch Transition";
                case "glitchTransitionOverride": return "Glitch Transition (Override)";
                case "feedbackIntensity": return "Feedback Intensity";
                case "feedbackIntensityOverride": return "Feedback Intensity (Override)";
                case "multiScaleCorruption": return "Multi Scale Corruption";
                case "multiScaleCorruptionOverride": return "Multi Scale Corruption (Override)";

                // Debug
                case "visualizeMotionVectors": return "Visualize Motion Vectors";
                case "visualizeMotionVectorsOverride": return "Visualize Motion Vectors (Override)";

                default: return fieldName;
            }
        }

        private string FormatValue(object value)
        {
            if (value is float f)
                return f.ToString("F3");
            if (value is bool b)
                return b ? "True" : "False";
            if (value is int i)
                return i.ToString();
            if (value is Texture2D tex)
                return tex != null ? tex.name : "null";
            return value?.ToString() ?? "null";
        }

        private void SaveAllChanges()
        {
            int savedCount = 0;

            foreach (var change in changes)
            {
                if (change.volume.profile.TryGet<FluxEffect>(out var fluxEffect))
                {
                    change.current.ApplyToFluxEffect(fluxEffect);
                    EditorUtility.SetDirty(change.volume.profile);
                    savedCount++;
                }
            }

            AssetDatabase.SaveAssets();

            Debug.Log($"[Flux] Saved changes to {savedCount} Volume Profile(s)");

            EditorUtility.DisplayDialog("Changes Saved",
                $"Flux settings have been saved to {savedCount} Volume Profile(s).",
                "OK");
        }
    }
}
#endif
