%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 9
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.212, g: 0.227, b: 0.259, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 0
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 10304, guid: 0000000000000000f000000000000000, type: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_IndirectSpecularColor: {r: 0.44657898, g: 0.4964133, b: 0.5748178, a: 1}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &3
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 12
  m_GIWorkflowMode: 1
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 1
    m_EnableRealtimeLightmaps: 0
  m_LightmapEditorSettings:
    serializedVersion: 12
    m_Resolution: 2
    m_BakeResolution: 40
    m_AtlasSize: 1024
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_ExtractAmbientOcclusion: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_FinalGather: 0
    m_FinalGatherFiltering: 1
    m_FinalGatherRayCount: 256
    m_ReflectionCompression: 2
    m_MixedBakeMode: 2
    m_BakeBackend: 1
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 512
    m_PVRBounces: 2
    m_PVREnvironmentSampleCount: 256
    m_PVREnvironmentReferencePointCount: 2048
    m_PVRFilteringMode: 1
    m_PVRDenoiserTypeDirect: 1
    m_PVRDenoiserTypeIndirect: 1
    m_PVRDenoiserTypeAO: 1
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVREnvironmentMIS: 1
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ExportTrainingData: 0
    m_TrainingDataDestination: TrainingData
    m_LightProbeSampleCountMultiplier: 4
  m_LightingDataAsset: {fileID: 0}
  m_LightingSettings: {fileID: 0}
--- !u!196 &4
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 2
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    accuratePlacement: 0
    maxJobWorkers: 0
    preserveTilesOutsideBounds: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!1 &241380877
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 241380878}
  m_Layer: 0
  m_Name: MMF Players
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &241380878
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 241380877}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 3.988657, y: 2.7829552, z: 5.5479393}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2103036736}
  - {fileID: 593719873}
  - {fileID: 1345306586}
  - {fileID: 1177841476}
  - {fileID: 1712588494}
  - {fileID: 293357685}
  - {fileID: 1046813962}
  - {fileID: 1231895994}
  - {fileID: 2017586134}
  - {fileID: 768629958}
  - {fileID: 998874844}
  - {fileID: 1404847137}
  - {fileID: 1635157581}
  - {fileID: 2010915430}
  - {fileID: 1850447477}
  - {fileID: 1191851717}
  m_Father: {fileID: 0}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &293357684
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 293357685}
  - component: {fileID: 293357686}
  m_Layer: 0
  m_Name: FontSize
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &293357685
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 293357684}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 241380878}
  m_RootOrder: 5
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &293357686
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 293357684}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6da43522623d4704e979466dc7650b65, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Feedbacks: []
  InitializationMode: 2
  AutoInitialization: 1
  SafeMode: 3
  Direction: 0
  AutoChangeDirectionOnEnd: 0
  AutoPlayOnStart: 0
  AutoPlayOnEnable: 0
  ForceTimescaleMode: 0
  ForcedTimescaleMode: 1
  DurationMultiplier: 1
  RandomizeDuration: 0
  RandomDurationMultiplier: {x: 0.5, y: 1.5}
  DisplayFullDurationDetails: 0
  PlayerTimescaleMode: 1
  OnlyPlayIfWithinRange: 0
  RangeCenter: {fileID: 0}
  RangeDistance: 5
  UseRangeFalloff: 0
  RangeFalloff:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  RemapRangeFalloff: {x: 0, y: 1}
  IgnoreRangeEvents: 0
  CooldownDuration: 0
  InitialDelay: 0
  CanPlay: 1
  CanPlayWhileAlreadyPlaying: 1
  ChanceToPlay: 100
  FeedbacksIntensity: 1
  Events:
    TriggerMMFeedbacksEvents: 0
    TriggerUnityEvents: 1
    OnPlay:
      m_PersistentCalls:
        m_Calls: []
    OnPause:
      m_PersistentCalls:
        m_Calls: []
    OnResume:
      m_PersistentCalls:
        m_Calls: []
    OnRevert:
      m_PersistentCalls:
        m_Calls: []
    OnComplete:
      m_PersistentCalls:
        m_Calls: []
    OnRestoreInitialValues:
      m_PersistentCalls:
        m_Calls: []
    OnSkipToTheEnd:
      m_PersistentCalls:
        m_Calls: []
  DebugActive: 0
  FeedbacksList:
  - rid: 5262747845385781255
  KeepPlayModeChanges: 0
  PerformanceMode: 0
  StopFeedbacksOnDisable: 0
  PlayCount: 0
  references:
    version: 2
    RefIds:
    - rid: 5262747845385781255
      type: {class: MMF_UIToolkitFontSize, ns: MoreMountains.FeedbacksForThirdParty,
        asm: MoreMountains.Feedbacks.UIToolkit}
      data:
        Active: 1
        UniqueID: 520400774
        Label: UITK Font Size
        ChannelMode: 0
        Channel: 0
        MMChannelDefinition: {fileID: 0}
        Chance: 100
        DisplayColor: {r: 0, g: 0, b: 0, a: 1}
        Timing:
          TimescaleMode: 0
          ExcludeFromHoldingPauses: 0
          ContributeToTotalDuration: 1
          InitialDelay: 0
          CooldownDuration: 0
          InterruptsOnStop: 1
          NumberOfRepeats: 0
          RepeatForever: 0
          DelayBetweenRepeats: 1
          MMFeedbacksDirectionCondition: 0
          PlayDirection: 0
          ConstantIntensity: 0
          UseIntensityInterval: 0
          IntensityIntervalMin: 0
          IntensityIntervalMax: 0
          Sequence: {fileID: 0}
          TrackID: 0
          Quantized: 0
          TargetBPM: 120
        AutomatedTargetAcquisition:
          Mode: 0
          ChildIndex: 0
        RandomizeOutput: 0
        RandomMultiplier: {x: 0.8, y: 1}
        RandomizeDuration: 0
        RandomDurationMultiplier: {x: 0.5, y: 2}
        UseRange: 0
        RangeDistance: 5
        UseRangeFalloff: 0
        RangeFalloff:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        RemapRangeFalloff: {x: 0, y: 1}
        Owner: {fileID: 293357686}
        DebugActive: 0
        TargetDocument: {fileID: 2114456165}
        QueryMode: 0
        Query: DemoText
        MarkDirty: 0
        Mode: 1
        RelativeValue: 1
        AllowAdditivePlays: 0
        Duration: 1
        InstantValue: 1
        Curve:
          MMTweenDefinitionType: 1
          MMTweenCurve: 4
          Curve:
            serializedVersion: 2
            m_Curve:
            - serializedVersion: 3
              time: 0
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 0.53003365
              value: 1
              inSlope: 1.4945879
              outSlope: 1.4945879
              tangentMode: 0
              weightedMode: 0
              inWeight: 0.33333334
              outWeight: 0.33333334
            - serializedVersion: 3
              time: 1
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            m_PreInfinity: 2
            m_PostInfinity: 2
            m_RotationOrder: 4
          Initialized: 0
        CurveRemapZero: 0
        CurveRemapOne: 10
        DestinationValue: 1
--- !u!1 &391915959
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 391915961}
  - component: {fileID: 391915960}
  m_Layer: 0
  m_Name: Directional Light
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!108 &391915960
Light:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 391915959}
  m_Enabled: 1
  serializedVersion: 10
  m_Type: 1
  m_Shape: 0
  m_Color: {r: 1, g: 0.95686275, b: 0.8392157, a: 1}
  m_Intensity: 1
  m_Range: 10
  m_SpotAngle: 30
  m_InnerSpotAngle: 21.80208
  m_CookieSize: 10
  m_Shadows:
    m_Type: 2
    m_Resolution: -1
    m_CustomResolution: -1
    m_Strength: 1
    m_Bias: 0.05
    m_NormalBias: 0.4
    m_NearPlane: 0.2
    m_CullingMatrixOverride:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    m_UseCullingMatrixOverride: 0
  m_Cookie: {fileID: 0}
  m_DrawHalo: 0
  m_Flare: {fileID: 0}
  m_RenderMode: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingLayerMask: 1
  m_Lightmapping: 4
  m_LightShadowCasterMode: 0
  m_AreaSize: {x: 1, y: 1}
  m_BounceIntensity: 1
  m_ColorTemperature: 6570
  m_UseColorTemperature: 0
  m_BoundingSphereOverride: {x: 0, y: 0, z: 0, w: 0}
  m_UseBoundingSphereOverride: 0
  m_UseViewFrustumForShadowCasterCull: 1
  m_ShadowRadius: 0
  m_ShadowAngle: 0
--- !u!4 &391915961
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 391915959}
  m_LocalRotation: {x: 0.40821788, y: -0.23456968, z: 0.10938163, w: 0.8754261}
  m_LocalPosition: {x: 0, y: 3, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 50, y: -30, z: 0}
--- !u!1 &593719872
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 593719873}
  - component: {fileID: 593719874}
  m_Layer: 0
  m_Name: BorderColor
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &593719873
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 593719872}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 241380878}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &593719874
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 593719872}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6da43522623d4704e979466dc7650b65, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Feedbacks: []
  InitializationMode: 2
  AutoInitialization: 1
  SafeMode: 3
  Direction: 0
  AutoChangeDirectionOnEnd: 0
  AutoPlayOnStart: 0
  AutoPlayOnEnable: 0
  ForceTimescaleMode: 0
  ForcedTimescaleMode: 1
  DurationMultiplier: 1
  RandomizeDuration: 0
  RandomDurationMultiplier: {x: 0.5, y: 1.5}
  DisplayFullDurationDetails: 0
  PlayerTimescaleMode: 1
  OnlyPlayIfWithinRange: 0
  RangeCenter: {fileID: 0}
  RangeDistance: 5
  UseRangeFalloff: 0
  RangeFalloff:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  RemapRangeFalloff: {x: 0, y: 1}
  IgnoreRangeEvents: 0
  CooldownDuration: 0
  InitialDelay: 0
  CanPlay: 1
  CanPlayWhileAlreadyPlaying: 1
  ChanceToPlay: 100
  FeedbacksIntensity: 1
  Events:
    TriggerMMFeedbacksEvents: 0
    TriggerUnityEvents: 1
    OnPlay:
      m_PersistentCalls:
        m_Calls: []
    OnPause:
      m_PersistentCalls:
        m_Calls: []
    OnResume:
      m_PersistentCalls:
        m_Calls: []
    OnRevert:
      m_PersistentCalls:
        m_Calls: []
    OnComplete:
      m_PersistentCalls:
        m_Calls: []
    OnRestoreInitialValues:
      m_PersistentCalls:
        m_Calls: []
    OnSkipToTheEnd:
      m_PersistentCalls:
        m_Calls: []
  DebugActive: 0
  FeedbacksList:
  - rid: 5262747845385781251
  KeepPlayModeChanges: 0
  PerformanceMode: 0
  StopFeedbacksOnDisable: 0
  PlayCount: 0
  references:
    version: 2
    RefIds:
    - rid: 5262747845385781251
      type: {class: MMF_UIToolkitBorderColor, ns: MoreMountains.FeedbacksForThirdParty,
        asm: MoreMountains.Feedbacks.UIToolkit}
      data:
        Active: 1
        UniqueID: 1172080969
        Label: UITK Border Color
        ChannelMode: 0
        Channel: 0
        MMChannelDefinition: {fileID: 0}
        Chance: 100
        DisplayColor: {r: 0, g: 0, b: 0, a: 1}
        Timing:
          TimescaleMode: 0
          ExcludeFromHoldingPauses: 0
          ContributeToTotalDuration: 1
          InitialDelay: 0
          CooldownDuration: 0
          InterruptsOnStop: 1
          NumberOfRepeats: 0
          RepeatForever: 0
          DelayBetweenRepeats: 1
          MMFeedbacksDirectionCondition: 0
          PlayDirection: 0
          ConstantIntensity: 0
          UseIntensityInterval: 0
          IntensityIntervalMin: 0
          IntensityIntervalMax: 0
          Sequence: {fileID: 0}
          TrackID: 0
          Quantized: 0
          TargetBPM: 120
        AutomatedTargetAcquisition:
          Mode: 0
          ChildIndex: 0
        RandomizeOutput: 0
        RandomMultiplier: {x: 0.8, y: 1}
        RandomizeDuration: 0
        RandomDurationMultiplier: {x: 0.5, y: 2}
        UseRange: 0
        RangeDistance: 5
        UseRangeFalloff: 0
        RangeFalloff:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        RemapRangeFalloff: {x: 0, y: 1}
        Owner: {fileID: 593719874}
        DebugActive: 0
        TargetDocument: {fileID: 2114456165}
        QueryMode: 0
        Query: DemoText
        MarkDirty: 0
        Mode: 0
        Duration: 0.2
        AllowAdditivePlays: 0
        ModifyColor: 1
        ColorOverTime:
          serializedVersion: 2
          key0: {r: 1, g: 1, b: 1, a: 1}
          key1: {r: 1, g: 1, b: 1, a: 1}
          key2: {r: 1, g: 1, b: 1, a: 1}
          key3: {r: 0, g: 0, b: 0, a: 0}
          key4: {r: 0, g: 0, b: 0, a: 0}
          key5: {r: 0, g: 0, b: 0, a: 0}
          key6: {r: 0, g: 0, b: 0, a: 0}
          key7: {r: 0, g: 0, b: 0, a: 0}
          ctime0: 0
          ctime1: 32768
          ctime2: 65535
          ctime3: 0
          ctime4: 0
          ctime5: 0
          ctime6: 0
          ctime7: 0
          atime0: 0
          atime1: 32768
          atime2: 65535
          atime3: 0
          atime4: 0
          atime5: 0
          atime6: 0
          atime7: 0
          m_Mode: 0
          m_NumColorKeys: 3
          m_NumAlphaKeys: 3
        InstantColor: {r: 0, g: 0, b: 0, a: 0}
        ApplyInitialColorToGradientStart: 1
        ApplyInitialColorToGradientEnd: 1
        ApplyInitialColorsOnPlay: 1
        BorderLeft: 1
        BorderRight: 1
        BorderBottom: 1
        BorderTop: 1
--- !u!1 &768629957
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 768629958}
  - component: {fileID: 768629959}
  m_Layer: 0
  m_Name: Scale
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &768629958
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 768629957}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 241380878}
  m_RootOrder: 9
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &768629959
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 768629957}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6da43522623d4704e979466dc7650b65, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Feedbacks: []
  InitializationMode: 2
  AutoInitialization: 1
  SafeMode: 3
  Direction: 0
  AutoChangeDirectionOnEnd: 0
  AutoPlayOnStart: 0
  AutoPlayOnEnable: 0
  ForceTimescaleMode: 0
  ForcedTimescaleMode: 1
  DurationMultiplier: 1
  RandomizeDuration: 0
  RandomDurationMultiplier: {x: 0.5, y: 1.5}
  DisplayFullDurationDetails: 0
  PlayerTimescaleMode: 1
  OnlyPlayIfWithinRange: 0
  RangeCenter: {fileID: 0}
  RangeDistance: 5
  UseRangeFalloff: 0
  RangeFalloff:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  RemapRangeFalloff: {x: 0, y: 1}
  IgnoreRangeEvents: 0
  CooldownDuration: 0
  InitialDelay: 0
  CanPlay: 1
  CanPlayWhileAlreadyPlaying: 1
  ChanceToPlay: 100
  FeedbacksIntensity: 1
  Events:
    TriggerMMFeedbacksEvents: 0
    TriggerUnityEvents: 1
    OnPlay:
      m_PersistentCalls:
        m_Calls: []
    OnPause:
      m_PersistentCalls:
        m_Calls: []
    OnResume:
      m_PersistentCalls:
        m_Calls: []
    OnRevert:
      m_PersistentCalls:
        m_Calls: []
    OnComplete:
      m_PersistentCalls:
        m_Calls: []
    OnRestoreInitialValues:
      m_PersistentCalls:
        m_Calls: []
    OnSkipToTheEnd:
      m_PersistentCalls:
        m_Calls: []
  DebugActive: 0
  FeedbacksList:
  - rid: 5262747845385781259
  KeepPlayModeChanges: 0
  PerformanceMode: 0
  StopFeedbacksOnDisable: 0
  PlayCount: 0
  references:
    version: 2
    RefIds:
    - rid: 5262747845385781259
      type: {class: MMF_UIToolkitScale, ns: MoreMountains.FeedbacksForThirdParty,
        asm: MoreMountains.Feedbacks.UIToolkit}
      data:
        Active: 1
        UniqueID: -1377214849
        Label: UITK Scale
        ChannelMode: 0
        Channel: 0
        MMChannelDefinition: {fileID: 0}
        Chance: 100
        DisplayColor: {r: 0, g: 0, b: 0, a: 1}
        Timing:
          TimescaleMode: 0
          ExcludeFromHoldingPauses: 0
          ContributeToTotalDuration: 1
          InitialDelay: 0
          CooldownDuration: 0
          InterruptsOnStop: 1
          NumberOfRepeats: 0
          RepeatForever: 0
          DelayBetweenRepeats: 1
          MMFeedbacksDirectionCondition: 0
          PlayDirection: 0
          ConstantIntensity: 0
          UseIntensityInterval: 0
          IntensityIntervalMin: 0
          IntensityIntervalMax: 0
          Sequence: {fileID: 0}
          TrackID: 0
          Quantized: 0
          TargetBPM: 120
        AutomatedTargetAcquisition:
          Mode: 0
          ChildIndex: 0
        RandomizeOutput: 0
        RandomMultiplier: {x: 0.8, y: 1}
        RandomizeDuration: 0
        RandomDurationMultiplier: {x: 0.5, y: 2}
        UseRange: 0
        RangeDistance: 5
        UseRangeFalloff: 0
        RangeFalloff:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        RemapRangeFalloff: {x: 0, y: 1}
        Owner: {fileID: 768629959}
        DebugActive: 0
        TargetDocument: {fileID: 2114456165}
        QueryMode: 0
        Query: DemoFace
        MarkDirty: 0
        Mode: 1
        RelativeValues: 0
        AllowAdditivePlays: 0
        Duration: 0.2
        InstantValue: {x: 1, y: 1}
        AnimateX: 1
        CurveX:
          MMTweenDefinitionType: 1
          MMTweenCurve: 4
          Curve:
            serializedVersion: 2
            m_Curve:
            - serializedVersion: 3
              time: 0
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 0.25
              value: 1
              inSlope: 1.3294845
              outSlope: 1.3294845
              tangentMode: 0
              weightedMode: 0
              inWeight: 0.33333334
              outWeight: 0.33333334
            - serializedVersion: 3
              time: 1
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            m_PreInfinity: 2
            m_PostInfinity: 2
            m_RotationOrder: 4
          Initialized: 0
        CurveRemapZeroX: 1
        CurveRemapOneX: 1.5
        DestinationValueX: 1
        AnimateY: 1
        CurveY:
          MMTweenDefinitionType: 1
          MMTweenCurve: 4
          Curve:
            serializedVersion: 2
            m_Curve:
            - serializedVersion: 3
              time: 0
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 0.25
              value: 1
              inSlope: 1.3294845
              outSlope: 1.3294845
              tangentMode: 0
              weightedMode: 0
              inWeight: 0.33333334
              outWeight: 0.33333334
            - serializedVersion: 3
              time: 1
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            m_PreInfinity: 2
            m_PostInfinity: 2
            m_RotationOrder: 4
          Initialized: 0
        CurveRemapZeroY: 1
        CurveRemapOneY: 1.5
        DestinationValueY: 1
--- !u!1 &998874843
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 998874844}
  - component: {fileID: 998874845}
  m_Layer: 0
  m_Name: Size
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &998874844
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 998874843}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 241380878}
  m_RootOrder: 10
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &998874845
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 998874843}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6da43522623d4704e979466dc7650b65, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Feedbacks: []
  InitializationMode: 2
  AutoInitialization: 1
  SafeMode: 3
  Direction: 0
  AutoChangeDirectionOnEnd: 0
  AutoPlayOnStart: 0
  AutoPlayOnEnable: 0
  ForceTimescaleMode: 0
  ForcedTimescaleMode: 1
  DurationMultiplier: 1
  RandomizeDuration: 0
  RandomDurationMultiplier: {x: 0.5, y: 1.5}
  DisplayFullDurationDetails: 0
  PlayerTimescaleMode: 1
  OnlyPlayIfWithinRange: 0
  RangeCenter: {fileID: 0}
  RangeDistance: 5
  UseRangeFalloff: 0
  RangeFalloff:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  RemapRangeFalloff: {x: 0, y: 1}
  IgnoreRangeEvents: 0
  CooldownDuration: 0
  InitialDelay: 0
  CanPlay: 1
  CanPlayWhileAlreadyPlaying: 1
  ChanceToPlay: 100
  FeedbacksIntensity: 1
  Events:
    TriggerMMFeedbacksEvents: 0
    TriggerUnityEvents: 1
    OnPlay:
      m_PersistentCalls:
        m_Calls: []
    OnPause:
      m_PersistentCalls:
        m_Calls: []
    OnResume:
      m_PersistentCalls:
        m_Calls: []
    OnRevert:
      m_PersistentCalls:
        m_Calls: []
    OnComplete:
      m_PersistentCalls:
        m_Calls: []
    OnRestoreInitialValues:
      m_PersistentCalls:
        m_Calls: []
    OnSkipToTheEnd:
      m_PersistentCalls:
        m_Calls: []
  DebugActive: 0
  FeedbacksList:
  - rid: 5262747845385781260
  KeepPlayModeChanges: 0
  PerformanceMode: 0
  StopFeedbacksOnDisable: 0
  PlayCount: 0
  references:
    version: 2
    RefIds:
    - rid: 5262747845385781260
      type: {class: MMF_UIToolkitSize, ns: MoreMountains.FeedbacksForThirdParty, asm: MoreMountains.Feedbacks.UIToolkit}
      data:
        Active: 1
        UniqueID: 55115274
        Label: UITK Size
        ChannelMode: 0
        Channel: 0
        MMChannelDefinition: {fileID: 0}
        Chance: 100
        DisplayColor: {r: 0, g: 0, b: 0, a: 1}
        Timing:
          TimescaleMode: 0
          ExcludeFromHoldingPauses: 0
          ContributeToTotalDuration: 1
          InitialDelay: 0
          CooldownDuration: 0
          InterruptsOnStop: 1
          NumberOfRepeats: 0
          RepeatForever: 0
          DelayBetweenRepeats: 1
          MMFeedbacksDirectionCondition: 0
          PlayDirection: 0
          ConstantIntensity: 0
          UseIntensityInterval: 0
          IntensityIntervalMin: 0
          IntensityIntervalMax: 0
          Sequence: {fileID: 0}
          TrackID: 0
          Quantized: 0
          TargetBPM: 120
        AutomatedTargetAcquisition:
          Mode: 0
          ChildIndex: 0
        RandomizeOutput: 0
        RandomMultiplier: {x: 0.8, y: 1}
        RandomizeDuration: 0
        RandomDurationMultiplier: {x: 0.5, y: 2}
        UseRange: 0
        RangeDistance: 5
        UseRangeFalloff: 0
        RangeFalloff:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        RemapRangeFalloff: {x: 0, y: 1}
        Owner: {fileID: 998874845}
        DebugActive: 0
        TargetDocument: {fileID: 2114456165}
        QueryMode: 0
        Query: DemoFace
        MarkDirty: 0
        Mode: 1
        RelativeValues: 1
        AllowAdditivePlays: 0
        Duration: 0.2
        InstantValue: {x: 1, y: 1}
        AnimateX: 1
        CurveX:
          MMTweenDefinitionType: 1
          MMTweenCurve: 4
          Curve:
            serializedVersion: 2
            m_Curve:
            - serializedVersion: 3
              time: 0
              value: 0
              inSlope: 7.5172725
              outSlope: 7.5172725
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0.106765985
            - serializedVersion: 3
              time: 0.25
              value: 1
              inSlope: 1.192558
              outSlope: 1.192558
              tangentMode: 0
              weightedMode: 0
              inWeight: 0.33333334
              outWeight: 0.33333334
            - serializedVersion: 3
              time: 1
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            m_PreInfinity: 2
            m_PostInfinity: 2
            m_RotationOrder: 4
          Initialized: 0
        CurveRemapZeroX: 0
        CurveRemapOneX: -20
        DestinationValueX: 1
        AnimateY: 1
        CurveY:
          MMTweenDefinitionType: 1
          MMTweenCurve: 4
          Curve:
            serializedVersion: 2
            m_Curve:
            - serializedVersion: 3
              time: 0
              value: 0
              inSlope: 7.5172725
              outSlope: 7.5172725
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0.106765985
            - serializedVersion: 3
              time: 0.25
              value: 1
              inSlope: 1.192558
              outSlope: 1.192558
              tangentMode: 0
              weightedMode: 0
              inWeight: 0.33333334
              outWeight: 0.33333334
            - serializedVersion: 3
              time: 1
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            m_PreInfinity: 2
            m_PostInfinity: 2
            m_RotationOrder: 4
          Initialized: 0
        CurveRemapZeroY: 0
        CurveRemapOneY: -20
        DestinationValueY: 1
--- !u!1 &1046813961
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1046813962}
  - component: {fileID: 1046813963}
  m_Layer: 0
  m_Name: ImageTint
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1046813962
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1046813961}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 241380878}
  m_RootOrder: 6
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1046813963
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1046813961}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6da43522623d4704e979466dc7650b65, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Feedbacks: []
  InitializationMode: 2
  AutoInitialization: 1
  SafeMode: 3
  Direction: 0
  AutoChangeDirectionOnEnd: 0
  AutoPlayOnStart: 0
  AutoPlayOnEnable: 0
  ForceTimescaleMode: 0
  ForcedTimescaleMode: 1
  DurationMultiplier: 1
  RandomizeDuration: 0
  RandomDurationMultiplier: {x: 0.5, y: 1.5}
  DisplayFullDurationDetails: 0
  PlayerTimescaleMode: 1
  OnlyPlayIfWithinRange: 0
  RangeCenter: {fileID: 0}
  RangeDistance: 5
  UseRangeFalloff: 0
  RangeFalloff:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  RemapRangeFalloff: {x: 0, y: 1}
  IgnoreRangeEvents: 0
  CooldownDuration: 0
  InitialDelay: 0
  CanPlay: 1
  CanPlayWhileAlreadyPlaying: 1
  ChanceToPlay: 100
  FeedbacksIntensity: 1
  Events:
    TriggerMMFeedbacksEvents: 0
    TriggerUnityEvents: 1
    OnPlay:
      m_PersistentCalls:
        m_Calls: []
    OnPause:
      m_PersistentCalls:
        m_Calls: []
    OnResume:
      m_PersistentCalls:
        m_Calls: []
    OnRevert:
      m_PersistentCalls:
        m_Calls: []
    OnComplete:
      m_PersistentCalls:
        m_Calls: []
    OnRestoreInitialValues:
      m_PersistentCalls:
        m_Calls: []
    OnSkipToTheEnd:
      m_PersistentCalls:
        m_Calls: []
  DebugActive: 0
  FeedbacksList:
  - rid: 5262747845385781256
  KeepPlayModeChanges: 0
  PerformanceMode: 0
  StopFeedbacksOnDisable: 0
  PlayCount: 0
  references:
    version: 2
    RefIds:
    - rid: 5262747845385781256
      type: {class: MMF_UIToolkitImageTint, ns: MoreMountains.FeedbacksForThirdParty,
        asm: MoreMountains.Feedbacks.UIToolkit}
      data:
        Active: 1
        UniqueID: 150208497
        Label: UITK Image Tint
        ChannelMode: 0
        Channel: 0
        MMChannelDefinition: {fileID: 0}
        Chance: 100
        DisplayColor: {r: 0, g: 0, b: 0, a: 1}
        Timing:
          TimescaleMode: 0
          ExcludeFromHoldingPauses: 0
          ContributeToTotalDuration: 1
          InitialDelay: 0
          CooldownDuration: 0
          InterruptsOnStop: 1
          NumberOfRepeats: 0
          RepeatForever: 0
          DelayBetweenRepeats: 1
          MMFeedbacksDirectionCondition: 0
          PlayDirection: 0
          ConstantIntensity: 0
          UseIntensityInterval: 0
          IntensityIntervalMin: 0
          IntensityIntervalMax: 0
          Sequence: {fileID: 0}
          TrackID: 0
          Quantized: 0
          TargetBPM: 120
        AutomatedTargetAcquisition:
          Mode: 0
          ChildIndex: 0
        RandomizeOutput: 0
        RandomMultiplier: {x: 0.8, y: 1}
        RandomizeDuration: 0
        RandomDurationMultiplier: {x: 0.5, y: 2}
        UseRange: 0
        RangeDistance: 5
        UseRangeFalloff: 0
        RangeFalloff:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        RemapRangeFalloff: {x: 0, y: 1}
        Owner: {fileID: 1046813963}
        DebugActive: 0
        TargetDocument: {fileID: 2114456165}
        QueryMode: 0
        Query: DemoFace
        MarkDirty: 0
        Mode: 0
        Duration: 0.2
        AllowAdditivePlays: 0
        ModifyColor: 1
        ColorOverTime:
          serializedVersion: 2
          key0: {r: 1, g: 1, b: 1, a: 1}
          key1: {r: 1, g: 0, b: 0, a: 1}
          key2: {r: 1, g: 1, b: 1, a: 1}
          key3: {r: 0, g: 0, b: 0, a: 0}
          key4: {r: 0, g: 0, b: 0, a: 0}
          key5: {r: 0, g: 0, b: 0, a: 0}
          key6: {r: 0, g: 0, b: 0, a: 0}
          key7: {r: 0, g: 0, b: 0, a: 0}
          ctime0: 0
          ctime1: 32768
          ctime2: 65535
          ctime3: 0
          ctime4: 0
          ctime5: 0
          ctime6: 0
          ctime7: 0
          atime0: 0
          atime1: 32768
          atime2: 65535
          atime3: 0
          atime4: 0
          atime5: 0
          atime6: 0
          atime7: 0
          m_Mode: 0
          m_NumColorKeys: 3
          m_NumAlphaKeys: 3
        InstantColor: {r: 0, g: 0, b: 0, a: 0}
        ApplyInitialColorToGradientStart: 1
        ApplyInitialColorToGradientEnd: 1
        ApplyInitialColorsOnPlay: 1
--- !u!1 &1177841475
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1177841476}
  - component: {fileID: 1177841477}
  m_Layer: 0
  m_Name: BorderWidth
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1177841476
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1177841475}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 241380878}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1177841477
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1177841475}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6da43522623d4704e979466dc7650b65, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Feedbacks: []
  InitializationMode: 2
  AutoInitialization: 1
  SafeMode: 3
  Direction: 0
  AutoChangeDirectionOnEnd: 0
  AutoPlayOnStart: 0
  AutoPlayOnEnable: 0
  ForceTimescaleMode: 0
  ForcedTimescaleMode: 1
  DurationMultiplier: 1
  RandomizeDuration: 0
  RandomDurationMultiplier: {x: 0.5, y: 1.5}
  DisplayFullDurationDetails: 0
  PlayerTimescaleMode: 1
  OnlyPlayIfWithinRange: 0
  RangeCenter: {fileID: 0}
  RangeDistance: 5
  UseRangeFalloff: 0
  RangeFalloff:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  RemapRangeFalloff: {x: 0, y: 1}
  IgnoreRangeEvents: 0
  CooldownDuration: 0
  InitialDelay: 0
  CanPlay: 1
  CanPlayWhileAlreadyPlaying: 1
  ChanceToPlay: 100
  FeedbacksIntensity: 1
  Events:
    TriggerMMFeedbacksEvents: 0
    TriggerUnityEvents: 1
    OnPlay:
      m_PersistentCalls:
        m_Calls: []
    OnPause:
      m_PersistentCalls:
        m_Calls: []
    OnResume:
      m_PersistentCalls:
        m_Calls: []
    OnRevert:
      m_PersistentCalls:
        m_Calls: []
    OnComplete:
      m_PersistentCalls:
        m_Calls: []
    OnRestoreInitialValues:
      m_PersistentCalls:
        m_Calls: []
    OnSkipToTheEnd:
      m_PersistentCalls:
        m_Calls: []
  DebugActive: 0
  FeedbacksList:
  - rid: 5262747845385781253
  KeepPlayModeChanges: 0
  PerformanceMode: 0
  StopFeedbacksOnDisable: 0
  PlayCount: 0
  references:
    version: 2
    RefIds:
    - rid: 5262747845385781253
      type: {class: MMF_UIToolkitBorderWidth, ns: MoreMountains.FeedbacksForThirdParty,
        asm: MoreMountains.Feedbacks.UIToolkit}
      data:
        Active: 1
        UniqueID: 1721156019
        Label: UITK Border Width
        ChannelMode: 0
        Channel: 0
        MMChannelDefinition: {fileID: 0}
        Chance: 100
        DisplayColor: {r: 0, g: 0, b: 0, a: 1}
        Timing:
          TimescaleMode: 0
          ExcludeFromHoldingPauses: 0
          ContributeToTotalDuration: 1
          InitialDelay: 0
          CooldownDuration: 0
          InterruptsOnStop: 1
          NumberOfRepeats: 0
          RepeatForever: 0
          DelayBetweenRepeats: 1
          MMFeedbacksDirectionCondition: 0
          PlayDirection: 0
          ConstantIntensity: 0
          UseIntensityInterval: 0
          IntensityIntervalMin: 0
          IntensityIntervalMax: 0
          Sequence: {fileID: 0}
          TrackID: 0
          Quantized: 0
          TargetBPM: 120
        AutomatedTargetAcquisition:
          Mode: 0
          ChildIndex: 0
        RandomizeOutput: 0
        RandomMultiplier: {x: 0.8, y: 1}
        RandomizeDuration: 0
        RandomDurationMultiplier: {x: 0.5, y: 2}
        UseRange: 0
        RangeDistance: 5
        UseRangeFalloff: 0
        RangeFalloff:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        RemapRangeFalloff: {x: 0, y: 1}
        Owner: {fileID: 1177841477}
        DebugActive: 0
        TargetDocument: {fileID: 2114456165}
        QueryMode: 0
        Query: DemoText
        MarkDirty: 0
        Mode: 1
        RelativeValue: 0
        AllowAdditivePlays: 0
        Duration: 0.2
        InstantValue: 1
        Curve:
          MMTweenDefinitionType: 1
          MMTweenCurve: 4
          Curve:
            serializedVersion: 2
            m_Curve:
            - serializedVersion: 3
              time: 0
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 0.5
              value: 1
              inSlope: 1.497552
              outSlope: 1.497552
              tangentMode: 0
              weightedMode: 0
              inWeight: 0.33333334
              outWeight: 0.33333334
            - serializedVersion: 3
              time: 1
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            m_PreInfinity: 2
            m_PostInfinity: 2
            m_RotationOrder: 4
          Initialized: 0
        CurveRemapZero: 2
        CurveRemapOne: 20
        DestinationValue: 1
        Left: 1
        Right: 1
        Top: 1
        Bottom: 1
--- !u!1 &1191851716
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1191851717}
  - component: {fileID: 1191851718}
  m_Layer: 0
  m_Name: Visible
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1191851717
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1191851716}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 241380878}
  m_RootOrder: 15
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1191851718
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1191851716}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6da43522623d4704e979466dc7650b65, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Feedbacks: []
  InitializationMode: 2
  AutoInitialization: 1
  SafeMode: 3
  Direction: 0
  AutoChangeDirectionOnEnd: 0
  AutoPlayOnStart: 0
  AutoPlayOnEnable: 0
  ForceTimescaleMode: 0
  ForcedTimescaleMode: 1
  DurationMultiplier: 1
  RandomizeDuration: 0
  RandomDurationMultiplier: {x: 0.5, y: 1.5}
  DisplayFullDurationDetails: 0
  PlayerTimescaleMode: 1
  OnlyPlayIfWithinRange: 0
  RangeCenter: {fileID: 0}
  RangeDistance: 5
  UseRangeFalloff: 0
  RangeFalloff:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  RemapRangeFalloff: {x: 0, y: 1}
  IgnoreRangeEvents: 0
  CooldownDuration: 0
  InitialDelay: 0
  CanPlay: 1
  CanPlayWhileAlreadyPlaying: 1
  ChanceToPlay: 100
  FeedbacksIntensity: 1
  Events:
    TriggerMMFeedbacksEvents: 0
    TriggerUnityEvents: 1
    OnPlay:
      m_PersistentCalls:
        m_Calls: []
    OnPause:
      m_PersistentCalls:
        m_Calls: []
    OnResume:
      m_PersistentCalls:
        m_Calls: []
    OnRevert:
      m_PersistentCalls:
        m_Calls: []
    OnComplete:
      m_PersistentCalls:
        m_Calls: []
    OnRestoreInitialValues:
      m_PersistentCalls:
        m_Calls: []
    OnSkipToTheEnd:
      m_PersistentCalls:
        m_Calls: []
  DebugActive: 0
  FeedbacksList:
  - rid: 5262747845385781266
  KeepPlayModeChanges: 0
  PerformanceMode: 0
  StopFeedbacksOnDisable: 0
  PlayCount: 0
  references:
    version: 2
    RefIds:
    - rid: 5262747845385781266
      type: {class: MMF_UIToolkitVisible, ns: MoreMountains.FeedbacksForThirdParty,
        asm: MoreMountains.Feedbacks.UIToolkit}
      data:
        Active: 1
        UniqueID: -1052237079
        Label: UITK Visible
        ChannelMode: 0
        Channel: 0
        MMChannelDefinition: {fileID: 0}
        Chance: 100
        DisplayColor: {r: 0, g: 0, b: 0, a: 1}
        Timing:
          TimescaleMode: 0
          ExcludeFromHoldingPauses: 0
          ContributeToTotalDuration: 1
          InitialDelay: 0
          CooldownDuration: 0
          InterruptsOnStop: 1
          NumberOfRepeats: 0
          RepeatForever: 0
          DelayBetweenRepeats: 1
          MMFeedbacksDirectionCondition: 0
          PlayDirection: 0
          ConstantIntensity: 0
          UseIntensityInterval: 0
          IntensityIntervalMin: 0
          IntensityIntervalMax: 0
          Sequence: {fileID: 0}
          TrackID: 0
          Quantized: 0
          TargetBPM: 120
        AutomatedTargetAcquisition:
          Mode: 0
          ChildIndex: 0
        RandomizeOutput: 0
        RandomMultiplier: {x: 0.8, y: 1}
        RandomizeDuration: 0
        RandomDurationMultiplier: {x: 0.5, y: 2}
        UseRange: 0
        RangeDistance: 5
        UseRangeFalloff: 0
        RangeFalloff:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        RemapRangeFalloff: {x: 0, y: 1}
        Owner: {fileID: 1191851718}
        DebugActive: 0
        TargetDocument: {fileID: 2114456165}
        QueryMode: 0
        Query: DemoText
        MarkDirty: 0
        Mode: 1
        Visible: 0
--- !u!1 &1231895993
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1231895994}
  - component: {fileID: 1231895995}
  m_Layer: 0
  m_Name: Opacity
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1231895994
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1231895993}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 241380878}
  m_RootOrder: 7
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1231895995
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1231895993}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6da43522623d4704e979466dc7650b65, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Feedbacks: []
  InitializationMode: 2
  AutoInitialization: 1
  SafeMode: 3
  Direction: 0
  AutoChangeDirectionOnEnd: 0
  AutoPlayOnStart: 0
  AutoPlayOnEnable: 0
  ForceTimescaleMode: 0
  ForcedTimescaleMode: 1
  DurationMultiplier: 1
  RandomizeDuration: 0
  RandomDurationMultiplier: {x: 0.5, y: 1.5}
  DisplayFullDurationDetails: 0
  PlayerTimescaleMode: 1
  OnlyPlayIfWithinRange: 0
  RangeCenter: {fileID: 0}
  RangeDistance: 5
  UseRangeFalloff: 0
  RangeFalloff:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  RemapRangeFalloff: {x: 0, y: 1}
  IgnoreRangeEvents: 0
  CooldownDuration: 0
  InitialDelay: 0
  CanPlay: 1
  CanPlayWhileAlreadyPlaying: 1
  ChanceToPlay: 100
  FeedbacksIntensity: 1
  Events:
    TriggerMMFeedbacksEvents: 0
    TriggerUnityEvents: 1
    OnPlay:
      m_PersistentCalls:
        m_Calls: []
    OnPause:
      m_PersistentCalls:
        m_Calls: []
    OnResume:
      m_PersistentCalls:
        m_Calls: []
    OnRevert:
      m_PersistentCalls:
        m_Calls: []
    OnComplete:
      m_PersistentCalls:
        m_Calls: []
    OnRestoreInitialValues:
      m_PersistentCalls:
        m_Calls: []
    OnSkipToTheEnd:
      m_PersistentCalls:
        m_Calls: []
  DebugActive: 0
  FeedbacksList:
  - rid: 5262747845385781257
  KeepPlayModeChanges: 0
  PerformanceMode: 0
  StopFeedbacksOnDisable: 0
  PlayCount: 0
  references:
    version: 2
    RefIds:
    - rid: 5262747845385781257
      type: {class: MMF_UIToolkitOpacity, ns: MoreMountains.FeedbacksForThirdParty,
        asm: MoreMountains.Feedbacks.UIToolkit}
      data:
        Active: 1
        UniqueID: 82445877
        Label: UITK Opacity
        ChannelMode: 0
        Channel: 0
        MMChannelDefinition: {fileID: 0}
        Chance: 100
        DisplayColor: {r: 0, g: 0, b: 0, a: 1}
        Timing:
          TimescaleMode: 0
          ExcludeFromHoldingPauses: 0
          ContributeToTotalDuration: 1
          InitialDelay: 0
          CooldownDuration: 0
          InterruptsOnStop: 1
          NumberOfRepeats: 0
          RepeatForever: 0
          DelayBetweenRepeats: 1
          MMFeedbacksDirectionCondition: 0
          PlayDirection: 0
          ConstantIntensity: 0
          UseIntensityInterval: 0
          IntensityIntervalMin: 0
          IntensityIntervalMax: 0
          Sequence: {fileID: 0}
          TrackID: 0
          Quantized: 0
          TargetBPM: 120
        AutomatedTargetAcquisition:
          Mode: 0
          ChildIndex: 0
        RandomizeOutput: 0
        RandomMultiplier: {x: 0.8, y: 1}
        RandomizeDuration: 0
        RandomDurationMultiplier: {x: 0.5, y: 2}
        UseRange: 0
        RangeDistance: 5
        UseRangeFalloff: 0
        RangeFalloff:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        RemapRangeFalloff: {x: 0, y: 1}
        Owner: {fileID: 1231895995}
        DebugActive: 0
        TargetDocument: {fileID: 2114456165}
        QueryMode: 0
        Query: DemoText
        MarkDirty: 0
        Mode: 1
        RelativeValue: 0
        AllowAdditivePlays: 0
        Duration: 0.2
        InstantValue: 1
        Curve:
          MMTweenDefinitionType: 0
          MMTweenCurve: 4
          Curve:
            serializedVersion: 2
            m_Curve:
            - serializedVersion: 3
              time: 0
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 1
              value: 1
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            m_PreInfinity: 2
            m_PostInfinity: 2
            m_RotationOrder: 4
          Initialized: 0
        CurveRemapZero: 0
        CurveRemapOne: 1
        DestinationValue: 1
--- !u!1 &1285911388
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1285911391}
  - component: {fileID: 1285911390}
  - component: {fileID: 1285911389}
  m_Layer: 0
  m_Name: Main Camera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!81 &1285911389
AudioListener:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1285911388}
  m_Enabled: 1
--- !u!20 &1285911390
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1285911388}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 1
  m_BackGroundColor: {r: 0.19215687, g: 0.3019608, b: 0.4745098, a: 0}
  m_projectionMatrixMode: 1
  m_GateFitMode: 2
  m_FOVAxisMode: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_FocalLength: 50
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.3
  far clip plane: 1000
  field of view: 60
  orthographic: 0
  orthographic size: 5
  m_Depth: -1
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 1
  m_AllowMSAA: 1
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 0
  m_OcclusionCulling: 1
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!4 &1285911391
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1285911388}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 1, z: -10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1345306585
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1345306586}
  - component: {fileID: 1345306587}
  m_Layer: 0
  m_Name: BorderRadius
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1345306586
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1345306585}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 241380878}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1345306587
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1345306585}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6da43522623d4704e979466dc7650b65, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Feedbacks: []
  InitializationMode: 2
  AutoInitialization: 1
  SafeMode: 3
  Direction: 0
  AutoChangeDirectionOnEnd: 0
  AutoPlayOnStart: 0
  AutoPlayOnEnable: 0
  ForceTimescaleMode: 0
  ForcedTimescaleMode: 1
  DurationMultiplier: 1
  RandomizeDuration: 0
  RandomDurationMultiplier: {x: 0.5, y: 1.5}
  DisplayFullDurationDetails: 0
  PlayerTimescaleMode: 1
  OnlyPlayIfWithinRange: 0
  RangeCenter: {fileID: 0}
  RangeDistance: 5
  UseRangeFalloff: 0
  RangeFalloff:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  RemapRangeFalloff: {x: 0, y: 1}
  IgnoreRangeEvents: 0
  CooldownDuration: 0
  InitialDelay: 0
  CanPlay: 1
  CanPlayWhileAlreadyPlaying: 1
  ChanceToPlay: 100
  FeedbacksIntensity: 1
  Events:
    TriggerMMFeedbacksEvents: 0
    TriggerUnityEvents: 1
    OnPlay:
      m_PersistentCalls:
        m_Calls: []
    OnPause:
      m_PersistentCalls:
        m_Calls: []
    OnResume:
      m_PersistentCalls:
        m_Calls: []
    OnRevert:
      m_PersistentCalls:
        m_Calls: []
    OnComplete:
      m_PersistentCalls:
        m_Calls: []
    OnRestoreInitialValues:
      m_PersistentCalls:
        m_Calls: []
    OnSkipToTheEnd:
      m_PersistentCalls:
        m_Calls: []
  DebugActive: 0
  FeedbacksList:
  - rid: 5262747845385781252
  KeepPlayModeChanges: 0
  PerformanceMode: 0
  StopFeedbacksOnDisable: 0
  PlayCount: 0
  references:
    version: 2
    RefIds:
    - rid: 5262747845385781252
      type: {class: MMF_UIToolkitBorderRadius, ns: MoreMountains.FeedbacksForThirdParty,
        asm: MoreMountains.Feedbacks.UIToolkit}
      data:
        Active: 1
        UniqueID: -1940442478
        Label: UITK Border Radius
        ChannelMode: 0
        Channel: 0
        MMChannelDefinition: {fileID: 0}
        Chance: 100
        DisplayColor: {r: 0, g: 0, b: 0, a: 1}
        Timing:
          TimescaleMode: 0
          ExcludeFromHoldingPauses: 0
          ContributeToTotalDuration: 1
          InitialDelay: 0
          CooldownDuration: 0
          InterruptsOnStop: 1
          NumberOfRepeats: 0
          RepeatForever: 0
          DelayBetweenRepeats: 1
          MMFeedbacksDirectionCondition: 0
          PlayDirection: 0
          ConstantIntensity: 0
          UseIntensityInterval: 0
          IntensityIntervalMin: 0
          IntensityIntervalMax: 0
          Sequence: {fileID: 0}
          TrackID: 0
          Quantized: 0
          TargetBPM: 120
        AutomatedTargetAcquisition:
          Mode: 0
          ChildIndex: 0
        RandomizeOutput: 0
        RandomMultiplier: {x: 0.8, y: 1}
        RandomizeDuration: 0
        RandomDurationMultiplier: {x: 0.5, y: 2}
        UseRange: 0
        RangeDistance: 5
        UseRangeFalloff: 0
        RangeFalloff:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        RemapRangeFalloff: {x: 0, y: 1}
        Owner: {fileID: 1345306587}
        DebugActive: 0
        TargetDocument: {fileID: 2114456165}
        QueryMode: 0
        Query: DemoText
        MarkDirty: 0
        Mode: 1
        RelativeValue: 0
        AllowAdditivePlays: 0
        Duration: 0.6
        InstantValue: 1
        Curve:
          MMTweenDefinitionType: 1
          MMTweenCurve: 4
          Curve:
            serializedVersion: 2
            m_Curve:
            - serializedVersion: 3
              time: 0
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 0.5
              value: 1
              inSlope: 1.4949656
              outSlope: 1.4949656
              tangentMode: 0
              weightedMode: 0
              inWeight: 0.33333334
              outWeight: 0.33333334
            - serializedVersion: 3
              time: 1
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            m_PreInfinity: 2
            m_PostInfinity: 2
            m_RotationOrder: 4
          Initialized: 0
        CurveRemapZero: 20
        CurveRemapOne: 100
        DestinationValue: 1
        BottomLeft: 1
        BottomRight: 1
        TopLeft: 1
        TopRight: 1
--- !u!1 &1404847136
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1404847137}
  - component: {fileID: 1404847138}
  m_Layer: 0
  m_Name: Stylesheet
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1404847137
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1404847136}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 241380878}
  m_RootOrder: 11
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1404847138
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1404847136}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6da43522623d4704e979466dc7650b65, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Feedbacks: []
  InitializationMode: 2
  AutoInitialization: 1
  SafeMode: 3
  Direction: 0
  AutoChangeDirectionOnEnd: 0
  AutoPlayOnStart: 0
  AutoPlayOnEnable: 0
  ForceTimescaleMode: 0
  ForcedTimescaleMode: 1
  DurationMultiplier: 1
  RandomizeDuration: 0
  RandomDurationMultiplier: {x: 0.5, y: 1.5}
  DisplayFullDurationDetails: 0
  PlayerTimescaleMode: 1
  OnlyPlayIfWithinRange: 0
  RangeCenter: {fileID: 0}
  RangeDistance: 5
  UseRangeFalloff: 0
  RangeFalloff:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  RemapRangeFalloff: {x: 0, y: 1}
  IgnoreRangeEvents: 0
  CooldownDuration: 0
  InitialDelay: 0
  CanPlay: 1
  CanPlayWhileAlreadyPlaying: 1
  ChanceToPlay: 100
  FeedbacksIntensity: 1
  Events:
    TriggerMMFeedbacksEvents: 0
    TriggerUnityEvents: 1
    OnPlay:
      m_PersistentCalls:
        m_Calls: []
    OnPause:
      m_PersistentCalls:
        m_Calls: []
    OnResume:
      m_PersistentCalls:
        m_Calls: []
    OnRevert:
      m_PersistentCalls:
        m_Calls: []
    OnComplete:
      m_PersistentCalls:
        m_Calls: []
    OnRestoreInitialValues:
      m_PersistentCalls:
        m_Calls: []
    OnSkipToTheEnd:
      m_PersistentCalls:
        m_Calls: []
  DebugActive: 0
  FeedbacksList:
  - rid: 5262747845385781261
  KeepPlayModeChanges: 0
  PerformanceMode: 0
  StopFeedbacksOnDisable: 0
  PlayCount: 0
  references:
    version: 2
    RefIds:
    - rid: 5262747845385781261
      type: {class: MMF_UIToolkitStylesheet, ns: MoreMountains.FeedbacksForThirdParty,
        asm: MoreMountains.Feedbacks.UIToolkit}
      data:
        Active: 1
        UniqueID: -412821863
        Label: UITK Stylesheet
        ChannelMode: 0
        Channel: 0
        MMChannelDefinition: {fileID: 0}
        Chance: 100
        DisplayColor: {r: 0, g: 0, b: 0, a: 1}
        Timing:
          TimescaleMode: 0
          ExcludeFromHoldingPauses: 0
          ContributeToTotalDuration: 1
          InitialDelay: 0
          CooldownDuration: 0
          InterruptsOnStop: 1
          NumberOfRepeats: 0
          RepeatForever: 0
          DelayBetweenRepeats: 1
          MMFeedbacksDirectionCondition: 0
          PlayDirection: 0
          ConstantIntensity: 0
          UseIntensityInterval: 0
          IntensityIntervalMin: 0
          IntensityIntervalMax: 0
          Sequence: {fileID: 0}
          TrackID: 0
          Quantized: 0
          TargetBPM: 120
        AutomatedTargetAcquisition:
          Mode: 0
          ChildIndex: 0
        RandomizeOutput: 0
        RandomMultiplier: {x: 0.8, y: 1}
        RandomizeDuration: 0
        RandomDurationMultiplier: {x: 0.5, y: 2}
        UseRange: 0
        RangeDistance: 5
        UseRangeFalloff: 0
        RangeFalloff:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        RemapRangeFalloff: {x: 0, y: 1}
        Owner: {fileID: 1404847138}
        DebugActive: 0
        TargetDocument: {fileID: 2114456165}
        QueryMode: 0
        Query: MainContainer
        MarkDirty: 0
        NewStylesheet: {fileID: 7433441132597879392, guid: a34739bef9f96964ea9c4e74bc76b121,
          type: 3}
--- !u!1 &1635157580
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1635157581}
  - component: {fileID: 1635157582}
  m_Layer: 0
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1635157581
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1635157580}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 241380878}
  m_RootOrder: 12
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1635157582
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1635157580}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6da43522623d4704e979466dc7650b65, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Feedbacks: []
  InitializationMode: 2
  AutoInitialization: 1
  SafeMode: 3
  Direction: 0
  AutoChangeDirectionOnEnd: 0
  AutoPlayOnStart: 0
  AutoPlayOnEnable: 0
  ForceTimescaleMode: 0
  ForcedTimescaleMode: 1
  DurationMultiplier: 1
  RandomizeDuration: 0
  RandomDurationMultiplier: {x: 0.5, y: 1.5}
  DisplayFullDurationDetails: 0
  PlayerTimescaleMode: 1
  OnlyPlayIfWithinRange: 0
  RangeCenter: {fileID: 0}
  RangeDistance: 5
  UseRangeFalloff: 0
  RangeFalloff:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  RemapRangeFalloff: {x: 0, y: 1}
  IgnoreRangeEvents: 0
  CooldownDuration: 0
  InitialDelay: 0
  CanPlay: 1
  CanPlayWhileAlreadyPlaying: 1
  ChanceToPlay: 100
  FeedbacksIntensity: 1
  Events:
    TriggerMMFeedbacksEvents: 0
    TriggerUnityEvents: 1
    OnPlay:
      m_PersistentCalls:
        m_Calls: []
    OnPause:
      m_PersistentCalls:
        m_Calls: []
    OnResume:
      m_PersistentCalls:
        m_Calls: []
    OnRevert:
      m_PersistentCalls:
        m_Calls: []
    OnComplete:
      m_PersistentCalls:
        m_Calls: []
    OnRestoreInitialValues:
      m_PersistentCalls:
        m_Calls: []
    OnSkipToTheEnd:
      m_PersistentCalls:
        m_Calls: []
  DebugActive: 0
  FeedbacksList:
  - rid: 5262747845385781262
  KeepPlayModeChanges: 0
  PerformanceMode: 0
  StopFeedbacksOnDisable: 0
  PlayCount: 0
  references:
    version: 2
    RefIds:
    - rid: 5262747845385781262
      type: {class: MMF_UIToolkitText, ns: MoreMountains.FeedbacksForThirdParty, asm: MoreMountains.Feedbacks.UIToolkit}
      data:
        Active: 1
        UniqueID: -1661671583
        Label: UITK Text
        ChannelMode: 0
        Channel: 0
        MMChannelDefinition: {fileID: 0}
        Chance: 100
        DisplayColor: {r: 0, g: 0, b: 0, a: 1}
        Timing:
          TimescaleMode: 0
          ExcludeFromHoldingPauses: 0
          ContributeToTotalDuration: 1
          InitialDelay: 0
          CooldownDuration: 0
          InterruptsOnStop: 1
          NumberOfRepeats: 0
          RepeatForever: 0
          DelayBetweenRepeats: 1
          MMFeedbacksDirectionCondition: 0
          PlayDirection: 0
          ConstantIntensity: 0
          UseIntensityInterval: 0
          IntensityIntervalMin: 0
          IntensityIntervalMax: 0
          Sequence: {fileID: 0}
          TrackID: 0
          Quantized: 0
          TargetBPM: 120
        AutomatedTargetAcquisition:
          Mode: 0
          ChildIndex: 0
        RandomizeOutput: 0
        RandomMultiplier: {x: 0.8, y: 1}
        RandomizeDuration: 0
        RandomDurationMultiplier: {x: 0.5, y: 2}
        UseRange: 0
        RangeDistance: 5
        UseRangeFalloff: 0
        RangeFalloff:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        RemapRangeFalloff: {x: 0, y: 1}
        Owner: {fileID: 1635157582}
        DebugActive: 0
        TargetDocument: {fileID: 2114456165}
        QueryMode: 0
        Query: DemoText
        MarkDirty: 0
        NewText: I hope you'll have fun with Feel and UI Toolkit!
--- !u!1 &1712588493
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1712588494}
  - component: {fileID: 1712588495}
  m_Layer: 0
  m_Name: Class
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1712588494
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1712588493}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 241380878}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1712588495
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1712588493}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6da43522623d4704e979466dc7650b65, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Feedbacks: []
  InitializationMode: 2
  AutoInitialization: 1
  SafeMode: 3
  Direction: 0
  AutoChangeDirectionOnEnd: 0
  AutoPlayOnStart: 0
  AutoPlayOnEnable: 0
  ForceTimescaleMode: 0
  ForcedTimescaleMode: 1
  DurationMultiplier: 1
  RandomizeDuration: 0
  RandomDurationMultiplier: {x: 0.5, y: 1.5}
  DisplayFullDurationDetails: 0
  PlayerTimescaleMode: 1
  OnlyPlayIfWithinRange: 0
  RangeCenter: {fileID: 0}
  RangeDistance: 5
  UseRangeFalloff: 0
  RangeFalloff:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  RemapRangeFalloff: {x: 0, y: 1}
  IgnoreRangeEvents: 0
  CooldownDuration: 0
  InitialDelay: 0
  CanPlay: 1
  CanPlayWhileAlreadyPlaying: 1
  ChanceToPlay: 100
  FeedbacksIntensity: 1
  Events:
    TriggerMMFeedbacksEvents: 0
    TriggerUnityEvents: 1
    OnPlay:
      m_PersistentCalls:
        m_Calls: []
    OnPause:
      m_PersistentCalls:
        m_Calls: []
    OnResume:
      m_PersistentCalls:
        m_Calls: []
    OnRevert:
      m_PersistentCalls:
        m_Calls: []
    OnComplete:
      m_PersistentCalls:
        m_Calls: []
    OnRestoreInitialValues:
      m_PersistentCalls:
        m_Calls: []
    OnSkipToTheEnd:
      m_PersistentCalls:
        m_Calls: []
  DebugActive: 0
  FeedbacksList:
  - rid: 5262747845385781254
  KeepPlayModeChanges: 0
  PerformanceMode: 0
  StopFeedbacksOnDisable: 0
  PlayCount: 0
  references:
    version: 2
    RefIds:
    - rid: 5262747845385781254
      type: {class: MMF_UIToolkitClass, ns: MoreMountains.FeedbacksForThirdParty,
        asm: MoreMountains.Feedbacks.UIToolkit}
      data:
        Active: 1
        UniqueID: -828177984
        Label: UITK Class
        ChannelMode: 0
        Channel: 0
        MMChannelDefinition: {fileID: 0}
        Chance: 100
        DisplayColor: {r: 0, g: 0, b: 0, a: 1}
        Timing:
          TimescaleMode: 0
          ExcludeFromHoldingPauses: 0
          ContributeToTotalDuration: 1
          InitialDelay: 0
          CooldownDuration: 0
          InterruptsOnStop: 1
          NumberOfRepeats: 0
          RepeatForever: 0
          DelayBetweenRepeats: 1
          MMFeedbacksDirectionCondition: 0
          PlayDirection: 0
          ConstantIntensity: 0
          UseIntensityInterval: 0
          IntensityIntervalMin: 0
          IntensityIntervalMax: 0
          Sequence: {fileID: 0}
          TrackID: 0
          Quantized: 0
          TargetBPM: 120
        AutomatedTargetAcquisition:
          Mode: 0
          ChildIndex: 0
        RandomizeOutput: 0
        RandomMultiplier: {x: 0.8, y: 1}
        RandomizeDuration: 0
        RandomDurationMultiplier: {x: 0.5, y: 2}
        UseRange: 0
        RangeDistance: 5
        UseRangeFalloff: 0
        RangeFalloff:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        RemapRangeFalloff: {x: 0, y: 1}
        Owner: {fileID: 1712588495}
        DebugActive: 0
        TargetDocument: {fileID: 2114456165}
        QueryMode: 0
        Query: DemoText
        MarkDirty: 0
        Mode: 2
        ClassName: MainTextAlt
        Enable: 1
--- !u!1 &1850447476
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1850447477}
  - component: {fileID: 1850447478}
  m_Layer: 0
  m_Name: Translate
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1850447477
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1850447476}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 241380878}
  m_RootOrder: 14
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1850447478
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1850447476}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6da43522623d4704e979466dc7650b65, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Feedbacks: []
  InitializationMode: 2
  AutoInitialization: 1
  SafeMode: 3
  Direction: 0
  AutoChangeDirectionOnEnd: 0
  AutoPlayOnStart: 0
  AutoPlayOnEnable: 0
  ForceTimescaleMode: 0
  ForcedTimescaleMode: 1
  DurationMultiplier: 1
  RandomizeDuration: 0
  RandomDurationMultiplier: {x: 0.5, y: 1.5}
  DisplayFullDurationDetails: 0
  PlayerTimescaleMode: 1
  OnlyPlayIfWithinRange: 0
  RangeCenter: {fileID: 0}
  RangeDistance: 5
  UseRangeFalloff: 0
  RangeFalloff:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  RemapRangeFalloff: {x: 0, y: 1}
  IgnoreRangeEvents: 0
  CooldownDuration: 0
  InitialDelay: 0
  CanPlay: 1
  CanPlayWhileAlreadyPlaying: 1
  ChanceToPlay: 100
  FeedbacksIntensity: 1
  Events:
    TriggerMMFeedbacksEvents: 0
    TriggerUnityEvents: 1
    OnPlay:
      m_PersistentCalls:
        m_Calls: []
    OnPause:
      m_PersistentCalls:
        m_Calls: []
    OnResume:
      m_PersistentCalls:
        m_Calls: []
    OnRevert:
      m_PersistentCalls:
        m_Calls: []
    OnComplete:
      m_PersistentCalls:
        m_Calls: []
    OnRestoreInitialValues:
      m_PersistentCalls:
        m_Calls: []
    OnSkipToTheEnd:
      m_PersistentCalls:
        m_Calls: []
  DebugActive: 0
  FeedbacksList:
  - rid: 5262747845385781265
  KeepPlayModeChanges: 0
  PerformanceMode: 0
  StopFeedbacksOnDisable: 0
  PlayCount: 0
  references:
    version: 2
    RefIds:
    - rid: 5262747845385781265
      type: {class: MMF_UIToolkitTranslate, ns: MoreMountains.FeedbacksForThirdParty,
        asm: MoreMountains.Feedbacks.UIToolkit}
      data:
        Active: 1
        UniqueID: 1569068004
        Label: UITK Translate
        ChannelMode: 0
        Channel: 0
        MMChannelDefinition: {fileID: 0}
        Chance: 100
        DisplayColor: {r: 0, g: 0, b: 0, a: 1}
        Timing:
          TimescaleMode: 0
          ExcludeFromHoldingPauses: 0
          ContributeToTotalDuration: 1
          InitialDelay: 0
          CooldownDuration: 0
          InterruptsOnStop: 1
          NumberOfRepeats: 0
          RepeatForever: 0
          DelayBetweenRepeats: 1
          MMFeedbacksDirectionCondition: 0
          PlayDirection: 0
          ConstantIntensity: 0
          UseIntensityInterval: 0
          IntensityIntervalMin: 0
          IntensityIntervalMax: 0
          Sequence: {fileID: 0}
          TrackID: 0
          Quantized: 0
          TargetBPM: 120
        AutomatedTargetAcquisition:
          Mode: 0
          ChildIndex: 0
        RandomizeOutput: 0
        RandomMultiplier: {x: 0.8, y: 1}
        RandomizeDuration: 0
        RandomDurationMultiplier: {x: 0.5, y: 2}
        UseRange: 0
        RangeDistance: 5
        UseRangeFalloff: 0
        RangeFalloff:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        RemapRangeFalloff: {x: 0, y: 1}
        Owner: {fileID: 1850447478}
        DebugActive: 0
        TargetDocument: {fileID: 2114456165}
        QueryMode: 0
        Query: DemoText
        MarkDirty: 0
        Mode: 1
        RelativeValues: 0
        AllowAdditivePlays: 0
        Duration: 0.2
        InstantValue: {x: 1, y: 1}
        AnimateX: 0
        CurveX:
          MMTweenDefinitionType: 0
          MMTweenCurve: 4
          Curve:
            serializedVersion: 2
            m_Curve:
            - serializedVersion: 3
              time: 0
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 1
              value: 1
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            m_PreInfinity: 2
            m_PostInfinity: 2
            m_RotationOrder: 4
          Initialized: 0
        CurveRemapZeroX: 0
        CurveRemapOneX: 1
        DestinationValueX: 1
        AnimateY: 1
        CurveY:
          MMTweenDefinitionType: 1
          MMTweenCurve: 4
          Curve:
            serializedVersion: 2
            m_Curve:
            - serializedVersion: 3
              time: 0
              value: 0
              inSlope: 6.7613773
              outSlope: 6.7613773
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0.15890752
            - serializedVersion: 3
              time: 0.25
              value: 1
              inSlope: 1.0528295
              outSlope: 1.0528295
              tangentMode: 0
              weightedMode: 0
              inWeight: 0.33333334
              outWeight: 0.33333334
            - serializedVersion: 3
              time: 1
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            m_PreInfinity: 2
            m_PostInfinity: 2
            m_RotationOrder: 4
          Initialized: 0
        CurveRemapZeroY: 0
        CurveRemapOneY: -20
        DestinationValueY: 1
        LengthUnitX: 0
        LengthUnitY: 0
--- !u!1 &2010915429
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2010915430}
  - component: {fileID: 2010915431}
  m_Layer: 0
  m_Name: TextColor
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2010915430
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2010915429}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 241380878}
  m_RootOrder: 13
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2010915431
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2010915429}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6da43522623d4704e979466dc7650b65, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Feedbacks: []
  InitializationMode: 2
  AutoInitialization: 1
  SafeMode: 3
  Direction: 0
  AutoChangeDirectionOnEnd: 0
  AutoPlayOnStart: 0
  AutoPlayOnEnable: 0
  ForceTimescaleMode: 0
  ForcedTimescaleMode: 1
  DurationMultiplier: 1
  RandomizeDuration: 0
  RandomDurationMultiplier: {x: 0.5, y: 1.5}
  DisplayFullDurationDetails: 0
  PlayerTimescaleMode: 1
  OnlyPlayIfWithinRange: 0
  RangeCenter: {fileID: 0}
  RangeDistance: 5
  UseRangeFalloff: 0
  RangeFalloff:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  RemapRangeFalloff: {x: 0, y: 1}
  IgnoreRangeEvents: 0
  CooldownDuration: 0
  InitialDelay: 0
  CanPlay: 1
  CanPlayWhileAlreadyPlaying: 1
  ChanceToPlay: 100
  FeedbacksIntensity: 1
  Events:
    TriggerMMFeedbacksEvents: 0
    TriggerUnityEvents: 1
    OnPlay:
      m_PersistentCalls:
        m_Calls: []
    OnPause:
      m_PersistentCalls:
        m_Calls: []
    OnResume:
      m_PersistentCalls:
        m_Calls: []
    OnRevert:
      m_PersistentCalls:
        m_Calls: []
    OnComplete:
      m_PersistentCalls:
        m_Calls: []
    OnRestoreInitialValues:
      m_PersistentCalls:
        m_Calls: []
    OnSkipToTheEnd:
      m_PersistentCalls:
        m_Calls: []
  DebugActive: 0
  FeedbacksList:
  - rid: 5262747845385781263
  KeepPlayModeChanges: 0
  PerformanceMode: 0
  StopFeedbacksOnDisable: 0
  PlayCount: 0
  references:
    version: 2
    RefIds:
    - rid: 5262747845385781263
      type: {class: MMF_UIToolkitTextColor, ns: MoreMountains.FeedbacksForThirdParty,
        asm: MoreMountains.Feedbacks.UIToolkit}
      data:
        Active: 1
        UniqueID: 824449960
        Label: UITK Text Color
        ChannelMode: 0
        Channel: 0
        MMChannelDefinition: {fileID: 0}
        Chance: 100
        DisplayColor: {r: 0, g: 0, b: 0, a: 1}
        Timing:
          TimescaleMode: 0
          ExcludeFromHoldingPauses: 0
          ContributeToTotalDuration: 1
          InitialDelay: 0
          CooldownDuration: 0
          InterruptsOnStop: 1
          NumberOfRepeats: 0
          RepeatForever: 0
          DelayBetweenRepeats: 1
          MMFeedbacksDirectionCondition: 0
          PlayDirection: 0
          ConstantIntensity: 0
          UseIntensityInterval: 0
          IntensityIntervalMin: 0
          IntensityIntervalMax: 0
          Sequence: {fileID: 0}
          TrackID: 0
          Quantized: 0
          TargetBPM: 120
        AutomatedTargetAcquisition:
          Mode: 0
          ChildIndex: 0
        RandomizeOutput: 0
        RandomMultiplier: {x: 0.8, y: 1}
        RandomizeDuration: 0
        RandomDurationMultiplier: {x: 0.5, y: 2}
        UseRange: 0
        RangeDistance: 5
        UseRangeFalloff: 0
        RangeFalloff:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        RemapRangeFalloff: {x: 0, y: 1}
        Owner: {fileID: 2010915431}
        DebugActive: 0
        TargetDocument: {fileID: 2114456165}
        QueryMode: 0
        Query: DemoText
        MarkDirty: 0
        Mode: 0
        Duration: 0.2
        AllowAdditivePlays: 0
        ModifyColor: 1
        ColorOverTime:
          serializedVersion: 2
          key0: {r: 1, g: 1, b: 1, a: 1}
          key1: {r: 1, g: 0.37222314, b: 0, a: 1}
          key2: {r: 1, g: 1, b: 1, a: 1}
          key3: {r: 0, g: 0, b: 0, a: 0}
          key4: {r: 0, g: 0, b: 0, a: 0}
          key5: {r: 0, g: 0, b: 0, a: 0}
          key6: {r: 0, g: 0, b: 0, a: 0}
          key7: {r: 0, g: 0, b: 0, a: 0}
          ctime0: 0
          ctime1: 32768
          ctime2: 65535
          ctime3: 0
          ctime4: 0
          ctime5: 0
          ctime6: 0
          ctime7: 0
          atime0: 0
          atime1: 32768
          atime2: 65535
          atime3: 0
          atime4: 0
          atime5: 0
          atime6: 0
          atime7: 0
          m_Mode: 0
          m_NumColorKeys: 3
          m_NumAlphaKeys: 3
        InstantColor: {r: 0, g: 0, b: 0, a: 0}
        ApplyInitialColorToGradientStart: 1
        ApplyInitialColorToGradientEnd: 1
        ApplyInitialColorsOnPlay: 1
--- !u!1 &2017586133
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2017586134}
  - component: {fileID: 2017586135}
  m_Layer: 0
  m_Name: Rotate
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2017586134
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2017586133}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 241380878}
  m_RootOrder: 8
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2017586135
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2017586133}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6da43522623d4704e979466dc7650b65, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Feedbacks: []
  InitializationMode: 2
  AutoInitialization: 1
  SafeMode: 3
  Direction: 0
  AutoChangeDirectionOnEnd: 0
  AutoPlayOnStart: 0
  AutoPlayOnEnable: 0
  ForceTimescaleMode: 0
  ForcedTimescaleMode: 1
  DurationMultiplier: 1
  RandomizeDuration: 0
  RandomDurationMultiplier: {x: 0.5, y: 1.5}
  DisplayFullDurationDetails: 0
  PlayerTimescaleMode: 1
  OnlyPlayIfWithinRange: 0
  RangeCenter: {fileID: 0}
  RangeDistance: 5
  UseRangeFalloff: 0
  RangeFalloff:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  RemapRangeFalloff: {x: 0, y: 1}
  IgnoreRangeEvents: 0
  CooldownDuration: 0
  InitialDelay: 0
  CanPlay: 1
  CanPlayWhileAlreadyPlaying: 1
  ChanceToPlay: 100
  FeedbacksIntensity: 1
  Events:
    TriggerMMFeedbacksEvents: 0
    TriggerUnityEvents: 1
    OnPlay:
      m_PersistentCalls:
        m_Calls: []
    OnPause:
      m_PersistentCalls:
        m_Calls: []
    OnResume:
      m_PersistentCalls:
        m_Calls: []
    OnRevert:
      m_PersistentCalls:
        m_Calls: []
    OnComplete:
      m_PersistentCalls:
        m_Calls: []
    OnRestoreInitialValues:
      m_PersistentCalls:
        m_Calls: []
    OnSkipToTheEnd:
      m_PersistentCalls:
        m_Calls: []
  DebugActive: 0
  FeedbacksList:
  - rid: 5262747845385781258
  KeepPlayModeChanges: 0
  PerformanceMode: 0
  StopFeedbacksOnDisable: 0
  PlayCount: 0
  references:
    version: 2
    RefIds:
    - rid: 5262747845385781258
      type: {class: MMF_UIToolkitRotate, ns: MoreMountains.FeedbacksForThirdParty,
        asm: MoreMountains.Feedbacks.UIToolkit}
      data:
        Active: 1
        UniqueID: 1339391638
        Label: UITK Rotate
        ChannelMode: 0
        Channel: 0
        MMChannelDefinition: {fileID: 0}
        Chance: 100
        DisplayColor: {r: 0, g: 0, b: 0, a: 1}
        Timing:
          TimescaleMode: 0
          ExcludeFromHoldingPauses: 0
          ContributeToTotalDuration: 1
          InitialDelay: 0
          CooldownDuration: 0
          InterruptsOnStop: 1
          NumberOfRepeats: 0
          RepeatForever: 0
          DelayBetweenRepeats: 1
          MMFeedbacksDirectionCondition: 0
          PlayDirection: 0
          ConstantIntensity: 0
          UseIntensityInterval: 0
          IntensityIntervalMin: 0
          IntensityIntervalMax: 0
          Sequence: {fileID: 0}
          TrackID: 0
          Quantized: 0
          TargetBPM: 120
        AutomatedTargetAcquisition:
          Mode: 0
          ChildIndex: 0
        RandomizeOutput: 0
        RandomMultiplier: {x: 0.8, y: 1}
        RandomizeDuration: 0
        RandomDurationMultiplier: {x: 0.5, y: 2}
        UseRange: 0
        RangeDistance: 5
        UseRangeFalloff: 0
        RangeFalloff:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        RemapRangeFalloff: {x: 0, y: 1}
        Owner: {fileID: 2017586135}
        DebugActive: 0
        TargetDocument: {fileID: 2114456165}
        QueryMode: 0
        Query: DemoFace
        MarkDirty: 0
        Mode: 1
        RelativeValue: 0
        AllowAdditivePlays: 0
        Duration: 1
        InstantValue: 1
        Curve:
          MMTweenDefinitionType: 0
          MMTweenCurve: 4
          Curve:
            serializedVersion: 2
            m_Curve:
            - serializedVersion: 3
              time: 0
              value: 0
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            - serializedVersion: 3
              time: 1
              value: 1
              inSlope: 0
              outSlope: 0
              tangentMode: 0
              weightedMode: 0
              inWeight: 0
              outWeight: 0
            m_PreInfinity: 2
            m_PostInfinity: 2
            m_RotationOrder: 4
          Initialized: 0
        CurveRemapZero: 0
        CurveRemapOne: 360
        DestinationValue: 1
--- !u!1 &2103036734
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2103036736}
  - component: {fileID: 2103036735}
  m_Layer: 0
  m_Name: BackgroundColor
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &2103036735
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2103036734}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6da43522623d4704e979466dc7650b65, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Feedbacks: []
  InitializationMode: 2
  AutoInitialization: 1
  SafeMode: 3
  Direction: 0
  AutoChangeDirectionOnEnd: 0
  AutoPlayOnStart: 0
  AutoPlayOnEnable: 0
  ForceTimescaleMode: 0
  ForcedTimescaleMode: 1
  DurationMultiplier: 1
  RandomizeDuration: 0
  RandomDurationMultiplier: {x: 0.5, y: 1.5}
  DisplayFullDurationDetails: 0
  PlayerTimescaleMode: 1
  OnlyPlayIfWithinRange: 0
  RangeCenter: {fileID: 0}
  RangeDistance: 5
  UseRangeFalloff: 0
  RangeFalloff:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  RemapRangeFalloff: {x: 0, y: 1}
  IgnoreRangeEvents: 0
  CooldownDuration: 0
  InitialDelay: 0
  CanPlay: 1
  CanPlayWhileAlreadyPlaying: 1
  ChanceToPlay: 100
  FeedbacksIntensity: 1
  Events:
    TriggerMMFeedbacksEvents: 0
    TriggerUnityEvents: 1
    OnPlay:
      m_PersistentCalls:
        m_Calls: []
    OnPause:
      m_PersistentCalls:
        m_Calls: []
    OnResume:
      m_PersistentCalls:
        m_Calls: []
    OnRevert:
      m_PersistentCalls:
        m_Calls: []
    OnComplete:
      m_PersistentCalls:
        m_Calls: []
    OnRestoreInitialValues:
      m_PersistentCalls:
        m_Calls: []
    OnSkipToTheEnd:
      m_PersistentCalls:
        m_Calls: []
  DebugActive: 0
  FeedbacksList:
  - rid: 5262747796941570048
  KeepPlayModeChanges: 0
  PerformanceMode: 0
  StopFeedbacksOnDisable: 0
  PlayCount: 0
  references:
    version: 2
    RefIds:
    - rid: 5262747796941570048
      type: {class: MMF_UIToolkitBackgroundColor, ns: MoreMountains.FeedbacksForThirdParty,
        asm: MoreMountains.Feedbacks.UIToolkit}
      data:
        Active: 1
        UniqueID: 1539362931
        Label: UITK Background Color
        ChannelMode: 0
        Channel: 0
        MMChannelDefinition: {fileID: 0}
        Chance: 100
        DisplayColor: {r: 0, g: 0, b: 0, a: 1}
        Timing:
          TimescaleMode: 0
          ExcludeFromHoldingPauses: 0
          ContributeToTotalDuration: 1
          InitialDelay: 0
          CooldownDuration: 0
          InterruptsOnStop: 1
          NumberOfRepeats: 0
          RepeatForever: 0
          DelayBetweenRepeats: 1
          MMFeedbacksDirectionCondition: 0
          PlayDirection: 0
          ConstantIntensity: 0
          UseIntensityInterval: 0
          IntensityIntervalMin: 0
          IntensityIntervalMax: 0
          Sequence: {fileID: 0}
          TrackID: 0
          Quantized: 0
          TargetBPM: 120
        AutomatedTargetAcquisition:
          Mode: 0
          ChildIndex: 0
        RandomizeOutput: 0
        RandomMultiplier: {x: 0.8, y: 1}
        RandomizeDuration: 0
        RandomDurationMultiplier: {x: 0.5, y: 2}
        UseRange: 0
        RangeDistance: 5
        UseRangeFalloff: 0
        RangeFalloff:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        RemapRangeFalloff: {x: 0, y: 1}
        Owner: {fileID: 2103036735}
        DebugActive: 0
        TargetDocument: {fileID: 2114456165}
        QueryMode: 0
        Query: DemoText
        MarkDirty: 0
        Mode: 0
        Duration: 0.2
        AllowAdditivePlays: 0
        ModifyColor: 1
        ColorOverTime:
          serializedVersion: 2
          key0: {r: 1, g: 1, b: 1, a: 1}
          key1: {r: 0.3301887, g: 0.3301887, b: 0.3301887, a: 1}
          key2: {r: 1, g: 1, b: 1, a: 1}
          key3: {r: 0, g: 0, b: 0, a: 0}
          key4: {r: 0, g: 0, b: 0, a: 0}
          key5: {r: 0, g: 0, b: 0, a: 0}
          key6: {r: 0, g: 0, b: 0, a: 0}
          key7: {r: 0, g: 0, b: 0, a: 0}
          ctime0: 0
          ctime1: 32768
          ctime2: 65535
          ctime3: 0
          ctime4: 0
          ctime5: 0
          ctime6: 0
          ctime7: 0
          atime0: 0
          atime1: 32768
          atime2: 65535
          atime3: 0
          atime4: 0
          atime5: 0
          atime6: 0
          atime7: 0
          m_Mode: 0
          m_NumColorKeys: 3
          m_NumAlphaKeys: 3
        InstantColor: {r: 0, g: 0, b: 0, a: 0}
        ApplyInitialColorToGradientStart: 1
        ApplyInitialColorToGradientEnd: 1
        ApplyInitialColorsOnPlay: 1
--- !u!4 &2103036736
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2103036734}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 241380878}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2114456164
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2114456166}
  - component: {fileID: 2114456165}
  - component: {fileID: 2114456167}
  m_Layer: 5
  m_Name: UIDocument
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &2114456165
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2114456164}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 19102, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_PanelSettings: {fileID: 11400000, guid: 163ab44657be0cb4c94660830d851752, type: 2}
  m_ParentUI: {fileID: 0}
  sourceAsset: {fileID: 9197481963319205126, guid: b09595e3fbdee4d41aa72ecd8eb4fd76,
    type: 3}
  m_SortingOrder: 0
--- !u!4 &2114456166
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2114456164}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2114456167
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2114456164}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a4f5c9e03cba42942ba85f0d492a06e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  FaceTexture: {fileID: 2800000, guid: 12737a94889c737439a866490605ea4d, type: 3}
  Actions:
  - ButtonName: BtnBackgroundColor
    TargetPlayer: {fileID: 2103036735}
  - ButtonName: BtnBorderColor
    TargetPlayer: {fileID: 593719874}
  - ButtonName: BtnBorderRadius
    TargetPlayer: {fileID: 1345306587}
  - ButtonName: BtnBorderWidth
    TargetPlayer: {fileID: 1177841477}
  - ButtonName: BtnClass
    TargetPlayer: {fileID: 1712588495}
  - ButtonName: BtnFontSize
    TargetPlayer: {fileID: 293357686}
  - ButtonName: BtnTint
    TargetPlayer: {fileID: 1046813963}
  - ButtonName: BtnOpacity
    TargetPlayer: {fileID: 1231895995}
  - ButtonName: BtnRotate
    TargetPlayer: {fileID: 2017586135}
  - ButtonName: BtnScale
    TargetPlayer: {fileID: 768629959}
  - ButtonName: BtnSize
    TargetPlayer: {fileID: 998874845}
  - ButtonName: BtnStylesheet
    TargetPlayer: {fileID: 1404847138}
  - ButtonName: BtnText
    TargetPlayer: {fileID: 1635157582}
  - ButtonName: BtnTextColor
    TargetPlayer: {fileID: 2010915431}
  - ButtonName: BtnTranslate
    TargetPlayer: {fileID: 1850447478}
  - ButtonName: BtnVisible
    TargetPlayer: {fileID: 1191851718}
