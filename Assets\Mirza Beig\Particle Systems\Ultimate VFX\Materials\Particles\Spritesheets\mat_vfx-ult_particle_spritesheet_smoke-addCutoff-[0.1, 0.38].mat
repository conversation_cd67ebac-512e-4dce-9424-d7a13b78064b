%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 6
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: mat_vfx-ult_particle_spritesheet_smoke-addCutoff-[0.1, 0.38]
  m_Shader: {fileID: 4800000, guid: eeae1f85fcecc9b4a81ebca510428844, type: 3}
  m_ShaderKeywords: 
  m_LightmapFlags: 5
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: bb0cae31eec296d4c950584b24d07a1d, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Floats:
    - _BumpScale: 1
    - _Cutoff: 0.5
    - _DetailNormalMapScale: 1
    - _DstBlend: 0
    - _Glossiness: 0.5
    - _HighlightThresholdMax: 0.12
    - _InvFade: 0.1
    - _Metallic: 0
    - _Mode: 0
    - _OcclusionStrength: 1
    - _Parallax: 0.02
    - _SrcBlend: 1
    - _UVSec: 0
    - _ZWrite: 1
    - _alphaCutoffThreshold: 0.38
    - _cutoutThreshold: 0.609
    - _edgeThreshold: 0.5
    - _fadee: 10.4
    - _highlightColourStrength: 1.5
    - _highlightEdgeThreshold: 8
    - _highlightThresholdMax: 1
    - _textureBased: 0
    m_Colors:
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _HighlightColor: {r: 1, g: 0, b: 0, a: 0.5}
    - _HighlightColour: {r: 1, g: 0, b: 0, a: 0.5}
    - _RegularColor: {r: 0, g: 0, b: 0, a: 0}
    - _RegularColour: {r: 0, g: 0, b: 0, a: 0.5}
    - _TintColor: {r: 1, g: 1, b: 1, a: 1}
    - _highlightColour: {r: 1, g: 1, b: 1, a: 1}
    - _intersectionColour: {r: 1, g: 1, b: 1, a: 0.5}
    - _regularColour: {r: 1, g: 1, b: 1, a: 0.5}
