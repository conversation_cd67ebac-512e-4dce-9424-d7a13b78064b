%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-2786428230938764867
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1cca7768aaaea4b0081f14e7f9d4b5ad, type: 3}
  m_Name: FlatKitOutline
  m_EditorClassIdentifier: 
  m_Active: 1
  settings: {fileID: 11400000, guid: 6a9ce4523839c4a44a17d8d1334b16d2, type: 2}
  _effectMaterial: {fileID: 0}
  _copyMaterial: {fileID: 0}
--- !u!114 &-1344586528338988631
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 770acbaed70db4ad696458511aa3f084, type: 3}
  m_Name: FlatKitFog
  m_EditorClassIdentifier: 
  m_Active: 1
  settings: {fileID: 11400000, guid: d18dac82fe9b0473289115b2531228d9, type: 2}
  _effectMaterial: {fileID: 0}
  _copyMaterial: {fileID: 0}
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: de640fe3d0db1804a85f9fc8f5cadab6, type: 3}
  m_Name: '[FlatKit] Wanderer-ForwardRenderer'
  m_EditorClassIdentifier: 
  debugShaders:
    debugReplacementPS: {fileID: 4800000, guid: cf852408f2e174538bcd9b7fda1c5ae7, type: 3}
    hdrDebugViewPS: {fileID: 4800000, guid: 573620ae32aec764abd4d728906d2587, type: 3}
    probeVolumeSamplingDebugComputeShader: {fileID: 7200000, guid: 53626a513ea68ce47b59dc1299fe3959, type: 3}
  probeVolumeResources:
    probeVolumeDebugShader: {fileID: 0}
    probeVolumeFragmentationDebugShader: {fileID: 0}
    probeVolumeOffsetDebugShader: {fileID: 0}
    probeVolumeSamplingDebugShader: {fileID: 0}
    probeSamplingDebugMesh: {fileID: 0}
    probeSamplingDebugTexture: {fileID: 0}
    probeVolumeBlendStatesCS: {fileID: 0}
  m_RendererFeatures:
  - {fileID: -2786428230938764867}
  - {fileID: -1344586528338988631}
  - {fileID: 2801006864390447537}
  m_RendererFeatureMap: bd59c67e499e54d9a9319428ad1157ed
  m_UseNativeRenderPass: 0
  xrSystemData: {fileID: 11400000, guid: 60e1133243b97e347b653163a8c01b64, type: 2}
  postProcessData: {fileID: 11400000, guid: 41439944d30ece34e96484bdb6645b55, type: 2}
  m_AssetVersion: 2
  m_OpaqueLayerMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_TransparentLayerMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_DefaultStencilState:
    overrideStencilState: 0
    stencilReference: 0
    stencilCompareFunction: 8
    passOperation: 0
    failOperation: 0
    zFailOperation: 0
  m_ShadowTransparentReceive: 1
  m_RenderingMode: 0
  m_DepthPrimingMode: 0
  m_CopyDepthMode: 0
  m_DepthAttachmentFormat: 0
  m_DepthTextureFormat: 0
  m_AccurateGbufferNormals: 0
  m_IntermediateTextureMode: 1
--- !u!114 &2801006864390447537
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 729a2e952309e2547a3747b2cdd9a36f, type: 3}
  m_Name: Shapes Render Feature
  m_EditorClassIdentifier: 
  m_Active: 1
