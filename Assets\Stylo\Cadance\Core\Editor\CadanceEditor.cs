using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using Stylo.Cadance;

namespace Stylo.Cadance.Editor
{
    /// <summary>
    /// Custom editor for the Cadance component to provide equivalent functionality to Koreographer's inspector.
    /// </summary>
    [CustomEditor(typeof(Cadance))]
    public class CadanceEditor : UnityEditor.Editor
    {
        private SerializedProperty eventDelayInSecondsProperty;
        private bool showLoadedCadances = true;
        private Vector2 loadedCadancesScrollPosition;

        private void OnEnable()
        {
            eventDelayInSecondsProperty = serializedObject.FindProperty("eventDelayInSeconds");
        }

        public override void OnInspectorGUI()
        {
            serializedObject.Update();

            Cadance cadanceComponent = (Cadance)target;

            // Header
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Cadance", EditorStyles.boldLabel);
            EditorGUILayout.HelpBox("The Cadance system manages musical event synchronization and choreography. " +
                                   "This is the equivalent of Kore<PERSON>'s main component.",
                                   MessageType.Info);

            EditorGUILayout.Space();

            // Event Delay Configuration
            EditorGUILayout.LabelField("Event Delay Configuration", EditorStyles.boldLabel);
            EditorGUILayout.PropertyField(eventDelayInSecondsProperty, new GUIContent("Event Delay In Seconds"));

            EditorGUILayout.Space();

            // Loaded Cadance section (equivalent to Koreographer's "Loaded Koreography")
            showLoadedCadances = EditorGUILayout.Foldout(showLoadedCadances, "Loaded Cadance", true);

            if (showLoadedCadances)
            {
                EditorGUI.indentLevel++;

                if (Application.isPlaying)
                {
                    int numLoadedCadances = cadanceComponent.GetNumLoadedCadances();

                    if (numLoadedCadances == 0)
                    {
                        EditorGUILayout.LabelField("Empty", EditorStyles.miniLabel);
                    }
                    else
                    {
                        // Count different types of loaded assets
                        int koreographyCount = 0;
                        int cadanceCount = 0;

                        for (int i = 0; i < numLoadedCadances; i++)
                        {
                            CadanceAsset cadance = cadanceComponent.GetCadanceAtIndex(i);
                            if (cadance != null)
                            {
                                if (cadance.name.StartsWith("Koreography_"))
                                    koreographyCount++;
                                else
                                    cadanceCount++;
                            }
                        }

                        // Show counts like Koreographer does
                        if (koreographyCount > 0 && cadanceCount > 0)
                        {
                            EditorGUILayout.LabelField($"{koreographyCount} Koreography, {cadanceCount} Cadance", EditorStyles.miniLabel);
                        }
                        else if (koreographyCount > 0)
                        {
                            EditorGUILayout.LabelField($"{koreographyCount} items", EditorStyles.miniLabel);
                        }
                        else
                        {
                            EditorGUILayout.LabelField($"{cadanceCount} items", EditorStyles.miniLabel);
                        }

                        EditorGUILayout.Space(2);

                        // Scrollable list of loaded Cadances
                        loadedCadancesScrollPosition = EditorGUILayout.BeginScrollView(
                            loadedCadancesScrollPosition,
                            GUILayout.MaxHeight(150)
                        );

                        for (int i = 0; i < numLoadedCadances; i++)
                        {
                            CadanceAsset cadance = cadanceComponent.GetCadanceAtIndex(i);
                            if (cadance != null)
                            {
                                // Check if this is a converted Koreography (has the special naming pattern)
                                bool isConvertedKoreography = cadance.name.StartsWith("Koreography_");

                                EditorGUILayout.BeginHorizontal();

                                // Show icon and name like Koreographer does
                                if (isConvertedKoreography)
                                {
                                    // Create a style similar to Koreographer's asset display
                                    GUIStyle koreographyStyle = new GUIStyle(EditorStyles.objectField);
                                    koreographyStyle.imagePosition = ImagePosition.ImageLeft;

                                    // Show Koreography icon and name (similar to Koreographer display)
                                    GUIContent koreographyContent = new GUIContent(
                                        $"{cadance.SourceClipName} (Koreography)",
                                        EditorGUIUtility.IconContent("ScriptableObject Icon").image,
                                        $"Converted from Koreography\nSource: {cadance.SourceClipName}\nTracks: {cadance.Tracks.Count}"
                                    );

                                    EditorGUI.BeginDisabledGroup(true);
                                    EditorGUILayout.LabelField(koreographyContent, koreographyStyle, GUILayout.ExpandWidth(true));
                                    EditorGUI.EndDisabledGroup();
                                }
                                else
                                {
                                    // Show regular CadanceAsset
                                    EditorGUI.BeginDisabledGroup(true);
                                    EditorGUILayout.ObjectField(cadance, typeof(CadanceAsset), false);
                                    EditorGUI.EndDisabledGroup();
                                }

                                // Add button to unload (only in play mode)
                                if (GUILayout.Button("Unload", GUILayout.Width(60)))
                                {
                                    cadanceComponent.UnloadCadance(cadance);
                                }

                                EditorGUILayout.EndHorizontal();

                                // Show additional info (more compact for Koreography entries)
                                if (!isConvertedKoreography)
                                {
                                    EditorGUI.indentLevel++;
                                    EditorGUILayout.LabelField($"Source Clip: {cadance.SourceClipName}", EditorStyles.miniLabel);
                                    EditorGUILayout.LabelField($"Tracks: {cadance.Tracks.Count}", EditorStyles.miniLabel);
                                    EditorGUILayout.LabelField($"Sample Rate: {cadance.SampleRate} Hz", EditorStyles.miniLabel);
                                    EditorGUI.indentLevel--;
                                }

                                EditorGUILayout.Space(2);
                            }
                        }

                        EditorGUILayout.EndScrollView();
                    }

                    EditorGUILayout.Space();

                    // Manual load section
                    EditorGUILayout.LabelField("Manual Load", EditorStyles.boldLabel);

                    // Load CadanceAsset
                    EditorGUILayout.BeginHorizontal();
                    CadanceAsset cadanceToLoad = (CadanceAsset)EditorGUILayout.ObjectField(
                        "Load Cadance",
                        null,
                        typeof(CadanceAsset),
                        false
                    );

                    if (GUILayout.Button("Load", GUILayout.Width(60)))
                    {
                        if (cadanceToLoad != null)
                        {
                            cadanceComponent.LoadCadance(cadanceToLoad);
                        }
                    }
                    EditorGUILayout.EndHorizontal();

                    // Load FMODKoreographySet (for backward compatibility)
                    EditorGUILayout.BeginHorizontal();
                    SonicBloom.Koreo.Players.FMODStudio.FMODKoreographySet koreographySetToLoad =
                        (SonicBloom.Koreo.Players.FMODStudio.FMODKoreographySet)EditorGUILayout.ObjectField(
                            "Load Koreography Set",
                            null,
                            typeof(SonicBloom.Koreo.Players.FMODStudio.FMODKoreographySet),
                            false
                        );

                    if (GUILayout.Button("Load", GUILayout.Width(60)))
                    {
                        if (koreographySetToLoad != null)
                        {
                            cadanceComponent.LoadFMODKoreographySet(koreographySetToLoad);
                        }
                    }
                    EditorGUILayout.EndHorizontal();

                    // Utility buttons
                    EditorGUILayout.Space();
                    EditorGUILayout.BeginHorizontal();

                    if (GUILayout.Button("Unload All"))
                    {
                        // Unload all cadances
                        for (int i = numLoadedCadances - 1; i >= 0; i--)
                        {
                            CadanceAsset cadance = cadanceComponent.GetCadanceAtIndex(i);
                            if (cadance != null)
                            {
                                cadanceComponent.UnloadCadance(cadance);
                            }
                        }
                    }

                    if (GUILayout.Button("Refresh"))
                    {
                        // Force repaint to refresh the display
                        Repaint();
                    }

                    EditorGUILayout.EndHorizontal();
                }
                else
                {
                    EditorGUILayout.LabelField("Runtime information available when playing", EditorStyles.miniLabel);
                }

                EditorGUI.indentLevel--;
            }

            EditorGUILayout.Space();

            // Runtime Statistics (additional info not in Koreographer)
            if (Application.isPlaying)
            {
                EditorGUILayout.LabelField("Runtime Statistics", EditorStyles.boldLabel);

                EditorGUI.BeginDisabledGroup(true);
                EditorGUILayout.IntField("Loaded Cadances", cadanceComponent.GetNumLoadedCadances());

                // Show music playback controller if set
                if (cadanceComponent.musicPlaybackController != null)
                {
                    EditorGUILayout.ObjectField("Music Playback Controller",
                                              cadanceComponent.musicPlaybackController as Object,
                                              typeof(Object),
                                              true);
                }
                else
                {
                    EditorGUILayout.LabelField("Music Playback Controller", "None");
                }

                EditorGUI.EndDisabledGroup();
            }

            serializedObject.ApplyModifiedProperties();

            // Auto-refresh during play mode
            if (Application.isPlaying)
            {
                EditorUtility.SetDirty(target);
                Repaint();
            }
        }
    }
}
