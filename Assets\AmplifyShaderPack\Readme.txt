About

  Amplify Shader Pack (c) Amplify Creations, Lda. All rights reserved.

  Amplify Shader Pack is a shader pack for Amplify Shader Editor with over 180 examples.
     
  Redistribution of Amplify Shader Pack is frowned upon. If you want to share the 
  software, please refer others to the official product page:
  https://assetstore.unity.com/publishers/707

  Shader Editor: http://amplify.pt/unity/amplify-shader-editor/
    
Description

 180+ Amplify Shader Editor shaders for HDRP, URP, and Built-in Renderer. 
 Fully editable with Amplify Shader Editor, sold separately.
    
Features

  * Amplify Shader Editor not required to use, only needed to edit/update
  * HDRP, URP, and Built-in Renderer
  * Fully Customizable

Supported Platforms and Renderers

  * All platforms
  * Built-in Renderer
  * URP v10 to v17
  * HDRP v10 to v17

Quick Guide Amplify Shader Pack
  
  1) Import a Shader package(HDRP, URP or Built-in) from the Start Screen.
     (Window\Amplify Shader Pack\Start Screen)
  2) Select a shader folder and open the included scene, e.g. Animated Fire.
  3) Consult the included Readme file on each folder for additional details.
  4) Adjust the included Material Textures/Properties and apply to your own Model.
  5) OPTIONAL STEP: Open the shader with Amplify Shader Editor to examine how its
     constructed and to alter it to your own requirement.
  
Technical Considerations
  
  Built-in
  * These shaders have been created with Unity 2019.4
  * Built-in shaders will work with any Unity version

  SRP
  * Includes HDRP/URP shaders been created for versions 10 to 16. You will need 
    Amplify Shader Editor to updated these shaders in order to make them work with 
    unsupported SRP versions.
  * Using HDRP/URP shaders with a different SRP version without updating them will 
    likely render incorrectly or show as Pink/Magenta. This is expected, shaders must
    be open with the editor and saved to automatically update them to a different 
    supported SRP version.

Troubleshooting PINK/MAGENTA shaders
  
  * Be sure that your opening the correct version of the shader, Built-in shaders 
    only work with the Built-in Renderer, URP with the Universal Renderer, and HDRP
    with the High Definition Renderer.
  * When using URP/HDRP, check your Unity and SRP version, make sure that you have the 
    correct shader pack version imported. 
  * Open and save to update a shader your specific(compatible) SRP version.

Using the Editor

  Please refer to the following website for an up-to-date online manual:

    http://amplify.pt/unity/amplify-shader-editor/manual

Feedback

  To file error reports, questions or suggestions, you may use 
  our feedback form online:
    
    http://amplify.pt/contact

  Or contact us directly:

    For general inquiries - <EMAIL>
    For technical support - <EMAIL> (customers only)
