%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e78934177bebe0545ab566bb787b0282, type: 3}
  m_Name: Readme
  m_EditorClassIdentifier: 
  Icon: {fileID: 2800000, guid: 26888e395062fb943b3850449c7d7936, type: 3}
  Title: PlaneClip
  Description:
    Heading: 
    Text: This sample performs clipping to an object according to a plane position
      and orientation.
    LinkText: Based on the tutorial by <PERSON><PERSON><PERSON>
    Url: https://www.ronja-tutorials.com/2018/08/06/plane-clipping.html
  PropertiesHeader:
    Heading: Plane Clip Properties
    Text: 
    LinkText: 
    Url: 
  Properties:
  - Heading: CutoffColor
    Text: Emissive color applied to cut section.
    LinkText: 
    Url: 
  - Heading: Emission
    Text: Emissive color applied to non-cut section.
    LinkText: 
    Url: 
  - Heading: Activate Clip
    Text: Switch to toggle on/off clipping.
    LinkText: 
    Url: 
  - Heading: Textured_Course_AlbedoTransparency
    Text: Albedo texture to be applied to surface.
    LinkText: 
    Url: 
  - Heading: Smoothness
    Text: Smoothness texture to be applied to surface.
    LinkText: 
    Url: 
  AdditionalProperties:
  - BlockHeader:
      Heading: Clip Area Properties
      Text: 
      LinkText: 
      Url: 
    BlockContent:
    - Heading: Color
      Text: Main color for plane surface.
      LinkText: 
      Url: 
    - Heading: Depth Range
      Text: Value that controls transparency on plane according to distance to what's
        behind.
      LinkText: 
      Url: 
    - Heading: Edge Gain
      Text: Value to control exponential behavior on transparency effect.
      LinkText: 
      Url: 
    - Heading: Edge Contrast
      Text: Value to control overall plane transparency.
      LinkText: 
      Url: 
    - Heading: Intensity
      Text: Intensity value applied to plane color.
      LinkText: 
      Url: 
    - Heading: Grid Tile
      Text: Value for tile amount for grid effect on plane.
      LinkText: 
      Url: 
    - Heading: Grid Size
      Text: Value for grid border size amount on plane.
      LinkText: 
      Url: 
  AdditionalScripts:
  - BlockHeader:
      Heading: Scripts
      Text: 
      LinkText: 
      Url: 
    BlockContent:
    - Heading: PlaneClip
      Text: 'Sends current plane transform information to PlaneClip via <b>_PlaneClipNormals</b>
        global variable. '
      LinkText: 
      Url: 
  LoadedLayout: 0
  RPType: 3
