using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.Linq;

namespace Stylo.Cadance.Editor
{
    /// <summary>
    /// Custom Property Drawer for the EventIDAttribute. This class customizes 
    /// the representation of fields marked with [EventID] or [EventIDAttribute].
    /// 
    /// Provides full compatibility with Koreographer's EventID property drawer.
    /// </summary>
    [CustomPropertyDrawer(typeof(EventIDAttribute))]
    public class EventIDPropertyDrawer : PropertyDrawer
    {
        private static List<string> cachedEventIDs = new List<string>();
        private static bool cacheNeedsRefresh = true;

        public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
        {
            if (property.propertyType != SerializedPropertyType.String)
            {
                EditorGUI.LabelField(position, label.text, "EventID attribute can only be used on string fields");
                return;
            }

            // Refresh cache if needed
            if (cacheNeedsRefresh)
            {
                RefreshEventIDCache();
                cacheNeedsRefresh = false;
            }

            EditorGUI.BeginProperty(position, label, property);

            // Calculate rects for the field and dropdown button
            Rect fieldRect = new Rect(position.x, position.y, position.width - 20, position.height);
            Rect buttonRect = new Rect(position.x + position.width - 18, position.y, 18, position.height);

            // Draw the string field
            string currentValue = property.stringValue;
            string newValue = EditorGUI.TextField(fieldRect, label, currentValue);

            if (newValue != currentValue)
            {
                property.stringValue = newValue;
            }

            // Draw dropdown button
            if (GUI.Button(buttonRect, "▼", EditorStyles.miniButton))
            {
                ShowEventIDDropdown(property, buttonRect);
            }

            EditorGUI.EndProperty();
        }

        private void ShowEventIDDropdown(SerializedProperty property, Rect buttonRect)
        {
            GenericMenu menu = new GenericMenu();

            // Add common event IDs
            string[] commonEventIDs = {
                "Beat",
                "Measure",
                "Group Enemy Shooting",
                "Unlock",
                "Lock",
                "Player Shooting",
                "Rewind Time",
                "Coordinated Attack",
                "EnemySpawn",
                "PowerUp",
                "Transition"
            };

            foreach (string eventID in commonEventIDs)
            {
                menu.AddItem(new GUIContent($"Common/{eventID}"),
                           property.stringValue == eventID,
                           () => SetEventID(property, eventID));
            }

            // Add separator
            menu.AddSeparator("");

            // Add cached event IDs from project
            if (cachedEventIDs.Count > 0)
            {
                foreach (string eventID in cachedEventIDs)
                {
                    if (!commonEventIDs.Contains(eventID))
                    {
                        menu.AddItem(new GUIContent($"Project/{eventID}"),
                                   property.stringValue == eventID,
                                   () => SetEventID(property, eventID));
                    }
                }
            }
            else
            {
                menu.AddDisabledItem(new GUIContent("Project/No Cadance assets found"));
            }

            // Add separator and utility options
            menu.AddSeparator("");
            menu.AddItem(new GUIContent("Clear"), false, () => SetEventID(property, ""));
            menu.AddItem(new GUIContent("Refresh Event List"), false, () =>
            {
                cacheNeedsRefresh = true;
                RefreshEventIDCache();
            });

            menu.DropDown(buttonRect);
        }

        private void SetEventID(SerializedProperty property, string eventID)
        {
            property.stringValue = eventID;
            property.serializedObject.ApplyModifiedProperties();
        }

        private void RefreshEventIDCache()
        {
            cachedEventIDs.Clear();

            // Find all CadanceAsset files in the project
            string[] guids = AssetDatabase.FindAssets("t:CadanceAsset");

            foreach (string guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                CadanceAsset asset = AssetDatabase.LoadAssetAtPath<CadanceAsset>(path);

                if (asset != null && asset.Tracks != null)
                {
                    foreach (var track in asset.Tracks)
                    {
                        if (track != null && !string.IsNullOrEmpty(track.EventID) && !cachedEventIDs.Contains(track.EventID))
                        {
                            cachedEventIDs.Add(track.EventID);
                        }
                    }
                }
            }

            // Sort the list for better organization
            cachedEventIDs.Sort();
        }

        public override float GetPropertyHeight(SerializedProperty property, GUIContent label)
        {
            return EditorGUIUtility.singleLineHeight;
        }
    }
}
