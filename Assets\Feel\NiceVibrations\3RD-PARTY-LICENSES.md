# Rust Core

addr2line: Apache-2.0 OR MIT
adler: 0BSD OR Apache-2.0 OR MIT
aho-corasick: MIT OR Unlicense
android_log-sys: Apache-2.0 OR MIT
android_logger: Apache-2.0 OR MIT
ansi_term: MIT
arrayvec: Apache-2.0 OR MIT
atty: MIT
autocfg: Apache-2.0 OR MIT
backtrace: Apache-2.0 OR MIT
bincode: MIT
bitflags: Apache-2.0 OR MIT
block-buffer: Apache-2.0 OR MIT
block-padding: Apache-2.0 OR MIT
bstr: Apache-2.0 OR MIT
bumpalo: Apache-2.0 OR MIT
byte-tools: Apache-2.0 OR MIT
byteorder: MIT OR Unlicense
bytes: MIT
cast: Apache-2.0 OR MIT
cbindgen: MPL-2.0
cc: Apache-2.0 OR MIT
cesu8: Apache-2.0 OR MIT
cfg-if: Apache-2.0 OR MIT
clap: MIT
combine: MIT
console_error_panic_hook: Apache-2.0 OR MIT
criterion: Apache-2.0 OR MIT
criterion-plot: Apache-2.0 OR MIT
crossbeam-channel: Apache-2.0 OR MIT
crossbeam-deque: Apache-2.0 OR MIT
crossbeam-epoch: Apache-2.0 OR MIT
csv: MIT OR Unlicense
csv-core: MIT OR Unlicense
dasp_sample: Apache-2.0 OR MIT
digest: Apache-2.0 OR MIT
either: Apache-2.0 OR MIT
encoding_rs: 
env_logger: Apache-2.0 OR MIT
error-chain: Apache-2.0 OR MIT
failure: Apache-2.0 OR MIT
failure_derive: Apache-2.0 OR MIT
fake-simd: Apache-2.0 OR MIT
fourier: Apache-2.0 OR MIT
fourier-algorithms: Apache-2.0 OR MIT
fourier-macros: Apache-2.0 OR MIT
fuchsia-cprng: 
generic-array: MIT
getrandom: Apache-2.0 OR MIT
gimli: Apache-2.0 OR MIT
half: Apache-2.0 OR MIT
hermit-abi: Apache-2.0 OR MIT
hound: Apache-2.0
humantime: Apache-2.0 OR MIT
itertools: Apache-2.0 OR MIT
itertools-num: Apache-2.0 OR MIT
itoa: Apache-2.0 OR MIT
jni: Apache-2.0 OR MIT
jni-sys: Apache-2.0 OR MIT
js-sys: Apache-2.0 OR MIT
lazy_static: Apache-2.0 OR MIT
log: Apache-2.0 OR MIT
log-panics: Apache-2.0 OR MIT
maplit: Apache-2.0 OR MIT
memchr: MIT OR Unlicense
memoffset: MIT
multiversion: Apache-2.0 OR MIT
multiversion-macros: Apache-2.0 OR MIT
num-complex: Apache-2.0 OR MIT
num-traits: Apache-2.0 OR MIT
num_cpus: Apache-2.0 OR MIT
object: Apache-2.0 OR MIT
oorandom: MIT
opaque-debug: Apache-2.0 OR MIT
pest: Apache-2.0 OR MIT
pest_derive: Apache-2.0 OR MIT
pest_generator: Apache-2.0 OR MIT
pest_meta: Apache-2.0 OR MIT
pico-args: MIT
plotters: MIT
plotters-backend: MIT
plotters-svg: MIT
ppv-lite86: Apache-2.0 OR MIT
proc-macro2: Apache-2.0 OR MIT
quote: Apache-2.0 OR MIT
rand: Apache-2.0 OR MIT
rand_chacha: Apache-2.0 OR MIT
rand_core: Apache-2.0 OR MIT
rand_hc: Apache-2.0 OR MIT
rayon: Apache-2.0 OR MIT
rayon-core: Apache-2.0 OR MIT
rdrand: ISC
redox_syscall: MIT
regex: Apache-2.0 OR MIT
regex-automata: MIT OR Unlicense
regex-syntax: Apache-2.0 OR MIT
remove_dir_all: Apache-2.0 OR MIT
rustc-demangle: Apache-2.0 OR MIT
rustc_version: Apache-2.0 OR MIT
ryu: Apache-2.0 OR BSL-1.0
same-file: MIT OR Unlicense
scoped-tls: Apache-2.0 OR MIT
scopeguard: Apache-2.0 OR MIT
semver: Apache-2.0 OR MIT
serde: Apache-2.0 OR MIT
serde_cbor: Apache-2.0 OR MIT
serde_derive: Apache-2.0 OR MIT
serde_derive_internals: Apache-2.0 OR MIT
serde_json: Apache-2.0 OR MIT
sha-1: Apache-2.0 OR MIT
strsim: MIT
symphonia: MPL-2.0
symphonia-bundle-flac: MPL-2.0
symphonia-bundle-mp3: MPL-2.0
symphonia-codec-pcm: MPL-2.0
symphonia-codec-vorbis: MPL-2.0
symphonia-core: MPL-2.0
symphonia-format-ogg: MPL-2.0
symphonia-format-wav: MPL-2.0
symphonia-metadata: MPL-2.0
syn: Apache-2.0 OR MIT
synstructure: MIT
tempdir: Apache-2.0 OR MIT
tempfile: Apache-2.0 OR MIT
termcolor: MIT OR Unlicense
test_bin: Apache-2.0 OR MIT
textwrap: MIT
tinytemplate: Apache-2.0 OR MIT
toml: Apache-2.0 OR MIT
typenum: Apache-2.0 OR MIT
typescript-definitions: Apache-2.0 OR MIT
typescript-definitions-derive: Apache-2.0 OR MIT
ucd-trie: Apache-2.0 OR MIT
unicode-width: Apache-2.0 OR MIT
unicode-xid: Apache-2.0 OR MIT
vec_map: Apache-2.0 OR MIT
version_check: Apache-2.0 OR MIT
walkdir: MIT OR Unlicense
wasi: Apache-2.0 OR Apache-2.0 WITH LLVM-exception OR MIT
wasm-bindgen: Apache-2.0 OR MIT
wasm-bindgen-backend: Apache-2.0 OR MIT
wasm-bindgen-futures: Apache-2.0 OR MIT
wasm-bindgen-macro: Apache-2.0 OR MIT
wasm-bindgen-macro-support: Apache-2.0 OR MIT
wasm-bindgen-shared: Apache-2.0 OR MIT
wasm-bindgen-test: Apache-2.0 OR MIT
wasm-bindgen-test-macro: Apache-2.0 OR MIT
web-sys: Apache-2.0 OR MIT

# Android

AndroidX: Apache-2.0

# iOS

N/A

# Licenses

## Apache 2.0

https://www.apache.org/licenses/LICENSE-2.0

## Apache 2.0 with LLVM-Exception

https://releases.llvm.org/10.0.0/LICENSE.TXT

## BSD-2-Clause

https://opensource.org/licenses/BSD-2-Clause

## BSL-1.0

https://opensource.org/licenses/BSL-1.0

## MIT

https://opensource.org/licenses/MIT

## MPL-2.0

https://www.mozilla.org/en-US/MPL/2.0/

## Unlicense

https://choosealicense.com/licenses/unlicense/

## ISC

https://opensource.org/licenses/ISC
