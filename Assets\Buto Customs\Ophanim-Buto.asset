%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-3809079633457641213
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9392aacf3349be549bb1fd9eece49154, type: 3}
  m_Name: VolumetricFog
  m_EditorClassIdentifier: 
  active: 1
  mode:
    m_OverrideState: 1
    m_Value: 1
  qualityLevel:
    m_OverrideState: 0
    m_Value: 4
  gridPixelSize:
    m_OverrideState: 0
    m_Value: 25
  gridSizeZ:
    m_OverrideState: 1
    m_Value: 128
  maxDistanceVolumetric:
    m_OverrideState: 1
    m_Value: 54.3
  depthRatio:
    m_OverrideState: 0
    m_Value: 2
  temporalAALighting:
    m_OverrideState: 1
    m_Value: 0.162
  temporalAAMedia:
    m_OverrideState: 1
    m_Value: 0.163
  fogDensity:
    m_OverrideState: 1
    m_Value: 1.41
  anisotropy:
    m_OverrideState: 1
    m_Value: -0.58
  lightIntensity:
    m_OverrideState: 0
    m_Value: 0.51
  densityInLight:
    m_OverrideState: 0
    m_Value: 1
  densityInShadow:
    m_OverrideState: 0
    m_Value: 1
  overrideDefaultMaxLightDistance:
    m_OverrideState: 0
    m_Value: 0
  maxLightDistance:
    m_OverrideState: 0
    m_Value: 64
  baseHeight:
    m_OverrideState: 1
    m_Value: 84.5
  attenuationBoundarySize:
    m_OverrideState: 0
    m_Value: 46.1
  litColor:
    m_OverrideState: 1
    m_Value: {r: 0.027049968, g: 0, b: 0.3627451, a: 1}
  shadowedColor:
    m_OverrideState: 1
    m_Value: {r: 0, g: 0, b: 0, a: 1}
  emitColor:
    m_OverrideState: 1
    m_Value: {r: 0.052005004, g: 0, b: 3.2, a: 1}
  colorRamp:
    m_OverrideState: 0
    m_Value: {fileID: 2800000, guid: a7c274017ff97fb48b4cd2166d1e7702, type: 3}
  colorRampId:
    m_OverrideState: 0
    m_Value: 0
  colorInfluence:
    m_OverrideState: 1
    m_Value: 0.389
  directionalForward:
    m_OverrideState: 1
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  directionalBack:
    m_OverrideState: 1
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  directionalRatio:
    m_OverrideState: 1
    m_Value: 1
  noiseTexture:
    m_OverrideState: 0
    m_Value: {fileID: 0}
  octaves:
    m_OverrideState: 1
    m_Value: 2
  lacunarity:
    m_OverrideState: 0
    m_Value: 2
  gain:
    m_OverrideState: 0
    m_Value: 0.3
  noiseTiling:
    m_OverrideState: 0
    m_Value: 10
  noiseWindSpeed:
    m_OverrideState: 0
    m_Value: {x: 0, y: 6.69, z: 7.02}
  noiseMap:
    m_OverrideState: 1
    m_Value: {x: 0.7421784, y: 1}
  volumeNoise:
    m_OverrideState: 1
    m_Value:
      frequency: 29
      octaves: 2
      lacunarity: 5
      gain: 0.214
      seed: 131
      noiseType: 5
      noiseQuality: 2
      invert: 0
      userTexture: {fileID: 0}
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d7fd9488000d3734a9e00ee676215985, type: 3}
  m_Name: Ophanim-Buto
  m_EditorClassIdentifier: 
  components:
  - {fileID: -3809079633457641213}
