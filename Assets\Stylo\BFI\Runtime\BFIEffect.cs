#if URP_INSTALLED
using System;
using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;

namespace Stylo.BFI.Universal
{
    [ExecuteInEditMode, VolumeComponentMenu("Stylo - BFI Effect")] // Black Frame Insertion Effect
    public class BFIEffect : VolumeComponent, IPostProcessComponent
    {
        public enum BFIMode 
        { 
            [InspectorName("Off")] Off,
            [InspectorName("2x Frame Rate (60→120Hz)")] DoubleFrameRate,
            [InspectorName("3x Frame Rate (60→180Hz)")] TripleFrameRate,
            [InspectorName("4x Frame Rate (60→240Hz)")] QuadFrameRate
        }
        
        public enum FlickerReduction
        {
            [InspectorName("None")] None,
            [InspectorName("Low")] Low,
            [InspectorName("Medium")] Medium,
            [InspectorName("High")] High
        }

        [Serializable] public sealed class BFIModeParameter : VolumeParameter<BFIMode> 
        { 
            public BFIModeParameter(BFIMode value, bool overrideState = false) : base(value, overrideState) { } 
        }
        
        [Serializable] public sealed class FlickerReductionParameter : VolumeParameter<FlickerReduction> 
        { 
            public FlickerReductionParameter(FlickerReduction value, bool overrideState = false) : base(value, overrideState) { } 
        }

        [Header("🖥️ BFI MASTER CONTROLS")]
        [Tooltip("Black Frame Insertion mode. Higher frame rates provide better motion clarity but require more performance and compatible displays." +
            "\n\nOff: No BFI effect" +
            "\n2x: Inserts 1 black frame between each rendered frame (60→120Hz)" +
            "\n3x: Inserts 2 black frames between each rendered frame (60→180Hz)" +
            "\n4x: Inserts 3 black frames between each rendered frame (60→240Hz)" +
            "\n\nNote: Your display must support the target refresh rate for optimal results.")]
        public BFIModeParameter Mode = new BFIModeParameter(BFIMode.Off);

        [Tooltip("Master intensity control for the BFI effect. 0 = disabled, 1 = full effect.")]
        public ClampedFloatParameter Intensity = new ClampedFloatParameter(1f, 0f, 1f);

        [Space(10)]
        [Header("⚡ BRIGHTNESS & COMPENSATION")]
        [Tooltip("Automatically compensates brightness to account for black frames. Higher values make visible frames brighter.")]
        public ClampedFloatParameter BrightnessCompensation = new ClampedFloatParameter(1.5f, 1f, 3f);

        [Tooltip("Manual brightness boost applied to visible frames. Use this for fine-tuning brightness levels.")]
        public ClampedFloatParameter ManualBrightness = new ClampedFloatParameter(1f, 0.5f, 2f);

        [Tooltip("Gamma correction applied during brightness compensation. Higher values preserve dark detail.")]
        public ClampedFloatParameter GammaCorrection = new ClampedFloatParameter(1f, 0.5f, 2f);

        [Space(10)]
        [Header("🎯 TIMING & SYNCHRONIZATION")]
        [Tooltip("Controls the duration of black frames relative to visible frames. 0.5 = equal duration, 1.0 = black frames last as long as visible frames.")]
        public ClampedFloatParameter BlackFrameDuration = new ClampedFloatParameter(0.5f, 0.1f, 1f);

        [Tooltip("Phase offset for BFI timing. Adjusts when black frames appear in the refresh cycle.")]
        public ClampedFloatParameter PhaseOffset = new ClampedFloatParameter(0f, 0f, 1f);

        [Tooltip("Synchronizes BFI with display refresh rate. Enable for smoother motion on compatible displays.")]
        public BoolParameter SyncToRefreshRate = new BoolParameter(true);

        [Space(10)]
        [Header("🌊 FLICKER REDUCTION")]
        [Tooltip("Reduces perceived flicker from black frame insertion. Higher settings provide smoother appearance but may reduce motion clarity benefits.")]
        public FlickerReductionParameter FlickerReductionMode = new FlickerReductionParameter(FlickerReduction.Medium);

        [Tooltip("Temporal smoothing applied to reduce flicker. Higher values create smoother transitions but may blur motion.")]
        public ClampedFloatParameter TemporalSmoothing = new ClampedFloatParameter(0.2f, 0f, 1f);

        [Tooltip("Edge-preserving smoothing to maintain sharp edges while reducing flicker.")]
        public ClampedFloatParameter EdgePreservation = new ClampedFloatParameter(0.8f, 0f, 1f);

        [Space(10)]
        [Header("🎮 MOTION ADAPTIVE")]
        [Tooltip("Enables motion-adaptive BFI that adjusts intensity based on scene motion. More motion = stronger BFI effect.")]
        public BoolParameter MotionAdaptive = new BoolParameter(false);

        [Tooltip("Motion detection sensitivity. Higher values detect more subtle motion and apply BFI more aggressively.")]
        public ClampedFloatParameter MotionSensitivity = new ClampedFloatParameter(1f, 0.1f, 5f);

        [Tooltip("Minimum motion threshold before BFI activates. Lower values make BFI more responsive to small movements.")]
        public ClampedFloatParameter MotionThreshold = new ClampedFloatParameter(0.01f, 0f, 0.1f);

        [Tooltip("How quickly BFI intensity adapts to motion changes. Higher values create more immediate response.")]
        public ClampedFloatParameter AdaptationSpeed = new ClampedFloatParameter(2f, 0.1f, 10f);

        [Space(10)]
        [Header("🔧 ADVANCED SETTINGS")]
        [Tooltip("Dithering pattern to reduce banding artifacts in black frame transitions.")]
        public ClampedFloatParameter DitheringStrength = new ClampedFloatParameter(0.1f, 0f, 1f);

        [Tooltip("Subpixel precision for frame timing. Higher values provide smoother motion but require more processing.")]
        public ClampedFloatParameter SubpixelPrecision = new ClampedFloatParameter(1f, 0.5f, 2f);

        [Tooltip("Debug visualization mode. Shows BFI timing and frame information.")]
        public BoolParameter DebugVisualization = new BoolParameter(false);

        // IPostProcessComponent implementation
        public bool IsActive() => Mode.value != BFIMode.Off && Intensity.value > 0f;
        public bool IsTileCompatible() => false; // BFI requires full-screen processing
    }
}
#endif
