<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<link rel="icon" href="../images/favicon.svg" />
		<link rel="icon" href="../images/favicon.ico" />
		<meta name="viewport" content="width=device-width, height=device-height, initial-scale=1">
		<meta property="og:type" content="website">
		<meta http-equiv="content-security-policy" content="">
		<link href="../_app/immutable/assets/_layout.21aea584.css" rel="stylesheet">
		<link href="../_app/immutable/assets/_layout.0154a58a.css" rel="stylesheet">
		
		
		
		
		
		
		
		
		
		
		
		
		<title>Core Concepts</title><!-- HEAD_svelte-i5aht6_START --><meta property="og:title" content="Core Concepts"><meta name="description" property="og:description" content="Flexalon lets Unity developers arrange objects in 3D layouts that are precise and easy to adjust. Use built-in interactions and animations to create powerful experiences."><meta name="image" property="og:image" content="../images/flexalon-1200x630.png"><meta name="twitter:card" content="summary_large_image"><meta name="twitter:title" content="Core Concepts"><meta name="twitter:site" content="@alon_farchy"><meta name="twitter:description" content="Flexalon lets Unity developers arrange objects in 3D layouts that are precise and easy to adjust. Use built-in interactions and animations to create powerful experiences."><meta name="twitter:image" content="../images/flexalon-1200x630.png"><!-- HEAD_svelte-i5aht6_END -->
	</head>
	<body>
		<div>


<div class="text-white font-body p-4 sticky top-0 bg-black flex gap-5 w-full text-gray-200 z-20 border-b-2 border-[#21a6f0]"><button class="lg:hidden"><svg width="24" height="24"><path d="M5 6h14M5 12h14M5 18h14" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"></path></svg></button>
    <a href="https://www.flexalon.com/" class="font-logo upper text-lg text-[#21a6f0] lg:pl-5 svelte-194nj6y">Flexalon Layouts</a></div>

<div class="w-full max-w-[1600px] lg:pl-10"><div class="flex w-full min-h-full text-zinc-400 relative"><div class="h-full fixed bg-black z-10 max-lg:hidden"><div class="flex flex-col w-[300px] gap-2 border-[#21a6f0] border-r-2 bg-black min-h-0 h-full pt-10 pb-36 overflow-auto max-lg:pl-10"><b class="text-zinc-200">Getting Started</b>
                <a href="../docs.html" class="svelte-194nj6y">Installation</a>
                <a href="../docs/coreConcepts.html" class="svelte-194nj6y">Core Concepts</a>
                <a href="../docs/ui.html" class="svelte-194nj6y">Flexalon UI <span class="ml-2 text-xs font-bold text-pink-300">(New in v4.0!)</span></a>
                <a href="../docs/templates.html" class="svelte-194nj6y">Template Pack</a>
                <a href="../docs/challenges.html" class="svelte-194nj6y">Challenges!</a>

                <a class="text-zinc-200 mt-5 font-bold svelte-194nj6y" href="../docs/flexalonObject.html">Flexalon Objects</a>
                <a href="../docs/flexalonObject.html#size" class="svelte-194nj6y">Width, Height, and Depth</a>
                <a href="../docs/flexalonObject.html#minmax" class="svelte-194nj6y">Min / Max Size<span class="ml-2 text-xs font-bold text-purple-400">(v4.1)</span></a>
                <a href="../docs/flexalonObject.html#margins" class="svelte-194nj6y">Margins and Padding</a>
                <a href="../docs/flexalonObject.html#offset" class="svelte-194nj6y">Offset, Scale, and Rotation</a>

                <b class="text-zinc-200 mt-5">Layouts</b>
                <a href="../docs/flexibleLayout.html" class="svelte-194nj6y">Flexible Layout</a>
                <a href="../docs/gridLayout.html" class="svelte-194nj6y">Grid Layout</a>
                <a href="../docs/circleLayout.html" class="svelte-194nj6y">Circle / Spiral Layout</a>
                <a href="../docs/curveLayout.html" class="svelte-194nj6y">Curve Layout</a>
                <a href="../docs/randomLayout.html" class="svelte-194nj6y">Random Layout</a>
                <a href="../docs/alignLayout.html" class="svelte-194nj6y">Align Layout</a>
                <a href="../docs/shapeLayout.html" class="svelte-194nj6y">Shape Layout</a>
                <a href="../docs/constraints.html" class="svelte-194nj6y">Constraints</a>
                <a href="../docs/randomModifier.html" class="svelte-194nj6y">Random Modifier</a>

                <a class="text-zinc-200 mt-5 font-bold svelte-194nj6y" href="../docs/animators.html">Animators</a>
                <a href="../docs/animators.html#curve" class="svelte-194nj6y">Curve Animator</a>
                <a href="../docs/animators.html#lerp" class="svelte-194nj6y">Lerp Animator</a>
                <a href="../docs/animators.html#rb" class="svelte-194nj6y">Rigid Body Animator</a>
                <a href="../docs/animators.html#layouts" class="svelte-194nj6y">Animating Layouts</a>

                <b class="text-zinc-200 mt-5">Click &amp; Drag</b>
                <a href="../docs/interactable.html" class="svelte-194nj6y">Interactable</a>
                <a href="../docs/interactable.html#dragTarget" class="svelte-194nj6y">Drag Target</a>
                <a href="../docs/xr.html" class="svelte-194nj6y">XR Interactions <span class="ml-2 text-xs font-bold text-blue-300">(v3.2)</span></a>
                <a href="../docs/interactable.html#customInputProvider" class="svelte-194nj6y">Custom Input Provider</a>

                <b class="text-zinc-200 mt-5">Data Binding</b>
                <a href="../docs/cloner.html" class="svelte-194nj6y">Cloner</a>
                <a href="../docs/cloner.html#databinding" class="svelte-194nj6y">Data Binding</a>

                <b class="text-zinc-200 mt-5">Advanced</b>
                <a href="../docs/pipeline.html" class="svelte-194nj6y">Flexalon Pipeline</a>
                <a href="../docs/customLayout.html" class="svelte-194nj6y">Custom Layouts</a>
                <a href="../docs/customAnimators.html" class="svelte-194nj6y">Custom Animators</a>
                <a href="../docs/adapters.html" class="svelte-194nj6y">Adapters</a>

                <a class="text-zinc-200 mt-5 font-bold svelte-194nj6y" href="../docs/api/Flexalon.html">Scripting API</a>
                <div class="mb-20"></div></div></div>
        
        <div class="flex flex-col w-full p-10 lg:ml-[300px]"><div class="doc svelte-194nj6y">

<div class="text-white text-2xl mb-10">Core Concepts</div>

<div class="w-full h-0 pb-[56.25%] relative"><iframe width="100%" height="100%" class="absolute" src="https://www.youtube-nocookie.com/embed/-8tuEbq4Wf4" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe></div>
<p class="text-xl mt-10">Fundamentals</p>
<p>Welcome to Flexalon! In Flexalon, gameObjects are positioned, rotated, and sized by different Flexalon components that you add your gameObjects and their parents.
    Each of these components hooks into a different stage of the <a href="../docs/pipeline.html">Flexalon Pipeline</a>, the series of steps which computes the final state of the gameObject.</p>
<p class="text-xl mt-10">Flexalon Singleton Component</p>
<p>The Flexalon Singleton Component stores the runtime state of all objects managed by Flexalon. If you accidentally delete it in edit mode, you may need to re-open Unity.</p>
<div class="flex ml-10 m-5 mb-10"><img src="../images/doc-singleton.png" alt="Flexalon Singleton Component"></div>

<p>By default, the Flexalon Component is configured to automatically update all layout in the scene when you change properties on your gameObjects. You can change this by unchecking the &quot;Update in Edit Mode&quot; and &quot;Update in Play Mode&quot; options.
    If these are unchecked, an Update button will appear to manually update layout for the scene.</p>
<p>In some edge cases, Flexalon may not detect that layout needs to run again. Use &quot;Force Update&quot; to force Flexalon to recalculate layout for the entire scene.</p>

<p class="text-xl mt-10">Flexalon Objects</p>
<p>The Flexalon Object component manages how an individual gameObject&#39;s position, rotation, and size are calculated.</p>
<div class="flex ml-10 m-5 mb-10"><img src="../images/doc-object.png" alt="Flexalon Object Component"></div>
<p>Use this component to:</p>
<p class="ml-10">- Configure the <a href="../docs/flexalonObject.html#size">width, height, and depth</a></p>
<p class="ml-10">- Add <a href="../docs/flexalonObject.html#offset">offset, rotation, and scale</a></p>
<p class="ml-10">- Add <a href="../docs/flexalonObject.html#margins">margins and padding</a></p>

<p class="text-xl mt-10">Layouts</p>
<p>A Layout component determines the position, rotation, and available size for its children. Flexalon comes with the following layouts:</p>
<p><a href="../docs/flexibleLayout.html">Flexible Layout</a>: A linear series of objects with optional wrapping.</p>
<div class="flex ml-10 m-5 mb-10"><img src="../images/doc-flexible.png" alt="Flexible Layout"></div>
<p><a href="../docs/gridLayout.html">Grid Layout</a>: An evenly spaced grid of objects.</p>
<div class="flex ml-10 m-5 mb-10"><img src="../images/doc-grid.png" alt="Grid Layout"></div>
<p><a href="../docs/circleLayout.html">Circle / Spiral Layout</a>: A circle or spiral of objects.</p>
<div class="flex ml-10 m-5 mb-10"><img src="../images/doc-circle.png" alt="Circle Layout"></div>
<p><a href="../docs/curveLayout.html">Curve Layout</a>: A bezier curve of objects.</p>
<div class="flex ml-10 m-5 mb-10"><img src="../images/doc-curve.png" alt="Curve Layout"></div>
<p><a href="../docs/randomLayout.html">Random Layout</a>: Randomize positions, rotations, or scales for objects.</p>
<div class="flex ml-10 m-5 mb-10"><img src="../images/doc-random.png" alt="Random Layout"></div>
<p><a href="../docs/alignLayout.html">Align Layout</a>: Align position child objects to the parent along selected axes.</p>
<div class="flex ml-10 m-5 mb-10"><img src="../images/doc-align.png" alt="Align Layout"></div>
<p><a href="../docs/shapeLayout.html">Shape Layout</a>: Shape position child objects to the parent along selected axes.</p>
<div class="flex ml-10 m-5 mb-10"><img src="../images/doc-shape.png" alt="Shape Layout"></div>
<p>You can also create your own <a href="../docs/customLayout.html">Custom Layouts</a>.</p>

<p class="text-xl mt-10">Constraints</p>
<p>The Flexalon Constraint component allows your to position one gameObject relative to another. The two gameObjects don&#39;t have to be in the same hierarchy.</p>
<div class="flex ml-10 m-5 mb-10"><video muted autoplay loop playsinline><source src="https://www.flexalon.com/videos/doc-constraint.mp4"></video></div>

<p class="text-xl mt-10">Animators</p>
<p>Use animators to create smooth transitions and motions between layout positions.</p>
<div class="flex ml-10 m-5 mb-10"><video muted autoplay loop playsinline><source src="https://www.flexalon.com/videos/doc-chess-physics.mp4"></video></div>

<p><a href="../docs/animators.html#curve">Curve animator</a></p>
<p><a href="../docs/animators.html#lerp">Lerp animator</a></p>
<p><a href="../docs/animators.html#rb">Rigid Body animator</a></p>

<p class="text-xl mt-10">Interactions</p>
<p>Use Flexalon Interactable to easily create user interactions for clicking and dragging objects between layouts.</p>
<div class="flex ml-10 m-5 mb-10"><video muted autoplay loop playsinline><source src="https://www.flexalon.com/videos/doc-interactable.mp4"></video></div>

<p><a href="../docs/interactable.html">Interactable</a></p>
<p><a href="../docs/interactable.html#dragTarget">Drag Target</a></p>
<p><a href="../docs/interactable.html#customInputProvider">Custom Input Provider</a></p>

<p class="text-xl mt-10">Data Binding</p>
<p>Generate child objects with a cloner and bind them to a data source.</p>
<div class="flex ml-10 m-5 mb-10"><img src="../images/doc-google-search.png" alt="Data Binding Example"></div>
<p><a href="../docs/cloner.html">Cloner</a>: Generate child objects from prefabs.</p>
<p><a href="../docs/cloner.html#databinding">Data Binding</a>: Learn how to create a data source and bind it to child objects.</p>

<p class="text-xl mt-10">API</p>
<p>Extend the Flexalon Pipeline with your own layouts, animators, and adapters.</p>
<p><a href="../docs/pipeline.html">Flexalon Pipeline.</a></p>
<p><a href="../docs/customLayout.html">Custom Layouts</a></p>
<p><a href="../docs/customAnimators.html">Custom Animators</a></p>
<p><a href="../docs/adapters.html">Adapters</a></p></div></div></div>
</div>


			
			
		</div>
	</body>
</html>
