fileFormatVersion: 2
guid: 1b6d9104e84a945408637c4e98fc7d32
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: GIANT_VIPER
    100002: GIANT_VIPER_
    100004: GIANT_VIPER_ Footsteps
    100006: GIANT_VIPER_ Head
    100008: GIANT_VIPER_ Neck
    100010: GIANT_VIPER_ Ponytail1
    100012: GIANT_VIPER_ Spine
    100014: GIANT_VIPER_ Spine1
    100016: GIANT_VIPER_ Spine2
    100018: GIANT_VIPER_ Spine3
    100020: GIANT_VIPER_ Spine4
    100022: GIANT_VIPER_ Spine5
    100024: GIANT_VIPER_ Spine6
    100026: GIANT_VIPER_ Spine7
    100028: GIANT_VIPER_ Tail
    100030: GIANT_VIPER_ Tail1
    100032: GIANT_VIPER_ Tail2
    100034: GIANT_VIPER_ Tail3
    100036: GIANT_VIPER_ Tail4
    100038: GIANT_VIPER_ Tail5
    100040: GIANT_VIPER_ Tail6
    100042: GIANT_VIPER_ Tail7
    100044: GIANT_VIPER_ Tail8
    100046: GIANT_VIPER_ Tail9
    100048: GIANT_VIPER_HOOK_Left
    100050: GIANT_VIPER_HOOK_Right
    100052: GIANT_VIPER_Tongue_01
    100054: GIANT_VIPER_Tongue_02
    100056: GIANT_VIPER_Tongue_03
    100058: GIANT_VIPER_Tongue_04
    100060: GIANT_VIPER_Tongue_05
    100062: ROOT_
    100064: //RootNode
    400000: GIANT_VIPER
    400002: GIANT_VIPER_
    400004: GIANT_VIPER_ Footsteps
    400006: GIANT_VIPER_ Head
    400008: GIANT_VIPER_ Neck
    400010: GIANT_VIPER_ Ponytail1
    400012: GIANT_VIPER_ Spine
    400014: GIANT_VIPER_ Spine1
    400016: GIANT_VIPER_ Spine2
    400018: GIANT_VIPER_ Spine3
    400020: GIANT_VIPER_ Spine4
    400022: GIANT_VIPER_ Spine5
    400024: GIANT_VIPER_ Spine6
    400026: GIANT_VIPER_ Spine7
    400028: GIANT_VIPER_ Tail
    400030: GIANT_VIPER_ Tail1
    400032: GIANT_VIPER_ Tail2
    400034: GIANT_VIPER_ Tail3
    400036: GIANT_VIPER_ Tail4
    400038: GIANT_VIPER_ Tail5
    400040: GIANT_VIPER_ Tail6
    400042: GIANT_VIPER_ Tail7
    400044: GIANT_VIPER_ Tail8
    400046: GIANT_VIPER_ Tail9
    400048: GIANT_VIPER_HOOK_Left
    400050: GIANT_VIPER_HOOK_Right
    400052: GIANT_VIPER_Tongue_01
    400054: GIANT_VIPER_Tongue_02
    400056: GIANT_VIPER_Tongue_03
    400058: GIANT_VIPER_Tongue_04
    400060: GIANT_VIPER_Tongue_05
    400062: ROOT_
    400064: //RootNode
    2100000: GIANT_VIPER
    4300000: GIANT_VIPER
    7400000: Take 001
    9500000: //RootNode
    13700000: GIANT_VIPER
  externalObjects: {}
  materials:
    importMaterials: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 1
    hasPreviousCalculatedGlobalScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: ROOT_
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 2
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
