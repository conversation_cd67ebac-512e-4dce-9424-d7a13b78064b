﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2013/05/nuspec.xsd">
  <metadata>
    <id>System.Text.Json</id>
    <version>8.0.5</version>
    <authors>Microsoft</authors>
    <license type="expression">MIT</license>
    <licenseUrl>https://licenses.nuget.org/MIT</licenseUrl>
    <icon>Icon.png</icon>
    <readme>PACKAGE.md</readme>
    <projectUrl>https://dot.net/</projectUrl>
    <description>Provides high-performance and low-allocating types that serialize objects to JavaScript Object Notation (JSON) text and deserialize JSON text to objects, with UTF-8 support built-in. Also provides types to read and write JSON text encoded as UTF-8, and to create an in-memory document object model (DOM), that is read-only, for random access of the JSON elements within a structured view of the data.

The System.Text.Json library is built-in as part of the shared framework in .NET Runtime. The package can be installed when you need to use it in other target frameworks.</description>
    <releaseNotes>https://go.microsoft.com/fwlink/?LinkID=799421</releaseNotes>
    <copyright>© Microsoft Corporation. All rights reserved.</copyright>
    <serviceable>true</serviceable>
    <repository type="git" url="https://github.com/dotnet/runtime" commit="81cabf2857a01351e5ab578947c7403a5b128ad1" />
    <dependencies>
      <group targetFramework=".NETFramework4.6.2">
        <dependency id="Microsoft.Bcl.AsyncInterfaces" version="8.0.0" exclude="Build,Analyzers" />
        <dependency id="System.Text.Encodings.Web" version="8.0.0" exclude="Build,Analyzers" />
        <dependency id="System.Buffers" version="4.5.1" exclude="Build,Analyzers" />
        <dependency id="System.Memory" version="4.5.5" exclude="Build,Analyzers" />
        <dependency id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" exclude="Build,Analyzers" />
        <dependency id="System.Threading.Tasks.Extensions" version="4.5.4" exclude="Build,Analyzers" />
        <dependency id="System.ValueTuple" version="4.5.0" exclude="Build,Analyzers" />
      </group>
      <group targetFramework="net6.0">
        <dependency id="System.Text.Encodings.Web" version="8.0.0" exclude="Build,Analyzers" />
        <dependency id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" exclude="Build,Analyzers" />
      </group>
      <group targetFramework="net7.0">
        <dependency id="System.Text.Encodings.Web" version="8.0.0" exclude="Build,Analyzers" />
      </group>
      <group targetFramework="net8.0" />
      <group targetFramework=".NETStandard2.0">
        <dependency id="Microsoft.Bcl.AsyncInterfaces" version="8.0.0" exclude="Build,Analyzers" />
        <dependency id="System.Text.Encodings.Web" version="8.0.0" exclude="Build,Analyzers" />
        <dependency id="System.Buffers" version="4.5.1" exclude="Build,Analyzers" />
        <dependency id="System.Memory" version="4.5.5" exclude="Build,Analyzers" />
        <dependency id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" exclude="Build,Analyzers" />
        <dependency id="System.Threading.Tasks.Extensions" version="4.5.4" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
</package>