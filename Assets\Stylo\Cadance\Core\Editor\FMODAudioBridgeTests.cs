using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace Stylo.Cadance
{
    /// <summary>
    /// Test suite for FMOD Audio Bridge functionality.
    /// Validates FMOD-to-Unity audio integration for timeline functionality.
    /// </summary>
    public static class FMODAudioBridgeTests
    {
        [MenuItem("Stylo/Cadance/Tools/Test FMOD Audio Bridge")]
        public static void RunTests()
        {
            Debug.Log("=== FMOD Audio Bridge Test Suite ===");
            
            bool allTestsPassed = true;
            
            allTestsPassed &= TestFMODFileDiscovery();
            allTestsPassed &= TestAudioClipImport();
            allTestsPassed &= TestCadanceAssetIntegration();
            allTestsPassed &= TestBatchImport();
            
            string result = allTestsPassed ? "✅ ALL TESTS PASSED" : "❌ SOME TESTS FAILED";
            Debug.Log($"=== Test Results: {result} ===");
            
            EditorUtility.DisplayDialog("FMOD Audio Bridge Tests", 
                $"Test suite completed.\n\n{result}\n\nCheck console for detailed results.", "OK");
        }

        private static bool TestFMODFileDiscovery()
        {
            Debug.Log("🔍 Testing FMOD File Discovery...");
            
            try
            {
                var availableFiles = FMODAudioBridge.GetAvailableFMODAudioFiles();
                
                if (availableFiles.Count > 0)
                {
                    Debug.Log($"✅ Found {availableFiles.Count} FMOD audio files");
                    
                    // Log first few files for verification
                    for (int i = 0; i < Mathf.Min(5, availableFiles.Count); i++)
                    {
                        Debug.Log($"   • {availableFiles[i]}");
                    }
                    
                    return true;
                }
                else
                {
                    Debug.LogWarning("⚠ No FMOD audio files found. Check FMOD project path.");
                    return false;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"❌ FMOD File Discovery failed: {ex.Message}");
                return false;
            }
        }

        private static bool TestAudioClipImport()
        {
            Debug.Log("📥 Testing AudioClip Import...");
            
            try
            {
                // Find a test CadanceAsset with missing AudioClip
                string[] cadanceGuids = AssetDatabase.FindAssets("t:CadanceAsset");
                CadanceAsset testAsset = null;
                
                foreach (string guid in cadanceGuids)
                {
                    string path = AssetDatabase.GUIDToAssetPath(guid);
                    var asset = AssetDatabase.LoadAssetAtPath<CadanceAsset>(path);
                    
                    if (asset != null && asset.SourceClip == null && !string.IsNullOrEmpty(asset.SourceClipName))
                    {
                        testAsset = asset;
                        break;
                    }
                }

                if (testAsset == null)
                {
                    Debug.LogWarning("⚠ No suitable test asset found (need CadanceAsset with missing AudioClip)");
                    return false;
                }

                Debug.Log($"🧪 Testing with asset: {testAsset.name} (SourceClipName: {testAsset.SourceClipName})");
                
                // Test import
                AudioClip importedClip = FMODAudioBridge.GetOrCreateAudioClipForTimeline(testAsset.SourceClipName);
                
                if (importedClip != null)
                {
                    Debug.Log($"✅ Successfully imported AudioClip: {importedClip.name}");
                    Debug.Log($"   Duration: {importedClip.length:F2}s, Sample Rate: {importedClip.frequency}Hz");
                    return true;
                }
                else
                {
                    Debug.LogWarning($"⚠ Could not import AudioClip for '{testAsset.SourceClipName}'");
                    return false;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"❌ AudioClip Import test failed: {ex.Message}");
                return false;
            }
        }

        private static bool TestCadanceAssetIntegration()
        {
            Debug.Log("🔗 Testing CadanceAsset Integration...");
            
            try
            {
                // Find FMOD-based CadanceAssets
                string[] cadanceGuids = AssetDatabase.FindAssets("t:CadanceAsset");
                int fmodAssetsFound = 0;
                int successfulIntegrations = 0;
                
                foreach (string guid in cadanceGuids)
                {
                    string path = AssetDatabase.GUIDToAssetPath(guid);
                    var asset = AssetDatabase.LoadAssetAtPath<CadanceAsset>(path);
                    
                    if (asset != null && !string.IsNullOrEmpty(asset.SourceClipName))
                    {
                        // Check if it's FMOD-based
                        string sourceName = asset.SourceClipName.ToLower();
                        bool isFMOD = sourceName.Contains("gs_") || sourceName.Contains("flux") || 
                                     sourceName.Contains("drum_loop") || sourceName.Contains("_v");
                        
                        if (isFMOD)
                        {
                            fmodAssetsFound++;
                            
                            // Test integration
                            AudioClip clip = FMODAudioBridge.GetOrCreateAudioClipForTimeline(asset.SourceClipName);
                            if (clip != null)
                            {
                                successfulIntegrations++;
                            }
                        }
                    }
                }

                Debug.Log($"✅ Found {fmodAssetsFound} FMOD-based CadanceAssets");
                Debug.Log($"✅ Successfully integrated {successfulIntegrations} assets");
                
                return fmodAssetsFound > 0 && successfulIntegrations > 0;
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"❌ CadanceAsset Integration test failed: {ex.Message}");
                return false;
            }
        }

        private static bool TestBatchImport()
        {
            Debug.Log("📦 Testing Batch Import...");
            
            try
            {
                // Find a CadanceSet to test with
                string[] setGuids = AssetDatabase.FindAssets("t:CadanceSet");
                
                if (setGuids.Length == 0)
                {
                    Debug.LogWarning("⚠ No CadanceSets found for batch import test");
                    return false;
                }

                string path = AssetDatabase.GUIDToAssetPath(setGuids[0]);
                var testSet = AssetDatabase.LoadAssetAtPath<CadanceSet>(path);
                
                if (testSet == null)
                {
                    Debug.LogWarning("⚠ Could not load test CadanceSet");
                    return false;
                }

                Debug.Log($"🧪 Testing batch import with CadanceSet: {testSet.name}");
                
                // Count missing AudioClips before
                int missingBefore = 0;
                foreach (var entry in testSet.Cadances)
                {
                    if (entry.cadance != null && entry.cadance.SourceClip == null && 
                        !string.IsNullOrEmpty(entry.cadance.SourceClipName))
                    {
                        missingBefore++;
                    }
                }

                // Perform batch import
                int imported = FMODAudioBridge.BatchImportForCadanceSet(testSet);
                
                Debug.Log($"✅ Batch import completed: {imported} AudioClips imported");
                Debug.Log($"   Missing before: {missingBefore}, Imported: {imported}");
                
                return imported > 0;
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"❌ Batch Import test failed: {ex.Message}");
                return false;
            }
        }

        [MenuItem("Stylo/Cadance/Tools/Show FMOD Integration Status")]
        public static void ShowIntegrationStatus()
        {
            Debug.Log("=== FMOD Integration Status ===");
            
            // Count CadanceAssets
            string[] cadanceGuids = AssetDatabase.FindAssets("t:CadanceAsset");
            int totalAssets = cadanceGuids.Length;
            int fmodAssets = 0;
            int withAudioClip = 0;
            int missingAudioClip = 0;
            
            foreach (string guid in cadanceGuids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                var asset = AssetDatabase.LoadAssetAtPath<CadanceAsset>(path);
                
                if (asset != null && !string.IsNullOrEmpty(asset.SourceClipName))
                {
                    string sourceName = asset.SourceClipName.ToLower();
                    bool isFMOD = sourceName.Contains("gs_") || sourceName.Contains("flux") || 
                                 sourceName.Contains("drum_loop") || sourceName.Contains("_v");
                    
                    if (isFMOD)
                    {
                        fmodAssets++;
                        
                        if (asset.SourceClip != null)
                        {
                            withAudioClip++;
                        }
                        else
                        {
                            missingAudioClip++;
                        }
                    }
                }
            }

            // Count available FMOD files
            var availableFMODFiles = FMODAudioBridge.GetAvailableFMODAudioFiles();
            
            string status = $"FMOD Integration Status:\n\n" +
                           $"📊 CadanceAssets: {totalAssets} total\n" +
                           $"🎵 FMOD-based assets: {fmodAssets}\n" +
                           $"✅ With AudioClip: {withAudioClip}\n" +
                           $"❌ Missing AudioClip: {missingAudioClip}\n" +
                           $"📁 Available FMOD files: {availableFMODFiles.Count}\n\n" +
                           $"Integration Rate: {(fmodAssets > 0 ? (withAudioClip * 100 / fmodAssets) : 0)}%";

            Debug.Log(status);
            EditorUtility.DisplayDialog("FMOD Integration Status", status, "OK");
        }

        [MenuItem("Stylo/Cadance/Tools/Clear FMOD Audio Cache")]
        public static void ClearFMODCache()
        {
            FMODAudioBridge.ClearCache();
            Debug.Log("🧹 FMOD Audio Bridge cache cleared");
            EditorUtility.DisplayDialog("Cache Cleared", "FMOD Audio Bridge cache has been cleared.", "OK");
        }
    }
}
