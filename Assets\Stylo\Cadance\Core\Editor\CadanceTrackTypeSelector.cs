using UnityEngine;
using UnityEditor;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace Stylo.Cadance.Editor
{
    /// <summary>
    /// Advanced track type selection system that matches Koreographer's track creation workflow.
    /// Provides payload type filtering and track type validation equivalent to KoreographyTrackTypeUtils.
    /// </summary>
    public static class CadanceTrackTypeSelector
    {
        private static Dictionary<Type, List<Type>> trackToPayloadTypes;
        private static List<Type> allTrackTypes;
        private static List<Type> allPayloadTypes;

        /// <summary>
        /// Shows a track type selection menu with payload filtering, equivalent to Koreographer's track creation.
        /// </summary>
        /// <param name="position">Position to show the menu</param>
        /// <param name="onTrackTypeSelected">Callback when track type is selected</param>
        public static void ShowTrackTypeMenu(Rect position, Action<Type> onTrackTypeSelected)
        {
            RefreshTrackTypes();

            GenericMenu menu = new GenericMenu();

            // Add basic track types
            menu.AddItem(new GUIContent("Basic Track/CadanceTrack"), false, () => onTrackTypeSelected?.Invoke(typeof(CadanceTrack)));

            // Add specialized track types if any exist
            foreach (var trackType in allTrackTypes)
            {
                if (trackType != typeof(CadanceTrack) && !IsTypeNoEditorCreate(trackType))
                {
                    string friendlyName = GetFriendlyTrackTypeName(trackType);
                    menu.AddItem(new GUIContent($"Specialized/{friendlyName}"), false, () => onTrackTypeSelected?.Invoke(trackType));
                }
            }

            menu.DropDown(position);
        }

        /// <summary>
        /// Shows a payload type selection menu for a specific track type.
        /// </summary>
        /// <param name="position">Position to show the menu</param>
        /// <param name="trackType">Track type to filter payload types for</param>
        /// <param name="onPayloadTypeSelected">Callback when payload type is selected</param>
        public static void ShowPayloadTypeMenu(Rect position, Type trackType, Action<Type> onPayloadTypeSelected)
        {
            RefreshTrackTypes();

            GenericMenu menu = new GenericMenu();

            // Add null payload option
            menu.AddItem(new GUIContent("No Payload"), false, () => onPayloadTypeSelected?.Invoke(null));

            // Get supported payload types for this track
            var supportedPayloads = GetPayloadTypesForTrackType(trackType);

            foreach (var payloadType in supportedPayloads)
            {
                if (!IsTypeNoEditorCreate(payloadType))
                {
                    string friendlyName = GetFriendlyPayloadTypeName(payloadType);
                    menu.AddItem(new GUIContent(friendlyName), false, () => onPayloadTypeSelected?.Invoke(payloadType));
                }
            }

            menu.DropDown(position);
        }

        /// <summary>
        /// Gets all track types, equivalent to KoreographyTrackTypeUtils.GetTrackTypes().
        /// </summary>
        /// <param name="excludeNoEditorCreate">Whether to exclude types with NoEditorCreate attribute</param>
        /// <returns>List of track types</returns>
        public static List<Type> GetTrackTypes(bool excludeNoEditorCreate = true)
        {
            RefreshTrackTypes();

            if (excludeNoEditorCreate)
            {
                return allTrackTypes.Where(t => !IsTypeNoEditorCreate(t)).ToList();
            }

            return new List<Type>(allTrackTypes);
        }

        /// <summary>
        /// Gets payload types supported by a specific track type.
        /// </summary>
        /// <param name="trackType">Track type to query</param>
        /// <param name="excludeNoEditorCreate">Whether to exclude types with NoEditorCreate attribute</param>
        /// <returns>List of supported payload types</returns>
        public static List<Type> GetPayloadTypesForTrackType(Type trackType, bool excludeNoEditorCreate = true)
        {
            RefreshTrackTypes();

            if (trackToPayloadTypes.TryGetValue(trackType, out var payloadTypes))
            {
                if (excludeNoEditorCreate)
                {
                    return payloadTypes.Where(t => !IsTypeNoEditorCreate(t)).ToList();
                }
                return new List<Type>(payloadTypes);
            }

            // Default to all payload types for unknown track types
            return GetAllPayloadTypes(excludeNoEditorCreate);
        }

        /// <summary>
        /// Gets all payload types, equivalent to KoreographyTrackTypeUtils.GetAllPayloadTypes().
        /// </summary>
        /// <param name="excludeNoEditorCreate">Whether to exclude types with NoEditorCreate attribute</param>
        /// <returns>List of all payload types</returns>
        public static List<Type> GetAllPayloadTypes(bool excludeNoEditorCreate = true)
        {
            RefreshTrackTypes();

            if (excludeNoEditorCreate)
            {
                return allPayloadTypes.Where(t => !IsTypeNoEditorCreate(t)).ToList();
            }

            return new List<Type>(allPayloadTypes);
        }

        /// <summary>
        /// Creates a track of the specified type with the given event ID.
        /// </summary>
        /// <param name="trackType">Type of track to create</param>
        /// <param name="eventID">Event ID for the track</param>
        /// <returns>Created track instance</returns>
        public static CadanceTrackBase CreateTrackOfType(Type trackType, string eventID)
        {
            if (!typeof(CadanceTrackBase).IsAssignableFrom(trackType))
            {
                Debug.LogError($"[CadanceTrackTypeSelector] Type {trackType.Name} is not a valid track type");
                return null;
            }

            var track = ScriptableObject.CreateInstance(trackType) as CadanceTrackBase;
            if (track != null)
            {
                track.EventID = eventID;
                track.name = $"{trackType.Name}_{eventID}";
            }

            return track;
        }

        /// <summary>
        /// Gets a friendly display name for a track type.
        /// </summary>
        /// <param name="trackType">Track type</param>
        /// <returns>Friendly name</returns>
        public static string GetFriendlyTrackTypeName(Type trackType)
        {
            // Check for custom display name attribute
            var displayNameAttr = trackType.GetCustomAttribute<System.ComponentModel.DisplayNameAttribute>();
            if (displayNameAttr != null)
            {
                return displayNameAttr.DisplayName;
            }

            // Default to class name without "Track" suffix
            string name = trackType.Name;
            if (name.EndsWith("Track"))
            {
                name = name.Substring(0, name.Length - 5);
            }

            return name;
        }

        /// <summary>
        /// Gets a friendly display name for a payload type.
        /// </summary>
        /// <param name="payloadType">Payload type</param>
        /// <returns>Friendly name</returns>
        public static string GetFriendlyPayloadTypeName(Type payloadType)
        {
            // Check for custom display name attribute
            var displayNameAttr = payloadType.GetCustomAttribute<System.ComponentModel.DisplayNameAttribute>();
            if (displayNameAttr != null)
            {
                return displayNameAttr.DisplayName;
            }

            // Default to class name without "Payload" suffix
            string name = payloadType.Name;
            if (name.EndsWith("Payload"))
            {
                name = name.Substring(0, name.Length - 7);
            }

            return name;
        }

        /// <summary>
        /// Checks if a type has the NoEditorCreate attribute.
        /// </summary>
        /// <param name="type">Type to check</param>
        /// <returns>True if type should not be created in editor</returns>
        public static bool IsTypeNoEditorCreate(Type type)
        {
            // Check for NoEditorCreate attribute (would need to be defined)
            // For now, return false as we don't have this attribute defined
            return false;
        }

        /// <summary>
        /// Refreshes the cached track and payload type information.
        /// </summary>
        private static void RefreshTrackTypes()
        {
            if (allTrackTypes != null && allPayloadTypes != null && trackToPayloadTypes != null)
                return;

            allTrackTypes = new List<Type>();
            allPayloadTypes = new List<Type>();
            trackToPayloadTypes = new Dictionary<Type, List<Type>>();

            // Find all track types
            foreach (var assembly in AppDomain.CurrentDomain.GetAssemblies())
            {
                try
                {
                    foreach (var type in assembly.GetTypes())
                    {
                        if (typeof(CadanceTrackBase).IsAssignableFrom(type) && !type.IsAbstract)
                        {
                            allTrackTypes.Add(type);
                        }

                        if (typeof(IPayload).IsAssignableFrom(type) && !type.IsAbstract && !type.IsInterface)
                        {
                            allPayloadTypes.Add(type);
                        }
                    }
                }
                catch (ReflectionTypeLoadException)
                {
                    // Skip assemblies that can't be loaded
                    continue;
                }
            }

            // Build track to payload mapping
            foreach (var trackType in allTrackTypes)
            {
                trackToPayloadTypes[trackType] = new List<Type>(allPayloadTypes);
            }
        }
    }
}
