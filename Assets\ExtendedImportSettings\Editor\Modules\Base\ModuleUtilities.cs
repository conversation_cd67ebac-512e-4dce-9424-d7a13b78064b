using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using UnityEditor;
using UnityEngine;

namespace ExtendedImportSettings
{
    [Flags, Serializable]
    public enum UVChannel
    {
        None = 0,
        UV0 = 1 << 0,
        UV1 = 1 << 1,
        UV2 = 1 << 2,
        UV3 = 1 << 3,
        UV4 = 1 << 4,
        UV5 = 1 << 5,
        UV6 = 1 << 6,
        UV7 = 1 << 7
    }
    
    [Serializable]
    public enum UVProjectionType
    {
        PlanarXY,
        PlanarXZ,
        PlanarZY,
        Spherical,
        Cubic,
    }
    
    public static class ModuleUtilities
    {
        public static List<int> ToIndexes(this UVChannel channel)
        {
            List<int> indices = new List<int>();
            for (int i = 0; i < 8; i++)
            {
                if (((int)channel & (1 << i)) != 0)
                {
                    indices.Add(i);
                }
            }
            return indices;
        }
        
        public static bool Has(this UVChannel current, UVChannel other)
        {
            return (current & other) != 0;
        }
        
        public static List<Mesh> GetModelMeshes(object assetImporter)
        {
            var assetPath = ((ModelImporter)assetImporter).assetPath;
            return GetModelMeshes(assetPath);
        }
        
        public static List<Mesh> GetModelMeshes(string assetPath)
        {
            var assets = AssetDatabase.LoadAllAssetsAtPath(assetPath);
            
            var meshes = new List<Mesh>();
            foreach(var next in assets)
                if(next is Mesh mesh)
                    meshes.Add(mesh);

            return meshes;
        }

        public static void DrawPropertyField(Type type, SerializedProperty serializedProperty, params string[] except)
        {
            var fieldInfos = type.GetFields(BindingFlags.Public | BindingFlags.Instance);
            for (var i = 0; i < fieldInfos.Length; i++)
            {
                var fieldInfo = fieldInfos[i];
                if(except.Contains(fieldInfo.Name))
                    continue;
                        
                SerializedProperty fieldSerializedProperty = serializedProperty.FindPropertyRelative(fieldInfo.Name);
                EditorGUILayout.PropertyField(fieldSerializedProperty);
            }
        }
    }
}