{"name": "org.happy-turtle.order-independent-transparency", "version": "5.2.1", "unity": "2022.3", "displayName": "Order-independent Transparency", "description": "This is an implementation of order-independent transparency for the Built-In Pipeline. It uses Per-Pixel Linked Lists, implemented with RWStructuredBuffers. This is a feature requiring Shader Model 5.0 with ComputeBuffers, see the Unity Manual for supported platforms.", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "dependencies": {}, "samples": [{"displayName": "Post Process Demo", "description": "Order-independent transparency using a post processing effect", "path": "Samples~/Post Process Demo"}, {"displayName": "HDRP Custom Pass Demo", "description": "Order-independent transparency in the High-Definition render pipeline", "path": "Samples~/HDRP Demo"}, {"displayName": "URP Renderer Feature Demo", "description": "Order-independent transparency in the Universal render pipeline", "path": "Samples~/URP Demo"}], "_fingerprint": "dbf1829e74fa210a92a5b660289073a068c3d999"}