using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using SonicBloom.Koreo;
using SonicBloom.Koreo.Players.FMODStudio;

namespace Stylo.Cadance.Editor
{
    /// <summary>
    /// Comprehensive tool for converting all Koreographer assets to Cadance equivalents.
    /// Handles FMODKoreographySet, individual Koreography, and KoreographyTrack assets.
    /// </summary>
    public class CadanceAssetConversionTool : EditorWindow
    {
        // Discovery results
        private List<KoreographerAssetInfo> discoveredAssets = new List<KoreographerAssetInfo>();
        private Dictionary<string, List<KoreographerAssetInfo>> assetsByType = new Dictionary<string, List<KoreographerAssetInfo>>();
        private bool discoveryComplete = false;

        // Conversion settings
        private bool convertFMODKoreographySets = true;
        private bool convertIndividualKoreographies = true;
        private bool convertKoreographyTracks = true;
        private bool createCadanceSets = true;
        private bool preserveOriginalAssets = true;
        private bool validateConversions = true;

        // Output settings
        private string outputDirectory = "Assets/Converted Cadance Assets";
        private bool organizeByType = true;
        private bool generateReport = true;

        // UI state
        private Vector2 scrollPosition;
        private Vector2 assetScrollPosition;
        private bool showAdvancedOptions = false;
        private bool showConversionSettings = true;
        private bool showAssetList = true;

        // Conversion state
        private bool isConverting = false;
        private float conversionProgress = 0f;
        private string currentConversionStep = "";
        private List<ConversionResult> conversionResults = new List<ConversionResult>();

        [MenuItem("Stylo/Cadance/Asset Conversion Tool")]
        public static void ShowWindow()
        {
            var window = GetWindow<CadanceAssetConversionTool>("Cadance Asset Conversion Tool");
            window.minSize = new Vector2(800, 600);
            window.titleContent = new GUIContent("Asset Conversion Tool", "Convert Koreographer assets to Cadance");
        }

        private void OnGUI()
        {
            DrawHeader();

            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);

            DrawDiscoverySection();

            if (discoveryComplete)
            {
                DrawConversionSettings();
                DrawAssetList();
                DrawConversionControls();
                DrawConversionProgress();
                DrawResults();
            }

            EditorGUILayout.EndScrollView();
        }

        private void DrawHeader()
        {
            EditorGUILayout.LabelField("Cadance Asset Conversion Tool", EditorStyles.boldLabel);
            EditorGUILayout.Space();

            EditorGUILayout.HelpBox(
                "This tool will scan your project for all Koreographer assets and convert them to Cadance equivalents. " +
                "It handles FMODKoreographySet, Koreography, and KoreographyTrack assets with full validation.",
                MessageType.Info);

            EditorGUILayout.Space();
        }

        private void DrawDiscoverySection()
        {
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("Asset Discovery", EditorStyles.boldLabel);

            if (!discoveryComplete)
            {
                EditorGUILayout.LabelField("Click 'Discover Assets' to scan your project for Koreographer assets.");

                if (GUILayout.Button("Discover Assets", GUILayout.Height(30)))
                {
                    DiscoverKoreographerAssets();
                }
            }
            else
            {
                EditorGUILayout.LabelField($"Discovery complete. Found {discoveredAssets.Count} Koreographer assets:");

                foreach (var kvp in assetsByType)
                {
                    EditorGUILayout.LabelField($"  • {kvp.Key}: {kvp.Value.Count} assets");
                }

                EditorGUILayout.Space();

                if (GUILayout.Button("Re-scan Project"))
                {
                    DiscoverKoreographerAssets();
                }
            }

            EditorGUILayout.EndVertical();
        }

        private void DrawConversionSettings()
        {
            showConversionSettings = EditorGUILayout.Foldout(showConversionSettings, "Conversion Settings", true);

            if (showConversionSettings)
            {
                EditorGUILayout.BeginVertical("box");

                // Asset type selection
                EditorGUILayout.LabelField("Asset Types to Convert", EditorStyles.boldLabel);
                convertFMODKoreographySets = EditorGUILayout.Toggle("FMODKoreographySet Assets", convertFMODKoreographySets);
                convertIndividualKoreographies = EditorGUILayout.Toggle("Individual Koreography Assets", convertIndividualKoreographies);
                convertKoreographyTracks = EditorGUILayout.Toggle("KoreographyTrack Assets", convertKoreographyTracks);

                EditorGUILayout.Space();

                // Output settings
                EditorGUILayout.LabelField("Output Settings", EditorStyles.boldLabel);

                EditorGUILayout.BeginHorizontal();
                outputDirectory = EditorGUILayout.TextField("Output Directory", outputDirectory);
                if (GUILayout.Button("Browse", GUILayout.Width(60)))
                {
                    string selectedPath = EditorUtility.OpenFolderPanel("Select Output Directory", "Assets", "");
                    if (!string.IsNullOrEmpty(selectedPath) && selectedPath.StartsWith(Application.dataPath))
                    {
                        outputDirectory = "Assets" + selectedPath.Substring(Application.dataPath.Length);
                    }
                }
                EditorGUILayout.EndHorizontal();

                createCadanceSets = EditorGUILayout.Toggle("Create CadanceSet Assets", createCadanceSets);
                organizeByType = EditorGUILayout.Toggle("Organize by Asset Type", organizeByType);
                preserveOriginalAssets = EditorGUILayout.Toggle("Preserve Original Assets", preserveOriginalAssets);

                EditorGUILayout.Space();

                // Advanced options
                showAdvancedOptions = EditorGUILayout.Foldout(showAdvancedOptions, "Advanced Options");
                if (showAdvancedOptions)
                {
                    EditorGUI.indentLevel++;
                    validateConversions = EditorGUILayout.Toggle("Validate Conversions", validateConversions);
                    generateReport = EditorGUILayout.Toggle("Generate Conversion Report", generateReport);
                    EditorGUI.indentLevel--;
                }

                EditorGUILayout.EndVertical();
            }
        }

        private void DrawAssetList()
        {
            showAssetList = EditorGUILayout.Foldout(showAssetList, $"Discovered Assets ({discoveredAssets.Count})", true);

            if (showAssetList && discoveredAssets.Count > 0)
            {
                EditorGUILayout.BeginVertical("box");

                assetScrollPosition = EditorGUILayout.BeginScrollView(assetScrollPosition, GUILayout.Height(200));

                foreach (var assetInfo in discoveredAssets)
                {
                    DrawAssetItem(assetInfo);
                }

                EditorGUILayout.EndScrollView();

                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button("Select All"))
                {
                    foreach (var asset in discoveredAssets)
                        asset.selected = true;
                }

                if (GUILayout.Button("Select None"))
                {
                    foreach (var asset in discoveredAssets)
                        asset.selected = false;
                }

                if (GUILayout.Button("Select by Type..."))
                {
                    ShowAssetTypeSelectionMenu();
                }
                EditorGUILayout.EndHorizontal();

                EditorGUILayout.EndVertical();
            }
        }

        private void DrawAssetItem(KoreographerAssetInfo assetInfo)
        {
            EditorGUILayout.BeginHorizontal();

            assetInfo.selected = EditorGUILayout.Toggle(assetInfo.selected, GUILayout.Width(20));

            EditorGUILayout.LabelField(assetInfo.name, GUILayout.Width(200));
            EditorGUILayout.LabelField(assetInfo.type, GUILayout.Width(150));
            EditorGUILayout.LabelField(assetInfo.path, EditorStyles.miniLabel);

            if (GUILayout.Button("Select", GUILayout.Width(60)))
            {
                Selection.activeObject = AssetDatabase.LoadAssetAtPath<Object>(assetInfo.path);
                EditorGUIUtility.PingObject(Selection.activeObject);
            }

            EditorGUILayout.EndHorizontal();
        }

        private void DrawConversionControls()
        {
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("Conversion Controls", EditorStyles.boldLabel);

            GUI.enabled = !isConverting && discoveredAssets.Any(a => a.selected);

            EditorGUILayout.BeginHorizontal();

            if (GUILayout.Button("Convert Selected Assets", GUILayout.Height(30)))
            {
                StartConversion();
            }

            if (GUILayout.Button("Convert All Assets", GUILayout.Height(30)))
            {
                foreach (var asset in discoveredAssets)
                    asset.selected = true;
                StartConversion();
            }

            EditorGUILayout.EndHorizontal();

            GUI.enabled = true;

            if (isConverting && GUILayout.Button("Cancel Conversion"))
            {
                CancelConversion();
            }

            EditorGUILayout.EndVertical();
        }

        private void DrawConversionProgress()
        {
            if (isConverting)
            {
                EditorGUILayout.BeginVertical("box");
                EditorGUILayout.LabelField("Conversion Progress", EditorStyles.boldLabel);

                EditorGUI.ProgressBar(GUILayoutUtility.GetRect(0, 20, GUILayout.ExpandWidth(true)),
                    conversionProgress, $"{(conversionProgress * 100):F1}%");

                EditorGUILayout.LabelField($"Current Step: {currentConversionStep}");

                EditorGUILayout.EndVertical();

                Repaint();
            }
        }

        private void DrawResults()
        {
            if (conversionResults.Count > 0)
            {
                EditorGUILayout.BeginVertical("box");
                EditorGUILayout.LabelField($"Conversion Results ({conversionResults.Count})", EditorStyles.boldLabel);

                int successful = conversionResults.Count(r => r.success);
                int failed = conversionResults.Count - successful;

                EditorGUILayout.LabelField($"Successful: {successful}, Failed: {failed}");

                if (GUILayout.Button("View Detailed Results"))
                {
                    ShowDetailedResults();
                }

                if (GUILayout.Button("Clear Results"))
                {
                    conversionResults.Clear();
                }

                EditorGUILayout.EndVertical();
            }
        }

        // Core functionality methods
        private void DiscoverKoreographerAssets()
        {
            discoveredAssets.Clear();
            assetsByType.Clear();

            try
            {
                EditorUtility.DisplayProgressBar("Discovering Assets", "Scanning project...", 0f);

                // Find all asset files
                string[] allAssetPaths = AssetDatabase.GetAllAssetPaths()
                    .Where(path => path.StartsWith("Assets/") && path.EndsWith(".asset"))
                    .ToArray();

                int totalAssets = allAssetPaths.Length;
                int processedAssets = 0;

                foreach (string assetPath in allAssetPaths)
                {
                    processedAssets++;
                    EditorUtility.DisplayProgressBar("Discovering Assets",
                        $"Scanning {Path.GetFileName(assetPath)}",
                        (float)processedAssets / totalAssets);

                    var assetInfo = AnalyzeAsset(assetPath);
                    if (assetInfo != null)
                    {
                        discoveredAssets.Add(assetInfo);

                        if (!assetsByType.ContainsKey(assetInfo.type))
                            assetsByType[assetInfo.type] = new List<KoreographerAssetInfo>();

                        assetsByType[assetInfo.type].Add(assetInfo);
                    }
                }

                discoveryComplete = true;
                Debug.Log($"[Cadance Conversion] Discovered {discoveredAssets.Count} Koreographer assets");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[Cadance Conversion] Error during asset discovery: {ex.Message}");
            }
            finally
            {
                EditorUtility.ClearProgressBar();
            }
        }

        private KoreographerAssetInfo AnalyzeAsset(string assetPath)
        {
            try
            {
                var asset = AssetDatabase.LoadAssetAtPath<Object>(assetPath);
                if (asset == null) return null;

                string typeName = asset.GetType().FullName;

                // Check if it's a Koreographer asset
                if (IsKoreographerAsset(typeName))
                {
                    var assetInfo = new KoreographerAssetInfo
                    {
                        name = asset.name,
                        type = GetFriendlyTypeName(typeName),
                        path = assetPath,
                        guid = AssetDatabase.AssetPathToGUID(assetPath),
                        asset = asset,
                        selected = true
                    };

                    // Analyze dependencies
                    AnalyzeDependencies(assetInfo);

                    return assetInfo;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"[Cadance Conversion] Failed to analyze asset {assetPath}: {ex.Message}");
            }

            return null;
        }

        private bool IsKoreographerAsset(string typeName)
        {
            return typeName.Contains("SonicBloom.Koreo.Koreography") ||
                   typeName.Contains("SonicBloom.Koreo.KoreographyTrack") ||
                   typeName.Contains("FMODKoreographySet") ||
                   typeName.StartsWith("SonicBloom.Koreo.");
        }

        private string GetFriendlyTypeName(string typeName)
        {
            if (typeName.Contains("FMODKoreographySet")) return "FMODKoreographySet";
            if (typeName.Contains("Koreography") && !typeName.Contains("Track")) return "Koreography";
            if (typeName.Contains("KoreographyTrack")) return "KoreographyTrack";
            return typeName.Split('.').Last();
        }

        private void AnalyzeDependencies(KoreographerAssetInfo assetInfo)
        {
            try
            {
                string[] dependencies = AssetDatabase.GetDependencies(assetInfo.path, false);
                foreach (string dep in dependencies)
                {
                    if (dep != assetInfo.path && dep.EndsWith(".asset"))
                    {
                        var depAsset = AssetDatabase.LoadAssetAtPath<Object>(dep);
                        if (depAsset != null && IsKoreographerAsset(depAsset.GetType().FullName))
                        {
                            assetInfo.dependencies.Add(dep);
                        }
                    }
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"[Cadance Conversion] Failed to analyze dependencies for {assetInfo.path}: {ex.Message}");
            }
        }

        private void StartConversion()
        {
            if (isConverting) return;

            var selectedAssets = discoveredAssets.Where(a => a.selected).ToList();
            if (selectedAssets.Count == 0)
            {
                EditorUtility.DisplayDialog("No Assets Selected",
                    "Please select at least one asset to convert.", "OK");
                return;
            }

            isConverting = true;
            conversionProgress = 0f;
            conversionResults.Clear();

            // Ensure output directory exists
            if (!Directory.Exists(outputDirectory))
            {
                Directory.CreateDirectory(outputDirectory);
                AssetDatabase.Refresh();
            }

            // Start conversion process
            EditorApplication.update += UpdateConversion;

            Debug.Log($"[Cadance Conversion] Starting conversion of {selectedAssets.Count} assets");
        }

        private void CancelConversion()
        {
            if (isConverting)
            {
                isConverting = false;
                EditorApplication.update -= UpdateConversion;
                EditorUtility.ClearProgressBar();
                Debug.Log("[Cadance Conversion] Conversion cancelled by user");
            }
        }

        private int currentConversionIndex = 0;
        private List<KoreographerAssetInfo> assetsToConvert = new List<KoreographerAssetInfo>();

        private void UpdateConversion()
        {
            if (!isConverting) return;

            if (currentConversionIndex == 0)
            {
                // Initialize conversion
                assetsToConvert = discoveredAssets.Where(a => a.selected).ToList();
                currentConversionIndex = 0;
            }

            if (currentConversionIndex >= assetsToConvert.Count)
            {
                // Conversion complete
                CompleteConversion();
                return;
            }

            // Convert current asset
            var assetInfo = assetsToConvert[currentConversionIndex];
            currentConversionStep = $"Converting {assetInfo.name} ({assetInfo.type})";
            conversionProgress = (float)currentConversionIndex / assetsToConvert.Count;

            var result = ConvertAsset(assetInfo);
            conversionResults.Add(result);

            currentConversionIndex++;
        }

        private void CompleteConversion()
        {
            isConverting = false;
            EditorApplication.update -= UpdateConversion;
            EditorUtility.ClearProgressBar();

            int successful = conversionResults.Count(r => r.success);
            int failed = conversionResults.Count - successful;

            string message = $"Conversion complete!\n\nSuccessful: {successful}\nFailed: {failed}";

            if (generateReport)
            {
                GenerateConversionReport();
                message += "\n\nDetailed report generated.";
            }

            EditorUtility.DisplayDialog("Conversion Complete", message, "OK");

            // Reset state
            currentConversionIndex = 0;
            assetsToConvert.Clear();

            AssetDatabase.Refresh();
            Debug.Log($"[Cadance Conversion] Conversion complete. {successful} successful, {failed} failed.");
        }

        private ConversionResult ConvertAsset(KoreographerAssetInfo assetInfo)
        {
            var result = new ConversionResult
            {
                originalPath = assetInfo.path,
                assetType = assetInfo.type,
                conversionTime = System.DateTime.Now
            };

            try
            {
                switch (assetInfo.type)
                {
                    case "FMODKoreographySet":
                        if (convertFMODKoreographySets)
                            result = ConvertFMODKoreographySet(assetInfo);
                        break;

                    case "Koreography":
                        if (convertIndividualKoreographies)
                            result = ConvertKoreography(assetInfo);
                        break;

                    case "KoreographyTrack":
                        if (convertKoreographyTracks)
                            result = ConvertKoreographyTrack(assetInfo);
                        break;

                    default:
                        result.success = false;
                        result.errorMessage = $"Unknown asset type: {assetInfo.type}";
                        break;
                }
            }
            catch (System.Exception ex)
            {
                result.success = false;
                result.errorMessage = ex.Message;
                Debug.LogError($"[Cadance Conversion] Failed to convert {assetInfo.path}: {ex.Message}");
            }

            return result;
        }

        private ConversionResult ConvertFMODKoreographySet(KoreographerAssetInfo assetInfo)
        {
            var result = new ConversionResult
            {
                originalPath = assetInfo.path,
                assetType = assetInfo.type,
                conversionTime = System.DateTime.Now
            };

            try
            {
                var fmodSet = assetInfo.asset as FMODKoreographySet;
                if (fmodSet == null)
                {
                    result.success = false;
                    result.errorMessage = "Failed to load FMODKoreographySet";
                    return result;
                }

                // Create CadanceSet equivalent
                var cadanceSet = ScriptableObject.CreateInstance<CadanceSet>();
                cadanceSet.name = fmodSet.name + "_CadanceSet";

                // Find FMOD event associations for this set
                var fmodEventAssociations = FindFMODEventAssociationsForSet(fmodSet);

                // Convert each Koreography in the set
                foreach (var koreoEntry in fmodSet.koreographies)
                {
                    if (koreoEntry.koreo != null)
                    {
                        var convertedCadance = ConvertKoreographyToCadanceAsset(koreoEntry.koreo);
                        if (convertedCadance != null)
                        {
                            // Try to find FMOD event reference for this specific Koreography
                            FMODUnity.EventReference fmodEvent = FindFMODEventForKoreography(koreoEntry.koreo, fmodEventAssociations);

                            if (!fmodEvent.IsNull)
                            {
                                // Add with FMOD integration
                                cadanceSet.AddCadance(convertedCadance, fmodEvent, koreoEntry.koreo.SourceClipName);
                                Debug.Log($"[Conversion] Associated FMOD event '{fmodEvent.Path}' with CadanceAsset '{convertedCadance.name}'");
                            }
                            else
                            {
                                // Add without FMOD integration
                                cadanceSet.AddCadance(convertedCadance, koreoEntry.koreo.SourceClipName);
                                Debug.LogWarning($"[Conversion] No FMOD event found for CadanceAsset '{convertedCadance.name}'");
                            }
                        }
                    }
                }

                // Save the CadanceSet
                string outputPath = GetOutputPath(assetInfo, "CadanceSet");
                AssetDatabase.CreateAsset(cadanceSet, outputPath);

                // Add all CadanceAssets and their tracks as sub-assets
                foreach (var cadanceEntry in cadanceSet.Cadances)
                {
                    if (cadanceEntry.cadance != null)
                    {
                        AssetDatabase.AddObjectToAsset(cadanceEntry.cadance, cadanceSet);

                        // Add all tracks as sub-assets to the CadanceAsset
                        foreach (var track in cadanceEntry.cadance.Tracks)
                        {
                            if (track != null)
                            {
                                AssetDatabase.AddObjectToAsset(track, cadanceEntry.cadance);
                            }
                        }
                    }
                }

                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();

                result.success = true;
                result.convertedPath = outputPath;

                Debug.Log($"[Cadance Conversion] Converted FMODKoreographySet: {assetInfo.path} -> {outputPath}");
            }
            catch (System.Exception ex)
            {
                result.success = false;
                result.errorMessage = ex.Message;
            }

            return result;
        }

        private ConversionResult ConvertKoreography(KoreographerAssetInfo assetInfo)
        {
            var result = new ConversionResult
            {
                originalPath = assetInfo.path,
                assetType = assetInfo.type,
                conversionTime = System.DateTime.Now
            };

            try
            {
                var koreography = assetInfo.asset as Koreography;
                if (koreography == null)
                {
                    result.success = false;
                    result.errorMessage = "Failed to load Koreography";
                    return result;
                }

                var cadanceAsset = ConvertKoreographyToCadanceAsset(koreography);
                if (cadanceAsset != null)
                {
                    string outputPath = GetOutputPath(assetInfo, "CadanceAsset");
                    AssetDatabase.CreateAsset(cadanceAsset, outputPath);

                    // Add all tracks as sub-assets to ensure they're properly saved
                    foreach (var track in cadanceAsset.Tracks)
                    {
                        if (track != null)
                        {
                            AssetDatabase.AddObjectToAsset(track, cadanceAsset);
                        }
                    }

                    AssetDatabase.SaveAssets();
                    AssetDatabase.Refresh();

                    result.success = true;
                    result.convertedPath = outputPath;

                    Debug.Log($"[Cadance Conversion] Converted Koreography: {assetInfo.path} -> {outputPath} with {cadanceAsset.Tracks.Count} tracks");
                }
                else
                {
                    result.success = false;
                    result.errorMessage = "Failed to convert Koreography to CadanceAsset";
                }
            }
            catch (System.Exception ex)
            {
                result.success = false;
                result.errorMessage = ex.Message;
            }

            return result;
        }

        private ConversionResult ConvertKoreographyTrack(KoreographerAssetInfo assetInfo)
        {
            var result = new ConversionResult
            {
                originalPath = assetInfo.path,
                assetType = assetInfo.type,
                conversionTime = System.DateTime.Now
            };

            try
            {
                var koreographyTrack = assetInfo.asset as KoreographyTrackBase;
                if (koreographyTrack == null)
                {
                    result.success = false;
                    result.errorMessage = "Failed to load KoreographyTrack";
                    return result;
                }

                var cadanceTrack = ConvertKoreographyTrackToCadanceTrack(koreographyTrack);
                if (cadanceTrack != null)
                {
                    string outputPath = GetOutputPath(assetInfo, "CadanceTrack");
                    AssetDatabase.CreateAsset(cadanceTrack, outputPath);

                    result.success = true;
                    result.convertedPath = outputPath;

                    Debug.Log($"[Cadance Conversion] Converted KoreographyTrack: {assetInfo.path} -> {outputPath}");
                }
                else
                {
                    result.success = false;
                    result.errorMessage = "Failed to convert KoreographyTrack to CadanceTrack";
                }
            }
            catch (System.Exception ex)
            {
                result.success = false;
                result.errorMessage = ex.Message;
            }

            return result;
        }

        private string GetOutputPath(KoreographerAssetInfo assetInfo, string newType)
        {
            string directory = outputDirectory;

            if (organizeByType)
            {
                directory = Path.Combine(outputDirectory, newType + "s");
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }
            }

            string fileName = assetInfo.name + "_" + newType + ".asset";
            string outputPath = Path.Combine(directory, fileName).Replace('\\', '/');

            // Ensure unique filename
            int counter = 1;
            while (File.Exists(outputPath))
            {
                fileName = assetInfo.name + "_" + newType + "_" + counter + ".asset";
                outputPath = Path.Combine(directory, fileName).Replace('\\', '/');
                counter++;
            }

            return outputPath;
        }

        private void ShowAssetTypeSelectionMenu()
        {
            GenericMenu menu = new GenericMenu();

            foreach (var kvp in assetsByType)
            {
                string assetType = kvp.Key;
                menu.AddItem(new GUIContent($"Select All {assetType}"), false, () =>
                {
                    foreach (var asset in kvp.Value)
                        asset.selected = true;
                });

                menu.AddItem(new GUIContent($"Deselect All {assetType}"), false, () =>
                {
                    foreach (var asset in kvp.Value)
                        asset.selected = false;
                });
            }

            menu.ShowAsContext();
        }

        private void ShowDetailedResults()
        {
            var window = GetWindow<ConversionResultsWindow>("Conversion Results");
            window.SetResults(conversionResults);
        }

        private void GenerateConversionReport()
        {
            try
            {
                string reportPath = Path.Combine(outputDirectory, "ConversionReport.txt");
                using (var writer = new StreamWriter(reportPath))
                {
                    writer.WriteLine("Cadance Asset Conversion Report");
                    writer.WriteLine($"Generated: {System.DateTime.Now}");
                    writer.WriteLine($"Total Assets Processed: {conversionResults.Count}");
                    writer.WriteLine($"Successful Conversions: {conversionResults.Count(r => r.success)}");
                    writer.WriteLine($"Failed Conversions: {conversionResults.Count(r => !r.success)}");
                    writer.WriteLine();

                    writer.WriteLine("Detailed Results:");
                    writer.WriteLine("================");

                    foreach (var result in conversionResults)
                    {
                        writer.WriteLine($"Original: {result.originalPath}");
                        writer.WriteLine($"Type: {result.assetType}");
                        writer.WriteLine($"Status: {(result.success ? "SUCCESS" : "FAILED")}");

                        if (result.success)
                        {
                            writer.WriteLine($"Converted To: {result.convertedPath}");
                        }
                        else
                        {
                            writer.WriteLine($"Error: {result.errorMessage}");
                        }

                        writer.WriteLine($"Time: {result.conversionTime}");
                        writer.WriteLine();
                    }
                }

                Debug.Log($"[Cadance Conversion] Report generated: {reportPath}");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[Cadance Conversion] Failed to generate report: {ex.Message}");
            }
        }

        // Conversion helper methods using existing Cadance conversion logic
        private CadanceAsset ConvertKoreographyToCadanceAsset(Koreography koreography)
        {
            // Use the existing conversion method from Cadance.cs
            var cadanceAsset = Cadance.Instance.ConvertKoreographyToCadance(koreography);

            if (cadanceAsset != null)
            {
                // Ensure proper naming for the asset
                cadanceAsset.name = $"{koreography.name}_CadanceAsset";

                // Try to discover and assign AudioClip reference
                TryAssignAudioClip(cadanceAsset);

                // Log conversion details for debugging
                Debug.Log($"[Conversion] Created CadanceAsset '{cadanceAsset.name}' with {cadanceAsset.Tracks.Count} tracks");

                // Validate tracks
                int validTracks = 0;
                foreach (var track in cadanceAsset.Tracks)
                {
                    if (track != null)
                    {
                        validTracks++;
                        Debug.Log($"[Conversion] Track '{track.EventID}' with {track.EventCount} events");
                    }
                    else
                    {
                        Debug.LogWarning($"[Conversion] Found null track in CadanceAsset '{cadanceAsset.name}'");
                    }
                }

                Debug.Log($"[Conversion] CadanceAsset '{cadanceAsset.name}' has {validTracks} valid tracks out of {cadanceAsset.Tracks.Count} total");

                if (cadanceAsset.SourceClip != null)
                {
                    Debug.Log($"[Conversion] AudioClip assigned: {cadanceAsset.SourceClip.name}");
                }
                else
                {
                    Debug.LogWarning($"[Conversion] No AudioClip found for source: {cadanceAsset.SourceClipName}");
                }
            }

            return cadanceAsset;
        }

        /// <summary>
        /// Attempts to find and assign a matching AudioClip to the CadanceAsset based on its SourceClipName.
        /// </summary>
        private void TryAssignAudioClip(CadanceAsset cadanceAsset)
        {
            if (cadanceAsset == null || string.IsNullOrEmpty(cadanceAsset.SourceClipName))
                return;

            // Search for AudioClip by exact name match
            string[] audioClipGuids = AssetDatabase.FindAssets($"{cadanceAsset.SourceClipName} t:AudioClip");

            if (audioClipGuids.Length > 0)
            {
                string path = AssetDatabase.GUIDToAssetPath(audioClipGuids[0]);
                var audioClip = AssetDatabase.LoadAssetAtPath<AudioClip>(path);

                if (audioClip != null)
                {
                    cadanceAsset.SourceClip = audioClip;
                    Debug.Log($"[Conversion] Found exact AudioClip match: {audioClip.name} for {cadanceAsset.SourceClipName}");
                    return;
                }
            }

            // If no exact match, try partial matching
            audioClipGuids = AssetDatabase.FindAssets("t:AudioClip");
            foreach (string guid in audioClipGuids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                var audioClip = AssetDatabase.LoadAssetAtPath<AudioClip>(path);

                if (audioClip != null)
                {
                    string clipName = System.IO.Path.GetFileNameWithoutExtension(audioClip.name);

                    // Check if the clip name contains the source clip name or vice versa
                    if (clipName.Contains(cadanceAsset.SourceClipName) || cadanceAsset.SourceClipName.Contains(clipName))
                    {
                        cadanceAsset.SourceClip = audioClip;
                        Debug.Log($"[Conversion] Found partial AudioClip match: {audioClip.name} for {cadanceAsset.SourceClipName}");
                        return;
                    }
                }
            }

            Debug.LogWarning($"[Conversion] No AudioClip found for source clip name: {cadanceAsset.SourceClipName}");
        }

        private CadanceTrackBase ConvertKoreographyTrackToCadanceTrack(KoreographyTrackBase koreographyTrack)
        {
            // Use the existing conversion method from Cadance.cs
            return Cadance.Instance.ConvertKoreographyTrackToCadanceTrack(koreographyTrack, koreographyTrack.EventID);
        }

        /// <summary>
        /// Finds FMOD event associations for a given FMODKoreographySet by scanning all FMODEventDescriptionVisor components in the project.
        /// </summary>
        private Dictionary<FMODKoreographySet, FMODUnity.EventReference> FindFMODEventAssociationsForSet(FMODKoreographySet targetSet)
        {
            var associations = new Dictionary<FMODKoreographySet, FMODUnity.EventReference>();

            try
            {
                // Method 1: Find all FMODEventDescriptionVisor components in scenes and prefabs
                FindEventAssociationsInScenes(targetSet, associations);

                // Method 2: Find all FMODEventDescriptionVisor components in prefabs
                FindEventAssociationsInPrefabs(targetSet, associations);

                // Method 3: Try to find associations based on naming conventions
                if (associations.Count == 0)
                {
                    FindEventAssociationsByNaming(targetSet, associations);
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"[Conversion] Error finding FMOD event associations: {ex.Message}");
            }

            return associations;
        }

        /// <summary>
        /// Finds FMOD event associations by scanning scene files for FMODEventDescriptionVisor components.
        /// </summary>
        private void FindEventAssociationsInScenes(FMODKoreographySet targetSet, Dictionary<FMODKoreographySet, FMODUnity.EventReference> associations)
        {
            try
            {
                // Find all scene files
                string[] sceneGuids = AssetDatabase.FindAssets("t:Scene");

                foreach (string sceneGuid in sceneGuids)
                {
                    string scenePath = AssetDatabase.GUIDToAssetPath(sceneGuid);

                    // Load scene content as text to search for references
                    string sceneContent = System.IO.File.ReadAllText(scenePath);

                    // Check if this scene references our target set
                    if (sceneContent.Contains(targetSet.name) || sceneContent.Contains(AssetDatabase.AssetPathToGUID(AssetDatabase.GetAssetPath(targetSet))))
                    {
                        Debug.Log($"[Conversion] Found potential reference to '{targetSet.name}' in scene: {scenePath}");

                        // Try to extract FMOD event information from the scene
                        ExtractFMODEventFromSceneContent(sceneContent, targetSet, associations);
                    }
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"[Conversion] Error scanning scenes for FMOD event associations: {ex.Message}");
            }
        }

        /// <summary>
        /// Finds FMOD event associations by scanning prefab files for FMODEventDescriptionVisor components.
        /// </summary>
        private void FindEventAssociationsInPrefabs(FMODKoreographySet targetSet, Dictionary<FMODKoreographySet, FMODUnity.EventReference> associations)
        {
            try
            {
                // Find all prefab files
                string[] prefabGuids = AssetDatabase.FindAssets("t:Prefab");

                foreach (string prefabGuid in prefabGuids)
                {
                    string prefabPath = AssetDatabase.GUIDToAssetPath(prefabGuid);

                    // Load prefab content as text to search for references
                    string prefabContent = System.IO.File.ReadAllText(prefabPath);

                    // Check if this prefab references our target set
                    if (prefabContent.Contains(targetSet.name) || prefabContent.Contains(AssetDatabase.AssetPathToGUID(AssetDatabase.GetAssetPath(targetSet))))
                    {
                        Debug.Log($"[Conversion] Found potential reference to '{targetSet.name}' in prefab: {prefabPath}");

                        // Try to extract FMOD event information from the prefab
                        ExtractFMODEventFromSceneContent(prefabContent, targetSet, associations);
                    }
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"[Conversion] Error scanning prefabs for FMOD event associations: {ex.Message}");
            }
        }

        /// <summary>
        /// Extracts FMOD event information from scene or prefab content.
        /// </summary>
        private void ExtractFMODEventFromSceneContent(string content, FMODKoreographySet targetSet, Dictionary<FMODKoreographySet, FMODUnity.EventReference> associations)
        {
            try
            {
                // Look for FMOD event paths in the content
                // FMOD events typically appear as "Path: event:/..." in YAML
                var lines = content.Split('\n');
                string currentEventPath = null;
                bool foundTargetSetReference = false;

                for (int i = 0; i < lines.Length; i++)
                {
                    string line = lines[i].Trim();

                    // Look for FMOD event path
                    if (line.StartsWith("Path: event:/"))
                    {
                        currentEventPath = line.Substring("Path: ".Length).Trim();
                    }

                    // Look for reference to our target set
                    if (line.Contains(targetSet.name) || line.Contains(AssetDatabase.AssetPathToGUID(AssetDatabase.GetAssetPath(targetSet))))
                    {
                        foundTargetSetReference = true;

                        // If we found both an event path and a reference to our set, create the association
                        if (!string.IsNullOrEmpty(currentEventPath))
                        {
                            var eventRef = CreateProperEventReference(currentEventPath);
                            if (!eventRef.IsNull)
                            {
                                associations[targetSet] = eventRef;
                                Debug.Log($"[Conversion] Found FMOD event association: '{currentEventPath}' -> '{targetSet.name}'");
                                return;
                            }
                        }
                    }
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"[Conversion] Error extracting FMOD event from content: {ex.Message}");
            }
        }

        /// <summary>
        /// Finds FMOD event associations based on naming conventions.
        /// </summary>
        private void FindEventAssociationsByNaming(FMODKoreographySet targetSet, Dictionary<FMODKoreographySet, FMODUnity.EventReference> associations)
        {
            try
            {
                // Try to find FMOD events that match the set name or contained Koreography names
                string[] eventGuids = AssetDatabase.FindAssets("t:FMODEvent");

                foreach (string eventGuid in eventGuids)
                {
                    string eventPath = AssetDatabase.GUIDToAssetPath(eventGuid);
                    string eventName = System.IO.Path.GetFileNameWithoutExtension(eventPath);

                    // Check if event name matches set name or any Koreography in the set
                    if (DoesEventMatchSet(eventName, targetSet))
                    {
                        // Create proper event reference with GUID lookup
                        var eventRef = CreateProperEventReference($"event:/{eventName}");
                        if (!eventRef.IsNull)
                        {
                            associations[targetSet] = eventRef;
                            Debug.Log($"[Conversion] Found FMOD event by naming convention: '{eventRef.Path}' -> '{targetSet.name}'");
                            return;
                        }
                    }
                }

                Debug.LogWarning($"[Conversion] No FMOD event associations found for '{targetSet.name}' - CadanceEntry will have empty FMOD event references");
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"[Conversion] Error finding FMOD event associations by naming: {ex.Message}");
            }
        }

        /// <summary>
        /// Checks if an FMOD event name matches a FMODKoreographySet based on naming conventions.
        /// </summary>
        private bool DoesEventMatchSet(string eventName, FMODKoreographySet targetSet)
        {
            // Check if event name contains set name
            if (eventName.ToLower().Contains(targetSet.name.ToLower()))
                return true;

            // Check if event name matches any Koreography source clip name in the set
            foreach (var entry in targetSet.koreographies)
            {
                if (entry.koreo != null && !string.IsNullOrEmpty(entry.koreo.SourceClipName))
                {
                    if (eventName.ToLower().Contains(entry.koreo.SourceClipName.ToLower()) ||
                        entry.koreo.SourceClipName.ToLower().Contains(eventName.ToLower()))
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        /// <summary>
        /// Creates a proper FMOD EventReference with both GUID and Path populated.
        /// </summary>
        private FMODUnity.EventReference CreateProperEventReference(string eventPath)
        {
            try
            {
                // Method 1: Use EventManager.EventFromPath (Editor only)
#if UNITY_EDITOR
                var editorEventRef = FMODUnity.EventManager.EventFromPath(eventPath);
                if (editorEventRef != null)
                {
                    return new FMODUnity.EventReference
                    {
                        Path = editorEventRef.Path,
                        Guid = editorEventRef.Guid
                    };
                }
#endif

                // Method 2: Use EventReference.Find (requires GuidLookupDelegate)
                try
                {
                    if (FMODUnity.EventReference.GuidLookupDelegate != null)
                    {
                        return FMODUnity.EventReference.Find(eventPath);
                    }
                }
                catch (System.Exception ex)
                {
                    Debug.LogWarning($"[Conversion] EventReference.Find failed for '{eventPath}': {ex.Message}");
                }

                // Method 3: Use RuntimeManager.PathToEventReference (fallback)
                try
                {
                    return FMODUnity.RuntimeManager.PathToEventReference(eventPath);
                }
                catch (System.Exception ex)
                {
                    Debug.LogWarning($"[Conversion] RuntimeManager.PathToEventReference failed for '{eventPath}': {ex.Message}");
                }

                Debug.LogWarning($"[Conversion] Could not create proper EventReference for path: '{eventPath}' - all methods failed");
                return new FMODUnity.EventReference(); // Return empty/null reference
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[Conversion] Error creating EventReference for '{eventPath}': {ex.Message}");
                return new FMODUnity.EventReference();
            }
        }

        /// <summary>
        /// Finds the FMOD event reference for a specific Koreography based on the associations found.
        /// </summary>
        private FMODUnity.EventReference FindFMODEventForKoreography(Koreography koreography, Dictionary<FMODKoreographySet, FMODUnity.EventReference> associations)
        {
            // For now, return the first association found since FMODKoreographySet typically has one FMOD event per set
            // In the future, this could be enhanced to match specific Koreography files to specific events
            foreach (var kvp in associations)
            {
                var koreographySet = kvp.Key;
                var eventRef = kvp.Value;

                // Check if this Koreography is part of the set
                foreach (var entry in koreographySet.koreographies)
                {
                    if (entry.koreo == koreography)
                    {
                        return eventRef;
                    }
                }
            }

            return new FMODUnity.EventReference(); // Return null/empty EventReference
        }
    }

    // Supporting data structures
    [System.Serializable]
    public class KoreographerAssetInfo
    {
        public string name;
        public string type;
        public string path;
        public string guid;
        public bool selected = true;
        public Object asset;
        public List<string> dependencies = new List<string>();
    }

    [System.Serializable]
    public class ConversionResult
    {
        public string originalPath;
        public string convertedPath;
        public string assetType;
        public bool success;
        public string errorMessage;
        public System.DateTime conversionTime;
    }
}
