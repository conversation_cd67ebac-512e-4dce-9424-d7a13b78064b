%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!43 &4300000
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: demo_fish_pupil_l
  serializedVersion: 10
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 240
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 84
    localAABB:
      m_Center: {x: 0.13652396, y: 0.21268936, z: 0.7825612}
      m_Extent: {x: 0.3014574, y: 0.3024394, z: 0.049409598}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose:
  - e00: 1
    e01: 0
    e02: -0
    e03: 0
    e10: 0
    e11: 1
    e12: 0
    e13: 0
    e20: 0
    e21: -0
    e22: 1
    e23: -0
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: 1
    e01: 0
    e02: -0
    e03: -0.011112675
    e10: -0
    e11: 1
    e12: 0
    e13: -0.0011642873
    e20: 0
    e21: 0
    e22: 1
    e23: 0
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: -1.0000002
    e01: 0.00002937313
    e02: 0
    e03: -0.64736646
    e10: 0
    e11: -0.00000011920929
    e12: 1.0000001
    e13: 4.1119193e-16
    e20: 0.00002937313
    e21: 1.0000001
    e22: -0.00000011920929
    e23: 0.00009876619
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: -1.0000002
    e01: -0.00014712971
    e02: 0
    e03: -1.354551
    e10: 0
    e11: -0.00000011920929
    e12: 1.0000001
    e13: 4.976892e-16
    e20: -0.00014712971
    e21: 1.0000001
    e22: -0.00000011920929
    e23: -0.00014031588
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: -1
    e01: -0.00048060168
    e02: 2.910383e-11
    e03: -2.1633656
    e10: -2.910383e-11
    e11: 0
    e12: 1
    e13: 3.9862525e-16
    e20: -0.00048060168
    e21: 0.99999994
    e22: -0.00000011920929
    e23: -0.00086173764
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: -0.9999015
    e01: -0.014035968
    e02: 0
    e03: -2.9928126
    e10: 0
    e11: 0
    e12: 1
    e13: 4.025491e-16
    e20: -0.014035968
    e21: 0.9999015
    e22: 0
    e23: -0.041434355
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: -0.9997916
    e01: 0.020430014
    e02: 0
    e03: -3.8419251
    e10: 0
    e11: -0.00000011920929
    e12: 1.0000001
    e13: 7.983489e-16
    e20: 0.020430014
    e21: 0.99979144
    e22: -0.00000011920929
    e23: 0.7152465
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: -0.9997916
    e01: 0.020430014
    e02: 0
    e03: -4.2017236
    e10: 0
    e11: -0.00000011920929
    e12: 1.0000001
    e13: 7.474209e-16
    e20: 0.020430014
    e21: 0.99979144
    e22: -0.00000011920929
    e23: 0.7152465
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: -0.99992675
    e01: -0.012107229
    e02: 0
    e03: -3.8915958
    e10: 0
    e11: 0
    e12: 1
    e13: 2.9879386e-16
    e20: -0.012107229
    e21: 0.9999267
    e22: 0
    e23: -0.033927903
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: -0.99992675
    e01: -0.012107229
    e02: 0
    e03: -4.279387
    e10: 0
    e11: 0
    e12: 1
    e13: 2.5510348e-16
    e20: -0.012107229
    e21: 0.9999267
    e22: 0
    e23: -0.033927903
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: -0.99921274
    e01: -0.039670877
    e02: 0
    e03: -3.8479002
    e10: 0
    e11: 0
    e12: 1
    e13: -1.8828514e-16
    e20: -0.039670877
    e21: 0.99921274
    e22: 0
    e23: -0.78897315
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: -0.99921274
    e01: -0.039670877
    e02: 0
    e03: -4.2241964
    e10: 0
    e11: 0
    e12: 1
    e13: -2.214354e-16
    e20: -0.039670877
    e21: 0.99921274
    e22: 0
    e23: -0.78897315
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: 0.00000017881393
    e01: -6.617445e-24
    e02: -0.99999994
    e03: -0.72993296
    e10: -1.2246469e-16
    e11: -1
    e12: -1.9852335e-23
    e13: 0.21268936
    e20: -0.99999994
    e21: 1.2246469e-16
    e22: -0.00000011920929
    e23: 0.13652386
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: 0.00000017881393
    e01: -6.617445e-24
    e02: -0.99999994
    e03: -0.78256124
    e10: -1.2246469e-16
    e11: -1
    e12: -1.9852335e-23
    e13: 0.21268936
    e20: -0.99999994
    e21: 1.2246469e-16
    e22: -0.00000011920929
    e23: 0.13652386
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: -0.00000011920929
    e01: -0
    e02: 1.0000001
    e03: -0.729933
    e10: 0
    e11: 1
    e12: 0
    e13: -0.212689
    e20: -1.0000001
    e21: 0
    e22: -0.00000011920929
    e23: 0.136524
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: 0.00000017881393
    e01: 8.881784e-16
    e02: -0.99999994
    e03: 0.782561
    e10: -0.000000021073422
    e11: 1
    e12: -2.6645353e-15
    e13: -0.212689
    e20: 0.99999994
    e21: 0.000000021073422
    e22: 0.00000017881393
    e23: -0.13652411
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB:
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: -0.049409747, y: -0.30243903, z: -0.3014574}
    m_Max: {x: 0.04940945, y: 0.30243975, z: 0.30145738}
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 020001000000000001000300030001000400040001000500050001000600060001000700070001000800080001000900090001000a000a0001000b000b0001000c000c0001000d000d0001000e000e0001000f000f00010010001000010011001100010012001200010013001300010014001400010002001700160015001800160017001900160018001a00160019001b0016001a001c0016001b001d0016001c001e0016001d001f0016001e00200016001f002100160020002200160021002300160022002400160023002500160024002600160025002700160026002800160027002900160028001500160029002c002b002a002d002c002a002e002c002d002f002e002d0030002e002f00310030002f003200300031003300320031003400320033003500340033003600340035003700360035003800360037003900380037003a00380039003b003a0039003c003a003b003d003c003b003e003c003d003f003e003d0040003e003f00410040003f004200400041004300420041004400420043004500440043004600440045004700460045004800460047004900480047004a00480049004b004a0049004c004a004b004d004c004b004e004c004d004f004e004d0050004e004f00510050004f00520050005100530052005100
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 84
    m_Channels:
    - stream: 0
      offset: 0
      format: 0
      dimension: 3
    - stream: 0
      offset: 12
      format: 0
      dimension: 3
    - stream: 0
      offset: 24
      format: 0
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 1
      offset: 0
      format: 0
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 2
      offset: 0
      format: 0
      dimension: 4
    - stream: 2
      offset: 16
      format: 10
      dimension: 4
    m_DataSize: 6720
    _typelessdata: 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
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0.13652396, y: 0.21268936, z: 0.7825612}
    m_Extent: {x: 0.3014574, y: 0.3024394, z: 0.049409598}
  m_MeshUsageFlags: 0
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  m_MeshMetrics[0]: 1
  m_MeshMetrics[1]: 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
