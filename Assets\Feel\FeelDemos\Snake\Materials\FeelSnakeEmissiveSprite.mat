%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 6
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: FeelSnakeEmissiveSprite
  m_Shader: {fileID: 10753, guid: 0000000000000000f000000000000000, type: 0}
  m_ShaderKeywords: ETC1_EXTERNAL_ALPHA
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 1
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _AlphaTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TextureSample0:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _texcoord:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Floats:
    - PixelSnap: 0
    - _BlendDst: 10
    - _BlendSrc: 1
    - _BumpScale: 1
    - _ColorMask: 255
    - _CullMode: 0
    - _Cutoff: 0.5
    - _DetailNormalMapScale: 1
    - _DstBlend: 0
    - _EmissionForce: 2
    - _EmissionFresnelBias: 1
    - _EmissionFresnelPower: 1
    - _EmissionFresnelScale: 1
    - _EmissionIntensity: 0.3
    - _EnableExternalAlpha: 0
    - _GlossMapScale: 1
    - _Glossiness: 0.5
    - _GlossyReflections: 1
    - _InvertOpacityFresnel: 0
    - _Metallic: 0
    - _Mode: 0
    - _OcclusionStrength: 1
    - _OcclussionStrength: 1
    - _Opacity: 1
    - _OpacityFresnelBias: 1
    - _OpacityFresnelPower: 1
    - _OpacityFresnelScale: 1
    - _Parallax: 0.02
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _Stencil: 0
    - _StencilComp: 0
    - _StencilOpFailFront: 0
    - _StencilOpPassFront: 0
    - _StencilOpZFailFront: 0
    - _StencilReadMask: 15
    - _StencilWriteMask: 15
    - _UVSec: 0
    - _UseEmissionFresnel: 0
    - _UseOpacityFresnel: 0
    - _ZTest: 167.5171
    - _ZWrite: 1
    - __dirty: 1
    m_Colors:
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _DiffuseColor: {r: 1, g: 1, b: 1, a: 1}
    - _EmissionColor: {r: 8, g: 8, b: 8, a: 1}
    - _Flip: {r: 1, g: 1, b: 1, a: 1}
    - _RendererColor: {r: 1, g: 1, b: 1, a: 1}
