%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!43 &4300000
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: demo_fish_eye_l
  serializedVersion: 10
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 240
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 84
    localAABB:
      m_Center: {x: 0.13652396, y: 0.21268936, z: 0.72993296}
      m_Extent: {x: 0.3433091, y: 0.34442866, z: 0.055618465}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose:
  - e00: 1
    e01: 0
    e02: -0
    e03: 0
    e10: 0
    e11: 1
    e12: 0
    e13: 0
    e20: 0
    e21: -0
    e22: 1
    e23: -0
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: 1
    e01: 0
    e02: -0
    e03: -0.011112675
    e10: -0
    e11: 1
    e12: 0
    e13: -0.0011642873
    e20: 0
    e21: 0
    e22: 1
    e23: 0
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: -1.0000002
    e01: 0.00002937313
    e02: 0
    e03: -0.64736646
    e10: 0
    e11: -0.00000011920929
    e12: 1.0000001
    e13: 4.1119193e-16
    e20: 0.00002937313
    e21: 1.0000001
    e22: -0.00000011920929
    e23: 0.00009876619
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: -1.0000002
    e01: -0.00014712971
    e02: 0
    e03: -1.354551
    e10: 0
    e11: -0.00000011920929
    e12: 1.0000001
    e13: 4.976892e-16
    e20: -0.00014712971
    e21: 1.0000001
    e22: -0.00000011920929
    e23: -0.00014031588
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: -1
    e01: -0.00048060168
    e02: 2.910383e-11
    e03: -2.1633656
    e10: -2.910383e-11
    e11: 0
    e12: 1
    e13: 3.9862525e-16
    e20: -0.00048060168
    e21: 0.99999994
    e22: -0.00000011920929
    e23: -0.00086173764
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: -0.9999015
    e01: -0.014035968
    e02: 0
    e03: -2.9928126
    e10: 0
    e11: 0
    e12: 1
    e13: 4.025491e-16
    e20: -0.014035968
    e21: 0.9999015
    e22: 0
    e23: -0.041434355
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: -0.9997916
    e01: 0.020430014
    e02: 0
    e03: -3.8419251
    e10: 0
    e11: -0.00000011920929
    e12: 1.0000001
    e13: 7.983489e-16
    e20: 0.020430014
    e21: 0.99979144
    e22: -0.00000011920929
    e23: 0.7152465
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: -0.9997916
    e01: 0.020430014
    e02: 0
    e03: -4.2017236
    e10: 0
    e11: -0.00000011920929
    e12: 1.0000001
    e13: 7.474209e-16
    e20: 0.020430014
    e21: 0.99979144
    e22: -0.00000011920929
    e23: 0.7152465
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: -0.99992675
    e01: -0.012107229
    e02: 0
    e03: -3.8915958
    e10: 0
    e11: 0
    e12: 1
    e13: 2.9879386e-16
    e20: -0.012107229
    e21: 0.9999267
    e22: 0
    e23: -0.033927903
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: -0.99992675
    e01: -0.012107229
    e02: 0
    e03: -4.279387
    e10: 0
    e11: 0
    e12: 1
    e13: 2.5510348e-16
    e20: -0.012107229
    e21: 0.9999267
    e22: 0
    e23: -0.033927903
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: -0.99921274
    e01: -0.039670877
    e02: 0
    e03: -3.8479002
    e10: 0
    e11: 0
    e12: 1
    e13: -1.8828514e-16
    e20: -0.039670877
    e21: 0.99921274
    e22: 0
    e23: -0.78897315
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: -0.99921274
    e01: -0.039670877
    e02: 0
    e03: -4.2241964
    e10: 0
    e11: 0
    e12: 1
    e13: -2.214354e-16
    e20: -0.039670877
    e21: 0.99921274
    e22: 0
    e23: -0.78897315
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: 0.00000017881393
    e01: -6.617445e-24
    e02: -0.99999994
    e03: -0.72993296
    e10: -1.2246469e-16
    e11: -1
    e12: -1.9852335e-23
    e13: 0.21268936
    e20: -0.99999994
    e21: 1.2246469e-16
    e22: -0.00000011920929
    e23: 0.13652386
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: 0.00000017881393
    e01: -6.617445e-24
    e02: -0.99999994
    e03: -0.78256124
    e10: -1.2246469e-16
    e11: -1
    e12: -1.9852335e-23
    e13: 0.21268936
    e20: -0.99999994
    e21: 1.2246469e-16
    e22: -0.00000011920929
    e23: 0.13652386
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: -0.00000011920929
    e01: -0
    e02: 1.0000001
    e03: -0.729933
    e10: 0
    e11: 1
    e12: 0
    e13: -0.212689
    e20: -1.0000001
    e21: 0
    e22: -0.00000011920929
    e23: 0.136524
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: 0.00000017881393
    e01: 8.881784e-16
    e02: -0.99999994
    e03: 0.782561
    e10: -0.000000021073422
    e11: 1
    e12: -2.6645353e-15
    e13: -0.212689
    e20: 0.99999994
    e21: 0.000000021073422
    e22: 0.00000017881393
    e23: -0.13652411
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB:
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: -0.055618465, y: -0.3444283, z: -0.34330922}
    m_Max: {x: 0.055618525, y: 0.34442902, z: 0.3433091}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 020001000000000001000300030001000400040001000500050001000600060001000700070001000800080001000900090001000a000a0001000b000b0001000c000c0001000d000d0001000e000e0001000f000f00010010001000010011001100010012001200010013001300010014001400010002001700160015001800160017001900160018001a00160019001b0016001a001c0016001b001d0016001c001e0016001d001f0016001e00200016001f002100160020002200160021002300160022002400160023002500160024002600160025002700160026002800160027002900160028001500160029002c002b002a002d002c002a002e002c002d002f002e002d0030002e002f00310030002f003200300031003300320031003400320033003500340033003600340035003700360035003800360037003900380037003a00380039003b003a0039003c003a003b003d003c003b003e003c003d003f003e003d0040003e003f00410040003f004200400041004300420041004400420043004500440043004600440045004700460045004800460047004900480047004a00480049004b004a0049004c004a004b004d004c004b004e004c004d004f004e004d0050004e004f00510050004f00520050005100530052005100
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 84
    m_Channels:
    - stream: 0
      offset: 0
      format: 0
      dimension: 3
    - stream: 0
      offset: 12
      format: 0
      dimension: 3
    - stream: 0
      offset: 24
      format: 0
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 1
      offset: 0
      format: 0
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 2
      offset: 0
      format: 0
      dimension: 4
    - stream: 2
      offset: 16
      format: 10
      dimension: 4
    m_DataSize: 6720
    _typelessdata: d01ad43ecdb6d23e43932f3f3432403534cea6bd44267fbf4f1d643fe69ae73e1b6917bd000080bff3cc0b3ec37a573e62c8333faeb82fb22ccea6bd44267fbf521d643fd99ae73eb96917bd000080bf5712ed3e66e0a13e1c92313f28a75b3431cea6bd43267fbf4a1d643ffb9ae73ea16917bd000080bfc537ad3ec478f93edbfd2d3f5c5f9ba91acea6bd44267fbf4f1d643fe59ae73eae6917bd000080bf626f783e872d093f91f92c3f483240b434cea6bd43267fbf511d643fdb9ae73eeb6917bd000080bff3cc0b3e0d770d3fe09f2c3f0000000036cea6bd43267fbf571d643fc69ae73eb46917bd000080bf2054f93c872d093f91f92c3f700f47b52fcea6bd43267fbf521d643fd89ae73e6c6a17bd000080bf44ab85bdc178f93edbfd2d3f21a7dbb431cea6bd44267fbf571d643fc49ae73e106a17bd000080bfb29b10becbb6d23e44932f3f0000000028cea6bd44267fbf591d643fbd9ae73ea16917bd000080bfbf8a42be65e0a13e1c92313f0000000013cea6bd45267fbf511d643fdf9ae73ea36917bd000080bf7cbf53bec37a573e62c8333f000000002ccea6bd43267fbf521d643fd89ae73eb76917bd000080bfbf8a42be7169d63da9fe353f000000003ccea6bd43267fbf541d643fd39ae73ec26917bd000080bfb19b10be307f183c81fd373f23a75bb41dcea6bd44267fbf531d643fd79ae73ed96917bd000080bf40ab85bdf4f787bde992393f20a7dbb31fcea6bd44267fbf4b1d643ff49ae73ed66917bd000080bf3054f93c2081ebbd33973a3f764889b431cea6bd43267fbf501d643fe29ae73eff6917bd000080bff2cc0b3eaae606bee4f03a3f655f1ba939cea6bd43267fbf631d643f969ae73e986917bd000080bf5e6f783e2081ebbd33973a3fe902173536cea6bd43267fbf5d1d643faf9ae73e1e6917bd000080bfc237ad3ef2f787bde992393f665f9b291fcea6bd45267fbf581d643fc49ae73e9d6917bd000080bfc91ad43e407f183c81fd373f1a0f69a91ccea6bd44267fbf501d643fe09ae73ead6917bd000080bf5012ed3e7569d63da9fe353f2ca75b3440cea6bd44267fbf451d643f0d9be73eba6917bd000080bfaeacf53ec37a573e62c8333f0000000026cea6bd44267fbf481d643f029be73ecc6917bd000080bf5712ed3edd30a43e1ebb3f3f28a7db3439cea63d43267f3faed87c3fccb31f3ec7d150bc000080bff3cc0b3eb11b5c3e65f1413ff3b8af322dcea63d44267f3fb0d87c3f89b31f3ec2cf50bc000080bfd01ad43e4407d53e46bc3d3f6b4809b536cea63d44267f3fafd87c3fbbb31f3ed9cd50bc000080bfc537ad3e3bc9fb3edd263c3f2aa75bb42bcea63d43267f3fb0d87c3f97b31f3ee3ce50bc000080bf626f783ec2550a3f94223b3fac25103525cea63d45267f3fb1d87c3f7db31f3ecdd150bc000080bff3cc0b3e499f0e3fe3c83a3f0000000033cea63d44267f3fb2d87c3f64b31f3e83cf50bc000080bf2054f93cc2550a3f94223b3faa2510b526cea63d44267f3fb1d87c3f86b31f3e65cd50bc000080bf44ab85bd39c9fb3edd263c3f120f69aa2ecea63d44267f3fb2d87c3f5eb31f3e76cf50bc000080bfb29b10be4307d53e46bc3d3f7748093535cea63d43267f3fb4d87c3f2fb31f3e62d150bc000080bfbf8a42bedd30a43e1ebb3f3f27a75bb43dcea63d44267f3fb2d87c3f6bb31f3ebfce50bc000080bf7cbf53beb11b5c3e65f1413f000000002bcea63d44267f3fb1d87c3f83b31f3ea2cf50bc000080bfbf8a42be55abdf3dab27443f1ea75b340ecea63d44267f3fb1d87c3f81b31f3e54d050bc000080bfb19b10bee08d623c8326463f59bda4342bcea63d44267f3fb0d87c3f94b31f3efed050bc000080bf40ab85bd2c6c7dbdecbb473f1fa75bb439cea63d43267f3fb0d87c3f9bb31f3eface50bc000080bf3054f93c383fe2bd36c0483f608462b52acea63d44267f3fb1d87c3f76b31f3e0dcc50bc000080bff2cc0b3eba4502bee619493f6b5f1ba922cea63d44267f3fb2d87c3f64b31f3e6ecf50bc000080bf5e6f783e383fe2bd36c0483f979a2b352ccea63d43267f3fb1d87c3f82b31f3e4bd250bc000080bfc237ad3e246c7dbdecbb473f2ba75b3439cea63d43267f3fafd87c3f9fb31f3eb3d050bc000080bfc91ad43ee08d623c8326463f2aa7db332ccea63d45267f3fb0d87c3f9fb31f3e34d050bc000080bf5012ed3e59abdf3dab27443f0000000012cea63d45267f3fafd87c3fa9b31f3eb4cf50bc000080bfaeacf53eb11b5c3e65f1413f000000002acea63d43267f3faed87c3fbeb31f3eeecf50bc000080bf5712ed3e66e0a13e1c92313f7678733fccb09d3e0a2ecebc57662b38f78ba63df0267f3f000080bf5712ed3edd30a43e1ebb3f3f7778733fc9b09d3e072ecebc367e2c388d8ba63df1267f3f000080bfd01ad43e4407d53e46bc3d3fbb1b4f3f1ff9153f351744bd92d3a1387d96a63dd5267f3f000080bfd01ad43ecdb6d23e43932f3fbb1b4f3f20f9153f371744bd734ca138ac96a63dd4267f3f000080bfc537ad3e3bc9fb3edd263c3f1879163f976b4e3fecf286bd3c99de380aa6a63dac267f3f000080bfc537ad3ec478f93edbfd2d3f1879163f976b4e3fecf286bdfbadde3807a6a63dac267f3f000080bf626f783ec2550a3f94223b3f74379e3e5ea9723f4fa49ebdcccb023920b9a63d7a267f3f000080bf626f783e872d093f91f92c3f70379e3e5ea9723f4ea49ebdd1c5023920b9a63d7a267f3f000080bff3cc0b3e499f0e3fe3c83a3f0000000043267f3f35cea6bdfc2b093935cea63d43267f3f000080bff3cc0b3e0d770d3fe09f2c3f0000000044267f3f36cea6bdfc2b093935cea63d43267f3f000080bf2054f93cc2550a3f94223b3f7e379ebe5ca9723ffaa39ebda257023911e3a63d0d267f3f000080bf2054f93c872d093f91f92c3f7c379ebe5ca9723ffba39ebd1258023912e3a63d0d267f3f000080bf44ab85bd39c9fb3edd263c3f1c7916bf936b4e3f9bf286bd4f63de3839f6a63dda257f3f000080bf44ab85bdc178f93edbfd2d3f1d7916bf946b4e3f9cf286bd628fde3841f6a63dda257f3f000080bfb29b10be4307d53e46bc3d3fbf1b4fbf1bf9153f6a1744bdfd74a1388d05a73db2257f3f000080bfb29b10becbb6d23e44932f3fbe1b4fbf1af9153f6a1744bde172a1388d05a73db2257f3f000080bfbf8a42bedd30a43e1ebb3f3f727873bfe6b09d3e9c2ecebc05232a38460fa73d99257f3f000080bfbf8a42be65e0a13e1c92313f727873bfe4b09d3e9a2ecebc02042a383b0fa73d99257f3f000080bf7cbf53beb11b5c3e65f1413f000080bf7c41d8b364bdca32497f83325e12a73d91257f3f000080bf7cbf53bec37a573e62c8333f000080bf7d4158b2f4898a329d4181322f12a73d91257f3f000080bfbf8a42be55abdf3dab27443f717873bfeeb09dbec12ece3cd04127b8410ea73d9c257f3f000080bfbf8a42be7169d63da9fe353f6f7873bff2b09dbec42ece3ccc2327b8330ea73d9c257f3f000080bfb19b10bee08d623c8326463fb71b4fbf24f915bf2b17443d5a58a0b8ec04a73db4257f3f000080bfb19b10be307f183c81fd373fba1b4fbf22f915bf2917443d13bda0b80f05a73db4257f3f000080bf40ab85bd2c6c7dbdecbb473f197916bf976b4ebfd0f2863d5d99ddb852f6a63dda257f3f000080bf40ab85bdf4f787bde992393f187916bf976b4ebfd0f2863dcb37ddb840f6a63dda257f3f000080bf3054f93c383fe2bd36c0483f83379ebe5aa972bf86a49e3dd32400b949e3a63d0c267f3f000080bf3054f93c2081ebbd33973a3f7f379ebe5aa972bf85a49e3df576ffb837e3a63d0c267f3f000080bff2cc0b3eba4502bee619493f126f1bb442267fbf94cea63d953006b994cea63d42267f3f000080bff2cc0b3eaae606bee4f03a3f126f1bb442267fbf94cea63d7aa406b994cea63d42267f3f000080bf5e6f783e383fe2bd36c0483f7f379e3e5ca972bf86a49e3da3ea01b97fb9a63d79267f3f000080bf5e6f783e2081ebbd33973a3f7e379e3e5aa972bf87a49e3d19ed01b981b9a63d79267f3f000080bfc237ad3e246c7dbdecbb473f1a79163f956b4ebfcdf2863db99cd9b8cda6a63daa267f3f000080bfc237ad3ef2f787bde992393f1b79163f956b4ebfcef2863d6803dab8bca6a63daa267f3f000080bfc91ad43ee08d623c8326463fbb1b4f3f1ff915bf1717443d4556a1b88e96a63dd5267f3f000080bfc91ad43e407f183c81fd373fbd1b4f3f1df915bf1517443d5dc7a1b86896a63dd5267f3f000080bf5012ed3e59abdf3dab27443f6f78733ff1b09dbeb12ece3cb9c228b85a8da63ded267f3f000080bf5012ed3e7569d63da9fe353f6f78733ff5b09dbeb62ece3c349328b86c8da63ded267f3f000080bfaeacf53eb11b5c3e65f1413f0000803f350188b549ab0134ab0d23b3b189a63df6267f3f000080bfaeacf53ec37a573e62c8333f0000803f3f5b78b539faf4332b3425b36289a63df7267f3f000080bf5712ed3edd30a43e1ebb3f3f7778733fc9b09d3e072ecebc17682b38f78ba63df0267f3f000080bf5712ed3e66e0a13e1c92313f7678733fccb09d3e0a2ecebc7d7c2c388c8ba63df1267f3f000080bf9d866d3fcab66b3fbaed5c3f3315693fcf866d3fd674663f83e66b3fcdb6703f37cf683f95f7743f358e643f8d0e783f138e5f3f49ae793f224c5a3f17ae793f204c553ffb0d783f570b513fb2f6743f5ff44d3fb0b5703fa3544c3f8eb56b3fd6544c3f9c73663ff2f44d3f9973613f390c513fd0325d3f3b4d553fd81b5a3f5d4d5a3f1d7c583f4f8f5f3f4f7c583f518f643f6b1c5a3f19d0683fb3335d3f13e76b3fb574613fbf866d3f6b74663fb9ed5c3f3315693fdfe66b3f5374613fcbcf683f65335d3fef8e643f381c5a3fe18e5f3f3e7c583fef4c5a3f2f7c583fd94c553f0c1c5a3feb0b513f21335d3fbdf44d3ffd73613fc3544c3f0a74663fb5544c3ffcb56b3f91f44d3f13b6703fa50b513f01f7743f814c553f2e0e783f8f4c5a3f28ae793f818e5f3f37ae793f998e643f5a0e783f85cf683f46f7743fb3e66b3f6ab6703faf866d3f5db66b3f3f5c723f68cf433ee8a8773f24d0433e8ea8773ffad66c3ee45b723f42d66c3e36a8773fe5ee8a3e8a5b723f89ee8a3edca7773f52729f3e325b723ff6719f3e82a7773fbaf5b33ed75a723f5ef5b33e28a7773f2479c83e7e5a723fc878c83ed0a6773f8efcdc3e265a723f32fcdc3e76a6773ff87ff13ecc59723f9c7ff13e1ea6773fb001033f7459723f8201033fc6a5773f66430d3f1a59723f38430d3f6ca5773f1a85173fc258723fed84173f12a5773fcfc6213f6858723fa1c6213fbaa4773f84082c3f1058723f56082c3f60a4773f384a363fb657723f0b4a363f06a4773fed8b403f5d57723fc08b403faea3773fa2cd4a3f0457723f74cd4a3f54a3773f560f553faa56723f290f553ffca2773f0b515f3f5256723fdd505f3fa2a2773fc092693ff855723f9292693f4aa2773f74d4733fa055723f46d4733ff0a1773f2a167e3f4655723ffb157e3f0000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e0000000000803f0000000000000000000000000e0000000e0000000e0000000e000000
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0.13652396, y: 0.21268936, z: 0.72993296}
    m_Extent: {x: 0.3433091, y: 0.34442866, z: 0.055618465}
  m_MeshUsageFlags: 0
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  m_MeshMetrics[0]: 1
  m_MeshMetrics[1]: 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
