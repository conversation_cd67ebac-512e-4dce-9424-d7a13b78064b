﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Struct FlexalonCurveLayout.CurvePoint
 | Flexalon 3D Layouts </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Struct FlexalonCurveLayout.CurvePoint
 | Flexalon 3D Layouts ">
    <meta name="generator" content="docfx ">
  
    <link rel="shortcut icon" href="images/favicon.svg">
    <link rel="stylesheet" href="styles/docfx.vendor.css">
    <link rel="stylesheet" href="styles/docfx.css">
    <link rel="stylesheet" href="styles/main.css">
    <meta property="docfx:navrel" content="tochead.html">
    <meta property="docfx:tocrel" content="toc.html">
  
  <meta property="docfx:rel" content="">
  
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="https://www.flexalon.com/">
                <img id="logo" class="svg" src="images/logo_32x32.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first="First" data-prev="Previous" data-next="Next" data-last="Last"></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">

        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="Flexalon.FlexalonCurveLayout.CurvePoint">


  <h1 id="Flexalon_FlexalonCurveLayout_CurvePoint" data-uid="Flexalon.FlexalonCurveLayout.CurvePoint" class="text-break">Struct FlexalonCurveLayout.CurvePoint
</h1>
  <div class="markdown level0 summary"><p>A point on the curve.</p>
</div>
  <div class="markdown level0 conceptual"></div>
  <h5 id="Flexalon_FlexalonCurveLayout_CurvePoint_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">[Serializable]
public struct FlexalonCurveLayout.CurvePoint</code></pre>
  </div>
  <h3 id="fields">Fields
</h3>
  <h4 id="Flexalon_FlexalonCurveLayout_CurvePoint_Position" data-uid="Flexalon.FlexalonCurveLayout.CurvePoint.Position">Position</h4>
  <div class="markdown level1 summary"><p>The position of the point.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3 Position</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h4 id="Flexalon_FlexalonCurveLayout_CurvePoint_Tangent" data-uid="Flexalon.FlexalonCurveLayout.CurvePoint.Tangent">Tangent</h4>
  <div class="markdown level1 summary"><p>The tangent of the point.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3 Tangent</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h4 id="Flexalon_FlexalonCurveLayout_CurvePoint_TangentMode" data-uid="Flexalon.FlexalonCurveLayout.CurvePoint.TangentMode">TangentMode</h4>
  <div class="markdown level1 summary"><p>Determines how the tangent for this point is determined.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public FlexalonCurveLayout.TangentMode TangentMode</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.FlexalonCurveLayout.html">FlexalonCurveLayout</a>.<a class="xref" href="Flexalon.FlexalonCurveLayout.TangentMode.html">TangentMode</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
</h3>
  <a id="Flexalon_FlexalonCurveLayout_CurvePoint_ChangePosition_" data-uid="Flexalon.FlexalonCurveLayout.CurvePoint.ChangePosition*"></a>
  <h4 id="Flexalon_FlexalonCurveLayout_CurvePoint_ChangePosition_UnityEngine_Vector3_" data-uid="Flexalon.FlexalonCurveLayout.CurvePoint.ChangePosition(UnityEngine.Vector3)">ChangePosition(Vector3)</h4>
  <div class="markdown level1 summary"><p>Returns a copy of this CurvePoint with a different position.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public FlexalonCurveLayout.CurvePoint ChangePosition(Vector3 position)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><span class="parametername">position</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.FlexalonCurveLayout.html">FlexalonCurveLayout</a>.<a class="xref" href="Flexalon.FlexalonCurveLayout.CurvePoint.html">CurvePoint</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonCurveLayout_CurvePoint_ChangeTangent_" data-uid="Flexalon.FlexalonCurveLayout.CurvePoint.ChangeTangent*"></a>
  <h4 id="Flexalon_FlexalonCurveLayout_CurvePoint_ChangeTangent_UnityEngine_Vector3_" data-uid="Flexalon.FlexalonCurveLayout.CurvePoint.ChangeTangent(UnityEngine.Vector3)">ChangeTangent(Vector3)</h4>
  <div class="markdown level1 summary"><p>Returns a copy of this CurvePoint with a different tangent.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public FlexalonCurveLayout.CurvePoint ChangeTangent(Vector3 tangent)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><span class="parametername">tangent</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.FlexalonCurveLayout.html">FlexalonCurveLayout</a>.<a class="xref" href="Flexalon.FlexalonCurveLayout.CurvePoint.html">CurvePoint</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonCurveLayout_CurvePoint_Copy_" data-uid="Flexalon.FlexalonCurveLayout.CurvePoint.Copy*"></a>
  <h4 id="Flexalon_FlexalonCurveLayout_CurvePoint_Copy" data-uid="Flexalon.FlexalonCurveLayout.CurvePoint.Copy">Copy()</h4>
  <div class="markdown level1 summary"><p>Returns a copy of this CurvePoint.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public FlexalonCurveLayout.CurvePoint Copy()</code></pre>
  </div>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.FlexalonCurveLayout.html">FlexalonCurveLayout</a>.<a class="xref" href="Flexalon.FlexalonCurveLayout.CurvePoint.html">CurvePoint</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In This Article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      Flexalon 3D Layouts
      
          </div>
        </div>
      </footer>
    </div>

    
    
    
  </body>
</html>
