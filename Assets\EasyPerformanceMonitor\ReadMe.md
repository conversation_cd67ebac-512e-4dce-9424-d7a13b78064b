# Description

EasyPerformanceMonitor is a performance monitoring tool designed for unity. With real-time monitoring of essential metrics such as FPS, CPU usage, GPU usage, and memory usage, EasyPerformanceMonitor empowers you to optimize your application and game performance.

**Key Features:**

1. In-Game Monitoring: EasyPerformanceMonitor provides a seamless in-game overlay that keeps you updated on crucial performance metrics without disrupting your gameplay.

2. Simple to Use: We believe monitoring your game performance should be effortless. EasyPerformanceMonitor boasts a user-friendly interface and an intuitive design, allowing you to start monitoring your game performance with ease. No complicated setup or technical knowledge required—simply plug and play.

3. Plug and Play Compatibility: EasyPerformanceMonitor is compatible with all major gaming platforms, including PC, Xbox, PlayStation, and more. Just add the tool, and it seamlessly integrates with your setup, delivering real-time performance insights on demand.

4. Custom Data Extension: We understand that every developer have unique preferences and requirements. EasyPerformanceMonitor allows you to extend its monitoring capabilities by integrating custom data. Whether you want to monitor network latency, or any other specific metric, EasyPerformanceMonitor provides the flexibility to tailor the monitoring experience according to your needs.

5. Performance Optimization: By analyzing the real-time metrics, you can identify bottlenecks and make informed adjustments to your settings, ensuring smooth and uninterrupted gameplay.

6. Historical Data Analysis: EasyPerformanceMonitor doesn't just provide real-time monitoring; it also allows to store historical data for analysis. Evaluate your performance over time, identify patterns, and make data-driven decisions to enhance your game or application.

# How to start

Get started by downloading and adding the "Monitor" or "Monitor - Compact" to your game or application. Integrating is super easy - just include the performance monitor prefab in your initial scene, and you're good to go.

After adding the performance monitor prefab, you adjust its settings. You have the ability to specify if the performance monitor should be available exclusively in development mode and activate/deactivate monitoring components as desired. By default, the monitor can be activated in-game by pressing the F1 key, but you also have the freedom to set custom keys for this, providing a personalized monitoring experience.