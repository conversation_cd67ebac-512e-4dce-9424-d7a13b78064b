<?xml version="1.0" encoding="UTF-8"?><package><identifier v="Unsaved Package"/><formatVersion v="1.1.0.202204"/><updaterVersion v="1.1.0.202204"/><fileUID v="{84b9ec4e-af4c-4574-952a-e3d014b1549e}"/><versionUID v="0"/><dependencies><dependency><filename v="sbs://pattern_polygon_2.sbs"/><uid v="1486809879"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://gradient_linear_1.sbs"/><uid v="1486810660"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_crystal_1.sbs"/><uid v="1486811282"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://pattern_tile_sampler.sbs"/><uid v="1486812999"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://blur_hq.sbs"/><uid v="1486811589"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://histogram_scan.sbs"/><uid v="1487074801"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency></dependencies><content><graph><identifier v="cliff"/><uid v="1486798876"/><graphtype v="material"/><graphOutputs><graphoutput><identifier v="basecolor"/><uid v="1213284336"/><attributes><label v="Base Color"/></attributes><usages><usage><components v="RGBA"/><name v="baseColor"/></usage></usages><group v="Material"/></graphoutput><graphoutput><identifier v="normal"/><uid v="1213284338"/><attributes><label v="Normal"/></attributes><usages><usage><components v="RGBA"/><name v="normal"/></usage></usages><group v="Material"/></graphoutput><graphoutput><identifier v="roughness"/><uid v="1213284340"/><attributes><label v="Roughness"/></attributes><usages><usage><components v="RGBA"/><name v="roughness"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput><graphoutput><identifier v="metallic"/><uid v="1213284342"/><attributes><label v="Metallic"/></attributes><usages><usage><components v="RGBA"/><name v="metallic"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput><graphoutput><identifier v="height"/><uid v="1279137031"/><attributes><label v="Height"/></attributes><usages><usage><components v="RGBA"/><name v="height"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput><graphoutput><identifier v="ambientocclusion"/><uid v="1359211721"/><attributes><label v="Ambient Occlusion"/></attributes><usages><usage><components v="RGBA"/><name v="ambientOcclusion"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput></graphOutputs><compNodes><compNode><uid v="1213284337"/><GUILayout><gpos v="-48 -240 0"/></GUILayout><connections><connection><identifier v="inputNodeOutput"/><connRef v="1486842230"/><connRefOutput v="1486842231"/></connection></connections><compImplementation><compOutputBridge><output v="1213284336"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1213284339"/><GUILayout><gpos v="-48 -80 0"/></GUILayout><connections><connection><identifier v="inputNodeOutput"/><connRef v="1359211383"/><connRefOutput v="1359211384"/></connection></connections><compImplementation><compOutputBridge><output v="1213284338"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1213284341"/><GUILayout><gpos v="-48 80 0"/></GUILayout><connections><connection><identifier v="inputNodeOutput"/><connRef v="1487074852"/><connRefOutput v="1487074853"/></connection></connections><compImplementation><compOutputBridge><output v="1213284340"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1213284343"/><GUILayout><gpos v="-48 240 0"/></GUILayout><connections><connection><identifier v="inputNodeOutput"/><connRef v="1359211407"/><connRefOutput v="1359211408"/></connection></connections><compImplementation><compOutputBridge><output v="1213284342"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1279137030"/><GUILayout><gpos v="-48 432 0"/></GUILayout><connections><connection><identifier v="inputNodeOutput"/><connRef v="1487074852"/><connRefOutput v="1487074853"/></connection></connections><compImplementation><compOutputBridge><output v="1279137031"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1359211383"/><GUILayout><gpos v="-272 -80 0"/></GUILayout><compOutputs><compOutput><uid v="1359211384"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487074852"/><connRefOutput v="1487074853"/></connection></connections><compImplementation><compFilter><filter v="normal"/><parameters><parameter><name v="input2alpha"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="20"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1359211407"/><GUILayout><gpos v="-208 240 0"/></GUILayout><compOutputs><compOutput><uid v="1359211408"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="uniform"/><parameters><parameter><name v="colorswitch"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1359211720"/><GUILayout><gpos v="-48 592 0"/></GUILayout><connections><connection><identifier v="inputNodeOutput"/><connRef v="1487074852"/><connRefOutput v="1487074853"/></connection></connections><compImplementation><compOutputBridge><output v="1359211721"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1486810087"/><GUILayout><gpos v="-2256 112 0"/></GUILayout><compOutputs><compOutput><uid v="1486810088"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///polygon_2?dependency=1486809879"/><parameters><parameter><name v="Sides"/><relativeTo v="0"/><paramValue><constantValueInt32 v="7"/></paramValue></parameter><parameter><name v="Scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.810000002"/></paramValue></parameter><parameter><name v="Curve"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0700000003"/></paramValue></parameter><parameter><name v="Gradient"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.310000002"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1486810088"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1486810165"/><GUILayout><gpos v="-2256 305.767212 0"/></GUILayout><compOutputs><compOutput><uid v="1486810088"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///polygon_2?dependency=1486809879"/><parameters><parameter><name v="Sides"/><relativeTo v="0"/><paramValue><constantValueInt32 v="9"/></paramValue></parameter><parameter><name v="Curve"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.140000001"/></paramValue></parameter><parameter><name v="Gradient"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.310000002"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1486810088"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1486810627"/><GUILayout><gpos v="-2096 112 0"/></GUILayout><compOutputs><compOutput><uid v="1486810628"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="destination"/><connRef v="1486810087"/><connRefOutput v="1486810088"/></connection><connection><identifier v="source"/><connRef v="1486810801"/><connRefOutput v="1486810802"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1486810635"/><GUILayout><gpos v="-2096 304 0"/></GUILayout><compOutputs><compOutput><uid v="1486810636"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="destination"/><connRef v="1486810165"/><connRefOutput v="1486810088"/></connection><connection><identifier v="source"/><connRef v="1486810801"/><connRefOutput v="1486810802"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1486810705"/><GUILayout><gpos v="-2461.88818 -47.9999962 0"/></GUILayout><compOutputs><compOutput><uid v="1486810706"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///gradient_linear_1?dependency=1486810660"/><parameters/><outputBridgings><outputBridging><uid v="1486810706"/><identifier v="Simple_Gradient"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1486810801"/><GUILayout><gpos v="-2256 -47.9999962 0"/></GUILayout><compOutputs><compOutput><uid v="1486810802"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1486810705"/><connRefOutput v="1486810706"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.484931499 0.484931499 0.484931499 0.5"/></paramValue></parameter><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.667307675 0.667307675 0.667307675 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1486811269"/><GUILayout><gpos v="-1936 112 0"/></GUILayout><compOutputs><compOutput><uid v="1486811270"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1486810627"/></connection><connection><identifier v="inputintensity"/><connRef v="1486811627"/><connRefOutput v="1486811628"/></connection></connections><compImplementation><compFilter><filter v="directionalwarp"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="20"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1486811508"/><GUILayout><gpos v="-2256 -189.677582 0"/></GUILayout><compOutputs><compOutput><uid v="1486811509"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///crystal_1?dependency=1486811282"/><parameters><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter><parameter><name v="disorder"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.270000011"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1486811509"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1486811627"/><GUILayout><gpos v="-2096 -190.844543 0"/></GUILayout><compOutputs><compOutput><uid v="1486811628"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="Source"/><connRef v="1486811508"/><connRefOutput v="1486811509"/></connection></connections><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1486811589"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="2.69000006"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1486811628"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1486811896"/><GUILayout><gpos v="-1936 305.767212 0"/></GUILayout><compOutputs><compOutput><uid v="1486811270"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="inputintensity"/><connRef v="1486811627"/><connRefOutput v="1486811628"/></connection><connection><identifier v="input1"/><connRef v="1486810635"/><connRefOutput v="1486810636"/></connection></connections><compImplementation><compFilter><filter v="directionalwarp"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="20"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1486813769"/><GUILayout><gpos v="-1570.23608 298.189545 0"/></GUILayout><compOutputs><compOutput><uid v="1486813770"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="pattern_input"/><connRef v="1486811269"/><connRefOutput v="1486811270"/></connection><connection><identifier v="pattern_input_2"/><connRef v="1486811896"/><connRefOutput v="1486811270"/></connection></connections><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1486812999"/><parameters><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="pattern_input_number"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="8"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="4.54999971"/></paramValue></parameter><parameter><name v="rotation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.119999997"/></paramValue></parameter><parameter><name v="size"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="1.77999997 0.629999995"/></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.529999971"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1486813770"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1486818891"/><GUILayout><gpos v="-1599.7915 -304 0"/></GUILayout><compOutputs><compOutput><uid v="1486818892"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1486812999"/><parameters><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="8"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="10"/></paramValue></parameter><parameter><name v="size"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="1 0.300000012"/></paramValue></parameter><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="6.76000023"/></paramValue></parameter><parameter><name v="size_random"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0 0.330000013"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.540000021"/></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="2.96000004"/></paramValue></parameter><parameter><name v="rotation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0638888925"/></paramValue></parameter><parameter><name v="color_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.779999971"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1486818892"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1486819423"/><GUILayout><gpos v="-1456 -304 0"/></GUILayout><compOutputs><compOutput><uid v="1486819424"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1486818891"/><connRefOutput v="1486818892"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.422033906 0.422033906 0.422033906 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1486819714"/><GUILayout><gpos v="-1296 -290.236145 0"/></GUILayout><compOutputs><compOutput><uid v="1486819715"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1486819423"/></connection><connection><identifier v="inputintensity"/><connRef v="1486811627"/><connRefOutput v="1486811628"/></connection></connections><compImplementation><compFilter><filter v="directionalwarp"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="30"/></paramValue></parameter><parameter><name v="warpangle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.200000003"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1486820135"/><GUILayout><gpos v="-1008 176 0"/></GUILayout><compOutputs><compOutput><uid v="1486820136"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="source"/><connRef v="1486819714"/><connRefOutput v="1486819715"/></connection><connection><identifier v="destination"/><connRef v="1486823112"/><connRefOutput v="1486823113"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.569999993"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1486823112"/><GUILayout><gpos v="-1200 176 0"/></GUILayout><compOutputs><compOutput><uid v="1486823113"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="inputintensity"/><connRef v="1486819714"/><connRefOutput v="1486819715"/></connection><connection><identifier v="input1"/><connRef v="1486836796"/><connRefOutput v="1486836797"/></connection></connections><compImplementation><compFilter><filter v="directionalwarp"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="15.4300003"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1486836796"/><GUILayout><gpos v="-1360 176 0"/></GUILayout><compOutputs><compOutput><uid v="1486836797"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1486813769"/><connRefOutput v="1486813770"/></connection><connection><identifier v="inputintensity"/><connRef v="1486811627"/><connRefOutput v="1486811628"/></connection></connections><compImplementation><compFilter><filter v="directionalwarp"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="14.4799995"/></paramValue></parameter><parameter><name v="warpangle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.340000004"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1486842230"/><GUILayout><gpos v="-272 -240 0"/></GUILayout><compOutputs><compOutput><uid v="1486842231"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1486842994"/></connection></connections><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1486842247"/><paramsArrayCells><paramsArrayCell><uid v="1486855468"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.828118205"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="1 1 1 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1486855469"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.0421940833"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.9173913 0.802369535 0.586703718 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1486855470"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.688366413"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.926984906 0.934782624 0.755434811 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1486842994"/><GUILayout><gpos v="-482.527893 -194.429932 0"/></GUILayout><compOutputs><compOutput><uid v="1486842995"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487074852"/><connRefOutput v="1487074853"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.494656503 0.494656503 0.494656503 0.5"/></paramValue></parameter><parameter><name v="levelouthigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.78793776 0.78793776 0.78793776 1"/></paramValue></parameter><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.683270693 0.683270693 0.683270693 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487074852"/><GUILayout><gpos v="-688 319.015228 0"/></GUILayout><compOutputs><compOutput><uid v="1487074853"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="Input_1"/><connRef v="1486820135"/><connRefOutput v="1486820136"/></connection></connections><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1487074801"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.5"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487074853"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode></compNodes><baseParameters/><options><option><name v="defaultParentSize"/><value v="11x11"/></option><option><name v="export/fromGraph/autoExport"/><value v="false"/></option><option><name v="export/fromGraph/destination"/><value v="D:/work/Annery/stylized material/stylized material/images/cliff"/></option><option><name v="export/fromGraph/extension"/><value v="png"/></option><option><name v="export/fromGraph/outputs/ambientocclusion"/><value v="true"/></option><option><name v="export/fromGraph/outputs/basecolor"/><value v="true"/></option><option><name v="export/fromGraph/outputs/height"/><value v="true"/></option><option><name v="export/fromGraph/outputs/metallic"/><value v="true"/></option><option><name v="export/fromGraph/outputs/normal"/><value v="true"/></option><option><name v="export/fromGraph/outputs/roughness"/><value v="true"/></option><option><name v="export/fromGraph/outputsColorspace/ambientocclusion"/><value v="Raw"/></option><option><name v="export/fromGraph/outputsColorspace/basecolor"/><value v="sRGB"/></option><option><name v="export/fromGraph/outputsColorspace/height"/><value v="Raw"/></option><option><name v="export/fromGraph/outputsColorspace/metallic"/><value v="Raw"/></option><option><name v="export/fromGraph/outputsColorspace/normal"/><value v="Raw"/></option><option><name v="export/fromGraph/outputsColorspace/roughness"/><value v="Raw"/></option><option><name v="export/fromGraph/pattern"/><value v="$(graph)_$(identifier)"/></option></options><root><rootOutputs><rootOutput><output v="1213284336"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1213284338"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1213284340"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1213284342"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1279137031"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1359211721"/><format v="0"/><usertag v=""/></rootOutput></rootOutputs></root></graph></content></package>
