%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-8181695470071874726
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0353a89b1f911e48b9e16bdc9f2e058, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 9
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Ophanim Rings 4
  m_Shader: {fileID: -6465566751694194690, guid: e77111046b851ae47b57003f367ef054, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords:
  - _ALPHAMODULATE_ON
  m_InvalidKeywords:
  - DR_OUTLINE_ON
  - DR_RIM_ON
  - DR_SPECULAR_ON
  - _CELPRIMARYMODE_SINGLE
  - _EMISSION
  - _HATCHINGTEXTURESPACE_WORLD
  - _TEXTUREBLENDINGMODE_ADD
  - _UNITYSHADOWMODE_NONE
  m_LightmapFlags: 2
  m_EnableInstancingVariants: 1
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: 3000
  stringTagMap:
    RenderType: Opaque
  disabledShaderPasses:
  - MOTIONVECTORS
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BaseMap:
        m_Texture: {fileID: 2800000, guid: 0e9e071d2d3358d43b016e380b9cd405, type: 3}
        m_Scale: {x: -4.3, y: 1.28}
        m_Offset: {x: -1.56, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 2800000, guid: 5b653e484c8e303439ef414b62f969f0, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _CelCurveTexture:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _CelStepTexture:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 2800000, guid: 4753ca42b0ba96a41ad2a8e77df6190c, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Emission_Map:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _HatchingTexture:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _RoughnessMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecularDabTexture:
        m_Texture: {fileID: 2800000, guid: 442e6933888925c48b05d56bcfba9809, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AOEnabled: 1
    - _AOStrength: 0.359
    - _AOToonShadingEnabled: 1
    - _AdditionalLightToonShadingEnabled: 1
    - _AdditionalLightToonShadingThreshold: 0.498
    - _AlphaClip: 0
    - _AlphaClipThreshold: 0
    - _AlphaToMask: 0
    - _AmbientLightStrength: 0.526
    - _Blend: 3
    - _BlendModePreserveSpecular: 1
    - _BumpScale: 1
    - _CameraDistanceImpact: 0.72
    - _CastShadows: 1
    - _CelExtraEnabled: 0
    - _CelNumSteps: 4
    - _CelPrimaryMode: 1
    - _ClearCoatMask: 0
    - _ClearCoatSmoothness: 0
    - _Cull: 0
    - _Cutoff: 0.5
    - _DetailAlbedoMapScale: 1
    - _DetailNormalMapScale: 1
    - _DstBlend: 0
    - _Emissive: 0
    - _EnvironmentReflections: 1
    - _FlatRimEdgeSmoothness: 0.29
    - _FlatRimLightAlign: 0
    - _FlatRimSize: 0.17
    - _FlatSpecularEdgeSmoothness: 0.6
    - _FlatSpecularSize: 0.44
    - _Flatness: 1
    - _FlatnessExtra: 1
    - _FogEnabled: 1
    - _GlossMapScale: 0
    - _Glossiness: 0
    - _GlossyReflections: 0
    - _GradientAngle: 0
    - _GradientCenterX: 0.08
    - _GradientCenterY: 0
    - _GradientEnabled: 0
    - _GradientSize: 10
    - _HATCHINGTEXTURESPACE: 0
    - _HasNormalMap: 0
    - _HatchingEnabled: 0
    - _HatchingScale: 1
    - _HatchingTextureSpace: 0
    - _LightContribution: 1
    - _LightFalloffSize: 0.47
    - _LightingMode: 1
    - _LightmapDirectionPitch: 0
    - _LightmapDirectionYaw: 0
    - _Metallic: 0
    - _Midpoint: 0.691
    - _MidpointStrength: 0.32
    - _NormalStrength: 1
    - _OcclusionStrength: 1
    - _OutlineDepthOffset: 0.51
    - _OutlineEnabled: 1
    - _OutlineScale: 1
    - _OutlineWidth: 7.22
    - _OverrideLightmapDir: 0
    - _Parallax: 0.005
    - _QueueControl: -1
    - _QueueOffset: 0
    - _ReceiveAdditionalLights: 1
    - _ReceiveShadows: 1
    - _ReceiveShadows_TK2: 1
    - _RimEnabled: 1
    - _RimLightingEnabled: 1
    - _RimThreshold: 1
    - _RoughnessAmount: 0.25
    - _SelfShadingSize: 0.48
    - _SelfShadingSizeExtra: 0.46
    - _ShadowEdgeSize: 0.05
    - _ShadowEdgeSizeExtra: 0.081
    - _Smoothness: 0.5
    - _SmoothnessTextureChannel: 0
    - _SpecularColorAmount: 0
    - _SpecularDabRotation: 0
    - _SpecularDabScale: 20
    - _SpecularEnabled: 1
    - _SpecularHighlights: 1
    - _SpecularHighlightsEnabled: 1
    - _SrcBlend: 1
    - _Surface: 0
    - _TextureBlendingMode: 1
    - _TextureImpact: 0.04
    - _UnityShadowMode: 0
    - _UnityShadowOcclusion: 0
    - _UnityShadowPower: 0.2
    - _UnityShadowSharpness: 1
    - _VertexColorsEnabled: 0
    - _ViewState: 0
    - _WorkflowMode: 1
    - _ZTest: 4
    - _ZWrite: 1
    - _ZWriteControl: 0
    m_Colors:
    - _BaseColor: {r: 0.04313725, g: 0.3360499, b: 0.44313726, a: 0.9098039}
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _ColorDim: {r: 1, g: 1, b: 1, a: 1}
    - _ColorDimCurve: {r: 0.03137255, g: 0.0470588, b: 0.14901957, a: 1}
    - _ColorDimExtra: {r: 0.03137255, g: 0.0470588, b: 0.14901957, a: 1}
    - _ColorDimSteps: {r: 0.03137255, g: 0.0470588, b: 0.14901957, a: 1}
    - _ColorGradient: {r: 0.03137255, g: 0.0470588, b: 0.14901957, a: 1}
    - _Color_Lit: {r: 1, g: 1, b: 1, a: 0}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _Emission_Color: {r: 0, g: 0, b: 0, a: 0}
    - _FlatRimColor: {r: 0.5188679, g: 0.031533405, b: 0, a: 1}
    - _FlatSpecularColor: {r: 0.5849056, g: 0.03554675, b: 0, a: 1}
    - _HatchingThresholds: {r: 1, g: 0.7, b: 0.5, a: 0.25}
    - _LightmapDirection: {r: 0, g: 1, b: 0, a: 0}
    - _MainColor: {r: 0.5849056, g: 0, b: 0.11968248, a: 0.36078432}
    - _OutlineColor: {r: 0.754717, g: 0.33819866, b: 0.754717, a: 1}
    - _RimColor: {r: 1, g: 1, b: 1, a: 0}
    - _ShadowTint: {r: 1, g: 0.6273585, b: 0.6273585, a: 0}
    - _SpecColor: {r: 0.19999996, g: 0.19999996, b: 0.19999996, a: 1}
    - _SpecularColor: {r: 1, g: 1, b: 1, a: 0}
    - _UnityShadowColor: {r: 0.85023, g: 0.85034, b: 0.8504499, a: 0.85056}
  m_BuildTextureStacks: []
  m_AllowLocking: 1
