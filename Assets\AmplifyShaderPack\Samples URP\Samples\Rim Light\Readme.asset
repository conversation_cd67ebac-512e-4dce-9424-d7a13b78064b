%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e78934177bebe0545ab566bb787b0282, type: 3}
  m_Name: Readme
  m_EditorClassIdentifier: 
  Icon: {fileID: 2800000, guid: 5a826b2f74301474f921b37df15456c3, type: 3}
  Title: Rim Light
  Description:
    Heading: 
    Text: This sample simulates rim lighting over surface borders.
    LinkText: 
    Url: 
  PropertiesHeader:
    Heading: Properties
    Text: 
    LinkText: 
    Url: 
  Properties:
  - Heading: RimColor
    Text: Color value to tint rim light.
    LinkText: 
    Url: 
  - Heading: RimPower
    Text: Value to control how spread is the rim light.
    LinkText: 
    Url: 
  - Heading: Metallic
    Text: Metallic texture to be applied to surface. ( Only R channel used  )
    LinkText: 
    Url: 
  - Heading: Occlusion
    Text: Occlusion texture to be applied to surface. ( Only R channel used  )
    LinkText: 
    Url: 
  - Heading: Normals
    Text: Normals texture to be applied to surface.
    LinkText: 
    Url: 
  - Heading: Albedo
    Text: Albedo texture to be applied to surface.
    LinkText: 
    Url: 
  AdditionalProperties: []
  AdditionalScripts: []
  LoadedLayout: 0
  RPType: 3
