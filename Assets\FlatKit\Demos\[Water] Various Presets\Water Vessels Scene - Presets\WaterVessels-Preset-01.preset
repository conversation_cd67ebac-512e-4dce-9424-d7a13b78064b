%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!181963792 &2655988077585873504
Preset:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: WaterVessels-Preset-01
  m_TargetType:
    m_NativeTypeID: 21
    m_ManagedTypePPtr: {fileID: 0}
    m_ManagedTypeFallback: 
  m_Properties:
  - target: {fileID: 0}
    propertyPath: m_Shader
    value: 
    objectReference: {fileID: 4800000, guid: efcaf526ceb1f4efd9eb4e4c30c6ade6, type: 3}
  - target: {fileID: 0}
    propertyPath: m_ShaderKeywords
    value: _ALLOWALPHAOVERFLOW_OFF _COLORMODE_LINEAR _FOAMMODE_NONE _WAVEMODE_ROUND
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_LightmapFlags
    value: 4
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_EnableInstancingVariants
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_DoubleSidedGI
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_CustomRenderQueue
    value: -1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: stringTagMap.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: disabledShaderPasses.Array.size
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.size
    value: 9
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[0].first
    value: _BaseMap
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[0].second.m_Texture
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[0].second.m_Scale.x
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[0].second.m_Scale.y
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[0].second.m_Offset.x
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[0].second.m_Offset.y
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[1].first
    value: _BumpMap
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[1].second.m_Texture
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[1].second.m_Scale.x
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[1].second.m_Scale.y
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[1].second.m_Offset.x
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[1].second.m_Offset.y
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[2].first
    value: _ColorGradient
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[2].second.m_Texture
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[2].second.m_Scale.x
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[2].second.m_Scale.y
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[2].second.m_Offset.x
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[2].second.m_Offset.y
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[3].first
    value: _EmissionMap
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[3].second.m_Texture
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[3].second.m_Scale.x
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[3].second.m_Scale.y
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[3].second.m_Offset.x
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[3].second.m_Offset.y
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[4].first
    value: _MainTex
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[4].second.m_Texture
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[4].second.m_Scale.x
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[4].second.m_Scale.y
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[4].second.m_Offset.x
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[4].second.m_Offset.y
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[5].first
    value: _MetallicGlossMap
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[5].second.m_Texture
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[5].second.m_Scale.x
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[5].second.m_Scale.y
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[5].second.m_Offset.x
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[5].second.m_Offset.y
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[6].first
    value: _NoiseMap
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[6].second.m_Texture
    value: 
    objectReference: {fileID: 2800000, guid: 062db1959aabc4afb95dd37cf1c09dd6, type: 3}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[6].second.m_Scale.x
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[6].second.m_Scale.y
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[6].second.m_Offset.x
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[6].second.m_Offset.y
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[7].first
    value: _OcclusionMap
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[7].second.m_Texture
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[7].second.m_Scale.x
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[7].second.m_Scale.y
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[7].second.m_Offset.x
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[7].second.m_Offset.y
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[8].first
    value: _SpecGlossMap
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[8].second.m_Texture
    value: 
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[8].second.m_Scale.x
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[8].second.m_Scale.y
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[8].second.m_Offset.x
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_TexEnvs.Array.data[8].second.m_Offset.y
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.size
    value: 63
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[0].first
    value: _AllowAlphaOverflow
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[0].second
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[1].first
    value: _AlphaClip
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[1].second
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[2].first
    value: _Amount
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[2].second
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[3].first
    value: _Amplitude
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[3].second
    value: 0.51
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[4].first
    value: _Blend
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[4].second
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[5].first
    value: _BumpScale
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[5].second
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[6].first
    value: _CameraDistanceFadeClose
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[6].second
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[7].first
    value: _CameraDistanceFadeFar
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[7].second
    value: 10
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[8].first
    value: _ColorMode
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[8].second
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[9].first
    value: _CrestSharpness
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[9].second
    value: 0.3
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[10].first
    value: _CrestSize
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[10].second
    value: 0.517
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[11].first
    value: _Cull
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[11].second
    value: 2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[12].first
    value: _Cutoff
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[12].second
    value: 0.5
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[13].first
    value: _Depth
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[13].second
    value: 100
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[14].first
    value: _DstBlend
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[14].second
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[15].first
    value: _EnvironmentReflections
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[15].second
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[16].first
    value: _FadeDistance
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[16].second
    value: 0.3
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[17].first
    value: _FoamAmount
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[17].second
    value: 2.3
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[18].first
    value: _FoamDepth
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[18].second
    value: -0.88
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[19].first
    value: _FoamDirection
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[19].second
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[20].first
    value: _FoamFromTexture
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[20].second
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[21].first
    value: _FoamMode
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[21].second
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[22].first
    value: _FoamNoiseAmount
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[22].second
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[23].first
    value: _FoamProcedural
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[23].second
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[24].first
    value: _FoamScale
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[24].second
    value: -11.29
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[25].first
    value: _FoamSharpness
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[25].second
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[26].first
    value: _FoamSpeed
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[26].second
    value: 0.2
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[27].first
    value: _FoamStretchX
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[27].second
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[28].first
    value: _FoamStretchY
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[28].second
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[29].first
    value: _FoamTexture
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[29].second
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[30].first
    value: _Frequency
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[30].second
    value: 0.08
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[31].first
    value: _GlossMapScale
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[31].second
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[32].first
    value: _Glossiness
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[32].second
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[33].first
    value: _GlossyReflections
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[33].second
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[34].first
    value: _Metallic
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[34].second
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[35].first
    value: _OcclusionStrength
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[35].second
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[36].first
    value: _ProceduralFoam
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[36].second
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[37].first
    value: _QueueOffset
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[37].second
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[38].first
    value: _ReceiveShadows
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[38].second
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[39].first
    value: _RefractionAmplitude
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[39].second
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[40].first
    value: _RefractionFrequency
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[40].second
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[41].first
    value: _RefractionScale
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[41].second
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[42].first
    value: _RefractionSpeed
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[42].second
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[43].first
    value: _RefractionStrength
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[43].second
    value: 0.01
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[44].first
    value: _ShadowStrength
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[44].second
    value: 0.35
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[45].first
    value: _Smoothness
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[45].second
    value: 0.5
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[46].first
    value: _SmoothnessTextureChannel
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[46].second
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[47].first
    value: _SpecularHighlights
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[47].second
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[48].first
    value: _Speed
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[48].second
    value: 0.3
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[49].first
    value: _SrcBlend
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[49].second
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[50].first
    value: _Surface
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[50].second
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[51].first
    value: _UvFadeX
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[51].second
    value: 0.1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[52].first
    value: _UvFadeY
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[52].second
    value: 0.1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[53].first
    value: _WaterClearness
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[53].second
    value: 0.493
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[54].first
    value: _WaterDepth
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[54].second
    value: 6.75
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[55].first
    value: _WaveAmplitude
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[55].second
    value: 0.25
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[56].first
    value: _WaveDirection
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[56].second
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[57].first
    value: _WaveFrequency
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[57].second
    value: 2.43
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[58].first
    value: _WaveMode
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[58].second
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[59].first
    value: _WaveNoise
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[59].second
    value: 0.751
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[60].first
    value: _WaveSpeed
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[60].second
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[61].first
    value: _WorkflowMode
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[61].second
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[62].first
    value: _ZWrite
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Floats.Array.data[62].second
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.size
    value: 10
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[0].first
    value: _BaseColor
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[0].second.r
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[0].second.g
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[0].second.b
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[0].second.a
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[1].first
    value: _Color
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[1].second.r
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[1].second.g
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[1].second.b
    value: 0.7287984
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[1].second.a
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[2].first
    value: _ColorDeep
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[2].second.r
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[2].second.g
    value: 0.88191414
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[2].second.b
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[2].second.a
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[3].first
    value: _ColorShallow
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[3].second.r
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[3].second.g
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[3].second.b
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[3].second.a
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[4].first
    value: _ColorTop
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[4].second.r
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[4].second.g
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[4].second.b
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[4].second.a
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[5].first
    value: _CrestColor
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[5].second.r
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[5].second.g
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[5].second.b
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[5].second.a
    value: 0.9
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[6].first
    value: _EmissionColor
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[6].second.r
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[6].second.g
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[6].second.b
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[6].second.a
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[7].first
    value: _FoamColor
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[7].second.r
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[7].second.g
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[7].second.b
    value: 0
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[7].second.a
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[8].first
    value: _FoamStretch
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[8].second.r
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[8].second.g
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[8].second.b
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[8].second.a
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[9].first
    value: _SpecColor
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[9].second.r
    value: 0.19999996
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[9].second.g
    value: 0.19999996
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[9].second.b
    value: 0.19999996
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_SavedProperties.m_Colors.Array.data[9].second.a
    value: 1
    objectReference: {fileID: 0}
  - target: {fileID: 0}
    propertyPath: m_BuildTextureStacks.Array.size
    value: 0
    objectReference: {fileID: 0}
  m_ExcludedProperties: []
