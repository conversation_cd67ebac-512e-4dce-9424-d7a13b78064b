%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Road Base Shoulder 02
  m_Shader: {fileID: 4800000, guid: 7d24bc1c621e42e1a1f5f9e0a1326e3a, type: 3}
  m_ValidKeywords: []
  m_InvalidKeywords:
  - _ACTIVATECLIP_ON
  - _TOGGLESWITCH0_ON
  m_LightmapFlags: 0
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - <noninit>:
        m_Texture: {fileID: 2800000, guid: 7b705e3c0dd4d4240acdbee5cfcd62c8, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Smoothness:
        m_Texture: {fileID: 2800000, guid: 990b5224ab6b406487e7328c391ad210, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Textured_Course_AlbedoTransparency:
        m_Texture: {fileID: 2800000, guid: 25a357f6ca52475daf0f03609c392f79, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _texcoord:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - <noninit>: 1
    - _ActivateClip: 1
    - _AlphaCutoff: 0.5
    - _EnvironmentReflections: 1
    - _QueueControl: 0
    - _QueueOffset: 0
    - _ReceiveShadows: 1
    - _SpecularHighlights: 1
    - __dirty: 0
    m_Colors:
    - <noninit>: {r: 0.13333334, g: 0.11764707, b: 0.10196079, a: 0}
    - _CutoffColor: {r: 0.13333334, g: 0.11764706, b: 0.101960786, a: 1}
    - _Emission: {r: 0, g: 0, b: 0, a: 0}
    - _EmissionColor: {r: 1, g: 1, b: 1, a: 1}
  m_BuildTextureStacks: []
