#if URP_INSTALLED
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Experimental.Rendering;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using UnityEngine.Rendering.RenderGraphModule;

namespace Stylo.BFI.Universal
{
    public class BFIRendererFeature : ScriptableRendererFeature
    {
        private class BFIRenderPass : ScriptableRenderPass
        {
            // PassData structure for Unity 6 Render Graph
            private class BFIPassData
            {
                // Input textures
                internal TextureHandle sourceTexture;
                internal TextureHandle motionVectorTexture;
                internal TextureHandle depthTexture;
                internal TextureHandle previousFrameTexture;

                // Output texture
                internal TextureHandle outputTexture;

                // Material and parameters
                internal Material bfiMaterial;
                internal int passIndex;
                internal Vector4 screenTexelSize;

                // BFI-specific parameters
                internal int bfiMode;
                internal float intensity;
                internal float brightnessCompensation;
                internal float manualBrightness;
                internal float gammaCorrection;
                internal float blackFrameDuration;
                internal float phaseOffset;
                internal bool syncToRefreshRate;
                internal int flickerReductionMode;
                internal float temporalSmoothing;
                internal float edgePreservation;
                internal bool motionAdaptive;
                internal float motionSensitivity;
                internal float motionThreshold;
                internal float adaptationSpeed;
                internal float ditheringStrength;
                internal float subpixelPrecision;
                internal bool debugVisualization;

                // Timing data
                internal float currentTime;
                internal float deltaTime;
                internal int frameCount;
            }

            private BFIEffect v;
            private Material material;
            private RTHandle previousFrameRT;
            private bool isFirstFrame = true;
            private float lastFrameTime;
            private int frameCounter;

            public void Setup(Shader shader, ScriptableRenderer renderer, RenderingData renderingData)
            {
                if (material == null || material.shader != shader)
                {
                    if (material != null)
                        CoreUtils.Destroy(material);
                    material = CoreUtils.CreateEngineMaterial(shader);
                }
            }

            public override void RecordRenderGraph(RenderGraph renderGraph, ContextContainer frameData)
            {
                FetchVolumeComponent();

                // Debug logging
                if (v == null)
                {
                    Debug.LogWarning("BFI: Volume component is null");
                    return;
                }

                if (!v.IsActive())
                {
                    Debug.Log($"BFI: Effect not active - Mode: {v.Mode.value}, Intensity: {v.Intensity.value}");
                    return;
                }

                Debug.Log($"BFI: Effect is active - Mode: {v.Mode.value}, Intensity: {v.Intensity.value}, Debug: {v.DebugVisualization.value}");

                var resourceData = frameData.Get<UniversalResourceData>();
                var cameraData = frameData.Get<UniversalCameraData>();

                // Skip BFI effect entirely for Scene View cameras
                if (cameraData.isSceneViewCamera) return;

                // Skip if rendering to back buffer
                if (resourceData.isActiveTargetBackBuffer)
                    return;

                // Update timing information
                UpdateTiming();

                // Determine if we should render a black frame based on BFI timing
                bool shouldRenderBlackFrame = ShouldRenderBlackFrame();

                TextureHandle outputTexture;
                if (shouldRenderBlackFrame)
                {
                    // Render black frame
                    outputTexture = AddBlackFramePass(renderGraph, resourceData.activeColorTexture);
                }
                else
                {
                    // Render normal frame with brightness compensation
                    // Get motion vector texture if available
                    TextureHandle motionVectorTexture = resourceData.motionVectorColor;
                    outputTexture = AddBrightnessCompensationPass(renderGraph, resourceData.activeColorTexture, motionVectorTexture, resourceData.activeDepthTexture);
                }

                // Copy the result back to the active color texture
                AddCopyPass(renderGraph, outputTexture, resourceData.activeColorTexture);
            }

            private void FetchVolumeComponent()
            {
                var stack = VolumeManager.instance.stack;
                v = stack.GetComponent<BFIEffect>();
            }

            private void UpdateTiming()
            {
                float currentTime = Time.unscaledTime;
                if (isFirstFrame)
                {
                    lastFrameTime = currentTime;
                    isFirstFrame = false;
                }

                frameCounter++;
            }

            private bool ShouldRenderBlackFrame()
            {
                if (v.Mode.value == BFIEffect.BFIMode.Off) return false;

                float currentTime = Time.unscaledTime;
                float deltaTime = currentTime - lastFrameTime;

                // Calculate frame multiplier based on BFI mode
                int frameMultiplier = GetFrameMultiplier();

                // Calculate phase within the BFI cycle
                float cycleTime = deltaTime * frameMultiplier;
                float phase = (cycleTime + v.PhaseOffset.value) % 1f;

                // Determine if we're in a black frame period
                float blackFrameThreshold = v.BlackFrameDuration.value / frameMultiplier;

                return phase < blackFrameThreshold;
            }

            private int GetFrameMultiplier()
            {
                return v.Mode.value switch
                {
                    BFIEffect.BFIMode.DoubleFrameRate => 2,
                    BFIEffect.BFIMode.TripleFrameRate => 3,
                    BFIEffect.BFIMode.QuadFrameRate => 4,
                    _ => 1
                };
            }

            private TextureHandle AddBlackFramePass(RenderGraph renderGraph, TextureHandle sourceTexture)
            {
                using (var builder = renderGraph.AddRasterRenderPass<BFIPassData>("BFI Black Frame", out var passData))
                {
                    // Create output texture with same properties as source
                    var desc = renderGraph.GetTextureDesc(sourceTexture);
                    desc.name = "BFI_BlackFrame";
                    TextureHandle outputTexture = renderGraph.CreateTexture(desc);

                    // Setup pass data for black frame
                    passData.sourceTexture = sourceTexture;
                    passData.outputTexture = outputTexture;
                    passData.bfiMaterial = material;
                    passData.passIndex = 0; // Black frame pass
                    passData.intensity = v.Intensity.value;
                    passData.debugVisualization = v.DebugVisualization.value;

                    // Declare inputs/outputs
                    builder.UseTexture(passData.sourceTexture, AccessFlags.Read);
                    builder.SetRenderAttachment(passData.outputTexture, 0, AccessFlags.Write);

                    builder.SetRenderFunc((BFIPassData data, RasterGraphContext context) =>
                    {
                        ExecuteBFIBlackFramePass(data, context);
                    });

                    return outputTexture;
                }
            }

            private TextureHandle AddBrightnessCompensationPass(RenderGraph renderGraph, TextureHandle sourceTexture, TextureHandle motionTexture, TextureHandle depthTexture)
            {
                using (var builder = renderGraph.AddRasterRenderPass<BFIPassData>("BFI Brightness Compensation", out var passData))
                {
                    // Create output texture with same properties as source
                    var desc = renderGraph.GetTextureDesc(sourceTexture);
                    desc.name = "BFI_BrightnessCompensated";
                    TextureHandle outputTexture = renderGraph.CreateTexture(desc);

                    // Setup pass data for brightness compensation
                    passData.sourceTexture = sourceTexture;
                    passData.motionVectorTexture = motionTexture;
                    passData.depthTexture = depthTexture;
                    passData.outputTexture = outputTexture;
                    passData.bfiMaterial = material;
                    passData.passIndex = 1; // Brightness compensation pass

                    // Copy all BFI parameters
                    CopyBFIParameters(passData);

                    // Declare inputs/outputs
                    builder.UseTexture(passData.sourceTexture, AccessFlags.Read);
                    if (motionTexture.IsValid())
                        builder.UseTexture(passData.motionVectorTexture, AccessFlags.Read);
                    if (depthTexture.IsValid())
                        builder.UseTexture(passData.depthTexture, AccessFlags.Read);
                    builder.SetRenderAttachment(passData.outputTexture, 0, AccessFlags.Write);

                    builder.SetRenderFunc((BFIPassData data, RasterGraphContext context) =>
                    {
                        ExecuteBFIBrightnessPass(data, context);
                    });

                    return outputTexture;
                }
            }

            private void AddCopyPass(RenderGraph renderGraph, TextureHandle sourceTexture, TextureHandle destinationTexture)
            {
                using (var builder = renderGraph.AddRasterRenderPass<BFIPassData>("BFI Copy Result", out var passData))
                {
                    // Setup pass data for copy
                    passData.sourceTexture = sourceTexture;
                    passData.outputTexture = destinationTexture;
                    passData.bfiMaterial = material;
                    passData.passIndex = 2; // Copy pass (we'll add this to the shader)

                    // Declare inputs/outputs
                    builder.UseTexture(passData.sourceTexture, AccessFlags.Read);
                    builder.SetRenderAttachment(passData.outputTexture, 0, AccessFlags.Write);

                    builder.SetRenderFunc((BFIPassData data, RasterGraphContext context) =>
                    {
                        // Simple blit copy
                        Blitter.BlitTexture(context.cmd, data.sourceTexture, Vector2.one, data.bfiMaterial, 2);
                    });
                }
            }

            private void CopyBFIParameters(BFIPassData passData)
            {
                passData.bfiMode = (int)v.Mode.value;
                passData.intensity = v.Intensity.value;
                passData.brightnessCompensation = v.BrightnessCompensation.value;
                passData.manualBrightness = v.ManualBrightness.value;
                passData.gammaCorrection = v.GammaCorrection.value;
                passData.blackFrameDuration = v.BlackFrameDuration.value;
                passData.phaseOffset = v.PhaseOffset.value;
                passData.syncToRefreshRate = v.SyncToRefreshRate.value;
                passData.flickerReductionMode = (int)v.FlickerReductionMode.value;
                passData.temporalSmoothing = v.TemporalSmoothing.value;
                passData.edgePreservation = v.EdgePreservation.value;
                passData.motionAdaptive = v.MotionAdaptive.value;
                passData.motionSensitivity = v.MotionSensitivity.value;
                passData.motionThreshold = v.MotionThreshold.value;
                passData.adaptationSpeed = v.AdaptationSpeed.value;
                passData.ditheringStrength = v.DitheringStrength.value;
                passData.subpixelPrecision = v.SubpixelPrecision.value;
                passData.debugVisualization = v.DebugVisualization.value;
                passData.currentTime = Time.unscaledTime;
                passData.deltaTime = Time.unscaledDeltaTime;
                passData.frameCount = frameCounter;
            }

            // Execution functions for render graph passes
            static void ExecuteBFIBlackFramePass(BFIPassData data, RasterGraphContext context)
            {
                // Set material properties for black frame
                data.bfiMaterial.SetFloat("_Intensity", data.intensity);
                data.bfiMaterial.SetInt("_DebugVisualization", data.debugVisualization ? 1 : 0);

                // Render black frame
                Blitter.BlitTexture(context.cmd, data.sourceTexture, Vector2.one, data.bfiMaterial, data.passIndex);
            }

            static void ExecuteBFIBrightnessPass(BFIPassData data, RasterGraphContext context)
            {
                // Set all material properties for brightness compensation
                SetBFIMaterialProperties(data);

                // Render brightness compensated frame
                Blitter.BlitTexture(context.cmd, data.sourceTexture, Vector2.one, data.bfiMaterial, data.passIndex);
            }

            static void SetBFIMaterialProperties(BFIPassData data)
            {
                data.bfiMaterial.SetInt("_BFIMode", data.bfiMode);
                data.bfiMaterial.SetFloat("_Intensity", data.intensity);
                data.bfiMaterial.SetFloat("_BrightnessCompensation", data.brightnessCompensation);
                data.bfiMaterial.SetFloat("_ManualBrightness", data.manualBrightness);
                data.bfiMaterial.SetFloat("_GammaCorrection", data.gammaCorrection);
                data.bfiMaterial.SetFloat("_BlackFrameDuration", data.blackFrameDuration);
                data.bfiMaterial.SetFloat("_PhaseOffset", data.phaseOffset);
                data.bfiMaterial.SetInt("_SyncToRefreshRate", data.syncToRefreshRate ? 1 : 0);
                data.bfiMaterial.SetInt("_FlickerReductionMode", data.flickerReductionMode);
                data.bfiMaterial.SetFloat("_TemporalSmoothing", data.temporalSmoothing);
                data.bfiMaterial.SetFloat("_EdgePreservation", data.edgePreservation);
                data.bfiMaterial.SetInt("_MotionAdaptive", data.motionAdaptive ? 1 : 0);
                data.bfiMaterial.SetFloat("_MotionSensitivity", data.motionSensitivity);
                data.bfiMaterial.SetFloat("_MotionThreshold", data.motionThreshold);
                data.bfiMaterial.SetFloat("_AdaptationSpeed", data.adaptationSpeed);
                data.bfiMaterial.SetFloat("_DitheringStrength", data.ditheringStrength);
                data.bfiMaterial.SetFloat("_SubpixelPrecision", data.subpixelPrecision);
                data.bfiMaterial.SetInt("_DebugVisualization", data.debugVisualization ? 1 : 0);
                data.bfiMaterial.SetFloat("_CurrentTime", data.currentTime);
                data.bfiMaterial.SetFloat("_DeltaTime", data.deltaTime);
                data.bfiMaterial.SetInt("_FrameCount", data.frameCount);
            }

            public override void OnCameraCleanup(CommandBuffer cmd)
            {
                // Cleanup resources
            }
        }

        private BFIRenderPass renderPass;
        private Shader shader;

        public override void Create()
        {
            renderPass = new BFIRenderPass();
            renderPass.renderPassEvent = RenderPassEvent.BeforeRenderingPostProcessing;
        }

        public override void AddRenderPasses(ScriptableRenderer renderer, ref RenderingData renderingData)
        {
            Debug.Log("BFI: AddRenderPasses called");

            shader = Shader.Find("Hidden/Universal Render Pipeline/BFI");
            if (shader == null)
            {
                Debug.LogWarning("BFI shader was not found. Please ensure it compiles correctly");
                return;
            }

            Debug.Log("BFI: Shader found, setting up render pass");
            renderPass.Setup(shader, renderer, renderingData);
            renderer.EnqueuePass(renderPass);
            Debug.Log("BFI: Render pass enqueued");
        }
    }
}
#endif
