%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e78934177bebe0545ab566bb787b0282, type: 3}
  m_Name: Readme
  m_EditorClassIdentifier: 
  Icon: {fileID: 2800000, guid: 67151c868da28c342a3ecdd482b56efa, type: 3}
  Title: Simple Refraction
  Description:
    Heading: 
    Text: This sample performs a simple refraction using a normal map to guide refractive
      behavior.
    LinkText: 
    Url: 
  PropertiesHeader:
    Heading: Properties
    Text: 
    LinkText: 
    Url: 
  Properties:
  - Heading: Refraction Map
    Text: Normal map texture to set refractive behavior.
    LinkText: 
    Url: 
  - Heading: Distortion
    Text: Scale value to control overall refraction intensity.
    LinkText: 
    Url: 
  AdditionalProperties: []
  AdditionalScripts: []
  LoadedLayout: 0
  RPType: 3
