using UnityEngine;
using UnityEditor;
using BTR;

/// <summary>
/// Creates a properly configured debug system in the scene or as a prefab
/// </summary>
public class BTRDebugSystemCreator : EditorWindow
{
    // All [MenuItem(...)] methods previously here have been removed.
    // The new way to manage debug filtering is via "Tools > BTR > Debug Script Filter"
}

// Helper class for displaying an input dialog
// This can be removed if EditorInputDialog.Show is no longer called from anywhere else.
public class EditorInputDialog : EditorWindow
{
    public static string Show(string title, string message, string defaultValue = "")
    {
        EditorInputDialog window = CreateInstance<EditorInputDialog>();
        window.position = new Rect(Screen.width / 2, Screen.height / 2, 400, 150);
        window.titleContent = new GUIContent(title);
        window._message = message;
        window._inputValue = defaultValue;
        window.ShowModal();
        return window._returnValue;
    }

    private string _message = "";
    private string _inputValue = "";
    private string _returnValue = "";
    private bool _gotReturnValue = false;

    private void OnGUI()
    {
        EditorGUILayout.LabelField(_message);
        GUI.SetNextControlName("InputField");
        _inputValue = EditorGUILayout.TextField("", _inputValue);
        GUI.FocusControl("InputField");
        
        GUILayout.Space(10);
        
        EditorGUILayout.BeginHorizontal();
        GUILayout.FlexibleSpace();
        
        if (GUILayout.Button("Cancel", GUILayout.Width(100)))
        {
            _returnValue = "";
            _gotReturnValue = true;
            Close();
        }
        
        if (GUILayout.Button("OK", GUILayout.Width(100)) || Event.current.keyCode == KeyCode.Return)
        {
            _returnValue = _inputValue;
            _gotReturnValue = true;
            Close();
        }
        
        EditorGUILayout.EndHorizontal();
    }
} 