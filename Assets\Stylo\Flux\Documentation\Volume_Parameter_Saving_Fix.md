# Flux Volume Parameter Saving Fix

## Issue Description

Previously, Flux Volume parameters were not properly saving their **override states** when transitioning between Play Mode and Edit Mode. This caused several problems:

1. **Parameters appearing "turned off"** - Override states would be lost, making parameters appear inactive
2. **Advanced Datamoshing parameters not saving** - Values would change but override states wouldn't persist
3. **New Camera Motion parameters not being tracked** - The snapshot system wasn't aware of the new parameters
4. **Inconsistent behavior** - Some parameters would save while others wouldn't

## Root Cause

The `FluxEffectSnapshot` class was only capturing and restoring **parameter values**, but not their **override states**. In Unity's Volume system, each parameter has:
- **Value**: The actual setting (e.g., 0.5 for a float)
- **Override State**: Whether the parameter is active/overridden (true/false)

When only values were saved, parameters would lose their "enabled" state when exiting Play Mode.

## Solution Implemented

### 1. Enhanced FluxEffectSnapshot Class

**Before:**
```csharp
public float keyframeResetRate;
public float motionVectorCorruption;
// Missing: override states, camera motion parameters, corruption mask
```

**After:**
```csharp
// Advanced Datamoshing - now with override states
public float keyframeResetRate;
public bool keyframeResetRateOverride;

public float motionVectorCorruption;
public bool motionVectorCorruptionOverride;

// Camera Motion Sensitivity (NEW)
public float cameraMotionAmplification;
public bool cameraMotionAmplificationOverride;

// Enhanced Corruption (FIXED)
public Texture2D corruptionMask;
public bool corruptionMaskOverride;
```

### 2. Complete Parameter Coverage

All Flux parameters now properly tracked:
- ✅ **Core Parameters** (Effect Intensity, Only Stenciled)
- ✅ **Block Encoding** (Color Crunch, Downscaling, Block Size, etc.)
- ✅ **Datamoshing Reprojection** (Base Noise, Reroll Speed, Length Influence)
- ✅ **Camera Motion Sensitivity** (NEW - all 4 parameters)
- ✅ **Advanced Datamoshing** (Keyframe Reset, Motion Vector Corruption, Error Accumulation, DCT Corruption)
- ✅ **Enhanced Corruption** (Corruption Mask, Chroma Corruption, Glitch Transition, Feedback Intensity, Multi-Scale Corruption)
- ✅ **Debug** (Visualize Motion Vectors)

### 3. Proper State Restoration

**Capture (entering Play Mode):**
```csharp
keyframeResetRate = fluxEffect.KeyframeResetRate.value;
keyframeResetRateOverride = fluxEffect.KeyframeResetRate.overrideState;
```

**Restore (exiting Play Mode):**
```csharp
fluxEffect.KeyframeResetRate.value = keyframeResetRate;
fluxEffect.KeyframeResetRate.overrideState = keyframeResetRateOverride;
```

### 4. Enhanced Change Detection

The comparison now checks both values AND override states:
```csharp
Mathf.Approximately(keyframeResetRate, other.keyframeResetRate) && 
keyframeResetRateOverride == other.keyframeResetRateOverride
```

## Testing the Fix

### 1. Basic Parameter Saving Test
1. Open `Assets/Stylo/Flux/Demo/Demo_URP.unity`
2. Enter Play Mode
3. In the Volume component, enable **Advanced Datamoshing** parameters:
   - Turn ON **Keyframe Reset Rate** override and set to 0.1
   - Turn ON **Motion Vector Corruption** override and set to 0.5
   - Turn ON **Error Accumulation** override and set to 0.3
4. Exit Play Mode
5. **Expected Result**: All parameters should remain enabled with their values preserved

### 2. Camera Motion Parameters Test
1. Enter Play Mode
2. Enable **Camera Motion Sensitivity** parameters:
   - Turn ON **Camera Motion Amplification** and set to 5.0
   - Turn ON **Camera Motion Threshold** and set to 0.002
   - Turn ON **Camera Motion Influence** and set to 3.0
   - Turn ON **Camera Motion Smoothing** and set to 0.2
3. Exit Play Mode
4. **Expected Result**: All camera motion parameters should remain enabled and preserve their values

### 3. Mixed Parameter State Test
1. Enter Play Mode
2. Set up a mix of enabled and disabled parameters:
   - **Effect Intensity**: ON, value 0.5
   - **Color Crunch**: OFF (disabled override)
   - **Keyframe Reset Rate**: ON, value 0.15
   - **Motion Vector Corruption**: OFF (disabled override)
   - **Camera Motion Amplification**: ON, value 3.0
3. Exit Play Mode
4. **Expected Result**: Enabled parameters stay enabled, disabled parameters stay disabled

### 4. Corruption Mask Test
1. Enter Play Mode
2. Enable **Corruption Mask** and assign a texture
3. Exit Play Mode
4. **Expected Result**: Corruption Mask should remain enabled with the texture assigned

## Validation Tools

### Menu Items Available
- **Tools/Flux/Check for Unsaved Changes** - Manual check during Play Mode
- **Tools/Flux/Save All Current Settings** - Force save current settings
- **Tools/Flux/Test System Status** - Check system status and captured volumes

### Change Details Window
When changes are detected, the "Show Details" option now displays:
- Parameter names with clear labels
- Override state changes (marked as "Override")
- Before/after values for both values and override states
- Support for all parameter types including textures

## Troubleshooting

### Issue: Parameters still not saving
**Solution**: Check that the Volume Profile is not read-only and that the FluxPlayModeChangeDetector is initialized (check Console for "[Flux] Play Mode Change Detector initialized")

### Issue: Some parameters save but others don't
**Solution**: This should no longer occur. If it does, use "Tools/Flux/Test System Status" to check system state and report the issue.

### Issue: Override states not preserved
**Solution**: This was the main issue that has been fixed. The new system captures and restores both values and override states.

### Issue: New Camera Motion parameters not tracked
**Solution**: The snapshot system now includes all Camera Motion parameters with full override state support.

## Technical Details

### Files Modified
- `FluxEffectSnapshot.cs` - Complete rewrite to support override states and new parameters
- `FluxChangeDetailsWindow.cs` - Updated to display new parameters and override states
- No changes needed to `FluxPlayModeChangeDetector.cs` - it automatically uses the enhanced snapshot system

### Backward Compatibility
- Existing Volume Profiles continue to work unchanged
- The system gracefully handles profiles created before the Camera Motion parameters were added
- No migration or conversion required

## Success Criteria

✅ **All parameter values save correctly**
✅ **All parameter override states save correctly**  
✅ **Camera Motion parameters fully supported**
✅ **Advanced Datamoshing parameters work consistently**
✅ **Enhanced Corruption parameters (including Corruption Mask) save properly**
✅ **Mixed enabled/disabled parameter states preserve correctly**
✅ **Change detection works for both values and override states**
✅ **Detailed change window shows comprehensive information**
