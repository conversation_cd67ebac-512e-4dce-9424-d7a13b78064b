# Render Graph Pass Execution Order Analysis

## JPG Bitcrunch vs Flux Implementation Differences

---

## 🔍 **CRITICAL FINDINGS**

After analyzing both implementations, I've identified several key differences in how pass execution is handled between JPG Bitcrunch's legacy pipeline and Flux's Render Graph implementation.

---

## 📊 **JPG BITCRUNCH EXECUTION ORDER (Legacy URP)**

### **Fixed Sequential Execution:**

```
1. Create prevScreenTex (persistent, always available)
2. Create temporary textures (downscaledTex, blocksTex)
3. Pass 0: Downscale    (CameraTarget → downscaledTex)
4. Pass 1: Encode       (downscaledTex → blocksTex)
5. Pass 2: Decode       (blocksTex → downscaledTex)
6. SetGlobalTexture("_PrevScreen", prevScreenTex)  ← CRITICAL TIMING
7. Pass 3: UpscalePull  (downscaledTex → CameraTarget) [uses _PrevScreen]
8. Pass 5: CopyToPrev   (CameraTarget → prevScreenTex) [if reprojection enabled]
9. Release temporary textures
```

### **Key Characteristics:**

- **Synchronous execution**: Each pass completes before the next begins
- **Explicit texture management**: Manual creation/release of RenderTextures
- **Guaranteed availability**: `_PrevScreen` is set before UpscalePull needs it
- **No optimization**: Passes execute in exact order specified

---

## 🔧 **FLUX EXECUTION ORDER (Unity 6 Render Graph)**

### **Render Graph Managed Execution:**

```
1. RecordRenderGraph() - Define all passes and dependencies
   ├── AddDownscalePass()  (activeColorTexture → downscaledTexture)
   ├── AddEncodePass()     (downscaledTexture → encodedTexture)
   ├── AddDecodePass()     (encodedTexture → decodedTexture)
   ├── AddUpscalePass()    (decodedTexture → activeColorTexture) [needs prevFrameTexture]
   └── AddCopyToPrevPass() (activeColorTexture → prevFrameTexture)

2. Render Graph Execution (optimized, potentially reordered)
   - Automatic pass merging on mobile TBDR
   - Resource lifetime optimization
   - Potential pass reordering based on dependencies
```

### **Key Characteristics:**

- **Deferred execution**: Passes recorded first, executed later with optimization
- **Automatic resource management**: TextureHandle system manages lifetimes
- **Dependency-based ordering**: Execution order determined by declared dependencies
- **Optimization potential**: Pass merging, reordering, culling

---

## 🚨 **CRITICAL DIFFERENCES IDENTIFIED**

### **1. Texture Availability Timing**

**JPG Bitcrunch:**

```csharp
// Line 227: Create texture BEFORE it's needed
prevScreenTex = RenderTexture.GetTemporary(...);

// Line 245: Set global texture BEFORE upscale pass
cmd.SetGlobalTexture("_PrevScreen", prevScreenTex);

// Line 246: Upscale pass executes with texture available
RenderWith(downscaledTex, CameraTarget, cmd, mat, Pass.UpscalePull);
```

**Flux (Before Fix):**

```csharp
// Previous frame texture only created in CopyToPrev pass
// Upscale pass executed without previous frame data
// Chicken-and-egg problem: texture needed before it exists
```

**Flux (After Fix):**

```csharp
// Line 469: Create texture BEFORE passes are recorded
UpdatePersistentTexture(desc.width, desc.height, desc.format);

// Line 472: Import texture for use in passes
prevFrameTexture = renderGraph.ImportTexture(prevFrameRTHandle);
```

### **2. Pass Dependency Declaration**

**JPG Bitcrunch:**

- No explicit dependencies (sequential execution guaranteed)
- Texture availability managed manually
- Simple linear execution model

**Flux:**

```csharp
// Explicit dependency declarations
builder.UseTexture(passData.sourceTexture, AccessFlags.Read);
builder.SetRenderAttachment(passData.outputTexture, 0, AccessFlags.Write);
builder.UseTexture(passData.motionVectorTexture, AccessFlags.Read);
builder.UseTexture(passData.previousFrameTexture, AccessFlags.Read);
```

### **3. Optimization Impact**

**Potential Render Graph Optimizations:**

- **Pass Merging**: Multiple passes combined into single GPU dispatch
- **Resource Aliasing**: Textures reused when lifetimes don't overlap
- **Pass Culling**: Unused passes eliminated
- **Memory Optimization**: Reduced GPU memory usage

**Risk Areas:**

- **Temporal Dependencies**: Previous frame data might be optimized away
- **Global Texture Timing**: `SetGlobalTexture` timing might change
- **Pass Reordering**: Dependencies might not capture all requirements

---

## 🔍 **DEPENDENCY ANALYSIS**

### **Flux Pass Dependencies (Current Implementation):**

**Downscale Pass:**

- Input: `activeColorTexture` (Read)
- Output: `downscaledTexture` (Write)
- Dependencies: None

**Encode Pass:**

- Input: `downscaledTexture` (Read), `depthTexture` (Read, optional)
- Output: `encodedTexture` (Write)
- Dependencies: Downscale Pass

**Decode Pass:**

- Input: `encodedTexture` (Read)
- Output: `decodedTexture` (Write)
- Dependencies: Encode Pass

**Upscale Pass:**

- Input: `decodedTexture` (Read), `motionVectorTexture` (Read), `previousFrameTexture` (Read), `depthTexture` (Read, optional)
- Output: `activeColorTexture` (Write)
- Dependencies: Decode Pass, Previous Frame (temporal)

**CopyToPrev Pass:**

- Input: `activeColorTexture` (Read)
- Output: `prevFrameTexture` (Write)
- Dependencies: Upscale Pass

### **Missing Dependencies:**

- **Temporal dependency**: CopyToPrev from previous frame → Upscale current frame
- **Global texture timing**: When `_PrevScreen` is available to shader

---

## ⚠️ **POTENTIAL ISSUES**

### **1. Pass Merging Conflicts**

If Render Graph merges passes, the timing of when `_PrevScreen` becomes available might change.

### **2. Resource Aliasing**

Previous frame texture might be aliased with other textures, affecting temporal consistency.

### **3. Mobile TBDR Optimization**

On mobile TBDR GPUs, pass merging is more aggressive, potentially affecting datamosh behavior.

### **4. Implicit Dependencies**

The temporal dependency between frames isn't explicitly declared to Render Graph.

---

## ✅ **VERIFICATION STEPS**

### **1. Test Pass Execution Order**

- Add logging to each pass execution
- Verify passes execute in expected order
- Check timing of texture availability

### **2. Validate Temporal Consistency**

- Verify previous frame data is available when needed
- Test across multiple frames to ensure consistency
- Check for frame-to-frame variations

### **3. Mobile Testing**

- Test on mobile TBDR devices
- Verify behavior with aggressive pass merging
- Check for platform-specific issues

### **4. Optimization Impact**

- Test with different Render Graph optimization levels
- Verify behavior doesn't change with optimizations
- Monitor for performance vs correctness trade-offs

---

## 🎯 **RECOMMENDATIONS**

### **1. Explicit Temporal Dependencies**

Consider adding explicit temporal dependency declarations to Render Graph.

### **2. Pass Culling Prevention**

Use `builder.AllowPassCulling(false)` for critical temporal passes.

### **3. Resource Lifetime Management**

Ensure previous frame texture lifetime spans multiple frames correctly.

### **4. Platform-Specific Testing**

Test extensively on mobile TBDR devices where optimization is most aggressive.

---

## 💡 **CONCLUSION**

The main execution order difference is in **texture availability timing**. JPG Bitcrunch guarantees texture availability through manual management, while Flux relies on Render Graph's automatic optimization. The recent fix addresses the critical timing issue, but ongoing monitoring is needed to ensure Render Graph optimizations don't break temporal dependencies.

The pass execution order itself is correct - the issue was primarily the texture availability timing that has now been resolved.

---

## 🔧 **CURRENT STATUS**

### **✅ CONFIRMED CORRECT IMPLEMENTATIONS:**

1. **Pass Culling Prevention**: All Flux passes use `builder.AllowPassCulling(false)`
2. **Dependency Declaration**: Proper `UseTexture` and `SetRenderAttachment` calls
3. **Texture Availability**: Fixed with early `UpdatePersistentTexture` call
4. **Material Property Setting**: Done in execution function (Render Graph compliant)

### **🔍 AREAS FOR CONTINUED MONITORING:**

1. **Mobile TBDR Optimization**: May still affect pass merging behavior
2. **Temporal Consistency**: Cross-frame dependencies need ongoing validation
3. **Performance vs Correctness**: Balance between optimization and effect accuracy

### **🎯 NEXT STEPS:**

1. Test the fixed implementation with moving objects in Game View
2. Monitor for any frame-to-frame inconsistencies
3. Validate on mobile TBDR devices if available
4. Compare visual output with JPG Bitcrunch side-by-side

The Render Graph implementation should now match JPG Bitcrunch's execution characteristics while benefiting from automatic optimization.
