%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 178e7d2a793fae74bbfa69e510fd25ee, type: 3}
  m_Name: 172 Flux Drums - Scene 1 - 2 - Locking_CadanceTrack
  m_EditorClassIdentifier: 
  eventID: Locking
  events:
  - _eventID: Locking
    _startSample: 0
    _endSample: 0
    _legacyPayload: 
  - _eventID: Locking
    _startSample: 30767
    _endSample: 30767
    _legacyPayload: 
  - _eventID: Locking
    _startSample: 61534
    _endSample: 61534
    _legacyPayload: 
  - _eventID: Locking
    _startSample: 92302
    _endSample: 92302
    _legacyPayload: 
  - _eventID: Locking
    _startSample: 123069
    _endSample: 123069
    _legacyPayload: 
  - _eventID: Locking
    _startSample: 153836
    _endSample: 153836
    _legacyPayload: 
  - _eventID: Locking
    _startSample: 184603
    _endSample: 184603
    _legacyPayload: 
  - _eventID: Locking
    _startSample: 215371
    _endSample: 215371
    _legacyPayload: 
  - _eventID: Locking
    _startSample: 246139
    _endSample: 246139
    _legacyPayload: 
  - _eventID: Locking
    _startSample: 276906
    _endSample: 276906
    _legacyPayload: 
  - _eventID: Locking
    _startSample: 307673
    _endSample: 307673
    _legacyPayload: 
  - _eventID: Locking
    _startSample: 338441
    _endSample: 338441
    _legacyPayload: 
  - _eventID: Locking
    _startSample: 369209
    _endSample: 369209
    _legacyPayload: 
  - _eventID: Locking
    _startSample: 399976
    _endSample: 399976
    _legacyPayload: 
  - _eventID: Locking
    _startSample: 430743
    _endSample: 430743
    _legacyPayload: 
  - _eventID: Locking
    _startSample: 461511
    _endSample: 461511
    _legacyPayload: 
