%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Cave - Fog Ray
  m_Shader: {fileID: -6465566751694194690, guid: cfa23777b25174f04a5a1885a7eb9483, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords:
  - _BLEND_MODE_OVERWRITE
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses:
  - MOTIONVECTORS
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - __Gradient_Color_Map:
        m_Texture: {fileID: 5914111101166320495}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _BLEND_MODE: 0
    - _Borders: 0.6
    - _Edge: 0.5
    - _EdgeWidth: 1
    - _Fill: 1.59
    - _Height: 1
    - _Opacity: 0.06
    - _Polygon_Sides: 4
    - _QueueControl: 0
    - _QueueOffset: 0
    - _Range_Scale: 50
    - _Range_Scale_1: 18.1
    - _Roundness: 1
    - _Sharpness: 0.351
    - _Vignette: 3.85
    - _Width: 1
    m_Colors:
    - _Dissolve_Camera_Distance: {r: 0, g: 0.66555834, b: 0, a: 0}
    - _Range: {r: 0, g: 1.3232317, b: 0, a: 0}
  m_BuildTextureStacks: []
  m_AllowLocking: 1
--- !u!28 &5914111101166320495
Texture2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: z___Gradient_Color_MapTex{"mode":0,"colorKeys":[{"color":{"r":0.8224902153015137,"g":0.9515133500099182,"b":0.9528301954269409,"a":1.0},"time":0.010833906009793282},{"color":{"r":0.8679245114326477,"g":0.8679245114326477,"b":0.8679245114326477,"a":1.0},"time":1.0}],"alphaKeys":[{"alpha":1.0,"time":0.0},{"alpha":1.0,"time":1.0}]}
  m_ImageContentsHash:
    serializedVersion: 2
    Hash: 00000000000000000000000000000000
  m_IsAlphaChannelOptional: 0
  serializedVersion: 3
  m_Width: 256
  m_Height: 1
  m_CompleteImageSize: 1024
  m_MipsStripped: 0
  m_TextureFormat: 5
  m_MipCount: 1
  m_IsReadable: 1
  m_IsPreProcessed: 0
  m_IgnoreMipmapLimit: 0
  m_MipmapLimitGroupName: 
  m_StreamingMipmaps: 0
  m_StreamingMipmapsPriority: 0
  m_VTOnly: 0
  m_AlphaIsTransparency: 0
  m_ImageCount: 1
  m_TextureDimension: 2
  m_TextureSettings:
    serializedVersion: 2
    m_FilterMode: 1
    m_Aniso: 1
    m_MipBias: 0
    m_WrapU: 1
    m_WrapV: 1
    m_WrapW: 1
  m_LightmapFormat: 0
  m_ColorSpace: 1
  m_PlatformBlob: 
  image data: 1024
  _typelessdata: ffd2f3f3ffd2f3f3ffd2f3f3ffd2f3f3ffd2f3f3ffd2f2f3ffd2f2f3ffd2f2f3ffd2f2f3ffd2f2f2ffd2f2f2ffd2f2f2ffd2f2f2ffd2f2f2ffd2f2f2ffd2f2f2ffd2f2f2ffd2f1f2ffd2f1f2ffd2f1f2ffd3f1f1ffd3f1f1ffd3f1f1ffd3f1f1ffd3f1f1ffd3f1f1ffd3f1f1ffd3f1f1ffd3f1f1ffd3f0f1ffd3f0f1ffd3f0f1ffd3f0f0ffd3f0f0ffd3f0f0ffd3f0f0ffd3f0f0ffd3f0f0ffd3f0f0ffd3f0f0ffd3eff0ffd3eff0ffd4eff0ffd4eff0ffd4efefffd4efefffd4efefffd4efefffd4efefffd4efefffd4efefffd4efefffd4eeefffd4eeefffd4eeefffd4eeeeffd4eeeeffd4eeeeffd4eeeeffd4eeeeffd4eeeeffd4eeeeffd4eeeeffd5eeeeffd5edeeffd5edeeffd5edeeffd5ededffd5ededffd5ededffd5ededffd5ededffd5ededffd5ededffd5ededffd5ededffd5ecedffd5ecedffd5ecedffd5ececffd5ececffd5ececffd5ececffd5ececffd5ececffd6ececffd6ececffd6ececffd6ebecffd6ebecffd6ebebffd6ebebffd6ebebffd6ebebffd6ebebffd6ebebffd6ebebffd6ebebffd6ebebffd6ebebffd6eaebffd6eaebffd6eaeaffd6eaeaffd6eaeaffd6eaeaffd6eaeaffd7eaeaffd7eaeaffd7eaeaffd7eaeaffd7e9eaffd7e9eaffd7e9eaffd7e9e9ffd7e9e9ffd7e9e9ffd7e9e9ffd7e9e9ffd7e9e9ffd7e9e9ffd7e9e9ffd7e9e9ffd7e8e9ffd7e8e9ffd7e8e8ffd7e8e8ffd7e8e8ffd7e8e8ffd8e8e8ffd8e8e8ffd8e8e8ffd8e8e8ffd8e8e8ffd8e8e8ffd8e7e8ffd8e7e8ffd8e7e7ffd8e7e7ffd8e7e7ffd8e7e7ffd8e7e7ffd8e7e7ffd8e7e7ffd8e7e7ffd8e7e7ffd8e7e7ffd8e6e7ffd8e6e7ffd8e6e6ffd8e6e6ffd9e6e6ffd9e6e6ffd9e6e6ffd9e6e6ffd9e6e6ffd9e6e6ffd9e6e6ffd9e6e6ffd9e5e6ffd9e5e5ffd9e5e5ffd9e5e5ffd9e5e5ffd9e5e5ffd9e5e5ffd9e5e5ffd9e5e5ffd9e5e5ffd9e5e5ffd9e5e5ffd9e4e5ffdae4e4ffdae4e4ffdae4e4ffdae4e4ffdae4e4ffdae4e4ffdae4e4ffdae4e4ffdae4e4ffdae4e4ffdae3e4ffdae3e4ffdae3e3ffdae3e3ffdae3e3ffdae3e3ffdae3e3ffdae3e3ffdae3e3ffdae3e3ffdae3e3ffdae3e3ffdbe2e3ffdbe2e2ffdbe2e2ffdbe2e2ffdbe2e2ffdbe2e2ffdbe2e2ffdbe2e2ffdbe2e2ffdbe2e2ffdbe2e2ffdbe2e2ffdbe1e2ffdbe1e1ffdbe1e1ffdbe1e1ffdbe1e1ffdbe1e1ffdbe1e1ffdbe1e1ffdbe1e1ffdbe1e1ffdce1e1ffdce1e1ffdce0e0ffdce0e0ffdce0e0ffdce0e0ffdce0e0ffdce0e0ffdce0e0ffdce0e0ffdce0e0ffdce0e0ffdce0e0ffdce0e0ffdcdfdfffdcdfdfffdcdfdfffdcdfdfffdcdfdfffdcdfdfffdcdfdfffdcdfdfffdddfdfffdddfdfffdddfdfffdddfdfffdddedeffdddedeffdddedeffdddedeffdddedeffdddedeffdddedeffdddedeffdddedeffdddedeffdddedeffddddddffddddddffdddddd
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!114 &6163086304218336242
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0353a89b1f911e48b9e16bdc9f2e058, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 9
