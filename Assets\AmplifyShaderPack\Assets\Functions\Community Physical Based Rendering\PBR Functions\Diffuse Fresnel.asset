%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: Diffuse Fresnel
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor v1.9.5.1\n// Available at the
    Unity Asset Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=19501\nNode;AmplifyShaderEditor.CommentaryNode;98;-1920,-128;Inherit;False;1946;894.5089;Math;24;78;35;99;97;75;96;27;25;16;82;42;76;77;74;83;45;18;0;26;20;81;53;94;101;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;88;-3008,-128;Inherit;False;1022.683;385.9627;Dot
    Products;5;89;93;91;90;105;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;94;-1216,-80;Inherit;False;194;142;NdotH;1;95;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;102;-1920,-448;Inherit;False;785.7505;291.7751;DESF
    Diffuse Fresnel Node;1;103;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;91;-2272,-48;Inherit;True;NdotH;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;93;-2944,-48;Inherit;False;Normal;3;1;False;1;0;FLOAT3;0,0,1;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.ScaleAndOffsetNode;53;-1376,224;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;9;False;2;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.ScaleAndOffsetNode;81;-1136,144;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;2;False;2;FLOAT;-1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;20;-912,144;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SaturateNode;26;-352,-32;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;95;-1200,-32;Inherit;False;91;NdotH;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FaceVariableNode;18;-1280,144;Inherit;False;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;45;-880,304;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0.5;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionNode;83;-1552,304;Inherit;False;Square;-1;;6;fea980a1f68019543b2cd91d506986e8;0;1;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.ScaleAndOffsetNode;74;-1376,368;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;0.25;False;2;FLOAT;0.5;False;1;FLOAT;0\nNode;AmplifyShaderEditor.ScaleAndOffsetNode;77;-1376,496;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;-0.25;False;2;FLOAT;0.75;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SaturateNode;42;-1696,304;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;82;-960,256;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.PowerNode;16;-736,-32;Inherit;False;False;2;0;FLOAT;0;False;1;FLOAT;5;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;25;-912,-32;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.ScaleAndOffsetNode;27;-576,-32;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;1;False;2;FLOAT;0.5;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;75;-624,352;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;97;-624,80;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.StickyNoteNode;99;-1872,384;Inherit;False;202.2734;113.4899;Smoothness;;0,0,0,1;;0;0\nNode;AmplifyShaderEditor.FunctionInput;35;-1872,304;Inherit;False;Smoothness;1;2;False;1;0;FLOAT;0.5;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;78;-1312,624;Inherit;False;Bias;1;3;False;1;0;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.StickyNoteNode;101;-1120,496;Inherit;False;202.2734;113.4899;Bias;;0,0,0,1;;0;0\nNode;AmplifyShaderEditor.FunctionSwitch;76;-1120,368;Inherit;False;Bias;False;1;3;0;Scaling;Scale_Inv;Custom;Object;-1;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;96;-656,384;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.StickyNoteNode;103;-1535.402,-368;Inherit;False;349.6386;150.0229;;;0,0,0,1;;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;104;-1920,-640;Inherit;False;558.8756;147.9872;Credits;;0,0,0,0.5019608;Method
    by Jordan Stevens$Link: https://www.jordanstevenstechart.com/physically-based-rendering$$Node
    Made by Tiber Legacy$Special Thanks to Dawie;0;0\nNode;AmplifyShaderEditor.FunctionSwitch;90;-2560,-48;Inherit;False;Custom
    Dot Products;True;0;2;1;In 0;In 1;Object;-1;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionNode;105;-2784,-48;Inherit;False;NdotH;-1;;8;fadb6690f179c1448859861f76ebb061;7,27,1,373,0,451,0,463,0,460,0,461,0,452,0;6;230;FLOAT3;0,0,1;False;443;FLOAT3;0,0,0;False;462;FLOAT3;0,0,0;False;454;FLOAT3;0,0,0;False;453;FLOAT3;0,0,0;False;417;FLOAT;1E-37;False;1;FLOAT;109\nNode;AmplifyShaderEditor.FunctionInput;89;-2720,32;Inherit;False;NdotH;1;0;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;0;-208,-32;Inherit;True;True;-1;Out;0;False;1;0;FLOAT;0;False;1;FLOAT;0\nWireConnection;91;0;90;0\nWireConnection;53;0;83;0\nWireConnection;81;0;18;0\nWireConnection;20;0;81;0\nWireConnection;20;1;82;0\nWireConnection;26;0;27;0\nWireConnection;45;0;83;0\nWireConnection;45;1;76;0\nWireConnection;83;2;42;0\nWireConnection;74;0;83;0\nWireConnection;77;0;83;0\nWireConnection;42;0;35;0\nWireConnection;82;0;53;0\nWireConnection;16;0;25;0\nWireConnection;16;1;20;0\nWireConnection;25;0;95;0\nWireConnection;27;0;16;0\nWireConnection;27;1;45;0\nWireConnection;27;2;97;0\nWireConnection;75;0;96;0\nWireConnection;97;0;75;0\nWireConnection;76;0;74;0\nWireConnection;76;1;77;0\nWireConnection;76;2;78;0\nWireConnection;96;0;76;0\nWireConnection;90;0;105;109\nWireConnection;90;1;89;0\nWireConnection;105;230;93;0\nWireConnection;0;0;26;0\nASEEND*/\n//CHKSM=E96C85A3160DD3AE1A3D8037EE8E4012A3BABC22"
  m_functionName: 
  m_description: 'DESF Diffuse Fresnel Node



    Default is set with NdotH

'
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 0
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_customEdited: 0
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 0
  m_headerStyle: 2
  m_headerColor: {r: 0.7529412, g: 0.6784314, b: 0, a: 1}
  m_customNodeCategory: Amplify Shader Pack
  m_previewPosition: 0
  m_hidden: 0
  m_url: 
