<?xml version="1.0" encoding="UTF-8"?><package><identifier v="Unsaved Package"/><formatVersion v="1.1.0.202302"/><updaterVersion v="1.1.0.202302"/><fileUID v="{84b9ec4e-af4c-4574-952a-e3d014b1549e}"/><versionUID v="0"/><dependencies><dependency><filename v="sbs://pattern_tile_sampler.sbs"/><uid v="1449399972"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://edge_detect.sbs"/><uid v="1407202483"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_crystal_1.sbs"/><uid v="1407271237"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://blur_hq.sbs"/><uid v="1299236171"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://flood_fill_2.sbs"/><uid v="1323881949"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://slope_blur.sbs"/><uid v="1449402040"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_perlin_noise.sbs"/><uid v="1407332030"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_creased.sbs"/><uid v="1487699857"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_clouds_2.sbs"/><uid v="1372403415"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://hbao_2.sbs"/><uid v="1297162667"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://curvature_smooth.sbs"/><uid v="1449590280"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://height_blend.sbs"/><uid v="1449623608"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://auto_levels.sbs"/><uid v="1383786570"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://histogram_scan.sbs"/><uid v="1149349207"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://waveform_1.sbs"/><uid v="1416542616"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://non_uniform_blur.sbs"/><uid v="1290776959"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://gradient_linear_1.sbs"/><uid v="1357065436"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency></dependencies><content><graph><identifier v="rock_with_grass"/><uid v="1487420008"/><graphtype v="material"/><graphOutputs><graphoutput><identifier v="basecolor"/><uid v="1213284336"/><attributes><label v="Base Color"/></attributes><usages><usage><components v="RGBA"/><name v="baseColor"/></usage></usages><group v="Material"/></graphoutput><graphoutput><identifier v="normal"/><uid v="1213284338"/><attributes><label v="Normal"/></attributes><usages><usage><components v="RGBA"/><name v="normal"/></usage></usages><group v="Material"/></graphoutput><graphoutput><identifier v="roughness"/><uid v="1213284340"/><attributes><label v="Roughness"/></attributes><usages><usage><components v="RGBA"/><name v="roughness"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput><graphoutput><identifier v="metallic"/><uid v="1213284342"/><attributes><label v="Metallic"/></attributes><usages><usage><components v="RGBA"/><name v="metallic"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput><graphoutput><identifier v="height"/><uid v="1279137031"/><attributes><label v="Height"/></attributes><usages><usage><components v="RGBA"/><name v="height"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput><graphoutput><identifier v="ambientocclusion"/><uid v="1359211721"/><attributes><label v="Ambient Occlusion"/></attributes><usages><usage><components v="RGBA"/><name v="ambientOcclusion"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput></graphOutputs><compNodes><compNode><uid v="1213284337"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1487719592"/><connRefOutput v="1487719593"/></connection></connections><GUILayout><gpos v="-48 -240 0"/></GUILayout><compImplementation><compOutputBridge><output v="1213284336"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1213284339"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1359211383"/><connRefOutput v="1359211384"/></connection></connections><GUILayout><gpos v="-48 -80 0"/></GUILayout><compImplementation><compOutputBridge><output v="1213284338"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1213284341"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1359211391"/><connRefOutput v="1359211392"/></connection></connections><GUILayout><gpos v="-48 80 0"/></GUILayout><compImplementation><compOutputBridge><output v="1213284340"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1213284343"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1359211407"/><connRefOutput v="1359211408"/></connection></connections><GUILayout><gpos v="-48 240 0"/></GUILayout><compImplementation><compOutputBridge><output v="1213284342"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1279137030"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1487437633"/><connRefOutput v="1487437634"/></connection></connections><GUILayout><gpos v="-48 848 0"/></GUILayout><compImplementation><compOutputBridge><output v="1279137031"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1359211383"/><connections><connection><identifier v="input1"/><connRef v="1487437633"/><connRefOutput v="1487437634"/></connection></connections><GUILayout><gpos v="-848 16 0"/></GUILayout><compOutputs><compOutput><uid v="1359211384"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="normal"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="10"/></paramValue></parameter><parameter><name v="input2alpha"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1359211391"/><GUILayout><gpos v="-208 80 0"/></GUILayout><compOutputs><compOutput><uid v="1359211392"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="uniform"/><parameters><parameter><name v="colorswitch"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="outputcolor"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.25 0.25 0.25 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1359211407"/><GUILayout><gpos v="-208 240 0"/></GUILayout><compOutputs><compOutput><uid v="1359211408"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="uniform"/><parameters><parameter><name v="colorswitch"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1359211720"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1487703978"/><connRefOutput v="1487703979"/></connection></connections><GUILayout><gpos v="-48 624 0"/></GUILayout><compImplementation><compOutputBridge><output v="1359211721"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1487424389"/><GUILayout><gpos v="-4656 -190.219666 0"/></GUILayout><compOutputs><compOutput><uid v="1487424390"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1449399972"/><parameters><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="8"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="9"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="6.82999992"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.370000005"/></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="4.71999979"/></paramValue></parameter><parameter><name v="color_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.629999995"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487424390"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487424666"/><connections><connection><identifier v="input"/><connRef v="1487424909"/><connRefOutput v="1487424910"/></connection></connections><GUILayout><gpos v="-4161.92627 -176 0"/></GUILayout><compOutputs><compOutput><uid v="1487424667"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///edge_detect?dependency=1407202483"/><parameters><parameter><name v="edge_width"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="3.6099999"/></paramValue></parameter><parameter><name v="edge_roundness"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.34000003"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487424667"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487424909"/><connections><connection><identifier v="input1"/><connRef v="1487424389"/><connRefOutput v="1487424390"/></connection><connection><identifier v="inputintensity"/><connRef v="1487432349"/><connRefOutput v="1487432350"/></connection></connections><GUILayout><gpos v="-4337.08008 -176 0"/></GUILayout><compOutputs><compOutput><uid v="1487424910"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="directionalwarp"/><parameters><parameter><name v="warpangle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0451944433"/></paramValue></parameter><parameter><name v="randomseed"/><relativeTo v="2"/><paramValue><constantValueInt32 v="10"/></paramValue></parameter><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="10.5799999"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487424943"/><GUILayout><gpos v="-4609.92627 48 0"/></GUILayout><compOutputs><compOutput><uid v="1487424944"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///crystal_1?dependency=1407271237"/><parameters><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueInt32 v="7"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487424944"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487425265"/><connections><connection><identifier v="Source"/><connRef v="1487424666"/><connRefOutput v="1487424667"/></connection></connections><GUILayout><gpos v="-4033.92627 -176 0"/></GUILayout><compOutputs><compOutput><uid v="1487425266"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1299236171"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="4.17999983"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487425266"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487432349"/><connections><connection><identifier v="Source"/><connRef v="1487424943"/><connRefOutput v="1487424944"/></connection></connections><GUILayout><gpos v="-4449.92627 48 0"/></GUILayout><compOutputs><compOutput><uid v="1487432350"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1299236171"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="3.06999993"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487432350"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487436181"/><connections><connection><identifier v="mask"/><connRef v="1487425265"/><connRefOutput v="1487425266"/></connection></connections><GUILayout><gpos v="-3745.92627 -159.07608 0"/></GUILayout><compOutputs><compOutput><uid v="1487436182"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters><parameter><name v="colorswitch"/><paramValue><constantValueBool v="0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487442222"/><connections><connection><identifier v="mask"/><connRef v="1487425265"/><connRefOutput v="1487425266"/></connection></connections><GUILayout><gpos v="-3937.92627 48.6608887 0"/></GUILayout><compOutputs><compOutput><uid v="1487442223"/><comptype v="1"/></compOutput><compOutput><uid v="1487442224"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill?dependency=1323881949"/><parameters/><outputBridgings><outputBridging><uid v="1487442223"/><identifier v="output"/></outputBridging><outputBridging><uid v="1487442224"/><identifier v="referenceMap"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487442502"/><connections><connection><identifier v="input"/><connRef v="1487442222"/><connRefOutput v="1487442223"/></connection></connections><GUILayout><gpos v="-3792.76196 65.050415 0"/></GUILayout><compOutputs><compOutput><uid v="1487442503"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487442503"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487442713"/><connections><connection><identifier v="source"/><connRef v="1487442502"/><connRefOutput v="1487442503"/></connection><connection><identifier v="mask"/><connRef v="1487425265"/><connRefOutput v="1487425266"/></connection></connections><GUILayout><gpos v="-3649.92627 51.6407776 0"/></GUILayout><compOutputs><compOutput><uid v="1487442714"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1487442832"/><connections><connection><identifier v="input"/><connRef v="1487442833"/><connRefOutput v="1487442223"/></connection></connections><GUILayout><gpos v="-3792.76196 272 0"/></GUILayout><compOutputs><compOutput><uid v="1487442503"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="angle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.305555552"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487442503"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487442833"/><connections><connection><identifier v="mask"/><connRef v="1487425265"/><connRefOutput v="1487425266"/></connection></connections><GUILayout><gpos v="-3937.92627 272 0"/></GUILayout><compOutputs><compOutput><uid v="1487442223"/><comptype v="1"/></compOutput><compOutput><uid v="1487442224"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill?dependency=1323881949"/><parameters/><outputBridgings><outputBridging><uid v="1487442223"/><identifier v="output"/></outputBridging><outputBridging><uid v="1487442224"/><identifier v="referenceMap"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487442834"/><connections><connection><identifier v="source"/><connRef v="1487442832"/><connRefOutput v="1487442503"/></connection><connection><identifier v="mask"/><connRef v="1487425265"/><connRefOutput v="1487425266"/></connection></connections><GUILayout><gpos v="-3649.92627 272 0"/></GUILayout><compOutputs><compOutput><uid v="1487442714"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1487428995"/><connections><connection><identifier v="source"/><connRef v="1487428996"/><connRefOutput v="1487442503"/></connection><connection><identifier v="mask"/><connRef v="1487425265"/><connRefOutput v="1487425266"/></connection></connections><GUILayout><gpos v="-3635.09058 496 0"/></GUILayout><compOutputs><compOutput><uid v="1487442714"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1487428996"/><connections><connection><identifier v="input"/><connRef v="1487428997"/><connRefOutput v="1487442223"/></connection></connections><GUILayout><gpos v="-3777.92627 496 0"/></GUILayout><compOutputs><compOutput><uid v="1487442503"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="angle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.36944443"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487442503"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487428997"/><connections><connection><identifier v="mask"/><connRef v="1487425265"/><connRefOutput v="1487425266"/></connection></connections><GUILayout><gpos v="-3923.09058 496 0"/></GUILayout><compOutputs><compOutput><uid v="1487442223"/><comptype v="1"/></compOutput><compOutput><uid v="1487442224"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill?dependency=1323881949"/><parameters/><outputBridgings><outputBridging><uid v="1487442223"/><identifier v="output"/></outputBridging><outputBridging><uid v="1487442224"/><identifier v="referenceMap"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487429361"/><connections><connection><identifier v="destination"/><connRef v="1487436181"/><connRefOutput v="1487436182"/></connection><connection><identifier v="source"/><connRef v="1487436402"/><connRefOutput v="1487436403"/></connection></connections><GUILayout><gpos v="-3361.92627 -161.088806 0"/></GUILayout><compOutputs><compOutput><uid v="1487429362"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487436402"/><connections><connection><identifier v="input1"/><connRef v="1487442713"/><connRefOutput v="1487442714"/></connection></connections><GUILayout><gpos v="-3521.92627 48 0"/></GUILayout><compOutputs><compOutput><uid v="1487436403"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.569164276 0.569164276 0.569164276 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487436540"/><connections><connection><identifier v="destination"/><connRef v="1487429361"/><connRefOutput v="1487429362"/></connection><connection><identifier v="source"/><connRef v="1487436721"/><connRefOutput v="1487436722"/></connection></connections><GUILayout><gpos v="-3233.92627 144 0"/></GUILayout><compOutputs><compOutput><uid v="1487436541"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487436721"/><connections><connection><identifier v="input1"/><connRef v="1487442834"/><connRefOutput v="1487442714"/></connection></connections><GUILayout><gpos v="-3521.92627 272 0"/></GUILayout><compOutputs><compOutput><uid v="1487436722"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.240634009 0.240634009 0.240634009 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487436869"/><connections><connection><identifier v="destination"/><connRef v="1487436540"/><connRefOutput v="1487436541"/></connection><connection><identifier v="source"/><connRef v="1487437073"/><connRefOutput v="1487437074"/></connection></connections><GUILayout><gpos v="-3041.92627 304 0"/></GUILayout><compOutputs><compOutput><uid v="1487436870"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487437073"/><connections><connection><identifier v="input1"/><connRef v="1487428995"/><connRefOutput v="1487442714"/></connection></connections><GUILayout><gpos v="-3489.92627 496 0"/></GUILayout><compOutputs><compOutput><uid v="1487437074"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.427953899 0.427953899 0.427953899 0.5"/></paramValue></parameter><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.246397689 0.246397689 0.246397689 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487437633"/><connections><connection><identifier v="input1"/><connRef v="1487713718"/><connRefOutput v="1487713719"/></connection></connections><GUILayout><gpos v="-1602.77124 208 0"/></GUILayout><compOutputs><compOutput><uid v="1487437634"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1487497686"/><connections><connection><identifier v="Source"/><connRef v="1487436869"/><connRefOutput v="1487436870"/></connection><connection><identifier v="Effect"/><connRef v="1487498286"/><connRefOutput v="1487498287"/></connection></connections><GUILayout><gpos v="-2864 400 0"/></GUILayout><compOutputs><compOutput><uid v="1487497687"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///slope_blur_grayscale_2?dependency=1449402040"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="32"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.449999988"/></paramValue></parameter><parameter><name v="mode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487497687"/><identifier v="Slope_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487498286"/><GUILayout><gpos v="-3026.17139 592 0"/></GUILayout><compOutputs><compOutput><uid v="1487498287"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///perlin_noise?dependency=1407332030"/><parameters><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueInt32 v="14"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487498287"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487499217"/><connections><connection><identifier v="destination"/><connRef v="1487436869"/><connRefOutput v="1487436870"/></connection><connection><identifier v="source"/><connRef v="1487499314"/><connRefOutput v="1487499315"/></connection></connections><GUILayout><gpos v="-2625.87598 208 0"/></GUILayout><compOutputs><compOutput><uid v="1487499218"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487499314"/><connections><connection><identifier v="input1"/><connRef v="1487497686"/><connRefOutput v="1487497687"/></connection></connections><GUILayout><gpos v="-2722.20532 400 0"/></GUILayout><compOutputs><compOutput><uid v="1487499315"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="randomseed"/><relativeTo v="2"/><paramValue><constantValueInt32 v="10"/></paramValue></parameter><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.678707242 0.678707242 0.678707242 0.5"/></paramValue></parameter><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.18871595 0.18871595 0.18871595 0"/></paramValue></parameter><parameter><name v="levelinlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.0553691275 0.0553691275 0.0553691275 0"/></paramValue></parameter><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.833892643 0.833892643 0.833892643 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487690913"/><connections><connection><identifier v="input1"/><connRef v="1487499217"/><connRefOutput v="1487499218"/></connection><connection><identifier v="inputintensity"/><connRef v="1487499217"/><connRefOutput v="1487499218"/></connection></connections><GUILayout><gpos v="-2480 208 0"/></GUILayout><compOutputs><compOutput><uid v="1487690914"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="directionalwarp"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="10.4499998"/></paramValue></parameter><parameter><name v="warpangle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.159999996"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487693524"/><connections><connection><identifier v="destination"/><connRef v="1487690913"/><connRefOutput v="1487690914"/></connection></connections><GUILayout><gpos v="-2320 208 0"/></GUILayout><compOutputs><compOutput><uid v="1487693525"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0199999996"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487694525"/><connections><connection><identifier v="input1"/><connRef v="1487693524"/><connRefOutput v="1487693525"/></connection></connections><GUILayout><gpos v="-1616 -240 0"/></GUILayout><compOutputs><compOutput><uid v="1487694526"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1487694620"/><paramsArrayCells><paramsArrayCell><uid v="1487695180"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.607438028 0.568618 0.534252763 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487695181"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.38818568"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.541322291 0.525754631 0.5119735 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487695182"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.953586459"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.80578512 0.751714289 0.703848422 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487695183"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.824894547"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.586776853 0.549277186 0.516080856 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1487699725"/><GUILayout><gpos v="-2544 -511.857727 0"/></GUILayout><compOutputs><compOutput><uid v="1487699726"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///perlin_noise?dependency=1407332030"/><parameters><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487699726"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487699760"/><connections><connection><identifier v="Source"/><connRef v="1487699725"/><connRefOutput v="1487699726"/></connection><connection><identifier v="Effect"/><connRef v="1487700046"/><connRefOutput v="1487700047"/></connection></connections><GUILayout><gpos v="-2384 -496 0"/></GUILayout><compOutputs><compOutput><uid v="1487699761"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///slope_blur_grayscale_2?dependency=1449402040"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="32"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487699761"/><identifier v="Slope_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487700046"/><GUILayout><gpos v="-2544 -368 0"/></GUILayout><compOutputs><compOutput><uid v="1487700047"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///creased?dependency=1487699857"/><parameters><parameter><name v="warp_intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487700047"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487700388"/><connections><connection><identifier v="Source"/><connRef v="1487699760"/><connRefOutput v="1487699761"/></connection><connection><identifier v="Effect"/><connRef v="1487700934"/><connRefOutput v="1487700935"/></connection></connections><GUILayout><gpos v="-2256 -496 0"/></GUILayout><compOutputs><compOutput><uid v="1487700389"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///slope_blur_grayscale_2?dependency=1449402040"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="32"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="6.23999977"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487700389"/><identifier v="Slope_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487700934"/><GUILayout><gpos v="-2384 -368 0"/></GUILayout><compOutputs><compOutput><uid v="1487700935"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///clouds_2?dependency=1372403415"/><parameters/><outputBridgings><outputBridging><uid v="1487700935"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487701253"/><connections><connection><identifier v="Source"/><connRef v="1487700388"/><connRefOutput v="1487700389"/></connection></connections><GUILayout><gpos v="-2128 -496 0"/></GUILayout><compOutputs><compOutput><uid v="1487701254"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1299236171"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.689999998"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487701254"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487701428"/><connections><connection><identifier v="Source"/><connRef v="1487701253"/><connRefOutput v="1487701254"/></connection><connection><identifier v="Effect"/><connRef v="1487701253"/><connRefOutput v="1487701254"/></connection></connections><GUILayout><gpos v="-2000 -496 0"/></GUILayout><compOutputs><compOutput><uid v="1487701429"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///slope_blur_grayscale_2?dependency=1449402040"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="32"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="12.9899998"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487701429"/><identifier v="Slope_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487701488"/><connections><connection><identifier v="input1"/><connRef v="1487701428"/><connRefOutput v="1487701429"/></connection></connections><GUILayout><gpos v="-1840 -496 0"/></GUILayout><compOutputs><compOutput><uid v="1487701489"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters><parameter><name v="addressingrepeat"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="colorswitch"/><paramValue><constantValueBool v="1"/></paramValue></parameter></parameters><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1487701548"/><paramsArrayCells><paramsArrayCell><uid v="1487701981"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.592156887 0.509803951 0.431372553 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487701982"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.0363128483"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.556862772 0.431372553 0.321568638 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487701983"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.0698323995"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.490196079 0.368627459 0.258823544 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487701984"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.078212291"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.635294139 0.505882382 0.392156869 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487701985"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.108938545"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.494117647 0.376470596 0.274509817 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487701986"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.201117307"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.600000024 0.494117647 0.403921574 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487701987"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.223463684"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.490196079 0.388235301 0.298039228 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487701988"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.237430155"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.576470613 0.482352942 0.396078438 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487701989"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.251396626"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.509803951 0.403921574 0.282352954 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487701990"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.405027926"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.592156887 0.494117647 0.380392164 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487701991"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.513966441"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.494117647 0.392156869 0.282352954 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487701992"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.567039073"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.596078455 0.490196079 0.400000006 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487701993"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.606145263"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.517647088 0.403921574 0.290196091 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487701994"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.656424582"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.619607866 0.501960814 0.368627459 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487701995"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.681564212"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.533333361 0.403921574 0.305882365 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487701996"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.768156409"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.635294139 0.517647088 0.388235301 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487701997"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.793296039"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.490196079 0.380392164 0.262745112 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487701998"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.913407803"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.53725493 0.454901963 0.376470596 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487701999"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.955307245"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.643137276 0.513725519 0.407843143 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487702000"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.986033499"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.650980413 0.533333361 0.423529416 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487702001"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.991620064"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.203921571 0.125490203 0.0509803928 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487702002"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.997206688"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.215686277 0.192156866 0.168627456 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1487702288"/><connections><connection><identifier v="destination"/><connRef v="1487694525"/></connection><connection><identifier v="source"/><connRef v="1487701488"/><connRefOutput v="1487701489"/></connection></connections><GUILayout><gpos v="-1456 -240 0"/></GUILayout><compOutputs><compOutput><uid v="1487702289"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.200000003"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487703978"/><connections><connection><identifier v="input"/><connRef v="1487437633"/><connRefOutput v="1487437634"/></connection></connections><GUILayout><gpos v="-1168 272 0"/></GUILayout><compOutputs><compOutput><uid v="1487703979"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///hbao?dependency=1297162667"/><parameters/><outputBridgings><outputBridging><uid v="1487703979"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487704933"/><connections><connection><identifier v="input1"/><connRef v="1487703978"/><connRefOutput v="1487703979"/></connection></connections><GUILayout><gpos v="-1008 176 0"/></GUILayout><compOutputs><compOutput><uid v="1487704934"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1487704959"/><paramsArrayCells><paramsArrayCell><uid v="1487704967"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.768627465 0.870588243 0.937254906 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487704968"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.295652181 0.292214364 0.292214364 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1487705099"/><connections><connection><identifier v="source"/><connRef v="1487704933"/><connRefOutput v="1487704934"/></connection><connection><identifier v="destination"/><connRef v="1487717578"/><connRefOutput v="1487717579"/></connection></connections><GUILayout><gpos v="-1008 -240 0"/></GUILayout><compOutputs><compOutput><uid v="1487705100"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="9"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.409999996"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487706181"/><connections><connection><identifier v="input"/><connRef v="1359211383"/></connection></connections><GUILayout><gpos v="-720 208 0"/></GUILayout><compOutputs><compOutput><uid v="1487706182"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///curvature_smooth?dependency=1449590280"/><parameters/><outputBridgings><outputBridging><uid v="1487706182"/><identifier v="height"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487706200"/><connections><connection><identifier v="input1"/><connRef v="1487706181"/><connRefOutput v="1487706182"/></connection></connections><GUILayout><gpos v="-592 208 0"/></GUILayout><compOutputs><compOutput><uid v="1487706201"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1487706227"/><paramsArrayCells><paramsArrayCell><uid v="1487706228"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.566455662"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0 0 0 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487706229"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.607594907"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="1 1 1 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1487706483"/><connections><connection><identifier v="destination"/><connRef v="1487705099"/><connRefOutput v="1487705100"/></connection><connection><identifier v="source"/><connRef v="1487706200"/><connRefOutput v="1487706201"/></connection></connections><GUILayout><gpos v="-688 -240 0"/></GUILayout><compOutputs><compOutput><uid v="1487706484"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.170000002"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487713718"/><connections><connection><identifier v="height_bottom"/><connRef v="1487693524"/><connRefOutput v="1487693525"/></connection><connection><identifier v="height_top"/><connRef v="1490522778"/><connRefOutput v="1372395846"/></connection></connections><GUILayout><gpos v="-1954.65295 208 0"/></GUILayout><compOutputs><compOutput><uid v="1487713719"/><comptype v="2"/></compOutput><compOutput><uid v="1487713720"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///height_blend?dependency=1449623608"/><parameters><parameter><name v="height_position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0799999982"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487713719"/><identifier v="blended_height"/></outputBridging><outputBridging><uid v="1487713720"/><identifier v="height_mask"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487717512"/><connections><connection><identifier v="input1"/><connRef v="1490522778"/><connRefOutput v="1372395846"/></connection></connections><GUILayout><gpos v="-1488 961.949219 0"/></GUILayout><compOutputs><compOutput><uid v="1487717513"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1487717527"/><paramsArrayCells><paramsArrayCell><uid v="1490886803"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.704693675"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.180053443 0.243801653 0.167430043 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1490886804"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.937523425"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.309624732 0.508264482 0.0275565051 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1487717578"/><connections><connection><identifier v="destination"/><connRef v="1487702288"/><connRefOutput v="1487702289"/></connection><connection><identifier v="source"/><connRef v="1487717512"/><connRefOutput v="1487717513"/></connection><connection><identifier v="opacity"/><connRef v="1487713718"/><connRefOutput v="1487713720"/></connection></connections><GUILayout><gpos v="-1232 -240 0"/></GUILayout><compOutputs><compOutput><uid v="1487717579"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487719592"/><connections><connection><identifier v="destination"/><connRef v="1487706483"/><connRefOutput v="1487706484"/></connection></connections><GUILayout><gpos v="-349.37851 -240 0"/></GUILayout><compOutputs><compOutput><uid v="1487719593"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.319999993"/></paramValue></parameter><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1490522771"/><connections><connection><identifier v="Input"/><connRef v="1490522772"/><connRefOutput v="1372392666"/></connection></connections><GUILayout><gpos v="-2192 929.949219 0"/></GUILayout><compOutputs><compOutput><uid v="1372394006"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1383786570"/><parameters/><outputBridgings><outputBridging><uid v="1372394006"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1490522772"/><connections><connection><identifier v="destination"/><connRef v="1490522777"/><connRefOutput v="1372392130"/></connection><connection><identifier v="source"/><connRef v="1490522773"/><connRefOutput v="1372392624"/></connection></connections><GUILayout><gpos v="-2352 929.949219 0"/></GUILayout><compOutputs><compOutput><uid v="1372392666"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.899999976"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1490522773"/><connections><connection><identifier v="Input_1"/><connRef v="1490522776"/><connRefOutput v="1372392121"/></connection></connections><GUILayout><gpos v="-2512 705.949219 0"/></GUILayout><compOutputs><compOutput><uid v="1372392624"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1149349207"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.429999977"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1372392624"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1490522774"/><GUILayout><gpos v="-2928 929.949219 0"/></GUILayout><compOutputs><compOutput><uid v="1372349116"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///waveform_1?dependency=1416542616"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1024"/></paramValue></parameter><parameter><name v="SizeMax"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.170000002"/></paramValue></parameter><parameter><name v="Pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="Noise"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="WaveNumber"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1372349116"/><identifier v="Waveform"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1490522775"/><connections><connection><identifier v="Source"/><connRef v="1490522774"/><connRefOutput v="1372349116"/></connection><connection><identifier v="Effect"/><connRef v="1490522774"/><connRefOutput v="1372349116"/></connection></connections><GUILayout><gpos v="-2800 929.949219 0"/></GUILayout><compOutputs><compOutput><uid v="1372349558"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///non_uniform_blur_grayscale?dependency=1290776959"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="5.78999996"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1372349558"/><identifier v="Non_Uniform_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1490522776"/><GUILayout><gpos v="-2718.47046 705.949219 0"/></GUILayout><compOutputs><compOutput><uid v="1372392121"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///gradient_linear_1?dependency=1357065436"/><parameters><parameter><name v="rotation"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1372392121"/><identifier v="Simple_Gradient"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1490522777"/><connections><connection><identifier v="source"/><connRef v="1490522776"/><connRefOutput v="1372392121"/></connection><connection><identifier v="destination"/><connRef v="1489881715"/><connRefOutput v="1489881716"/></connection></connections><GUILayout><gpos v="-2527.45923 929.949219 0"/></GUILayout><compOutputs><compOutput><uid v="1372392130"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1490522778"/><connections><connection><identifier v="pattern_input"/><connRef v="1490522771"/><connRefOutput v="1372394006"/></connection></connections><GUILayout><gpos v="-1904 1011.57642 0"/></GUILayout><compOutputs><compOutput><uid v="1372395846"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1449399972"/><parameters><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="rotation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="-0.16402778"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="10"/></paramValue></parameter><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="54"/></paramValue></parameter><parameter><name v="vector_map_displacement"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.539999962"/></paramValue></parameter><parameter><name v="scale_vector_map_multiplier"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="64"/></paramValue></parameter><parameter><name v="scale_map_multiplier"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.189999998"/></paramValue></parameter><parameter><name v="rotation_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.129999995"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1372395846"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1490599153"/><connections><connection><identifier v="input1"/><connRef v="1490522775"/><connRefOutput v="1372349558"/></connection></connections><GUILayout><gpos v="-2672 929.949219 0"/></GUILayout><compOutputs><compOutput><uid v="1490599154"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="transformation"/><parameters><parameter><name v="matrix22"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="-0.702799976 0.000600000028 -0.000500000024 -0.896200001"/></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="-0.135399997 0.426499993"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1489881715"/><connections><connection><identifier v="input1"/><connRef v="1490599153"/><connRefOutput v="1490599154"/></connection></connections><GUILayout><gpos v="-2512 1025.94922 0"/></GUILayout><compOutputs><compOutput><uid v="1489881716"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="warp"/><parameters/></compFilter></compImplementation></compNode></compNodes><baseParameters/><options><option><name v="defaultParentSize"/><value v="11x11"/></option></options><root><rootOutputs><rootOutput><output v="1213284336"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1213284338"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1213284340"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1213284342"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1279137031"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1359211721"/><format v="0"/><usertag v=""/></rootOutput></rootOutputs></root></graph></content></package>
