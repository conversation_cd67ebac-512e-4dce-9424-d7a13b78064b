using UnityEngine;
using System.Collections.Generic;
using System.Linq;
#if UNITY_EDITOR
using UnityEditor;
#endif

namespace Stylo.Flux
{
    /// <summary>
    /// Enhanced utility class for managing Flux presets with validation and conflict detection
    /// </summary>
    public static class FluxPresetManager
    {
        public struct PresetValidationResult
        {
            public bool isValid;
            public string[] warnings;
            public string[] errors;
            public string[] suggestions;
            
            public bool hasIssues => (warnings?.Length > 0) || (errors?.Length > 0);
        }
        private static List<FluxPreset> _cachedPresets;
        private static bool _cacheValid = false;

        /// <summary>
        /// Gets all Flux presets in the project
        /// </summary>
        public static List<FluxPreset> GetAllPresets()
        {
            if (!_cacheValid || _cachedPresets == null)
            {
                RefreshPresetCache();
            }
            return _cachedPresets ?? new List<FluxPreset>();
        }
        
        /// <summary>
        /// Gets presets filtered by performance tier
        /// </summary>
        public static List<FluxPreset> GetPresetsByPerformanceTier(PerformanceTier tier)
        {
            return GetAllPresets().Where(p => GetPresetPerformanceTier(p) <= tier).ToList();
        }
        
        public enum PerformanceTier
        {
            Mobile = 0,
            Console = 1, 
            PC = 2,
            HighEnd = 3
        }

        /// <summary>
        /// Refreshes the preset cache by scanning the project
        /// </summary>
        public static void RefreshPresetCache()
        {
            _cachedPresets = new List<FluxPreset>();

#if UNITY_EDITOR
            // Find all FluxPreset assets in the project
            string[] guids = AssetDatabase.FindAssets("t:FluxPreset");
            foreach (string guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                FluxPreset preset = AssetDatabase.LoadAssetAtPath<FluxPreset>(path);
                if (preset != null)
                {
                    _cachedPresets.Add(preset);
                }
            }
            
            // Sort by category then by name
            _cachedPresets = _cachedPresets.OrderBy(p => p.category).ThenBy(p => p.GetDisplayName()).ToList();
#else
            // Runtime: Load from Resources folder
            FluxPreset[] presets = Resources.LoadAll<FluxPreset>("");
            _cachedPresets.AddRange(presets);
            _cachedPresets = _cachedPresets.OrderBy(p => p.category).ThenBy(p => p.GetDisplayName()).ToList();
#endif

            _cacheValid = true;
        }

        /// <summary>
        /// Gets presets organized by category
        /// </summary>
        public static Dictionary<string, List<FluxPreset>> GetPresetsByCategory()
        {
            var presets = GetAllPresets();
            var categorized = new Dictionary<string, List<FluxPreset>>();

            foreach (var preset in presets)
            {
                string category = string.IsNullOrEmpty(preset.category) ? "Uncategorized" : preset.category;

                if (!categorized.ContainsKey(category))
                {
                    categorized[category] = new List<FluxPreset>();
                }

                categorized[category].Add(preset);
            }

            return categorized;
        }

        /// <summary>
        /// Finds a preset by name
        /// </summary>
        public static FluxPreset FindPresetByName(string name)
        {
            return GetAllPresets().FirstOrDefault(p => p.GetDisplayName() == name || p.name == name);
        }

        /// <summary>
        /// Gets preset names for dropdown UI
        /// </summary>
        public static string[] GetPresetNames()
        {
            var presets = GetAllPresets();
            return presets.Select(p => p.GetDisplayName()).ToArray();
        }

        /// <summary>
        /// Gets categorized preset names for dropdown UI
        /// </summary>
        public static string[] GetCategorizedPresetNames()
        {
            var presets = GetAllPresets();
            return presets.Select(p => p.GetCategorizedName()).ToArray();
        }

#if UNITY_EDITOR
        /// <summary>
        /// Creates a new preset asset
        /// </summary>
        public static FluxPreset CreatePresetAsset(string fileName, string folderPath = "Assets/Stylo/Flux/Presets")
        {
            // Ensure the folder exists
            if (!AssetDatabase.IsValidFolder(folderPath))
            {
                string[] folders = folderPath.Split('/');
                string currentPath = folders[0];
                
                for (int i = 1; i < folders.Length; i++)
                {
                    string newPath = currentPath + "/" + folders[i];
                    if (!AssetDatabase.IsValidFolder(newPath))
                    {
                        AssetDatabase.CreateFolder(currentPath, folders[i]);
                    }
                    currentPath = newPath;
                }
            }
            
            // Create the preset asset
            FluxPreset preset = ScriptableObject.CreateInstance<FluxPreset>();
            
            // Generate unique filename
            string assetPath = AssetDatabase.GenerateUniqueAssetPath($"{folderPath}/{fileName}.asset");
            
            // Save the asset
            AssetDatabase.CreateAsset(preset, assetPath);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
            
            // Invalidate cache
            _cacheValid = false;
            
            // Select the new asset
            Selection.activeObject = preset;
            EditorGUIUtility.PingObject(preset);
            
            return preset;
        }
        
        /// <summary>
        /// Creates default presets if they don't exist
        /// </summary>
        public static void CreateDefaultPresets()
        {
            string presetsFolder = "Assets/Stylo/Flux/Presets";
            
            // VHS Datamosh preset
            if (FindPresetByName("VHS Datamosh") == null)
            {
                var vhsPreset = CreatePresetAsset("VHS Datamosh", presetsFolder);
                vhsPreset.presetName = "VHS Datamosh";
                vhsPreset.category = "VHS";
                vhsPreset.description = "Classic VHS-style datamoshing with temporal trails and chroma corruption";
                vhsPreset.effectIntensity = 0.7f;
                vhsPreset.colorCrunch = 0.6f;
                vhsPreset.downscaling = 8;
                vhsPreset.blockSize = FluxPreset.FluxBlockSize._8x8;
                vhsPreset.keyframeResetRate = 0.05f;
                vhsPreset.motionVectorCorruption = 1.2f;
                vhsPreset.errorAccumulation = 0.6f;
                vhsPreset.chromaCorruption = 0.4f;
                vhsPreset.multiScaleCorruption = 0.3f;
                EditorUtility.SetDirty(vhsPreset);
            }
            
            // Digital Glitch preset
            if (FindPresetByName("Digital Glitch") == null)
            {
                var digitalPreset = CreatePresetAsset("Digital Glitch", presetsFolder);
                digitalPreset.presetName = "Digital Glitch";
                digitalPreset.category = "Digital";
                digitalPreset.description = "Modern digital glitch art with feedback loops and multi-scale corruption";
                digitalPreset.effectIntensity = 0.8f;
                digitalPreset.colorCrunch = 0.4f;
                digitalPreset.downscaling = 6;
                digitalPreset.blockSize = FluxPreset.FluxBlockSize._4x4;
                digitalPreset.feedbackIntensity = 0.6f;
                digitalPreset.multiScaleCorruption = 0.8f;
                digitalPreset.dctCorruption = 0.5f;
                digitalPreset.glitchTransition = 0.7f;
                EditorUtility.SetDirty(digitalPreset);
            }
            
            // Subtle Compression preset
            if (FindPresetByName("Subtle Compression") == null)
            {
                var subtlePreset = CreatePresetAsset("Subtle Compression", presetsFolder);
                subtlePreset.presetName = "Subtle Compression";
                subtlePreset.category = "Subtle";
                subtlePreset.description = "Light compression artifacts for realistic video quality degradation";
                subtlePreset.effectIntensity = 0.3f;
                subtlePreset.colorCrunch = 0.2f;
                subtlePreset.downscaling = 10;
                subtlePreset.blockSize = FluxPreset.FluxBlockSize._16x16;
                subtlePreset.dctCorruption = 0.2f;
                subtlePreset.chromaCorruption = 0.1f;
                subtlePreset.multiScaleCorruption = 0.2f;
                EditorUtility.SetDirty(subtlePreset);
            }
            
            // Satellite Feed Loss preset
            if (FindPresetByName("Satellite Feed Loss") == null)
            {
                var satellitePreset = CreatePresetAsset("Satellite Feed Loss", presetsFolder);
                satellitePreset.presetName = "Satellite Feed Loss";
                satellitePreset.category = "Cinematic";
                satellitePreset.description = "Simulates satellite transmission corruption and signal loss";
                satellitePreset.effectIntensity = 0.9f;
                satellitePreset.colorCrunch = 0.8f;
                satellitePreset.downscaling = 4;
                satellitePreset.blockSize = FluxPreset.FluxBlockSize._16x16;
                satellitePreset.keyframeResetRate = 0.02f;
                satellitePreset.motionVectorCorruption = 2.0f;
                satellitePreset.errorAccumulation = 0.8f;
                satellitePreset.multiScaleCorruption = 0.6f;
                EditorUtility.SetDirty(satellitePreset);
            }
            
            AssetDatabase.SaveAssets();
            RefreshPresetCache();
        }
#endif

        /// <summary>
        /// Invalidates the preset cache (call when presets are added/removed)
        /// </summary>
        public static void InvalidateCache()
        {
            _cacheValid = false;
        }
        
        /// <summary>
        /// Validates a preset for parameter conflicts and performance issues
        /// </summary>
        public static PresetValidationResult ValidatePreset(FluxPreset preset)
        {
            var warnings = new List<string>();
            var errors = new List<string>();
            var suggestions = new List<string>();
            
            if (preset == null)
            {
                errors.Add("Preset is null");
                return new PresetValidationResult { isValid = false, errors = errors.ToArray() };
            }
            
            // Performance validation
            var performanceTier = GetPresetPerformanceTier(preset);
            if (performanceTier >= PerformanceTier.HighEnd)
            {
                warnings.Add($"High performance requirement - may not run well on mobile/console");
            }
            
            // Parameter conflict detection
            if (preset.effectIntensity > 0.1f)
            {
                // Motion system validation
                bool hasMotionFeatures = preset.motionAmplification > 0f || preset.trailIntensity > 0f;
                bool hasReprojection = preset.reprojectBaseNoise > 0f || preset.reprojectLengthInfluence > 0f;
                
                if (hasMotionFeatures && !hasReprojection)
                {
                    warnings.Add("Motion features enabled but reprojection disabled - trails may not work");
                    suggestions.Add("Enable Base Noise (0.1+) or Length Influence (0.1+) for motion effects");
                }
                
                // Performance warnings for dangerous combinations
                if (preset.blockSize == FluxPreset.FluxBlockSize._32x32 && preset.downscaling < 5)
                {
                    warnings.Add("32x32 blocks with low downscaling - severe performance impact");
                    suggestions.Add("Increase downscaling to 6+ or use smaller block size");
                }
                
                if (preset.blockSize == FluxPreset.FluxBlockSize._16x16 && preset.downscaling < 3)
                {
                    warnings.Add("16x16 blocks with low downscaling - potential performance issues");
                    suggestions.Add("Increase downscaling to 4+ for better performance");
                }
                
                // Quality validation
                if (preset.jpegQuality < 10f && preset.effectIntensity > 0.8f)
                {
                    warnings.Add("Very low JPEG quality with high intensity - may cause visual artifacts");
                    suggestions.Add("Increase JPEG Quality to 15+ or reduce Effect Intensity");
                }
                
                // Feature utilization
                if (preset.effectIntensity > 0.5f && 
                    preset.motionVectorCorruption == 0f && 
                    preset.errorAccumulation == 0f && 
                    preset.dctCorruption == 0f)
                {
                    suggestions.Add("Consider enabling corruption features for more dramatic effects");
                }
            }
            
            bool isValid = errors.Count == 0;
            
            return new PresetValidationResult
            {
                isValid = isValid,
                warnings = warnings.ToArray(),
                errors = errors.ToArray(),
                suggestions = suggestions.ToArray()
            };
        }
        
        /// <summary>
        /// Gets the estimated performance tier required for a preset
        /// </summary>
        public static PerformanceTier GetPresetPerformanceTier(FluxPreset preset)
        {
            if (preset == null) return PerformanceTier.Mobile;
            
            int performanceScore = 0;
            
            // Block size impact
            switch (preset.blockSize)
            {
                case FluxPreset.FluxBlockSize._2x2: performanceScore += 0; break;
                case FluxPreset.FluxBlockSize._4x4: performanceScore += 1; break;
                case FluxPreset.FluxBlockSize._8x8: performanceScore += 2; break;
                case FluxPreset.FluxBlockSize._16x16: performanceScore += 4; break;
                case FluxPreset.FluxBlockSize._32x32: performanceScore += 8; break;
            }
            
            // Downscaling impact (inverted - lower downscaling = higher cost)
            if (preset.downscaling <= 2) performanceScore += 6;
            else if (preset.downscaling <= 4) performanceScore += 4;
            else if (preset.downscaling <= 6) performanceScore += 2;
            else if (preset.downscaling <= 8) performanceScore += 1;
            
            // Feature complexity
            if (preset.multiScaleCorruption > 0.5f) performanceScore += 2;
            if (preset.feedbackIntensity > 0.3f) performanceScore += 1;
            if (preset.trailIntensity > 1.0f) performanceScore += 2;
            if (preset.flowSpread > 2.0f) performanceScore += 1;
            
            // Effect intensity multiplier
            performanceScore = Mathf.RoundToInt(performanceScore * preset.effectIntensity);
            
            // Map score to tier
            if (performanceScore <= 2) return PerformanceTier.Mobile;
            if (performanceScore <= 5) return PerformanceTier.Console;
            if (performanceScore <= 8) return PerformanceTier.PC;
            return PerformanceTier.HighEnd;
        }
        
        /// <summary>
        /// Auto-fixes common preset parameter conflicts
        /// </summary>
        public static string AutoFixPreset(FluxPreset preset)
        {
            if (preset == null) return "Preset is null";
            
            var fixes = new List<string>();
            
            // Auto-enable reprojection if motion features are used
            bool hasMotionFeatures = preset.motionAmplification > 0f || preset.trailIntensity > 0f;
            bool hasReprojection = preset.reprojectBaseNoise > 0f || preset.reprojectLengthInfluence > 0f;
            
            if (hasMotionFeatures && !hasReprojection)
            {
                preset.reprojectBaseNoise = 0.1f;
                fixes.Add("Enabled Base Noise (0.1) to support motion features");
            }
            
            // Auto-adjust dangerous performance combinations
            if (preset.blockSize == FluxPreset.FluxBlockSize._32x32 && preset.downscaling < 5)
            {
                preset.downscaling = 6;
                fixes.Add("Increased downscaling to 6 for better 32x32 block performance");
            }
            
            if (preset.blockSize == FluxPreset.FluxBlockSize._16x16 && preset.downscaling < 3)
            {
                preset.downscaling = 4;
                fixes.Add("Increased downscaling to 4 for better 16x16 block performance");
            }
            
#if UNITY_EDITOR
            if (fixes.Count > 0)
            {
                EditorUtility.SetDirty(preset);
            }
#endif
            
            return fixes.Count > 0 ? string.Join("\n", fixes) : "No fixes needed";
        }
    }
}
