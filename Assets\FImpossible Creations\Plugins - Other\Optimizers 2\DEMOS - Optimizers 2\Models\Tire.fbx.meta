fileFormatVersion: 2
guid: 1a99dc8e1c305a54e8409341de7df888
timeCreated: **********
licenseType: Free
ModelImporter:
  serializedVersion: 19
  fileIDToRecycleName:
    100000: //RootNode
    100002: TIRE_LOD0
    100004: TIRE_LOD1
    100006: TIRE_LOD2
    100008: TIRE_LOD3
    100010: TIRE_LOD4
    400000: //RootNode
    400002: TIRE_LOD0
    400004: TIRE_LOD1
    400006: TIRE_LOD2
    400008: TIRE_LOD3
    400010: TIRE_LOD4
    2300000: TIRE_LOD0
    2300002: TIRE_LOD1
    2300004: TIRE_LOD2
    2300006: TIRE_LOD3
    2300008: TIRE_LOD4
    3300000: TIRE_LOD0
    3300002: TIRE_LOD1
    3300004: TIRE_LOD2
    3300006: TIRE_LOD3
    3300008: TIRE_LOD4
    4300000: TIRE_LOD0
    4300002: TIRE_LOD1
    4300004: TIRE_LOD2
    4300006: TIRE_LOD3
    4300008: TIRE_LOD4
    9500000: //RootNode
    20500000: //RootNode
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages:
    - 0.25
    - 0.125
    - 0.0625
    - 0.03125
    - 0.01
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    importBlendShapes: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
  importAnimation: 0
  copyAvatar: 0
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    rootMotionBoneRotation: {x: 0, y: 0, z: 0, w: 1}
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 0
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
