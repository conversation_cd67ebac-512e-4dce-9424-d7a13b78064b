﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class FlexalonRandomModifier
 | Flexalon 3D Layouts </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class FlexalonRandomModifier
 | Flexalon 3D Layouts ">
    <meta name="generator" content="docfx ">
  
    <link rel="shortcut icon" href="images/favicon.svg">
    <link rel="stylesheet" href="styles/docfx.vendor.css">
    <link rel="stylesheet" href="styles/docfx.css">
    <link rel="stylesheet" href="styles/main.css">
    <meta property="docfx:navrel" content="tochead.html">
    <meta property="docfx:tocrel" content="toc.html">
  
  <meta property="docfx:rel" content="">
  
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="https://www.flexalon.com/">
                <img id="logo" class="svg" src="images/logo_32x32.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first="First" data-prev="Previous" data-next="Next" data-last="Last"></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">

        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="Flexalon.FlexalonRandomModifier">


  <h1 id="Flexalon_FlexalonRandomModifier" data-uid="Flexalon.FlexalonRandomModifier" class="text-break">Class FlexalonRandomModifier
</h1>
  <div class="markdown level0 summary"><p>The Flexalon Random Modifier component can be added to any layout
to randomly modify the positions and rotations of the children.</p>
</div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">object</span></div>
    <div class="level1"><span class="xref">UnityEngine.Object</span></div>
    <div class="level2"><span class="xref">UnityEngine.Component</span></div>
    <div class="level3"><span class="xref">UnityEngine.Behaviour</span></div>
    <div class="level4"><span class="xref">UnityEngine.MonoBehaviour</span></div>
    <div class="level5"><a class="xref" href="Flexalon.FlexalonComponent.html">FlexalonComponent</a></div>
    <div class="level6"><span class="xref">FlexalonRandomModifier</span></div>
  </div>
  <div class="implements">
    <h5>Implements</h5>
    <div><a class="xref" href="Flexalon.FlexalonModifier.html">FlexalonModifier</a></div>
  </div>
  <div class="inheritedMembers">
    <h5>Inherited Members</h5>
    <div>
      <a class="xref" href="Flexalon.FlexalonComponent.html#Flexalon_FlexalonComponent__node">FlexalonComponent._node</a>
    </div>
    <div>
      <a class="xref" href="Flexalon.FlexalonComponent.html#Flexalon_FlexalonComponent_Node">FlexalonComponent.Node</a>
    </div>
    <div>
      <a class="xref" href="Flexalon.FlexalonComponent.html#Flexalon_FlexalonComponent_MarkDirty">FlexalonComponent.MarkDirty()</a>
    </div>
    <div>
      <a class="xref" href="Flexalon.FlexalonComponent.html#Flexalon_FlexalonComponent_ForceUpdate">FlexalonComponent.ForceUpdate()</a>
    </div>
    <div>
      <a class="xref" href="Flexalon.FlexalonComponent.html#Flexalon_FlexalonComponent_DoOnEnable">FlexalonComponent.DoOnEnable()</a>
    </div>
    <div>
      <a class="xref" href="Flexalon.FlexalonComponent.html#Flexalon_FlexalonComponent_DoOnDisable">FlexalonComponent.DoOnDisable()</a>
    </div>
    <div>
      <a class="xref" href="Flexalon.FlexalonComponent.html#Flexalon_FlexalonComponent_DoUpdate">FlexalonComponent.DoUpdate()</a>
    </div>
    <div>
      <a class="xref" href="Flexalon.FlexalonComponent.html#Flexalon_FlexalonComponent_Initialize">FlexalonComponent.Initialize()</a>
    </div>
    <div>
      <a class="xref" href="Flexalon.FlexalonComponent.html#Flexalon_FlexalonComponent_Upgrade_System_Int32_">FlexalonComponent.Upgrade(int)</a>
    </div>
  </div>
  <h5 id="Flexalon_FlexalonRandomModifier_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">[AddComponentMenu(&quot;Flexalon/Flexalon Random Modifier&quot;)]
[HelpURL(&quot;https://www.flexalon.com/docs/randomModifier&quot;)]
public class FlexalonRandomModifier : FlexalonComponent, FlexalonModifier</code></pre>
  </div>
  <h3 id="properties">Properties
</h3>
  <a id="Flexalon_FlexalonRandomModifier_PositionMax_" data-uid="Flexalon.FlexalonRandomModifier.PositionMax*"></a>
  <h4 id="Flexalon_FlexalonRandomModifier_PositionMax" data-uid="Flexalon.FlexalonRandomModifier.PositionMax">PositionMax</h4>
  <div class="markdown level1 summary"><p>Maximum position value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3 PositionMax { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonRandomModifier_PositionMaxX_" data-uid="Flexalon.FlexalonRandomModifier.PositionMaxX*"></a>
  <h4 id="Flexalon_FlexalonRandomModifier_PositionMaxX" data-uid="Flexalon.FlexalonRandomModifier.PositionMaxX">PositionMaxX</h4>
  <div class="markdown level1 summary"><p>Maximum X position value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float PositionMaxX { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">float</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonRandomModifier_PositionMaxY_" data-uid="Flexalon.FlexalonRandomModifier.PositionMaxY*"></a>
  <h4 id="Flexalon_FlexalonRandomModifier_PositionMaxY" data-uid="Flexalon.FlexalonRandomModifier.PositionMaxY">PositionMaxY</h4>
  <div class="markdown level1 summary"><p>Maximum Y position value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float PositionMaxY { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">float</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonRandomModifier_PositionMaxZ_" data-uid="Flexalon.FlexalonRandomModifier.PositionMaxZ*"></a>
  <h4 id="Flexalon_FlexalonRandomModifier_PositionMaxZ" data-uid="Flexalon.FlexalonRandomModifier.PositionMaxZ">PositionMaxZ</h4>
  <div class="markdown level1 summary"><p>Maximum Z position value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float PositionMaxZ { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">float</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonRandomModifier_PositionMin_" data-uid="Flexalon.FlexalonRandomModifier.PositionMin*"></a>
  <h4 id="Flexalon_FlexalonRandomModifier_PositionMin" data-uid="Flexalon.FlexalonRandomModifier.PositionMin">PositionMin</h4>
  <div class="markdown level1 summary"><p>Minimum position value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector3 PositionMin { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonRandomModifier_PositionMinX_" data-uid="Flexalon.FlexalonRandomModifier.PositionMinX*"></a>
  <h4 id="Flexalon_FlexalonRandomModifier_PositionMinX" data-uid="Flexalon.FlexalonRandomModifier.PositionMinX">PositionMinX</h4>
  <div class="markdown level1 summary"><p>Minimum X position value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float PositionMinX { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">float</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonRandomModifier_PositionMinY_" data-uid="Flexalon.FlexalonRandomModifier.PositionMinY*"></a>
  <h4 id="Flexalon_FlexalonRandomModifier_PositionMinY" data-uid="Flexalon.FlexalonRandomModifier.PositionMinY">PositionMinY</h4>
  <div class="markdown level1 summary"><p>Minimum Y position value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float PositionMinY { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">float</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonRandomModifier_PositionMinZ_" data-uid="Flexalon.FlexalonRandomModifier.PositionMinZ*"></a>
  <h4 id="Flexalon_FlexalonRandomModifier_PositionMinZ" data-uid="Flexalon.FlexalonRandomModifier.PositionMinZ">PositionMinZ</h4>
  <div class="markdown level1 summary"><p>Minimum Z position value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float PositionMinZ { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">float</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonRandomModifier_RandomizePositionX_" data-uid="Flexalon.FlexalonRandomModifier.RandomizePositionX*"></a>
  <h4 id="Flexalon_FlexalonRandomModifier_RandomizePositionX" data-uid="Flexalon.FlexalonRandomModifier.RandomizePositionX">RandomizePositionX</h4>
  <div class="markdown level1 summary"><p>If true, the X position of each child will be randomized.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool RandomizePositionX { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">bool</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonRandomModifier_RandomizePositionY_" data-uid="Flexalon.FlexalonRandomModifier.RandomizePositionY*"></a>
  <h4 id="Flexalon_FlexalonRandomModifier_RandomizePositionY" data-uid="Flexalon.FlexalonRandomModifier.RandomizePositionY">RandomizePositionY</h4>
  <div class="markdown level1 summary"><p>If true, the Y position of each child will be randomized.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool RandomizePositionY { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">bool</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonRandomModifier_RandomizePositionZ_" data-uid="Flexalon.FlexalonRandomModifier.RandomizePositionZ*"></a>
  <h4 id="Flexalon_FlexalonRandomModifier_RandomizePositionZ" data-uid="Flexalon.FlexalonRandomModifier.RandomizePositionZ">RandomizePositionZ</h4>
  <div class="markdown level1 summary"><p>If true, the Z position of each child will be randomized.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool RandomizePositionZ { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">bool</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonRandomModifier_RandomizeRotationX_" data-uid="Flexalon.FlexalonRandomModifier.RandomizeRotationX*"></a>
  <h4 id="Flexalon_FlexalonRandomModifier_RandomizeRotationX" data-uid="Flexalon.FlexalonRandomModifier.RandomizeRotationX">RandomizeRotationX</h4>
  <div class="markdown level1 summary"><p>If true, the X rotation of each child will be randomized.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool RandomizeRotationX { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">bool</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonRandomModifier_RandomizeRotationY_" data-uid="Flexalon.FlexalonRandomModifier.RandomizeRotationY*"></a>
  <h4 id="Flexalon_FlexalonRandomModifier_RandomizeRotationY" data-uid="Flexalon.FlexalonRandomModifier.RandomizeRotationY">RandomizeRotationY</h4>
  <div class="markdown level1 summary"><p>If true, the Y rotation of each child will be randomized.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool RandomizeRotationY { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">bool</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonRandomModifier_RandomizeRotationZ_" data-uid="Flexalon.FlexalonRandomModifier.RandomizeRotationZ*"></a>
  <h4 id="Flexalon_FlexalonRandomModifier_RandomizeRotationZ" data-uid="Flexalon.FlexalonRandomModifier.RandomizeRotationZ">RandomizeRotationZ</h4>
  <div class="markdown level1 summary"><p>If true, the Z rotation of each child will be randomized.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool RandomizeRotationZ { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">bool</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonRandomModifier_RandomSeed_" data-uid="Flexalon.FlexalonRandomModifier.RandomSeed*"></a>
  <h4 id="Flexalon_FlexalonRandomModifier_RandomSeed" data-uid="Flexalon.FlexalonRandomModifier.RandomSeed">RandomSeed</h4>
  <div class="markdown level1 summary"><p>Seed value used to determine random values.
This ensures results the remain consistent each time layout is computed.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int RandomSeed { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">int</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonRandomModifier_RotationMaxX_" data-uid="Flexalon.FlexalonRandomModifier.RotationMaxX*"></a>
  <h4 id="Flexalon_FlexalonRandomModifier_RotationMaxX" data-uid="Flexalon.FlexalonRandomModifier.RotationMaxX">RotationMaxX</h4>
  <div class="markdown level1 summary"><p>Maximum X rotation value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float RotationMaxX { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">float</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonRandomModifier_RotationMaxY_" data-uid="Flexalon.FlexalonRandomModifier.RotationMaxY*"></a>
  <h4 id="Flexalon_FlexalonRandomModifier_RotationMaxY" data-uid="Flexalon.FlexalonRandomModifier.RotationMaxY">RotationMaxY</h4>
  <div class="markdown level1 summary"><p>Maximum Y rotation value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float RotationMaxY { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">float</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonRandomModifier_RotationMaxZ_" data-uid="Flexalon.FlexalonRandomModifier.RotationMaxZ*"></a>
  <h4 id="Flexalon_FlexalonRandomModifier_RotationMaxZ" data-uid="Flexalon.FlexalonRandomModifier.RotationMaxZ">RotationMaxZ</h4>
  <div class="markdown level1 summary"><p>Maximum Z rotation value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float RotationMaxZ { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">float</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonRandomModifier_RotationMinX_" data-uid="Flexalon.FlexalonRandomModifier.RotationMinX*"></a>
  <h4 id="Flexalon_FlexalonRandomModifier_RotationMinX" data-uid="Flexalon.FlexalonRandomModifier.RotationMinX">RotationMinX</h4>
  <div class="markdown level1 summary"><p>Minimum X rotation value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float RotationMinX { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">float</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonRandomModifier_RotationMinY_" data-uid="Flexalon.FlexalonRandomModifier.RotationMinY*"></a>
  <h4 id="Flexalon_FlexalonRandomModifier_RotationMinY" data-uid="Flexalon.FlexalonRandomModifier.RotationMinY">RotationMinY</h4>
  <div class="markdown level1 summary"><p>Minimum Y rotation value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float RotationMinY { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">float</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonRandomModifier_RotationMinZ_" data-uid="Flexalon.FlexalonRandomModifier.RotationMinZ*"></a>
  <h4 id="Flexalon_FlexalonRandomModifier_RotationMinZ" data-uid="Flexalon.FlexalonRandomModifier.RotationMinZ">RotationMinZ</h4>
  <div class="markdown level1 summary"><p>Minimum Z rotation value.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public float RotationMinZ { get; set; }</code></pre>
  </div>
  <h5 class="propertyValue">Property Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">float</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
</h3>
  <a id="Flexalon_FlexalonRandomModifier_PostArrange_" data-uid="Flexalon.FlexalonRandomModifier.PostArrange*"></a>
  <h4 id="Flexalon_FlexalonRandomModifier_PostArrange_Flexalon_FlexalonNode_" data-uid="Flexalon.FlexalonRandomModifier.PostArrange(Flexalon.FlexalonNode)">PostArrange(FlexalonNode)</h4>
  <div class="markdown level1 summary"><p>Called after the node's children are arranged.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void PostArrange(FlexalonNode node)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.FlexalonNode.html">FlexalonNode</a></td>
        <td><span class="parametername">node</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_FlexalonRandomModifier_ResetProperties_" data-uid="Flexalon.FlexalonRandomModifier.ResetProperties*"></a>
  <h4 id="Flexalon_FlexalonRandomModifier_ResetProperties" data-uid="Flexalon.FlexalonRandomModifier.ResetProperties">ResetProperties()</h4>
  <div class="markdown level1 summary"><p>Called when the component is destroyed to reset properties on the FlexalonNode.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected override void ResetProperties()</code></pre>
  </div>
  <h5 class="overrides">Overrides</h5>
  <div><a class="xref" href="Flexalon.FlexalonComponent.html#Flexalon_FlexalonComponent_ResetProperties">FlexalonComponent.ResetProperties()</a></div>
  <a id="Flexalon_FlexalonRandomModifier_UpdateProperties_" data-uid="Flexalon.FlexalonRandomModifier.UpdateProperties*"></a>
  <h4 id="Flexalon_FlexalonRandomModifier_UpdateProperties" data-uid="Flexalon.FlexalonRandomModifier.UpdateProperties">UpdateProperties()</h4>
  <div class="markdown level1 summary"><p>Called when the component is enabled to apply properties to the FlexalonNode.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected override void UpdateProperties()</code></pre>
  </div>
  <h5 class="overrides">Overrides</h5>
  <div><a class="xref" href="Flexalon.FlexalonComponent.html#Flexalon_FlexalonComponent_UpdateProperties">FlexalonComponent.UpdateProperties()</a></div>
  <h3 id="implements">Implements</h3>
  <div>
      <a class="xref" href="Flexalon.FlexalonModifier.html">FlexalonModifier</a>
  </div>
</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In This Article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      Flexalon 3D Layouts
      
          </div>
        </div>
      </footer>
    </div>

    
    
    
  </body>
</html>
