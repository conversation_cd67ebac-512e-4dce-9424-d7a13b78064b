%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e78934177bebe0545ab566bb787b0282, type: 3}
  m_Name: Readme
  m_EditorClassIdentifier: 
  Icon: {fileID: 2800000, guid: 830aa5e71ccf80440965251098f55b1f, type: 3}
  Title: Simple Blur
  Description:
    Heading: 
    Text: This sample performs a really simple blur over a given texture.
    LinkText: 
    Url: 
  PropertiesHeader:
    Heading: Properties
    Text: 
    LinkText: 
    Url: 
  Properties:
  - Heading: Main Sample
    Text: Texture to be blurred.
    LinkText: 
    Url: 
  - Heading: Toggle Blur
    Text: Toggles on/off the texture blur.
    LinkText: 
    Url: 
  - Heading: Blur Size
    Text: Value that controls blur spreading.
    LinkText: 
    Url: 
  AdditionalProperties: []
  AdditionalScripts: []
  LoadedLayout: 0
  RPType: 3
