<?xml version="1.0" encoding="UTF-8"?><package><identifier v="Unsaved Package"/><formatVersion v="1.1.0.202302"/><updaterVersion v="1.1.0.202302"/><fileUID v="{64dfaf85-3a74-441d-8805-d5b3c2782cc9}"/><versionUID v="0"/><dependencies><dependency><filename v="sbs://hbao_2.sbs"/><uid v="1297162667"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://pattern_tile_sampler.sbs"/><uid v="1449399972"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://flood_fill_2.sbs"/><uid v="1323881949"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://edge_detect.sbs"/><uid v="1407202483"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://histogram_scan.sbs"/><uid v="1449404462"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://histogram_select.sbs"/><uid v="1407203168"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://bevel.sbs"/><uid v="1449405586"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://auto_levels.sbs"/><uid v="1449400845"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_crystal_1.sbs"/><uid v="1407271237"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://blur_hq.sbs"/><uid v="1299236171"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://slope_blur.sbs"/><uid v="1449402040"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_fractal_sum_base.sbs"/><uid v="1449402686"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://invert.sbs"/><uid v="1177447620"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://rgba_split.sbs"/><uid v="1147283954"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://highpass.sbs"/><uid v="1449753752"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://quantize.sbs"/><uid v="1407503933"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://curvature_smooth.sbs"/><uid v="1449590280"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://height_blend.sbs"/><uid v="1449623608"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency></dependencies><content><graph><identifier v="rocks"/><uid v="1407184709"/><graphOutputs><graphoutput><identifier v="basecolor_1"/><uid v="1407198767"/><attributes><label v="Base Color"/></attributes><usages><usage><components v="RGBA"/><name v="baseColor"/></usage></usages><group v="Material"/></graphoutput><graphoutput><identifier v="normal_1"/><uid v="1407198769"/><attributes><label v="Normal"/></attributes><usages><usage><components v="RGBA"/><name v="normal"/></usage></usages><group v="Material"/></graphoutput><graphoutput><identifier v="roughness_1"/><uid v="1407198771"/><attributes><label v="Roughness"/></attributes><usages><usage><components v="RGBA"/><name v="roughness"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput><graphoutput><identifier v="height_1"/><uid v="1407198775"/><attributes><label v="Height"/></attributes><usages><usage><components v="RGBA"/><name v="height"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput><graphoutput><identifier v="AO"/><uid v="1407198777"/><usages><usage><components v="RGBA"/><name v="ambientOcclusion"/></usage></usages><channels v="2"/></graphoutput></graphOutputs><compNodes><compNode><uid v="1407198761"/><connections><connection><identifier v="input"/><connRef v="1407198764"/><connRefOutput v="1355424890"/></connection></connections><GUILayout><gpos v="5057.29248 22.4624691 0"/></GUILayout><compOutputs><compOutput><uid v="1355424961"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///hbao?dependency=1297162667"/><parameters><parameter><name v="height_depth"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.170000002"/></paramValue></parameter><parameter><name v="samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="16"/></paramValue></parameter><parameter><name v="surface_size"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="268.820007"/></paramValue></parameter><parameter><name v="height_depth_cm"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="93.1899948"/></paramValue></parameter><parameter><name v="radius"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.579999983"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1355424961"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407198763"/><connections><connection><identifier v="input1"/><connRef v="1407198764"/><connRefOutput v="1355424890"/></connection></connections><GUILayout><gpos v="5046.62744 171.795532 0"/></GUILayout><compOutputs><compOutput><uid v="1355431358"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="normal"/><parameters><parameter><name v="input2alpha"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="inversedy"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="20"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407198764"/><connections><connection><identifier v="destination"/><connRef v="1407420471"/><connRefOutput v="1407420472"/></connection></connections><GUILayout><gpos v="4822.62744 331.795532 0"/></GUILayout><compOutputs><compOutput><uid v="1355424890"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407198766"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1407516236"/><connRefOutput v="1407516237"/></connection></connections><GUILayout><gpos v="5270.62744 -308.204468 0"/></GUILayout><compImplementation><compOutputBridge><output v="1407198767"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1407198768"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1407198763"/><connRefOutput v="1355431358"/></connection></connections><GUILayout><gpos v="5270.62744 171.795532 0"/></GUILayout><compImplementation><compOutputBridge><output v="1407198769"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1407198770"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1407527732"/><connRefOutput v="1407527733"/></connection></connections><GUILayout><gpos v="5281.29248 -158.87085 0"/></GUILayout><compImplementation><compOutputBridge><output v="1407198771"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1407198774"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1407198764"/><connRefOutput v="1355424890"/></connection></connections><GUILayout><gpos v="5270.62744 331.795532 0"/></GUILayout><compImplementation><compOutputBridge><output v="1407198775"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1407198776"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1407198761"/><connRefOutput v="1355424961"/></connection></connections><GUILayout><gpos v="5270.62744 11.7954168 0"/></GUILayout><compImplementation><compOutputBridge><output v="1407198777"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1407201466"/><GUILayout><gpos v="-5840 -1040 0"/></GUILayout><compOutputs><compOutput><uid v="1407201467"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1449399972"/><parameters><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="10"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.229999989"/></paramValue></parameter><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="2.3599999"/></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.459999979"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.649999976"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407201467"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407202195"/><connections><connection><identifier v="mask"/><connRef v="1407201466"/><connRefOutput v="1407201467"/></connection></connections><GUILayout><gpos v="-5680 -1040 0"/></GUILayout><compOutputs><compOutput><uid v="1407202196"/><comptype v="1"/></compOutput><compOutput><uid v="1407202197"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill?dependency=1323881949"/><parameters/><outputBridgings><outputBridging><uid v="1407202196"/><identifier v="output"/></outputBridging><outputBridging><uid v="1407202197"/><identifier v="referenceMap"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407202279"/><connections><connection><identifier v="source"/><connRef v="1407202317"/><connRefOutput v="1407202318"/></connection><connection><identifier v="mask"/><connRef v="1407201466"/><connRefOutput v="1407201467"/></connection></connections><GUILayout><gpos v="-5360 -1040 0"/></GUILayout><compOutputs><compOutput><uid v="1407202280"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters><parameter><name v="combinedistance"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="256"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407202317"/><connections><connection><identifier v="input"/><connRef v="1407202195"/><connRefOutput v="1407202196"/></connection></connections><GUILayout><gpos v="-5520 -1040 0"/></GUILayout><compOutputs><compOutput><uid v="1407202318"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_random_grayscale?dependency=1323881949"/><parameters/><outputBridgings><outputBridging><uid v="1407202318"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407202524"/><connections><connection><identifier v="input"/><connRef v="1407202279"/><connRefOutput v="1407202280"/></connection></connections><GUILayout><gpos v="-5200 -1040 0"/></GUILayout><compOutputs><compOutput><uid v="1407202525"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///edge_detect?dependency=1407202483"/><parameters><parameter><name v="edge_roundness"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="12.3299999"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407202525"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407202745"/><connections><connection><identifier v="Input_1"/><connRef v="1407202279"/><connRefOutput v="1407202280"/></connection></connections><GUILayout><gpos v="-5200 -1168 0"/></GUILayout><compOutputs><compOutput><uid v="1407202746"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1449404462"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.349999994"/></paramValue></parameter><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407202746"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407202845"/><connections><connection><identifier v="input"/><connRef v="1407202279"/><connRefOutput v="1407202280"/></connection></connections><GUILayout><gpos v="-5200 -912 0"/></GUILayout><compOutputs><compOutput><uid v="1407202525"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///edge_detect?dependency=1407202483"/><parameters><parameter><name v="edge_roundness"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="7.69000006"/></paramValue></parameter><parameter><name v="edge_width"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="2.36999989"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407202525"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407202867"/><connections><connection><identifier v="source"/><connRef v="1407202524"/><connRefOutput v="1407202525"/></connection><connection><identifier v="destination"/><connRef v="1407202845"/><connRefOutput v="1407202525"/></connection><connection><identifier v="opacity"/><connRef v="1407202745"/><connRefOutput v="1407202746"/></connection></connections><GUILayout><gpos v="-5040 -912 0"/></GUILayout><compOutputs><compOutput><uid v="1407202868"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1407203017"/><connections><connection><identifier v="input"/><connRef v="1407202279"/><connRefOutput v="1407202280"/></connection></connections><GUILayout><gpos v="-5200 -1296 0"/></GUILayout><compOutputs><compOutput><uid v="1407202525"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///edge_detect?dependency=1407202483"/><parameters><parameter><name v="edge_roundness"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="12.0999994"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407202525"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407203207"/><connections><connection><identifier v="input"/><connRef v="1407202279"/><connRefOutput v="1407202280"/></connection></connections><GUILayout><gpos v="-5200 -1424 0"/></GUILayout><compOutputs><compOutput><uid v="1407203208"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_select?dependency=1407203168"/><parameters><parameter><name v="constrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.199999988"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407203208"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407203294"/><connections><connection><identifier v="destination"/><connRef v="1407202867"/><connRefOutput v="1407202868"/></connection><connection><identifier v="source"/><connRef v="1407203017"/><connRefOutput v="1407202525"/></connection><connection><identifier v="opacity"/><connRef v="1407203207"/><connRefOutput v="1407203208"/></connection></connections><GUILayout><gpos v="-4880 -912 0"/></GUILayout><compOutputs><compOutput><uid v="1407203295"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1407203431"/><connections><connection><identifier v="input"/><connRef v="1407202279"/><connRefOutput v="1407202280"/></connection></connections><GUILayout><gpos v="-5200 -1712 0"/></GUILayout><compOutputs><compOutput><uid v="1407203208"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_select?dependency=1407203168"/><parameters><parameter><name v="constrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.359999985"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407203208"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407203469"/><connections><connection><identifier v="input"/><connRef v="1407202279"/><connRefOutput v="1407202280"/></connection></connections><GUILayout><gpos v="-5200 -1584 0"/></GUILayout><compOutputs><compOutput><uid v="1407202525"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///edge_detect?dependency=1407202483"/><parameters><parameter><name v="edge_roundness"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="15.4299994"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407202525"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407203524"/><connections><connection><identifier v="destination"/><connRef v="1407203294"/><connRefOutput v="1407203295"/></connection><connection><identifier v="opacity"/><connRef v="1407203431"/><connRefOutput v="1407203208"/></connection><connection><identifier v="source"/><connRef v="1407203469"/><connRefOutput v="1407202525"/></connection></connections><GUILayout><gpos v="-4688 -912 0"/></GUILayout><compOutputs><compOutput><uid v="1407203295"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1407205796"/><connections><connection><identifier v="input"/><connRef v="1407203524"/><connRefOutput v="1407203295"/></connection></connections><GUILayout><gpos v="-3824 464 0"/></GUILayout><compOutputs><compOutput><uid v="1407205797"/><comptype v="2"/></compOutput><compOutput><uid v="1407205798"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///bevel?dependency=1449405586"/><parameters><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0599999987"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407205797"/><identifier v="height"/></outputBridging><outputBridging><uid v="1407205798"/><identifier v="normal"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407212376"/><connections><connection><identifier v="mask"/><connRef v="1407203524"/><connRefOutput v="1407203295"/></connection></connections><GUILayout><gpos v="-3808.72217 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407212377"/><comptype v="1"/></compOutput><compOutput><uid v="1407212378"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill?dependency=1323881949"/><parameters/><outputBridgings><outputBridging><uid v="1407212377"/><identifier v="output"/></outputBridging><outputBridging><uid v="1407212378"/><identifier v="referenceMap"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407212731"/><connections><connection><identifier v="input"/><connRef v="1407212376"/><connRefOutput v="1407212377"/></connection></connections><GUILayout><gpos v="-3648.72217 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407212732"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_random_grayscale?dependency=1323881949"/><parameters/><outputBridgings><outputBridging><uid v="1407212732"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407213312"/><connections><connection><identifier v="input"/><connRef v="1407212376"/><connRefOutput v="1407212377"/></connection></connections><GUILayout><gpos v="-3632 144 0"/></GUILayout><compOutputs><compOutput><uid v="1407213313"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.75999999"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407213313"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407213377"/><connections><connection><identifier v="input1"/><connRef v="1407213312"/><connRefOutput v="1407213313"/></connection></connections><GUILayout><gpos v="-3504 144 0"/></GUILayout><compOutputs><compOutput><uid v="1407213378"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.292048931 0.292048931 0.292048931 1"/></paramValue></parameter><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.383792043 0.383792043 0.383792043 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407213680"/><connections><connection><identifier v="destination"/><connRef v="1407282150"/><connRefOutput v="1407282151"/></connection><connection><identifier v="source"/><connRef v="1407431746"/><connRefOutput v="1407431747"/></connection></connections><GUILayout><gpos v="-2672 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407213681"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407213687"/><connections><connection><identifier v="source"/><connRef v="1407213377"/><connRefOutput v="1407213378"/></connection><connection><identifier v="mask"/><connRef v="1407203524"/><connRefOutput v="1407203295"/></connection></connections><GUILayout><gpos v="-3376 144 0"/></GUILayout><compOutputs><compOutput><uid v="1407213688"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters><parameter><name v="combinedistance"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="256"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407214062"/><connections><connection><identifier v="destination"/><connRef v="1407213680"/><connRefOutput v="1407213681"/></connection><connection><identifier v="source"/><connRef v="1407312563"/><connRefOutput v="1407287588"/></connection></connections><GUILayout><gpos v="-2512 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407214063"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407220796"/><connections><connection><identifier v="input1"/><connRef v="1407220797"/><connRefOutput v="1407213313"/></connection></connections><GUILayout><gpos v="-3504 16 0"/></GUILayout><compOutputs><compOutput><uid v="1407213378"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.246177375 0.246177375 0.246177375 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407220797"/><connections><connection><identifier v="input"/><connRef v="1407212376"/><connRefOutput v="1407212377"/></connection></connections><GUILayout><gpos v="-3632 16 0"/></GUILayout><compOutputs><compOutput><uid v="1407213313"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.75999999"/></paramValue></parameter><parameter><name v="angle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.43599999"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407213313"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407220798"/><connections><connection><identifier v="source"/><connRef v="1407220796"/><connRefOutput v="1407213378"/></connection><connection><identifier v="mask"/><connRef v="1407203524"/><connRefOutput v="1407203295"/></connection></connections><GUILayout><gpos v="-3376 16 0"/></GUILayout><compOutputs><compOutput><uid v="1407213688"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters><parameter><name v="combinedistance"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="256"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407222068"/><connections><connection><identifier v="input1"/><connRef v="1407205796"/><connRefOutput v="1407205797"/></connection></connections><GUILayout><gpos v="-3664 464 0"/></GUILayout><compOutputs><compOutput><uid v="1407222069"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1407225785"/><connections><connection><identifier v="input1"/><connRef v="1407225786"/><connRefOutput v="1407213313"/></connection></connections><GUILayout><gpos v="-3504 -112 0"/></GUILayout><compOutputs><compOutput><uid v="1407213378"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.16055046 0.16055046 0.16055046 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407225786"/><connections><connection><identifier v="input"/><connRef v="1407212376"/><connRefOutput v="1407212377"/></connection></connections><GUILayout><gpos v="-3632 -112 0"/></GUILayout><compOutputs><compOutput><uid v="1407213313"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.909999967"/></paramValue></parameter><parameter><name v="angle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.133083329"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407213313"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407225787"/><connections><connection><identifier v="source"/><connRef v="1407225785"/><connRefOutput v="1407213378"/></connection><connection><identifier v="mask"/><connRef v="1407203524"/><connRefOutput v="1407203295"/></connection></connections><GUILayout><gpos v="-3376 -112 0"/></GUILayout><compOutputs><compOutput><uid v="1407213688"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters><parameter><name v="combinedistance"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="256"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407225796"/><connections><connection><identifier v="destination"/><connRef v="1407214062"/><connRefOutput v="1407214063"/></connection><connection><identifier v="source"/><connRef v="1407341920"/><connRefOutput v="1407287588"/></connection></connections><GUILayout><gpos v="-2352 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407214063"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407229211"/><connections><connection><identifier v="source"/><connRef v="1407229215"/><connRefOutput v="1407213378"/></connection><connection><identifier v="mask"/><connRef v="1407203524"/><connRefOutput v="1407203295"/></connection></connections><GUILayout><gpos v="-3376 -368 0"/></GUILayout><compOutputs><compOutput><uid v="1407213688"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters><parameter><name v="combinedistance"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="256"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407229212"/><connections><connection><identifier v="source"/><connRef v="1407229219"/><connRefOutput v="1407213378"/></connection><connection><identifier v="mask"/><connRef v="1407203524"/><connRefOutput v="1407203295"/></connection></connections><GUILayout><gpos v="-3376 -496 0"/></GUILayout><compOutputs><compOutput><uid v="1407213688"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters><parameter><name v="combinedistance"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="256"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407229213"/><connections><connection><identifier v="input1"/><connRef v="1407229218"/><connRefOutput v="1407213313"/></connection></connections><GUILayout><gpos v="-3504 -240 0"/></GUILayout><compOutputs><compOutput><uid v="1407213378"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.288990825 0.288990825 0.288990825 1"/></paramValue></parameter><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.270642191 0.270642191 0.270642191 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407229214"/><connections><connection><identifier v="input"/><connRef v="1407212376"/><connRefOutput v="1407212377"/></connection></connections><GUILayout><gpos v="-3632 -496 0"/></GUILayout><compOutputs><compOutput><uid v="1407213313"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.909999967"/></paramValue></parameter><parameter><name v="angle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.366277784"/></paramValue></parameter><parameter><name v="randomseed"/><relativeTo v="2"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407213313"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407229215"/><connections><connection><identifier v="input1"/><connRef v="1407229216"/><connRefOutput v="1407213313"/></connection></connections><GUILayout><gpos v="-3504 -368 0"/></GUILayout><compOutputs><compOutput><uid v="1407213378"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.246177375 0.246177375 0.246177375 1"/></paramValue></parameter><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.172782868 0.172782868 0.172782868 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407229216"/><connections><connection><identifier v="input"/><connRef v="1407212376"/><connRefOutput v="1407212377"/></connection></connections><GUILayout><gpos v="-3632 -368 0"/></GUILayout><compOutputs><compOutput><uid v="1407213313"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.75999999"/></paramValue></parameter><parameter><name v="angle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="-0.0278611109"/></paramValue></parameter><parameter><name v="randomseed"/><relativeTo v="2"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407213313"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407229217"/><connections><connection><identifier v="source"/><connRef v="1407229213"/><connRefOutput v="1407213378"/></connection><connection><identifier v="mask"/><connRef v="1407203524"/><connRefOutput v="1407203295"/></connection></connections><GUILayout><gpos v="-3376 -240 0"/></GUILayout><compOutputs><compOutput><uid v="1407213688"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters><parameter><name v="combinedistance"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="256"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407229218"/><connections><connection><identifier v="input"/><connRef v="1407212376"/><connRefOutput v="1407212377"/></connection></connections><GUILayout><gpos v="-3632 -240 0"/></GUILayout><compOutputs><compOutput><uid v="1407213313"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.479999989"/></paramValue></parameter><parameter><name v="angle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="-0.266583323"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407213313"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407229219"/><connections><connection><identifier v="input1"/><connRef v="1407229214"/><connRefOutput v="1407213313"/></connection></connections><GUILayout><gpos v="-3504 -496 0"/></GUILayout><compOutputs><compOutput><uid v="1407213378"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.194189608 0.194189608 0.194189608 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407229236"/><connections><connection><identifier v="destination"/><connRef v="1407229237"/><connRefOutput v="1407213681"/></connection><connection><identifier v="source"/><connRef v="1407450726"/><connRefOutput v="1407431747"/></connection></connections><GUILayout><gpos v="-1984.72217 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407214063"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407229237"/><connections><connection><identifier v="destination"/><connRef v="1407225796"/><connRefOutput v="1407214063"/></connection><connection><identifier v="source"/><connRef v="1407438084"/><connRefOutput v="1407431747"/></connection></connections><GUILayout><gpos v="-2192 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407213681"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407229238"/><connections><connection><identifier v="destination"/><connRef v="1407229236"/><connRefOutput v="1407214063"/></connection><connection><identifier v="source"/><connRef v="1407229212"/><connRefOutput v="1407213688"/></connection></connections><GUILayout><gpos v="-1824.72217 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407214063"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407237835"/><connections><connection><identifier v="source"/><connRef v="1407237837"/><connRefOutput v="1407213378"/></connection><connection><identifier v="mask"/><connRef v="1407203524"/><connRefOutput v="1407203295"/></connection></connections><GUILayout><gpos v="-3376 -624 0"/></GUILayout><compOutputs><compOutput><uid v="1407213688"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters><parameter><name v="combinedistance"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="256"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407237836"/><connections><connection><identifier v="input"/><connRef v="1407212376"/><connRefOutput v="1407212377"/></connection></connections><GUILayout><gpos v="-3632 -624 0"/></GUILayout><compOutputs><compOutput><uid v="1407213313"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.909999967"/></paramValue></parameter><parameter><name v="angle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.541166663"/></paramValue></parameter><parameter><name v="randomseed"/><relativeTo v="2"/><paramValue><constantValueInt32 v="9"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407213313"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407237837"/><connections><connection><identifier v="input1"/><connRef v="1407237836"/><connRefOutput v="1407213313"/></connection></connections><GUILayout><gpos v="-3504 -624 0"/></GUILayout><compOutputs><compOutput><uid v="1407213378"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.408256888 0.408256888 0.408256888 1"/></paramValue></parameter><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.659021378 0.659021378 0.659021378 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407242426"/><connections><connection><identifier v="destination"/><connRef v="1407229238"/><connRefOutput v="1407214063"/></connection><connection><identifier v="source"/><connRef v="1407433074"/><connRefOutput v="1407431747"/></connection></connections><GUILayout><gpos v="-1680 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407214063"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407246477"/><connections><connection><identifier v="source"/><connRef v="1407246479"/><connRefOutput v="1407213378"/></connection><connection><identifier v="mask"/><connRef v="1407203524"/><connRefOutput v="1407203295"/></connection></connections><GUILayout><gpos v="-3373.72095 -752 0"/></GUILayout><compOutputs><compOutput><uid v="1407213688"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters><parameter><name v="combinedistance"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="256"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407246478"/><connections><connection><identifier v="input"/><connRef v="1407212376"/><connRefOutput v="1407212377"/></connection></connections><GUILayout><gpos v="-3629.72095 -752 0"/></GUILayout><compOutputs><compOutput><uid v="1407213313"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.909999967"/></paramValue></parameter><parameter><name v="angle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.88794446"/></paramValue></parameter><parameter><name v="randomseed"/><relativeTo v="2"/><paramValue><constantValueInt32 v="20"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407213313"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407246479"/><connections><connection><identifier v="input1"/><connRef v="1407246478"/><connRefOutput v="1407213313"/></connection></connections><GUILayout><gpos v="-3501.72095 -752 0"/></GUILayout><compOutputs><compOutput><uid v="1407213378"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.200305805 0.200305805 0.200305805 1"/></paramValue></parameter><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.659021378 0.659021378 0.659021378 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407246626"/><connections><connection><identifier v="destination"/><connRef v="1407242426"/><connRefOutput v="1407214063"/></connection><connection><identifier v="source"/><connRef v="1407309663"/><connRefOutput v="1407287588"/></connection></connections><GUILayout><gpos v="-1520 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407214063"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407249360"/><connections><connection><identifier v="source"/><connRef v="1407249365"/><connRefOutput v="1407213378"/></connection><connection><identifier v="mask"/><connRef v="1407203524"/><connRefOutput v="1407203295"/></connection></connections><GUILayout><gpos v="-3376 -880 0"/></GUILayout><compOutputs><compOutput><uid v="1407213688"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters><parameter><name v="combinedistance"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="256"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407249361"/><connections><connection><identifier v="input1"/><connRef v="1407249364"/><connRefOutput v="1407213313"/></connection></connections><GUILayout><gpos v="-3501.7207 -1008 0"/></GUILayout><compOutputs><compOutput><uid v="1407213378"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.389908254 0.389908254 0.389908254 1"/></paramValue></parameter><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.536697268 0.536697268 0.536697268 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407249362"/><connections><connection><identifier v="source"/><connRef v="1407249361"/><connRefOutput v="1407213378"/></connection><connection><identifier v="mask"/><connRef v="1407203524"/><connRefOutput v="1407203295"/></connection></connections><GUILayout><gpos v="-3373.7207 -1008 0"/></GUILayout><compOutputs><compOutput><uid v="1407213688"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters><parameter><name v="combinedistance"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="256"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407249363"/><connections><connection><identifier v="input"/><connRef v="1407212376"/><connRefOutput v="1407212377"/></connection></connections><GUILayout><gpos v="-3632 -880 0"/></GUILayout><compOutputs><compOutput><uid v="1407213313"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.709999979"/></paramValue></parameter><parameter><name v="angle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.541166663"/></paramValue></parameter><parameter><name v="randomseed"/><relativeTo v="2"/><paramValue><constantValueInt32 v="12"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407213313"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407249364"/><connections><connection><identifier v="input"/><connRef v="1407212376"/><connRefOutput v="1407212377"/></connection></connections><GUILayout><gpos v="-3629.7207 -1008 0"/></GUILayout><compOutputs><compOutput><uid v="1407213313"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.370000005"/></paramValue></parameter><parameter><name v="angle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.0070833"/></paramValue></parameter><parameter><name v="randomseed"/><relativeTo v="2"/><paramValue><constantValueInt32 v="26"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407213313"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407249365"/><connections><connection><identifier v="input1"/><connRef v="1407249363"/><connRefOutput v="1407213313"/></connection></connections><GUILayout><gpos v="-3504 -880 0"/></GUILayout><compOutputs><compOutput><uid v="1407213378"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.282874614 0.282874614 0.282874614 1"/></paramValue></parameter><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.438837916 0.438837916 0.438837916 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407249395"/><connections><connection><identifier v="destination"/><connRef v="1407249396"/><connRefOutput v="1407214063"/></connection><connection><identifier v="source"/><connRef v="1407435567"/><connRefOutput v="1407431747"/></connection></connections><GUILayout><gpos v="-1232 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407214063"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407249396"/><connections><connection><identifier v="destination"/><connRef v="1407246626"/><connRefOutput v="1407214063"/></connection><connection><identifier v="source"/><connRef v="1407434323"/><connRefOutput v="1407431747"/></connection></connections><GUILayout><gpos v="-1392 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407214063"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407253754"/><connections><connection><identifier v="input1"/><connRef v="1407253760"/><connRefOutput v="1407213313"/></connection></connections><GUILayout><gpos v="-3504 -1392 0"/></GUILayout><compOutputs><compOutput><uid v="1407213378"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.282874614 0.282874614 0.282874614 1"/></paramValue></parameter><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.438837916 0.438837916 0.438837916 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407253755"/><connections><connection><identifier v="input1"/><connRef v="1407253758"/><connRefOutput v="1407213313"/></connection></connections><GUILayout><gpos v="-3504 -1136 0"/></GUILayout><compOutputs><compOutput><uid v="1407213378"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.258409798 0.258409798 0.258409798 1"/></paramValue></parameter><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.659021378 0.659021378 0.659021378 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407253756"/><connections><connection><identifier v="source"/><connRef v="1407253761"/><connRefOutput v="1407213378"/></connection><connection><identifier v="mask"/><connRef v="1407203524"/><connRefOutput v="1407203295"/></connection></connections><GUILayout><gpos v="-3376 -1520 0"/></GUILayout><compOutputs><compOutput><uid v="1407213688"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters><parameter><name v="combinedistance"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="256"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407253757"/><connections><connection><identifier v="input1"/><connRef v="1407253759"/><connRefOutput v="1407213313"/></connection></connections><GUILayout><gpos v="-3501.72095 -1264 0"/></GUILayout><compOutputs><compOutput><uid v="1407213378"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.200305805 0.200305805 0.200305805 1"/></paramValue></parameter><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.659021378 0.659021378 0.659021378 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407253758"/><connections><connection><identifier v="input"/><connRef v="1407212376"/><connRefOutput v="1407212377"/></connection></connections><GUILayout><gpos v="-3632 -1136 0"/></GUILayout><compOutputs><compOutput><uid v="1407213313"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.909999967"/></paramValue></parameter><parameter><name v="angle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.541166663"/></paramValue></parameter><parameter><name v="randomseed"/><relativeTo v="2"/><paramValue><constantValueInt32 v="10"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407213313"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407253759"/><connections><connection><identifier v="input"/><connRef v="1407212376"/><connRefOutput v="1407212377"/></connection></connections><GUILayout><gpos v="-3629.72095 -1264 0"/></GUILayout><compOutputs><compOutput><uid v="1407213313"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.909999967"/></paramValue></parameter><parameter><name v="angle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.00711107"/></paramValue></parameter><parameter><name v="randomseed"/><relativeTo v="2"/><paramValue><constantValueInt32 v="35"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407213313"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407253760"/><connections><connection><identifier v="input"/><connRef v="1407212376"/><connRefOutput v="1407212377"/></connection></connections><GUILayout><gpos v="-3632 -1392 0"/></GUILayout><compOutputs><compOutput><uid v="1407213313"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.709999979"/></paramValue></parameter><parameter><name v="angle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.541166663"/></paramValue></parameter><parameter><name v="randomseed"/><relativeTo v="2"/><paramValue><constantValueInt32 v="22"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407213313"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407253761"/><connections><connection><identifier v="input1"/><connRef v="1407253762"/><connRefOutput v="1407213313"/></connection></connections><GUILayout><gpos v="-3504 -1520 0"/></GUILayout><compOutputs><compOutput><uid v="1407213378"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.37155962 0.37155962 0.37155962 1"/></paramValue></parameter><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.536697268 0.536697268 0.536697268 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407253762"/><connections><connection><identifier v="input"/><connRef v="1407212376"/><connRefOutput v="1407212377"/></connection></connections><GUILayout><gpos v="-3632 -1520 0"/></GUILayout><compOutputs><compOutput><uid v="1407213313"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.370000005"/></paramValue></parameter><parameter><name v="angle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.0070833"/></paramValue></parameter><parameter><name v="randomseed"/><relativeTo v="2"/><paramValue><constantValueInt32 v="26"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407213313"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407253763"/><connections><connection><identifier v="source"/><connRef v="1407253754"/><connRefOutput v="1407213378"/></connection><connection><identifier v="mask"/><connRef v="1407203524"/><connRefOutput v="1407203295"/></connection></connections><GUILayout><gpos v="-3376 -1392 0"/></GUILayout><compOutputs><compOutput><uid v="1407213688"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters><parameter><name v="combinedistance"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="256"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407253764"/><connections><connection><identifier v="source"/><connRef v="1407253755"/><connRefOutput v="1407213378"/></connection><connection><identifier v="mask"/><connRef v="1407203524"/><connRefOutput v="1407203295"/></connection></connections><GUILayout><gpos v="-3376 -1136 0"/></GUILayout><compOutputs><compOutput><uid v="1407213688"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters><parameter><name v="combinedistance"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="256"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407253765"/><connections><connection><identifier v="source"/><connRef v="1407253757"/><connRefOutput v="1407213378"/></connection><connection><identifier v="mask"/><connRef v="1407203524"/><connRefOutput v="1407203295"/></connection></connections><GUILayout><gpos v="-3373.72095 -1264 0"/></GUILayout><compOutputs><compOutput><uid v="1407213688"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters><parameter><name v="combinedistance"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="256"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407253788"/><connections><connection><identifier v="destination"/><connRef v="1407253789"/><connRefOutput v="1407214063"/></connection><connection><identifier v="source"/><connRef v="1407253765"/><connRefOutput v="1407213688"/></connection></connections><GUILayout><gpos v="-464 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407214063"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407253789"/><connections><connection><identifier v="source"/><connRef v="1407253764"/><connRefOutput v="1407213688"/></connection><connection><identifier v="destination"/><connRef v="1407464089"/><connRefOutput v="1407464090"/></connection></connections><GUILayout><gpos v="-624 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407214063"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407253790"/><connections><connection><identifier v="destination"/><connRef v="1407253791"/><connRefOutput v="1407214063"/></connection><connection><identifier v="source"/><connRef v="1407253756"/><connRefOutput v="1407213688"/></connection></connections><GUILayout><gpos v="-63.277832 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407214063"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407253791"/><connections><connection><identifier v="destination"/><connRef v="1407253788"/><connRefOutput v="1407214063"/></connection><connection><identifier v="source"/><connRef v="1407253763"/><connRefOutput v="1407213688"/></connection></connections><GUILayout><gpos v="-272 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407214063"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407263529"/><GUILayout><gpos v="-4208 879.05249 0"/></GUILayout><compOutputs><compOutput><uid v="1407263530"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1449399972"/><parameters><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="20"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="20"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="8.0199995"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.589999974"/></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="5.58999968"/></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.469999999"/></paramValue></parameter><parameter><name v="rotation_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.319999993"/></paramValue></parameter><parameter><name v="mask_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.159999996"/></paramValue></parameter><parameter><name v="color_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.75"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407263530"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407264172"/><connections><connection><identifier v="Input"/><connRef v="1407263529"/><connRefOutput v="1407263530"/></connection></connections><GUILayout><gpos v="-4048 879.05249 0"/></GUILayout><compOutputs><compOutput><uid v="1407264173"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1449400845"/><parameters/><outputBridgings><outputBridging><uid v="1407264173"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407264204"/><connections><connection><identifier v="input1"/><connRef v="1407264172"/><connRefOutput v="1407264173"/></connection></connections><GUILayout><gpos v="-3888 879.05249 0"/></GUILayout><compOutputs><compOutput><uid v="1407264205"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelouthigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.451070338 0.451070338 0.451070338 1"/></paramValue></parameter><parameter><name v="levelinlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.441634238 0.441634238 0.441634238 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407264388"/><connections><connection><identifier v="destination"/><connRef v="1407222068"/><connRefOutput v="1407222069"/></connection><connection><identifier v="source"/><connRef v="1407270305"/><connRefOutput v="1407270306"/></connection></connections><GUILayout><gpos v="-3519.27783 464 0"/></GUILayout><compOutputs><compOutput><uid v="1407264389"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.370000005"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407270305"/><connections><connection><identifier v="inputintensity"/><connRef v="1407271662"/><connRefOutput v="1407271663"/></connection><connection><identifier v="input1"/><connRef v="1407264204"/><connRefOutput v="1407264205"/></connection></connections><GUILayout><gpos v="-3696 880 0"/></GUILayout><compOutputs><compOutput><uid v="1407270306"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="directionalwarp"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="5.33999968"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407271606"/><GUILayout><gpos v="-3903.04932 1088.02344 0"/></GUILayout><compOutputs><compOutput><uid v="1407271607"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///crystal_1?dependency=1407271237"/><parameters><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueInt32 v="14"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407271607"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407271662"/><connections><connection><identifier v="Source"/><connRef v="1407271606"/><connRefOutput v="1407271607"/></connection></connections><GUILayout><gpos v="-3743.04932 1088.02344 0"/></GUILayout><compOutputs><compOutput><uid v="1407271663"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1299236171"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.25999999"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407271663"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407278315"/><connections><connection><identifier v="inputintensity"/><connRef v="1407271662"/><connRefOutput v="1407271663"/></connection><connection><identifier v="input1"/><connRef v="1407292258"/><connRefOutput v="1407214063"/></connection></connections><GUILayout><gpos v="1216.80273 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407270306"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="directionalwarp"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.64999998"/></paramValue></parameter><parameter><name v="warpangle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.116666667"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407280227"/><connections><connection><identifier v="inputintensity"/><connRef v="1407271662"/><connRefOutput v="1407271663"/></connection><connection><identifier v="input1"/><connRef v="1407278315"/><connRefOutput v="1407270306"/></connection></connections><GUILayout><gpos v="1376.80273 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407270306"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="directionalwarp"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="3.25999999"/></paramValue></parameter><parameter><name v="warpangle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.417861104"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407282150"/><connections><connection><identifier v="destination"/><connRef v="1407264388"/><connRefOutput v="1407264389"/></connection><connection><identifier v="source"/><connRef v="1407455314"/><connRefOutput v="1407455315"/></connection></connections><GUILayout><gpos v="-3376 464 0"/></GUILayout><compOutputs><compOutput><uid v="1407282151"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.370000005"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407282167"/><connections><connection><identifier v="mask"/><connRef v="1407203524"/><connRefOutput v="1407203295"/></connection><connection><identifier v="source"/><connRef v="1407212731"/><connRefOutput v="1407212732"/></connection></connections><GUILayout><gpos v="-3504 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407213688"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters><parameter><name v="combinedistance"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="256"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407283127"/><connections><connection><identifier v="Input"/><connRef v="1407280227"/><connRefOutput v="1407270306"/></connection></connections><GUILayout><gpos v="1536.80273 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407283128"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1449400845"/><parameters/><outputBridgings><outputBridging><uid v="1407283128"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407285009"/><connections><connection><identifier v="input"/><connRef v="1407212376"/><connRefOutput v="1407212377"/></connection></connections><GUILayout><gpos v="-3632 -1648 0"/></GUILayout><compOutputs><compOutput><uid v="1407213313"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.370000005"/></paramValue></parameter><parameter><name v="angle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.36494446"/></paramValue></parameter><parameter><name v="randomseed"/><relativeTo v="2"/><paramValue><constantValueInt32 v="26"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407213313"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407285010"/><connections><connection><identifier v="source"/><connRef v="1407285011"/><connRefOutput v="1407213378"/></connection><connection><identifier v="mask"/><connRef v="1407203524"/><connRefOutput v="1407203295"/></connection></connections><GUILayout><gpos v="-3376 -1648 0"/></GUILayout><compOutputs><compOutput><uid v="1407213688"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters><parameter><name v="combinedistance"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="256"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407285011"/><connections><connection><identifier v="input1"/><connRef v="1407285009"/><connRefOutput v="1407213313"/></connection></connections><GUILayout><gpos v="-3504 -1648 0"/></GUILayout><compOutputs><compOutput><uid v="1407213378"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.45718655 0.45718655 0.45718655 1"/></paramValue></parameter><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.426605493 0.426605493 0.426605493 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407285025"/><connections><connection><identifier v="destination"/><connRef v="1407253790"/><connRefOutput v="1407214063"/></connection><connection><identifier v="source"/><connRef v="1407285010"/><connRefOutput v="1407213688"/></connection></connections><GUILayout><gpos v="157.193848 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407214063"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407288418"/><connections><connection><identifier v="input"/><connRef v="1407212376"/><connRefOutput v="1407212377"/></connection></connections><GUILayout><gpos v="-3632 -1776 0"/></GUILayout><compOutputs><compOutput><uid v="1407213313"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.370000005"/></paramValue></parameter><parameter><name v="angle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.36494446"/></paramValue></parameter><parameter><name v="randomseed"/><relativeTo v="2"/><paramValue><constantValueInt32 v="46"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407213313"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407288419"/><connections><connection><identifier v="input1"/><connRef v="1407288418"/><connRefOutput v="1407213313"/></connection></connections><GUILayout><gpos v="-3504 -1776 0"/></GUILayout><compOutputs><compOutput><uid v="1407213378"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.45718655 0.45718655 0.45718655 1"/></paramValue></parameter><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.426605493 0.426605493 0.426605493 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407288420"/><connections><connection><identifier v="source"/><connRef v="1407288419"/><connRefOutput v="1407213378"/></connection><connection><identifier v="mask"/><connRef v="1407203524"/><connRefOutput v="1407203295"/></connection></connections><GUILayout><gpos v="-3376 -1776 0"/></GUILayout><compOutputs><compOutput><uid v="1407213688"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters><parameter><name v="combinedistance"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="256"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407288433"/><connections><connection><identifier v="destination"/><connRef v="1407285025"/><connRefOutput v="1407214063"/></connection><connection><identifier v="source"/><connRef v="1407288420"/><connRefOutput v="1407213688"/></connection></connections><GUILayout><gpos v="400 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407214063"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407292136"/><GUILayout><gpos v="413.079651 656 0"/></GUILayout><compOutputs><compOutput><uid v="1407292137"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1449404462"/><parameters><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.289999992"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407292137"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407292257"/><connections><connection><identifier v="destination"/><connRef v="1407292259"/><connRefOutput v="1407214063"/></connection><connection><identifier v="opacity"/><connRef v="1407292136"/><connRefOutput v="1407292137"/></connection></connections><GUILayout><gpos v="816 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407214063"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407292258"/><connections><connection><identifier v="destination"/><connRef v="1407292257"/><connRefOutput v="1407214063"/></connection><connection><identifier v="opacity"/><connRef v="1407292136"/><connRefOutput v="1407292137"/></connection></connections><GUILayout><gpos v="1008 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407214063"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407292259"/><connections><connection><identifier v="destination"/><connRef v="1407288433"/><connRefOutput v="1407214063"/></connection><connection><identifier v="opacity"/><connRef v="1407292136"/><connRefOutput v="1407292137"/></connection></connections><GUILayout><gpos v="624 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407214063"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407322618"/><connections><connection><identifier v="Input"/><connRef v="1407282167"/><connRefOutput v="1407213688"/></connection></connections><GUILayout><gpos v="3103.39746 -449.669189 0"/></GUILayout><compOutputs><compOutput><uid v="1407322619"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1449400845"/><parameters/><outputBridgings><outputBridging><uid v="1407322619"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407322631"/><connections><connection><identifier v="input1"/><connRef v="1407322618"/><connRefOutput v="1407322619"/></connection></connections><GUILayout><gpos v="3266.12305 -449.669189 0"/></GUILayout><compOutputs><compOutput><uid v="1407322632"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1407322639"/><paramsArrayCells><paramsArrayCell><uid v="1489769226"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.662745118 0.678431392 0.690196097 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1489769227"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.329411775 0.349019617 0.349019617 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1407431746"/><connections><connection><identifier v="Source"/><connRef v="1407213687"/><connRefOutput v="1407213688"/></connection><connection><identifier v="Effect"/><connRef v="1407431756"/><connRefOutput v="1407431757"/></connection></connections><GUILayout><gpos v="-3201.02734 144 0"/></GUILayout><compOutputs><compOutput><uid v="1407431747"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///slope_blur_grayscale_2?dependency=1449402040"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="32"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.49000001"/></paramValue></parameter><parameter><name v="mode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407431747"/><identifier v="Slope_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407431756"/><GUILayout><gpos v="-3408 848 0"/></GUILayout><compOutputs><compOutput><uid v="1407431757"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///fractal_sum_base_2?dependency=1449402686"/><parameters><parameter><name v="Roughness"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.219999999"/></paramValue></parameter><parameter><name v="MinLevel"/><relativeTo v="0"/><paramValue><constantValueInt32 v="5"/></paramValue></parameter><parameter><name v="MaxLevel"/><relativeTo v="0"/><paramValue><constantValueInt32 v="7"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407431757"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407433074"/><connections><connection><identifier v="Effect"/><connRef v="1407431756"/><connRefOutput v="1407431757"/></connection><connection><identifier v="Source"/><connRef v="1407237835"/><connRefOutput v="1407213688"/></connection></connections><GUILayout><gpos v="-3216 -624 0"/></GUILayout><compOutputs><compOutput><uid v="1407431747"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///slope_blur_grayscale_2?dependency=1449402040"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="32"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="2.86999989"/></paramValue></parameter><parameter><name v="mode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407431747"/><identifier v="Slope_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407434323"/><connections><connection><identifier v="Effect"/><connRef v="1407431756"/><connRefOutput v="1407431757"/></connection><connection><identifier v="Source"/><connRef v="1407249360"/><connRefOutput v="1407213688"/></connection></connections><GUILayout><gpos v="-3216 -880 0"/></GUILayout><compOutputs><compOutput><uid v="1407431747"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///slope_blur_grayscale_2?dependency=1449402040"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="32"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.49000001"/></paramValue></parameter><parameter><name v="mode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407431747"/><identifier v="Slope_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407435567"/><connections><connection><identifier v="Effect"/><connRef v="1407431756"/><connRefOutput v="1407431757"/></connection><connection><identifier v="Source"/><connRef v="1407249362"/><connRefOutput v="1407213688"/></connection></connections><GUILayout><gpos v="-3216 -1008 0"/></GUILayout><compOutputs><compOutput><uid v="1407431747"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///slope_blur_grayscale_2?dependency=1449402040"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="32"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="9.17999935"/></paramValue></parameter><parameter><name v="mode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407431747"/><identifier v="Slope_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407438084"/><connections><connection><identifier v="Effect"/><connRef v="1407431756"/><connRefOutput v="1407431757"/></connection><connection><identifier v="Source"/><connRef v="1407229217"/><connRefOutput v="1407213688"/></connection></connections><GUILayout><gpos v="-3216 -240 0"/></GUILayout><compOutputs><compOutput><uid v="1407431747"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///slope_blur_grayscale_2?dependency=1449402040"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="32"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.09000003"/></paramValue></parameter><parameter><name v="mode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407431747"/><identifier v="Slope_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407450726"/><connections><connection><identifier v="Effect"/><connRef v="1407431756"/><connRefOutput v="1407431757"/></connection><connection><identifier v="Source"/><connRef v="1407229211"/><connRefOutput v="1407213688"/></connection></connections><GUILayout><gpos v="-3216 -368 0"/></GUILayout><compOutputs><compOutput><uid v="1407431747"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///slope_blur_grayscale_2?dependency=1449402040"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="32"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.09000003"/></paramValue></parameter><parameter><name v="mode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407431747"/><identifier v="Slope_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407452756"/><connections><connection><identifier v="input"/><connRef v="1407212376"/><connRefOutput v="1407212377"/></connection></connections><GUILayout><gpos v="-3839.82983 688 0"/></GUILayout><compOutputs><compOutput><uid v="1407452757"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_bbox_size?dependency=1323881949"/><parameters/><outputBridgings><outputBridging><uid v="1407452757"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407452772"/><connections><connection><identifier v="Input"/><connRef v="1407452756"/><connRefOutput v="1407452757"/></connection></connections><GUILayout><gpos v="-3679.82983 688 0"/></GUILayout><compOutputs><compOutput><uid v="1407452773"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1449400845"/><parameters/><outputBridgings><outputBridging><uid v="1407452773"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407454032"/><connections><connection><identifier v="source"/><connRef v="1407452772"/><connRefOutput v="1407452773"/></connection><connection><identifier v="mask"/><connRef v="1407452772"/><connRefOutput v="1407452773"/></connection></connections><GUILayout><gpos v="-3536 688 0"/></GUILayout><compOutputs><compOutput><uid v="1407213688"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters><parameter><name v="combinedistance"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="256"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407455314"/><connections><connection><identifier v="Input"/><connRef v="1407454032"/><connRefOutput v="1407213688"/></connection></connections><GUILayout><gpos v="-3391.82983 688 0"/></GUILayout><compOutputs><compOutput><uid v="1407455315"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1449400845"/><parameters/><outputBridgings><outputBridging><uid v="1407455315"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407464089"/><connections><connection><identifier v="Effect"/><connRef v="1407464111"/><connRefOutput v="1407464112"/></connection><connection><identifier v="Source"/><connRef v="1407427627"/><connRefOutput v="1407427628"/></connection></connections><GUILayout><gpos v="-752 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407464090"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///slope_blur_grayscale_2?dependency=1449402040"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="32"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.519999981"/></paramValue></parameter><parameter><name v="mode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407464090"/><identifier v="Slope_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407464111"/><GUILayout><gpos v="-958.015686 432 0"/></GUILayout><compOutputs><compOutput><uid v="1407464112"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///fractal_sum_base_2?dependency=1449402686"/><parameters><parameter><name v="Roughness"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.399999976"/></paramValue></parameter><parameter><name v="MinLevel"/><relativeTo v="0"/><paramValue><constantValueInt32 v="5"/></paramValue></parameter><parameter><name v="MaxLevel"/><relativeTo v="0"/><paramValue><constantValueInt32 v="9"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407464112"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407485813"/><connections><connection><identifier v="destination"/><connRef v="1407283127"/><connRefOutput v="1407283128"/></connection></connections><GUILayout><gpos v="1981.02258 314.666656 0"/></GUILayout><compOutputs><compOutput><uid v="1407485814"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="5"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407486069"/><connections><connection><identifier v="height_top"/><connRef v="1407283127"/><connRefOutput v="1407283128"/></connection></connections><GUILayout><gpos v="3408 -255.500763 0"/></GUILayout><compOutputs><compOutput><uid v="1407486070"/><comptype v="2"/></compOutput><compOutput><uid v="1407486071"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///height_blend?dependency=1449623608"/><parameters><parameter><name v="contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407486070"/><identifier v="blended_height"/></outputBridging><outputBridging><uid v="1407486071"/><identifier v="height_mask"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407490500"/><connections><connection><identifier v="source"/><connRef v="1407490514"/><connRefOutput v="1407490515"/></connection><connection><identifier v="opacity"/><connRef v="1407490571"/><connRefOutput v="1407490572"/></connection><connection><identifier v="destination"/><connRef v="1407502679"/><connRefOutput v="1407502680"/></connection></connections><GUILayout><gpos v="3653.3335 -304 0"/></GUILayout><compOutputs><compOutput><uid v="1407490501"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1407490514"/><connections><connection><identifier v="input1"/><connRef v="1407486069"/><connRefOutput v="1407486071"/></connection></connections><GUILayout><gpos v="3575.3335 -325.333344 0"/><docked v="1"/><dockDistance v="103.514404 -32"/></GUILayout><compOutputs><compOutput><uid v="1407490515"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1407490596"/><paramsArrayCells><paramsArrayCell><uid v="1407490604"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0 0 0 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1407490605"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.215373904 0.293987304 0.425619841 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1407490571"/><connections><connection><identifier v="Source"/><connRef v="1407486069"/><connRefOutput v="1407486071"/></connection></connections><GUILayout><gpos v="3575.3335 -282.666656 0"/><docked v="1"/><dockDistance v="215.514648 16"/></GUILayout><compOutputs><compOutput><uid v="1407490572"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///invert_grayscale?dependency=1177447620"/><parameters/><outputBridgings><outputBridging><uid v="1407490572"/><identifier v="Invert_Grayscale"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407309663"/><connections><connection><identifier v="Source"/><connRef v="1407246477"/><connRefOutput v="1407213688"/></connection></connections><GUILayout><gpos v="-3216 -752 0"/></GUILayout><compOutputs><compOutput><uid v="1407287588"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///slope_blur_grayscale_2?dependency=1449402040"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="32"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.100000001"/></paramValue></parameter><parameter><name v="mode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407287588"/><identifier v="Slope_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407312563"/><connections><connection><identifier v="Source"/><connRef v="1407220798"/><connRefOutput v="1407213688"/></connection></connections><GUILayout><gpos v="-3200.85913 16 0"/></GUILayout><compOutputs><compOutput><uid v="1407287588"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///slope_blur_grayscale_2?dependency=1449402040"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="32"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.100000001"/></paramValue></parameter><parameter><name v="mode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407287588"/><identifier v="Slope_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407341920"/><connections><connection><identifier v="Source"/><connRef v="1407225787"/><connRefOutput v="1407213688"/></connection></connections><GUILayout><gpos v="-3216 -112 0"/></GUILayout><compOutputs><compOutput><uid v="1407287588"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///slope_blur_grayscale_2?dependency=1449402040"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="32"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.100000001"/></paramValue></parameter><parameter><name v="mode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407287588"/><identifier v="Slope_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407355537"/><connections><connection><identifier v="destination"/><connRef v="1407442049"/><connRefOutput v="1407442050"/></connection></connections><GUILayout><gpos v="2557.02246 314.666656 0"/></GUILayout><compOutputs><compOutput><uid v="1407355538"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0799999982"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407368937"/><connections><connection><identifier v="Input"/><connRef v="1407495853"/><connRefOutput v="1407355538"/></connection></connections><GUILayout><gpos v="2877.02246 314.666656 0"/></GUILayout><compOutputs><compOutput><uid v="1407368938"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1449400845"/><parameters/><outputBridgings><outputBridging><uid v="1407368938"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407442049"/><connections><connection><identifier v="destination"/><connRef v="1407485813"/><connRefOutput v="1407485814"/></connection></connections><GUILayout><gpos v="2365.02246 314.666656 0"/></GUILayout><compOutputs><compOutput><uid v="1407442050"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.229999989"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407498822"/><connections><connection><identifier v="input"/><connRef v="1407212376"/><connRefOutput v="1407212377"/></connection></connections><GUILayout><gpos v="3103.21973 -609.669189 0"/></GUILayout><compOutputs><compOutput><uid v="1407498823"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.25"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407498823"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407498865"/><connections><connection><identifier v="source"/><connRef v="1407498822"/><connRefOutput v="1407498823"/></connection><connection><identifier v="mask"/><connRef v="1407203524"/><connRefOutput v="1407203295"/></connection></connections><GUILayout><gpos v="3232.59277 -674.207336 0"/></GUILayout><compOutputs><compOutput><uid v="1407498866"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters><parameter><name v="combinedistance"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407502611"/><connections><connection><identifier v="input1"/><connRef v="1407498865"/><connRefOutput v="1407498866"/></connection></connections><GUILayout><gpos v="3365.3335 -609.375977 0"/></GUILayout><compOutputs><compOutput><uid v="1407502612"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1407502617"/><paramsArrayCells><paramsArrayCell><uid v="1407502618"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.257301062 0.278933644 0.388235331 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1407502619"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.674509823 0.54420805 0.402060747 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1407502679"/><connections><connection><identifier v="destination"/><connRef v="1407322631"/><connRefOutput v="1407322632"/></connection><connection><identifier v="source"/><connRef v="1407504667"/><connRefOutput v="1407502612"/></connection></connections><GUILayout><gpos v="3440 -450.343506 0"/></GUILayout><compOutputs><compOutput><uid v="1407502680"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0700000003"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407502993"/><connections><connection><identifier v="input"/><connRef v="1407368937"/><connRefOutput v="1407368938"/></connection></connections><GUILayout><gpos v="3184 -16 0"/></GUILayout><compOutputs><compOutput><uid v="1355424961"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///hbao?dependency=1297162667"/><parameters><parameter><name v="height_depth"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.370000005"/></paramValue></parameter><parameter><name v="samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="16"/></paramValue></parameter><parameter><name v="surface_size"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="268.820007"/></paramValue></parameter><parameter><name v="height_depth_cm"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="93.1899948"/></paramValue></parameter><parameter><name v="radius"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.579999983"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1355424961"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407503912"/><connections><connection><identifier v="Source"/><connRef v="1407502993"/><connRefOutput v="1355424961"/></connection></connections><GUILayout><gpos v="3344 -16 0"/></GUILayout><compOutputs><compOutput><uid v="1407503913"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///highpass_grayscale?dependency=1449753752"/><parameters/><outputBridgings><outputBridging><uid v="1407503913"/><identifier v="Highpass"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407503926"/><connections><connection><identifier v="destination"/><connRef v="1407490500"/><connRefOutput v="1407490501"/></connection><connection><identifier v="source"/><connRef v="1407504046"/><connRefOutput v="1407504047"/></connection></connections><GUILayout><gpos v="3845.3335 -304 0"/></GUILayout><compOutputs><compOutput><uid v="1407503927"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.25999999"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407503976"/><connections><connection><identifier v="Input"/><connRef v="1407503912"/><connRefOutput v="1407503913"/></connection></connections><GUILayout><gpos v="3504 -16 0"/></GUILayout><compOutputs><compOutput><uid v="1407503977"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///quantize_grayscale?dependency=1407503933"/><parameters><parameter><name v="Quantize"/><relativeTo v="0"/><paramValue><constantValueInt32 v="7"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407503977"/><identifier v="Quantize"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407504046"/><connections><connection><identifier v="input1"/><connRef v="1407503976"/><connRefOutput v="1407503977"/></connection></connections><GUILayout><gpos v="3767.3335 -325.333344 0"/><docked v="1"/><dockDistance v="160 -160"/></GUILayout><compOutputs><compOutput><uid v="1407504047"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1407504222"/><connections><connection><identifier v="input1"/><connRef v="1407368937"/><connRefOutput v="1407368938"/></connection></connections><GUILayout><gpos v="3120 -816 0"/></GUILayout><compOutputs><compOutput><uid v="1355431358"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="normal"/><parameters><parameter><name v="input2alpha"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="inversedy"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="10"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407504239"/><connections><connection><identifier v="RGBA"/><connRef v="1407504222"/><connRefOutput v="1355431358"/></connection></connections><GUILayout><gpos v="3280 -816 0"/></GUILayout><compOutputs><compOutput><uid v="1407504240"/><comptype v="2"/></compOutput><compOutput><uid v="1407504241"/><comptype v="2"/></compOutput><compOutput><uid v="1407504242"/><comptype v="2"/></compOutput><compOutput><uid v="1407504243"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///rgba_split?dependency=1147283954"/><parameters/><outputBridgings><outputBridging><uid v="1407504240"/><identifier v="R"/></outputBridging><outputBridging><uid v="1407504241"/><identifier v="G"/></outputBridging><outputBridging><uid v="1407504242"/><identifier v="B"/></outputBridging><outputBridging><uid v="1407504243"/><identifier v="A"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407504250"/><connections><connection><identifier v="Input_1"/><connRef v="1407504239"/><connRefOutput v="1407504241"/></connection></connections><GUILayout><gpos v="3408 -816 0"/></GUILayout><compOutputs><compOutput><uid v="1407504251"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1449404462"/><parameters><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.48999998"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407504251"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407504667"/><connections><connection><identifier v="input1"/><connRef v="1407526934"/><connRefOutput v="1407526935"/></connection></connections><GUILayout><gpos v="3728 -816 0"/></GUILayout><compOutputs><compOutput><uid v="1407502612"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1407502617"/><paramsArrayCells><paramsArrayCell><uid v="1407502618"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.257301062 0.278933644 0.388235331 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1407502619"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.80392164 0.499349415 0.167089537 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1407505789"/><connections><connection><identifier v="input"/><connRef v="1407504222"/><connRefOutput v="1355431358"/></connection></connections><GUILayout><gpos v="3197.65112 -976 0"/></GUILayout><compOutputs><compOutput><uid v="1407505790"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///curvature_smooth?dependency=1449590280"/><parameters><parameter><name v="normal_format"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407505790"/><identifier v="height"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407505805"/><connections><connection><identifier v="Input_1"/><connRef v="1407505789"/><connRefOutput v="1407505790"/></connection></connections><GUILayout><gpos v="3408 -1072 0"/></GUILayout><compOutputs><compOutput><uid v="1407505806"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1449404462"/><parameters><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.419999987"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407505806"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407506213"/><connections><connection><identifier v="destination"/><connRef v="1407503926"/><connRefOutput v="1407503927"/></connection><connection><identifier v="source"/><connRef v="1407511607"/><connRefOutput v="1407511608"/></connection></connections><GUILayout><gpos v="3941.3335 -624 0"/></GUILayout><compOutputs><compOutput><uid v="1407506214"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0399999991"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407511607"/><connections><connection><identifier v="input1"/><connRef v="1407505805"/><connRefOutput v="1407505806"/></connection></connections><GUILayout><gpos v="3863.3335 -645.333313 0"/><docked v="1"/><dockDistance v="384 352"/></GUILayout><compOutputs><compOutput><uid v="1407511608"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters><parameter><name v="colorswitch"/><paramValue><constantValueBool v="1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407516178"/><connections><connection><identifier v="Input_1"/><connRef v="1407505789"/><connRefOutput v="1407505790"/></connection></connections><GUILayout><gpos v="3408 -1200 0"/></GUILayout><compOutputs><compOutput><uid v="1407505806"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1449404462"/><parameters><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.519999981"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407505806"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407516225"/><connections><connection><identifier v="Source"/><connRef v="1407516178"/><connRefOutput v="1407505806"/></connection></connections><GUILayout><gpos v="3536 -1200 0"/></GUILayout><compOutputs><compOutput><uid v="1407516226"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///invert_grayscale?dependency=1177447620"/><parameters/><outputBridgings><outputBridging><uid v="1407516226"/><identifier v="Invert_Grayscale"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407516236"/><connections><connection><identifier v="destination"/><connRef v="1407506213"/><connRefOutput v="1407506214"/></connection><connection><identifier v="source"/><connRef v="1407521608"/><connRefOutput v="1407521609"/></connection></connections><GUILayout><gpos v="4290.61768 -624 0"/></GUILayout><compOutputs><compOutput><uid v="1407516237"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407521608"/><connections><connection><identifier v="input1"/><connRef v="1407516225"/><connRefOutput v="1407516226"/></connection></connections><GUILayout><gpos v="4087.3335 -645.333313 0"/><docked v="1"/><dockDistance v="320 352"/></GUILayout><compOutputs><compOutput><uid v="1407521609"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1407526512"/><connections><connection><identifier v="Input_1"/><connRef v="1407504239"/><connRefOutput v="1407504242"/></connection></connections><GUILayout><gpos v="3408 -944 0"/></GUILayout><compOutputs><compOutput><uid v="1407526513"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1449404462"/><parameters><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.00999999978"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407526513"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407526934"/><connections><connection><identifier v="destination"/><connRef v="1407504250"/><connRefOutput v="1407504251"/></connection><connection><identifier v="source"/><connRef v="1407527109"/><connRefOutput v="1407527110"/></connection></connections><GUILayout><gpos v="3586.85205 -816 0"/></GUILayout><compOutputs><compOutput><uid v="1407526935"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407527109"/><connections><connection><identifier v="Source"/><connRef v="1407526512"/><connRefOutput v="1407526513"/></connection></connections><GUILayout><gpos v="3508.85205 -837.333313 0"/><docked v="1"/><dockDistance v="-64 288"/></GUILayout><compOutputs><compOutput><uid v="1407527110"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///invert_grayscale?dependency=1177447620"/><parameters/><outputBridgings><outputBridging><uid v="1407527110"/><identifier v="Invert_Grayscale"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407527225"/><connections><connection><identifier v="input1"/><connRef v="1407368937"/><connRefOutput v="1407368938"/></connection></connections><GUILayout><gpos v="3984 80 0"/></GUILayout><compOutputs><compOutput><uid v="1407527226"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.836391449 0.836391449 0.836391449 0"/></paramValue></parameter><parameter><name v="levelouthigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.897553504 0.897553504 0.897553504 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407527465"/><connections><connection><identifier v="source"/><connRef v="1407526934"/><connRefOutput v="1407526935"/></connection><connection><identifier v="destination"/><connRef v="1407532249"/><connRefOutput v="1407532250"/></connection></connections><GUILayout><gpos v="4336 48 0"/></GUILayout><compOutputs><compOutput><uid v="1407527466"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0899999961"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407527515"/><connections><connection><identifier v="destination"/><connRef v="1407527465"/><connRefOutput v="1407527466"/></connection><connection><identifier v="source"/><connRef v="1407527536"/><connRefOutput v="1407527537"/></connection></connections><GUILayout><gpos v="4528 58.6666565 0"/></GUILayout><compOutputs><compOutput><uid v="1407527516"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.149999991"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407527536"/><connections><connection><identifier v="input1"/><connRef v="1407516225"/><connRefOutput v="1407516226"/></connection></connections><GUILayout><gpos v="4450 37.3333244 0"/><docked v="1"/><dockDistance v="400 720"/></GUILayout><compOutputs><compOutput><uid v="1407527537"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelouthigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.726299703 0.726299703 0.726299703 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407527732"/><connections><connection><identifier v="destination"/><connRef v="1407527515"/><connRefOutput v="1407527516"/></connection><connection><identifier v="source"/><connRef v="1407503976"/><connRefOutput v="1407503977"/></connection></connections><GUILayout><gpos v="4720 58.6666565 0"/></GUILayout><compOutputs><compOutput><uid v="1407527733"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.109999999"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407532249"/><connections><connection><identifier v="destination"/><connRef v="1407527225"/><connRefOutput v="1407527226"/></connection><connection><identifier v="source"/><connRef v="1407532268"/><connRefOutput v="1407532269"/></connection></connections><GUILayout><gpos v="4176 48 0"/></GUILayout><compOutputs><compOutput><uid v="1407532250"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.140000001"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407532268"/><connections><connection><identifier v="Source"/><connRef v="1407486069"/><connRefOutput v="1407486071"/></connection></connections><GUILayout><gpos v="4098 26.666666 0"/><docked v="1"/><dockDistance v="208 280.834564"/></GUILayout><compOutputs><compOutput><uid v="1407532269"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///invert_grayscale?dependency=1177447620"/><parameters/><outputBridgings><outputBridging><uid v="1407532269"/><identifier v="Invert_Grayscale"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407420471"/><connections><connection><identifier v="Input"/><connRef v="1407368937"/><connRefOutput v="1407368938"/></connection></connections><GUILayout><gpos v="3376 319.623596 0"/></GUILayout><compOutputs><compOutput><uid v="1407420472"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1449400845"/><parameters/><outputBridgings><outputBridging><uid v="1407420472"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407427580"/><connections><connection><identifier v="input"/><connRef v="1407282167"/><connRefOutput v="1407213688"/></connection></connections><GUILayout><gpos v="-1472.15051 464 0"/><dockDistance v="-270 -261.88208"/></GUILayout><compOutputs><compOutput><uid v="1407427581"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///edge_detect?dependency=1407202483"/><parameters><parameter><name v="edge_roundness"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.229999989"/></paramValue></parameter><parameter><name v="edge_width"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.26999998"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407427581"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407427627"/><connections><connection><identifier v="destination"/><connRef v="1407249395"/><connRefOutput v="1407214063"/></connection><connection><identifier v="source"/><connRef v="1407461299"/><connRefOutput v="1407461300"/></connection></connections><GUILayout><gpos v="-957.452881 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407427628"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1407427649"/><connections><connection><identifier v="Source"/><connRef v="1407427580"/><connRefOutput v="1407427581"/></connection></connections><GUILayout><gpos v="-1328 912 0"/><docked v="1"/><dockDistance v="192 -64"/></GUILayout><compOutputs><compOutput><uid v="1407427650"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///invert_grayscale?dependency=1177447620"/><parameters/><outputBridgings><outputBridging><uid v="1407427650"/><identifier v="Invert_Grayscale"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407459602"/><connections><connection><identifier v="input"/><connRef v="1407427649"/><connRefOutput v="1407427650"/></connection></connections><GUILayout><gpos v="-1280.15051 464 0"/></GUILayout><compOutputs><compOutput><uid v="1407459603"/><comptype v="2"/></compOutput><compOutput><uid v="1407459604"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///bevel?dependency=1449405586"/><parameters><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="-0.129999995"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407459603"/><identifier v="height"/></outputBridging><outputBridging><uid v="1407459604"/><identifier v="normal"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407461299"/><connections><connection><identifier v="Input"/><connRef v="1407459602"/><connRefOutput v="1407459603"/></connection></connections><GUILayout><gpos v="-1035.45288 282.666656 -1"/><docked v="1"/><dockDistance v="-96 -288"/></GUILayout><compOutputs><compOutput><uid v="1407461300"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1449400845"/><parameters/><outputBridgings><outputBridging><uid v="1407461300"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1407495853"/><connections><connection><identifier v="destination"/><connRef v="1407355537"/><connRefOutput v="1407355538"/></connection></connections><GUILayout><gpos v="2717.02246 314.666656 0"/></GUILayout><compOutputs><compOutput><uid v="1407355538"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0199999996"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode></compNodes><baseParameters/><options><option><name v="defaultParentSize"/><value v="11x11"/></option><option><name v="export/fromGraph/autoExport"/><value v="true"/></option><option><name v="export/fromGraph/destination"/><value v="D:/work/Annery/stylized material/stylized material/images/rocks"/></option><option><name v="export/fromGraph/extension"/><value v="png"/></option><option><name v="export/fromGraph/outputs/AO"/><value v="true"/></option><option><name v="export/fromGraph/outputs/basecolor_1"/><value v="true"/></option><option><name v="export/fromGraph/outputs/height_1"/><value v="true"/></option><option><name v="export/fromGraph/outputs/normal_1"/><value v="true"/></option><option><name v="export/fromGraph/outputs/roughness_1"/><value v="true"/></option><option><name v="export/fromGraph/outputsColorspace/AO"/><value v="Raw"/></option><option><name v="export/fromGraph/outputsColorspace/basecolor_1"/><value v="sRGB"/></option><option><name v="export/fromGraph/outputsColorspace/height_1"/><value v="Raw"/></option><option><name v="export/fromGraph/outputsColorspace/normal_1"/><value v="Raw"/></option><option><name v="export/fromGraph/outputsColorspace/roughness_1"/><value v="Raw"/></option><option><name v="export/fromGraph/pattern"/><value v="$(graph)_$(identifier)"/></option></options><root><rootOutputs><rootOutput><output v="1407198767"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1407198769"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1407198771"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1407198775"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1407198777"/><format v="0"/><usertag v=""/></rootOutput></rootOutputs></root></graph></content></package>
