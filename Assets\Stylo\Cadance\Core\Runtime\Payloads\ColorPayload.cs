using System;
using UnityEngine;

namespace Stylo.Cadance
{
    /// <summary>
    /// Color payload for Cadance events.
    /// Compatible with Koreographer's ColorPayload.
    /// </summary>
    [Serializable]
    public class ColorPayload : IPayload
    {
        [SerializeField] private Color colorValue;
        
        /// <summary>
        /// Gets or sets the Color value.
        /// </summary>
        public Color ColorValue
        {
            get => colorValue;
            set => colorValue = value;
        }
        
        /// <summary>
        /// Creates a new ColorPayload with the specified color.
        /// </summary>
        /// <param name="color">The Color value</param>
        public ColorPayload(Color color = default)
        {
            colorValue = color;
        }
        
        /// <summary>
        /// Returns a copy of this payload.
        /// </summary>
        /// <returns>A copy of the ColorPayload</returns>
        public IPayload GetCopy()
        {
            return new ColorPayload(colorValue);
        }
        
        /// <summary>
        /// Gets the display width for editor visualization.
        /// </summary>
        /// <returns>The display width in pixels</returns>
        public float GetDisplayWidth()
        {
            return 100f;
        }
        
        /// <summary>
        /// Returns the color value as a string.
        /// </summary>
        /// <returns>The color value as a string</returns>
        public override string ToString()
        {
            return $"RGBA({colorValue.r:F2}, {colorValue.g:F2}, {colorValue.b:F2}, {colorValue.a:F2})";
        }
        
        /// <summary>
        /// Implicit conversion from Color to ColorPayload.
        /// </summary>
        /// <param name="color">The Color value</param>
        /// <returns>A new ColorPayload</returns>
        public static implicit operator ColorPayload(Color color)
        {
            return new ColorPayload(color);
        }
        
        /// <summary>
        /// Implicit conversion from ColorPayload to Color.
        /// </summary>
        /// <param name="payload">The ColorPayload</param>
        /// <returns>The Color value</returns>
        public static implicit operator Color(ColorPayload payload)
        {
            return payload?.colorValue ?? Color.white;
        }
    }
}
