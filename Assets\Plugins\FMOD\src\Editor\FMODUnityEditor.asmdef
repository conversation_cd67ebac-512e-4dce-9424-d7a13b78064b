{"name": "FMODUnityEditor", "references": ["FMODUnity", "Unity.Timeline.Editor", "Unity.Timeline", "Unity.VisualScripting.Core", "Unity.VisualScripting.Core.Editor", "Unity.VisualScripting.Flow.Editor", "Unity.ScriptableBuildPipeline.Editor", "Unity.InputSystem"], "includePlatforms": ["Editor"], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": ["UNITY_2021_3_OR_NEWER"], "versionDefines": [{"name": "com.unity.timeline", "expression": "1.0.0", "define": "UNITY_TIMELINE_EXIST"}, {"name": "com.unity.addressables", "expression": "1.0.0", "define": "UNITY_ADDRESSABLES_EXIST"}, {"name": "com.unity.visualscripting", "expression": "1.0.0", "define": "UNITY_VISUALSCRIPTING_EXIST"}, {"name": "com.unity.inputsystem", "expression": "1.0.0", "define": "UNITY_INPUTSYSTEM_EXIST"}], "noEngineReferences": false}