#if URP_INSTALLED
using UnityEngine;
using UnityEditor;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using UnityEditor.Rendering;
using Stylo.Flux;
using Stylo.Flux.Universal;

namespace Stylo.Flux.Editor
{
    [CustomEditor(typeof(FluxEffect))]
    public class FluxVolumeComponentEditor : VolumeComponentEditor
    {
        private FluxPreset? selectedPreset;
        private FluxPreset[]? availablePresets;
        private string[]? presetNames;
        private int selectedPresetIndex = 0;

        public override void OnInspectorGUI()
        {
            // Load available presets if not already loaded
            if (availablePresets == null)
            {
                LoadAvailablePresets();
            }

            // Play Mode warning
            if (Application.isPlaying)
            {
                EditorGUILayout.BeginVertical("box");
                EditorGUILayout.HelpBox("⚠️ Play Mode Active: Changes will be lost when exiting Play Mode unless saved!\nUse Tools → Flux → Save All Current Settings to save changes.", MessageType.Warning);
                EditorGUILayout.EndVertical();
                EditorGUILayout.Space(5);
            }

            // Preset section at the top
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("Flux Preset", EditorStyles.boldLabel);

            // Quick preset dropdown
            if (availablePresets != null && availablePresets.Length > 0)
            {
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField("Quick Select:", GUILayout.Width(80));

                int newIndex = EditorGUILayout.Popup(selectedPresetIndex, presetNames);
                if (newIndex != selectedPresetIndex)
                {
                    selectedPresetIndex = newIndex;
                    if (selectedPresetIndex > 0 && selectedPresetIndex <= availablePresets.Length)
                    {
                        selectedPreset = availablePresets[selectedPresetIndex - 1];
                    }
                    else
                    {
                        selectedPreset = null;
                    }
                }

                // Quick apply button
                GUI.enabled = selectedPreset != null;
                if (GUILayout.Button("Apply", GUILayout.Width(60)))
                {
                    if (selectedPreset != null)
                    {
                        ApplyPresetToVolumeComponent(selectedPreset);
                    }
                }
                GUI.enabled = true;

                // Refresh button
                if (GUILayout.Button("↻", GUILayout.Width(25)))
                {
                    LoadAvailablePresets();
                }

                EditorGUILayout.EndHorizontal();
                EditorGUILayout.Space(5);
            }

            EditorGUILayout.BeginHorizontal();
            selectedPreset = (FluxPreset)EditorGUILayout.ObjectField("Select Preset", selectedPreset, typeof(FluxPreset), false);

            // Apply button
            GUI.enabled = selectedPreset != null;
            if (GUILayout.Button("Apply", GUILayout.Width(60)))
            {
                if (selectedPreset != null)
                {
                    ApplyPresetToVolumeComponent(selectedPreset);
                }
            }
            GUI.enabled = true;
            EditorGUILayout.EndHorizontal();

            // Capture current settings button
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.Space();
            if (GUILayout.Button("Capture Current Settings as New Preset", GUILayout.Height(25)))
            {
                CaptureCurrentSettingsAsPreset();
            }
            EditorGUILayout.EndHorizontal();

            // Show preset description
            if (selectedPreset != null && !string.IsNullOrEmpty(selectedPreset.description))
            {
                EditorGUILayout.HelpBox(selectedPreset.description, MessageType.Info);
            }

            EditorGUILayout.EndVertical();

            EditorGUILayout.Space(10);

            // Draw default volume component inspector
            base.OnInspectorGUI();
        }

        private void ApplyPresetToVolumeComponent(FluxPreset preset)
        {
            // Get the FluxEffect component from the target
            FluxEffect? fluxEffect = target as FluxEffect;
            if (fluxEffect == null)
            {
                Debug.LogError("[Flux] Could not find FluxEffect component to apply preset to.");
                return;
            }

            // Record undo operation
            Undo.RecordObject(fluxEffect, "Apply Flux Preset");

            // Apply the preset using the URP method
            preset.ApplyToURP(fluxEffect);

            // Mark the object as dirty so changes are saved
            EditorUtility.SetDirty(fluxEffect);

            Debug.Log($"[Flux] Applied preset '{preset.presetName}' to FluxEffect component.");
        }

        private void CaptureCurrentSettingsAsPreset()
        {
            // Get the FluxEffect component from the target
            FluxEffect? fluxEffect = target as FluxEffect;
            if (fluxEffect == null)
            {
                Debug.LogError("[Flux] Could not find FluxEffect component to capture settings from.");
                return;
            }

            // Create a new preset
            FluxPreset newPreset = ScriptableObject.CreateInstance<FluxPreset>();

            // Capture current settings
            newPreset.CaptureFromURP(fluxEffect);

            // Set default name and description
            newPreset.presetName = "Custom Preset " + System.DateTime.Now.ToString("yyyy-MM-dd HH-mm-ss");
            newPreset.description = "Captured from current Volume Component settings";
            newPreset.category = "Custom";

            // Save the preset as an asset
            string path = EditorUtility.SaveFilePanelInProject(
                "Save Flux Preset",
                newPreset.presetName + ".asset",
                "asset",
                "Choose where to save the new Flux preset");

            if (!string.IsNullOrEmpty(path))
            {
                AssetDatabase.CreateAsset(newPreset, path);
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();

                // Select the new preset
                selectedPreset = newPreset;
                EditorGUIUtility.PingObject(newPreset);

                Debug.Log($"[Flux] Created new preset '{newPreset.presetName}' at {path}");
            }
            else
            {
                // User cancelled, destroy the temporary preset
                DestroyImmediate(newPreset);
            }
        }

        private void LoadAvailablePresets()
        {
            // Find all FluxPreset assets in the project
            string[] guids = AssetDatabase.FindAssets("t:FluxPreset");
            availablePresets = new FluxPreset[guids.Length];

            for (int i = 0; i < guids.Length; i++)
            {
                string path = AssetDatabase.GUIDToAssetPath(guids[i]);
                availablePresets[i] = AssetDatabase.LoadAssetAtPath<FluxPreset>(path);
            }

            // Create preset names array for dropdown (with "None" option)
            presetNames = new string[availablePresets.Length + 1];
            presetNames[0] = "None";

            for (int i = 0; i < availablePresets.Length; i++)
            {
                if (availablePresets[i] != null)
                {
                    presetNames[i + 1] = availablePresets[i].presetName;
                }
                else
                {
                    presetNames[i + 1] = "Unknown Preset";
                }
            }
        }
    }
}
#endif
