%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: Terrain 8 Layer
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor v1.9.3.4\n// Available at the
    Unity Asset Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=19304\nNode;AmplifyShaderEditor.CommentaryNode;1000;-4080,-4480;Inherit;False;3623;3418;Snow
    ;3;1003;1002;1001;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;988;-1456,-608;Inherit;False;901.2057;383.1604;Vertex
    Tangents;5;999;993;992;991;990;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;989;-1456,-992;Inherit;False;899.9009;360.7447;Vertex
    Tangents;5;998;997;996;995;994;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;1001;-3472,-1824;Inherit;False;2932;709;Snow
    Textures and Color;26;1131;1130;1129;1128;1127;1112;1111;1110;1109;1108;1107;1106;1104;1103;1102;1101;1090;1089;1088;1087;1086;1085;1084;1083;1082;1263;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;1002;-4032,-4400;Inherit;False;1412;723;Snow
    Splat;18;1121;1120;1119;1118;1117;1096;1095;1094;1093;1092;1091;1081;1080;1079;1078;1077;1076;1075;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;1003;-2576,-4416;Inherit;False;2034.429;2532.228;Snow
    Splat Mask;80;1126;1125;1124;1123;1122;1100;1099;1098;1097;1074;1073;1072;1071;1070;1069;1068;1067;1066;1065;1064;1063;1062;1061;1060;1059;1058;1057;1056;1055;1054;1053;1052;1051;1050;1049;1048;1047;1046;1045;1044;1043;1042;1041;1040;1039;1038;1037;1036;1035;1034;1033;1032;1031;1030;1029;1028;1027;1026;1025;1024;1023;1022;1021;1020;1019;1018;1017;1016;1015;1014;1013;1012;1011;1010;1009;1008;1007;1006;1005;1004;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;1132;1296,-256;Inherit;False;1732;1123;Snow
    BaseColor;42;1194;1193;1192;1191;1183;1182;1181;1180;1179;1178;1177;1176;1175;1174;1173;1163;1162;1161;1160;1159;1158;1157;1156;1155;1154;1153;1152;1151;1142;1141;1140;1139;1138;1137;1136;1135;1134;1133;1195;1198;1211;668;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;1213;1184,2064;Inherit;False;1364;835;Snow
    Normals;24;1248;1247;1246;1245;1244;1243;1241;1240;1239;1237;1235;1233;1223;1222;1221;1220;1219;1218;1217;1216;1215;1214;1255;669;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;285;-3421.475,-459.4324;Inherit;False;451.0647;123.0272;Terrain
    Offset **for terrain control mapping;;0,0,0,1;push towards camera a bit, so that
    coord mismatch due to dynamic batching is not affecting us$Offset for CameraX
    0.001$Offset for CameraY 0.0001$$;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;450;579.1412,-60.08492;Inherit;False;376.1325;121;#ifdef
    _ALPHATEST_ON;;0,0,0,1;float hole = tex2D(_TerrainHolesTexture, uv).r@$clip(hole
    == 0.0f ? -1 : 1)@;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;499;-393.4553,2281.917;Inherit;False;176.1696;143.1629;Normal
    Strength;;0,0,0,1;_NormalScale0$_NormalScale1$_NormalScale2$_NormalScale3$;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;505;442.816,2284.878;Inherit;False;608.4122;108.9153;Enable
    PerPixel Normal;;0,0,0,1;#pragma shader_feature_local _TERRAIN_INSTANCED_PERPIXEL_NORMAL;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;512;-19.1656,2303.563;Inherit;False;244.7206;103.6938;to
    avoid nan after normalizing;;0,0,0,1;mixedNormal.z += 1e-5f@;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;583;-373.131,5160.878;Inherit;False;157.4512;179.7598;Smoothness;;0,0,0,1;_Smoothness0$_Smoothness1$_Smoothness2$_Smoothness3;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;584;-368.7694,3817.649;Inherit;False;167.4512;171.7598;Metallic;;0,0,0,1;_Metallic0$_Metallic1$_Metallic2$_Metallic3;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;603;-358.311,6506.566;Inherit;False;166.0841;189.7598;Occlusion;;0,0,0,1;;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;633;125.1228,6421.351;Inherit;False;301;101;Saturate;;0,0,0,1;Saturate
    final Occlusion to avoid possible nan in unity lighting ;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;635;-4062.505,-723.2172;Inherit;False;150;100;;;0,0,0,1;_Control
    ;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;636;-4324.75,507.3965;Inherit;False;149.3613;101.6721;;;0,0,0,1;_Splat0$_Normal0$_Mask0;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;637;-4324.121,1982.694;Inherit;False;149.3613;101.6721;;;0,0,0,1;_Splat2$_Normal2$_Mask2;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;638;-4311.699,1239.669;Inherit;False;149.3613;101.6721;;;0,0,0,1;_Splat1$_Normal1$_Mask1;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;639;-4323.158,2694.31;Inherit;False;149.3613;101.6721;;;0,0,0,1;_Splat3$_Normal3$_Mask3;0;0\nNode;AmplifyShaderEditor.SamplerNode;414;-4326.265,618.3862;Inherit;True;Property;_TextureSample0;Texture
    Sample 0;35;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SamplerNode;416;-4326.265,1022.386;Inherit;True;Property;_TextureSample2;Texture
    Sample 2;36;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.TextureCoordinatesNode;426;-4575.231,541.7663;Inherit;False;0;-1;2;3;2;SAMPLER2D;;False;0;FLOAT2;1,1;False;1;FLOAT2;0,0;False;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.WireNode;430;-4396.478,888.6406;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;431;-4395.478,677.6406;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;432;-4391.478,1077.641;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.SamplerNode;420;-4317.574,1353.878;Inherit;True;Property;_TextureSample6;Texture
    Sample 6;40;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SamplerNode;421;-4322.041,1562.276;Inherit;True;Property;_TextureSample7;Texture
    Sample 7;41;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SamplerNode;422;-4326.506,1770.675;Inherit;True;Property;_TextureSample8;Texture
    Sample 8;42;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.TextureCoordinatesNode;428;-4587.776,1276.831;Inherit;False;0;-1;2;3;2;SAMPLER2D;;False;0;FLOAT2;1,1;False;1;FLOAT2;0,0;False;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.GetLocalVarNode;365;-4542.978,1625.716;Inherit;False;327;SSNormal0;1;0;OBJECT;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.GetLocalVarNode;366;-4535.21,1838.577;Inherit;False;333;SSMask0;1;0;OBJECT;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.GetLocalVarNode;364;-4532.875,1415.613;Inherit;False;325;SSSplat0;1;0;OBJECT;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.SamplerNode;417;-4326.123,2094.626;Inherit;True;Property;_TextureSample3;Texture
    Sample 3;37;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.TextureCoordinatesNode;427;-4581.301,2018.732;Inherit;False;0;-1;2;3;2;SAMPLER2D;;False;0;FLOAT2;1,1;False;1;FLOAT2;0,0;False;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SamplerNode;419;-4326.124,2488.372;Inherit;True;Property;_TextureSample5;Texture
    Sample 5;39;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SamplerNode;418;-4326.769,2295.66;Inherit;True;Property;_TextureSample4;Texture
    Sample 4;38;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.GetLocalVarNode;349;-4538.876,2553.519;Inherit;False;333;SSMask0;1;0;OBJECT;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.GetLocalVarNode;348;-4545.819,2365.147;Inherit;False;327;SSNormal0;1;0;OBJECT;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.GetLocalVarNode;347;-4535.688,2159.768;Inherit;False;325;SSSplat0;1;0;OBJECT;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.TextureCoordinatesNode;429;-4576.688,2735.466;Inherit;False;0;-1;2;3;2;SAMPLER2D;;False;0;FLOAT2;1,1;False;1;FLOAT2;0,0;False;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SamplerNode;425;-4327.032,3204.633;Inherit;True;Property;_TextureSample11;Texture
    Sample 11;45;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SamplerNode;424;-4328.032,3008.633;Inherit;True;Property;_TextureSample10;Texture
    Sample 10;44;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SamplerNode;423;-4330.032,2803.633;Inherit;True;Property;_TextureSample9;Texture
    Sample 9;43;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.GetLocalVarNode;383;-4535.587,2865.616;Inherit;False;325;SSSplat0;1;0;OBJECT;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.GetLocalVarNode;387;-4537.853,3270.219;Inherit;False;333;SSMask0;1;0;OBJECT;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.GetLocalVarNode;385;-4551.587,3076.616;Inherit;False;327;SSNormal0;1;0;OBJECT;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.TexturePropertyNode;318;-4819.397,1352.214;Inherit;True;Property;_Splat1;Splat1;10;1;[HideInInspector];Create;False;1;;0;0;False;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.TexturePropertyNode;317;-4816.875,1560.946;Inherit;True;Property;_Normal1;Normal1;11;1;[HideInInspector];Create;False;1;;0;0;False;0;False;None;None;False;bump;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.TexturePropertyNode;307;-4815.639,1768.852;Inherit;True;Property;_Mask1;Mask1;13;1;[HideInInspector];Create;False;1;;0;0;False;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.TexturePropertyNode;316;-4820.527,2093.342;Inherit;True;Property;_Splat2;Splat2;16;1;[HideInInspector];Create;False;1;;0;0;False;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.TexturePropertyNode;308;-4817.57,2291.421;Inherit;True;Property;_Normal2;Normal2;17;1;[HideInInspector];Create;False;1;;0;0;False;0;False;None;None;False;bump;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.TexturePropertyNode;311;-4817.953,2487.696;Inherit;True;Property;_Mask2;Mask2;19;1;[HideInInspector];Create;False;1;;0;0;False;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.TexturePropertyNode;314;-4825.497,2801.204;Inherit;True;Property;_Splat3;Splat3;22;1;[HideInInspector];Create;False;1;;0;0;False;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.TexturePropertyNode;310;-4822.514,3005.567;Inherit;True;Property;_Normal3;Normal3;27;1;[HideInInspector];Create;False;0;0;0;False;0;False;None;None;False;bump;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.TexturePropertyNode;309;-4819.546,3203.908;Inherit;True;Property;_Mask3;Mask3;23;1;[HideInInspector];Create;False;0;0;0;False;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.TexturePropertyNode;332;-4813.015,1016.258;Inherit;True;Property;_Mask0;Mask0;7;1;[HideInInspector];Create;False;1;;0;0;False;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.TexturePropertyNode;324;-4813.986,618.611;Inherit;True;Property;_Splat0;Splat0;4;1;[HideInInspector];Create;False;0;0;0;False;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.RegisterLocalVarNode;325;-4567.715,710.0908;Inherit;False;SSSplat0;-1;True;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;327;-4571.883,917.6796;Inherit;False;SSNormal0;-1;True;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;333;-4569.231,1106.87;Inherit;False;SSMask0;-1;True;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.SwizzleNode;471;-3968.949,2094.082;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SwizzleNode;470;-3966.949,1352.082;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;436;-3970.939,1445.235;Inherit;False;Splat1A;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;378;-3972.159,1562.018;Inherit;False;Normal1;-1;True;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;370;-3973.006,1700.472;Inherit;False;Mask1R;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;371;-3973.223,1773.129;Inherit;False;Mask1G;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;379;-3804.628,1351.338;Inherit;False;Splat1;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;412;-3974.223,1847.132;Inherit;False;Mask1B;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;369;-3971.626,1920.649;Inherit;False;Mask1A;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;341;-3964.502,827.8027;Inherit;False;Normal0;-1;True;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;334;-3967.711,948.2559;Inherit;False;Mask0R;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;409;-3968.333,1020.522;Inherit;False;Mask0G;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;410;-3967.698,1093.529;Inherit;False;Mask0B;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;335;-3968.623,1163.326;Inherit;False;Mask0A;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SwizzleNode;469;-3964.349,620.582;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;342;-3806.09,621.7546;Inherit;False;Splat0;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;435;-3965.583,712.6168;Inherit;False;Splat0A;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;356;-3967.32,2297.206;Inherit;False;Normal2;-1;True;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;359;-3970.348,2412.838;Inherit;False;Mask2R;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;358;-3971.168,2485.594;Inherit;False;Mask2G;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;411;-3968.836,2559.897;Inherit;False;Mask2B;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;360;-3967.448,2633.555;Inherit;False;Mask2A;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;357;-3802.056,2092.957;Inherit;False;Splat2;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;438;-3969.931,2184.846;Inherit;False;Splat2A;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SwizzleNode;472;-3971.193,2803.082;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;390;-3807.439,2802.311;Inherit;False;Splat3;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;440;-3965.336,2895.318;Inherit;False;Splat3A;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;398;-3967.718,3010.222;Inherit;False;Normal3;-1;True;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;388;-3966.236,3130.126;Inherit;False;Mask3R;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;389;-3967.592,3201.97;Inherit;False;Mask3G;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;413;-3967.784,3276.611;Inherit;False;Mask3B;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;391;-3967.21,3347.533;Inherit;False;Mask3A;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SamplerNode;415;-4326.265,826.3862;Inherit;True;Property;_TextureSample1;Texture
    Sample 1;35;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.TexturePropertyNode;326;-4816.021,824.8513;Inherit;True;Property;_Normal0;Normal0;5;1;[HideInInspector];Create;False;1;;0;0;False;0;False;None;None;False;bump;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.SamplerNode;696;-2968.996,620.3208;Inherit;True;Property;_TextureSample15;Texture
    Sample 0;35;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SamplerNode;697;-2968.996,1024.321;Inherit;True;Property;_TextureSample17;Texture
    Sample 2;36;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.TextureCoordinatesNode;698;-3217.961,543.7007;Inherit;False;0;-1;2;3;2;SAMPLER2D;;False;0;FLOAT2;1,1;False;1;FLOAT2;0,0;False;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.WireNode;699;-3039.208,890.5751;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;700;-3038.208,679.5752;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;701;-3034.208,1079.576;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.SamplerNode;702;-2960.305,1355.813;Inherit;True;Property;_TextureSample18;Texture
    Sample 6;40;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SamplerNode;703;-2964.771,1564.211;Inherit;True;Property;_TextureSample19;Texture
    Sample 7;41;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SamplerNode;704;-2969.236,1772.61;Inherit;True;Property;_TextureSample20;Texture
    Sample 8;42;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.TextureCoordinatesNode;705;-3230.506,1278.766;Inherit;False;0;-1;2;3;2;SAMPLER2D;;False;0;FLOAT2;1,1;False;1;FLOAT2;0,0;False;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.GetLocalVarNode;706;-3185.708,1627.651;Inherit;False;327;SSNormal0;1;0;OBJECT;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.GetLocalVarNode;707;-3177.94,1840.512;Inherit;False;333;SSMask0;1;0;OBJECT;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.GetLocalVarNode;708;-3175.605,1417.548;Inherit;False;325;SSSplat0;1;0;OBJECT;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.SamplerNode;709;-2968.854,2096.561;Inherit;True;Property;_TextureSample21;Texture
    Sample 3;37;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.TextureCoordinatesNode;710;-3224.031,2020.667;Inherit;False;0;-1;2;3;2;SAMPLER2D;;False;0;FLOAT2;1,1;False;1;FLOAT2;0,0;False;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SamplerNode;711;-2968.854,2490.307;Inherit;True;Property;_TextureSample22;Texture
    Sample 5;39;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SamplerNode;712;-2969.5,2297.594;Inherit;True;Property;_TextureSample23;Texture
    Sample 4;38;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.GetLocalVarNode;713;-3181.606,2555.454;Inherit;False;333;SSMask0;1;0;OBJECT;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.GetLocalVarNode;714;-3188.549,2367.082;Inherit;False;327;SSNormal0;1;0;OBJECT;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.GetLocalVarNode;715;-3178.418,2161.703;Inherit;False;325;SSSplat0;1;0;OBJECT;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.TextureCoordinatesNode;716;-3219.418,2737.401;Inherit;False;0;-1;2;3;2;SAMPLER2D;;False;0;FLOAT2;1,1;False;1;FLOAT2;0,0;False;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SamplerNode;717;-2969.763,3206.568;Inherit;True;Property;_TextureSample24;Texture
    Sample 11;45;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SamplerNode;718;-2970.763,3010.568;Inherit;True;Property;_TextureSample25;Texture
    Sample 10;44;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SamplerNode;719;-2972.763,2805.568;Inherit;True;Property;_TextureSample26;Texture
    Sample 9;43;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.GetLocalVarNode;721;-3180.583,3272.154;Inherit;False;333;SSMask0;1;0;OBJECT;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;734;-3210.445,712.0253;Inherit;False;SSSplat0;-1;True;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;735;-3214.613,919.6142;Inherit;False;SSNormal0;-1;True;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;736;-3211.961,1108.805;Inherit;False;SSMask0;-1;True;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.SwizzleNode;737;-2611.679,2096.017;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SwizzleNode;738;-2609.679,1354.017;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SwizzleNode;751;-2607.08,622.5165;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SwizzleNode;761;-2613.924,2805.017;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SamplerNode;769;-2968.996,828.3207;Inherit;True;Property;_TextureSample27;Texture
    Sample 1;35;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.StickyNoteNode;771;-2966.852,1984.629;Inherit;False;149.3613;101.6721;;;0,0,0,1;_Splat6$_Normal6$_Mask6;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;772;-2954.43,1241.604;Inherit;False;149.3613;101.6721;;;0,0,0,1;_Splat5$_Normal5$_Mask5;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;773;-2965.889,2696.245;Inherit;False;149.3613;101.6721;;;0,0,0,1;_Splat7$_Normal7$_Mask7;0;0\nNode;AmplifyShaderEditor.TexturePropertyNode;733;-3456.716,620.5456;Inherit;True;Property;_Splat4;Splat4;28;1;[HideInInspector];Create;False;0;0;0;False;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.TexturePropertyNode;770;-3458.751,829.5489;Inherit;True;Property;_Normal4;Normal4;34;1;[HideInInspector];Create;False;1;;0;0;False;0;False;None;None;False;bump;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.TexturePropertyNode;732;-3455.746,1018.193;Inherit;True;Property;_Mask4;Mask4;30;1;[HideInInspector];Create;False;1;;0;0;False;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.TexturePropertyNode;723;-3462.127,1354.149;Inherit;True;Property;_Splat5;Splat5;35;1;[HideInInspector];Create;False;1;;0;0;False;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.TexturePropertyNode;724;-3459.605,1564.262;Inherit;True;Property;_Normal5;Normal5;36;1;[HideInInspector];Create;False;1;;0;0;False;0;False;None;None;False;bump;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.TexturePropertyNode;725;-3458.37,1770.787;Inherit;True;Property;_Mask5;Mask5;37;1;[HideInInspector];Create;False;1;;0;0;False;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.TexturePropertyNode;726;-3463.257,2095.277;Inherit;True;Property;_Splat6;Splat6;41;1;[HideInInspector];Create;False;1;;0;0;False;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.TexturePropertyNode;727;-3460.3,2293.355;Inherit;True;Property;_Normal6;Normal6;43;1;[HideInInspector];Create;False;1;;0;0;False;0;False;None;None;False;bump;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.TexturePropertyNode;728;-3460.684,2489.631;Inherit;True;Property;_Mask6;Mask6;45;1;[HideInInspector];Create;False;1;;0;0;False;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.TexturePropertyNode;729;-3468.228,2803.139;Inherit;True;Property;_Splat7;Splat7;47;1;[HideInInspector];Create;False;1;;0;0;False;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.TexturePropertyNode;730;-3465.245,3007.501;Inherit;True;Property;_Normal7;Normal7;48;1;[HideInInspector];Create;False;0;0;0;False;0;False;None;None;False;bump;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.TexturePropertyNode;731;-3462.276,3205.843;Inherit;True;Property;_Mask7;Mask7;51;1;[HideInInspector];Create;False;0;0;0;False;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.GetLocalVarNode;720;-3179.317,2874.551;Inherit;False;325;SSSplat0;1;0;OBJECT;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.GetLocalVarNode;722;-3195.317,3083.551;Inherit;False;327;SSNormal0;1;0;OBJECT;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.StickyNoteNode;774;-2955.549,502.2629;Inherit;False;149.3613;101.6721;;;0,0,0,1;_Splat4$_Normal4$_Mask4;0;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;752;-2448.821,623.6891;Inherit;False;Splat4;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;753;-2608.313,714.5513;Inherit;False;Splat4A;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;746;-2607.232,829.7372;Inherit;False;Normal4;-1;True;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;747;-2610.441,950.1905;Inherit;False;Mask4R;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;748;-2611.063,1022.457;Inherit;False;Mask4G;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;749;-2610.428,1095.464;Inherit;False;Mask4B;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;750;-2611.354,1165.261;Inherit;False;Mask4A;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;743;-2447.358,1353.273;Inherit;False;Splat5;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;739;-2614.669,1447.17;Inherit;False;Splat5A;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;740;-2614.889,1563.953;Inherit;False;Normal5;-1;True;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;741;-2615.737,1702.407;Inherit;False;Mask5R;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;742;-2615.953,1775.064;Inherit;False;Mask5G;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;744;-2616.953,1849.067;Inherit;False;Mask5B;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;745;-2614.356,1922.584;Inherit;False;Mask5A;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;759;-2444.786,2094.892;Inherit;False;Splat6;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;760;-2612.661,2186.781;Inherit;False;Splat6A;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;754;-2610.051,2299.141;Inherit;False;Normal6;-1;True;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;755;-2613.078,2414.772;Inherit;False;Mask6R;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;756;-2613.898,2487.529;Inherit;False;Mask6G;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;757;-2611.566,2561.832;Inherit;False;Mask6B;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;758;-2610.178,2635.49;Inherit;False;Mask6A;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;762;-2450.169,2804.246;Inherit;False;Splat7;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;763;-2608.066,2897.253;Inherit;False;Splat7A;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;764;-2610.448,3012.156;Inherit;False;Normal7;-1;True;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;765;-2608.967,3132.061;Inherit;False;Mask7R;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;766;-2610.323,3204.905;Inherit;False;Mask7G;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;767;-2610.514,3278.546;Inherit;False;Mask7B;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;768;-2609.94,3349.468;Inherit;False;Mask7A;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.StickyNoteNode;775;-371.854,7418.567;Inherit;False;164.2549;181.6885;Occlusion;;0,0,0,1;;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;809;-368.5497,5814.478;Inherit;False;157.0662;175.7906;Smoothness;;0,0,0,1;_Smoothness4$_Smoothness5$_Smoothness6$_Smoothness7;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;831;-370.8706,4467.174;Inherit;False;163.2127;181.1973;Metallic;;0,0,0,1;_Metallic4$_Metallic5$_Metallic6$_Metallic7;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;853;-392.075,3008.945;Inherit;False;168.2686;137.2372;Normal
    Strength;;0,0,0,1;_NormalScale0$_NormalScale1$_NormalScale2$_NormalScale3$;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;941;-3413.575,197.3866;Inherit;False;451.0647;123.0272;Terrain
    Offset **for terrain control mapping;;0,0,0,1;push towards camera a bit, so that
    coord mismatch due to dynamic batching is not affecting us$Offset for CameraX
    0.001$Offset for CameraY 0.0001$$;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;965;-4054.605,-66.39829;Inherit;False;150;100;;;0,0,0,1;_Control1;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;974;577.6412,68.92357;Inherit;False;620.0586;268.4355;Alpha;;0,0,0,1;(hole,
    0.5)@;0;0\nNode;AmplifyShaderEditor.WireNode;461;-567.1022,-38.75796;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;449;-573.3159,-238.0204;Inherit;False;26;Control;1;0;OBJECT;;False;1;COLOR;0\nNode;AmplifyShaderEditor.WireNode;460;-597.5052,-70.01765;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;464;-586.0824,-95.94048;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;466;-555.5854,-70.5263;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SummedBlendNode;9;-389.9985,-209.2545;Inherit;False;5;0;COLOR;0,0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;3;FLOAT3;0,0,0;False;4;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SamplerNode;451;255.0275,-8.580652;Inherit;True;Property;_TextureSample12;Texture
    Sample 4;284;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.FunctionSwitch;102;782.7018,-222.9707;Inherit;False;Enable
    Holes;True;1;2;1;In 0;In 1;Object;-1;10;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.CustomExpressionNode;453;582.2852,-151.7376;Inherit;False;#ifdef
    _ALPHATEST_ON$\tclip(Hole == 0.0f ? -1 : 1)@$#endif;1;Call;1;True;Hole;FLOAT;0;In;;Inherit;False;ClipHoles;False;False;0;;False;2;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;968;-169.785,-207.0994;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;969;508.5993,-181.2202;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.TexturePropertyNode;452;0.8216734,-7.99081;Inherit;True;Property;_TerrainHolesTexture;_TerrainHolesTexture;1;1;[HideInInspector];Create;False;0;0;0;False;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.RangedFloatNode;977;597.3311,144.0086;Inherit;False;Constant;_Float10;Float
    1;183;0;Create;True;0;0;0;False;0;False;0.5;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;980;599.7131,243.9975;Inherit;False;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.FunctionSwitch;976;790.7637,119.6805;Inherit;False;Option;False;1;2;1;In
    0;In 1;Instance;102;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;978;793.7627,216.6804;Inherit;False;Option;False;1;2;1;In
    0;In 1;Instance;102;10;0;FLOAT;0;False;1;COLOR;0,0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;COLOR;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;38;-775.4791,99.35408;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;462;-643.2646,91.36279;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;465;-597.1339,348.3938;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;41;-760.9117,367.5128;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;467;-579.3975,609.7296;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;44;-769.2614,653.9069;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SummedBlendNode;912;-372.9749,909.9531;Inherit;False;5;0;COLOR;0,0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;3;FLOAT3;0,0,0;False;4;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;902;-556.2923,881.1871;Inherit;False;922;Control1;1;0;OBJECT;;False;1;COLOR;0\nNode;AmplifyShaderEditor.WireNode;926;-538.0717,1043.811;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;927;-512.0718,1068.811;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;928;-479.0717,1077.811;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;923;-556.9576,1186.945;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;924;-540.1773,1445.743;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;925;-498.8969,1729.971;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;521;-1078.32,6368.714;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;522;-1075.12,6485.383;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;527;-1231.394,6369.157;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0.5;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;609;-1082.287,6600.263;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;610;-1079.087,6716.931;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;613;-1235.361,6600.705;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0.5;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;616;-1087.087,6837.063;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;617;-1083.887,6953.731;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;620;-1240.161,6837.505;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0.5;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;623;-1091.356,7052.398;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;624;-1088.156,7169.066;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;627;-1244.43,7052.841;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0.5;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;598;-1425.991,6595.974;Inherit;False;371;Mask1G;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;599;-1426.291,6831.373;Inherit;False;358;Mask2G;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;600;-1426.791,7048.372;Inherit;False;389;Mask3G;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;626;-851.5524,7051.457;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;619;-847.2828,6837.546;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;612;-848.1805,6602.17;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;525;-847.0624,6366.351;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SaturateNode;526;-695.5475,6369.854;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SaturateNode;615;-693.8168,6604.25;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SaturateNode;622;-695.7679,6838.201;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SaturateNode;629;-697.1885,7052.112;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SummedBlendNode;602;-356.4161,6349.038;Inherit;False;5;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;596;-541.4855,6328.607;Inherit;False;26;Control;1;0;OBJECT;;False;1;COLOR;0\nNode;AmplifyShaderEditor.WireNode;630;-530.2101,6479.234;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;631;-499.171,6503.376;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;632;-473.3052,6534.416;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;597;-1418.321,6363.48;Inherit;False;409;Mask0G;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;608;-1249.374,6479.755;Inherit;False;Constant;_Float0;Float
    0;27;0;Create;True;0;0;0;False;0;False;0.25;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;614;-1258.159,6713.12;Inherit;False;Constant;_Float1;Float
    0;27;0;Create;True;0;0;0;False;0;False;0.25;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;621;-1259.323,6946.284;Inherit;False;Constant;_Float2;Float
    0;27;0;Create;True;0;0;0;False;0;False;0.25;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;628;-1265.411,7163.438;Inherit;False;Constant;_Float3;Float
    0;27;0;Create;True;0;0;0;False;0;False;0.25;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;776;-1091.863,7280.715;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;777;-1088.663,7397.384;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;778;-1244.937,7281.158;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0.5;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;779;-1095.83,7512.264;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;780;-1092.63,7628.931;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;781;-1248.904,7512.707;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0.5;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;782;-1100.63,7749.064;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;783;-1097.43,7865.731;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;784;-1253.704,7749.506;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0.5;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;785;-1104.899,7964.399;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;786;-1101.699,8081.068;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;787;-1257.973,7964.842;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0.5;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;792;-860.8259,7749.547;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;793;-861.7236,7514.171;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;794;-860.6053,7278.352;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SaturateNode;795;-709.0905,7281.855;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SaturateNode;796;-707.3597,7516.251;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SaturateNode;797;-709.3109,7750.203;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SaturateNode;798;-710.7314,7964.114;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SummedBlendNode;799;-369.9591,7261.039;Inherit;False;5;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;806;-1271.702,7625.121;Inherit;False;Constant;_Float6;Float
    0;27;0;Create;True;0;0;0;False;0;False;0.25;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;807;-1272.866,7858.285;Inherit;False;Constant;_Float8;Float
    0;27;0;Create;True;0;0;0;False;0;False;0.25;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;808;-1278.954,8075.439;Inherit;False;Constant;_Float9;Float
    0;27;0;Create;True;0;0;0;False;0;False;0.25;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;805;-1262.917,7391.756;Inherit;False;Constant;_Float4;Float
    0;27;0;Create;True;0;0;0;False;0;False;0.25;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;804;-1431.864,7275.481;Inherit;False;748;Mask4G;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;788;-1439.534,7507.975;Inherit;False;742;Mask5G;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;789;-1439.834,7744.374;Inherit;False;756;Mask6G;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;790;-1440.334,7960.374;Inherit;False;766;Mask7G;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;791;-865.0953,7963.458;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;548;-876.8516,5024.267;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;550;-874.8516,5185.266;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;551;-716.1149,5184.448;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;552;-714.1691,5027.739;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;554;-876.8516,5351.266;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;555;-714.2853,5350.512;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;557;-876.8516,5511.266;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;558;-711.8212,5510.5;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;549;-892.8895,5094.079;Inherit;False;335;Mask0A;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;553;-893.8895,5258.079;Inherit;False;369;Mask1A;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;556;-894.8895,5425.079;Inherit;False;360;Mask2A;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;559;-895.8895,5591.079;Inherit;False;391;Mask3A;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SummedBlendNode;547;-369.5093,5002.624;Inherit;False;5;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;579;-1258.865,5018.64;Float;False;Property;_Smoothness0;Smoothness0;9;1;[HideInInspector];Create;False;0;0;0;False;0;False;0;0.077;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;580;-1263.151,5180.215;Float;False;Property;_Smoothness1;Smoothness1;15;1;[HideInInspector];Create;False;0;0;0;False;0;False;0;0.077;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;581;-1260.995,5346.376;Float;False;Property;_Smoothness2;Smoothness2;21;1;[HideInInspector];Create;False;0;0;0;False;0;False;0;0.077;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;582;-1263.1,5508.036;Float;False;Property;_Smoothness3;Smoothness3;25;1;[HideInInspector];Create;False;0;0;0;False;0;False;0;0.077;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;810;-552.0905,5805.059;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;811;-529.3763,5822.095;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;812;-881.2701,5675.866;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;813;-879.2701,5836.866;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;814;-720.5338,5836.048;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;815;-718.5878,5679.338;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;816;-881.2701,6002.866;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;817;-718.704,6002.112;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;818;-881.2701,6162.866;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;819;-716.2399,6162.1;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;820;-576.2244,5780.925;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SummedBlendNode;826;-373.928,5654.224;Inherit;False;5;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;827;-1263.284,5669.239;Float;False;Property;_Smoothness4;Smoothness4;33;1;[HideInInspector];Create;False;0;0;0;False;0;False;0;0.077;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;828;-1267.57,5831.815;Float;False;Property;_Smoothness5;Smoothness5;39;1;[HideInInspector];Create;False;0;0;0;False;0;False;0;0.077;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;829;-1265.414,5997.977;Float;False;Property;_Smoothness6;Smoothness6;42;1;[HideInInspector];Create;False;0;0;0;False;0;False;0;0.077;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;830;-1267.519,6159.636;Float;False;Property;_Smoothness7;Smoothness7;50;1;[HideInInspector];Create;False;0;0;0;False;0;False;0;0.077;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;543;-545.0364,3806.928;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;544;-522.3221,3823.965;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SummedBlendNode;536;-365.4492,3656.094;Inherit;False;5;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;530;-874.2156,3677.737;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;538;-890.2537,3747.549;Inherit;False;334;Mask0R;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;529;-872.2156,3838.736;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;528;-713.4797,3837.918;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;531;-711.5336,3681.209;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;539;-891.2537,3911.549;Inherit;False;370;Mask1R;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;533;-874.2156,4004.735;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;532;-711.6498,4003.982;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;540;-892.2537,4078.549;Inherit;False;359;Mask2R;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;535;-874.2156,4164.735;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;534;-709.1857,4163.97;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;542;-569.1705,3782.795;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;537;-549.095,3635.662;Inherit;False;26;Control;1;0;OBJECT;;False;1;COLOR;0\nNode;AmplifyShaderEditor.WireNode;832;-547.1376,4456.454;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;833;-524.4233,4473.49;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SummedBlendNode;834;-367.5504,4305.619;Inherit;False;5;0;COLOR;0,0,0,0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;835;-876.3168,4327.263;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;837;-874.3168,4488.261;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;838;-715.5809,4487.443;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;839;-713.6348,4330.735;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;841;-876.3168,4654.261;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;842;-713.751,4653.507;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;844;-876.3168,4814.261;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;845;-711.2869,4813.495;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;847;-571.2717,4432.32;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;51;-1222.915,4159.522;Float;False;Property;_Metallic3;Metallic3;26;2;[HideInInspector];[Gamma];Create;False;0;0;0;False;0;False;0;0;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;50;-1219.915,4000.522;Float;False;Property;_Metallic2;Metallic2;20;2;[HideInInspector];[Gamma];Create;False;0;0;0;False;0;False;0;0;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;49;-1210.915,3832.522;Float;False;Property;_Metallic1;Metallic1;14;2;[HideInInspector];[Gamma];Create;False;0;0;0;False;0;False;0;0;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;48;-1207.583,3675.674;Float;False;Property;_Metallic0;Metallic0;8;2;[HideInInspector];[Gamma];Create;False;0;0;0;False;0;False;0;0;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;852;-1225.016,4809.047;Float;False;Property;_Metallic7;Metallic7;52;2;[HideInInspector];[Gamma];Create;False;0;0;0;False;0;False;0;0;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;851;-1222.016,4650.047;Float;False;Property;_Metallic6;Metallic6;46;2;[HideInInspector];[Gamma];Create;False;0;0;0;False;0;False;0;0;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;850;-1209.684,4326.822;Float;False;Property;_Metallic4;Metallic4;31;2;[HideInInspector];[Gamma];Create;False;0;0;0;False;0;False;0;0;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;849;-1213.016,4482.047;Float;False;Property;_Metallic5;Metallic5;40;2;[HideInInspector];[Gamma];Create;False;0;0;0;False;0;False;0;0;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;541;-893.2537,4244.548;Inherit;False;388;Mask3R;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;836;-892.355,4397.074;Inherit;False;747;Mask4R;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;840;-893.355,4561.074;Inherit;False;741;Mask5R;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;843;-894.355,4728.074;Inherit;False;755;Mask6R;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;846;-895.355,4894.074;Inherit;False;765;Mask7R;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;822;-897.3082,5745.679;Inherit;False;750;Mask4A;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;823;-898.3082,5909.679;Inherit;False;745;Mask5A;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;824;-899.3082,6076.679;Inherit;False;758;Mask6A;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;825;-900.3082,6240.679;Inherit;False;768;Mask7A;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.UnpackScaleNormalNode;491;-811.4894,2681.77;Inherit;False;Tangent;2;0;FLOAT4;0,0,0,0;False;1;FLOAT;1;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.GetLocalVarNode;481;-1017.392,2133.535;Inherit;False;341;Normal0;1;0;OBJECT;;False;1;COLOR;0\nNode;AmplifyShaderEditor.UnpackScaleNormalNode;490;-809.4595,2138.302;Inherit;False;Tangent;2;0;FLOAT4;0,0,0,0;False;1;FLOAT;1;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.UnpackScaleNormalNode;496;-810.0593,2321.77;Inherit;False;Tangent;2;0;FLOAT4;0,0,0,0;False;1;FLOAT;1;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.UnpackScaleNormalNode;494;-813.5215,2503.342;Inherit;False;Tangent;2;0;FLOAT4;0,0,0,0;False;1;FLOAT;1;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.GetLocalVarNode;477;-1021.514,2678.328;Inherit;False;398;Normal3;1;0;OBJECT;;False;1;COLOR;0\nNode;AmplifyShaderEditor.SummedBlendNode;473;-389.5634,2115.762;Inherit;False;5;0;COLOR;0,0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;3;FLOAT3;0,0,0;False;4;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;500;-575.4893,2246.743;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;501;-553.4894,2273.743;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;502;-531.4894,2305.743;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;476;-1018.833,2318.23;Inherit;False;378;Normal1;1;0;OBJECT;;False;1;COLOR;0\nNode;AmplifyShaderEditor.GetLocalVarNode;480;-1019.569,2497.792;Inherit;False;356;Normal2;1;0;OBJECT;;False;1;COLOR;0\nNode;AmplifyShaderEditor.GetLocalVarNode;474;-574.2983,2087.82;Inherit;False;26;Control;1;0;OBJECT;;False;1;COLOR;0\nNode;AmplifyShaderEditor.RangedFloatNode;482;-1019.591,2208.586;Half;False;Property;_NormalScale0;NormalScale0;6;1;[HideInInspector];Create;False;1;;0;0;False;0;False;1;1;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;483;-1020.977,2392.877;Half;False;Property;_NormalScale1;NormalScale1;12;1;[HideInInspector];Create;False;1;;0;0;False;0;False;1;1;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;484;-1022.811,2574.809;Half;False;Property;_NormalScale2;NormalScale2;18;1;[HideInInspector];Create;False;1;;0;0;False;0;False;1;1;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;485;-1028.6,2754.375;Half;False;Property;_NormalScale3;_NormalScale3;24;1;[HideInInspector];Create;False;1;;0;0;False;0;False;1;1;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.UnpackScaleNormalNode;854;-810.1086,3408.798;Inherit;False;Tangent;2;0;FLOAT4;0,0,0,0;False;1;FLOAT;1;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.UnpackScaleNormalNode;856;-808.0787,2865.331;Inherit;False;Tangent;2;0;FLOAT4;0,0,0,0;False;1;FLOAT;1;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.UnpackScaleNormalNode;857;-808.6788,3048.797;Inherit;False;Tangent;2;0;FLOAT4;0,0,0,0;False;1;FLOAT;1;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.UnpackScaleNormalNode;858;-812.1409,3230.371;Inherit;False;Tangent;2;0;FLOAT4;0,0,0,0;False;1;FLOAT;1;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.SummedBlendNode;860;-388.1831,2842.79;Inherit;False;5;0;COLOR;0,0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;3;FLOAT3;0,0,0;False;4;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;861;-574.1089,2973.771;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;862;-552.1089,3000.771;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;863;-530.1089,3032.771;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;855;-1016.011,2860.563;Inherit;False;746;Normal4;1;0;OBJECT;;False;1;COLOR;0\nNode;AmplifyShaderEditor.GetLocalVarNode;864;-1017.452,3045.258;Inherit;False;740;Normal5;1;0;OBJECT;;False;1;COLOR;0\nNode;AmplifyShaderEditor.GetLocalVarNode;865;-1018.188,3224.82;Inherit;False;754;Normal6;1;0;OBJECT;;False;1;COLOR;0\nNode;AmplifyShaderEditor.GetLocalVarNode;859;-1020.133,3405.357;Inherit;False;764;Normal7;1;0;OBJECT;;False;1;COLOR;0\nNode;AmplifyShaderEditor.RangedFloatNode;867;-1018.21,2935.614;Half;False;Property;_NormalScale4;NormalScale4;32;1;[HideInInspector];Create;False;1;;0;0;False;0;False;1;1;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;868;-1019.596,3119.905;Half;False;Property;_NormalScale5;NormalScale5;38;1;[HideInInspector];Create;False;1;;0;0;False;0;False;1;1;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;869;-1021.43,3301.837;Half;False;Property;_NormalScale6;NormalScale6;44;1;[HideInInspector];Create;False;1;;0;0;False;0;False;1;1;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;870;-1027.219,3481.404;Half;False;Property;_NormalScale7;_NormalScale7;49;1;[HideInInspector];Create;False;1;;0;0;False;0;False;1;1;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;866;-572.9178,2814.85;Inherit;False;922;Control1;1;0;OBJECT;;False;1;COLOR;0\nNode;AmplifyShaderEditor.GetLocalVarNode;848;-551.1962,4285.188;Inherit;False;922;Control1;1;0;OBJECT;;False;1;COLOR;0\nNode;AmplifyShaderEditor.GetLocalVarNode;561;-551.7303,4982.193;Inherit;False;26;Control;1;0;OBJECT;;False;1;COLOR;0\nNode;AmplifyShaderEditor.GetLocalVarNode;821;-556.1489,5633.791;Inherit;False;922;Control1;1;0;OBJECT;;False;1;COLOR;0\nNode;AmplifyShaderEditor.GetLocalVarNode;800;-555.0283,7240.608;Inherit;False;922;Control1;1;0;OBJECT;;False;1;COLOR;0\nNode;AmplifyShaderEditor.WireNode;929;-533.491,5103.393;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;930;-517.0878,5132.685;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;931;-495.9979,5170.177;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;932;-551.066,5187.752;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;933;-522.9462,5317.807;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;934;-497.1694,5471.295;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;935;-558.8121,7389.511;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;936;-527.7847,7421.538;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;937;-500.7608,7459.574;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;938;-568.821,7501.611;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;939;-539.7952,7711.796;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;940;-501.7614,7921.982;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.BreakToComponentsNode;513;17.4623,2114.423;Inherit;False;FLOAT3;1;0;FLOAT3;0,0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.SimpleAddOpNode;515;151.3315,2196.121;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;516;-15.57978,2229.926;Float;False;Constant;_Float7;Float
    7;9;0;Create;True;0;0;0;False;0;False;0.001;0.001;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.DynamicAppendNode;514;277.1641,2116.027;Inherit;False;FLOAT3;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.StaticSwitch;503;438.3303,2185.91;Inherit;False;Property;_EnableInstancedPerPixelNormal;Enable
    Instanced Per-Pixel Normal;0;0;Create;False;0;0;0;False;0;False;0;0;0;True;_TERRAIN_INSTANCED_PERPIXEL_NORMAL;Toggle;2;Key0;Key1;Create;True;False;All;9;1;FLOAT3;0,0,0;False;0;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;3;FLOAT3;0,0,0;False;4;FLOAT3;0,0,0;False;5;FLOAT3;0,0,0;False;6;FLOAT3;0,0,0;False;7;FLOAT3;0,0,0;False;8;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitch;504;797.8615,2108.475;Inherit;False;Enable
    PerPixel Normals;True;1;2;0;In 0;In 1;Object;-1;10;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;970;-152.0415,2113.575;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;971;-136.7638,3654.954;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;972;-159.9123,5002.109;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SaturateNode;634;122.7843,6350.025;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;973;-125.9701,6351.981;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SamplerNode;283;-2759.263,-913.2341;Inherit;True;Property;_TextureSample16;Texture
    Sample 16;12;1;[NoScaleOffset];Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;295;-3580.614,-735.7291;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.TexCoordVertexDataNode;296;-3813.936,-733.9022;Inherit;False;0;2;0;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.TextureTransformNode;289;-3826.673,-616.7462;Inherit;False;-1;False;1;0;SAMPLER2D;;False;2;FLOAT2;0;FLOAT2;1\nNode;AmplifyShaderEditor.BreakToComponentsNode;291;-3578.393,-589.6215;Inherit;False;FLOAT2;1;0;FLOAT2;0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.RangedFloatNode;292;-3640.942,-499.2858;Inherit;False;Constant;_OffsetCameraX;Offset
    CameraX;0;0;Create;False;0;0;0;False;0;False;0.001;-0.02;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;297;-3643.403,-426.7054;Inherit;False;Constant;_OffsetCameraY;Offset
    CameraY;0;0;Create;False;0;0;0;False;0;False;0.0001;-0.02;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;290;-3422.146,-637.6532;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;288;-3422.621,-548.4026;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.DynamicAppendNode;293;-3280.349,-637.1168;Inherit;False;FLOAT2;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;294;-3137.114,-731.0146;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.VertexToFragmentNode;286;-3014.286,-730.5999;Inherit;False;False;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.TexturePropertyNode;284;-4066.596,-907.9948;Inherit;True;Property;_Control;Control;2;1;[HideInInspector];Create;False;0;0;0;False;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.WireNode;448;-2828.455,-853.806;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.SamplerNode;947;-2751.364,-256.4153;Inherit;True;Property;_TextureSample28;Texture
    Sample 16;12;1;[NoScaleOffset];Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;948;-3572.714,-78.91023;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.TexCoordVertexDataNode;949;-3806.036,-77.08334;Inherit;False;0;2;0;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.TextureTransformNode;950;-3818.773,40.0727;Inherit;False;-1;False;1;0;SAMPLER2D;;False;2;FLOAT2;0;FLOAT2;1\nNode;AmplifyShaderEditor.BreakToComponentsNode;951;-3570.493,67.1974;Inherit;False;FLOAT2;1;0;FLOAT2;0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.RangedFloatNode;952;-3633.042,157.5332;Inherit;False;Constant;_OffsetCameraX1;Offset
    CameraX;0;0;Create;False;0;0;0;False;0;False;0.001;-0.02;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;953;-3635.503,230.1137;Inherit;False;Constant;_OffsetCameraY1;Offset
    CameraY;0;0;Create;False;0;0;0;False;0;False;0.0001;-0.02;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;954;-3414.246,19.16566;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;955;-3414.721,108.4162;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.DynamicAppendNode;956;-3272.45,19.7021;Inherit;False;FLOAT2;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;957;-3129.214,-74.19573;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.VertexToFragmentNode;958;-3006.386,-73.78103;Inherit;False;False;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;962;-2820.556,-196.9871;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;26;-2433.44,-914.4166;Float;False;Control;-1;True;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;922;-2433.252,-256.7772;Float;False;Control1;-1;True;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.TexturePropertyNode;961;-4058.696,-251.1759;Inherit;True;Property;_Control1;Control1;3;1;[HideInInspector];Create;False;0;0;0;False;0;False;None;None;False;black;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.Vector3Node;990;-1360,-400;Inherit;False;Constant;_Vector7;Vector
    7;234;0;Create;True;0;0;0;False;0;False;0,0,1;0,0,0;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.NormalVertexDataNode;991;-1360,-544;Inherit;False;0;5;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.CrossProductOpNode;992;-1152,-544;Inherit;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.DynamicAppendNode;993;-992,-544;Inherit;False;FLOAT4;4;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;-1;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.NormalVertexDataNode;994;-1360,-832;Inherit;False;0;5;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.CustomExpressionNode;995;-1392,-912;Float;False;v.tangent.xyz
    = cross ( v.normal, float3( 0, 0, 1 ) )@$v.tangent.w = -1@;1;Call;0;CalculateTangentsStandard;True;False;0;;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;996;-1136,-912;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitchByPipeline;997;-960,-896;Inherit;False;4;0;FLOAT3;0,0,0;False;3;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;463;-608,-128;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;459;-640,-80;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;35;-768,-192;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.StickyNoteNode;1004;-2336,-4288;Inherit;False;394;151;WorldPosition;;0,0,0,1;used
    for the Altitude Mask and creates a grayscale (float) mask based on the height
    or altitude in the world.  ;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;1005;-1200,-4368;Inherit;False;406.2698;157.4146;Min
    Max;;0,0,0,1;The gradient begins with an output value of zero at the Minimum
    height and ends with an output value of one at the Maximum height. $$Heights
    less than Minimum will always return zero and heights greater than Maximum will
    always return one.;0;0\nNode;AmplifyShaderEditor.TFHCRemapNode;1006;-1568,-2096;Inherit;False;5;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;1;False;3;FLOAT;-1;False;4;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.PowerNode;1007;-1536,-2304;Inherit;False;True;2;0;FLOAT;0;False;1;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.LerpOp;1008;-1344,-2304;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;1009;-1696,-2208;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;1010;-1984,-2208;Half;False;Property;_SnowSplatABlendFalloff;Blend
    Falloff;87;0;Create;False;1;;0;0;False;0;False;0;0.01;0;10;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMaxOpNode;1011;-1696,-2304;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;1012;-1984,-2304;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;1013;-2112,-2224;Inherit;False;2;2;0;FLOAT;1;False;1;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;1014;-1696,-2784;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;1015;-2000,-2784;Half;False;Property;_SnowSplatBBlendFalloff;Blend
    Falloff;80;0;Create;False;1;;0;0;False;0;False;0;0.01;0;10;0;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1016;-2208,-2880;Inherit;False;734;SSSplat0;1;0;OBJECT;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;1017;-2144,-2800;Inherit;False;2;2;0;FLOAT;1;False;1;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;1018;-1696,-3344;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;1019;-1984,-3344;Half;False;Property;_SnowSplatGBlendFalloff;Blend
    Falloff;73;0;Create;False;1;;0;0;False;0;False;0;0.01;0;10;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMaxOpNode;1020;-1696,-3440;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.PowerNode;1021;-1536,-3440;Inherit;False;True;2;0;FLOAT;0;False;1;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.LerpOp;1022;-1344,-3440;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;1023;-1984,-3440;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1024;-2208,-3440;Inherit;False;1081;SnowSplatG;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;1025;-2144,-3360;Inherit;False;2;2;0;FLOAT;1;False;1;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMaxOpNode;1026;-1760,-4080;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.PowerNode;1027;-1536,-4080;Inherit;False;True;2;0;FLOAT;0;False;1;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;1028;-1792,-3968;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.LerpOp;1029;-1360,-4080;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;1030;-2080,-3968;Half;False;Property;_SnowSplatRBlendFalloff;Blend
    Falloff;66;0;Create;False;1;;0;0;False;0;False;0;0.01;0;10;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;1031;-2064,-4080;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1032;-2272,-4080;Inherit;False;1119;SnowSplatR;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;1033;-2208,-4000;Inherit;False;2;2;0;FLOAT;1;False;1;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SaturateNode;1034;-1168,-3440;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SaturateNode;1035;-1168,-4080;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.LerpOp;1036;-976,-3472;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SmoothstepOpNode;1037;-1200,-3024;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.LerpOp;1038;-976,-2928;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.BreakToComponentsNode;1039;-1344,-3024;Inherit;False;FLOAT3;1;0;FLOAT3;0,0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.GetLocalVarNode;1042;-1584,-3024;Inherit;False;1123;WorldPosition;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SmoothstepOpNode;1043;-1200,-2448;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.BreakToComponentsNode;1044;-1344,-2448;Inherit;False;FLOAT3;1;0;FLOAT3;0,0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.GetLocalVarNode;1045;-1584,-2448;Inherit;False;1123;WorldPosition;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RangedFloatNode;1046;-1376,-2528;Half;False;Property;_SnowSplatAMax;Max;85;0;Create;False;1;;0;0;False;0;False;1;0.5;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SaturateNode;1048;-1168,-2304;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1049;-1568,-4208;Inherit;False;1123;WorldPosition;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RangedFloatNode;1050;-1376,-4368;Half;False;Property;_SnowSplatRMin;Min;63;0;Create;False;1;;0;0;False;0;False;-0.5;0.5;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.BreakToComponentsNode;1051;-1344,-4208;Inherit;False;FLOAT3;1;0;FLOAT3;0,0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.RangedFloatNode;1052;-1376,-4288;Half;False;Property;_SnowSplatRMax;Max;64;0;Create;False;1;;0;0;False;0;False;1;0.5;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SmoothstepOpNode;1053;-1200,-4208;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.TFHCRemapNode;1054;-1584,-3248;Inherit;False;5;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;1;False;3;FLOAT;-1;False;4;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;1055;-1936,-3248;Half;False;Property;_SnowSplatGBlendStrength;Blend
    Strength;72;0;Create;False;0;0;0;False;0;False;0;0;0;5;0;1;FLOAT;0\nNode;AmplifyShaderEditor.TFHCRemapNode;1056;-1584,-3856;Inherit;False;5;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;1;False;3;FLOAT;-1;False;4;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;1057;-1936,-3856;Half;False;Property;_SnowSplatRBlendStrength;Blend
    Strength;65;0;Create;False;0;0;0;False;0;False;0;0;0;5;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;1058;-1376,-3648;Half;False;Property;_SnowSplatGMax;Max;71;0;Create;False;1;;0;0;False;0;False;1;0.5;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.BreakToComponentsNode;1059;-1344,-3568;Inherit;False;FLOAT3;1;0;FLOAT3;0,0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.SmoothstepOpNode;1060;-1200,-3568;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1062;-1584,-3568;Inherit;False;1123;WorldPosition;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SaturateNode;1063;-1168,-2896;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.LerpOp;1064;-1360,-2896;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.PowerNode;1065;-1552,-2896;Inherit;False;True;2;0;FLOAT;0;False;1;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMaxOpNode;1066;-1712,-2896;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;1067;-2016,-2896;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.TFHCRemapNode;1068;-1584,-2704;Inherit;False;5;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;1;False;3;FLOAT;-1;False;4;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;1069;-1936,-2704;Half;False;Property;_SnowSplatBBlendStrength;Blend
    Strength;79;0;Create;False;0;0;0;False;0;False;0;0;0;5;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;1070;-1936,-2096;Half;False;Property;_SnowSplatABlendStrength;Blend
    Strength;86;0;Create;False;0;0;0;False;0;False;0;0;0;5;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;1071;-784,-4096;Inherit;False;SnowSplatRMask;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;1072;-784,-3472;Inherit;False;SnowSplatGMask;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;1073;-784,-2928;Inherit;False;SnowSplatBMask;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;1074;-784,-2320;Inherit;False;SnowSplatAMask;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;1075;-3376,-4112;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.DynamicAppendNode;1076;-3200,-4208;Inherit;False;FLOAT4;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;1077;-3376,-4208;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;1078;-3376,-4016;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;1079;-3376,-3920;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SamplerNode;1080;-3696,-4224;Inherit;True;Property;_TextureSample29;Texture
    Sample 0;35;1;[SingleLineTexture];Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.RegisterLocalVarNode;1081;-2864,-4144;Inherit;False;SnowSplatG;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;1082;-1984,-1264;Half;False;Property;_SnowNormalStrength;Normal
    Strength;60;0;Create;False;1;;0;0;False;0;False;2;1;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;1083;-2528,-1744;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.RangedFloatNode;1084;-2704,-1696;Inherit;False;Constant;_Float5;Float
    3;13;0;Create;True;0;0;0;False;0;False;100;300;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.VertexToFragmentNode;1085;-2768,-1776;Inherit;False;False;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;1086;-2896,-1776;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;1087;-3072,-1776;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SwizzleNode;1088;-3232,-1648;Inherit;False;FLOAT2;0;1;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SwizzleNode;1089;-3072,-1600;Inherit;False;FLOAT2;2;3;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;1090;-1456,-1344;Inherit;False;SnowNormal;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.TextureCoordinatesNode;1091;-3984,-4352;Inherit;False;0;731;2;3;2;SAMPLER2D;;False;0;FLOAT2;1,1;False;1;FLOAT2;0,0;False;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.TexturePropertyNode;1092;-3984,-4224;Inherit;True;Property;_SnowMapSplat;Splat
    Mask;54;1;[SingleLineTexture];Create;False;1;;0;0;False;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.RangedFloatNode;1093;-3568,-4032;Half;False;Property;_SnowSplatRSplatBias;Splat
    Bias;62;0;Create;False;0;0;0;False;0;False;1;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;1094;-3568,-3952;Half;False;Property;_SnowSplatGSplatBias;Splat
    Bias;69;0;Create;False;0;0;0;False;0;False;1;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;1095;-3568,-3872;Half;False;Property;_SnowSplatBSplatBias;Splat
    Bias;76;0;Create;False;0;0;0;False;0;False;1;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;1096;-3568,-3792;Half;False;Property;_SnowSplatASplatBias;Splat
    Bias;83;0;Create;False;0;0;0;False;0;False;1;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;1097;-2512,-3968;Half;False;Property;_SnowSplatRBlendFactor;Blend
    Factor;67;0;Create;False;1;;0;0;False;0;False;0;0.01;0;10;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;1098;-2464,-3328;Half;False;Property;_SnowSplatGBlendFactor;Blend
    Factor;74;0;Create;False;1;;0;0;False;0;False;0;0.01;0;10;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;1099;-2448,-2768;Half;False;Property;_SnowSplatBBlendFactor;Blend
    Factor;81;0;Create;False;1;;0;0;False;0;False;0;0.01;0;10;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;1100;-2416,-2192;Half;False;Property;_SnowSplatABlendFactor;Blend
    Factor;88;0;Create;False;1;;0;0;False;0;False;0;0.01;0;10;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SamplerNode;1101;-2352,-1600;Inherit;True;Property;_TextureSample30;Texture
    Sample 2;6;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.RegisterLocalVarNode;1102;-2352,-1680;Inherit;False;SSSnowMapBaseColor;-1;True;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.SamplerNode;1103;-2352,-1344;Inherit;True;Property;_TextureSample31;Texture
    Sample 4;60;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SwizzleNode;1104;-2048,-1600;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.ColorNode;1108;-1296,-1776;Half;False;Property;_SnowColor;Tint;29;0;Create;False;1;;0;0;False;0;False;1,1,1,0;0.3647059,0.5372549,0.3411765,0;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SwizzleNode;1109;-1072,-1776;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;1117;-3024,-4304;Inherit;False;SnowSplatRGBA;-1;True;1;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.BreakToComponentsNode;1118;-3024,-4208;Inherit;False;FLOAT4;1;0;FLOAT4;0,0,0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.RegisterLocalVarNode;1119;-2864,-4224;Inherit;False;SnowSplatR;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;1120;-2864,-4064;Inherit;False;SnowSplatB;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;1121;-2864,-3984;Inherit;False;SnowSplatA;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WorldPosInputsNode;1122;-2544,-4368;Inherit;False;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.RegisterLocalVarNode;1123;-2336,-4368;Inherit;False;WorldPosition;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;1124;-976,-4096;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1125;-2176,-2304;Inherit;False;735;SSNormal0;1;0;OBJECT;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.LerpOp;1126;-976,-2320;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.UnpackScaleNormalNode;1127;-1728,-1344;Inherit;False;Tangent;2;0;FLOAT4;0,0,0,0;False;1;FLOAT;1;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.TexturePropertyNode;1128;-2672,-1600;Inherit;True;Property;_SnowMapBaseColor;BaseColor;57;1;[SingleLineTexture];Create;False;0;0;0;False;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.TexturePropertyNode;1129;-2672,-1344;Inherit;True;Property;_SnowMapNormal;Normal
    Map;59;2;[Normal];[SingleLineTexture];Create;False;0;0;0;False;0;False;None;None;False;bump;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.TexCoordVertexDataNode;1130;-3440,-1776;Inherit;False;0;2;0;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.Vector4Node;1131;-3424,-1600;Inherit;False;Property;_SnowMainUVs;Main
    UVs;58;0;Create;False;0;0;0;False;0;False;0.002,0.002,0,0;0,0,0,0;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.RangedFloatNode;1061;-1408,-3728;Half;False;Property;_SnowSplatGMin;Min;70;0;Create;False;1;;0;0;False;0;False;-0.5;0.5;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;1041;-1392,-3184;Half;False;Property;_SnowSplatBMin;Min;77;0;Create;False;1;;0;0;False;0;False;-0.5;0.5;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;1040;-1360,-3104;Half;False;Property;_SnowSplatBMax;Max;78;0;Create;False;1;;0;0;False;0;False;1;0.5;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;1047;-1392,-2608;Half;False;Property;_SnowSplatAMin;Min;84;0;Create;False;1;;0;0;False;0;False;-0.5;0.5;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.LerpOp;1133;2096,-16;Inherit;False;3;0;FLOAT3;1,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;1134;2096,192;Inherit;False;3;0;FLOAT3;1,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1135;2320,80;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SummedBlendNode;1136;2432,-32;Inherit;False;5;0;FLOAT4;0,0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;3;FLOAT3;0,0,0;False;4;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1137;2352,96;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1138;2384,112;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1139;2320,192;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1140;2336,384;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1141;2352,576;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;1142;2640,-128;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1151;1600,448;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1152;1600,656;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1153;1600,272;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1154;1584,80;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1155;1584,80;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1156;1584,80;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1157;1360,32;Inherit;False;1112;SnowBaseColor;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;1158;1680,224;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;1159;1680,0;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;1160;1680,416;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;1161;1680,624;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;1162;2096,608;Inherit;False;3;0;FLOAT3;1,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;1163;2096,400;Inherit;False;3;0;FLOAT3;1,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;1173;2624,144;Inherit;False;SnowEnable;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;1174;2096,112;Inherit;False;SnowEnableRChannel;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;1175;2096,320;Inherit;False;SnowEnableGChannel;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;1176;2096,528;Inherit;False;SnowEnableBChannel;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;1177;2096,736;Inherit;False;SnowEnableAChannel;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1178;1360,112;Inherit;False;1071;SnowSplatRMask;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1179;1360,272;Inherit;False;1072;SnowSplatGMask;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1180;1360,464;Inherit;False;1073;SnowSplatBMask;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1181;1360,672;Inherit;False;1074;SnowSplatAMask;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;1182;2464,144;Half;False;Property;_SnowEnable;ENABLE;53;2;[Header];[ToggleUI];Create;False;1;SNOW;0;0;False;0;False;0;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1183;2192,-96;Inherit;False;1117;SnowSplatRGBA;1;0;OBJECT;;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.RangedFloatNode;1191;1792,544;Half;False;Property;_SnowSplatBEnable;ENABLE
    CHANNEL BLUE;75;1;[ToggleUI];Create;False;0;0;0;False;1;Space(10);False;0;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;1192;1776,128;Half;False;Property;_SnowSplatREnable;ENABLE
    CHANNEL RED;61;1;[ToggleUI];Create;False;0;0;0;False;1;Space(10);False;0;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;1193;1760,336;Half;False;Property;_SnowSplatGEnable;ENABLE
    CHANNEL GREEN;68;1;[ToggleUI];Create;False;0;0;0;False;1;Space(10);False;0;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;1194;1776,752;Half;False;Property;_SnowSplatAEnable;ENABLE
    CHANNEL ALPHA;82;1;[ToggleUI];Create;False;0;0;0;False;1;Space(10);False;0;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1196;2571.894,-145.4189;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1195;2544,-192;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1197;1952,-192;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1198;2000,-160;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1199;1536,-192;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1200;1600,-160;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1202;2000,-160;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1201;1952,-192;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1204;2000,-160;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1203;1952,-192;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1206;1600,-160;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1205;1536,-192;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1208;2000,-160;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1207;1952,-192;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1210;1600,-160;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1209;1536,-192;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1212;1600,-160;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1211;1536,-192;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitch;668;2800,-224;Inherit;False;Enable
    Snow;True;0;2;2;In 0;In 1;Object;-1;10;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;1214;1568,2480;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;1215;1568,2608;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;1216;1568,2736;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1217;1728,2496;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1218;1808,2480;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1219;1792,2736;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SummedBlendNode;1220;1920,2336;Inherit;False;5;0;FLOAT4;0,0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;3;FLOAT3;0,0,0;False;4;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;1221;1568,2352;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;1222;2144,2208;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1223;1920,2512;Inherit;False;1173;SnowEnable;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1239;1760,2432;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1240;1776,2464;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1241;1760,2608;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1243;1696,2288;Inherit;False;1117;SnowSplatRGBA;1;0;OBJECT;;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1244;1248,2528;Inherit;False;1175;SnowEnableGChannel;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1245;1248,2400;Inherit;False;1174;SnowEnableRChannel;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1247;1248,2656;Inherit;False;1176;SnowEnableBChannel;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1248;1248,2784;Inherit;False;1177;SnowEnableAChannel;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1246;1248,2320;Inherit;False;1090;SnowNormal;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1249;1440,2144;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1251;1440,2144;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1252;1472,2192;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1250;1472,2192;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1254;1472,2192;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1253;1440,2144;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1256;1472,2192;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1255;1440,2144;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1257;1488,2352;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1258;1488,2480;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1237;1488,2512;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1259;1488,2592;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1235;1488,2640;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1260;1488,2720;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1233;1488,2768;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1261;2048,2128;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1262;2064,2160;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitch;669;2304,2096;Inherit;False;Option;False;0;2;2;In
    0;In 1;Instance;668;10;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionNode;1263;-1504,-1600;Inherit;False;Saturation;-1;;1;4f383aa3b2a7ef640be83276d286e709;0;2;12;FLOAT3;0,0,0;False;21;FLOAT;0.5;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RangedFloatNode;1106;-2000,-1520;Half;False;Property;_SnowSaturation;Saturation;56;0;Create;False;0;0;0;False;1;Space(5);False;0;0.65;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;1107;-1712,-1520;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;1110;-928,-1632;Inherit;False;3;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RangedFloatNode;1111;-1248,-1536;Half;False;Property;_SnowBrightness;Brightness;55;0;Create;False;1;;0;0;False;0;False;1;1;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;1112;-768,-1632;Inherit;False;SnowBaseColor;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SwizzleNode;226;-944,-192;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SwizzleNode;232;-944,80;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.ColorNode;222;-1248,80;Inherit;False;Global;_DiffuseRemapScale1;_DiffuseRemapScale1;23;0;Create;True;0;0;0;False;0;False;1,1,1,1;0,0,0,0;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.ColorNode;221;-1248,-192;Inherit;False;Global;_DiffuseRemapScale0;_DiffuseRemapScale0;23;0;Create;True;0;0;0;False;0;False;1,1,1,1;0,0,0,0;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.GetLocalVarNode;445;-976,160;Inherit;False;379;Splat1;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;433;-976,-112;Inherit;False;342;Splat0;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SwizzleNode;236;-944,368;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;446;-976,448;Inherit;False;357;Splat2;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.ColorNode;223;-1248,368;Inherit;False;Global;_DiffuseRemapScale2;_DiffuseRemapScale2;23;0;Create;True;0;0;0;False;0;False;1,1,1,1;0,0,0,0;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.GetLocalVarNode;447;-976,736;Inherit;False;390;Splat3;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SwizzleNode;240;-944,656;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.ColorNode;224;-1248,656;Inherit;False;Global;_DiffuseRemapScale3;_DiffuseRemapScale3;23;0;Create;True;0;0;0;False;0;False;1,1,1,1;0,0,0,0;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SwizzleNode;881;-944,944;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;911;-976,1024;Inherit;False;752;Splat4;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.ColorNode;916;-1248,944;Inherit;False;Global;_DiffuseRemapScale4;_DiffuseRemapScale4;23;0;Create;True;0;0;0;False;0;False;1,1,1,1;0,0,0,0;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;901;-768,1216;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;900;-768,944;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.ColorNode;913;-1248,1216;Inherit;False;Global;_DiffuseRemapScale5;_DiffuseRemapScale5;23;0;Create;True;0;0;0;False;0;False;1,1,1,1;0,0,0,0;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.GetLocalVarNode;917;-976,1296;Inherit;False;743;Splat5;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SwizzleNode;883;-944,1216;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;919;-768,1488;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;918;-992,1568;Inherit;False;759;Splat6;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SwizzleNode;888;-960,1488;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.ColorNode;914;-1248,1488;Inherit;False;Global;_DiffuseRemapScale6;_DiffuseRemapScale6;23;0;Create;True;0;0;0;False;0;False;1,1,1,1;0,0,0,0;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.GetLocalVarNode;920;-976,1872;Inherit;False;762;Splat7;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;921;-768,1776;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SwizzleNode;897;-944,1776;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.ColorNode;898;-1248,1776;Inherit;False;Global;_DiffuseRemapScale7;_DiffuseRemapScale7;23;0;Create;True;0;0;0;False;0;False;1,1,1,1;0,0,0,0;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.FunctionOutput;975;1037.223,125.0496;Inherit;False;False;-1;Alpha;5;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;979;1042.269,219.9976;Inherit;False;False;-1;Alpha
    Clip;6;False;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.FunctionOutput;56;31.45929,3656.361;Inherit;False;False;-1;Metallic;2;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;45;-4.72972,5002.875;Inherit;False;False;-1;Smoothness;3;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;200;286.5341,6349.577;Inherit;False;False;-1;Occlusion;4;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;998;-736,-896;Inherit;False;False;-1;Vertex
    Normals;7;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionOutput;999;-816,-544;Inherit;False;False;-1;Vertex
    Tangents;8;False;1;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.FunctionOutput;0;3072,-224;Inherit;False;True;-1;BaseColor;0;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionOutput;14;2576,2096;Inherit;False;False;-1;Normal;1;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nWireConnection;414;0;324;0\nWireConnection;414;1;426;0\nWireConnection;414;7;431;0\nWireConnection;416;0;332;0\nWireConnection;416;1;426;0\nWireConnection;416;7;432;0\nWireConnection;426;2;324;0\nWireConnection;430;0;326;1\nWireConnection;431;0;324;1\nWireConnection;432;0;332;1\nWireConnection;420;0;318;0\nWireConnection;420;1;428;0\nWireConnection;420;7;364;0\nWireConnection;421;0;317;0\nWireConnection;421;1;428;0\nWireConnection;421;7;365;0\nWireConnection;422;0;307;0\nWireConnection;422;1;428;0\nWireConnection;422;7;366;0\nWireConnection;428;2;318;0\nWireConnection;417;0;316;0\nWireConnection;417;1;427;0\nWireConnection;417;7;347;0\nWireConnection;427;2;316;0\nWireConnection;419;0;311;0\nWireConnection;419;1;427;0\nWireConnection;419;7;349;0\nWireConnection;418;0;308;0\nWireConnection;418;1;427;0\nWireConnection;418;7;348;0\nWireConnection;429;2;314;0\nWireConnection;425;0;309;0\nWireConnection;425;1;429;0\nWireConnection;425;7;387;0\nWireConnection;424;0;310;0\nWireConnection;424;1;429;0\nWireConnection;424;7;385;0\nWireConnection;423;0;314;0\nWireConnection;423;1;429;0\nWireConnection;423;7;383;0\nWireConnection;325;0;324;1\nWireConnection;327;0;326;1\nWireConnection;333;0;332;1\nWireConnection;471;0;417;0\nWireConnection;470;0;420;0\nWireConnection;436;0;420;4\nWireConnection;378;0;421;0\nWireConnection;370;0;422;1\nWireConnection;371;0;422;2\nWireConnection;379;0;470;0\nWireConnection;412;0;422;3\nWireConnection;369;0;422;4\nWireConnection;341;0;415;0\nWireConnection;334;0;416;1\nWireConnection;409;0;416;2\nWireConnection;410;0;416;3\nWireConnection;335;0;416;4\nWireConnection;469;0;414;0\nWireConnection;342;0;469;0\nWireConnection;435;0;414;4\nWireConnection;356;0;418;0\nWireConnection;359;0;419;1\nWireConnection;358;0;419;2\nWireConnection;411;0;419;3\nWireConnection;360;0;419;4\nWireConnection;357;0;471;0\nWireConnection;438;0;417;4\nWireConnection;472;0;423;0\nWireConnection;390;0;472;0\nWireConnection;440;0;423;4\nWireConnection;398;0;424;0\nWireConnection;388;0;425;1\nWireConnection;389;0;425;2\nWireConnection;413;0;425;3\nWireConnection;391;0;425;4\nWireConnection;415;0;326;0\nWireConnection;415;1;426;0\nWireConnection;415;7;430;0\nWireConnection;696;0;733;0\nWireConnection;696;1;698;0\nWireConnection;696;7;700;0\nWireConnection;697;0;732;0\nWireConnection;697;1;698;0\nWireConnection;697;7;701;0\nWireConnection;698;2;733;0\nWireConnection;699;0;770;1\nWireConnection;700;0;733;1\nWireConnection;701;0;732;1\nWireConnection;702;0;723;0\nWireConnection;702;1;705;0\nWireConnection;702;7;708;0\nWireConnection;703;0;724;0\nWireConnection;703;1;705;0\nWireConnection;703;7;706;0\nWireConnection;704;0;725;0\nWireConnection;704;1;705;0\nWireConnection;704;7;707;0\nWireConnection;705;2;723;0\nWireConnection;709;0;726;0\nWireConnection;709;1;710;0\nWireConnection;709;7;715;0\nWireConnection;710;2;726;0\nWireConnection;711;0;728;0\nWireConnection;711;1;710;0\nWireConnection;711;7;713;0\nWireConnection;712;0;727;0\nWireConnection;712;1;710;0\nWireConnection;712;7;714;0\nWireConnection;716;2;729;0\nWireConnection;717;0;731;0\nWireConnection;717;1;716;0\nWireConnection;717;7;721;0\nWireConnection;718;0;730;0\nWireConnection;718;1;716;0\nWireConnection;718;7;722;0\nWireConnection;719;0;729;0\nWireConnection;719;1;716;0\nWireConnection;719;7;720;0\nWireConnection;734;0;733;1\nWireConnection;735;0;770;1\nWireConnection;736;0;732;1\nWireConnection;737;0;709;0\nWireConnection;738;0;702;0\nWireConnection;751;0;696;0\nWireConnection;761;0;719;0\nWireConnection;769;0;770;0\nWireConnection;769;1;698;0\nWireConnection;769;7;699;0\nWireConnection;752;0;751;0\nWireConnection;753;0;696;4\nWireConnection;746;0;769;0\nWireConnection;747;0;697;1\nWireConnection;748;0;697;2\nWireConnection;749;0;697;3\nWireConnection;750;0;697;4\nWireConnection;743;0;738;0\nWireConnection;739;0;702;4\nWireConnection;740;0;703;0\nWireConnection;741;0;704;1\nWireConnection;742;0;704;2\nWireConnection;744;0;704;3\nWireConnection;745;0;704;4\nWireConnection;759;0;737;0\nWireConnection;760;0;709;4\nWireConnection;754;0;712;0\nWireConnection;755;0;711;1\nWireConnection;756;0;711;2\nWireConnection;757;0;711;3\nWireConnection;758;0;711;4\nWireConnection;762;0;761;0\nWireConnection;763;0;719;4\nWireConnection;764;0;718;0\nWireConnection;765;0;717;1\nWireConnection;766;0;717;2\nWireConnection;767;0;717;3\nWireConnection;768;0;717;4\nWireConnection;461;0;467;0\nWireConnection;460;0;465;0\nWireConnection;464;0;460;0\nWireConnection;466;0;461;0\nWireConnection;9;0;449;0\nWireConnection;9;1;35;0\nWireConnection;9;2;463;0\nWireConnection;9;3;464;0\nWireConnection;9;4;466;0\nWireConnection;451;0;452;0\nWireConnection;451;7;452;1\nWireConnection;102;0;968;0\nWireConnection;102;1;453;0\nWireConnection;453;0;969;0\nWireConnection;453;1;451;1\nWireConnection;968;0;9;0\nWireConnection;968;1;912;0\nWireConnection;969;0;968;0\nWireConnection;980;0;451;0\nWireConnection;976;1;977;0\nWireConnection;978;1;980;0\nWireConnection;38;0;232;0\nWireConnection;38;1;445;0\nWireConnection;462;0;38;0\nWireConnection;465;0;41;0\nWireConnection;41;0;236;0\nWireConnection;41;1;446;0\nWireConnection;467;0;44;0\nWireConnection;44;0;240;0\nWireConnection;44;1;447;0\nWireConnection;912;0;902;0\nWireConnection;912;1;900;0\nWireConnection;912;2;926;0\nWireConnection;912;3;927;0\nWireConnection;912;4;928;0\nWireConnection;926;0;923;0\nWireConnection;927;0;924;0\nWireConnection;928;0;925;0\nWireConnection;923;0;901;0\nWireConnection;924;0;919;0\nWireConnection;925;0;921;0\nWireConnection;521;0;527;0\nWireConnection;521;1;608;0\nWireConnection;522;0;608;0\nWireConnection;527;0;597;0\nWireConnection;609;0;613;0\nWireConnection;609;1;614;0\nWireConnection;610;0;614;0\nWireConnection;613;0;598;0\nWireConnection;616;0;620;0\nWireConnection;616;1;621;0\nWireConnection;617;0;621;0\nWireConnection;620;0;599;0\nWireConnection;623;0;627;0\nWireConnection;623;1;628;0\nWireConnection;624;0;628;0\nWireConnection;627;0;600;0\nWireConnection;626;0;623;0\nWireConnection;626;1;624;0\nWireConnection;619;0;616;0\nWireConnection;619;1;617;0\nWireConnection;612;0;609;0\nWireConnection;612;1;610;0\nWireConnection;525;0;521;0\nWireConnection;525;1;522;0\nWireConnection;526;0;525;0\nWireConnection;615;0;612;0\nWireConnection;622;0;619;0\nWireConnection;629;0;626;0\nWireConnection;602;0;596;0\nWireConnection;602;1;526;0\nWireConnection;602;2;630;0\nWireConnection;602;3;631;0\nWireConnection;602;4;632;0\nWireConnection;630;0;615;0\nWireConnection;631;0;622;0\nWireConnection;632;0;629;0\nWireConnection;776;0;778;0\nWireConnection;776;1;805;0\nWireConnection;777;0;805;0\nWireConnection;778;0;804;0\nWireConnection;779;0;781;0\nWireConnection;779;1;806;0\nWireConnection;780;0;806;0\nWireConnection;781;0;788;0\nWireConnection;782;0;784;0\nWireConnection;782;1;807;0\nWireConnection;783;0;807;0\nWireConnection;784;0;789;0\nWireConnection;785;0;787;0\nWireConnection;785;1;808;0\nWireConnection;786;0;808;0\nWireConnection;787;0;790;0\nWireConnection;792;0;782;0\nWireConnection;792;1;783;0\nWireConnection;793;0;779;0\nWireConnection;793;1;780;0\nWireConnection;794;0;776;0\nWireConnection;794;1;777;0\nWireConnection;795;0;794;0\nWireConnection;796;0;793;0\nWireConnection;797;0;792;0\nWireConnection;798;0;791;0\nWireConnection;799;0;800;0\nWireConnection;799;1;795;0\nWireConnection;799;2;935;0\nWireConnection;799;3;936;0\nWireConnection;799;4;937;0\nWireConnection;791;0;785;0\nWireConnection;791;1;786;0\nWireConnection;548;0;579;0\nWireConnection;550;0;580;0\nWireConnection;551;0;550;0\nWireConnection;551;1;553;0\nWireConnection;552;0;548;0\nWireConnection;552;1;549;0\nWireConnection;554;0;581;0\nWireConnection;555;0;554;0\nWireConnection;555;1;556;0\nWireConnection;557;0;582;0\nWireConnection;558;0;557;0\nWireConnection;558;1;559;0\nWireConnection;547;0;561;0\nWireConnection;547;1;552;0\nWireConnection;547;2;929;0\nWireConnection;547;3;930;0\nWireConnection;547;4;931;0\nWireConnection;810;0;817;0\nWireConnection;811;0;819;0\nWireConnection;812;0;827;0\nWireConnection;813;0;828;0\nWireConnection;814;0;813;0\nWireConnection;814;1;823;0\nWireConnection;815;0;812;0\nWireConnection;815;1;822;0\nWireConnection;816;0;829;0\nWireConnection;817;0;816;0\nWireConnection;817;1;824;0\nWireConnection;818;0;830;0\nWireConnection;819;0;818;0\nWireConnection;819;1;825;0\nWireConnection;820;0;814;0\nWireConnection;826;0;821;0\nWireConnection;826;1;815;0\nWireConnection;826;2;820;0\nWireConnection;826;3;810;0\nWireConnection;826;4;811;0\nWireConnection;543;0;532;0\nWireConnection;544;0;534;0\nWireConnection;536;0;537;0\nWireConnection;536;1;531;0\nWireConnection;536;2;542;0\nWireConnection;536;3;543;0\nWireConnection;536;4;544;0\nWireConnection;530;0;48;0\nWireConnection;529;0;49;0\nWireConnection;528;0;529;0\nWireConnection;528;1;539;0\nWireConnection;531;0;530;0\nWireConnection;531;1;538;0\nWireConnection;533;0;50;0\nWireConnection;532;0;533;0\nWireConnection;532;1;540;0\nWireConnection;535;0;51;0\nWireConnection;534;0;535;0\nWireConnection;534;1;541;0\nWireConnection;542;0;528;0\nWireConnection;832;0;842;0\nWireConnection;833;0;845;0\nWireConnection;834;0;848;0\nWireConnection;834;1;839;0\nWireConnection;834;2;847;0\nWireConnection;834;3;832;0\nWireConnection;834;4;833;0\nWireConnection;835;0;850;0\nWireConnection;837;0;849;0\nWireConnection;838;0;837;0\nWireConnection;838;1;840;0\nWireConnection;839;0;835;0\nWireConnection;839;1;836;0\nWireConnection;841;0;851;0\nWireConnection;842;0;841;0\nWireConnection;842;1;843;0\nWireConnection;844;0;852;0\nWireConnection;845;0;844;0\nWireConnection;845;1;846;0\nWireConnection;847;0;838;0\nWireConnection;491;0;477;0\nWireConnection;491;1;485;0\nWireConnection;490;0;481;0\nWireConnection;490;1;482;0\nWireConnection;496;0;476;0\nWireConnection;496;1;483;0\nWireConnection;494;0;480;0\nWireConnection;494;1;484;0\nWireConnection;473;0;474;0\nWireConnection;473;1;490;0\nWireConnection;473;2;500;0\nWireConnection;473;3;501;0\nWireConnection;473;4;502;0\nWireConnection;500;0;496;0\nWireConnection;501;0;494;0\nWireConnection;502;0;491;0\nWireConnection;854;0;859;0\nWireConnection;854;1;870;0\nWireConnection;856;0;855;0\nWireConnection;856;1;867;0\nWireConnection;857;0;864;0\nWireConnection;857;1;868;0\nWireConnection;858;0;865;0\nWireConnection;858;1;869;0\nWireConnection;860;0;866;0\nWireConnection;860;1;856;0\nWireConnection;860;2;861;0\nWireConnection;860;3;862;0\nWireConnection;860;4;863;0\nWireConnection;861;0;857;0\nWireConnection;862;0;858;0\nWireConnection;863;0;854;0\nWireConnection;929;0;932;0\nWireConnection;930;0;933;0\nWireConnection;931;0;934;0\nWireConnection;932;0;551;0\nWireConnection;933;0;555;0\nWireConnection;934;0;558;0\nWireConnection;935;0;938;0\nWireConnection;936;0;939;0\nWireConnection;937;0;940;0\nWireConnection;938;0;796;0\nWireConnection;939;0;797;0\nWireConnection;940;0;798;0\nWireConnection;513;0;970;0\nWireConnection;515;0;513;2\nWireConnection;515;1;516;0\nWireConnection;514;0;513;0\nWireConnection;514;1;513;1\nWireConnection;514;2;515;0\nWireConnection;503;1;514;0\nWireConnection;503;0;514;0\nWireConnection;504;0;514;0\nWireConnection;504;1;503;0\nWireConnection;970;0;473;0\nWireConnection;970;1;860;0\nWireConnection;971;0;536;0\nWireConnection;971;1;834;0\nWireConnection;972;0;547;0\nWireConnection;972;1;826;0\nWireConnection;634;0;973;0\nWireConnection;973;0;602;0\nWireConnection;973;1;799;0\nWireConnection;283;0;284;0\nWireConnection;283;1;286;0\nWireConnection;283;7;448;0\nWireConnection;295;0;296;0\nWireConnection;295;1;289;0\nWireConnection;289;0;284;0\nWireConnection;291;0;289;1\nWireConnection;290;0;291;0\nWireConnection;290;1;292;0\nWireConnection;288;0;291;1\nWireConnection;288;1;297;0\nWireConnection;293;0;290;0\nWireConnection;293;1;288;0\nWireConnection;294;0;295;0\nWireConnection;294;1;293;0\nWireConnection;286;0;294;0\nWireConnection;448;0;284;1\nWireConnection;947;0;961;0\nWireConnection;947;1;958;0\nWireConnection;947;7;962;0\nWireConnection;948;0;949;0\nWireConnection;948;1;950;0\nWireConnection;950;0;961;0\nWireConnection;951;0;950;1\nWireConnection;954;0;951;0\nWireConnection;954;1;952;0\nWireConnection;955;0;951;1\nWireConnection;955;1;953;0\nWireConnection;956;0;954;0\nWireConnection;956;1;955;0\nWireConnection;957;0;948;0\nWireConnection;957;1;956;0\nWireConnection;958;0;957;0\nWireConnection;962;0;961;1\nWireConnection;26;0;283;0\nWireConnection;922;0;947;0\nWireConnection;992;0;991;0\nWireConnection;992;1;990;0\nWireConnection;993;0;992;0\nWireConnection;996;0;995;0\nWireConnection;996;1;994;0\nWireConnection;997;0;996;0\nWireConnection;997;3;996;0\nWireConnection;997;1;994;0\nWireConnection;997;2;994;0\nWireConnection;463;0;459;0\nWireConnection;459;0;462;0\nWireConnection;35;0;226;0\nWireConnection;35;1;433;0\nWireConnection;1006;0;1070;0\nWireConnection;1007;0;1011;0\nWireConnection;1007;1;1009;0\nWireConnection;1008;1;1007;0\nWireConnection;1008;2;1006;0\nWireConnection;1009;0;1010;0\nWireConnection;1011;0;1012;0\nWireConnection;1012;1;1013;0\nWireConnection;1013;1;1100;0\nWireConnection;1014;0;1015;0\nWireConnection;1017;1;1099;0\nWireConnection;1018;0;1019;0\nWireConnection;1020;0;1023;0\nWireConnection;1021;0;1020;0\nWireConnection;1021;1;1018;0\nWireConnection;1022;1;1021;0\nWireConnection;1022;2;1054;0\nWireConnection;1023;0;1024;0\nWireConnection;1023;1;1025;0\nWireConnection;1025;1;1098;0\nWireConnection;1026;0;1031;0\nWireConnection;1027;0;1026;0\nWireConnection;1027;1;1028;0\nWireConnection;1028;0;1030;0\nWireConnection;1029;1;1027;0\nWireConnection;1029;2;1056;0\nWireConnection;1031;0;1032;0\nWireConnection;1031;1;1033;0\nWireConnection;1033;1;1097;0\nWireConnection;1034;0;1022;0\nWireConnection;1035;0;1029;0\nWireConnection;1036;1;1034;0\nWireConnection;1036;2;1060;0\nWireConnection;1037;0;1039;1\nWireConnection;1037;1;1041;0\nWireConnection;1037;2;1040;0\nWireConnection;1038;1;1063;0\nWireConnection;1038;2;1037;0\nWireConnection;1039;0;1042;0\nWireConnection;1043;0;1044;1\nWireConnection;1043;1;1047;0\nWireConnection;1043;2;1046;0\nWireConnection;1044;0;1045;0\nWireConnection;1048;0;1008;0\nWireConnection;1051;0;1049;0\nWireConnection;1053;0;1051;1\nWireConnection;1053;1;1050;0\nWireConnection;1053;2;1052;0\nWireConnection;1054;0;1055;0\nWireConnection;1056;0;1057;0\nWireConnection;1059;0;1062;0\nWireConnection;1060;0;1059;1\nWireConnection;1060;1;1061;0\nWireConnection;1060;2;1058;0\nWireConnection;1063;0;1064;0\nWireConnection;1064;1;1065;0\nWireConnection;1064;2;1068;0\nWireConnection;1065;0;1066;0\nWireConnection;1065;1;1014;0\nWireConnection;1066;0;1067;0\nWireConnection;1067;1;1017;0\nWireConnection;1068;0;1069;0\nWireConnection;1071;0;1124;0\nWireConnection;1072;0;1036;0\nWireConnection;1073;0;1038;0\nWireConnection;1074;0;1126;0\nWireConnection;1075;0;1080;2\nWireConnection;1075;1;1094;0\nWireConnection;1076;0;1077;0\nWireConnection;1076;1;1075;0\nWireConnection;1076;2;1078;0\nWireConnection;1076;3;1079;0\nWireConnection;1077;0;1080;1\nWireConnection;1077;1;1093;0\nWireConnection;1078;0;1080;3\nWireConnection;1078;1;1095;0\nWireConnection;1079;0;1080;4\nWireConnection;1079;1;1096;0\nWireConnection;1080;0;1092;0\nWireConnection;1080;1;1091;0\nWireConnection;1080;7;1092;1\nWireConnection;1081;0;1118;1\nWireConnection;1083;0;1085;0\nWireConnection;1083;1;1084;0\nWireConnection;1085;0;1086;0\nWireConnection;1086;0;1087;0\nWireConnection;1086;1;1089;0\nWireConnection;1087;0;1130;0\nWireConnection;1087;1;1088;0\nWireConnection;1088;0;1131;0\nWireConnection;1089;0;1131;0\nWireConnection;1090;0;1127;0\nWireConnection;1101;0;1128;0\nWireConnection;1101;1;1083;0\nWireConnection;1101;7;1128;1\nWireConnection;1102;0;1128;1\nWireConnection;1103;0;1129;0\nWireConnection;1103;1;1083;0\nWireConnection;1103;7;1129;1\nWireConnection;1104;0;1101;0\nWireConnection;1109;0;1108;0\nWireConnection;1117;0;1076;0\nWireConnection;1118;0;1076;0\nWireConnection;1119;0;1118;0\nWireConnection;1120;0;1118;2\nWireConnection;1121;0;1118;3\nWireConnection;1123;0;1122;0\nWireConnection;1124;1;1035;0\nWireConnection;1124;2;1053;0\nWireConnection;1126;1;1048;0\nWireConnection;1126;2;1043;0\nWireConnection;1127;0;1103;0\nWireConnection;1127;1;1082;0\nWireConnection;1133;0;1198;0\nWireConnection;1133;1;1159;0\nWireConnection;1133;2;1192;0\nWireConnection;1134;0;1202;0\nWireConnection;1134;1;1158;0\nWireConnection;1134;2;1193;0\nWireConnection;1135;0;1139;0\nWireConnection;1136;0;1183;0\nWireConnection;1136;1;1133;0\nWireConnection;1136;2;1135;0\nWireConnection;1136;3;1137;0\nWireConnection;1136;4;1138;0\nWireConnection;1137;0;1140;0\nWireConnection;1138;0;1141;0\nWireConnection;1139;0;1134;0\nWireConnection;1140;0;1163;0\nWireConnection;1141;0;1162;0\nWireConnection;1142;0;1196;0\nWireConnection;1142;1;1136;0\nWireConnection;1142;2;1182;0\nWireConnection;1151;0;1155;0\nWireConnection;1152;0;1156;0\nWireConnection;1153;0;1154;0\nWireConnection;1154;0;1157;0\nWireConnection;1155;0;1157;0\nWireConnection;1156;0;1157;0\nWireConnection;1158;0;1206;0\nWireConnection;1158;1;1153;0\nWireConnection;1158;2;1179;0\nWireConnection;1159;0;1200;0\nWireConnection;1159;1;1157;0\nWireConnection;1159;2;1178;0\nWireConnection;1160;0;1210;0\nWireConnection;1160;1;1151;0\nWireConnection;1160;2;1180;0\nWireConnection;1161;0;1212;0\nWireConnection;1161;1;1152;0\nWireConnection;1161;2;1181;0\nWireConnection;1162;0;1208;0\nWireConnection;1162;1;1161;0\nWireConnection;1162;2;1194;0\nWireConnection;1163;0;1204;0\nWireConnection;1163;1;1160;0\nWireConnection;1163;2;1191;0\nWireConnection;1173;0;1182;0\nWireConnection;1174;0;1192;0\nWireConnection;1175;0;1193;0\nWireConnection;1176;0;1191;0\nWireConnection;1177;0;1194;0\nWireConnection;1196;0;1195;0\nWireConnection;1195;0;102;0\nWireConnection;1197;0;102;0\nWireConnection;1198;0;1197;0\nWireConnection;1199;0;102;0\nWireConnection;1200;0;1199;0\nWireConnection;1202;0;1201;0\nWireConnection;1201;0;102;0\nWireConnection;1204;0;1203;0\nWireConnection;1203;0;102;0\nWireConnection;1206;0;1205;0\nWireConnection;1205;0;102;0\nWireConnection;1208;0;1207;0\nWireConnection;1207;0;102;0\nWireConnection;1210;0;1209;0\nWireConnection;1209;0;102;0\nWireConnection;1212;0;1211;0\nWireConnection;1211;0;102;0\nWireConnection;668;0;102;0\nWireConnection;668;1;1142;0\nWireConnection;1214;0;1258;0\nWireConnection;1214;1;1237;0\nWireConnection;1214;2;1244;0\nWireConnection;1215;0;1259;0\nWireConnection;1215;1;1235;0\nWireConnection;1215;2;1247;0\nWireConnection;1216;0;1260;0\nWireConnection;1216;1;1233;0\nWireConnection;1216;2;1248;0\nWireConnection;1217;0;1214;0\nWireConnection;1218;0;1219;0\nWireConnection;1219;0;1216;0\nWireConnection;1220;0;1243;0\nWireConnection;1220;1;1221;0\nWireConnection;1220;2;1239;0\nWireConnection;1220;3;1240;0\nWireConnection;1220;4;1218;0\nWireConnection;1221;0;1257;0\nWireConnection;1221;1;1246;0\nWireConnection;1221;2;1245;0\nWireConnection;1222;0;1262;0\nWireConnection;1222;1;1220;0\nWireConnection;1222;2;1223;0\nWireConnection;1239;0;1217;0\nWireConnection;1240;0;1241;0\nWireConnection;1241;0;1215;0\nWireConnection;1249;0;504;0\nWireConnection;1251;0;504;0\nWireConnection;1252;0;1251;0\nWireConnection;1250;0;1249;0\nWireConnection;1254;0;1253;0\nWireConnection;1253;0;504;0\nWireConnection;1256;0;1255;0\nWireConnection;1255;0;504;0\nWireConnection;1257;0;1250;0\nWireConnection;1258;0;1252;0\nWireConnection;1237;0;1246;0\nWireConnection;1259;0;1254;0\nWireConnection;1235;0;1246;0\nWireConnection;1260;0;1256;0\nWireConnection;1233;0;1246;0\nWireConnection;1261;0;504;0\nWireConnection;1262;0;1261;0\nWireConnection;669;0;504;0\nWireConnection;669;1;1222;0\nWireConnection;1263;12;1104;0\nWireConnection;1263;21;1107;0\nWireConnection;1107;0;1106;0\nWireConnection;1110;0;1109;0\nWireConnection;1110;1;1263;0\nWireConnection;1110;2;1111;0\nWireConnection;1112;0;1110;0\nWireConnection;226;0;221;0\nWireConnection;232;0;222;0\nWireConnection;236;0;223;0\nWireConnection;240;0;224;0\nWireConnection;881;0;916;0\nWireConnection;901;0;883;0\nWireConnection;901;1;917;0\nWireConnection;900;0;881;0\nWireConnection;900;1;911;0\nWireConnection;883;0;913;0\nWireConnection;919;0;888;0\nWireConnection;919;1;918;0\nWireConnection;888;0;914;0\nWireConnection;921;0;897;0\nWireConnection;921;1;920;0\nWireConnection;897;0;898;0\nWireConnection;975;0;976;0\nWireConnection;979;0;978;0\nWireConnection;56;0;971;0\nWireConnection;45;0;972;0\nWireConnection;200;0;634;0\nWireConnection;998;0;997;0\nWireConnection;999;0;993;0\nWireConnection;0;0;668;0\nWireConnection;14;0;669;0\nASEEND*/\n//CHKSM=303B0251815B93A749C9609F05E59F379347ADCE"
  m_functionName: 
  m_description: 'Amplify Shader Pack Example

    *HDRP'
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 1
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_customEdited: 0
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 0
  m_headerStyle: 2
  m_headerColor: {r: 0.7647059, g: 0.30588236, b: 0, a: 1}
  m_customNodeCategory: Amplify Shader Pack
  m_previewPosition: 0
  m_hidden: 0
  m_url: 
