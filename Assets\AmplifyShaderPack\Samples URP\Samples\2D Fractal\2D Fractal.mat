%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 6
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: 2D Fractal
  m_Shader: {fileID: 4800000, guid: 965d986dbc7a4c2f9e69f37e2026aa39, type: 3}
  m_ShaderKeywords: 
  m_LightmapFlags: 2
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Floats:
    - _AlphaCutoff: 0.5
    - _MaxIter: 250
    - _QueueControl: 0
    - _QueueOffset: 0
    - _ReceiveShadows: 1
    - _Threshold: 2
    - _ZoomBase: 0.2
    - _ZoomOffset: 1
    - _ZoomScale: 3
    m_Colors:
    - _Center: {r: -0.766, g: -0.1009, b: 0, a: 0}
    - _EmissionColor: {r: 1, g: 1, b: 1, a: 1}
    - _Tint: {r: 1, g: 1, b: 1, a: 0}
  m_BuildTextureStacks: []
