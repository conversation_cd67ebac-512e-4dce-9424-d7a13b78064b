%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e78934177bebe0545ab566bb787b0282, type: 3}
  m_Name: Readme
  m_EditorClassIdentifier: 
  Icon: {fileID: 2800000, guid: 6e0dcf42c17f9a044936e8c9fc013768, type: 3}
  Title: Read From Atlas Tiled
  Description:
    Heading: 
    Text: This sample tiles a specific section of a spritesheet/atlas. <b>Improved
      Read From Atlas</b> material performs a more bit complex sampling that is less
      prone to mip issues.
    LinkText: 
    Url: 
  PropertiesHeader:
    Heading: Properties
    Text: 
    LinkText: 
    Url: 
  Properties:
  - Heading: Min
    Text: Minimum pixel coordinates from section to tile.
    LinkText: 
    Url: 
  - Heading: Max
    Text: Maximum pixel coordinates from section to tile.
    LinkText: 
    Url: 
  - Heading: TileSize
    Text: Value to set amount of tiling to perform.
    LinkText: 
    Url: 
  - Heading: Atlas
    Text: 'Texture containing atlas/spritesheet to perform sampling of specified
      section. '
    LinkText: 
    Url: 
  AdditionalProperties: []
  AdditionalScripts: []
  LoadedLayout: 0
  RPType: 3
