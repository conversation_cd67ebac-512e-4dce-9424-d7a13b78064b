fileFormatVersion: 2
guid: 1cc01168159cbaa4bb0b2963c8e36da0
ModelImporter:
  serializedVersion: 19301
  internalIDToNameTable:
  - first:
      1: 100000
    second: //RootNode
  - first:
      1: 100002
    second: prop_cctvCam_body_001
  - first:
      1: 100004
    second: prop_cctvCam_joint_001
  - first:
      1: 100006
    second: prop_cctvCam_body
  - first:
      1: 100008
    second: prop_cctvCam_joint
  - first:
      4: 400000
    second: //RootNode
  - first:
      4: 400002
    second: prop_cctvCam_body_001
  - first:
      4: 400004
    second: prop_cctvCam_joint_001
  - first:
      4: 400006
    second: prop_cctvCam_body
  - first:
      4: 400008
    second: prop_cctvCam_joint
  - first:
      23: 2300000
    second: //RootNode
  - first:
      23: 2300002
    second: prop_cctvCam_body_001
  - first:
      23: 2300004
    second: prop_cctvCam_joint_001
  - first:
      23: 2300006
    second: prop_cctvCam_body
  - first:
      23: 2300008
    second: prop_cctvCam_joint
  - first:
      33: 3300000
    second: //RootNode
  - first:
      33: 3300002
    second: prop_cctvCam_body_001
  - first:
      33: 3300004
    second: prop_cctvCam_joint_001
  - first:
      33: 3300006
    second: prop_cctvCam_body
  - first:
      33: 3300008
    second: prop_cctvCam_joint
  - first:
      43: 4300000
    second: prop_cctvCam_base_001
  - first:
      43: 4300002
    second: prop_cctvCam_joint_001
  - first:
      43: 4300004
    second: prop_cctvCam_body_001
  - first:
      43: 4300006
    second: prop_cctvCam_base
  - first:
      43: 4300008
    second: prop_cctvCam_joint
  - first:
      43: 4300010
    second: prop_cctvCam_body
  - first:
      95: 9500000
    second: //RootNode
  externalObjects:
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: prop_cctvCam_array_mat
    second: {fileID: 2100000, guid: 22f03e47f2501604da610957f04b3398, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: prop_cctvCam_glass_mat
    second: {fileID: 2100000, guid: ffb4375e1740d8a4db3521b50a8873f3, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: prop_cctvCam_mat
    second: {fileID: 2100000, guid: d5998aa084d481f41b629fe26de89c90, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: tile_tube_ribbed_mat
    second: {fileID: 2100000, guid: e05c61ec1ddcd4a45bdbdf6a9e833b75, type: 2}
  materials:
    materialImportMode: 0
    materialName: 1
    materialSearch: 1
    materialLocation: 0
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 0.01
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 0
    importBlendShapes: 1
    importCameras: 0
    importLights: 0
    fileIdsGeneration: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    meshOptimizationFlags: -1
    indexFormat: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 0
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 4
    normalCalculationMode: 0
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 1
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 0
  humanDescription:
    serializedVersion: 3
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 0.01
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 0
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 0
  humanoidOversampling: 1
  avatarSetup: 0
  additionalBone: 1
  userData: 
  assetBundleName: 
  assetBundleVariant: 
