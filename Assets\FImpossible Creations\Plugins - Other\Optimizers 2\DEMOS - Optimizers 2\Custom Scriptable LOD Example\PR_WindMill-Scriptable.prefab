%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &1041993026298586
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4139469948635072}
  - component: {fileID: 33025198681299532}
  - component: {fileID: 23450808877300066}
  m_Layer: 0
  m_Name: Head
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4139469948635072
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1041993026298586}
  serializedVersion: 2
  m_LocalRotation: {x: 0.7071068, y: 0, z: 0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 31.03, z: -0.39}
  m_LocalScale: {x: 2, y: 3, z: 2}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4316805118661950}
  m_LocalEulerAnglesHint: {x: 90, y: 0, z: 0}
--- !u!33 &33025198681299532
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1041993026298586}
  m_Mesh: {fileID: 10208, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &23450808877300066
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1041993026298586}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 60f805de395c51e4f86f073078fe564c, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 1
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &1100466955538692
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4805682158289842}
  - component: {fileID: 33544767074606692}
  - component: {fileID: 23988691365283214}
  - component: {fileID: 114070407282300220}
  m_Layer: 0
  m_Name: Cube
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4805682158289842
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1100466955538692}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: 0.86602545, w: 0.49999994}
  m_LocalPosition: {x: -7.3352356, y: -4.2350006, z: 0}
  m_LocalScale: {x: 1.7099999, y: 18, z: 0.29999998}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4620431284305208}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &33544767074606692
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1100466955538692}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &23988691365283214
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1100466955538692}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 60f805de395c51e4f86f073078fe564c, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 1
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!114 &114070407282300220
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1100466955538692}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ebc354317bbc5dd40b24b190775f892a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  RotationAxis: {x: 0, y: 1, z: 0}
  RotationRange: 15
  SinSpeed: 2
--- !u!1 &1117790654811296
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4316805118661950}
  - component: {fileID: 114327750956549150}
  m_Layer: 0
  m_Name: PR_WindMill-Scriptable
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4316805118661950
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1117790654811296}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 118.1, y: 27.8, z: 162.4}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4509537545006128}
  - {fileID: 4139469948635072}
  - {fileID: 4620431284305208}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &114327750956549150
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1117790654811296}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a49c4834b5aab84409df113a192e4c8d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  AddToContainer: 1
  _editor_DrawSetup: 1
  _editor_DrawOptimizeList: 1
  _editor_DrawAddFeatures: 0
  _editor_DrawLODLevelsSetup: 1
  _editor_DrawExtra: 0
  _editor_horizontal: 1
  _editor_category: 0
  Editor_InIsolatedScene: 0
  Editor_JustCreated: 0
  UseDOTS: 0
  DOTSObstacleType: 2
  DOTSDetection: 0
  DOTSMeshData: []
  DOTSOffset: {x: 0, y: 0, z: 0}
  DOTSSize: {x: 1, y: 1, z: 1}
  DOTSRadius: 1
  UseMultiShape: 0
  AutoPrecision: 0.25
  AutoReferenceMesh: {fileID: 0}
  DrawPositionHandles: 1
  ScalingHandles: 1
  Shapes: []
  ShapePos: []
  ShapeRadius: []
  UseObstacleDetection: 0
  CoveragePrecision: 1
  CoverageScale: 1
  CoverageMask:
    serializedVersion: 2
    m_Bits: 1
  CustomCoveragePoints: 0
  CoverageOffsets: []
  ignoredObstacleColliders: []
  OnlyCamCollLayer:
    serializedVersion: 2
    m_Bits: 0
  LODLevels: 1
  preLODLevels: 1
  MaxDistance: 900
  OptimizingMethod: 0
  CullIfNotSee: 1
  DetectionRadius: 28.424
  DetectionBounds: {x: 2.052, y: 21.6, z: 0.35999998}
  Hideable: 0
  DetectionOffset: {x: 0, y: 21.9, z: 0}
  GizmosAlpha: 1
  DrawGizmos: 1
  FadeDuration: 0
  FadeViewVisibility: 0
  DeactivateObject: 0
  LODPercent:
  - 1
  AutoDistance: 0
  AutoDistanceFactor: 0
  DrawAutoDistanceToggle: 1
  HiddenCullAt: 0
  LimitLODLevels: 0
  UnlockFirstLOD: 0
  DrawGeneratedPrefabInfo: 0
  DrawDeactivateToggle: 1
  SaveSetFilesInPrefab: 0
  ToOptimize:
  - constructed: 1
    ToOptimizeIndex: 1
    optimizer: {fileID: 114327750956549150}
    Component: {fileID: 114070407282300220}
    Version: 0
    lockFirstLOD: 1
    editorHeader: FBasic_RotateSpinSin
    drawProperties: 0
    LODSet: {fileID: 11400000, guid: b57be60d84b4dba47924c1a21a8cc5a1, type: 2}
    sharedLODSet: {fileID: 11400000, guid: b57be60d84b4dba47924c1a21a8cc5a1, type: 2}
    uniqueLODSet: {fileID: 0}
    sOptimizer: {fileID: 114327750956549150}
    RootReference: {fileID: 11400000, guid: 0a80034ec3ba6e34abf5f78c74facca9, type: 2}
    UsingShared: 1
    nullTry: 0
  - constructed: 1
    ToOptimizeIndex: 2
    optimizer: {fileID: 114327750956549150}
    Component: {fileID: 114732679238885296}
    Version: 0
    lockFirstLOD: 1
    editorHeader: Custom Component
    drawProperties: 0
    LODSet: {fileID: 11400000, guid: 406536d0ed920a94191ca2bc30d6930f, type: 2}
    sharedLODSet: {fileID: 11400000, guid: 406536d0ed920a94191ca2bc30d6930f, type: 2}
    uniqueLODSet: {fileID: 0}
    sOptimizer: {fileID: 114327750956549150}
    RootReference: {fileID: 11400000, guid: fa50bedff972cff48b3ae922866d7235, type: 2}
    UsingShared: 1
    nullTry: 0
  - constructed: 1
    ToOptimizeIndex: 3
    optimizer: {fileID: 114327750956549150}
    Component: {fileID: 114887922373445812}
    Version: 0
    lockFirstLOD: 1
    editorHeader: FBasic_RotateSpinSin
    drawProperties: 0
    LODSet: {fileID: 11400000, guid: b57be60d84b4dba47924c1a21a8cc5a1, type: 2}
    sharedLODSet: {fileID: 11400000, guid: b57be60d84b4dba47924c1a21a8cc5a1, type: 2}
    uniqueLODSet: {fileID: 0}
    sOptimizer: {fileID: 114327750956549150}
    RootReference: {fileID: 11400000, guid: 0a80034ec3ba6e34abf5f78c74facca9, type: 2}
    UsingShared: 1
    nullTry: 0
  - constructed: 1
    ToOptimizeIndex: 4
    optimizer: {fileID: 114327750956549150}
    Component: {fileID: 114474271908296802}
    Version: 0
    lockFirstLOD: 1
    editorHeader: FBasic_RotateSpinSin
    drawProperties: 0
    LODSet: {fileID: 11400000, guid: b57be60d84b4dba47924c1a21a8cc5a1, type: 2}
    sharedLODSet: {fileID: 11400000, guid: b57be60d84b4dba47924c1a21a8cc5a1, type: 2}
    uniqueLODSet: {fileID: 0}
    sOptimizer: {fileID: 114327750956549150}
    RootReference: {fileID: 11400000, guid: 0a80034ec3ba6e34abf5f78c74facca9, type: 2}
    UsingShared: 1
    nullTry: 0
--- !u!1 &1219074139026044
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4509537545006128}
  - component: {fileID: 33784656983157130}
  - component: {fileID: 23543084113029578}
  m_Layer: 0
  m_Name: Body
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4509537545006128
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1219074139026044}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 14.700001, z: 0}
  m_LocalScale: {x: 3, y: 22, z: 3}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4316805118661950}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &33784656983157130
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1219074139026044}
  m_Mesh: {fileID: 10208, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &23543084113029578
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1219074139026044}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 60f805de395c51e4f86f073078fe564c, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 1
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &1236934819714664
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4684584567707910}
  - component: {fileID: 33468381731862744}
  - component: {fileID: 23282464710764382}
  - component: {fileID: 114887922373445812}
  m_Layer: 0
  m_Name: Cube
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4684584567707910
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1236934819714664}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0.86602545, w: 0.49999994}
  m_LocalPosition: {x: 7.3352356, y: -4.2350006, z: 0}
  m_LocalScale: {x: 1.7099999, y: 18, z: 0.29999998}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4620431284305208}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &33468381731862744
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1236934819714664}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &23282464710764382
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1236934819714664}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 60f805de395c51e4f86f073078fe564c, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 1
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!114 &114887922373445812
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1236934819714664}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ebc354317bbc5dd40b24b190775f892a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  RotationAxis: {x: 0, y: 1, z: 0}
  RotationRange: 15
  SinSpeed: 2
--- !u!1 &1374787208949942
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4620431284305208}
  - component: {fileID: 114732679238885296}
  m_Layer: 0
  m_Name: Mill
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4620431284305208
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1374787208949942}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 31.48, z: -2.0400085}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4805682158289842}
  - {fileID: 4684584567707910}
  - {fileID: 4927870263368128}
  m_Father: {fileID: 4316805118661950}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &114732679238885296
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1374787208949942}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d9395db310ef22949a1ccf8e6f5443b9, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  RotationAxis: {x: 0, y: 0, z: 1}
  RotationSpeed: 100
  UnscaledDeltaTime: 0
--- !u!1 &1518669026780318
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4927870263368128}
  - component: {fileID: 33196164395573122}
  - component: {fileID: 23475335645174510}
  - component: {fileID: 114474271908296802}
  m_Layer: 0
  m_Name: Cube
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4927870263368128
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1518669026780318}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 8.470001, z: 0}
  m_LocalScale: {x: 1.7099999, y: 18, z: 0.29999998}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4620431284305208}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &33196164395573122
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1518669026780318}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &23475335645174510
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1518669026780318}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 60f805de395c51e4f86f073078fe564c, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 1
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!114 &114474271908296802
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1518669026780318}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ebc354317bbc5dd40b24b190775f892a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  RotationAxis: {x: 0, y: 1, z: 0}
  RotationRange: 15
  SinSpeed: 2
--- !u!114 &114005113619516724
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f86a7e3f817eee848a4a21d249936138, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  settings:
    SetDisabled: 0
    _version: 1
    _Locked: 0
    cmp: {fileID: 0}
    UseShadows: 1
    MotionVectors: 1
    SkinnedQuality: 0
    shaderParam: _Transparency
    ColorParameter: 0
    targetParamValue: 1
--- !u!114 &114016620694740486
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f86a7e3f817eee848a4a21d249936138, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  settings:
    SetDisabled: 0
    _version: 1
    _Locked: 0
    cmp: {fileID: 0}
    UseShadows: 1
    MotionVectors: 1
    SkinnedQuality: 0
    shaderParam: _Transparency
    ColorParameter: 0
    targetParamValue: 1
--- !u!114 &114036467756092074
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f86a7e3f817eee848a4a21d249936138, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  settings:
    SetDisabled: 0
    _version: 1
    _Locked: 0
    cmp: {fileID: 0}
    UseShadows: 1
    MotionVectors: 1
    SkinnedQuality: 0
    shaderParam: _Transparency
    ColorParameter: 0
    targetParamValue: 1
--- !u!114 &114041192449065462
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 516eced26f003be418a4132deebb3c62, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  settings:
    SetDisabled: 0
    ver: 0
    _Locked: 0
    cmp: {fileID: 0}
    BaseLOD: 0
    Event:
      m_PersistentCalls:
        m_Calls: []
    Parameters:
    - Change: 0
      ParamID: 1042296649
      TypeID: 97526364
      ParamName: RotationSpeed
      ParamType: float
      Supported: 1
      Int: 0
      Float: 100
      Vec2: {x: 0, y: 0}
      Vec3: {x: 0, y: 0, z: 0}
      Color: {r: 0, g: 0, b: 0, a: 0}
      Bool: 0
    - Change: 0
      ParamID: -1350453358
      TypeID: 3029738
      ParamName: UnscaledDeltaTime
      ParamType: bool
      Supported: 1
      Int: 0
      Float: 0
      Vec2: {x: 0, y: 0}
      Vec3: {x: 0, y: 0, z: 0}
      Color: {r: 0, g: 0, b: 0, a: 0}
      Bool: 0
    NotSupported:
    - Change: 0
      ParamID: 1834209375
      TypeID: 2002444080
      ParamName: RotationAxis
      ParamType: Vector3
      Supported: 0
      Int: 0
      Float: 0
      Vec2: {x: 0, y: 0}
      Vec3: {x: 0, y: 0, z: 0}
      Color: {r: 0, g: 0, b: 0, a: 0}
      Bool: 0
--- !u!114 &114071046370117318
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 516eced26f003be418a4132deebb3c62, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  settings:
    SetDisabled: 0
    ver: 0
    _Locked: 0
    cmp: {fileID: 0}
    BaseLOD: 0
    Event:
      m_PersistentCalls:
        m_Calls: []
    Parameters:
    - Change: 0
      ParamID: 1040934975
      TypeID: 97526364
      ParamName: RotationRange
      ParamType: float
      Supported: 1
      Int: 0
      Float: 15
      Vec2: {x: 0, y: 0}
      Vec3: {x: 0, y: 0, z: 0}
      Color: {r: 0, g: 0, b: 0, a: 0}
      Bool: 0
    - Change: 0
      ParamID: 552271471
      TypeID: 97526364
      ParamName: SinSpeed
      ParamType: float
      Supported: 1
      Int: 0
      Float: 2
      Vec2: {x: 0, y: 0}
      Vec3: {x: 0, y: 0, z: 0}
      Color: {r: 0, g: 0, b: 0, a: 0}
      Bool: 0
    NotSupported:
    - Change: 0
      ParamID: 1834209375
      TypeID: 2002444080
      ParamName: RotationAxis
      ParamType: Vector3
      Supported: 0
      Int: 0
      Float: 0
      Vec2: {x: 0, y: 0}
      Vec3: {x: 0, y: 0, z: 0}
      Color: {r: 0, g: 0, b: 0, a: 0}
      Bool: 0
--- !u!114 &114129111783021748
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 516eced26f003be418a4132deebb3c62, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  settings:
    SetDisabled: 0
    ver: 0
    _Locked: 0
    cmp: {fileID: 0}
    BaseLOD: 0
    Event:
      m_PersistentCalls:
        m_Calls: []
    Parameters: []
    NotSupported: []
--- !u!114 &114183097966099274
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f86a7e3f817eee848a4a21d249936138, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  settings:
    SetDisabled: 0
    _version: 1
    _Locked: 0
    cmp: {fileID: 0}
    UseShadows: 1
    MotionVectors: 1
    SkinnedQuality: 0
    shaderParam: _Transparency
    ColorParameter: 0
    targetParamValue: 1
--- !u!114 &114184424734478300
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f86a7e3f817eee848a4a21d249936138, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  settings:
    SetDisabled: 0
    _version: 1
    _Locked: 0
    cmp: {fileID: 0}
    UseShadows: 1
    MotionVectors: 1
    SkinnedQuality: 0
    shaderParam: _Transparency
    ColorParameter: 0
    targetParamValue: 1
--- !u!114 &114184577646501366
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2c820a5417307d643bb2ff023144e0ea, type: 3}
  m_Name: LOD Set-PR_WindMill-Scriptable
  m_EditorClassIdentifier: 
  LevelOfDetailSets:
  - {fileID: 114184424734478300}
  - {fileID: 114873797097452142}
  - {fileID: 114271630400099100}
  - {fileID: 114198970976895912}
--- !u!114 &114190332452896432
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 516eced26f003be418a4132deebb3c62, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  settings:
    SetDisabled: 0
    ver: 0
    _Locked: 0
    cmp: {fileID: 0}
    BaseLOD: 0
    Event:
      m_PersistentCalls:
        m_Calls: []
    Parameters:
    - Change: 0
      ParamID: 1042296649
      TypeID: 97526364
      ParamName: RotationSpeed
      ParamType: float
      Supported: 1
      Int: 0
      Float: 100
      Vec2: {x: 0, y: 0}
      Vec3: {x: 0, y: 0, z: 0}
      Color: {r: 0, g: 0, b: 0, a: 0}
      Bool: 0
    - Change: 0
      ParamID: -1350453358
      TypeID: 3029738
      ParamName: UnscaledDeltaTime
      ParamType: bool
      Supported: 1
      Int: 0
      Float: 0
      Vec2: {x: 0, y: 0}
      Vec3: {x: 0, y: 0, z: 0}
      Color: {r: 0, g: 0, b: 0, a: 0}
      Bool: 0
    NotSupported:
    - Change: 0
      ParamID: 1834209375
      TypeID: 2002444080
      ParamName: RotationAxis
      ParamType: Vector3
      Supported: 0
      Int: 0
      Float: 0
      Vec2: {x: 0, y: 0}
      Vec3: {x: 0, y: 0, z: 0}
      Color: {r: 0, g: 0, b: 0, a: 0}
      Bool: 0
--- !u!114 &114198970976895912
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f86a7e3f817eee848a4a21d249936138, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  settings:
    SetDisabled: 0
    _version: 1
    _Locked: 0
    cmp: {fileID: 0}
    UseShadows: 1
    MotionVectors: 1
    SkinnedQuality: 0
    shaderParam: _Transparency
    ColorParameter: 0
    targetParamValue: 1
--- !u!114 &114218926054097024
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f86a7e3f817eee848a4a21d249936138, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  settings:
    SetDisabled: 0
    _version: 1
    _Locked: 0
    cmp: {fileID: 0}
    UseShadows: 1
    MotionVectors: 1
    SkinnedQuality: 0
    shaderParam: _Transparency
    ColorParameter: 0
    targetParamValue: 1
--- !u!114 &114271630400099100
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f86a7e3f817eee848a4a21d249936138, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  settings:
    SetDisabled: 0
    _version: 1
    _Locked: 0
    cmp: {fileID: 0}
    UseShadows: 1
    MotionVectors: 1
    SkinnedQuality: 0
    shaderParam: _Transparency
    ColorParameter: 0
    targetParamValue: 1
--- !u!114 &114279229727051770
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 516eced26f003be418a4132deebb3c62, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  settings:
    SetDisabled: 0
    ver: 0
    _Locked: 0
    cmp: {fileID: 0}
    BaseLOD: 0
    Event:
      m_PersistentCalls:
        m_Calls: []
    Parameters:
    - Change: 0
      ParamID: 1042296649
      TypeID: 97526364
      ParamName: RotationSpeed
      ParamType: float
      Supported: 1
      Int: 0
      Float: 100
      Vec2: {x: 0, y: 0}
      Vec3: {x: 0, y: 0, z: 0}
      Color: {r: 0, g: 0, b: 0, a: 0}
      Bool: 0
    - Change: 0
      ParamID: -1350453358
      TypeID: 3029738
      ParamName: UnscaledDeltaTime
      ParamType: bool
      Supported: 1
      Int: 0
      Float: 0
      Vec2: {x: 0, y: 0}
      Vec3: {x: 0, y: 0, z: 0}
      Color: {r: 0, g: 0, b: 0, a: 0}
      Bool: 0
    NotSupported:
    - Change: 0
      ParamID: 1834209375
      TypeID: 2002444080
      ParamName: RotationAxis
      ParamType: Vector3
      Supported: 0
      Int: 0
      Float: 0
      Vec2: {x: 0, y: 0}
      Vec3: {x: 0, y: 0, z: 0}
      Color: {r: 0, g: 0, b: 0, a: 0}
      Bool: 0
--- !u!114 &114327540842380722
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f86a7e3f817eee848a4a21d249936138, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  settings:
    SetDisabled: 0
    _version: 1
    _Locked: 0
    cmp: {fileID: 0}
    UseShadows: 1
    MotionVectors: 1
    SkinnedQuality: 0
    shaderParam: _Transparency
    ColorParameter: 0
    targetParamValue: 1
--- !u!114 &114335681902719698
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f86a7e3f817eee848a4a21d249936138, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  settings:
    SetDisabled: 0
    _version: 1
    _Locked: 0
    cmp: {fileID: 0}
    UseShadows: 1
    MotionVectors: 1
    SkinnedQuality: 0
    shaderParam: _Transparency
    ColorParameter: 0
    targetParamValue: 1
--- !u!114 &114363346501763350
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f86a7e3f817eee848a4a21d249936138, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  settings:
    SetDisabled: 0
    _version: 1
    _Locked: 0
    cmp: {fileID: 0}
    UseShadows: 1
    MotionVectors: 1
    SkinnedQuality: 0
    shaderParam: _Transparency
    ColorParameter: 0
    targetParamValue: 1
--- !u!114 &114428985821051658
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 516eced26f003be418a4132deebb3c62, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  settings:
    SetDisabled: 0
    ver: 0
    _Locked: 0
    cmp: {fileID: 0}
    BaseLOD: 0
    Event:
      m_PersistentCalls:
        m_Calls: []
    Parameters:
    - Change: 0
      ParamID: 1040934975
      TypeID: 97526364
      ParamName: RotationRange
      ParamType: float
      Supported: 1
      Int: 0
      Float: 15
      Vec2: {x: 0, y: 0}
      Vec3: {x: 0, y: 0, z: 0}
      Color: {r: 0, g: 0, b: 0, a: 0}
      Bool: 0
    - Change: 0
      ParamID: 552271471
      TypeID: 97526364
      ParamName: SinSpeed
      ParamType: float
      Supported: 1
      Int: 0
      Float: 2
      Vec2: {x: 0, y: 0}
      Vec3: {x: 0, y: 0, z: 0}
      Color: {r: 0, g: 0, b: 0, a: 0}
      Bool: 0
    NotSupported:
    - Change: 0
      ParamID: 1834209375
      TypeID: 2002444080
      ParamName: RotationAxis
      ParamType: Vector3
      Supported: 0
      Int: 0
      Float: 0
      Vec2: {x: 0, y: 0}
      Vec3: {x: 0, y: 0, z: 0}
      Color: {r: 0, g: 0, b: 0, a: 0}
      Bool: 0
--- !u!114 &114511004262861152
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 516eced26f003be418a4132deebb3c62, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  settings:
    SetDisabled: 0
    ver: 0
    _Locked: 0
    cmp: {fileID: 0}
    BaseLOD: 0
    Event:
      m_PersistentCalls:
        m_Calls: []
    Parameters: []
    NotSupported: []
--- !u!114 &114540024216217488
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f86a7e3f817eee848a4a21d249936138, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  settings:
    SetDisabled: 0
    _version: 1
    _Locked: 0
    cmp: {fileID: 0}
    UseShadows: 1
    MotionVectors: 1
    SkinnedQuality: 0
    shaderParam: _Transparency
    ColorParameter: 0
    targetParamValue: 1
--- !u!114 &114548421811432356
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2c820a5417307d643bb2ff023144e0ea, type: 3}
  m_Name: LOD Set-PR_WindMill-Scriptable
  m_EditorClassIdentifier: 
  LevelOfDetailSets:
  - {fileID: 114552352012497606}
  - {fileID: 114789864240649662}
  - {fileID: 114857605594221674}
  - {fileID: 114540024216217488}
--- !u!114 &114552352012497606
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f86a7e3f817eee848a4a21d249936138, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  settings:
    SetDisabled: 0
    _version: 1
    _Locked: 0
    cmp: {fileID: 0}
    UseShadows: 1
    MotionVectors: 1
    SkinnedQuality: 0
    shaderParam: _Transparency
    ColorParameter: 0
    targetParamValue: 1
--- !u!114 &114588174666747318
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f86a7e3f817eee848a4a21d249936138, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  settings:
    SetDisabled: 0
    _version: 1
    _Locked: 0
    cmp: {fileID: 0}
    UseShadows: 1
    MotionVectors: 1
    SkinnedQuality: 0
    shaderParam: _Transparency
    ColorParameter: 0
    targetParamValue: 1
--- !u!114 &114602063756857340
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 516eced26f003be418a4132deebb3c62, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  settings:
    SetDisabled: 0
    ver: 0
    _Locked: 0
    cmp: {fileID: 0}
    BaseLOD: 0
    Event:
      m_PersistentCalls:
        m_Calls: []
    Parameters: []
    NotSupported: []
--- !u!114 &114606144139415730
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f86a7e3f817eee848a4a21d249936138, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  settings:
    SetDisabled: 0
    _version: 1
    _Locked: 0
    cmp: {fileID: 0}
    UseShadows: 1
    MotionVectors: 1
    SkinnedQuality: 0
    shaderParam: _Transparency
    ColorParameter: 0
    targetParamValue: 1
--- !u!114 &114636885385479612
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 516eced26f003be418a4132deebb3c62, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  settings:
    SetDisabled: 0
    ver: 0
    _Locked: 0
    cmp: {fileID: 0}
    BaseLOD: 0
    Event:
      m_PersistentCalls:
        m_Calls: []
    Parameters:
    - Change: 0
      ParamID: 1042296649
      TypeID: 97526364
      ParamName: RotationSpeed
      ParamType: float
      Supported: 1
      Int: 0
      Float: 100
      Vec2: {x: 0, y: 0}
      Vec3: {x: 0, y: 0, z: 0}
      Color: {r: 0, g: 0, b: 0, a: 0}
      Bool: 0
    - Change: 0
      ParamID: -1350453358
      TypeID: 3029738
      ParamName: UnscaledDeltaTime
      ParamType: bool
      Supported: 1
      Int: 0
      Float: 0
      Vec2: {x: 0, y: 0}
      Vec3: {x: 0, y: 0, z: 0}
      Color: {r: 0, g: 0, b: 0, a: 0}
      Bool: 0
    NotSupported:
    - Change: 0
      ParamID: 1834209375
      TypeID: 2002444080
      ParamName: RotationAxis
      ParamType: Vector3
      Supported: 0
      Int: 0
      Float: 0
      Vec2: {x: 0, y: 0}
      Vec3: {x: 0, y: 0, z: 0}
      Color: {r: 0, g: 0, b: 0, a: 0}
      Bool: 0
--- !u!114 &114680565562938962
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2c820a5417307d643bb2ff023144e0ea, type: 3}
  m_Name: LOD Set-PR_WindMill-Scriptable
  m_EditorClassIdentifier: 
  LevelOfDetailSets:
  - {fileID: 114129111783021748}
  - {fileID: 114511004262861152}
  - {fileID: 114602063756857340}
  - {fileID: 114999285423043686}
--- !u!114 &114688378484873854
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2c820a5417307d643bb2ff023144e0ea, type: 3}
  m_Name: LOD Set-PR_WindMill-Scriptable
  m_EditorClassIdentifier: 
  LevelOfDetailSets:
  - {fileID: 114218926054097024}
  - {fileID: 114036467756092074}
  - {fileID: 114899327761749598}
  - {fileID: 114183097966099274}
--- !u!114 &114727887567357132
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2c820a5417307d643bb2ff023144e0ea, type: 3}
  m_Name: LOD Set-PR_WindMill-Scriptable
  m_EditorClassIdentifier: 
  LevelOfDetailSets:
  - {fileID: 114016620694740486}
  - {fileID: 114363346501763350}
  - {fileID: 114005113619516724}
  - {fileID: 114335681902719698}
--- !u!114 &114789801853858416
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2c820a5417307d643bb2ff023144e0ea, type: 3}
  m_Name: LOD Set-PR_WindMill-Scriptable
  m_EditorClassIdentifier: 
  LevelOfDetailSets:
  - {fileID: 114190332452896432}
  - {fileID: 114041192449065462}
  - {fileID: 114279229727051770}
--- !u!114 &114789864240649662
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f86a7e3f817eee848a4a21d249936138, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  settings:
    SetDisabled: 0
    _version: 1
    _Locked: 0
    cmp: {fileID: 0}
    UseShadows: 1
    MotionVectors: 1
    SkinnedQuality: 0
    shaderParam: _Transparency
    ColorParameter: 0
    targetParamValue: 1
--- !u!114 &114801528592104914
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2c820a5417307d643bb2ff023144e0ea, type: 3}
  m_Name: LOD Set-PR_WindMill-Scriptable
  m_EditorClassIdentifier: 
  LevelOfDetailSets:
  - {fileID: 114998175066791204}
  - {fileID: 114428985821051658}
  - {fileID: 114071046370117318}
--- !u!114 &114819052755221854
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 516eced26f003be418a4132deebb3c62, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  settings:
    SetDisabled: 0
    ver: 0
    _Locked: 0
    cmp: {fileID: 0}
    BaseLOD: 0
    Event:
      m_PersistentCalls:
        m_Calls: []
    Parameters:
    - Change: 0
      ParamID: 1040934975
      TypeID: 97526364
      ParamName: RotationRange
      ParamType: float
      Supported: 1
      Int: 0
      Float: 15
      Vec2: {x: 0, y: 0}
      Vec3: {x: 0, y: 0, z: 0}
      Color: {r: 0, g: 0, b: 0, a: 0}
      Bool: 0
    - Change: 0
      ParamID: 552271471
      TypeID: 97526364
      ParamName: SinSpeed
      ParamType: float
      Supported: 1
      Int: 0
      Float: 2
      Vec2: {x: 0, y: 0}
      Vec3: {x: 0, y: 0, z: 0}
      Color: {r: 0, g: 0, b: 0, a: 0}
      Bool: 0
    NotSupported:
    - Change: 0
      ParamID: 1834209375
      TypeID: 2002444080
      ParamName: RotationAxis
      ParamType: Vector3
      Supported: 0
      Int: 0
      Float: 0
      Vec2: {x: 0, y: 0}
      Vec3: {x: 0, y: 0, z: 0}
      Color: {r: 0, g: 0, b: 0, a: 0}
      Bool: 0
--- !u!114 &114834741947427452
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2c820a5417307d643bb2ff023144e0ea, type: 3}
  m_Name: LOD Set-PR_WindMill-Scriptable
  m_EditorClassIdentifier: 
  LevelOfDetailSets:
  - {fileID: 114588174666747318}
  - {fileID: 114606144139415730}
  - {fileID: 114935360922512948}
  - {fileID: 114327540842380722}
--- !u!114 &114857605594221674
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f86a7e3f817eee848a4a21d249936138, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  settings:
    SetDisabled: 0
    _version: 1
    _Locked: 0
    cmp: {fileID: 0}
    UseShadows: 1
    MotionVectors: 1
    SkinnedQuality: 0
    shaderParam: _Transparency
    ColorParameter: 0
    targetParamValue: 1
--- !u!114 &114873797097452142
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f86a7e3f817eee848a4a21d249936138, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  settings:
    SetDisabled: 0
    _version: 1
    _Locked: 0
    cmp: {fileID: 0}
    UseShadows: 1
    MotionVectors: 1
    SkinnedQuality: 0
    shaderParam: _Transparency
    ColorParameter: 0
    targetParamValue: 1
--- !u!114 &114899327761749598
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f86a7e3f817eee848a4a21d249936138, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  settings:
    SetDisabled: 0
    _version: 1
    _Locked: 0
    cmp: {fileID: 0}
    UseShadows: 1
    MotionVectors: 1
    SkinnedQuality: 0
    shaderParam: _Transparency
    ColorParameter: 0
    targetParamValue: 1
--- !u!114 &114935360922512948
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f86a7e3f817eee848a4a21d249936138, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  settings:
    SetDisabled: 0
    _version: 1
    _Locked: 0
    cmp: {fileID: 0}
    UseShadows: 1
    MotionVectors: 1
    SkinnedQuality: 0
    shaderParam: _Transparency
    ColorParameter: 0
    targetParamValue: 1
--- !u!114 &114998175066791204
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 516eced26f003be418a4132deebb3c62, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  settings:
    SetDisabled: 0
    ver: 0
    _Locked: 0
    cmp: {fileID: 0}
    BaseLOD: 0
    Event:
      m_PersistentCalls:
        m_Calls: []
    Parameters:
    - Change: 0
      ParamID: 1040934975
      TypeID: 97526364
      ParamName: RotationRange
      ParamType: float
      Supported: 1
      Int: 0
      Float: 15
      Vec2: {x: 0, y: 0}
      Vec3: {x: 0, y: 0, z: 0}
      Color: {r: 0, g: 0, b: 0, a: 0}
      Bool: 0
    - Change: 0
      ParamID: 552271471
      TypeID: 97526364
      ParamName: SinSpeed
      ParamType: float
      Supported: 1
      Int: 0
      Float: 2
      Vec2: {x: 0, y: 0}
      Vec3: {x: 0, y: 0, z: 0}
      Color: {r: 0, g: 0, b: 0, a: 0}
      Bool: 0
    NotSupported:
    - Change: 0
      ParamID: 1834209375
      TypeID: 2002444080
      ParamName: RotationAxis
      ParamType: Vector3
      Supported: 0
      Int: 0
      Float: 0
      Vec2: {x: 0, y: 0}
      Vec3: {x: 0, y: 0, z: 0}
      Color: {r: 0, g: 0, b: 0, a: 0}
      Bool: 0
--- !u!114 &114999285423043686
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 516eced26f003be418a4132deebb3c62, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  settings:
    SetDisabled: 0
    ver: 0
    _Locked: 0
    cmp: {fileID: 0}
    BaseLOD: 0
    Event:
      m_PersistentCalls:
        m_Calls: []
    Parameters: []
    NotSupported: []
