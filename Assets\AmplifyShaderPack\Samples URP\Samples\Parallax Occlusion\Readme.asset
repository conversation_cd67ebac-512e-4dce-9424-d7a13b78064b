%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e78934177bebe0545ab566bb787b0282, type: 3}
  m_Name: Readme
  m_EditorClassIdentifier: 
  Icon: {fileID: 2800000, guid: 6493366649dfaa744b930dda17fb8f7a, type: 3}
  Title: Parallax Occlusion Mapping
  Description:
    Heading: 
    Text: Parallax Occlusion Mapping with Curvature Clip.
    LinkText: 
    Url: 
  PropertiesHeader:
    Heading: Properties
    Text: 
    LinkText: 
    Url: 
  Properties:
  - Heading: Albedo
    Text: 'Albedotexture to be applied to surface in conjunction with the parallax
      effect. '
    LinkText: 
    Url: 
  - Heading: Normal
    Text: 'Normals texture to be applied to surface in conjunction with the parallax
      effect. '
    LinkText: 
    Url: 
  - Heading: Normal Scale
    Text: ' Normal intensity value.  '
    LinkText: 
    Url: 
  - Heading: Metallic
    Text: 'Metallic texture to be applied to surface in conjunction with the parallax
      effect. '
    LinkText: 
    Url: 
  - Heading: Roughness
    Text: 'Roughness texture to be applied to surface in conjunction with the parallax
      effect. '
    LinkText: 
    Url: 
  - Heading: Rough Scale
    Text: 'Roughness value.  '
    LinkText: 
    Url: 
  - Heading: Occlusion
    Text: 'Occlusion texture to be applied to surface in conjunction with the parallax
      effect. '
    LinkText: 
    Url: 
  - Heading: HeightMap
    Text: 'Height texture used to calculate the parallax effect.  '
    LinkText: 
    Url: 
  - Heading: Scale
    Text: 'Scale of the parallax effect.  '
    LinkText: 
    Url: 
  - Heading: Curvature U
    Text: Curvature U clip value.
    LinkText: 
    Url: 
  - Heading: Curvature V
    Text: Curvature V clip value.
    LinkText: 
    Url: 
  - Heading: Curvature Bias
    Text: Curvature clip bias value.
    LinkText: 
    Url: 
  AdditionalProperties: []
  AdditionalScripts: []
  LoadedLayout: 0
  RPType: 3
