%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: Material Sample Fabirc
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor v1.9.4.2\n// Available at the
    Unity Asset Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=19402\nNode;AmplifyShaderEditor.CommentaryNode;463;-2555.513,261.7889;Inherit;False;975.2742;404.448;TwoSidedSign;4;462;455;454;443;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;127;-255.6981,-1210.087;Inherit;False;1390.685;883.4981;ALPHA
    CLIPPING;22;118;117;125;8;71;126;122;116;121;156;120;160;110;115;155;124;159;100;119;200;199;259;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;462;-2505.513,311.7889;Inherit;False;354.6746;321.1274;NdotV;3;460;459;461;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;259;234.637,-603.0857;Inherit;False;857.2662;240.2057;Alpha
    Clip Threshold Shadow;4;72;128;129;130;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;400;-3265.155,685.9261;Inherit;False;1701.055;1399.059;UV;50;452;451;450;449;448;447;446;445;444;441;440;439;438;437;436;435;434;433;432;431;430;429;428;427;426;425;424;423;422;421;420;419;418;417;416;415;414;413;412;411;410;409;408;407;406;405;404;403;402;401;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;199;843.2662,-760.8951;Inherit;False;240;123;HDRP
    Hidden _Cutoff;;0,0,0,1;_Cutoff must be set on both Master Node Properties Additional
    Options and Material Inspector;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;200;631.0419,-991.781;Inherit;False;351.7355;100;Alpha;;0,0,0,1;If
    Alpha value  is lower than Alpha Clip Threshold then Alpha Clip happens;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;257;1021.076,1933.126;Inherit;False;178.6;100;;;0,0,0,1;Saturate
    Occclusion to Prevent Nan in Unity;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;258;-887.9719,1228.825;Inherit;False;271.6077;121.433;SS
    Sampler States Normal Map;;0,0,0,1;Keep Normal SS separated to prevent possible
    GPU crossing in fragment stages of the Mip stream;0;0\nNode;AmplifyShaderEditor.WireNode;119;155.1598,-1124.665;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitchByPipeline;100;626.2991,-1131.881;Inherit;False;4;0;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;159;514.8707,-1112.55;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;124;515.747,-1111.224;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;155;212.6626,-1124.754;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SaturateNode;115;466.0771,-1007.173;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.ClipNode;110;278.9474,-1007.022;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;160;222.7162,-997.432;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;120;172.7353,-1109.038;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;156;226.2449,-1103.793;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;121;179.1251,-979.2283;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;116;92.10937,-807.7035;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitchByPipeline;122;630.4697,-828.0585;Inherit;False;4;0;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitchByPipeline;130;576.6103,-541.8799;Inherit;False;4;0;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;117;-192.3051,-782.1603;Half;False;Property;_AlphaCutoffBias;Alpha
    Cutoff Bias;1;1;[Header];Create;False;1;ALPHA CLIPPING;0;0;False;0;False;0.45;0.9;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;129;284.637,-478.5873;Half;False;Property;_AlphaCutoffShadow;Alpha
    Cutoff Shadow;2;1;[HideInInspector];Create;False;0;0;0;False;0;False;1;0.851;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;128;285.978,-553.0857;Half;False;Property;_AlphaCutoffShadowBias;Alpha
    Cutoff Shadow;3;1;[Header];Create;False;1;SHADOW;0;0;False;0;False;0.5;0.9;0.01;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;118;-168.3186,-1168.883;Inherit;False;107;MapBaseColorAlpha;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.StickyNoteNode;269;303.6679,2886.327;Inherit;False;270.9963;145.2141;Enable
    Specular IOR;;0,0,0,1;;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;272;-1411.474,252.4198;Inherit;False;175.4583;100;BaseColor
    Map;;0,0,0,1;_MainTex;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;273;-1408.18,1049.929;Inherit;False;173.303;100;Normal
    Map;;0,0,0,1;_BumpMap;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;274;-1403.912,1895.149;Inherit;False;170.1927;100;Occlusion
    Map;;0,0,0,1;_OcclusionMap;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;275;-1408.682,2497.014;Inherit;False;166.5214;100;Smoothness
    Map;;0,0,0,1;_SmoothnessMap;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;276;-1406.892,3202.967;Inherit;False;176.6407;100;Specular
    Map;;0,0,0,1;_SpecularMap;0;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;383;-409.8776,638.4867;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.StickyNoteNode;401;-2657.471,1263.291;Inherit;False;411.6606;213.611;UV
    Channel Selection;;0,0,0,1;This node allows the user to select the UV channel
    with a Vector 4 by typing \"1\" in the column that corresponds to the channel
    they want and 0 in the others.$1 in X will use UV channel 0$1 in Y will use UV
    channel 1$1 in Z will use UV channel 2$1 in W will use UV channel 3;0;0\nNode;AmplifyShaderEditor.UnpackScaleNormalNode;475;-249.3635,1415.504;Inherit;False;Tangent;2;0;FLOAT4;0,0,0,0;False;1;FLOAT;1;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.UnpackScaleNormalNode;98;-254.1219,1047.047;Inherit;False;Tangent;2;0;FLOAT4;0,0,0,0;False;1;FLOAT;1;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.StickyNoteNode;477;144,1344;Inherit;False;268.1831;100;to
    avoid nan after normalizing;;0,0,0,1;mixedNormal.z += 1e-5f@;0;0\nNode;AmplifyShaderEditor.RangedFloatNode;484;-188.4713,1187.684;Inherit;False;Constant;_Float2;Float
    2;45;0;Create;True;0;0;0;False;0;False;0.5;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.LayeredBlendNode;482;5.407593,1141.478;Inherit;False;6;0;FLOAT;0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.BreakToComponentsNode;481;211.4561,1138.676;Inherit;False;FLOAT3;1;0;FLOAT3;0,0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.SimpleAddOpNode;478;343.5911,1234.615;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.DynamicAppendNode;479;468.5101,1139.895;Inherit;False;FLOAT3;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;485;689.4128,1117.066;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;488;634.1236,1120.571;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;487;620.1236,1081.571;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.ColorNode;497;-903.1747,-201.7275;Half;False;Property;_BaseColor1;Base
    Color Fornt Face;4;0;Create;False;0;0;0;False;0;False;0.7294118,0.7294118,0.7294118,0;0.9150943,0.9150943,0.9150943,0;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SwizzleNode;498;-635.5206,-35.41737;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SwizzleNode;499;-641.0875,-202.0934;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;501;-339.2613,-29.24257;Inherit;False;3;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;500;-334.1654,-196.0545;Inherit;False;3;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SwizzleNode;73;-519.8774,246.0809;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;505;-117.4501,-52.14035;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;506;-340.4573,81.82884;Inherit;False;443;TwoSidedSign;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.LerpOp;382;19.68076,617.6167;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RangedFloatNode;380;-276.1671,713.3023;Half;False;Property;_FuzzMaskStrength;Fuzz
    Mask Strength;40;0;Create;False;0;0;0;False;0;False;0.5;0.118;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;381;-147.2882,582.7944;Inherit;False;Constant;_Float1;Float
    0;32;0;Create;True;0;0;0;False;0;False;0;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;507;171.859,614.4483;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;508;171.8591,125.8096;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;386;223.3695,47.65528;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;509;160.9091,-17.90765;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;510;177.3338,5.360863;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SaturateNode;387;353.4901,48.3768;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;388;536.5784,26.13531;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;512;496.2493,10.83581;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;511;483.9307,-16.53893;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SamplerNode;24;-904.4761,1892.093;Inherit;True;Property;_TextureSample16;Texture
    Sample 16;36;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SamplerNode;470;-906.1979,1412.762;Inherit;True;Property;_TextureSample0;Texture
    Sample 0;44;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SamplerNode;22;-907.7601,1043.961;Inherit;True;Property;_TextureSample14;Texture
    Sample 14;34;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SamplerNode;391;-907.3288,632.2528;Inherit;True;Property;_TextureSample2;Texture
    Sample 2;37;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SamplerNode;21;-907.6309,247.7912;Inherit;True;Property;_TextureSample13;Texture
    Sample 13;33;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.GetLocalVarNode;464;-1168.169,171.1684;Inherit;False;420;UVBase;1;0;OBJECT;;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.StickyNoteNode;515;-1411.1,636.0884;Inherit;False;169.066;102.1185;Fuzz
    Mask Map;;0,0,0,1;_FuzzMaskMap;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;516;-1407.81,1414.209;Inherit;False;166.9476;100;Thread
    Normal Map;;0,0,0,1;_ThreadNormalMap;0;0\nNode;AmplifyShaderEditor.SwizzleNode;27;-570.1936,1891.644;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.StickyNoteNode;518;-1414.451,3759.386;Inherit;False;180.4856;100;Thread
    Mask Map;;0,0,0,1;_ThreadMaskMap;0;0\nNode;AmplifyShaderEditor.TexturePropertyNode;468;-1222.719,1413.278;Inherit;True;Property;_ThreadNormalMap;Thread
    Normal Map;26;2;[Normal];[SingleLineTexture];Create;False;0;0;0;False;0;False;None;None;False;bump;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.TexturePropertyNode;379;-1227.285,632.6854;Inherit;True;Property;_FuzzMaskMap;Fuzz
    Mask Map;38;1;[SingleLineTexture];Create;False;0;0;0;False;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.ColorNode;502;-899.5999,-35.16548;Half;False;Property;_BaseColorBackFace;Base
    Color Back Face;6;0;Create;False;0;0;0;False;0;False;0.7294118,0.7294118,0.7294118,0;0.9150943,0.9150943,0.9150943,0;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.RangedFloatNode;504;-641.0469,-121.9677;Half;False;Property;_BaseBrightnessFrontFace;Brightness
    Front Face;5;0;Create;False;1;;0;0;False;0;False;1;1.12;0;2;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;503;-646.506,47.5093;Half;False;Property;_BaseBrightnessBackFace;Brightness
    Back Face;7;0;Create;False;1;;0;0;False;0;False;1;1.12;0;2;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SwizzleNode;28;-572.6599,2495.4;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;526;555.622,2543.233;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;531;507.8925,2541.906;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;530;497.8925,2507.906;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;533;344.8925,2569.906;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;532;327.8925,2509.906;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;175;-100.5838,2472.246;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;176;-104.922,1870.768;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;525;389.3467,2564.344;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitch;486;856.0746,1038.573;Inherit;False;Enable
    Thread Map;True;1;2;1;In 0;In 1;Object;-1;10;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitch;527;766.4846,2472.037;Inherit;False;Option;True;1;2;1;In
    0;In 1;Instance;486;10;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;542;564.0853,1931.798;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;548;504.6184,1928.973;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;547;484.9455,1895.528;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;541;392.4667,1957.9;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;550;333.4634,1954.548;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;549;319.6923,1895.529;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.OneMinusNode;271;-267.9722,1764.197;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;138;-550.7715,1760.162;Half;False;Property;_OcclusionStrengthAO;Occlusion
    Strength;17;0;Create;False;0;0;0;False;0;False;0;0;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SaturateNode;528;1019.08,2474.9;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.TexCoordVertexDataNode;402;-3192.92,735.9261;Inherit;False;0;2;0;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.TexCoordVertexDataNode;403;-3189.978,918.7309;Inherit;False;1;2;0;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.TexCoordVertexDataNode;404;-3193.762,1099.084;Inherit;False;2;2;0;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.TexCoordVertexDataNode;405;-3189.684,1282.516;Inherit;False;3;2;0;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;406;-2951.868,1457.228;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;407;-2949.48,1547.417;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;408;-2951.265,1643.627;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;409;-2953.579,1735.772;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;410;-2799.425,1495.742;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;411;-2801.49,1683.846;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;412;-2660.043,1573.36;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;413;-2958.897,1282.383;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;414;-2962.197,1101.592;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;415;-2973.409,919.0909;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;416;-2970.758,738.043;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;417;-2820.523,896.1899;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;418;-2815.628,1096.422;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;419;-2707.813,992.1566;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;421;-2160.75,986.3009;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;423;-2314.5,987.8767;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SwizzleNode;424;-2314.935,1091.808;Inherit;False;FLOAT2;2;3;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SwizzleNode;425;-2474.224,1056.036;Inherit;False;FLOAT2;0;1;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;426;-2150.774,1567.678;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.VertexToFragmentNode;427;-2023.315,1566.724;Inherit;False;False;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;428;-2304.524,1569.253;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SwizzleNode;429;-2304.959,1673.185;Inherit;False;FLOAT2;2;3;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SwizzleNode;430;-2464.247,1637.413;Inherit;False;FLOAT2;0;1;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;431;-2143.852,1769.225;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.VertexToFragmentNode;432;-2016.394,1768.272;Inherit;False;False;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SwizzleNode;433;-2298.037,1874.732;Inherit;False;FLOAT2;2;3;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SwizzleNode;434;-2457.325,1838.961;Inherit;False;FLOAT2;0;1;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;435;-2359.97,1599.081;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;436;-2344.566,1627.962;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;437;-2295.976,1769.176;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WorldNormalVector;461;-2491.213,347.5086;Inherit;False;True;1;0;FLOAT3;0,0,1;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.ViewDirInputsCoordNode;459;-2467.545,482.2369;Inherit;False;World;True;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.DotProductOpNode;460;-2274.277,349.5681;Inherit;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SignOpNode;455;-2119.782,348.6077;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.TFHCRemapNode;454;-1987.14,349.2567;Inherit;False;5;0;FLOAT;0;False;1;FLOAT;-1;False;2;FLOAT;1;False;3;FLOAT;1;False;4;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;443;-1804.238,343.5852;Inherit;False;TwoSidedSign;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.VertexToFragmentNode;422;-2034.41,985.3477;Inherit;False;False;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.RangedFloatNode;452;-3209.201,1760.467;Half;False;Property;_ThreadMaskUVAffectchannel3;Affect
    UV channel 3;31;1;[ToggleUI];Create;False;0;0;0;False;0;False;0;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;451;-3208.54,1665.255;Half;False;Property;_ThreadMaskUVAffectchannel2;Affect
    UV channel 2;30;1;[ToggleUI];Create;False;0;0;0;False;0;False;0;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;450;-3211.972,1564.442;Half;False;Property;_ThreadMaskUVAffectchannel1;Affect
    UV channel 1;29;1;[ToggleUI];Create;False;0;0;0;False;0;False;0;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;449;-3215.155,1478.481;Half;False;Property;_ThreadMaskUVAffectchannel0;Affect
    UV channel 0;28;1;[ToggleUI];Create;False;0;0;0;False;0;False;1;1;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;448;-3212.837,1394.388;Half;False;Property;_BaseAffectUVchannel3;Affect
    UV channel 3;12;1;[ToggleUI];Create;False;0;0;0;False;0;False;0;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;447;-3208.812,1211.956;Half;False;Property;_BaseAffectUVchannel2;Affect
    UV channel 2;11;1;[ToggleUI];Create;False;0;0;0;False;0;False;0;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;446;-3206.219,1029.956;Half;False;Property;_BaseAffectUVchannel1;Affect
    UV channel 1;10;1;[ToggleUI];Create;False;0;0;0;False;0;False;0;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;445;-3207.924,846.525;Half;False;Property;_BaseAffectUVchannel0;Affect
    UV channel 0;9;1;[ToggleUI];Create;False;0;0;0;False;0;False;1;1;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.Vector4Node;438;-2659.645,1674.703;Half;False;Property;_FuzzMaskUV;Fuzz
    Mask UV;39;0;Create;False;0;0;0;False;0;False;4,4,0,0;0,0,0,0;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.Vector4Node;439;-2661.735,1874.572;Half;False;Property;_ThreadMaskUV;Thread
    Mask UV;32;0;Create;False;0;0;0;False;0;False;1,1,0,0;0,0,0,0;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.Vector4Node;444;-2667.201,1098.523;Half;False;Property;_BaseMainUV;Base
    Main UV;13;0;Create;False;0;0;0;False;0;False;1,1,0,0;0,0,0,0;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SwizzleNode;163;-532.0075,3193.146;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;266;-346.8616,3047.259;Inherit;False;2;0;FLOAT;1;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;267;-345.329,3135.784;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleDivideOpNode;265;-205.0263,3114.916;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;264;-56.63622,3103.457;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;261;42.3438,2999.326;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;262;174.6762,3078.408;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.ColorNode;201;-557.1783,2923.94;Inherit;False;Property;_SpecularColor;Specular
    Color;21;0;Create;True;0;0;0;False;0;False;0,0,0,0;1,0.661858,0.1650943,0;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;165;587.0509,3170.657;Inherit;False;3;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SwizzleNode;202;-144.1411,2924.038;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitch;263;327.4218,2921.136;Inherit;False;Enable
    Specular IOR;True;1;2;0;In 0;In 1;Object;-1;10;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SamplerNode;161;-910.3088,3195.591;Inherit;True;Property;_TextureSample19;Texture
    Sample 17;37;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.GetLocalVarNode;467;-1166.527,3124.125;Inherit;False;420;UVBase;1;0;OBJECT;;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SamplerNode;493;-907.9709,3754.78;Inherit;True;Property;_TextureSample1;Texture
    Sample 1;40;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.TexturePropertyNode;490;-1226.215,3757.158;Inherit;True;Property;_ThreadMaskMap;Thread
    Mask Map;33;1;[SingleLineTexture];Create;False;0;0;0;False;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.SamplerNode;25;-912.8251,2496.123;Inherit;True;Property;_TextureSample17;Texture
    Sample 17;37;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.GetLocalVarNode;514;-1174.014,2421.049;Inherit;False;420;UVBase;1;0;OBJECT;;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.TexturePropertyNode;41;-1228.255,2495.67;Inherit;True;Property;_SmoothnessMap;Smoothness
    Map;18;1;[SingleLineTexture];Create;False;0;0;0;False;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.GetLocalVarNode;465;-1169.198,560.1982;Inherit;False;441;UVFuzz;1;0;OBJECT;;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.TexturePropertyNode;17;-1227.247,246.1845;Inherit;True;Property;_MainTex;BaseColor
    Map;8;1;[SingleLineTexture];Create;False;1;;0;0;False;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.ColorNode;385;-832.972,461.0595;Half;False;Property;_FuzzMaskColor;Fuzz
    Mask Color;37;1;[HDR];Create;False;1;;0;0;False;1;Space(10);False;0.7294118,0.7294118,0.7294118,0;0.9150943,0.9150943,0.9150943,0;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SwizzleNode;384;-585.1086,461.0136;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;107;-592.3462,339.195;Inherit;False;MapBaseColorAlpha;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;535;575.6785,147.0497;Inherit;False;EnableFuzzMask;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;389;693.9838,-48.89154;Inherit;False;Enable
    Fuzz Mask;True;1;2;2;In 0;In 1;Object;-1;10;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitch;543;775.4609,1857.491;Inherit;False;Option;False;1;2;1;In
    0;In 1;Instance;486;10;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RangedFloatNode;97;-547.9122,1121.288;Half;False;Property;_NormalStrength;Normal
    Strength;15;0;Create;True;1;;0;0;False;0;False;1;1;0;2;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;476;-545.1129,1486.98;Half;False;Property;_ThreadNormalStrength;Thread
    Normal Strength;27;0;Create;False;0;0;0;False;0;False;0.5;0;0;2;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;141;263.2331,3259.115;Half;False;Property;_SpecularStrength;Specular
    Strength;22;0;Create;False;0;0;0;False;0;False;0.04;0;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;260;-210.6698,3017.666;Inherit;False;Property;_SpecularColorWeight;Specular
    Color Weight;23;0;Create;True;0;0;0;False;0;False;1;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;469;-1176.055,1334.547;Inherit;False;440;UVThread;1;0;OBJECT;;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;420;-1816.122,980.7561;Inherit;False;UVBase;-1;True;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;441;-1803.533,1561.235;Inherit;False;UVFuzz;-1;True;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;440;-1792.1,1761.019;Inherit;False;UVThread;-1;True;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.TexturePropertyNode;42;-1223.871,1891.455;Inherit;True;Property;_OcclusionMap;Occlusion
    Map;16;1;[SingleLineTexture];Create;False;1;;0;0;False;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.GetLocalVarNode;513;-1166.921,1813.44;Inherit;False;420;UVBase;1;0;OBJECT;;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.GetLocalVarNode;546;313.8061,2066.917;Inherit;False;534;EnableThreadMap;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;494;-1166.223,3679.187;Inherit;False;420;UVBase;1;0;OBJECT;;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.LerpOp;560;77.7495,2590.938;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RangedFloatNode;523;-254.5434,2608.748;Half;False;Property;_ThreadMaskSmoothnessStrength;Thread
    Smoothness Strength;35;0;Create;False;0;0;0;False;0;False;0;0.5544218;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.LerpOp;561;116.0237,1981.969;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.OneMinusNode;539;-80.47659,2001.099;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;538;-369.2026,1995.343;Half;False;Property;_ThreadMaskOcclusionStrength;Thread
    Occlusion Strength;34;0;Create;False;0;0;0;False;0;False;0;0.5544218;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;93;-385.1746,2435.17;Half;False;Property;_SmoothnessStrength;Smoothness
    Strength;19;0;Create;False;0;0;0;False;0;False;0;0;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;390;333.0667,147.6921;Half;False;Property;_FuzzMaskEnable;Enable
    Fuzz Mask;36;2;[Header];[ToggleUI];Create;False;1;FUZZ MASK;0;0;False;1;Space(15);False;0;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.TexturePropertyNode;164;-1226.476,3195.696;Inherit;True;Property;_SpecularMap;Specular
    Map;20;2;[Header];[SingleLineTexture];Create;False;1;SPECULAR;0;0;False;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.GetLocalVarNode;536;325.7835,2678.374;Inherit;False;534;EnableThreadMap;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;524;-188.6859,2688.947;Inherit;False;491;MASK_G_Thread;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.TexturePropertyNode;20;-1221.175,1046.563;Inherit;True;Property;_BumpMap;Normal
    Map;14;2;[Normal];[SingleLineTexture];Create;False;0;0;0;False;0;False;None;None;False;bump;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.GetLocalVarNode;466;-1160.19,971.5087;Inherit;False;420;UVBase;1;0;OBJECT;;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;564;-977.3323,1141.521;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.RangedFloatNode;268;-592,3104;Inherit;False;Property;_SpecularColorIOR;Specular
    Color IOR;24;0;Create;True;0;0;0;False;0;False;0;1.51;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;565;992,32;Inherit;False;BaseColor;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;566;672.7996,2581.511;Inherit;False;BaseColor;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;569;640,3088;Inherit;False;565;BaseColor;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;570;848,3152;Inherit;False;3;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SaturateNode;144;1040,1856;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;571;1280,1776;Inherit;False;Occlusion;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;572;640,3296;Inherit;False;571;Occlusion;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;492;-555.7895,3778.402;Inherit;False;MASK_R_Thread;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;491;-558.0307,3855.142;Inherit;False;MASK_G_Thread;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;537;-144,2096;Inherit;False;492;MASK_R_Thread;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;480;144,1264;Float;False;Constant;_Float7;Float
    7;9;0;Create;True;0;0;0;False;0;False;0.001;0.001;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;489;464,1280;Half;False;Property;_ThreadMaskEnable;Enable
    Thread Map;25;2;[Header];[ToggleUI];Create;False;1;THREAD MASK;0;0;False;1;Space(15);False;0;1;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;534;704,1280;Inherit;False;EnableThreadMap;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;126;432,-832;Inherit;False;Constant;_Float0;Float
    0;13;0;Create;True;0;0;0;False;0;False;0.45;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;125;464,-720;Inherit;False;Property;_Cutoff;Cutoff;0;1;[HideInInspector];Create;False;1;;0;0;False;0;False;0;0.9;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;71;845.1862,-828.0752;Inherit;False;False;-1;Alpha
    Clip Threshold;6;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;8;845.6235,-1132.196;Inherit;False;False;-1;Alpha;5;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;72;811.4559,-542.3411;Inherit;False;False;-1;Alpha
    Clip Threshold Shadow;7;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;6;1151.61,1042.478;Inherit;False;False;-1;Normal;1;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionOutput;2;1284.528,2472.416;Inherit;False;False;-1;Smoothness;3;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionOutput;1;992,-48;Inherit;False;True;-1;BaseColor;0;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionOutput;5;1024,3152;Inherit;False;False;-1;Specular;2;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionOutput;4;1280,1856;Inherit;False;False;-1;Occlusion;4;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nWireConnection;119;0;118;0\nWireConnection;100;0;115;0\nWireConnection;100;3;115;0\nWireConnection;100;1;159;0\nWireConnection;100;2;124;0\nWireConnection;159;0;118;0\nWireConnection;124;0;118;0\nWireConnection;155;0;118;0\nWireConnection;115;0;110;0\nWireConnection;110;0;160;0\nWireConnection;110;1;121;0\nWireConnection;110;2;116;0\nWireConnection;160;0;156;0\nWireConnection;120;0;119;0\nWireConnection;156;0;155;0\nWireConnection;121;0;120;0\nWireConnection;116;0;117;0\nWireConnection;122;0;126;0\nWireConnection;122;3;126;0\nWireConnection;122;1;117;0\nWireConnection;122;2;125;0\nWireConnection;130;0;128;0\nWireConnection;130;3;128;0\nWireConnection;130;1;128;0\nWireConnection;130;2;129;0\nWireConnection;383;0;384;0\nWireConnection;383;1;391;1\nWireConnection;475;0;470;0\nWireConnection;475;1;476;0\nWireConnection;98;0;22;0\nWireConnection;98;1;97;0\nWireConnection;482;0;484;0\nWireConnection;482;1;98;0\nWireConnection;482;2;475;0\nWireConnection;481;0;482;0\nWireConnection;478;0;481;2\nWireConnection;478;1;480;0\nWireConnection;479;0;481;0\nWireConnection;479;1;481;1\nWireConnection;479;2;478;0\nWireConnection;485;0;488;0\nWireConnection;485;1;479;0\nWireConnection;485;2;489;0\nWireConnection;488;0;487;0\nWireConnection;487;0;98;0\nWireConnection;498;0;502;0\nWireConnection;499;0;497;0\nWireConnection;501;0;498;0\nWireConnection;501;1;73;0\nWireConnection;501;2;503;0\nWireConnection;500;0;499;0\nWireConnection;500;1;73;0\nWireConnection;500;2;504;0\nWireConnection;73;0;21;0\nWireConnection;505;0;500;0\nWireConnection;505;1;501;0\nWireConnection;505;2;506;0\nWireConnection;382;0;381;0\nWireConnection;382;1;383;0\nWireConnection;382;2;380;0\nWireConnection;507;0;382;0\nWireConnection;508;0;507;0\nWireConnection;386;0;510;0\nWireConnection;386;1;508;0\nWireConnection;509;0;505;0\nWireConnection;510;0;509;0\nWireConnection;387;0;386;0\nWireConnection;388;0;512;0\nWireConnection;388;1;387;0\nWireConnection;388;2;390;0\nWireConnection;512;0;511;0\nWireConnection;511;0;505;0\nWireConnection;24;0;42;0\nWireConnection;24;1;513;0\nWireConnection;24;7;42;1\nWireConnection;470;0;468;0\nWireConnection;470;1;469;0\nWireConnection;470;7;564;0\nWireConnection;22;0;20;0\nWireConnection;22;1;466;0\nWireConnection;22;7;20;1\nWireConnection;391;0;379;0\nWireConnection;391;1;465;0\nWireConnection;391;7;379;1\nWireConnection;21;0;17;0\nWireConnection;21;1;464;0\nWireConnection;21;7;17;1\nWireConnection;27;0;24;0\nWireConnection;28;0;25;0\nWireConnection;526;0;531;0\nWireConnection;526;1;525;0\nWireConnection;526;2;536;0\nWireConnection;531;0;530;0\nWireConnection;530;0;175;0\nWireConnection;533;0;532;0\nWireConnection;532;0;175;0\nWireConnection;175;0;93;0\nWireConnection;175;1;28;0\nWireConnection;176;0;271;0\nWireConnection;176;1;27;0\nWireConnection;525;0;533;0\nWireConnection;525;1;560;0\nWireConnection;486;0;98;0\nWireConnection;486;1;485;0\nWireConnection;527;0;175;0\nWireConnection;527;1;526;0\nWireConnection;542;0;548;0\nWireConnection;542;1;541;0\nWireConnection;542;2;546;0\nWireConnection;548;0;547;0\nWireConnection;547;0;176;0\nWireConnection;541;0;550;0\nWireConnection;541;1;561;0\nWireConnection;550;0;549;0\nWireConnection;549;0;176;0\nWireConnection;271;0;138;0\nWireConnection;528;0;527;0\nWireConnection;406;0;402;0\nWireConnection;406;1;449;0\nWireConnection;407;0;403;0\nWireConnection;407;1;450;0\nWireConnection;408;0;404;0\nWireConnection;408;1;451;0\nWireConnection;409;0;405;0\nWireConnection;409;1;452;0\nWireConnection;410;0;406;0\nWireConnection;410;1;407;0\nWireConnection;411;0;408;0\nWireConnection;411;1;409;0\nWireConnection;412;0;410;0\nWireConnection;412;1;411;0\nWireConnection;413;0;405;0\nWireConnection;413;1;448;0\nWireConnection;414;0;404;0\nWireConnection;414;1;447;0\nWireConnection;415;0;403;0\nWireConnection;415;1;446;0\nWireConnection;416;0;402;0\nWireConnection;416;1;445;0\nWireConnection;417;0;416;0\nWireConnection;417;1;415;0\nWireConnection;418;0;414;0\nWireConnection;418;1;413;0\nWireConnection;419;0;417;0\nWireConnection;419;1;418;0\nWireConnection;421;0;423;0\nWireConnection;421;1;424;0\nWireConnection;423;0;419;0\nWireConnection;423;1;425;0\nWireConnection;424;0;444;0\nWireConnection;425;0;444;0\nWireConnection;426;0;428;0\nWireConnection;426;1;429;0\nWireConnection;427;0;426;0\nWireConnection;428;0;412;0\nWireConnection;428;1;430;0\nWireConnection;429;0;438;0\nWireConnection;430;0;438;0\nWireConnection;431;0;437;0\nWireConnection;431;1;433;0\nWireConnection;432;0;431;0\nWireConnection;433;0;439;0\nWireConnection;434;0;439;0\nWireConnection;435;0;412;0\nWireConnection;436;0;435;0\nWireConnection;437;0;436;0\nWireConnection;437;1;434;0\nWireConnection;460;0;461;0\nWireConnection;460;1;459;0\nWireConnection;455;0;460;0\nWireConnection;454;0;455;0\nWireConnection;443;0;454;0\nWireConnection;422;0;421;0\nWireConnection;163;0;161;0\nWireConnection;266;1;268;0\nWireConnection;267;0;268;0\nWireConnection;265;0;266;0\nWireConnection;265;1;267;0\nWireConnection;264;0;265;0\nWireConnection;264;1;265;0\nWireConnection;261;0;202;0\nWireConnection;261;1;260;0\nWireConnection;262;0;261;0\nWireConnection;262;1;264;0\nWireConnection;165;0;263;0\nWireConnection;165;1;163;0\nWireConnection;165;2;141;0\nWireConnection;202;0;201;0\nWireConnection;263;0;202;0\nWireConnection;263;1;262;0\nWireConnection;161;0;164;0\nWireConnection;161;1;467;0\nWireConnection;161;7;164;1\nWireConnection;493;0;490;0\nWireConnection;493;1;494;0\nWireConnection;493;7;490;1\nWireConnection;25;0;41;0\nWireConnection;25;1;514;0\nWireConnection;25;7;41;1\nWireConnection;384;0;385;0\nWireConnection;107;0;21;4\nWireConnection;535;0;390;0\nWireConnection;389;0;505;0\nWireConnection;389;1;388;0\nWireConnection;543;0;176;0\nWireConnection;543;1;542;0\nWireConnection;420;0;422;0\nWireConnection;441;0;427;0\nWireConnection;440;0;432;0\nWireConnection;560;0;175;0\nWireConnection;560;1;523;0\nWireConnection;560;2;524;0\nWireConnection;561;0;176;0\nWireConnection;561;1;539;0\nWireConnection;561;2;537;0\nWireConnection;539;0;538;0\nWireConnection;564;0;20;1\nWireConnection;565;0;389;0\nWireConnection;570;0;569;0\nWireConnection;570;1;165;0\nWireConnection;570;2;572;0\nWireConnection;144;0;543;0\nWireConnection;571;0;144;0\nWireConnection;492;0;493;1\nWireConnection;491;0;493;2\nWireConnection;534;0;489;0\nWireConnection;71;0;122;0\nWireConnection;8;0;100;0\nWireConnection;72;0;130;0\nWireConnection;6;0;486;0\nWireConnection;2;0;528;0\nWireConnection;1;0;389;0\nWireConnection;5;0;570;0\nWireConnection;4;0;144;0\nASEEND*/\n//CHKSM=0131E5DA030F12E448D6CCA2C095982374FFC8AD"
  m_functionName: 
  m_description: Amplify Shader Pack Example
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 0
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_customEdited: 0
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 0
  m_headerStyle: 2
  m_headerColor: {r: 1, g: 0.4, b: 0, a: 1}
  m_customNodeCategory: Amplify Shader Pack
  m_previewPosition: 0
  m_hidden: 0
  m_url: 
