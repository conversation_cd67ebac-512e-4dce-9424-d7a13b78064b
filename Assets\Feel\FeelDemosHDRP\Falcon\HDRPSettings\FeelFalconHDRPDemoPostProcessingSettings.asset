%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-7939911367390343821
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5a6b00fcf518bb94a90b408492e07b44, type: 3}
  m_Name: FilmGrain
  m_EditorClassIdentifier: 
  active: 1
  m_AdvancedMode: 0
  type:
    m_OverrideState: 1
    m_Value: 8
  intensity:
    m_OverrideState: 1
    m_Value: 0.1
    min: 0
    max: 1
  response:
    m_OverrideState: 1
    m_Value: 0.8
    min: 0
    max: 1
  texture:
    m_OverrideState: 1
    m_Value: {fileID: 0}
--- !u!114 &-4683021943386259129
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: aaa3b8214f75b354e9ba2caadd022259, type: 3}
  m_Name: DepthOfField
  m_EditorClassIdentifier: 
  active: 1
  m_AdvancedMode: 0
  quality:
    m_OverrideState: 1
    m_Value: 1
  focusMode:
    m_OverrideState: 1
    m_Value: 1
  focusDistance:
    m_OverrideState: 1
    m_Value: 3.06
    min: 0.1
  nearFocusStart:
    m_OverrideState: 1
    m_Value: 0.33
    min: 0
  nearFocusEnd:
    m_OverrideState: 1
    m_Value: 6.57
    min: 0
  farFocusStart:
    m_OverrideState: 1
    m_Value: 1
    min: 0
  farFocusEnd:
    m_OverrideState: 1
    m_Value: 2
    min: 0
  m_NearSampleCount:
    m_OverrideState: 1
    m_Value: 5
    min: 3
    max: 8
  m_NearMaxBlur:
    m_OverrideState: 1
    m_Value: 4
    min: 0
    max: 8
  m_FarSampleCount:
    m_OverrideState: 1
    m_Value: 7
    min: 3
    max: 16
  m_FarMaxBlur:
    m_OverrideState: 1
    m_Value: 8
    min: 0
    max: 16
  m_HighQualityFiltering:
    m_OverrideState: 1
    m_Value: 1
  m_Resolution:
    m_OverrideState: 1
    m_Value: 2
--- !u!114 &-2314332179760026362
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4b8bcdf71d7fafa419fca1ed162f5fc9, type: 3}
  m_Name: ColorAdjustments
  m_EditorClassIdentifier: 
  active: 1
  m_AdvancedMode: 0
  postExposure:
    m_OverrideState: 1
    m_Value: 0
  contrast:
    m_OverrideState: 1
    m_Value: 34.1
    min: -100
    max: 100
  colorFilter:
    m_OverrideState: 1
    m_Value: {r: 1, g: 1, b: 1, a: 1}
    hdr: 1
    showAlpha: 0
    showEyeDropper: 1
  hueShift:
    m_OverrideState: 1
    m_Value: 0
    min: -180
    max: 180
  saturation:
    m_OverrideState: 1
    m_Value: 0
    min: -100
    max: 100
--- !u!114 &-2109818796249167647
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 598e2d32e2c7b0c418e030c3236d663a, type: 3}
  m_Name: ChromaticAberration
  m_EditorClassIdentifier: 
  active: 1
  m_AdvancedMode: 0
  quality:
    m_OverrideState: 1
    m_Value: 2
  spectralLut:
    m_OverrideState: 0
    m_Value: {fileID: 0}
  intensity:
    m_OverrideState: 1
    m_Value: 0.05
    min: 0
    max: 1
  m_MaxSamples:
    m_OverrideState: 0
    m_Value: 8
    min: 3
    max: 24
--- !u!114 &-1817739172173926176
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9008a067f4d626c4d8bc4bc48f04bb89, type: 3}
  m_Name: AmbientOcclusion
  m_EditorClassIdentifier: 
  active: 1
  m_AdvancedMode: 0
  quality:
    m_OverrideState: 1
    m_Value: 1
  rayTracing:
    m_OverrideState: 1
    m_Value: 0
  intensity:
    m_OverrideState: 1
    m_Value: 4
    min: 0
    max: 4
  directLightingStrength:
    m_OverrideState: 1
    m_Value: 0
    min: 0
    max: 1
  radius:
    m_OverrideState: 1
    m_Value: 2
    min: 0.25
    max: 5
  temporalAccumulation:
    m_OverrideState: 1
    m_Value: 1
  ghostingReduction:
    m_OverrideState: 1
    m_Value: 0.5
    min: 0
    max: 1
  blurSharpness:
    m_OverrideState: 1
    m_Value: 0.1
    min: 0
    max: 1
  layerMask:
    m_OverrideState: 1
    m_Value:
      serializedVersion: 2
      m_Bits: **********
  rayLength:
    m_OverrideState: 1
    m_Value: 0.5
    min: 0
    max: 50
  sampleCount:
    m_OverrideState: 1
    m_Value: 4
    min: 1
    max: 64
  denoise:
    m_OverrideState: 1
    m_Value: 0
  denoiserRadius:
    m_OverrideState: 1
    m_Value: 0.5
    min: 0.001
    max: 1
  m_StepCount:
    m_OverrideState: 1
    m_Value: 6
    min: 2
    max: 32
  m_FullResolution:
    m_OverrideState: 1
    m_Value: 0
  m_MaximumRadiusInPixels:
    m_OverrideState: 1
    m_Value: 40
    min: 16
    max: 256
  m_BilateralUpsample:
    m_OverrideState: 1
    m_Value: 1
  m_DirectionCount:
    m_OverrideState: 1
    m_Value: 2
    min: 1
    max: 6
--- !u!114 &-877394172629233039
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: bcf384b154398e341b6b29969c078198, type: 3}
  m_Name: MotionBlur
  m_EditorClassIdentifier: 
  active: 1
  m_AdvancedMode: 0
  quality:
    m_OverrideState: 1
    m_Value: 2
  intensity:
    m_OverrideState: 1
    m_Value: 10
    min: 0
  maximumVelocity:
    m_OverrideState: 1
    m_Value: 50
    min: 0
    max: 1500
  minimumVelocity:
    m_OverrideState: 1
    m_Value: 0
    min: 0
    max: 64
  cameraRotationVelocityClamp:
    m_OverrideState: 1
    m_Value: 0.03
    min: 0
    max: 0.2
  depthComparisonExtent:
    m_OverrideState: 1
    m_Value: 1
    min: 0
    max: 20
  cameraMotionBlur:
    m_OverrideState: 1
    m_Value: 1
  m_SampleCount:
    m_OverrideState: 1
    m_Value: 8
    min: 2
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d7fd9488000d3734a9e00ee676215985, type: 3}
  m_Name: FeelFalconHDRPDemoPostProcessingSettings
  m_EditorClassIdentifier: 
  components:
  - {fileID: 8711377374188185572}
  - {fileID: 6640133647794636954}
  - {fileID: 396278178000043239}
  - {fileID: -2109818796249167647}
  - {fileID: -1817739172173926176}
  - {fileID: -877394172629233039}
  - {fileID: 1622680066408518032}
  - {fileID: -4683021943386259129}
  - {fileID: 6013285994444237452}
  - {fileID: -7939911367390343821}
  - {fileID: -2314332179760026362}
  - {fileID: 7381206715512943917}
  - {fileID: 4921280515941939543}
  - {fileID: 6704185719683628941}
  - {fileID: 1190844326130119954}
  - {fileID: 1867153897525965622}
  - {fileID: 9005373322329986601}
  - {fileID: 3260594495799357335}
--- !u!114 &396278178000043239
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b51a78e223a2e504bb88a059b55229ea, type: 3}
  m_Name: WhiteBalance
  m_EditorClassIdentifier: 
  active: 1
  m_AdvancedMode: 0
  temperature:
    m_OverrideState: 1
    m_Value: 20
    min: -100
    max: 100
  tint:
    m_OverrideState: 0
    m_Value: 0
    min: -100
    max: 100
--- !u!114 &1190844326130119954
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6b24b200358312b4fa1004e2431c2f1f, type: 3}
  m_Name: ShadowsMidtonesHighlights
  m_EditorClassIdentifier: 
  active: 1
  m_AdvancedMode: 0
  shadows:
    m_OverrideState: 0
    m_Value: {x: 1, y: 1, z: 1, w: -0.15740286}
  midtones:
    m_OverrideState: 0
    m_Value: {x: 1, y: 1, z: 1, w: -1}
  highlights:
    m_OverrideState: 0
    m_Value: {x: 1, y: 1, z: 1, w: 0.79685175}
  shadowsStart:
    m_OverrideState: 0
    m_Value: 0
    min: 0
  shadowsEnd:
    m_OverrideState: 0
    m_Value: 0.3
    min: 0
  highlightsStart:
    m_OverrideState: 0
    m_Value: 0.55
    min: 0
  highlightsEnd:
    m_OverrideState: 0
    m_Value: 1
    min: 0
--- !u!114 &1622680066408518032
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 24f077503be6ae942a1e1245dbd53ea9, type: 3}
  m_Name: Bloom
  m_EditorClassIdentifier: 
  active: 1
  m_AdvancedMode: 0
  quality:
    m_OverrideState: 1
    m_Value: 1
  threshold:
    m_OverrideState: 1
    m_Value: 0
    min: 0
  intensity:
    m_OverrideState: 1
    m_Value: 0.497
    min: 0
    max: 1
  scatter:
    m_OverrideState: 1
    m_Value: 0.7
    min: 0
    max: 1
  tint:
    m_OverrideState: 1
    m_Value: {r: 1, g: 1, b: 1, a: 1}
    hdr: 0
    showAlpha: 0
    showEyeDropper: 1
  dirtTexture:
    m_OverrideState: 1
    m_Value: {fileID: 2800000, guid: 517afba2f824eb244b94a340fe47a650, type: 3}
  dirtIntensity:
    m_OverrideState: 1
    m_Value: 1
    min: 0
  anamorphic:
    m_OverrideState: 1
    m_Value: 1
  m_Resolution:
    m_OverrideState: 1
    m_Value: 2
  m_HighQualityFiltering:
    m_OverrideState: 1
    m_Value: 1
--- !u!114 &1867153897525965622
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7ddcec8a8eb2d684d833ac8f5d26aebd, type: 3}
  m_Name: HDShadowSettings
  m_EditorClassIdentifier: 
  active: 1
  m_AdvancedMode: 0
  maxShadowDistance:
    m_OverrideState: 1
    m_Value: 150
    min: 0
  directionalTransmissionMultiplier:
    m_OverrideState: 0
    m_Value: 1
    min: 0
    max: 1
  cascadeShadowSplitCount:
    m_OverrideState: 0
    m_Value: 4
    min: 1
    max: 4
  cascadeShadowSplit0:
    m_OverrideState: 0
    m_Value: 0.05
  cascadeShadowSplit1:
    m_OverrideState: 0
    m_Value: 0.15
  cascadeShadowSplit2:
    m_OverrideState: 0
    m_Value: 0.3
  cascadeShadowBorder0:
    m_OverrideState: 1
    m_Value: 0.13333334
  cascadeShadowBorder1:
    m_OverrideState: 1
    m_Value: 0.06666666
  cascadeShadowBorder2:
    m_OverrideState: 0
    m_Value: 0
  cascadeShadowBorder3:
    m_OverrideState: 0
    m_Value: 0
--- !u!114 &3260594495799357335
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f086a068d4c5889438831b3ae9afc11c, type: 3}
  m_Name: Tonemapping
  m_EditorClassIdentifier: 
  active: 1
  m_AdvancedMode: 0
  mode:
    m_OverrideState: 1
    m_Value: 2
  toeStrength:
    m_OverrideState: 0
    m_Value: 0
    min: 0
    max: 1
  toeLength:
    m_OverrideState: 0
    m_Value: 0.5
    min: 0
    max: 1
  shoulderStrength:
    m_OverrideState: 0
    m_Value: 0
    min: 0
    max: 1
  shoulderLength:
    m_OverrideState: 0
    m_Value: 0.5
    min: 0
  shoulderAngle:
    m_OverrideState: 0
    m_Value: 0
    min: 0
    max: 1
  gamma:
    m_OverrideState: 0
    m_Value: 1
    min: 0.001
  lutTexture:
    m_OverrideState: 0
    m_Value: {fileID: 0}
  lutContribution:
    m_OverrideState: 0
    m_Value: 1
    min: 0
    max: 1
--- !u!114 &4921280515941939543
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a7649d9368d3a5c4ab8ad01a63e04962, type: 3}
  m_Name: ChannelMixer
  m_EditorClassIdentifier: 
  active: 1
  m_AdvancedMode: 0
  redOutRedIn:
    m_OverrideState: 1
    m_Value: 100
    min: -200
    max: 200
  redOutGreenIn:
    m_OverrideState: 1
    m_Value: 0
    min: -200
    max: 200
  redOutBlueIn:
    m_OverrideState: 1
    m_Value: 0
    min: -200
    max: 200
  greenOutRedIn:
    m_OverrideState: 1
    m_Value: 0
    min: -200
    max: 200
  greenOutGreenIn:
    m_OverrideState: 1
    m_Value: 100
    min: -200
    max: 200
  greenOutBlueIn:
    m_OverrideState: 1
    m_Value: 0
    min: -200
    max: 200
  blueOutRedIn:
    m_OverrideState: 1
    m_Value: 0
    min: -200
    max: 200
  blueOutGreenIn:
    m_OverrideState: 1
    m_Value: 0
    min: -200
    max: 200
  blueOutBlueIn:
    m_OverrideState: 1
    m_Value: 100
    min: -200
    max: 200
  m_SelectedChannel: 0
--- !u!114 &6013285994444237452
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9c1bfcd0f0fa7b8468f281d6bbbaf320, type: 3}
  m_Name: LensDistortion
  m_EditorClassIdentifier: 
  active: 1
  m_AdvancedMode: 0
  intensity:
    m_OverrideState: 1
    m_Value: -0.1
    min: -1
    max: 1
  xMultiplier:
    m_OverrideState: 1
    m_Value: 1
    min: 0
    max: 1
  yMultiplier:
    m_OverrideState: 1
    m_Value: 1
    min: 0
    max: 1
  center:
    m_OverrideState: 1
    m_Value: {x: 0.5, y: 0.5}
  scale:
    m_OverrideState: 1
    m_Value: 1
    min: 0.01
    max: 5
--- !u!114 &6640133647794636954
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2d08ce26990eb1a4a9177b860541e702, type: 3}
  m_Name: Exposure
  m_EditorClassIdentifier: 
  active: 1
  m_AdvancedMode: 0
  mode:
    m_OverrideState: 1
    m_Value: 0
  meteringMode:
    m_OverrideState: 0
    m_Value: 2
  luminanceSource:
    m_OverrideState: 0
    m_Value: 1
  fixedExposure:
    m_OverrideState: 1
    m_Value: 8.5
  compensation:
    m_OverrideState: 0
    m_Value: 0
  limitMin:
    m_OverrideState: 0
    m_Value: -10
  limitMax:
    m_OverrideState: 1
    m_Value: 8.5
  curveMap:
    m_OverrideState: 0
    m_Value:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: -10
        value: -10
        inSlope: 0
        outSlope: 1
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 20
        value: 20
        inSlope: 1
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
  adaptationMode:
    m_OverrideState: 0
    m_Value: 1
  adaptationSpeedDarkToLight:
    m_OverrideState: 0
    m_Value: 3
    min: 0.001
  adaptationSpeedLightToDark:
    m_OverrideState: 0
    m_Value: 1
    min: 0.001
--- !u!114 &6704185719683628941
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: da5ab44aadfb1804db5fd470983ac1b8, type: 3}
  m_Name: LiftGammaGain
  m_EditorClassIdentifier: 
  active: 1
  m_AdvancedMode: 0
  lift:
    m_OverrideState: 0
    m_Value: {x: 1, y: 1, z: 1, w: -0.978849}
  gamma:
    m_OverrideState: 0
    m_Value: {x: 1, y: 1, z: 1, w: 0.7988451}
  gain:
    m_OverrideState: 0
    m_Value: {x: 1, y: 1, z: 1, w: -0.37536085}
--- !u!114 &7381206715512943917
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4b709909182ba0943abef2c49ed59205, type: 3}
  m_Name: PaniniProjection
  m_EditorClassIdentifier: 
  active: 1
  m_AdvancedMode: 0
  distance:
    m_OverrideState: 1
    m_Value: 0
    min: 0
    max: 1
  cropToFit:
    m_OverrideState: 1
    m_Value: 1
    min: 0
    max: 1
--- !u!114 &8711377374188185572
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2c1be1b6c95cd2e41b27903b9270817f, type: 3}
  m_Name: Vignette
  m_EditorClassIdentifier: 
  active: 1
  m_AdvancedMode: 0
  mode:
    m_OverrideState: 1
    m_Value: 0
  color:
    m_OverrideState: 1
    m_Value: {r: 0, g: 0, b: 0, a: 1}
    hdr: 0
    showAlpha: 0
    showEyeDropper: 1
  center:
    m_OverrideState: 1
    m_Value: {x: 0.5, y: 0.5}
  intensity:
    m_OverrideState: 1
    m_Value: 0.155
    min: 0
    max: 1
  smoothness:
    m_OverrideState: 1
    m_Value: 0.194
    min: 0.01
    max: 1
  roundness:
    m_OverrideState: 1
    m_Value: 1
    min: 0
    max: 1
  rounded:
    m_OverrideState: 1
    m_Value: 0
  mask:
    m_OverrideState: 1
    m_Value: {fileID: 0}
  opacity:
    m_OverrideState: 1
    m_Value: 1
    min: 0
    max: 1
--- !u!114 &9005373322329986601
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 56b145d2b9ee1ac4f846968484e7485a, type: 3}
  m_Name: ContactShadows
  m_EditorClassIdentifier: 
  active: 1
  m_AdvancedMode: 0
  quality:
    m_OverrideState: 0
    m_Value: 1
  enable:
    m_OverrideState: 1
    m_Value: 1
  length:
    m_OverrideState: 0
    m_Value: 0.15
    min: 0
    max: 1
  opacity:
    m_OverrideState: 0
    m_Value: 1
    min: 0
    max: 1
  distanceScaleFactor:
    m_OverrideState: 0
    m_Value: 0.5
    min: 0
    max: 1
  maxDistance:
    m_OverrideState: 0
    m_Value: 50
    min: 0
  fadeDistance:
    m_OverrideState: 0
    m_Value: 5
    min: 0
  m_SampleCount:
    m_OverrideState: 0
    m_Value: 8
    min: 4
    max: 64
