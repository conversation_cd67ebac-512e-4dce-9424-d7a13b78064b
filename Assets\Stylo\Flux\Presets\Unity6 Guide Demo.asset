%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7be522ae007ba5a4f87ffaaf2e793f4c, type: 3}
  m_Name: Unity6 Guide Demo
  m_EditorClassIdentifier: 
  presetName: Unity6 Guide Demo
  description: Demonstrates the Unity6 Datamosh Guide integration with Custom Render Texture processing. Shows authentic Unity6 Guide effects with pixelated noise and motion-offset sampling.
  category: Unity6 Guide
  effectIntensity: 1
  colorCrunch: 0.3
  downscaling: 4
  blockSize: 1
  oversharpening: 0.3
  dontCrunchSkybox: 0
  onlyStenciled: 0
  reprojectBaseNoise: 0.5
  reprojectBaseRerollSpeed: 3
  reprojectLengthInfluence: 3.0
  cameraMotionAmplification: 0
  cameraMotionThreshold: 0.001
  cameraMotionInfluence: 0
  cameraMotionSmoothing: 0.1
  pixelFlowIntensity: 0
  trailSmoothness: 0
  motionPersistence: 0
  flowGradient: 0
  temporalAccumulation: 0
  keyframeResetRate: 0
  motionVectorCorruption: 0
  errorAccumulation: 0
  dctCorruption: 0
  corruptionMask: {fileID: 0}
  chromaCorruption: 0
  glitchTransition: 0
  feedbackIntensity: 0
  multiScaleCorruption: 0
  jpegQuality: 100
  luminanceQuantization: 0
  chrominanceQuantization: 0
  chromaSubsampling: 0
  ringingArtifacts: 0
  mosquitoNoise: 0
  edgeSensitivity: 0.5

  visualizeMotionVectors: 0
  enableUnity6Guide: 1
  unity6RTResolutionScale: 1
  unity6EffectIntensity: 3.0
  unity6PixelationScale: 80
  unity6BlendFactor: 0.9
  unity6NoiseScale: 25
  unity6MotionAmplification: 8
