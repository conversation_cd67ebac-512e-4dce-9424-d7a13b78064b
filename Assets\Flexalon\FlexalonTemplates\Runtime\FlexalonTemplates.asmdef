{"name": "Flexalon Templates", "rootNamespace": "", "references": ["Flexalon", "Unity.TextMeshPro"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": true, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [{"name": "com.unity.modules.physics", "expression": "", "define": "UNITY_PHYSICS"}, {"name": "com.unity.textmeshpro", "expression": "", "define": "UNITY_TMPRO"}], "noEngineReferences": false}