%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Decal Scifi Panel 3
  m_Shader: {fileID: 4800000, guid: 634eabc5553391d40b08639651ec8818, type: 3}
  m_ValidKeywords:
  - _MATERIAL_AFFECTS_ALBEDO
  - _MATERIAL_AFFECTS_MASKMAP
  - _MATERIAL_AFFECTS_NORMAL
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 1
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: 2001
  stringTagMap: {}
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BaseColor:
        m_Texture: {fileID: 2800000, guid: 9058b7ce77d448f9a967080425fab9d4, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Decal_BaseColor:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Decal_Emissive:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Decal_MaskMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Decal_Normal:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Emissive:
        m_Texture: {fileID: 2800000, guid: 1566835bc2024388b365b715bfd7e1aa, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Mask:
        m_Texture: {fileID: 2800000, guid: 860dd950cbaa4f16b9554bf71f25ae4f, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Normal:
        m_Texture: {fileID: 2800000, guid: f59394e2460c4b4e97cf6f678b4a6532, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _texcoord:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AffectAO: 1
    - _AffectAlbedo: 1
    - _AffectEmission: 1
    - _AffectMetal: 1
    - _AffectNormal: 1
    - _AffectSmoothness: 1
    - _AlphaCutoff: 0.5
    - _DecalBlend: 0.5
    - _DecalColorMask0: 15
    - _DecalColorMask1: 15
    - _DecalColorMask2: 15
    - _DecalColorMask3: 12
    - _DecalMaskMapBlueScale: 1
    - _DecalMeshBiasType: 0
    - _DecalMeshDepthBias: 0.15
    - _DecalMeshViewBias: 0
    - _DecalStencilRef: 16
    - _DecalStencilWriteMask: 16
    - _DrawOrder: 1.34
    - _EmissionIntensity: 1
    - _EmissionIntensity2: 4.15
    - _EmissionWeight: 1
    - _EmissionWeight2: 2
    - _Float0: 1.92
    - _Float1: 0
    - _MaskBlendSrc: 1
    - _NormalBlendSrc: 0
    - _TileNoise1: 0
    - __dirty: 1
    m_Colors:
    - _Color0: {r: 119.42823, g: 93.20086, b: 0, a: 1}
    - _EmissionColor: {r: 1, g: 1, b: 1, a: 1}
    - _EmissionTintIntensity: {r: 119.42823, g: 93.20086, b: 0, a: 1}
    - _Offset: {r: 1.67, g: 1.67, b: 0, a: 0}
    - _Tiling: {r: 0.33, g: 0.33, b: 0, a: 0}
  m_BuildTextureStacks: []
