using UnityEngine;
using UnityEngine.UI;
using UnityEngine.Rendering.Universal;
using TMPro;
using System.Collections.Generic;
using System.Linq;

namespace Stylo.MenUI
{
    /// <summary>
    /// Graphics settings panel that provides controls for quality presets, resolution,
    /// fullscreen mode, VSync, and other graphics-related options.
    /// </summary>
    public class GraphicsSettingsPanel : MonoBehaviour
    {
        [Header("Quality Settings")]
        [SerializeField] private TMP_Dropdown qualityPresetDropdown;
        [SerializeField] private Slider renderScaleSlider;
        [SerializeField] private TextMeshProUGUI renderScaleValueText;

        [Header("Display Settings")]
        [SerializeField] private TMP_Dropdown resolutionDropdown;
        [SerializeField] private TMP_Dropdown fullscreenModeDropdown;
        [SerializeField] private Toggle vSyncToggle;
        [SerializeField] private TMP_Dropdown targetFramerateDropdown;

        [Header("Rendering Settings")]
        [SerializeField] private Toggle hdrToggle;
        [SerializeField] private Slider shadowDistanceSlider;
        [SerializeField] private TextMeshProUGUI shadowDistanceValueText;
        [SerializeField] private TMP_Dropdown shadowQualityDropdown;
        [SerializeField] private TMP_Dropdown shadowResolutionDropdown;

        [Header("Post-Processing")]
        [SerializeField] private Toggle postProcessingToggle;
        [SerializeField] private Slider bloomIntensitySlider;
        [SerializeField] private TextMeshProUGUI bloomIntensityValueText;

        [Header("Unity 6 Specific")]
        [SerializeField] private Toggle additionalLightShadowsToggle;
        [SerializeField] private Slider cascadeShadowDistanceSlider;
        [SerializeField] private TextMeshProUGUI cascadeShadowDistanceValueText;

        // Data
        private Resolution[] _availableResolutions;
        private UniversalRenderPipelineAsset _urpAsset;
        private GraphicsSettingsData _currentSettings;

        // Events
        public System.Action<GraphicsSettingsData> OnSettingsChanged;

        [System.Serializable]
        public class GraphicsSettingsData
        {
            public int qualityLevel = 0;
            public float renderScale = 1.0f;
            public int resolutionIndex = 0;
            public FullScreenMode fullscreenMode = FullScreenMode.ExclusiveFullScreen;
            public bool vSyncEnabled = true;
            public int targetFramerate = 60;
            public bool hdrEnabled = true;
            public float shadowDistance = 50f;
            public int shadowQuality = 2;
            public int shadowResolution = 2; // 0=Low, 1=Medium, 2=High, 3=Very High
            public bool postProcessingEnabled = true;
            public float bloomIntensity = 0.5f;

            // Unity 6 URP Specific
            public bool additionalLightShadowsEnabled = true;
            public float cascadeShadowDistance = 150f;
        }

        #region Unity Lifecycle

        private void Awake()
        {
            InitializeComponents();
            LoadCurrentSettings();
        }

        private void Start()
        {
            SetupDropdowns();
            SetupSliders();
            SetupToggles();
            RefreshUI();
        }

        private void OnEnable()
        {
            RefreshUI();
        }

        #endregion

        #region Initialization

        private void InitializeComponents()
        {
            // Get URP asset reference
            _urpAsset = UnityEngine.Rendering.GraphicsSettings.defaultRenderPipeline as UniversalRenderPipelineAsset;

            // Initialize settings data
            _currentSettings = new GraphicsSettingsData();

            // Get available resolutions
            _availableResolutions = Screen.resolutions
                .Where(r => r.refreshRate >= 60) // Filter to reasonable refresh rates
                .Distinct()
                .OrderByDescending(r => r.width * r.height)
                .ToArray();
        }

        private void SetupDropdowns()
        {
            SetupQualityPresetDropdown();
            SetupResolutionDropdown();
            SetupFullscreenModeDropdown();
            SetupTargetFramerateDropdown();
            SetupShadowQualityDropdown();
            SetupShadowResolutionDropdown();
        }

        private void SetupQualityPresetDropdown()
        {
            if (qualityPresetDropdown == null) return;

            qualityPresetDropdown.ClearOptions();
            var qualityNames = QualitySettings.names.ToList();
            qualityPresetDropdown.AddOptions(qualityNames);
            qualityPresetDropdown.onValueChanged.AddListener(OnQualityPresetChanged);
        }

        private void SetupResolutionDropdown()
        {
            if (resolutionDropdown == null) return;

            resolutionDropdown.ClearOptions();
            var resolutionOptions = _availableResolutions
                .Select(r => $"{r.width} x {r.height} @ {r.refreshRate}Hz")
                .ToList();
            resolutionDropdown.AddOptions(resolutionOptions);
            resolutionDropdown.onValueChanged.AddListener(OnResolutionChanged);
        }

        private void SetupFullscreenModeDropdown()
        {
            if (fullscreenModeDropdown == null) return;

            fullscreenModeDropdown.ClearOptions();
            var fullscreenOptions = new List<string>
            {
                "Exclusive Fullscreen",
                "Fullscreen Window",
                "Maximized Window",
                "Windowed"
            };
            fullscreenModeDropdown.AddOptions(fullscreenOptions);
            fullscreenModeDropdown.onValueChanged.AddListener(OnFullscreenModeChanged);
        }

        private void SetupTargetFramerateDropdown()
        {
            if (targetFramerateDropdown == null) return;

            targetFramerateDropdown.ClearOptions();
            var framerateOptions = new List<string> { "30", "60", "120", "144", "Unlimited" };
            targetFramerateDropdown.AddOptions(framerateOptions);
            targetFramerateDropdown.onValueChanged.AddListener(OnTargetFramerateChanged);
        }

        private void SetupShadowQualityDropdown()
        {
            if (shadowQualityDropdown == null) return;

            shadowQualityDropdown.ClearOptions();
            var shadowOptions = new List<string> { "Disabled", "Low", "Medium", "High", "Very High" };
            shadowQualityDropdown.AddOptions(shadowOptions);
            shadowQualityDropdown.onValueChanged.AddListener(OnShadowQualityChanged);
        }

        private void SetupShadowResolutionDropdown()
        {
            if (shadowResolutionDropdown == null) return;

            shadowResolutionDropdown.ClearOptions();
            var resolutionOptions = new List<string> { "Low (512)", "Medium (1024)", "High (2048)", "Very High (4096)" };
            shadowResolutionDropdown.AddOptions(resolutionOptions);
            shadowResolutionDropdown.onValueChanged.AddListener(OnShadowResolutionChanged);
        }

        private void SetupSliders()
        {
            if (renderScaleSlider != null)
            {
                renderScaleSlider.minValue = 0.5f;
                renderScaleSlider.maxValue = 2.0f;
                renderScaleSlider.onValueChanged.AddListener(OnRenderScaleChanged);
            }

            if (shadowDistanceSlider != null)
            {
                shadowDistanceSlider.minValue = 10f;
                shadowDistanceSlider.maxValue = 200f;
                shadowDistanceSlider.onValueChanged.AddListener(OnShadowDistanceChanged);
            }

            if (bloomIntensitySlider != null)
            {
                bloomIntensitySlider.minValue = 0f;
                bloomIntensitySlider.maxValue = 1f;
                bloomIntensitySlider.onValueChanged.AddListener(OnBloomIntensityChanged);
            }

            if (cascadeShadowDistanceSlider != null)
            {
                cascadeShadowDistanceSlider.minValue = 50f;
                cascadeShadowDistanceSlider.maxValue = 500f;
                cascadeShadowDistanceSlider.onValueChanged.AddListener(OnCascadeShadowDistanceChanged);
            }
        }

        private void SetupToggles()
        {
            if (vSyncToggle != null)
                vSyncToggle.onValueChanged.AddListener(OnVSyncChanged);

            if (hdrToggle != null)
                hdrToggle.onValueChanged.AddListener(OnHDRChanged);

            if (postProcessingToggle != null)
                postProcessingToggle.onValueChanged.AddListener(OnPostProcessingChanged);

            if (additionalLightShadowsToggle != null)
                additionalLightShadowsToggle.onValueChanged.AddListener(OnAdditionalLightShadowsChanged);
        }

        #endregion

        #region Settings Management

        private void LoadCurrentSettings()
        {
            _currentSettings.qualityLevel = QualitySettings.GetQualityLevel();
            _currentSettings.renderScale = _urpAsset != null ? _urpAsset.renderScale : 1.0f;

            // Find current resolution index
            var currentRes = Screen.currentResolution;
            _currentSettings.resolutionIndex = System.Array.FindIndex(_availableResolutions,
                r => r.width == currentRes.width && r.height == currentRes.height);
            if (_currentSettings.resolutionIndex < 0) _currentSettings.resolutionIndex = 0;

            _currentSettings.fullscreenMode = Screen.fullScreenMode;
            _currentSettings.vSyncEnabled = QualitySettings.vSyncCount > 0;
            _currentSettings.targetFramerate = Application.targetFrameRate;

            _currentSettings.hdrEnabled = _urpAsset != null ? _urpAsset.supportsHDR : true;
            _currentSettings.shadowDistance = QualitySettings.shadowDistance;
            _currentSettings.shadowQuality = (int)QualitySettings.shadows;
            _currentSettings.shadowResolution = GetShadowResolutionIndex();
            _currentSettings.postProcessingEnabled = true; // Default, would need Volume Profile check
            _currentSettings.bloomIntensity = 0.5f; // Default, would need Volume Profile check
        }

        public void ApplySettings()
        {
            // Apply quality level
            QualitySettings.SetQualityLevel(_currentSettings.qualityLevel);

            // Apply render scale
            if (_urpAsset != null)
                _urpAsset.renderScale = _currentSettings.renderScale;

            // Apply resolution and fullscreen
            if (_currentSettings.resolutionIndex >= 0 && _currentSettings.resolutionIndex < _availableResolutions.Length)
            {
                var resolution = _availableResolutions[_currentSettings.resolutionIndex];
                Screen.SetResolution(resolution.width, resolution.height, _currentSettings.fullscreenMode, resolution.refreshRate);
            }

            // Apply VSync
            QualitySettings.vSyncCount = _currentSettings.vSyncEnabled ? 1 : 0;

            // Apply target framerate
            Application.targetFrameRate = _currentSettings.targetFramerate;

            // Apply HDR
            if (_urpAsset != null)
                _urpAsset.supportsHDR = _currentSettings.hdrEnabled;

            // Apply shadow settings
            QualitySettings.shadowDistance = _currentSettings.shadowDistance;
            QualitySettings.shadows = (UnityEngine.ShadowQuality)_currentSettings.shadowQuality;
            ApplyShadowResolution(_currentSettings.shadowResolution);

            // Trigger settings changed event
            OnSettingsChanged?.Invoke(_currentSettings);
        }

        public void ResetToDefaults()
        {
            _currentSettings = new GraphicsSettingsData();
            RefreshUI();
        }

        private void RefreshUI()
        {
            // Update dropdowns
            if (qualityPresetDropdown != null)
                qualityPresetDropdown.SetValueWithoutNotify(_currentSettings.qualityLevel);

            if (resolutionDropdown != null)
                resolutionDropdown.SetValueWithoutNotify(_currentSettings.resolutionIndex);

            if (fullscreenModeDropdown != null)
                fullscreenModeDropdown.SetValueWithoutNotify((int)_currentSettings.fullscreenMode);

            if (targetFramerateDropdown != null)
            {
                int framerateIndex = _currentSettings.targetFramerate switch
                {
                    30 => 0,
                    60 => 1,
                    120 => 2,
                    144 => 3,
                    _ => 4 // Unlimited
                };
                targetFramerateDropdown.SetValueWithoutNotify(framerateIndex);
            }

            if (shadowQualityDropdown != null)
                shadowQualityDropdown.SetValueWithoutNotify(_currentSettings.shadowQuality);

            if (shadowResolutionDropdown != null)
                shadowResolutionDropdown.SetValueWithoutNotify(_currentSettings.shadowResolution);

            // Update sliders
            if (renderScaleSlider != null)
            {
                renderScaleSlider.SetValueWithoutNotify(_currentSettings.renderScale);
                UpdateRenderScaleText(_currentSettings.renderScale);
            }

            if (shadowDistanceSlider != null)
            {
                shadowDistanceSlider.SetValueWithoutNotify(_currentSettings.shadowDistance);
                UpdateShadowDistanceText(_currentSettings.shadowDistance);
            }

            if (bloomIntensitySlider != null)
            {
                bloomIntensitySlider.SetValueWithoutNotify(_currentSettings.bloomIntensity);
                UpdateBloomIntensityText(_currentSettings.bloomIntensity);
            }

            // Update toggles
            if (vSyncToggle != null)
                vSyncToggle.SetIsOnWithoutNotify(_currentSettings.vSyncEnabled);

            if (hdrToggle != null)
                hdrToggle.SetIsOnWithoutNotify(_currentSettings.hdrEnabled);

            if (postProcessingToggle != null)
                postProcessingToggle.SetIsOnWithoutNotify(_currentSettings.postProcessingEnabled);
        }

        #endregion

        #region UI Callbacks

        private void OnQualityPresetChanged(int value)
        {
            _currentSettings.qualityLevel = value;
        }

        private void OnRenderScaleChanged(float value)
        {
            _currentSettings.renderScale = value;
            UpdateRenderScaleText(value);
        }

        private void OnResolutionChanged(int value)
        {
            _currentSettings.resolutionIndex = value;
        }

        private void OnFullscreenModeChanged(int value)
        {
            _currentSettings.fullscreenMode = (FullScreenMode)value;
        }

        private void OnVSyncChanged(bool value)
        {
            _currentSettings.vSyncEnabled = value;
        }

        private void OnTargetFramerateChanged(int value)
        {
            _currentSettings.targetFramerate = value switch
            {
                0 => 30,
                1 => 60,
                2 => 120,
                3 => 144,
                _ => -1 // Unlimited
            };
        }

        private void OnHDRChanged(bool value)
        {
            _currentSettings.hdrEnabled = value;
        }

        private void OnShadowDistanceChanged(float value)
        {
            _currentSettings.shadowDistance = value;
            UpdateShadowDistanceText(value);
        }

        private void OnShadowQualityChanged(int value)
        {
            _currentSettings.shadowQuality = value;
        }

        private void OnShadowResolutionChanged(int value)
        {
            _currentSettings.shadowResolution = value;
        }

        private void OnPostProcessingChanged(bool value)
        {
            _currentSettings.postProcessingEnabled = value;
        }

        private void OnBloomIntensityChanged(float value)
        {
            _currentSettings.bloomIntensity = value;
            UpdateBloomIntensityText(value);
        }

        private void OnAdditionalLightShadowsChanged(bool value)
        {
            _currentSettings.additionalLightShadowsEnabled = value;
        }

        private void OnCascadeShadowDistanceChanged(float value)
        {
            _currentSettings.cascadeShadowDistance = value;
            UpdateCascadeShadowDistanceText(value);
        }

        #endregion

        #region UI Text Updates

        private void UpdateRenderScaleText(float value)
        {
            if (renderScaleValueText != null)
                renderScaleValueText.text = $"{value:F1}x";
        }

        private void UpdateShadowDistanceText(float value)
        {
            if (shadowDistanceValueText != null)
                shadowDistanceValueText.text = $"{value:F0}m";
        }

        private void UpdateBloomIntensityText(float value)
        {
            if (bloomIntensityValueText != null)
                bloomIntensityValueText.text = $"{value:F2}";
        }

        private void UpdateCascadeShadowDistanceText(float value)
        {
            if (cascadeShadowDistanceValueText != null)
                cascadeShadowDistanceValueText.text = $"{value:F0}m";
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Get current shadow resolution index based on URP asset settings
        /// Unity 6 doesn't have QualitySettings.shadowResolution, so we use URP asset
        /// </summary>
        private int GetShadowResolutionIndex()
        {
            if (_urpAsset != null)
            {
                // In Unity 6 URP, shadow resolution is typically controlled through the URP asset
                // We'll use a default based on the main light shadow resolution
                var mainLightShadowResolution = _urpAsset.mainLightShadowmapResolution;
                return mainLightShadowResolution switch
                {
                    512 => 0,   // Low
                    1024 => 1,  // Medium
                    2048 => 2,  // High
                    4096 => 3,  // Very High
                    _ => 1      // Default to Medium
                };
            }
            return 1; // Default to Medium
        }

        /// <summary>
        /// Apply shadow resolution setting to URP asset
        /// Unity 6 uses URP asset properties instead of QualitySettings.shadowResolution
        /// </summary>
        private void ApplyShadowResolution(int index)
        {
            if (_urpAsset != null)
            {
                int resolution = index switch
                {
                    0 => 512,   // Low
                    1 => 1024,  // Medium
                    2 => 2048,  // High
                    3 => 4096,  // Very High
                    _ => 1024   // Default to Medium
                };

                // Apply to main light shadows
                _urpAsset.mainLightShadowmapResolution = resolution;

                // Also apply to additional light shadows if available
                _urpAsset.additionalLightsShadowmapResolution = resolution;
            }
        }

        #endregion

        #region Public API

        /// <summary>
        /// Get current graphics settings data
        /// </summary>
        public GraphicsSettingsData GetCurrentSettings()
        {
            return _currentSettings;
        }

        /// <summary>
        /// Set graphics settings data
        /// </summary>
        public void SetSettings(GraphicsSettingsData settings)
        {
            _currentSettings = settings;
            RefreshUI();
        }

        #endregion
    }
}
