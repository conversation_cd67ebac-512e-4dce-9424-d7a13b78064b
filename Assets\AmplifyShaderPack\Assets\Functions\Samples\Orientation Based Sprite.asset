%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: Orientation Based Sprite
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor v1.9.1.5\n// Available at the
    Unity Asset Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=19105\nNode;AmplifyShaderEditor.CommentaryNode;6;-984.9904,122.4028;Inherit;False;1318.133;738.2407;;11;35;31;30;26;23;19;17;13;10;42;45;Offset
    V Coordinate;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;12;-1640.469,-725.7608;Inherit;False;786.1016;314.9933;;4;27;25;22;15;Frame
    Index;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;1;-3082.758,-1226.069;Inherit;False;1941.194;446.4606;;11;24;16;14;11;8;7;5;4;3;2;48;Angle
    From Position;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;21;-810.7599,-702.7303;Inherit;False;550.3418;572.5402;;5;39;34;33;32;28;Offset
    U Coordinate;1,1,1,1;0;0\nNode;AmplifyShaderEditor.VectorFromMatrixNode;3;-2826.119,-1176.069;Inherit;False;Column;3;1;0;FLOAT4x4;0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0;False;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.BreakToComponentsNode;11;-1837.37,-1142.397;Inherit;False;FLOAT3;1;0;FLOAT3;0,0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.SwizzleNode;4;-2576.209,-1146.124;Inherit;False;FLOAT3;0;1;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.ObjectToWorldMatrixNode;2;-3032.759,-1153.33;Inherit;False;0;1;FLOAT4x4;0\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;7;-2373.52,-1106.284;Inherit;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;34;-416.2903,-566.998;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;30;-150.8604,347.4032;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;32;-588.6506,-635.1859;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;23;-474.8606,494.4031;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.TimeNode;10;-934.9904,681.6429;Inherit;False;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.PiNode;16;-1709.749,-926.3923;Inherit;False;1;0;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;39;-776.1599,-322.772;Float;False;Constant;_Float4;Float
    4;3;0;Create;True;0;0;0;False;0;False;1;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;48;-1530.805,-938.6661;Inherit;False;Angle
    Offset;1;2;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SwizzleNode;47;554.2314,-121.0587;Inherit;False;FLOAT;0;1;2;3;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;46;305.5089,4.38942;Inherit;False;Animated;False;1;2;-1;Static;Animated;Object;-1;9;0;FLOAT2;0,0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;41;-1589.206,-270.2057;Inherit;False;Rows;1;3;False;1;0;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;45;-490.9224,249.8521;Inherit;False;Columns;1;4;False;1;0;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.NormalizeNode;8;-2104.44,-1134.359;Inherit;False;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleDivideOpNode;28;-627.9807,-274.3898;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;13;-721.84,621.7819;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;33;-429.4104,-296.8155;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;24;-1269.5,-1057.539;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleDivideOpNode;22;-1401.2,-543.7676;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RoundOpNode;19;-555.5305,641.4789;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.VertexToFragmentNode;37;830.8096,-251.8021;Inherit;False;False;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.ATan2OpNode;14;-1578.009,-1132.859;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FloorOpNode;27;-1008.37,-675.7608;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;31;-12.86046,181.4032;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.TauNode;15;-1514.469,-538.3823;Inherit;False;0;1;FLOAT;0\nNode;AmplifyShaderEditor.WorldSpaceCameraPos;5;-2695.369,-982.2531;Inherit;True;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.SimpleDivideOpNode;25;-1206.87,-625.1526;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.DynamicAppendNode;36;684.9901,-239.1286;Inherit;False;FLOAT2;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SamplerNode;38;1053.551,-289.0468;Inherit;True;Property;_TextureSample0;Texture
    Sample 0;1;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.FmodOpNode;26;-312.8606,431.403;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;42;-901.7347,585.1371;Inherit;False;Anim
    Speed;1;5;False;1;0;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleDivideOpNode;35;176.1395,172.4032;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;43;-807.257,-20.10132;Inherit;False;UV;2;1;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.BreakToComponentsNode;44;-584.8108,-104.9738;Inherit;False;FLOAT2;1;0;FLOAT2;0,0;False;16;FLOAT;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT;5;FLOAT;6;FLOAT;7;FLOAT;8;FLOAT;9;FLOAT;10;FLOAT;11;FLOAT;12;FLOAT;13;FLOAT;14;FLOAT;15\nNode;AmplifyShaderEditor.FunctionInput;40;836.4571,-345.5445;Inherit;False;Tex;9;0;False;1;0;SAMPLER2D;0.0;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.RangedFloatNode;17;-631.8605,474.4031;Float;False;Constant;_Float1;Float
    1;4;0;Create;True;0;0;0;False;0;False;1;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.TextureCoordinatesNode;29;-1068.475,-49.26042;Inherit;False;0;-1;2;3;2;SAMPLER2D;;False;0;FLOAT2;1,1;False;1;FLOAT2;0,0;False;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.FunctionOutput;0;1386.399,-309.5234;Inherit;False;True;-1;Output;0;False;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nWireConnection;3;0;2;0\nWireConnection;11;0;8;0\nWireConnection;4;0;3;0\nWireConnection;7;0;4;0\nWireConnection;7;1;5;0\nWireConnection;34;0;32;0\nWireConnection;34;1;33;0\nWireConnection;30;0;45;0\nWireConnection;30;1;26;0\nWireConnection;32;0;27;0\nWireConnection;32;1;28;0\nWireConnection;23;0;17;0\nWireConnection;23;1;19;0\nWireConnection;48;0;16;0\nWireConnection;47;0;46;0\nWireConnection;46;0;43;0\nWireConnection;46;1;35;0\nWireConnection;8;0;7;0\nWireConnection;28;0;39;0\nWireConnection;28;1;41;0\nWireConnection;13;0;42;0\nWireConnection;13;1;10;2\nWireConnection;33;0;28;0\nWireConnection;33;1;44;0\nWireConnection;24;0;14;0\nWireConnection;24;1;48;0\nWireConnection;22;0;15;0\nWireConnection;22;1;41;0\nWireConnection;19;0;13;0\nWireConnection;37;0;36;0\nWireConnection;14;0;11;2\nWireConnection;14;1;11;0\nWireConnection;27;0;25;0\nWireConnection;31;0;44;1\nWireConnection;31;1;30;0\nWireConnection;25;0;24;0\nWireConnection;25;1;22;0\nWireConnection;36;0;34;0\nWireConnection;36;1;47;0\nWireConnection;38;0;40;0\nWireConnection;38;1;37;0\nWireConnection;26;0;23;0\nWireConnection;26;1;45;0\nWireConnection;35;0;31;0\nWireConnection;35;1;45;0\nWireConnection;43;0;29;0\nWireConnection;44;0;43;0\nWireConnection;0;0;38;0\nASEEND*/\n//CHKSM=CB526503A0FB884962785B2133ADF2E4FA146400"
  m_functionName: 
  m_description: Amplify Shader Pack Example
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 0
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_customEdited: 0
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 0
  m_headerStyle: 0
  m_headerColor: {r: 1, g: 0.4, b: 0, a: 1}
  m_customNodeCategory: Amplify Shader Pack
  m_previewPosition: 0
  m_hidden: 0
  m_url: 
