%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: MAT_TaiDenolGhost
  m_Shader: {fileID: 4800000, guid: 88e9264ba3f8ff64fbbcb07a4957e5d3, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords:
  - _TRANSPARENT_ON
  m_InvalidKeywords:
  - _DUPLICATEDETAILS_ON
  - _EMISSION
  - _INVERT_ON
  - _MAPCONTRIBUTION_NONE
  - _USEDIRTMAPASGLOSSMAP_ON
  - _UVSCREENPROJECTION_UVPROJECTION
  m_LightmapFlags: 2
  m_EnableInstancingVariants: 1
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BaseMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Cubemap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMap:
        m_Texture: {fileID: 2800000, guid: 8c38d2e72ca280f4783343ec9c4418f7, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ReflectionTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AlphaClip: 0
    - _AlphaCutoff: 0.5
    - _AmplitudeSpeed: 1
    - _Blend: 0
    - _BumpScale: 1
    - _ClearCoatMask: 0
    - _ClearCoatSmoothness: 0
    - _ContrastDetailMap: 1
    - _CubemapBlur: 0
    - _CubemapIntensity: 1
    - _Cull: 2
    - _Cutoff: 0.5
    - _DesaturateBackground: 0.365
    - _DetailAlbedoMapScale: 1
    - _DetailNormalMapScale: 1
    - _DetailScale: 1
    - _DstBlend: 0
    - _DuplicateDetails: 1
    - _EnableCubemap: 0
    - _EnableRGBchannels: 0
    - _EnableSpecularLight: 0
    - _EnvironmentReflections: 1
    - _FresnelBias: 0.06
    - _FresnelIntensity: 4
    - _FresnelPower: 4
    - _FresnelStrength: 0
    - _GlossMapScale: 1
    - _Glossiness: 0
    - _GlossyReflections: 1
    - _Intensity: 1
    - _Invert: 1
    - _MapContribution: 0
    - _MaxValueAmplitude: 2
    - _Metallic: 0
    - _MinValueAmplitude: 1
    - _Mode: 0
    - _OcclusionStrength: 1
    - _Opacity: 0.453
    - _Parallax: 0.02
    - _QueueOffset: 0
    - _ReceiveShadows: 1
    - _Refraction: 1.38
    - _RotationAngle: 0
    - _RotationSpeed: 0
    - _ScreenBlendingmode: 0
    - _SelfIllumination: 6.6
    - _ShadowOpacity: 0.95
    - _Smoothness: 0
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SpecularIntensity1: 0.2
    - _SpreadDetailMap: 0
    - _SrcBlend: 1
    - _Surface: 0
    - _TexturesScale: 1
    - _TranslationSpeed: 0
    - _TransparentMode: 1
    - _UVScreenProjection: 0
    - _UVSec: 0
    - _UseDirtmapasGlossmap: 0.5
    - _WorkflowMode: 1
    - _XRayBias: 0.04
    - _XRayFresnelIntensity: 1
    - _XRayFresnelPower: 1
    - _ZWrite: 1
    m_Colors:
    - _BaseColor: {r: 0, g: 0.38679245, b: 0.3412875, a: 1}
    - _Color: {r: 0, g: 0.38679242, b: 0.34128746, a: 1}
    - _EmissionColor: {r: 0, g: 0.6981132, b: 0.51098996, a: 1}
    - _FillColorBackground: {r: 0.4528302, g: 0, b: 0.017944768, a: 0}
    - _FresnelColor: {r: 0.005102546, g: 0.6538109, b: 0.8113208, a: 1}
    - _SpecColor: {r: 0.19999996, g: 0.19999996, b: 0.19999996, a: 1}
    - _XRayColor: {r: 0, g: 1, b: 0.9802119, a: 0.1019608}
  m_BuildTextureStacks: []
  m_AllowLocking: 1
--- !u!114 &3769269525897314377
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0353a89b1f911e48b9e16bdc9f2e058, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 5
