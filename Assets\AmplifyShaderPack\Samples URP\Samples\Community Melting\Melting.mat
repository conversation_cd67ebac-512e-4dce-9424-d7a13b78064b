%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Melting
  m_Shader: {fileID: 4800000, guid: d89412b84bfc44ee8c146f0ceace8aa9, type: 3}
  m_ValidKeywords: []
  m_InvalidKeywords:
  - _ANIMATEDMELT_ON
  - _TOGGLEANIMATEDFX_ON
  - _TOGGLECONTROLTYPE_ON
  m_LightmapFlags: 0
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - <noninit>:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BaseNormal:
        m_Texture: {fileID: 2800000, guid: bec834914d444ee688f9583988f45e43, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BrushedMetalNormal:
        m_Texture: {fileID: 2800000, guid: bd734c29baceb63499732f24fbaea45f, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DisplaceNoise:
        m_Texture: {fileID: 2800000, guid: 4d85556227fd4d5a8f034d10f4261675, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Normal:
        m_Texture: {fileID: 2800000, guid: bd734c29baceb63499732f24fbaea45f, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TextureSample0:
        m_Texture: {fileID: 2800000, guid: cd460ee4ac5c1e746b7a734cc7cc64dd, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _texcoord:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - <noninit>: 0
    - _AlbedoSmoothness: 14.61
    - _AlphaCutoff: 0.5
    - _AnimatedMelt: 1
    - _EnvironmentReflections: 1
    - _Float0: 1.64
    - _Float1: 1.92
    - _Intensity: 8
    - _Limit: 2.78
    - _ManualControl: 0.84
    - _Metallic: 1
    - _NoiseMultiply: 1.92
    - _NoiseScale: 0.0527
    - _Oscillation: 2.24
    - _QueueControl: 0
    - _QueueOffset: 0
    - _ReceiveShadows: 1
    - _Smoothness: 0.6536242
    - _SpecularHighlights: 1
    - _ToggleAnimatedFX: 1
    - _ToggleControlType: 1
    - _ToggleSwitch0: 0
    - __dirty: 0
    m_Colors:
    - <noninit>: {r: 0, g: 1.7256007e-33, b: 0, a: 1.725589e-33}
    - _BaseColor: {r: 0.26470563, g: 0.26470563, b: 0.26470563, a: 0}
    - _Color1: {r: 1.354, g: 0.30815142, b: 0, a: 1}
    - _Color2: {r: 12.517014, g: 11.653772, b: 0, a: 1}
    - _EmissionColor: {r: 1, g: 1, b: 1, a: 1}
  m_BuildTextureStacks: []
