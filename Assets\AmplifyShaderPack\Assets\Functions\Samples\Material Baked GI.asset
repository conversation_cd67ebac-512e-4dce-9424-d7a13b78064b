%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: Material Baked GI
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor v1.9.6.2\n// Available at the
    Unity Asset Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=19602\nNode;AmplifyShaderEditor.CommentaryNode;529;-912,-1840;Inherit;False;4322.288;2203.654;BAKED
    GI;100;1001;1000;984;983;982;966;965;964;962;961;960;959;956;955;954;953;952;951;950;949;948;942;941;937;936;935;934;933;932;931;930;929;928;927;926;925;924;923;922;920;919;913;912;911;910;909;840;839;838;837;836;835;834;833;832;831;830;829;828;827;826;825;824;823;822;821;820;819;818;817;813;812;811;810;806;743;340;332;333;339;1016;1015;1342;1340;1341;1339;1330;1338;1336;1337;1334;1335;1333;1329;1332;1327;1328;1326;1344;1345;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;743;1680,-1600;Inherit;False;337.9711;366.3911;Decode
    Directional Lightmap;2;963;745;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;1031;-880,-2608;Inherit;False;703.506;350.7954;Static
    Lightmap (UV1) unity_LightmapST;5;1258;1215;1216;1257;1219;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;1032;-880,-2240;Inherit;False;704.4957;360.5587;Dynamic
    Lightmap (UV2) unity_DynamicLightmapST;5;1259;1218;1217;1256;1255;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;1033;-880,-2944;Inherit;False;933.7199;314.8777;Normals;5;975;1070;1101;1063;1073;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;745;1696,-1408;Inherit;False;317.5652;139.3022;;;0,0,0,1;Calls
    Unity internal DecodeDirectionalLightmap function.$Uses custom graph on all other
    pipelines;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;962;352,-784;Inherit;False;383.0747;114.9632;Sample
    Lightmap Bilinear;;0,0,0,1;render-pipelines.core / ShaderLibrary$ / EntityLighting.hlsl;0;0\nNode;AmplifyShaderEditor.LerpOp;824;-112,192;Inherit;False;3;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0,0,0,0;False;2;FLOAT;0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.GetLocalVarNode;828;-352,128;Inherit;False;959;FilterMode;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;812;944,-1472;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;820;944,-1712;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;821;944,-1712;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;822;944,-1712;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;823;944,-1712;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.LerpOp;825;-112,80;Inherit;False;3;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0,0,0,0;False;2;FLOAT;0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.WireNode;832;816,-512;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;833;992,-528;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;834;944,-544;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;835;832,-416;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;836;992,-288;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;837;944,-288;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;909;1648,-1552;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;912;992,-1024;Inherit;False;3;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;FLOAT;0;False;1;COLOR;0\nNode;AmplifyShaderEditor.WireNode;913;944,-1040;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;924;1648,-1440;Inherit;False;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.WireNode;926;2352,-1600;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;927;2352,-1600;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;928;2352,-1600;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;929;2352,-1600;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;930;2352,-736;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;931;2352,-736;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;932;2352,-464;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;933;2352,-464;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitchByPipeline;934;2432,-448;Inherit;False;4;0;FLOAT3;0,0,0;False;3;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitchByPipeline;935;2432,-720;Inherit;False;4;0;FLOAT3;0,0,0;False;3;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;955;1632,-1264;Inherit;False;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.WireNode;956;1616,-1232;Inherit;False;1;0;COLOR;0,0,0,0;False;1;COLOR;0\nNode;AmplifyShaderEditor.CustomExpressionNode;965;352,-400;Inherit;False;bakeDiffuseLighting
    = 0@$backBakeDiffuseLighting = 0@$#if defined( SHADER_STAGE_FRAGMENT ) || defined(
    SHADER_STAGE_RAY_TRACING )$    int width, height@$    float4 texSize@$    float3
    illuminance@$    float halfLambert@$    float backHalfLambert@$$    #if defined(
    LIGHTMAP_ON )$        #if defined( UNITY_DOTS_INSTANCING_ENABLED )$           
    #define LM_NAME unity_Lightmaps$            #define LM_SAMPLER samplerunity_Lightmaps$           
    #define LM_SLICE unity_LightmapIndex.x$        #else$            #define LM_NAME
    unity_Lightmap$            #define LM_SAMPLER samplerunity_Lightmap$           
    #define LM_SLICE 0$        #endif$$        #ifdef UNITY_LIGHTMAP_FULL_HDR$           
    bool encodedLightmap = false@$        #else$            bool encodedLightmap
    = true@$        #endif$$        LIGHTMAP_NAME.GetDimensions( width, height )@$       
    texSize = float4( width, height, 1.0 / float2( width, height ) )@$$        float4
    encodedIlluminance = SampleTexture2DBicubic( LM_NAME, LM_SAMPLER, staticUV, texSize,
    float2( 1, 1 ), LM_SLICE ).rgba@$        illuminance = encodedLightmap ? DecodeLightmap(
    encodedIlluminance, decodeInstructions ) : encodedIlluminance.rgb@$$        #if
    defined( DIRLIGHTMAP_COMBINED )$            halfLambert = dot( normalWS, staticDir.xyz
    - 0.5 ) + 0.5@$            bakeDiffuseLighting += illuminance * halfLambert /
    max( 1e-4, staticDir.w )@$$            backHalfLambert = dot( backNormalWS, staticDir.xyz
    - 0.5 ) + 0.5@$            backBakeDiffuseLighting += illuminance * backHalfLambert
    / max( 1e-4, staticDir.w )@$        #else$            bakeDiffuseLighting +=
    illuminance@$            backBakeDiffuseLighting += illuminance@$        #endif$   
    #endif$$    #if defined( DYNAMICLIGHTMAP_ON )$        unity_DynamicLightmap.GetDimensions(
    width, height )@$        texSize = float4( width, height, 1.0 / float2( width,
    height ) )@$$        illuminance = SampleTexture2DBicubic( unity_DynamicLightmap,
    samplerunity_DynamicLightmap, dynamicUV, texSize, float2( 1, 1 ), 0 ).rgb@$$       
    #if defined( DIRLIGHTMAP_COMBINED )$            halfLambert = dot( normalWS,
    dynamicDir.xyz - 0.5 ) + 0.5@$            bakeDiffuseLighting += illuminance
    * halfLambert / max( 1e-4, dynamicDir.w )@$$            backHalfLambert = dot(
    backNormalWS, dynamicDir.xyz - 0.5 ) + 0.5@$            backBakeDiffuseLighting
    += illuminance * backHalfLambert / max( 1e-4, dynamicDir.w )@$        #else$           
    bakeDiffuseLighting += illuminance@$            backBakeDiffuseLighting += illuminance@$       
    #endif$    #endif$$#endif$return@;7;Create;9;True;normalWS;FLOAT3;0,0,0;In;;Inherit;False;True;backNormalWS;FLOAT3;0,0,0;In;;Inherit;False;True;staticUV;FLOAT2;0,0;In;;Inherit;False;True;dynamicUV;FLOAT2;0,0;In;;Inherit;False;True;bakeDiffuseLighting;FLOAT3;0,0,0;Out;;Inherit;False;True;backBakeDiffuseLighting;FLOAT3;0,0,0;Out;;Inherit;False;True;decodeInstructions;FLOAT4;0,0,0,0;In;;Inherit;False;True;staticDir;FLOAT4;0,0,0,0;In;;Inherit;False;True;dynamicDir;FLOAT4;0,0,0,0;In;;Inherit;False;Sample
    Lightmap Bicubic;False;False;0;;False;10;0;FLOAT;0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT3;0,0,0;False;6;FLOAT3;0,0,0;False;7;FLOAT4;0,0,0,0;False;8;FLOAT4;0,0,0,0;False;9;FLOAT4;0,0,0,0;False;3;FLOAT;0;FLOAT3;6;FLOAT3;7\nNode;AmplifyShaderEditor.WireNode;806;384,-1456;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.LerpOp;941;3008,-736;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;942;3024,-464;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionNode;340;560,-1440;Inherit;False;Bicubic
    Sample;-1;;6;ce0e14d5ad5eac645b2e5892ab3506ff;2,92,2,72,2;7;99;SAMPLER2D;0;False;91;SAMPLER2DARRAY;0;False;93;FLOAT;0;False;97;FLOAT2;0,0;False;198;FLOAT4;0,0,0,0;False;199;FLOAT2;0,0;False;94;SAMPLERSTATE;0;False;5;COLOR;86;FLOAT;84;FLOAT;85;FLOAT;82;FLOAT;83\nNode;AmplifyShaderEditor.LerpOp;813;1008,-1456;Inherit;False;3;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;FLOAT;0;False;1;COLOR;0\nNode;AmplifyShaderEditor.WireNode;818;416,-1472;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SamplerNode;810;496,-1664;Inherit;True;Property;_TextureSample20;Texture
    Sample 0;0;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;6;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT3;5\nNode;AmplifyShaderEditor.WireNode;922;416,-1056;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;923;400,-1008;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SamplerNode;911;496,-1232;Inherit;True;Property;_TextureSample21;Texture
    Sample 0;0;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;6;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT3;5\nNode;AmplifyShaderEditor.FunctionSwitch;910;1184,-1232;Inherit;False;Option;False;1;2;0;In
    0;In 1;Instance;839;10;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;COLOR;0,0,0,0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;COLOR;0\nNode;AmplifyShaderEditor.FunctionSwitchByPipeline;811;48,-480;Inherit;False;4;0;FLOAT4;0,0,0,0;False;3;FLOAT4;0,0,0,0;False;1;FLOAT4;0,0,0,0;False;2;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.CustomExpressionNode;984;-240,-368;Inherit;False;return
    float4(LIGHTMAP_HDR_MULTIPLIER, LIGHTMAP_HDR_EXPONENT, 0, 0)@;4;Create;0;URPDecodeInstruction;True;False;0;;False;0;1;FLOAT4;0\nNode;AmplifyShaderEditor.CustomExpressionNode;966;-432,-176;Inherit;False;#if
    defined( DIRLIGHTMAP_COMBINED ) && ( defined( SHADER_STAGE_FRAGMENT ) || defined(
    SHADER_STAGE_RAY_TRACING ) )$$    #if defined( LIGHTMAP_ON )$        #if defined(
    UNITY_DOTS_INSTANCING_ENABLED )$            #define LM_IND_NAME unity_LightmapsInd$           
    #define LM_SAMPLER samplerunity_Lightmaps$            #define LM_EXTRA_ARGS staticUV,
    unity_LightmapIndex.x$        #else$            #define LM_IND_NAME unity_LightmapInd$           
    #define LM_SAMPLER samplerunity_Lightmap$            #define LM_EXTRA_ARGS staticUV$       
    #endif$        dynamicDir = SAMPLE_TEXTURE2D_LIGHTMAP( LM_IND_NAME, LM_SAMPLER,
    LM_EXTRA_ARGS )@$    #endif$$    #if defined( DYNAMICLIGHTMAP_ON )$        staticDir
    = SAMPLE_TEXTURE2D_LIGHTMAP( unity_DynamicDirectionality, samplerunity_DynamicLightmap,
    dynamicUV )@$    #endif$$#endif;7;Create;4;True;staticUV;FLOAT2;0,0;In;;Inherit;False;True;dynamicUV;FLOAT2;0,0;In;;Inherit;False;True;staticDir;FLOAT4;0,0,0,0;Out;;Inherit;False;True;dynamicDir;FLOAT4;0,0,0,0;Out;;Inherit;False;Sample
    Direction Bilinear;False;False;0;;False;5;0;FLOAT;0;False;1;FLOAT2;0,0;False;2;FLOAT2;0,0;False;3;FLOAT4;0,0,0,0;False;4;FLOAT4;0,0,0,0;False;3;FLOAT;0;FLOAT4;4;FLOAT4;5\nNode;AmplifyShaderEditor.CustomExpressionNode;961;-432,-48;Inherit;False;#if
    defined( DIRLIGHTMAP_COMBINED ) && ( defined( SHADER_STAGE_FRAGMENT ) || defined(
    SHADER_STAGE_RAY_TRACING ) )$    int width, height@$    float4 texSize@$$   
    #if defined( LIGHTMAP_ON )$        #if defined( UNITY_DOTS_INSTANCING_ENABLED
    )$            #define LM_IND_NAME unity_LightmapsInd$            #define LM_SAMPLER
    samplerunity_Lightmaps$            #define LM_SLICE unity_LightmapIndex.x$       
    #else$            #define LM_IND_NAME unity_LightmapInd$            #define LM_SAMPLER
    samplerunity_Lightmap$            #define LM_SLICE 0$        #endif$$       
    LM_IND_NAME.GetDimensions( width, height )@$        texSize = float4( width,
    height, 1.0 / float2( width, height ) )@$$        staticDir = SampleTexture2DBicubic(
    LM_IND_NAME, LM_SAMPLER, staticUV, texSize, float2( 1, 1 ), LM_SLICE )@$    #endif$$   
    #if defined( DYNAMICLIGHTMAP_ON )$        unity_DynamicDirectionality.GetDimensions(
    width, height )@$        texSize = float4( width, height, 1.0 / float2( width,
    height ) )@$$        dynamicDir = SampleTexture2DBicubic( unity_DynamicDirectionality,
    samplerunity_DynamicLightmap, dynamicUV, texSize, float2( 1, 1 ), 0 )@$    #endif$$#endif;7;Create;4;True;staticUV;FLOAT2;0,0;In;;Inherit;False;True;dynamicUV;FLOAT2;0,0;In;;Inherit;False;True;staticDir;FLOAT4;0,0,0,0;Out;;Inherit;False;True;dynamicDir;FLOAT4;0,0,0,0;Out;;Inherit;False;Sample
    Direction Bicubic;False;False;0;;False;5;0;FLOAT;0;False;1;FLOAT2;0,0;False;2;FLOAT2;0,0;False;3;FLOAT4;0,0,0,0;False;4;FLOAT4;0,0,0,0;False;3;FLOAT;0;FLOAT4;4;FLOAT4;5\nNode;AmplifyShaderEditor.Vector4Node;936;-896,-416;Float;False;Constant;_Vector3;Vector
    3;3;0;Create;True;0;0;0;False;0;False;34.49324,2.2,0,0;0,0,0,0;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.Vector4Node;937;-896,-592;Float;False;Constant;_Vector2;Vector
    2;3;0;Create;True;0;0;0;False;0;False;2,2.2,0,0;0,0,0,0;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.Vector4Node;960;-496,-368;Float;False;Constant;_Vector1;Vector
    1;3;0;Create;True;0;0;0;False;0;False;0,0,0,0;0,0,0,0;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.CustomExpressionNode;964;352,-656;Inherit;False;bakeDiffuseLighting
    = 0@$backBakeDiffuseLighting = 0@$#if defined( SHADER_STAGE_FRAGMENT ) || defined(
    SHADER_STAGE_RAY_TRACING )$    float3 illuminance@$    float halfLambert@$   
    float backHalfLambert@$$    #if defined( LIGHTMAP_ON )$        #if defined( UNITY_DOTS_INSTANCING_ENABLED
    )$            #define LM_NAME unity_Lightmaps$            #define LM_SAMPLER
    samplerunity_Lightmaps$            #define LM_EXTRA_ARGS staticUV, unity_LightmapIndex.x$       
    #else$            #define LM_NAME unity_Lightmap$            #define LM_SAMPLER
    samplerunity_Lightmap$            #define LM_EXTRA_ARGS staticUV$        #endif$$       
    #ifdef UNITY_LIGHTMAP_FULL_HDR$            bool encodedLightmap = false@$       
    #else$            bool encodedLightmap = true@$        #endif$$        float4
    encodedIlluminance = SAMPLE_TEXTURE2D_LIGHTMAP( LM_NAME, LM_SAMPLER, LM_EXTRA_ARGS
    ).rgba@$        illuminance = encodedLightmap ? DecodeLightmap( encodedIlluminance,
    decodeInstructions ) : encodedIlluminance.rgb@$$        #if defined( DIRLIGHTMAP_COMBINED
    )$            halfLambert = dot( normalWS, staticDir.xyz - 0.5 ) + 0.5@$           
    bakeDiffuseLighting += illuminance * halfLambert / max( 1e-4, staticDir.w )@$$           
    backHalfLambert = dot( backNormalWS, staticDir.xyz - 0.5 ) + 0.5@$           
    backBakeDiffuseLighting += illuminance * backHalfLambert / max( 1e-4, staticDir.w
    )@$        #else$            bakeDiffuseLighting += illuminance@$           
    backBakeDiffuseLighting += illuminance@$        #endif$    #endif$$    #if defined(
    DYNAMICLIGHTMAP_ON )$        illuminance = SAMPLE_TEXTURE2D_LIGHTMAP( unity_DynamicLightmap,
    samplerunity_DynamicLightmap, dynamicUV ).rgb@$$        #if defined( DIRLIGHTMAP_COMBINED
    )$            halfLambert = dot( normalWS, dynamicDir.xyz - 0.5 ) + 0.5@$           
    bakeDiffuseLighting += illuminance * halfLambert / max( 1e-4, dynamicDir.w )@$$           
    backHalfLambert = dot( backNormalWS, dynamicDir.xyz - 0.5 ) + 0.5@$           
    backBakeDiffuseLighting += illuminance * backHalfLambert / max( 1e-4, dynamicDir.w
    )@$        #else$            bakeDiffuseLighting += illuminance@$           
    backBakeDiffuseLighting += illuminance@$        #endif$    #endif$$#endif$return@;7;Create;9;True;normalWS;FLOAT3;0,0,0;In;;Inherit;False;True;backNormalWS;FLOAT3;0,0,0;In;;Inherit;False;True;staticUV;FLOAT2;0,0;In;;Inherit;False;True;dynamicUV;FLOAT2;0,0;In;;Inherit;False;True;bakeDiffuseLighting;FLOAT3;0,0,0;Out;;Inherit;False;True;backBakeDiffuseLighting;FLOAT3;0,0,0;Out;;Inherit;False;True;decodeInstructions;FLOAT4;0,0,0,0;In;;Inherit;False;True;staticDir;FLOAT4;0,0,0,0;In;;Inherit;False;True;dynamicDir;FLOAT4;0,0,0,0;In;;Inherit;False;Sample
    Lightmap Bilinear;False;False;0;;False;10;0;FLOAT;0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT3;0,0,0;False;6;FLOAT3;0,0,0;False;7;FLOAT4;0,0,0,0;False;8;FLOAT4;0,0,0,0;False;9;FLOAT4;0,0,0,0;False;3;FLOAT;0;FLOAT3;6;FLOAT3;7\nNode;AmplifyShaderEditor.FunctionNode;332;208,-1408;Inherit;False;Bicubic
    Precompute;-1;;7;818835145cc522e4da1f9915d8b8a984;0;2;5;FLOAT2;0,0;False;55;FLOAT4;0,0,0,0;False;2;FLOAT4;34;FLOAT2;54\nNode;AmplifyShaderEditor.FunctionNode;339;560,-992;Inherit;False;Bicubic
    Sample;-1;;10;ce0e14d5ad5eac645b2e5892ab3506ff;2,92,2,72,2;7;99;SAMPLER2D;0;False;91;SAMPLER2DARRAY;0;False;93;FLOAT;0;False;97;FLOAT2;0,0;False;198;FLOAT4;0,0,0,0;False;199;FLOAT2;0,0;False;94;SAMPLERSTATE;0;False;5;COLOR;86;FLOAT;84;FLOAT;85;FLOAT;82;FLOAT;83\nNode;AmplifyShaderEditor.FunctionNode;333;208,-960;Inherit;False;Bicubic
    Precompute;-1;;11;818835145cc522e4da1f9915d8b8a984;0;2;5;FLOAT2;0,0;False;55;FLOAT4;0,0,0,0;False;2;FLOAT4;34;FLOAT2;54\nNode;AmplifyShaderEditor.CustomExpressionNode;1015;-48,-1408;Inherit;False;int
    width = 1, height = 1@$#if !defined( SHADER_TARGET_SURFACE_ANALYSIS )$unity_Lightmap.GetDimensions(
    width, height )@$#endif$TexelSize = float4( 1.0 / float2( width, height ), width,
    height )@;7;Create;1;True;TexelSize;FLOAT4;0,0,0,0;Out;;Inherit;False;Lightmap
    Texel Size;False;False;0;;False;2;0;FLOAT;0;False;1;FLOAT4;0,0,0,0;False;2;FLOAT;0;FLOAT4;2\nNode;AmplifyShaderEditor.FunctionSwitch;827;64,-160;Inherit;False;Option;False;1;2;0;In
    0;In 1;Instance;839;10;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0,0,0,0;False;2;FLOAT4;0,0,0,0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.TexturePropertyNode;819;-112,-1680;Float;True;Global;unity_Lightmap;unity_Lightmap;2;0;Fetch;True;0;0;0;False;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.TexturePropertyNode;920;-96,-1232;Float;True;Global;unity_LightmapInd;unity_LightmapInd;3;0;Fetch;True;0;0;0;False;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.WireNode;1326;432,-1440;Inherit;False;1;0;SAMPLER2D;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.WireNode;1328;400,-1616;Inherit;False;1;0;SAMPLER2D;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.WireNode;1327;384,-1632;Inherit;False;1;0;SAMPLER2D;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.WireNode;1332;384,-1616;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;1329;400,-1600;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;1333;384,-1616;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;1335;400,-1600;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;1334;384,-1616;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;1337;400,-1600;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;1336;384,-1616;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;1338;448,-1200;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;1330;432,-1360;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;1339;464,-960;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;1341;432,-1168;Inherit;False;1;0;SAMPLER2D;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.WireNode;1340;400,-1200;Inherit;False;1;0;SAMPLER2D;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.WireNode;1342;464,-992;Inherit;False;1;0;SAMPLER2D;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.GetLocalVarNode;817;-48,-1488;Inherit;False;1247;UV1Static;1;0;OBJECT;;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.GetLocalVarNode;919;-64,-1040;Inherit;False;1251;UV2Dynamic;1;0;OBJECT;;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.CustomExpressionNode;1016;-64,-960;Inherit;False;int
    width = 1, height = 1@$#if !defined( SHADER_TARGET_SURFACE_ANALYSIS )$unity_LightmapInd.GetDimensions(
    width, height )@$#endif$TexelSize = float4( 1.0 / float2( width, height ), width,
    height )@;7;Call;1;True;TexelSize;FLOAT4;0,0,0,0;Out;;Inherit;False;LightmapInd
    Texel Size;False;False;0;;False;2;0;FLOAT;0;False;1;FLOAT4;0,0,0,0;False;2;FLOAT;0;FLOAT4;2\nNode;AmplifyShaderEditor.GetLocalVarNode;952;80,-320;Inherit;False;1247;UV1Static;1;0;OBJECT;;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.GetLocalVarNode;830;-672,-176;Inherit;False;1247;UV1Static;1;0;OBJECT;;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.FunctionSwitch;826;64,-16;Inherit;False;Option;False;1;2;0;In
    0;In 1;Instance;839;10;0;FLOAT4;0,0,0,0;False;1;FLOAT4;0,0,0,0;False;2;FLOAT4;0,0,0,0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.FunctionSwitch;838;1184,-384;Inherit;False;Option;False;1;2;0;In
    0;In 1;Instance;839;10;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitch;831;1184,-624;Inherit;False;Option;False;1;2;0;In
    0;In 1;Instance;839;10;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;1073;-400,-2864;Inherit;False;NormalWS;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;1063;-400,-2784;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;-1,-1,-1;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.ScaleAndOffsetNode;1255;-384,-2192;Inherit;False;3;0;FLOAT2;0,0;False;1;FLOAT2;1,0;False;2;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.TexCoordVertexDataNode;1256;-816,-2192;Inherit;False;2;2;0;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.TexCoordVertexDataNode;1257;-816,-2560;Inherit;False;1;2;0;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SwizzleNode;1217;-544,-2128;Inherit;False;FLOAT2;0;1;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SwizzleNode;1218;-544,-2048;Inherit;False;FLOAT2;2;3;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SwizzleNode;1216;-576,-2416;Inherit;False;FLOAT2;2;3;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SwizzleNode;1215;-576,-2496;Inherit;False;FLOAT2;0;1;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.Vector4Node;1258;-816,-2432;Float;False;Global;unity_LightmapST;unity_LightmapST;2;0;Fetch;True;0;0;0;False;0;False;0,0,0,0;1,1,0,0;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.Vector4Node;1259;-848,-2064;Float;False;Global;unity_DynamicLightmapST;unity_DynamicLightmapST;2;0;Fetch;True;0;0;0;False;0;False;0,0,0,0;1,1,0,0;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.FunctionInput;1246;-128,-2480;Inherit;False;UV1
    Lightmap Static ;2;1;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.RangedFloatNode;1001;768,-1776;Half;False;Property;_BakedGIFilterMode;Filter
    Mode;1;1;[Enum];Create;False;0;2;Bilinear;0;Bicubic;1;0;False;0;False;1;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;829;-672,0;Inherit;False;1251;UV2Dynamic;1;0;OBJECT;;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.RangedFloatNode;1000;2736,-800;Inherit;False;Property;_BakedGIEnable;ENABLE
    BAKED GI;0;2;[Header];[ToggleUI];Create;False;1;BAKED GI;0;0;False;0;False;1;1;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;1322;128,-2560;Inherit;False;Custom
    Inputs;True;0;2;1;In 0;In 1;Object;-1;10;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.FunctionSwitch;1323;128,-2192;Inherit;False;Option;False;0;2;1;In
    0;In 1;Instance;1322;10;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.FunctionInput;1250;-128,-2128;Inherit;False;UV2 
    Lightmap Dynamic;2;2;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;1247;384,-2560;Inherit;False;UV1Static;-1;True;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;1251;384,-2192;Inherit;False;UV2Dynamic;-1;True;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.CustomExpressionNode;963;1728,-1552;Float;False;Color
    = DecodeDirectionalLightmap( Color,DirTex,NormalWorld)@;7;Create;3;True;Color;FLOAT3;0,0,0;InOut;;Float;False;True;DirTex;FLOAT4;0,0,0,0;In;;Float;False;True;NormalWorld;FLOAT3;0,0,0;In;;Float;False;ASEDecodeDirectionalLightmap;False;False;0;;False;4;0;FLOAT;0;False;1;FLOAT3;0,0,0;False;2;FLOAT4;0,0,0,0;False;3;FLOAT3;0,0,0;False;2;FLOAT;0;FLOAT3;2\nNode;AmplifyShaderEditor.StaticSwitch;983;-656,-480;Float;False;Property;_Keyword2;Keyword
    2;2;0;Create;True;0;0;0;False;0;False;0;0;0;False;UNITY_LIGHTMAP_RGBM_ENCODING;Toggle;2;Key0;Key1;Fetch;True;True;All;9;1;FLOAT4;0,0,0,0;False;0;FLOAT4;0,0,0,0;False;2;FLOAT4;0,0,0,0;False;3;FLOAT4;0,0,0,0;False;4;FLOAT4;0,0,0,0;False;5;FLOAT4;0,0,0,0;False;6;FLOAT4;0,0,0,0;False;7;FLOAT4;0,0,0,0;False;8;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.StaticSwitch;982;-288,-480;Float;False;Property;_Keyword0;Keyword
    0;1;0;Create;True;0;0;0;False;0;False;0;0;0;False;UNITY_LIGHTMAP_FULL_HDR;Toggle;2;Key0;Key1;Fetch;True;True;All;9;1;FLOAT4;0,0,0,0;False;0;FLOAT4;0,0,0,0;False;2;FLOAT4;0,0,0,0;False;3;FLOAT4;0,0,0,0;False;4;FLOAT4;0,0,0,0;False;5;FLOAT4;0,0,0,0;False;6;FLOAT4;0,0,0,0;False;7;FLOAT4;0,0,0,0;False;8;FLOAT4;0,0,0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.ScaleAndOffsetNode;1219;-400,-2560;Inherit;False;3;0;FLOAT2;0,0;False;1;FLOAT2;1,0;False;2;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.GetLocalVarNode;950;1456,-1472;Inherit;False;1073;NormalWS;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;1101;-224,-2784;Inherit;False;BackNormalWS;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WorldNormalVector;1070;-656,-2864;Inherit;False;False;1;0;FLOAT3;0,0,1;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.RegisterLocalVarNode;959;992,-1776;Inherit;False;FilterMode;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;839;1184,-1664;Inherit;False;Baked
    GI Direction;False;1;3;0;Bilinear;Bicubic;Switch;Object;-1;10;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;COLOR;0,0,0,0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;COLOR;0\nNode;AmplifyShaderEditor.SwizzleNode;840;1456,-1664;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.StaticSwitch;925;2048,-1664;Float;False;Property;_Keyword1;Keyword
    0;1;0;Create;True;0;0;0;False;0;False;0;0;0;False;DIRLIGHTMAP_COMBINED;Toggle;2;Key0;Key1;Fetch;True;True;All;9;1;FLOAT3;0,0,0;False;0;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;3;FLOAT3;0,0,0;False;4;FLOAT3;0,0,0;False;5;FLOAT3;0,0,0;False;6;FLOAT3;0,0,0;False;7;FLOAT3;0,0,0;False;8;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionInput;975;-832,-2864;Inherit;False;Normal;3;0;False;1;0;FLOAT3;0,0,1;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;951;80,-240;Inherit;False;1251;UV2Dynamic;1;0;OBJECT;;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.GetLocalVarNode;953;48,-576;Inherit;False;1101;BackNormalWS;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;954;80,-656;Inherit;False;1073;NormalWS;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.Exposure;1343;2025.08,-468.7105;Inherit;False;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;1344;2176,-640;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;1345;2176,-400;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionOutput;948;3184,-736;Inherit;False;True;-1;Baked
    GI;0;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionOutput;949;3184,-464;Inherit;False;False;-1;Baked
    GI Back;1;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nWireConnection;824;0;966;5\nWireConnection;824;1;961;5\nWireConnection;824;2;828;0\nWireConnection;812;0;823;0\nWireConnection;820;0;1001;0\nWireConnection;821;0;1001;0\nWireConnection;822;0;1001;0\nWireConnection;823;0;1001;0\nWireConnection;825;0;966;4\nWireConnection;825;1;961;4\nWireConnection;825;2;828;0\nWireConnection;832;0;965;6\nWireConnection;833;0;964;6\nWireConnection;833;1;965;6\nWireConnection;833;2;834;0\nWireConnection;834;0;821;0\nWireConnection;835;0;964;7\nWireConnection;836;0;964;7\nWireConnection;836;1;965;7\nWireConnection;836;2;837;0\nWireConnection;837;0;820;0\nWireConnection;909;0;840;0\nWireConnection;912;0;911;0\nWireConnection;912;1;339;86\nWireConnection;912;2;913;0\nWireConnection;913;0;822;0\nWireConnection;924;0;955;0\nWireConnection;926;0;925;0\nWireConnection;927;0;925;0\nWireConnection;928;0;925;0\nWireConnection;929;0;925;0\nWireConnection;930;0;928;0\nWireConnection;931;0;929;0\nWireConnection;932;0;926;0\nWireConnection;933;0;927;0\nWireConnection;934;0;933;0\nWireConnection;934;3;932;0\nWireConnection;934;1;1345;0\nWireConnection;934;2;1345;0\nWireConnection;935;0;931;0\nWireConnection;935;3;930;0\nWireConnection;935;1;1344;0\nWireConnection;935;2;1344;0\nWireConnection;955;0;956;0\nWireConnection;956;0;910;0\nWireConnection;965;1;954;0\nWireConnection;965;2;953;0\nWireConnection;965;3;952;0\nWireConnection;965;4;951;0\nWireConnection;965;7;811;0\nWireConnection;965;8;827;0\nWireConnection;965;9;826;0\nWireConnection;806;0;817;0\nWireConnection;941;1;935;0\nWireConnection;941;2;1000;0\nWireConnection;942;1;934;0\nWireConnection;942;2;1000;0\nWireConnection;340;99;1326;0\nWireConnection;340;198;332;34\nWireConnection;340;199;332;54\nWireConnection;340;94;1330;0\nWireConnection;813;0;810;0\nWireConnection;813;1;340;86\nWireConnection;813;2;812;0\nWireConnection;818;0;806;0\nWireConnection;810;0;819;0\nWireConnection;810;1;818;0\nWireConnection;810;7;1333;0\nWireConnection;922;0;923;0\nWireConnection;923;0;919;0\nWireConnection;911;0;920;0\nWireConnection;911;1;922;0\nWireConnection;911;7;1338;0\nWireConnection;910;0;911;0\nWireConnection;910;1;339;86\nWireConnection;910;2;912;0\nWireConnection;811;0;982;0\nWireConnection;811;3;982;0\nWireConnection;811;1;984;0\nWireConnection;811;2;982;0\nWireConnection;966;1;830;0\nWireConnection;966;2;829;0\nWireConnection;961;1;830;0\nWireConnection;961;2;829;0\nWireConnection;964;1;954;0\nWireConnection;964;2;953;0\nWireConnection;964;3;952;0\nWireConnection;964;4;951;0\nWireConnection;964;7;811;0\nWireConnection;964;8;827;0\nWireConnection;964;9;826;0\nWireConnection;332;5;817;0\nWireConnection;332;55;1015;2\nWireConnection;339;99;1342;0\nWireConnection;339;198;333;34\nWireConnection;339;199;333;54\nWireConnection;339;94;1339;0\nWireConnection;333;5;919;0\nWireConnection;333;55;1016;2\nWireConnection;827;0;966;4\nWireConnection;827;1;961;4\nWireConnection;827;2;825;0\nWireConnection;1326;0;1328;0\nWireConnection;1328;0;1327;0\nWireConnection;1327;0;819;0\nWireConnection;1332;0;819;1\nWireConnection;1329;0;1332;0\nWireConnection;1333;0;819;1\nWireConnection;1335;0;1334;0\nWireConnection;1334;0;819;1\nWireConnection;1337;0;1336;0\nWireConnection;1336;0;819;1\nWireConnection;1338;0;1335;0\nWireConnection;1330;0;1329;0\nWireConnection;1339;0;1337;0\nWireConnection;1341;0;1340;0\nWireConnection;1340;0;920;0\nWireConnection;1342;0;1341;0\nWireConnection;826;0;966;5\nWireConnection;826;1;961;5\nWireConnection;826;2;824;0\nWireConnection;838;0;835;0\nWireConnection;838;1;965;7\nWireConnection;838;2;836;0\nWireConnection;831;0;964;6\nWireConnection;831;1;832;0\nWireConnection;831;2;833;0\nWireConnection;1073;0;1070;0\nWireConnection;1063;0;1070;0\nWireConnection;1255;0;1256;0\nWireConnection;1255;1;1217;0\nWireConnection;1255;2;1218;0\nWireConnection;1217;0;1259;0\nWireConnection;1218;0;1259;0\nWireConnection;1216;0;1258;0\nWireConnection;1215;0;1258;0\nWireConnection;1246;0;1219;0\nWireConnection;1322;0;1219;0\nWireConnection;1322;1;1246;0\nWireConnection;1323;0;1255;0\nWireConnection;1323;1;1250;0\nWireConnection;1250;0;1255;0\nWireConnection;1247;0;1322;0\nWireConnection;1251;0;1323;0\nWireConnection;963;1;909;0\nWireConnection;963;2;924;0\nWireConnection;963;3;950;0\nWireConnection;983;1;937;0\nWireConnection;983;0;936;0\nWireConnection;982;1;983;0\nWireConnection;982;0;960;0\nWireConnection;1219;0;1257;0\nWireConnection;1219;1;1215;0\nWireConnection;1219;2;1216;0\nWireConnection;1101;0;1063;0\nWireConnection;1070;0;975;0\nWireConnection;959;0;1001;0\nWireConnection;839;0;810;0\nWireConnection;839;1;340;86\nWireConnection;839;2;813;0\nWireConnection;840;0;839;0\nWireConnection;925;1;840;0\nWireConnection;925;0;963;2\nWireConnection;1344;0;831;0\nWireConnection;1344;1;1343;0\nWireConnection;1345;0;838;0\nWireConnection;1345;1;1343;0\nWireConnection;948;0;941;0\nWireConnection;949;0;942;0\nASEEND*/\n//CHKSM=4E5909F499866B7D17C23D9203706EB61E807C95"
  m_functionName: 
  m_description: 'Amplify Shader Pack Example

    -- Baked GI


'
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 0
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_customEdited: 0
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 0
  m_headerStyle: 2
  m_headerColor: {r: 0.7529412, g: 0.6784314, b: 0, a: 1}
  m_customNodeCategory: Amplify Shader Pack
  m_previewPosition: 0
  m_hidden: 0
  m_url: 
