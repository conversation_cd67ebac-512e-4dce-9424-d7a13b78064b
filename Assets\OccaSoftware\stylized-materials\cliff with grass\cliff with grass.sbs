<?xml version="1.0" encoding="UTF-8"?><package><identifier v="Unsaved Package"/><formatVersion v="1.1.0.202201"/><updaterVersion v="1.1.0.202201"/><fileUID v="{84b9ec4e-af4c-4574-952a-e3d014b1549e}"/><versionUID v="0"/><dependencies><dependency><filename v="sbs://pattern_tile_sampler.sbs"/><uid v="1486812999"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://edge_detect.sbs"/><uid v="1487424625"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_crystal_1.sbs"/><uid v="1486811282"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://blur_hq.sbs"/><uid v="1486811589"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://flood_fill_2.sbs"/><uid v="1323881949"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://slope_blur.sbs"/><uid v="1487497056"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_perlin_noise.sbs"/><uid v="1487497977"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_creased.sbs"/><uid v="1487699857"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_clouds_2.sbs"/><uid v="1487700427"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://hbao_2.sbs"/><uid v="1297162667"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://gradient_linear_1.sbs"/><uid v="1482654803"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://histogram_scan.sbs"/><uid v="1487772706"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_gaussian_noise.sbs"/><uid v="1486875707"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://pbr_base_material.sbs"/><uid v="1487822363"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://pattern_shape.sbs"/><uid v="1311189026"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://trapezoid_transform.sbs"/><uid v="1486830014"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://curvature_smooth.sbs"/><uid v="1487705407"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency></dependencies><content><graph><identifier v="cliff_with_dirt"/><uid v="1487420008"/><graphtype v="material"/><graphOutputs><graphoutput><identifier v="basecolor"/><uid v="1213284336"/><attributes><label v="Base Color"/></attributes><usages><usage><components v="RGBA"/><name v="baseColor"/></usage></usages><group v="Material"/></graphoutput><graphoutput><identifier v="normal"/><uid v="1213284338"/><attributes><label v="Normal"/></attributes><usages><usage><components v="RGBA"/><name v="normal"/></usage></usages><group v="Material"/></graphoutput><graphoutput><identifier v="roughness"/><uid v="1213284340"/><attributes><label v="Roughness"/></attributes><usages><usage><components v="RGBA"/><name v="roughness"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput><graphoutput><identifier v="height"/><uid v="1279137031"/><attributes><label v="Height"/></attributes><usages><usage><components v="RGBA"/><name v="height"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput><graphoutput><identifier v="ambientocclusion"/><uid v="1359211721"/><attributes><label v="Ambient Occlusion"/></attributes><usages><usage><components v="RGBA"/><name v="ambientOcclusion"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput></graphOutputs><compNodes><compNode><uid v="1213284337"/><GUILayout><gpos v="1518.66541 -336 0"/></GUILayout><connections><connection><identifier v="inputNodeOutput"/><connRef v="1487861414"/><connRefOutput v="1487861415"/></connection></connections><compImplementation><compOutputBridge><output v="1213284336"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1213284339"/><GUILayout><gpos v="1518.66541 -144 0"/></GUILayout><connections><connection><identifier v="inputNodeOutput"/><connRef v="1359211383"/><connRefOutput v="1359211384"/></connection></connections><compImplementation><compOutputBridge><output v="1213284338"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1213284341"/><GUILayout><gpos v="1520 144 0"/></GUILayout><connections><connection><identifier v="inputNodeOutput"/><connRef v="1487437633"/><connRefOutput v="1487437634"/></connection></connections><compImplementation><compOutputBridge><output v="1213284340"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1279137030"/><GUILayout><gpos v="1518.66541 784 0"/></GUILayout><connections><connection><identifier v="inputNodeOutput"/><connRef v="1487437633"/><connRefOutput v="1487437634"/></connection></connections><compImplementation><compOutputBridge><output v="1279137031"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1359211383"/><GUILayout><gpos v="1052.26697 -144 0"/></GUILayout><compOutputs><compOutput><uid v="1359211384"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487437633"/><connRefOutput v="1487437634"/></connection></connections><compImplementation><compFilter><filter v="normal"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="10"/></paramValue></parameter><parameter><name v="input2alpha"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1359211720"/><GUILayout><gpos v="1518.66541 560 0"/></GUILayout><connections><connection><identifier v="inputNodeOutput"/><connRef v="1487703978"/><connRefOutput v="1487703979"/></connection></connections><compImplementation><compOutputBridge><output v="1359211721"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1487424389"/><GUILayout><gpos v="-4926.50195 -447.956543 0"/></GUILayout><compOutputs><compOutput><uid v="1487424390"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1486812999"/><parameters><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="11"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="9"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="5.01999998"/></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="4.71999979"/></paramValue></parameter><parameter><name v="color_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.610000014"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487424390"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487424666"/><GUILayout><gpos v="-4432.42822 -432 0"/></GUILayout><compOutputs><compOutput><uid v="1487424667"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input"/><connRef v="1487424909"/><connRefOutput v="1487424910"/></connection></connections><compImplementation><compInstance><path v="pkg:///edge_detect?dependency=1487424625"/><parameters><parameter><name v="edge_width"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="3.67999983"/></paramValue></parameter><parameter><name v="edge_roundness"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.34000003"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487424667"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487424909"/><GUILayout><gpos v="-4607.58203 -432 0"/></GUILayout><compOutputs><compOutput><uid v="1487424910"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487424389"/><connRefOutput v="1487424390"/></connection><connection><identifier v="inputintensity"/><connRef v="1487432349"/><connRefOutput v="1487432350"/></connection></connections><compImplementation><compFilter><filter v="directionalwarp"/><parameters><parameter><name v="warpangle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.162055552"/></paramValue></parameter><parameter><name v="randomseed"/><relativeTo v="2"/><paramValue><constantValueInt32 v="10"/></paramValue></parameter><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="31.2999992"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487424943"/><GUILayout><gpos v="-4880.42822 -208 0"/></GUILayout><compOutputs><compOutput><uid v="1487424944"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///crystal_1?dependency=1486811282"/><parameters><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueInt32 v="7"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487424944"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487425265"/><GUILayout><gpos v="-4304.42822 -432 0"/></GUILayout><compOutputs><compOutput><uid v="1487425266"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="Source"/><connRef v="1487424666"/><connRefOutput v="1487424667"/></connection></connections><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1486811589"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="3.11999989"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487425266"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487432349"/><GUILayout><gpos v="-4720.42822 -208 0"/></GUILayout><compOutputs><compOutput><uid v="1487432350"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="Source"/><connRef v="1487424943"/><connRefOutput v="1487424944"/></connection></connections><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1486811589"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="3.06999993"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487432350"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487436181"/><GUILayout><gpos v="-4016.42822 -415.07608 0"/></GUILayout><compOutputs><compOutput><uid v="1487436182"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="mask"/><connRef v="1487425265"/><connRefOutput v="1487425266"/></connection></connections><compImplementation><compFilter><filter v="distance"/><parameters><parameter><name v="colorswitch"/><paramValue><constantValueBool v="0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487442222"/><GUILayout><gpos v="-4208.42822 -207.339111 0"/></GUILayout><compOutputs><compOutput><uid v="1487442223"/><comptype v="1"/></compOutput><compOutput><uid v="1487442224"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="mask"/><connRef v="1487425265"/><connRefOutput v="1487425266"/></connection></connections><compImplementation><compInstance><path v="pkg:///flood_fill?dependency=1323881949"/><parameters/><outputBridgings><outputBridging><uid v="1487442223"/><identifier v="output"/></outputBridging><outputBridging><uid v="1487442224"/><identifier v="referenceMap"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487442502"/><GUILayout><gpos v="-4063.26392 -190.949585 0"/></GUILayout><compOutputs><compOutput><uid v="1487442503"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input"/><connRef v="1487442222"/><connRefOutput v="1487442223"/></connection></connections><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487442503"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487442713"/><GUILayout><gpos v="-3920.42822 -204.359222 0"/></GUILayout><compOutputs><compOutput><uid v="1487442714"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="source"/><connRef v="1487442502"/><connRefOutput v="1487442503"/></connection><connection><identifier v="mask"/><connRef v="1487425265"/><connRefOutput v="1487425266"/></connection></connections><compImplementation><compFilter><filter v="distance"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1487442832"/><GUILayout><gpos v="-4063.26392 16 0"/></GUILayout><compOutputs><compOutput><uid v="1487442503"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input"/><connRef v="1487442833"/><connRefOutput v="1487442223"/></connection></connections><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="angle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.305555552"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487442503"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487442833"/><GUILayout><gpos v="-4208.42822 16 0"/></GUILayout><compOutputs><compOutput><uid v="1487442223"/><comptype v="1"/></compOutput><compOutput><uid v="1487442224"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="mask"/><connRef v="1487425265"/><connRefOutput v="1487425266"/></connection></connections><compImplementation><compInstance><path v="pkg:///flood_fill?dependency=1323881949"/><parameters/><outputBridgings><outputBridging><uid v="1487442223"/><identifier v="output"/></outputBridging><outputBridging><uid v="1487442224"/><identifier v="referenceMap"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487442834"/><GUILayout><gpos v="-3920.42822 16 0"/></GUILayout><compOutputs><compOutput><uid v="1487442714"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="source"/><connRef v="1487442832"/><connRefOutput v="1487442503"/></connection><connection><identifier v="mask"/><connRef v="1487425265"/><connRefOutput v="1487425266"/></connection></connections><compImplementation><compFilter><filter v="distance"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1487428995"/><GUILayout><gpos v="-3905.59253 240 0"/></GUILayout><compOutputs><compOutput><uid v="1487442714"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="source"/><connRef v="1487428996"/><connRefOutput v="1487442503"/></connection><connection><identifier v="mask"/><connRef v="1487425265"/><connRefOutput v="1487425266"/></connection></connections><compImplementation><compFilter><filter v="distance"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1487428996"/><GUILayout><gpos v="-4048.42822 240 0"/></GUILayout><compOutputs><compOutput><uid v="1487442503"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input"/><connRef v="1487428997"/><connRefOutput v="1487442223"/></connection></connections><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="angle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.36944443"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487442503"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487428997"/><GUILayout><gpos v="-4193.59277 240 0"/></GUILayout><compOutputs><compOutput><uid v="1487442223"/><comptype v="1"/></compOutput><compOutput><uid v="1487442224"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="mask"/><connRef v="1487425265"/><connRefOutput v="1487425266"/></connection></connections><compImplementation><compInstance><path v="pkg:///flood_fill?dependency=1323881949"/><parameters/><outputBridgings><outputBridging><uid v="1487442223"/><identifier v="output"/></outputBridging><outputBridging><uid v="1487442224"/><identifier v="referenceMap"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487429361"/><GUILayout><gpos v="-3632.42822 -417.088806 0"/></GUILayout><compOutputs><compOutput><uid v="1487429362"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="destination"/><connRef v="1487436181"/><connRefOutput v="1487436182"/></connection><connection><identifier v="source"/><connRef v="1487436402"/><connRefOutput v="1487436403"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487436402"/><GUILayout><gpos v="-3792.42822 -208 0"/></GUILayout><compOutputs><compOutput><uid v="1487436403"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487442713"/><connRefOutput v="1487442714"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.569164276 0.569164276 0.569164276 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487436540"/><GUILayout><gpos v="-3504.42822 -112 0"/></GUILayout><compOutputs><compOutput><uid v="1487436541"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="destination"/><connRef v="1487429361"/><connRefOutput v="1487429362"/></connection><connection><identifier v="source"/><connRef v="1487436721"/><connRefOutput v="1487436722"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487436721"/><GUILayout><gpos v="-3792.42822 16 0"/></GUILayout><compOutputs><compOutput><uid v="1487436722"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487442834"/><connRefOutput v="1487442714"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.240634009 0.240634009 0.240634009 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487436869"/><GUILayout><gpos v="-3312.42822 48 0"/></GUILayout><compOutputs><compOutput><uid v="1487436870"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="destination"/><connRef v="1487436540"/><connRefOutput v="1487436541"/></connection><connection><identifier v="source"/><connRef v="1487437073"/><connRefOutput v="1487437074"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487437073"/><GUILayout><gpos v="-3760.42822 240 0"/></GUILayout><compOutputs><compOutput><uid v="1487437074"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487428995"/><connRefOutput v="1487442714"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.427953899 0.427953899 0.427953899 0.5"/></paramValue></parameter><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.246397689 0.246397689 0.246397689 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487437633"/><GUILayout><gpos v="-994.368347 304 0"/></GUILayout><compOutputs><compOutput><uid v="1487437634"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487855332"/><connRefOutput v="1487855333"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1487497686"/><GUILayout><gpos v="-3088.42822 144 0"/></GUILayout><compOutputs><compOutput><uid v="1487497687"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="Source"/><connRef v="1487436869"/><connRefOutput v="1487436870"/></connection><connection><identifier v="Effect"/><connRef v="1487498286"/><connRefOutput v="1487498287"/></connection></connections><compImplementation><compInstance><path v="pkg:///slope_blur_grayscale_2?dependency=1487497056"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="32"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.449999988"/></paramValue></parameter><parameter><name v="mode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487497687"/><identifier v="Slope_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487498286"/><GUILayout><gpos v="-3102.50195 272 0"/></GUILayout><compOutputs><compOutput><uid v="1487498287"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///perlin_noise?dependency=1487497977"/><parameters><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueInt32 v="14"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487498287"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487499217"/><GUILayout><gpos v="-2864 -48 0"/></GUILayout><compOutputs><compOutput><uid v="1487499218"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="destination"/><connRef v="1487436869"/><connRefOutput v="1487436870"/></connection><connection><identifier v="source"/><connRef v="1487499314"/><connRefOutput v="1487499315"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487499314"/><GUILayout><gpos v="-2942.50195 144 0"/></GUILayout><compOutputs><compOutput><uid v="1487499315"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487497686"/><connRefOutput v="1487497687"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="randomseed"/><relativeTo v="2"/><paramValue><constantValueInt32 v="10"/></paramValue></parameter><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.5625 0.5625 0.5625 0.5"/></paramValue></parameter><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.18871595 0.18871595 0.18871595 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487690913"/><GUILayout><gpos v="-2173.39111 -48 0"/></GUILayout><compOutputs><compOutput><uid v="1487690914"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487499217"/><connRefOutput v="1487499218"/></connection><connection><identifier v="inputintensity"/><connRef v="1487499217"/><connRefOutput v="1487499218"/></connection></connections><compImplementation><compFilter><filter v="directionalwarp"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="10.4499998"/></paramValue></parameter><parameter><name v="warpangle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.159999996"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487693524"/><GUILayout><gpos v="-1981.39099 -48 0"/></GUILayout><compOutputs><compOutput><uid v="1487693525"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="destination"/><connRef v="1487690913"/><connRefOutput v="1487690914"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487694525"/><GUILayout><gpos v="-1153.60461 -848 0"/></GUILayout><compOutputs><compOutput><uid v="1487694526"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487693524"/><connRefOutput v="1487693525"/></connection></connections><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1487694620"/><paramsArrayCells><paramsArrayCell><uid v="1487868700"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.121739127 0.121739127 0.121739127 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487868701"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.953586459"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.886956513 0.886956513 0.886956513 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487868702"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.721518993"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.352173924 0.352173924 0.352173924 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487868703"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.400530517"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.508695662 0.50085175 0.493907988 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1487699725"/><GUILayout><gpos v="-2209.60474 -1104 0"/></GUILayout><compOutputs><compOutput><uid v="1487699726"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///perlin_noise?dependency=1487497977"/><parameters><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487699726"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487699760"/><GUILayout><gpos v="-2049.60474 -1072 0"/></GUILayout><compOutputs><compOutput><uid v="1487699761"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="Source"/><connRef v="1487699725"/><connRefOutput v="1487699726"/></connection><connection><identifier v="Effect"/><connRef v="1487700046"/><connRefOutput v="1487700047"/></connection></connections><compImplementation><compInstance><path v="pkg:///slope_blur_grayscale_2?dependency=1487497056"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="32"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487699761"/><identifier v="Slope_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487700046"/><GUILayout><gpos v="-2209.60474 -944 0"/></GUILayout><compOutputs><compOutput><uid v="1487700047"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///creased?dependency=1487699857"/><parameters><parameter><name v="warp_intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487700047"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487700388"/><GUILayout><gpos v="-1921.60461 -1072 0"/></GUILayout><compOutputs><compOutput><uid v="1487700389"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="Source"/><connRef v="1487699760"/><connRefOutput v="1487699761"/></connection><connection><identifier v="Effect"/><connRef v="1487700934"/><connRefOutput v="1487700935"/></connection></connections><compImplementation><compInstance><path v="pkg:///slope_blur_grayscale_2?dependency=1487497056"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="32"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="6.23999977"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487700389"/><identifier v="Slope_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487700934"/><GUILayout><gpos v="-2049.60474 -944 0"/></GUILayout><compOutputs><compOutput><uid v="1487700935"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///clouds_2?dependency=1487700427"/><parameters/><outputBridgings><outputBridging><uid v="1487700935"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487701253"/><GUILayout><gpos v="-1793.60461 -1072 0"/></GUILayout><compOutputs><compOutput><uid v="1487701254"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="Source"/><connRef v="1487700388"/><connRefOutput v="1487700389"/></connection></connections><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1486811589"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.689999998"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487701254"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487701428"/><GUILayout><gpos v="-1665.60461 -1072 0"/></GUILayout><compOutputs><compOutput><uid v="1487701429"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="Source"/><connRef v="1487701253"/><connRefOutput v="1487701254"/></connection><connection><identifier v="Effect"/><connRef v="1487701253"/><connRefOutput v="1487701254"/></connection></connections><compImplementation><compInstance><path v="pkg:///slope_blur_grayscale_2?dependency=1487497056"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="32"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="12.9899998"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487701429"/><identifier v="Slope_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487701488"/><GUILayout><gpos v="-1505.60461 -1072 0"/></GUILayout><compOutputs><compOutput><uid v="1487701489"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487701428"/><connRefOutput v="1487701429"/></connection></connections><compImplementation><compFilter><filter v="gradient"/><parameters><parameter><name v="addressingrepeat"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="colorswitch"/><paramValue><constantValueBool v="1"/></paramValue></parameter></parameters><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1487701548"/><paramsArrayCells><paramsArrayCell><uid v="1487701981"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.592156887 0.509803951 0.431372553 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487701982"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.0363128483"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.556862772 0.431372553 0.321568638 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487701983"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.0698323995"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.490196079 0.368627459 0.258823544 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487701984"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.078212291"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.635294139 0.505882382 0.392156869 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487701985"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.108938545"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.494117647 0.376470596 0.274509817 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487701986"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.201117307"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.600000024 0.494117647 0.403921574 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487701987"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.223463684"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.490196079 0.388235301 0.298039228 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487701988"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.237430155"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.576470613 0.482352942 0.396078438 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487701989"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.251396626"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.509803951 0.403921574 0.282352954 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487701990"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.405027926"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.592156887 0.494117647 0.380392164 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487701991"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.513966441"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.494117647 0.392156869 0.282352954 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487701992"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.567039073"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.596078455 0.490196079 0.400000006 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487701993"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.606145263"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.517647088 0.403921574 0.290196091 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487701994"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.656424582"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.619607866 0.501960814 0.368627459 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487701995"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.681564212"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.533333361 0.403921574 0.305882365 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487701996"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.768156409"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.635294139 0.517647088 0.388235301 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487701997"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.793296039"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.490196079 0.380392164 0.262745112 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487701998"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.913407803"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.53725493 0.454901963 0.376470596 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487701999"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.955307245"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.643137276 0.513725519 0.407843143 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487702000"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.986033499"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.650980413 0.533333361 0.423529416 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487702001"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.991620064"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.203921571 0.125490203 0.0509803928 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487702002"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.997206688"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.215686277 0.192156866 0.168627456 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1487702288"/><GUILayout><gpos v="-961.604675 -848 0"/></GUILayout><compOutputs><compOutput><uid v="1487702289"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="destination"/><connRef v="1487694525"/></connection><connection><identifier v="source"/><connRef v="1487701488"/><connRefOutput v="1487701489"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.200000003"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487703978"/><GUILayout><gpos v="432 464 0"/></GUILayout><compOutputs><compOutput><uid v="1487703979"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input"/><connRef v="1487437633"/><connRefOutput v="1487437634"/></connection></connections><compImplementation><compInstance><path v="pkg:///hbao?dependency=1297162667"/><parameters/><outputBridgings><outputBridging><uid v="1487703979"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487704933"/><GUILayout><gpos v="592 464 0"/></GUILayout><compOutputs><compOutput><uid v="1487704934"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487703978"/><connRefOutput v="1487703979"/></connection></connections><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1487704959"/><paramsArrayCells><paramsArrayCell><uid v="1487704967"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.768627465 0.870588243 0.937254906 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487704968"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.295652181 0.292214364 0.292214364 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1487705099"/><GUILayout><gpos v="-386.939331 -816 0"/></GUILayout><compOutputs><compOutput><uid v="1487705100"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="source"/><connRef v="1487704933"/><connRefOutput v="1487704934"/></connection><connection><identifier v="destination"/><connRef v="1487717578"/><connRefOutput v="1487717579"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="9"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.409999996"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487706181"/><GUILayout><gpos v="1008 80 0"/></GUILayout><compOutputs><compOutput><uid v="1487706182"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input"/><connRef v="1359211383"/></connection></connections><compImplementation><compInstance><path v="pkg:///curvature_smooth?dependency=1487705407"/><parameters/><outputBridgings><outputBridging><uid v="1487706182"/><identifier v="height"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487706200"/><GUILayout><gpos v="1149.94629 80 0"/></GUILayout><compOutputs><compOutput><uid v="1487706201"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487706181"/><connRefOutput v="1487706182"/></connection></connections><compImplementation><compFilter><filter v="gradient"/><parameters><parameter><name v="colorswitch"/><paramValue><constantValueBool v="1"/></paramValue></parameter></parameters><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1487706227"/><paramsArrayCells><paramsArrayCell><uid v="1487706228"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.555845559"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0 0 0 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487706229"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.596984804"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="1 1 1 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1487706483"/><GUILayout><gpos v="-194.939331 -816 0"/></GUILayout><compOutputs><compOutput><uid v="1487706484"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="destination"/><connRef v="1487705099"/><connRefOutput v="1487705100"/></connection><connection><identifier v="source"/><connRef v="1487706200"/><connRefOutput v="1487706201"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.170000002"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487717578"/><GUILayout><gpos v="-706.939331 -816 0"/></GUILayout><compOutputs><compOutput><uid v="1487717579"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="destination"/><connRef v="1487702288"/><connRefOutput v="1487702289"/></connection><connection><identifier v="opacity"/><connRef v="1487855332"/><connRefOutput v="1487855333"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.639999986"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487719592"/><GUILayout><gpos v="93.06073 -816 0"/></GUILayout><compOutputs><compOutput><uid v="1487719593"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="destination"/><connRef v="1487706483"/><connRefOutput v="1487706484"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.319999993"/></paramValue></parameter><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487836405"/><GUILayout><gpos v="-3774.56982 897.273376 1"/></GUILayout><compOutputs><compOutput><uid v="1482609709"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="pattern_input"/><connRef v="1487836409"/><connRefOutput v="1482608790"/></connection><connection><identifier v="rotation_map_input"/><connRef v="1487836413"/><connRefOutput v="1482609943"/></connection></connections><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1486812999"/><parameters><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="85"/></paramValue></parameter><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="size"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="7.13999987 6.13999987"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="3.46000004"/></paramValue></parameter><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="85"/></paramValue></parameter><parameter><name v="size_random"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.329999983 0.319999993"/></paramValue></parameter><parameter><name v="rotation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.920000017"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.659999967"/></paramValue></parameter><parameter><name v="rotation_map_multiplier"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.539999962"/></paramValue></parameter><parameter><name v="rotation_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0199999996"/></paramValue></parameter><parameter><name v="non_square_expansion"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="color_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.689999998"/></paramValue></parameter><parameter><name v="pattern_input_number"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1482609709"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487836406"/><GUILayout><gpos v="-3102.56982 944 0"/></GUILayout><compOutputs><compOutput><uid v="1359211384"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487836408"/><connRefOutput v="1482709066"/></connection></connections><compImplementation><compFilter><filter v="normal"/><parameters><parameter><name v="input2alpha"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="3"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487836407"/><GUILayout><gpos v="-3296.20117 1252.22144 1"/></GUILayout><compOutputs><compOutput><uid v="1482628326"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487836408"/><connRefOutput v="1482709066"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.478316337 0.478316337 0.478316337 0.5"/></paramValue></parameter><parameter><name v="levelinlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.12627551 0.12627551 0.12627551 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487836408"/><GUILayout><gpos v="-3486.56982 950.606689 1"/></GUILayout><compOutputs><compOutput><uid v="1482709066"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487836411"/><connRefOutput v="1482708240"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.151785716 0.151785716 0.151785716 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487836409"/><GUILayout><gpos v="-3934.78076 841.555054 1"/></GUILayout><compOutputs><compOutput><uid v="1482608790"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487836422"/></connection></connections><compImplementation><compFilter><filter v="blur"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.91999996"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487836410"/><GUILayout><gpos v="-3294.56982 822.606689 1"/></GUILayout><compOutputs><compOutput><uid v="1482625016"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487836408"/><connRefOutput v="1482709066"/></connection></connections><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1482625039"/><paramsArrayCells><paramsArrayCell><uid v="1482625052"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.926086962 0.546007454 0.393048555 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1482625053"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.875527442"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.650328338 0.908695638 0.507178962 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1482625213"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.0780590624"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.168359771 0.352173924 0.114661269 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1482625257"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.386075944"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.41995281 0.682608724 0.170652181 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1482625330"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.630801678"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.660464287 0.813043475 0.207987875 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1487836411"/><GUILayout><gpos v="-3614.56982 950.606689 1"/></GUILayout><compOutputs><compOutput><uid v="1482708240"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487836405"/><connRefOutput v="1482609709"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.53188777 0.53188777 0.53188777 0.5"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487836412"/><GUILayout><gpos v="-3294.56982 1124.22144 1"/></GUILayout><compOutputs><compOutput><uid v="1482628326"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487836408"/><connRefOutput v="1482709066"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.478316337 0.478316337 0.478316337 0.5"/></paramValue></parameter><parameter><name v="levelinlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.12627551 0.12627551 0.12627551 0"/></paramValue></parameter><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.235969394 0.235969394 0.235969394 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487836413"/><GUILayout><gpos v="-3934.56982 950.606689 1"/></GUILayout><compOutputs><compOutput><uid v="1482609943"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///gaussian_noise?dependency=1486875707"/><parameters><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueInt32 v="9"/></paramValue></parameter><parameter><name v="disorder"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.099999994"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1482609943"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487836414"/><GUILayout><gpos v="-4350.78125 969.555054 1"/></GUILayout><compOutputs><compOutput><uid v="1482591041"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///gradient_linear_1?dependency=1482654803"/><parameters><parameter><name v="rotation"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1482591041"/><identifier v="Simple_Gradient"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487836415"/><GUILayout><gpos v="-3102.56982 1156.22144 1"/></GUILayout><compOutputs><compOutput><uid v="1482708448"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487836412"/><connRefOutput v="1482628326"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.460459173 0.460459173 0.460459173 0.5"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487836416"/><GUILayout><gpos v="-4222.78125 969.555054 1"/></GUILayout><compOutputs><compOutput><uid v="1482590975"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="Input_1"/><connRef v="1487836414"/><connRefOutput v="1482591041"/></connection></connections><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1487772706"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.610000014"/></paramValue></parameter><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0700000003"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1482590975"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487836417"/><GUILayout><gpos v="-3102.56982 1268.39331 1"/></GUILayout><compOutputs><compOutput><uid v="1482708448"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487836407"/><connRefOutput v="1482628326"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.3125 0.3125 0.3125 0.5"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487836418"/><GUILayout><gpos v="-2910.56982 1046.60669 1"/></GUILayout><compOutputs><compOutput><uid v="1482593503"/><comptype v="1"/></compOutput><compOutput><uid v="1482593504"/><comptype v="1"/></compOutput><compOutput><uid v="1482593505"/><comptype v="1"/></compOutput><compOutput><uid v="1482593506"/><comptype v="1"/></compOutput><compOutput><uid v="1482593507"/><comptype v="2"/></compOutput><compOutput><uid v="1482593508"/><comptype v="2"/></compOutput><compOutput><uid v="1482593509"/><comptype v="2"/></compOutput><compOutput><uid v="1482593510"/><comptype v="2"/></compOutput><compOutput><uid v="1482593511"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="normal_input"/><connRef v="1487836406"/><connRefOutput v="1359211384"/></connection><connection><identifier v="ambientOcclusion_input"/><connRef v="1487836417"/><connRefOutput v="1482708448"/></connection><connection><identifier v="roughness_input"/><connRef v="1487836415"/><connRefOutput v="1482708448"/></connection><connection><identifier v="height_input"/><connRef v="1487836421"/><connRefOutput v="1486906083"/></connection><connection><identifier v="basecolor_input"/><connRef v="1487836425"/><connRefOutput v="1486907756"/></connection></connections><compImplementation><compInstance><path v="pkg:///pbr_base_material?dependency=1487822363"/><parameters><parameter><name v="user_basecolor"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="user_normal"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="user_roughness"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="user_metallic"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="user_ao"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="user_height"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="height_position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.519999981"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1482593503"/><identifier v="diffuse"/></outputBridging><outputBridging><uid v="1482593504"/><identifier v="basecolor"/></outputBridging><outputBridging><uid v="1482593505"/><identifier v="normal"/></outputBridging><outputBridging><uid v="1482593506"/><identifier v="specular"/></outputBridging><outputBridging><uid v="1482593507"/><identifier v="glossiness"/></outputBridging><outputBridging><uid v="1482593508"/><identifier v="roughness"/></outputBridging><outputBridging><uid v="1482593509"/><identifier v="metallic"/></outputBridging><outputBridging><uid v="1482593510"/><identifier v="height"/></outputBridging><outputBridging><uid v="1482593511"/><identifier v="ambientOcclusion"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487836419"/><GUILayout><gpos v="-4352.14648 841.554993 1"/></GUILayout><compOutputs><compOutput><uid v="1482590232"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487836424"/><connRefOutput v="1482591428"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.149234697 0.149234697 0.149234697 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487836420"/><GUILayout><gpos v="-4222.56982 841.554993 1"/></GUILayout><compOutputs><compOutput><uid v="1482590705"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="source"/><connRef v="1487836419"/><connRefOutput v="1482590232"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="maskrectangle"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.199999988 0.779999971 0 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487836421"/><GUILayout><gpos v="-3102.56982 1046.60669 1"/></GUILayout><compOutputs><compOutput><uid v="1486906083"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487836408"/><connRefOutput v="1482709066"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelouthigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.679846942 0.679846942 0.679846942 1"/></paramValue></parameter><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.459302336 0.459302336 0.459302336 0.5"/></paramValue></parameter><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.672193885 0.672193885 0.672193885 1"/></paramValue></parameter><parameter><name v="levelinlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.304846942 0.304846942 0.304846942 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487836422"/><GUILayout><gpos v="-4062.78101 841.555054 1"/></GUILayout><compOutputs><compOutput><uid v="1482591088"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="destination"/><connRef v="1487836420"/><connRefOutput v="1482590705"/></connection><connection><identifier v="source"/><connRef v="1487836416"/><connRefOutput v="1482590975"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="maskrectangle"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0 1 0 0"/></paramValue></parameter><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487836423"/><GUILayout><gpos v="-4606.43018 841.992065 1"/></GUILayout><compOutputs><compOutput><uid v="1482576333"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///shape?dependency=1311189026"/><parameters><parameter><name v="Pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="16"/></paramValue></parameter><parameter><name v="Size"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.74000001"/></paramValue></parameter><parameter><name v="Pattern_Specific"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.829999983"/></paramValue></parameter><parameter><name v="Size_xy"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.199999988 1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1482576333"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487836424"/><GUILayout><gpos v="-4480.94434 841.554993 1"/></GUILayout><compOutputs><compOutput><uid v="1482591428"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input"/><connRef v="1487836423"/><connRefOutput v="1482576333"/></connection></connections><compImplementation><compInstance><path v="pkg:///trapezoid_transform_grayscale?dependency=1486830014"/><parameters><parameter><name v="top_stretch"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.620000005"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1482591428"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487836425"/><GUILayout><gpos v="-3102.56982 822.606689 1"/></GUILayout><compOutputs><compOutput><uid v="1486907756"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487836410"/><connRefOutput v="1482625016"/></connection></connections><compImplementation><compFilter><filter v="hsl"/><parameters><parameter><name v="hue"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.50999999"/></paramValue></parameter><parameter><name v="saturation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.469999999"/></paramValue></parameter><parameter><name v="luminosity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.48999998"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487852651"/><GUILayout><gpos v="-2160 1008 0"/></GUILayout><compOutputs><compOutput><uid v="1487424667"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input"/><connRef v="1487424909"/><connRefOutput v="1487424910"/></connection></connections><compImplementation><compInstance><path v="pkg:///edge_detect?dependency=1487424625"/><parameters><parameter><name v="edge_width"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="2.3599999"/></paramValue></parameter><parameter><name v="edge_roundness"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="6.5999999"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487424667"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487852703"/><GUILayout><gpos v="-2000 1008 0"/></GUILayout><compOutputs><compOutput><uid v="1487852704"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487852651"/><connRefOutput v="1487424667"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.387811571 0.387811571 0.387811571 0.5"/></paramValue></parameter><parameter><name v="levelinlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.751404524 0.751404524 0.751404524 0"/></paramValue></parameter><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.964887619 0.964887619 0.964887619 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487855332"/><GUILayout><gpos v="-1200 304 0"/></GUILayout><compOutputs><compOutput><uid v="1487855333"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="source"/><connRef v="1487836418"/><connRefOutput v="1482593510"/></connection><connection><identifier v="destination"/><connRef v="1487693524"/><connRefOutput v="1487693525"/></connection><connection><identifier v="opacity"/><connRef v="1487858150"/><connRefOutput v="1487858151"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1487855845"/><GUILayout><gpos v="-1840 1008 0"/></GUILayout><compOutputs><compOutput><uid v="1487855846"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487852703"/><connRefOutput v="1487852704"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0 0 0 1"/></paramValue></parameter><parameter><name v="levelinlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="1 1 1 0"/></paramValue></parameter><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.259831488 0.259831488 0.259831488 0.5"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487858150"/><GUILayout><gpos v="-1648 1008 0"/></GUILayout><compOutputs><compOutput><uid v="1487858151"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487855845"/><connRefOutput v="1487855846"/></connection></connections><compImplementation><compFilter><filter v="blur"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="2.76999998"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487861414"/><GUILayout><gpos v="720 -816 0"/></GUILayout><compOutputs><compOutput><uid v="1487861415"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="destination"/><connRef v="1487719592"/><connRefOutput v="1487719593"/></connection><connection><identifier v="source"/><connRef v="1487836425"/><connRefOutput v="1486907756"/></connection><connection><identifier v="opacity"/><connRef v="1487858150"/><connRefOutput v="1487858151"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode></compNodes><baseParameters/><options><option><name v="defaultParentSize"/><value v="11x11"/></option></options><root><rootOutputs><rootOutput><output v="1213284336"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1213284338"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1213284340"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1279137031"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1359211721"/><format v="0"/><usertag v=""/></rootOutput></rootOutputs></root></graph></content></package>
