%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: Saturation
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor v1.9.1.6\n// Available at the
    Unity Asset Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=19106\nNode;AmplifyShaderEditor.StickyNoteNode;29;-33.2765,-1351.862;Inherit;False;261;101;;;0,0,0,1;<PERSON><PERSON><PERSON>
    using a dot product with the perceptual weights.;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;35;-273.1733,-1542.513;Inherit;False;503.6;166.2999;Saturation
    Node;;0,0,0,1;The Saturation Node adjusts the intensity of the In color.  When
    the Saturation input is great that one, the intensity of the In color is increased. 
    When it's less than one, it's descreased.$$With a Saturation value of 0, the
    image becomes black and white.;0;0\nNode;AmplifyShaderEditor.DotProductOpNode;28;-186.1765,-1353.362;Inherit;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.LerpOp;31;-28.93127,-1239.065;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionInput;21;-200.9016,-1134.16;Inherit;False;Saturation;1;1;False;1;0;FLOAT;0.5;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;0;133.4027,-1244.605;Inherit;False;True;-1;Out;0;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.Vector3Node;26;-424.3238,-1357.282;Inherit;False;Constant;_PerceptualWeights;Perceptual
    Weights;0;0;Create;True;0;0;0;False;0;False;0.2126729,0.7151522,0.072175;0,0,0;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.FunctionInput;12;-360.6382,-1221.56;Inherit;False;In;3;0;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nWireConnection;28;0;26;0\nWireConnection;28;1;12;0\nWireConnection;31;0;28;0\nWireConnection;31;1;12;0\nWireConnection;31;2;21;0\nWireConnection;0;0;31;0\nASEEND*/\n//CHKSM=DB632B9F7C7733FFAB40E51823977C4A90F2F927"
  m_functionName: 
  m_description: Amplify Shader Pack Example
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 0
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_customEdited: 0
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 0
  m_headerStyle: 2
  m_headerColor: {r: 0.30588236, g: 0.5176471, b: 0.16078432, a: 1}
  m_customNodeCategory: Amplify Shader Pack
  m_previewPosition: 0
  m_hidden: 0
  m_url: 
