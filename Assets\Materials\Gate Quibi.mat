%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!28 &-7615427098411967581
Texture2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: z__GradientRampTex{"mode":0,"colorKeys":[{"color":{"r":1.0,"g":0.0,"b":0.0,"a":1.0},"time":0.0},{"color":{"r":1.0,"g":0.0,"b":0.0,"a":1.0},"time":1.0}],"alphaKeys":[{"alpha":1.0,"time":0.05882352963089943},{"alpha":1.0,"time":1.0}]}
  m_ImageContentsHash:
    serializedVersion: 2
    Hash: 00000000000000000000000000000000
  m_ForcedFallbackFormat: 4
  m_DownscaleFallback: 0
  m_IsAlphaChannelOptional: 0
  serializedVersion: 2
  m_Width: 256
  m_Height: 1
  m_CompleteImageSize: 1024
  m_MipsStripped: 0
  m_TextureFormat: 5
  m_MipCount: 1
  m_IsReadable: 1
  m_IsPreProcessed: 0
  m_IgnoreMipmapLimit: 0
  m_MipmapLimitGroupName: 
  m_StreamingMipmaps: 0
  m_StreamingMipmapsPriority: 0
  m_VTOnly: 0
  m_AlphaIsTransparency: 0
  m_ImageCount: 1
  m_TextureDimension: 2
  m_TextureSettings:
    serializedVersion: 2
    m_FilterMode: 1
    m_Aniso: 1
    m_MipBias: 0
    m_WrapU: 1
    m_WrapV: 1
    m_WrapW: 1
  m_LightmapFormat: 0
  m_ColorSpace: 0
  m_PlatformBlob: 
  image data: 1024
  _typelessdata: ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Gate Quibi
  m_Shader: {fileID: 4800000, guid: 03cbdd32a8958fe459d7b5edaae49ff7, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords: []
  m_InvalidKeywords:
  - _USEDIRTMAPASGLOSSMAP_ON
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 1
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: 3000
  stringTagMap: {}
  disabledShaderPasses:
  - SRPDEFAULTUNLIT
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BakedGIRamp:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BaseMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Cubemap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Diffuse:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _GradientRamp:
        m_Texture: {fileID: -7615427098411967581}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _M_map:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _N_map:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ReflectionTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Texture:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _originalDiffuse:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - ENUM_5312DE06: 2
    - _AlphaClip: 0
    - _AlphaCutoff: 0.5
    - _Amount: 0
    - _Amplitude: 0
    - _AmplitudeSpeed: 1
    - _Bias: 0
    - _Blend: 3
    - _BumpScale: 1
    - _CameraDistanceImpact: 0.5
    - _ClearCoatMask: 0
    - _ClearCoatSmoothness: 0
    - _ColorMaskDistance: 25
    - _ColorMaskPower: 1
    - _CubemapBlur: 0
    - _CubemapIntensity: 1
    - _Cull: 2
    - _Cutoff: 0.5
    - _CutoutThickness: 0.03
    - _DesaturateBackground: 0
    - _DetailAlbedoMapScale: 1
    - _DetailMapBlendingMode: 0
    - _DetailMapImpact: 0
    - _DetailNormalMapScale: 1
    - _Distance: 0
    - _DstBlend: 0
    - _EnableCubemap: 0
    - _EnableRGBchannels: 0
    - _EnvironmentReflections: 1
    - _FlatRimEdgeSmoothness: 0.444
    - _FlatRimLightAlign: 0.74
    - _FlatRimSize: 0.176
    - _FlatSpecularEdgeSmoothness: 0.629
    - _FlatSpecularSize: 0.562
    - _FresnelBias: 0
    - _FresnelIntensity: 93.09
    - _FresnelPower: 282.65
    - _FresnelStrength: 0
    - _GlossMapScale: 0
    - _Glossiness: 0
    - _GlossyReflections: 0
    - _GradientAngle: 0
    - _GradientCenterX: 0
    - _GradientCenterY: 0
    - _GradientEnabled: 1
    - _GradientSize: 10
    - _Intensity: 1
    - _InvFade: 1
    - _Invert: 0
    - _LightContribution: 1
    - _LightmapDirectionPitch: 0
    - _LightmapDirectionYaw: 0
    - _MaxValueAmplitude: 2
    - _Metallic: 0
    - _MinValueAmplitude: 1
    - _OcclusionStrength: 1
    - _Opacity: 0
    - _OriginalUVSwitch: 0
    - _OutlineDepthOffset: 0
    - _OutlineEnabled: 0
    - _OutlineScale: 1
    - _OutlineWidth: 3.76
    - _OverrideBakedGi: 0
    - _OverrideLightAttenuation: 0
    - _OverrideLightmapDir: 0
    - _Parallax: 0.005
    - _Power: 0
    - _QueueControl: 0
    - _QueueOffset: 0
    - _ReceiveShadows: 1
    - _Refraction: 2
    - _RimEnabled: 1
    - _Scale: 0
    - _ScreenBlendingmode: 0
    - _SelfIllumination: 10
    - _SelfShadingSize: 0.85
    - _ShadowOpacity: 0
    - _Smoothness: 0.5
    - _SmoothnessTextureChannel: 0
    - _SpecularEnabled: 1
    - _SpecularHighlights: 1
    - _SpecularIntensity1: 0.2
    - _Speed: 0
    - _Speed_Up: 0
    - _SrcBlend: 1
    - _Surface: 0
    - _TextureBlendingMode: 0
    - _TextureImpact: 0
    - _TexturePower: 1
    - _TextureTiling: 10
    - _UnityShadowOcclusion: 0
    - _UseDirtmapasGlossmap: 0.5
    - _VertexColorsEnabled: 0
    - _WireframeShader_Diameter: 1
    - _WireframeShader_Smoothness: 1
    - _WireframeShader_Thickness: 1
    - _WorkflowMode: 1
    - _X: 1
    - _XRayBias: 0.04
    - _XRayFresnelIntensity: 1
    - _XRayFresnelPower: 1
    - _Y: 1
    - _ZWrite: 1
    - _deform: 1
    - _glitchColor: 1
    - _intensity: 10.54
    - _monochrom: 1
    - _noise_details: 0
    - _t: 0
    m_Colors:
    - Color_E904F11F: {r: 1, g: 0, b: 0, a: 1}
    - _BaseColor: {r: 1, g: 1, b: 1, a: 1}
    - _BorderColor: {r: 1, g: 1, b: 1, a: 1}
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _ColorGradient: {r: 0, g: 0, b: 0, a: 0.85056}
    - _ColorMaskOuterColor: {r: 0.5294118, g: 0.5294118, b: 0.5294118, a: 1}
    - _DetailMapColor: {r: 0.9528302, g: 0, b: 0, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _FillColorBackground: {r: 0, g: 0, b: 0, a: 0}
    - _FlatRimColor: {r: 0, g: 0, b: 0, a: 0.85056}
    - _FlatSpecularColor: {r: 0.5566038, g: 0.4589356, b: 0.4589356, a: 0.85056}
    - _FresnelColor: {r: 0.6933962, g: 1, b: 0.98143524, a: 1}
    - _LightAttenuation: {r: 0, g: 1, b: 0, a: 0}
    - _LightmapDirection: {r: 0, g: 1, b: 0, a: 0}
    - _OutlineColor: {r: 0.9622642, g: 0.9622642, b: 0.9622642, a: 1}
    - _ShadowColor: {r: 0, g: 0, b: 0, a: 1}
    - _SpecColor: {r: 0.2, g: 0.2, b: 0.2, a: 1}
    - _TextureColorTint: {r: 1, g: 1, b: 1, a: 1}
    - _XRayColor: {r: 0, g: 1, b: 0.98039216, a: 0.1019608}
    - _diff_Color: {r: 1, g: 1, b: 1, a: 1}
  m_BuildTextureStacks: []
--- !u!114 &4609003894670666248
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0353a89b1f911e48b9e16bdc9f2e058, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 4
