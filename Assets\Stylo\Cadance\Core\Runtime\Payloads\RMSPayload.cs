using System;
using UnityEngine;

namespace Stylo.Cadance
{
    /// <summary>
    /// RMS (Root Mean Square) payload for Cadance events containing audio energy/volume data.
    /// Compatible with Koreographer's RMSPayload.
    /// </summary>
    [Serializable]
    public class RMSPayload : IPayload
    {
        [SerializeField] private float rmsValue;
        [SerializeField] private float peakValue;
        [SerializeField] private RMSInfo rmsDataInfo;

        /// <summary>
        /// Gets or sets the RMS (Root Mean Square) value representing average energy.
        /// </summary>
        public float RMSValue
        {
            get => rmsValue;
            set => rmsValue = value;
        }

        /// <summary>
        /// Gets or sets the peak value representing maximum amplitude.
        /// </summary>
        public float PeakValue
        {
            get => peakValue;
            set => peakValue = value;
        }

        /// <summary>
        /// Gets or sets the RMS analysis information.
        /// </summary>
        public RMSInfo RMSDataInfo
        {
            get => rmsDataInfo;
            set => rmsDataInfo = value;
        }

        /// <summary>
        /// Gets the RMS value in decibels.
        /// </summary>
        public float RMSValueDB
        {
            get
            {
                if (rmsValue <= 0f) return -80f; // Silence threshold
                return 20f * Mathf.Log10(rmsValue);
            }
        }

        /// <summary>
        /// Gets the peak value in decibels.
        /// </summary>
        public float PeakValueDB
        {
            get
            {
                if (peakValue <= 0f) return -80f; // Silence threshold
                return 20f * Mathf.Log10(peakValue);
            }
        }

        /// <summary>
        /// Creates a new RMSPayload with the specified values.
        /// </summary>
        /// <param name="rms">The RMS value</param>
        /// <param name="peak">The peak value</param>
        /// <param name="info">The RMS analysis information</param>
        public RMSPayload(float rms = 0f, float peak = 0f, RMSInfo info = default)
        {
            rmsValue = rms;
            peakValue = peak;
            rmsDataInfo = info;
        }

        /// <summary>
        /// Gets the energy level as a normalized value (0.0 to 1.0).
        /// </summary>
        /// <returns>Normalized energy level</returns>
        public float GetNormalizedEnergy()
        {
            // Normalize RMS value to 0-1 range
            return Mathf.Clamp01(rmsValue);
        }

        /// <summary>
        /// Gets the energy level in a specified range.
        /// </summary>
        /// <param name="minValue">Minimum output value</param>
        /// <param name="maxValue">Maximum output value</param>
        /// <returns>Energy level mapped to the specified range</returns>
        public float GetEnergyInRange(float minValue, float maxValue)
        {
            float normalizedEnergy = GetNormalizedEnergy();
            return Mathf.Lerp(minValue, maxValue, normalizedEnergy);
        }

        /// <summary>
        /// Determines if the audio is above a specified energy threshold.
        /// </summary>
        /// <param name="threshold">The energy threshold (0.0 to 1.0)</param>
        /// <returns>True if energy is above threshold</returns>
        public bool IsAboveThreshold(float threshold)
        {
            return GetNormalizedEnergy() > threshold;
        }

        /// <summary>
        /// Gets the dynamic range between RMS and peak values.
        /// </summary>
        /// <returns>Dynamic range in decibels</returns>
        public float GetDynamicRange()
        {
            return PeakValueDB - RMSValueDB;
        }

        /// <summary>
        /// Returns a copy of this payload.
        /// </summary>
        /// <returns>A copy of the RMSPayload</returns>
        public IPayload GetCopy()
        {
            return new RMSPayload(rmsValue, peakValue, rmsDataInfo);
        }

        /// <summary>
        /// Gets the friendly name for editor display.
        /// </summary>
        /// <returns>The friendly name</returns>
        public string GetFriendlyName()
        {
            return "RMS";
        }

        /// <summary>
        /// Gets the display width for editor visualization.
        /// </summary>
        /// <returns>The display width in pixels</returns>
        public float GetDisplayWidth()
        {
            return 80f; // Default width for RMS visualization
        }

        /// <summary>
        /// Returns a string representation of the RMS payload.
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"RMSPayload: RMS={rmsValue:F3} ({RMSValueDB:F1}dB), Peak={peakValue:F3} ({PeakValueDB:F1}dB)";
        }
    }

    /// <summary>
    /// Information about RMS analysis parameters.
    /// </summary>
    [Serializable]
    public struct RMSInfo
    {
        /// <summary>
        /// The sample rate of the audio data.
        /// </summary>
        public int sampleRate;

        /// <summary>
        /// The window size used for RMS analysis (in samples).
        /// </summary>
        public int windowSize;

        /// <summary>
        /// The overlap between analysis windows (0.0 to 1.0).
        /// </summary>
        public float windowOverlap;

        /// <summary>
        /// The analysis method used for RMS calculation.
        /// </summary>
        public RMSAnalysisMethod analysisMethod;

        /// <summary>
        /// Creates a new RMSInfo.
        /// </summary>
        /// <param name="sampleRate">The sample rate</param>
        /// <param name="windowSize">The window size in samples</param>
        /// <param name="windowOverlap">The window overlap (0.0 to 1.0)</param>
        /// <param name="analysisMethod">The analysis method</param>
        public RMSInfo(int sampleRate, int windowSize, float windowOverlap = 0.5f, RMSAnalysisMethod analysisMethod = RMSAnalysisMethod.Standard)
        {
            this.sampleRate = sampleRate;
            this.windowSize = windowSize;
            this.windowOverlap = windowOverlap;
            this.analysisMethod = analysisMethod;
        }

        /// <summary>
        /// Gets the window duration in seconds.
        /// </summary>
        public float WindowDurationSeconds => (float)windowSize / sampleRate;

        /// <summary>
        /// Gets the hop size (samples between analysis windows).
        /// </summary>
        public int HopSize => Mathf.RoundToInt(windowSize * (1f - windowOverlap));
    }

    /// <summary>
    /// RMS analysis methods.
    /// </summary>
    public enum RMSAnalysisMethod
    {
        /// <summary>
        /// Standard RMS calculation.
        /// </summary>
        Standard,

        /// <summary>
        /// Weighted RMS with frequency emphasis.
        /// </summary>
        Weighted,

        /// <summary>
        /// A-weighted RMS for perceptual loudness.
        /// </summary>
        AWeighted,

        /// <summary>
        /// LUFS (Loudness Units relative to Full Scale) measurement.
        /// </summary>
        LUFS
    }

    /// <summary>
    /// Extension methods for RMSPayload events.
    /// </summary>
    public static class RMSPayloadEventExtensions
    {
        /// <summary>
        /// Determines if the event has an RMSPayload.
        /// </summary>
        /// <param name="evt">The CadanceEvent</param>
        /// <returns>True if the event has an RMSPayload</returns>
        public static bool HasRMSPayload(this CadanceEvent evt)
        {
            return evt.Payload is RMSPayload;
        }

        /// <summary>
        /// Gets the RMSPayload from the event.
        /// </summary>
        /// <param name="evt">The CadanceEvent</param>
        /// <returns>The RMSPayload or null if not present</returns>
        public static RMSPayload GetRMSPayload(this CadanceEvent evt)
        {
            return evt.Payload as RMSPayload;
        }

        /// <summary>
        /// Gets the RMS value from the event.
        /// </summary>
        /// <param name="evt">The CadanceEvent</param>
        /// <returns>The RMS value or 0 if no RMSPayload</returns>
        public static float GetRMSValue(this CadanceEvent evt)
        {
            var rmsPayload = evt.GetRMSPayload();
            return rmsPayload?.RMSValue ?? 0f;
        }

        /// <summary>
        /// Gets the peak value from the event.
        /// </summary>
        /// <param name="evt">The CadanceEvent</param>
        /// <returns>The peak value or 0 if no RMSPayload</returns>
        public static float GetPeakValue(this CadanceEvent evt)
        {
            var rmsPayload = evt.GetRMSPayload();
            return rmsPayload?.PeakValue ?? 0f;
        }

        /// <summary>
        /// Gets the normalized energy level from the event.
        /// </summary>
        /// <param name="evt">The CadanceEvent</param>
        /// <returns>Normalized energy level (0.0 to 1.0)</returns>
        public static float GetNormalizedEnergy(this CadanceEvent evt)
        {
            var rmsPayload = evt.GetRMSPayload();
            return rmsPayload?.GetNormalizedEnergy() ?? 0f;
        }

        /// <summary>
        /// Checks if the event's energy is above a threshold.
        /// </summary>
        /// <param name="evt">The CadanceEvent</param>
        /// <param name="threshold">The energy threshold (0.0 to 1.0)</param>
        /// <returns>True if energy is above threshold</returns>
        public static bool IsAboveEnergyThreshold(this CadanceEvent evt, float threshold)
        {
            var rmsPayload = evt.GetRMSPayload();
            return rmsPayload?.IsAboveThreshold(threshold) ?? false;
        }
    }
}
