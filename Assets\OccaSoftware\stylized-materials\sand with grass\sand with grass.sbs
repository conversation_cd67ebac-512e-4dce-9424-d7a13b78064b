<?xml version="1.0" encoding="UTF-8"?><package><identifier v="Unsaved Package"/><formatVersion v="1.1.0.202302"/><updaterVersion v="1.1.0.202302"/><fileUID v="{0ebd38e1-7490-477e-91d7-66f4e7c7186d}"/><versionUID v="0"/><dependencies><dependency><filename v="sbs://hbao_2.sbs"/><uid v="1297162667"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://pattern_tile_sampler.sbs"/><uid v="1449399972"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://auto_levels.sbs"/><uid v="1449400845"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://blur_hq.sbs"/><uid v="1299236171"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://histogram_scan.sbs"/><uid v="1449404462"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_gaussian_noise.sbs"/><uid v="1449451779"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://gradient_linear_1.sbs"/><uid v="1449532383"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://invert.sbs"/><uid v="1368115694"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://curvature_smooth.sbs"/><uid v="1449590280"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://non_uniform_blur.sbs"/><uid v="1290776959"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://curvature.sbs"/><uid v="1449591180"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://height_blend.sbs"/><uid v="1449623608"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://waveform_1.sbs"/><uid v="1446771957"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://bevel.sbs"/><uid v="1449405586"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://pattern_splatter_circular.sbs"/><uid v="1448559733"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://pattern_shape.sbs"/><uid v="1311189026"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://flood_fill_2.sbs"/><uid v="1323881949"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://highpass.sbs"/><uid v="1449753752"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://rgba_split.sbs"/><uid v="1147283954"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_fractal_sum_base.sbs"/><uid v="1449402686"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://slope_blur.sbs"/><uid v="1449402040"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency></dependencies><content><graph><identifier v="Sand_with_grass"/><uid v="1447871750"/><graphtype v="material"/><attributes><author v="3dex"/><authorURL v="3dex.net"/></attributes><paraminputs><paraminput><identifier v="opacitymult"/><uid v="1484632959"/><attributes><label v="Opacity"/><description v="The &lt;b&gt;Opacity&lt;/b&gt; parameter sets the transparency of the blend"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="0"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Rocks"/></paraminput><paraminput><identifier v="x_amount"/><uid v="1484637660"/><attributes><label v="X Amount"/></attributes><type v="16"/><defaultValue><constantValueInt1 v="7"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="7"/></option><option><name v="max"/><value v="64"/></option><option><name v="min"/><value v="1"/></option><option><name v="step"/><value v="1"/></option></options></defaultWidget><group v="Rocks"/></paraminput><paraminput><identifier v="y_amount"/><uid v="1484642363"/><attributes><label v="Y Amount"/></attributes><type v="16"/><defaultValue><constantValueInt1 v="7"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="7"/></option><option><name v="max"/><value v="64"/></option><option><name v="min"/><value v="1"/></option><option><name v="step"/><value v="1"/></option></options></defaultWidget><group v="Rocks"/></paraminput><paraminput><identifier v="scale"/><uid v="1484647070"/><attributes><label v="Scale"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="1.62"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="1.62"/></option><option><name v="max"/><value v="10"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Rocks"/></paraminput><paraminput><identifier v="scale_random"/><uid v="1484651773"/><attributes><label v="Scale Random"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.379999995"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.38"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Rocks"/></paraminput><paraminput><identifier v="position_random"/><uid v="1484656481"/><attributes><label v="Position Random"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.209999993"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="0.21"/></option><option><name v="max"/><value v="10"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Rocks"/></paraminput><paraminput><identifier v="offset"/><uid v="1484661186"/><attributes><label v="Offset"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.5"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.5"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Rocks"/></paraminput><paraminput><identifier v="rotation_random"/><uid v="1484665890"/><attributes><label v="Rotation Random"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="1"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="1"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Rocks"/></paraminput><paraminput><identifier v="mask_random"/><uid v="1484670595"/><attributes><label v="Mask Random"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.680000007"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.68"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Rocks"/></paraminput><paraminput><identifier v="opacitymult_1"/><uid v="1484675324"/><attributes><label v="Opacity"/><description v="The &lt;b&gt;Opacity&lt;/b&gt; parameter sets the transparency of the blend"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="0"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Rocks Small"/></paraminput><paraminput><identifier v="x_amount_1"/><uid v="1484679979"/><attributes><label v="X Amount"/></attributes><type v="16"/><defaultValue><constantValueInt1 v="14"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="14"/></option><option><name v="max"/><value v="64"/></option><option><name v="min"/><value v="1"/></option><option><name v="step"/><value v="1"/></option></options></defaultWidget><group v="Rocks Small"/></paraminput><paraminput><identifier v="y_amount_1"/><uid v="1484684632"/><attributes><label v="Y Amount"/></attributes><type v="16"/><defaultValue><constantValueInt1 v="14"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="14"/></option><option><name v="max"/><value v="64"/></option><option><name v="min"/><value v="1"/></option><option><name v="step"/><value v="1"/></option></options></defaultWidget><group v="Rocks Small"/></paraminput><paraminput><identifier v="scale_1"/><uid v="1484689292"/><attributes><label v="Scale"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="1.62"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="1.62"/></option><option><name v="max"/><value v="10"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Rocks Small"/></paraminput><paraminput><identifier v="scale_random_1"/><uid v="1484693945"/><attributes><label v="Scale Random"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.379999995"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.38"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Rocks Small"/></paraminput><paraminput><identifier v="position_random_1"/><uid v="1484698597"/><attributes><label v="Position Random"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.209999993"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="0.21"/></option><option><name v="max"/><value v="10"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Rocks Small"/></paraminput><paraminput><identifier v="offset_1"/><uid v="1484703249"/><attributes><label v="Offset"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.5"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.5"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Rocks Small"/></paraminput><paraminput><identifier v="rotation_random_1"/><uid v="1484707902"/><attributes><label v="Rotation Random"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.819999993"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.82"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Rocks Small"/></paraminput><paraminput><identifier v="opacitymult_2"/><uid v="1484712567"/><attributes><label v="Opacity"/><description v="The &lt;b&gt;Opacity&lt;/b&gt; parameter sets the transparency of the blend"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="0"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Pebbles"/></paraminput><paraminput><identifier v="x_amount_2"/><uid v="1484717150"/><attributes><label v="X Amount"/></attributes><type v="16"/><defaultValue><constantValueInt1 v="25"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="25"/></option><option><name v="max"/><value v="64"/></option><option><name v="min"/><value v="1"/></option><option><name v="step"/><value v="1"/></option></options></defaultWidget><group v="Pebbles"/></paraminput><paraminput><identifier v="y_amount_2"/><uid v="1484721742"/><attributes><label v="Y Amount"/></attributes><type v="16"/><defaultValue><constantValueInt1 v="25"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="25"/></option><option><name v="max"/><value v="64"/></option><option><name v="min"/><value v="1"/></option><option><name v="step"/><value v="1"/></option></options></defaultWidget><group v="Pebbles"/></paraminput><paraminput><identifier v="scale_2"/><uid v="1484726336"/><attributes><label v="Scale"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="1.05999994"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="1.06"/></option><option><name v="max"/><value v="10"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Pebbles"/></paraminput><paraminput><identifier v="scale_random_2"/><uid v="1484730927"/><attributes><label v="Scale Random"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.660000026"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.66"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Pebbles"/></paraminput><paraminput><identifier v="position_random_2"/><uid v="1484735520"/><attributes><label v="Position Random"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.209999993"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="0.21"/></option><option><name v="max"/><value v="10"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Pebbles"/></paraminput><paraminput><identifier v="offset_2"/><uid v="1484740111"/><attributes><label v="Offset"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.5"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.5"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Pebbles"/></paraminput><paraminput><identifier v="rotation_random_2"/><uid v="1484744704"/><attributes><label v="Rotation Random"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.889999986"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.89"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Pebbles"/></paraminput><paraminput><identifier v="mask_random_1"/><uid v="1484749299"/><attributes><label v="Mask Random"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.790000021"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.79"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Pebbles"/></paraminput><paraminput><identifier v="mask_random_2"/><uid v="1484753904"/><attributes><label v="Mask Random"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.589999974"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.59"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Rocks Small"/></paraminput><paraminput><identifier v="opacitymult_3"/><uid v="1484796768"/><attributes><label v="Opacity"/><description v="The &lt;b&gt;Opacity&lt;/b&gt; parameter sets the transparency of the blend"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="0"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Sticks"/></paraminput><paraminput><identifier v="x_amount_3"/><uid v="1484798476"/><attributes><label v="X Amount"/></attributes><type v="16"/><defaultValue><constantValueInt1 v="8"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="8"/></option><option><name v="max"/><value v="64"/></option><option><name v="min"/><value v="1"/></option><option><name v="step"/><value v="1"/></option></options></defaultWidget><group v="Sticks"/></paraminput><paraminput><identifier v="y_amount_3"/><uid v="1484800167"/><attributes><label v="Y Amount"/></attributes><type v="16"/><defaultValue><constantValueInt1 v="8"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="8"/></option><option><name v="max"/><value v="64"/></option><option><name v="min"/><value v="1"/></option><option><name v="step"/><value v="1"/></option></options></defaultWidget><group v="Sticks"/></paraminput><paraminput><identifier v="scale_3"/><uid v="1484801859"/><attributes><label v="Scale"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="1.87"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="1.87"/></option><option><name v="max"/><value v="10"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Sticks"/></paraminput><paraminput><identifier v="scale_random_3"/><uid v="1484803550"/><attributes><label v="Scale Random"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.560000002"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.56"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Sticks"/></paraminput><paraminput><identifier v="position_random_3"/><uid v="1484805243"/><attributes><label v="Position Random"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.600000024"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="0.6"/></option><option><name v="max"/><value v="10"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Sticks"/></paraminput><paraminput><identifier v="offset_3"/><uid v="1484806933"/><attributes><label v="Offset"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.389999986"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.39"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Sticks"/></paraminput><paraminput><identifier v="rotation_random_3"/><uid v="1484808649"/><attributes><label v="Rotation Random"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.49000001"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.49"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Sticks"/></paraminput><paraminput><identifier v="mask_random_3"/><uid v="1484810343"/><attributes><label v="Mask Random"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Sticks"/></paraminput><paraminput><identifier v="opacitymult_4"/><uid v="1484816012"/><attributes><label v="Opacity"/><description v="The &lt;b&gt;Opacity&lt;/b&gt; parameter sets the transparency of the blend"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="0"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Plants"/></paraminput><paraminput><identifier v="x_amount_4"/><uid v="1484817341"/><attributes><label v="X Amount"/></attributes><type v="16"/><defaultValue><constantValueInt1 v="10"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="10"/></option><option><name v="max"/><value v="64"/></option><option><name v="min"/><value v="1"/></option><option><name v="step"/><value v="1"/></option></options></defaultWidget><group v="Plants"/></paraminput><paraminput><identifier v="y_amount_4"/><uid v="1484818650"/><attributes><label v="Y Amount"/></attributes><type v="16"/><defaultValue><constantValueInt1 v="10"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="10"/></option><option><name v="max"/><value v="64"/></option><option><name v="min"/><value v="1"/></option><option><name v="step"/><value v="1"/></option></options></defaultWidget><group v="Plants"/></paraminput><paraminput><identifier v="scale_4"/><uid v="1484819961"/><attributes><label v="Scale"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="3.45000005"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="3.45"/></option><option><name v="max"/><value v="10"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Plants"/></paraminput><paraminput><identifier v="scale_random_4"/><uid v="1484821271"/><attributes><label v="Scale Random"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.430000007"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.43"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Plants"/></paraminput><paraminput><identifier v="position_random_4"/><uid v="1484822581"/><attributes><label v="Position Random"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.319999993"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="0.32"/></option><option><name v="max"/><value v="10"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Plants"/></paraminput><paraminput><identifier v="offset_4"/><uid v="1484823891"/><attributes><label v="Offset"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.0900000036"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.09"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Plants"/></paraminput><paraminput><identifier v="rotation_random_4"/><uid v="1484825203"/><attributes><label v="Rotation Random"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.370000005"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.37"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Plants"/></paraminput><paraminput><identifier v="mask_map_threshold"/><uid v="1484830433"/><attributes><label v="Mask Map Threshold"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.370000005"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.37"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Plants"/></paraminput><paraminput><identifier v="mask_random_4"/><uid v="1484831745"/><attributes><label v="Mask Random"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Plants"/></paraminput><paraminput><identifier v="opacitymult_5"/><uid v="1484846000"/><attributes><label v="Opacity"/><description v="The &lt;b&gt;Opacity&lt;/b&gt; parameter sets the transparency of the blend"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="0"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Sand"/></paraminput><paraminput><identifier v="x_amount_5"/><uid v="1484866157"/><attributes><label v="X Amount"/></attributes><type v="16"/><defaultValue><constantValueInt1 v="7"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="7"/></option><option><name v="max"/><value v="64"/></option><option><name v="min"/><value v="1"/></option><option><name v="step"/><value v="1"/></option></options></defaultWidget><group v="Sand"/></paraminput><paraminput><identifier v="y_amount_5"/><uid v="1484871440"/><attributes><label v="Y Amount"/></attributes><type v="16"/><defaultValue><constantValueInt1 v="13"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="13"/></option><option><name v="max"/><value v="64"/></option><option><name v="min"/><value v="1"/></option><option><name v="step"/><value v="1"/></option></options></defaultWidget><group v="Sand"/></paraminput><paraminput><identifier v="scale_5"/><uid v="1484876725"/><attributes><label v="Scale"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="8.59000015"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="8.59"/></option><option><name v="max"/><value v="10"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Sand"/></paraminput><paraminput><identifier v="position_random_5"/><uid v="1484882010"/><attributes><label v="Position Random"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="1.15999997"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="1.16"/></option><option><name v="max"/><value v="10"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Sand"/></paraminput><paraminput><identifier v="rotation"/><uid v="1484887294"/><attributes><label v="Rotation"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0"/></defaultValue><defaultWidget><name v="angle"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="0"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="valueInterpretation"/><value v="angle"/></option></options></defaultWidget><group v="Sand"/></paraminput><paraminput><identifier v="rotation_random_5"/><uid v="1484892578"/><attributes><label v="Rotation Random"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Sand"/></paraminput><paraminput><identifier v="outputcolor"/><uid v="1484897984"/><attributes><label v="Rocks"/><description v="The &lt;b&gt;Color Output&lt;/b&gt; parameter creates a uniform color output image"/></attributes><type v="2048"/><defaultValue><constantValueFloat4 v="0.435294002 0.423528999 0.396077991 1"/></defaultValue><defaultWidget><name v="color"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.435294;0.423529;0.396078;1"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="valueInterpretation"/><value v="color"/></option></options></defaultWidget><group v="Color"/></paraminput><paraminput><identifier v="outputcolor_1"/><uid v="1484898018"/><attributes><label v="Sand"/><description v="The &lt;b&gt;Color Output&lt;/b&gt; parameter creates a uniform color output image"/></attributes><type v="2048"/><defaultValue><constantValueFloat4 v="0.615685999 0.564706028 0.450980008 1"/></defaultValue><defaultWidget><name v="color"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.615686;0.564706;0.45098;1"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="valueInterpretation"/><value v="color"/></option></options></defaultWidget><group v="Color"/></paraminput><paraminput><identifier v="opacitymult_6"/><uid v="1484898064"/><attributes><label v="Rocks Value Intensity"/><description v="The &lt;b&gt;Opacity&lt;/b&gt; parameter sets the transparency of the blend"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.159999996"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="0.16"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Color"/></paraminput><paraminput><identifier v="opacitymult_7"/><uid v="1484898119"/><attributes><label v="Rocks Curvature"/><description v="The &lt;b&gt;Opacity&lt;/b&gt; parameter sets the transparency of the blend"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.0700000003"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="0.07"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Color"/></paraminput><paraminput><identifier v="opacitymult_8"/><uid v="1484898187"/><attributes><label v="Sand Curvature"/><description v="The &lt;b&gt;Opacity&lt;/b&gt; parameter sets the transparency of the blend"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.0799999982"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="0.08"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Color"/></paraminput><paraminput><identifier v="opacitymult_9"/><uid v="1484898241"/><attributes><label v="Sand Edge"/><description v="The &lt;b&gt;Opacity&lt;/b&gt; parameter sets the transparency of the blend"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.00999999978"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="0.01"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Color"/></paraminput><paraminput><identifier v="opacitymult_10"/><uid v="1484898268"/><attributes><label v="Sand Cavity"/><description v="The &lt;b&gt;Opacity&lt;/b&gt; parameter sets the transparency of the blend"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.0700000003"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="0.07"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Color"/></paraminput><paraminput><identifier v="outputcolor_2"/><uid v="1484898350"/><attributes><label v="Sticks"/><description v="The &lt;b&gt;Color Output&lt;/b&gt; parameter creates a uniform color output image"/></attributes><type v="2048"/><defaultValue><constantValueFloat4 v="0.388235003 0.337255001 0.282353014 1"/></defaultValue><defaultWidget><name v="color"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.388235;0.337255;0.282353;1"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="valueInterpretation"/><value v="color"/></option></options></defaultWidget><group v="Color"/></paraminput><paraminput><identifier v="outputcolor_3"/><uid v="1484898413"/><attributes><label v="Plants"/><description v="The &lt;b&gt;Color Output&lt;/b&gt; parameter creates a uniform color output image"/></attributes><type v="2048"/><defaultValue><constantValueFloat4 v="0.407842994 0.454901993 0.313726008 1"/></defaultValue><defaultWidget><name v="color"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.407843;0.454902;0.313726;1"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="valueInterpretation"/><value v="color"/></option></options></defaultWidget><group v="Color"/></paraminput><paraminput><identifier v="opacitymult_11"/><uid v="1484898458"/><attributes><label v="Plants Highlights"/><description v="The &lt;b&gt;Opacity&lt;/b&gt; parameter sets the transparency of the blend"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.340000004"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="0.34"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Color"/></paraminput><paraminput><identifier v="opacitymult_12"/><uid v="1484898505"/><attributes><label v="Plants Value Intensity"/><description v="The &lt;b&gt;Opacity&lt;/b&gt; parameter sets the transparency of the blend"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.109999999"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="0.11"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Color"/></paraminput><paraminput><identifier v="outputcolor_4"/><uid v="1484898651"/><attributes><label v="Rocks"/><description v="The &lt;b&gt;Color Output&lt;/b&gt; parameter creates a uniform color output image"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.729412019"/></defaultValue><defaultWidget><name v="color"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.729412"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="valueInterpretation"/><value v="color"/></option></options></defaultWidget><group v="Roughness"/></paraminput><paraminput><identifier v="outputcolor_5"/><uid v="1484898711"/><attributes><label v="Sand"/><description v="The &lt;b&gt;Color Output&lt;/b&gt; parameter creates a uniform color output image"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.819607973"/></defaultValue><defaultWidget><name v="color"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.819608"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="valueInterpretation"/><value v="color"/></option></options></defaultWidget><group v="Roughness"/></paraminput><paraminput><identifier v="outputcolor_6"/><uid v="1484898792"/><attributes><label v="Sticks"/><description v="The &lt;b&gt;Color Output&lt;/b&gt; parameter creates a uniform color output image"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.654901981"/></defaultValue><defaultWidget><name v="color"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.654902"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="valueInterpretation"/><value v="color"/></option></options></defaultWidget><group v="Roughness"/></paraminput><paraminput><identifier v="outputcolor_7"/><uid v="1484898829"/><attributes><label v="Plants"/><description v="The &lt;b&gt;Color Output&lt;/b&gt; parameter creates a uniform color output image"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.713725984"/></defaultValue><defaultWidget><name v="color"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.713726"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="valueInterpretation"/><value v="color"/></option></options></defaultWidget><group v="Roughness"/></paraminput><paraminput><identifier v="intensity"/><uid v="1484898867"/><attributes><label v="Normal Intensity"/><description v="The &lt;b&gt;Intensity&lt;/b&gt; parameter modifies the intensity of height map "/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="10"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="10"/></option><option><name v="max"/><value v="50"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget></paraminput><paraminput><identifier v="inversedy"/><uid v="1484898874"/><attributes><label v="Normal Format"/><description v="The &lt;b&gt;Normal Format&lt;/b&gt; parameter inverts y coordinates of height map (Open-GL)"/></attributes><type v="4"/><defaultValue><constantValueBool v="1"/></defaultValue><defaultWidget><name v="buttons"/><options><option><name v="booleditortype"/><value v="enumbuttons"/></option><option><name v="default"/><value v="1"/></option><option><name v="label0"/><value v="DirectX"/></option><option><name v="label1"/><value v="OpenGL"/></option></options></defaultWidget></paraminput><paraminput><identifier v="height_depth"/><uid v="1484898886"/><attributes><label v="Height Depth"/><description v="Defines the height map scale compared to the image size.&#10;A value of 1 means the height map depth is the same as its the largest border."/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.180000007"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.18"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="AO"/></paraminput><paraminput><identifier v="radius"/><uid v="1484899789"/><attributes><label v="Radius"/><description v="Adjusts the Ambient Occlusion radius. Corresponds to the &quot;Max Occluder Distance&quot; parameter in the bakers.&#10;A value of 1 equals to the largest border of the image."/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.270000011"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.27"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="AO"/></paraminput></paraminputs><graphOutputs><graphoutput><identifier v="basecolor"/><uid v="1447885835"/><attributes><label v="Base Color"/></attributes><usages><usage><components v="RGBA"/><name v="baseColor"/></usage></usages><group v="Material"/></graphoutput><graphoutput><identifier v="normal"/><uid v="1447885837"/><attributes><label v="Normal"/></attributes><usages><usage><components v="RGBA"/><name v="normal"/></usage></usages><group v="Material"/></graphoutput><graphoutput><identifier v="roughness"/><uid v="1447885839"/><attributes><label v="Roughness"/></attributes><usages><usage><components v="RGBA"/><name v="roughness"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput><graphoutput><identifier v="metallic"/><uid v="1447885841"/><attributes><label v="Metallic"/></attributes><usages><usage><components v="RGBA"/><name v="metallic"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput><graphoutput><identifier v="height"/><uid v="1447885843"/><attributes><label v="Height"/></attributes><usages><usage><components v="RGBA"/><name v="height"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput><graphoutput><identifier v="AO"/><uid v="1447885845"/><usages><usage><components v="RGBA"/><name v="ambientOcclusion"/></usage></usages><channels v="2"/></graphoutput></graphOutputs><compNodes><compNode><uid v="1447885827"/><connections><connection><identifier v="input1"/><connRef v="1447885832"/><connRefOutput v="1410296043"/></connection></connections><GUILayout><gpos v="10992 -574.916626 0"/></GUILayout><compOutputs><compOutput><uid v="1410297936"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="normal"/><parameters><parameter><name v="input2alpha"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="inversedy"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1484898875"/><paramNodes><paramNode><uid v="1484898875"/><function v="get_bool"/><type v="4"/><funcDatas><funcData><name v="get_bool"/><constantValue><constantValueString v="inversedy"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1484898868"/><paramNodes><paramNode><uid v="1484898868"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="intensity"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1447885828"/><connections><connection><identifier v="input"/><connRef v="1447885832"/><connRefOutput v="1410296043"/></connection></connections><GUILayout><gpos v="10944.418 -254.916687 0"/></GUILayout><compOutputs><compOutput><uid v="1410296137"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///hbao?dependency=1297162667"/><parameters><parameter><name v="radius"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1484899790"/><paramNodes><paramNode><uid v="1484899790"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="radius"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter><parameter><name v="height_depth"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1484898887"/><paramNodes><paramNode><uid v="1484898887"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="height_depth"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter><parameter><name v="samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="16"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1410296137"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1447885832"/><connections><connection><identifier v="destination"/><connRef v="1448050847"/><connRefOutput v="1448050848"/></connection></connections><GUILayout><gpos v="10752.418 -414.916626 0"/></GUILayout><compOutputs><compOutput><uid v="1410296043"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1447885833"/><GUILayout><gpos v="10944.418 -94.916687 0"/></GUILayout><compOutputs><compOutput><uid v="1360419830"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="uniform"/><parameters><parameter><name v="colorswitch"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1447885834"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1448114649"/><connRefOutput v="1448114650"/></connection></connections><GUILayout><gpos v="11104.418 -894.916626 0"/></GUILayout><compImplementation><compOutputBridge><output v="1447885835"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1447885836"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1447885827"/><connRefOutput v="1410297936"/></connection></connections><GUILayout><gpos v="11104.418 -574.916626 0"/></GUILayout><compImplementation><compOutputBridge><output v="1447885837"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1447885838"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1447885832"/><connRefOutput v="1410296043"/></connection></connections><GUILayout><gpos v="11104.418 -734.916626 0"/></GUILayout><compImplementation><compOutputBridge><output v="1447885839"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1447885840"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1447885833"/><connRefOutput v="1360419830"/></connection></connections><GUILayout><gpos v="11104.418 -94.916687 0"/></GUILayout><compImplementation><compOutputBridge><output v="1447885841"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1447885842"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1447885832"/><connRefOutput v="1410296043"/></connection></connections><GUILayout><gpos v="11104.418 -414.916626 0"/></GUILayout><compImplementation><compOutputBridge><output v="1447885843"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1447885844"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1447885828"/><connRefOutput v="1410296137"/></connection></connections><GUILayout><gpos v="11104.418 -254.916687 0"/></GUILayout><compImplementation><compOutputBridge><output v="1447885845"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1447889476"/><GUILayout><gpos v="-7408 432 0"/></GUILayout><compOutputs><compOutput><uid v="1447889477"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1449399972"/><parameters><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="12"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="12"/></paramValue></parameter><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="5.5999999"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.449999988"/></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="5.3499999"/></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.769999981"/></paramValue></parameter><parameter><name v="color_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.560000002"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1447889477"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1447889490"/><GUILayout><gpos v="-7408 720 0"/></GUILayout><compOutputs><compOutput><uid v="1447889477"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1449399972"/><parameters><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1447889477"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1447889552"/><connections><connection><identifier v="destination"/><connRef v="1447889490"/><connRefOutput v="1447889477"/></connection><connection><identifier v="source"/><connRef v="1447889476"/><connRefOutput v="1447889477"/></connection></connections><GUILayout><gpos v="-7184 688 0"/></GUILayout><compOutputs><compOutput><uid v="1447889553"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.50999999"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1447889593"/><connections><connection><identifier v="input1"/><connRef v="1447889552"/><connRefOutput v="1447889553"/></connection></connections><GUILayout><gpos v="-7024 688 0"/></GUILayout><compOutputs><compOutput><uid v="1447889594"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="transformation"/><parameters><parameter><name v="matrix22"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.499500006 0 0 0.499500006"/></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="-0.25 -0.25"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1447889636"/><connections><connection><identifier v="Input"/><connRef v="1447889593"/><connRefOutput v="1447889594"/></connection></connections><GUILayout><gpos v="-6864 688 0"/></GUILayout><compOutputs><compOutput><uid v="1447889637"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1449400845"/><parameters/><outputBridgings><outputBridging><uid v="1447889637"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1447889651"/><connections><connection><identifier v="input1"/><connRef v="1447889552"/><connRefOutput v="1447889553"/></connection></connections><GUILayout><gpos v="-7024 560 0"/></GUILayout><compOutputs><compOutput><uid v="1447889594"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="transformation"/><parameters><parameter><name v="matrix22"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.499500006 0 0 0.499500006"/></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.25 -0.25"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1447889652"/><connections><connection><identifier v="Input"/><connRef v="1447889651"/><connRefOutput v="1447889594"/></connection></connections><GUILayout><gpos v="-6864 560 0"/></GUILayout><compOutputs><compOutput><uid v="1447889637"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1449400845"/><parameters/><outputBridgings><outputBridging><uid v="1447889637"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1447889657"/><connections><connection><identifier v="input1"/><connRef v="1447889552"/><connRefOutput v="1447889553"/></connection></connections><GUILayout><gpos v="-7024 432 0"/></GUILayout><compOutputs><compOutput><uid v="1447889594"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="transformation"/><parameters><parameter><name v="matrix22"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.499500006 0 0 0.499500006"/></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.25 0.25"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1447889658"/><connections><connection><identifier v="Input"/><connRef v="1447889657"/><connRefOutput v="1447889594"/></connection></connections><GUILayout><gpos v="-6864 432 0"/></GUILayout><compOutputs><compOutput><uid v="1447889637"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1449400845"/><parameters/><outputBridgings><outputBridging><uid v="1447889637"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1447889666"/><connections><connection><identifier v="input1"/><connRef v="1447889552"/><connRefOutput v="1447889553"/></connection></connections><GUILayout><gpos v="-7024 304 0"/></GUILayout><compOutputs><compOutput><uid v="1447889594"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="transformation"/><parameters><parameter><name v="matrix22"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.499500006 0 0 0.499500006"/></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="-0.25 0.25"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1447889667"/><connections><connection><identifier v="Input"/><connRef v="1447889666"/><connRefOutput v="1447889594"/></connection></connections><GUILayout><gpos v="-6864 304 0"/></GUILayout><compOutputs><compOutput><uid v="1447889637"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1449400845"/><parameters/><outputBridgings><outputBridging><uid v="1447889637"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1447889712"/><connections><connection><identifier v="mask_map_input"/><connRef v="1447890772"/><connRefOutput v="1447890773"/></connection><connection><identifier v="pattern_input"/><connRef v="1448062667"/><connRefOutput v="1447889637"/></connection><connection><identifier v="pattern_input_2"/><connRef v="1448062669"/><connRefOutput v="1447889637"/></connection><connection><identifier v="pattern_input_3"/><connRef v="1448062670"/><connRefOutput v="1447889637"/></connection><connection><identifier v="pattern_input_4"/><connRef v="1448062673"/><connRefOutput v="1447889637"/></connection></connections><GUILayout><gpos v="-6370.79492 560 0"/></GUILayout><compOutputs><compOutput><uid v="1447889713"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1449399972"/><parameters><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="pattern_input_number"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="color_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.409999996"/></paramValue></parameter><parameter><name v="color_parametrization_mode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="color_parametrization_multiplier"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.629999995"/></paramValue></parameter><parameter><name v="mask_map_threshold"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.75"/></paramValue></parameter><parameter><name v="randomseed"/><relativeTo v="2"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter><parameter><name v="pattern_rotation_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="pattern_symmetry_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="5.25999975"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.409999996"/></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="3.01999998"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1447889713"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1447890106"/><connections><connection><identifier v="Source"/><connRef v="1447890115"/><connRefOutput v="1447890116"/></connection></connections><GUILayout><gpos v="-5712 144 0"/></GUILayout><compOutputs><compOutput><uid v="1447890107"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1299236171"/><parameters/><outputBridgings><outputBridging><uid v="1447890107"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1447890115"/><connections><connection><identifier v="Input"/><connRef v="1484572740"/><connRefOutput v="1484572741"/></connection></connections><GUILayout><gpos v="-5936 543.493774 0"/></GUILayout><compOutputs><compOutput><uid v="1447890116"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1449400845"/><parameters/><outputBridgings><outputBridging><uid v="1447890116"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1447890513"/><connections><connection><identifier v="Input_1"/><connRef v="1447890106"/><connRefOutput v="1447890107"/></connection></connections><GUILayout><gpos v="-5552 144 0"/></GUILayout><compOutputs><compOutput><uid v="1447890514"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1449404462"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.829999983"/></paramValue></parameter><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1447890514"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1447890615"/><connections><connection><identifier v="Source"/><connRef v="1447890513"/><connRefOutput v="1447890514"/></connection></connections><GUILayout><gpos v="-5392 144 0"/></GUILayout><compOutputs><compOutput><uid v="1447890616"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1299236171"/><parameters/><outputBridgings><outputBridging><uid v="1447890616"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1447890627"/><connections><connection><identifier v="Input"/><connRef v="1484586796"/><connRefOutput v="1484586797"/></connection></connections><GUILayout><gpos v="-4624 48 0"/></GUILayout><compOutputs><compOutput><uid v="1447890116"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1449400845"/><parameters/><outputBridgings><outputBridging><uid v="1447890116"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1447890628"/><connections><connection><identifier v="pattern_input"/><connRef v="1447889667"/><connRefOutput v="1447889637"/></connection><connection><identifier v="pattern_input_2"/><connRef v="1447889658"/><connRefOutput v="1447889637"/></connection><connection><identifier v="pattern_input_3"/><connRef v="1447889652"/><connRefOutput v="1447889637"/></connection><connection><identifier v="pattern_input_4"/><connRef v="1447889636"/><connRefOutput v="1447889637"/></connection><connection><identifier v="mask_map_input"/><connRef v="1447891274"/><connRefOutput v="1447891275"/></connection></connections><GUILayout><gpos v="-5072 65.8048401 0"/></GUILayout><compOutputs><compOutput><uid v="1447889713"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1449399972"/><parameters><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="pattern_input_number"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1484679980"/><paramNodes><paramNode><uid v="1484679980"/><function v="get_integer1"/><type v="16"/><funcDatas><funcData><name v="get_integer1"/><constantValue><constantValueString v="x_amount_1"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1484684633"/><paramNodes><paramNode><uid v="1484684633"/><function v="get_integer1"/><type v="16"/><funcDatas><funcData><name v="get_integer1"/><constantValue><constantValueString v="y_amount_1"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1484689293"/><paramNodes><paramNode><uid v="1484689293"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="scale_1"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1484698598"/><paramNodes><paramNode><uid v="1484698598"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="position_random_1"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1484693946"/><paramNodes><paramNode><uid v="1484693946"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="scale_random_1"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter><parameter><name v="mask_random"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1484753905"/><paramNodes><paramNode><uid v="1484753905"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="mask_random_2"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1484703250"/><paramNodes><paramNode><uid v="1484703250"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="offset_1"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter><parameter><name v="color_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.409999996"/></paramValue></parameter><parameter><name v="color_parametrization_mode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="color_parametrization_multiplier"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.629999995"/></paramValue></parameter><parameter><name v="mask_map_threshold"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.289999992"/></paramValue></parameter><parameter><name v="randomseed"/><relativeTo v="2"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="pattern_rotation_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="pattern_symmetry_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="rotation_random"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1484707903"/><paramNodes><paramNode><uid v="1484707903"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="rotation_random_1"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1447889713"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1447890758"/><GUILayout><gpos v="-6736 1040 0"/></GUILayout><compOutputs><compOutput><uid v="1447890759"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///gaussian_noise?dependency=1449451779"/><parameters><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1447890759"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1447890772"/><connections><connection><identifier v="Input"/><connRef v="1447890758"/><connRefOutput v="1447890759"/></connection></connections><GUILayout><gpos v="-6576 1040 0"/></GUILayout><compOutputs><compOutput><uid v="1447890773"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1449400845"/><parameters/><outputBridgings><outputBridging><uid v="1447890773"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1447891274"/><connections><connection><identifier v="destination"/><connRef v="1447890772"/><connRefOutput v="1447890773"/></connection><connection><identifier v="source"/><connRef v="1447890615"/><connRefOutput v="1447890616"/></connection></connections><GUILayout><gpos v="-5200 144 0"/></GUILayout><compOutputs><compOutput><uid v="1447891275"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1447891397"/><connections><connection><identifier v="destination"/><connRef v="1447890115"/><connRefOutput v="1447890116"/></connection><connection><identifier v="source"/><connRef v="1447891420"/><connRefOutput v="1447891421"/></connection></connections><GUILayout><gpos v="-4144 381.362305 0"/></GUILayout><compOutputs><compOutput><uid v="1447891398"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="5"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1447891420"/><connections><connection><identifier v="input1"/><connRef v="1447890627"/><connRefOutput v="1447890116"/></connection></connections><GUILayout><gpos v="-4417.67236 30.5915947 0"/></GUILayout><compOutputs><compOutput><uid v="1447891421"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelouthigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.550000012 0.550000012 0.550000012 1"/></paramValue></parameter><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.472701162 0.472701162 0.472701162 0.5"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1447893393"/><connections><connection><identifier v="source"/><connRef v="1447893394"/><connRefOutput v="1447890616"/></connection><connection><identifier v="destination"/><connRef v="1447891274"/><connRefOutput v="1447891275"/></connection></connections><GUILayout><gpos v="-4112 -240 0"/></GUILayout><compOutputs><compOutput><uid v="1447891275"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1447893394"/><connections><connection><identifier v="Source"/><connRef v="1447893396"/><connRefOutput v="1447890514"/></connection></connections><GUILayout><gpos v="-4304 -240 0"/></GUILayout><compOutputs><compOutput><uid v="1447890616"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1299236171"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="5.71000004"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1447890616"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1447893395"/><connections><connection><identifier v="Source"/><connRef v="1447890627"/><connRefOutput v="1447890116"/></connection></connections><GUILayout><gpos v="-4624 -240 0"/></GUILayout><compOutputs><compOutput><uid v="1447890107"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1299236171"/><parameters/><outputBridgings><outputBridging><uid v="1447890107"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1447893396"/><connections><connection><identifier v="Input_1"/><connRef v="1447893395"/><connRefOutput v="1447890107"/></connection></connections><GUILayout><gpos v="-4464 -240 0"/></GUILayout><compOutputs><compOutput><uid v="1447890514"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1449404462"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.829999983"/></paramValue></parameter><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1447890514"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1447893450"/><connections><connection><identifier v="pattern_input"/><connRef v="1447889667"/><connRefOutput v="1447889637"/></connection><connection><identifier v="pattern_input_2"/><connRef v="1447889658"/><connRefOutput v="1447889637"/></connection><connection><identifier v="pattern_input_3"/><connRef v="1447889652"/><connRefOutput v="1447889637"/></connection><connection><identifier v="pattern_input_4"/><connRef v="1447889636"/><connRefOutput v="1447889637"/></connection><connection><identifier v="mask_map_input"/><connRef v="1447893393"/><connRefOutput v="1447891275"/></connection></connections><GUILayout><gpos v="-3888 -417.210541 0"/></GUILayout><compOutputs><compOutput><uid v="1447889713"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1449399972"/><parameters><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="pattern_input_number"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1484717151"/><paramNodes><paramNode><uid v="1484717151"/><function v="get_integer1"/><type v="16"/><funcDatas><funcData><name v="get_integer1"/><constantValue><constantValueString v="x_amount_2"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1484721743"/><paramNodes><paramNode><uid v="1484721743"/><function v="get_integer1"/><type v="16"/><funcDatas><funcData><name v="get_integer1"/><constantValue><constantValueString v="y_amount_2"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1484726337"/><paramNodes><paramNode><uid v="1484726337"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="scale_2"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1484735521"/><paramNodes><paramNode><uid v="1484735521"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="position_random_2"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1484730928"/><paramNodes><paramNode><uid v="1484730928"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="scale_random_2"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter><parameter><name v="mask_random"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1484749300"/><paramNodes><paramNode><uid v="1484749300"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="mask_random_1"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1484740112"/><paramNodes><paramNode><uid v="1484740112"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="offset_2"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter><parameter><name v="color_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.409999996"/></paramValue></parameter><parameter><name v="color_parametrization_mode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="color_parametrization_multiplier"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.629999995"/></paramValue></parameter><parameter><name v="mask_map_threshold"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.289999992"/></paramValue></parameter><parameter><name v="rotation_random"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1484744705"/><paramNodes><paramNode><uid v="1484744705"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="rotation_random_2"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter><parameter><name v="pattern_rotation_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="pattern_symmetry_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1447889713"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1447893613"/><connections><connection><identifier v="input1"/><connRef v="1447893623"/><connRefOutput v="1447893624"/></connection></connections><GUILayout><gpos v="-16 -976 0"/></GUILayout><compOutputs><compOutput><uid v="1447893614"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelouthigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.100000001 0.100000001 0.100000001 1"/></paramValue></parameter><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.429999977 0.429999977 0.429999977 0.5"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1447893623"/><connections><connection><identifier v="Input"/><connRef v="1484600706"/><connRefOutput v="1484600707"/></connection></connections><GUILayout><gpos v="-3408 -240 0"/></GUILayout><compOutputs><compOutput><uid v="1447893624"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1449400845"/><parameters/><outputBridgings><outputBridging><uid v="1447893624"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1447894584"/><connections><connection><identifier v="pattern_input"/><connRef v="1447895990"/><connRefOutput v="1447895991"/></connection><connection><identifier v="pattern_input_2"/><connRef v="1447896023"/><connRefOutput v="1447895991"/></connection><connection><identifier v="pattern_input_3"/><connRef v="1447896032"/><connRefOutput v="1447895991"/></connection><connection><identifier v="pattern_input_4"/><connRef v="1447896040"/><connRefOutput v="1447895991"/></connection></connections><GUILayout><gpos v="-3856 -2672 0"/></GUILayout><compOutputs><compOutput><uid v="1447894585"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1449399972"/><parameters><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="pattern_input_number"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1484876726"/><paramNodes><paramNode><uid v="1484876726"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="scale_5"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1484866158"/><paramNodes><paramNode><uid v="1484866158"/><function v="get_integer1"/><type v="16"/><funcDatas><funcData><name v="get_integer1"/><constantValue><constantValueString v="x_amount_5"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter><parameter><name v="pattern_symmetry_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="pattern_symmetry_random_mode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.389999986"/></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1484882011"/><paramNodes><paramNode><uid v="1484882011"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="position_random_5"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.340000004"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1484871441"/><paramNodes><paramNode><uid v="1484871441"/><function v="get_integer1"/><type v="16"/><funcDatas><funcData><name v="get_integer1"/><constantValue><constantValueString v="y_amount_5"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter><parameter><name v="rotation"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1484887295"/><paramNodes><paramNode><uid v="1484887295"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="rotation"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter><parameter><name v="rotation_random"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1484892579"/><paramNodes><paramNode><uid v="1484892579"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="rotation_random_5"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1447894585"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1447894595"/><GUILayout><gpos v="-6032 -2416 0"/></GUILayout><compOutputs><compOutput><uid v="1447889477"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1449399972"/><parameters><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="9"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="9"/></paramValue></parameter><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="5.5999999"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.449999988"/></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="5.3499999"/></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.769999981"/></paramValue></parameter><parameter><name v="color_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.560000002"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1447889477"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1447894602"/><connections><connection><identifier v="source"/><connRef v="1447894749"/><connRefOutput v="1447894750"/></connection><connection><identifier v="destination"/><connRef v="1447894658"/><connRefOutput v="1447894659"/></connection></connections><GUILayout><gpos v="-5296 -2416 0"/></GUILayout><compOutputs><compOutput><uid v="1447894603"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.5"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1447894658"/><GUILayout><gpos v="-6032 -2640 0"/></GUILayout><compOutputs><compOutput><uid v="1447894659"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///gradient_linear_1?dependency=1449532383"/><parameters/><outputBridgings><outputBridging><uid v="1447894659"/><identifier v="Simple_Gradient"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1447894676"/><connections><connection><identifier v="input1"/><connRef v="1447894684"/><connRefOutput v="1447894685"/></connection></connections><GUILayout><gpos v="-4976 -2416 0"/></GUILayout><compOutputs><compOutput><uid v="1447894677"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="transformation"/><parameters><parameter><name v="matrix22"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="1 0 0 1.29009998"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1447894684"/><connections><connection><identifier v="Input"/><connRef v="1447894602"/><connRefOutput v="1447894603"/></connection></connections><GUILayout><gpos v="-5136 -2416 0"/></GUILayout><compOutputs><compOutput><uid v="1447894685"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1449400845"/><parameters/><outputBridgings><outputBridging><uid v="1447894685"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1447894689"/><connections><connection><identifier v="input1"/><connRef v="1447894676"/><connRefOutput v="1447894677"/></connection></connections><GUILayout><gpos v="-4816 -2416 0"/></GUILayout><compOutputs><compOutput><uid v="1447894690"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1447894720"/><connections><connection><identifier v="source"/><connRef v="1447894689"/><connRefOutput v="1447894690"/></connection><connection><identifier v="destination"/><connRef v="1447889667"/><connRefOutput v="1447889637"/></connection></connections><GUILayout><gpos v="-4464 -2416 0"/></GUILayout><compOutputs><compOutput><uid v="1447894721"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1447894749"/><connections><connection><identifier v="Source"/><connRef v="1447894595"/><connRefOutput v="1447889477"/></connection></connections><GUILayout><gpos v="-5840 -2416 0"/></GUILayout><compOutputs><compOutput><uid v="1447894750"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///invert_grayscale?dependency=1368115694"/><parameters/><outputBridgings><outputBridging><uid v="1447894750"/><identifier v="Invert_Grayscale"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1447895087"/><connections><connection><identifier v="source"/><connRef v="1447895109"/><connRefOutput v="1447895110"/></connection><connection><identifier v="destination"/><connRef v="1447894658"/><connRefOutput v="1447894659"/></connection></connections><GUILayout><gpos v="-5296 -2608 0"/></GUILayout><compOutputs><compOutput><uid v="1447894603"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.5"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1447895088"/><connections><connection><identifier v="input1"/><connRef v="1447895089"/><connRefOutput v="1447894677"/></connection></connections><GUILayout><gpos v="-4802.93652 -2608 0"/></GUILayout><compOutputs><compOutput><uid v="1447894690"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1447895089"/><connections><connection><identifier v="input1"/><connRef v="1447895090"/><connRefOutput v="1447894685"/></connection></connections><GUILayout><gpos v="-4976 -2608 0"/></GUILayout><compOutputs><compOutput><uid v="1447894677"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="transformation"/><parameters><parameter><name v="matrix22"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="1 0 0 1.29009998"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1447895090"/><connections><connection><identifier v="Input"/><connRef v="1447895087"/><connRefOutput v="1447894603"/></connection></connections><GUILayout><gpos v="-5136 -2608 0"/></GUILayout><compOutputs><compOutput><uid v="1447894685"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1449400845"/><parameters/><outputBridgings><outputBridging><uid v="1447894685"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1447895091"/><connections><connection><identifier v="source"/><connRef v="1447895088"/><connRefOutput v="1447894690"/></connection><connection><identifier v="destination"/><connRef v="1447889658"/><connRefOutput v="1447889637"/></connection></connections><GUILayout><gpos v="-4464 -2608 0"/></GUILayout><compOutputs><compOutput><uid v="1447894721"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1447895109"/><connections><connection><identifier v="input1"/><connRef v="1447894749"/><connRefOutput v="1447894750"/></connection></connections><GUILayout><gpos v="-5488 -2608 0"/><dockDistance v="160 -32"/></GUILayout><compOutputs><compOutput><uid v="1447895110"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="transformation"/><parameters><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.705500007 0.232800007"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1447895161"/><connections><connection><identifier v="source"/><connRef v="1447895178"/><connRefOutput v="1447895179"/></connection><connection><identifier v="destination"/><connRef v="1447894658"/><connRefOutput v="1447894659"/></connection></connections><GUILayout><gpos v="-5296 -2768 0"/></GUILayout><compOutputs><compOutput><uid v="1447894603"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.5"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1447895162"/><connections><connection><identifier v="input1"/><connRef v="1447895163"/><connRefOutput v="1447894677"/></connection></connections><GUILayout><gpos v="-4816 -2768 0"/></GUILayout><compOutputs><compOutput><uid v="1447894690"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1447895163"/><connections><connection><identifier v="input1"/><connRef v="1447895164"/><connRefOutput v="1447894685"/></connection></connections><GUILayout><gpos v="-4976 -2768 0"/></GUILayout><compOutputs><compOutput><uid v="1447894677"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="transformation"/><parameters><parameter><name v="matrix22"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="1 0 0 1.29009998"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1447895164"/><connections><connection><identifier v="Input"/><connRef v="1447895161"/><connRefOutput v="1447894603"/></connection></connections><GUILayout><gpos v="-5136 -2768 0"/></GUILayout><compOutputs><compOutput><uid v="1447894685"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1449400845"/><parameters/><outputBridgings><outputBridging><uid v="1447894685"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1447895165"/><connections><connection><identifier v="source"/><connRef v="1447895162"/><connRefOutput v="1447894690"/></connection><connection><identifier v="destination"/><connRef v="1447889652"/><connRefOutput v="1447889637"/></connection></connections><GUILayout><gpos v="-4464 -2768 0"/></GUILayout><compOutputs><compOutput><uid v="1447894721"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1447895178"/><connections><connection><identifier v="input1"/><connRef v="1447894749"/><connRefOutput v="1447894750"/></connection></connections><GUILayout><gpos v="-5488 -2768 0"/><dockDistance v="144 -176"/></GUILayout><compOutputs><compOutput><uid v="1447895179"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="transformation"/><parameters><parameter><name v="matrix22"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0 -1 1 0"/></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="-0.494199991 -0.350899994"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1447895361"/><connections><connection><identifier v="source"/><connRef v="1447895365"/><connRefOutput v="1447894690"/></connection><connection><identifier v="destination"/><connRef v="1447889636"/><connRefOutput v="1447889637"/></connection></connections><GUILayout><gpos v="-4464 -2928 0"/></GUILayout><compOutputs><compOutput><uid v="1447894721"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1447895362"/><connections><connection><identifier v="input1"/><connRef v="1447894749"/><connRefOutput v="1447894750"/></connection></connections><GUILayout><gpos v="-5488 -2928 0"/><dockDistance v="144 -176"/></GUILayout><compOutputs><compOutput><uid v="1447895179"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="transformation"/><parameters><parameter><name v="matrix22"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="-1 0 0 -1"/></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="-0.350899994 0.494199991"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1447895363"/><connections><connection><identifier v="input1"/><connRef v="1447895366"/><connRefOutput v="1447894685"/></connection></connections><GUILayout><gpos v="-4976 -2928 0"/></GUILayout><compOutputs><compOutput><uid v="1447894677"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="transformation"/><parameters><parameter><name v="matrix22"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="1 0 0 1.29009998"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1447895364"/><connections><connection><identifier v="source"/><connRef v="1447895362"/><connRefOutput v="1447895179"/></connection><connection><identifier v="destination"/><connRef v="1447894658"/><connRefOutput v="1447894659"/></connection></connections><GUILayout><gpos v="-5296 -2928 0"/></GUILayout><compOutputs><compOutput><uid v="1447894603"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.5"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1447895365"/><connections><connection><identifier v="input1"/><connRef v="1447895363"/><connRefOutput v="1447894677"/></connection></connections><GUILayout><gpos v="-4816 -2928 0"/></GUILayout><compOutputs><compOutput><uid v="1447894690"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.0272988509 0.0272988509 0.0272988509 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1447895366"/><connections><connection><identifier v="Input"/><connRef v="1447895364"/><connRefOutput v="1447894603"/></connection></connections><GUILayout><gpos v="-5136 -2928 0"/></GUILayout><compOutputs><compOutput><uid v="1447894685"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1449400845"/><parameters/><outputBridgings><outputBridging><uid v="1447894685"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1447895924"/><connections><connection><identifier v="Input"/><connRef v="1447894584"/><connRefOutput v="1447894585"/></connection></connections><GUILayout><gpos v="-3504 -2672 0"/></GUILayout><compOutputs><compOutput><uid v="1447895925"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1449400845"/><parameters/><outputBridgings><outputBridging><uid v="1447895925"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1447895953"/><connections><connection><identifier v="destination"/><connRef v="1447895361"/><connRefOutput v="1447894721"/></connection><connection><identifier v="source"/><connRef v="1447895966"/><connRefOutput v="1447894677"/></connection></connections><GUILayout><gpos v="-4304 -2928 0"/></GUILayout><compOutputs><compOutput><uid v="1447895954"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.75"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1447895966"/><connections><connection><identifier v="input1"/><connRef v="1447894658"/><connRefOutput v="1447894659"/></connection></connections><GUILayout><gpos v="-5136 -3198.68921 0"/></GUILayout><compOutputs><compOutput><uid v="1447894677"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="transformation"/><parameters><parameter><name v="matrix22"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="1 0 0 1.29009998"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1447895990"/><connections><connection><identifier v="Input"/><connRef v="1447895953"/><connRefOutput v="1447895954"/></connection></connections><GUILayout><gpos v="-4144 -2928 0"/></GUILayout><compOutputs><compOutput><uid v="1447895991"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1449400845"/><parameters/><outputBridgings><outputBridging><uid v="1447895991"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1447896022"/><connections><connection><identifier v="source"/><connRef v="1447895966"/><connRefOutput v="1447894677"/></connection><connection><identifier v="destination"/><connRef v="1447895165"/><connRefOutput v="1447894721"/></connection></connections><GUILayout><gpos v="-4304 -2768 0"/></GUILayout><compOutputs><compOutput><uid v="1447895954"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.75"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1447896023"/><connections><connection><identifier v="Input"/><connRef v="1447896022"/><connRefOutput v="1447895954"/></connection></connections><GUILayout><gpos v="-4144 -2768 0"/></GUILayout><compOutputs><compOutput><uid v="1447895991"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1449400845"/><parameters/><outputBridgings><outputBridging><uid v="1447895991"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1447896031"/><connections><connection><identifier v="source"/><connRef v="1447895966"/><connRefOutput v="1447894677"/></connection><connection><identifier v="destination"/><connRef v="1447895091"/><connRefOutput v="1447894721"/></connection></connections><GUILayout><gpos v="-4304 -2608 0"/></GUILayout><compOutputs><compOutput><uid v="1447895954"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.75"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1447896032"/><connections><connection><identifier v="Input"/><connRef v="1447896031"/><connRefOutput v="1447895954"/></connection></connections><GUILayout><gpos v="-4144 -2608 0"/></GUILayout><compOutputs><compOutput><uid v="1447895991"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1449400845"/><parameters/><outputBridgings><outputBridging><uid v="1447895991"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1447896039"/><connections><connection><identifier v="source"/><connRef v="1447895966"/><connRefOutput v="1447894677"/></connection><connection><identifier v="destination"/><connRef v="1447894720"/><connRefOutput v="1447894721"/></connection></connections><GUILayout><gpos v="-4304 -2416 0"/></GUILayout><compOutputs><compOutput><uid v="1447895954"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.75"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1447896040"/><connections><connection><identifier v="Input"/><connRef v="1447896039"/><connRefOutput v="1447895954"/></connection></connections><GUILayout><gpos v="-4144 -2416 0"/></GUILayout><compOutputs><compOutput><uid v="1447895991"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1449400845"/><parameters/><outputBridgings><outputBridging><uid v="1447895991"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1447896097"/><connections><connection><identifier v="input1"/><connRef v="1447895924"/><connRefOutput v="1447895925"/></connection><connection><identifier v="inputintensity"/><connRef v="1447969166"/><connRefOutput v="1447969167"/></connection></connections><GUILayout><gpos v="-3280 -2672 0"/></GUILayout><compOutputs><compOutput><uid v="1447896098"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="directionalwarp"/><parameters><parameter><name v="warpangle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.25"/></paramValue></parameter><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="53.4199982"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1447896318"/><connections><connection><identifier v="input1"/><connRef v="1447896097"/><connRefOutput v="1447896098"/></connection></connections><GUILayout><gpos v="-3088 -2928 0"/></GUILayout><compOutputs><compOutput><uid v="1447896319"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="normal"/><parameters><parameter><name v="input2alpha"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="inversedy"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="10"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1447897388"/><connections><connection><identifier v="Input"/><connRef v="1447920644"/><connRefOutput v="1447907849"/></connection></connections><GUILayout><gpos v="-2000 -2672 0"/></GUILayout><compOutputs><compOutput><uid v="1447897389"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1449400845"/><parameters/><outputBridgings><outputBridging><uid v="1447897389"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1447898295"/><connections><connection><identifier v="input1"/><connRef v="1447958486"/><connRefOutput v="1447958487"/></connection></connections><GUILayout><gpos v="-400 -2672 0"/></GUILayout><compOutputs><compOutput><uid v="1447898296"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.056034483 0.056034483 0.056034483 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1447902004"/><connections><connection><identifier v="input"/><connRef v="1447896318"/><connRefOutput v="1447896319"/></connection></connections><GUILayout><gpos v="-2928 -3120 0"/></GUILayout><compOutputs><compOutput><uid v="1447902005"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///curvature_smooth?dependency=1449590280"/><parameters><parameter><name v="normal_format"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1447902005"/><identifier v="height"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1447902016"/><connections><connection><identifier v="Input_1"/><connRef v="1447939226"/><connRefOutput v="1447939227"/></connection></connections><GUILayout><gpos v="-2768 -2928 0"/></GUILayout><compOutputs><compOutput><uid v="1447902017"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1449404462"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.729999959"/></paramValue></parameter><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1447902017"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1447907848"/><connections><connection><identifier v="Source"/><connRef v="1447896097"/><connRefOutput v="1447896098"/></connection><connection><identifier v="Effect"/><connRef v="1447942511"/><connRefOutput v="1447942512"/></connection></connections><GUILayout><gpos v="-2416 -2672 0"/></GUILayout><compOutputs><compOutput><uid v="1447907849"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///non_uniform_blur_grayscale?dependency=1290776959"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="16"/></paramValue></parameter><parameter><name v="Blades"/><relativeTo v="0"/><paramValue><constantValueInt32 v="9"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="5.82999992"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1447907849"/><identifier v="Non_Uniform_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1447908852"/><connections><connection><identifier v="Source"/><connRef v="1447902016"/><connRefOutput v="1447902017"/></connection></connections><GUILayout><gpos v="-2608 -2928 0"/></GUILayout><compOutputs><compOutput><uid v="1447908853"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///invert_grayscale?dependency=1368115694"/><parameters/><outputBridgings><outputBridging><uid v="1447908853"/><identifier v="Invert_Grayscale"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1447918071"/><connections><connection><identifier v="Source"/><connRef v="1447908852"/><connRefOutput v="1447908853"/></connection></connections><GUILayout><gpos v="-2448 -2928 0"/></GUILayout><compOutputs><compOutput><uid v="1447918072"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1299236171"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.5999999"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1447918072"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1447920595"/><connections><connection><identifier v="Input_1"/><connRef v="1447902004"/><connRefOutput v="1447902005"/></connection></connections><GUILayout><gpos v="-2768 -3120 0"/></GUILayout><compOutputs><compOutput><uid v="1447902017"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1449404462"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.359999985"/></paramValue></parameter><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1447902017"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1447920596"/><connections><connection><identifier v="Source"/><connRef v="1447920595"/><connRefOutput v="1447902017"/></connection></connections><GUILayout><gpos v="-2448 -3120 0"/></GUILayout><compOutputs><compOutput><uid v="1447918072"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1299236171"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.00999999"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1447918072"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1447920644"/><connections><connection><identifier v="Source"/><connRef v="1447907848"/><connRefOutput v="1447907849"/></connection><connection><identifier v="Effect"/><connRef v="1447920596"/><connRefOutput v="1447918072"/></connection></connections><GUILayout><gpos v="-2192 -2672 0"/></GUILayout><compOutputs><compOutput><uid v="1447907849"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///non_uniform_blur_grayscale?dependency=1290776959"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="16"/></paramValue></parameter><parameter><name v="Blades"/><relativeTo v="0"/><paramValue><constantValueInt32 v="9"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="2.32999992"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1447907849"/><identifier v="Non_Uniform_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1447939226"/><connections><connection><identifier v="Input"/><connRef v="1447896318"/><connRefOutput v="1447896319"/></connection></connections><GUILayout><gpos v="-2928 -2928 0"/></GUILayout><compOutputs><compOutput><uid v="1447939227"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///curvature?dependency=1449591180"/><parameters><parameter><name v="normal_format"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="10"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1447939227"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1447942511"/><connections><connection><identifier v="Input"/><connRef v="1447918071"/><connRefOutput v="1447918072"/></connection></connections><GUILayout><gpos v="-2288 -2928 0"/></GUILayout><compOutputs><compOutput><uid v="1447942512"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1449400845"/><parameters/><outputBridgings><outputBridging><uid v="1447942512"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1447954718"/><connections><connection><identifier v="inputintensity"/><connRef v="1447955631"/><connRefOutput v="1447955632"/></connection><connection><identifier v="input1"/><connRef v="1447897388"/><connRefOutput v="1447897389"/></connection></connections><GUILayout><gpos v="-1808 -2672 0"/></GUILayout><compOutputs><compOutput><uid v="1447954719"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="directionalwarp"/><parameters><parameter><name v="warpangle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.25"/></paramValue></parameter><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="20"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1447955631"/><connections><connection><identifier v="Source"/><connRef v="1447891397"/><connRefOutput v="1447891398"/></connection></connections><GUILayout><gpos v="-2000 -2512 0"/></GUILayout><compOutputs><compOutput><uid v="1447955632"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1299236171"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="4.63999987"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1447955632"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1447957503"/><connections><connection><identifier v="destination"/><connRef v="1447957515"/><connRefOutput v="1447957516"/></connection><connection><identifier v="source"/><connRef v="1447957536"/><connRefOutput v="1447957516"/></connection></connections><GUILayout><gpos v="-944 -2672 0"/></GUILayout><compOutputs><compOutput><uid v="1447957504"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1447957515"/><connections><connection><identifier v="input1"/><connRef v="1484833101"/><connRefOutput v="1484833102"/></connection></connections><GUILayout><gpos v="-1104 -2672 0"/></GUILayout><compOutputs><compOutput><uid v="1447957516"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelouthigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.100000001 0.100000001 0.100000001 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1447957536"/><connections><connection><identifier v="input1"/><connRef v="1447966354"/><connRefOutput v="1447966355"/></connection></connections><GUILayout><gpos v="-1104 -2832 0"/></GUILayout><compOutputs><compOutput><uid v="1447957516"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelouthigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.899999976 0.899999976 0.899999976 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1447958486"/><connections><connection><identifier v="Input"/><connRef v="1448097365"/><connRefOutput v="1448097366"/></connection></connections><GUILayout><gpos v="-592 -2672 0"/></GUILayout><compOutputs><compOutput><uid v="1447958487"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1449400845"/><parameters/><outputBridgings><outputBridging><uid v="1447958487"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1447959464"/><connections><connection><identifier v="height_top"/><connRef v="1447898295"/><connRefOutput v="1447898296"/></connection><connection><identifier v="height_bottom"/><connRef v="1447890115"/><connRefOutput v="1447890116"/></connection></connections><GUILayout><gpos v="-1104 -496 0"/></GUILayout><compOutputs><compOutput><uid v="1447959465"/><comptype v="2"/></compOutput><compOutput><uid v="1447959466"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///height_blend?dependency=1449623608"/><parameters><parameter><name v="contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1447959465"/><identifier v="blended_height"/></outputBridging><outputBridging><uid v="1447959466"/><identifier v="height_mask"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1447963210"/><connections><connection><identifier v="Input"/><connRef v="1448009866"/><connRefOutput v="1448009867"/></connection></connections><GUILayout><gpos v="1136 -368 0"/></GUILayout><compOutputs><compOutput><uid v="1447963211"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1449400845"/><parameters/><outputBridgings><outputBridging><uid v="1447963211"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1447966354"/><connections><connection><identifier v="Source"/><connRef v="1447890772"/><connRefOutput v="1447890773"/></connection></connections><GUILayout><gpos v="-4912 -336 0"/></GUILayout><compOutputs><compOutput><uid v="1447966355"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///invert_grayscale?dependency=1368115694"/><parameters/><outputBridgings><outputBridging><uid v="1447966355"/><identifier v="Invert_Grayscale"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1447969166"/><connections><connection><identifier v="Source"/><connRef v="1447890772"/><connRefOutput v="1447890773"/></connection></connections><GUILayout><gpos v="-3504 -2480 0"/></GUILayout><compOutputs><compOutput><uid v="1447969167"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///invert_grayscale?dependency=1368115694"/><parameters/><outputBridgings><outputBridging><uid v="1447969167"/><identifier v="Invert_Grayscale"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1447981830"/><connections><connection><identifier v="Source"/><connRef v="1447959464"/><connRefOutput v="1447959466"/></connection><connection><identifier v="Effect"/><connRef v="1447959464"/><connRefOutput v="1447959466"/></connection></connections><GUILayout><gpos v="-704 -560 0"/></GUILayout><compOutputs><compOutput><uid v="1447981831"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///non_uniform_blur_grayscale?dependency=1290776959"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.66999996"/></paramValue></parameter><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="16"/></paramValue></parameter><parameter><name v="Blades"/><relativeTo v="0"/><paramValue><constantValueInt32 v="9"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1447981831"/><identifier v="Non_Uniform_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1447995426"/><connections><connection><identifier v="destination"/><connRef v="1447995450"/><connRefOutput v="1447995451"/></connection><connection><identifier v="source"/><connRef v="1447995468"/><connRefOutput v="1447995451"/></connection></connections><GUILayout><gpos v="-480 -368 0"/></GUILayout><compOutputs><compOutput><uid v="1447995427"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1447995439"/><connections><connection><identifier v="Input"/><connRef v="1447959464"/><connRefOutput v="1447959465"/></connection></connections><GUILayout><gpos v="-848 -368 0"/></GUILayout><compOutputs><compOutput><uid v="1447995440"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1449400845"/><parameters/><outputBridgings><outputBridging><uid v="1447995440"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1447995450"/><connections><connection><identifier v="input1"/><connRef v="1447995439"/><connRefOutput v="1447995440"/></connection></connections><GUILayout><gpos v="-688 -368 0"/></GUILayout><compOutputs><compOutput><uid v="1447995451"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelouthigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.970000029 0.970000029 0.970000029 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1447995468"/><connections><connection><identifier v="input1"/><connRef v="1447981830"/><connRefOutput v="1447981831"/></connection></connections><GUILayout><gpos v="-576 -560 0"/></GUILayout><compOutputs><compOutput><uid v="1447995451"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelouthigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.0299999993 0.0299999993 0.0299999993 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448006807"/><connections><connection><identifier v="Source"/><connRef v="1448006808"/><connRefOutput v="1447959466"/></connection><connection><identifier v="Effect"/><connRef v="1448006808"/><connRefOutput v="1447959466"/></connection></connections><GUILayout><gpos v="-144 -560 0"/></GUILayout><compOutputs><compOutput><uid v="1447981831"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///non_uniform_blur_grayscale?dependency=1290776959"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.66999996"/></paramValue></parameter><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="16"/></paramValue></parameter><parameter><name v="Blades"/><relativeTo v="0"/><paramValue><constantValueInt32 v="9"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1447981831"/><identifier v="Non_Uniform_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448006808"/><connections><connection><identifier v="height_bottom"/><connRef v="1447995426"/><connRefOutput v="1447995427"/></connection><connection><identifier v="height_top"/><connRef v="1447891420"/><connRefOutput v="1447891421"/></connection></connections><GUILayout><gpos v="-289.229187 -368 0"/></GUILayout><compOutputs><compOutput><uid v="1447959465"/><comptype v="2"/></compOutput><compOutput><uid v="1447959466"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///height_blend?dependency=1449623608"/><parameters><parameter><name v="contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1447959465"/><identifier v="blended_height"/></outputBridging><outputBridging><uid v="1447959466"/><identifier v="height_mask"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448006809"/><connections><connection><identifier v="input1"/><connRef v="1448006812"/><connRefOutput v="1447995440"/></connection></connections><GUILayout><gpos v="62.770813 -368 0"/></GUILayout><compOutputs><compOutput><uid v="1447995451"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelouthigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.970000029 0.970000029 0.970000029 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448006810"/><connections><connection><identifier v="input1"/><connRef v="1448006807"/><connRefOutput v="1447981831"/></connection></connections><GUILayout><gpos v="-16 -560 0"/></GUILayout><compOutputs><compOutput><uid v="1447995451"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelouthigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.0299999993 0.0299999993 0.0299999993 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448006811"/><connections><connection><identifier v="destination"/><connRef v="1448006809"/><connRefOutput v="1447995451"/></connection><connection><identifier v="source"/><connRef v="1448006810"/><connRefOutput v="1447995451"/></connection></connections><GUILayout><gpos v="270.770813 -368 0"/></GUILayout><compOutputs><compOutput><uid v="1447995427"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448006812"/><connections><connection><identifier v="Input"/><connRef v="1448006808"/><connRefOutput v="1447959465"/></connection></connections><GUILayout><gpos v="-97.229187 -368 0"/></GUILayout><compOutputs><compOutput><uid v="1447995440"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1449400845"/><parameters/><outputBridgings><outputBridging><uid v="1447995440"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448009793"/><connections><connection><identifier v="Source"/><connRef v="1448006811"/><connRefOutput v="1447995427"/></connection><connection><identifier v="Effect"/><connRef v="1448009826"/><connRefOutput v="1448009827"/></connection></connections><GUILayout><gpos v="560 -368 0"/></GUILayout><compOutputs><compOutput><uid v="1447981831"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///non_uniform_blur_grayscale?dependency=1290776959"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.66999996"/></paramValue></parameter><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="16"/></paramValue></parameter><parameter><name v="Blades"/><relativeTo v="0"/><paramValue><constantValueInt32 v="9"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1447981831"/><identifier v="Non_Uniform_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448009826"/><connections><connection><identifier v="input1"/><connRef v="1447893623"/><connRefOutput v="1447893624"/></connection></connections><GUILayout><gpos v="-48 -720 0"/></GUILayout><compOutputs><compOutput><uid v="1448009827"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.188218385 0.188218385 0.188218385 0.5"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448009866"/><connections><connection><identifier v="destination"/><connRef v="1448009793"/><connRefOutput v="1447981831"/></connection><connection><identifier v="source"/><connRef v="1447893613"/><connRefOutput v="1447893614"/></connection></connections><GUILayout><gpos v="767.825928 -368 0"/></GUILayout><compOutputs><compOutput><uid v="1448009867"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448016461"/><connections><connection><identifier v="input1"/><connRef v="1447954718"/><connRefOutput v="1447954719"/></connection><connection><identifier v="inputintensity"/><connRef v="1448016470"/><connRefOutput v="1447955632"/></connection></connections><GUILayout><gpos v="-1552 -2576 0"/></GUILayout><compOutputs><compOutput><uid v="1447954719"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="directionalwarp"/><parameters><parameter><name v="warpangle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.25"/></paramValue></parameter><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="13.7299995"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448016470"/><connections><connection><identifier v="Source"/><connRef v="1447893623"/><connRefOutput v="1447893624"/></connection></connections><GUILayout><gpos v="-1808 -2512 0"/></GUILayout><compOutputs><compOutput><uid v="1447955632"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1299236171"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="4.63999987"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1447955632"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448020707"/><GUILayout><gpos v="1040 -2192 0"/></GUILayout><compOutputs><compOutput><uid v="1448020708"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///waveform_1?dependency=1446771957"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1024"/></paramValue></parameter><parameter><name v="SizeMax"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0700000003"/></paramValue></parameter><parameter><name v="WaveNumber"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="Noise"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="Pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="9"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448020708"/><identifier v="Waveform"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448020789"/><connections><connection><identifier v="input1"/><connRef v="1448020707"/><connRefOutput v="1448020708"/></connection></connections><GUILayout><gpos v="1200 -2192 0"/></GUILayout><compOutputs><compOutput><uid v="1448020790"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="transformation"/><parameters><parameter><name v="tiling"/><relativeTo v="0"/><paramValue><constantValueInt32 v="0"/></paramValue></parameter><parameter><name v="matrix22"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0 1 -1 0"/></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.255600005 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448020870"/><connections><connection><identifier v="Source"/><connRef v="1448020789"/><connRefOutput v="1448020790"/></connection></connections><GUILayout><gpos v="1360 -2192 0"/></GUILayout><compOutputs><compOutput><uid v="1448020871"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1299236171"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.049999997"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448020871"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448020911"/><connections><connection><identifier v="Input_1"/><connRef v="1448020870"/><connRefOutput v="1448020871"/></connection></connections><GUILayout><gpos v="1520 -2192 0"/></GUILayout><compOutputs><compOutput><uid v="1448020912"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1449404462"/><parameters><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448020912"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448021083"/><connections><connection><identifier v="source"/><connRef v="1448021504"/><connRefOutput v="1448021505"/></connection><connection><identifier v="destination"/><connRef v="1448037264"/><connRefOutput v="1448037265"/></connection></connections><GUILayout><gpos v="2384 -2192 0"/></GUILayout><compOutputs><compOutput><uid v="1448021084"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448021094"/><GUILayout><gpos v="1840 -2480 0"/></GUILayout><compOutputs><compOutput><uid v="1448021095"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///gradient_linear_1?dependency=1449532383"/><parameters/><outputBridgings><outputBridging><uid v="1448021095"/><identifier v="Simple_Gradient"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448021100"/><connections><connection><identifier v="input1"/><connRef v="1448021094"/><connRefOutput v="1448021095"/></connection></connections><GUILayout><gpos v="2000 -2480 0"/></GUILayout><compOutputs><compOutput><uid v="1448021101"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="curve"/><parameters><parameter><name v="exposecurve"/><paramValue><constantValueBool v="0"/></paramValue></parameter></parameters><paramsArrays><paramsArray><name v="curveluminance"/><uid v="1448021108"/><paramsArrayCells><paramsArrayCell><uid v="1448021109"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat2 v="0 0"/></paramValue></parameter><parameter><name v="left"/><paramValue><constantValueFloat2 v="0 0"/></paramValue></parameter><parameter><name v="isLeftBroken"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="right"/><paramValue><constantValueFloat2 v="0.100000001 0"/></paramValue></parameter><parameter><name v="isRightBroken"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="isLocked"/><paramValue><constantValueBool v="1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1448021110"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat2 v="1 0.444999993"/></paramValue></parameter><parameter><name v="left"/><paramValue><constantValueFloat2 v="0.899999976 0.444999993"/></paramValue></parameter><parameter><name v="isLeftBroken"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="right"/><paramValue><constantValueFloat2 v="1 0.444999993"/></paramValue></parameter><parameter><name v="isRightBroken"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="isLocked"/><paramValue><constantValueBool v="1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1448021111"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat2 v="0.324000001 0.748000026"/></paramValue></parameter><parameter><name v="left"/><paramValue><constantValueFloat2 v="0.0960000008 0.63499999"/></paramValue></parameter><parameter><name v="isLeftBroken"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="right"/><paramValue><constantValueFloat2 v="0.782000005 0.976000011"/></paramValue></parameter><parameter><name v="isRightBroken"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="isLocked"/><paramValue><constantValueBool v="1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1448021504"/><connections><connection><identifier v="input1"/><connRef v="1448021100"/><connRefOutput v="1448021101"/></connection></connections><GUILayout><gpos v="2160 -2480 0"/></GUILayout><compOutputs><compOutput><uid v="1448021505"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="transformation"/><parameters><parameter><name v="matrix22"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="1 0 0 1.3506"/></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0 -0.175300002"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448021705"/><connections><connection><identifier v="input"/><connRef v="1448020911"/><connRefOutput v="1448020912"/></connection></connections><GUILayout><gpos v="1680 -2192 0"/></GUILayout><compOutputs><compOutput><uid v="1448021706"/><comptype v="2"/></compOutput><compOutput><uid v="1448021707"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///bevel?dependency=1449405586"/><parameters><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0199999996"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448021706"/><identifier v="height"/></outputBridging><outputBridging><uid v="1448021707"/><identifier v="normal"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448032198"/><connections><connection><identifier v="input"/><connRef v="1448020911"/><connRefOutput v="1448020912"/></connection></connections><GUILayout><gpos v="1488 -1936 0"/></GUILayout><compOutputs><compOutput><uid v="1448032199"/><comptype v="2"/></compOutput><compOutput><uid v="1448032200"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///bevel?dependency=1449405586"/><parameters><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="-0.099999994"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448032199"/><identifier v="height"/></outputBridging><outputBridging><uid v="1448032200"/><identifier v="normal"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448037264"/><connections><connection><identifier v="source"/><connRef v="1448037271"/><connRefOutput v="1448037272"/></connection><connection><identifier v="destination"/><connRef v="1448021705"/><connRefOutput v="1448021706"/></connection></connections><GUILayout><gpos v="1872 -2192 0"/></GUILayout><compOutputs><compOutput><uid v="1448037265"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.310000002"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448037271"/><connections><connection><identifier v="Source"/><connRef v="1448032198"/><connRefOutput v="1448032199"/></connection></connections><GUILayout><gpos v="1648 -1936 0"/></GUILayout><compOutputs><compOutput><uid v="1448037272"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///invert_grayscale?dependency=1368115694"/><parameters/><outputBridgings><outputBridging><uid v="1448037272"/><identifier v="Invert_Grayscale"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448038822"/><connections><connection><identifier v="pattern_input_1"/><connRef v="1448039892"/><connRefOutput v="1448039893"/></connection></connections><GUILayout><gpos v="2864 -2192 0"/></GUILayout><compOutputs><compOutput><uid v="1448038823"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///splatter_circular?dependency=1448559733"/><parameters><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="pattern_scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="21.9899998"/></paramValue></parameter><parameter><name v="pattern_scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.599999964"/></paramValue></parameter><parameter><name v="radius"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="pattern_size_random"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.109999999 0.50999999"/></paramValue></parameter><parameter><name v="pattern_size"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.100000001 0.0700000003"/></paramValue></parameter><parameter><name v="spread"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.67999995"/></paramValue></parameter><parameter><name v="luminance_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.669999957"/></paramValue></parameter><parameter><name v="angle_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="pattern_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="12"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448038823"/><identifier v="Splatter_Circular"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448039699"/><GUILayout><gpos v="2384 -2416 0"/></GUILayout><compOutputs><compOutput><uid v="1448039700"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///shape?dependency=1311189026"/><parameters><parameter><name v="Size"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0199999996"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448039700"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448039873"/><connections><connection><identifier v="source"/><connRef v="1448039699"/><connRefOutput v="1448039700"/></connection><connection><identifier v="destination"/><connRef v="1448039892"/><connRefOutput v="1448039893"/></connection></connections><GUILayout><gpos v="2736 -2416 0"/></GUILayout><compOutputs><compOutput><uid v="1448039874"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448039892"/><connections><connection><identifier v="input1"/><connRef v="1448021083"/><connRefOutput v="1448021084"/></connection></connections><GUILayout><gpos v="2544 -2192 0"/></GUILayout><compOutputs><compOutput><uid v="1448039893"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="transformation"/><parameters><parameter><name v="tiling"/><relativeTo v="0"/><paramValue><constantValueInt32 v="0"/></paramValue></parameter><parameter><name v="matrix22"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="1.78989995 0 0 1.78989995"/></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0 0.504800022"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448040356"/><connections><connection><identifier v="pattern_input_1"/><connRef v="1448039892"/><connRefOutput v="1448039893"/></connection></connections><GUILayout><gpos v="2864 -2032 0"/></GUILayout><compOutputs><compOutput><uid v="1448038823"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///splatter_circular?dependency=1448559733"/><parameters><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="pattern_scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="18.039999"/></paramValue></parameter><parameter><name v="pattern_scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.599999964"/></paramValue></parameter><parameter><name v="radius"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="pattern_size_random"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.109999999 0.50999999"/></paramValue></parameter><parameter><name v="pattern_size"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.100000001 0.0700000003"/></paramValue></parameter><parameter><name v="spread"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.67999995"/></paramValue></parameter><parameter><name v="luminance_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.899999976"/></paramValue></parameter><parameter><name v="angle_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="randomseed"/><relativeTo v="2"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter><parameter><name v="pattern_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448038823"/><identifier v="Splatter_Circular"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448040378"/><connections><connection><identifier v="pattern_input_1"/><connRef v="1448039892"/><connRefOutput v="1448039893"/></connection></connections><GUILayout><gpos v="2864 -1872 0"/></GUILayout><compOutputs><compOutput><uid v="1448038823"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///splatter_circular?dependency=1448559733"/><parameters><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="pattern_scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="18.039999"/></paramValue></parameter><parameter><name v="pattern_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="5"/></paramValue></parameter><parameter><name v="pattern_scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.599999964"/></paramValue></parameter><parameter><name v="radius"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="pattern_size_random"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.109999999 0.50999999"/></paramValue></parameter><parameter><name v="pattern_size"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.100000001 0.0700000003"/></paramValue></parameter><parameter><name v="spread"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.5"/></paramValue></parameter><parameter><name v="luminance_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.669999957"/></paramValue></parameter><parameter><name v="angle_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="randomseed"/><relativeTo v="2"/><paramValue><constantValueInt32 v="9"/></paramValue></parameter><parameter><name v="pattern_rotation_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.340000004"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448038823"/><identifier v="Splatter_Circular"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448040530"/><connections><connection><identifier v="input"/><connRef v="1448009866"/><connRefOutput v="1448009867"/></connection></connections><GUILayout><gpos v="912 -1552 0"/></GUILayout><compOutputs><compOutput><uid v="1448040531"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///hbao?dependency=1297162667"/><parameters><parameter><name v="height_depth"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.439999998"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448040531"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448041431"/><connections><connection><identifier v="destination"/><connRef v="1448050058"/><connRefOutput v="1448050059"/></connection><connection><identifier v="source"/><connRef v="1448050198"/><connRefOutput v="1448050199"/></connection></connections><GUILayout><gpos v="1264 -1552 0"/></GUILayout><compOutputs><compOutput><uid v="1448041432"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448041438"/><connections><connection><identifier v="Source"/><connRef v="1448050083"/><connRefOutput v="1448050084"/></connection></connections><GUILayout><gpos v="656 -1808 0"/></GUILayout><compOutputs><compOutput><uid v="1448041439"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1299236171"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.64999998"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448041439"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448041444"/><connections><connection><identifier v="Input_1"/><connRef v="1448041438"/><connRefOutput v="1448041439"/></connection></connections><GUILayout><gpos v="816 -1808 0"/></GUILayout><compOutputs><compOutput><uid v="1448041445"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1449404462"/><parameters><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0199999996"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448041445"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448050058"/><connections><connection><identifier v="Source"/><connRef v="1448040530"/><connRefOutput v="1448040531"/></connection></connections><GUILayout><gpos v="1072 -1552 0"/></GUILayout><compOutputs><compOutput><uid v="1448050059"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///invert_grayscale?dependency=1368115694"/><parameters/><outputBridgings><outputBridging><uid v="1448050059"/><identifier v="Invert_Grayscale"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448050083"/><connections><connection><identifier v="destination"/><connRef v="1447959464"/><connRefOutput v="1447959466"/></connection><connection><identifier v="source"/><connRef v="1448006807"/><connRefOutput v="1447981831"/></connection></connections><GUILayout><gpos v="496 -1808 0"/></GUILayout><compOutputs><compOutput><uid v="1448050084"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448050198"/><connections><connection><identifier v="Source"/><connRef v="1448041444"/><connRefOutput v="1448041445"/></connection></connections><GUILayout><gpos v="976 -1808 0"/></GUILayout><compOutputs><compOutput><uid v="1448050199"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///invert_grayscale?dependency=1368115694"/><parameters/><outputBridgings><outputBridging><uid v="1448050199"/><identifier v="Invert_Grayscale"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448050213"/><connections><connection><identifier v="Input_1"/><connRef v="1448050214"/><connRefOutput v="1448041439"/></connection></connections><GUILayout><gpos v="976 -1936 0"/></GUILayout><compOutputs><compOutput><uid v="1448041445"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1449404462"/><parameters><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.889999986"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448041445"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448050214"/><connections><connection><identifier v="Source"/><connRef v="1448009826"/><connRefOutput v="1448009827"/></connection></connections><GUILayout><gpos v="816 -1936 0"/></GUILayout><compOutputs><compOutput><uid v="1448041439"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1299236171"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.64999998"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448041439"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448050262"/><connections><connection><identifier v="destination"/><connRef v="1448041431"/><connRefOutput v="1448041432"/></connection><connection><identifier v="source"/><connRef v="1448050213"/><connRefOutput v="1448041445"/></connection></connections><GUILayout><gpos v="1424 -1552 0"/></GUILayout><compOutputs><compOutput><uid v="1448041432"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448050302"/><connections><connection><identifier v="pattern_input"/><connRef v="1448038822"/><connRefOutput v="1448038823"/></connection><connection><identifier v="pattern_input_2"/><connRef v="1448040356"/><connRefOutput v="1448038823"/></connection><connection><identifier v="pattern_input_3"/><connRef v="1448040378"/><connRefOutput v="1448038823"/></connection><connection><identifier v="mask_map_input"/><connRef v="1448050262"/><connRefOutput v="1448041432"/></connection></connections><GUILayout><gpos v="3088 -2106.66675 0"/></GUILayout><compOutputs><compOutput><uid v="1448050303"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1449399972"/><parameters><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="pattern_input_number"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="pattern_rotation_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="pattern_symmetry_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="color_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.629999995"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.92999995"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.449999988"/></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="3.12999988"/></paramValue></parameter><parameter><name v="mask_map_threshold"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.25"/></paramValue></parameter><parameter><name v="mask_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.109999999"/></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.269999981"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448050303"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448050788"/><connections><connection><identifier v="Input"/><connRef v="1484812091"/><connRefOutput v="1484812092"/></connection></connections><GUILayout><gpos v="3376 -2128 0"/></GUILayout><compOutputs><compOutput><uid v="1448050789"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1449400845"/><parameters/><outputBridgings><outputBridging><uid v="1448050789"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448050795"/><connections><connection><identifier v="input1"/><connRef v="1448050788"/><connRefOutput v="1448050789"/></connection></connections><GUILayout><gpos v="3568 -2064 0"/></GUILayout><compOutputs><compOutput><uid v="1448050796"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.24568966 0.24568966 0.24568966 0.5"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448050827"/><connections><connection><identifier v="input1"/><connRef v="1448050788"/><connRefOutput v="1448050789"/></connection></connections><GUILayout><gpos v="3472 -2288 0"/></GUILayout><compOutputs><compOutput><uid v="1448050828"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelouthigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.200000003 0.200000003 0.200000003 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448050847"/><connections><connection><identifier v="source"/><connRef v="1448050827"/><connRefOutput v="1448050828"/></connection><connection><identifier v="destination"/><connRef v="1448050869"/><connRefOutput v="1447981831"/></connection></connections><GUILayout><gpos v="4336 -417.918762 0"/></GUILayout><compOutputs><compOutput><uid v="1448050848"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448050869"/><connections><connection><identifier v="Effect"/><connRef v="1448050795"/><connRefOutput v="1448050796"/></connection><connection><identifier v="Source"/><connRef v="1448141686"/><connRefOutput v="1448050848"/></connection></connections><GUILayout><gpos v="4144 -417.918762 0"/></GUILayout><compOutputs><compOutput><uid v="1447981831"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///non_uniform_blur_grayscale?dependency=1290776959"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.66999996"/></paramValue></parameter><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="16"/></paramValue></parameter><parameter><name v="Blades"/><relativeTo v="0"/><paramValue><constantValueInt32 v="9"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1447981831"/><identifier v="Non_Uniform_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448055521"/><connections><connection><identifier v="input1"/><connRef v="1448055529"/><connRefOutput v="1448055530"/></connection></connections><GUILayout><gpos v="8592 -3905.81104 0"/></GUILayout><compOutputs><compOutput><uid v="1448055522"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1448055871"/><paramsArrayCells><paramsArrayCell><uid v="1448055872"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0 0 0 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1448055873"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.353892207 0.446280986 0.166683257 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1448055529"/><connections><connection><identifier v="Input_1"/><connRef v="1448050788"/><connRefOutput v="1448050789"/></connection></connections><GUILayout><gpos v="8400 -3905.81104 0"/></GUILayout><compOutputs><compOutput><uid v="1448055530"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1449404462"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448055530"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448055561"/><connections><connection><identifier v="destination"/><connRef v="1448055571"/><connRefOutput v="1448055572"/></connection><connection><identifier v="source"/><connRef v="1448006808"/><connRefOutput v="1447959466"/></connection></connections><GUILayout><gpos v="5566.88184 -3069.11401 0"/></GUILayout><compOutputs><compOutput><uid v="1448055562"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448055571"/><connections><connection><identifier v="Source"/><connRef v="1447959464"/><connRefOutput v="1447959466"/></connection></connections><GUILayout><gpos v="5406.88184 -3069.11401 0"/></GUILayout><compOutputs><compOutput><uid v="1448055572"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///invert_grayscale?dependency=1368115694"/><parameters/><outputBridgings><outputBridging><uid v="1448055572"/><identifier v="Invert_Grayscale"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448055612"/><connections><connection><identifier v="destination"/><connRef v="1448055561"/><connRefOutput v="1448055562"/></connection><connection><identifier v="source"/><connRef v="1448055633"/><connRefOutput v="1448055634"/></connection></connections><GUILayout><gpos v="5758.88184 -3069.11401 0"/></GUILayout><compOutputs><compOutput><uid v="1448055562"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448055633"/><connections><connection><identifier v="Input_1"/><connRef v="1447893623"/><connRefOutput v="1447893624"/></connection></connections><GUILayout><gpos v="5566.88184 -3197.11401 0"/></GUILayout><compOutputs><compOutput><uid v="1448055634"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1449404462"/><parameters><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448055634"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448055833"/><connections><connection><identifier v="opacity"/><connRef v="1448055529"/><connRefOutput v="1448055530"/></connection><connection><identifier v="destination"/><connRef v="1448169787"/><connRefOutput v="1448169788"/></connection><connection><identifier v="source"/><connRef v="1484898384"/><connRefOutput v="1484897897"/></connection></connections><GUILayout><gpos v="9008 -3073.81104 0"/></GUILayout><compOutputs><compOutput><uid v="1448055834"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1448060707"/><connections><connection><identifier v="mask"/><connRef v="1448055571"/><connRefOutput v="1448055572"/></connection></connections><GUILayout><gpos v="5566.88184 -3421.11401 0"/></GUILayout><compOutputs><compOutput><uid v="1448060708"/><comptype v="1"/></compOutput><compOutput><uid v="1448060709"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill?dependency=1323881949"/><parameters/><outputBridgings><outputBridging><uid v="1448060708"/><identifier v="output"/></outputBridging><outputBridging><uid v="1448060709"/><identifier v="referenceMap"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448060818"/><connections><connection><identifier v="input"/><connRef v="1448060707"/><connRefOutput v="1448060708"/></connection></connections><GUILayout><gpos v="5726.88184 -3421.11401 0"/></GUILayout><compOutputs><compOutput><uid v="1448060819"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_random_grayscale?dependency=1323881949"/><parameters/><outputBridgings><outputBridging><uid v="1448060819"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448060906"/><connections><connection><identifier v="input"/><connRef v="1448060907"/><connRefOutput v="1448060708"/></connection></connections><GUILayout><gpos v="5726.88184 -3549.11401 0"/></GUILayout><compOutputs><compOutput><uid v="1448060819"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_random_grayscale?dependency=1323881949"/><parameters/><outputBridgings><outputBridging><uid v="1448060819"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448060907"/><connections><connection><identifier v="mask"/><connRef v="1448006808"/><connRefOutput v="1447959466"/></connection></connections><GUILayout><gpos v="5566.88184 -3549.11401 0"/></GUILayout><compOutputs><compOutput><uid v="1448060708"/><comptype v="1"/></compOutput><compOutput><uid v="1448060709"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill?dependency=1323881949"/><parameters/><outputBridgings><outputBridging><uid v="1448060708"/><identifier v="output"/></outputBridging><outputBridging><uid v="1448060709"/><identifier v="referenceMap"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448060998"/><connections><connection><identifier v="input"/><connRef v="1448060999"/><connRefOutput v="1448060708"/></connection></connections><GUILayout><gpos v="5726.88184 -3677.11401 0"/></GUILayout><compOutputs><compOutput><uid v="1448060819"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_random_grayscale?dependency=1323881949"/><parameters/><outputBridgings><outputBridging><uid v="1448060819"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448060999"/><connections><connection><identifier v="mask"/><connRef v="1448055633"/><connRefOutput v="1448055634"/></connection></connections><GUILayout><gpos v="5566.88184 -3677.11401 0"/></GUILayout><compOutputs><compOutput><uid v="1448060708"/><comptype v="1"/></compOutput><compOutput><uid v="1448060709"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill?dependency=1323881949"/><parameters/><outputBridgings><outputBridging><uid v="1448060708"/><identifier v="output"/></outputBridging><outputBridging><uid v="1448060709"/><identifier v="referenceMap"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448061086"/><connections><connection><identifier v="destination"/><connRef v="1448060818"/><connRefOutput v="1448060819"/></connection><connection><identifier v="source"/><connRef v="1448060906"/><connRefOutput v="1448060819"/></connection><connection><identifier v="opacity"/><connRef v="1448006808"/><connRefOutput v="1447959466"/></connection></connections><GUILayout><gpos v="6046.88184 -3421.11401 0"/></GUILayout><compOutputs><compOutput><uid v="1448061087"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1448061113"/><connections><connection><identifier v="destination"/><connRef v="1448061086"/><connRefOutput v="1448061087"/></connection><connection><identifier v="opacity"/><connRef v="1448055633"/><connRefOutput v="1448055634"/></connection><connection><identifier v="source"/><connRef v="1448060998"/><connRefOutput v="1448060819"/></connection></connections><GUILayout><gpos v="6206.88184 -3421.11401 0"/></GUILayout><compOutputs><compOutput><uid v="1448061087"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1448061132"/><connections><connection><identifier v="source"/><connRef v="1448062607"/><connRefOutput v="1448062608"/></connection><connection><identifier v="opacity"/><connRef v="1448055612"/><connRefOutput v="1448055562"/></connection><connection><identifier v="destination"/><connRef v="1484897926"/><connRefOutput v="1484897927"/></connection></connections><GUILayout><gpos v="6526.88184 -3069.11401 0"/></GUILayout><compOutputs><compOutput><uid v="1448061133"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1484898065"/><paramNodes><paramNode><uid v="1484898065"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="opacitymult_6"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448062607"/><connections><connection><identifier v="input1"/><connRef v="1448061113"/><connRefOutput v="1448061087"/></connection></connections><GUILayout><gpos v="6366.88184 -3421.11401 0"/></GUILayout><compOutputs><compOutput><uid v="1448062608"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1448062663"/><connections><connection><identifier v="destination"/><connRef v="1448062665"/><connRefOutput v="1447889477"/></connection><connection><identifier v="source"/><connRef v="1448062672"/><connRefOutput v="1447889477"/></connection></connections><GUILayout><gpos v="-7184 80 0"/></GUILayout><compOutputs><compOutput><uid v="1447889553"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.50999999"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448062664"/><connections><connection><identifier v="input1"/><connRef v="1448062663"/><connRefOutput v="1447889553"/></connection></connections><GUILayout><gpos v="-7024 -176 0"/></GUILayout><compOutputs><compOutput><uid v="1447889594"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="transformation"/><parameters><parameter><name v="matrix22"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.499500006 0 0 0.499500006"/></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.25 0.25"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448062665"/><GUILayout><gpos v="-7408 112 0"/></GUILayout><compOutputs><compOutput><uid v="1447889477"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1449399972"/><parameters><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1447889477"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448062666"/><connections><connection><identifier v="input1"/><connRef v="1448062663"/><connRefOutput v="1447889553"/></connection></connections><GUILayout><gpos v="-7024 -48 0"/></GUILayout><compOutputs><compOutput><uid v="1447889594"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="transformation"/><parameters><parameter><name v="matrix22"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.499500006 0 0 0.499500006"/></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.25 -0.25"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448062667"/><connections><connection><identifier v="Input"/><connRef v="1448062671"/><connRefOutput v="1447889594"/></connection></connections><GUILayout><gpos v="-6864 -304 0"/></GUILayout><compOutputs><compOutput><uid v="1447889637"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1449400845"/><parameters/><outputBridgings><outputBridging><uid v="1447889637"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448062668"/><connections><connection><identifier v="input1"/><connRef v="1448062663"/><connRefOutput v="1447889553"/></connection></connections><GUILayout><gpos v="-7024 80 0"/></GUILayout><compOutputs><compOutput><uid v="1447889594"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="transformation"/><parameters><parameter><name v="matrix22"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.499500006 0 0 0.499500006"/></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="-0.25 -0.25"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448062669"/><connections><connection><identifier v="Input"/><connRef v="1448062664"/><connRefOutput v="1447889594"/></connection></connections><GUILayout><gpos v="-6864 -176 0"/></GUILayout><compOutputs><compOutput><uid v="1447889637"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1449400845"/><parameters/><outputBridgings><outputBridging><uid v="1447889637"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448062670"/><connections><connection><identifier v="Input"/><connRef v="1448062666"/><connRefOutput v="1447889594"/></connection></connections><GUILayout><gpos v="-6864 -48 0"/></GUILayout><compOutputs><compOutput><uid v="1447889637"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1449400845"/><parameters/><outputBridgings><outputBridging><uid v="1447889637"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448062671"/><connections><connection><identifier v="input1"/><connRef v="1448062663"/><connRefOutput v="1447889553"/></connection></connections><GUILayout><gpos v="-7024 -304 0"/></GUILayout><compOutputs><compOutput><uid v="1447889594"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="transformation"/><parameters><parameter><name v="matrix22"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.499500006 0 0 0.499500006"/></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="-0.25 0.25"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448062672"/><GUILayout><gpos v="-7408 -176 0"/></GUILayout><compOutputs><compOutput><uid v="1447889477"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1449399972"/><parameters><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="14"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="14"/></paramValue></parameter><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="5.5999999"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.449999988"/></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="5.3499999"/></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.769999981"/></paramValue></parameter><parameter><name v="color_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.560000002"/></paramValue></parameter><parameter><name v="randomseed"/><relativeTo v="2"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1447889477"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448062673"/><connections><connection><identifier v="Input"/><connRef v="1448062668"/><connRefOutput v="1447889594"/></connection></connections><GUILayout><gpos v="-6864 80 0"/></GUILayout><compOutputs><compOutput><uid v="1447889637"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1449400845"/><parameters/><outputBridgings><outputBridging><uid v="1447889637"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448089502"/><connections><connection><identifier v="Source"/><connRef v="1448089523"/><connRefOutput v="1448089524"/></connection></connections><GUILayout><gpos v="5886.88184 -3837.11401 0"/></GUILayout><compOutputs><compOutput><uid v="1448089503"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///highpass_grayscale?dependency=1449753752"/><parameters><parameter><name v="Radius"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="4.26999998"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448089503"/><identifier v="Highpass"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448089523"/><connections><connection><identifier v="destination"/><connRef v="1447891397"/><connRefOutput v="1447891398"/></connection><connection><identifier v="source"/><connRef v="1448089545"/><connRefOutput v="1448089546"/></connection></connections><GUILayout><gpos v="5726.88184 -3837.11401 0"/></GUILayout><compOutputs><compOutput><uid v="1448089524"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="5"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448089545"/><connections><connection><identifier v="input1"/><connRef v="1484600706"/><connRefOutput v="1484600707"/></connection></connections><GUILayout><gpos v="5648.88184 -3858.44727 -1"/><docked v="1"/><dockDistance v="398.505615 224"/></GUILayout><compOutputs><compOutput><uid v="1448089546"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelouthigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.814655185 0.814655185 0.814655185 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448089623"/><connections><connection><identifier v="input1"/><connRef v="1448089502"/><connRefOutput v="1448089503"/></connection></connections><GUILayout><gpos v="6046.88184 -3837.11401 0"/></GUILayout><compOutputs><compOutput><uid v="1448089624"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="normal"/><parameters><parameter><name v="input2alpha"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="inversedy"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="10"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448089640"/><connections><connection><identifier v="input"/><connRef v="1448089623"/><connRefOutput v="1448089624"/></connection></connections><GUILayout><gpos v="6206.88184 -3837.11401 0"/></GUILayout><compOutputs><compOutput><uid v="1448089641"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///curvature_smooth?dependency=1449590280"/><parameters><parameter><name v="normal_format"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448089641"/><identifier v="height"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448089661"/><connections><connection><identifier v="Input_1"/><connRef v="1448089640"/><connRefOutput v="1448089641"/></connection></connections><GUILayout><gpos v="6366.88184 -3837.11401 0"/></GUILayout><compOutputs><compOutput><uid v="1448089662"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1449404462"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.429999977"/></paramValue></parameter><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.979999959"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448089662"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448089838"/><connections><connection><identifier v="destination"/><connRef v="1448061132"/><connRefOutput v="1448061133"/></connection><connection><identifier v="opacity"/><connRef v="1448055612"/><connRefOutput v="1448055562"/></connection><connection><identifier v="source"/><connRef v="1448089856"/><connRefOutput v="1448089857"/></connection></connections><GUILayout><gpos v="6768 -3056 0"/></GUILayout><compOutputs><compOutput><uid v="1448089839"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1484898120"/><paramNodes><paramNode><uid v="1484898120"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="opacitymult_7"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448089856"/><connections><connection><identifier v="input1"/><connRef v="1448089661"/><connRefOutput v="1448089662"/></connection></connections><GUILayout><gpos v="6690 -3077.33325 0"/><docked v="1"/><dockDistance v="96 544"/></GUILayout><compOutputs><compOutput><uid v="1448089857"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1448089938"/><connections><connection><identifier v="input"/><connRef v="1448089939"/><connRefOutput v="1448089624"/></connection></connections><GUILayout><gpos v="6206.88184 -4074.78955 0"/></GUILayout><compOutputs><compOutput><uid v="1448089641"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///curvature_smooth?dependency=1449590280"/><parameters><parameter><name v="normal_format"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448089641"/><identifier v="height"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448089939"/><connections><connection><identifier v="input1"/><connRef v="1447898295"/><connRefOutput v="1447898296"/></connection></connections><GUILayout><gpos v="6046.88184 -4074.78955 0"/></GUILayout><compOutputs><compOutput><uid v="1448089624"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="normal"/><parameters><parameter><name v="input2alpha"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="inversedy"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="10"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448089940"/><connections><connection><identifier v="Input_1"/><connRef v="1448089938"/><connRefOutput v="1448089641"/></connection></connections><GUILayout><gpos v="6366.88184 -4074.78955 0"/></GUILayout><compOutputs><compOutput><uid v="1448089662"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1449404462"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.459999979"/></paramValue></parameter><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.949999988"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448089662"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448089957"/><connections><connection><identifier v="Input_1"/><connRef v="1448089958"/><connRefOutput v="1448089641"/></connection></connections><GUILayout><gpos v="6366.88184 -4221.11377 0"/></GUILayout><compOutputs><compOutput><uid v="1448089662"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1449404462"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.359999985"/></paramValue></parameter><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.949999988"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448089662"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448089958"/><connections><connection><identifier v="input"/><connRef v="1448089959"/><connRefOutput v="1448089624"/></connection></connections><GUILayout><gpos v="6206.88184 -4221.11377 0"/></GUILayout><compOutputs><compOutput><uid v="1448089641"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///curvature_smooth?dependency=1449590280"/><parameters><parameter><name v="normal_format"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448089641"/><identifier v="height"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448089959"/><connections><connection><identifier v="input1"/><connRef v="1484833101"/><connRefOutput v="1484833102"/></connection></connections><GUILayout><gpos v="6046.88184 -4221.11377 0"/></GUILayout><compOutputs><compOutput><uid v="1448089624"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="normal"/><parameters><parameter><name v="input2alpha"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="14.04"/></paramValue></parameter><parameter><name v="inversedy"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448089976"/><connections><connection><identifier v="destination"/><connRef v="1448089838"/><connRefOutput v="1448089839"/></connection><connection><identifier v="source"/><connRef v="1448090032"/><connRefOutput v="1448090033"/></connection><connection><identifier v="opacity"/><connRef v="1448090188"/><connRefOutput v="1448090189"/></connection></connections><GUILayout><gpos v="6942.88184 -3069.11401 0"/></GUILayout><compOutputs><compOutput><uid v="1448089977"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1484898188"/><paramNodes><paramNode><uid v="1484898188"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="opacitymult_8"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448090032"/><connections><connection><identifier v="input1"/><connRef v="1448089940"/><connRefOutput v="1448089662"/></connection></connections><GUILayout><gpos v="6526.88184 -4074.78955 0"/></GUILayout><compOutputs><compOutput><uid v="1448090033"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1448090160"/><connections><connection><identifier v="destination"/><connRef v="1448089976"/><connRefOutput v="1448089977"/></connection><connection><identifier v="source"/><connRef v="1448090175"/><connRefOutput v="1448090176"/></connection><connection><identifier v="opacity"/><connRef v="1448090188"/><connRefOutput v="1448090189"/></connection></connections><GUILayout><gpos v="7102.88184 -3069.11401 0"/></GUILayout><compOutputs><compOutput><uid v="1448090161"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1484898242"/><paramNodes><paramNode><uid v="1484898242"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="opacitymult_9"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448090175"/><connections><connection><identifier v="input1"/><connRef v="1448089957"/><connRefOutput v="1448089662"/></connection></connections><GUILayout><gpos v="6526.88184 -4221.11377 0"/></GUILayout><compOutputs><compOutput><uid v="1448090176"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters><parameter><name v="colorswitch"/><paramValue><constantValueBool v="1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448090188"/><connections><connection><identifier v="Source"/><connRef v="1448055612"/><connRefOutput v="1448055562"/></connection></connections><GUILayout><gpos v="6032 -2849.60376 0"/></GUILayout><compOutputs><compOutput><uid v="1448090189"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///invert_grayscale?dependency=1368115694"/><parameters/><outputBridgings><outputBridging><uid v="1448090189"/><identifier v="Invert_Grayscale"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448090245"/><connections><connection><identifier v="Input_1"/><connRef v="1448089938"/><connRefOutput v="1448089641"/></connection></connections><GUILayout><gpos v="6366.88184 -4365.95947 0"/></GUILayout><compOutputs><compOutput><uid v="1448089662"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1449404462"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.519999981"/></paramValue></parameter><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.98999995"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448089662"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448090274"/><connections><connection><identifier v="opacity"/><connRef v="1448090188"/><connRefOutput v="1448090189"/></connection><connection><identifier v="destination"/><connRef v="1448090160"/><connRefOutput v="1448090161"/></connection><connection><identifier v="source"/><connRef v="1448090293"/><connRefOutput v="1448090294"/></connection></connections><GUILayout><gpos v="7266.47803 -3069.70166 0"/></GUILayout><compOutputs><compOutput><uid v="1448090275"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1484898269"/><paramNodes><paramNode><uid v="1484898269"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="opacitymult_10"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448090293"/><connections><connection><identifier v="input1"/><connRef v="1448090305"/><connRefOutput v="1448090306"/></connection></connections><GUILayout><gpos v="6686.88184 -4365.95947 0"/></GUILayout><compOutputs><compOutput><uid v="1448090294"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters><parameter><name v="colorswitch"/><paramValue><constantValueBool v="1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448090305"/><connections><connection><identifier v="Source"/><connRef v="1448090245"/><connRefOutput v="1448089662"/></connection></connections><GUILayout><gpos v="6526.88184 -4365.95947 0"/></GUILayout><compOutputs><compOutput><uid v="1448090306"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///invert_grayscale?dependency=1368115694"/><parameters/><outputBridgings><outputBridging><uid v="1448090306"/><identifier v="Invert_Grayscale"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448097240"/><connections><connection><identifier v="pattern_input"/><connRef v="1447889667"/><connRefOutput v="1447889637"/></connection><connection><identifier v="pattern_input_2"/><connRef v="1447889658"/><connRefOutput v="1447889637"/></connection><connection><identifier v="pattern_input_3"/><connRef v="1447889652"/><connRefOutput v="1447889637"/></connection><connection><identifier v="pattern_input_4"/><connRef v="1447889636"/><connRefOutput v="1447889637"/></connection></connections><GUILayout><gpos v="-1117.49329 -3152 0"/></GUILayout><compOutputs><compOutput><uid v="1447889713"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1449399972"/><parameters><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="pattern_input_number"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="397"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="397"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.26999998"/></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.74000001"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.819999993"/></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.5"/></paramValue></parameter><parameter><name v="color_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.409999996"/></paramValue></parameter><parameter><name v="color_parametrization_mode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="color_parametrization_multiplier"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.629999995"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1447889713"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448097365"/><connections><connection><identifier v="destination"/><connRef v="1447957503"/><connRefOutput v="1447957504"/></connection><connection><identifier v="opacity"/><connRef v="1448100955"/><connRefOutput v="1448100956"/></connection><connection><identifier v="source"/><connRef v="1448110525"/><connRefOutput v="1448110526"/></connection></connections><GUILayout><gpos v="-752 -2672 0"/></GUILayout><compOutputs><compOutput><uid v="1448097366"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.00999999978"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448100955"/><connections><connection><identifier v="Source"/><connRef v="1484833101"/><connRefOutput v="1484833102"/></connection></connections><GUILayout><gpos v="-1008 -2448 0"/></GUILayout><compOutputs><compOutput><uid v="1448100956"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///invert_grayscale?dependency=1368115694"/><parameters/><outputBridgings><outputBridging><uid v="1448100956"/><identifier v="Invert_Grayscale"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448108174"/><connections><connection><identifier v="source"/><connRef v="1448108191"/><connRefOutput v="1448108192"/></connection><connection><identifier v="opacity"/><connRef v="1448110509"/><connRefOutput v="1448110510"/></connection><connection><identifier v="destination"/><connRef v="1448176577"/><connRefOutput v="1448176578"/></connection></connections><GUILayout><gpos v="7824 -3056.86035 0"/></GUILayout><compOutputs><compOutput><uid v="1448108175"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.219999999"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448108191"/><connections><connection><identifier v="input1"/><connRef v="1448110525"/><connRefOutput v="1448110526"/></connection></connections><GUILayout><gpos v="7600 -3297.81104 0"/></GUILayout><compOutputs><compOutput><uid v="1448108192"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1448110509"/><connections><connection><identifier v="destination"/><connRef v="1448090188"/><connRefOutput v="1448090189"/></connection><connection><identifier v="source"/><connRef v="1448100955"/><connRefOutput v="1448100956"/></connection></connections><GUILayout><gpos v="7581.11572 -2849.81104 0"/></GUILayout><compOutputs><compOutput><uid v="1448110510"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448110525"/><connections><connection><identifier v="Input"/><connRef v="1448097240"/><connRefOutput v="1447889713"/></connection></connections><GUILayout><gpos v="-957.493286 -3152 0"/></GUILayout><compOutputs><compOutput><uid v="1448110526"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1449400845"/><parameters/><outputBridgings><outputBridging><uid v="1448110526"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448114368"/><connections><connection><identifier v="mask"/><connRef v="1448055529"/><connRefOutput v="1448055530"/></connection></connections><GUILayout><gpos v="8592 -4065.81104 0"/></GUILayout><compOutputs><compOutput><uid v="1448114369"/><comptype v="1"/></compOutput><compOutput><uid v="1448114370"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill?dependency=1323881949"/><parameters/><outputBridgings><outputBridging><uid v="1448114369"/><identifier v="output"/></outputBridging><outputBridging><uid v="1448114370"/><identifier v="referenceMap"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448114534"/><connections><connection><identifier v="input"/><connRef v="1448114368"/><connRefOutput v="1448114369"/></connection></connections><GUILayout><gpos v="8752 -4065.81104 0"/></GUILayout><compOutputs><compOutput><uid v="1448114535"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_random_grayscale?dependency=1323881949"/><parameters><parameter><name v="randomseed"/><relativeTo v="2"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448114535"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448114564"/><connections><connection><identifier v="destination"/><connRef v="1448055833"/><connRefOutput v="1448055834"/></connection><connection><identifier v="opacity"/><connRef v="1448055529"/><connRefOutput v="1448055530"/></connection><connection><identifier v="source"/><connRef v="1448114582"/><connRefOutput v="1448114583"/></connection></connections><GUILayout><gpos v="9168 -3073.81104 0"/></GUILayout><compOutputs><compOutput><uid v="1448114565"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1484898506"/><paramNodes><paramNode><uid v="1484898506"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="opacitymult_12"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448114582"/><connections><connection><identifier v="input1"/><connRef v="1448114534"/><connRefOutput v="1448114535"/></connection></connections><GUILayout><gpos v="8912 -4065.81104 0"/></GUILayout><compOutputs><compOutput><uid v="1448114583"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1448114649"/><connections><connection><identifier v="destination"/><connRef v="1448114564"/><connRefOutput v="1448114565"/></connection><connection><identifier v="opacity"/><connRef v="1448055529"/><connRefOutput v="1448055530"/></connection><connection><identifier v="source"/><connRef v="1448114670"/><connRefOutput v="1448114671"/></connection></connections><GUILayout><gpos v="9345.11816 -3060.69702 0"/></GUILayout><compOutputs><compOutput><uid v="1448114650"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1484898459"/><paramNodes><paramNode><uid v="1484898459"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="opacitymult_11"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448114670"/><connections><connection><identifier v="input1"/><connRef v="1448050788"/><connRefOutput v="1448050789"/></connection></connections><GUILayout><gpos v="9267.11816 -3082.03027 -1"/><docked v="1"/><dockDistance v="2688 -554.666504"/></GUILayout><compOutputs><compOutput><uid v="1448114671"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters><parameter><name v="colorswitch"/><paramValue><constantValueBool v="1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448141685"/><connections><connection><identifier v="Source"/><connRef v="1447963210"/><connRefOutput v="1447963211"/></connection></connections><GUILayout><gpos v="3184 -400 0"/></GUILayout><compOutputs><compOutput><uid v="1447981831"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///non_uniform_blur_grayscale?dependency=1290776959"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.66999996"/></paramValue></parameter><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="16"/></paramValue></parameter><parameter><name v="Blades"/><relativeTo v="0"/><paramValue><constantValueInt32 v="9"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1447981831"/><identifier v="Non_Uniform_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448141686"/><connections><connection><identifier v="destination"/><connRef v="1448141685"/><connRefOutput v="1447981831"/></connection></connections><GUILayout><gpos v="3374.7019 -398.5 0"/></GUILayout><compOutputs><compOutput><uid v="1448050848"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448169555"/><GUILayout><gpos v="7677.93164 -3486.94458 0"/></GUILayout><compOutputs><compOutput><uid v="1448169556"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1449404462"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.98999995"/></paramValue></parameter><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448169556"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448169615"/><connections><connection><identifier v="destination"/><connRef v="1448108174"/><connRefOutput v="1448108175"/></connection><connection><identifier v="opacity"/><connRef v="1448169555"/><connRefOutput v="1448169556"/></connection></connections><GUILayout><gpos v="8080 -3041.81104 0"/></GUILayout><compOutputs><compOutput><uid v="1448169616"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1448169775"/><connections><connection><identifier v="mask"/><connRef v="1448169555"/><connRefOutput v="1448169556"/></connection></connections><GUILayout><gpos v="7902.28271 -3504 0"/></GUILayout><compOutputs><compOutput><uid v="1448114369"/><comptype v="1"/></compOutput><compOutput><uid v="1448114370"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill?dependency=1323881949"/><parameters/><outputBridgings><outputBridging><uid v="1448114369"/><identifier v="output"/></outputBridging><outputBridging><uid v="1448114370"/><identifier v="referenceMap"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448169776"/><connections><connection><identifier v="input"/><connRef v="1448169775"/><connRefOutput v="1448114369"/></connection></connections><GUILayout><gpos v="8044.12988 -3504 0"/></GUILayout><compOutputs><compOutput><uid v="1448114535"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_random_grayscale?dependency=1323881949"/><parameters><parameter><name v="randomseed"/><relativeTo v="2"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448114535"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448169777"/><connections><connection><identifier v="input1"/><connRef v="1448169776"/><connRefOutput v="1448114535"/></connection></connections><GUILayout><gpos v="8217.55469 -3504 0"/></GUILayout><compOutputs><compOutput><uid v="1448114583"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1448169787"/><connections><connection><identifier v="destination"/><connRef v="1448169615"/><connRefOutput v="1448169616"/></connection><connection><identifier v="opacity"/><connRef v="1448169555"/><connRefOutput v="1448169556"/></connection><connection><identifier v="source"/><connRef v="1448169777"/><connRefOutput v="1448114583"/></connection></connections><GUILayout><gpos v="8272 -3041.81104 0"/></GUILayout><compOutputs><compOutput><uid v="1448169788"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.109999999"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448176127"/><connections><connection><identifier v="RGBA"/><connRef v="1448176364"/><connRefOutput v="1448089624"/></connection></connections><GUILayout><gpos v="6224 -4517.3335 0"/></GUILayout><compOutputs><compOutput><uid v="1448176128"/><comptype v="2"/></compOutput><compOutput><uid v="1448176129"/><comptype v="2"/></compOutput><compOutput><uid v="1448176130"/><comptype v="2"/></compOutput><compOutput><uid v="1448176131"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///rgba_split?dependency=1147283954"/><parameters/><outputBridgings><outputBridging><uid v="1448176128"/><identifier v="R"/></outputBridging><outputBridging><uid v="1448176129"/><identifier v="G"/></outputBridging><outputBridging><uid v="1448176130"/><identifier v="B"/></outputBridging><outputBridging><uid v="1448176131"/><identifier v="A"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448176364"/><connections><connection><identifier v="input1"/><connRef v="1448089523"/><connRefOutput v="1448089524"/></connection></connections><GUILayout><gpos v="5872 -4336 0"/></GUILayout><compOutputs><compOutput><uid v="1448089624"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="normal"/><parameters><parameter><name v="input2alpha"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="inversedy"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="10"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448176378"/><connections><connection><identifier v="Input_1"/><connRef v="1448176127"/><connRefOutput v="1448176129"/></connection></connections><GUILayout><gpos v="6401.72998 -4528 0"/></GUILayout><compOutputs><compOutput><uid v="1448176379"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1449404462"/><parameters><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.669999957"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448176379"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448176549"/><connections><connection><identifier v="Source"/><connRef v="1448176378"/><connRefOutput v="1448176379"/></connection></connections><GUILayout><gpos v="6561.72998 -4528 0"/></GUILayout><compOutputs><compOutput><uid v="1448176550"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///invert_grayscale?dependency=1368115694"/><parameters/><outputBridgings><outputBridging><uid v="1448176550"/><identifier v="Invert_Grayscale"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448176577"/><connections><connection><identifier v="destination"/><connRef v="1448090274"/><connRefOutput v="1448090275"/></connection><connection><identifier v="source"/><connRef v="1448176602"/><connRefOutput v="1448055732"/></connection><connection><identifier v="opacity"/><connRef v="1448176630"/><connRefOutput v="1448176631"/></connection></connections><GUILayout><gpos v="7440 -3073.12793 0"/></GUILayout><compOutputs><compOutput><uid v="1448176578"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.209999993"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448176602"/><connections><connection><identifier v="input1"/><connRef v="1448189796"/><connRefOutput v="1448189797"/></connection></connections><GUILayout><gpos v="7069.35449 -4528 0"/></GUILayout><compOutputs><compOutput><uid v="1448055732"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1448055737"/><paramsArrayCells><paramsArrayCell><uid v="1448176621"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.617391288 0.56426692 0.452275008 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1448176630"/><connections><connection><identifier v="destination"/><connRef v="1448055612"/><connRefOutput v="1448055562"/></connection><connection><identifier v="source"/><connRef v="1448189796"/><connRefOutput v="1448189797"/></connection></connections><GUILayout><gpos v="7280 -2864 0"/></GUILayout><compOutputs><compOutput><uid v="1448176631"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1448188979"/><GUILayout><gpos v="6512 -4720 0"/></GUILayout><compOutputs><compOutput><uid v="1448188980"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///fractal_sum_base_2?dependency=1449402686"/><parameters><parameter><name v="Roughness"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.48999998"/></paramValue></parameter><parameter><name v="MinLevel"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448188980"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448189616"/><connections><connection><identifier v="Source"/><connRef v="1448176549"/><connRefOutput v="1448176550"/></connection><connection><identifier v="Effect"/><connRef v="1448188979"/><connRefOutput v="1448188980"/></connection></connections><GUILayout><gpos v="6704 -4528 0"/></GUILayout><compOutputs><compOutput><uid v="1448189617"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///slope_blur_grayscale_2?dependency=1449402040"/><parameters><parameter><name v="mode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="5"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.91999996"/></paramValue></parameter><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="32"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448189617"/><identifier v="Slope_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1448189796"/><connections><connection><identifier v="Input_1"/><connRef v="1448189616"/><connRefOutput v="1448189617"/></connection></connections><GUILayout><gpos v="6864 -4528 0"/></GUILayout><compOutputs><compOutput><uid v="1448189797"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1449404462"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.569999993"/></paramValue></parameter><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1448189797"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1484572740"/><connections><connection><identifier v="destination"/><connRef v="1447889712"/><connRefOutput v="1447889713"/></connection></connections><GUILayout><gpos v="-6160 560 0"/></GUILayout><compOutputs><compOutput><uid v="1484572741"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1484586796"/><connections><connection><identifier v="destination"/><connRef v="1447890628"/><connRefOutput v="1447889713"/></connection></connections><GUILayout><gpos v="-4865.00293 112 0"/></GUILayout><compOutputs><compOutput><uid v="1484586797"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1484675325"/><paramNodes><paramNode><uid v="1484675325"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="opacitymult_1"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1484600706"/><connections><connection><identifier v="destination"/><connRef v="1447893450"/><connRefOutput v="1447889713"/></connection></connections><GUILayout><gpos v="-3728 -417.210541 0"/></GUILayout><compOutputs><compOutput><uid v="1484600707"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1484712568"/><paramNodes><paramNode><uid v="1484712568"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="opacitymult_2"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1484812091"/><connections><connection><identifier v="destination"/><connRef v="1448050302"/><connRefOutput v="1448050303"/></connection></connections><GUILayout><gpos v="3232 -2117.3335 0"/></GUILayout><compOutputs><compOutput><uid v="1484812092"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1484816013"/><paramNodes><paramNode><uid v="1484816013"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="opacitymult_4"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1484833101"/><connections><connection><identifier v="destination"/><connRef v="1448016461"/><connRefOutput v="1447954719"/></connection></connections><GUILayout><gpos v="-1360 -2576 0"/></GUILayout><compOutputs><compOutput><uid v="1484833102"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.189999998"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1484897896"/><GUILayout><gpos v="6032 -3216 0"/></GUILayout><compOutputs><compOutput><uid v="1484897897"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="uniform"/><parameters><parameter><name v="outputsize"/><relativeTo v="0"/><paramValue><constantValueInt2 v="4 4"/></paramValue></parameter><parameter><name v="colorswitch"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="outputcolor"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.758064508 0.604821444 0.326197475 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1484897917"/><GUILayout><gpos v="6032 -3056 0"/></GUILayout><compOutputs><compOutput><uid v="1484897897"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="uniform"/><parameters><parameter><name v="outputsize"/><relativeTo v="0"/><paramValue><constantValueInt2 v="4 4"/></paramValue></parameter><parameter><name v="outputcolor"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.715053737 0.575147688 0.268686861 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1484897926"/><connections><connection><identifier v="source"/><connRef v="1484897896"/><connRefOutput v="1484897897"/></connection><connection><identifier v="destination"/><connRef v="1484897917"/><connRefOutput v="1484897897"/></connection><connection><identifier v="opacity"/><connRef v="1448090188"/><connRefOutput v="1448090189"/></connection></connections><GUILayout><gpos v="6288 -3056 0"/></GUILayout><compOutputs><compOutput><uid v="1484897927"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="outputsize"/><relativeTo v="1"/><paramValue><constantValueInt2 v="0 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1484898384"/><GUILayout><gpos v="8816 -2975.67334 0"/></GUILayout><compOutputs><compOutput><uid v="1484897897"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="uniform"/><parameters><parameter><name v="outputsize"/><relativeTo v="0"/><paramValue><constantValueInt2 v="4 4"/></paramValue></parameter><parameter><name v="outputcolor"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.304754138 0.408602148 0.0866731703 1"/></paramValue></parameter><parameter><name v="colorswitch"/><paramValue><constantValueBool v="1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode></compNodes><baseParameters><parameter><name v="outputsize"/><relativeTo v="0"/><paramValue><constantValueInt2 v="11 11"/></paramValue></parameter></baseParameters><options><option><name v="export/fromGraph/autoExport"/><value v="true"/></option><option><name v="export/fromGraph/destination"/><value v="D:/work/Annery/stylized material/stylized material/images/sand with grass"/></option><option><name v="export/fromGraph/extension"/><value v="png"/></option><option><name v="export/fromGraph/outputs/AO"/><value v="true"/></option><option><name v="export/fromGraph/outputs/basecolor"/><value v="true"/></option><option><name v="export/fromGraph/outputs/height"/><value v="true"/></option><option><name v="export/fromGraph/outputs/metallic"/><value v="true"/></option><option><name v="export/fromGraph/outputs/normal"/><value v="true"/></option><option><name v="export/fromGraph/outputs/roughness"/><value v="true"/></option><option><name v="export/fromGraph/outputsColorspace/AO"/><value v="Raw"/></option><option><name v="export/fromGraph/outputsColorspace/basecolor"/><value v="sRGB"/></option><option><name v="export/fromGraph/outputsColorspace/height"/><value v="Raw"/></option><option><name v="export/fromGraph/outputsColorspace/metallic"/><value v="Raw"/></option><option><name v="export/fromGraph/outputsColorspace/normal"/><value v="Raw"/></option><option><name v="export/fromGraph/outputsColorspace/roughness"/><value v="Raw"/></option><option><name v="export/fromGraph/pattern"/><value v="$(graph)_$(identifier)"/></option></options><root><rootOutputs><rootOutput><output v="1447885835"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1447885837"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1447885839"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1447885841"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1447885843"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1447885845"/><format v="0"/><usertag v=""/></rootOutput></rootOutputs></root></graph></content></package>
