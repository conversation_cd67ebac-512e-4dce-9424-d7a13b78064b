<?xml version="1.0" encoding="UTF-8"?><package><identifier v="Unsaved Package"/><formatVersion v="1.1.0.202201"/><updaterVersion v="1.1.0.202201"/><fileUID v="{2e6772ce-3a4c-4791-97f5-1d63b50eb72d}"/><versionUID v="0"/><dependencies><dependency><filename v="sbs://pattern_tile_sampler.sbs"/><uid v="1486875900"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://pbr_base_material.sbs"/><uid v="1482592834"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_gaussian_noise.sbs"/><uid v="1486875707"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://trapezoid_transform.sbs"/><uid v="1486830014"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://histogram_scan.sbs"/><uid v="1149349207"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://gradient_linear_1.sbs"/><uid v="1335474237"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://pattern_shape.sbs"/><uid v="1311189026"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_clouds_3.sbs"/><uid v="1487173201"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://hbao_2.sbs"/><uid v="1297162667"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency></dependencies><content><graph><identifier v="Worn_Grass"/><uid v="1487083873"/><graphtype v="material"/><graphOutputs><graphoutput><identifier v="basecolor"/><uid v="1487083941"/><attributes><label v="Base Color"/></attributes><usages><usage><components v="RGBA"/><name v="baseColor"/></usage></usages><group v="Material"/></graphoutput><graphoutput><identifier v="normal"/><uid v="1487083943"/><attributes><label v="Normal"/></attributes><usages><usage><components v="RGBA"/><name v="normal"/></usage></usages><group v="Material"/></graphoutput><graphoutput><identifier v="height"/><uid v="1487083949"/><attributes><label v="Height"/></attributes><usages><usage><components v="RGBA"/><name v="height"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput><graphoutput><identifier v="ambientOcclusion"/><uid v="1487083951"/><attributes><label v="Ambient Occlusion"/></attributes><usages><usage><components v="RGBA"/><name v="ambientOcclusion"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput><graphoutput><identifier v="Rough_1"/><uid v="1487176129"/><attributes><label v="Rough"/></attributes><usages><usage><components v="RGBA"/><name v="roughness"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput></graphOutputs><compNodes><compNode><uid v="1487083919"/><GUILayout><gpos v="144 240 0"/></GUILayout><compOutputs><compOutput><uid v="1486906083"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487231342"/><connRefOutput v="1487231343"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.427835047 0.427835047 0.427835047 0.5"/></paramValue></parameter><parameter><name v="levelinlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.345663279 0.345663279 0.345663279 0"/></paramValue></parameter><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.725765288 0.725765288 0.725765288 1"/></paramValue></parameter><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.12372449 0.12372449 0.12372449 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487083920"/><GUILayout><gpos v="287.751617 455.757568 0"/></GUILayout><compOutputs><compOutput><uid v="1482708448"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487083932"/><connRefOutput v="1482628326"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.440051019 0.440051019 0.440051019 0.5"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487083921"/><GUILayout><gpos v="-350.488098 101.333336 0"/></GUILayout><compOutputs><compOutput><uid v="1482609709"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="pattern_input"/><connRef v="1487083922"/><connRefOutput v="1482608790"/></connection><connection><identifier v="rotation_map_input"/><connRef v="1487083924"/><connRefOutput v="1482609943"/></connection><connection><identifier v="pattern_input_2"/><connRef v="1487086292"/><connRefOutput v="1482608790"/></connection></connections><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1486875900"/><parameters><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="36"/></paramValue></parameter><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="size"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="2.07999992 2.89999986"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="4.08999968"/></paramValue></parameter><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="38"/></paramValue></parameter><parameter><name v="rotation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.920000017"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.359999985"/></paramValue></parameter><parameter><name v="rotation_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0199999996"/></paramValue></parameter><parameter><name v="pattern_input_number"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter><parameter><name v="scale_map_multiplier"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.229999989"/></paramValue></parameter><parameter><name v="rotation_map_multiplier"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="rotation_vector_map_multiplier"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.859999955"/></paramValue></parameter><parameter><name v="color_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="color_parametrization_multiplier"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.919999957"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1482609709"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487083922"/><GUILayout><gpos v="-528 -176 0"/></GUILayout><compOutputs><compOutput><uid v="1482608790"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487083936"/></connection></connections><compImplementation><compFilter><filter v="blur"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.91999996"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487083923"/><GUILayout><gpos v="478.784637 226.385223 0"/></GUILayout><compOutputs><compOutput><uid v="1482593503"/><comptype v="1"/></compOutput><compOutput><uid v="1482593504"/><comptype v="1"/></compOutput><compOutput><uid v="1482593505"/><comptype v="1"/></compOutput><compOutput><uid v="1482593506"/><comptype v="1"/></compOutput><compOutput><uid v="1482593507"/><comptype v="2"/></compOutput><compOutput><uid v="1482593508"/><comptype v="2"/></compOutput><compOutput><uid v="1482593509"/><comptype v="2"/></compOutput><compOutput><uid v="1482593510"/><comptype v="2"/></compOutput><compOutput><uid v="1482593511"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="normal_input"/><connRef v="1487083925"/><connRefOutput v="1359211384"/></connection><connection><identifier v="ambientOcclusion_input"/><connRef v="1487083920"/><connRefOutput v="1482708448"/></connection><connection><identifier v="height_input"/><connRef v="1487083919"/><connRefOutput v="1486906083"/></connection><connection><identifier v="basecolor_input"/><connRef v="1487142751"/><connRefOutput v="1487142752"/></connection><connection><identifier v="roughness_input"/><connRef v="1487231342"/><connRefOutput v="1487231343"/></connection></connections><compImplementation><compInstance><path v="pkg:///pbr_base_material?dependency=1482592834"/><parameters><parameter><name v="user_basecolor"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="user_normal"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="user_roughness"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="user_metallic"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="user_ao"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="user_height"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="height_position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.519999981"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1482593503"/><identifier v="diffuse"/></outputBridging><outputBridging><uid v="1482593504"/><identifier v="basecolor"/></outputBridging><outputBridging><uid v="1482593505"/><identifier v="normal"/></outputBridging><outputBridging><uid v="1482593506"/><identifier v="specular"/></outputBridging><outputBridging><uid v="1482593507"/><identifier v="glossiness"/></outputBridging><outputBridging><uid v="1482593508"/><identifier v="roughness"/></outputBridging><outputBridging><uid v="1482593509"/><identifier v="metallic"/></outputBridging><outputBridging><uid v="1482593510"/><identifier v="height"/></outputBridging><outputBridging><uid v="1482593511"/><identifier v="ambientOcclusion"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487083924"/><GUILayout><gpos v="-512.65155 130.389099 0"/></GUILayout><compOutputs><compOutput><uid v="1482609943"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///gaussian_noise?dependency=1486875707"/><parameters><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueInt32 v="9"/></paramValue></parameter><parameter><name v="disorder"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.099999994"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1482609943"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487083925"/><GUILayout><gpos v="286.784637 112 0"/></GUILayout><compOutputs><compOutput><uid v="1359211384"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487231342"/><connRefOutput v="1487231343"/></connection></connections><compImplementation><compFilter><filter v="normal"/><parameters><parameter><name v="input2alpha"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="3"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487083926"/><GUILayout><gpos v="286.784637 343.229645 0"/></GUILayout><compOutputs><compOutput><uid v="1482708448"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487083931"/><connRefOutput v="1482628326"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.460459173 0.460459173 0.460459173 0.5"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487083927"/><GUILayout><gpos v="-1074.16345 -176 0"/></GUILayout><compOutputs><compOutput><uid v="1482591428"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input"/><connRef v="1487083939"/><connRefOutput v="1482576333"/></connection></connections><compImplementation><compInstance><path v="pkg:///trapezoid_transform_grayscale?dependency=1486830014"/><parameters><parameter><name v="top_stretch"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.620000005"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1482591428"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487083928"/><GUILayout><gpos v="-222.018982 144 0"/></GUILayout><compOutputs><compOutput><uid v="1482708240"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487083921"/><connRefOutput v="1482609709"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.53188777 0.53188777 0.53188777 0.5"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487083929"/><GUILayout><gpos v="-816 -48 0"/></GUILayout><compOutputs><compOutput><uid v="1482590975"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="Input_1"/><connRef v="1487083937"/><connRefOutput v="1482591041"/></connection></connections><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1149349207"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.610000014"/></paramValue></parameter><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0700000003"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1482590975"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487083930"/><GUILayout><gpos v="-109.499947 144 0"/></GUILayout><compOutputs><compOutput><uid v="1482709066"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487083928"/><connRefOutput v="1482708240"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.151785716 0.151785716 0.151785716 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487083931"/><GUILayout><gpos v="139.327011 342.230652 0"/></GUILayout><compOutputs><compOutput><uid v="1482628326"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487083930"/><connRefOutput v="1482709066"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.478316337 0.478316337 0.478316337 0.5"/></paramValue></parameter><parameter><name v="levelinlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.12627551 0.12627551 0.12627551 0"/></paramValue></parameter><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.235969394 0.235969394 0.235969394 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487083932"/><GUILayout><gpos v="139.327011 454.654022 0"/></GUILayout><compOutputs><compOutput><uid v="1482628326"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487231342"/><connRefOutput v="1487231343"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.478316337 0.478316337 0.478316337 0.5"/></paramValue></parameter><parameter><name v="levelinlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.12627551 0.12627551 0.12627551 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487083933"/><GUILayout><gpos v="-945.365845 -176 0"/></GUILayout><compOutputs><compOutput><uid v="1482590232"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487083927"/><connRefOutput v="1482591428"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.149234697 0.149234697 0.149234697 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487083934"/><GUILayout><gpos v="125.308029 1.80444336 0"/></GUILayout><compOutputs><compOutput><uid v="1486907756"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487083938"/><connRefOutput v="1482625016"/></connection></connections><compImplementation><compFilter><filter v="hsl"/><parameters><parameter><name v="hue"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.50999999"/></paramValue></parameter><parameter><name v="saturation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.469999999"/></paramValue></parameter><parameter><name v="luminosity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.48999998"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487083935"/><GUILayout><gpos v="-815.78894 -176 0"/></GUILayout><compOutputs><compOutput><uid v="1482590705"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="source"/><connRef v="1487083933"/><connRefOutput v="1482590232"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="maskrectangle"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.199999988 0.779999971 0 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487083936"/><GUILayout><gpos v="-656.000122 -176 0"/></GUILayout><compOutputs><compOutput><uid v="1482591088"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="destination"/><connRef v="1487083935"/><connRefOutput v="1482590705"/></connection><connection><identifier v="source"/><connRef v="1487083929"/><connRefOutput v="1482590975"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="maskrectangle"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0 1 0 0"/></paramValue></parameter><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487083937"/><GUILayout><gpos v="-944 -48 0"/></GUILayout><compOutputs><compOutput><uid v="1482591041"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///gradient_linear_1?dependency=1335474237"/><parameters><parameter><name v="rotation"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1482591041"/><identifier v="Simple_Gradient"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487083938"/><GUILayout><gpos v="-48 -80 0"/></GUILayout><compOutputs><compOutput><uid v="1482625016"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487083930"/><connRefOutput v="1482709066"/></connection></connections><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1482625039"/><paramsArrayCells><paramsArrayCell><uid v="1487090400"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.111691743"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.347826093 0.335762978 0.149646118 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487090401"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.817391276 0.774579465 0.114054605 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487090402"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.274430156"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.463709503 0.521739125 0.188068748 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487090403"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.385938078"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.6652174 0.605489135 0.235920116 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1487083939"/><GUILayout><gpos v="-1199.64929 -175.562973 0"/></GUILayout><compOutputs><compOutput><uid v="1482576333"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///shape?dependency=1311189026"/><parameters><parameter><name v="Pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="16"/></paramValue></parameter><parameter><name v="Size"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.649999976"/></paramValue></parameter><parameter><name v="Pattern_Specific"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.829999983"/></paramValue></parameter><parameter><name v="Size_xy"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.25999999 1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1482576333"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487083940"/><GUILayout><gpos v="688 80 0"/></GUILayout><connections><connection><identifier v="inputNodeOutput"/><connRef v="1487083923"/><connRefOutput v="1482593504"/></connection></connections><compImplementation><compOutputBridge><output v="1487083941"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1487083942"/><GUILayout><gpos v="688 208 0"/></GUILayout><connections><connection><identifier v="inputNodeOutput"/><connRef v="1487083923"/><connRefOutput v="1482593505"/></connection></connections><compImplementation><compOutputBridge><output v="1487083943"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1487083948"/><GUILayout><gpos v="688 464 0"/></GUILayout><connections><connection><identifier v="inputNodeOutput"/><connRef v="1487083923"/><connRefOutput v="1482593510"/></connection></connections><compImplementation><compOutputBridge><output v="1487083949"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1487083950"/><GUILayout><gpos v="688.917053 592 0"/></GUILayout><connections><connection><identifier v="inputNodeOutput"/><connRef v="1488323253"/><connRefOutput v="1488323254"/></connection></connections><compImplementation><compOutputBridge><output v="1487083951"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1487086292"/><GUILayout><gpos v="-528 -16 0"/></GUILayout><compOutputs><compOutput><uid v="1482608790"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487083936"/></connection></connections><compImplementation><compFilter><filter v="blur"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.91999996"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487089304"/><GUILayout><gpos v="128.478867 -432 0"/></GUILayout><compOutputs><compOutput><uid v="1487089305"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="destination"/><connRef v="1487083934"/><connRefOutput v="1486907756"/></connection><connection><identifier v="source"/><connRef v="1487089331"/><connRefOutput v="1486907756"/></connection><connection><identifier v="opacity"/><connRef v="1487090241"/><connRefOutput v="1482708240"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.879999995"/></paramValue></parameter><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487089331"/><GUILayout><gpos v="125.308029 -112 0"/></GUILayout><compOutputs><compOutput><uid v="1486907756"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487083938"/><connRefOutput v="1482625016"/></connection></connections><compImplementation><compFilter><filter v="hsl"/><parameters><parameter><name v="luminosity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.550000012"/></paramValue></parameter><parameter><name v="saturation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.329999983"/></paramValue></parameter><parameter><name v="hue"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.429999977"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487090241"/><GUILayout><gpos v="-48 -190.05571 0"/></GUILayout><compOutputs><compOutput><uid v="1482708240"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487083921"/><connRefOutput v="1482609709"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.478190631 0.478190631 0.478190631 0.5"/></paramValue></parameter><parameter><name v="levelinlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.151785716 0.151785716 0.151785716 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487142751"/><GUILayout><gpos v="641.181641 -432 0"/></GUILayout><compOutputs><compOutput><uid v="1487142752"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="destination"/><connRef v="1487089304"/><connRefOutput v="1487089305"/></connection><connection><identifier v="source"/><connRef v="1487142813"/><connRefOutput v="1487142814"/></connection><connection><identifier v="opacity"/><connRef v="1487143114"/><connRefOutput v="1487143115"/></connection></connections><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.389999986"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487142813"/><GUILayout><gpos v="368 -560 0"/></GUILayout><compOutputs><compOutput><uid v="1487142814"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487174859"/><connRefOutput v="1487174860"/></connection></connections><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1487143233"/><paramsArrayCells><paramsArrayCell><uid v="1487174903"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.513317168"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.386956513 0.285228819 0.0944893807 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487174904"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.055690065"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.734782636 0.547892272 0.234959543 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487174975"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.23146987"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.525141776 0.560869575 0.267391294 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1487142963"/><GUILayout><gpos v="240 -304 501"/></GUILayout><compOutputs><compOutput><uid v="1482708240"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487083921"/><connRefOutput v="1482609709"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.53188777 0.53188777 0.53188777 0.5"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487142980"/><GUILayout><gpos v="352.660278 -304 0"/></GUILayout><compOutputs><compOutput><uid v="1487142981"/><comptype v="1"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487142963"/></connection></connections><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1487143054"/><paramsArrayCells><paramsArrayCell><uid v="1487143055"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.266343832"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0 0 0 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487143056"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="1 1 1 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1487143114"/><GUILayout><gpos v="464 -304 0"/></GUILayout><compOutputs><compOutput><uid v="1487143115"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487142980"/></connection></connections><compImplementation><compFilter><filter v="grayscaleconversion"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1487174859"/><GUILayout><gpos v="208 -560 0"/></GUILayout><compOutputs><compOutput><uid v="1487174860"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///clouds_3?dependency=1487173201"/><parameters><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487174860"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487176128"/><GUILayout><gpos v="688 336 0"/></GUILayout><connections><connection><identifier v="inputNodeOutput"/><connRef v="1487176232"/><connRefOutput v="1487176233"/></connection></connections><compImplementation><compOutputBridge><output v="1487176129"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1487176232"/><GUILayout><gpos v="528 418.133789 0"/></GUILayout><compOutputs><compOutput><uid v="1487176233"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487083923"/><connRefOutput v="1482593508"/></connection></connections><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.363520414 0.363520414 0.363520414 0.5"/></paramValue></parameter><parameter><name v="levelouthigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.71811223 0.71811223 0.71811223 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487231342"/><GUILayout><gpos v="-80 464 0"/></GUILayout><compOutputs><compOutput><uid v="1487231343"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1487142751"/></connection></connections><compImplementation><compFilter><filter v="grayscaleconversion"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1488297922"/><GUILayout><gpos v="466.750702 592 0"/></GUILayout><compOutputs><compOutput><uid v="1488297923"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input"/><connRef v="1487083923"/><connRefOutput v="1482593511"/></connection></connections><compImplementation><compInstance><path v="pkg:///hbao?dependency=1297162667"/><parameters><parameter><name v="height_depth"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0599999987"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1488297923"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1488323253"/><GUILayout><gpos v="577.833862 592 0"/></GUILayout><compOutputs><compOutput><uid v="1488323254"/><comptype v="2"/></compOutput></compOutputs><connections><connection><identifier v="input1"/><connRef v="1488297922"/><connRefOutput v="1488297923"/></connection></connections><compImplementation><compFilter><filter v="blur"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.699999988"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode></compNodes><baseParameters/><options><option><name v="defaultParentSize"/><value v="11x11"/></option><option><name v="export/fromGraph/autoExport"/><value v="false"/></option><option><name v="export/fromGraph/destination"/><value v="C:/Users/<USER>/Desktop"/></option><option><name v="export/fromGraph/extension"/><value v="png"/></option><option><name v="export/fromGraph/outputs/Rough_1"/><value v="true"/></option><option><name v="export/fromGraph/outputs/ambientOcclusion"/><value v="true"/></option><option><name v="export/fromGraph/outputs/basecolor"/><value v="true"/></option><option><name v="export/fromGraph/outputs/height"/><value v="true"/></option><option><name v="export/fromGraph/outputs/normal"/><value v="true"/></option><option><name v="export/fromGraph/outputsColorspace/Rough_1"/><value v="Raw"/></option><option><name v="export/fromGraph/outputsColorspace/ambientOcclusion"/><value v="Raw"/></option><option><name v="export/fromGraph/outputsColorspace/basecolor"/><value v="sRGB"/></option><option><name v="export/fromGraph/outputsColorspace/height"/><value v="Raw"/></option><option><name v="export/fromGraph/outputsColorspace/normal"/><value v="Raw"/></option><option><name v="export/fromGraph/pattern"/><value v="$(graph)_$(identifier)"/></option></options><root><rootOutputs><rootOutput><output v="1487083941"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1487083943"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1487083949"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1487083951"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1487176129"/><format v="0"/><usertag v=""/></rootOutput></rootOutputs></root></graph></content></package>
