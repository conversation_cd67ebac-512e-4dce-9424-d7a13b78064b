// Advanced Dissolve <https://u3d.as/16cX>
// Copyright (c) Amazing Assets <https://amazingassets.world>
 
namespace AmazingAssets.AdvancedDissolve.Editor
{
    internal static class AssetInfo
    {
        public const string assetName = "Advanced Dissolve";
        public static readonly string assetNameTrimmed = assetName.Replace(" ", string.Empty);
        public const string assetVersion = "2024.1";
        public const string assetStorePath = "content/111598";
        public const string assetStorePathShortLink = "http://u3d.as/16cX";
        public const string assetForumPath = "https://forum.unity.com/threads/advanced-dissolve.517354/";
        public const string assetManualLocation = "https://amazing-assets.gitbook.io/advanced-dissolve";

        public const string assetSupportMail = "<EMAIL>";
        public const string publisherPage = "https://assetstore.unity.com/publishers/1295";
    }
}
