%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: <PERSON><PERSON> Sparkle
  m_Shader: {fileID: 4800000, guid: 37b892986f78405fa95ee53a4810b437, type: 3}
  m_ValidKeywords:
  - _DIRECTION_Y
  - _INVERTDIRECTION_ON
  m_InvalidKeywords:
  - _ADD_PRECOMPUTED_VELOCITY
  m_LightmapFlags: 2
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap:
    MotionVector: User
  disabledShaderPasses:
  - TransparentBackface
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _Noise:
        m_Texture: {fileID: 2800000, guid: 62695c15a49d430abd2ae1471c72b1c7, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Normals:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Texture0:
        m_Texture: {fileID: 2800000, guid: af6ca4ce20747824fb44f614761895f4, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TextureSample2:
        m_Texture: {fileID: 2800000, guid: 5b653e484c8e303439ef414b62f969f0, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _texcoord:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AddPrecomputedVelocity: 1
    - _AlphaCutoff: 0.5
    - _AlphaCutoffEnable: 0
    - _AlphaDstBlend: 0
    - _AlphaSrcBlend: 1
    - _BlendMode: 0
    - _Brightness: 5
    - _CullMode: 2
    - _CullModeForward: 2
    - _Direction: 1
    - _DoubleSidedEnable: 0
    - _DoubleSidedNormalMode: 2
    - _DstBlend: 0
    - _EnableFogOnTransparent: 1
    - _EnvironmentReflections: 1
    - _Float1: 0.023
    - _Float3: 0.05
    - _Frequency: 4
    - _InvertDirection: 1
    - _QueueControl: 0
    - _QueueOffset: 0
    - _Range: 0.5
    - _ReceiveShadows: 1
    - _ReceivesSSR: 1
    - _RenderQueueType: 1
    - _RequireSplitLighting: 0
    - _ScreenContribution: 0.2
    - _ScreenMovementContribution: 0.2
    - _SpakleSpeed: 0.002
    - _SparklesBrightness: 5
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _StencilRef: 0
    - _StencilRefDepth: 8
    - _StencilRefDistortionVec: 4
    - _StencilRefGBuffer: 10
    - _StencilRefMV: 40
    - _StencilWriteMask: 6
    - _StencilWriteMaskDepth: 8
    - _StencilWriteMaskDistortionVec: 4
    - _StencilWriteMaskGBuffer: 14
    - _StencilWriteMaskMV: 40
    - _SurfaceType: 0
    - _TailHeadFalloff: 0.9
    - _Threshold: 0.5
    - _TransparentBackfaceEnable: 0
    - _TransparentCullMode: 2
    - _TransparentSortPriority: 0
    - _TransparentZWrite: 1
    - _UseShadowThreshold: 0
    - _ZTestDepthEqualForOpaque: 3
    - _ZTestGBuffer: 4
    - _ZTestTransparent: 4
    - _ZWrite: 1
    - __dirty: 0
    m_Colors:
    - _Albedo: {r: 0.7647059, g: 0.5730754, b: 0.112456016, a: 0}
    - _BodyGlow: {r: 0.6323529, g: 0.4794762, b: 0, a: 0}
    - _Color0: {r: 0.63235295, g: 0.47947615, b: 0, a: 0}
    - _DoubleSidedConstants: {r: 1, g: 1, b: -1, a: 0}
    - _EmissionColor: {r: 1, g: 1, b: 1, a: 1}
    - _GlintColor: {r: 1, g: 0.9154145, b: 0.42924252, a: 0}
    - _GlintFresnel: {r: 0.01, g: 3, b: 4, a: 0}
    - _MainGlowFresnel: {r: 0.02, g: 1, b: 5, a: 0}
    - _SizeSpeedInterval: {r: 0.5, g: 10, b: 5, a: 0}
    - _SparkleColor: {r: 1, g: 0.7779999, b: 0.29716682, a: 1}
    - _SparkleFresnel: {r: 0.02, g: 0.8, b: 2, a: 0}
  m_BuildTextureStacks: []
