{
    "m_SGVersion": 2,
    "m_Type": "UnityEditor.ShaderGraph.GraphData",
    "m_ObjectId": "c888f1df35794f25bcd334d5b066a13b",
    "m_Properties": [
        {
            "m_Id": "e3bff07fa6279c8b99078b50e8f3314e"
        },
        {
            "m_Id": "1d393f7c04d9b4839359bd7cdd70f310"
        },
        {
            "m_Id": "2e696f8ff8c5bc888552323a8a9c5210"
        },
        {
            "m_Id": "b7b7cdf9064c5286a1b1ccc94b5fc4fd"
        }
    ],
    "m_Keywords": [],
    "m_Nodes": [
        {
            "m_Id": "2433ef33b51be98e946b431d6954a428"
        },
        {
            "m_Id": "6d37c27fbc6cbb8286b7a7e2eeba570e"
        },
        {
            "m_Id": "3a6ad0b0d45eea8fa5e323d9de8d5ad3"
        },
        {
            "m_Id": "8fe758c84e8ae78791b4682d08b24bd1"
        },
        {
            "m_Id": "5cb840a387da5c8984d5a67bad132f44"
        },
        {
            "m_Id": "1edba9247c03b28b9a446317c4ac645a"
        },
        {
            "m_Id": "7f8c50f716fbee8e92309d628d85207a"
        }
    ],
    "m_GroupDatas": [],
    "m_StickyNoteDatas": [],
    "m_Edges": [
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "1edba9247c03b28b9a446317c4ac645a"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "5cb840a387da5c8984d5a67bad132f44"
                },
                "m_SlotId": 5
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "3a6ad0b0d45eea8fa5e323d9de8d5ad3"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "5cb840a387da5c8984d5a67bad132f44"
                },
                "m_SlotId": 2
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "5cb840a387da5c8984d5a67bad132f44"
                },
                "m_SlotId": 4
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "2433ef33b51be98e946b431d6954a428"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "6d37c27fbc6cbb8286b7a7e2eeba570e"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "5cb840a387da5c8984d5a67bad132f44"
                },
                "m_SlotId": 1
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "7f8c50f716fbee8e92309d628d85207a"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "5cb840a387da5c8984d5a67bad132f44"
                },
                "m_SlotId": 6
            }
        },
        {
            "m_OutputSlot": {
                "m_Node": {
                    "m_Id": "8fe758c84e8ae78791b4682d08b24bd1"
                },
                "m_SlotId": 0
            },
            "m_InputSlot": {
                "m_Node": {
                    "m_Id": "5cb840a387da5c8984d5a67bad132f44"
                },
                "m_SlotId": 3
            }
        }
    ],
    "m_VertexContext": {
        "m_Position": {
            "x": 266.0,
            "y": -5.0
        },
        "m_Blocks": []
    },
    "m_FragmentContext": {
        "m_Position": {
            "x": 266.0,
            "y": 195.0
        },
        "m_Blocks": []
    },
    "m_PreviewData": {
        "serializedMesh": {
            "m_SerializedMesh": "{\"mesh\":{\"instanceID\":0}}",
            "m_Guid": ""
        }
    },
    "m_Path": "Amazing Assets/Wireframe Shader",
    "m_ConcretePrecision": 0,
    "m_PreviewMode": 2,
    "m_OutputNode": {
        "m_Id": "2433ef33b51be98e946b431d6954a428"
    },
    "m_ActiveTargets": []
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "0a5c063bf5a63c81b88542d5793aec49",
    "m_Id": 1,
    "m_DisplayName": "Mask Value",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "MaskValue",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": [
        "X"
    ]
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "0e41ee201d918383ac0879b280c074b2",
    "m_Id": 0,
    "m_DisplayName": "Out",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [
        "X",
        "Y",
        "Z"
    ]
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.Internal.Vector3ShaderProperty",
    "m_ObjectId": "1d393f7c04d9b4839359bd7cdd70f310",
    "m_Guid": {
        "m_GuidSerialized": "35554d58-c97e-486e-9388-651ad53238bd"
    },
    "m_Name": "Plane Normal",
    "m_DefaultReferenceName": "Vector3_A44B3133",
    "m_OverrideReferenceName": "",
    "m_GeneratePropertyBlock": true,
    "m_Precision": 0,
    "overrideHLSLDeclaration": false,
    "hlslDeclarationOverride": 0,
    "m_Hidden": false,
    "m_Value": {
        "x": 0.0,
        "y": 1.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PropertyNode",
    "m_ObjectId": "1edba9247c03b28b9a446317c4ac645a",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Property",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -431.0,
            "y": -68.0,
            "width": 156.0,
            "height": 34.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "e5ba3d4a034e578fa9442d2f24dc8e13"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Property": {
        "m_Id": "e3bff07fa6279c8b99078b50e8f3314e"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "203188124dcace819fd2015cb29927e5",
    "m_Id": 2,
    "m_DisplayName": "edgeFalloff",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "edgeFalloff",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": [
        "X"
    ]
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.SubGraphOutputNode",
    "m_ObjectId": "2433ef33b51be98e946b431d6954a428",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Out_Vector4",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": 266.0,
            "y": -5.0,
            "width": 138.0,
            "height": 77.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "0a5c063bf5a63c81b88542d5793aec49"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "IsFirstSlotValid": true
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "29a8500db045ad89b1cb8f4ecfb04bdc",
    "m_Id": 3,
    "m_DisplayName": "invert",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "invert",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": [
        "X"
    ]
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.Internal.Vector1ShaderProperty",
    "m_ObjectId": "2e696f8ff8c5bc888552323a8a9c5210",
    "m_Guid": {
        "m_GuidSerialized": "4c1755af-d938-4686-8341-d1b75d1c2058"
    },
    "m_Name": "Edge Smooth",
    "m_DefaultReferenceName": "Vector1_C99B3E15",
    "m_OverrideReferenceName": "",
    "m_GeneratePropertyBlock": true,
    "m_Precision": 0,
    "overrideHLSLDeclaration": false,
    "hlslDeclarationOverride": 0,
    "m_Hidden": false,
    "m_Value": 0.0,
    "m_FloatType": 0,
    "m_RangeValues": {
        "x": 0.0,
        "y": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "2eda9cd43f1cba869887a35d90c695c6",
    "m_Id": 6,
    "m_DisplayName": "Plane Normal",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "PlaneNormal",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 1.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [
        "X",
        "Y",
        "Z"
    ]
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PropertyNode",
    "m_ObjectId": "3a6ad0b0d45eea8fa5e323d9de8d5ad3",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Property",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -445.0,
            "y": 200.00003051757813,
            "width": 154.0,
            "height": 34.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "b8278c5a1b17ef8dbdda74d012f7b572"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Property": {
        "m_Id": "2e696f8ff8c5bc888552323a8a9c5210"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "3c6b241e76673b87989fb8fff2daf2de",
    "m_Id": 0,
    "m_DisplayName": "Invert",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": [
        "X"
    ]
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.CustomFunctionNode",
    "m_ObjectId": "5cb840a387da5c8984d5a67bad132f44",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "WireframeShaderMaskPlane (Custom Function)",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -120.0,
            "y": -4.0,
            "width": 233.0,
            "height": 142.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "9512839b130fa48a8921543aaee0cf0b"
        },
        {
            "m_Id": "cb63cc7985b0f08682bad08d801e7ae8"
        },
        {
            "m_Id": "2eda9cd43f1cba869887a35d90c695c6"
        },
        {
            "m_Id": "5efe54e35f17a48286926a626634a149"
        },
        {
            "m_Id": "203188124dcace819fd2015cb29927e5"
        },
        {
            "m_Id": "29a8500db045ad89b1cb8f4ecfb04bdc"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": false,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_SourceType": 0,
    "m_FunctionName": "WireframeShaderMaskPlane",
    "m_FunctionSource": "36072c560231e604cb8f0c03c7ff456d",
    "m_FunctionBody": "Enter function body here..."
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "5efe54e35f17a48286926a626634a149",
    "m_Id": 1,
    "m_DisplayName": "vertexPositionWS",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "vertexPositionWS",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [
        "X",
        "Y",
        "Z"
    ]
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "6b857821de643a88abefe72b6cf9ea5e",
    "m_Id": 0,
    "m_DisplayName": "Plane Normal",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [
        "X",
        "Y",
        "Z"
    ]
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.PositionNode",
    "m_ObjectId": "6d37c27fbc6cbb8286b7a7e2eeba570e",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Position",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -495.0,
            "y": 62.0,
            "width": 206.0,
            "height": 131.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "0e41ee201d918383ac0879b280c074b2"
        }
    ],
    "synonyms": [],
    "m_Precision": 1,
    "m_PreviewExpanded": false,
    "m_PreviewMode": 2,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Space": 4
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PropertyNode",
    "m_ObjectId": "7f8c50f716fbee8e92309d628d85207a",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Property",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -431.0,
            "y": -5.0,
            "width": 152.0,
            "height": 34.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "6b857821de643a88abefe72b6cf9ea5e"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Property": {
        "m_Id": "1d393f7c04d9b4839359bd7cdd70f310"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.PropertyNode",
    "m_ObjectId": "8fe758c84e8ae78791b4682d08b24bd1",
    "m_Group": {
        "m_Id": ""
    },
    "m_Name": "Property",
    "m_DrawState": {
        "m_Expanded": true,
        "m_Position": {
            "serializedVersion": "2",
            "x": -417.0,
            "y": 244.00003051757813,
            "width": 112.0,
            "height": 34.0
        }
    },
    "m_Slots": [
        {
            "m_Id": "3c6b241e76673b87989fb8fff2daf2de"
        }
    ],
    "synonyms": [],
    "m_Precision": 0,
    "m_PreviewExpanded": true,
    "m_PreviewMode": 0,
    "m_CustomColors": {
        "m_SerializableColors": []
    },
    "m_Property": {
        "m_Id": "b7b7cdf9064c5286a1b1ccc94b5fc4fd"
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "9512839b130fa48a8921543aaee0cf0b",
    "m_Id": 4,
    "m_DisplayName": "value",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "value",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": [
        "X"
    ]
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.Internal.Vector1ShaderProperty",
    "m_ObjectId": "b7b7cdf9064c5286a1b1ccc94b5fc4fd",
    "m_Guid": {
        "m_GuidSerialized": "baf9261c-3c39-4cdb-bbc3-1502d4a4b343"
    },
    "m_Name": "Invert",
    "m_DefaultReferenceName": "Vector1_98EDB234",
    "m_OverrideReferenceName": "",
    "m_GeneratePropertyBlock": true,
    "m_Precision": 0,
    "overrideHLSLDeclaration": false,
    "hlslDeclarationOverride": 0,
    "m_Hidden": false,
    "m_Value": 0.0,
    "m_FloatType": 0,
    "m_RangeValues": {
        "x": 0.0,
        "y": 1.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector1MaterialSlot",
    "m_ObjectId": "b8278c5a1b17ef8dbdda74d012f7b572",
    "m_Id": 0,
    "m_DisplayName": "Edge Smooth",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": 0.0,
    "m_DefaultValue": 0.0,
    "m_Labels": [
        "X"
    ]
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "cb63cc7985b0f08682bad08d801e7ae8",
    "m_Id": 5,
    "m_DisplayName": "Plane Position",
    "m_SlotType": 0,
    "m_Hidden": false,
    "m_ShaderOutputName": "PlanePosition",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [
        "X",
        "Y",
        "Z"
    ]
}

{
    "m_SGVersion": 1,
    "m_Type": "UnityEditor.ShaderGraph.Internal.Vector3ShaderProperty",
    "m_ObjectId": "e3bff07fa6279c8b99078b50e8f3314e",
    "m_Guid": {
        "m_GuidSerialized": "3d59f0d9-eb24-4167-b039-1fd0a007b012"
    },
    "m_Name": "Plane Position",
    "m_DefaultReferenceName": "Vector3_D835B32E",
    "m_OverrideReferenceName": "",
    "m_GeneratePropertyBlock": true,
    "m_Precision": 0,
    "overrideHLSLDeclaration": false,
    "hlslDeclarationOverride": 0,
    "m_Hidden": false,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0,
        "w": 0.0
    }
}

{
    "m_SGVersion": 0,
    "m_Type": "UnityEditor.ShaderGraph.Vector3MaterialSlot",
    "m_ObjectId": "e5ba3d4a034e578fa9442d2f24dc8e13",
    "m_Id": 0,
    "m_DisplayName": "Plane Position",
    "m_SlotType": 1,
    "m_Hidden": false,
    "m_ShaderOutputName": "Out",
    "m_StageCapability": 3,
    "m_Value": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_DefaultValue": {
        "x": 0.0,
        "y": 0.0,
        "z": 0.0
    },
    "m_Labels": [
        "X",
        "Y",
        "Z"
    ]
}

