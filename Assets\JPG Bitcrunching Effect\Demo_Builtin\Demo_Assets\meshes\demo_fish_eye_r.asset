%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!43 &4300000
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: demo_fish_eye_r
  serializedVersion: 10
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 240
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 84
    localAABB:
      m_Center: {x: 0.13652396, y: 0.21268936, z: -0.72993296}
      m_Extent: {x: 0.3433091, y: 0.34442866, z: 0.055618465}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose:
  - e00: 1
    e01: 0
    e02: -0
    e03: 0
    e10: 0
    e11: 1
    e12: 0
    e13: 0
    e20: 0
    e21: -0
    e22: 1
    e23: -0
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: 1
    e01: 0
    e02: -0
    e03: -0.011112675
    e10: -0
    e11: 1
    e12: 0
    e13: -0.0011642873
    e20: 0
    e21: 0
    e22: 1
    e23: 0
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: -1.0000002
    e01: 0.00002937313
    e02: 0
    e03: -0.64736646
    e10: 0
    e11: -0.00000011920929
    e12: 1.0000001
    e13: 4.1119193e-16
    e20: 0.00002937313
    e21: 1.0000001
    e22: -0.00000011920929
    e23: 0.00009876619
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: -1.0000002
    e01: -0.00014712971
    e02: 0
    e03: -1.354551
    e10: 0
    e11: -0.00000011920929
    e12: 1.0000001
    e13: 4.976892e-16
    e20: -0.00014712971
    e21: 1.0000001
    e22: -0.00000011920929
    e23: -0.00014031588
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: -1
    e01: -0.00048060168
    e02: 2.910383e-11
    e03: -2.1633656
    e10: -2.910383e-11
    e11: 0
    e12: 1
    e13: 3.9862525e-16
    e20: -0.00048060168
    e21: 0.99999994
    e22: -0.00000011920929
    e23: -0.00086173764
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: -0.9999015
    e01: -0.014035968
    e02: 0
    e03: -2.9928126
    e10: 0
    e11: 0
    e12: 1
    e13: 4.025491e-16
    e20: -0.014035968
    e21: 0.9999015
    e22: 0
    e23: -0.041434355
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: -0.9997916
    e01: 0.020430014
    e02: 0
    e03: -3.8419251
    e10: 0
    e11: -0.00000011920929
    e12: 1.0000001
    e13: 7.983489e-16
    e20: 0.020430014
    e21: 0.99979144
    e22: -0.00000011920929
    e23: 0.7152465
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: -0.9997916
    e01: 0.020430014
    e02: 0
    e03: -4.2017236
    e10: 0
    e11: -0.00000011920929
    e12: 1.0000001
    e13: 7.474209e-16
    e20: 0.020430014
    e21: 0.99979144
    e22: -0.00000011920929
    e23: 0.7152465
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: -0.99992675
    e01: -0.012107229
    e02: 0
    e03: -3.8915958
    e10: 0
    e11: 0
    e12: 1
    e13: 2.9879386e-16
    e20: -0.012107229
    e21: 0.9999267
    e22: 0
    e23: -0.033927903
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: -0.99992675
    e01: -0.012107229
    e02: 0
    e03: -4.279387
    e10: 0
    e11: 0
    e12: 1
    e13: 2.5510348e-16
    e20: -0.012107229
    e21: 0.9999267
    e22: 0
    e23: -0.033927903
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: -0.99921274
    e01: -0.039670877
    e02: 0
    e03: -3.8479002
    e10: 0
    e11: 0
    e12: 1
    e13: -1.8828514e-16
    e20: -0.039670877
    e21: 0.99921274
    e22: 0
    e23: -0.78897315
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: -0.99921274
    e01: -0.039670877
    e02: 0
    e03: -4.2241964
    e10: 0
    e11: 0
    e12: 1
    e13: -2.214354e-16
    e20: -0.039670877
    e21: 0.99921274
    e22: 0
    e23: -0.78897315
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: 0.00000017881393
    e01: -6.617445e-24
    e02: -0.99999994
    e03: -0.72993296
    e10: -1.2246469e-16
    e11: -1
    e12: -1.9852335e-23
    e13: 0.21268936
    e20: -0.99999994
    e21: 1.2246469e-16
    e22: -0.00000011920929
    e23: 0.13652386
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: 0.00000017881393
    e01: -6.617445e-24
    e02: -0.99999994
    e03: -0.78256124
    e10: -1.2246469e-16
    e11: -1
    e12: -1.9852335e-23
    e13: 0.21268936
    e20: -0.99999994
    e21: 1.2246469e-16
    e22: -0.00000011920929
    e23: 0.13652386
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: -0.00000011920929
    e01: -0
    e02: 1.0000001
    e03: -0.729933
    e10: 0
    e11: 1
    e12: 0
    e13: -0.212689
    e20: -1.0000001
    e21: 0
    e22: -0.00000011920929
    e23: 0.136524
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  - e00: 0.00000017881393
    e01: 8.881784e-16
    e02: -0.99999994
    e03: 0.782561
    e10: -0.000000021073422
    e11: 1
    e12: -2.6645353e-15
    e13: -0.212689
    e20: 0.99999994
    e21: 0.000000021073422
    e22: 0.00000017881393
    e23: -0.13652411
    e30: 0
    e31: 0
    e32: 0
    e33: 1
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB:
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: -0.055618525, y: -0.34442866, z: -0.3433091}
    m_Max: {x: 0.055618405, y: 0.34442866, z: 0.34330907}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  - m_Min: {x: Infinity, y: Infinity, z: Infinity}
    m_Max: {x: -Infinity, y: -Infinity, z: -Infinity}
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 020001000000020000000300020003000400020004000500020005000600020006000700020007000800020008000900020009000a0002000a000b0002000b000c0002000c000d0002000d000e0002000e000f0002000f00100002001000110002001100120002001200130002001300140002001400010017001600150017001800160017001900180017001a00190017001b001a0017001c001b0017001d001c0017001e001d0017001f001e00170020001f001700210020001700220021001700230022001700240023001700250024001700260025001700270026001700280027001700290028001700150029002c002b002a002d002c002a002f002e002b002c002f002b00310030002e002f0031002e003300320030003100330030003500340032003300350032003700360034003500370034003900380036003700390036003b003a00380039003b0038003d003c003a003b003d003a003f003e003c003d003f003c00410040003e003f0041003e004300420040004100430040004500440042004300450042004700460044004500470044004900480046004700490046004b004a00480049004b0048004d004c004a004b004d004a004f004e004c004d004f004c00510050004e004f0051004e00530052005000510053005000
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 84
    m_Channels:
    - stream: 0
      offset: 0
      format: 0
      dimension: 3
    - stream: 0
      offset: 12
      format: 0
      dimension: 3
    - stream: 0
      offset: 24
      format: 0
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 1
      offset: 0
      format: 0
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 2
      offset: 0
      format: 0
      dimension: 4
    - stream: 2
      offset: 16
      format: 10
      dimension: 4
    m_DataSize: 6720
    _typelessdata: 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
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0.13652396, y: 0.21268936, z: -0.72993296}
    m_Extent: {x: 0.3433091, y: 0.34442866, z: 0.055618465}
  m_MeshUsageFlags: 0
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  m_MeshMetrics[0]: 1
  m_MeshMetrics[1]: 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
