%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!28 &-3903479665328065482
Texture2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: z___Gradient_ShadingTex{"mode":0,"colorKeys":[{"color":{"r":0.3960784375667572,"g":0.9176470637321472,"b":0.8196078538894653,"a":1.0},"time":0.2500038146972656},{"color":{"r":0.95686274766922,"g":0.4117647111415863,"b":0.6627451181411743,"a":1.0},"time":0.5000076293945313},{"color":{"r":1.0,"g":0.5686274766921997,"b":0.48235294222831728,"a":1.0},"time":0.7499961853027344},{"color":{"r":0.9882352948188782,"g":1.0,"b":0.7568627595901489,"a":1.0},"time":1.0}],"alphaKeys":[{"alpha":1.0,"time":0.0},{"alpha":1.0,"time":1.0}]}
  m_ImageContentsHash:
    serializedVersion: 2
    Hash: 00000000000000000000000000000000
  m_IsAlphaChannelOptional: 0
  serializedVersion: 3
  m_Width: 256
  m_Height: 1
  m_CompleteImageSize: 1024
  m_MipsStripped: 0
  m_TextureFormat: 5
  m_MipCount: 1
  m_IsReadable: 1
  m_IsPreProcessed: 0
  m_IgnoreMipmapLimit: 0
  m_MipmapLimitGroupName: 
  m_StreamingMipmaps: 0
  m_StreamingMipmapsPriority: 0
  m_VTOnly: 0
  m_AlphaIsTransparency: 0
  m_ImageCount: 1
  m_TextureDimension: 2
  m_TextureSettings:
    serializedVersion: 2
    m_FilterMode: 1
    m_Aniso: 1
    m_MipBias: 0
    m_WrapU: 1
    m_WrapV: 1
    m_WrapW: 1
  m_LightmapFormat: 0
  m_ColorSpace: 1
  m_PlatformBlob: 
  image data: 1024
  _typelessdata: ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff65ead1ff66e9d1ff68e7d0ff6ae5d0ff6ce3cfff6fe1ceff71dfceff73ddcdff75dbccff78d9ccff7ad7cbff7cd5cbff7ed3caff80d1c9ff83cfc9ff85cdc8ff87cbc7ff89c9c7ff8cc7c6ff8ec5c6ff90c3c5ff92c1c4ff95bfc4ff97bdc3ff99bbc2ff9bb9c2ff9eb7c1ffa0b5c1ffa2b3c0ffa4b1bfffa7afbfffa9adbeffababbdffada9bdffb0a7bcffb2a5bcffb4a3bbffb6a1baffb99fbaffbb9db9ffbd9bb8ffbf99b8ffc297b7ffc495b6ffc692b6ffc890b5ffca8eb5ffcd8cb4ffcf8ab3ffd188b3ffd386b2ffd684b1ffd882b1ffda80b0ffdc7eb0ffdf7cafffe17aaeffe378aeffe576adffe874acffea72acffec70abffee6eabfff16caafff36aa9fff469a9fff46aa8fff46ba7fff56ba6fff56ca6fff56ca5fff56da4fff56ea4fff56ea3fff66fa2fff670a1fff670a1fff671a0fff6719ffff7729ffff7739efff7739dfff7749cfff7759cfff7759bfff8769afff87699fff87799fff87898fff87897fff87997fff97a96fff97a95fff97b94fff97c94fff97c93fff97d92fffa7d92fffa7e91fffa7f90fffa7f8ffffa808ffffa818efffb818dfffb828cfffb828cfffb838bfffb848afffc848afffc8589fffc8688fffc8687fffc8787fffc8786fffd8885fffd8985fffd8984fffd8a83fffd8b82fffd8b82fffe8c81fffe8c80fffe8d80fffe8e7ffffe8e7efffe8f7dffff907dffff907cffff917bffff927cffff947dffff967effff977fffff9980ffff9b81ffff9d82ffff9e84ffffa085ffffa286fffea487fffea588fffea789fffea98afffeaa8bfffeac8cfffeae8dfffeb08efffeb190fffeb391fffeb592fffeb793fffeb894fffeba95fffebc96fffebd97fffebf98fffec199fffec39bfffec49cfffec69dfffec89efffdca9ffffdcba0fffdcda1fffdcfa2fffdd0a3fffdd2a4fffdd4a6fffdd6a7fffdd7a8fffdd9a9fffddbaafffddcabfffddeacfffde0adfffde2aefffde3affffde5b1fffde7b2fffde9b3fffdeab4fffdecb5fffceeb6fffcefb7fffcf1b8fffcf3b9fffcf5bafffcf6bcfffcf8bdfffcfabefffcfcbffffcfdc0fffcffc1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!114 &-1826353197901718966
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0353a89b1f911e48b9e16bdc9f2e058, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 9
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Vase - Chroma - Pink
  m_Shader: {fileID: -6465566751694194690, guid: 6f3f4768a2d0d0349949aa010fa89c98, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords: []
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses:
  - MOTIONVECTORS
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BaseMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - __Gradient_Shading:
        m_Texture: {fileID: -3903479665328065482}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AlphaClip: 0
    - _Blend: 0
    - _BumpScale: 1
    - _ClearCoatMask: 0
    - _ClearCoatSmoothness: 0
    - _Cull: 2
    - _Cutoff: 0.5
    - _DetailAlbedoMapScale: 1
    - _DetailNormalMapScale: 1
    - _DstBlend: 0
    - _EnvironmentReflections: 1
    - _GlossMapScale: 0
    - _Glossiness: 0
    - _GlossyReflections: 0
    - _Metallic: 0
    - _OcclusionStrength: 1
    - _Parallax: 0.005
    - _QueueControl: 0
    - _QueueOffset: 0
    - _ReceiveShadows: 1
    - _Smoothness: 0.5
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _Surface: 0
    - _WorkflowMode: 1
    - _ZWrite: 1
    m_Colors:
    - _BaseColor: {r: 0.509434, g: 0.509434, b: 0.509434, a: 1}
    - _Color: {r: 0.509434, g: 0.509434, b: 0.509434, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _SpecColor: {r: 0.19999996, g: 0.19999996, b: 0.19999996, a: 1}
  m_BuildTextureStacks: []
  m_AllowLocking: 1
