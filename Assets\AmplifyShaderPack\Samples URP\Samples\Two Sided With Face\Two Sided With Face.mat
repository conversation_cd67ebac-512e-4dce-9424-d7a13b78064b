%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Two Sided With Face
  m_Shader: {fileID: 4800000, guid: 88a40ef964924d62b9fcd8abd9b4ac8d, type: 3}
  m_ValidKeywords: []
  m_InvalidKeywords: []
  m_LightmapFlags: 5
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BackAlbedo:
        m_Texture: {fileID: 2800000, guid: f0325b098cd84bc1a847391f531b3007, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BackNormalMap:
        m_Texture: {fileID: 2800000, guid: 8ec217f770e34536be4d0dee12abb7a6, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BackTexture:
        m_Texture: {fileID: 2800000, guid: b297077dae62c1944ba14cad801cddf5, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _FrontAlbedo:
        m_Texture: {fileID: 2800000, guid: 6633e66536f148a281a9fcaa7ef60863, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _FrontNormalMap:
        m_Texture: {fileID: 2800000, guid: b91e3e42ac594b849bc5700ddfa92aa0, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _FrontTexture:
        m_Texture: {fileID: 2800000, guid: c68296334e691ed45b62266cbc716628, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Grass_N:
        m_Texture: {fileID: 2800000, guid: f5453dca2ac649e4182c56a3966ad395, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Mask:
        m_Texture: {fileID: 2800000, guid: 4a78764018124872b6f511e00fb51f97, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _rock_d:
        m_Texture: {fileID: 2800000, guid: b297077dae62c1944ba14cad801cddf5, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _rock_n:
        m_Texture: {fileID: 2800000, guid: 0bebe40e9ebbecc48b8e9cfea982da7e, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _texcoord:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AlphaCutoff: 0.5
    - _Cull: 0
    - _Cutoff: 0.5
    - _EnvironmentReflections: 1
    - _MaskClipValue: 0.5
    - _QueueControl: 0
    - _QueueOffset: 0
    - _ReceiveShadows: 1
    - _SpecularHighlights: 1
    - __dirty: 0
    m_Colors:
    - _BackColor: {r: 0, g: 0, b: 1, a: 0}
    - _EmissionColor: {r: 1, g: 1, b: 1, a: 1}
    - _FrontColor: {r: 1, g: 0, b: 0, a: 0}
  m_BuildTextureStacks: []
