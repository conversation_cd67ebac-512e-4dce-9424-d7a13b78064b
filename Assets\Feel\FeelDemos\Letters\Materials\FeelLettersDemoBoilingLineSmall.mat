%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 6
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: FeelLettersDemoBoilingLineSmall
  m_Shader: {fileID: 4800000, guid: f54b8ae4d72e16445b407e4ad5ad4264, type: 3}
  m_ShaderKeywords: ETC1_EXTERNAL_ALPHA UNITY_UI_ALPHACLIP _COLOR_SHIFTING_ON _NOISE_UVSOURCE_UV
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTexStatic:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Noise:
        m_Texture: {fileID: 2800000, guid: 75b254a55f7e3f347ac8f2b5db9b11d9, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 1, y: 1}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Floats:
    - _AlphaCutoff: 0.5
    - _Amount: 0.075
    - _AmountVertex: 0
    - _BumpScale: 1
    - _COLOR_SHIFTING: 1
    - _ColorMask: 15
    - _Cutoff: 0.5
    - _DesaturateAmount: 0
    - _DetailNormalMapScale: 1
    - _DstBlend: 0
    - _EmissiveIntensity: 1
    - _GlossMapScale: 1
    - _Glossiness: 0.5
    - _GlossyReflections: 1
    - _MULTIPLY_TINT: 0
    - _Metallic: 0
    - _Mode: 0
    - _NOISE_UVSOURCE: 0
    - _NOISE_VERTEX: 0
    - _OcclusionStrength: 1
    - _Parallax: 0.02
    - _SKIP_PER_RENDERERDATA: 0
    - _ScaleVertex: 1
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _Stencil: 0
    - _StencilComp: 8
    - _StencilOp: 0
    - _StencilReadMask: 255
    - _StencilWriteMask: 255
    - _TimeQuantize: 15
    - _USE_EMISSION: 0
    - _UVSec: 0
    - _UseUIAlphaClip: 1
    - _ZWrite: 1
    m_Colors:
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _EmissiveColor: {r: 1, g: 0.7666667, b: 0, a: 0}
    - _HSVShift: {r: 0, g: 0, b: 0, a: 0}
    - _PanSpeed: {r: 2, g: 2, b: 1, a: 1}
    - _TintColor: {r: 1, g: 1, b: 1, a: 1}
