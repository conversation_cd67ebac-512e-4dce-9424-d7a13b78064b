# BTR Development Commands

## Windows System Commands
- **Directory listing**: `dir` or `ls` (if using PowerShell with aliases)
- **Change directory**: `cd <path>`
- **Find files**: `Get-ChildItem -Recurse -Name "*.cs"` (PowerShell)
- **Search in files**: `Select-String -Path "*.cs" -Pattern "pattern"`
- **Git operations**: `git status`, `git add .`, `git commit -m "message"`

## Unity Development Workflow
- **Open Unity**: Launch Unity Hub and open BTR U6 2025 RG project
- **Scene navigation**: Open `Assets/_Scenes/Levels/Ouroboros - Base.unity` for main testing
- **Build project**: File → Build Settings → Build
- **Package Manager**: Window → Package Manager (for Unity packages)

## Testing & Validation
- **Play Mode Testing**: Enter Play Mode in Unity Editor
- **Scene Testing**: Test individual Ouroboros scenes
- **Performance Profiling**: Window → Analysis → Profiler
- **Console Monitoring**: Window → General → Console (check for errors/warnings)

## Code Quality & Maintenance
- **Debug System**: Uses Unity's standard Debug.Log() (migrated from custom BTRDebug)
- **Performance Monitoring**: Built-in performance monitoring tools
- **Memory Profiling**: Unity Memory Profiler package
- **Code Analysis**: Use IDE built-in analysis (Rider/Visual Studio)

## Custom Unity Menu Items
- **BTR Tools**: Look for "BTR" menu items in Unity Editor
- **Performance Tools**: BTR → Performance → Performance Optimizer
- **Audio Tools**: BTR → Audio → Audio Configuration
- **Debug Tools**: BTR → Debug → Interactive Performance Monitor

## PowerShell Scripts
- **Migration Scripts**: Various .ps1 files in root for system migrations
- **Namespace Updates**: `ChronosToEpochBatchMigration.ps1`
- **Field Fixes**: `fix_duplicate_fields.ps1`, `fix_parent_fields.ps1`

## Development Best Practices
- **Always backup scenes** before major changes
- **Test in Play Mode** after code changes
- **Check Console** for errors before committing
- **Use Git** for version control with meaningful commit messages
- **Follow namespace conventions** when creating new scripts