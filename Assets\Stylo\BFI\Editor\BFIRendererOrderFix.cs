#if UNITY_EDITOR && URP_INSTALLED
using System.Reflection;
using UnityEngine;
using UnityEngine.Rendering.Universal;
using UnityEditor;
using Stylo.BFI.Universal;

namespace Stylo.BFI.Editor
{
    public static class BFIRendererOrderFix
    {
        [MenuItem("Stylo/BFI/Fix Renderer Order (Move to First)")]
        public static void MoveBFIToFirst()
        {
            var urpAsset = UnityEngine.Rendering.GraphicsSettings.currentRenderPipeline as UniversalRenderPipelineAsset;
            if (urpAsset == null)
            {
                Debug.LogError("No URP asset found in Graphics Settings");
                return;
            }

            // Get renderer data using reflection
            var rendererDataListField = typeof(UniversalRenderPipelineAsset).GetField("m_RendererDataList", 
                BindingFlags.NonPublic | BindingFlags.Instance);
            
            if (rendererDataListField == null)
            {
                Debug.LogError("Could not access renderer data list");
                return;
            }
            
            var rendererDataArray = rendererDataListField.GetValue(urpAsset) as ScriptableRendererData[];
            if (rendererDataArray == null || rendererDataArray.Length == 0)
            {
                Debug.LogError("No renderer data found");
                return;
            }
            
            var rendererData = rendererDataArray[0] as UniversalRendererData;
            if (rendererData == null)
            {
                Debug.LogError("First renderer is not UniversalRendererData");
                return;
            }

            // Find BFI renderer feature
            BFIRendererFeature bfiFeature = null;
            int bfiIndex = -1;
            
            for (int i = 0; i < rendererData.rendererFeatures.Count; i++)
            {
                if (rendererData.rendererFeatures[i] is BFIRendererFeature feature)
                {
                    bfiFeature = feature;
                    bfiIndex = i;
                    break;
                }
            }

            if (bfiFeature == null)
            {
                Debug.LogError("BFI Renderer Feature not found in renderer");
                return;
            }

            if (bfiIndex == 0)
            {
                Debug.Log("BFI Renderer Feature is already first in the list");
                return;
            }

            // Get the renderer features list using reflection
            var featuresField = typeof(UniversalRendererData).GetField("m_RendererFeatures", 
                BindingFlags.NonPublic | BindingFlags.Instance);
            
            if (featuresField == null)
            {
                Debug.LogError("Could not access renderer features list");
                return;
            }

            var featuresList = featuresField.GetValue(rendererData) as System.Collections.Generic.List<ScriptableRendererFeature>;
            if (featuresList == null)
            {
                Debug.LogError("Renderer features list is null");
                return;
            }

            // Remove BFI from current position
            featuresList.RemoveAt(bfiIndex);
            
            // Insert BFI at the beginning
            featuresList.Insert(0, bfiFeature);

            // Mark dirty and save
            EditorUtility.SetDirty(rendererData);
            AssetDatabase.SaveAssets();
            
            Debug.Log($"✅ BFI Renderer Feature moved from position {bfiIndex} to position 0 (first)");
            Debug.Log("BFI will now execute before all other renderer features");
            
            // Show the renderer data asset
            EditorGUIUtility.PingObject(rendererData);
        }

        [MenuItem("Stylo/BFI/Show Renderer Feature Order")]
        public static void ShowRendererFeatureOrder()
        {
            var urpAsset = UnityEngine.Rendering.GraphicsSettings.currentRenderPipeline as UniversalRenderPipelineAsset;
            if (urpAsset == null)
            {
                Debug.LogError("No URP asset found");
                return;
            }

            var rendererDataListField = typeof(UniversalRenderPipelineAsset).GetField("m_RendererDataList", 
                BindingFlags.NonPublic | BindingFlags.Instance);
            
            if (rendererDataListField == null) return;
            
            var rendererDataArray = rendererDataListField.GetValue(urpAsset) as ScriptableRendererData[];
            if (rendererDataArray == null || rendererDataArray.Length == 0) return;
            
            var rendererData = rendererDataArray[0] as UniversalRendererData;
            if (rendererData == null) return;

            Debug.Log("=== Renderer Feature Execution Order ===");
            
            for (int i = 0; i < rendererData.rendererFeatures.Count; i++)
            {
                var feature = rendererData.rendererFeatures[i];
                if (feature != null)
                {
                    string status = feature.isActive ? "✅ Active" : "❌ Inactive";
                    string isBFI = feature is BFIRendererFeature ? " 🎯 BFI" : "";
                    Debug.Log($"{i}: {feature.name} ({feature.GetType().Name}) - {status}{isBFI}");
                }
                else
                {
                    Debug.Log($"{i}: [NULL FEATURE]");
                }
            }
            
            Debug.Log("=== End Renderer Feature Order ===");
        }

        [MenuItem("Stylo/BFI/Fix Renderer Order (Move to First)", true)]
        public static bool ValidateMoveBFIToFirst()
        {
            var urpAsset = UnityEngine.Rendering.GraphicsSettings.currentRenderPipeline as UniversalRenderPipelineAsset;
            return urpAsset != null;
        }
    }
}
#endif
