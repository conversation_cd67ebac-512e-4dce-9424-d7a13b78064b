%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Parallax Occlusion Mapping
  m_Shader: {fileID: 4800000, guid: 1bae747ec41d4a72b45a5885fe911899, type: 3}
  m_ValidKeywords: []
  m_InvalidKeywords: []
  m_LightmapFlags: 5
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _Albedo:
        m_Texture: {fileID: 2800000, guid: 4998839cc7304cff87a29e2535dd1bbb, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _HeightMap:
        m_Texture: {fileID: 2800000, guid: 085af6efd01d48aaa9981d4b8d75ecce, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Metallic:
        m_Texture: {fileID: 2800000, guid: 0f1be42ec7d6427fbeb99bb7a4480d54, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NewAlbedo:
        m_Texture: {fileID: 2800000, guid: 433c486a4b3bf12429cb2a5c0a2b2ce7, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NewNormal:
        m_Texture: {fileID: 2800000, guid: b30f5c2b49e801743b7a7387fe63cbfb, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Normal:
        m_Texture: {fileID: 2800000, guid: 0e5318892134427a92eb01a797250708, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Occlusion:
        m_Texture: {fileID: 2800000, guid: d6dd85c78644495f98c427634c7d81e7, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Roughness:
        m_Texture: {fileID: 2800000, guid: b6a6c7bc322040c5b1fed27b535af25f, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SSSDepth:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TextureSample0:
        m_Texture: {fileID: 2800000, guid: 03bfb407f5ca36749a704dbff6bb9c7a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _texcoord:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AlphaCutoff: 0.5
    - _Bias: 0.61
    - _CurvFix: 1
    - _CurvatureU: 0
    - _CurvatureV: 0
    - _EnvironmentReflections: 1
    - _Float0: 0.34
    - _Glossiness: 0.5
    - _Metallic: 0
    - _NormalScale: 1
    - _QueueControl: 0
    - _QueueOffset: 0
    - _ReceiveShadows: 1
    - _Ref: -0.03
    - _RoughScale: 0.5
    - _Scale: 0.095
    - _ScatteringPower: 0.1
    - _SpecularHighlights: 1
    - _Translucency: 0
    - _TranslucencyViewDependency: 0
    - __dirty: 0
    m_Colors:
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _Color0: {r: 0, g: 0, b: 0, a: 0}
    - _Color1: {r: 1, g: 0, b: 0, a: 0}
    - _EmissionColor: {r: 1, g: 1, b: 1, a: 1}
    - _SSSColor: {r: 1, g: 1, b: 1, a: 1}
  m_BuildTextureStacks: []
