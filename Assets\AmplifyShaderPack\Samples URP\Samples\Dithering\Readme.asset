%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e78934177bebe0545ab566bb787b0282, type: 3}
  m_Name: Readme
  m_EditorClassIdentifier: 
  Icon: {fileID: 2800000, guid: e254e82d21091b7409af2dada16020e6, type: 3}
  Title: Dithering
  Description:
    Heading: 
    Text: This sample performs alpha clipping through a dithering pattern, either
      via a 4x4 Bayer pattern or through a blue noise texture.
    LinkText: 
    Url: 
  PropertiesHeader:
    Heading: Properties
    Text: 
    LinkText: 
    Url: 
  Properties:
  - Heading: Mask Clip Value
    Text: Value from which alpha clip should be performed.
    LinkText: 
    Url: 
  - Heading: Albedo
    Text: Albedo texture to be applied to surface.
    LinkText: 
    Url: 
  - Heading: Normal
    Text: Normals texture to be applied to surface.
    LinkText: 
    Url: 
  - Heading: Specular
    Text: Specular texture to be applied to surface.
    LinkText: 
    Url: 
  - Heading: Occlusion
    Text: Occlusion texture to be applied to surface.
    LinkText: 
    Url: 
  - Heading: Blue Noise (only on DitheringFadeBlueNoise)
    Text: Blue noise texture to be used as dithering pattern.
    LinkText: 
    Url: 
  - Heading: Start Dithering Fade
    Text: Value in camera/eye space to when start performing dithering.
    LinkText: 
    Url: 
  - Heading: End Dithering Fade
    Text: Value in camera/eye space to when stop performing dithering.
    LinkText: 
    Url: 
  AdditionalProperties: []
  AdditionalScripts: []
  LoadedLayout: 0
  RPType: 3
