%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 94ddca8ef013926408780e6e292bad16, type: 3}
  m_Name: DemoElement_TextureBlending
  m_EditorClassIdentifier: 
  demoName: Texture Blending
  prefab: {fileID: 2481555142680936367, guid: 126e619f8065a6f4296f6bad0cd27765, type: 3}
  environment: {fileID: 11400000, guid: b2ff69ed7bda20048b17e73eedc7ab4c, type: 2}
  info: Blend up to 3 textures and their texture maps with this effect. Either by
    using a texture or using vertex colors (you can use Polybrush to paint vertex
    colors in the Editor).
  directionalLightEnabled: 1
  mainLightIntensity: 1
  skyboxEnabled: 1
  postProcessEnabled: 1
