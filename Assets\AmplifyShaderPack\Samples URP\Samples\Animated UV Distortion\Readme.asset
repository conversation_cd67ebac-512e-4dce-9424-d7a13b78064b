%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e78934177bebe0545ab566bb787b0282, type: 3}
  m_Name: Readme
  m_EditorClassIdentifier: 
  Icon: {fileID: 2800000, guid: 96389c3a9f2fa5049b73ad2ad9b313e6, type: 3}
  Title: Animated UV Distortion
  Description:
    Heading: 
    Text: This sample distorts and animates UVs which are then used to sample a main
      texture.
    LinkText: 
    Url: 
  PropertiesHeader:
    Heading: Properties
    Text: 
    LinkText: 
    Url: 
  Properties:
  - Heading: Main Texture
    Text: Texture to be sampled via the modified Uvs.
    LinkText: 
    Url: 
  - Heading: Distort Texture
    Text: Terxure ( Normal Map ) which contains direction info on how to distort
      UVs.
    LinkText: 
    Url: 
  - Heading: Tint Color
    Text: Color tint to be applied to final result.
    LinkText: 
    Url: 
  - Heading: Speed
    Text: Speed used across distortion panning animation.
    LinkText: 
    Url: 
  - Heading: UV Distort Intensity
    Text: Value to scale distortion intensity retrieved from Distort Texture.
    LinkText: 
    Url: 
  AdditionalProperties: []
  AdditionalScripts: []
  LoadedLayout: 0
  RPType: 3
