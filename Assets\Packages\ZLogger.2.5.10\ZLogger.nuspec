﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2013/05/nuspec.xsd">
  <metadata>
    <id>ZLogger</id>
    <version>2.5.10</version>
    <authors>Cysharp</authors>
    <license type="expression">MIT</license>
    <licenseUrl>https://licenses.nuget.org/MIT</licenseUrl>
    <icon>Icon.png</icon>
    <projectUrl>https://github.com/Cysharp/ZLogger</projectUrl>
    <description>Zero Allocation Text/Strcutured Logger for .NET Core, built on top of a Microsoft.Extensions.Logging.</description>
    <copyright>© Cysharp, Inc.</copyright>
    <tags>logging</tags>
    <repository type="git" url="https://github.com/Cysharp/ZLogger" commit="ac885aa1e6588d28aaf6b523682a591e97ed9ba5" />
    <dependencies>
      <group targetFramework="net6.0">
        <dependency id="Microsoft.Bcl.TimeProvider" version="8.0.0" exclude="Build,Analyzers" />
        <dependency id="Microsoft.Extensions.Logging" version="8.0.0" exclude="Build,Analyzers" />
        <dependency id="System.IO.Hashing" version="8.0.0" exclude="Build,Analyzers" />
        <dependency id="System.Threading.Channels" version="8.0.0" exclude="Build,Analyzers" />
        <dependency id="Utf8StringInterpolation" version="1.3.1" exclude="Build,Analyzers" />
      </group>
      <group targetFramework="net8.0">
        <dependency id="Microsoft.Extensions.Logging" version="8.0.0" exclude="Build,Analyzers" />
        <dependency id="System.IO.Hashing" version="8.0.0" exclude="Build,Analyzers" />
        <dependency id="System.Threading.Channels" version="8.0.0" exclude="Build,Analyzers" />
        <dependency id="Utf8StringInterpolation" version="1.3.1" exclude="Build,Analyzers" />
      </group>
      <group targetFramework=".NETStandard2.0">
        <dependency id="Microsoft.Bcl.AsyncInterfaces" version="8.0.0" exclude="Build,Analyzers" />
        <dependency id="Microsoft.Bcl.TimeProvider" version="8.0.0" exclude="Build,Analyzers" />
        <dependency id="Microsoft.Extensions.Logging" version="8.0.0" exclude="Build,Analyzers" />
        <dependency id="System.Collections.Immutable" version="8.0.0" exclude="Build,Analyzers" />
        <dependency id="System.IO.Hashing" version="8.0.0" exclude="Build,Analyzers" />
        <dependency id="System.Text.Json" version="8.0.5" exclude="Build,Analyzers" />
        <dependency id="System.Threading.Channels" version="8.0.0" exclude="Build,Analyzers" />
        <dependency id="Utf8StringInterpolation" version="1.3.1" exclude="Build,Analyzers" />
      </group>
      <group targetFramework=".NETStandard2.1">
        <dependency id="Microsoft.Bcl.AsyncInterfaces" version="8.0.0" exclude="Build,Analyzers" />
        <dependency id="Microsoft.Bcl.TimeProvider" version="8.0.0" exclude="Build,Analyzers" />
        <dependency id="Microsoft.Extensions.Logging" version="8.0.0" exclude="Build,Analyzers" />
        <dependency id="System.Collections.Immutable" version="8.0.0" exclude="Build,Analyzers" />
        <dependency id="System.IO.Hashing" version="8.0.0" exclude="Build,Analyzers" />
        <dependency id="System.Text.Json" version="8.0.5" exclude="Build,Analyzers" />
        <dependency id="System.Threading.Channels" version="8.0.0" exclude="Build,Analyzers" />
        <dependency id="Utf8StringInterpolation" version="1.3.1" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
</package>