using System;
using UnityEngine;

namespace ExtendedImportSettings
{
    [Serializable]
    public class SubmeshCombinerData : PerMeshDataBase
    {
        public bool Combine;
    }

    [Serializable]
    public class SubmeshCombinerModule : PerMeshModuleBase<SubmeshCombinerData>
    {
        protected override void OnPostprocessModelPerMesh(Mesh mesh, SubmeshCombinerData data,
            GameObject meshGameObject, GameObject rootGameObject)
        {
            if(!data.Combine)
                return;
            
            mesh.SetTriangles(mesh.triangles, 0);
            mesh.subMeshCount = 1;
            
            if (meshGameObject.TryGetComponent<Renderer>(out var renderer))
                renderer.sharedMaterials = new Material[] { renderer.sharedMaterials[0] };
        }
    }
}