using UnityEngine;
using UnityEditor;
using UnityEngine.SceneManagement;
using UnityEditor.SceneManagement;
using System.Collections.Generic;
using System.Linq;

namespace Stylo.Cadance.Editor
{
    /// <summary>
    /// Tool to migrate Koreographer components to Cadance equivalents in scenes and prefabs.
    /// Phase 3 Component Migration Tool.
    /// </summary>
    public class CadanceComponentMigrationTool : EditorWindow
    {
        private Vector2 scrollPosition;
        private List<ComponentMigrationInfo> foundComponents = new List<ComponentMigrationInfo>();
        private bool scanComplete = false;
        private bool showMigrationOptions = false;

        [System.Serializable]
        public class ComponentMigrationInfo
        {
            public string sceneName;
            public string gameObjectName;
            public string gameObjectPath;
            public string componentType;
            public string targetComponentType;
            public bool isActive;
            public bool shouldMigrate = true;
            public string migrationNotes;
        }

        [MenuItem("Stylo/Cadance/Component Migration Tool")]
        public static void ShowWindow()
        {
            GetWindow<CadanceComponentMigrationTool>("Cadance Component Migration");
        }

        private void OnGUI()
        {
            EditorGUILayout.LabelField("Cadance Component Migration Tool", EditorStyles.boldLabel);
            EditorGUILayout.Space();

            EditorGUILayout.HelpBox(
                "This tool scans scenes for Koreographer components and helps migrate them to Cadance equivalents.\n" +
                "Phase 3: Component Migration",
                MessageType.Info);

            EditorGUILayout.Space();

            if (GUILayout.Button("Scan All Scenes for Koreographer Components"))
            {
                ScanAllScenes();
            }

            if (scanComplete && foundComponents.Count > 0)
            {
                EditorGUILayout.Space();
                EditorGUILayout.LabelField($"Found {foundComponents.Count} Koreographer components to migrate:", EditorStyles.boldLabel);

                scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition, GUILayout.Height(300));

                foreach (var component in foundComponents)
                {
                    EditorGUILayout.BeginVertical("box");

                    EditorGUILayout.BeginHorizontal();
                    component.shouldMigrate = EditorGUILayout.Toggle(component.shouldMigrate, GUILayout.Width(20));
                    EditorGUILayout.LabelField($"{component.sceneName}: {component.gameObjectPath}", EditorStyles.boldLabel);
                    EditorGUILayout.EndHorizontal();

                    EditorGUILayout.LabelField($"Component: {component.componentType}");
                    EditorGUILayout.LabelField($"Target: {component.targetComponentType}", EditorStyles.miniLabel);

                    if (!string.IsNullOrEmpty(component.migrationNotes))
                    {
                        EditorGUILayout.LabelField($"Notes: {component.migrationNotes}", EditorStyles.miniLabel);
                    }

                    EditorGUILayout.EndVertical();
                    EditorGUILayout.Space();
                }

                EditorGUILayout.EndScrollView();

                EditorGUILayout.Space();

                showMigrationOptions = EditorGUILayout.Foldout(showMigrationOptions, "Migration Options");
                if (showMigrationOptions)
                {
                    EditorGUILayout.HelpBox(
                        "Migration will:\n" +
                        "• Replace SimpleMusicPlayer with AudioSourceCadancePlayer\n" +
                        "• Replace Koreographer with Cadance\n" +
                        "• Update component references\n" +
                        "• Preserve existing settings where possible",
                        MessageType.Warning);
                }

                EditorGUILayout.Space();

                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button("Migrate Selected Components"))
                {
                    MigrateSelectedComponents();
                }

                if (GUILayout.Button("Export Migration Report"))
                {
                    ExportMigrationReport();
                }
                EditorGUILayout.EndHorizontal();
            }
            else if (scanComplete)
            {
                EditorGUILayout.HelpBox("No Koreographer components found in scanned scenes.", MessageType.Info);
            }
        }

        private void ScanAllScenes()
        {
            foundComponents.Clear();

            // Get all scene paths from build settings
            var scenePaths = new List<string>();

            // Add main scenes
            scenePaths.Add("Assets/_Scenes/Levels/Ouroboros - Base.unity");
            scenePaths.Add("Assets/_Scenes/Levels/Ouroboros - Scene 1.unity");

            // Add any other scenes found in the project
            string[] allScenes = AssetDatabase.FindAssets("t:Scene");
            foreach (string sceneGuid in allScenes)
            {
                string scenePath = AssetDatabase.GUIDToAssetPath(sceneGuid);
                if (scenePath.Contains("_Scenes/Levels/") && !scenePaths.Contains(scenePath))
                {
                    scenePaths.Add(scenePath);
                }
            }

            foreach (string scenePath in scenePaths)
            {
                if (System.IO.File.Exists(scenePath))
                {
                    ScanScene(scenePath);
                }
            }

            scanComplete = true;
            Debug.Log($"Component migration scan complete. Found {foundComponents.Count} components to migrate.");
        }

        private void ScanScene(string scenePath)
        {
            string currentScenePath = EditorSceneManager.GetActiveScene().path;

            try
            {
                var scene = EditorSceneManager.OpenScene(scenePath, OpenSceneMode.Single);
                GameObject[] allObjects = FindObjectsOfType<GameObject>();

                foreach (GameObject go in allObjects)
                {
                    Component[] components = go.GetComponents<Component>();

                    foreach (Component comp in components)
                    {
                        if (comp == null) continue;

                        string typeName = comp.GetType().FullName;
                        (string targetType, string notes)? migrationInfo = GetMigrationInfo(typeName);

                        if (migrationInfo.HasValue)
                        {
                            var info = new ComponentMigrationInfo
                            {
                                sceneName = scene.name,
                                gameObjectName = go.name,
                                gameObjectPath = GetGameObjectPath(go),
                                componentType = typeName,
                                targetComponentType = migrationInfo.Value.targetType,
                                isActive = go.activeInHierarchy,
                                migrationNotes = migrationInfo.Value.notes
                            };

                            foundComponents.Add(info);
                        }
                    }
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Error scanning scene {scenePath}: {e.Message}");
            }
            finally
            {
                if (!string.IsNullOrEmpty(currentScenePath) && currentScenePath != scenePath)
                {
                    EditorSceneManager.OpenScene(currentScenePath, OpenSceneMode.Single);
                }
            }
        }

        private (string targetType, string notes)? GetMigrationInfo(string typeName)
        {
            if (typeName.Contains("SimpleMusicPlayer"))
                return ("AudioSourceCadancePlayer", "Requires CadanceAsset reference");

            if (typeName.Contains("Koreographer") && !typeName.Contains("Handler"))
                return ("Cadance", "Main singleton component");

            if (typeName.Contains("AudioSourceVisor"))
                return ("AudioSourceCadancePlayer", "Audio integration component");

            if (typeName.Contains("KoreographedEventEmitter"))
                return ("CadancedEventEmitter", "Event emission component");

            return null;
        }

        private string GetGameObjectPath(GameObject go)
        {
            string path = go.name;
            Transform parent = go.transform.parent;

            while (parent != null)
            {
                path = parent.name + "/" + path;
                parent = parent.parent;
            }

            return path;
        }

        private void MigrateSelectedComponents()
        {
            var componentsToMigrate = foundComponents.Where(c => c.shouldMigrate).ToList();

            if (componentsToMigrate.Count == 0)
            {
                EditorUtility.DisplayDialog("Migration", "No components selected for migration.", "OK");
                return;
            }

            bool proceed = EditorUtility.DisplayDialog(
                "Confirm Migration",
                $"This will migrate {componentsToMigrate.Count} components. This action cannot be undone.\n\nMake sure you have a backup!",
                "Proceed",
                "Cancel");

            if (!proceed) return;

            Debug.Log($"[CadanceComponentMigrationTool] Starting migration of {componentsToMigrate.Count} components...");

            // Group by scene for efficient processing
            var componentsByScene = componentsToMigrate.GroupBy(c => c.sceneName);

            foreach (var sceneGroup in componentsByScene)
            {
                Debug.Log($"[CadanceComponentMigrationTool] Migrating components in scene: {sceneGroup.Key}");
                MigrateComponentsInScene(sceneGroup.Key, sceneGroup.ToList());
            }

            EditorUtility.DisplayDialog("Migration Complete", $"Successfully migrated {componentsToMigrate.Count} components.", "OK");
        }

        private void MigrateComponentsInScene(string sceneName, List<ComponentMigrationInfo> components)
        {
            string scenePath = $"Assets/_Scenes/Levels/{sceneName}.unity";

            try
            {
                var scene = EditorSceneManager.OpenScene(scenePath, OpenSceneMode.Single);

                foreach (var componentInfo in components)
                {
                    MigrateComponent(componentInfo);
                }

                // Save the scene
                EditorSceneManager.SaveScene(scene);
                Debug.Log($"[CadanceComponentMigrationTool] Scene {sceneName} migration completed and saved");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[CadanceComponentMigrationTool] Error migrating scene {sceneName}: {e.Message}");
            }
        }

        private void MigrateComponent(ComponentMigrationInfo componentInfo)
        {
            // Find the GameObject by path
            GameObject targetObject = GameObject.Find(componentInfo.gameObjectName);
            if (targetObject == null)
            {
                Debug.LogWarning($"[CadanceComponentMigrationTool] GameObject not found: {componentInfo.gameObjectName}");
                return;
            }

            Debug.Log($"[CadanceComponentMigrationTool] Migrating {componentInfo.componentType} on {componentInfo.gameObjectPath}");

            // Note: Actual component replacement would require specific logic for each component type
            // For now, we'll log what would be done
            Debug.Log($"  Would replace {componentInfo.componentType} with {componentInfo.targetComponentType}");
            Debug.Log($"  Notes: {componentInfo.migrationNotes}");
        }

        private void ExportMigrationReport()
        {
            Debug.Log("=== CADANCE COMPONENT MIGRATION REPORT ===");

            var groupedByScene = foundComponents.GroupBy(c => c.sceneName);

            foreach (var sceneGroup in groupedByScene)
            {
                Debug.Log($"\n--- SCENE: {sceneGroup.Key} ---");

                foreach (var component in sceneGroup)
                {
                    string status = component.shouldMigrate ? "MIGRATE" : "SKIP";
                    Debug.Log($"[{status}] {component.gameObjectPath}");
                    Debug.Log($"  From: {component.componentType}");
                    Debug.Log($"  To: {component.targetComponentType}");
                    if (!string.IsNullOrEmpty(component.migrationNotes))
                        Debug.Log($"  Notes: {component.migrationNotes}");
                }
            }

            Debug.Log($"\n=== TOTAL: {foundComponents.Count} COMPONENTS FOUND ===");
        }
    }
}
