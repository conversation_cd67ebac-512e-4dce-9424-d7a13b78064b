%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Radial UV Distortion
  m_Shader: {fileID: 4800000, guid: 0463f65da3df4685bf249c7c10375e22, type: 3}
  m_ValidKeywords: []
  m_InvalidKeywords:
  - _TOGGLESWITCH0_ON
  m_LightmapFlags: 1
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - <noninit>:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BaseTexture:
        m_Texture: {fileID: 2800000, guid: 468c5859e2a9496ea1afdbc5cfce02ee, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BaseTexture1:
        m_Texture: {fileID: 2800000, guid: 21b9f4e0af3140a99ef0bc5a43d58a97, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NoiseMap:
        m_Texture: {fileID: 2800000, guid: 937c2d88ef9b4e889d5a0a6a952c1a4b, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _texcoord:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - <noninit>: 0
    - _AlphaCutoff: 0.5
    - _Displacement: 0.1
    - _EnvironmentReflections: 1
    - _Float0: 5.17
    - _NoiseMapStrength: 0.57
    - _OffsetMultiplier: 0
    - _OffsetMultiplier1: 0.08
    - _QueueControl: 0
    - _QueueOffset: 0
    - _ReceiveShadows: 1
    - _Smoothness: 0.75
    - _Smoothness1: 0
    - _SpecularHighlights: 1
    - _TessPhongStrength: 0.8
    - _TessValue: 32
    - __dirty: 0
    m_Colors:
    - <noninit>: {r: 0, g: 1.849924e-33, b: 0, a: 1.8499122e-33}
    - _EmissionColor: {r: 1, g: 1, b: 1, a: 1}
    - _NoiseMapPannerSpeed: {r: 1.61, g: -0.43, b: 0, a: 0}
    - _NoiseMapSize: {r: 1, g: 1, b: 0, a: 0}
    - _RingPannerSpeed: {r: 0.1, g: -1.27, b: 0, a: 0}
    - _Tint: {r: 1, g: 0.68235296, b: 0, a: 0}
  m_BuildTextureStacks: []
