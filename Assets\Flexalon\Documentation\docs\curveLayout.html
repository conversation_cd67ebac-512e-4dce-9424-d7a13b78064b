<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<link rel="icon" href="../images/favicon.svg" />
		<link rel="icon" href="../images/favicon.ico" />
		<meta name="viewport" content="width=device-width, height=device-height, initial-scale=1">
		<meta property="og:type" content="website">
		<meta http-equiv="content-security-policy" content="">
		<link href="../_app/immutable/assets/_layout.21aea584.css" rel="stylesheet">
		<link href="../_app/immutable/assets/_layout.0154a58a.css" rel="stylesheet">
		
		
		
		
		
		
		
		
		
		
		
		
		<title>Curve Layout</title><!-- HEAD_svelte-i5aht6_START --><meta property="og:title" content="Curve Layout"><meta name="description" property="og:description" content="Flexalon lets Unity developers arrange objects in 3D layouts that are precise and easy to adjust. Use built-in interactions and animations to create powerful experiences."><meta name="image" property="og:image" content="../images/flexalon-1200x630.png"><meta name="twitter:card" content="summary_large_image"><meta name="twitter:title" content="Curve Layout"><meta name="twitter:site" content="@alon_farchy"><meta name="twitter:description" content="Flexalon lets Unity developers arrange objects in 3D layouts that are precise and easy to adjust. Use built-in interactions and animations to create powerful experiences."><meta name="twitter:image" content="../images/flexalon-1200x630.png"><!-- HEAD_svelte-i5aht6_END -->
	</head>
	<body>
		<div>


<div class="text-white font-body p-4 sticky top-0 bg-black flex gap-5 w-full text-gray-200 z-20 border-b-2 border-[#21a6f0]"><button class="lg:hidden"><svg width="24" height="24"><path d="M5 6h14M5 12h14M5 18h14" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"></path></svg></button>
    <a href="https://www.flexalon.com/" class="font-logo upper text-lg text-[#21a6f0] lg:pl-5 svelte-194nj6y">Flexalon Layouts</a></div>

<div class="w-full max-w-[1600px] lg:pl-10"><div class="flex w-full min-h-full text-zinc-400 relative"><div class="h-full fixed bg-black z-10 max-lg:hidden"><div class="flex flex-col w-[300px] gap-2 border-[#21a6f0] border-r-2 bg-black min-h-0 h-full pt-10 pb-36 overflow-auto max-lg:pl-10"><b class="text-zinc-200">Getting Started</b>
                <a href="../docs.html" class="svelte-194nj6y">Installation</a>
                <a href="../docs/coreConcepts.html" class="svelte-194nj6y">Core Concepts</a>
                <a href="../docs/ui.html" class="svelte-194nj6y">Flexalon UI <span class="ml-2 text-xs font-bold text-pink-300">(New in v4.0!)</span></a>
                <a href="../docs/templates.html" class="svelte-194nj6y">Template Pack</a>
                <a href="../docs/challenges.html" class="svelte-194nj6y">Challenges!</a>

                <a class="text-zinc-200 mt-5 font-bold svelte-194nj6y" href="../docs/flexalonObject.html">Flexalon Objects</a>
                <a href="../docs/flexalonObject.html#size" class="svelte-194nj6y">Width, Height, and Depth</a>
                <a href="../docs/flexalonObject.html#minmax" class="svelte-194nj6y">Min / Max Size<span class="ml-2 text-xs font-bold text-purple-400">(v4.1)</span></a>
                <a href="../docs/flexalonObject.html#margins" class="svelte-194nj6y">Margins and Padding</a>
                <a href="../docs/flexalonObject.html#offset" class="svelte-194nj6y">Offset, Scale, and Rotation</a>

                <b class="text-zinc-200 mt-5">Layouts</b>
                <a href="../docs/flexibleLayout.html" class="svelte-194nj6y">Flexible Layout</a>
                <a href="../docs/gridLayout.html" class="svelte-194nj6y">Grid Layout</a>
                <a href="../docs/circleLayout.html" class="svelte-194nj6y">Circle / Spiral Layout</a>
                <a href="../docs/curveLayout.html" class="svelte-194nj6y">Curve Layout</a>
                <a href="../docs/randomLayout.html" class="svelte-194nj6y">Random Layout</a>
                <a href="../docs/alignLayout.html" class="svelte-194nj6y">Align Layout</a>
                <a href="../docs/shapeLayout.html" class="svelte-194nj6y">Shape Layout</a>
                <a href="../docs/constraints.html" class="svelte-194nj6y">Constraints</a>
                <a href="../docs/randomModifier.html" class="svelte-194nj6y">Random Modifier</a>

                <a class="text-zinc-200 mt-5 font-bold svelte-194nj6y" href="../docs/animators.html">Animators</a>
                <a href="../docs/animators.html#curve" class="svelte-194nj6y">Curve Animator</a>
                <a href="../docs/animators.html#lerp" class="svelte-194nj6y">Lerp Animator</a>
                <a href="../docs/animators.html#rb" class="svelte-194nj6y">Rigid Body Animator</a>
                <a href="../docs/animators.html#layouts" class="svelte-194nj6y">Animating Layouts</a>

                <b class="text-zinc-200 mt-5">Click &amp; Drag</b>
                <a href="../docs/interactable.html" class="svelte-194nj6y">Interactable</a>
                <a href="../docs/interactable.html#dragTarget" class="svelte-194nj6y">Drag Target</a>
                <a href="../docs/xr.html" class="svelte-194nj6y">XR Interactions <span class="ml-2 text-xs font-bold text-blue-300">(v3.2)</span></a>
                <a href="../docs/interactable.html#customInputProvider" class="svelte-194nj6y">Custom Input Provider</a>

                <b class="text-zinc-200 mt-5">Data Binding</b>
                <a href="../docs/cloner.html" class="svelte-194nj6y">Cloner</a>
                <a href="../docs/cloner.html#databinding" class="svelte-194nj6y">Data Binding</a>

                <b class="text-zinc-200 mt-5">Advanced</b>
                <a href="../docs/pipeline.html" class="svelte-194nj6y">Flexalon Pipeline</a>
                <a href="../docs/customLayout.html" class="svelte-194nj6y">Custom Layouts</a>
                <a href="../docs/customAnimators.html" class="svelte-194nj6y">Custom Animators</a>
                <a href="../docs/adapters.html" class="svelte-194nj6y">Adapters</a>

                <a class="text-zinc-200 mt-5 font-bold svelte-194nj6y" href="../docs/api/Flexalon.html">Scripting API</a>
                <div class="mb-20"></div></div></div>
        
        <div class="flex flex-col w-full p-10 lg:ml-[300px]"><div class="doc svelte-194nj6y">

<div class="text-white text-2xl mb-10">Curve Layout</div>

<p>Use a curve layout to position children along a bézier curve.</p>
<div class="flex ml-10 m-5 mb-10"><img src="../images/doc-curve.png" alt="Curve layout"></div>

<p class="text-xl mt-10">Editing the Curve</p>

<div class="flex ml-10 m-5 mb-10"><img src="../images/doc-curve-editor.png" alt="Curve layout in editor"></div>

<p>The curve layout initially has 3 points positioned along the X axis. You can add more points by adding items to the &quot;Points&quot; property in the inspector.
    Each point has handles to move the position and tangent line. Drag these handles in the editor to define the shape of your curve.
    You can automatically generate a tangent by choosing the &quot;Match Previous&quot;, &quot;Corner&quot;, or &quot;Smooth&quot; tangent type.
</p>
<p>To reduce visual clutter, you can lock the positions or tangents by checking the corresponding property.
    This will hide the associated handles in the editor.
</p>

<p class="text-xl mt-10">Curve Layout Options</p>
<div class="flex ml-10 m-5 mb-10"><img src="../images/doc-curve-options.png" alt="Curve layout options"></div>

<table><tr><td>Lock Tangents</td>
        <td>Prevents the tangent handles from appearing in the editor.</td></tr>
    <tr><td>Lock Positions</td>
        <td>Prevents the position handles from appearing in the editor.</td></tr>
    <tr><td>Points</td>
        <td><p>Defines the positions and tangents of the points that define the curve.
                The tangent line is defined as an offset from the point.
            </p>
            <p>Tangent Modes:</p>
            <p class="ml-10"><b>Manual</b>: Define the tangent by entering a value or dragging the handle in the scene window.</p>
            <p class="ml-10"><b>Match Previous</b>: Sets the tangent to match the tangent at the previous point.</p>
            <p class="ml-10"><b>Corner</b>: Sets the tangent to zero to create a sharp corner.</p>
            <p class="ml-10"><b>Smooth</b>: Computes a tangent that will create a smooth curve between the previous and next points.</p></td></tr>
    <tr><td>Spacing Type</td>
        <td><p>Determines how the children will be spaced along the curve.</p>
            <p class="ml-10"><b>Fixed</b>: Define the distance between each child with the &quot;Spacing&quot; property.</p>
            <p class="ml-10"><b>Evenly</b>: The first child is placed at the beginning of the curve and the last child is placed at the end of the curve. The rest of the children are placed at even distances between these points along the curve.</p>
            <p class="ml-10"><b>Evenly Connected</b>: If the beginning of the curve is connected to the end of the curve, then the first child is placed at the beginning/end of the curve, and the rest of the children are placed
                at even distances along the curve.</p></td></tr>
    <tr><td>Start At</td>
        <td>Offsets all objects along the curve.
        </td></tr>
    <tr><td>Extend Behavior <span class="ml-2 text-xs font-bold text-pink-400">(v3.0)</span></td>
        <td><p>Determines what should happen after the start/end of the curve.</p>

            <p class="ml-10"><b>Stop</b>: Do not extend the curve. All objects before the beginning are placed at the start, and all objects after the end are placed at the end.</p>
            <p class="ml-10"><b>PingPong</b>: Extend the curve by continuing in the opposite direction.</p>
            <p class="ml-10"><b>ExtendLine</b>: Extend the curve in a straight line based on the tangent at the start/end of the curve.</p>
            <p class="ml-10"><b>Repeat</b>: Extend the curve by repeating the curve.</p>
            <p class="ml-10"><b>RepeatMirror</b>: Extend the curve by mirroring the curve and repeating it.</p></td></tr>
    <tr><td>Rotation</td>
        <td><p>Determines how children should be rotated.</p>

            <p class="ml-10"><b>None</b>: Sets all child rotations to zero.</p>
            <p class="ml-10"><b>In</b>: Each child is rotated to the right of the forward direction of the curve.</p>
            <p class="ml-10"><b>Out</b>: Each child is rotated to the left of the forward direction of the curve.</p>
            <p class="ml-10"><b>In With Roll</b>: Each child is rotated to the right of the forward direction of the curve and rolled so that its X axis matches the curve backward direction.</p>
            <p class="ml-10"><b>Out With Roll</b>: Each child is rotated to the left of the forward direction of the curve and rolled so that its X axis matches the curve forward direction.</p>
            <p class="ml-10"><b>Forward</b>: Each child is rotated to face forward along the curve.</p>
            <p class="ml-10"><b>Backward</b>: Each child is rotated to face backward along the curve.</p></td></tr></table></div></div></div>
</div>


			
			
		</div>
	</body>
</html>
