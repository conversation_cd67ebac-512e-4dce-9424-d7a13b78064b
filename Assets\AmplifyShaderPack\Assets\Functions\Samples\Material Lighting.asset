%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: Material Lighting
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor v1.9.6.2\n// Available at the
    Unity Asset Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=19602\nNode;AmplifyShaderEditor.CommentaryNode;1450;512,-4624;Inherit;False;2928.783;885.2217;GEOMETRIC
    SHADOWING;16;1439;1384;1438;1376;1380;1381;1377;1120;1119;1383;1379;1117;1116;1118;1382;1028;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;1436;512,-5616;Inherit;False;2524.748;974.5503;LightProbe
    Contribution;32;1456;1323;1250;1391;1301;1165;1390;1389;1231;1246;1322;1434;1433;1432;1168;1422;1431;1430;1429;1428;1172;1426;1252;1363;1425;1424;1321;1171;1435;1427;1457;1458;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;1419;513.4961,-7056;Inherit;False;2690.599;1426.769;Reflection
    Probe Contribution;61;1437;1239;1238;1421;1420;1265;1411;1413;1412;1268;1410;1269;1260;1418;1184;1183;1182;1296;1298;1297;1245;1244;1417;1201;1199;1243;1196;1392;1393;1204;1262;1414;1415;1203;1273;1267;1266;1295;1407;1406;1404;1202;1292;1398;1293;1294;1408;1409;1405;1192;1200;1399;1403;1402;1401;1400;1396;1185;1189;1188;1416;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;1028;592,-4336;Inherit;False;949.5003;448.9414;GSF
    Kelemen Modified ;9;1372;1451;1308;1309;1312;1310;1132;1311;1307;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;1033;-1200,-5584;Inherit;False;1579.32;1224.477;Normals;41;1349;1350;1096;1082;1100;1099;1098;1069;1057;1067;1283;1061;1056;975;1062;1044;1053;1045;1075;1074;1288;1289;1290;1055;1048;1046;1077;1072;1081;1079;1054;1052;1051;1050;1049;1047;1034;1452;1453;1454;1455;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;1034;-1168,-5392;Inherit;False;556.0923;208.2122;BlinnPhong
    Half Vector;;0,0,0,1;;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;1036;4384,-3840;Inherit;False;345.6641;333.8774;;;0,0,0,1;--
    LIGHTING INDIRECT SPECULAR$_IndirectSpecularColor$_IndirectSpecular$_IndirectSpecularRoughness$_IndirectSpecularOcclusion$$--
    LIGHTING INDIRECT DIFFUSE$_IndirectDiffuse$$-- LIGHTING SHADOWS$_ShadowStrength$_ShadowIntensity$_ShadowApproxmation$_ShadowSharpness$_ShadowOffset$;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;1037;3664,-3280;Inherit;False;248;104;HDRP;;0,0,0,1;Indirect
    Specular Light node currently not supported on HDRP;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;1382;2000,-4512;Inherit;False;194.6616;122.9033;Shadow
    Color;;0,0,0,1;;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;1416;2144,-5856;Inherit;False;205.832;107.7588;Bug
    #220;;0.8773585,0.0289694,0.0289694,1;Indirect Specular Light Node Breaking in
    URP Forward+$$;0;0\nNode;AmplifyShaderEditor.ScaleAndOffsetNode;1188;1296,-6368;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;10;False;2;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.Exp2OpNode;1189;1520,-6368;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.PowerNode;1185;1664,-6448;Inherit;False;False;2;0;FLOAT;0;False;1;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1396;1456,-6448;Inherit;False;1098;Dot
    NdotH;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;1400;1664,-6560;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;1401;1664,-6656;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.CustomExpressionNode;1402;1424,-6560;Inherit;False;Light
    light = GetMainLight()@$return Color = light.color@;3;Create;1;True;Color;FLOAT3;0,0,0;Out;;Inherit;False;Get
    Main Light Color Node;True;False;0;;False;1;0;FLOAT3;0,0,0;False;2;FLOAT3;0;FLOAT3;1\nNode;AmplifyShaderEditor.LightColorNode;1403;1488,-6688;Inherit;False;0;3;COLOR;0;FLOAT3;1;FLOAT;2\nNode;AmplifyShaderEditor.FunctionSwitchByPipeline;1399;1872,-6560;Inherit;False;4;0;FLOAT3;0,0,0;False;3;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1200;2096,-6512;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;1192;2144,-6496;Inherit;False;3;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1405;2288,-6512;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1409;2464,-6464;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1408;2464,-6464;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1294;1008,-6800;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1293;944,-6752;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1398;1488,-6880;Inherit;False;1397;View
    Dir;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionNode;1292;1712,-6848;Inherit;False;SRP
    Additional Light;-1;;12;6c86746ad131a0a408ca599df5f40861;8,212,0,6,2,9,1,23,0,24,0,142,0,168,0,154,0;6;2;FLOAT3;0,0,0;False;11;FLOAT3;0,0,0;False;15;FLOAT3;0,0,0;False;14;FLOAT3;1,1,1;False;18;FLOAT;0.5;False;32;FLOAT4;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;1202;2336,-6848;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1404;2288,-6752;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1406;2464,-6800;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1407;2464,-6800;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1295;928,-6208;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;1266;1952,-6208;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RangedFloatNode;1267;1664,-6112;Half;False;Property;_IndirectSpecular;Indirect
    Specular ;1;0;Create;False;0;0;0;False;0;False;0.85;0.422;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;1273;1776,-6224;Float;False;Constant;_Float5;Float
    5;18;0;Create;True;0;0;0;False;0;False;1;1;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitchByPipeline;1203;2544,-6496;Inherit;False;4;0;FLOAT3;0,0,0;False;3;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1415;2768,-6224;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1414;2768,-6416;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FresnelNode;1262;1296,-5840;Inherit;False;Standard;WorldNormal;ViewDir;False;False;5;0;FLOAT3;0,0,1;False;4;FLOAT3;0,0,0;False;1;FLOAT;0.04;False;2;FLOAT;1;False;3;FLOAT;5;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SaturateNode;1204;1568,-5840;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;1393;1744,-5840;Inherit;False;Indirect
    Specular Fresnel;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;1392;2992,-6224;Inherit;False;Indirect
    Specular ;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SwizzleNode;1196;1520,-7008;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.ColorNode;1243;1232,-7008;Inherit;False;Property;_IndirectSpecularColor;Indirect
    Specular Color;0;1;[Header];Create;False;1;INDIRECT SPECULAR;0;0;False;1;Space(5);False;1,0.9568627,0.8392157,0;1,1,1,0;True;True;0;6;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT3;5\nNode;AmplifyShaderEditor.WireNode;1199;2080,-6976;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1201;2096,-6928;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1417;1232,-6304;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;1244;928,-6112;Half;False;Property;_IndirectSpecularSmoothness;Indirect
    Specular Smoothness;2;0;Create;False;1;;0;0;False;0;False;0.25;0;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;1245;928,-6032;Half;False;Property;_IndirectSpecularOcclusion;Indirect
    Specular Occlusion;3;0;Create;False;1;;0;0;False;0;False;0.5;0;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1297;1232,-6704;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1298;1264,-6736;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.IndirectSpecularLight;1296;1296,-6176;Inherit;False;World;3;0;FLOAT3;0,0,1;False;1;FLOAT;0.5;False;2;FLOAT;1;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1182;1232,-6128;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1183;1200,-6144;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1184;1232,-5856;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1418;1216,-5792;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WorldNormalVector;1260;736,-6176;Inherit;False;True;1;0;FLOAT3;0,0,1;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.GetLocalVarNode;1269;544,-6176;Inherit;False;1069;Normal;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.Vector3Node;1410;1936,-6000;Inherit;False;Constant;_Vector2;Vector
    2;12;0;Create;True;0;0;0;False;0;False;0,0,0;0,0,0;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.SwitchBySRPVersionNode;1268;2144,-6128;Inherit;False;9;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;3;FLOAT3;0,0,0;False;4;FLOAT3;0,0,0;False;5;FLOAT3;0,0,0;False;6;FLOAT3;0,0,0;False;7;FLOAT3;0,0,0;False;8;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1412;2384,-6176;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1413;2384,-6176;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitchByPipeline;1411;2432,-6208;Inherit;False;4;0;FLOAT3;0,0,0;False;3;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;1265;2832,-6224;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.StickyNoteNode;1420;1664,-6032;Inherit;False;254.7212;100;Indirect
    Specular ;;0,0,0,1;_IndirectSpecular$Range 0-1;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;1421;992,-7008;Inherit;False;212.6338;100;Indirect
    Specular Color;;0,0,0,1;_IndirectSpecularColor;0;0\nNode;AmplifyShaderEditor.SwitchByFaceNode;1238;2768,-6112;Inherit;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1239;2560,-6000;Inherit;False;1240;BaseColor;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.StickyNoteNode;1427;1712,-5376;Inherit;False;252.2357;100;Indirect
    Diffuse;;0,0,0,1;_IndirectDiffuse$Range 0-1;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;1435;1056,-5392;Inherit;False;288.7295;102;Lambert
    Half;;0,0,0,1;Diffuse Lighting is set with Lamber Half vs Lambert to reduce the
    rear of objects looking flat;0;0\nNode;AmplifyShaderEditor.RangedFloatNode;1171;1712,-5456;Float;False;Property;_IndirectDiffuse;Indirect
    Diffuse;4;1;[Header];Create;False;1;INDIRECT DIFFUSE;0;0;False;1;Space(5);False;0.85;0;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.IndirectDiffuseLighting;1321;1712,-5536;Inherit;False;World;1;0;FLOAT3;0,0,1;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;1424;2048,-5536;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;1425;2048,-5264;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;1363;1856,-5264;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.BakedGINode;1252;1600,-5168;Inherit;False;True;5;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT2;0,0;False;3;FLOAT2;0,0;False;4;FLOAT4;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;1426;2048,-5168;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitchByPipeline;1172;2336,-5536;Inherit;False;4;0;FLOAT3;0,0,0;False;3;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1428;2224,-5424;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1429;2256,-5408;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1430;2224,-5280;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1431;2256,-5184;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WorldPosInputsNode;1168;1344,-5168;Inherit;False;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.WorldNormalVector;1432;1344,-5536;Inherit;False;False;1;0;FLOAT3;0,0,1;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.WireNode;1433;1536,-5456;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1434;1536,-5200;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitch;1322;1296,-5024;Inherit;False;Option;False;0;2;0;In
    0;In 1;Instance;1074;10;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.FunctionInput;1246;1056,-4944;Inherit;False;UV1
    Lightmap Static ;2;6;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.StickyNoteNode;1437;1304.373,-6688;Inherit;False;164.094;110.2605;Bug
    #310;;0.8773585,0.0289694,0.0289694,1;URP Unlit breaking with Light Attenuation
    x Light Color$$$;0;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1231;832,-5264;Inherit;False;1069;Normal;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionNode;1389;768,-5184;Inherit;False;Shadow
    Mask;-1;;26;b50f5becdd6b8504a861ba5b9b861159;0;1;3;FLOAT2;0,0;False;1;FLOAT4;0\nNode;AmplifyShaderEditor.FunctionNode;1390;544,-5024;Inherit;False;Lightmap
    UV;-1;;29;1940f027d0458684eb0ad486f669d7d5;1,1,0;0;1;FLOAT2;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1165;1152,-5536;Inherit;False;1069;Normal;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;1118;1904,-4272;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleDivideOpNode;1116;2304,-4272;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0.01;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SaturateNode;1117;2480,-4272;Inherit;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.LerpOp;1379;2688,-4512;Inherit;True;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1383;2928,-4448;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RangedFloatNode;1119;1600,-4208;Half;False;Property;_ShadowOffset;Shadow
    Offset;7;0;Create;False;1;;0;0;False;0;False;0.5;0.295;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;1120;2000,-4192;Half;False;Property;_ShadowSharpness;Shadow
    Sharpness;6;0;Create;False;1;;0;0;False;0;False;0.7;0.426;0.01;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.LerpOp;1377;2992,-4272;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.ColorNode;1381;2256,-4512;Inherit;False;Property;_ShadowColor;Shadow
    Color;9;1;[HDR];Create;True;0;0;0;False;0;False;0.3113208,0.3113208,0.3113208,0;1,1,1,0;True;True;0;6;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT3;5\nNode;AmplifyShaderEditor.SwizzleNode;1380;2512,-4512;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RangedFloatNode;1376;2704,-4192;Inherit;False;Property;_ShadowColorEnable;Enable
    Shadow Color;8;1;[ToggleUI];Create;False;0;0;0;False;0;False;0;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;1438;2704,-4112;Inherit;False;Shadow
    ;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1384;2928,-4272;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;1439;3168,-4272;Inherit;False;Shadow
    Color;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;1307;1024,-4192;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;1311;1024,-4080;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;1132;848,-4080;Inherit;False;3;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1310;816,-4192;Inherit;False;1099;Dot
    NdotV;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;1312;1184,-4192;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1309;1120,-4272;Inherit;False;1100;Dot
    NdotL;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;1308;1312,-4272;Inherit;True;3;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;1451;608,-4000;Inherit;False;Constant;_ApproxSqrt2PI1;_Approx.
    Sqrt(2/PI);0;0;Create;True;0;0;0;False;0;False;0.7978846;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1372;608,-4080;Inherit;False;1138;Shadow
    Strength;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionNode;1301;1056,-5264;Inherit;False;SRP
    Additional Light;-1;;31;6c86746ad131a0a408ca599df5f40861;8,212,0,6,1,9,0,23,1,24,1,142,1,168,1,154,1;6;2;FLOAT3;0,0,0;False;11;FLOAT3;0,0,0;False;15;FLOAT3;0,0,0;False;14;FLOAT3;1,1,1;False;18;FLOAT;0.5;False;32;FLOAT4;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1047;-576,-5152;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1049;-576,-5056;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1051;-576,-5264;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1052;-608,-5504;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.DotProductOpNode;1045;-528,-5536;Inherit;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1056;-912,-5264;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1283;-912,-5104;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;1069;-816,-5008;Inherit;False;Normal;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;1062;-864,-5360;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WorldSpaceLightDirHlpNode;1061;-1152,-5344;Inherit;False;True;1;0;FLOAT;0;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.WireNode;1050;-592,-5472;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WorldNormalVector;1072;-800,-5168;Inherit;False;True;1;0;FLOAT3;0,0,1;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.NormalizeNode;1044;-736,-5360;Inherit;False;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.DotProductOpNode;1046;-528,-5168;Inherit;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.DotProductOpNode;1048;-528,-5056;Inherit;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.DotProductOpNode;1055;-528,-4928;Inherit;False;2;0;FLOAT3;1,1,1;False;1;FLOAT3;0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1054;-592,-4976;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1057;-880,-4880;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1067;-896,-4912;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionInput;975;-1072,-5168;Inherit;False;Normal;3;1;False;1;0;FLOAT3;0,0,1;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMaxOpNode;1452;-368,-5536;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMaxOpNode;1453;-368,-5168;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMaxOpNode;1454;-368,-5056;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMaxOpNode;1455;-368,-4928;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;1075;-240,-5472;Inherit;False;Dot
    VdotH;1;5;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;1077;-240,-5104;Inherit;False;Dot
    NdotH;1;4;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;1079;-240,-4992;Inherit;False;Dot
    NdotV;1;3;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;1081;-240,-4864;Inherit;False;Dot
    NdotL;1;2;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;1288;-80,-5168;Inherit;False;Option;False;0;2;0;In
    0;In 1;Instance;1074;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;1289;-80,-5056;Inherit;False;Option;False;0;2;0;In
    0;In 1;Instance;1074;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;1290;-80,-4928;Inherit;False;Option;False;0;2;0;In
    0;In 1;Instance;1074;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;1074;-80,-5536;Inherit;False;Use
    Custom Inputs;True;0;2;0;In 0;In 1;Object;-1;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;1082;176,-5536;Inherit;False;Dot
    VdotH;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;1098;176,-5168;Inherit;False;Dot
    NdotH;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;1099;176,-5056;Inherit;False;Dot
    NdotV;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;1100;176,-4928;Inherit;False;Dot
    NdotL;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.ViewDirInputsCoordNode;1053;-1072,-5536;Inherit;False;World;True;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.LightColorNode;1096;-784,-4768;Inherit;False;0;3;COLOR;0;FLOAT3;1;FLOAT;2\nNode;AmplifyShaderEditor.SimpleMaxOpNode;1350;-576,-4720;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;1349;-432,-4720;Inherit;False;LightColorIntensity;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.ViewDirInputsCoordNode;1214;-800,-4576;Float;False;World;False;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.RegisterLocalVarNode;1397;-592,-4576;Inherit;False;View
    Dir;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RangedFloatNode;1152;1168,-3472;Half;False;Property;_ShadowStrength;Shadow
    Strength;5;1;[Header];Create;False;1;SHADOWS;0;0;False;0;False;0.85;0.239;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;1138;1472,-3472;Inherit;False;Shadow
    Strength;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;1123;944,-3376;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1440;736,-3376;Inherit;False;1438;Shadow
    ;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;1148;944,-3280;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1233;736,-3296;Inherit;False;1098;Dot
    NdotH;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitchByPipeline;1150;880,-3184;Inherit;False;4;0;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WorldSpaceLightPos;1213;624,-3184;Inherit;False;0;3;FLOAT4;0;FLOAT3;1;FLOAT;2\nNode;AmplifyShaderEditor.GetLocalVarNode;1151;688,-3088;Inherit;False;1100;Dot
    NdotL;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.LerpOp;1109;1472,-3376;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;1149;1120,-3280;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;1108;1296,-3280;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1352;1200,-3184;Inherit;False;1349;LightColorIntensity;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1423;1232,-3104;Inherit;False;1422;Indirect
    Diffuse;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;1112;1472,-3248;Inherit;False;3;3;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;1356;1472,-3120;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitchByPipeline;1355;1664,-3248;Inherit;False;4;0;FLOAT3;0,0,0;False;3;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;1126;1936,-3248;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1374;1840,-3344;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;1375;1872,-3312;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;1128;2096,-3376;Inherit;False;Enable
    Indirect Diffuse;True;1;2;2;In 0;In 1;Object;-1;10;0;FLOAT;0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;1235;2608,-3376;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;1240;2608,-3264;Inherit;False;BaseColor;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionInput;898;2416,-3264;Inherit;False;BaseColor;3;0;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SaturateNode;1210;2896,-3376;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;1236;3168,-3248;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitch;1280;3344,-3296;Inherit;False;Enable
    Indirect Specular;True;1;2;1;In 0;In 1;Object;-1;10;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitchByPipeline;1211;3664,-3440;Inherit;False;4;0;FLOAT3;0,0,0;False;3;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1443;3104,-3328;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1444;3104,-3248;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1442;3280,-3296;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1441;3264,-3344;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1395;2896,-3248;Inherit;False;1392;Indirect
    Specular ;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1394;2864,-3168;Inherit;False;1393;Indirect
    Specular Fresnel;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;1447;3984,-3328;Inherit;False;1439;Shadow
    Color;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1448;4144,-3408;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;1449;4160,-3376;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;1446;4208,-3360;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitch;1445;4368,-3440;Inherit;False;Enable
    Shadow Color;True;1;2;-1;In 0;In 1;Object;-1;10;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionNode;1391;1088,-4864;Inherit;False;Lightmap
    UV;-1;;32;1940f027d0458684eb0ad486f669d7d5;1,1,1;0;1;FLOAT2;0\nNode;AmplifyShaderEditor.FunctionInput;1250;1024,-4784;Inherit;False;UV2 
    Lightmap Dynamic;2;7;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.FunctionSwitch;1323;1296,-4928;Inherit;False;Option;False;0;2;0;In
    0;In 1;Instance;1074;10;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.ScreenPosInputsNode;1456;1360,-4832;Inherit;False;0;False;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.Exposure;1457;2432,-5376;Inherit;False;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;1422;2752,-5536;Inherit;False;Indirect
    Diffuse;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;1458;2576,-5536;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionOutput;894;4624,-3440;Inherit;False;True;-1;BaseColor;0;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nWireConnection;1188;0;1417;0\nWireConnection;1189;0;1188;0\nWireConnection;1185;0;1396;0\nWireConnection;1185;1;1189;0\nWireConnection;1400;0;1396;0\nWireConnection;1400;1;1402;1\nWireConnection;1401;0;1396;0\nWireConnection;1401;1;1403;1\nWireConnection;1399;0;1401;0\nWireConnection;1399;3;1401;0\nWireConnection;1399;1;1400;0\nWireConnection;1399;2;1401;0\nWireConnection;1200;0;1201;0\nWireConnection;1192;0;1200;0\nWireConnection;1192;1;1399;0\nWireConnection;1192;2;1185;0\nWireConnection;1405;0;1192;0\nWireConnection;1409;0;1407;0\nWireConnection;1408;0;1406;0\nWireConnection;1294;0;1293;0\nWireConnection;1293;0;1295;0\nWireConnection;1292;11;1294;0\nWireConnection;1292;15;1398;0\nWireConnection;1292;14;1196;0\nWireConnection;1292;18;1298;0\nWireConnection;1202;0;1292;0\nWireConnection;1202;1;1404;0\nWireConnection;1404;0;1405;0\nWireConnection;1406;0;1202;0\nWireConnection;1407;0;1202;0\nWireConnection;1295;0;1260;0\nWireConnection;1266;0;1273;0\nWireConnection;1266;1;1296;0\nWireConnection;1266;2;1267;0\nWireConnection;1203;0;1192;0\nWireConnection;1203;3;1192;0\nWireConnection;1203;1;1408;0\nWireConnection;1203;2;1409;0\nWireConnection;1415;0;1414;0\nWireConnection;1414;0;1203;0\nWireConnection;1262;0;1184;0\nWireConnection;1262;2;1418;0\nWireConnection;1204;0;1262;0\nWireConnection;1393;0;1204;0\nWireConnection;1392;0;1265;0\nWireConnection;1196;0;1243;0\nWireConnection;1199;0;1196;0\nWireConnection;1201;0;1199;0\nWireConnection;1417;0;1244;0\nWireConnection;1297;0;1244;0\nWireConnection;1298;0;1297;0\nWireConnection;1296;0;1260;0\nWireConnection;1296;1;1244;0\nWireConnection;1296;2;1245;0\nWireConnection;1182;0;1183;0\nWireConnection;1183;0;1260;0\nWireConnection;1184;0;1182;0\nWireConnection;1418;0;1244;0\nWireConnection;1260;0;1269;0\nWireConnection;1268;0;1266;0\nWireConnection;1268;1;1266;0\nWireConnection;1268;2;1266;0\nWireConnection;1268;3;1266;0\nWireConnection;1268;4;1266;0\nWireConnection;1268;5;1410;0\nWireConnection;1268;6;1410;0\nWireConnection;1268;7;1410;0\nWireConnection;1268;8;1410;0\nWireConnection;1412;0;1266;0\nWireConnection;1413;0;1266;0\nWireConnection;1411;0;1266;0\nWireConnection;1411;3;1413;0\nWireConnection;1411;1;1268;0\nWireConnection;1411;2;1412;0\nWireConnection;1265;0;1415;0\nWireConnection;1265;1;1411;0\nWireConnection;1238;1;1239;0\nWireConnection;1321;0;1432;0\nWireConnection;1424;0;1321;0\nWireConnection;1424;1;1171;0\nWireConnection;1425;0;1363;0\nWireConnection;1425;1;1171;0\nWireConnection;1363;0;1301;0\nWireConnection;1363;1;1252;0\nWireConnection;1252;0;1168;0\nWireConnection;1252;1;1434;0\nWireConnection;1252;2;1322;0\nWireConnection;1252;3;1323;0\nWireConnection;1252;4;1456;0\nWireConnection;1426;0;1252;0\nWireConnection;1426;1;1171;0\nWireConnection;1172;0;1424;0\nWireConnection;1172;3;1424;0\nWireConnection;1172;1;1428;0\nWireConnection;1172;2;1429;0\nWireConnection;1428;0;1430;0\nWireConnection;1429;0;1431;0\nWireConnection;1430;0;1425;0\nWireConnection;1431;0;1426;0\nWireConnection;1432;0;1165;0\nWireConnection;1433;0;1432;0\nWireConnection;1434;0;1433;0\nWireConnection;1322;0;1390;0\nWireConnection;1322;1;1246;0\nWireConnection;1389;3;1390;0\nWireConnection;1118;0;1308;0\nWireConnection;1118;1;1119;0\nWireConnection;1116;0;1118;0\nWireConnection;1116;1;1120;0\nWireConnection;1117;0;1116;0\nWireConnection;1379;0;1380;0\nWireConnection;1379;1;1117;0\nWireConnection;1379;2;1117;0\nWireConnection;1383;0;1379;0\nWireConnection;1377;0;1117;0\nWireConnection;1377;1;1384;0\nWireConnection;1377;2;1376;0\nWireConnection;1380;0;1381;0\nWireConnection;1438;0;1117;0\nWireConnection;1384;0;1383;0\nWireConnection;1439;0;1377;0\nWireConnection;1307;0;1310;0\nWireConnection;1307;1;1132;0\nWireConnection;1311;0;1132;0\nWireConnection;1132;0;1372;0\nWireConnection;1132;1;1372;0\nWireConnection;1132;2;1451;0\nWireConnection;1312;0;1307;0\nWireConnection;1312;1;1311;0\nWireConnection;1308;0;1309;0\nWireConnection;1308;1;1312;0\nWireConnection;1308;2;1312;0\nWireConnection;1301;2;1231;0\nWireConnection;1301;32;1389;0\nWireConnection;1047;0;1051;0\nWireConnection;1049;0;1050;0\nWireConnection;1051;0;1044;0\nWireConnection;1052;0;1053;0\nWireConnection;1045;0;1053;0\nWireConnection;1045;1;1044;0\nWireConnection;1056;0;1061;0\nWireConnection;1283;0;975;0\nWireConnection;1069;0;1283;0\nWireConnection;1062;0;1053;0\nWireConnection;1062;1;1061;0\nWireConnection;1050;0;1052;0\nWireConnection;1072;0;975;0\nWireConnection;1044;0;1062;0\nWireConnection;1046;0;1072;0\nWireConnection;1046;1;1047;0\nWireConnection;1048;0;1072;0\nWireConnection;1048;1;1049;0\nWireConnection;1055;0;1054;0\nWireConnection;1055;1;1057;0\nWireConnection;1054;0;1072;0\nWireConnection;1057;0;1067;0\nWireConnection;1067;0;1056;0\nWireConnection;1452;0;1045;0\nWireConnection;1453;0;1046;0\nWireConnection;1454;0;1048;0\nWireConnection;1455;0;1055;0\nWireConnection;1075;0;1452;0\nWireConnection;1077;0;1453;0\nWireConnection;1079;0;1454;0\nWireConnection;1081;0;1455;0\nWireConnection;1288;0;1453;0\nWireConnection;1288;1;1077;0\nWireConnection;1289;0;1454;0\nWireConnection;1289;1;1079;0\nWireConnection;1290;0;1455;0\nWireConnection;1290;1;1081;0\nWireConnection;1074;0;1452;0\nWireConnection;1074;1;1075;0\nWireConnection;1082;0;1074;0\nWireConnection;1098;0;1288;0\nWireConnection;1099;0;1289;0\nWireConnection;1100;0;1290;0\nWireConnection;1350;0;1096;2\nWireConnection;1349;0;1350;0\nWireConnection;1397;0;1214;0\nWireConnection;1138;0;1152;0\nWireConnection;1123;0;1440;0\nWireConnection;1123;1;1233;0\nWireConnection;1148;0;1233;0\nWireConnection;1150;0;1213;2\nWireConnection;1150;3;1213;2\nWireConnection;1150;1;1213;2\nWireConnection;1150;2;1151;0\nWireConnection;1109;0;1123;0\nWireConnection;1109;1;1108;0\nWireConnection;1109;2;1152;0\nWireConnection;1149;0;1148;0\nWireConnection;1149;1;1150;0\nWireConnection;1108;0;1149;0\nWireConnection;1112;0;1423;0\nWireConnection;1112;1;1108;0\nWireConnection;1112;2;1352;0\nWireConnection;1356;0;1423;0\nWireConnection;1356;1;1108;0\nWireConnection;1355;0;1112;0\nWireConnection;1355;3;1112;0\nWireConnection;1355;1;1112;0\nWireConnection;1355;2;1356;0\nWireConnection;1126;0;1355;0\nWireConnection;1126;1;1375;0\nWireConnection;1374;0;1109;0\nWireConnection;1375;0;1374;0\nWireConnection;1128;0;1109;0\nWireConnection;1128;1;1126;0\nWireConnection;1235;0;1128;0\nWireConnection;1235;1;898;0\nWireConnection;1240;0;898;0\nWireConnection;1210;0;1235;0\nWireConnection;1236;0;1444;0\nWireConnection;1236;1;1395;0\nWireConnection;1236;2;1394;0\nWireConnection;1280;0;1442;0\nWireConnection;1280;1;1236;0\nWireConnection;1211;0;1280;0\nWireConnection;1211;3;1280;0\nWireConnection;1211;1;1280;0\nWireConnection;1211;2;1210;0\nWireConnection;1443;0;1210;0\nWireConnection;1444;0;1443;0\nWireConnection;1442;0;1441;0\nWireConnection;1441;0;1210;0\nWireConnection;1448;0;1211;0\nWireConnection;1449;0;1448;0\nWireConnection;1446;0;1449;0\nWireConnection;1446;1;1447;0\nWireConnection;1445;0;1211;0\nWireConnection;1445;1;1446;0\nWireConnection;1323;0;1391;0\nWireConnection;1323;1;1250;0\nWireConnection;1422;0;1458;0\nWireConnection;1458;0;1172;0\nWireConnection;1458;1;1457;0\nWireConnection;894;0;1445;0\nASEEND*/\n//CHKSM=7D19062A54B0DF0BA541075DB2D77BBD2E1AF787"
  m_functionName: 
  m_description: "Amplify Shader Pack Example\n-- ReflectionProbe\n-- LightProbe
    \n-- Shadows\n\n"
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 0
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_customEdited: 0
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 0
  m_headerStyle: 2
  m_headerColor: {r: 0.7529412, g: 0.6784314, b: 0, a: 1}
  m_customNodeCategory: Amplify Shader Pack
  m_previewPosition: 0
  m_hidden: 0
  m_url: 
