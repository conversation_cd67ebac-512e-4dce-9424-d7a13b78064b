%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e78934177bebe0545ab566bb787b0282, type: 3}
  m_Name: Readme
  m_EditorClassIdentifier: 
  Icon: {fileID: 2800000, guid: f6d979b4e53f58f4da7b87cc5244d9a4, type: 3}
  Title: Terrain Simple
  Description:
    Heading: 
    Text: This sample contains a Unity terrain with custom shaders for its rendering.
      For a terrain to be properly used it must contain a First Pass shader for the
      initial 4 splats, an Add Pass for all other groups of 4 splats and a Base pass,
      a simple shader which is used to render the terrain when far away from camera.
      These shaders have little properties visible as most of them are controlled
      directly over the terrain inspector.
    LinkText: 
    Url: 
  PropertiesHeader:
    Heading: First Pass Properties
    Text: 
    LinkText: 
    Url: 
  Properties:
  - Heading: Wetness
    Text: Value that controls terrain smoothness, which itself depends of terrain
      height. Increasing this value will increase smoothness on higher grounds.
    LinkText: 
    Url: 
  - Heading: Metallic
    Text: Overall metallic value.
    LinkText: 
    Url: 
  AdditionalProperties:
  - BlockHeader:
      Heading: Add Pass Properties
      Text: 
      LinkText: 
      Url: 
    BlockContent:
    - Heading: Wetness
      Text: Value that controls terrain smoothness, which itself depends of terrain
        height. Increasing this value will increase smoothness on higher grounds.
      LinkText: 
      Url: 
    - Heading: Metallic
      Text: Overall metallic value.
      LinkText: 
      Url: 
  - BlockHeader:
      Heading: Base Pass Properties
      Text: 
      LinkText: 
      Url: 
    BlockContent:
    - Heading: MainTex
      Text: Texture that will contain color combination of all splats. ( Set automaticaly
        by Unity )
      LinkText: 
      Url: 
    - Heading: MetallicTex
      Text: Texture that will contain metallic combination of all splats. ( Set automaticaly
        by Unity )
      LinkText: 
      Url: 
    - Heading: Color
      Text: Tint color value.
      LinkText: 
      Url: 
  - BlockHeader:
      Heading: Simple Terrain Water Properties
      Text: 
      LinkText: 
      Url: 
    BlockContent:
    - Heading: Max Tessellation
      Text: Fixed tessellation value.
      LinkText: 
      Url: 
    - Heading: Water Normal
      Text: Normals texture for water area.
      LinkText: 
      Url: 
    - Heading: Normal Scale
      Text: Value to control normal scale from sampled normal map.
      LinkText: 
      Url: 
    - Heading: Deep Color
      Text: Color value to be used on deep areas.
      LinkText: 
      Url: 
    - Heading: Shalow Color
      Text: Color value to be used on shalow areas.
      LinkText: 
      Url: 
    - Heading: Water Depth
      Text: Value to tweak final depth value between water and land.
      LinkText: 
      Url: 
    - Heading: Water Falloff
      Text: Value to control falloff between deep and shalow areas.
      LinkText: 
      Url: 
    - Heading: Water Specular
      Text: Overall specular value for water area.
      LinkText: 
      Url: 
    - Heading: Water Smoothness
      Text: Value to control distortion made by water, simulating refracttion.
      LinkText: 
      Url: 
    - Heading: Distortion
      Text: Value to control distortion made by water, simulating refracttion.
      LinkText: 
      Url: 
    - Heading: Foam
      Text: Texture to be applied on foam areas.
      LinkText: 
      Url: 
    - Heading: Foam Depth
      Text: Value to adjust from which depth foam should start to appear.
      LinkText: 
      Url: 
    - Heading: Foam Falloff
      Text: Value to control falloff between water and foam areas.
      LinkText: 
      Url: 
    - Heading: Foam Specular
      Text: Overall specular value for foam area.
      LinkText: 
      Url: 
    - Heading: Foam Smoothness
      Text: Overall smoothness value for foam area.
      LinkText: 
      Url: 
    - Heading: Waves Amplitude
      Text: Value that controls amplitude of animated waves,
      LinkText: 
      Url: 
    - Heading: Waves amount
      Text: Value that controls frequency of animated waves.
      LinkText: 
      Url: 
  AdditionalScripts:
  - BlockHeader:
      Heading: WARNING
      Text: 'Terrain shaders must follow exact construction steps. Please consult
        the available documentation. '
      LinkText: Terrains - ASE Official Wiki
      Url: 'https://wiki.amplify.pt/index.php?title=Unity_Products:Amplify_Shader_Editor/Terrain_Shaders  '
    BlockContent: []
  LoadedLayout: 0
  RPType: 3
