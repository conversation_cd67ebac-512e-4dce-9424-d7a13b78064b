%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!28 &-7337326229449539405
Texture2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: z___Curve_BrightnessTex{"keys":[{"time":0.002209186553955078,"value":0.1129947304725647,"inTangent":-0.5502215623855591,"outTangent":-0.5502215623855591,"inWeight":0.0,"outWeight":0.14915980398654939,"weightedMode":0},{"time":0.50445556640625,"value":0.374114990234375,"inTangent":0.9531375169754028,"outTangent":0.9531375169754028,"inWeight":0.3079046308994293,"outWeight":0.0,"weightedMode":0}]}
  m_ImageContentsHash:
    serializedVersion: 2
    Hash: 00000000000000000000000000000000
  m_IsAlphaChannelOptional: 0
  serializedVersion: 3
  m_Width: 256
  m_Height: 1
  m_CompleteImageSize: 512
  m_MipsStripped: 0
  m_TextureFormat: 15
  m_MipCount: 1
  m_IsReadable: 1
  m_IsPreProcessed: 0
  m_IgnoreMipmapLimit: 0
  m_MipmapLimitGroupName: 
  m_StreamingMipmaps: 0
  m_StreamingMipmapsPriority: 0
  m_VTOnly: 0
  m_AlphaIsTransparency: 0
  m_ImageCount: 1
  m_TextureDimension: 2
  m_TextureSettings:
    serializedVersion: 2
    m_FilterMode: 1
    m_Aniso: 1
    m_MipBias: 0
    m_WrapU: 1
    m_WrapV: 1
    m_WrapW: 1
  m_LightmapFormat: 0
  m_ColorSpace: 0
  m_PlatformBlob: 
  image data: 512
  _typelessdata: 3b2f2c2f0a2fea2ecc2eaf2e942e7b2e632e4d2e382e252e132e032ef52de82ddc2dd32dca2dc32dbd2db92db62db52db52db62db92dbd2dc22dc92dd12dda2de52df02dfd2d0c2e1b2e2b2e3d2e502e642e792e8f2ea72ebf2ed82ef32e0e2f2b2f492f672f872fa72fc92feb2f073019302c303f30523066307a308f30a430b930cf30e530fb3012312a314131593171318a31a331bc31d531ef31093224323e32593274329032ab32c732e332ff321c333933553372339033ad33cb33e833033412342134303440344f345e346e347d348c349c34ab34bb34cb34da34ea34f9340935193528353835483557356735773586359635a535b535c435d435e335f335fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35fc35
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Pixel - Water Reflection
  m_Shader: {fileID: -6465566751694194690, guid: 4d921be9a065a2f4fb95a98a9f258e56, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords: []
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses:
  - MOTIONVECTORS
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BaseMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Render_Texture:
        m_Texture: {fileID: 8600000, guid: 53789c3439d6d464f8fb5cd340959783, type: 2}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - __Curve_Brightness:
        m_Texture: {fileID: -7337326229449539405}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - __Gradient_Color_Map:
        m_Texture: {fileID: 1126506091608851014}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AlphaClip: 0
    - _Amplitude: 0.067
    - _Blend: 0
    - _BumpScale: 1
    - _ClearCoatMask: 0
    - _ClearCoatSmoothness: 0
    - _Cull: 2
    - _Cutoff: 0.5
    - _DepthSensitivity: 0
    - _DetailAlbedoMapScale: 1
    - _DetailNormalMapScale: 1
    - _DstBlend: 0
    - _EdgeThickness: 1
    - _EnvironmentReflections: 1
    - _Frequency: 1
    - _GlossMapScale: 0
    - _Glossiness: 0
    - _GlossyReflections: 0
    - _Height: 0.28
    - _Metallic: 0
    - _NormalsSensitivity: 0
    - _OcclusionStrength: 1
    - _Opacity: 1
    - _Outline_Sample_Point: 0
    - _PREVIEW_LUMINANCE: 0
    - _Parallax: 0.005
    - _QueueControl: 0
    - _QueueOffset: 0
    - _ReceiveShadows: 1
    - _Smoothness: 0.5
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _Speed: 1
    - _SrcBlend: 1
    - _Surface: 0
    - _WorkflowMode: 1
    - _ZWrite: 1
    - __d_2_Distortion: 0
    - __d_3_Distortion: 0
    - __d_5_Outline: 0
    - __f_Distortion: 0
    - __h_Sensitivity: 0
    m_Colors:
    - _BaseColor: {r: 1, g: 1, b: 1, a: 1}
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _SpecColor: {r: 0.19999996, g: 0.19999996, b: 0.19999996, a: 1}
    - __MinMax_Luminance_Range: {r: 0, g: 1, b: 0, a: 0}
  m_BuildTextureStacks: []
  m_AllowLocking: 1
--- !u!114 &1092607693438611411
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0353a89b1f911e48b9e16bdc9f2e058, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 9
--- !u!28 &1126506091608851014
Texture2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: z___Gradient_Color_MapTex{"mode":0,"colorKeys":[{"color":{"r":0.4288003444671631,"g":0.3151032328605652,"b":0.6886792182922363,"a":1.0},"time":0.4725261330604553},{"color":{"r":0.8867924213409424,"g":0.414115309715271,"b":0.429200679063797,"a":1.0},"time":0.7417563199996948}],"alphaKeys":[{"alpha":1.0,"time":0.0},{"alpha":1.0,"time":1.0}]}
  m_ImageContentsHash:
    serializedVersion: 2
    Hash: 00000000000000000000000000000000
  m_IsAlphaChannelOptional: 0
  serializedVersion: 3
  m_Width: 256
  m_Height: 1
  m_CompleteImageSize: 1024
  m_MipsStripped: 0
  m_TextureFormat: 5
  m_MipCount: 1
  m_IsReadable: 1
  m_IsPreProcessed: 0
  m_IgnoreMipmapLimit: 0
  m_MipmapLimitGroupName: 
  m_StreamingMipmaps: 0
  m_StreamingMipmapsPriority: 0
  m_VTOnly: 0
  m_AlphaIsTransparency: 0
  m_ImageCount: 1
  m_TextureDimension: 2
  m_TextureSettings:
    serializedVersion: 2
    m_FilterMode: 1
    m_Aniso: 1
    m_MipBias: 0
    m_WrapU: 1
    m_WrapV: 1
    m_WrapW: 1
  m_LightmapFormat: 0
  m_ColorSpace: 1
  m_PlatformBlob: 
  image data: 1024
  _typelessdata: ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6d50b0ff6e51afff7051aeff7251adff7352acff7552abff7752aaff7853a9ff7a53a8ff7c53a7ff7e54a6ff7f54a5ff8155a5ff8355a4ff8455a3ff8656a2ff8856a1ff8956a0ff8b579fff8d579eff8f589dff90589cff92589bff94599aff955999ff975998ff995a97ff9a5a96ff9c5a95ff9e5b94ffa05b93ffa15c92ffa35c91ffa55c90ffa65d8fffa85d8effaa5d8dffab5e8cffad5e8bffaf5f8bffb15f8affb25f89ffb46088ffb66087ffb76086ffb96185ffbb6184ffbc6183ffbe6282ffc06281ffc26380ffc3637fffc5637effc7647dffc8647cffca647bffcc657affcd6579ffcf6578ffd16677ffd36676ffd46775ffd66774ffd86773ffd96872ffdb6871ffdd6870ffde6970ffe0696fffe26a6effe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6dffe26a6d
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
