%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: NDF Trowbridge Reitz Anisotropic
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor v1.9.5.1\n// Available at the
    Unity Asset Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=19501\nNode;AmplifyShaderEditor.CommentaryNode;349;-2160,-512;Inherit;False;2671.352;920.7775;Math;38;19;354;15;58;16;81;80;20;18;79;59;22;23;25;24;39;38;350;353;351;60;352;61;41;35;0;56;78;36;75;76;33;77;28;217;216;47;87;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;218;-3264.31,-512;Inherit;False;1030.436;800.2011;Dot
    Products;13;341;334;322;336;291;338;337;343;257;266;363;364;365;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;355;-2176,-864;Inherit;False;841.9381;287.7856;Comment;1;52;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;87;-992,-464;Inherit;False;204.1918;132.6709;NdotH;1;346;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;216;-992,-320;Inherit;False;202.2413;129.1683;HdotX;1;347;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;217;-992,-176;Inherit;False;201.5715;133.0102;HdotY;1;348;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;52;-1760,-800;Inherit;False;383;160;Discription;;0,0,0,1;Anisotropic
    NDF functions produce the normal distribution Anisotropically. This allows for
    us to create surface effects that mimic brushed metals and other finely faceted/anisotropic
    surfaces - Jordan Stevens$=;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;47;-2115.857,48;Inherit;False;301.0407;118.553;Anisotropic
    Value Range;;0,0,0,0.5019608;in Jordan Stevens's article, he set his own Ansitropic
    Porperty to a Range of -20 to 1, so try this when creating yours!;0;0\nNode;AmplifyShaderEditor.SimpleDivideOpNode;28;-736,-272;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;77;-736,-432;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleDivideOpNode;33;-736,-128;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;76;-576,-128;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;75;-576,-272;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;36;-352,-432;Inherit;False;3;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;78;-224,-432;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;41;-32,-432;Inherit;False;4;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;61;-96,-336;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;60;-80,-304;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;350;-112,128;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.ScaleNode;38;-928,0;Inherit;False;5;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.ScaleNode;39;-928,96;Inherit;False;5;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMaxOpNode;24;-1056,0;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0.001;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMaxOpNode;25;-1056,96;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0.001;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;23;-1248,96;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleDivideOpNode;22;-1248,0;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;59;-1280,208;Inherit;False;Constant;_NonZero;Non
    Zero;0;0;Create;True;0;0;0;False;0;False;0.001;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;79;-1424,0;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SqrtOpNode;18;-1392,96;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;20;-1584,0;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;80;-1584,96;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;81;-1776,96;Half;False;Constant;_1h;1h;0;0;Create;True;0;0;0;False;0;False;1;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;16;-1776,176;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.StickyNoteNode;354;-1792,-112;Inherit;False;194.8724;102.4255;Smoothness
    ;;0,0,0,1;;0;0\nNode;AmplifyShaderEditor.WireNode;351;-128,32;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;353;-80,112;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;352;-96,16;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.StickyNoteNode;356;-2176,-1056;Inherit;False;598.0645;162.2523;Credits;;0,0,0,1;Method
    by Jordan Stevens$Link: https://www.jordanstevenstechart.com/physically-based-rendering$$Node
    Made by Tiber Legacy$Special Thanks to Dawie;0;0\nNode;AmplifyShaderEditor.GetLocalVarNode;347;-960,-272;Inherit;False;337;HdotX;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;257;-2496,-416;Inherit;True;NdotH;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;343;-2944,112;Inherit;False;HdotY;1;3;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;291;-2784,-416;Inherit;False;Custom
    Dot Products;True;0;2;1;In 0;In 1;Object;-1;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;334;-2784,-192;Inherit;False;Option;False;0;2;1;In
    0;In 1;Instance;291;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;341;-2784,32;Inherit;False;Option;False;0;2;1;In
    0;In 1;Instance;291;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.PiNode;35;-288,-288;Inherit;False;1;0;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.ReciprocalOpNode;56;112,-432;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;58;-1952,256;Half;False;Constant;_09h;0.9h;0;0;Create;True;0;0;0;False;0;False;0.9;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;15;-1952,176;Inherit;False;Anistropic;1;4;False;1;0;FLOAT;0.9;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;348;-960,-128;Inherit;False;338;HdotY;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;336;-2944,-112;Inherit;False;HdotX;1;2;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;346;-960,-416;Inherit;False;257;NdotH;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;337;-2496,-192;Inherit;True;HdotX;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;338;-2496,32;Inherit;True;HdotY;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;19;-1792,0;Inherit;False;Smoothness
    ;1;5;False;1;0;FLOAT;0.5;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionNode;364;-2944,-192;Inherit;False;HdotX;-1;;8;e4a7c169e85b67f408600ac15b2fd149;5,443,0,444,0,121,0,445,0,446,0;5;440;FLOAT3;0,0,0;False;439;FLOAT3;0,0,0;False;130;FLOAT3;0,0,0;False;441;FLOAT3;0,0,0;False;447;FLOAT3;0,0,0;False;1;FLOAT;299\nNode;AmplifyShaderEditor.FunctionInput;266;-2944,-336;Inherit;False;NdotH;1;1;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionNode;363;-3008,-416;Inherit;False;NdotH;-1;;9;fadb6690f179c1448859861f76ebb061;7,27,1,373,0,451,0,463,0,460,0,461,0,452,0;6;230;FLOAT3;0,0,1;False;443;FLOAT3;0,0,0;False;462;FLOAT3;0,0,0;False;454;FLOAT3;0,0,0;False;453;FLOAT3;0,0,0;False;417;FLOAT;1E-37;False;1;FLOAT;109\nNode;AmplifyShaderEditor.FunctionInput;322;-3168,-416;Inherit;False;Normal;3;0;False;1;0;FLOAT3;0,0,1;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionNode;365;-2944,32;Inherit;False;HdotY;-1;;10;1c82aeabcbdbf7041b87a9a5449f37a1;5,453,0,462,0,463,0,454,0,465,0;5;445;FLOAT3;0,0,0;False;464;FLOAT3;0,0,0;False;456;FLOAT3;0,0,0;False;455;FLOAT3;0,0,0;False;466;FLOAT3;0,0,0;False;1;FLOAT;300\nNode;AmplifyShaderEditor.FunctionOutput;0;256,-432;Inherit;True;True;-1;Out;0;True;1;0;FLOAT;0;False;1;FLOAT;0\nWireConnection;28;0;347;0\nWireConnection;28;1;38;0\nWireConnection;77;0;346;0\nWireConnection;77;1;346;0\nWireConnection;33;0;348;0\nWireConnection;33;1;39;0\nWireConnection;76;0;33;0\nWireConnection;76;1;33;0\nWireConnection;75;0;28;0\nWireConnection;75;1;28;0\nWireConnection;36;0;77;0\nWireConnection;36;1;75;0\nWireConnection;36;2;76;0\nWireConnection;78;0;36;0\nWireConnection;78;1;36;0\nWireConnection;41;0;78;0\nWireConnection;41;1;61;0\nWireConnection;41;2;60;0\nWireConnection;41;3;35;0\nWireConnection;61;0;352;0\nWireConnection;60;0;353;0\nWireConnection;350;0;39;0\nWireConnection;38;0;24;0\nWireConnection;39;0;25;0\nWireConnection;24;0;22;0\nWireConnection;24;1;59;0\nWireConnection;25;0;23;0\nWireConnection;25;1;59;0\nWireConnection;23;0;18;0\nWireConnection;23;1;79;0\nWireConnection;22;0;79;0\nWireConnection;22;1;18;0\nWireConnection;79;0;20;0\nWireConnection;79;1;20;0\nWireConnection;18;0;80;0\nWireConnection;20;0;19;0\nWireConnection;80;0;81;0\nWireConnection;80;1;16;0\nWireConnection;16;0;15;0\nWireConnection;16;1;58;0\nWireConnection;351;0;38;0\nWireConnection;353;0;350;0\nWireConnection;352;0;351;0\nWireConnection;257;0;291;0\nWireConnection;291;0;363;109\nWireConnection;291;1;266;0\nWireConnection;334;0;364;299\nWireConnection;334;1;336;0\nWireConnection;341;0;365;300\nWireConnection;341;1;343;0\nWireConnection;56;0;41;0\nWireConnection;337;0;334;0\nWireConnection;338;0;341;0\nWireConnection;363;230;322;0\nWireConnection;0;0;56;0\nASEEND*/\n//CHKSM=F63E7C629B81F3BCFBD3E4EF5246681294DFDA50"
  m_functionName: 
  m_description: 'NDF Trowbridge Reitz Anisotropic Node

'
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 0
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_customEdited: 0
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 0
  m_headerStyle: 2
  m_headerColor: {r: 0.7529412, g: 0.6784314, b: 0, a: 1}
  m_customNodeCategory: Amplify Shader Pack
  m_previewPosition: 0
  m_hidden: 0
  m_url: 
