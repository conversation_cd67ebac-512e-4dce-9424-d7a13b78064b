%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Furniture Green - Color Remap
  m_Shader: {fileID: -6465566751694194690, guid: 15413806d5d5193458fa0600f7dc97b7, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords: []
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses:
  - MOTIONVECTORS
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BaseMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - __Gradient_Color_Map:
        m_Texture: {fileID: 7989558088273959363}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AlphaClip: 0
    - _Blend: 0
    - _BumpScale: 1
    - _ClearCoatMask: 0
    - _ClearCoatSmoothness: 0
    - _Cull: 2
    - _Cutoff: 0.5
    - _DepthSensitivity: 1
    - _DetailAlbedoMapScale: 1
    - _DetailNormalMapScale: 1
    - _DstBlend: 0
    - _EdgeThickness: 1
    - _EnvironmentReflections: 1
    - _GlossMapScale: 0
    - _Glossiness: 0
    - _GlossyReflections: 0
    - _Metallic: 0
    - _NormalsSensitivity: 0
    - _OcclusionStrength: 1
    - _Opacity: 1
    - _Outline_Sample_Point: 0.845
    - _PREVIEW_LUMINANCE: 0
    - _Parallax: 0.005
    - _QueueControl: 0
    - _QueueOffset: 0
    - _ReceiveShadows: 1
    - _Smoothness: 0.5
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _Surface: 0
    - _WorkflowMode: 1
    - _ZWrite: 1
    - __d_5_Outline: 1
    - __f_Outline: 0
    - __h_12_Sensitivity: 0
    - __h_Sensitivity: 0
    m_Colors:
    - _BaseColor: {r: 1, g: 1, b: 1, a: 1}
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _SpecColor: {r: 0.19999996, g: 0.19999996, b: 0.19999996, a: 1}
    - __MinMax_Luminance_Range: {r: 0, g: 1, b: 0, a: 0}
  m_BuildTextureStacks: []
  m_AllowLocking: 1
--- !u!114 &1718471602477201467
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0353a89b1f911e48b9e16bdc9f2e058, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 9
--- !u!28 &7989558088273959363
Texture2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: z___Gradient_Color_MapTex{"mode":1,"colorKeys":[{"color":{"r":0.06666667014360428,"g":0.11764705926179886,"b":0.05098039284348488,"a":1.0},"time":0.20000000298023225},{"color":{"r":0.10196078568696976,"g":0.14901961386203767,"b":0.06666667014360428,"a":1.0},"time":0.4000000059604645},{"color":{"r":0.24705882370471955,"g":0.3490196168422699,"b":0.23137255012989045,"a":1.0},"time":0.6000000238418579},{"color":{"r":0.3294117748737335,"g":0.45098039507865908,"b":0.30980393290519717,"a":1.0},"time":0.800000011920929},{"color":{"r":0.48627451062202456,"g":0.545098066329956,"b":0.4901960790157318,"a":1.0},"time":1.0}],"alphaKeys":[{"alpha":1.0,"time":0.0},{"alpha":1.0,"time":1.0}]}
  m_ImageContentsHash:
    serializedVersion: 2
    Hash: 00000000000000000000000000000000
  m_IsAlphaChannelOptional: 0
  serializedVersion: 3
  m_Width: 256
  m_Height: 1
  m_CompleteImageSize: 1024
  m_MipsStripped: 0
  m_TextureFormat: 5
  m_MipCount: 1
  m_IsReadable: 1
  m_IsPreProcessed: 0
  m_IgnoreMipmapLimit: 0
  m_MipmapLimitGroupName: 
  m_StreamingMipmaps: 0
  m_StreamingMipmapsPriority: 0
  m_VTOnly: 0
  m_AlphaIsTransparency: 0
  m_ImageCount: 1
  m_TextureDimension: 2
  m_TextureSettings:
    serializedVersion: 2
    m_FilterMode: 0
    m_Aniso: 1
    m_MipBias: 0
    m_WrapU: 1
    m_WrapV: 1
    m_WrapW: 1
  m_LightmapFormat: 0
  m_ColorSpace: 1
  m_PlatformBlob: 
  image data: 1024
  _typelessdata: ff111e0dff111e0dff111e0dff111e0dff111e0dff111e0dff111e0dff111e0dff111e0dff111e0dff111e0dff111e0dff111e0dff111e0dff111e0dff111e0dff111e0dff111e0dff111e0dff111e0dff111e0dff111e0dff111e0dff111e0dff111e0dff111e0dff111e0dff111e0dff111e0dff111e0dff111e0dff111e0dff111e0dff111e0dff111e0dff111e0dff111e0dff111e0dff111e0dff111e0dff111e0dff111e0dff111e0dff111e0dff111e0dff111e0dff111e0dff111e0dff111e0dff111e0dff111e0dff111e0dff1a2611ff1a2611ff1a2611ff1a2611ff1a2611ff1a2611ff1a2611ff1a2611ff1a2611ff1a2611ff1a2611ff1a2611ff1a2611ff1a2611ff1a2611ff1a2611ff1a2611ff1a2611ff1a2611ff1a2611ff1a2611ff1a2611ff1a2611ff1a2611ff1a2611ff1a2611ff1a2611ff1a2611ff1a2611ff1a2611ff1a2611ff1a2611ff1a2611ff1a2611ff1a2611ff1a2611ff1a2611ff1a2611ff1a2611ff1a2611ff1a2611ff1a2611ff1a2611ff1a2611ff1a2611ff1a2611ff1a2611ff1a2611ff1a2611ff1a2611ff1a2611ff3f593bff3f593bff3f593bff3f593bff3f593bff3f593bff3f593bff3f593bff3f593bff3f593bff3f593bff3f593bff3f593bff3f593bff3f593bff3f593bff3f593bff3f593bff3f593bff3f593bff3f593bff3f593bff3f593bff3f593bff3f593bff3f593bff3f593bff3f593bff3f593bff3f593bff3f593bff3f593bff3f593bff3f593bff3f593bff3f593bff3f593bff3f593bff3f593bff3f593bff3f593bff3f593bff3f593bff3f593bff3f593bff3f593bff3f593bff3f593bff3f593bff3f593bff3f593bff54734fff54734fff54734fff54734fff54734fff54734fff54734fff54734fff54734fff54734fff54734fff54734fff54734fff54734fff54734fff54734fff54734fff54734fff54734fff54734fff54734fff54734fff54734fff54734fff54734fff54734fff54734fff54734fff54734fff54734fff54734fff54734fff54734fff54734fff54734fff54734fff54734fff54734fff54734fff54734fff54734fff54734fff54734fff54734fff54734fff54734fff54734fff54734fff54734fff54734fff54734fff7c8b7dff7c8b7dff7c8b7dff7c8b7dff7c8b7dff7c8b7dff7c8b7dff7c8b7dff7c8b7dff7c8b7dff7c8b7dff7c8b7dff7c8b7dff7c8b7dff7c8b7dff7c8b7dff7c8b7dff7c8b7dff7c8b7dff7c8b7dff7c8b7dff7c8b7dff7c8b7dff7c8b7dff7c8b7dff7c8b7dff7c8b7dff7c8b7dff7c8b7dff7c8b7dff7c8b7dff7c8b7dff7c8b7dff7c8b7dff7c8b7dff7c8b7dff7c8b7dff7c8b7dff7c8b7dff7c8b7dff7c8b7dff7c8b7dff7c8b7dff7c8b7dff7c8b7dff7c8b7dff7c8b7dff7c8b7dff7c8b7dff7c8b7dff7c8b7d
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
