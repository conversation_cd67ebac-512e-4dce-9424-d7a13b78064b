# BTR Tech Stack

## Core Engine & Framework
- **Unity 6** (Unity 2023.2.3f1+)
- **Universal Render Pipeline (URP)**
- **C# .NET** with modern async/await patterns
- **Unity UI Toolkit** (UXML/USS) for modern UI development

## Key Unity Packages & Systems
- **Unity Input System** for modern input handling
- **Unity Jobs System & Burst Compiler** for performance
- **Unity Visual Effect Graph** for VFX
- **Unity Cinemachine** for camera management
- **Unity Collections** for high-performance data structures

## Audio Systems
- **FMOD Studio** for advanced audio
- **Koreographer** for rhythm/music synchronization (being replaced by Cadance)
- **Custom audio pooling** and management systems

## Third-Party Assets & Tools
- **A* Pathfinding Project** for enemy AI navigation
- **DOTween/PrimeTween** for animations
- **Odin Inspector** for enhanced editor experience
- **Feel (MMFeedbacks)** for game feel and effects
- **UniTask** for zero-allocation async operations
- **ZLogger** for high-performance logging

## Custom Framework (Stylo)
- **Stylo.Epoch**: Time management and chronos integration
- **Stylo.Flux**: Visual effects and datamoshing systems
- **Stylo.MenUI**: UI Toolkit-based menu system
- **Stylo.BFI**: Black Frame Insertion render features
- **Stylo.Cadance**: Rhythm/audio synchronization system

## Development Tools
- **Visual Studio/Rider** for IDE
- **Git** for version control
- **PowerShell** scripts for automation
- **Custom Unity editor tools** for workflow optimization