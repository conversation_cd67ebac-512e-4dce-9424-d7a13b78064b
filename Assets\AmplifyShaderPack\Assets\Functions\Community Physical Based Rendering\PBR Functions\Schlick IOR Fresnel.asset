%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: Schlick IOR Fresnel
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor v1.9.4.4\n// Available at the
    Unity Asset Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=19404\nNode;AmplifyShaderEditor.CommentaryNode;32;-1664,-128;Inherit;False;1533.64;617.9634;Math;15;0;5;10;7;6;19;23;20;1;3;2;9;8;31;167;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;33;-2620.369,-128;Inherit;False;892.8807;386.9156;Dot
    Products;4;149;156;78;171;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;30;-1664,-576;Inherit;False;725.4016;381.3086;Schlick
    IOR Fresnel Node;2;29;27;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;31;-1360,224;Inherit;False;210.5148;148.6939;LdotH;1;163;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;167;-1120,224;Inherit;False;493.4888;237.2242;Schlick
    Fresnel Approx.;3;170;169;168;;0,0,0,0.5019608;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;26;-1664,-768;Inherit;False;566.0713;154.1154;Credits;;0,0,0,0.5019608;Method
    by Jordan Stevens$Link: https://www.jordanstevenstechart.com/physically-based-rendering$$Node
    Made by Tiber Legacy$Special Thanks to Dawie;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;27;-1360,-352;Inherit;False;392;136;Full
    passage from Article;;0,0,0,0.5019608;\"This next algorithm relies on a specific
    value to be passed instead of the specular color. This new value is the Index
    of Refraction. The IOR is a dimensionless number used to describe the speed at
    which light passes through a surface.\";0;0\nNode;AmplifyShaderEditor.StickyNoteNode;29;-1360,-512;Inherit;False;387.6433;136.4648;;;0,0,0,1;\"This
    next algorithm relies on a specific value -. This new value is the Index of Refraction.
    The IOR is a dimensionless number used to describe the speed at which light passes
    through a surface.\" - Jordans Stevens;0;0\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;8;-1264,0;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;9;-1264,96;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.PowerNode;2;-1072,0;Inherit;False;False;2;0;FLOAT;0;False;1;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.PowerNode;3;-1072,96;Inherit;False;False;2;0;FLOAT;0;False;1;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleDivideOpNode;1;-912,0;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;23;-592,64;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;19;-448,0;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;6;-1616,0;Inherit;False;IOR;1;1;False;1;0;FLOAT;1.5;False;1;FLOAT;0\nNode;AmplifyShaderEditor.ClampOpNode;7;-1456,0;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;1;False;2;FLOAT;4;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;10;-1456,-80;Inherit;False;Constant;_One;One;0;0;Create;True;0;0;0;False;0;False;1;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;5;-1264,-80;Inherit;False;Constant;_Two;Two;0;0;Create;True;0;0;0;False;0;False;2;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;163;-1328,272;Inherit;False;156;LdotH;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;78;-2480,48;Inherit;False;LdotH;1;0;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;156;-2032,-32;Inherit;True;LdotH;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;149;-2304,-32;Inherit;False;Custom
    Dot Products;True;0;2;1;In 0;In 1;Object;-1;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;168;-1104,272;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;170;-784,272;Inherit;False;5;5;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SaturateNode;169;-944,272;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;20;-752,64;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionNode;171;-2544,-32;Inherit;False;LdotH;-1;;4;0364555185933b049a6a96d60ccefd1a;4,385,0,460,0,451,0,461,0;4;443;FLOAT3;0,0,0;False;462;FLOAT3;0,0,0;False;454;FLOAT3;0,0,0;False;419;FLOAT;1E-37;False;1;FLOAT;112\nNode;AmplifyShaderEditor.FunctionOutput;0;-336,0;Inherit;True;True;-1;IOR;0;False;1;0;FLOAT;0;False;1;FLOAT;0\nWireConnection;8;0;7;0\nWireConnection;8;1;10;0\nWireConnection;9;0;7;0\nWireConnection;9;1;10;0\nWireConnection;2;0;8;0\nWireConnection;2;1;5;0\nWireConnection;3;0;9;0\nWireConnection;3;1;5;0\nWireConnection;1;0;2;0\nWireConnection;1;1;3;0\nWireConnection;23;0;20;0\nWireConnection;23;1;170;0\nWireConnection;19;0;1;0\nWireConnection;19;1;23;0\nWireConnection;7;0;6;0\nWireConnection;156;0;149;0\nWireConnection;149;0;171;112\nWireConnection;149;1;78;0\nWireConnection;168;0;163;0\nWireConnection;170;0;169;0\nWireConnection;170;1;169;0\nWireConnection;170;2;169;0\nWireConnection;170;3;169;0\nWireConnection;170;4;169;0\nWireConnection;169;0;168;0\nWireConnection;20;0;1;0\nWireConnection;0;0;19;0\nASEEND*/\n//CHKSM=0E78015BB70C72C6C1FB5045D3A04E1DDEE8F3E8"
  m_functionName: 
  m_description: 'Schlick IOR Fresnel Node

'
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 0
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_customEdited: 0
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 0
  m_headerStyle: 2
  m_headerColor: {r: 0.7529412, g: 0.6784314, b: 0, a: 1}
  m_customNodeCategory: Amplify Shader Pack
  m_previewPosition: 0
  m_hidden: 0
  m_url: 
