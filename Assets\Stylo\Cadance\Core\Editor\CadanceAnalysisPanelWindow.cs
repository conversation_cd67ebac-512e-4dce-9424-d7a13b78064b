using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace Stylo.Cadance.Editor
{
    /// <summary>
    /// Audio analysis panel for generating RMS and Spectrum events from audio data.
    /// Provides professional-grade audio analysis tools for Cadance.
    /// </summary>
    public class CadanceAnalysisPanelWindow : EditorWindow
    {
        private CadanceAsset targetCadance;
        private AudioClip targetAudioClip;
        private Vector2 scrollPosition;

        [Header("Analysis Configuration")]
        private bool enableRMSAnalysis = true;
        private bool enableSpectrumAnalysis = true;
        private int fftSize = 1024;
        private FFTWindow windowFunction = FFTWindow.BlackmanHarris;
        private float analysisInterval = 0.1f; // Analysis every 100ms

        [Header("RMS Settings")]
        private string rmsTrackID = "RMS_Analysis";
        private float rmsThreshold = 0.01f;
        private bool generateRMSEvents = true;
        private int rmsWindowSize = 1024;

        [Header("Spectrum Settings")]
        private string spectrumTrackID = "Spectrum_Analysis";
        private int spectrumBins = 256;
        private float spectrumThreshold = 0.001f;
        private bool generateSpectrumEvents = true;

        [Header("Beat Detection")]
        private bool enableBeatDetection = false;
        private string beatTrackID = "Beat_Detection";
        private float beatSensitivity = 1.0f;

        private bool isAnalyzing = false;
        private float analysisProgress = 0f;
        private string analysisStatus = "";

        /// <summary>
        /// Shows the analysis panel window.
        /// </summary>
        /// <param name="cadanceAsset">The target CadanceAsset</param>
        /// <param name="audioClip">The target AudioClip</param>
        public static void ShowWindow(CadanceAsset cadanceAsset, AudioClip audioClip)
        {
            var window = GetWindow<CadanceAnalysisPanelWindow>("Audio Analysis");
            window.targetCadance = cadanceAsset;
            window.targetAudioClip = audioClip;
            window.minSize = new Vector2(400, 600);
        }

        private void OnGUI()
        {
            EditorGUILayout.LabelField("Cadance Audio Analysis", EditorStyles.boldLabel);
            EditorGUILayout.HelpBox("Generate RMS and Spectrum analysis events from audio data. " +
                                   "This provides professional-grade audio analysis capabilities equivalent to Koreographer Professional.",
                                   MessageType.Info);

            EditorGUILayout.Space();

            // Target information
            EditorGUILayout.LabelField("Target Assets", EditorStyles.boldLabel);
            EditorGUI.BeginDisabledGroup(true);
            EditorGUILayout.ObjectField("CadanceAsset", targetCadance, typeof(CadanceAsset), false);
            EditorGUILayout.ObjectField("AudioClip", targetAudioClip, typeof(AudioClip), false);
            EditorGUI.EndDisabledGroup();

            if (targetAudioClip != null)
            {
                EditorGUILayout.LabelField($"Duration: {targetAudioClip.length:F2}s");
                EditorGUILayout.LabelField($"Sample Rate: {targetAudioClip.frequency}Hz");
                EditorGUILayout.LabelField($"Channels: {targetAudioClip.channels}");
            }

            EditorGUILayout.Space();

            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);

            // Analysis Configuration
            EditorGUILayout.LabelField("Analysis Configuration", EditorStyles.boldLabel);

            fftSize = EditorGUILayout.IntPopup("FFT Size", fftSize,
                new string[] { "256", "512", "1024", "2048", "4096" },
                new int[] { 256, 512, 1024, 2048, 4096 });

            windowFunction = (FFTWindow)EditorGUILayout.EnumPopup("Window Function", windowFunction);
            analysisInterval = EditorGUILayout.Slider("Analysis Interval (s)", analysisInterval, 0.01f, 1.0f);

            EditorGUILayout.Space();

            // RMS Analysis Settings
            EditorGUILayout.LabelField("RMS Analysis", EditorStyles.boldLabel);
            enableRMSAnalysis = EditorGUILayout.Toggle("Enable RMS Analysis", enableRMSAnalysis);

            if (enableRMSAnalysis)
            {
                EditorGUI.indentLevel++;
                generateRMSEvents = EditorGUILayout.Toggle("Generate Events", generateRMSEvents);
                rmsTrackID = EditorGUILayout.TextField("Track ID", rmsTrackID);
                rmsThreshold = EditorGUILayout.Slider("RMS Threshold", rmsThreshold, 0.001f, 1.0f);
                rmsWindowSize = EditorGUILayout.IntSlider("Window Size", rmsWindowSize, 256, 4096);
                EditorGUI.indentLevel--;
            }

            EditorGUILayout.Space();

            // Spectrum Analysis Settings
            EditorGUILayout.LabelField("Spectrum Analysis", EditorStyles.boldLabel);
            enableSpectrumAnalysis = EditorGUILayout.Toggle("Enable Spectrum Analysis", enableSpectrumAnalysis);

            if (enableSpectrumAnalysis)
            {
                EditorGUI.indentLevel++;
                generateSpectrumEvents = EditorGUILayout.Toggle("Generate Events", generateSpectrumEvents);
                spectrumTrackID = EditorGUILayout.TextField("Track ID", spectrumTrackID);
                spectrumBins = EditorGUILayout.IntSlider("Spectrum Bins", spectrumBins, 64, 1024);
                spectrumThreshold = EditorGUILayout.Slider("Spectrum Threshold", spectrumThreshold, 0.0001f, 0.1f);
                EditorGUI.indentLevel--;
            }

            EditorGUILayout.Space();

            // Beat Detection Settings
            EditorGUILayout.LabelField("Beat Detection (Experimental)", EditorStyles.boldLabel);
            enableBeatDetection = EditorGUILayout.Toggle("Enable Beat Detection", enableBeatDetection);

            if (enableBeatDetection)
            {
                EditorGUI.indentLevel++;
                beatTrackID = EditorGUILayout.TextField("Beat Track ID", beatTrackID);
                beatSensitivity = EditorGUILayout.Slider("Beat Sensitivity", beatSensitivity, 0.1f, 3.0f);
                EditorGUI.indentLevel--;

                EditorGUILayout.HelpBox("Beat detection is experimental and may require manual adjustment.", MessageType.Warning);
            }

            EditorGUILayout.EndScrollView();

            EditorGUILayout.Space();

            // Analysis Controls
            EditorGUILayout.LabelField("Analysis Controls", EditorStyles.boldLabel);

            EditorGUI.BeginDisabledGroup(isAnalyzing || targetAudioClip == null || targetCadance == null);

            if (GUILayout.Button("Start Analysis", GUILayout.Height(30)))
            {
                StartAnalysis();
            }

            EditorGUI.EndDisabledGroup();

            if (isAnalyzing)
            {
                EditorGUILayout.Space();
                EditorGUILayout.LabelField("Analysis Progress", EditorStyles.boldLabel);
                EditorGUI.ProgressBar(EditorGUILayout.GetControlRect(), analysisProgress, analysisStatus);

                if (GUILayout.Button("Cancel Analysis"))
                {
                    CancelAnalysis();
                }
            }

            EditorGUILayout.Space();

            // Information
            EditorGUILayout.LabelField("Information", EditorStyles.boldLabel);
            EditorGUILayout.HelpBox("This tool analyzes the audio data and generates events with RMS and Spectrum payloads. " +
                                   "The generated events can be used for audio-reactive gameplay, visualizations, and advanced audio analysis workflows.",
                                   MessageType.Info);
        }

        /// <summary>
        /// Starts the audio analysis process.
        /// </summary>
        private void StartAnalysis()
        {
            if (targetAudioClip == null || targetCadance == null)
            {
                EditorUtility.DisplayDialog("Analysis Error", "Please ensure both CadanceAsset and AudioClip are assigned.", "OK");
                return;
            }

            isAnalyzing = true;
            analysisProgress = 0f;
            analysisStatus = "Initializing analysis...";

            // Start analysis in a coroutine-like manner using EditorApplication.update
            EditorApplication.update += UpdateAnalysis;

            Debug.Log("[Cadance Analysis] Starting audio analysis...");
        }

        /// <summary>
        /// Cancels the current analysis.
        /// </summary>
        private void CancelAnalysis()
        {
            isAnalyzing = false;
            EditorApplication.update -= UpdateAnalysis;
            analysisStatus = "Analysis cancelled";
            Debug.Log("[Cadance Analysis] Analysis cancelled by user");
        }

        /// <summary>
        /// Updates the analysis progress (called by EditorApplication.update).
        /// </summary>
        private void UpdateAnalysis()
        {
            if (!isAnalyzing) return;

            // Perform real-time audio analysis in chunks
            PerformRealTimeAnalysis();

            Repaint();
        }

        /// <summary>
        /// Performs real-time audio analysis with professional-grade algorithms.
        /// </summary>
        private void PerformRealTimeAnalysis()
        {
            if (targetAudioClip == null) return;

            try
            {
                // Load audio data
                int sampleCount = targetAudioClip.samples * targetAudioClip.channels;
                float[] audioSamples = new float[sampleCount];

                if (!targetAudioClip.GetData(audioSamples, 0))
                {
                    Debug.LogError("[Cadance Analysis] Failed to load audio data");
                    CancelAnalysis();
                    return;
                }

                // Process audio in chunks for better performance
                float duration = targetAudioClip.length;
                int sampleRate = targetAudioClip.frequency;
                int channels = targetAudioClip.channels;

                float timeStep = analysisInterval;
                int totalSteps = Mathf.RoundToInt(duration / timeStep);
                int currentStep = Mathf.RoundToInt(analysisProgress * totalSteps);

                if (currentStep < totalSteps)
                {
                    float currentTime = currentStep * timeStep;
                    int startSample = Mathf.RoundToInt(currentTime * sampleRate) * channels;
                    int windowSamples = Mathf.RoundToInt(timeStep * sampleRate) * channels;

                    // Ensure we don't exceed array bounds
                    windowSamples = Mathf.Min(windowSamples, audioSamples.Length - startSample);

                    if (windowSamples > 0)
                    {
                        // Extract window of audio data
                        float[] window = new float[windowSamples];
                        System.Array.Copy(audioSamples, startSample, window, 0, windowSamples);

                        // Perform analysis on this window
                        AnalyzeAudioWindow(window, currentTime, sampleRate, channels);
                    }

                    analysisProgress = (float)(currentStep + 1) / totalSteps;
                    analysisStatus = $"Analyzing audio... {(analysisProgress * 100):F0}%";
                }
                else
                {
                    CompleteAnalysis();
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[Cadance Analysis] Analysis error: {ex.Message}");
                CancelAnalysis();
            }
        }

        /// <summary>
        /// Analyzes a window of audio data with professional algorithms.
        /// </summary>
        private void AnalyzeAudioWindow(float[] window, float timePosition, int sampleRate, int channels)
        {
            // RMS Analysis
            if (enableRMSAnalysis)
            {
                float rmsValue = CalculateRMS(window, channels);
                if (rmsValue >= rmsThreshold)
                {
                    GenerateRMSEvent(timePosition, rmsValue, sampleRate);
                }
            }

            // FFT Spectrum Analysis
            if (enableSpectrumAnalysis)
            {
                float[] spectrumData = PerformFFTAnalysis(window, channels);
                if (HasSignificantSpectralContent(spectrumData))
                {
                    GenerateSpectrumEvent(timePosition, spectrumData, sampleRate);
                }
            }

            // Beat Detection
            if (enableBeatDetection)
            {
                bool beatDetected = DetectBeat(window, timePosition, channels);
                if (beatDetected)
                {
                    GenerateBeatEvent(timePosition, sampleRate);
                }
            }
        }

        /// <summary>
        /// Calculates RMS (Root Mean Square) energy of audio window.
        /// </summary>
        private float CalculateRMS(float[] window, int channels)
        {
            float sum = 0f;
            int sampleCount = 0;

            for (int i = 0; i < window.Length; i += channels)
            {
                // Average all channels for this sample
                float sample = 0f;
                for (int ch = 0; ch < channels && i + ch < window.Length; ch++)
                {
                    sample += window[i + ch];
                }
                sample /= channels;

                sum += sample * sample;
                sampleCount++;
            }

            return sampleCount > 0 ? Mathf.Sqrt(sum / sampleCount) : 0f;
        }

        /// <summary>
        /// Performs FFT analysis on audio window.
        /// </summary>
        private float[] PerformFFTAnalysis(float[] window, int channels)
        {
            // Convert to mono if needed
            float[] monoData = new float[window.Length / channels];
            for (int i = 0; i < monoData.Length; i++)
            {
                float sample = 0f;
                for (int ch = 0; ch < channels; ch++)
                {
                    int index = i * channels + ch;
                    if (index < window.Length)
                        sample += window[index];
                }
                monoData[i] = sample / channels;
            }

            // Pad or truncate to FFT size
            float[] fftInput = new float[fftSize];
            int copyLength = Mathf.Min(fftSize, monoData.Length);
            System.Array.Copy(monoData, fftInput, copyLength);

            // Apply window function
            ApplyWindowFunction(fftInput, windowFunction);

            // Perform FFT (simplified - in real implementation use proper FFT library)
            float[] spectrum = new float[spectrumBins];
            for (int i = 0; i < spectrumBins && i < fftSize / 2; i++)
            {
                // Simplified magnitude calculation
                float real = fftInput[i * 2];
                float imag = i * 2 + 1 < fftInput.Length ? fftInput[i * 2 + 1] : 0f;
                spectrum[i] = Mathf.Sqrt(real * real + imag * imag);
            }

            return spectrum;
        }

        /// <summary>
        /// Applies window function to FFT input data.
        /// </summary>
        private void ApplyWindowFunction(float[] data, FFTWindow window)
        {
            int length = data.Length;

            switch (window)
            {
                case FFTWindow.Rectangular:
                    // No windowing
                    break;

                case FFTWindow.Triangle:
                    for (int i = 0; i < length; i++)
                    {
                        float w = 1f - Mathf.Abs((2f * i - length + 1) / length);
                        data[i] *= w;
                    }
                    break;

                case FFTWindow.Hamming:
                    for (int i = 0; i < length; i++)
                    {
                        float w = 0.54f - 0.46f * Mathf.Cos(2f * Mathf.PI * i / (length - 1));
                        data[i] *= w;
                    }
                    break;

                case FFTWindow.Hanning:
                    for (int i = 0; i < length; i++)
                    {
                        float w = 0.5f * (1f - Mathf.Cos(2f * Mathf.PI * i / (length - 1)));
                        data[i] *= w;
                    }
                    break;

                case FFTWindow.Blackman:
                case FFTWindow.BlackmanHarris:
                    for (int i = 0; i < length; i++)
                    {
                        float w = 0.42f - 0.5f * Mathf.Cos(2f * Mathf.PI * i / (length - 1)) +
                                 0.08f * Mathf.Cos(4f * Mathf.PI * i / (length - 1));
                        data[i] *= w;
                    }
                    break;
            }
        }

        /// <summary>
        /// Checks if spectrum data has significant content above threshold.
        /// </summary>
        private bool HasSignificantSpectralContent(float[] spectrum)
        {
            float maxValue = 0f;
            for (int i = 0; i < spectrum.Length; i++)
            {
                if (spectrum[i] > maxValue)
                    maxValue = spectrum[i];
            }
            return maxValue >= spectrumThreshold;
        }

        /// <summary>
        /// Simple beat detection algorithm based on energy changes.
        /// </summary>
        private bool DetectBeat(float[] window, float timePosition, int channels)
        {
            float energy = CalculateRMS(window, channels);

            // Simple threshold-based beat detection
            // In a professional implementation, this would use more sophisticated algorithms
            // like spectral flux, onset detection, or machine learning approaches

            return energy >= beatSensitivity * 0.1f; // Simplified threshold
        }

        /// <summary>
        /// Generates an RMS event and adds it to the appropriate track.
        /// </summary>
        private void GenerateRMSEvent(float timePosition, float rmsValue, int sampleRate)
        {
            var rmsTrack = GetOrCreateTrack(rmsTrackID);
            int sampleTime = Mathf.RoundToInt(timePosition * sampleRate);

            var rmsInfo = new RMSInfo(sampleRate, rmsWindowSize);
            var rmsPayload = new RMSPayload(rmsValue, rmsValue * 1.2f, rmsInfo);
            var rmsEvent = new CadanceEvent(rmsTrackID, sampleTime, rmsPayload);

            rmsTrack.AddEvent(rmsEvent);
        }

        /// <summary>
        /// Generates a spectrum event and adds it to the appropriate track.
        /// </summary>
        private void GenerateSpectrumEvent(float timePosition, float[] spectrumData, int sampleRate)
        {
            var spectrumTrack = GetOrCreateTrack(spectrumTrackID);
            int sampleTime = Mathf.RoundToInt(timePosition * sampleRate);

            var spectrumInfo = new SpectrumInfo(sampleRate, fftSize, windowFunction);
            var spectrumPayload = new SpectrumPayload(spectrumData, spectrumInfo);
            var spectrumEvent = new CadanceEvent(spectrumTrackID, sampleTime, spectrumPayload);

            spectrumTrack.AddEvent(spectrumEvent);
        }

        /// <summary>
        /// Generates a beat event and adds it to the appropriate track.
        /// </summary>
        private void GenerateBeatEvent(float timePosition, int sampleRate)
        {
            var beatTrack = GetOrCreateTrack(beatTrackID);
            int sampleTime = Mathf.RoundToInt(timePosition * sampleRate);

            var beatPayload = new FloatPayload(beatSensitivity);
            var beatEvent = new CadanceEvent(beatTrackID, sampleTime, beatPayload);

            beatTrack.AddEvent(beatEvent);
        }

        /// <summary>
        /// Gets or creates a track with the specified ID.
        /// </summary>
        private CadanceTrackBase GetOrCreateTrack(string trackID)
        {
            var track = targetCadance.GetTrackByEventID(trackID);
            if (track == null)
            {
                track = CadanceTrack.CreateTrack(trackID);
                targetCadance.Tracks.Add(track);
            }
            return track;
        }

        /// <summary>
        /// Completes the analysis and generates events.
        /// </summary>
        private void CompleteAnalysis()
        {
            isAnalyzing = false;
            EditorApplication.update -= UpdateAnalysis;
            analysisProgress = 1.0f;
            analysisStatus = "Analysis complete!";

            // Generate analysis events
            int eventsGenerated = GenerateAnalysisEvents();

            // Mark the CadanceAsset as dirty
            EditorUtility.SetDirty(targetCadance);

            Debug.Log($"[Cadance Analysis] Analysis complete. Generated {eventsGenerated} events.");

            EditorUtility.DisplayDialog("Analysis Complete",
                $"Audio analysis completed successfully!\n\nGenerated {eventsGenerated} analysis events.",
                "OK");
        }

        /// <summary>
        /// Generates analysis events based on the configuration.
        /// </summary>
        /// <returns>The number of events generated</returns>
        private int GenerateAnalysisEvents()
        {
            int eventsGenerated = 0;

            // This is a simplified implementation - in a real system, you would:
            // 1. Load the audio data from the AudioClip
            // 2. Perform FFT analysis at regular intervals
            // 3. Calculate RMS values
            // 4. Generate events with appropriate payloads
            // 5. Add events to the CadanceAsset tracks

            float duration = targetAudioClip.length;
            int sampleRate = targetAudioClip.frequency;
            float timeStep = analysisInterval;

            // Create tracks if they don't exist
            if (enableRMSAnalysis && generateRMSEvents)
            {
                var rmsTrack = targetCadance.GetTrackByEventID(rmsTrackID);
                if (rmsTrack == null)
                {
                    rmsTrack = CadanceTrack.CreateTrack(rmsTrackID);
                    targetCadance.Tracks.Add(rmsTrack);
                }

                // Generate RMS events (simplified)
                for (float time = 0; time < duration; time += timeStep)
                {
                    int sampleTime = Mathf.RoundToInt(time * sampleRate);

                    // Simulate RMS calculation (in real implementation, analyze actual audio data)
                    float rmsValue = Random.Range(0f, 1f) * 0.5f; // Placeholder

                    if (rmsValue >= rmsThreshold)
                    {
                        var rmsInfo = new RMSInfo(sampleRate, 1024);
                        var rmsPayload = new RMSPayload(rmsValue, rmsValue * 1.2f, rmsInfo);
                        var rmsEvent = new CadanceEvent(rmsTrackID, sampleTime, rmsPayload);

                        rmsTrack.AddEvent(rmsEvent);
                        eventsGenerated++;
                    }
                }
            }

            if (enableSpectrumAnalysis && generateSpectrumEvents)
            {
                var spectrumTrack = targetCadance.GetTrackByEventID(spectrumTrackID);
                if (spectrumTrack == null)
                {
                    spectrumTrack = CadanceTrack.CreateTrack(spectrumTrackID);
                    targetCadance.Tracks.Add(spectrumTrack);
                }

                // Generate Spectrum events (simplified)
                for (float time = 0; time < duration; time += timeStep)
                {
                    int sampleTime = Mathf.RoundToInt(time * sampleRate);

                    // Simulate spectrum data (in real implementation, perform FFT)
                    float[] spectrumData = new float[spectrumBins];
                    for (int i = 0; i < spectrumBins; i++)
                    {
                        spectrumData[i] = Random.Range(0f, 1f) * 0.1f; // Placeholder
                    }

                    var spectrumInfo = new SpectrumInfo(sampleRate, fftSize, windowFunction);
                    var spectrumPayload = new SpectrumPayload(spectrumData, spectrumInfo);
                    var spectrumEvent = new CadanceEvent(spectrumTrackID, sampleTime, spectrumPayload);

                    spectrumTrack.AddEvent(spectrumEvent);
                    eventsGenerated++;
                }
            }

            return eventsGenerated;
        }

        private void OnDestroy()
        {
            // Clean up if window is closed during analysis
            if (isAnalyzing)
            {
                EditorApplication.update -= UpdateAnalysis;
            }
        }
    }
}
