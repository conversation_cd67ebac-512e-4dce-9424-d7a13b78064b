%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Default Particle (Additive)
  m_Shader: {fileID: 4800000, guid: 88e9264ba3f8ff64fbbcb07a4957e5d3, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords:
  - _TRANSPARENT_ON
  m_InvalidKeywords:
  - _ReceiveFogEnabled
  - _ReceiveShadowsEnabled
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 1
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses:
  - MOTIONVECTORS
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BaseMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _HeightMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _HeightMap2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetalnessMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetalnessMap2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalMap2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _RoughnessMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _RoughnessMap2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SubsurfaceThicknessMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AddPrecomputedVelocity: 0
    - _AlphaClip: 0
    - _AlphaClipEnabled: 0
    - _AlphaToMask: 0
    - _AmplitudeSpeed: 1
    - _Blend: 0
    - _BlendModePreserveSpecular: 1
    - _BumpScale: 1
    - _Cull: 2
    - _Culling: 2
    - _Cutoff: 0.5
    - _DetailNormalMapScale: 1
    - _DstBlend: 0
    - _DstBlendAlpha: 0
    - _EmissionIntensity: 1
    - _ExcludeFromJPG: 0
    - _FresnelBias: 0
    - _FresnelIntensity: 4
    - _FresnelPower: 4
    - _GlossMapScale: 1
    - _Glossiness: 0.5
    - _GlossinessSource: 0
    - _GlossyReflections: 1
    - _HasEmissionMap: 0
    - _HasEmissionMap2: 0
    - _HasHeightMap: 0
    - _HasHeightMap2: 0
    - _HasMetalnessMap: 0
    - _HasMetalnessMap2: 0
    - _HasNormalMap: 0
    - _HasNormalMap2: 0
    - _HasRoughnessMap: 0
    - _HasRoughnessMap2: 0
    - _HasSubsurfaceMap: 0
    - _HeightStrength: 0.02
    - _HeightStrength2: 0.02
    - _InvFade: 1
    - _Invert: 0
    - _MaxValueAmplitude: 2
    - _Metallic: 0
    - _Metalness: 0
    - _Metalness2: 0
    - _MetalnessMapExposure: 0
    - _MetalnessMapExposure2: 0
    - _MinValueAmplitude: 1
    - _Mode: 0
    - _NormalStrength: 1
    - _NormalStrength2: 1
    - _OcclusionStrength: 1
    - _OcclusionStrength2: 1
    - _Opacity: 0.841
    - _Parallax: 0.02
    - _QueueOffset: 0
    - _ReceiveFogEnabled: 1
    - _ReceiveShadows: 1
    - _ReceiveShadowsEnabled: 1
    - _Roughness: 0.5
    - _Roughness2: 0.5
    - _RoughnessMapExposure: 0
    - _RoughnessMapExposure2: 0
    - _SelfIllumination: 7.17
    - _Shininess: 0
    - _Smoothness: 0.5
    - _SmoothnessSource: 0
    - _SmoothnessTextureChannel: 0
    - _SortPriority: 0
    - _SpecSource: 0
    - _SpecularHighlights: 1
    - _Specularity: 0.5
    - _Specularity2: 0.5
    - _SrcBlend: 1
    - _SrcBlendAlpha: 1
    - _SubsurfaceAmbient: 0.2
    - _SubsurfaceDistortion: 0.3
    - _SubsurfaceEnabled: 0
    - _SubsurfaceFalloff: 2
    - _SubsurfaceThickness: 0.8
    - _Surface: 0
    - _TransparentMode: 1
    - _UVSec: 0
    - _UseVertexColors: 0
    - _ZTest: 4
    - _ZWrite: 1
    m_Colors:
    - _BaseColor: {r: 1, g: 1, b: 1, a: 1}
    - _BaseColor2: {r: 0.5, g: 0.5, b: 0.5, a: 1}
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _Emission: {r: 0, g: 0, b: 0, a: 1}
    - _Emission2: {r: 0, g: 0, b: 0, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _FresnelColor: {r: 35.62523, g: 51.37788, b: 50.424065, a: 1}
    - _SpecColor: {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
    - _SubsurfaceColor: {r: 1, g: 1, b: 1, a: 1}
    - _TintColor: {r: 1, g: 1, b: 1, a: 1}
  m_BuildTextureStacks: []
  m_AllowLocking: 1
