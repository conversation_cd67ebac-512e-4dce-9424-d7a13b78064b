# BFI Troubleshooting Guide

## No Effect Visible / Debug Visualization Not Showing

### Step 1: Use the Debug Tools

1. **Open BFI Debug Tool**: `<PERSON>ylo → BFI → Debug Tool`
2. **Run Quick Test Setup**: `Stylo → BFI → Quick Test Setup`
3. **Check Console**: Look for BFI debug messages

### Step 2: Verify Basic Setup

#### Check URP Configuration
- Ensure URP is set as the render pipeline in Graphics Settings
- Verify you're using Unity 6.0+

#### Check BFI Renderer Feature
1. Open `Assets/BT Render Pipeline/BT Render 1.asset`
2. Look for "BFI Renderer Feature" in the Renderer Features list
3. Ensure it's **Active** (checkbox checked)
4. If missing, run `<PERSON>ylo → BFI → Add to Current Renderer`

#### Check Volume Setup
1. Ensure you have a **Global Volume** in your scene
2. Volume must have a **Volume Profile** assigned
3. Volume Profile must have **BFI Effect** added
4. BFI Effect parameters must have **Override** checkboxes enabled

### Step 3: Console Debug Messages

Look for these messages in the Console:

#### Good Messages (BFI Working):
```
BFI: AddRenderPasses called
BFI: Shader found, setting up render pass
BFI: Render pass enqueued
BFI: Effect is active - Mode: DoubleFrameRate, Intensity: 1, Debug: True
```

#### Problem Messages:
```
BFI: Volume component is null
→ No Volume Profile with BFI Effect in scene

BFI: Effect not active - Mode: Off, Intensity: 0
→ BFI Effect not properly configured

BFI shader was not found
→ Shader compilation issue
```

### Step 4: Manual Verification Steps

#### Force BFI Active
1. Run `Stylo → BFI → Test - Force BFI Active`
2. Check console for confirmation
3. Enter Play Mode and look for debug visualization

#### Create Test Scene
1. Run `Stylo → BFI → Quick Test Setup`
2. This creates everything needed for testing
3. Enter Play Mode
4. Look for timing bars at bottom of screen

### Step 5: Common Issues and Fixes

#### Issue: "BFI: AddRenderPasses called" not appearing
**Problem**: BFI Renderer Feature not added or not active
**Fix**: 
- Run `Stylo → BFI → Add to Current Renderer`
- Check BT Render 1.asset manually

#### Issue: "Volume component is null"
**Problem**: No Volume Profile with BFI Effect
**Fix**:
- Run `Stylo → BFI → Create BFI Volume Profile`
- Add Global Volume to scene with the profile

#### Issue: "Effect not active"
**Problem**: BFI parameters not configured
**Fix**:
- Set Mode to "2x Frame Rate" (not "Off")
- Set Intensity to 1.0
- Enable Override checkboxes
- Run `Stylo → BFI → Test - Force BFI Active`

#### Issue: "Shader not found"
**Problem**: Shader compilation failed
**Fix**:
- Check Console for shader errors
- Run `Stylo → BFI → Debug Tool` → "Force Shader Recompile"
- Check `Assets/Stylo/BFI/Shaders/URP_BFI.shader`

### Step 6: Debug Visualization Details

When working correctly, you should see:
- **Timing bars** at the bottom of the screen
- **Color gradients** showing BFI phase
- **Motion visualization** (blue areas for moving objects)

If debug visualization is enabled but not visible:
1. Check camera is not Scene View camera
2. Verify BFI Effect has Debug Visualization = True
3. Ensure Override checkbox is enabled

### Step 7: Advanced Debugging

#### Check Render Graph Execution
Look for these messages during Play Mode:
```
BFI: Effect is active - Mode: DoubleFrameRate...
```

If this appears but no visual effect:
- Check render pass event timing
- Verify texture handling in render graph
- Check for conflicts with other effects

#### Check Volume Stack
In Play Mode, verify:
```csharp
var stack = VolumeManager.instance.stack;
var bfiEffect = stack.GetComponent<BFIEffect>();
Debug.Log($"BFI in stack: {bfiEffect != null}");
Debug.Log($"BFI active: {bfiEffect?.IsActive()}");
```

### Step 8: Performance Considerations

#### Frame Rate Requirements
- BFI works best with stable frame rates
- 2x mode needs consistent 60+ FPS for 120Hz effect
- Monitor your frame timing

#### Display Compatibility
- 60Hz displays: Limited BFI benefit
- 120Hz+ displays: Full BFI effectiveness
- Check your display refresh rate

### Step 9: Integration with Other Effects

#### Flux Compatibility
- BFI should work alongside Flux
- Check render pass order if conflicts occur
- Both use BeforeRenderingPostProcessing timing

#### Other Post-Processing
- BFI runs before standard post-processing
- Should be compatible with URP post-processing stack
- Check for render target conflicts

### Step 10: Last Resort Fixes

#### Complete Reset
1. Delete `Assets/Stylo/BFI/BFI_QuickTest_Profile.asset`
2. Run `Stylo → BFI → Quick Test Setup`
3. Enter Play Mode immediately
4. Check console for all debug messages

#### Manual Setup Verification
1. Open BT Render 1.asset
2. Manually add BFI Renderer Feature if missing
3. Create new Volume Profile from scratch
4. Add BFI Effect with these exact settings:
   - Mode: 2x Frame Rate (Override: ✓)
   - Intensity: 1.0 (Override: ✓)
   - Debug Visualization: True (Override: ✓)

#### Shader Recompilation
1. Delete `Library/ShaderCache`
2. Restart Unity
3. Let all shaders recompile
4. Test again

## Still Not Working?

If BFI still doesn't work after following this guide:

1. **Check Unity Console** for any error messages
2. **Run Debug Tool** and copy all output
3. **Verify Unity 6** and URP versions
4. **Test with minimal scene** (just camera + volume)
5. **Check Graphics Settings** for URP asset assignment

The debug tools should reveal exactly what's preventing BFI from working.
