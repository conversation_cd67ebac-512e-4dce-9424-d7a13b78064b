using System.Reflection;
using Newtonsoft.Json;
using UnityEditor;
using UnityEngine;

namespace ExtendedImportSettings
{
    public class ExtendedImportPostprocessor : AssetPostprocessor
    {
        private void OnPostprocessModel(GameObject gameObject)
        {
            var moduleInfos = ExtendedImportUtilities.GetFields(typeof(ExtendedImportModuleList));
            for (var i = 0; i < moduleInfos.Length; i++)
            {
                if (!TryLoadDataByType(moduleInfos[i], assetImporter, out var data))
                    continue;
                
                if (data.IsActive)
                    data.OnPostprocessModel(gameObject);
            }
        }
        
        private static bool TryLoadDataByType(FieldInfo moduleInfo, AssetImporter assetImporter, out ExtendedImportModule data)
        {
            data = null;
            
            var moduleName = moduleInfo.FieldType.Name;
            var dataID = ExtendedImportUtilities.GetExtraUserProperty(assetImporter, moduleName, out string json);
            var existed = dataID > -1;

            if (!existed)
                return false;

            json = json.Replace($"\"{moduleName}\":", "");
            var dataObject = JsonConvert.DeserializeObject(json, moduleInfo.FieldType);
            if (dataObject is ExtendedImportModule dataModule)
            {
                data = dataModule;
                return true;
            }
            
            return false;
        }
    }
}