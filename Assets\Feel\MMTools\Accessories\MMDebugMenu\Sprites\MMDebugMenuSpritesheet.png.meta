fileFormatVersion: 2
guid: 72f7a32ac9c7676409eb438757bc6b4a
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -2741756353435428437
    second: MMDebugMenuSpritesheet_Close
  - first:
      213: 1282028943509491997
    second: MMDebugMenuSpritesheet_Button
  - first:
      213: -8121766140965501267
    second: MMDebugMenuSpritesheet_ButtonBorder
  - first:
      213: -2239278724076068761
    second: MMDebugMenuSpritesheet_Check
  - first:
      213: -6396098458971428934
    second: MMDebugMenuSpritesheet_Check
  - first:
      213: -2239278724076068761
    second: MMDebugMenuSpritesheet_Check
  - first:
      213: -6396098458971428934
    second: MMDebugMenuSpritesheet_SliderKnob
  - first:
      213: 4694184556055176316
    second: MMDebugMenuSpritesheet_Touch
  externalObjects: {}
  serializedVersion: 12
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 0
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 1
  swizzle: 50462976
  cookieLightType: 1
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: iPhone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: MMDebugMenuSpritesheet_Close
      rect:
        serializedVersion: 2
        x: 0
        y: 896
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ba99fcc7f1353f9d0800000000000000
      internalID: -2741756353435428437
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: MMDebugMenuSpritesheet_Button
      rect:
        serializedVersion: 2
        x: 0
        y: 768
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d1117a9188eaac110800000000000000
      internalID: 1282028943509491997
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: MMDebugMenuSpritesheet_ButtonBorder
      rect:
        serializedVersion: 2
        x: 128
        y: 896
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 20, y: 20, z: 20, w: 20}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: da6c4fac9b0b94f80800000000000000
      internalID: -8121766140965501267
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: MMDebugMenuSpritesheet_Check
      rect:
        serializedVersion: 2
        x: 128
        y: 768
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7602b5a5ddb7ce0e0800000000000000
      internalID: -2239278724076068761
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: MMDebugMenuSpritesheet_SliderKnob
      rect:
        serializedVersion: 2
        x: 256
        y: 896
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ab7a59ce65e7c37a0800000000000000
      internalID: -6396098458971428934
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: MMDebugMenuSpritesheet_Touch
      rect:
        serializedVersion: 2
        x: 0
        y: 517
        width: 251
        height: 251
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c742bcd9bf7152140800000000000000
      internalID: 4694184556055176316
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: 50917cc409c24284499fbd599f4b57aa
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      MMDebugMenuSpritesheet_Button: 1282028943509491997
      MMDebugMenuSpritesheet_ButtonBorder: -8121766140965501267
      MMDebugMenuSpritesheet_Check: -2239278724076068761
      MMDebugMenuSpritesheet_Close: -2741756353435428437
      MMDebugMenuSpritesheet_SliderKnob: -6396098458971428934
      MMDebugMenuSpritesheet_Touch: 4694184556055176316
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
AssetOrigin:
  serializedVersion: 1
  productId: 183370
  packageName: Feel
  packageVersion: 5.5
  assetPath: Assets/Feel/MMTools/Accessories/MMDebugMenu/Sprites/MMDebugMenuSpritesheet.png
  uploadId: 759320
