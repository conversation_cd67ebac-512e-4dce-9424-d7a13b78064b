<?xml version="1.0" encoding="UTF-8"?><package><identifier v="Unsaved Package"/><formatVersion v="1.1.0.202302"/><updaterVersion v="1.1.0.202302"/><fileUID v="{1eba1f72-0e81-497f-90ff-ce31580208bf}"/><versionUID v="0"/><dependencies><dependency><filename v="sbs://gradient_linear_1.sbs"/><uid v="1335474237"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://pattern_tile_sampler.sbs"/><uid v="1449399972"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://histogram_scan.sbs"/><uid v="1449404462"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://trapezoid_transform.sbs"/><uid v="1486830014"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_gaussian_noise.sbs"/><uid v="1486875707"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://pattern_shape.sbs"/><uid v="1311189026"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://blur_hq.sbs"/><uid v="1299236171"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://height_blend.sbs"/><uid v="1449623608"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_perlin_noise.sbs"/><uid v="1407332030"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://auto_levels.sbs"/><uid v="1449400845"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://skew.sbs"/><uid v="1407476587"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://non_uniform_blur.sbs"/><uid v="1290776959"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://hbao_2.sbs"/><uid v="1297162667"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://invert.sbs"/><uid v="1368115694"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://mirror.sbs"/><uid v="1407476641"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency></dependencies><content><graph><identifier v="dirt_with_grass"/><uid v="1482651114"/><graphtype v="material"/><graphOutputs><graphoutput><identifier v="basecolor"/><uid v="1213284336"/><attributes><label v="Base Color"/></attributes><usages><usage><components v="RGBA"/><name v="baseColor"/></usage></usages><group v="Material"/></graphoutput><graphoutput><identifier v="normal"/><uid v="1213284338"/><attributes><label v="Normal"/></attributes><usages><usage><components v="RGBA"/><name v="normal"/></usage></usages><group v="Material"/></graphoutput><graphoutput><identifier v="roughness"/><uid v="1213284340"/><attributes><label v="Roughness"/></attributes><usages><usage><components v="RGBA"/><name v="roughness"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput><graphoutput><identifier v="height"/><uid v="1279137031"/><attributes><label v="Height"/></attributes><usages><usage><components v="RGBA"/><name v="height"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput><graphoutput><identifier v="ambientocclusion"/><uid v="1359211721"/><attributes><label v="Ambient Occlusion"/></attributes><usages><usage><components v="RGBA"/><name v="ambientOcclusion"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput></graphOutputs><compNodes><compNode><uid v="1213284337"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1490309177"/><connRefOutput v="1490309178"/></connection></connections><GUILayout><gpos v="400 -272 0"/></GUILayout><compImplementation><compOutputBridge><output v="1213284336"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1213284339"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1489823008"/><connRefOutput v="1359211384"/></connection></connections><GUILayout><gpos v="417.43515 -16 0"/></GUILayout><compImplementation><compOutputBridge><output v="1213284338"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1213284341"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1489823010"/><connRefOutput v="1482708448"/></connection></connections><GUILayout><gpos v="417.43515 97.8779907 0"/></GUILayout><compImplementation><compOutputBridge><output v="1213284340"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1279137030"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1489823004"/><connRefOutput v="1486906083"/></connection></connections><GUILayout><gpos v="417.43515 449.877991 0"/></GUILayout><compImplementation><compOutputBridge><output v="1279137031"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1359211720"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1489822999"/><connRefOutput v="1482708448"/></connection></connections><GUILayout><gpos v="417.43515 641.877991 0"/></GUILayout><compImplementation><compOutputBridge><output v="1359211721"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1489822998"/><connections><connection><identifier v="input1"/><connRef v="1489823012"/><connRefOutput v="1482709066"/></connection></connections><GUILayout><gpos v="-944 208 0"/></GUILayout><compOutputs><compOutput><uid v="1482628326"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.478316337 0.478316337 0.478316337 0.5"/></paramValue></parameter><parameter><name v="levelinlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.12627551 0.12627551 0.12627551 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1489822999"/><connections><connection><identifier v="input1"/><connRef v="1489822998"/><connRefOutput v="1482628326"/></connection></connections><GUILayout><gpos v="-609.495728 224.171875 0"/></GUILayout><compOutputs><compOutput><uid v="1482708448"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.3125 0.3125 0.3125 0.5"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1489823003"/><connections><connection><identifier v="input1"/><connRef v="1489823012"/><connRefOutput v="1482709066"/></connection></connections><GUILayout><gpos v="-942.368652 80 0"/></GUILayout><compOutputs><compOutput><uid v="1482628326"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.478316337 0.478316337 0.478316337 0.5"/></paramValue></parameter><parameter><name v="levelinlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.12627551 0.12627551 0.12627551 0"/></paramValue></parameter><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.235969394 0.235969394 0.235969394 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1489823004"/><connections><connection><identifier v="input1"/><connRef v="1489823012"/><connRefOutput v="1482709066"/></connection></connections><GUILayout><gpos v="-609.495728 2.38525391 0"/></GUILayout><compOutputs><compOutput><uid v="1486906083"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelouthigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.679846942 0.679846942 0.679846942 1"/></paramValue></parameter><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.459302336 0.459302336 0.459302336 0.5"/></paramValue></parameter><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.672193885 0.672193885 0.672193885 1"/></paramValue></parameter><parameter><name v="levelinlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.304846942 0.304846942 0.304846942 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1489823007"/><connections><connection><identifier v="input1"/><connRef v="1489834590"/><connRefOutput v="1487713719"/></connection></connections><GUILayout><gpos v="-1392 -94.4292679 0"/></GUILayout><compOutputs><compOutput><uid v="1482708240"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.53188777 0.53188777 0.53188777 0.5"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1489823008"/><connections><connection><identifier v="input1"/><connRef v="1489823012"/><connRefOutput v="1482709066"/></connection></connections><GUILayout><gpos v="-609.495728 -112 0"/></GUILayout><compOutputs><compOutput><uid v="1359211384"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="normal"/><parameters><parameter><name v="input2alpha"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="3"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1489823010"/><connections><connection><identifier v="input1"/><connRef v="1489823003"/><connRefOutput v="1482628326"/></connection></connections><GUILayout><gpos v="-609.495728 112 0"/></GUILayout><compOutputs><compOutput><uid v="1482708448"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.460459173 0.460459173 0.460459173 0.5"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1489823012"/><connections><connection><identifier v="input1"/><connRef v="1489823007"/><connRefOutput v="1482708240"/></connection></connections><GUILayout><gpos v="-1249.52576 -93.6147461 0"/></GUILayout><compOutputs><compOutput><uid v="1482709066"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.151785716 0.151785716 0.151785716 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1489823201"/><connections><connection><identifier v="opacity"/><connRef v="1489834590"/><connRefOutput v="1487713720"/></connection><connection><identifier v="source"/><connRef v="1494047100"/><connRefOutput v="1482625016"/></connection><connection><identifier v="destination"/><connRef v="1490218840"/><connRefOutput v="1487923396"/></connection></connections><GUILayout><gpos v="-624 -304 0"/></GUILayout><compOutputs><compOutput><uid v="1489823202"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1489834590"/><connections><connection><identifier v="height_top"/><connRef v="1494047095"/><connRefOutput v="1482609709"/></connection><connection><identifier v="height_bottom"/><connRef v="1494009355"/><connRefOutput v="1407458893"/></connection></connections><GUILayout><gpos v="-1552 -304 0"/></GUILayout><compOutputs><compOutput><uid v="1487713719"/><comptype v="2"/></compOutput><compOutput><uid v="1487713720"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///height_blend?dependency=1449623608"/><parameters><parameter><name v="contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="height_position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.979999959"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487713719"/><identifier v="blended_height"/></outputBridging><outputBridging><uid v="1487713720"/><identifier v="height_mask"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1490218840"/><connections><connection><identifier v="input1"/><connRef v="1494009355"/><connRefOutput v="1407458893"/></connection></connections><GUILayout><gpos v="-720 -720 0"/></GUILayout><compOutputs><compOutput><uid v="1487923396"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1487923422"/><paramsArrayCells><paramsArrayCell><uid v="1494091020"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.979338825 0.73613745 0.294981569 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1494091021"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.698312223"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.727272749 0.438502491 0 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1494091022"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.620253146"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.495867759 0.316885322 0.122473359 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1490309177"/><connections><connection><identifier v="destination"/><connRef v="1489823201"/><connRefOutput v="1489823202"/></connection></connections><GUILayout><gpos v="-0.717688322 -272 0"/></GUILayout><compOutputs><compOutput><uid v="1490309178"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1494009284"/><connections><connection><identifier v="pattern_input"/><connRef v="1494009311"/><connRefOutput v="1407492498"/></connection></connections><GUILayout><gpos v="-4048 368 0"/></GUILayout><compOutputs><compOutput><uid v="1407475981"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1449399972"/><parameters><parameter><name v="rotation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.75"/></paramValue></parameter><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="40"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="50"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="15"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.560000002"/></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="3.03999996"/></paramValue></parameter><parameter><name v="rotation_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0299999993"/></paramValue></parameter><parameter><name v="color_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.479999989"/></paramValue></parameter><parameter><name v="size"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.969999969 1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407475981"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1494009285"/><connections><connection><identifier v="input1"/><connRef v="1494009301"/><connRefOutput v="1407499433"/></connection></connections><GUILayout><gpos v="-2786.11572 368 0"/></GUILayout><compOutputs><compOutput><uid v="1407500338"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelouthigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.814984739 0.814984739 0.814984739 1"/></paramValue></parameter><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.548929691 0.548929691 0.548929691 0"/></paramValue></parameter><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.533639133 0.533639133 0.533639133 0.5"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1494009286"/><connections><connection><identifier v="input1"/><connRef v="1494009305"/><connRefOutput v="1407493828"/></connection></connections><GUILayout><gpos v="-3312 368 0"/></GUILayout><compOutputs><compOutput><uid v="1407485846"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelouthigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.484709471 0.484709471 0.484709471 1"/></paramValue></parameter><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.316513747 0.316513747 0.316513747 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1494009287"/><GUILayout><gpos v="-4720 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407476317"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///shape?dependency=1311189026"/><parameters><parameter><name v="Pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="Size"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.769999981"/></paramValue></parameter><parameter><name v="Size_xy"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.449999988 1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407476317"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1494009290"/><connections><connection><identifier v="source"/><connRef v="1494009314"/><connRefOutput v="1407468889"/></connection><connection><identifier v="destination"/><connRef v="1494009310"/><connRefOutput v="1407520222"/></connection></connections><GUILayout><gpos v="-2256 368 0"/></GUILayout><compOutputs><compOutput><uid v="1407458893"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.00999999978"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1494009291"/><connections><connection><identifier v="Source"/><connRef v="1494009304"/><connRefOutput v="1355424961"/></connection></connections><GUILayout><gpos v="-3709.50439 527.999878 0"/></GUILayout><compOutputs><compOutput><uid v="1407484543"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1299236171"/><parameters/><outputBridgings><outputBridging><uid v="1407484543"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1494009292"/><GUILayout><gpos v="-4720 -16 0"/></GUILayout><compOutputs><compOutput><uid v="1407476317"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///shape?dependency=1311189026"/><parameters><parameter><name v="Pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="Size"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.769999981"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407476317"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1494009293"/><GUILayout><gpos v="-3504 911.999878 0"/></GUILayout><compOutputs><compOutput><uid v="1407465564"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1449399972"/><parameters><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="250"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="250"/></paramValue></parameter><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.75"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.22000003"/></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.5"/></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.269999981"/></paramValue></parameter><parameter><name v="color_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.789999962"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407465564"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1494009294"/><GUILayout><gpos v="-4720 -144 0"/></GUILayout><compOutputs><compOutput><uid v="1407476317"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///shape?dependency=1311189026"/><parameters><parameter><name v="Pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="Size"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.769999981"/></paramValue></parameter><parameter><name v="Size_xy"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.449999988 1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407476317"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1494009295"/><connections><connection><identifier v="inputintensity"/><connRef v="1494009309"/><connRefOutput v="1407507540"/></connection><connection><identifier v="input1"/><connRef v="1494009297"/><connRefOutput v="1407485716"/></connection></connections><GUILayout><gpos v="-3632.00049 368 0"/></GUILayout><compOutputs><compOutput><uid v="1407504138"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="directionalwarp"/><parameters><parameter><name v="warpangle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="-0.25"/></paramValue></parameter><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="5.4000001"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1494009297"/><connections><connection><identifier v="Source"/><connRef v="1494009298"/><connRefOutput v="1407481373"/></connection></connections><GUILayout><gpos v="-3760.00049 368 0"/></GUILayout><compOutputs><compOutput><uid v="1407485716"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1299236171"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.02999997"/></paramValue></parameter><parameter><name v="Quality"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407485716"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1494009298"/><connections><connection><identifier v="Input"/><connRef v="1494009284"/><connRefOutput v="1407475981"/></connection></connections><GUILayout><gpos v="-3906.56299 368 0"/></GUILayout><compOutputs><compOutput><uid v="1407481373"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1449400845"/><parameters/><outputBridgings><outputBridging><uid v="1407481373"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1494009299"/><connections><connection><identifier v="Source"/><connRef v="1494009287"/><connRefOutput v="1407476317"/></connection></connections><GUILayout><gpos v="-4560 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407476635"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///skew_grayscale?dependency=1407476587"/><parameters><parameter><name v="Amount"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="-0.159999996"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407476635"/><identifier v="Skew"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1494009301"/><connections><connection><identifier v="Input"/><connRef v="1494009306"/><connRefOutput v="1407485769"/></connection></connections><GUILayout><gpos v="-2946.11572 368 0"/></GUILayout><compOutputs><compOutput><uid v="1407499433"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1449400845"/><parameters/><outputBridgings><outputBridging><uid v="1407499433"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1494009302"/><connections><connection><identifier v="Source"/><connRef v="1494009315"/><connRefOutput v="1407476733"/></connection><connection><identifier v="Effect"/><connRef v="1494009315"/><connRefOutput v="1407476733"/></connection></connections><GUILayout><gpos v="-4240 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407476806"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///non_uniform_blur_grayscale?dependency=1290776959"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="16"/></paramValue></parameter><parameter><name v="Blades"/><relativeTo v="0"/><paramValue><constantValueInt32 v="9"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="14.3400002"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407476806"/><identifier v="Non_Uniform_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1494009303"/><GUILayout><gpos v="-3408 176 0"/></GUILayout><compOutputs><compOutput><uid v="1407485775"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///perlin_noise?dependency=1407332030"/><parameters><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueInt32 v="7"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407485775"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1494009304"/><GUILayout><gpos v="-3872.19141 527.999878 0"/></GUILayout><compOutputs><compOutput><uid v="1355424961"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///hbao?dependency=1297162667"/><parameters><parameter><name v="height_depth"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.370000005"/></paramValue></parameter><parameter><name v="samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="16"/></paramValue></parameter><parameter><name v="surface_size"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="268.820007"/></paramValue></parameter><parameter><name v="height_depth_cm"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="93.1899948"/></paramValue></parameter><parameter><name v="radius"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.579999983"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1355424961"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1494009305"/><connections><connection><identifier v="inputintensity"/><connRef v="1494009307"/><connRefOutput v="1407485701"/></connection><connection><identifier v="input1"/><connRef v="1494009295"/><connRefOutput v="1407504138"/></connection></connections><GUILayout><gpos v="-3468.78223 368 0"/></GUILayout><compOutputs><compOutput><uid v="1407493828"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="directionalwarp"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="2.87999988"/></paramValue></parameter><parameter><name v="warpangle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="-0.732749999"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1494009306"/><connections><connection><identifier v="source"/><connRef v="1494009303"/><connRefOutput v="1407485775"/></connection><connection><identifier v="destination"/><connRef v="1494009286"/><connRefOutput v="1407485846"/></connection></connections><GUILayout><gpos v="-3106.11572 368 0"/></GUILayout><compOutputs><compOutput><uid v="1407485769"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1494009307"/><connections><connection><identifier v="Source"/><connRef v="1494009291"/><connRefOutput v="1407484543"/></connection></connections><GUILayout><gpos v="-3549.50391 527.999878 0"/></GUILayout><compOutputs><compOutput><uid v="1407485701"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///invert_grayscale?dependency=1368115694"/><parameters/><outputBridgings><outputBridging><uid v="1407485701"/><identifier v="Invert_Grayscale"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1494009309"/><connections><connection><identifier v="Source"/><connRef v="1494009313"/><connRefOutput v="1407475981"/></connection></connections><GUILayout><gpos v="-3660.78223 -144 0"/></GUILayout><compOutputs><compOutput><uid v="1407507540"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1299236171"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="2.63999987"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407507540"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1494009310"/><connections><connection><identifier v="destination"/><connRef v="1494009312"/><connRefOutput v="1407509758"/></connection><connection><identifier v="source"/><connRef v="1494009291"/><connRefOutput v="1407484543"/></connection></connections><GUILayout><gpos v="-2416 368 0"/></GUILayout><compOutputs><compOutput><uid v="1407520222"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.479999989"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1494009311"/><connections><connection><identifier v="input1"/><connRef v="1494009294"/><connRefOutput v="1407476317"/></connection><connection><identifier v="inputintensity"/><connRef v="1494009292"/><connRefOutput v="1407476317"/></connection></connections><GUILayout><gpos v="-4432 -16 0"/></GUILayout><compOutputs><compOutput><uid v="1407492498"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="directionalwarp"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="50"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1494009312"/><connections><connection><identifier v="destination"/><connRef v="1494009285"/><connRefOutput v="1407500338"/></connection></connections><GUILayout><gpos v="-2608 367.999878 0"/></GUILayout><compOutputs><compOutput><uid v="1407509758"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1494009313"/><connections><connection><identifier v="pattern_input"/><connRef v="1494009302"/><connRefOutput v="1407476806"/></connection></connections><GUILayout><gpos v="-3856 -144 0"/></GUILayout><compOutputs><compOutput><uid v="1407475981"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1449399972"/><parameters><parameter><name v="rotation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.5"/></paramValue></parameter><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="20"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="20"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="8.77999973"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.560000002"/></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="3.03999996"/></paramValue></parameter><parameter><name v="rotation_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0299999993"/></paramValue></parameter><parameter><name v="color_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.479999989"/></paramValue></parameter><parameter><name v="size"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.969999969 1"/></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.680000007"/></paramValue></parameter><parameter><name v="mask_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.340000004"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407475981"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1494009314"/><connections><connection><identifier v="Source"/><connRef v="1494009293"/><connRefOutput v="1407465564"/></connection></connections><GUILayout><gpos v="-3376 911.999878 0"/></GUILayout><compOutputs><compOutput><uid v="1407468889"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1299236171"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.460000008"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407468889"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1494009315"/><connections><connection><identifier v="input"/><connRef v="1494009299"/><connRefOutput v="1407476635"/></connection></connections><GUILayout><gpos v="-4400 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407476733"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///mirror_grayscale?dependency=1407476641"/><parameters/><outputBridgings><outputBridging><uid v="1407476733"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1494009349"/><connections><connection><identifier v="pattern_input"/><connRef v="1494009376"/><connRefOutput v="1407492498"/></connection></connections><GUILayout><gpos v="-4048 368 0"/></GUILayout><compOutputs><compOutput><uid v="1407475981"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1449399972"/><parameters><parameter><name v="rotation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.75"/></paramValue></parameter><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="40"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="50"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="15"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.560000002"/></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="3.03999996"/></paramValue></parameter><parameter><name v="rotation_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0299999993"/></paramValue></parameter><parameter><name v="color_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.479999989"/></paramValue></parameter><parameter><name v="size"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.969999969 1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407475981"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1494009350"/><connections><connection><identifier v="input1"/><connRef v="1494009366"/><connRefOutput v="1407499433"/></connection></connections><GUILayout><gpos v="-2786.11572 368 0"/></GUILayout><compOutputs><compOutput><uid v="1407500338"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelouthigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.814984739 0.814984739 0.814984739 1"/></paramValue></parameter><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.548929691 0.548929691 0.548929691 0"/></paramValue></parameter><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.533639133 0.533639133 0.533639133 0.5"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1494009351"/><connections><connection><identifier v="input1"/><connRef v="1494009370"/><connRefOutput v="1407493828"/></connection></connections><GUILayout><gpos v="-3312 368 0"/></GUILayout><compOutputs><compOutput><uid v="1407485846"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelouthigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.484709471 0.484709471 0.484709471 1"/></paramValue></parameter><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.316513747 0.316513747 0.316513747 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1494009352"/><GUILayout><gpos v="-4720 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407476317"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///shape?dependency=1311189026"/><parameters><parameter><name v="Pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="Size"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.769999981"/></paramValue></parameter><parameter><name v="Size_xy"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.449999988 1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407476317"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1494009355"/><connections><connection><identifier v="source"/><connRef v="1494009379"/><connRefOutput v="1407468889"/></connection><connection><identifier v="destination"/><connRef v="1494009375"/><connRefOutput v="1407520222"/></connection></connections><GUILayout><gpos v="-2256 368 0"/></GUILayout><compOutputs><compOutput><uid v="1407458893"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.329999983"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1494009356"/><connections><connection><identifier v="Source"/><connRef v="1494009369"/><connRefOutput v="1355424961"/></connection></connections><GUILayout><gpos v="-3709.50391 528.000122 0"/></GUILayout><compOutputs><compOutput><uid v="1407484543"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1299236171"/><parameters/><outputBridgings><outputBridging><uid v="1407484543"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1494009357"/><GUILayout><gpos v="-4720 -16 0"/></GUILayout><compOutputs><compOutput><uid v="1407476317"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///shape?dependency=1311189026"/><parameters><parameter><name v="Pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="Size"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.769999981"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407476317"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1494009358"/><GUILayout><gpos v="-3504 912.000122 0"/></GUILayout><compOutputs><compOutput><uid v="1407465564"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1449399972"/><parameters><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="250"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="250"/></paramValue></parameter><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.75"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.22000003"/></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.5"/></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.269999981"/></paramValue></parameter><parameter><name v="color_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.789999962"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407465564"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1494009359"/><GUILayout><gpos v="-4720 -144 0"/></GUILayout><compOutputs><compOutput><uid v="1407476317"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///shape?dependency=1311189026"/><parameters><parameter><name v="Pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="Size"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.769999981"/></paramValue></parameter><parameter><name v="Size_xy"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.449999988 1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407476317"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1494009360"/><connections><connection><identifier v="inputintensity"/><connRef v="1494009374"/><connRefOutput v="1407507540"/></connection><connection><identifier v="input1"/><connRef v="1494009362"/><connRefOutput v="1407485716"/></connection></connections><GUILayout><gpos v="-3632 368 0"/></GUILayout><compOutputs><compOutput><uid v="1407504138"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="directionalwarp"/><parameters><parameter><name v="warpangle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="-0.25"/></paramValue></parameter><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="5.4000001"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1494009362"/><connections><connection><identifier v="Source"/><connRef v="1494009363"/><connRefOutput v="1407481373"/></connection></connections><GUILayout><gpos v="-3760 368 0"/></GUILayout><compOutputs><compOutput><uid v="1407485716"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1299236171"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.02999997"/></paramValue></parameter><parameter><name v="Quality"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407485716"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1494009363"/><connections><connection><identifier v="Input"/><connRef v="1494009349"/><connRefOutput v="1407475981"/></connection></connections><GUILayout><gpos v="-3906.56299 368 0"/></GUILayout><compOutputs><compOutput><uid v="1407481373"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1449400845"/><parameters/><outputBridgings><outputBridging><uid v="1407481373"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1494009364"/><connections><connection><identifier v="Source"/><connRef v="1494009352"/><connRefOutput v="1407476317"/></connection></connections><GUILayout><gpos v="-4560 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407476635"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///skew_grayscale?dependency=1407476587"/><parameters><parameter><name v="Amount"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="-0.159999996"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407476635"/><identifier v="Skew"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1494009366"/><connections><connection><identifier v="Input"/><connRef v="1494009371"/><connRefOutput v="1407485769"/></connection></connections><GUILayout><gpos v="-2946.11572 368 0"/></GUILayout><compOutputs><compOutput><uid v="1407499433"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1449400845"/><parameters/><outputBridgings><outputBridging><uid v="1407499433"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1494009367"/><connections><connection><identifier v="Source"/><connRef v="1494009380"/><connRefOutput v="1407476733"/></connection><connection><identifier v="Effect"/><connRef v="1494009380"/><connRefOutput v="1407476733"/></connection></connections><GUILayout><gpos v="-4240 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407476806"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///non_uniform_blur_grayscale?dependency=1290776959"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="16"/></paramValue></parameter><parameter><name v="Blades"/><relativeTo v="0"/><paramValue><constantValueInt32 v="9"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="14.3400002"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407476806"/><identifier v="Non_Uniform_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1494009368"/><GUILayout><gpos v="-3408 176 0"/></GUILayout><compOutputs><compOutput><uid v="1407485775"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///perlin_noise?dependency=1407332030"/><parameters><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueInt32 v="7"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407485775"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1494009369"/><GUILayout><gpos v="-3872.19141 528.000122 0"/></GUILayout><compOutputs><compOutput><uid v="1355424961"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///hbao?dependency=1297162667"/><parameters><parameter><name v="height_depth"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.370000005"/></paramValue></parameter><parameter><name v="samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="16"/></paramValue></parameter><parameter><name v="surface_size"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="268.820007"/></paramValue></parameter><parameter><name v="height_depth_cm"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="93.1899948"/></paramValue></parameter><parameter><name v="radius"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.579999983"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1355424961"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1494009370"/><connections><connection><identifier v="inputintensity"/><connRef v="1494009372"/><connRefOutput v="1407485701"/></connection><connection><identifier v="input1"/><connRef v="1494009360"/><connRefOutput v="1407504138"/></connection></connections><GUILayout><gpos v="-3468.78223 368 0"/></GUILayout><compOutputs><compOutput><uid v="1407493828"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="directionalwarp"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="2.87999988"/></paramValue></parameter><parameter><name v="warpangle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="-0.732749999"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1494009371"/><connections><connection><identifier v="source"/><connRef v="1494009368"/><connRefOutput v="1407485775"/></connection><connection><identifier v="destination"/><connRef v="1494009351"/><connRefOutput v="1407485846"/></connection></connections><GUILayout><gpos v="-3106.11572 368 0"/></GUILayout><compOutputs><compOutput><uid v="1407485769"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1494009372"/><connections><connection><identifier v="Source"/><connRef v="1494009356"/><connRefOutput v="1407484543"/></connection></connections><GUILayout><gpos v="-3549.50391 528.000122 0"/></GUILayout><compOutputs><compOutput><uid v="1407485701"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///invert_grayscale?dependency=1368115694"/><parameters/><outputBridgings><outputBridging><uid v="1407485701"/><identifier v="Invert_Grayscale"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1494009374"/><connections><connection><identifier v="Source"/><connRef v="1494009378"/><connRefOutput v="1407475981"/></connection></connections><GUILayout><gpos v="-3660.78223 -144 0"/></GUILayout><compOutputs><compOutput><uid v="1407507540"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1299236171"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="2.63999987"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407507540"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1494009375"/><connections><connection><identifier v="destination"/><connRef v="1494009377"/><connRefOutput v="1407509758"/></connection><connection><identifier v="source"/><connRef v="1494009356"/><connRefOutput v="1407484543"/></connection></connections><GUILayout><gpos v="-2416 368 0"/></GUILayout><compOutputs><compOutput><uid v="1407520222"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.329999983"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1494009376"/><connections><connection><identifier v="input1"/><connRef v="1494009359"/><connRefOutput v="1407476317"/></connection><connection><identifier v="inputintensity"/><connRef v="1494009357"/><connRefOutput v="1407476317"/></connection></connections><GUILayout><gpos v="-4432 -16 0"/></GUILayout><compOutputs><compOutput><uid v="1407492498"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="directionalwarp"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="50"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1494009377"/><connections><connection><identifier v="destination"/><connRef v="1494009350"/><connRefOutput v="1407500338"/></connection></connections><GUILayout><gpos v="-2589.62842 143.999878 0"/></GUILayout><compOutputs><compOutput><uid v="1407509758"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1494009378"/><connections><connection><identifier v="pattern_input"/><connRef v="1494009367"/><connRefOutput v="1407476806"/></connection></connections><GUILayout><gpos v="-3856 -144 0"/></GUILayout><compOutputs><compOutput><uid v="1407475981"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1449399972"/><parameters><parameter><name v="rotation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.5"/></paramValue></parameter><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="20"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="20"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="8.77999973"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.560000002"/></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="3.03999996"/></paramValue></parameter><parameter><name v="rotation_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0299999993"/></paramValue></parameter><parameter><name v="color_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.479999989"/></paramValue></parameter><parameter><name v="size"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.969999969 1"/></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.680000007"/></paramValue></parameter><parameter><name v="mask_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.340000004"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407475981"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1494009379"/><connections><connection><identifier v="Source"/><connRef v="1494009358"/><connRefOutput v="1407465564"/></connection></connections><GUILayout><gpos v="-3376 912.000122 0"/></GUILayout><compOutputs><compOutput><uid v="1407468889"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1299236171"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1407468889"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1494009380"/><connections><connection><identifier v="input"/><connRef v="1494009364"/><connRefOutput v="1407476635"/></connection></connections><GUILayout><gpos v="-4400 304 0"/></GUILayout><compOutputs><compOutput><uid v="1407476733"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///mirror_grayscale?dependency=1407476641"/><parameters/><outputBridgings><outputBridging><uid v="1407476733"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1494047095"/><connections><connection><identifier v="pattern_input"/><connRef v="1494047104"/><connRefOutput v="1482608790"/></connection><connection><identifier v="rotation_map_input"/><connRef v="1494047103"/><connRefOutput v="1482609943"/></connection></connections><GUILayout><gpos v="-3290.86426 -728.492432 0"/></GUILayout><compOutputs><compOutput><uid v="1482609709"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1449399972"/><parameters><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="234"/></paramValue></parameter><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="size"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="13.6499996 14.3999996"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="3.41999984"/></paramValue></parameter><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="234"/></paramValue></parameter><parameter><name v="size_random"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.329999983 0.319999993"/></paramValue></parameter><parameter><name v="rotation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.969916642"/></paramValue></parameter><parameter><name v="non_square_expansion"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="rotation_map_multiplier"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.649999976"/></paramValue></parameter><parameter><name v="rotation_vector_map_multiplier"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.399999976"/></paramValue></parameter><parameter><name v="color_parametrization_mode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="scale_vector_map_multiplier"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.519999981"/></paramValue></parameter><parameter><name v="mask_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.849999964"/></paramValue></parameter><parameter><name v="vector_map_displacement"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.969999969"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.359999985"/></paramValue></parameter><parameter><name v="color_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="10"/></paramValue></parameter><parameter><name v="pattern_input_number"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1482609709"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1494047096"/><connections><connection><identifier v="input"/><connRef v="1494047105"/><connRefOutput v="1482576333"/></connection></connections><GUILayout><gpos v="-4058.82666 -847.174316 0"/></GUILayout><compOutputs><compOutput><uid v="1482591428"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///trapezoid_transform_grayscale?dependency=1486830014"/><parameters><parameter><name v="top_stretch"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.620000005"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1482591428"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1494047097"/><connections><connection><identifier v="input1"/><connRef v="1494047096"/><connRefOutput v="1482591428"/></connection></connections><GUILayout><gpos v="-3930.0293 -847.174316 0"/></GUILayout><compOutputs><compOutput><uid v="1482590232"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.149234697 0.149234697 0.149234697 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1494047098"/><GUILayout><gpos v="-3930.86426 -717.825684 0"/></GUILayout><compOutputs><compOutput><uid v="1482591041"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///gradient_linear_1?dependency=1335474237"/><parameters><parameter><name v="rotation"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1482591041"/><identifier v="Simple_Gradient"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1494047099"/><connections><connection><identifier v="Input_1"/><connRef v="1494047098"/><connRefOutput v="1482591041"/></connection></connections><GUILayout><gpos v="-3802.86426 -717.825684 0"/></GUILayout><compOutputs><compOutput><uid v="1482590975"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1449404462"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.610000014"/></paramValue></parameter><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0700000003"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1482590975"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1494047100"/><connections><connection><identifier v="input1"/><connRef v="1494047095"/><connRefOutput v="1482609709"/></connection></connections><GUILayout><gpos v="-1264 -688 0"/></GUILayout><compOutputs><compOutput><uid v="1482625016"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1482625039"/><paramsArrayCells><paramsArrayCell><uid v="1482625052"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.286956519 0.182096973 0.0550556034 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1482625053"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.875527442"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.650328338 0.908695638 0.507178962 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1482625213"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.0354818478"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.168359771 0.352173924 0.114661269 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1482625257"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.386075944"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.41995281 0.682608724 0.170652181 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1482625330"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.630801678"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.660464287 0.813043475 0.207987875 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487693664"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.0126582272"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.0372851379 0.256521732 0.0391915254 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1494047101"/><connections><connection><identifier v="source"/><connRef v="1494047097"/><connRefOutput v="1482590232"/></connection></connections><GUILayout><gpos v="-3800.45215 -847.174316 0"/></GUILayout><compOutputs><compOutput><uid v="1482590705"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="maskrectangle"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.199999988 0.779999971 0 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1494047102"/><connections><connection><identifier v="destination"/><connRef v="1494047101"/><connRefOutput v="1482590705"/></connection><connection><identifier v="source"/><connRef v="1494047099"/><connRefOutput v="1482590975"/></connection></connections><GUILayout><gpos v="-3640.66357 -847.174194 0"/></GUILayout><compOutputs><compOutput><uid v="1482591088"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="maskrectangle"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0 1 0 0"/></paramValue></parameter><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1494047103"/><GUILayout><gpos v="-3450.86426 -717.825684 0"/></GUILayout><compOutputs><compOutput><uid v="1482609943"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///gaussian_noise?dependency=1486875707"/><parameters><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueInt32 v="9"/></paramValue></parameter><parameter><name v="disorder"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.099999994"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1482609943"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1494047104"/><connections><connection><identifier v="input1"/><connRef v="1494047102"/></connection></connections><GUILayout><gpos v="-3512.66309 -847.174194 0"/></GUILayout><compOutputs><compOutput><uid v="1482608790"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blur"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.91999996"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1494047105"/><GUILayout><gpos v="-4184.3125 -846.737183 0"/></GUILayout><compOutputs><compOutput><uid v="1482576333"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///shape?dependency=1311189026"/><parameters><parameter><name v="Pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="16"/></paramValue></parameter><parameter><name v="Size"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.74000001"/></paramValue></parameter><parameter><name v="Pattern_Specific"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.829999983"/></paramValue></parameter><parameter><name v="Size_xy"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.199999988 1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1482576333"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode></compNodes><baseParameters/><options><option><name v="defaultParentSize"/><value v="11x11"/></option><option><name v="export/batch/destination"/><value v="D:/work/Annery/stylized material/stylized material/images/New folder (2)"/></option><option><name v="export/batch/extension"/><value v="png"/></option><option><name v="export/batch/outputSize"/><value v="11x11"/></option><option><name v="export/batch/outputs/ambientocclusion"/><value v="true"/></option><option><name v="export/batch/outputs/basecolor"/><value v="true"/></option><option><name v="export/batch/outputs/height"/><value v="true"/></option><option><name v="export/batch/outputs/metallic"/><value v="true"/></option><option><name v="export/batch/outputs/normal"/><value v="true"/></option><option><name v="export/batch/outputs/roughness"/><value v="true"/></option><option><name v="export/batch/outputsColorspace/ambientocclusion"/><value v="Raw"/></option><option><name v="export/batch/outputsColorspace/basecolor"/><value v="Raw"/></option><option><name v="export/batch/outputsColorspace/height"/><value v="Raw"/></option><option><name v="export/batch/outputsColorspace/metallic"/><value v="Raw"/></option><option><name v="export/batch/outputsColorspace/normal"/><value v="Raw"/></option><option><name v="export/batch/outputsColorspace/roughness"/><value v="Raw"/></option><option><name v="export/batch/pattern"/><value v="$(graph)_$(identifier)"/></option><option><name v="export/fromGraph/autoExport"/><value v="false"/></option><option><name v="export/fromGraph/destination"/><value v="D:/work/Annery/stylized material/stylized material/images/dirt with grass"/></option><option><name v="export/fromGraph/extension"/><value v="png"/></option><option><name v="export/fromGraph/outputs/ambientocclusion"/><value v="true"/></option><option><name v="export/fromGraph/outputs/basecolor"/><value v="true"/></option><option><name v="export/fromGraph/outputs/height"/><value v="true"/></option><option><name v="export/fromGraph/outputs/normal"/><value v="true"/></option><option><name v="export/fromGraph/outputs/roughness"/><value v="true"/></option><option><name v="export/fromGraph/outputsColorspace/ambientocclusion"/><value v="sRGB"/></option><option><name v="export/fromGraph/outputsColorspace/basecolor"/><value v="sRGB"/></option><option><name v="export/fromGraph/outputsColorspace/height"/><value v="sRGB"/></option><option><name v="export/fromGraph/outputsColorspace/normal"/><value v="sRGB"/></option><option><name v="export/fromGraph/outputsColorspace/roughness"/><value v="sRGB"/></option><option><name v="export/fromGraph/pattern"/><value v="$(graph)_$(identifier)"/></option></options><root><rootOutputs><rootOutput><output v="1213284336"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1213284338"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1213284340"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1279137031"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1359211721"/><format v="0"/><usertag v=""/></rootOutput></rootOutputs></root></graph></content></package>
