using UnityEngine;
#if UNITY_EDITOR
using UnityEditor;
#endif
#if URP_INSTALLED
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
#endif

namespace Stylo.Flux
{
    [CreateAssetMenu(fileName = "New Flux Preset", menuName = "Stylo/Flux Preset", order = 1)]
    public class FluxPreset : ScriptableObject
    {
        [Header("Preset Information")]
        public string presetName = "New Flux Preset";
        [TextArea(2, 4)]
        public string description = "Custom datamoshing preset";
        public string category = "Custom";

        [Header("🔧 Master Controls")]
        [Range(0f, 1f)] public float effectIntensity = 0.35f;
        public bool onlyStenciled = false;

        [Header("🎯 TRUE DATAMOSHING")]
        [Header("Compression Encoding")]
        [Range(0f, 1f)] public float colorCrunch = 0.5f;
        [Range(1, 10)] public int downscaling = 10;
        public FluxBlockSize blockSize = FluxBlockSize._16x16;
        public bool dontCrunchSkybox = true;

        [Header("Motion Vector Reprojection")]
        [Range(0f, 1f)] public float reprojectBaseNoise = 0f;
        [Range(0f, 20f)] public float reprojectBaseRerollSpeed = 3f;
        [Range(0f, 5f)] public float reprojectLengthInfluence = 0f;

        [Header("Authentic Compression Artifacts")]
        [Range(0f, 1f)] public float keyframeResetRate = 0f;
        [Range(0f, 2f)] public float motionVectorCorruption = 0f;
        [Range(0f, 1f)] public float errorAccumulation = 0f;
        [Range(0f, 1f)] public float dctCorruption = 0f;
        [Range(0f, 1f)] public float chromaCorruption = 0f;

        [Header("🌟 ENHANCED VISUAL EFFECTS")]
        [Header("Consolidated Motion Processing")]
        [Range(0f, 10f)] public float motionAmplification = 3f;
        [Range(0f, 0.1f)] public float motionThreshold = 0.001f;
        [Range(0f, 1f)] public float cameraObjectMotionBalance = 0.3f;
        [Range(0f, 1f)] public float motionSmoothing = 0.1f;

        [Header("Pixel Flow & Trailing")]
        [Range(0f, 5f)] public float trailIntensity = 2f;
        [Range(0f, 1f)] public float trailSmoothness = 0.5f;
        [Range(0f, 1f)] public float trailPersistence = 0.8f;
        [Range(0f, 5f)] public float flowSpread = 2f;

        [Header("Artistic Effects")]
        public Texture2D corruptionMask = null;
        [Range(0f, 1f)] public float glitchTransition = 0f;
        [Range(0f, 0.8f)] public float feedbackIntensity = 0f;
        [Range(0f, 1f)] public float multiScaleCorruption = 0f;

        [Header("JPEG Quality Control")]
        [Range(1f, 100f)] public float jpegQuality = 50f;
        [Range(0f, 2f)] public float luminanceQuantization = 0.5f;
        [Range(0f, 2f)] public float chrominanceQuantization = 0.7f;
        public bool chromaSubsampling = false;

        [Header("Compression Artifacts")]
        [Range(0f, 1f)] public float ringingArtifacts = 0f;
        [Range(0f, 1f)] public float mosquitoNoise = 0f;
        [Range(0.1f, 2f)] public float edgeSensitivity = 0.5f;



        [Header("💡 Brightness Control")]
        [Range(0f, 0.5f)] public float noiseTransparency = 0.05f;
        [Range(0f, 1f)] public float maxNoiseBrightness = 0.9f;
        [Range(0f, 1f)] public float brightnessThreshold = 0.7f;
        [Range(0f, 1f)] public float brightAreaMasking = 0.8f;

        [Header("🔧 Utility Controls")]
        [Range(0f, 10f)] public float oversharpening = 0f;
        public bool visualizeMotionVectors = false;
        public bool debugCompressionArtifacts = false;

        // Enum to match both Runtime and SRP versions
        public enum FluxBlockSize
        {
            _2x2 = 0,
            _4x4 = 1,
            _8x8 = 2,
            _16x16 = 3,
            _32x32 = 4
        }

        /// <summary>
        /// Returns a formatted display name for UI
        /// </summary>
        public string GetDisplayName()
        {
            return string.IsNullOrEmpty(presetName) ? name : presetName;
        }

        /// <summary>
        /// Returns category and name for organized display
        /// </summary>
        public string GetCategorizedName()
        {
            return string.IsNullOrEmpty(category) ? GetDisplayName() : $"{category}/{GetDisplayName()}";
        }

        /// <summary>
        /// Creates a preset from current Runtime Flux Effect parameters
        /// </summary>
        public void CaptureFromRuntime(object fluxEffect)
        {
            // Use reflection to avoid assembly reference issues
            if (fluxEffect == null) return;

            var type = fluxEffect.GetType();

            // Master controls
            effectIntensity = GetFloatProperty(fluxEffect, type, "EffectIntensity");
            onlyStenciled = GetBoolProperty(fluxEffect, type, "OnlyStenciled");

            // TRUE DATAMOSHING - Compression encoding
            colorCrunch = GetFloatProperty(fluxEffect, type, "ColorCrunch");
            downscaling = GetIntProperty(fluxEffect, type, "Downscaling");
            blockSize = (FluxBlockSize)GetIntProperty(fluxEffect, type, "BlockSize");
            dontCrunchSkybox = GetBoolProperty(fluxEffect, type, "DontCrunchSkybox");

            // TRUE DATAMOSHING - Motion vector reprojection
            reprojectBaseNoise = GetFloatProperty(fluxEffect, type, "ReprojectBaseNoise");
            reprojectBaseRerollSpeed = GetFloatProperty(fluxEffect, type, "ReprojectBaseRerollSpeed");
            reprojectLengthInfluence = GetFloatProperty(fluxEffect, type, "ReprojectLengthInfluence");

            // TRUE DATAMOSHING - Authentic compression artifacts
            keyframeResetRate = GetFloatProperty(fluxEffect, type, "KeyframeResetRate");
            motionVectorCorruption = GetFloatProperty(fluxEffect, type, "MotionVectorCorruption");
            errorAccumulation = GetFloatProperty(fluxEffect, type, "ErrorAccumulation");
            dctCorruption = GetFloatProperty(fluxEffect, type, "DCTCorruption");
            chromaCorruption = GetFloatProperty(fluxEffect, type, "ChromaCorruption");

            // CONSOLIDATED MOTION PROCESSING - New unified system
            motionAmplification = GetFloatProperty(fluxEffect, type, "MotionAmplification");
            motionThreshold = GetFloatProperty(fluxEffect, type, "MotionThreshold");
            cameraObjectMotionBalance = GetFloatProperty(fluxEffect, type, "CameraObjectMotionBalance");
            motionSmoothing = GetFloatProperty(fluxEffect, type, "MotionSmoothing");

            // ENHANCED VISUAL EFFECTS - Pixel flow & trailing
            trailIntensity = GetFloatProperty(fluxEffect, type, "TrailIntensity");
            trailSmoothness = GetFloatProperty(fluxEffect, type, "TrailSmoothness");
            trailPersistence = GetFloatProperty(fluxEffect, type, "TrailPersistence");
            flowSpread = GetFloatProperty(fluxEffect, type, "FlowSpread");

            // ENHANCED VISUAL EFFECTS - Artistic effects
            corruptionMask = GetTextureProperty(fluxEffect, type, "CorruptionMask");
            glitchTransition = GetFloatProperty(fluxEffect, type, "GlitchTransition");
            feedbackIntensity = GetFloatProperty(fluxEffect, type, "FeedbackIntensity");
            multiScaleCorruption = GetFloatProperty(fluxEffect, type, "MultiScaleCorruption");

            // JPEG Quality Control
            jpegQuality = GetFloatProperty(fluxEffect, type, "JPEGQuality");
            luminanceQuantization = GetFloatProperty(fluxEffect, type, "LuminanceQuantization");
            chrominanceQuantization = GetFloatProperty(fluxEffect, type, "ChrominanceQuantization");
            chromaSubsampling = GetBoolProperty(fluxEffect, type, "ChromaSubsampling");

            // Compression Artifacts
            ringingArtifacts = GetFloatProperty(fluxEffect, type, "RingingArtifacts");
            mosquitoNoise = GetFloatProperty(fluxEffect, type, "MosquitoNoise");
            edgeSensitivity = GetFloatProperty(fluxEffect, type, "EdgeSensitivity");



            // Brightness Control
            noiseTransparency = GetFloatProperty(fluxEffect, type, "NoiseTransparency");
            maxNoiseBrightness = GetFloatProperty(fluxEffect, type, "MaxNoiseBrightness");
            brightnessThreshold = GetFloatProperty(fluxEffect, type, "BrightnessThreshold");
            brightAreaMasking = GetFloatProperty(fluxEffect, type, "BrightAreaMasking");

            // Utility controls
            oversharpening = GetFloatProperty(fluxEffect, type, "Oversharpening");
            visualizeMotionVectors = GetBoolProperty(fluxEffect, type, "PreviewMotionVectors");
            debugCompressionArtifacts = GetBoolProperty(fluxEffect, type, "DebugCompressionArtifacts");
        }

        /// <summary>
        /// Applies this preset to a Runtime Flux Effect
        /// </summary>
        public void ApplyToRuntime(object fluxEffect)
        {
            // Use reflection to avoid assembly reference issues
            if (fluxEffect == null) return;

            var type = fluxEffect.GetType();

            // Master controls
            SetFloatProperty(fluxEffect, type, "EffectIntensity", effectIntensity);
            SetBoolProperty(fluxEffect, type, "OnlyStenciled", onlyStenciled);

            // TRUE DATAMOSHING - Compression encoding
            SetFloatProperty(fluxEffect, type, "ColorCrunch", colorCrunch);
            SetIntProperty(fluxEffect, type, "Downscaling", downscaling);
            SetEnumProperty(fluxEffect, type, "BlockSize", (int)blockSize);
            SetBoolProperty(fluxEffect, type, "DontCrunchSkybox", dontCrunchSkybox);

            // TRUE DATAMOSHING - Motion vector reprojection
            SetFloatProperty(fluxEffect, type, "ReprojectBaseNoise", reprojectBaseNoise);
            SetFloatProperty(fluxEffect, type, "ReprojectBaseRerollSpeed", reprojectBaseRerollSpeed);
            SetFloatProperty(fluxEffect, type, "ReprojectLengthInfluence", reprojectLengthInfluence);

            // TRUE DATAMOSHING - Authentic compression artifacts
            SetFloatProperty(fluxEffect, type, "KeyframeResetRate", keyframeResetRate);
            SetFloatProperty(fluxEffect, type, "MotionVectorCorruption", motionVectorCorruption);
            SetFloatProperty(fluxEffect, type, "ErrorAccumulation", errorAccumulation);
            SetFloatProperty(fluxEffect, type, "DCTCorruption", dctCorruption);
            SetFloatProperty(fluxEffect, type, "ChromaCorruption", chromaCorruption);

            // CONSOLIDATED MOTION PROCESSING - New unified system
            SetFloatProperty(fluxEffect, type, "MotionAmplification", motionAmplification);
            SetFloatProperty(fluxEffect, type, "MotionThreshold", motionThreshold);
            SetFloatProperty(fluxEffect, type, "CameraObjectMotionBalance", cameraObjectMotionBalance);
            SetFloatProperty(fluxEffect, type, "MotionSmoothing", motionSmoothing);

            // ENHANCED VISUAL EFFECTS - Pixel flow & trailing
            SetFloatProperty(fluxEffect, type, "TrailIntensity", trailIntensity);
            SetFloatProperty(fluxEffect, type, "TrailSmoothness", trailSmoothness);
            SetFloatProperty(fluxEffect, type, "TrailPersistence", trailPersistence);
            SetFloatProperty(fluxEffect, type, "FlowSpread", flowSpread);

            // ENHANCED VISUAL EFFECTS - Artistic effects
            SetTextureProperty(fluxEffect, type, "CorruptionMask", corruptionMask);
            SetFloatProperty(fluxEffect, type, "GlitchTransition", glitchTransition);
            SetFloatProperty(fluxEffect, type, "FeedbackIntensity", feedbackIntensity);
            SetFloatProperty(fluxEffect, type, "MultiScaleCorruption", multiScaleCorruption);

            // JPEG Quality Control
            SetFloatProperty(fluxEffect, type, "JPEGQuality", jpegQuality);
            SetFloatProperty(fluxEffect, type, "LuminanceQuantization", luminanceQuantization);
            SetFloatProperty(fluxEffect, type, "ChrominanceQuantization", chrominanceQuantization);
            SetBoolProperty(fluxEffect, type, "ChromaSubsampling", chromaSubsampling);

            // Compression Artifacts
            SetFloatProperty(fluxEffect, type, "RingingArtifacts", ringingArtifacts);
            SetFloatProperty(fluxEffect, type, "MosquitoNoise", mosquitoNoise);
            SetFloatProperty(fluxEffect, type, "EdgeSensitivity", edgeSensitivity);



            // Brightness Control
            SetFloatProperty(fluxEffect, type, "NoiseTransparency", noiseTransparency);
            SetFloatProperty(fluxEffect, type, "MaxNoiseBrightness", maxNoiseBrightness);
            SetFloatProperty(fluxEffect, type, "BrightnessThreshold", brightnessThreshold);
            SetFloatProperty(fluxEffect, type, "BrightAreaMasking", brightAreaMasking);

            // Utility controls
            SetFloatProperty(fluxEffect, type, "Oversharpening", oversharpening);
            SetBoolProperty(fluxEffect, type, "PreviewMotionVectors", visualizeMotionVectors);
            SetBoolProperty(fluxEffect, type, "DebugCompressionArtifacts", debugCompressionArtifacts);
        }

#if URP_INSTALLED
        /// <summary>
        /// Creates a preset from current URP Flux Effect parameters
        /// </summary>
        public void CaptureFromURP(Stylo.Flux.Universal.FluxEffect fluxEffect)
        {
            if (fluxEffect == null) return;

            // Master controls
            effectIntensity = fluxEffect.EffectIntensity.value;
            onlyStenciled = fluxEffect.OnlyStenciled.value;

            // TRUE DATAMOSHING - Compression encoding
            colorCrunch = fluxEffect.ColorCrunch.value;
            downscaling = fluxEffect.Downscaling.value;
            blockSize = (FluxBlockSize)(int)fluxEffect.BlockSize.value;
            dontCrunchSkybox = fluxEffect.DontCrunchSkybox.value;

            // TRUE DATAMOSHING - Motion vector reprojection
            reprojectBaseNoise = fluxEffect.ReprojectBaseNoise.value;
            reprojectBaseRerollSpeed = fluxEffect.ReprojectBaseRerollSpeed.value;
            reprojectLengthInfluence = fluxEffect.ReprojectLengthInfluence.value;

            // TRUE DATAMOSHING - Authentic compression artifacts
            keyframeResetRate = fluxEffect.KeyframeResetRate.value;
            motionVectorCorruption = fluxEffect.MotionVectorCorruption.value;
            errorAccumulation = fluxEffect.ErrorAccumulation.value;
            dctCorruption = fluxEffect.DCTCorruption.value;
            chromaCorruption = fluxEffect.ChromaCorruption.value;

            // CONSOLIDATED MOTION PROCESSING - New unified system
            motionAmplification = fluxEffect.MotionAmplification.value;
            motionThreshold = fluxEffect.MotionThreshold.value;
            cameraObjectMotionBalance = fluxEffect.CameraObjectMotionBalance.value;
            motionSmoothing = fluxEffect.MotionSmoothing.value;

            // ENHANCED VISUAL EFFECTS - Pixel flow & trailing
            trailIntensity = fluxEffect.TrailIntensity.value;
            trailSmoothness = fluxEffect.TrailSmoothness.value;
            trailPersistence = fluxEffect.TrailPersistence.value;
            flowSpread = fluxEffect.FlowSpread.value;

            // ENHANCED VISUAL EFFECTS - Artistic effects
            corruptionMask = fluxEffect.CorruptionMask.value as Texture2D;
            glitchTransition = fluxEffect.GlitchTransition.value;
            feedbackIntensity = fluxEffect.FeedbackIntensity.value;
            multiScaleCorruption = fluxEffect.MultiScaleCorruption.value;

            // JPEG Quality Control
            jpegQuality = fluxEffect.JPEGQuality.value;
            luminanceQuantization = fluxEffect.LuminanceQuantization.value;
            chrominanceQuantization = fluxEffect.ChrominanceQuantization.value;
            chromaSubsampling = fluxEffect.ChromaSubsampling.value;

            // Compression Artifacts
            ringingArtifacts = fluxEffect.RingingArtifacts.value;
            mosquitoNoise = fluxEffect.MosquitoNoise.value;
            edgeSensitivity = fluxEffect.EdgeSensitivity.value;



            // Brightness Control
            noiseTransparency = fluxEffect.NoiseTransparency.value;
            maxNoiseBrightness = fluxEffect.MaxNoiseBrightness.value;
            brightnessThreshold = fluxEffect.BrightnessThreshold.value;
            brightAreaMasking = fluxEffect.BrightAreaMasking.value;

            // Utility controls
            oversharpening = fluxEffect.Oversharpening.value;
            visualizeMotionVectors = fluxEffect.VisualizeMotionVectors.value;
            debugCompressionArtifacts = fluxEffect.DebugCompressionArtifacts.value;
        }

        /// <summary>
        /// Applies this preset to a URP Flux Effect
        /// </summary>
        public void ApplyToURP(Stylo.Flux.Universal.FluxEffect fluxEffect)
        {
            if (fluxEffect == null) return;

            // Master controls
            fluxEffect.EffectIntensity.value = effectIntensity;
            fluxEffect.OnlyStenciled.value = onlyStenciled;

            // TRUE DATAMOSHING - Compression encoding
            fluxEffect.ColorCrunch.value = colorCrunch;
            fluxEffect.Downscaling.value = downscaling;
            fluxEffect.BlockSize.value = (Stylo.Flux.Universal.FluxEffect._BlockSize)(int)blockSize;
            fluxEffect.DontCrunchSkybox.value = dontCrunchSkybox;

            // TRUE DATAMOSHING - Motion vector reprojection
            fluxEffect.ReprojectBaseNoise.value = reprojectBaseNoise;
            fluxEffect.ReprojectBaseRerollSpeed.value = reprojectBaseRerollSpeed;
            fluxEffect.ReprojectLengthInfluence.value = reprojectLengthInfluence;

            // TRUE DATAMOSHING - Authentic compression artifacts
            fluxEffect.KeyframeResetRate.value = keyframeResetRate;
            fluxEffect.MotionVectorCorruption.value = motionVectorCorruption;
            fluxEffect.ErrorAccumulation.value = errorAccumulation;
            fluxEffect.DCTCorruption.value = dctCorruption;
            fluxEffect.ChromaCorruption.value = chromaCorruption;

            // CONSOLIDATED MOTION PROCESSING - New unified system
            fluxEffect.MotionAmplification.value = motionAmplification;
            fluxEffect.MotionThreshold.value = motionThreshold;
            fluxEffect.CameraObjectMotionBalance.value = cameraObjectMotionBalance;
            fluxEffect.MotionSmoothing.value = motionSmoothing;

            // ENHANCED VISUAL EFFECTS - Pixel flow & trailing
            fluxEffect.TrailIntensity.value = trailIntensity;
            fluxEffect.TrailSmoothness.value = trailSmoothness;
            fluxEffect.TrailPersistence.value = trailPersistence;
            fluxEffect.FlowSpread.value = flowSpread;

            // ENHANCED VISUAL EFFECTS - Artistic effects
            fluxEffect.CorruptionMask.value = corruptionMask;
            fluxEffect.GlitchTransition.value = glitchTransition;
            fluxEffect.FeedbackIntensity.value = feedbackIntensity;
            fluxEffect.MultiScaleCorruption.value = multiScaleCorruption;

            // JPEG Quality Control
            fluxEffect.JPEGQuality.value = jpegQuality;
            fluxEffect.LuminanceQuantization.value = luminanceQuantization;
            fluxEffect.ChrominanceQuantization.value = chrominanceQuantization;
            fluxEffect.ChromaSubsampling.value = chromaSubsampling;

            // Compression Artifacts
            fluxEffect.RingingArtifacts.value = ringingArtifacts;
            fluxEffect.MosquitoNoise.value = mosquitoNoise;
            fluxEffect.EdgeSensitivity.value = edgeSensitivity;



            // Brightness Control
            fluxEffect.NoiseTransparency.value = noiseTransparency;
            fluxEffect.MaxNoiseBrightness.value = maxNoiseBrightness;
            fluxEffect.BrightnessThreshold.value = brightnessThreshold;
            fluxEffect.BrightAreaMasking.value = brightAreaMasking;

            // Utility controls
            fluxEffect.Oversharpening.value = oversharpening;
            fluxEffect.VisualizeMotionVectors.value = visualizeMotionVectors;
            fluxEffect.DebugCompressionArtifacts.value = debugCompressionArtifacts;
        }
#endif

        // Reflection helper methods to avoid assembly reference issues
        private float GetFloatProperty(object obj, System.Type type, string propertyName)
        {
            var prop = type.GetProperty(propertyName);
            return prop != null ? (float)prop.GetValue(obj) : 0f;
        }

        private int GetIntProperty(object obj, System.Type type, string propertyName)
        {
            var prop = type.GetProperty(propertyName);
            return prop != null ? (int)prop.GetValue(obj) : 0;
        }

        private bool GetBoolProperty(object obj, System.Type type, string propertyName)
        {
            var prop = type.GetProperty(propertyName);
            return prop != null ? (bool)prop.GetValue(obj) : false;
        }

        private Texture2D GetTextureProperty(object obj, System.Type type, string propertyName)
        {
            var prop = type.GetProperty(propertyName);
            return prop != null ? prop.GetValue(obj) as Texture2D : null;
        }

        private void SetFloatProperty(object obj, System.Type type, string propertyName, float value)
        {
            var prop = type.GetProperty(propertyName);
            if (prop != null && prop.CanWrite) prop.SetValue(obj, value);
        }

        private void SetIntProperty(object obj, System.Type type, string propertyName, int value)
        {
            var prop = type.GetProperty(propertyName);
            if (prop != null && prop.CanWrite) prop.SetValue(obj, value);
        }

        private void SetBoolProperty(object obj, System.Type type, string propertyName, bool value)
        {
            var prop = type.GetProperty(propertyName);
            if (prop != null && prop.CanWrite) prop.SetValue(obj, value);
        }

        private void SetTextureProperty(object obj, System.Type type, string propertyName, Texture2D value)
        {
            var prop = type.GetProperty(propertyName);
            if (prop != null && prop.CanWrite) prop.SetValue(obj, value);
        }

        private void SetEnumProperty(object obj, System.Type type, string propertyName, int value)
        {
            var prop = type.GetProperty(propertyName);
            if (prop != null && prop.CanWrite)
            {
                var enumValue = System.Enum.ToObject(prop.PropertyType, value);
                prop.SetValue(obj, enumValue);
            }
        }

#if UNITY_EDITOR
        /// <summary>
        /// Applies this preset directly to a Volume Profile asset
        /// </summary>
#if URP_INSTALLED
        public void ApplyToVolumeProfile(VolumeProfile volumeProfile)
        {
            if (volumeProfile == null) return;

            // Get or add Flux effect to the volume profile
            if (volumeProfile.TryGet<Stylo.Flux.Universal.FluxEffect>(out var existingFlux))
            {
                // Apply to existing effect
                Undo.RecordObject(volumeProfile, "Apply Flux Preset");
                ApplyToURP(existingFlux);
                EditorUtility.SetDirty(volumeProfile);
            }
            else
            {
                // Add new Flux effect and apply preset
                Undo.RecordObject(volumeProfile, "Add Flux Effect and Apply Preset");
                var newFlux = volumeProfile.Add<Stylo.Flux.Universal.FluxEffect>();
                ApplyToURP(newFlux);
                EditorUtility.SetDirty(volumeProfile);
            }
        }
#endif

        /// <summary>
        /// Applies this preset directly to a Flux Effect component
        /// </summary>
        public void ApplyToFluxComponent(object fluxEffect)
        {
            if (fluxEffect == null) return;

            // Cast to UnityEngine.Object for Unity editor methods
            var unityObject = fluxEffect as UnityEngine.Object;
            if (unityObject == null) return;

            Undo.RecordObject(unityObject, "Apply Flux Preset");
            ApplyToRuntime(fluxEffect);
            EditorUtility.SetDirty(unityObject);
        }

        /// <summary>
        /// Creates a preset from a Volume Profile's Flux Effect
        /// </summary>
#if URP_INSTALLED
        public static FluxPreset CreateFromVolumeProfile(VolumeProfile volumeProfile, string presetName = "New Flux Preset")
        {
            if (volumeProfile == null) return null;

            if (volumeProfile.TryGet<Stylo.Flux.Universal.FluxEffect>(out var fluxEffect))
            {
                var preset = CreateInstance<FluxPreset>();
                preset.presetName = presetName;
                preset.CaptureFromURP(fluxEffect);
                return preset;
            }

            return null;
        }
#endif

        /// <summary>
        /// Creates a preset from a Flux Effect component
        /// </summary>
        public static FluxPreset CreateFromFluxComponent(object fluxEffect, string presetName = "New Flux Preset")
        {
            if (fluxEffect == null) return null;

            var preset = CreateInstance<FluxPreset>();
            preset.presetName = presetName;
            preset.CaptureFromRuntime(fluxEffect);
            return preset;
        }
#endif
    }
}
