%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e78934177bebe0545ab566bb787b0282, type: 3}
  m_Name: Readme
  m_EditorClassIdentifier: 
  Icon: {fileID: 2800000, guid: ecb46f49df4e23241a1d4f143d520fb2, type: 3}
  Title: Scale Independent Tile
  Description:
    Heading: 
    Text: This sample dynamically sets texture tiling to be independent of surface
      scale.
    LinkText: 
    Url: 
  PropertiesHeader:
    Heading: Properties
    Text: 
    LinkText: 
    Url: 
  Properties:
  - Heading: Tiling Scale
    Text: Amount of tiling to have, independently of surface scale.
    LinkText: 
    Url: 
  - Heading: Tiling Offset
    Text: Offset value of final calculated texture coordinates.
    LinkText: 
    Url: 
  - Heading: Albedo
    Text: Texture to be sampled and applied to surface.
    LinkText: 
    Url: 
  AdditionalProperties: []
  AdditionalScripts: []
  LoadedLayout: 0
  RPType: 3
