%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Water Simple 02
  m_Shader: {fileID: 4800000, guid: 139c2b3e722246db9ac3a0bf8cecd09e, type: 3}
  m_ValidKeywords: []
  m_InvalidKeywords: []
  m_LightmapFlags: 5
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _Foam:
        m_Texture: {fileID: 2800000, guid: 4d85556227fd4d5a8f034d10f4261675, type: 3}
        m_Scale: {x: 40, y: 40}
        m_Offset: {x: 0, y: 0}
    - _Normal:
        m_Texture: {fileID: 2800000, guid: fb6566c21f717904f83743a5a76dd0b0, type: 3}
        m_Scale: {x: 20, y: 20}
        m_Offset: {x: 0, y: 0}
    - _Normal2:
        m_Texture: {fileID: 2800000, guid: fd8b85b15dfe0ad4cbbc5613a12722c6, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TextureSample0:
        m_Texture: {fileID: 2800000, guid: fd8b85b15dfe0ad4cbbc5613a12722c6, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TextureSample1:
        m_Texture: {fileID: 2800000, guid: e4ec2a92d2183294d9d9be1f20a1fcdf, type: 3}
        m_Scale: {x: 10, y: 10}
        m_Offset: {x: 0, y: 0}
    - _WaterNormal:
        m_Texture: {fileID: 2800000, guid: e901190044a54e7186ca567492f42131, type: 3}
        m_Scale: {x: 20, y: 20}
        m_Offset: {x: 0, y: 0}
    - _texcoord:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AlphaCutoff: 0.5
    - _Depth: 0.98
    - _DepthFalloff: -13
    - _Distortion: 0.46
    - _EnvironmentReflections: 1
    - _Float0: 0.05
    - _Float1: 25
    - _Float3: 0.9
    - _Foam: 1.79
    - _FoamDepth: 0.92
    - _FoamFalloff: -58.2
    - _FoamPower: 4
    - _FoamSmoothness: 0
    - _FoamSpecular: 0
    - _Foamfalloff: -4
    - _MaskClipValue: 0.5
    - _NormalScale: 0.1
    - _QueueControl: 0
    - _QueueOffset: 0
    - _ReceiveShadows: 1
    - _Smoothness: 0.9
    - _Specular: 0.1
    - _SpecularHighlights: 1
    - _TessMax: 25
    - _TessMin: 10
    - _TessValue: 16
    - _WaterDepth: 1
    - _WaterFalloff: -4.72
    - _WaterSmoothness: 1
    - _WaterSpecular: 0.1
    - _WavesAmount: 1.98
    - _WavesAmplitude: 0.01
    - __dirty: 0
    m_Colors:
    - _Color0: {r: 0, g: 0.6749488, b: 0.8088235, a: 0}
    - _Color1: {r: 0, g: 0.048731543, b: 0.22794023, a: 0}
    - _DeepColor: {r: 0, g: 0.043101594, b: 0.24999812, a: 0}
    - _EmissionColor: {r: 1, g: 1, b: 1, a: 1}
    - _ShalowColor: {r: 0, g: 0.80882317, b: 0.8088235, a: 0}
    - _SpecColor: {r: 1, g: 1, b: 1, a: 1}
  m_BuildTextureStacks: []
