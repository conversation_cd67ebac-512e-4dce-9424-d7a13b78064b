%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12001, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: BuildReportWindow
  m_EditorClassIdentifier: 
  m_Font: {fileID: 10102, guid: 0000000000000000e000000000000000, type: 0}
  m_box:
    m_Name: box
    m_Normal:
      m_Background: {fileID: 2575269581626704993, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds:
      - {fileID: -8126836811217315462, guid: 0000000000000000d000000000000000, type: 0}
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 3
      m_Right: 3
      m_Top: 2
      m_Bottom: 2
    m_Margin:
      m_Left: 4
      m_Right: 4
      m_Top: 4
      m_Bottom: 4
    m_Padding:
      m_Left: 3
      m_Right: 3
      m_Top: 3
      m_Bottom: 3
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 1
    m_WordWrap: 1
    m_RichText: 0
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  m_button:
    m_Name: button
    m_Normal:
      m_Background: {fileID: -573041650897247223, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds:
      - {fileID: -1537457205435906773, guid: 0000000000000000d000000000000000, type: 0}
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: -7527060558648309217, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds:
      - {fileID: -8766172725880940643, guid: 0000000000000000d000000000000000, type: 0}
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 7832598784815925287, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds:
      - {fileID: -9059002882264723198, guid: 0000000000000000d000000000000000, type: 0}
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_OnNormal:
      m_Background: {fileID: -4454209017672384243, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds:
      - {fileID: -4993635991501620529, guid: 0000000000000000d000000000000000, type: 0}
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_OnActive:
      m_Background: {fileID: 4047951448802137905, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds:
      - {fileID: 7382603045041641420, guid: 0000000000000000d000000000000000, type: 0}
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_OnFocused:
      m_Background: {fileID: 8556163245987529883, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds:
      - {fileID: 8718812295543890339, guid: 0000000000000000d000000000000000, type: 0}
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 6
      m_Right: 6
      m_Top: 4
      m_Bottom: 4
    m_Margin:
      m_Left: 4
      m_Right: 4
      m_Top: 3
      m_Bottom: 3
    m_Padding:
      m_Left: 6
      m_Right: 6
      m_Top: 2
      m_Bottom: 3
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 1
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 0
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  m_toggle:
    m_Name: toggle
    m_Normal:
      m_Background: {fileID: -5335821736339326178, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: -5440736679484030619, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 8488476462167042113, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: -6861823045970851139, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: -6998870504307324776, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: -2043049995710802033, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 15
      m_Right: 0
      m_Top: 13
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 2
      m_Bottom: 2
    m_Padding:
      m_Left: 5
      m_Right: 5
      m_Top: 2
      m_Bottom: 3
    m_Overflow:
      m_Left: -3
      m_Right: 0
      m_Top: -3
      m_Bottom: 1
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 0
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 15, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  m_label:
    m_Name: label
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 0.023529412}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 0.023529412}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 0.023529412}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 4
      m_Right: 4
      m_Top: 2
      m_Bottom: 2
    m_Padding:
      m_Left: 2
      m_Right: 2
      m_Top: 1
      m_Bottom: 2
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 1
    m_RichText: 0
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  m_textField:
    m_Name: textfield
    m_Normal:
      m_Background: {fileID: 2597227618725028271, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds:
      - {fileID: -2355377567497197369, guid: 0000000000000000d000000000000000, type: 0}
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: -7902305245580594835, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds:
      - {fileID: -7480287054991643601, guid: 0000000000000000d000000000000000, type: 0}
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 3
      m_Right: 3
      m_Top: 3
      m_Bottom: 3
    m_Margin:
      m_Left: 4
      m_Right: 4
      m_Top: 2
      m_Bottom: 2
    m_Padding:
      m_Left: 3
      m_Right: 3
      m_Top: 1
      m_Bottom: 2
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 0
    m_TextClipping: 1
    m_ImagePosition: 3
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  m_textArea:
    m_Name: textarea
    m_Normal:
      m_Background: {fileID: 11024, guid: 0000000000000000e000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.9019608, g: 0.9019608, b: 0.9019608, a: 1}
    m_Hover:
      m_Background: {fileID: 11026, guid: 0000000000000000e000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.79999995, g: 0.79999995, b: 0.79999995, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 11025, guid: 0000000000000000e000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 4
      m_Right: 4
      m_Top: 4
      m_Bottom: 4
    m_Margin:
      m_Left: 4
      m_Right: 4
      m_Top: 4
      m_Bottom: 4
    m_Padding:
      m_Left: 3
      m_Right: 3
      m_Top: 3
      m_Bottom: 3
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 1
    m_RichText: 1
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  m_window:
    m_Name: window
    m_Normal:
      m_Background: {fileID: 11023, guid: 0000000000000000e000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 11022, guid: 0000000000000000e000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 8
      m_Right: 8
      m_Top: 18
      m_Bottom: 8
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 10
      m_Right: 10
      m_Top: 20
      m_Bottom: 10
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 1
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: -18}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  m_horizontalSlider:
    m_Name: horizontalslider
    m_Normal:
      m_Background: {fileID: 11009, guid: 0000000000000000e000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 3
      m_Right: 3
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 4
      m_Right: 4
      m_Top: 4
      m_Bottom: 4
    m_Padding:
      m_Left: -1
      m_Right: -1
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: -2
      m_Bottom: -3
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 1
    m_ImagePosition: 2
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 12
    m_StretchWidth: 1
    m_StretchHeight: 0
  m_horizontalSliderThumb:
    m_Name: horizontalsliderthumb
    m_Normal:
      m_Background: {fileID: 11011, guid: 0000000000000000e000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 11012, guid: 0000000000000000e000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 11010, guid: 0000000000000000e000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 4
      m_Right: 4
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 7
      m_Right: 7
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: -1
      m_Right: -1
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 1
    m_ImagePosition: 2
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 12
    m_StretchWidth: 1
    m_StretchHeight: 0
  m_verticalSlider:
    m_Name: verticalslider
    m_Normal:
      m_Background: {fileID: 11021, guid: 0000000000000000e000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 3
      m_Bottom: 3
    m_Margin:
      m_Left: 4
      m_Right: 4
      m_Top: 4
      m_Bottom: 4
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: -1
      m_Bottom: -1
    m_Overflow:
      m_Left: -2
      m_Right: -3
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 12
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 1
  m_verticalSliderThumb:
    m_Name: verticalsliderthumb
    m_Normal:
      m_Background: {fileID: 11011, guid: 0000000000000000e000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 11012, guid: 0000000000000000e000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 11010, guid: 0000000000000000e000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 7
      m_Bottom: 7
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: -1
      m_Bottom: -1
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 12
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 1
  m_horizontalScrollbar:
    m_Name: horizontalscrollbar
    m_Normal:
      m_Background: {fileID: -3273041606263085236, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds:
      - {fileID: -311698570160889163, guid: 0000000000000000d000000000000000, type: 0}
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 25
      m_Right: 25
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: -1
      m_Right: -1
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 1
      m_Right: 1
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 0
    m_TextClipping: 1
    m_ImagePosition: 2
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 15
    m_StretchWidth: 1
    m_StretchHeight: 0
  m_horizontalScrollbarThumb:
    m_Name: horizontalscrollbarthumb
    m_Normal:
      m_Background: {fileID: -7716058669073582569, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds:
      - {fileID: 8639621063432762522, guid: 0000000000000000d000000000000000, type: 0}
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 8
      m_Right: 8
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 8
      m_Right: 8
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 0
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 15
    m_StretchWidth: 1
    m_StretchHeight: 0
  m_horizontalScrollbarLeftButton:
    m_Name: horizontalscrollbarleftbutton
    m_Normal:
      m_Background: {fileID: 4476514712964175617, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds:
      - {fileID: -7917270662258052987, guid: 0000000000000000d000000000000000, type: 0}
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 2884324555085707582, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds:
      - {fileID: 7509333680719945740, guid: 0000000000000000d000000000000000, type: 0}
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 8
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 0
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 17
    m_FixedHeight: 15
    m_StretchWidth: 1
    m_StretchHeight: 0
  m_horizontalScrollbarRightButton:
    m_Name: horizontalscrollbarrightbutton
    m_Normal:
      m_Background: {fileID: -1802176199048881894, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds:
      - {fileID: -1719773835037083180, guid: 0000000000000000d000000000000000, type: 0}
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 7059179968116174260, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds:
      - {fileID: 7510343993111087507, guid: 0000000000000000d000000000000000, type: 0}
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 8
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 0
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 17.24739
    m_FixedHeight: 15
    m_StretchWidth: 1
    m_StretchHeight: 0
  m_verticalScrollbar:
    m_Name: verticalscrollbar
    m_Normal:
      m_Background: {fileID: 488918262070282077, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds:
      - {fileID: -3927972358972204458, guid: 0000000000000000d000000000000000, type: 0}
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 9
      m_Bottom: 9
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: -1
      m_Bottom: -1
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 1
      m_Bottom: 1
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 0
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 15
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  m_verticalScrollbarThumb:
    m_Name: verticalscrollbarthumb
    m_Normal:
      m_Background: {fileID: 944833255956038975, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds:
      - {fileID: 4570962278689354513, guid: 0000000000000000d000000000000000, type: 0}
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 8
      m_Bottom: 8
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 10
      m_Bottom: 10
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 0
    m_TextClipping: 1
    m_ImagePosition: 2
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 15
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 1
  m_verticalScrollbarUpButton:
    m_Name: verticalscrollbarupbutton
    m_Normal:
      m_Background: {fileID: -2655953919643799188, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds:
      - {fileID: -1797764562729272291, guid: 0000000000000000d000000000000000, type: 0}
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: -7354372766510949456, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds:
      - {fileID: 9141456082652879494, guid: 0000000000000000d000000000000000, type: 0}
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 8
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 0
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 15
    m_FixedHeight: 17
    m_StretchWidth: 1
    m_StretchHeight: 0
  m_verticalScrollbarDownButton:
    m_Name: verticalscrollbardownbutton
    m_Normal:
      m_Background: {fileID: -7119383920812324710, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds:
      - {fileID: -8460701622354431290, guid: 0000000000000000d000000000000000, type: 0}
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: -1371287000565055330, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds:
      - {fileID: -4129062762289497750, guid: 0000000000000000d000000000000000, type: 0}
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 8
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 0
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 15
    m_FixedHeight: 17
    m_StretchWidth: 1
    m_StretchHeight: 0
  m_ScrollView:
    m_Name: scrollview
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  m_CustomStyles:
  - m_Name: MiniButton
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 4
      m_Right: 4
      m_Top: 4
      m_Bottom: 4
    m_Margin:
      m_Left: 2
      m_Right: 2
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 12
      m_Right: 12
      m_Top: 1
      m_Bottom: 2
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 1
      m_Bottom: 1
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: -1}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: DebugOverlay
    m_Normal:
      m_Background: {fileID: 2800000, guid: c26d7a4f0daeb3b48973a2a5e2ccdcee, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 4
      m_Right: 4
      m_Top: 4
      m_Bottom: 4
    m_Margin:
      m_Left: 2
      m_Right: 2
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 4
      m_Right: 4
      m_Top: 3
      m_Bottom: 3
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: LabelSingleLine
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 4
      m_Right: 4
      m_Top: 2
      m_Bottom: 2
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 2
      m_Bottom: 2
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 3
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: Big1
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 10
      m_Bottom: 4
    m_Padding:
      m_Left: 0
      m_Right: 20
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 24
    m_FontStyle: 1
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: -3, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: TextInfo
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 20
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 16
    m_FontStyle: 1
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: -3, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: TinyHelp
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.16037738, g: 0.16037738, b: 0.16037738, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 2
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 11
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 1
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: Big2
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 32
    m_FontStyle: 1
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 3, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: Big3
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 17
    m_FontStyle: 1
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 3, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: Title
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 5
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 32
    m_FontStyle: 1
    m_Alignment: 1
    m_WordWrap: 1
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: Subtitle
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 11
    m_FontStyle: 0
    m_Alignment: 1
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: Header3
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 4
      m_Right: 4
      m_Top: 2
      m_Bottom: 2
    m_Padding:
      m_Left: 2
      m_Right: 2
      m_Top: 1
      m_Bottom: 2
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 1
    m_Alignment: 0
    m_WordWrap: 1
    m_RichText: 1
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: Header2
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 4
      m_Right: 4
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 14
    m_FontStyle: 0
    m_Alignment: 6
    m_WordWrap: 1
    m_RichText: 1
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: Header2Bold
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 4
      m_Right: 4
      m_Top: 4
      m_Bottom: 4
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 3
      m_Bottom: 3
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 6
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: Text
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 12
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 1
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: TextSelected
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 12
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 1
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: TextNoWrap
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 12
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: Text-Boxed
    m_Normal:
      m_Background: {fileID: 2800000, guid: 3051c49201420ce4eae4692f920675fc, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 3
      m_Right: 3
      m_Top: 3
      m_Bottom: 3
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 5
      m_Right: 5
      m_Top: 5
      m_Bottom: 5
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 1
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: Tooltip
    m_Normal:
      m_Background: {fileID: 2800000, guid: 828d9b22190dfe746ae229893d0ed85e, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 5
      m_Right: 9
      m_Top: 5
      m_Bottom: 9
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 5
      m_Right: 5
      m_Top: 5
      m_Bottom: 5
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 1
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: TooltipText
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 2
      m_Right: 2
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 1
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: TextField-Search
    m_Normal:
      m_Background: {fileID: 2800000, guid: ec577dc4a2bb0f24b9b056ed15024f75, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 17
      m_Right: 0
      m_Top: 4
      m_Bottom: 4
    m_Margin:
      m_Left: 3
      m_Right: 0
      m_Top: 3
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 1
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 11
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 0
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 17, y: 1}
    m_FixedWidth: 0
    m_FixedHeight: 18
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: TextField-Search-Text
    m_Normal:
      m_Background: {fileID: 2800000, guid: 7bab1b503eb28b7498b376aaef0c4f90, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 4
      m_Bottom: 4
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 3
      m_Bottom: 0
    m_Padding:
      m_Left: 2
      m_Right: 0
      m_Top: 1
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 11
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 0
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 1}
    m_FixedWidth: 0
    m_FixedHeight: 18
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: TextField-Search-DropDown
    m_Normal:
      m_Background: {fileID: 2800000, guid: bdfae28b47e3a5444b4490d930fd7a6f, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 2800000, guid: 8a51a690a4ea0ef4aa9394c0498e35b0, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 2800000, guid: 8a51a690a4ea0ef4aa9394c0498e35b0, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 24
      m_Right: 0
      m_Top: 4
      m_Bottom: 4
    m_Margin:
      m_Left: 3
      m_Right: 0
      m_Top: 3
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 1
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 11
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 0
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 1}
    m_FixedWidth: 24
    m_FixedHeight: 18
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: TextField-Search-ClearButton
    m_Normal:
      m_Background: {fileID: 2800000, guid: 052aa522a7b5fb646897414b72655233, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 2800000, guid: 6f3ea1a6420b7b54ea10732fdfe9eeb5, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 3
      m_Top: 3
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 1
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 11
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 0
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 16
    m_FixedHeight: 18
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: Version
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 5
      m_Top: 5
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 10102, guid: 0000000000000000e000000000000000, type: 0}
    m_FontSize: 11
    m_FontStyle: 0
    m_Alignment: 2
    m_WordWrap: 1
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: TextBold
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 12
    m_FontStyle: 1
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: ToolbarLeft
    m_Normal:
      m_Background: {fileID: 2800000, guid: 886f7fba07047d14b9b79b864468f9dc, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 2800000, guid: 71a8ca93cd5adf0479b0bbeca1507fc8, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 2800000, guid: fa8b703aedc9e2b4288eeff8b4022c05, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 2800000, guid: 16111157c6e49234e923ea8f4e450d37, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 5
      m_Right: 2
      m_Top: 2
      m_Bottom: 2
    m_Margin:
      m_Left: 3
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 5
      m_Right: 4
      m_Top: 4
      m_Bottom: 4
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 9
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 1
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: ToolbarMiddle
    m_Normal:
      m_Background: {fileID: 2800000, guid: 147e6ec51a8432c45a10b5b4c7dde820, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 2800000, guid: 35fe22e2115a5464d929ad10166b9b4c, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 2800000, guid: 759d1ac2eca8fcd4f89295dd9b10a613, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 2800000, guid: 7af8692d98dd6db4486da44329739efc, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 2
      m_Right: 2
      m_Top: 2
      m_Bottom: 2
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 4
      m_Right: 6
      m_Top: 4
      m_Bottom: 4
    m_Overflow:
      m_Left: 1
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 9
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 1
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: ToolbarRight
    m_Normal:
      m_Background: {fileID: 2800000, guid: 6a7d0f391f1753c479ae5e2441c9a6ac, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 2800000, guid: f8ef87e87e01a73458343e226c7264a3, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 2800000, guid: 53dac5eeceae7564ea274b0696ae5fd1, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 2800000, guid: 8d035465503f51a4fac1d5e80295a7f8, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 2
      m_Right: 5
      m_Top: 2
      m_Bottom: 2
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 4
      m_Right: 8
      m_Top: 4
      m_Bottom: 4
    m_Overflow:
      m_Left: 2
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 9
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 1
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: RadioLeft
    m_Normal:
      m_Background: {fileID: 2800000, guid: 886f7fba07047d14b9b79b864468f9dc, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 2800000, guid: 71a8ca93cd5adf0479b0bbeca1507fc8, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 2800000, guid: fa8b703aedc9e2b4288eeff8b4022c05, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 2800000, guid: 16111157c6e49234e923ea8f4e450d37, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 5
      m_Right: 2
      m_Top: 2
      m_Bottom: 2
    m_Margin:
      m_Left: 3
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 5
      m_Right: 4
      m_Top: 4
      m_Bottom: 4
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 11
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 1
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: RadioMiddle
    m_Normal:
      m_Background: {fileID: 2800000, guid: 147e6ec51a8432c45a10b5b4c7dde820, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 2800000, guid: 35fe22e2115a5464d929ad10166b9b4c, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 2800000, guid: 759d1ac2eca8fcd4f89295dd9b10a613, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 2800000, guid: 7af8692d98dd6db4486da44329739efc, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 2
      m_Right: 2
      m_Top: 2
      m_Bottom: 2
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 4
      m_Right: 6
      m_Top: 4
      m_Bottom: 4
    m_Overflow:
      m_Left: 1
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 11
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 1
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: RadioRight
    m_Normal:
      m_Background: {fileID: 2800000, guid: 6a7d0f391f1753c479ae5e2441c9a6ac, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 2800000, guid: f8ef87e87e01a73458343e226c7264a3, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 2800000, guid: 53dac5eeceae7564ea274b0696ae5fd1, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 2800000, guid: 8d035465503f51a4fac1d5e80295a7f8, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 2
      m_Right: 5
      m_Top: 2
      m_Bottom: 2
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 4
      m_Right: 8
      m_Top: 4
      m_Bottom: 4
    m_Overflow:
      m_Left: 2
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 11
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 1
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: TabLeft
    m_Normal:
      m_Background: {fileID: 2800000, guid: 373f51c87086b02459607308775e78b1, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 2800000, guid: 56b54d9b7e99bcc479c4823c1317c447, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 2800000, guid: 215aeacc8dd97ea409ba40042a72ebb9, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 2800000, guid: 4eee8a1e3ad70bd45ad389a03dfe5359, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 5
      m_Right: 2
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 3
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 4
      m_Right: 5
      m_Top: 6
      m_Bottom: 2
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: TabMiddle
    m_Normal:
      m_Background: {fileID: 2800000, guid: 5e13d30c0c9c75040bcdfbffcfb53988, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 2800000, guid: 48046c6ee29acef4ea8a4ce350a9c24b, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 2800000, guid: 3874401684fdc974695d1605db6560f9, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 2800000, guid: 7ee779862f08a1348901b65417c54188, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 5
      m_Right: 2
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 6
      m_Bottom: 2
    m_Overflow:
      m_Left: 1
      m_Right: 1
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: TabRight
    m_Normal:
      m_Background: {fileID: 2800000, guid: 20a776457908cc845babf45bdb476646, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 2800000, guid: 1d7941a993179f9469860cf3457f7234, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 2800000, guid: f32cc235fdcbbec4bbc2834d837b58fd, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 2800000, guid: 24d235eb01f65e941ac4dfcb7fd57098, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 2
      m_Right: 5
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 3
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 4
      m_Right: 7
      m_Top: 6
      m_Bottom: 2
    m_Overflow:
      m_Left: 2
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: StatusBarBg
    m_Normal:
      m_Background: {fileID: 2800000, guid: d349f3cb6d91cff46a973dce538ab51c, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 2
      m_Right: 2
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 7
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 10102, guid: 0000000000000000e000000000000000, type: 0}
    m_FontSize: 12
    m_FontStyle: 0
    m_Alignment: 3
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 4, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 1
  - m_Name: StatusBarLabel
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 2
      m_Right: 2
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 5
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 10102, guid: 0000000000000000e000000000000000, type: 0}
    m_FontSize: 12
    m_FontStyle: 0
    m_Alignment: 3
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 4, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 1
  - m_Name: TopBarBg
    m_Normal:
      m_Background: {fileID: 2800000, guid: 9df4d45783545b84b8ce5b9e9701c5f2, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 9999
      m_Right: 9999
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 10
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 23
    m_StretchWidth: 1
    m_StretchHeight: 1
  - m_Name: TopBarLabel
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 4
      m_Right: 4
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 5
      m_Right: 3
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 11
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 1
  - m_Name: TopBarButton
    m_Normal:
      m_Background: {fileID: 2800000, guid: 9df4d45783545b84b8ce5b9e9701c5f2, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 2800000, guid: 11acdc56b262a4641b40b01ef5f15c37, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 2800000, guid: 38ef303dece76ef45910e97f04f5e348, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 2800000, guid: 1c034283ef2550f4eb6858e32222c693, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 4
      m_Right: 4
      m_Top: 4
      m_Bottom: 4
    m_Margin:
      m_Left: -1
      m_Right: -1
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 5
      m_Right: 5
      m_Top: 3
      m_Bottom: 3
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 11
    m_FontStyle: 0
    m_Alignment: 3
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 1
  - m_Name: TopBarPopup
    m_Normal:
      m_Background: {fileID: 2800000, guid: 721f56281f947524eba4a280e2a1b050, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 2800000, guid: b1e01f571280a5c4b828236cd81dc58d, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 4
      m_Right: 10
      m_Top: 4
      m_Bottom: 4
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 5
      m_Right: 5
      m_Top: 3
      m_Bottom: 3
    m_Overflow:
      m_Left: 0
      m_Right: 5
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 11
    m_FontStyle: 0
    m_Alignment: 3
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 1
  - m_Name: ListColumnHeader
    m_Normal:
      m_Background: {fileID: 2800000, guid: 45dcc34572cb4234fbef9600252cfa5b, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 2800000, guid: ed75aa4653665b543af4301df7ab0aa6, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.1567164, g: 0.1567164, b: 0.1567164, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 2
      m_Right: 2
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 5
      m_Right: 0
      m_Top: 3
      m_Bottom: 3
    m_Overflow:
      m_Left: 0
      m_Right: 2
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 11
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: ListColumnHeader-Asc
    m_Normal:
      m_Background: {fileID: 2800000, guid: d21040d4186bf9546aafc2db57600f8a, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 2800000, guid: ec6253a98d8bee64789ebe15c586ab49, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.1567164, g: 0.1567164, b: 0.1567164, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 2800000, guid: 59fd6b4691d8a154ca5f24122b2183d3, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 2800000, guid: 163d65b61ac37404f9bea771fe477056, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 2
      m_Right: 15
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 5
      m_Right: 0
      m_Top: 3
      m_Bottom: 3
    m_Overflow:
      m_Left: 0
      m_Right: 2
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 11
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 1
    m_RichText: 1
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: ListColumnHeader-Desc
    m_Normal:
      m_Background: {fileID: 2800000, guid: f4306844cc37ee44e9b8867e960b025d, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 2800000, guid: 93968e46ef3c25d42ac4f60f2fdf45da, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.1567164, g: 0.1567164, b: 0.1567164, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 2800000, guid: 98e9c8080c5a37048be6f3f4b95f0ff8, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 2800000, guid: 2ee7fd8754b16f24fb5755637e60f925, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 2
      m_Right: 15
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 5
      m_Right: 0
      m_Top: 3
      m_Bottom: 3
    m_Overflow:
      m_Left: 0
      m_Right: 2
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 11
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 1
    m_RichText: 1
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: List
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.2509804, g: 0.2509804, b: 0.2509804, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 2
      m_Right: 2
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 11
    m_FontStyle: 0
    m_Alignment: 3
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: ListNormal
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 5
      m_Right: 5
      m_Top: 5
      m_Bottom: 5
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 3
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: ListIcon
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: ListAlt
    m_Normal:
      m_Background: {fileID: 2800000, guid: 25666cadf903fbd4989a8cefba8fc24c, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.2509804, g: 0.2509804, b: 0.2509804, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 2
      m_Right: 2
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 11
    m_FontStyle: 0
    m_Alignment: 3
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: ListAltNormal
    m_Normal:
      m_Background: {fileID: 2800000, guid: 25666cadf903fbd4989a8cefba8fc24c, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 5
      m_Right: 5
      m_Top: 5
      m_Bottom: 5
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 3
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: ListAltNormalSelected
    m_Normal:
      m_Background: {fileID: 2800000, guid: a274f5f5cbdf47c409729d951606ae1f, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 5
      m_Right: 5
      m_Top: 5
      m_Bottom: 5
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 3
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: ListAltIcon
    m_Normal:
      m_Background: {fileID: 2800000, guid: 25666cadf903fbd4989a8cefba8fc24c, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: ListAltSelected
    m_Normal:
      m_Background: {fileID: 2800000, guid: a274f5f5cbdf47c409729d951606ae1f, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.88235295, g: 0.88235295, b: 0.88235295, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 2
      m_Right: 2
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 11
    m_FontStyle: 0
    m_Alignment: 3
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: ListButton
    m_Normal:
      m_Background: {fileID: 2800000, guid: 9df4d45783545b84b8ce5b9e9701c5f2, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 2800000, guid: 11acdc56b262a4641b40b01ef5f15c37, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.88235295, g: 0.88235295, b: 0.88235295, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 2
      m_Right: 2
      m_Top: 3
      m_Bottom: 3
    m_Margin:
      m_Left: 1
      m_Right: 1
      m_Top: 1
      m_Bottom: 1
    m_Padding:
      m_Left: 3
      m_Right: 3
      m_Top: 2
      m_Bottom: 3
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: ListButtonRadio
    m_Normal:
      m_Background: {fileID: 2800000, guid: 9df4d45783545b84b8ce5b9e9701c5f2, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 2800000, guid: 11acdc56b262a4641b40b01ef5f15c37, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.88235295, g: 0.88235295, b: 0.88235295, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 2
      m_Right: 2
      m_Top: 3
      m_Bottom: 3
    m_Margin:
      m_Left: 1
      m_Right: 1
      m_Top: 2
      m_Bottom: 0
    m_Padding:
      m_Left: 3
      m_Right: 3
      m_Top: 1
      m_Bottom: 2
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 10
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: ExpandButton
    m_Normal:
      m_Background: {fileID: 2800000, guid: 06d0f14a2a2072042a3a46c3a3149986, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.2, g: 0.2, b: 0.2, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 2800000, guid: c3e29123c6d739644ababd6cd69eb2e9, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 2800000, guid: d422219eeef1b6844ac4fc1e006a9646, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.2, g: 0.2, b: 0.2, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 2800000, guid: 7b75248593b59d046a38f49deb01d380, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 12
      m_Right: 12
      m_Top: 2
      m_Bottom: 2
    m_Margin:
      m_Left: 1
      m_Right: 1
      m_Top: 2
      m_Bottom: 0
    m_Padding:
      m_Left: 14
      m_Right: 14
      m_Top: 0
      m_Bottom: 2
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 10
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 15
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: ButtonAlreadyPressed
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 4
      m_Right: 4
      m_Top: 4
      m_Bottom: 4
    m_Margin:
      m_Left: 2
      m_Right: 2
      m_Top: 2
      m_Bottom: 2
    m_Padding:
      m_Left: 8
      m_Right: 8
      m_Top: 4
      m_Bottom: 4
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: Popup
    m_Normal:
      m_Background: {fileID: 2800000, guid: f4306844cc37ee44e9b8867e960b025d, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 2800000, guid: f4306844cc37ee44e9b8867e960b025d, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 2800000, guid: 93968e46ef3c25d42ac4f60f2fdf45da, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 4
      m_Right: 15
      m_Top: 4
      m_Bottom: 4
    m_Margin:
      m_Left: 2
      m_Right: 2
      m_Top: 5
      m_Bottom: 5
    m_Padding:
      m_Left: 3
      m_Right: 3
      m_Top: 4
      m_Bottom: 4
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 4
      m_Bottom: -2
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: -4}
    m_FixedWidth: 0
    m_FixedHeight: 18
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: PopupPanel
    m_Normal:
      m_Background: {fileID: 2800000, guid: 3743f95c1a415f2468d32581b011aae3, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 9
      m_Right: 9
      m_Top: 4
      m_Bottom: 11
    m_Margin:
      m_Left: 5
      m_Right: 5
      m_Top: 5
      m_Bottom: 5
    m_Padding:
      m_Left: 5
      m_Right: 5
      m_Top: 5
      m_Bottom: 5
    m_Overflow:
      m_Left: 3
      m_Right: 0
      m_Top: 0
      m_Bottom: 5
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: ButtonHasContents
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 4
      m_Right: 4
      m_Top: 4
      m_Bottom: 4
    m_Margin:
      m_Left: 2
      m_Right: 2
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 4
      m_Right: 4
      m_Top: 3
      m_Bottom: 3
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: ButtonNoContents
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 4
      m_Right: 4
      m_Top: 4
      m_Bottom: 4
    m_Margin:
      m_Left: 2
      m_Right: 2
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 4
      m_Right: 4
      m_Top: 3
      m_Bottom: 3
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: AssetInfoPanel
    m_Normal:
      m_Background: {fileID: 2800000, guid: d0285fedba859c74689e8eba27e5297b, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 4
      m_Right: 4
      m_Top: 4
      m_Bottom: 4
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 2
      m_Right: 2
      m_Top: 2
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 3
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: AssetInfoPanelToolbarTop
    m_Normal:
      m_Background: {fileID: 2800000, guid: d0285fedba859c74689e8eba27e5297b, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 4
      m_Right: 4
      m_Top: 4
      m_Bottom: 4
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 2
      m_Right: 2
      m_Top: 2
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: AssetInfoPanelToolbarTopAllList
    m_Normal:
      m_Background: {fileID: 2800000, guid: d0285fedba859c74689e8eba27e5297b, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 4
      m_Right: 4
      m_Top: 4
      m_Bottom: 4
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 2
      m_Right: 2
      m_Top: 4
      m_Bottom: 2
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: AssetInfoPanelNoList
    m_Normal:
      m_Background: {fileID: 2800000, guid: d0285fedba859c74689e8eba27e5297b, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 4
      m_Right: 4
      m_Top: 4
      m_Bottom: 4
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 2
      m_Right: 2
      m_Top: 5
      m_Bottom: 5
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: IndentStyle1
    m_Normal:
      m_Background: {fileID: 2800000, guid: a68bd5f397e1f2a4ab605aa35026ca0d, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 2
      m_Right: 5
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 3
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 4
      m_Right: 7
      m_Top: 6
      m_Bottom: 2
    m_Overflow:
      m_Left: 2
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: ProjectSettingsGroup
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 0
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: Asset
    m_Normal:
      m_Background: {fileID: 2800000, guid: 1d35c6f19492f7147a4fb89911480ab2, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 2800000, guid: c26d7a4f0daeb3b48973a2a5e2ccdcee, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 4
      m_Right: 4
      m_Top: 4
      m_Bottom: 4
    m_Margin:
      m_Left: 2
      m_Right: 2
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 4
      m_Right: 6
      m_Top: 3
      m_Bottom: 3
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: AssetHovered
    m_Normal:
      m_Background: {fileID: 2800000, guid: 862200d03ab47bb4fbe67878d074f7eb, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 2800000, guid: c26d7a4f0daeb3b48973a2a5e2ccdcee, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 4
      m_Right: 4
      m_Top: 4
      m_Bottom: 4
    m_Margin:
      m_Left: 2
      m_Right: 2
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 4
      m_Right: 6
      m_Top: 3
      m_Bottom: 3
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: AssetUsageArrow
    m_Normal:
      m_Background: {fileID: 2800000, guid: f8e6be5e2d9da41449602ca7def584b9, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 2800000, guid: c26d7a4f0daeb3b48973a2a5e2ccdcee, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 9
    m_FixedHeight: 11
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: IconValidity
    m_Normal:
      m_Background: {fileID: 2800000, guid: 8c49083ee1fe30f498dd7298cd26d01d, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Hover:
      m_Background: {fileID: 2800000, guid: b900a745cfa24cb48b7e50457028a2f8, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 0
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: IconHovered
    m_Normal:
      m_Background: {fileID: 2800000, guid: ad6a7640fa699fc46b411728d1c18b51, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 2800000, guid: c26d7a4f0daeb3b48973a2a5e2ccdcee, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 3
      m_Right: 3
      m_Top: 3
      m_Bottom: 3
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: HiddenScrollbar
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 0}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: HiddenScrollbarthumb
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 0}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: HiddenScrollbarupbutton
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 0}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: HiddenScrollbardownbutton
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 0}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: HiddenScrollbarleftbutton
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 0}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: HiddenScrollbarrightbutton
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 0}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: DrawTexture
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 0}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 1
  - m_Name: Icon-ArrowBig-Left
    m_Normal:
      m_Background: {fileID: 2800000, guid: 476ce0ec9616e944284359243b46f7e5, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 0}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: Icon-ArrowBig-Right
    m_Normal:
      m_Background: {fileID: 2800000, guid: f8e6be5e2d9da41449602ca7def584b9, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 0}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: Icon-Toolbar-Log
    m_Normal:
      m_Background: {fileID: 2800000, guid: 73b48382876c31a4693ea55ac56ee9db, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 0}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: Icon-Toolbar-Open
    m_Normal:
      m_Background: {fileID: 2800000, guid: b3a0f42d2ccf3ee4599a4536f3761e07, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 0}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: Icon-Toolbar-Save
    m_Normal:
      m_Background: {fileID: 2800000, guid: 3f88df51b08315344a6f0becd7da78f2, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 0}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: Icon-Toolbar-Options
    m_Normal:
      m_Background: {fileID: 2800000, guid: 4d786bc9c8f625e469cce304e692c494, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 0}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: Icon-Toolbar-Help
    m_Normal:
      m_Background: {fileID: 2800000, guid: 36e97b21954b2c140bad7f0f34bda190, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 0}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: Icon-Toolbar-Column
    m_Normal:
      m_Background: {fileID: 2800000, guid: 9d0c5c006dbbc9744bfd357e2ec08bb7, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 0}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 0
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 30
    m_FixedHeight: 30
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: Icon-Warning
    m_Normal:
      m_Background: {fileID: 2800000, guid: 78babf560b3543043bce62a6d7d5edfc, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 0}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 0
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 30
    m_FixedHeight: 30
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: GUIEditor.BreadcrumbLeft
    m_Normal:
      m_Background: {fileID: -8730859949539617441, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 3607839988326647129, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: -2909435724611740479, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 658594365626841568, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 3
      m_Right: 10
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 5
      m_Right: 11
      m_Top: 2
      m_Bottom: 2
    m_Overflow:
      m_Left: 6
      m_Right: 7
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 19
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: GUIEditor.BreadcrumbMid
    m_Normal:
      m_Background: {fileID: 298390163510713244, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: -5694940394960273964, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 4917697211602105510, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 2726902712204841013, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 10
      m_Right: 10
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 10
      m_Right: 10
      m_Top: 2
      m_Bottom: 2
    m_Overflow:
      m_Left: 5
      m_Right: 7
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 19
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: RL DragHandle
    m_Normal:
      m_Background: {fileID: -126394901853745249, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds:
      - {fileID: 0}
      m_TextColor: {r: 0.043137256, g: 0.043137256, b: 0.043137256, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.705, g: 0.705, b: 0.705, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.705, g: 0.705, b: 0.705, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 6
      m_Right: 6
      m_Top: 6
      m_Bottom: 6
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 7
    m_WordWrap: 0
    m_RichText: 0
    m_TextClipping: 1
    m_ImagePosition: 3
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 6
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: RL Header
    m_Normal:
      m_Background: {fileID: 2222539610708135272, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.07058824, g: 0.07058824, b: 0.07058824, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.705, g: 0.705, b: 0.705, a: 1}
    m_Border:
      m_Left: 4
      m_Right: 4
      m_Top: 3
      m_Bottom: 2
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 1
    m_WordWrap: 0
    m_RichText: 0
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: -1}
    m_FixedWidth: 0
    m_FixedHeight: 18
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: RL Footer
    m_Normal:
      m_Background: {fileID: -5145904889709520074, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.8666667, g: 0.8666667, b: 0.8666667, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.101960786, g: 0.101960786, b: 0.101960786, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.101960786, g: 0.101960786, b: 0.101960786, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.1019608, g: 0.1019608, b: 0.1019608, a: 1}
    m_Border:
      m_Left: 4
      m_Right: 4
      m_Top: 3
      m_Bottom: 4
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 3
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 0
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 13
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: RL Background
    m_Normal:
      m_Background: {fileID: 3094889842989577616, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.705, g: 0.705, b: 0.705, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.705, g: 0.705, b: 0.705, a: 1}
    m_Border:
      m_Left: 6
      m_Right: 3
      m_Top: 0
      m_Bottom: 6
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 0
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 1
  - m_Name: RL FooterButton
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.8666667, g: 0.8666667, b: 0.8666667, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.101960786, g: 0.101960786, b: 0.101960786, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.1019608, g: 0.1019608, b: 0.1019608, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: -3
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 0
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 17
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: RL Element
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_OnNormal:
      m_Background: {fileID: -5193758048584019022, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 7516798493847391269, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 7516798493847391269, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 7516798493847391269, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 2
      m_Right: 2
      m_Top: 0
      m_Bottom: 3
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 0
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 1
  - m_Name: LogMessageIcons
    m_Normal:
      m_Background: {fileID: 5425037494185492166, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: -5763820162405496800, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: -2005373149481181617, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  m_Settings:
    m_DoubleClickSelectsWord: 1
    m_TripleClickSelectsLine: 1
    m_CursorColor: {r: 0, g: 0, b: 0, a: 1}
    m_CursorFlashSpeed: -1
    m_SelectionColor: {r: 0.23921569, g: 0.49803922, b: 0.87058824, a: 0.69803923}
