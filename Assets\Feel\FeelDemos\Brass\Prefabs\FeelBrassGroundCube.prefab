%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &2936013598542604254
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2936013598542604245}
  - component: {fileID: 2936013598542604242}
  - component: {fileID: 2936013598542604243}
  - component: {fileID: 2936013598542604240}
  - component: {fileID: 2936013598542604241}
  m_Layer: 0
  m_Name: FeelBrassGroundCube
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2936013598542604245
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2936013598542604254}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -5.15, y: -0.18, z: -1.32}
  m_LocalScale: {x: 0.5, y: 0.5, z: 0.5}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &2936013598542604242
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2936013598542604254}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &2936013598542604243
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2936013598542604254}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: a2a9867d32ed406488136cf9d81265a4, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!65 &2936013598542604240
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2936013598542604254}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!114 &2936013598542604241
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2936013598542604254}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 83dcb4ea8c2204a43adf98a01a3bc0d1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Receiver:
    TargetObject: {fileID: 2936013598542604254}
    TargetComponent: {fileID: 2936013598542604245}
    TargetScriptableObject: {fileID: 0}
    TargetPropertyName: position
    ShouldModifyValue: 1
    RelativeValue: 1
    ModifyX: 0
    ModifyY: 1
    ModifyZ: 0
    ModifyW: 1
    Threshold: 0.5
    BoolRemapZero: 0
    BoolRemapOne: 1
    StringRemapZero: Zero
    StringRemapOne: One
    IntRemapZero: 0
    IntRemapOne: 1
    FloatRemapZero: 0
    FloatRemapOne: 1
    Vector2RemapZero: {x: 0, y: 0}
    Vector2RemapOne: {x: 1, y: 1}
    Vector3RemapZero: {x: 0, y: 0, z: 0}
    Vector3RemapOne: {x: 1, y: 1, z: 1}
    Vector4RemapZero: {x: 0, y: 0, z: 0, w: 0}
    Vector4RemapOne: {x: 1, y: 1, z: 1, w: 1}
    QuaternionRemapZero: {x: 0, y: 0, z: 0}
    QuaternionRemapOne: {x: 180, y: 180, z: 180}
    ColorRemapZero: {r: 1, g: 1, b: 1, a: 1}
    ColorRemapOne: {r: 0, g: 0, b: 0, a: 1}
    Level: 0
  CanListen: 1
  Channel: 0
  RandomizeLevel: 1
  MinRandomLevelMultiplier: 0
  MaxRandomLevelMultiplier: 1
