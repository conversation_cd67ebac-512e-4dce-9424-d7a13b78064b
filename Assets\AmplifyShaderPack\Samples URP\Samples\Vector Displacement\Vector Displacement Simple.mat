%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Vector Displacement Simple
  m_Shader: {fileID: 4800000, guid: 4692060fdcd144dc9af75cf3ae2283e7, type: 3}
  m_ValidKeywords:
  - _SPECULARHIGHLIGHTS_OFF
  m_InvalidKeywords:
  - _GLOSSYREFLECTIONS_OFF
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _Albedo:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 2800000, guid: 0e5318892134427a92eb01a797250708, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMapVDM:
        m_Texture: {fileID: 2800000, guid: 876de209eecfa1d46b133f7f6abeda72, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _LeftHandVDM:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: 4998839cc7304cff87a29e2535dd1bbb, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTexVDM:
        m_Texture: {fileID: 2800000, guid: 897c9014cba411f47bd7278a4bc3d31e, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Masks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 2800000, guid: 0f1be42ec7d6427fbeb99bb7a4480d54, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NoiseFlipbook:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Normal:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalHands:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalTopSkull:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalsLeftHand:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 2800000, guid: d6dd85c78644495f98c427634c7d81e7, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ScreenHandsVDM:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SmoothnessMap:
        m_Texture: {fileID: 2800000, guid: b6a6c7bc322040c5b1fed27b535af25f, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecularMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TV_MetallicSmoothness:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TopSkullVDM:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _VDMMap:
        m_Texture: {fileID: 2800000, guid: cfedd5f350467c44195a77151e768abd, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _texcoord:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AlphaCutoff: 0.5
    - _BaseSmoothness: 0
    - _Brightness: 1
    - _Cull: 0
    - _DisplacementMultiplier: 1
    - _EnvironmentReflections: 1
    - _ExtrudeAmount: 0.181
    - _GlossyReflections: 0
    - _GlowIntensity: 0
    - _HandIntensity: 0
    - _MetallicStrength: 0
    - _NoiseTiling: 1
    - _NormalStrength: 1
    - _NormalStrengthVDM: 0.63
    - _OcclusionStrengthAO: 0
    - _QueueControl: 0
    - _QueueOffset: 0
    - _ReceiveShadows: 1
    - _ScreenColorTintBlend: 0
    - _ScreenToggle: 0
    - _SideHandIntensity: 0
    - _SkullIntensity: 0
    - _SmoothnessStrength: 0
    - _SpecularHighlights: 0
    - _SpecularStrength: 0.04
    - _TessMax: 25
    - _TessMin: 10
    - _TessValue: 32
    - _TessellationDistanceMax: 24.22
    - _TessellationDistanceMin: 0
    - _TessellationStrength: 96.3
    - _TilingGlow: 1
    - __dirty: 0
    m_Colors:
    - _BaseColor: {r: 1, g: 1, b: 1, a: 0}
    - _BaseColorVDM: {r: 0.9245283, g: 0.9245283, b: 0.9245283, a: 0}
    - _EmissionColor: {r: 1, g: 1, b: 1, a: 1}
    - _MainUVs: {r: 2, g: 2, b: 0, a: 0}
    - _NoiseTint: {r: 0, g: 0, b: 0, a: 0}
    - _SideHandTint: {r: 0, g: 0, b: 0, a: 0}
    - _SpecularColor: {r: 1, g: 1, b: 1, a: 0}
    - _TVHandsTint: {r: 0, g: 0, b: 0, a: 0}
    - _TopSkullTint: {r: 0, g: 0, b: 0, a: 0}
    - _VDMUVs: {r: 1, g: 1, b: 0, a: 0}
  m_BuildTextureStacks: []
