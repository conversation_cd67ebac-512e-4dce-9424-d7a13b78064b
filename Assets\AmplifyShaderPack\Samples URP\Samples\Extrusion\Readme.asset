%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e78934177bebe0545ab566bb787b0282, type: 3}
  m_Name: Readme
  m_EditorClassIdentifier: 
  Icon: {fileID: 2800000, guid: 622926f2f9f1bff43af5538c589c6c34, type: 3}
  Title: Normal Extrusion
  Description:
    Heading: 
    Text: This sample performs an animated  bubbly extrusion effect along a tessellated 
      object y axis.
    LinkText: 
    Url: 
  PropertiesHeader:
    Heading: Properties
    Text: 
    LinkText: 
    Url: 
  Properties:
  - Heading: Max Tessellation
    Text: Max tessellation amount for distance based tessellation.
    LinkText: 
    Url: 
  - Heading: Tess Min Distance
    Text: Minimum eye/camera distance for tessellation to be applied.
    LinkText: 
    Url: 
  - Heading: Tess Max Distance
    Text: Maximum eye/camera distance for tessellation to be applied.
    LinkText: 
    Url: 
  - Heading: ExtrusionPoint
    Text: Interval point to apply extrusion.
    LinkText: 
    Url: 
  - Heading: Extrusion Amount
    Text: Amount of extrusion to apply along the vertex normal.
    LinkText: 
    Url: 
  - Heading: Albedo
    Text: Albedo texture to be applied to surface.
    LinkText: 
    Url: 
  AdditionalProperties: []
  AdditionalScripts: []
  LoadedLayout: 0
  RPType: 3
