// Minimal stubs to satisfy references after removing MMAudio/MMSoundManager modules
// These provide no functionality but allow the project to compile.
#if UNITY_EDITOR
using UnityEngine;
using UnityEngine.Audio;
using Object = UnityEngine.Object;

namespace MoreMountains.Tools
{
    public partial class MMAudioAnalyzer : MonoBehaviour {
        public float NormalizedBufferedAmplitude => 0f;
        public float[] NormalizedBufferedBandLevels = new float[0];
        public MMAudioAnalyzerBeat[] Beats = new MMAudioAnalyzerBeat[0];
    }

    public class MMSoundManager : MonoBehaviour {
        public enum MMSoundManagerTracks { Master, Music, Sfx, UI }

        // editor settings reference
        public MMSoundManagerSettingsSO settingsSo;

        // stub singleton
        private static MMSoundManager _instance;
        public static MMSoundManager Instance {
            get {
                if (_instance == null) { _instance = new GameObject("MMSoundManagerStub").AddComponent<MMSoundManager>(); }
                return _instance;
            }
        }
        // minimal stubbed AudioSource holder object
        private readonly AudioSource _dummySource = null;
        public AudioSource FindByClip(AudioClip clip) => _dummySource;
        public void FreeSound(int soundID) {}
        public void FreeSound(AudioSource source) {}

        // track control stubs
        public void MuteTrack(MMSoundManagerTracks track) {}
        public void UnmuteTrack(MMSoundManagerTracks track) {}
        public void PauseTrack(MMSoundManagerTracks track) {}
        public void StopTrack(MMSoundManagerTracks track) {}
        public void PlayTrack(MMSoundManagerTracks track) {}
        public void FreeTrack(MMSoundManagerTracks track) {}
    }

    public class MMSoundManagerSettings : ScriptableObject {
        public const float _minimalVolume = 0f;
        public const float _maxVolume = 1f;
    }
    public class MMSoundManagerSettingsSO : ScriptableObject {
        [System.Serializable]
        public class SoundSettings {
            public bool MasterOn=true;
            public bool MusicOn=true;
            public bool SfxOn=true;
            public bool UIOn=true;
        }
        public SoundSettings Settings = new SoundSettings();
        private float _defaultVolume=1f;
        public float GetTrackVolume(MMSoundManager.MMSoundManagerTracks track) { return _defaultVolume; }
        public void SetTrackVolume(MMSoundManager.MMSoundManagerTracks track, float volume) {}
        public void SaveSoundSettings() {}
        public void LoadSoundSettings() {}
        public void ResetSoundSettings() {}
    }

    public enum MMSoundManagerAllSoundsControlEventTypes { Pause, Play, Stop, Free, FreeAllButPersistent, FreeAllLooping }
    public enum MMSoundManagerSoundControlEventTypes { Pause, Play, Stop }

    public class MMSMPlaylistManager : MonoBehaviour {
        public float CurrentClipDuration => 0f;
        public float CurrentTime => 0f;
        public int CurrentSongIndex => 0;
        public string CurrentSongName => "";
    }
    public class MMSMPlaylist : MonoBehaviour {}

    // Placeholder events and types
    public static class MMPlaylistNewSongStartedEvent {
        public static void Register(System.Action<int> callback) {}
        public static void Unregister(System.Action<int> callback) {}
    }
    public static class MMPlaylistPlayEvent { public static void Trigger(int channel){} }
    public static class MMPlaylistPlayNextEvent { public static void Trigger(int channel){} }
    public static class MMPlaylistPlayPreviousEvent { public static void Trigger(int channel){} }
    public static class MMPlaylistStopEvent { public static void Trigger(int channel){} }
    public static class MMPlaylistPauseEvent { public static void Trigger(int channel){} }
    public static class MMPlaylistPlayIndexEvent { public static void Trigger(int channel,int index){} }
    public static class MMPlaylistVolumeMultiplierEvent { public static void Trigger(int channel,float multiplier,bool instant){} }
    public static class MMPlaylistChangeEvent { public static void Trigger(int channel, Object newPlaylist,bool play){} }

    public class MMForceDestroyInPlayMode : MonoBehaviour {}

    public static class MMSfxEvent {
        public static void Trigger(AudioClip clip, AudioMixerGroup mixerGroup = null, float volume=1f, float pitch=1f, int priority=128){}
    }

    // Sound Manager events stubs
    public static class MMSoundManagerAllSoundsControlEvent {
        public static void Trigger(MMSoundManagerAllSoundsControlEventTypes eventType) {}
    }
    public static class MMSoundManagerSoundControlEvent {
        public static void Trigger(MMSoundManagerSoundControlEventTypes eventType, int soundID){}
    }
    public static class MMSoundManagerSoundFadeEvent {
        public enum Modes { PlayFade, StopFade }
        public static void Trigger(Modes mode, int soundID,float duration,float finalVolume,int tween){}
        public static void Trigger(Modes mode, int soundID,float duration,float finalVolume, MMTweenType tween){}
    }
    public static class MMSoundManagerTrackFadeEvent {
        public enum Modes { PlayFade, StopFade }
        public static void Trigger(Modes mode, MMSoundManager.MMSoundManagerTracks track,float duration,float finalVolume,int tween){}
        public static void Trigger(Modes mode, MMSoundManager.MMSoundManagerTracks track,float duration,float finalVolume, MMTweenType tween){}
    }
    public enum MMSoundManagerTrackEventTypes { MuteTrack, UnmuteTrack, SetVolumeTrack, PauseTrack, PlayTrack, StopTrack, FreeTrack }
    public static class MMSoundManagerTrackEvent {
        public static void Trigger(MMSoundManagerTrackEventTypes trackEventType, MMSoundManager.MMSoundManagerTracks track){}
        public static void Trigger(MMSoundManagerTrackEventTypes trackEventType, MMSoundManager.MMSoundManagerTracks track, float value){}
    }
    public static class MMSoundManagerSoundPlayEvent {
        public static AudioSource Trigger(AudioClip clip, MMSoundManagerPlayOptions options) { return null; }
        public static AudioSource Trigger(MMSoundManagerPlayOptions options) { return null; }
    }

    // Save/Load events
    public enum MMSoundManagerEventTypes { SaveSettings, LoadSettings, ResetSettings }
    public struct MMSoundManagerEvent {
        public MMSoundManagerEventTypes EventType;
        public MMSoundManagerEvent(MMSoundManagerEventTypes type) { EventType = type; }
        public static void Trigger(MMSoundManagerEventTypes type) {}
    }

    // Extend MMAudioAnalyzer for Beats struct
    public struct MMAudioAnalyzerBeat { public float CurrentValue; public Color BeatColor; }

    // provide default static options
    public static class MMSoundManagerPlayOptionsExtensions {
        public static MMSoundManagerPlayOptions Default => new MMSoundManagerPlayOptions();
    }

    public struct MMSoundManagerPlayOptions {
        public AudioResource AudioResourceToPlay;
        public MMSoundManager.MMSoundManagerTracks MmSoundManagerTrack;
        public Vector3 Location;
        public bool Loop;
        public float Volume;
        public int ID;
        public bool Fade;
        public float FadeInitialVolume;
        public float FadeDuration;
        public MMTweenType FadeTween;
        public bool Persistent;
        public bool RecycleAudioSource;
        public AudioMixerGroup AudioGroup;
        public float Pitch;
        public float PlaybackTime;
        public float PlaybackDuration;
        public float PanStereo;
        public float SpatialBlend;
        public bool SoloSingleTrack;
        public bool SoloAllTracks;
        public bool AutoUnSoloOnEnd;
        public bool BypassEffects;
        public bool BypassListenerEffects;
        public bool BypassReverbZones;
        public int Priority;
        public float ReverbZoneMix;
        public float DopplerLevel;
        public float Spread;
        public AudioRolloffMode RolloffMode;
        public float MinDistance;
        public float MaxDistance;
        public Transform AttachToTransform;
        public bool UseSpreadCurve;
        public AnimationCurve SpreadCurve;
        public bool UseCustomRolloffCurve;
        public AnimationCurve CustomRolloffCurve;
        public bool UseSpatialBlendCurve;
        public AnimationCurve SpatialBlendCurve;
        public bool UseReverbZoneMixCurve;
        public AnimationCurve ReverbZoneMixCurve;
        public bool DoNotAutoRecycleIfNotDonePlaying;

        public static MMSoundManagerPlayOptions Default => new MMSoundManagerPlayOptions();
    }

    public partial class MMAudioAnalyzer {
        public enum Modes { Off, On, AudioSource }
        public Modes Mode => Modes.Off;
        public void FindPeaks() {}
        public void PastePeaks() {}
        public void ClearPeaks() {}
    }
    public static class PeaksSaver { public static float[] Peaks = new float[0]; }

}
#endif
