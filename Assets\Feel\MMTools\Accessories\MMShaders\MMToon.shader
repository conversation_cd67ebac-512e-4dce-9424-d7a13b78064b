Shader "MoreMountains/MMToon"
{
    Properties
    {
        _ToonRamp("Toon Ramp", 2D) = "white" {}
        [HDR]_RimColor("Rim Color", Color) = (0,1,0.8758622,0)
        _RimPower("Rim Power", Range( 0 , 10)) = 0.5
        _RimOffset("Rim Offset", Float) = 0.24
        _Diffuse("Diffuse", 2D) = "white" {}
        _DiffuseColor("DiffuseColor", Color) = (1,1,1,1)
        [Toggle(_EMISSION_ON)] _Emission("Emission", Float) = 0
        _EmissionTexture("EmissionTexture", 2D) = "white" {}
        [HDR]_EmissionColor("EmissionColor", Color) = (1,1,1,1)
        _EmissionForce("EmissionForce", Float) = 0
        [HideInInspector] _texcoord( "", 2D ) = "white" {}
        [HideInInspector] __dirty( "", Int ) = 1
    }

    SubShader
    {
        Tags
        {
            "RenderType" = "Opaque" "Queue" = "Geometry+0" "IsEmissive" = "true"
        }
        Cull Back
        CGINCLUDE
        #include "UnityPBSLighting.cginc"
        #include "UnityCG.cginc"
        #include "UnityShaderVariables.cginc"
        #include "Lighting.cginc"
        #pragma target 3.0
        #pragma shader_feature _EMISSION_ON
        #ifdef UNITY_PASS_SHADOWCASTER
			#undef INTERNAL_DATA
			#undef WorldReflectionVector
			#undef WorldNormalVector
			#define INTERNAL_DATA half3 internalSurfaceTtoW0; half3 internalSurfaceTtoW1; half3 internalSurfaceTtoW2;
			#define WorldReflectionVector(data,normal) reflect (data.worldRefl, half3(dot(data.internalSurfaceTtoW0,normal), dot(data.internalSurfaceTtoW1,normal), dot(data.internalSurfaceTtoW2,normal)))
			#define WorldNormalVector(data,normal) half3(dot(data.internalSurfaceTtoW0,normal), dot(data.internalSurfaceTtoW1,normal), dot(data.internalSurfaceTtoW2,normal))
        #endif
        struct Input
        {
            float2 uv_texcoord;
            float3 worldNormal;
            INTERNAL_DATA
            float3 worldPos;
        };

        struct SurfaceOutputCustomLightingCustom
        {
            half3 Albedo;
            half3 Normal;
            half3 Emission;
            half Metallic;
            half Smoothness;
            half Occlusion;
            half Alpha;
            Input SurfInput;
            UnityGIInput GIData;
        };

        uniform sampler2D _EmissionTexture;
        uniform float4 _EmissionTexture_ST;
        uniform float4 _EmissionColor;
        uniform float _EmissionForce;
        uniform float4 _DiffuseColor;
        uniform sampler2D _Diffuse;
        uniform float4 _Diffuse_ST;
        uniform sampler2D _ToonRamp;
        uniform float _RimOffset;
        uniform float _RimPower;
        uniform float4 _RimColor;

        inline half4 LightingStandardCustomLighting(inout SurfaceOutputCustomLightingCustom s, half3 viewDir,
                                                    UnityGI gi)
        {
            UnityGIInput data = s.GIData;
            Input i = s.SurfInput;
            half4 c = 0;
            #ifdef UNITY_PASS_FORWARDBASE
			float ase_lightAtten = data.atten;
			if( _LightColor0.a == 0)
			ase_lightAtten = 0;
            #else
            float3 ase_lightAttenRGB = gi.light.color / ((_LightColor0.rgb) + 0.000001);
            float ase_lightAtten = max(max(ase_lightAttenRGB.r, ase_lightAttenRGB.g), ase_lightAttenRGB.b);
            #endif
            #if defined(HANDLE_SHADOWS_BLENDING_IN_GI)
			half bakedAtten = UnitySampleBakedOcclusion(data.lightmapUV.xy, data.worldPos);
			float zDist = dot(_WorldSpaceCameraPos - data.worldPos, UNITY_MATRIX_V[2].xyz);
			float fadeDist = UnityComputeShadowFadeDistance(data.worldPos, zDist);
			ase_lightAtten = UnityMixRealtimeAndBakedShadows(data.atten, bakedAtten, UnityComputeShadowFade(fadeDist));
            #endif
            float2 uv_Diffuse = i.uv_texcoord * _Diffuse_ST.xy + _Diffuse_ST.zw;
            float3 ase_worldNormal = WorldNormalVector(i, float3( 0, 0, 1 ));
            float3 ase_worldPos = i.worldPos;
            #if defined(LIGHTMAP_ON) && UNITY_VERSION < 560 //aseld
			float3 ase_worldlightDir = 0;
            #else //aseld
            float3 ase_worldlightDir = normalize(UnityWorldSpaceLightDir(ase_worldPos));
            #endif //aseld
            float dotResult3 = dot(ase_worldNormal, ase_worldlightDir);
            float2 temp_cast_1 = (saturate((dotResult3 * 0.5 + 0.5))).xx;
            #if defined(LIGHTMAP_ON) && ( UNITY_VERSION < 560 || ( defined(LIGHTMAP_SHADOW_MIXING) && !defined(SHADOWS_SHADOWMASK) && defined(SHADOWS_SCREEN) ) )//aselc


			float4 ase_lightColor = 0;
            #else //aselc
            float4 ase_lightColor = _LightColor0;
            #endif //aselc
            UnityGI gi11 = gi;
            float3 diffNorm11 = ase_worldNormal;
            gi11 = UnityGI_Base(data, 1, diffNorm11);
            float3 indirectDiffuse11 = gi11.indirect.diffuse + diffNorm11 * 0.0001;
            float3 ase_worldViewDir = normalize(UnityWorldSpaceViewDir(ase_worldPos));
            float dotResult38 = dot(ase_worldNormal, ase_worldViewDir);
            c.rgb = ((((_DiffuseColor * tex2D(_Diffuse, uv_Diffuse)) * tex2D(_ToonRamp, temp_cast_1)) * (ase_lightColor
                * float4((indirectDiffuse11 + ase_lightAtten), 0.0))) + (saturate(
                ((ase_lightAtten * dotResult3) * pow((1.0 - saturate((dotResult38 + _RimOffset))), _RimPower))) * (
                _RimColor * ase_lightColor))).rgb;
            c.a = 1;
            return c;
        }

        inline void LightingStandardCustomLighting_GI(inout SurfaceOutputCustomLightingCustom s, UnityGIInput data,
                                                      inout UnityGI gi)
        {
            s.GIData = data;
        }

        void surf(Input i, inout SurfaceOutputCustomLightingCustom o)
        {
            o.SurfInput = i;
            o.Normal = float3(0, 0, 1);
            float2 uv_EmissionTexture = i.uv_texcoord * _EmissionTexture_ST.xy + _EmissionTexture_ST.zw;
            #ifdef _EMISSION_ON
            float4 staticSwitch58 = ((tex2D(_EmissionTexture, uv_EmissionTexture) * _EmissionColor) * _EmissionForce);
            #else
				float4 staticSwitch58 = float4( 0,0,0,0 );
            #endif
            o.Emission = staticSwitch58.rgb;
        }
        ENDCG
        CGPROGRAM
        #pragma surface surf StandardCustomLighting keepalpha fullforwardshadows exclude_path:deferred
        ENDCG
        Pass
        {
            Name "ShadowCaster"
            Tags
            {
                "LightMode" = "ShadowCaster"
            }
            ZWrite On
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma target 3.0
            #pragma multi_compile_shadowcaster
            #pragma multi_compile UNITY_PASS_SHADOWCASTER
            #pragma skip_variants FOG_LINEAR FOG_EXP FOG_EXP2
            #include "HLSLSupport.cginc"
            #if ( SHADER_API_D3D11 || SHADER_API_GLCORE || SHADER_API_GLES || SHADER_API_GLES3 || SHADER_API_METAL || SHADER_API_VULKAN )
            #define CAN_SKIP_VPOS
            #endif
            #include "UnityCG.cginc"
            #include "Lighting.cginc"
            #include "UnityPBSLighting.cginc"

            struct v2f
            {
                V2F_SHADOW_CASTER;
                float2 customPack1 : TEXCOORD1;
                float4 tSpace0 : TEXCOORD2;
                float4 tSpace1 : TEXCOORD3;
                float4 tSpace2 : TEXCOORD4;
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            v2f vert(appdata_full v)
            {
                v2f o;
                UNITY_SETUP_INSTANCE_ID(v);
                UNITY_INITIALIZE_OUTPUT(v2f, o);
                UNITY_TRANSFER_INSTANCE_ID(v, o);
                Input customInputData;
                float3 worldPos = mul(unity_ObjectToWorld, v.vertex).xyz;
                half3 worldNormal = UnityObjectToWorldNormal(v.normal);
                half3 worldTangent = UnityObjectToWorldDir(v.tangent.xyz);
                half tangentSign = v.tangent.w * unity_WorldTransformParams.w;
                half3 worldBinormal = cross(worldNormal, worldTangent) * tangentSign;
                o.tSpace0 = float4(worldTangent.x, worldBinormal.x, worldNormal.x, worldPos.x);
                o.tSpace1 = float4(worldTangent.y, worldBinormal.y, worldNormal.y, worldPos.y);
                o.tSpace2 = float4(worldTangent.z, worldBinormal.z, worldNormal.z, worldPos.z);
                o.customPack1.xy = customInputData.uv_texcoord;
                o.customPack1.xy = v.texcoord;
                TRANSFER_SHADOW_CASTER_NORMALOFFSET(o)
                return o;
            }

            half4 frag(v2f IN
                #if !defined( CAN_SKIP_VPOS )
			, UNITY_VPOS_TYPE vpos : VPOS
                #endif
            ) : SV_Target
            {
                UNITY_SETUP_INSTANCE_ID(IN);
                Input surfIN;
                UNITY_INITIALIZE_OUTPUT(Input, surfIN);
                surfIN.uv_texcoord = IN.customPack1.xy;
                float3 worldPos = float3(IN.tSpace0.w, IN.tSpace1.w, IN.tSpace2.w);
                half3 worldViewDir = normalize(UnityWorldSpaceViewDir(worldPos));
                surfIN.worldPos = worldPos;
                surfIN.worldNormal = float3(IN.tSpace0.z, IN.tSpace1.z, IN.tSpace2.z);
                surfIN.internalSurfaceTtoW0 = IN.tSpace0.xyz;
                surfIN.internalSurfaceTtoW1 = IN.tSpace1.xyz;
                surfIN.internalSurfaceTtoW2 = IN.tSpace2.xyz;
                SurfaceOutputCustomLightingCustom o;
                UNITY_INITIALIZE_OUTPUT(SurfaceOutputCustomLightingCustom, o)
                surf(surfIN, o);
                #if defined( CAN_SKIP_VPOS )
                float2 vpos = IN.pos;
                #endif
                SHADOW_CASTER_FRAGMENT(IN)
            }
            ENDCG
        }
    }
    Fallback "Diffuse"
}