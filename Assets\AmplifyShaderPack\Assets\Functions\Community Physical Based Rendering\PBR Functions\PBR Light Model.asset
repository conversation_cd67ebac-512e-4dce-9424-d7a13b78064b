%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: PBR Light Model
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor v1.9.5.1\n// Available at the
    Unity Asset Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=19501\nNode;AmplifyShaderEditor.CommentaryNode;322;-6018,-1378;Inherit;False;1969.817;2373.042;Math;3;668;644;88;;0.02830189,0.02830189,0.02830189,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;777;-8256,-1376;Inherit;False;2186.034;1932.566;Primary
    Inputs;6;699;424;169;323;813;697;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;778;-4000,-1376;Inherit;False;1149.903;555.6772;Final
    Blend & Output;10;0;836;835;780;829;688;690;687;686;698;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;88;-5984,-1312;Inherit;False;1425.001;659.317;Diffuse
    Calculations;13;70;593;71;77;76;73;72;162;186;314;313;312;841;;0,0,0,0.5019608;0;0\nNode;AmplifyShaderEditor.CommentaryNode;644;-5984,-624;Inherit;False;1894.244;847.336;Specularity
    & Specular Calculations;33;748;657;662;659;658;653;735;736;734;733;660;648;647;646;645;665;655;731;664;650;649;730;656;729;728;654;721;720;652;651;732;667;666;;0,0,0,0.5019608;0;0\nNode;AmplifyShaderEditor.CommentaryNode;668;-5984,256;Inherit;False;1641.159;688.0969;Indirect
    Specular;15;683;679;678;681;680;682;677;676;675;670;673;674;671;672;685;;0,0,0,0.5019608;0;0\nNode;AmplifyShaderEditor.CommentaryNode;697;-8224,192;Inherit;False;1163.91;337.836;sceneLighting;7;779;781;782;695;696;694;783;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;813;-7040,208;Inherit;False;710.2363;317.0811;Inputs
    Indirect Lighting;4;592;600;775;773;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;698;-3792,-992;Inherit;False;207;138.3334;NdotL;1;692;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;323;-8192,-1328;Inherit;False;1019.626;776.5386;Dot
    Products;14;643;373;372;630;631;388;381;387;351;342;380;837;838;840;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;312;-5936,-928;Inherit;False;198;134;NdotV;1;69;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;169;-7104,-1312;Inherit;False;960.0198;759.5439;Main
    Input & Color;15;80;634;491;490;51;98;168;142;105;104;715;714;713;277;276;;0,0,0,0.5019608;0;0\nNode;AmplifyShaderEditor.CommentaryNode;278;-6016,-1744;Inherit;False;671.7236;327.3534;PBR
    Light Model Node;1;279;;0.0754717,0.0754717,0.0754717,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;313;-5936,-1216;Inherit;False;198;134;NdotL;1;68;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;314;-5936,-1072;Inherit;False;198;134;LdotH;1;67;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;424;-8224,-144;Inherit;False;965.3921;309.3308;Fresnel
    Reflectance;7;430;428;431;429;427;426;425;;0,0,0,0.5019608;0;0\nNode;AmplifyShaderEditor.CommentaryNode;685;-5872,464;Inherit;False;197.0894;132.1384;NdotV;1;669;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;699;-8224,-528;Inherit;False;1674.059;364.6329;Smoothness/Roughness,
    Metallic and grazing term;13;703;702;701;712;711;710;708;815;706;816;707;704;700;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;666;-5104,-208;Inherit;False;218;128;NdotV;1;663;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;667;-5104,-64;Inherit;False;221;132;NdotL;1;661;;0.06603771,0.05513526,0.05513526,1;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;40;-6016,-1936;Inherit;False;530.54;154.4753;Credits;;0,0,0,0.5019608;Base
    on method by Jordan Stevens$Link: https://www.jordanstevenstechart.com/physically-based-rendering$$Node
    Made by Tiber Legacy$Special Thanks to Dawie;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;186;-4976,-1024;Inherit;False;322;160;High
    Metallic;;0,0,0,0.5019608;At high enough 'Metal' value, the base 'Diffuse' is
    Reduced to Zero and just returns the Indirect Lighting. If this undesired then
    try either using lower metallic values OR using a Custom Indirect Diffuse of
    \"Lerp(Indirect Diffuse, Diffuse, 0.5f)@\".;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;276;-7088,-1248;Inherit;False;270.9414;111.9705;BaseColor;;0,0,0,1;;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;277;-7088,-1104;Inherit;False;266.8677;116.9852;Specular;;0,0,0,1;;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;279;-5648,-1696;Inherit;False;272.8736;115.3402;;;0,0,0,1;A
    Jordan Stevens Light Model from his article on \"Physically Based Rendering\";0;0\nNode;AmplifyShaderEditor.StickyNoteNode;425;-7648,16;Inherit;False;306.8462;123.1794;Fresnel
    Reflectance;;0,0,0,0.5019608;in Jordan's Article there are 'Fresnels' that include
    'Specular RGB' into to their calculations, this portion is to cover that and
    other Fresnels like them;0;0\nNode;AmplifyShaderEditor.GetLocalVarNode;162;-5632,-1248;Inherit;False;98;_Color;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;73;-5232,-1248;Inherit;False;3;3;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;76;-4992,-1248;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;77;-4848,-1248;Inherit;False;diffuseColor;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.StickyNoteNode;700;-8080,-448;Inherit;False;287;108;Compute
    Roughness;;0,0,0,1;in the event that Smoothness hasn't been processed/converted
    to 'Roughness' yet.;0;0\nNode;AmplifyShaderEditor.GetLocalVarNode;71;-5632,-1168;Inherit;False;711;_Metallic;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.StickyNoteNode;713;-7088,-976;Inherit;False;270.8677;122.9852;Shadow;;0,0,0,1;;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;714;-7088,-832;Inherit;False;270.8677;117.9852;Specular;;0,0,0,1;;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;715;-7088,-688;Inherit;False;270.8677;114.9852;Fresnel
    Term;;0,0,0,1;;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;732;-5696,-288;Inherit;False;280;111;Fresnel
    Reflectance;;0,0,0,1;;0;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;651;-5360,-560;Inherit;False;specColor;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;652;-5120,-352;Inherit;False;3;3;0;FLOAT;0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;663;-5072,-160;Inherit;False;372;NdotV;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.LerpOp;654;-5584,-560;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;728;-5424,-496;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;729;-5424,-496;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;656;-5360,-144;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;730;-5408,-144;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;649;-5840,-144;Inherit;False;104;fresnel;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;650;-5840,-64;Inherit;False;431;SpecFresnel;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;664;-5616,-32;Inherit;False;105;specularDistr;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;731;-5408,-16;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;655;-5360,-32;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitch;665;-5648,-144;Inherit;False;Fresnel
    Reflectance;True;0;2;1;In 0;In 1;Object;-1;10;0;FLOAT;0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;645;-5808,-560;Inherit;False;98;_Color;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;646;-5808,-496;Inherit;False;168;specRGB;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.ScaleNode;647;-5776,-416;Inherit;False;0.5;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;648;-5968,-416;Inherit;False;711;_Metallic;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMaxOpNode;660;-4848,-160;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0.1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;733;-5200,-256;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;734;-5184,-240;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;736;-5200,-144;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;735;-5184,-32;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;653;-5328,-352;Inherit;False;142;geoShadow;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleDivideOpNode;658;-4512,-352;Inherit;False;2;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;659;-4688,-160;Inherit;False;3;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;662;-4848,80;Float;False;Constant;_Four1;_Four;3;1;[Gamma];Create;True;0;0;0;False;0;False;4;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;661;-5072,-16;Inherit;False;373;NdotL;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;657;-4336,-352;Inherit;False;specularity;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.StickyNoteNode;748;-5360,80;Inherit;False;328.8457;127.3076;Max
    0.1;;0,0,0,0.5019608;currently a fix for issues arising, of which solves like
    Blowout or unusual Brightness, from this model being used in other Pipelines,
    Also leads to a \"Smoother\" result.;0;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;426;-7664,-96;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.OneMinusNode;427;-7984,-32;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;429;-7824,0;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;430;-8032,48;Inherit;False;104;fresnel;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;104;-6528,-688;Inherit;False;fresnel;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;105;-6528,-832;Inherit;False;specularDistr;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;142;-6528,-976;Inherit;False;geoShadow;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;168;-6528,-1104;Inherit;False;specRGB;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;98;-6528,-1248;Inherit;False;_Color;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;672;-5184,304;Inherit;False;600;Indirect
    Specular;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;671;-4912,304;Inherit;False;4;4;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;670;-5120,384;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;675;-5296,512;Inherit;False;5;5;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SaturateNode;676;-5472,512;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;677;-5632,512;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;669;-5856,512;Inherit;False;372;NdotV;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMaxOpNode;682;-5072,688;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0.15;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;686;-3728,-1312;Inherit;False;3;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;687;-3936,-1232;Inherit;False;77;diffuseColor;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;690;-3936,-1312;Inherit;False;683;finalSpec;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;688;-3936,-1152;Inherit;False;657;specularity;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.StickyNoteNode;783;-7648,336;Inherit;False;285.7002;157.9875;Enable
    Custom Scene Lighting;;0,0,0,1;;0;0\nNode;AmplifyShaderEditor.FunctionInput;51;-6752,-1248;Inherit;False;BaseColor;3;1;False;1;0;FLOAT3;0,1,1;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionInput;490;-6752,-976;Inherit;False;Shadow;1;9;False;1;0;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;491;-6752,-832;Inherit;False;Specular;1;4;False;1;0;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;634;-6784,-688;Inherit;False;Fresnel
    Term;1;5;True;1;0;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;80;-6752,-1104;Inherit;False;Specular;3;2;False;1;0;FLOAT3;0,1,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitch;704;-7536,-368;Inherit;False;Compute
    Roughness;True;1;2;0;In 0;In 1;Object;-1;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;707;-7264,-368;Inherit;False;_Roughness;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;72;-5440,-1168;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;816;-7680,-304;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;706;-7824,-304;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;815;-7968,-304;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;708;-8160,-304;Inherit;False;Smoothness;1;7;False;1;0;FLOAT;0.5;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;711;-7280,-480;Inherit;False;_Metallic;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;712;-7440,-480;Inherit;False;Metallic;1;6;False;1;0;FLOAT;0.75;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;701;-7040,-480;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SaturateNode;702;-6928,-480;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;703;-6768,-480;Inherit;False;grazingTerm;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;710;-7760,-400;Inherit;False;Roughness;1;8;False;1;0;FLOAT;0.5;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;673;-5360,432;Inherit;False;703;grazingTerm;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;674;-5360,352;Inherit;False;651;specColor;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;680;-5280,688;Inherit;False;711;_Metallic;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;678;-5248,800;Inherit;False;3;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;679;-5440,800;Inherit;False;707;_Roughness;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;681;-5104,800;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;70;-5920,-768;Inherit;False;707;_Roughness;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;69;-5920,-880;Inherit;False;372;NdotV;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;68;-5920,-1184;Inherit;False;373;NdotL;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;67;-5920,-1024;Inherit;False;643;LdotH;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMaxOpNode;720;-4848,-32;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;721;-5040,96;Inherit;False;Constant;_Max01;Max0.1;5;0;Create;True;0;0;0;False;0;False;0.1;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;829;-3520,-1312;Inherit;False;3;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LightColorNode;694;-8176,320;Inherit;False;0;3;COLOR;0;FLOAT3;1;FLOAT;2\nNode;AmplifyShaderEditor.GetLocalVarNode;692;-3760,-944;Inherit;False;373;NdotL;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;780;-3792,-1072;Inherit;False;779;sceneLighting;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;695;-7952,240;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionInput;380;-7856,-1088;Inherit;False;NdotV;1;13;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;773;-6976,256;Inherit;False;Indirect
    Specular;3;10;False;1;0;FLOAT3;1,1,1;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionInput;775;-6944,352;Inherit;False;Indirect
    Diffuse;3;11;False;1;0;FLOAT3;1,1,1;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;600;-6656,256;Inherit;False;Indirect
    Specular;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;592;-6656,352;Inherit;False;Indirect
    Diffuse;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;431;-7536,-96;Inherit;False;SpecFresnel;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;593;-5232,-1120;Inherit;False;592;Indirect
    Diffuse;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitch;387;-7680,-1168;Inherit;False;Custom
    Dot Products;True;0;2;2;In 0;In 1;Object;-1;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitchByPipeline;835;-3312,-1312;Inherit;False;4;0;FLOAT3;0,0,0;False;3;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;836;-3520,-1184;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionInput;782;-7952,352;Inherit;False;Custom
    Scene Lighting;3;0;False;1;0;FLOAT3;1,1,1;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitch;781;-7648,240;Inherit;False;Enable
    Custom Scene Lighting;True;0;2;3;In 0;In 1;Object;-1;10;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;779;-7312,240;Inherit;False;sceneLighting;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;683;-4736,304;Inherit;False;finalSpec;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionInput;381;-7856,-912;Inherit;False;NdotL;1;12;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;388;-7664,-1008;Inherit;False;Option;False;0;2;2;In
    0;In 1;Instance;387;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;631;-7856,-752;Inherit;False;LdotH;1;14;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;630;-7664,-832;Inherit;False;Option;False;0;2;2;In
    0;In 1;Instance;387;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;372;-7392,-1168;Inherit;False;NdotV;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;373;-7392,-1008;Inherit;False;NdotL;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;643;-7392,-832;Inherit;False;LdotH;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;351;-8112,-1168;Inherit;False;Normal;3;3;False;1;0;FLOAT3;0,0,1;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;342;-7936,-1264;Inherit;False;Normal;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionNode;837;-7920,-1168;Inherit;False;NdotV;-1;;59992;65be4ec269a534f4e8867af2f999aaa9;4,27,1,377,0,452,0,451,0;3;230;FLOAT3;0,0,1;False;443;FLOAT3;0,0,0;False;415;FLOAT;1E-37;False;1;FLOAT;23\nNode;AmplifyShaderEditor.FunctionNode;838;-7920,-992;Inherit;False;NdotL;-1;;59993;6d7ee1aa8c79587448475fa2b37affc1;5,27,1,381,0,460,0,461,0,463,0;4;230;FLOAT3;0,0,1;False;462;FLOAT3;0,0,0;False;454;FLOAT3;0,0,0;False;416;FLOAT;1E-37;False;1;FLOAT;111\nNode;AmplifyShaderEditor.FunctionNode;840;-7920,-832;Inherit;False;LdotH;-1;;59995;0364555185933b049a6a96d60ccefd1a;4,385,0,460,0,451,0,461,0;4;443;FLOAT3;0,0,0;False;462;FLOAT3;0,0,0;False;454;FLOAT3;0,0,0;False;419;FLOAT;1E-37;False;1;FLOAT;112\nNode;AmplifyShaderEditor.LightAttenuation;696;-8176,240;Inherit;False;0;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionNode;841;-5632,-1088;Inherit;False;Schlick
    Normal Incidence Reflection;-1;;59996;3dcd7b896a9f9df4786322661fbadb10;3,302,1,303,1,301,1;5;265;FLOAT3;0,0,1;False;295;FLOAT;0;False;297;FLOAT;0;False;294;FLOAT;0;False;33;FLOAT;0.5;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;428;-8192,-96;Inherit;False;651;specColor;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionOutput;0;-3072,-1312;Inherit;True;True;-1;BaseColor;0;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nWireConnection;73;0;162;0\nWireConnection;73;1;72;0\nWireConnection;73;2;841;0\nWireConnection;76;0;73;0\nWireConnection;76;1;593;0\nWireConnection;77;0;76;0\nWireConnection;651;0;654;0\nWireConnection;652;0;653;0\nWireConnection;652;1;733;0\nWireConnection;652;2;734;0\nWireConnection;654;0;645;0\nWireConnection;654;1;646;0\nWireConnection;654;2;647;0\nWireConnection;728;0;654;0\nWireConnection;729;0;654;0\nWireConnection;656;0;665;0\nWireConnection;656;1;730;0\nWireConnection;730;0;728;0\nWireConnection;731;0;729;0\nWireConnection;655;0;664;0\nWireConnection;655;1;731;0\nWireConnection;665;0;649;0\nWireConnection;665;1;650;0\nWireConnection;647;0;648;0\nWireConnection;660;0;663;0\nWireConnection;660;1;721;0\nWireConnection;733;0;736;0\nWireConnection;734;0;735;0\nWireConnection;736;0;656;0\nWireConnection;735;0;655;0\nWireConnection;658;0;652;0\nWireConnection;658;1;659;0\nWireConnection;659;0;660;0\nWireConnection;659;1;720;0\nWireConnection;659;2;662;0\nWireConnection;657;0;658;0\nWireConnection;426;0;428;0\nWireConnection;426;1;429;0\nWireConnection;427;0;428;0\nWireConnection;429;0;427;0\nWireConnection;429;1;430;0\nWireConnection;104;0;634;0\nWireConnection;105;0;491;0\nWireConnection;142;0;490;0\nWireConnection;168;0;80;0\nWireConnection;98;0;51;0\nWireConnection;671;0;672;0\nWireConnection;671;1;670;0\nWireConnection;671;2;682;0\nWireConnection;671;3;681;0\nWireConnection;670;0;674;0\nWireConnection;670;1;673;0\nWireConnection;670;2;675;0\nWireConnection;675;0;676;0\nWireConnection;675;1;676;0\nWireConnection;675;2;676;0\nWireConnection;675;3;676;0\nWireConnection;675;4;676;0\nWireConnection;676;0;677;0\nWireConnection;677;0;669;0\nWireConnection;682;0;680;0\nWireConnection;686;0;690;0\nWireConnection;686;1;687;0\nWireConnection;686;2;688;0\nWireConnection;704;0;710;0\nWireConnection;704;1;816;0\nWireConnection;707;0;704;0\nWireConnection;72;0;71;0\nWireConnection;816;0;706;0\nWireConnection;816;1;706;0\nWireConnection;706;0;815;0\nWireConnection;815;0;708;0\nWireConnection;815;1;708;0\nWireConnection;711;0;712;0\nWireConnection;701;0;711;0\nWireConnection;701;1;707;0\nWireConnection;702;0;701;0\nWireConnection;703;0;702;0\nWireConnection;678;0;679;0\nWireConnection;678;1;679;0\nWireConnection;678;2;679;0\nWireConnection;681;0;678;0\nWireConnection;720;0;661;0\nWireConnection;720;1;721;0\nWireConnection;829;0;686;0\nWireConnection;829;1;780;0\nWireConnection;829;2;692;0\nWireConnection;695;0;696;0\nWireConnection;695;1;694;1\nWireConnection;600;0;773;0\nWireConnection;592;0;775;0\nWireConnection;431;0;426;0\nWireConnection;387;0;837;23\nWireConnection;387;1;380;0\nWireConnection;835;0;829;0\nWireConnection;835;3;829;0\nWireConnection;835;1;836;0\nWireConnection;835;2;836;0\nWireConnection;836;0;686;0\nWireConnection;836;1;692;0\nWireConnection;781;0;695;0\nWireConnection;781;1;782;0\nWireConnection;779;0;781;0\nWireConnection;683;0;671;0\nWireConnection;388;0;838;111\nWireConnection;388;1;381;0\nWireConnection;630;0;840;112\nWireConnection;630;1;631;0\nWireConnection;372;0;387;0\nWireConnection;373;0;388;0\nWireConnection;643;0;630;0\nWireConnection;342;0;351;0\nWireConnection;837;230;351;0\nWireConnection;838;230;351;0\nWireConnection;841;295;68;0\nWireConnection;841;297;67;0\nWireConnection;841;294;69;0\nWireConnection;841;33;70;0\nWireConnection;0;0;835;0\nASEEND*/\n//CHKSM=64307AB6781CD67A827C3FC0774D60E00CE012D9"
  m_functionName: 
  m_description: 'Core PBR Light Model Node


    Based on Jordan Stevens functions
    Physically Based Rendering'
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 0
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_customEdited: 0
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 0
  m_headerStyle: 2
  m_headerColor: {r: 0.7529412, g: 0.6784314, b: 0, a: 1}
  m_customNodeCategory: Amplify Shader Pack
  m_previewPosition: 0
  m_hidden: 0
  m_url: 
