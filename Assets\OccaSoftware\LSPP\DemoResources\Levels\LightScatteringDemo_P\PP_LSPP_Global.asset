%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-7390653649047296223
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: af0c9bc5a1911b14db85e866ea3016e3, type: 3}
  m_Name: LightScatteringPostProcess
  m_EditorClassIdentifier: 
  active: 1
  numberOfSamples:
    m_OverrideState: 1
    m_Value: 64
  fogDensity:
    m_OverrideState: 1
    m_Value: 5
  maxRayDistance:
    m_OverrideState: 1
    m_Value: 0.834
  tint:
    m_OverrideState: 1
    m_Value: {r: 1, g: 1, b: 1, a: 1}
  softenScreenEdges:
    m_OverrideState: 1
    m_Value: 0
  animateSamplingOffset:
    m_OverrideState: 1
    m_Value: 0
  lightMustBeOnScreen:
    m_OverrideState: 1
    m_Value: 0
  falloffBasis:
    m_OverrideState: 1
    m_Value: 0
  occlusionAssumption:
    m_OverrideState: 1
    m_Value: 0
  occlusionOverDistanceAmount:
    m_OverrideState: 1
    m_Value: 0
  falloffIntensity:
    m_OverrideState: 1
    m_Value: 3
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d7fd9488000d3734a9e00ee676215985, type: 3}
  m_Name: PP_LSPP_Global
  m_EditorClassIdentifier: 
  components:
  - {fileID: -7390653649047296223}
