%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: HdotH
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor v1.9.4.4\n// Available at the
    Unity Asset Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=19404\nNode;AmplifyShaderEditor.CommentaryNode;390;-256,-768;Inherit;False;1453.749;501.9055;HdotH;12;194;422;367;331;451;450;449;296;393;195;437;448;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;448;-224,-720;Inherit;False;216;132.6667;Half
    Vector;1;291;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;119;-256,-1232;Inherit;False;938.2822;418.4586;HdotH
    Node;2;438;35;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;35;224,-1024;Inherit;False;413.6161;178.033;;;0,0,0,1;Common
    Dot Pruducts used in lighting calculations$$Naming Conventions:$'A-Vector' dot
    'B-Vector'$Example: dot(Light Dir, View Dir) = LdotV$$HdotH - dot( Half Vector,
    Half Vector )$;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;437;592,-464;Inherit;False;449.235;154.571;Light
    Wrapping;;0,0,0,0.5019608;Allows the side of the Obj that is in 'Shadow'/darkness
    to still be seen or while in a low light environment$$for more details see this
    Link:$https://www.jordanstevenstechart.com/lighting-models;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;438;224,-1168;Inherit;False;414.9685;134.1714;;;0,0,0,1;Common
    Normal Calculations typically used in PBR Shaders, expand as necessary or if
    needed. Source: \"Physically Based Rendering Algorithms:$A Comprehensive Study
    In Unity3D\" By Jordan Steve;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;442;-240,-944;Inherit;False;208;100;Inputs;;1,0,0,1;all
    Inputs should be pre normalized prior to input;0;0\nNode;AmplifyShaderEditor.FunctionInput;446;-1072,-656;Inherit;False;Half
    Vector;3;3;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;288;-624,-736;Inherit;False;Half
    Vector;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitch;447;-880,-736;Inherit;False;Custom
    Half Vector;True;0;2;4;In 0;In 1;Object;-1;10;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMaxOpNode;195;848,-672;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;393;592,-592;Inherit;False;Light
    Wrapping;False;0;3;0;Zero;Near Zero;Custom;Object;-1;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSubtitle;449;336,-592;Inherit;False;Light
    Wrapping Zero;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSubtitle;450;304,-512;Inherit;False;Light
    Wrapping Near Zero;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSubtitle;451;304,-432;Inherit;False;Light
    Wrapping Custom;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;331;144,-512;Inherit;False;Constant;_NearZero;Near
    Zero;0;0;Create;True;0;0;0;False;0;False;1E-37;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;367;144,-592;Inherit;False;Constant;_Zero;Zero;0;0;Create;True;0;0;0;False;0;False;0;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;422;112,-432;Inherit;False;Light
    Wrapping;1;4;False;1;0;FLOAT;1E-37;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;291;-192,-672;Inherit;False;288;Half
    Vector;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.DotProductOpNode;194;16,-672;Inherit;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.NormalizeNode;155;-1056,-736;Inherit;False;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;156;-1216,-736;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitch;443;-1504,-736;Inherit;False;Custom
    View Dir;True;0;2;1;In 0;In 1;Object;-1;10;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionInput;440;-1696,-656;Inherit;False;View
    Dir;3;0;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.ViewDirInputsCoordNode;210;-1920,-736;Inherit;False;World;True;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.WorldSpaceLightDirHlpNode;79;-1760,-560;Inherit;False;True;1;0;FLOAT;0;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.FunctionSwitch;445;-1504,-560;Inherit;False;Custom
    Light Dir;True;0;2;2;In 0;In 1;Object;-1;10;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitch;121;-1760,-400;Inherit;False;Fake
    Light Dir;True;0;2;3;In 0;In 1;Object;-1;10;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionInput;439;-1952,-400;Inherit;False;Light
    Dir;3;1;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.NormalizeNode;133;-1952,-320;Inherit;False;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;132;-2080,-320;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionInput;130;-2272,-320;Inherit;False;Fake
    Light Dir;3;2;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.ViewDirInputsCoordNode;131;-2272,-240;Float;False;World;False;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.FunctionOutput;296;992,-672;Inherit;True;True;-1;HdotH;0;True;1;0;FLOAT;0;False;1;FLOAT;0\nWireConnection;288;0;447;0\nWireConnection;447;0;155;0\nWireConnection;447;1;446;0\nWireConnection;195;0;194;0\nWireConnection;195;1;393;0\nWireConnection;393;0;449;0\nWireConnection;393;1;450;0\nWireConnection;393;2;451;0\nWireConnection;449;0;367;0\nWireConnection;450;0;331;0\nWireConnection;451;0;422;0\nWireConnection;194;0;291;0\nWireConnection;194;1;291;0\nWireConnection;155;0;156;0\nWireConnection;156;0;443;0\nWireConnection;156;1;445;0\nWireConnection;443;0;210;0\nWireConnection;443;1;440;0\nWireConnection;445;0;79;0\nWireConnection;445;1;121;0\nWireConnection;121;0;439;0\nWireConnection;121;1;133;0\nWireConnection;133;0;132;0\nWireConnection;132;0;130;0\nWireConnection;132;1;131;0\nWireConnection;296;0;195;0\nASEEND*/\n//CHKSM=1481DCC166992CF62AA292EE6634F3158535F071"
  m_functionName: 
  m_description: 'HdotH Node

'
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 0
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_customEdited: 0
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 0
  m_headerStyle: 2
  m_headerColor: {r: 0.7529412, g: 0.6784314, b: 0, a: 1}
  m_customNodeCategory: Amplify Shader Pack
  m_previewPosition: 0
  m_hidden: 0
  m_url: 
