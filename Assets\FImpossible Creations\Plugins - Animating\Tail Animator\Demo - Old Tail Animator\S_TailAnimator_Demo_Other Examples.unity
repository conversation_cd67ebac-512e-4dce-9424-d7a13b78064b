%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 10
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.45415223, g: 0.5438784, b: 0.7352941, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 3
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 10304, guid: 0000000000000000f000000000000000, type: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &3
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 13
  m_BakeOnSceneLoad: 0
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 0
    m_EnableRealtimeLightmaps: 0
  m_LightmapEditorSettings:
    serializedVersion: 12
    m_Resolution: 0.1
    m_BakeResolution: 40
    m_AtlasSize: 256
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_ExtractAmbientOcclusion: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_ReflectionCompression: 2
    m_MixedBakeMode: 0
    m_BakeBackend: 0
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 500
    m_PVRBounces: 2
    m_PVREnvironmentSampleCount: 500
    m_PVREnvironmentReferencePointCount: 2048
    m_PVRFilteringMode: 2
    m_PVRDenoiserTypeDirect: 0
    m_PVRDenoiserTypeIndirect: 0
    m_PVRDenoiserTypeAO: 0
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVREnvironmentMIS: 0
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ExportTrainingData: 0
    m_TrainingDataDestination: TrainingData
    m_LightProbeSampleCountMultiplier: 4
  m_LightingDataAsset: {fileID: 112000008, guid: 738dab14f22897e49a12eb102bb1a2ff, type: 2}
  m_LightingSettings: {fileID: 4890085278179872738, guid: 6aabc5bdb7b8b7641b3257ac92a92c03, type: 2}
--- !u!196 &4
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 3
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    buildHeightMesh: 0
    maxJobWorkers: 0
    preserveTilesOutsideBounds: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!1 &59881194
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 59881195}
  - component: {fileID: 59881197}
  - component: {fileID: 59881196}
  m_Layer: 5
  m_Name: UI Tail Example (6)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &59881195
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 59881194}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 482676023}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 15.4, y: 0}
  m_SizeDelta: {x: 12, y: 25}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &59881196
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 59881194}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10911, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &59881197
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 59881194}
  m_CullTransparentMesh: 1
--- !u!1 &76912343
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 76912344}
  - component: {fileID: 76912346}
  - component: {fileID: 76912345}
  m_Layer: 0
  m_Name: Sphere
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &76912344
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 76912343}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0, y: 0.899, z: -0}
  m_LocalScale: {x: 0.18418181, y: 0.18418187, z: 0.18418187}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1639218349}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!23 &76912345
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 76912343}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: cebc233f10f3ee149bf21ce02d5363f8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 1
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &76912346
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 76912343}
  m_Mesh: {fileID: 10207, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &78370397
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 78370398}
  - component: {fileID: 78370400}
  - component: {fileID: 78370399}
  m_Layer: 5
  m_Name: UI Tail Example (5)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &78370398
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 78370397}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2075760668}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 15.989996, y: -0.0000023376942}
  m_SizeDelta: {x: 12, y: 25}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &78370399
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 78370397}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10911, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &78370400
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 78370397}
  m_CullTransparentMesh: 1
--- !u!1 &93597052
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 93597055}
  - component: {fileID: 93597054}
  - component: {fileID: 93597053}
  m_Layer: 0
  m_Name: EventSystem
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &93597053
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 93597052}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4f231c4fb786f3946a6b90b886c48677, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_SendPointerHoverToParent: 1
  m_HorizontalAxis: Horizontal
  m_VerticalAxis: Vertical
  m_SubmitButton: Submit
  m_CancelButton: Cancel
  m_InputActionsPerSecond: 10
  m_RepeatDelay: 0.5
  m_ForceModuleActive: 0
--- !u!114 &93597054
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 93597052}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 76c392e42b5098c458856cdf6ecaaaa1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_FirstSelected: {fileID: 0}
  m_sendNavigationEvents: 1
  m_DragThreshold: 5
--- !u!4 &93597055
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 93597052}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &106963462
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 106963463}
  m_Layer: 0
  m_Name: ----------------------------------
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &106963463
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 106963462}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 1.5048004, y: -2.4112737, z: 28.424793}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &114507907
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 1531704303419712, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_Name
      value: PR_Sign
      objectReference: {fileID: 0}
    - target: {fileID: 4385575217446778, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_LocalScale.x
      value: 1.7
      objectReference: {fileID: 0}
    - target: {fileID: 4878481119553484, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_RootOrder
      value: 15
      objectReference: {fileID: 0}
    - target: {fileID: 4878481119553484, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_LocalPosition.x
      value: -12.46
      objectReference: {fileID: 0}
    - target: {fileID: 4878481119553484, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.049999952
      objectReference: {fileID: 0}
    - target: {fileID: 4878481119553484, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_LocalPosition.z
      value: -2.55
      objectReference: {fileID: 0}
    - target: {fileID: 4878481119553484, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_LocalRotation.w
      value: -0.7977629
      objectReference: {fileID: 0}
    - target: {fileID: 4878481119553484, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4878481119553484, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0.6029713
      objectReference: {fileID: 0}
    - target: {fileID: 4878481119553484, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4878481119553484, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: -285.83398
      objectReference: {fileID: 0}
    - target: {fileID: 102948136206050596, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_Text
      value: 'Have idea for other use

        of this component?

        Write
        it in package

        thread then I can

        make it become real!'
      objectReference: {fileID: 0}
    - target: {fileID: 102948136206050596, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_FontSize
      value: 39
      objectReference: {fileID: 0}
    - target: {fileID: 102948136206050596, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_Color.rgba
      value: 4278190080
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
--- !u!4 &********* stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 400000, guid: 034fdf2d85d2ae1448831ea892f09a84, type: 3}
  m_PrefabInstance: {fileID: 206509717}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &127307185
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 127307186}
  - component: {fileID: 127307188}
  - component: {fileID: 127307187}
  m_Layer: 5
  m_Name: UI Tail Example (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &127307186
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 127307185}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 819507954}
  m_Father: {fileID: 1178527600}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 90}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: -15.5001135, y: 0.00002861023}
  m_SizeDelta: {x: 20, y: 11.23}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &127307187
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 127307185}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10911, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &127307188
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 127307185}
  m_CullTransparentMesh: 1
--- !u!21 &129108427
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: MAT_FBasic_Floor (Instance) (Instance)
  m_Shader: {fileID: 46, guid: 0000000000000000f000000000000000, type: 0}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords: []
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 3.17, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 10305, guid: 0000000000000000f000000000000000, type: 0}
        m_Scale: {x: 1000, y: 1000}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _BumpScale: 1
    - _Cutoff: 0.5
    - _DetailNormalMapScale: 1
    - _DstBlend: 0
    - _GlossMapScale: 1
    - _Glossiness: 0
    - _GlossyReflections: 1
    - _Metallic: 0
    - _Mode: 0
    - _OcclusionStrength: 1
    - _Parallax: 0.02
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _UVSec: 0
    - _ZWrite: 1
    m_Colors:
    - _Color: {r: 0.71323526, g: 0.71323526, b: 0.71323526, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
  m_BuildTextureStacks: []
  m_AllowLocking: 1
--- !u!1001 &206509717
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 100016, guid: 034fdf2d85d2ae1448831ea892f09a84, type: 3}
      propertyPath: m_Name
      value: FFishSchoolLowpolyNotColored
      objectReference: {fileID: 0}
    - target: {fileID: 400016, guid: 034fdf2d85d2ae1448831ea892f09a84, type: 3}
      propertyPath: m_RootOrder
      value: 9
      objectReference: {fileID: 0}
    - target: {fileID: 400016, guid: 034fdf2d85d2ae1448831ea892f09a84, type: 3}
      propertyPath: m_LocalScale.x
      value: 0.35097086
      objectReference: {fileID: 0}
    - target: {fileID: 400016, guid: 034fdf2d85d2ae1448831ea892f09a84, type: 3}
      propertyPath: m_LocalScale.y
      value: 0.35097086
      objectReference: {fileID: 0}
    - target: {fileID: 400016, guid: 034fdf2d85d2ae1448831ea892f09a84, type: 3}
      propertyPath: m_LocalScale.z
      value: 0.35097086
      objectReference: {fileID: 0}
    - target: {fileID: 400016, guid: 034fdf2d85d2ae1448831ea892f09a84, type: 3}
      propertyPath: m_LocalPosition.x
      value: -14.82
      objectReference: {fileID: 0}
    - target: {fileID: 400016, guid: 034fdf2d85d2ae1448831ea892f09a84, type: 3}
      propertyPath: m_LocalPosition.y
      value: 2.499
      objectReference: {fileID: 0}
    - target: {fileID: 400016, guid: 034fdf2d85d2ae1448831ea892f09a84, type: 3}
      propertyPath: m_LocalPosition.z
      value: -2.44
      objectReference: {fileID: 0}
    - target: {fileID: 400016, guid: 034fdf2d85d2ae1448831ea892f09a84, type: 3}
      propertyPath: m_LocalRotation.w
      value: -0.018010706
      objectReference: {fileID: 0}
    - target: {fileID: 400016, guid: 034fdf2d85d2ae1448831ea892f09a84, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 400016, guid: 034fdf2d85d2ae1448831ea892f09a84, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0.9998378
      objectReference: {fileID: 0}
    - target: {fileID: 400016, guid: 034fdf2d85d2ae1448831ea892f09a84, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 400016, guid: 034fdf2d85d2ae1448831ea892f09a84, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: -182.064
      objectReference: {fileID: 0}
    - target: {fileID: 13700000, guid: 034fdf2d85d2ae1448831ea892f09a84, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: d174228b01b29c741a5e3f4a2d275ff1, type: 2}
    m_RemovedComponents:
    - {fileID: 9500000, guid: 034fdf2d85d2ae1448831ea892f09a84, type: 3}
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 100016, guid: 034fdf2d85d2ae1448831ea892f09a84, type: 3}
      insertIndex: -1
      addedObject: {fileID: 616513398}
    - targetCorrespondingSourceObject: {fileID: 100016, guid: 034fdf2d85d2ae1448831ea892f09a84, type: 3}
      insertIndex: -1
      addedObject: {fileID: 616513399}
  m_SourcePrefab: {fileID: 100100000, guid: 034fdf2d85d2ae1448831ea892f09a84, type: 3}
--- !u!1 &211133274
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 211133275}
  m_Layer: 5
  m_Name: UI Tail Origin (3)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &211133275
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 211133274}
  m_LocalRotation: {x: 0, y: -0, z: 0.7071068, w: -0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1759932473}
  m_Father: {fileID: 1536037902}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 270}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 10.7}
  m_SizeDelta: {x: 16, y: 10}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!4 &********* stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 400000, guid: 034fdf2d85d2ae1448831ea892f09a84, type: 3}
  m_PrefabInstance: {fileID: 1044335567}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &234028344
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 234028345}
  m_Layer: 0
  m_Name: BTail_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &234028345
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 234028344}
  serializedVersion: 2
  m_LocalRotation: {x: 4.132302e-10, y: -1.3796549e-33, z: -3.338708e-24, w: 1}
  m_LocalPosition: {x: -1.7774535e-17, y: 7.2653406e-20, z: -0.15149006}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 904911346}
  m_Father: {fileID: 1340436495}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &245975516
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 245975517}
  - component: {fileID: 245975519}
  - component: {fileID: 245975518}
  m_Layer: 0
  m_Name: Sign Text (4)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &245975517
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 245975516}
  serializedVersion: 2
  m_LocalRotation: {x: -0.000000014901161, y: 0.93749475, z: -0.3479994, w: 0.000000029802322}
  m_LocalPosition: {x: 0.8439999, y: 2.35, z: -0.22299996}
  m_LocalScale: {x: 0.9999998, y: 0.9999999, z: 0.99999976}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 365727461}
  m_LocalEulerAnglesHint: {x: 40.73, y: 180, z: 0}
--- !u!102 &245975518
TextMesh:
  serializedVersion: 3
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 245975516}
  m_Text: 'partial blend

    with source'
  m_OffsetZ: 0
  m_CharacterSize: 0.02
  m_LineSpacing: 1
  m_Anchor: 4
  m_Alignment: 1
  m_TabSize: 4
  m_FontSize: 31
  m_FontStyle: 0
  m_RichText: 0
  m_Font: {fileID: 12800000, guid: dbc9b820232f93d4aab6f1d3bdd0d4d3, type: 3}
  m_Color:
    serializedVersion: 2
    rgba: 4278190080
--- !u!23 &245975519
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 245975516}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10100, guid: 0000000000000000e000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &252233851
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 252233852}
  - component: {fileID: 252233854}
  - component: {fileID: 252233853}
  m_Layer: 5
  m_Name: UI Tail Example (3)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &252233852
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 252233851}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1921401580}
  m_Father: {fileID: 1693931111}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 19.53, y: 0}
  m_SizeDelta: {x: 20, y: 11.23}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &252233853
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 252233851}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10911, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &252233854
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 252233851}
  m_CullTransparentMesh: 1
--- !u!1 &263131698
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 263131700}
  - component: {fileID: 263131699}
  m_Layer: 0
  m_Name: Point light
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!108 &263131699
Light:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 263131698}
  m_Enabled: 1
  serializedVersion: 11
  m_Type: 2
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_Intensity: 0.58
  m_Range: 10
  m_SpotAngle: 30
  m_InnerSpotAngle: 21.80208
  m_CookieSize: 10
  m_Shadows:
    m_Type: 0
    m_Resolution: -1
    m_CustomResolution: -1
    m_Strength: 1
    m_Bias: 0.05
    m_NormalBias: 0.4
    m_NearPlane: 0.2
    m_CullingMatrixOverride:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    m_UseCullingMatrixOverride: 0
  m_Cookie: {fileID: 0}
  m_DrawHalo: 0
  m_Flare: {fileID: 0}
  m_RenderMode: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingLayerMask: 1
  m_Lightmapping: 4
  m_LightShadowCasterMode: 0
  m_AreaSize: {x: 1, y: 1}
  m_BounceIntensity: 1
  m_ColorTemperature: 6570
  m_UseColorTemperature: 0
  m_BoundingSphereOverride: {x: 0, y: 0, z: 0, w: 0}
  m_UseBoundingSphereOverride: 0
  m_UseViewFrustumForShadowCasterCull: 1
  m_ForceVisible: 0
  m_ShadowRadius: 0
  m_ShadowAngle: 0
  m_LightUnit: 1
  m_LuxAtDistance: 1
  m_EnableSpotReflector: 1
--- !u!4 &263131700
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 263131698}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -2.16, y: 2.3, z: -0.96}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &267416478 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 1337424202793534, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
  m_PrefabInstance: {fileID: 365727457}
  m_PrefabAsset: {fileID: 0}
--- !u!65 &267416479
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 267416478}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 1
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.7487983, y: 1, z: 1.2494229}
  m_Center: {x: 0.023681285, y: 0, z: 0.04284382}
--- !u!135 &267416481
SphereCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 267416478}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 1
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Radius: 1
  m_Center: {x: 0, y: 0, z: 0}
--- !u!1 &302796071
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 302796072}
  - component: {fileID: 302796074}
  - component: {fileID: 302796073}
  m_Layer: 5
  m_Name: UI Tail Example
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &302796072
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 302796071}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1381883038}
  m_Father: {fileID: 1556654199}
  m_LocalEulerAnglesHint: {x: 0, y: 180, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: -17.829992, y: 0}
  m_SizeDelta: {x: 20, y: 11.23}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &302796073
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 302796071}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10911, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &302796074
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 302796071}
  m_CullTransparentMesh: 1
--- !u!1001 &303658580
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 4325343788362922, guid: 176e4f0acb85ffe418c9b31490361eb6, type: 3}
      propertyPath: m_RootOrder
      value: 20
      objectReference: {fileID: 0}
    - target: {fileID: 4325343788362922, guid: 176e4f0acb85ffe418c9b31490361eb6, type: 3}
      propertyPath: m_LocalPosition.x
      value: -7.32
      objectReference: {fileID: 0}
    - target: {fileID: 4325343788362922, guid: 176e4f0acb85ffe418c9b31490361eb6, type: 3}
      propertyPath: m_LocalPosition.y
      value: 2.11
      objectReference: {fileID: 0}
    - target: {fileID: 4325343788362922, guid: 176e4f0acb85ffe418c9b31490361eb6, type: 3}
      propertyPath: m_LocalPosition.z
      value: 6.6251616
      objectReference: {fileID: 0}
    - target: {fileID: 4325343788362922, guid: 176e4f0acb85ffe418c9b31490361eb6, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4325343788362922, guid: 176e4f0acb85ffe418c9b31490361eb6, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4325343788362922, guid: 176e4f0acb85ffe418c9b31490361eb6, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4325343788362922, guid: 176e4f0acb85ffe418c9b31490361eb6, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 176e4f0acb85ffe418c9b31490361eb6, type: 3}
--- !u!1001 &365727457
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 4093769700768706, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
      propertyPath: m_RootOrder
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 4299260458184688, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
      propertyPath: m_RootOrder
      value: 4
      objectReference: {fileID: 0}
    - target: {fileID: 4603175785082560, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
      propertyPath: m_RootOrder
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 4676364681455862, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
      propertyPath: m_RootOrder
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 4676364681455862, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
      propertyPath: m_LocalScale.x
      value: 1.544
      objectReference: {fileID: 0}
    - target: {fileID: 4676364681455862, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0.27542
      objectReference: {fileID: 0}
    - target: {fileID: 4676364681455862, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.78999
      objectReference: {fileID: 0}
    - target: {fileID: 4676364681455862, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0.00036415
      objectReference: {fileID: 0}
    - target: {fileID: 4706356690559054, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
      propertyPath: m_RootOrder
      value: 12
      objectReference: {fileID: 0}
    - target: {fileID: 4706356690559054, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
      propertyPath: m_LocalPosition.x
      value: -0.85
      objectReference: {fileID: 0}
    - target: {fileID: 4706356690559054, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
      propertyPath: m_LocalPosition.y
      value: -1.17
      objectReference: {fileID: 0}
    - target: {fileID: 4706356690559054, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
      propertyPath: m_LocalPosition.z
      value: 5.44
      objectReference: {fileID: 0}
    - target: {fileID: 4706356690559054, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.5054394
      objectReference: {fileID: 0}
    - target: {fileID: 4706356690559054, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4706356690559054, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0.8628621
      objectReference: {fileID: 0}
    - target: {fileID: 4706356690559054, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4706356690559054, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: -119.27901
      objectReference: {fileID: 0}
    - target: {fileID: 114726729997197450, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
      propertyPath: StartValueY
      value: 0.676
      objectReference: {fileID: 0}
    m_RemovedComponents:
    - {fileID: 135365784624762536, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
    m_RemovedGameObjects: []
    m_AddedGameObjects:
    - targetCorrespondingSourceObject: {fileID: 4706356690559054, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
      insertIndex: -1
      addedObject: {fileID: 1047143184}
    - targetCorrespondingSourceObject: {fileID: 4706356690559054, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
      insertIndex: -1
      addedObject: {fileID: 1791080538}
    - targetCorrespondingSourceObject: {fileID: 4706356690559054, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
      insertIndex: -1
      addedObject: {fileID: 1568744427}
    - targetCorrespondingSourceObject: {fileID: 4706356690559054, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
      insertIndex: -1
      addedObject: {fileID: 910210864}
    - targetCorrespondingSourceObject: {fileID: 4706356690559054, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
      insertIndex: -1
      addedObject: {fileID: 1960043526}
    - targetCorrespondingSourceObject: {fileID: 4706356690559054, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
      insertIndex: -1
      addedObject: {fileID: 1549329016}
    - targetCorrespondingSourceObject: {fileID: 4706356690559054, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
      insertIndex: -1
      addedObject: {fileID: 245975517}
    - targetCorrespondingSourceObject: {fileID: 4706356690559054, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
      insertIndex: -1
      addedObject: {fileID: 722651017}
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 1771505761597716, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
      insertIndex: -1
      addedObject: {fileID: 365727460}
    - targetCorrespondingSourceObject: {fileID: 1771505761597716, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
      insertIndex: -1
      addedObject: {fileID: 365727462}
    - targetCorrespondingSourceObject: {fileID: 1337424202793534, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
      insertIndex: -1
      addedObject: {fileID: 267416479}
    - targetCorrespondingSourceObject: {fileID: 1337424202793534, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
      insertIndex: -1
      addedObject: {fileID: 267416481}
  m_SourcePrefab: {fileID: 100100000, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
--- !u!1 &365727458 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 1771505761597716, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
  m_PrefabInstance: {fileID: 365727457}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &365727459 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 114726729997197450, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
  m_PrefabInstance: {fileID: 365727457}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0dee3b3dda5183c4f89654b2dd4a822f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &365727460
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 365727458}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1043e4251d76645449c11c116da7571e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  lever: {fileID: 365727459}
  tailToBlend: {fileID: 0}
  blendChain: 0
--- !u!4 &365727461 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4706356690559054, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
  m_PrefabInstance: {fileID: 365727457}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &365727462
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 365727458}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1043e4251d76645449c11c116da7571e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  lever: {fileID: 1047143182}
  tailToBlend: {fileID: 0}
  blendChain: 1
--- !u!1 &388542839
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 388542840}
  - component: {fileID: 388542842}
  - component: {fileID: 388542841}
  m_Layer: 0
  m_Name: Cylinder
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &388542840
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 388542839}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: 0.7071068, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -0, y: 0.434, z: 0}
  m_LocalScale: {x: 0.1, y: 0.45, z: 0.1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1639218349}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!23 &388542841
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 388542839}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1adc3fed471f0144aa41262fdc7de3c7, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 1
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &388542842
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 388542839}
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &428257778
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1247455685}
  m_Layer: 0
  m_Name: BTail_1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &455146902
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 455146903}
  - component: {fileID: 455146905}
  - component: {fileID: 455146904}
  m_Layer: 5
  m_Name: UI Tail Example (3)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &455146903
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 455146902}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2075760668}
  m_Father: {fileID: 1810130950}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 19.53, y: 0}
  m_SizeDelta: {x: 20, y: 11.23}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &455146904
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 455146902}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10911, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &455146905
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 455146902}
  m_CullTransparentMesh: 1
--- !u!1 &469462810
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 469462814}
  - component: {fileID: 469462813}
  - component: {fileID: 469462812}
  - component: {fileID: 469462811}
  - component: {fileID: 469462815}
  m_Layer: 5
  m_Name: Floor
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!23 &469462811
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 469462810}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 129108427}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 1
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &469462812
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 469462810}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!33 &469462813
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 469462810}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &469462814
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 469462810}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 2.0076346, y: -4.05, z: -3.48}
  m_LocalScale: {x: 1000, y: 10, z: 1000}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1717911228}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &469462815
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 469462810}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6604dc3407fb5f64f8fed44b2a0b3b9a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  TextureProperty: _MainTex
  ScaleValues: {x: 1, y: 1}
  EqualDimensions: 0
--- !u!1 &474176125
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 474176127}
  - component: {fileID: 474176126}
  m_Layer: 0
  m_Name: Point light
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!108 &474176126
Light:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 474176125}
  m_Enabled: 1
  serializedVersion: 11
  m_Type: 2
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_Intensity: 0.3
  m_Range: 10
  m_SpotAngle: 30
  m_InnerSpotAngle: 21.80208
  m_CookieSize: 10
  m_Shadows:
    m_Type: 0
    m_Resolution: -1
    m_CustomResolution: -1
    m_Strength: 1
    m_Bias: 0.05
    m_NormalBias: 0.4
    m_NearPlane: 0.2
    m_CullingMatrixOverride:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    m_UseCullingMatrixOverride: 0
  m_Cookie: {fileID: 0}
  m_DrawHalo: 0
  m_Flare: {fileID: 0}
  m_RenderMode: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingLayerMask: 1
  m_Lightmapping: 4
  m_LightShadowCasterMode: 0
  m_AreaSize: {x: 1, y: 1}
  m_BounceIntensity: 1
  m_ColorTemperature: 6570
  m_UseColorTemperature: 0
  m_BoundingSphereOverride: {x: 0, y: 0, z: 0, w: 0}
  m_UseBoundingSphereOverride: 0
  m_UseViewFrustumForShadowCasterCull: 1
  m_ForceVisible: 0
  m_ShadowRadius: 0
  m_ShadowAngle: 0
  m_LightUnit: 1
  m_LuxAtDistance: 1
  m_EnableSpotReflector: 1
--- !u!4 &474176127
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 474176125}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -2.16, y: 1.83, z: 3.52}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &475779537
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 475779538}
  m_Layer: 0
  m_Name: BTail_8
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &475779538
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 475779537}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0000000017427209, y: 1.3119528e-32, z: -6.617454e-24, w: 1}
  m_LocalPosition: {x: 5.684774e-17, y: 2.0328792e-22, z: -0.14585431}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1289658228}
  m_Father: {fileID: 1729142642}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &482676022
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 482676023}
  - component: {fileID: 482676025}
  - component: {fileID: 482676024}
  m_Layer: 5
  m_Name: UI Tail Example (5)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &482676023
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 482676022}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 59881195}
  m_Father: {fileID: 923048215}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 20.24, y: -0.000058707}
  m_SizeDelta: {x: 20, y: 11.23}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &482676024
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 482676022}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10911, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &482676025
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 482676022}
  m_CullTransparentMesh: 1
--- !u!1 &********* stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 100016, guid: 034fdf2d85d2ae1448831ea892f09a84, type: 3}
  m_PrefabInstance: {fileID: 206509717}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &616513398
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: *********}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: afd3a8c0c34ba4d4d9679edb4af4db76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  TailTransforms:
  - {fileID: *********}
  RootToParent: 0
  AutoGetWithOne: 1
  InitBeforeAnimator: 0
  proceduralPoints: []
  localProceduralPoints: []
  PositionSpeed: 18
  RotationSpeed: 9
  MaxStretching: 1
  Sensitivity: 0.5
  Springiness: 0
  AngleLimit: 90
  AngleLimitAxis: {x: 0, y: 0, z: 0}
  LimitAxisRange: {x: 0, y: 0}
  LimitSmoothing: 0.15
  MotionInfluence: 1
  StiffTailEnd: 0
  UseAutoCorrectLookAxis: 1
  FullCorrection: 0
  LookUpMethod: 0
  OrientationReference: {fileID: 0}
  AnimateCorrections: 0
  AnimateRoot: 1
  LengthMultiplier: 1
  AxisCorrection: {x: 0, y: 0, z: 1}
  AxisLookBack: {x: 0, y: 1, z: 0}
  ExtraCorrectionOptions: 0
  ExtraFromDirection: {x: 0, y: 0, z: 1}
  ExtraToDirection: {x: 0, y: 0, z: 1}
  AddTailReferences: 0
  UpdateClock: 0
  SafeDeltaTime: 1
  RefreshHelpers: 0
  QueueToLastUpdate: 0
  UseCollision: 0
  CollisionMethod: 1
  CollidersType: 0
  CollideWithOtherTails: 0
  DetailedCollision: 0
  CollidersScale:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  CollidersScaleMul: 6.5
  DifferenceScaleFactor: 1
  IgnoredColliders: []
  BoxesDimensionsMul: {x: 1, y: 1, z: 1}
  CollidersSameLayer: 1
  CollidersAddRigidbody: 1
  RigidbodyMass: 1
  CollidersLayer:
    serializedVersion: 2
    m_Bits: 0
  CollisionSwapping: 0.5
  CollisionSpace: 0
  IncludedColliders: []
  Curving: {x: 0, y: 0, z: 0}
  Gravity: {x: 0, y: 0, z: 0}
  RootRotationOffset: {x: 0, y: 0, z: 0}
  RootPositionOffset: {x: 0, y: 0, z: 0}
  OptimizeWithMesh: {fileID: 0}
  RootPoint:
    index: -1
    Transform: {fileID: 0}
    Position: {x: 0, y: 0, z: 0}
    Rotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousRotation: {x: 0, y: 0, z: 0, w: 1}
    InitialRotation: {x: 0, y: 0, z: 0, w: 1}
    InitialLossyScale: {x: 1, y: 1, z: 1}
    PreviousPosition: {x: 0, y: 0, z: 0}
    SpringOffset: {x: 0, y: 0, z: 0}
    SpringOffset2: {x: 0, y: 0, z: 0}
    LookDirection: {x: 0, y: 0, z: 0}
    InitLookDirection: {x: 0, y: 0, z: 0}
    CrossUp: {x: 0, y: 1, z: 0}
    BoneLength: 0
    InitBoneLength: 0
    ScaleFactor: 1
    Correction: {x: 0, y: 0, z: 0, w: 1}
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: 0, y: 0, z: 0, w: 1}
    LookBackDirection: {x: 0, y: 0, z: 0}
    RotationTargetPos: {x: 0, y: 0, z: 0}
    PreCollisionPosition: {x: 0, y: 0, z: 0}
    PreCollisionRotation: {x: 0, y: 0, z: 0, w: 0}
    ColliderRadius: 1
    collisionOffsets: {x: 0, y: 0, z: 0}
    collisionFlags: 0
  initializationBone1: {fileID: 0}
  initializationBone2: {fileID: 0}
  CollisionLookBack: 1
  drawGizmos: 0
  UseWaving: 1
  CosinusAdd: 0
  WavingSpeed: 7
  WavingRange: 0.3
  WavingAxis: {x: 0.2, y: 0, z: 1}
  TailRotationOffset: {x: 0, y: 0, z: 0}
  WavingType: 0
  AlternateWave: 1
  DisconnectTransforms: 0
--- !u!114 &616513399
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: *********}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 43491a0ac6e9bb04cb722d4ed6d70d5e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  MainSpeed: 0.74
  RangeValue: {x: 0.79, y: 1, z: 0.58}
  RangeMul: 3.88
  AddYSin: 1
  AddYSinTimeSpeed: 1
  RotateForwardSpeed: 10
--- !u!4 &620441006 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4676224384890894, guid: 176e4f0acb85ffe418c9b31490361eb6, type: 3}
  m_PrefabInstance: {fileID: 1997598098}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &623215216
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 1771505761597716, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
      propertyPath: m_Name
      value: PR_ExampleLeverPanel (1)
      objectReference: {fileID: 0}
    - target: {fileID: 4093769700768706, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4299260458184688, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
      propertyPath: m_RootOrder
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 4603175785082560, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
      propertyPath: m_RootOrder
      value: 4
      objectReference: {fileID: 0}
    - target: {fileID: 4676364681455862, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
      propertyPath: m_RootOrder
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 4676364681455862, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
      propertyPath: m_LocalScale.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4676364681455862, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0.001
      objectReference: {fileID: 0}
    - target: {fileID: 4676364681455862, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.78999
      objectReference: {fileID: 0}
    - target: {fileID: 4676364681455862, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4706356690559054, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
      propertyPath: m_RootOrder
      value: 25
      objectReference: {fileID: 0}
    - target: {fileID: 4706356690559054, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
      propertyPath: m_LocalPosition.x
      value: -0.37
      objectReference: {fileID: 0}
    - target: {fileID: 4706356690559054, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
      propertyPath: m_LocalPosition.y
      value: -1.17
      objectReference: {fileID: 0}
    - target: {fileID: 4706356690559054, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
      propertyPath: m_LocalPosition.z
      value: -2.97
      objectReference: {fileID: 0}
    - target: {fileID: 4706356690559054, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.7299824
      objectReference: {fileID: 0}
    - target: {fileID: 4706356690559054, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4706356690559054, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0.6834661
      objectReference: {fileID: 0}
    - target: {fileID: 4706356690559054, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4706356690559054, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: -86.23
      objectReference: {fileID: 0}
    m_RemovedComponents:
    - {fileID: 135365784624762536, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
    m_RemovedGameObjects: []
    m_AddedGameObjects:
    - targetCorrespondingSourceObject: {fileID: 4706356690559054, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
      insertIndex: -1
      addedObject: {fileID: 2087479453}
    - targetCorrespondingSourceObject: {fileID: 4706356690559054, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
      insertIndex: -1
      addedObject: {fileID: 726694885}
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 1771505761597716, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
      insertIndex: -1
      addedObject: {fileID: 623215221}
    - targetCorrespondingSourceObject: {fileID: 1337424202793534, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
      insertIndex: -1
      addedObject: {fileID: 623215222}
    - targetCorrespondingSourceObject: {fileID: 1337424202793534, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
      insertIndex: -1
      addedObject: {fileID: 623215223}
  m_SourcePrefab: {fileID: 100100000, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
--- !u!1 &623215217 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 1771505761597716, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
  m_PrefabInstance: {fileID: 623215216}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &623215218 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 114726729997197450, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
  m_PrefabInstance: {fileID: 623215216}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0dee3b3dda5183c4f89654b2dd4a822f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &623215219 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 1337424202793534, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
  m_PrefabInstance: {fileID: 623215216}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &623215220 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4706356690559054, guid: 95576ee797e8e374a9b4155acf986be6, type: 3}
  m_PrefabInstance: {fileID: 623215216}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &623215221
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 623215217}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1043e4251d76645449c11c116da7571e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  lever: {fileID: 623215218}
  tailToBlend: {fileID: 1527365684}
  blendChain: 0
--- !u!135 &623215222
SphereCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 623215219}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 1
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Radius: 1
  m_Center: {x: 0, y: 0, z: 0}
--- !u!65 &623215223
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 623215219}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 1
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.7487983, y: 1, z: 1.2494229}
  m_Center: {x: 0.023681285, y: 0, z: 0.04284382}
--- !u!1001 &642893034
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 1531704303419712, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_Name
      value: PR_Sign (1)
      objectReference: {fileID: 0}
    - target: {fileID: 4385575217446778, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_LocalScale.x
      value: 1.7896425
      objectReference: {fileID: 0}
    - target: {fileID: 4385575217446778, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_LocalScale.y
      value: 0.96
      objectReference: {fileID: 0}
    - target: {fileID: 4385575217446778, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_LocalPosition.y
      value: 1.39
      objectReference: {fileID: 0}
    - target: {fileID: 4878481119553484, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_RootOrder
      value: 24
      objectReference: {fileID: 0}
    - target: {fileID: 4878481119553484, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_LocalPosition.x
      value: -0.28
      objectReference: {fileID: 0}
    - target: {fileID: 4878481119553484, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.050000012
      objectReference: {fileID: 0}
    - target: {fileID: 4878481119553484, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_LocalPosition.z
      value: -0.51
      objectReference: {fileID: 0}
    - target: {fileID: 4878481119553484, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.6969138
      objectReference: {fileID: 0}
    - target: {fileID: 4878481119553484, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4878481119553484, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0.71715504
      objectReference: {fileID: 0}
    - target: {fileID: 4878481119553484, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4878481119553484, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: -91.64001
      objectReference: {fileID: 0}
    - target: {fileID: 23751581541862106, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: f1cd8effbb084d048885dabeab62f910, type: 2}
    - target: {fileID: 102948136206050596, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_Text
      value: 'combining keyframed and tail

        animator motion is the most

        effective
        way to blend animation


        just click on button

        "connect with
        animator"'
      objectReference: {fileID: 0}
    - target: {fileID: 102948136206050596, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_FontSize
      value: 31
      objectReference: {fileID: 0}
    - target: {fileID: 102948136206050596, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_Color.rgba
      value: 4278190080
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
--- !u!1 &664454797
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 664454798}
  m_Layer: 0
  m_Name: ----------------------------------
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &664454798
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 664454797}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 1.5048004, y: -2.4112737, z: 28.424793}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &677890913 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4327838307410568, guid: 176e4f0acb85ffe418c9b31490361eb6, type: 3}
  m_PrefabInstance: {fileID: 1997598098}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &681307601
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 681307602}
  m_Layer: 5
  m_Name: UI Tail Origin (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &681307602
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 681307601}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1431716518}
  m_Father: {fileID: 1536037902}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: -10.79999, y: 0}
  m_SizeDelta: {x: 16, y: 10}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1001 &695748139
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 1531704303419712, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_Name
      value: PR_Sign
      objectReference: {fileID: 0}
    - target: {fileID: 4385575217446778, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_LocalScale.x
      value: 2.0109594
      objectReference: {fileID: 0}
    - target: {fileID: 4385575217446778, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_LocalScale.y
      value: 1.1727312
      objectReference: {fileID: 0}
    - target: {fileID: 4841337806935654, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_LocalScale.y
      value: 1.0791639
      objectReference: {fileID: 0}
    - target: {fileID: 4841337806935654, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_LocalPosition.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4841337806935654, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_LocalPosition.z
      value: -0.049
      objectReference: {fileID: 0}
    - target: {fileID: 4878481119553484, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_RootOrder
      value: 14
      objectReference: {fileID: 0}
    - target: {fileID: 4878481119553484, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_LocalPosition.x
      value: -8.63
      objectReference: {fileID: 0}
    - target: {fileID: 4878481119553484, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.05
      objectReference: {fileID: 0}
    - target: {fileID: 4878481119553484, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_LocalPosition.z
      value: -6.98
      objectReference: {fileID: 0}
    - target: {fileID: 4878481119553484, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.99569243
      objectReference: {fileID: 0}
    - target: {fileID: 4878481119553484, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4878481119553484, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.09271817
      objectReference: {fileID: 0}
    - target: {fileID: 4878481119553484, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4878481119553484, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 10.64
      objectReference: {fileID: 0}
    - target: {fileID: 102948136206050596, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_Text
      value: 'If you want add tail

        animator to animated

        object,
        you should use

        "late update'' option in

        tail animator component

        or
        use blending component'
      objectReference: {fileID: 0}
    - target: {fileID: 102948136206050596, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_FontSize
      value: 39
      objectReference: {fileID: 0}
    - target: {fileID: 102948136206050596, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_Color.rgba
      value: 4278190080
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
--- !u!1 &722651016
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 722651017}
  - component: {fileID: 722651019}
  - component: {fileID: 722651018}
  m_Layer: 0
  m_Name: Sign Text (5)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &722651017
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 722651016}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0.92307603, z: -0.3846176, w: 0}
  m_LocalPosition: {x: 0.834, y: 2.126, z: 0.112}
  m_LocalScale: {x: 0.9999998, y: 0.9999999, z: 0.99999976}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 365727461}
  m_LocalEulerAnglesHint: {x: 45.24, y: 180, z: 0}
--- !u!102 &722651018
TextMesh:
  serializedVersion: 3
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 722651016}
  m_Text: 'works only

    when blend

    is greater than 0

    or lower than 1'
  m_OffsetZ: 0
  m_CharacterSize: 0.017
  m_LineSpacing: 1
  m_Anchor: 4
  m_Alignment: 1
  m_TabSize: 4
  m_FontSize: 31
  m_FontStyle: 0
  m_RichText: 0
  m_Font: {fileID: 12800000, guid: dbc9b820232f93d4aab6f1d3bdd0d4d3, type: 3}
  m_Color:
    serializedVersion: 2
    rgba: 4278190080
--- !u!23 &722651019
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 722651016}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10100, guid: 0000000000000000e000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &726694884
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 726694885}
  - component: {fileID: 726694887}
  - component: {fileID: 726694886}
  m_Layer: 0
  m_Name: Sign Text (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &726694885
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 726694884}
  serializedVersion: 2
  m_LocalRotation: {x: -0.000000014901161, y: 0.93749475, z: -0.3479994, w: 0.000000029802322}
  m_LocalPosition: {x: -0.287, y: 2.35, z: -0.223}
  m_LocalScale: {x: 0.9999998, y: 0.9999999, z: 0.99999976}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 623215220}
  m_LocalEulerAnglesHint: {x: 40.73, y: 180, z: 0}
--- !u!102 &726694886
TextMesh:
  serializedVersion: 3
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 726694884}
  m_Text: 'only source

    animation'
  m_OffsetZ: 0
  m_CharacterSize: 0.019
  m_LineSpacing: 1
  m_Anchor: 4
  m_Alignment: 1
  m_TabSize: 4
  m_FontSize: 31
  m_FontStyle: 0
  m_RichText: 0
  m_Font: {fileID: 12800000, guid: dbc9b820232f93d4aab6f1d3bdd0d4d3, type: 3}
  m_Color:
    serializedVersion: 2
    rgba: 4278190080
--- !u!23 &726694887
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 726694884}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10100, guid: 0000000000000000e000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &753767828
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 753767829}
  m_Layer: 0
  m_Name: BTail_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &753767829
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 753767828}
  serializedVersion: 2
  m_LocalRotation: {x: -4.057923e-10, y: 6.1390395e-33, z: 5.898706e-26, w: 1}
  m_LocalPosition: {x: 3.461528e-17, y: 1.6270934e-13, z: -0.15437095}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1340436495}
  m_Father: {fileID: 2097662813}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &760925526
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 760925527}
  m_Layer: 0
  m_Name: BTail_11
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &760925527
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 760925526}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0000000020101711, y: 5.4965524e-33, z: -3.3974216e-24, w: 1}
  m_LocalPosition: {x: -9.3347775e-17, y: -5.34139e-21, z: -0.15169765}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1988418108}
  m_Father: {fileID: 1557254022}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &808913295
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 808913296}
  - component: {fileID: 808913298}
  - component: {fileID: 808913297}
  m_Layer: 5
  m_Name: UI Tail Example (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &808913296
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 808913295}
  m_LocalRotation: {x: -0, y: -1, z: -0, w: 0}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1151170460}
  m_Father: {fileID: 1431716518}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: -20.529999, y: 0}
  m_SizeDelta: {x: 20, y: 11.23}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &808913297
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 808913295}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10911, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &808913298
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 808913295}
  m_CullTransparentMesh: 1
--- !u!1 &819507953
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 819507954}
  - component: {fileID: 819507956}
  - component: {fileID: 819507955}
  m_Layer: 5
  m_Name: UI Tail Example (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &819507954
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 819507953}
  m_LocalRotation: {x: -0, y: -1, z: -0, w: 0}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1810130950}
  m_Father: {fileID: 127307186}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: -20.529999, y: 0}
  m_SizeDelta: {x: 20, y: 11.23}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &819507955
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 819507953}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10911, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &819507956
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 819507953}
  m_CullTransparentMesh: 1
--- !u!1 &********* stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 100106, guid: 6dcba9037908ac9428ecd713b32ee1bb, type: 3}
  m_PrefabInstance: {fileID: 1847961720}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &838928632
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: *********}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: afd3a8c0c34ba4d4d9679edb4af4db76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  TailTransforms: []
  RootToParent: 0
  AutoGetWithOne: 1
  InitBeforeAnimator: 0
  proceduralPoints: []
  localProceduralPoints: []
  PositionSpeed: 35
  RotationSpeed: 20
  MaxStretching: 1
  Sensitivity: 0.5
  Springiness: 0
  AngleLimit: 90
  AngleLimitAxis: {x: 0, y: 0, z: 0}
  LimitAxisRange: {x: 0, y: 0}
  LimitSmoothing: 0.15
  MotionInfluence: 1
  StiffTailEnd: 0
  UseAutoCorrectLookAxis: 1
  FullCorrection: 0
  LookUpMethod: 0
  OrientationReference: {fileID: 0}
  AnimateCorrections: 0
  AnimateRoot: 1
  LengthMultiplier: 1
  AxisCorrection: {x: 0, y: 0, z: 1}
  AxisLookBack: {x: 0, y: 1, z: 0}
  ExtraCorrectionOptions: 0
  ExtraFromDirection: {x: 0, y: 0, z: 1}
  ExtraToDirection: {x: 0, y: 0, z: 1}
  AddTailReferences: 0
  UpdateClock: 1
  SafeDeltaTime: 1
  RefreshHelpers: 0
  QueueToLastUpdate: 0
  UseCollision: 0
  CollisionMethod: 1
  CollidersType: 0
  CollideWithOtherTails: 0
  DetailedCollision: 0
  CollidersScale:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  CollidersScaleMul: 6.5
  DifferenceScaleFactor: 1
  IgnoredColliders: []
  BoxesDimensionsMul: {x: 1, y: 1, z: 1}
  CollidersSameLayer: 1
  CollidersAddRigidbody: 1
  RigidbodyMass: 1
  CollidersLayer:
    serializedVersion: 2
    m_Bits: 0
  CollisionSwapping: 0.5
  CollisionSpace: 0
  IncludedColliders: []
  Curving: {x: 0, y: 0, z: 0}
  Gravity: {x: 0, y: 0, z: 0}
  RootRotationOffset: {x: 0, y: 0, z: 0}
  RootPositionOffset: {x: 0, y: 0, z: 0}
  OptimizeWithMesh: {fileID: 0}
  RootPoint:
    index: -1
    Transform: {fileID: 0}
    Position: {x: 0, y: 0, z: 0}
    Rotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousRotation: {x: 0, y: 0, z: 0, w: 1}
    InitialRotation: {x: 0, y: 0, z: 0, w: 1}
    InitialLossyScale: {x: 1, y: 1, z: 1}
    PreviousPosition: {x: 0, y: 0, z: 0}
    SpringOffset: {x: 0, y: 0, z: 0}
    SpringOffset2: {x: 0, y: 0, z: 0}
    LookDirection: {x: 0, y: 0, z: 0}
    InitLookDirection: {x: 0, y: 0, z: 0}
    CrossUp: {x: 0, y: 1, z: 0}
    BoneLength: 0
    InitBoneLength: 0
    ScaleFactor: 1
    Correction: {x: 0, y: 0, z: 0, w: 1}
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: 0, y: 0, z: 0, w: 1}
    LookBackDirection: {x: 0, y: 0, z: 0}
    RotationTargetPos: {x: 0, y: 0, z: 0}
    PreCollisionPosition: {x: 0, y: 0, z: 0}
    PreCollisionRotation: {x: 0, y: 0, z: 0, w: 0}
    ColliderRadius: 1
    collisionOffsets: {x: 0, y: 0, z: 0}
    collisionFlags: 0
  initializationBone1: {fileID: 0}
  initializationBone2: {fileID: 0}
  CollisionLookBack: 1
  drawGizmos: 0
  UseWaving: 1
  CosinusAdd: 0
  WavingSpeed: 3
  WavingRange: 0.8
  WavingAxis: {x: 1, y: 0, z: 0}
  TailRotationOffset: {x: 0, y: 0, z: 0}
  WavingType: 0
  AlternateWave: 1
  DisconnectTransforms: 0
--- !u!1 &847979495
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 847979496}
  m_Layer: 0
  m_Name: ----------------------------------
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &847979496
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 847979495}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 1.5048004, y: -2.4112737, z: 28.424793}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &896349827
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 896349828}
  - component: {fileID: 896349830}
  - component: {fileID: 896349829}
  m_Layer: 5
  m_Name: UI Tail Example (3)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &896349828
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 896349827}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1040937462}
  m_Father: {fileID: 1151170460}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 19.53, y: 0}
  m_SizeDelta: {x: 20, y: 11.23}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &896349829
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 896349827}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10911, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &896349830
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 896349827}
  m_CullTransparentMesh: 1
--- !u!1 &904911345
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 904911346}
  m_Layer: 0
  m_Name: BTail_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &904911346
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 904911345}
  serializedVersion: 2
  m_LocalRotation: {x: -7.690062e-10, y: -1.5944519e-32, z: -3.3086984e-24, w: 1}
  m_LocalPosition: {x: 3.4084484e-18, y: 2.1175824e-23, z: -0.1486592}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1729142642}
  m_Father: {fileID: 234028345}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &910210863
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 910210864}
  - component: {fileID: 910210867}
  - component: {fileID: 910210866}
  - component: {fileID: 910210865}
  m_Layer: 0
  m_Name: PanelBox (3)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &910210864
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 910210863}
  serializedVersion: 2
  m_LocalRotation: {x: 0.30070573, y: -0.000000029802322, z: -0, w: 0.953717}
  m_LocalPosition: {x: 0.835, y: 1.7022, z: -0.196}
  m_LocalScale: {x: 0.42484003, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 365727461}
  m_LocalEulerAnglesHint: {x: 35, y: 0, z: 0}
--- !u!23 &910210865
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 910210863}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: cc44f8663b1fc8f439493267bf8260d6, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 1
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &910210866
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 910210863}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!33 &910210867
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 910210863}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &923048214
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 923048215}
  - component: {fileID: 923048217}
  - component: {fileID: 923048216}
  m_Layer: 5
  m_Name: UI Tail Example (3)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &923048215
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 923048214}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 482676023}
  m_Father: {fileID: 1445383162}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 19.53, y: 0}
  m_SizeDelta: {x: 20, y: 11.23}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &923048216
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 923048214}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10911, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &923048217
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 923048214}
  m_CullTransparentMesh: 1
--- !u!1 &925753599
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 925753601}
  - component: {fileID: 925753602}
  m_Layer: 0
  m_Name: Example Tail
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &925753601
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 925753599}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0.29202914, z: -0, w: 0.95640945}
  m_LocalPosition: {x: -0.55, y: 2.031, z: 6.39}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1736892842}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: -33.959003, z: 0}
--- !u!95 &925753602
Animator:
  serializedVersion: 7
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 925753599}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 0}
  m_CullingMode: 1
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_AnimatePhysics: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!1 &943753546
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 943753547}
  - component: {fileID: 943753549}
  - component: {fileID: 943753548}
  m_Layer: 5
  m_Name: UI Tail Example (5)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &943753547
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 943753546}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1921401580}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 14.9, y: 0}
  m_SizeDelta: {x: 12, y: 25}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &943753548
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 943753546}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10911, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &943753549
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 943753546}
  m_CullTransparentMesh: 1
--- !u!4 &950043304 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4393701047646832, guid: 176e4f0acb85ffe418c9b31490361eb6, type: 3}
  m_PrefabInstance: {fileID: 1997598098}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &955666648
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 955666649}
  - component: {fileID: 955666651}
  - component: {fileID: 955666650}
  m_Layer: 5
  m_Name: UI Tail Example (5)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &955666649
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 955666648}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1040937462}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 16.5, y: 0}
  m_SizeDelta: {x: 12, y: 25}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &955666650
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 955666648}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10911, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &955666651
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 955666648}
  m_CullTransparentMesh: 1
--- !u!1 &964547473
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 964547477}
  - component: {fileID: 964547476}
  - component: {fileID: 964547475}
  - component: {fileID: 964547474}
  - component: {fileID: 964547482}
  m_Layer: 0
  m_Name: Main Camera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!81 &964547474
AudioListener:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 964547473}
  m_Enabled: 1
--- !u!124 &964547475
Behaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 964547473}
  m_Enabled: 1
--- !u!20 &964547476
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 964547473}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 2
  m_BackGroundColor: {r: 0.6985294, g: 0.6985294, b: 0.6985294, a: 0}
  m_projectionMatrixMode: 1
  m_GateFitMode: 2
  m_FOVAxisMode: 0
  m_Iso: 200
  m_ShutterSpeed: 0.005
  m_Aperture: 16
  m_FocusDistance: 10
  m_FocalLength: 50
  m_BladeCount: 5
  m_Curvature: {x: 2, y: 11}
  m_BarrelClipping: 0.25
  m_Anamorphism: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.3
  far clip plane: 1000
  field of view: 60
  orthographic: 0
  orthographic size: 5
  m_Depth: -1
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 1
  m_AllowMSAA: 1
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 0
  m_OcclusionCulling: 1
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!4 &964547477
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 964547473}
  serializedVersion: 2
  m_LocalRotation: {x: -0.19477436, y: -0.69049925, z: 0.20170447, w: -0.66677517}
  m_LocalPosition: {x: -6.3873444, y: 4.2239227, z: -0.28525835}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &964547482
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 964547473}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2e401366475335a4c85c5431e4624158, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  SpeedMultiplier: 10
  AccelerationSmothnessValue: 10
  RotationSmothnessValue: 10
  MouseSensitivity: 5
  NeedRMB: 1
--- !u!1 &976130957 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 1665418947384104, guid: 176e4f0acb85ffe418c9b31490361eb6, type: 3}
  m_PrefabInstance: {fileID: 1997598098}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &976130958
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 976130957}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a3154f2961b62634da623a7d373919b3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  TailTransforms:
  - {fileID: 1888634008}
  RootToParent: 0
  AutoGetWithOne: 1
  InitBeforeAnimator: 0
  proceduralPoints:
  - index: 0
    Transform: {fileID: 1888634008}
    Position: {x: -13.340315, y: 0.86520094, z: 6.6251616}
    Rotation: {x: 0, y: 0, z: 0.16824804, w: 0.98574466}
    PreviousRotation: {x: 0, y: 0, z: 0, w: 1}
    InitialRotation: {x: 0, y: -0, z: -0.09591487, w: 0.9953896}
    InitialLossyScale: {x: 1.0000001, y: 1.0000001, z: 1}
    PreviousPosition: {x: -13.34552, y: 0.79271644, z: 6.6251616}
    SpringOffset: {x: 0, y: 0, z: 0}
    SpringOffset2: {x: 0, y: 0, z: 0}
    LookDirection: {x: -1, y: -0.00000024234518, z: -0}
    InitLookDirection: {x: -1, y: -0.00000024234518, z: -0}
    CrossUp: {x: -0, y: 0, z: 1}
    BoneLength: 0.20800014
    InitBoneLength: 0.20800014
    ScaleFactor: 1
    Correction: {x: 0, y: 0, z: 0, w: 1}
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: 0, y: 0, z: 0, w: 1}
    LookBackDirection: {x: 0, y: 0, z: 1}
    RotationTargetPos: {x: -13.34552, y: 0.79271644, z: 6.6251616}
    PreCollisionPosition: {x: -13.340315, y: 0.86520094, z: 6.6251616}
    PreCollisionRotation: {x: 0, y: 0, z: 0, w: 0}
    ColliderRadius: NaN
    collisionOffsets: {x: 0, y: 0, z: 0}
    collisionFlags: 0
  - index: 1
    Transform: {fileID: 1161435988}
    Position: {x: -13.138778, y: 0.9337535, z: 6.6251616}
    Rotation: {x: 0, y: 0, z: -0.109975696, w: -0.9939343}
    PreviousRotation: {x: 0, y: 0, z: 0, w: 1}
    InitialRotation: {x: -0, y: -0, z: -0.09591487, w: 0.9953896}
    InitialLossyScale: {x: 1.0000001, y: 1.0000001, z: 1}
    PreviousPosition: {x: -13.135195, y: 0.89665395, z: 6.6251616}
    SpringOffset: {x: 0.00020804998, y: 0.03589958, z: 0}
    SpringOffset2: {x: 0, y: 0, z: 0}
    LookDirection: {x: -1, y: 0.0000007596121, z: -0}
    InitLookDirection: {x: -1, y: 0.0000007596121, z: -0}
    CrossUp: {x: -0, y: 0, z: 1}
    BoneLength: 0.20800014
    InitBoneLength: 0.20800014
    ScaleFactor: 0.99999994
    Correction: {x: 0, y: 0, z: 0, w: 1}
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: -0, y: -0, z: -0, w: 1}
    LookBackDirection: {x: 0, y: 0, z: 1}
    RotationTargetPos: {x: -13.138015, y: 0.9258513, z: 6.6251616}
    PreCollisionPosition: {x: -13.138778, y: 0.9337535, z: 6.6251616}
    PreCollisionRotation: {x: 0, y: 0, z: 0, w: 0}
    ColliderRadius: NaN
    collisionOffsets: {x: 0, y: 0, z: 0}
    collisionFlags: 0
  - index: 2
    Transform: {fileID: 2033388393}
    Position: {x: -12.934927, y: 0.9713942, z: 6.6251616}
    Rotation: {x: 0, y: 0, z: -0.016262274, w: -0.9998678}
    PreviousRotation: {x: 0, y: 0, z: 0, w: 1}
    InitialRotation: {x: -0, y: -0, z: -0.09591487, w: 0.9953896}
    InitialLossyScale: {x: 1.0000001, y: 1.0000001, z: 1}
    PreviousPosition: {x: -12.928703, y: 0.91971976, z: 6.6251616}
    SpringOffset: {x: -0.001068647, y: 0.047428947, z: 0}
    SpringOffset2: {x: 0, y: 0, z: 0}
    LookDirection: {x: -1, y: -0.000000012545736, z: -0}
    InitLookDirection: {x: -1, y: -0.000000012545736, z: -0}
    CrossUp: {x: 0, y: 0, z: 1}
    BoneLength: 0.19999947
    InitBoneLength: 0.19999947
    ScaleFactor: 0.99999994
    Correction: {x: 0, y: 0, z: 0, w: 1}
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: -0, y: -0, z: -0, w: 1}
    LookBackDirection: {x: 0, y: 0, z: 1}
    RotationTargetPos: {x: -12.933601, y: 0.9603875, z: 6.6251616}
    PreCollisionPosition: {x: -12.934927, y: 0.9713942, z: 6.6251616}
    PreCollisionRotation: {x: 0, y: 0, z: 0, w: 0}
    ColliderRadius: NaN
    collisionOffsets: {x: 0, y: 0, z: 0}
    collisionFlags: 0
  - index: 3
    Transform: {fileID: 677890913}
    Position: {x: -12.755676, y: 0.9782494, z: 6.6251616}
    Rotation: {x: 0, y: 0, z: 0.057069685, w: -0.9983702}
    PreviousRotation: {x: 0, y: 0, z: 0, w: 1}
    InitialRotation: {x: -0, y: -0, z: -0.09591487, w: 0.9953896}
    InitialLossyScale: {x: 1.0000001, y: 1.0000001, z: 1}
    PreviousPosition: {x: -12.748095, y: 0.9162915, z: 6.6251616}
    SpringOffset: {x: -0.00057174545, y: 0.061383437, z: 0}
    SpringOffset2: {x: 0, y: 0, z: 0}
    LookDirection: {x: -1, y: -0.00000058842437, z: -0}
    InitLookDirection: {x: -1, y: -0.00000058842437, z: -0}
    CrossUp: {x: -0, y: 0, z: 1}
    BoneLength: 0.16702697
    InitBoneLength: 0.16702697
    ScaleFactor: 0.99999994
    Correction: {x: 0, y: 0, z: 0, w: 1}
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: -0, y: -0, z: -0, w: 1}
    LookBackDirection: {x: 0, y: 0, z: 1}
    RotationTargetPos: {x: -12.754062, y: 0.96505237, z: 6.6251616}
    PreCollisionPosition: {x: -12.755676, y: 0.9782494, z: 6.6251616}
    PreCollisionRotation: {x: 0, y: 0, z: 0, w: 0}
    ColliderRadius: NaN
    collisionOffsets: {x: 0, y: 0, z: 0}
    collisionFlags: 0
  - index: 4
    Transform: {fileID: 1426850346}
    Position: {x: -12.517276, y: 0.9428364, z: 6.6251616}
    Rotation: {x: 0, y: 0, z: -0.20212647, w: 0.97935945}
    PreviousRotation: {x: 0, y: 0, z: 0, w: 1}
    InitialRotation: {x: -0, y: -0, z: -0.09591487, w: 0.9953896}
    InitialLossyScale: {x: 1.0000001, y: 1.0000001, z: 1}
    PreviousPosition: {x: -12.517018, y: 0.84225994, z: 6.6251616}
    SpringOffset: {x: 0.012439602, y: 0.101588234, z: 0}
    SpringOffset2: {x: 0, y: 0, z: 0}
    LookDirection: {x: -1, y: 0.00000012216142, z: -0}
    InitLookDirection: {x: -1, y: 0.00000012216142, z: -0}
    CrossUp: {x: -0, y: 0, z: 1}
    BoneLength: 0.22000071
    InitBoneLength: 0.22000071
    ScaleFactor: 0.99999994
    Correction: {x: 0, y: 0, z: 0, w: 1}
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: -0, y: -0, z: -0, w: 1}
    LookBackDirection: {x: 0, y: 0, z: 1}
    RotationTargetPos: {x: -12.5172205, y: 0.9214136, z: 6.6251616}
    PreCollisionPosition: {x: -12.517276, y: 0.9428364, z: 6.6251616}
    PreCollisionRotation: {x: 0, y: 0, z: 0, w: 0}
    ColliderRadius: NaN
    collisionOffsets: {x: 0, y: 0, z: 0}
    collisionFlags: 0
  - index: 5
    Transform: {fileID: 950043304}
    Position: {x: -12.346356, y: 0.77845556, z: 6.6251616}
    Rotation: {x: 0, y: 0, z: 0.5289842, w: -0.8486317}
    PreviousRotation: {x: 0, y: 0, z: 0, w: 1}
    InitialRotation: {x: -0, y: -0, z: -0.09591487, w: 0.9953896}
    InitialLossyScale: {x: 1.0000001, y: 1.0000001, z: 1}
    PreviousPosition: {x: -12.367696, y: 0.6388909, z: 6.6251616}
    SpringOffset: {x: 0.023597438, y: 0.11501809, z: 0}
    SpringOffset2: {x: 0, y: 0, z: 0}
    LookDirection: {x: -1, y: -0.000000024295382, z: -0}
    InitLookDirection: {x: -1, y: -0.000000024295382, z: -0}
    CrossUp: {x: 0, y: 0, z: 1}
    BoneLength: 0.19631046
    InitBoneLength: 0.19631046
    ScaleFactor: 1.0000001
    Correction: {x: 0, y: 0, z: 0, w: 1}
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: -0, y: -0, z: -0, w: 1}
    LookBackDirection: {x: 0, y: 0, z: 1}
    RotationTargetPos: {x: -12.350902, y: 0.7487283, z: 6.6251616}
    PreCollisionPosition: {x: -12.346356, y: 0.77845556, z: 6.6251616}
    PreCollisionRotation: {x: 0, y: 0, z: 0, w: 0}
    ColliderRadius: NaN
    collisionOffsets: {x: 0, y: 0, z: 0}
    collisionFlags: 0
  - index: 6
    Transform: {fileID: 1787256901}
    Position: {x: -12.226681, y: 0.4849842, z: 6.6251616}
    Rotation: {x: 0, y: 0, z: -0.5756337, w: 0.81770766}
    PreviousRotation: {x: 0, y: 0, z: 0, w: 1}
    InitialRotation: {x: -0, y: -0, z: -0.09591487, w: 0.9953896}
    InitialLossyScale: {x: 1.0000001, y: 1.0000001, z: 1}
    PreviousPosition: {x: -12.238973, y: 0.35988417, z: 6.6251616}
    SpringOffset: {x: 0.024123443, y: 0.0667614, z: 0}
    SpringOffset2: {x: 0, y: 0, z: 0}
    LookDirection: {x: -1, y: -0.0000000027523033, z: -0}
    InitLookDirection: {x: -1, y: -0.0000000027523033, z: -0}
    CrossUp: {x: -0, y: 0, z: 1}
    BoneLength: 0.22999993
    InitBoneLength: 0.22999993
    ScaleFactor: 1.0000002
    Correction: {x: 0, y: 0, z: 0, w: 1}
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: -0, y: -0, z: -0, w: 1}
    LookBackDirection: {x: 0, y: 0, z: 1}
    RotationTargetPos: {x: -12.229299, y: 0.45833787, z: 6.6251616}
    PreCollisionPosition: {x: -12.226681, y: 0.4849842, z: 6.6251616}
    PreCollisionRotation: {x: 0, y: 0, z: 0, w: 0}
    ColliderRadius: NaN
    collisionOffsets: {x: 0, y: 0, z: 0}
    collisionFlags: 0
  - index: 7
    Transform: {fileID: 620441006}
    Position: {x: -12.126632, y: 0.29226747, z: 6.6251616}
    Rotation: {x: 0, y: 0, z: 0.52882475, w: -0.84873104}
    PreviousRotation: {x: 0, y: 0, z: 0, w: 1}
    InitialRotation: {x: -0, y: -0, z: -0.09591487, w: 0.9953896}
    InitialLossyScale: {x: 1.0000001, y: 1.0000001, z: 1}
    PreviousPosition: {x: -12.131846, y: 0.22599746, z: 6.6251616}
    SpringOffset: {x: 0.07900223, y: 0.0095767155, z: 0}
    SpringOffset2: {x: 0, y: 0, z: 0}
    LookDirection: {x: -1, y: 0.00000055491523, z: -0}
    InitLookDirection: {x: -1, y: 0.00000055491523, z: -0}
    CrossUp: {x: -0, y: 0, z: 1}
    BoneLength: 0.16918963
    InitBoneLength: 0.16918963
    ScaleFactor: 1.0000004
    Correction: {x: 0, y: 0, z: 0, w: 1}
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: -0, y: -0, z: -0, w: 1}
    LookBackDirection: {x: 0, y: 0, z: 1}
    RotationTargetPos: {x: -12.127743, y: 0.27815196, z: 6.6251616}
    PreCollisionPosition: {x: -12.126632, y: 0.29226747, z: 6.6251616}
    PreCollisionRotation: {x: 0, y: 0, z: 0, w: 0}
    ColliderRadius: NaN
    collisionOffsets: {x: 0, y: 0, z: 0}
    collisionFlags: 0
  - index: 8
    Transform: {fileID: 1417098957}
    Position: {x: -12.038163, y: 0.21401946, z: 6.6251616}
    Rotation: {x: 0, y: 0, z: -0.6811467, w: 0.7321469}
    PreviousRotation: {x: 0, y: 0, z: 0, w: 1}
    InitialRotation: {x: -0, y: -0, z: -0.09591487, w: 0.9953896}
    InitialLossyScale: {x: 1.0000001, y: 1.0000001, z: 1}
    PreviousPosition: {x: -12.153851, y: 0.21401939, z: 6.6251616}
    SpringOffset: {x: 0.2300346, y: -0.000000031541862, z: 0}
    SpringOffset2: {x: 0, y: 0, z: 0}
    LookDirection: {x: -1, y: -0.0000003166492, z: -0}
    InitLookDirection: {x: -1, y: -0.0000003166492, z: -0}
    CrossUp: {x: 0, y: 0, z: 1}
    BoneLength: 0.23999915
    InitBoneLength: 0.23999915
    ScaleFactor: 1.0000007
    Correction: {x: 0, y: 0, z: 0, w: 1}
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: -0, y: -0, z: -0, w: 1}
    LookBackDirection: {x: 0, y: 0, z: 1}
    RotationTargetPos: {x: -12.062804, y: 0.17517893, z: 6.6251616}
    PreCollisionPosition: {x: -12.038163, y: 0.16466683, z: 6.6251616}
    PreCollisionRotation: {x: 0, y: 0, z: 0, w: 0}
    ColliderRadius: NaN
    collisionOffsets: {x: 0, y: 0, z: 0}
    collisionFlags: 0
  - index: 9
    Transform: {fileID: 1885618746}
    Position: {x: -12.135813, y: 0.22373056, z: 6.6251616}
    Rotation: {x: 0, y: 0, z: -0.9934762, w: 0.11403929}
    PreviousRotation: {x: 0, y: 0, z: 0, w: 1}
    InitialRotation: {x: -0, y: -0, z: -0.09591487, w: 0.9953896}
    InitialLossyScale: {x: 1.0000001, y: 1.0000001, z: 1}
    PreviousPosition: {x: -12.515683, y: 0.22373044, z: 6.6251616}
    SpringOffset: {x: 0.21613991, y: -0.00000004639506, z: 0}
    SpringOffset2: {x: 0, y: 0, z: 0}
    LookDirection: {x: 0, y: 0, z: 0}
    InitLookDirection: {x: 0, y: 0, z: 0}
    CrossUp: {x: -0, y: 0, z: 1}
    BoneLength: 0.23999915
    InitBoneLength: 0.23999915
    ScaleFactor: 1.000001
    Correction: {x: 0, y: 0, z: 0, w: 1}
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: -0, y: -0, z: -0, w: 1}
    LookBackDirection: {x: 0, y: 0, z: 1}
    RotationTargetPos: {x: -12.216725, y: 0.*********, z: 6.6251616}
    PreCollisionPosition: {x: -12.135813, y: 0.*********, z: 6.6251616}
    PreCollisionRotation: {x: 0, y: 0, z: 0, w: 0}
    ColliderRadius: NaN
    collisionOffsets: {x: 0, y: 0, z: 0}
    collisionFlags: 0
  localProceduralPoints: []
  PositionSpeed: 35
  RotationSpeed: 20
  MaxStretching: 0.191
  Sensitivity: 0.753
  Springiness: 0.787
  AngleLimit: 90
  AngleLimitAxis: {x: 0, y: 0, z: 0}
  LimitAxisRange: {x: 0, y: 0}
  LimitSmoothing: 0.15
  MotionInfluence: 1
  StiffTailEnd: 0
  UseAutoCorrectLookAxis: 0
  FullCorrection: 0
  LookUpMethod: 0
  OrientationReference: {fileID: *********}
  AnimateCorrections: 0
  AnimateRoot: 1
  LengthMultiplier: 1
  AxisCorrection: {x: -1, y: -0, z: -0}
  AxisLookBack: {x: 0, y: 1, z: 0}
  ExtraCorrectionOptions: 0
  ExtraFromDirection: {x: 0, y: 0, z: 1}
  ExtraToDirection: {x: 1, y: 0, z: 0}
  AddTailReferences: 0
  UpdateClock: 0
  SafeDeltaTime: 1
  RefreshHelpers: 0
  QueueToLastUpdate: 1
  UseCollision: 1
  CollisionMethod: 1
  CollidersType: 0
  CollideWithOtherTails: 0
  DetailedCollision: 0
  CollidersScale:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  CollidersScaleMul: 1.4
  DifferenceScaleFactor: 0.228
  IgnoredColliders: []
  BoxesDimensionsMul: {x: 1, y: 1, z: 1}
  CollidersSameLayer: 1
  CollidersAddRigidbody: 1
  RigidbodyMass: 1
  CollidersLayer:
    serializedVersion: 2
    m_Bits: 0
  CollisionSwapping: 0.5
  CollisionSpace: 1
  IncludedColliders:
  - {fileID: 469462812}
  Curving: {x: 0, y: 0, z: 0}
  Gravity: {x: 0, y: 0, z: 0}
  RootRotationOffset: {x: 0, y: 0, z: 0}
  RootPositionOffset: {x: 0, y: 0, z: 0}
  OptimizeWithMesh: {fileID: 0}
  RootPoint:
    index: -1
    Transform: {fileID: *********}
    Position: {x: -13.64, y: 0.85, z: 6.6251616}
    Rotation: {x: 0, y: 0, z: -0.09591487, w: 0.9953896}
    PreviousRotation: {x: 0, y: 0, z: 0, w: 1}
    InitialRotation: {x: 0, y: 0, z: -0.09591487, w: 0.9953896}
    InitialLossyScale: {x: 1.0000001, y: 1.0000001, z: 1}
    PreviousPosition: {x: -13.64, y: 0.85, z: 6.6251616}
    SpringOffset: {x: 0, y: 0, z: 0}
    SpringOffset2: {x: 0, y: 0, z: 0}
    LookDirection: {x: -1, y: -0.00000014125054, z: -0}
    InitLookDirection: {x: -1, y: -0.00000014125054, z: -0}
    CrossUp: {x: -0, y: 0, z: 1}
    BoneLength: 0.30000013
    InitBoneLength: 0.30000013
    ScaleFactor: 1
    Correction: {x: 0, y: 0, z: 0, w: 1}
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: 0, y: 0, z: -0.09591487, w: 0.9953896}
    LookBackDirection: {x: 0, y: 0, z: 1}
    RotationTargetPos: {x: -13.64, y: 0.85, z: 6.6251616}
    PreCollisionPosition: {x: -13.64, y: 0.85, z: 6.6251616}
    PreCollisionRotation: {x: 0, y: 0, z: 0, w: 0}
    ColliderRadius: 1
    collisionOffsets: {x: 0, y: 0, z: 0}
    collisionFlags: 0
  initializationBone1: {fileID: 0}
  initializationBone2: {fileID: 0}
  CollisionLookBack: 1
  drawGizmos: 0
  UseWaving: 1
  CosinusAdd: 0
  WavingSpeed: 3
  WavingRange: 0.8
  WavingAxis: {x: 0, y: 0, z: 1}
  TailRotationOffset: {x: 0, y: 0, z: 0}
  WavingType: 0
  AlternateWave: 1
  DisconnectTransforms: 0
  Lock2D: 1
--- !u!4 &********* stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4325343788362922, guid: 176e4f0acb85ffe418c9b31490361eb6, type: 3}
  m_PrefabInstance: {fileID: 1997598098}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1040937461
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1040937462}
  - component: {fileID: 1040937464}
  - component: {fileID: 1040937463}
  m_Layer: 5
  m_Name: UI Tail Example (4)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1040937462
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1040937461}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 955666649}
  m_Father: {fileID: 896349828}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 19.61, y: 0}
  m_SizeDelta: {x: 20, y: 11.23}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1040937463
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1040937461}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10911, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1040937464
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1040937461}
  m_CullTransparentMesh: 1
--- !u!1001 &1044335567
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 100016, guid: 034fdf2d85d2ae1448831ea892f09a84, type: 3}
      propertyPath: m_Name
      value: FFishSchoolLowpolyNotColored
      objectReference: {fileID: 0}
    - target: {fileID: 400016, guid: 034fdf2d85d2ae1448831ea892f09a84, type: 3}
      propertyPath: m_RootOrder
      value: 10
      objectReference: {fileID: 0}
    - target: {fileID: 400016, guid: 034fdf2d85d2ae1448831ea892f09a84, type: 3}
      propertyPath: m_LocalScale.x
      value: 0.35097086
      objectReference: {fileID: 0}
    - target: {fileID: 400016, guid: 034fdf2d85d2ae1448831ea892f09a84, type: 3}
      propertyPath: m_LocalScale.y
      value: 0.35097086
      objectReference: {fileID: 0}
    - target: {fileID: 400016, guid: 034fdf2d85d2ae1448831ea892f09a84, type: 3}
      propertyPath: m_LocalScale.z
      value: 0.35097086
      objectReference: {fileID: 0}
    - target: {fileID: 400016, guid: 034fdf2d85d2ae1448831ea892f09a84, type: 3}
      propertyPath: m_LocalPosition.x
      value: -15.9
      objectReference: {fileID: 0}
    - target: {fileID: 400016, guid: 034fdf2d85d2ae1448831ea892f09a84, type: 3}
      propertyPath: m_LocalPosition.y
      value: 3.53
      objectReference: {fileID: 0}
    - target: {fileID: 400016, guid: 034fdf2d85d2ae1448831ea892f09a84, type: 3}
      propertyPath: m_LocalPosition.z
      value: -2.47
      objectReference: {fileID: 0}
    - target: {fileID: 400016, guid: 034fdf2d85d2ae1448831ea892f09a84, type: 3}
      propertyPath: m_LocalRotation.w
      value: -0.018010706
      objectReference: {fileID: 0}
    - target: {fileID: 400016, guid: 034fdf2d85d2ae1448831ea892f09a84, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 400016, guid: 034fdf2d85d2ae1448831ea892f09a84, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0.9998378
      objectReference: {fileID: 0}
    - target: {fileID: 400016, guid: 034fdf2d85d2ae1448831ea892f09a84, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 400016, guid: 034fdf2d85d2ae1448831ea892f09a84, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: -182.064
      objectReference: {fileID: 0}
    - target: {fileID: 13700000, guid: 034fdf2d85d2ae1448831ea892f09a84, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: d174228b01b29c741a5e3f4a2d275ff1, type: 2}
    m_RemovedComponents:
    - {fileID: 9500000, guid: 034fdf2d85d2ae1448831ea892f09a84, type: 3}
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 100016, guid: 034fdf2d85d2ae1448831ea892f09a84, type: 3}
      insertIndex: -1
      addedObject: {fileID: 1044335569}
    - targetCorrespondingSourceObject: {fileID: 100016, guid: 034fdf2d85d2ae1448831ea892f09a84, type: 3}
      insertIndex: -1
      addedObject: {fileID: 1044335570}
  m_SourcePrefab: {fileID: 100100000, guid: 034fdf2d85d2ae1448831ea892f09a84, type: 3}
--- !u!1 &1044335568 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 100016, guid: 034fdf2d85d2ae1448831ea892f09a84, type: 3}
  m_PrefabInstance: {fileID: 1044335567}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &1044335569
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1044335568}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: afd3a8c0c34ba4d4d9679edb4af4db76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  TailTransforms:
  - {fileID: *********}
  RootToParent: 0
  AutoGetWithOne: 1
  InitBeforeAnimator: 0
  proceduralPoints: []
  localProceduralPoints: []
  PositionSpeed: 18
  RotationSpeed: 9
  MaxStretching: 1
  Sensitivity: 0.5
  Springiness: 0
  AngleLimit: 90
  AngleLimitAxis: {x: 0, y: 0, z: 0}
  LimitAxisRange: {x: 0, y: 0}
  LimitSmoothing: 0.15
  MotionInfluence: 1
  StiffTailEnd: 0
  UseAutoCorrectLookAxis: 1
  FullCorrection: 0
  LookUpMethod: 0
  OrientationReference: {fileID: 0}
  AnimateCorrections: 0
  AnimateRoot: 1
  LengthMultiplier: 1
  AxisCorrection: {x: 0, y: 0, z: 1}
  AxisLookBack: {x: 0, y: 1, z: 0}
  ExtraCorrectionOptions: 0
  ExtraFromDirection: {x: 0, y: 0, z: 1}
  ExtraToDirection: {x: 0, y: 0, z: 1}
  AddTailReferences: 0
  UpdateClock: 0
  SafeDeltaTime: 1
  RefreshHelpers: 0
  QueueToLastUpdate: 0
  UseCollision: 0
  CollisionMethod: 1
  CollidersType: 0
  CollideWithOtherTails: 0
  DetailedCollision: 0
  CollidersScale:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  CollidersScaleMul: 6.5
  DifferenceScaleFactor: 1
  IgnoredColliders: []
  BoxesDimensionsMul: {x: 1, y: 1, z: 1}
  CollidersSameLayer: 1
  CollidersAddRigidbody: 1
  RigidbodyMass: 1
  CollidersLayer:
    serializedVersion: 2
    m_Bits: 0
  CollisionSwapping: 0.5
  CollisionSpace: 0
  IncludedColliders: []
  Curving: {x: 0, y: 0, z: 0}
  Gravity: {x: 0, y: 0, z: 0}
  RootRotationOffset: {x: 0, y: 0, z: 0}
  RootPositionOffset: {x: 0, y: 0, z: 0}
  OptimizeWithMesh: {fileID: 0}
  RootPoint:
    index: -1
    Transform: {fileID: 0}
    Position: {x: 0, y: 0, z: 0}
    Rotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousRotation: {x: 0, y: 0, z: 0, w: 1}
    InitialRotation: {x: 0, y: 0, z: 0, w: 1}
    InitialLossyScale: {x: 1, y: 1, z: 1}
    PreviousPosition: {x: 0, y: 0, z: 0}
    SpringOffset: {x: 0, y: 0, z: 0}
    SpringOffset2: {x: 0, y: 0, z: 0}
    LookDirection: {x: 0, y: 0, z: 0}
    InitLookDirection: {x: 0, y: 0, z: 0}
    CrossUp: {x: 0, y: 1, z: 0}
    BoneLength: 0
    InitBoneLength: 0
    ScaleFactor: 1
    Correction: {x: 0, y: 0, z: 0, w: 1}
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: 0, y: 0, z: 0, w: 1}
    LookBackDirection: {x: 0, y: 0, z: 0}
    RotationTargetPos: {x: 0, y: 0, z: 0}
    PreCollisionPosition: {x: 0, y: 0, z: 0}
    PreCollisionRotation: {x: 0, y: 0, z: 0, w: 0}
    ColliderRadius: 1
    collisionOffsets: {x: 0, y: 0, z: 0}
    collisionFlags: 0
  initializationBone1: {fileID: 0}
  initializationBone2: {fileID: 0}
  CollisionLookBack: 1
  drawGizmos: 0
  UseWaving: 1
  CosinusAdd: 0
  WavingSpeed: 7
  WavingRange: 0.3
  WavingAxis: {x: 0.2, y: 0, z: 1}
  TailRotationOffset: {x: 0, y: 0, z: 0}
  WavingType: 0
  AlternateWave: 1
  DisconnectTransforms: 0
--- !u!114 &1044335570
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1044335568}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 43491a0ac6e9bb04cb722d4ed6d70d5e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  MainSpeed: 0.74
  RangeValue: {x: 0.79, y: 1, z: 0.58}
  RangeMul: 3.88
  AddYSin: 1
  AddYSinTimeSpeed: 1
  RotateForwardSpeed: 10
--- !u!1 &1047143181
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1047143184}
  - component: {fileID: 1047143182}
  - component: {fileID: 1047143183}
  - component: {fileID: 1047143185}
  m_Layer: 0
  m_Name: PR_Lever Object (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1047143182
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1047143181}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0dee3b3dda5183c4f89654b2dd4a822f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  LookAtRange: 1
  AddRigidbody: 0
  InteractionKey: 0
  canvasObjectOffset: {x: 0, y: 0, z: 0}
  EventOnInteraction:
    m_PersistentCalls:
      m_Calls: []
  textInCanvas: 
  EventOnKeyUpInteraction:
    m_PersistentCalls:
      m_Calls: []
  XAxis: 0
  YAxis: 1
  ResetValue: 0
  Sensitivity: 0.65
  StartValueY: 0.359
  StartValueX: 0
  CanBePulledByMouse: 1
  OverrideCanBePulledByMouse: 1
  RotationRangesY: {x: 80, y: -6}
  RotationRangesX: {x: -40, y: 40}
--- !u!65 &1047143183
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1047143181}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 1
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.7487983, y: 1, z: 1.2494229}
  m_Center: {x: 0.023681285, y: 0, z: 0.04284382}
--- !u!4 &1047143184
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1047143181}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0.573, y: 1.7969999, z: -0.098}
  m_LocalScale: {x: 0.99999994, y: 1, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1639218349}
  m_Father: {fileID: 365727461}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!135 &1047143185
SphereCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1047143181}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 1
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Radius: 1
  m_Center: {x: 0, y: 0, z: 0}
--- !u!1 &1138115989
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1138115990}
  - component: {fileID: 1138115991}
  m_Layer: 0
  m_Name: Cylinder
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1138115990
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1138115989}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1736892842}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &1138115991
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1138115989}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 1247455685}
  - {fileID: 2097662813}
  - {fileID: 753767829}
  - {fileID: 1340436495}
  - {fileID: 234028345}
  - {fileID: 904911346}
  - {fileID: 1729142642}
  - {fileID: 475779538}
  - {fileID: 1289658228}
  - {fileID: 1557254022}
  - {fileID: 760925527}
  - {fileID: 1988418108}
  - {fileID: 1690211385}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 1247455685}
  m_AABB:
    m_Center: {x: -0.0027853511, y: -0.0045835897, z: -0.91688454}
    m_Extent: {x: 0.045650713, y: 0.04341641, z: 0.9642736}
  m_DirtyAABB: 0
--- !u!1 &1151170459
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1151170460}
  - component: {fileID: 1151170462}
  - component: {fileID: 1151170461}
  m_Layer: 5
  m_Name: UI Tail Example (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1151170460
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1151170459}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 896349828}
  m_Father: {fileID: 808913296}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 20.2, y: 0}
  m_SizeDelta: {x: 20, y: 11.23}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1151170461
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1151170459}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10911, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1151170462
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1151170459}
  m_CullTransparentMesh: 1
--- !u!4 &1161435988 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4408482433442078, guid: 176e4f0acb85ffe418c9b31490361eb6, type: 3}
  m_PrefabInstance: {fileID: 1997598098}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1178527599
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1178527600}
  m_Layer: 5
  m_Name: UI Tail Origin (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1178527600
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1178527599}
  m_LocalRotation: {x: 0, y: 0, z: 0.7071068, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 127307186}
  m_Father: {fileID: 1536037902}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 90}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: -10.7999}
  m_SizeDelta: {x: 16, y: 10}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!4 &1247455685
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 428257778}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0.000000019712383, w: 1}
  m_LocalPosition: {x: -0, y: 8.563755e-26, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2097662813}
  m_Father: {fileID: 1736892842}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1289658227
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1289658228}
  m_Layer: 0
  m_Name: BTail_9
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1289658228
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1289658227}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000000025130904, y: 1.834112e-33, z: -3.182173e-24, w: 1}
  m_LocalPosition: {x: 1.5227173e-16, y: -1.1434945e-23, z: -0.14348873}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1557254022}
  m_Father: {fileID: 475779538}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1292857967
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1292857971}
  - component: {fileID: 1292857970}
  - component: {fileID: 1292857969}
  - component: {fileID: 1292857968}
  m_Layer: 5
  m_Name: Canvas GO INSIDE FOR EXAMPLE
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1292857968
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1292857967}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc42784cf147c0c48a680349fa168899, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 4294967295
--- !u!114 &1292857969
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1292857967}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0cd44c1031e13a943bb63640046fad76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UiScaleMode: 0
  m_ReferencePixelsPerUnit: 100
  m_ScaleFactor: 1
  m_ReferenceResolution: {x: 800, y: 600}
  m_ScreenMatchMode: 0
  m_MatchWidthOrHeight: 0
  m_PhysicalUnit: 3
  m_FallbackScreenDPI: 96
  m_DefaultSpriteDPI: 96
  m_DynamicPixelsPerUnit: 1
  m_PresetInfoIsWorld: 0
--- !u!223 &1292857970
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1292857967}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 0
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_VertexColorAlwaysGammaSpace: 0
  m_AdditionalShaderChannelsFlag: 0
  m_UpdateRectTransformForStandalone: 0
  m_SortingLayerID: 0
  m_SortingOrder: 0
  m_TargetDisplay: 0
--- !u!224 &1292857971
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1292857967}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0, y: 0, z: 0}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1536037902}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0, y: 0}
--- !u!1 &1340436494
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1340436495}
  m_Layer: 0
  m_Name: BTail_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1340436495
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1340436494}
  serializedVersion: 2
  m_LocalRotation: {x: -8.193637e-10, y: -2.4185894e-35, z: 2.9517896e-26, w: 1}
  m_LocalPosition: {x: -2.7826048e-17, y: 5.3672302e-17, z: -0.15429448}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 234028345}
  m_Father: {fileID: 753767829}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1356396047
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1356396049}
  - component: {fileID: 1356396048}
  m_Layer: 0
  m_Name: Point light (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!108 &1356396048
Light:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1356396047}
  m_Enabled: 1
  serializedVersion: 11
  m_Type: 2
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_Intensity: 0.39
  m_Range: 10
  m_SpotAngle: 30
  m_InnerSpotAngle: 21.80208
  m_CookieSize: 10
  m_Shadows:
    m_Type: 0
    m_Resolution: -1
    m_CustomResolution: -1
    m_Strength: 1
    m_Bias: 0.05
    m_NormalBias: 0.4
    m_NearPlane: 0.2
    m_CullingMatrixOverride:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    m_UseCullingMatrixOverride: 0
  m_Cookie: {fileID: 0}
  m_DrawHalo: 0
  m_Flare: {fileID: 0}
  m_RenderMode: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingLayerMask: 1
  m_Lightmapping: 4
  m_LightShadowCasterMode: 0
  m_AreaSize: {x: 1, y: 1}
  m_BounceIntensity: 1
  m_ColorTemperature: 6570
  m_UseColorTemperature: 0
  m_BoundingSphereOverride: {x: 0, y: 0, z: 0, w: 0}
  m_UseBoundingSphereOverride: 0
  m_UseViewFrustumForShadowCasterCull: 1
  m_ForceVisible: 0
  m_ShadowRadius: 0
  m_ShadowAngle: 0
  m_LightUnit: 1
  m_LuxAtDistance: 1
  m_EnableSpotReflector: 1
--- !u!4 &1356396049
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1356396047}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -8.49, y: 1.81, z: -5.39}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1381883037
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1381883038}
  - component: {fileID: 1381883040}
  - component: {fileID: 1381883039}
  m_Layer: 5
  m_Name: UI Tail Example (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1381883038
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1381883037}
  m_LocalRotation: {x: -0, y: -1, z: -0, w: 0}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1693931111}
  m_Father: {fileID: 302796072}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: -20.529999, y: 0}
  m_SizeDelta: {x: 20, y: 11.23}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1381883039
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1381883037}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10911, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1381883040
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1381883037}
  m_CullTransparentMesh: 1
--- !u!1 &1407205959
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1407205960}
  m_Layer: 0
  m_Name: ----------------------------------
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &1407205960
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1407205959}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 1.5048004, y: -2.4112737, z: 28.424793}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &1417098957 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4513972952887558, guid: 176e4f0acb85ffe418c9b31490361eb6, type: 3}
  m_PrefabInstance: {fileID: 1997598098}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &1426850346 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4203866215698794, guid: 176e4f0acb85ffe418c9b31490361eb6, type: 3}
  m_PrefabInstance: {fileID: 1997598098}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1431716517
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1431716518}
  - component: {fileID: 1431716520}
  - component: {fileID: 1431716519}
  m_Layer: 5
  m_Name: UI Tail Example (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1431716518
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1431716517}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 808913296}
  m_Father: {fileID: 681307602}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: -17.100012, y: 0}
  m_SizeDelta: {x: 20, y: 11.23}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1431716519
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1431716517}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10911, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1431716520
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1431716517}
  m_CullTransparentMesh: 1
--- !u!1 &1445383161
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1445383162}
  - component: {fileID: 1445383164}
  - component: {fileID: 1445383163}
  m_Layer: 5
  m_Name: UI Tail Example (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1445383162
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1445383161}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 923048215}
  m_Father: {fileID: 1692568278}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 20.2, y: 0}
  m_SizeDelta: {x: 20, y: 11.23}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1445383163
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1445383161}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10911, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1445383164
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1445383161}
  m_CullTransparentMesh: 1
--- !u!114 &1527365684 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 114620798422570778, guid: 9d8b2ab2310ba314da5a1e808ca96dfe, type: 3}
  m_PrefabInstance: {fileID: 1806612024}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f6b477fbd742c3346ad21e2afaca5782, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1001 &1533512506
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 4385575217446778, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_LocalScale.x
      value: 1.6611938
      objectReference: {fileID: 0}
    - target: {fileID: 4385575217446778, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_LocalScale.y
      value: 1.063707
      objectReference: {fileID: 0}
    - target: {fileID: 4878481119553484, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_RootOrder
      value: 13
      objectReference: {fileID: 0}
    - target: {fileID: 4878481119553484, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_LocalPosition.x
      value: -0.8
      objectReference: {fileID: 0}
    - target: {fileID: 4878481119553484, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.050000012
      objectReference: {fileID: 0}
    - target: {fileID: 4878481119553484, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_LocalPosition.z
      value: 3.7
      objectReference: {fileID: 0}
    - target: {fileID: 4878481119553484, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.5301492
      objectReference: {fileID: 0}
    - target: {fileID: 4878481119553484, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4878481119553484, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0.8479044
      objectReference: {fileID: 0}
    - target: {fileID: 4878481119553484, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4878481119553484, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: -115.969
      objectReference: {fileID: 0}
    - target: {fileID: 102948136206050596, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_Text
      value: "Pull lever to blend with \nanimator, there is variable \ncalled \"blend
        chain value\" so\nonly first bones are blended \nto original then keyframed\nanimation
        will dictate main\ntail swiping animation"
      objectReference: {fileID: 0}
    - target: {fileID: 102948136206050596, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_FontSize
      value: 31
      objectReference: {fileID: 0}
    - target: {fileID: 102948136206050596, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_Color.rgba
      value: 4278190080
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
--- !u!1 &1536037901
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1536037902}
  - component: {fileID: 1536037905}
  - component: {fileID: 1536037904}
  - component: {fileID: 1536037909}
  - component: {fileID: 1536037910}
  m_Layer: 5
  m_Name: UI Tails Base
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1536037902
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1536037901}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.8, y: 0.8, z: 0.8}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 681307602}
  - {fileID: 1556654199}
  - {fileID: 1178527600}
  - {fileID: 211133275}
  m_Father: {fileID: 1292857971}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 144, y: -148.1}
  m_SizeDelta: {x: 32, y: 32}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1536037904
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1536037901}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1536037905
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1536037901}
  m_CullTransparentMesh: 1
--- !u!114 &1536037909
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1536037901}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc71bf79a039ae24894ef08c2171fb45, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  VibrationRate: 4
  BaseRange: 250
  SmoothTranslation: 0
  AxesMultiplier: {x: 3.81, y: 1.2, z: 1}
  ChangeObjectPosition: 1
--- !u!114 &1536037910
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1536037901}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ebc354317bbc5dd40b24b190775f892a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  RotationAxis: {x: 0, y: 0, z: 1}
  RotationRange: 150
  SinSpeed: 2
--- !u!1 &1549329015
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1549329016}
  - component: {fileID: 1549329018}
  - component: {fileID: 1549329017}
  m_Layer: 0
  m_Name: Sign Text (3)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1549329016
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1549329015}
  serializedVersion: 2
  m_LocalRotation: {x: -0.000000014901161, y: 0.93749475, z: -0.3479994, w: 0.000000029802322}
  m_LocalPosition: {x: 0.844, y: 1.875, z: 0.463}
  m_LocalScale: {x: 0.9999998, y: 0.9999999, z: 0.99999976}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 365727461}
  m_LocalEulerAnglesHint: {x: 40.73, y: 180, z: 0}
--- !u!102 &1549329017
TextMesh:
  serializedVersion: 3
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1549329015}
  m_Text: only tail anim.
  m_OffsetZ: 0
  m_CharacterSize: 0.02
  m_LineSpacing: 1
  m_Anchor: 4
  m_Alignment: 1
  m_TabSize: 4
  m_FontSize: 31
  m_FontStyle: 0
  m_RichText: 0
  m_Font: {fileID: 12800000, guid: dbc9b820232f93d4aab6f1d3bdd0d4d3, type: 3}
  m_Color:
    serializedVersion: 2
    rgba: 4278190080
--- !u!23 &1549329018
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1549329015}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10100, guid: 0000000000000000e000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &1556654198
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1556654199}
  m_Layer: 5
  m_Name: UI Tail Origin
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1556654199
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1556654198}
  m_LocalRotation: {x: -0, y: 1, z: -0, w: 0}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 302796072}
  m_Father: {fileID: 1536037902}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 10.8, y: 0}
  m_SizeDelta: {x: 16, y: 10}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &1557254021
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1557254022}
  m_Layer: 0
  m_Name: BTail_10
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1557254022
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1557254021}
  serializedVersion: 2
  m_LocalRotation: {x: -4.5089754e-10, y: 6.1882056e-33, z: -5.5954446e-26, w: 1}
  m_LocalPosition: {x: 4.6144744e-17, y: 1.7667449e-18, z: -0.15997715}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 760925527}
  m_Father: {fileID: 1289658228}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1568744426
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1568744427}
  - component: {fileID: 1568744429}
  - component: {fileID: 1568744428}
  m_Layer: 0
  m_Name: Sign Text (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1568744427
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1568744426}
  serializedVersion: 2
  m_LocalRotation: {x: -0.000000014901161, y: 0.93749475, z: -0.3479994, w: 0.000000029802322}
  m_LocalPosition: {x: -0.287, y: 2.35, z: -0.223}
  m_LocalScale: {x: 0.9999998, y: 0.9999999, z: 0.99999976}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 365727461}
  m_LocalEulerAnglesHint: {x: 40.73, y: 180, z: 0}
--- !u!102 &1568744428
TextMesh:
  serializedVersion: 3
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1568744426}
  m_Text: 'source

    animation'
  m_OffsetZ: 0
  m_CharacterSize: 0.02
  m_LineSpacing: 1
  m_Anchor: 4
  m_Alignment: 1
  m_TabSize: 4
  m_FontSize: 31
  m_FontStyle: 0
  m_RichText: 0
  m_Font: {fileID: 12800000, guid: dbc9b820232f93d4aab6f1d3bdd0d4d3, type: 3}
  m_Color:
    serializedVersion: 2
    rgba: 4278190080
--- !u!23 &1568744429
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1568744426}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10100, guid: 0000000000000000e000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &1639218348
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1639218349}
  m_Layer: 0
  m_Name: Lever
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1639218349
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1639218348}
  serializedVersion: 2
  m_LocalRotation: {x: 0.65678316, y: -0, z: -0, w: 0.7540796}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 76912344}
  - {fileID: 388542840}
  m_Father: {fileID: 1047143184}
  m_LocalEulerAnglesHint: {x: 82.11, y: 0, z: 0}
--- !u!1 &1690211384
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1690211385}
  m_Layer: 0
  m_Name: BTail_13
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1690211385
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1690211384}
  serializedVersion: 2
  m_LocalRotation: {x: -8.013312e-25, y: -7.1557343e-17, z: -3.3432024e-24, w: 1}
  m_LocalPosition: {x: 3.4793708e-17, y: -1.3495526e-17, z: -0.17734884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1988418108}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1692568277
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1692568278}
  - component: {fileID: 1692568280}
  - component: {fileID: 1692568279}
  m_Layer: 5
  m_Name: UI Tail Example (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1692568278
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1692568277}
  m_LocalRotation: {x: -0, y: -1, z: -0, w: 0}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1445383162}
  m_Father: {fileID: 1759932473}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: -20.529999, y: 0}
  m_SizeDelta: {x: 20, y: 11.23}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1692568279
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1692568277}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10911, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1692568280
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1692568277}
  m_CullTransparentMesh: 1
--- !u!1 &1693931110
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1693931111}
  - component: {fileID: 1693931113}
  - component: {fileID: 1693931112}
  m_Layer: 5
  m_Name: UI Tail Example (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1693931111
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1693931110}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 252233852}
  m_Father: {fileID: 1381883038}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 20.2, y: 0}
  m_SizeDelta: {x: 20, y: 11.23}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1693931112
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1693931110}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10911, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1693931113
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1693931110}
  m_CullTransparentMesh: 1
--- !u!1 &1717911227
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1717911228}
  m_Layer: 0
  m_Name: Level
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1717911228
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717911227}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -2.0076346, y: -0.87006235, z: 4.1002235}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 469462814}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1729142641
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1729142642}
  m_Layer: 0
  m_Name: BTail_7
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1729142642
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1729142641}
  serializedVersion: 2
  m_LocalRotation: {x: 7.685494e-10, y: 5.042931e-33, z: 6.5616227e-24, w: 1}
  m_LocalPosition: {x: -4.9798023e-17, y: 1.9600682e-19, z: -0.16278034}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 475779538}
  m_Father: {fileID: 904911346}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1736892841
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1736892842}
  m_Layer: 0
  m_Name: Example_Tail
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1736892842
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1736892841}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1247455685}
  - {fileID: 1138115990}
  m_Father: {fileID: 925753601}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1759932472
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1759932473}
  - component: {fileID: 1759932475}
  - component: {fileID: 1759932474}
  m_Layer: 5
  m_Name: UI Tail Example (0)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1759932473
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1759932472}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1692568278}
  m_Father: {fileID: 211133275}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 270}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: -15.900008, y: -0.0000038146973}
  m_SizeDelta: {x: 20, y: 11.23}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1759932474
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1759932472}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10911, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1759932475
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1759932472}
  m_CullTransparentMesh: 1
--- !u!4 &1787256901 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4771815545059084, guid: 176e4f0acb85ffe418c9b31490361eb6, type: 3}
  m_PrefabInstance: {fileID: 1997598098}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1791080537
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1791080538}
  - component: {fileID: 1791080540}
  - component: {fileID: 1791080539}
  m_Layer: 0
  m_Name: Sign Text (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1791080538
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1791080537}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0.93749475, z: -0.3479994, w: 0}
  m_LocalPosition: {x: -0.287, y: 1.875, z: 0.463}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 365727461}
  m_LocalEulerAnglesHint: {x: 40.73, y: 180, z: 0}
--- !u!102 &1791080539
TextMesh:
  serializedVersion: 3
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1791080537}
  m_Text: tail animator
  m_OffsetZ: 0
  m_CharacterSize: 0.02
  m_LineSpacing: 1
  m_Anchor: 4
  m_Alignment: 1
  m_TabSize: 4
  m_FontSize: 31
  m_FontStyle: 0
  m_RichText: 0
  m_Font: {fileID: 12800000, guid: dbc9b820232f93d4aab6f1d3bdd0d4d3, type: 3}
  m_Color:
    serializedVersion: 2
    rgba: 4278190080
--- !u!23 &1791080540
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1791080537}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10100, guid: 0000000000000000e000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1001 &1806612024
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 4584997759664756, guid: 9d8b2ab2310ba314da5a1e808ca96dfe, type: 3}
      propertyPath: m_RootOrder
      value: 23
      objectReference: {fileID: 0}
    - target: {fileID: 4584997759664756, guid: 9d8b2ab2310ba314da5a1e808ca96dfe, type: 3}
      propertyPath: m_LocalPosition.x
      value: -0.19
      objectReference: {fileID: 0}
    - target: {fileID: 4584997759664756, guid: 9d8b2ab2310ba314da5a1e808ca96dfe, type: 3}
      propertyPath: m_LocalPosition.y
      value: 2.031
      objectReference: {fileID: 0}
    - target: {fileID: 4584997759664756, guid: 9d8b2ab2310ba314da5a1e808ca96dfe, type: 3}
      propertyPath: m_LocalPosition.z
      value: -1.78
      objectReference: {fileID: 0}
    - target: {fileID: 4584997759664756, guid: 9d8b2ab2310ba314da5a1e808ca96dfe, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.9998157
      objectReference: {fileID: 0}
    - target: {fileID: 4584997759664756, guid: 9d8b2ab2310ba314da5a1e808ca96dfe, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4584997759664756, guid: 9d8b2ab2310ba314da5a1e808ca96dfe, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0.019199409
      objectReference: {fileID: 0}
    - target: {fileID: 4584997759664756, guid: 9d8b2ab2310ba314da5a1e808ca96dfe, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4584997759664756, guid: 9d8b2ab2310ba314da5a1e808ca96dfe, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: -2.2
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 9d8b2ab2310ba314da5a1e808ca96dfe, type: 3}
--- !u!1 &1810130949
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1810130950}
  - component: {fileID: 1810130952}
  - component: {fileID: 1810130951}
  m_Layer: 5
  m_Name: UI Tail Example (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1810130950
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1810130949}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 455146903}
  m_Father: {fileID: 819507954}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 20.2, y: 0}
  m_SizeDelta: {x: 20, y: 11.23}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1810130951
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1810130949}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10911, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1810130952
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1810130949}
  m_CullTransparentMesh: 1
--- !u!1001 &1847961720
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 100000, guid: 6dcba9037908ac9428ecd713b32ee1bb, type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 400000, guid: 6dcba9037908ac9428ecd713b32ee1bb, type: 3}
      propertyPath: m_RootOrder
      value: 8
      objectReference: {fileID: 0}
    - target: {fileID: 400000, guid: 6dcba9037908ac9428ecd713b32ee1bb, type: 3}
      propertyPath: m_LocalPosition.x
      value: -6.11
      objectReference: {fileID: 0}
    - target: {fileID: 400000, guid: 6dcba9037908ac9428ecd713b32ee1bb, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.05
      objectReference: {fileID: 0}
    - target: {fileID: 400000, guid: 6dcba9037908ac9428ecd713b32ee1bb, type: 3}
      propertyPath: m_LocalPosition.z
      value: -6.96
      objectReference: {fileID: 0}
    - target: {fileID: 400000, guid: 6dcba9037908ac9428ecd713b32ee1bb, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.9371603
      objectReference: {fileID: 0}
    - target: {fileID: 400000, guid: 6dcba9037908ac9428ecd713b32ee1bb, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 400000, guid: 6dcba9037908ac9428ecd713b32ee1bb, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0.34889922
      objectReference: {fileID: 0}
    - target: {fileID: 400000, guid: 6dcba9037908ac9428ecd713b32ee1bb, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 400000, guid: 6dcba9037908ac9428ecd713b32ee1bb, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: -40.84
      objectReference: {fileID: 0}
    - target: {fileID: 400004, guid: 6dcba9037908ac9428ecd713b32ee1bb, type: 3}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 400012, guid: 6dcba9037908ac9428ecd713b32ee1bb, type: 3}
      propertyPath: m_RootOrder
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 400042, guid: 6dcba9037908ac9428ecd713b32ee1bb, type: 3}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 400052, guid: 6dcba9037908ac9428ecd713b32ee1bb, type: 3}
      propertyPath: m_RootOrder
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 400074, guid: 6dcba9037908ac9428ecd713b32ee1bb, type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 400106, guid: 6dcba9037908ac9428ecd713b32ee1bb, type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 400106, guid: 6dcba9037908ac9428ecd713b32ee1bb, type: 3}
      propertyPath: m_LocalPosition.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 400106, guid: 6dcba9037908ac9428ecd713b32ee1bb, type: 3}
      propertyPath: m_LocalPosition.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 400106, guid: 6dcba9037908ac9428ecd713b32ee1bb, type: 3}
      propertyPath: m_LocalPosition.z
      value: -0.28
      objectReference: {fileID: 0}
    - target: {fileID: 9500000, guid: 6dcba9037908ac9428ecd713b32ee1bb, type: 3}
      propertyPath: m_Enabled
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 9500000, guid: 6dcba9037908ac9428ecd713b32ee1bb, type: 3}
      propertyPath: m_Controller
      value: 
      objectReference: {fileID: 9100000, guid: 3e6aeb243649f424b91d52b0f520fe8a, type: 2}
    - target: {fileID: 9500000, guid: 6dcba9037908ac9428ecd713b32ee1bb, type: 3}
      propertyPath: m_ApplyRootMotion
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 100106, guid: 6dcba9037908ac9428ecd713b32ee1bb, type: 3}
      insertIndex: -1
      addedObject: {fileID: 838928632}
  m_SourcePrefab: {fileID: 100100000, guid: 6dcba9037908ac9428ecd713b32ee1bb, type: 3}
--- !u!4 &1885618746 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4613912018262362, guid: 176e4f0acb85ffe418c9b31490361eb6, type: 3}
  m_PrefabInstance: {fileID: 1997598098}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &1888634008 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4093448544794518, guid: 176e4f0acb85ffe418c9b31490361eb6, type: 3}
  m_PrefabInstance: {fileID: 1997598098}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1889571826 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 1258571551842992, guid: 176e4f0acb85ffe418c9b31490361eb6, type: 3}
  m_PrefabInstance: {fileID: 1997598098}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &1889571828
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1889571826}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a3154f2961b62634da623a7d373919b3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  TailTransforms: []
  RootToParent: 0
  AutoGetWithOne: 1
  InitBeforeAnimator: 0
  proceduralPoints: []
  localProceduralPoints: []
  PositionSpeed: 35
  RotationSpeed: 20
  MaxStretching: 0.098
  Sensitivity: 0.63
  Springiness: 0.617
  AngleLimit: 90
  AngleLimitAxis: {x: 0, y: 0, z: 0}
  LimitAxisRange: {x: 0, y: 0}
  LimitSmoothing: 0.15
  MotionInfluence: 1
  StiffTailEnd: 0
  UseAutoCorrectLookAxis: 1
  FullCorrection: 0
  LookUpMethod: 0
  OrientationReference: {fileID: 0}
  AnimateCorrections: 0
  AnimateRoot: 1
  LengthMultiplier: 1
  AxisCorrection: {x: -1, y: -0, z: -0}
  AxisLookBack: {x: 0, y: 1, z: 0}
  ExtraCorrectionOptions: 0
  ExtraFromDirection: {x: 0, y: 0, z: 1}
  ExtraToDirection: {x: 1, y: 0, z: 0}
  AddTailReferences: 0
  UpdateClock: 0
  SafeDeltaTime: 1
  RefreshHelpers: 0
  QueueToLastUpdate: 1
  UseCollision: 1
  CollisionMethod: 1
  CollidersType: 0
  CollideWithOtherTails: 0
  DetailedCollision: 0
  CollidersScale:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  CollidersScaleMul: 1.26
  DifferenceScaleFactor: 0.071
  IgnoredColliders: []
  BoxesDimensionsMul: {x: 1, y: 1, z: 1}
  CollidersSameLayer: 1
  CollidersAddRigidbody: 1
  RigidbodyMass: 1
  CollidersLayer:
    serializedVersion: 2
    m_Bits: 0
  CollisionSwapping: 0.5
  CollisionSpace: 1
  IncludedColliders:
  - {fileID: 469462812}
  Curving: {x: 0, y: 0, z: 0}
  Gravity: {x: 0, y: 0, z: 0}
  RootRotationOffset: {x: 0, y: 0, z: 0}
  RootPositionOffset: {x: 0, y: 0, z: 0}
  OptimizeWithMesh: {fileID: 0}
  RootPoint:
    index: -1
    Transform: {fileID: 0}
    Position: {x: 0, y: 0, z: 0}
    Rotation: {x: 0, y: 0, z: 0, w: 1}
    PreviousRotation: {x: 0, y: 0, z: 0, w: 1}
    InitialRotation: {x: 0, y: 0, z: 0, w: 1}
    InitialLossyScale: {x: 1, y: 1, z: 1}
    PreviousPosition: {x: 0, y: 0, z: 0}
    SpringOffset: {x: 0, y: 0, z: 0}
    SpringOffset2: {x: 0, y: 0, z: 0}
    LookDirection: {x: 0, y: 0, z: 0}
    InitLookDirection: {x: 0, y: 0, z: 0}
    CrossUp: {x: 0, y: 1, z: 0}
    BoneLength: 0
    InitBoneLength: 0
    ScaleFactor: 1
    Correction: {x: 0, y: 0, z: 0, w: 1}
    InitialLocalPosition: {x: 0, y: 0, z: 0}
    InitialLocalRotation: {x: 0, y: 0, z: 0, w: 1}
    LookBackDirection: {x: 0, y: 0, z: 0}
    RotationTargetPos: {x: 0, y: 0, z: 0}
    PreCollisionPosition: {x: 0, y: 0, z: 0}
    PreCollisionRotation: {x: 0, y: 0, z: 0, w: 0}
    ColliderRadius: 1
    collisionOffsets: {x: 0, y: 0, z: 0}
    collisionFlags: 0
  initializationBone1: {fileID: 0}
  initializationBone2: {fileID: 0}
  CollisionLookBack: 1
  drawGizmos: 0
  UseWaving: 1
  CosinusAdd: 0
  WavingSpeed: 3
  WavingRange: 0.8
  WavingAxis: {x: 0, y: 0, z: 1}
  TailRotationOffset: {x: 0, y: 0, z: 0}
  WavingType: 0
  AlternateWave: 1
  DisconnectTransforms: 0
  Lock2D: 1
--- !u!1 &1921401579
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1921401580}
  - component: {fileID: 1921401582}
  - component: {fileID: 1921401581}
  m_Layer: 5
  m_Name: UI Tail Example (4)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1921401580
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1921401579}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 943753547}
  m_Father: {fileID: 252233852}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 19.61, y: 0}
  m_SizeDelta: {x: 20, y: 11.23}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1921401581
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1921401579}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10911, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1921401582
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1921401579}
  m_CullTransparentMesh: 1
--- !u!1 &1927895606
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1927895608}
  - component: {fileID: 1927895607}
  m_Layer: 0
  m_Name: Directional Light
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!108 &1927895607
Light:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1927895606}
  m_Enabled: 1
  serializedVersion: 11
  m_Type: 1
  m_Color: {r: 0.6397059, g: 0.6397059, b: 0.6397059, a: 1}
  m_Intensity: 1.33
  m_Range: 10
  m_SpotAngle: 30
  m_InnerSpotAngle: 21.80208
  m_CookieSize: 10
  m_Shadows:
    m_Type: 2
    m_Resolution: -1
    m_CustomResolution: -1
    m_Strength: 1
    m_Bias: 0.05
    m_NormalBias: 0.4
    m_NearPlane: 0.2
    m_CullingMatrixOverride:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    m_UseCullingMatrixOverride: 0
  m_Cookie: {fileID: 0}
  m_DrawHalo: 0
  m_Flare: {fileID: 0}
  m_RenderMode: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingLayerMask: 1
  m_Lightmapping: 4
  m_LightShadowCasterMode: 0
  m_AreaSize: {x: 1, y: 1}
  m_BounceIntensity: 1
  m_ColorTemperature: 6570
  m_UseColorTemperature: 0
  m_BoundingSphereOverride: {x: 0, y: 0, z: 0, w: 0}
  m_UseBoundingSphereOverride: 0
  m_UseViewFrustumForShadowCasterCull: 1
  m_ForceVisible: 0
  m_ShadowRadius: 0
  m_ShadowAngle: 0
  m_LightUnit: 1
  m_LuxAtDistance: 1
  m_EnableSpotReflector: 1
--- !u!4 &1927895608
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1927895606}
  serializedVersion: 2
  m_LocalRotation: {x: 0.40821788, y: -0.23456968, z: 0.10938163, w: 0.8754261}
  m_LocalPosition: {x: 0, y: 3, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 50, y: -30, z: 0}
--- !u!1 &1960043525
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1960043526}
  - component: {fileID: 1960043529}
  - component: {fileID: 1960043528}
  - component: {fileID: 1960043527}
  m_Layer: 0
  m_Name: PanelBox (4)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1960043526
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1960043525}
  serializedVersion: 2
  m_LocalRotation: {x: 0.30070573, y: -0.000000029802322, z: -0, w: 0.953717}
  m_LocalPosition: {x: 0.553, y: 1.9574, z: -0.561}
  m_LocalScale: {x: 0.19999999, y: 1, z: 0.11}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 365727461}
  m_LocalEulerAnglesHint: {x: 35, y: 0, z: 0}
--- !u!23 &1960043527
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1960043525}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: cc44f8663b1fc8f439493267bf8260d6, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 1
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &1960043528
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1960043525}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!33 &1960043529
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1960043525}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1988418107
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1988418108}
  m_Layer: 0
  m_Name: BTail_12
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1988418108
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1988418107}
  serializedVersion: 2
  m_LocalRotation: {x: -9.486012e-10, y: -4.319517e-32, z: 5.7282044e-26, w: 1}
  m_LocalPosition: {x: 3.0422626e-16, y: 2.6763544e-16, z: -0.15717402}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1690211385}
  m_Father: {fileID: 760925527}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &1991245043
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 1531704303419712, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_Name
      value: PR_Sign
      objectReference: {fileID: 0}
    - target: {fileID: 4385575217446778, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_LocalScale.x
      value: 1.3051335
      objectReference: {fileID: 0}
    - target: {fileID: 4385575217446778, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_LocalScale.y
      value: 0.57050467
      objectReference: {fileID: 0}
    - target: {fileID: 4385575217446778, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_LocalPosition.y
      value: 1.558
      objectReference: {fileID: 0}
    - target: {fileID: 4582760747290996, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_LocalPosition.y
      value: 1.563
      objectReference: {fileID: 0}
    - target: {fileID: 4878481119553484, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_RootOrder
      value: 16
      objectReference: {fileID: 0}
    - target: {fileID: 4878481119553484, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_LocalPosition.x
      value: -8.04
      objectReference: {fileID: 0}
    - target: {fileID: 4878481119553484, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.049999952
      objectReference: {fileID: 0}
    - target: {fileID: 4878481119553484, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_LocalPosition.z
      value: 4.81
      objectReference: {fileID: 0}
    - target: {fileID: 4878481119553484, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_LocalRotation.w
      value: -0.012216864
      objectReference: {fileID: 0}
    - target: {fileID: 4878481119553484, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4878481119553484, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0.9999254
      objectReference: {fileID: 0}
    - target: {fileID: 4878481119553484, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4878481119553484, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: -181.4
      objectReference: {fileID: 0}
    - target: {fileID: 102948136206050596, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_Text
      value: 'Example of tail

        with 2d sprites'
      objectReference: {fileID: 0}
    - target: {fileID: 102948136206050596, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_FontSize
      value: 39
      objectReference: {fileID: 0}
    - target: {fileID: 102948136206050596, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
      propertyPath: m_Color.rgba
      value: 4278190080
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: e90b0fe22a8457644846c9b9d9587b5a, type: 3}
--- !u!1001 &1997598098
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 1665418947384104, guid: 176e4f0acb85ffe418c9b31490361eb6, type: 3}
      propertyPath: m_Name
      value: PR_TailAnim_2D Sprites Tail Example (1)
      objectReference: {fileID: 0}
    - target: {fileID: 4325343788362922, guid: 176e4f0acb85ffe418c9b31490361eb6, type: 3}
      propertyPath: m_RootOrder
      value: 21
      objectReference: {fileID: 0}
    - target: {fileID: 4325343788362922, guid: 176e4f0acb85ffe418c9b31490361eb6, type: 3}
      propertyPath: m_LocalPosition.x
      value: -13.64
      objectReference: {fileID: 0}
    - target: {fileID: 4325343788362922, guid: 176e4f0acb85ffe418c9b31490361eb6, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.85
      objectReference: {fileID: 0}
    - target: {fileID: 4325343788362922, guid: 176e4f0acb85ffe418c9b31490361eb6, type: 3}
      propertyPath: m_LocalPosition.z
      value: 6.6251616
      objectReference: {fileID: 0}
    - target: {fileID: 4325343788362922, guid: 176e4f0acb85ffe418c9b31490361eb6, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4325343788362922, guid: 176e4f0acb85ffe418c9b31490361eb6, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4325343788362922, guid: 176e4f0acb85ffe418c9b31490361eb6, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4325343788362922, guid: 176e4f0acb85ffe418c9b31490361eb6, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 114786583886263170, guid: 176e4f0acb85ffe418c9b31490361eb6, type: 3}
      propertyPath: FullCorrection
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 114786583886263170, guid: 176e4f0acb85ffe418c9b31490361eb6, type: 3}
      propertyPath: ExtraCorrectionOptions
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 114978096826313280, guid: 176e4f0acb85ffe418c9b31490361eb6, type: 3}
      propertyPath: FullCorrection
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 114978096826313280, guid: 176e4f0acb85ffe418c9b31490361eb6, type: 3}
      propertyPath: ExtraCorrectionOptions
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents:
    - {fileID: 114786583886263170, guid: 176e4f0acb85ffe418c9b31490361eb6, type: 3}
    - {fileID: 114978096826313280, guid: 176e4f0acb85ffe418c9b31490361eb6, type: 3}
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 1665418947384104, guid: 176e4f0acb85ffe418c9b31490361eb6, type: 3}
      insertIndex: -1
      addedObject: {fileID: 976130958}
    - targetCorrespondingSourceObject: {fileID: 1258571551842992, guid: 176e4f0acb85ffe418c9b31490361eb6, type: 3}
      insertIndex: -1
      addedObject: {fileID: 1889571828}
  m_SourcePrefab: {fileID: 100100000, guid: 176e4f0acb85ffe418c9b31490361eb6, type: 3}
--- !u!1 &2032476391
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2032476392}
  m_Layer: 0
  m_Name: ----------------------------------
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &2032476392
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2032476391}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 1.5048004, y: -2.4112737, z: 28.424793}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &2033388393 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4643279633872814, guid: 176e4f0acb85ffe418c9b31490361eb6, type: 3}
  m_PrefabInstance: {fileID: 1997598098}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &2075760667
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2075760668}
  - component: {fileID: 2075760670}
  - component: {fileID: 2075760669}
  m_Layer: 5
  m_Name: UI Tail Example (4)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2075760668
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2075760667}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 78370398}
  m_Father: {fileID: 455146903}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 19.61, y: 0}
  m_SizeDelta: {x: 20, y: 11.23}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &2075760669
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2075760667}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10911, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &2075760670
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2075760667}
  m_CullTransparentMesh: 1
--- !u!1 &2087479452
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2087479453}
  - component: {fileID: 2087479455}
  - component: {fileID: 2087479454}
  m_Layer: 0
  m_Name: Sign Text (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2087479453
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2087479452}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0.93749475, z: -0.3479994, w: 0}
  m_LocalPosition: {x: -0.287, y: 1.896, z: 0.445}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 623215220}
  m_LocalEulerAnglesHint: {x: 40.73, y: 180, z: 0}
--- !u!102 &2087479454
TextMesh:
  serializedVersion: 3
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2087479452}
  m_Text: 'tail animator

    & source animation'
  m_OffsetZ: 0
  m_CharacterSize: 0.016
  m_LineSpacing: 1
  m_Anchor: 4
  m_Alignment: 1
  m_TabSize: 4
  m_FontSize: 31
  m_FontStyle: 0
  m_RichText: 0
  m_Font: {fileID: 12800000, guid: dbc9b820232f93d4aab6f1d3bdd0d4d3, type: 3}
  m_Color:
    serializedVersion: 2
    rgba: 4278190080
--- !u!23 &2087479455
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2087479452}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10100, guid: 0000000000000000e000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &2097662812
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2097662813}
  m_Layer: 0
  m_Name: BTail_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2097662813
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2097662812}
  serializedVersion: 2
  m_LocalRotation: {x: 8.122556e-10, y: -3.6310655e-17, z: -3.279229e-24, w: 1}
  m_LocalPosition: {x: 1.0221429e-17, y: 0.000000006983843, z: -0.15062094}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 753767829}
  m_Father: {fileID: 1247455685}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1660057539 &9223372036854775807
SceneRoots:
  m_ObjectHideFlags: 0
  m_Roots:
  - {fileID: 964547477}
  - {fileID: 1927895608}
  - {fileID: 1407205960}
  - {fileID: 93597055}
  - {fileID: 1292857971}
  - {fileID: 847979496}
  - {fileID: 1717911228}
  - {fileID: 925753601}
  - {fileID: 1847961720}
  - {fileID: 206509717}
  - {fileID: 1044335567}
  - {fileID: 106963463}
  - {fileID: 365727457}
  - {fileID: 1533512506}
  - {fileID: 695748139}
  - {fileID: 114507907}
  - {fileID: 1991245043}
  - {fileID: 474176127}
  - {fileID: 1356396049}
  - {fileID: 664454798}
  - {fileID: 303658580}
  - {fileID: 1997598098}
  - {fileID: 2032476392}
  - {fileID: 1806612024}
  - {fileID: 642893034}
  - {fileID: 623215216}
  - {fileID: 263131700}
