%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Double Layer Custom Surface
  m_Shader: {fileID: 4800000, guid: 3abed8409b6a4512a3f604c0182c08b1, type: 3}
  m_ValidKeywords: []
  m_InvalidKeywords:
  - _ADD_PRECOMPUTED_VELOCITY
  m_LightmapFlags: 2
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap:
    MotionVector: User
  disabledShaderPasses:
  - TransparentBackface
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _CarNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _CoatNormal:
        m_Texture: {fileID: 2800000, guid: 221db06f30ae4c25a257d852f88a374d, type: 3}
        m_Scale: {x: 2, y: 2}
        m_Offset: {x: 0, y: 0}
    - _CoatlNormal:
        m_Texture: {fileID: 2800000, guid: b3d940e75e1f5d24684cd93a2758e1bf, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _FlakeColorMask:
        m_Texture: {fileID: 2800000, guid: 21a6bd9b3f2c90047b38cea2fe37f5bb, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _FlakeMask:
        m_Texture: {fileID: 2800000, guid: f3337d1b16764ec4b94b4e148a2192e0, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Flakes:
        m_Texture: {fileID: 2800000, guid: 2741be98b31d56c43ad9cfbcaf99a799, type: 3}
        m_Scale: {x: 6, y: 6}
        m_Offset: {x: 0, y: 0}
    - _FlakesNormal:
        m_Texture: {fileID: 2800000, guid: 8f66c1c8460e4419a467d27f526fad23, type: 3}
        m_Scale: {x: 8, y: 8}
        m_Offset: {x: 0, y: 0}
    - _FlakesRGBcolorvariationAmask:
        m_Texture: {fileID: 2800000, guid: 67895d953be64bc4bd0b09b78633f920, type: 3}
        m_Scale: {x: 8, y: 8}
        m_Offset: {x: 0, y: 0}
    - _MaskFlaks:
        m_Texture: {fileID: 2800000, guid: 21a6bd9b3f2c90047b38cea2fe37f5bb, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OrangePeelNormal:
        m_Texture: {fileID: 2800000, guid: b3d940e75e1f5d24684cd93a2758e1bf, type: 3}
        m_Scale: {x: 5, y: 5}
        m_Offset: {x: 0, y: 0}
    - _T_FlakeMask:
        m_Texture: {fileID: 2800000, guid: f3337d1b16764ec4b94b4e148a2192e0, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _T_MetalFlakes_N:
        m_Texture: {fileID: 2800000, guid: 24496d6d8719fad4f864e967fbda4576, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TextureSample0:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TextureSample1:
        m_Texture: {fileID: 2800000, guid: 40bd4a70901dcd14ca65688f3f08b7c8, type: 3}
        m_Scale: {x: 50, y: 50}
        m_Offset: {x: 0, y: 0}
    - _normal_flakes_Flake_Mask:
        m_Texture: {fileID: 2800000, guid: a5cb640d1bcc09742b602f9a846b6f89, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _normal_flakes_output:
        m_Texture: {fileID: 2800000, guid: da27fb303f4a4214eb9a79e0ee21736e, type: 3}
        m_Scale: {x: 4, y: 4}
        m_Offset: {x: 0, y: 0}
    - _normal_flakes_outputcolors:
        m_Texture: {fileID: 2800000, guid: 8f0e673104ee74d4e8b81221b12ef44d, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _texcoord:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _texcoord2:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _A: 1
    - _AddPrecomputedVelocity: 1
    - _AlphaCutoff: 0.5
    - _AlphaCutoffEnable: 0
    - _AlphaDstBlend: 0
    - _AlphaSrcBlend: 1
    - _B: 1
    - _BaseMetallic: 0.6
    - _BaseOcclusion: 1.6204118
    - _BaseSmoothness: 0.447
    - _BlendMode: 0
    - _Brightness: 1
    - _CarMetallic: 0.5049318
    - _CarSmoothness: 0
    - _CarSmoothnessMax: 1
    - _CarSmoothnessMin: 0.36406696
    - _ClearCoatSmoothness: 0.95
    - _Coat: 0.3
    - _CoatAmount: 2.33
    - _CoatBump: 0.117
    - _CoatSmoothness: 0.265
    - _CullMode: 2
    - _CullModeForward: 2
    - _DoubleSidedEnable: 0
    - _DoubleSidedNormalMode: 2
    - _DstBlend: 0
    - _EnableFogOnTransparent: 1
    - _EnvironmentReflections: 1
    - _FlakeColorVariationAmount: 0.121
    - _FlakesBrightness: 1
    - _FlakesBump: 0.111
    - _FlakesMetallic: 0.8
    - _FlakesScale: 50
    - _FlakesSmoothness: 0.505
    - _FlakesTilling: 0
    - _Float0: 1
    - _Float1: 1.89
    - _Float2: 50.16
    - _Float3: 0
    - _Float4: 0.5
    - _OrangePeelBump: 0.2
    - _OrangePeelSmoothness: 0.95
    - _OrangePeelTilling: 20
    - _Phase: 0.02
    - _QueueControl: 0
    - _QueueOffset: 0
    - _ReceiveShadows: 1
    - _ReceivesSSR: 1
    - _RenderQueueType: 1
    - _RequireSplitLighting: 0
    - _Smooth: 0.5
    - _SmoothnessMultiplier: 1.89
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _StencilRef: 0
    - _StencilRefDepth: 8
    - _StencilRefDistortionVec: 4
    - _StencilRefGBuffer: 10
    - _StencilRefMV: 40
    - _StencilWriteMask: 6
    - _StencilWriteMaskDepth: 8
    - _StencilWriteMaskDistortionVec: 4
    - _StencilWriteMaskGBuffer: 14
    - _StencilWriteMaskMV: 40
    - _SurfaceType: 0
    - _Tilling: 1
    - _TransparentBackfaceEnable: 0
    - _TransparentCullMode: 2
    - _TransparentSortPriority: 0
    - _TransparentZWrite: 1
    - _UseShadowThreshold: 0
    - _ZTestDepthEqualForOpaque: 3
    - _ZTestGBuffer: 4
    - _ZTestTransparent: 4
    - _ZWrite: 1
    - __dirty: 0
    m_Colors:
    - _BaseColor: {r: 0.3970588, g: 0.19232786, b: 0.03503461, a: 0}
    - _BaseColor1: {r: 0.39705768, g: 0.19232678, b: 0.03503461, a: 0}
    - _BaseColor2: {r: 0.035034638, g: 0.08247126, b: 0.3970577, a: 0}
    - _CarColor: {r: 0.08832457, g: 0.07493501, b: 0.46323526, a: 0}
    - _CarPaintColor: {r: 0.088324, g: 0.074934505, b: 0.46323526, a: 0}
    - _Color1: {r: 0.021626277, g: 0.056079105, b: 0.7352942, a: 0.953}
    - _Color3: {r: 0.035034638, g: 0.08247225, b: 0.39705884, a: 0}
    - _DoubleSidedConstants: {r: 1, g: 1, b: -1, a: 0}
    - _EmissionColor: {r: 1, g: 1, b: 1, a: 1}
    - _FlakesColor: {r: 0.73529416, g: 0.021626318, b: 0.36123332, a: 0.953}
    - _FlakesColor1: {r: 1, g: 0, b: 0.47817135, a: 0}
    - _FlakesColor2: {r: 0.06905645, g: 0, b: 0.7735849, a: 0}
    - _Offset1: {r: 1.2, g: 6.53, b: -6.86, a: 0}
  m_BuildTextureStacks: []
