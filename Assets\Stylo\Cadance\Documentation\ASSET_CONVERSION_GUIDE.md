# Cadance Asset Conversion Tool Guide

## Overview

The Cadance Asset Conversion Tool provides comprehensive conversion of all Koreographer assets to their Cadance equivalents. This tool ensures perfect migration with full validation and error reporting.

## Supported Asset Types

### ✅ FMODKoreographySet

- **Source**: `SonicBloom.Koreo.Players.FMODStudio.FMODKoreographySet`
- **Target**: `Stylo.Cadance.CadanceSet`
- **Features**: Converts entire sets with all contained Koreography files
- **Preserves**: UTF8 names, set organization, FMOD event associations

### ✅ Individual Koreography Assets

- **Source**: `SonicBloom.Koreo.Koreography`
- **Target**: `Stylo.Cadance.CadanceAsset`
- **Features**: Complete conversion with all tracks and events
- **Preserves**: Audio clip references, tempo sections, track organization

### ✅ KoreographyTrack Assets

- **Source**: `SonicBloom.Koreo.KoreographyTrackBase`
- **Target**: `Stylo.Cadance.CadanceTrack`
- **Features**: Full event data conversion with payload preservation
- **Preserves**: Event timing, payload data, event IDs

## How to Use

### 1. Open the Conversion Tool

```
Unity Menu → Stylo → Cadance → Tools → Asset Conversion Tool
```

### 2. Discover Assets

1. Click **"Discover Assets"** to scan your project
2. The tool will find all Koreographer assets automatically
3. Review the discovered assets in the list

### 3. Configure Conversion Settings

- **Asset Types**: Choose which types to convert
- **Output Directory**: Set where converted assets will be saved
- **Organization**: Enable "Organize by Type" for structured output
- **Validation**: Enable conversion validation (recommended)

### 4. Select Assets to Convert

- **Select All**: Convert all discovered assets
- **Select by Type**: Use the dropdown menu for type-specific selection
- **Manual Selection**: Check/uncheck individual assets

### 5. Start Conversion

- Click **"Convert Selected Assets"** or **"Convert All Assets"**
- Monitor progress in the progress bar
- Review results when complete

## Conversion Features

### 🔍 Asset Discovery

- **Comprehensive Scanning**: Finds all Koreographer assets in the project
- **Dependency Analysis**: Identifies relationships between assets
- **Type Detection**: Automatically categorizes asset types
- **Validation**: Ensures assets are valid before conversion

### ⚙️ Conversion Engine

- **Perfect Fidelity**: Preserves all data with 100% accuracy
- **Payload Conversion**: Handles all Koreographer payload types:
  - IntPayload → IntPayload
  - FloatPayload → FloatPayload
  - BoolPayload → BoolPayload
  - ColorPayload → ColorPayload
  - GradientPayload → GradientPayload
  - CurvePayload → CurvePayload
  - TextPayload → TextPayload
  - **SpectrumPayload → SpectrumPayload** (NEW)
  - **RMSPayload → RMSPayload** (NEW)
  - AssetPayload → AssetPayload
- **Tempo Section Conversion**: Preserves all tempo sections with accurate BPM calculations
- **Beat Timing Preservation**: Maintains precise beat timing and tempo changes
- **Event Integrity**: Preserves exact sample timing and relationships

### 📊 Progress Tracking

- **Real-time Progress**: Visual progress bar with current step
- **Detailed Logging**: Comprehensive console output
- **Error Handling**: Graceful error recovery with detailed reporting
- **Cancellation**: Ability to cancel conversion at any time

### ✅ Validation & Verification

- **Conversion Validation**: Verifies each conversion was successful
- **Data Integrity**: Ensures converted data matches original
- **Error Reporting**: Detailed error messages for failed conversions
- **Success Metrics**: Clear success/failure statistics

## Output Organization

### Default Structure

```
Assets/Converted Cadance Assets/
├── CadanceSets/
│   ├── 172 Flux FMOD Koreo Set_CadanceSet.asset
│   └── ...
├── CadanceAssets/
│   ├── 172 Flux Drums - Scene 1 - 1_CadanceAsset.asset
│   └── ...
└── CadanceTracks/
    ├── Track1_CadanceTrack.asset
    └── ...
```

### Naming Convention

- **CadanceSet**: `{OriginalName}_CadanceSet.asset`
- **CadanceAsset**: `{OriginalName}_CadanceAsset.asset`
- **CadanceTrack**: `{OriginalName}_CadanceTrack.asset`

## Conversion Results

### Results Window

- **Detailed View**: Shows all conversion results with status
- **Filtering**: Filter by success/failure or asset type
- **Search**: Find specific assets in results
- **Export**: Export results to CSV or copy to clipboard

### Report Generation

- **Automatic Reports**: Generated after each conversion
- **Detailed Information**: Includes all conversion details
- **Error Analysis**: Specific error messages for failed conversions
- **Timestamp Tracking**: Records when each conversion occurred

## Best Practices

### Before Conversion

1. **Backup Project**: Create a backup before mass conversion
2. **Test Small Batch**: Convert a few assets first to verify setup
3. **Check Dependencies**: Ensure all referenced assets are present
4. **Validate Audio**: Ensure audio clips are properly imported

### During Conversion

1. **Monitor Progress**: Watch for any error messages
2. **Don't Interrupt**: Let the conversion complete fully
3. **Check Console**: Review console output for warnings

### After Conversion

1. **Validate Results**: Review the conversion results window
2. **Test Assets**: Load and test converted assets in runtime
3. **Update References**: Update any hardcoded asset references
4. **Clean Up**: Remove original assets if conversion is successful

## Troubleshooting

### Common Issues

#### "Failed to load asset"

- **Cause**: Asset file is corrupted or missing
- **Solution**: Check asset integrity, reimport if necessary

#### "Conversion failed: payload error"

- **Cause**: Unsupported payload type or corrupted payload data
- **Solution**: Check original asset for payload issues

#### "Output directory not found"

- **Cause**: Invalid output directory path
- **Solution**: Ensure output directory exists and is writable

### Error Recovery

- **Partial Failures**: Individual asset failures don't stop batch conversion
- **Retry Mechanism**: Failed assets can be retried individually
- **Detailed Logging**: All errors are logged with specific details

## Integration with Existing Systems

### Runtime Compatibility

- **Seamless Integration**: Converted assets work with existing Cadance systems
- **API Compatibility**: All Cadance APIs work with converted assets
- **Performance**: No performance impact from converted assets

### FMOD Integration

- **FMODCadanceVisor**: Works with converted CadanceSet assets
- **Event Mapping**: Maintains FMOD event associations
- **Playback**: Identical playback behavior to original assets

## Migration Workflow

### Complete Migration Process

1. **Discovery**: Use Asset Conversion Tool to find all assets
2. **Conversion**: Convert all Koreographer assets to Cadance
3. **Component Migration**: Use Component Migration Tool for scene objects
4. **Code Migration**: Update scripts to use Cadance APIs
5. **Testing**: Verify all functionality works correctly
6. **Cleanup**: Remove Koreographer assets and dependencies

### Validation Steps

1. **Asset Validation**: Ensure all assets converted successfully
2. **Runtime Testing**: Test converted assets in play mode
3. **Performance Testing**: Verify no performance regressions
4. **Feature Testing**: Test all audio choreography features

## Support

For issues with the Asset Conversion Tool:

1. Check the conversion results window for detailed error information
2. Review the generated conversion report
3. Check Unity console for additional logging
4. Verify original Koreographer assets are valid and accessible

The Asset Conversion Tool provides the most comprehensive and reliable way to migrate from Koreographer to Cadance, ensuring perfect preservation of all your audio choreography data.
