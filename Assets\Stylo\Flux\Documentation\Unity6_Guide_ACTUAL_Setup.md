# Unity6 Datamosh Guide - ACTUAL Setup Instructions

## 🎯 **THE REAL UNITY6 GUIDE SYSTEM**

Your project **already has** the Unity6 Datamosh Guide system implemented! It's a **separate Custom Render Texture system** that works independently from Flux.

---

## 📁 **EXISTING UNITY6 GUIDE ASSETS**

✅ **Already in your project:**

1. **`Assets\_Scripts\VFX\DatamoshFeature.cs`** - Unity6 Guide renderer feature
2. **`Assets\_Shadergraph\Datamosh.shadergraph`** - Custom Render Texture Shader Graph
3. **`Assets\_Shadergraph\DatamoshRenderTexture.asset`** - Custom Render Texture asset
4. **`Assets\_Shadergraph\DatamoshSG.mat`** - Material for the shader graph
5. **`Assets\BT Render Pipeline\BT Render 1.asset`** - DatamoshFeature already added

---

## 🔧 **SETUP INSTRUCTIONS**

### **Step 1: Verify DatamoshFeature Setup**

1. **Open BT Render Pipeline asset** (`Assets\BT Render Pipeline\BT Render 1.asset`)
2. **Find DatamoshFeature** in the Renderer Features list
3. **Assign the material**:
   - Set `Datamosh Material` to `Assets\_Shadergraph\DatamoshSG.mat`
4. **Configure settings**:
   - Intensity: 0.87 (already set)
   - Blend Amount: 0.386 (already set)
   - Update Rate: 2.4 (already set)

### **Step 2: Enable Motion Vectors in URP**

1. **Open URP Asset** (your main URP renderer asset)
2. **Enable Motion Vectors**:
   - Check "Motion Vectors" in the URP asset settings
   - Ensure "Depth Texture" is also enabled
3. **Camera Settings**:
   - On your main camera, ensure "Allow Motion Vectors" is enabled

### **Step 3: Configure Custom Render Texture**

1. **Select `DatamoshRenderTexture.asset`**
2. **Verify settings**:
   - Material: Should reference the Datamosh Shader Graph material
   - Update Mode: Realtime
   - Resolution: 1920x1080 (or match your screen)
   - Format: RGBA32

### **Step 4: Test the System**

1. **Enter Play Mode**
2. **Move camera or objects** to generate motion
3. **You should see datamosh effects** from the Unity6 Guide system
4. **Check Console** for any DatamoshFeature warnings

---

## 🎮 **HOW IT WORKS**

### **Unity6 Guide Architecture**

```
Camera → DatamoshFeature → Custom Render Texture → Screen
         ↓
    1. Captures color + motion vectors
    2. Sets global shader variables
    3. Custom RT processes datamosh effect
    4. Result blitted to screen
```

### **Flux vs Unity6 Guide**

| System | Type | Integration | Use Case |
|--------|------|-------------|----------|
| **Flux** | Volume Component | Post-processing stack | Integrated workflow, presets |
| **Unity6 Guide** | Custom RT Feature | Renderer feature | Advanced Custom RT effects |

**Both can run simultaneously** for layered effects!

---

## 🔍 **TROUBLESHOOTING**

### **No Visual Effect**

1. **Check Material Assignment**:
   - DatamoshFeature → Datamosh Material → `DatamoshSG.mat`
2. **Verify Motion Vectors**:
   - URP Asset → Motion Vectors ✅
   - Camera → Allow Motion Vectors ✅
3. **Check Custom RT**:
   - DatamoshRenderTexture → Material assigned ✅
   - Update Mode: Realtime ✅

### **Console Warnings**

- **"Motion vectors not available"**: Enable motion vectors in URP settings
- **"Datamosh Material is not assigned"**: Assign `DatamoshSG.mat` to DatamoshFeature
- **Shader compilation errors**: Check Datamosh.shadergraph for issues

### **Performance Issues**

1. **Reduce Custom RT Resolution**:
   - DatamoshRenderTexture → Width/Height → 1280x720 or lower
2. **Adjust Update Rate**:
   - DatamoshFeature → Update Rate → Lower values (1.0-1.5)
3. **Optimize Shader Graph**:
   - Simplify Datamosh.shadergraph if needed

---

## 🎨 **CUSTOMIZATION**

### **Modify Datamosh Effect**

1. **Open `Datamosh.shadergraph`**
2. **Edit the Custom Render Texture graph**:
   - Adjust motion vector processing
   - Modify pixelation effects
   - Change color blending
3. **Parameters available**:
   - Global textures: `_GlobalColorTexture`, `_GlobalMotionTexture`
   - Custom properties in the shader graph

### **Combine with Flux**

1. **Use both systems together**:
   - Unity6 Guide for base datamosh effect
   - Flux for additional post-processing
2. **Layered approach**:
   - Unity6 Guide → Custom RT processing
   - Flux → Volume-based refinements

---

## 📊 **COMPARISON: My Wrong Integration vs Real Unity6 Guide**

### **What I Did Wrong**
- ❌ Added "Unity6 parameters" to Flux Volume Component
- ❌ Tried to integrate Custom RT into Flux shaders
- ❌ Created non-functional "Custom RT Pipeline" checkbox
- ❌ Modified Flux instead of using existing Unity6 system

### **What Actually Exists**
- ✅ Complete Unity6 Guide implementation as separate system
- ✅ Custom Render Texture with Shader Graph
- ✅ DatamoshFeature renderer feature (with bugs I fixed)
- ✅ Independent from Flux, can work alongside it

---

## 🚀 **NEXT STEPS**

1. **Remove my wrong "Unity6 parameters"** from Flux (they do nothing)
2. **Use the real Unity6 Guide system** following this setup guide
3. **Test DatamoshFeature** with proper material assignment
4. **Experiment with Shader Graph** for custom datamosh effects
5. **Combine with Flux** for layered post-processing if desired

**The Unity6 Datamosh Guide system is already in your project and ready to use!** 🎬✨

---

## 📝 **SUMMARY**

- **Unity6 Guide ≠ Flux integration** - they're separate systems
- **DatamoshFeature already exists** - just needs proper setup
- **Custom RT system is complete** - just needs material assignment
- **Motion vector bug fixed** - DatamoshFeature now uses actual motion vectors
- **Both systems can coexist** - use Unity6 Guide for Custom RT effects, Flux for Volume-based processing
