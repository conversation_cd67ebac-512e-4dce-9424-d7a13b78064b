<?xml version="1.0" encoding="UTF-8"?><package><identifier v="Unsaved Package"/><formatVersion v="1.1.0.202302"/><updaterVersion v="1.1.0.202302"/><fileUID v="{9a7c36c1-6d8b-4c3f-83c3-107986ffe9fb}"/><versionUID v="0"/><dependencies><dependency><filename v="sbs://pattern_polygon_2.sbs"/><uid v="1487918781"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://splatter.sbs"/><uid v="1487921414"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://pattern_gaussian_1.sbs"/><uid v="1487922179"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://curvature_smooth.sbs"/><uid v="1449590280"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_clouds_2.sbs"/><uid v="1372403415"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://histogram_scan.sbs"/><uid v="1449404462"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://blur_hq.sbs"/><uid v="1299236171"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_perlin_noise.sbs"/><uid v="1487497977"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://hbao_2.sbs"/><uid v="1297162667"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://slope_blur.sbs"/><uid v="1449402040"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://pattern_tile_sampler.sbs"/><uid v="1449399972"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://height_blend.sbs"/><uid v="1449623608"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency></dependencies><content><graph><identifier v="sand"/><uid v="1487918720"/><graphtype v="material"/><graphOutputs><graphoutput><identifier v="basecolor"/><uid v="1213284336"/><attributes><label v="Base Color"/></attributes><usages><usage><components v="RGBA"/><name v="baseColor"/></usage></usages><group v="Material"/></graphoutput><graphoutput><identifier v="normal"/><uid v="1213284338"/><attributes><label v="Normal"/></attributes><usages><usage><components v="RGBA"/><name v="normal"/></usage></usages><group v="Material"/></graphoutput><graphoutput><identifier v="roughness"/><uid v="1213284340"/><attributes><label v="Roughness"/></attributes><usages><usage><components v="RGBA"/><name v="roughness"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput><graphoutput><identifier v="height"/><uid v="1279137031"/><attributes><label v="Height"/></attributes><usages><usage><components v="RGBA"/><name v="height"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput><graphoutput><identifier v="ambientocclusion"/><uid v="1359211721"/><attributes><label v="Ambient Occlusion"/></attributes><usages><usage><components v="RGBA"/><name v="ambientOcclusion"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput></graphOutputs><compNodes><compNode><uid v="1213284337"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1487923395"/><connRefOutput v="1487923396"/></connection></connections><GUILayout><gpos v="-48 -240 0"/></GUILayout><compImplementation><compOutputBridge><output v="1213284336"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1213284339"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1359211383"/><connRefOutput v="1359211384"/></connection></connections><GUILayout><gpos v="-48 -80 0"/></GUILayout><compImplementation><compOutputBridge><output v="1213284338"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1213284341"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1487925307"/><connRefOutput v="1487925308"/></connection></connections><GUILayout><gpos v="-48 80 0"/></GUILayout><compImplementation><compOutputBridge><output v="1213284340"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1279137030"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1487963664"/><connRefOutput v="1487963665"/></connection></connections><GUILayout><gpos v="-48 560 0"/></GUILayout><compImplementation><compOutputBridge><output v="1279137031"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1359211383"/><connections><connection><identifier v="input1"/><connRef v="1487925307"/><connRefOutput v="1487925308"/></connection></connections><GUILayout><gpos v="-208 -80 0"/></GUILayout><compOutputs><compOutput><uid v="1359211384"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="normal"/><parameters><parameter><name v="input2alpha"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="14.4699993"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1359211720"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1487927441"/><connRefOutput v="1487927442"/></connection></connections><GUILayout><gpos v="-48 400 0"/></GUILayout><compImplementation><compOutputBridge><output v="1359211721"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1487921380"/><GUILayout><gpos v="-2719.18628 48 0"/></GUILayout><compOutputs><compOutput><uid v="1487921381"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///polygon_2?dependency=1487918781"/><parameters/><outputBridgings><outputBridging><uid v="1487921381"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487921589"/><connections><connection><identifier v="Source"/><connRef v="1487921380"/><connRefOutput v="1487921381"/></connection></connections><GUILayout><gpos v="-2591.18628 48 0"/></GUILayout><compOutputs><compOutput><uid v="1487921590"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///splatter?dependency=1487921414"/><parameters><parameter><name v="Pattern_size_width"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="2448.72998"/></paramValue></parameter><parameter><name v="Pattern_size_height"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="723.639954"/></paramValue></parameter><parameter><name v="Rotation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="-231.709991"/></paramValue></parameter><parameter><name v="Disorder"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="25"/></paramValue></parameter><parameter><name v="Size_Var"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="42.0800018"/></paramValue></parameter><parameter><name v="Lum_Var"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.379999995"/></paramValue></parameter><parameter><name v="Gain"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.799999952"/></paramValue></parameter><parameter><name v="Grid_Number"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="PatternSize"/><relativeTo v="0"/><paramValue><constantValueInt32 v="12"/></paramValue></parameter><parameter><name v="Pan_X"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="-18.5499992"/></paramValue></parameter><parameter><name v="Pan_Y"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="20.7299995"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487921590"/><identifier v="Splatter_Grayscale"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487921945"/><connections><connection><identifier v="input1"/><connRef v="1487921589"/><connRefOutput v="1487921590"/></connection><connection><identifier v="inputgradient"/><connRef v="1487922253"/><connRefOutput v="1487922254"/></connection></connections><GUILayout><gpos v="-2414.37231 48 0"/></GUILayout><compOutputs><compOutput><uid v="1487921946"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="warp"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.419999987"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487922253"/><GUILayout><gpos v="-2591.18628 192.346771 0"/></GUILayout><compOutputs><compOutput><uid v="1487922254"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///gaussian_1?dependency=1487922179"/><parameters><parameter><name v="Tiling"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487922254"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487923119"/><connections><connection><identifier v="input"/><connRef v="1359211383"/><connRefOutput v="1359211384"/></connection></connections><GUILayout><gpos v="-223.050598 -240 0"/></GUILayout><compOutputs><compOutput><uid v="1487923120"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///curvature_smooth?dependency=1449590280"/><parameters/><outputBridgings><outputBridging><uid v="1487923120"/><identifier v="height"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487923395"/><connections><connection><identifier v="input1"/><connRef v="1487923119"/><connRefOutput v="1487923120"/></connection></connections><GUILayout><gpos v="-144 -368 0"/></GUILayout><compOutputs><compOutput><uid v="1487923396"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1487923422"/><paramsArrayCells><paramsArrayCell><uid v="1488102445"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.0470588244 0.0313725509 0.0392156877 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1488102446"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.562501371"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.600000024 0.425068349 0.27558139 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1488102447"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.831223607"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.752173901 0.522528648 0.319236606 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1488102448"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.287557662"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.530434787 0.366744369 0.246713847 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1488102449"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.478902936"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.756521761 0.585242212 0.373862505 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1487923790"/><connections><connection><identifier v="input1"/><connRef v="1487921945"/><connRefOutput v="1487921946"/></connection><connection><identifier v="inputgradient"/><connRef v="1487924311"/><connRefOutput v="1487924312"/></connection></connections><GUILayout><gpos v="-2271.18628 48 0"/></GUILayout><compOutputs><compOutput><uid v="1487923791"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="warp"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0199999996"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487924311"/><GUILayout><gpos v="-2412.63721 208 0"/></GUILayout><compOutputs><compOutput><uid v="1487924312"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///clouds_2?dependency=1372403415"/><parameters/><outputBridgings><outputBridging><uid v="1487924312"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487924476"/><connections><connection><identifier v="Input_1"/><connRef v="1487923790"/><connRefOutput v="1487923791"/></connection></connections><GUILayout><gpos v="-2128 48 0"/></GUILayout><compOutputs><compOutput><uid v="1487924477"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1449404462"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.5"/></paramValue></parameter><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.270000011"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487924477"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487924633"/><connections><connection><identifier v="Source"/><connRef v="1487924476"/><connRefOutput v="1487924477"/></connection></connections><GUILayout><gpos v="-1968 48 0"/></GUILayout><compOutputs><compOutput><uid v="1487924634"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1299236171"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.400000006"/></paramValue></parameter><parameter><name v="Quality"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487924634"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487924725"/><connections><connection><identifier v="destination"/><connRef v="1487924633"/><connRefOutput v="1487924634"/></connection></connections><GUILayout><gpos v="-1776 48 0"/></GUILayout><compOutputs><compOutput><uid v="1487924726"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0199999996"/></paramValue></parameter><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="9"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487925307"/><connections><connection><identifier v="destination"/><connRef v="1487976507"/><connRefOutput v="1487976508"/></connection></connections><GUILayout><gpos v="-944 48 0"/></GUILayout><compOutputs><compOutput><uid v="1487925308"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.200000003"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1487927441"/><connections><connection><identifier v="input"/><connRef v="1487925307"/><connRefOutput v="1487925308"/></connection></connections><GUILayout><gpos v="-223.030457 400 0"/></GUILayout><compOutputs><compOutput><uid v="1487927442"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///hbao?dependency=1297162667"/><parameters><parameter><name v="radius"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.319999993"/></paramValue></parameter><parameter><name v="height_depth"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.239999995"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487927442"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487963664"/><connections><connection><identifier v="Input_1"/><connRef v="1487925307"/><connRefOutput v="1487925308"/></connection></connections><GUILayout><gpos v="-222.963211 560 0"/></GUILayout><compOutputs><compOutput><uid v="1487963665"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1449404462"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.449999988"/></paramValue></parameter><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.75"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487963665"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487973331"/><GUILayout><gpos v="-2512 -432 0"/></GUILayout><compOutputs><compOutput><uid v="1487973332"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///polygon_2?dependency=1487918781"/><parameters><parameter><name v="Sides"/><relativeTo v="0"/><paramValue><constantValueInt32 v="8"/></paramValue></parameter><parameter><name v="Curve"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0399999991"/></paramValue></parameter><parameter><name v="Gradient"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.109999999"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487973332"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487974053"/><connections><connection><identifier v="Source"/><connRef v="1487973331"/><connRefOutput v="1487973332"/></connection><connection><identifier v="Effect"/><connRef v="1487974064"/><connRefOutput v="1487974065"/></connection></connections><GUILayout><gpos v="-2352 -432 0"/></GUILayout><compOutputs><compOutput><uid v="1487974054"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///slope_blur_grayscale_2?dependency=1449402040"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="32"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="7.53000021"/></paramValue></parameter><parameter><name v="mode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487974054"/><identifier v="Slope_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487974064"/><GUILayout><gpos v="-2512 -304 0"/></GUILayout><compOutputs><compOutput><uid v="1487974065"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///perlin_noise?dependency=1487497977"/><parameters><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueInt32 v="8"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487974065"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487975095"/><connections><connection><identifier v="pattern_input"/><connRef v="1487974053"/><connRefOutput v="1487974054"/></connection><connection><identifier v="mask_map_input"/><connRef v="1487976220"/><connRefOutput v="1487976221"/></connection></connections><GUILayout><gpos v="-2128 -464 0"/></GUILayout><compOutputs><compOutput><uid v="1487975096"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1449399972"/><parameters><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.5"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="10"/></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.5"/></paramValue></parameter><parameter><name v="rotation_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="color_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.610000014"/></paramValue></parameter><parameter><name v="mask_map_threshold"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.649999976"/></paramValue></parameter><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="38"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="30"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487975096"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487975722"/><connections><connection><identifier v="Source"/><connRef v="1487975095"/><connRefOutput v="1487975096"/></connection><connection><identifier v="Effect"/><connRef v="1487975754"/><connRefOutput v="1487975755"/></connection></connections><GUILayout><gpos v="-1904 -432 0"/></GUILayout><compOutputs><compOutput><uid v="1487975723"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///slope_blur_grayscale_2?dependency=1449402040"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0700000003"/></paramValue></parameter><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="32"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487975723"/><identifier v="Slope_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487975754"/><GUILayout><gpos v="-2128 -272 0"/></GUILayout><compOutputs><compOutput><uid v="1487975755"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///perlin_noise?dependency=1487497977"/><parameters><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueInt32 v="52"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1487975755"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487976220"/><GUILayout><gpos v="-2352 -272 0"/></GUILayout><compOutputs><compOutput><uid v="1487976221"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///clouds_2?dependency=1372403415"/><parameters/><outputBridgings><outputBridging><uid v="1487976221"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1487976507"/><connections><connection><identifier v="destination"/><connRef v="1487924725"/><connRefOutput v="1487924726"/></connection><connection><identifier v="source"/><connRef v="1488036823"/><connRefOutput v="1488036824"/></connection></connections><GUILayout><gpos v="-1456 48 0"/></GUILayout><compOutputs><compOutput><uid v="1487976508"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="10"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.560000002"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488036823"/><connections><connection><identifier v="height_bottom"/><connRef v="1487975722"/><connRefOutput v="1487975723"/></connection></connections><GUILayout><gpos v="-1712 -432 0"/></GUILayout><compOutputs><compOutput><uid v="1488036824"/><comptype v="2"/></compOutput><compOutput><uid v="1488036825"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///height_blend?dependency=1449623608"/><parameters><parameter><name v="contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.129999995"/></paramValue></parameter><parameter><name v="height_position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.599999964"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1488036824"/><identifier v="blended_height"/></outputBridging><outputBridging><uid v="1488036825"/><identifier v="height_mask"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode></compNodes><baseParameters/><options><option><name v="defaultParentSize"/><value v="11x11"/></option><option><name v="export/fromGraph/autoExport"/><value v="false"/></option><option><name v="export/fromGraph/destination"/><value v="D:/work/Annery/stylized material/stylized material/images/sand with dirt"/></option><option><name v="export/fromGraph/extension"/><value v="png"/></option><option><name v="export/fromGraph/outputs/ambientocclusion"/><value v="true"/></option><option><name v="export/fromGraph/outputs/basecolor"/><value v="true"/></option><option><name v="export/fromGraph/outputs/height"/><value v="true"/></option><option><name v="export/fromGraph/outputs/metallic"/><value v="true"/></option><option><name v="export/fromGraph/outputs/normal"/><value v="true"/></option><option><name v="export/fromGraph/outputs/roughness"/><value v="true"/></option><option><name v="export/fromGraph/outputsColorspace/ambientocclusion"/><value v="Raw"/></option><option><name v="export/fromGraph/outputsColorspace/basecolor"/><value v="sRGB"/></option><option><name v="export/fromGraph/outputsColorspace/height"/><value v="Raw"/></option><option><name v="export/fromGraph/outputsColorspace/metallic"/><value v="Raw"/></option><option><name v="export/fromGraph/outputsColorspace/normal"/><value v="Raw"/></option><option><name v="export/fromGraph/outputsColorspace/roughness"/><value v="Raw"/></option><option><name v="export/fromGraph/pattern"/><value v="$(graph)_$(identifier)"/></option></options><root><rootOutputs><rootOutput><output v="1213284336"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1213284338"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1213284340"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1279137031"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1359211721"/><format v="0"/><usertag v=""/></rootOutput></rootOutputs></root></graph></content></package>
