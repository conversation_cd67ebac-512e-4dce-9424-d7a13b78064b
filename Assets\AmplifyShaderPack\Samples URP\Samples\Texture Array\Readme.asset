%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e78934177bebe0545ab566bb787b0282, type: 3}
  m_Name: Readme
  m_EditorClassIdentifier: 
  Icon: {fileID: 2800000, guid: 6055ac3ba21c4ef4181916f98565ec35, type: 3}
  Title: Texture Array
  Description:
    Heading: 
    Text: This sample performs regular PBR shading making use of texture arrays.
    LinkText: 
    Url: 
  PropertiesHeader:
    Heading: Properties
    Text: 
    LinkText: 
    Url: 
  Properties:
  - Heading: Texture Array Albedo
    Text: Array of textures containing Albedo,Roughness and Occlusion maps.
    LinkText: 
    Url: 
  - Heading: Texture Array Normal
    Text: Array of texture containing normal map.
    LinkText: 
    Url: 
  - Heading: Normal Scale
    Text: Scale value for normal maps.
    LinkText: 
    Url: 
  - Heading: Rough Scale
    Text: Strength value to control roughness amount.
    LinkText: 
    Url: 
  AdditionalProperties: []
  AdditionalScripts: []
  LoadedLayout: 0
  RPType: 3
