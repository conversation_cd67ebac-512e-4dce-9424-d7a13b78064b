# Enhanced Cadance Conversion Tool Guide

## Overview

The enhanced Cadance Asset Conversion Tool now properly captures and preserves FMOD event references during the conversion process from Koreographer to Cadance. This ensures that converted CadanceSet assets maintain their FMOD event associations.

## Key Improvements

### 1. FMOD Event Association Preservation

The conversion tool now uses multiple methods to find and preserve FMOD event associations:

- **Scene Scanning**: Searches scene files for FMODEventDescriptionVisor components that reference the FMODKoreographySet
- **Prefab Scanning**: Searches prefab files for FMODEventDescriptionVisor components
- **Naming Convention Matching**: Falls back to intelligent naming-based matching when direct references aren't found

### 2. Enhanced CadanceSet Creation

When converting FMODKoreographySet to CadanceSet, the tool now:

- Creates CadanceEntry objects with proper FMOD event references (both GUID and Path)
- Uses multiple methods to ensure valid FMOD EventReference objects are created
- Enables FMOD integration automatically when event references are found
- Preserves all original metadata (UTF8 names, descriptions, etc.)
- Eliminates "GUID doesn't match path" validation errors

### 3. Comprehensive Error Handling

The tool includes robust error handling and logging:

- Detailed logging of found associations
- Graceful fallback when associations can't be found
- Clear warnings when FMOD events are missing

## How It Works

### Step 1: Asset Discovery

The tool scans your project for all Koreographer assets including:

- FMODKoreographySet assets
- Individual Koreography assets
- KoreographyTrack assets

### Step 2: FMOD Event Association Discovery

For each FMODKoreographySet, the tool:

1. **Scans Scene Files**: Looks for FMODEventDescriptionVisor components that reference the set
2. **Scans Prefab Files**: Searches prefabs for visor components
3. **Applies Naming Conventions**: Matches FMOD event names with set/Koreography names

### Step 3: Enhanced Conversion

During conversion:

1. **Creates CadanceAsset**: Converts each Koreography to CadanceAsset
2. **Associates FMOD Events**: Links found FMOD event references to CadanceEntry objects
3. **Enables Integration**: Automatically enables FMOD integration when events are found
4. **Preserves Metadata**: Maintains all original naming and configuration data

## Usage Instructions

### 1. Open the Conversion Tool

Navigate to: `Stylo > Cadance > Asset Conversion Tool`

### 2. Discover Assets

Click "Discover Assets" to scan your project for Koreographer assets.

### 3. Review Found Assets

The tool will display all discovered assets with their types and paths.

### 4. Configure Conversion Settings

- **Output Directory**: Choose where converted assets will be saved
- **Asset Types**: Select which types of assets to convert
- **Advanced Options**: Enable validation and reporting

### 5. Start Conversion

Click "Convert Selected Assets" or "Convert All Assets" to begin the process.

### 6. Review Results

The tool will display conversion results including:

- Number of successful conversions
- Number of failed conversions
- Detailed logs of FMOD event associations found

## Expected Results

After conversion, your CadanceSet assets should have:

### ✅ Proper FMOD Event References

- Each CadanceEntry will have valid EventReference with GUID and Path
- FMOD Integration will be automatically enabled
- No more "Event Not Found" warnings in the inspector

### ✅ Preserved Metadata

- Original UTF8 names maintained
- Source clip names preserved
- All timing and track data intact

### ✅ Full Functionality

- CadanceSet can be used directly with FMODCadanceVisor
- All FMOD events will play correctly
- Choreography data remains synchronized

## Troubleshooting

### No FMOD Events Found

If the tool can't find FMOD event associations:

1. **Check Visor Configuration**: Ensure FMODEventDescriptionVisor components are properly configured
2. **Verify Scene References**: Make sure scenes/prefabs actually reference the FMODKoreographySet
3. **Check Naming**: Ensure FMOD event names relate to Koreography/set names

### Partial Associations

If only some CadanceEntry objects have FMOD events:

1. **Review Logs**: Check conversion logs for specific association details
2. **Manual Assignment**: You can manually assign missing FMOD events in the inspector
3. **Re-run Conversion**: Try running the conversion again after fixing visor configurations

### Conversion Errors

If conversion fails:

1. **Check Dependencies**: Ensure all referenced assets are available
2. **Verify Permissions**: Make sure output directory is writable
3. **Review Logs**: Check Unity console for detailed error messages

## Migration Workflow

For best results, follow this workflow:

1. **Backup Project**: Always backup before major conversions
2. **Run Conversion Tool**: Convert all Koreographer assets
3. **Migrate Visors**: Use the Koreography Migration Utility to update visor components
4. **Test Functionality**: Verify all FMOD events play correctly
5. **Update References**: Update any code references to use new Cadance APIs

## Technical Details

### FMOD Event Association Methods

The tool uses three methods in order of preference:

1. **Direct Scene/Prefab Scanning**: Most reliable, finds actual component references
2. **YAML Content Analysis**: Parses scene/prefab files to extract event paths
3. **Naming Convention Matching**: Intelligent fallback based on name similarity

### CadanceEntry FMOD Integration

When FMOD events are found, the tool:

```csharp
// Creates CadanceEntry with FMOD integration
var entry = new CadanceEntry(cadanceAsset, fmodEvent, utf8Name);
// Automatically sets enableFMODIntegration = true
```

### Error Recovery

The tool includes comprehensive error recovery:

- Continues conversion even if some associations fail
- Provides detailed logging for troubleshooting
- Creates valid CadanceEntry objects even without FMOD events

This enhanced conversion tool ensures a smooth migration from Koreographer to Cadance while preserving all FMOD event functionality.
