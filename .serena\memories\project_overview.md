# BTR (Beat Traveller Reload) Project Overview

## Project Purpose
BTR (Beat Traveller Reload) is a Unity 6-based rhythm action game featuring:
- **Rhythm-based gameplay** with music synchronization via Koreographer
- **Projectile combat system** with advanced pooling and job system optimization
- **Enemy AI system** with component-based architecture and strategy patterns
- **Time manipulation mechanics** using Chronos time system
- **Ouroboros-themed levels** with serpentine boss encounters and space-time loop mechanics

## Core Game Concept
Players navigate through levels like "Ouroboros - Base" and "Ouroboros - Scene 1", fighting enemies and bosses in rhythm with the music. The game features:
- Beat-synchronized combat and movement
- Time control abilities (slow/rewind time)
- Projectile-based combat with advanced physics
- Arena-shrinking boss battles with wave-based attacks
- Mobius strip-style level design

## Technical Architecture
- **Unity 6** with Universal Render Pipeline (URP)
- **Custom Stylo Framework** with modular systems:
  - **Epoch**: Time management system
  - **Flux**: Visual effects and datamoshing
  - **MenUI**: UI Toolkit-based menu system
  - **BFI**: Black Frame Insertion for render pipeline
  - **Cadance**: Audio rhythm system (replacing <PERSON><PERSON><PERSON>)
- **Component-based architecture** with strategy patterns
- **Job System and Burst** for performance optimization
- **FMOD** for advanced audio management
- **Advanced pooling systems** for projectiles and effects