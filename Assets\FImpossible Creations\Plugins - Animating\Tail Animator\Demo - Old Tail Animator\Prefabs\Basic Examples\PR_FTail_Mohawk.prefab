%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &1000096094811730
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4608367468215096}
  - component: {fileID: 137860672070435174}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4608367468215096
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1000096094811730}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4222688486545566}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137860672070435174
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1000096094811730}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4582902832403578}
  - {fileID: 4072691009248966}
  - {fileID: 4554326689474886}
  - {fileID: 4397762129535532}
  - {fileID: 4548107541833350}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4582902832403578}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1002123801030588
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4901293845650826}
  m_Layer: 0
  m_Name: Fur(Clone)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4901293845650826
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1002123801030588}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00042926587, y: 0.99288726, z: -0.117085494, w: -0.021579787}
  m_LocalPosition: {x: -6.8721004, y: 2.1762285, z: -4.206262}
  m_LocalScale: {x: 1, y: 1.0000002, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4649251345835370}
  - {fileID: 4694969504235254}
  m_Father: {fileID: 4977961836617372}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1006298079338386
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4846589109640900}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4846589109640900
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1006298079338386}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4248073578691954}
  m_Father: {fileID: 4945638797137676}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1006534002706470
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4174441436357838}
  - component: {fileID: 137468205526163876}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4174441436357838
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1006534002706470}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4406525262696272}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137468205526163876
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1006534002706470}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4430308352352364}
  - {fileID: 4918694255455694}
  - {fileID: 4117431168131788}
  - {fileID: 4424588116255364}
  - {fileID: 4848106962410418}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4430308352352364}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1009167790061642
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4690225216439162}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4690225216439162
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1009167790061642}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4533768059356236}
  m_Father: {fileID: 4890904808900904}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1011348884381998
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4848106962410418}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4848106962410418
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1011348884381998}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4424588116255364}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1012495340341626
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4304330772313052}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4304330772313052
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1012495340341626}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4072949263928410}
  m_Father: {fileID: 4865116496707714}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1015542096622054
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4234808820688060}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4234808820688060
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1015542096622054}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4479242454100818}
  m_Father: {fileID: 4533458234848752}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1016093284145738
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4718693015350470}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4718693015350470
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1016093284145738}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4130549224605982}
  m_Father: {fileID: 4167555383973238}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1018659034449420
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4524649745880076}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4524649745880076
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1018659034449420}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4144806673296094}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1019329922817570
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4421920654009908}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4421920654009908
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1019329922817570}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4938663688755766}
  m_Father: {fileID: 4157678545001122}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1021841227434360
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4190871456304996}
  m_Layer: 0
  m_Name: Fur(Clone)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4190871456304996
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1021841227434360}
  serializedVersion: 2
  m_LocalRotation: {x: 0.4282734, y: -0.49107388, z: -0.17671305, w: 0.7376997}
  m_LocalPosition: {x: -6.9499526, y: 1.9752922, z: -3.6872835}
  m_LocalScale: {x: 1, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4425983070017752}
  - {fileID: 4620902779414840}
  m_Father: {fileID: 4977961836617372}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1022693144423078
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4754619340078462}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4754619340078462
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1022693144423078}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4627399967623150}
  m_Father: {fileID: 4478986039485242}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1024087458198272
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4551806082897238}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4551806082897238
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1024087458198272}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4230287617088586}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1028496806512792
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4933250456257874}
  - component: {fileID: 137463728818885522}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4933250456257874
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1028496806512792}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4809769831409178}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137463728818885522
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1028496806512792}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4368120535833634}
  - {fileID: 4979244364572272}
  - {fileID: 4615692085858678}
  - {fileID: 4563072722727396}
  - {fileID: 4182293280401814}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4368120535833634}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1030385449311504
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4626570946792822}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4626570946792822
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1030385449311504}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4433307594048860}
  m_Father: {fileID: 4001657965531710}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1036484053869708
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4053712204785014}
  - component: {fileID: 137937352428882914}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4053712204785014
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1036484053869708}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4263517636708870}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137937352428882914
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1036484053869708}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4714875017111912}
  - {fileID: 4461644577713470}
  - {fileID: 4219978465878828}
  - {fileID: 4227246508873876}
  - {fileID: 4530335046546304}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4714875017111912}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1042990722908984
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4578751123297142}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4578751123297142
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1042990722908984}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4657346393586062}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1047674649599416
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4121012289966634}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4121012289966634
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1047674649599416}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4068294932989172}
  m_Father: {fileID: 4627399967623150}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1050077751553822
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4147375165687756}
  m_Layer: 0
  m_Name: Fur(Clone)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4147375165687756
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1050077751553822}
  serializedVersion: 2
  m_LocalRotation: {x: 0.5304047, y: 0.37720114, z: 0.36954007, w: 0.6631971}
  m_LocalPosition: {x: -6.9150085, y: 1.7720487, z: -3.598846}
  m_LocalScale: {x: 1.0000002, y: 1.0000002, z: 1.0000005}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4986817217752682}
  - {fileID: 4536971664846306}
  m_Father: {fileID: 4977961836617372}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1053344731479566
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4847772282989154}
  - component: {fileID: 137930842921234890}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4847772282989154
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1053344731479566}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4921846498385518}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137930842921234890
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1053344731479566}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4296962120943674}
  - {fileID: 4127068397799744}
  - {fileID: 4539323340940212}
  - {fileID: 4863179927707052}
  - {fileID: 4245450905810606}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4296962120943674}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1054954156271238
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4412283709732870}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4412283709732870
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1054954156271238}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4609204755482258}
  m_Father: {fileID: 4072949263928410}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1055217436563450
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4296962120943674}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4296962120943674
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1055217436563450}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4127068397799744}
  m_Father: {fileID: 4921846498385518}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1056273250564078
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4361404020662066}
  m_Layer: 0
  m_Name: Fur(Clone)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4361404020662066
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1056273250564078}
  serializedVersion: 2
  m_LocalRotation: {x: 0.3207434, y: 0.4410356, z: 0.2393744, w: 0.80331266}
  m_LocalPosition: {x: -6.920833, y: 2.029761, z: -3.7267704}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4496229664588006}
  - {fileID: 4149952384548004}
  m_Father: {fileID: 4977961836617372}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1057459104232932
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4072949263928410}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4072949263928410
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1057459104232932}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4412283709732870}
  m_Father: {fileID: 4304330772313052}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1057772854551170
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4798276353987694}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4798276353987694
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1057772854551170}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4897583848602156}
  m_Father: {fileID: 4824459056264336}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1061826500622982
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4861914948284396}
  - component: {fileID: 137829478661593876}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4861914948284396
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1061826500622982}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4667746719447994}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137829478661593876
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1061826500622982}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4363388827427754}
  - {fileID: 4910638746150252}
  - {fileID: 4419046540371290}
  - {fileID: 4597697637988894}
  - {fileID: 4913953666140160}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4363388827427754}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1066667981837174
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4714875017111912}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4714875017111912
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1066667981837174}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4461644577713470}
  m_Father: {fileID: 4263517636708870}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1066805410702770
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4916152883094722}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4916152883094722
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1066805410702770}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4026798870534402}
  m_Father: {fileID: 4649251345835370}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1067874492479454
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4392647646421458}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4392647646421458
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1067874492479454}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4161054935575720}
  m_Father: {fileID: 4888743764280720}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1072591438301742
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4430308352352364}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4430308352352364
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1072591438301742}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4918694255455694}
  m_Father: {fileID: 4406525262696272}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1077190629854654
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4515752805541442}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4515752805541442
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1077190629854654}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4657346393586062}
  m_Father: {fileID: 4533768059356236}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1079519675268562
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4802156807059892}
  m_Layer: 0
  m_Name: Fur(Clone)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4802156807059892
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1079519675268562}
  serializedVersion: 2
  m_LocalRotation: {x: 0.50052863, y: 0.26540476, z: 0.19087867, w: 0.8016213}
  m_LocalPosition: {x: -6.8901696, y: 1.902974, z: -3.6381056}
  m_LocalScale: {x: 1, y: 0.9999999, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4945638797137676}
  - {fileID: 4094941458665390}
  m_Father: {fileID: 4977961836617372}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1081135833250886
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4359196667285750}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4359196667285750
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1081135833250886}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4500105474886184}
  m_Father: {fileID: 4837246579761702}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1088246718368314
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4773637574037170}
  m_Layer: 0
  m_Name: Fur(Clone)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4773637574037170
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1088246718368314}
  serializedVersion: 2
  m_LocalRotation: {x: 0.034637425, y: 0.9886298, z: -0.07053885, w: 0.12820199}
  m_LocalPosition: {x: -6.826713, y: 2.1837616, z: -4.1552963}
  m_LocalScale: {x: 1.0000012, y: 0.9999999, z: 1.000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4587924813672038}
  - {fileID: 4499785479890192}
  m_Father: {fileID: 4977961836617372}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1088440525399350
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4696546182723534}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4696546182723534
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1088440525399350}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4086800233472960}
  m_Father: {fileID: 4357541566505000}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1090143261624604
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4824459056264336}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4824459056264336
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1090143261624604}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4798276353987694}
  m_Father: {fileID: 4752875721389680}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1092903366376208
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4397762129535532}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4397762129535532
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1092903366376208}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4548107541833350}
  m_Father: {fileID: 4554326689474886}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1098888773323366
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4224402311386574}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4224402311386574
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1098888773323366}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4006290093314506}
  m_Father: {fileID: 4984024956374414}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1101653676484378
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4540374804730384}
  - component: {fileID: 137033602072394978}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4540374804730384
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1101653676484378}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4242606925366644}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137033602072394978
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1101653676484378}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4052611642327718}
  - {fileID: 4313060887433174}
  - {fileID: 4918264362442128}
  - {fileID: 4230287617088586}
  - {fileID: 4551806082897238}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4052611642327718}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1102822269560986
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4997042813539546}
  - component: {fileID: 137011528497678828}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4997042813539546
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1102822269560986}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4537663300902504}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137011528497678828
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1102822269560986}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4613222307439402}
  - {fileID: 4388266255378952}
  - {fileID: 4937691019764640}
  - {fileID: 4847153430429860}
  - {fileID: 4910196598776148}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4613222307439402}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1104179865383212
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4627399967623150}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4627399967623150
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1104179865383212}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4121012289966634}
  m_Father: {fileID: 4754619340078462}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1105091347222266
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4394753740093694}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4394753740093694
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1105091347222266}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4922032548121034}
  m_Father: {fileID: 4200513286318596}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1115077602354512
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4272896805277716}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4272896805277716
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1115077602354512}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4449624579996280}
  m_Father: {fileID: 4703030257586666}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1118582850409638
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4191479781446082}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4191479781446082
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1118582850409638}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4790392879173234}
  m_Father: {fileID: 4248073578691954}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1118612261439494
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4638915420087780}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4638915420087780
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1118612261439494}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4196335805681500}
  m_Father: {fileID: 4960828013354232}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1127523772412688
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4001657965531710}
  m_Layer: 0
  m_Name: Fur(Clone)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4001657965531710
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1127523772412688}
  serializedVersion: 2
  m_LocalRotation: {x: 0.14829455, y: 0.8985627, z: -0.33800387, w: -0.23737557}
  m_LocalPosition: {x: -6.816982, y: 2.0536997, z: -4.4289193}
  m_LocalScale: {x: 1, y: 1, z: 0.9999998}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4626570946792822}
  - {fileID: 4135479439572462}
  m_Father: {fileID: 4977961836617372}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1128610023725454
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4078181946602220}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4078181946602220
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1128610023725454}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4471016430163470}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1135133788237852
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4263517636708870}
  m_Layer: 0
  m_Name: Fur(Clone)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4263517636708870
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1135133788237852}
  serializedVersion: 2
  m_LocalRotation: {x: 0.55963576, y: 0.31460518, z: 0.36578506, w: 0.67381954}
  m_LocalPosition: {x: -6.9404087, y: 1.7429467, z: -3.5978284}
  m_LocalScale: {x: 1, y: 1.0000005, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4714875017111912}
  - {fileID: 4053712204785014}
  m_Father: {fileID: 4977961836617372}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1144922046453286
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4947970512328624}
  - component: {fileID: 137618713720353836}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4947970512328624
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1144922046453286}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4105069489818272}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137618713720353836
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1144922046453286}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4556502358832764}
  - {fileID: 4200513286318596}
  - {fileID: 4394753740093694}
  - {fileID: 4922032548121034}
  - {fileID: 4412675744483826}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4556502358832764}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1147921876636826
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4533768059356236}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4533768059356236
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1147921876636826}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4515752805541442}
  m_Father: {fileID: 4690225216439162}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1149569498408320
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4261180413190798}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4261180413190798
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1149569498408320}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4834631175720770}
  m_Father: {fileID: 4317401655048226}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1150492824517212
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4920532339840920}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4920532339840920
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1150492824517212}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4959273635272118}
  m_Father: {fileID: 4834631175720770}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1152826646012478
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4539077765570672}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4539077765570672
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1152826646012478}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4736993046457010}
  m_Father: {fileID: 4183220221287532}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1154765395098692
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4232745432906730}
  - component: {fileID: 137449173046870686}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4232745432906730
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1154765395098692}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4546294073972830}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137449173046870686
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1154765395098692}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4085788322006672}
  - {fileID: 4254815655435546}
  - {fileID: 4553626595404402}
  - {fileID: 4063075524144764}
  - {fileID: 4755461458901046}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4085788322006672}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1155472876431072
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4613037116500886}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4613037116500886
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1155472876431072}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4300497339241628}
  m_Father: {fileID: 4725026542486276}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1155876246800054
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4638125490918254}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4638125490918254
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1155876246800054}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4715441191537784}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1157424336574626
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4135479439572462}
  - component: {fileID: 137524000604166996}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4135479439572462
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1157424336574626}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4001657965531710}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137524000604166996
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1157424336574626}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4626570946792822}
  - {fileID: 4433307594048860}
  - {fileID: 4903818840579240}
  - {fileID: 4696930072443680}
  - {fileID: 4987700972223346}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4626570946792822}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1159749857574182
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4724685065434272}
  - component: {fileID: 33878977074522398}
  - component: {fileID: 135808634254663820}
  - component: {fileID: 23424105128421776}
  m_Layer: 1
  m_Name: Sphere
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4724685065434272
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1159749857574182}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 1.98, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4328043985580676}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &33878977074522398
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1159749857574182}
  m_Mesh: {fileID: 10207, guid: 0000000000000000e000000000000000, type: 0}
--- !u!135 &135808634254663820
SphereCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1159749857574182}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Radius: 0.5
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &23424105128421776
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1159749857574182}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10303, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 1
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &1161004168938334
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4644933580379040}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4644933580379040
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1161004168938334}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4645445171498760}
  m_Father: {fileID: 4409716045811712}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1165222509732076
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4376072352231028}
  m_Layer: 0
  m_Name: Fur(Clone)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4376072352231028
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1165222509732076}
  serializedVersion: 2
  m_LocalRotation: {x: 0.39107877, y: -0.6061485, z: -0.42827916, w: 0.5442595}
  m_LocalPosition: {x: -6.8739567, y: 1.853572, z: -3.6175508}
  m_LocalScale: {x: 0.9999998, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4255209885820608}
  - {fileID: 4702188306494432}
  m_Father: {fileID: 4977961836617372}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1166569320103968
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4130549224605982}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4130549224605982
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1166569320103968}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4707362654427690}
  m_Father: {fileID: 4718693015350470}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1167990301851400
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4556502358832764}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4556502358832764
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1167990301851400}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4200513286318596}
  m_Father: {fileID: 4105069489818272}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1169719828853698
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4848895636298698}
  m_Layer: 0
  m_Name: Fur(Clone)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4848895636298698
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1169719828853698}
  serializedVersion: 2
  m_LocalRotation: {x: 0.08837896, y: 0.4336177, z: 0.017440312, w: 0.8965828}
  m_LocalPosition: {x: -6.847314, y: 2.1818225, z: -4.0031986}
  m_LocalScale: {x: 1.0000002, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4567394065405726}
  - {fileID: 4799744229588196}
  m_Father: {fileID: 4977961836617372}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1172121422633380
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4026798870534402}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4026798870534402
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1172121422633380}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4974044196236162}
  m_Father: {fileID: 4916152883094722}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1176898216045732
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4250435098304044}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4250435098304044
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1176898216045732}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4096698606900844}
  m_Father: {fileID: 4137936656315706}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1179185117388746
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4674067540964586}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4674067540964586
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1179185117388746}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4665251582377956}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1190433751944880
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4086800233472960}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4086800233472960
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1190433751944880}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4991779370478380}
  m_Father: {fileID: 4696546182723534}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1191685442256250
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4677527877880628}
  m_Layer: 0
  m_Name: Fur(Clone)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4677527877880628
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1191685442256250}
  serializedVersion: 2
  m_LocalRotation: {x: 0.109232016, y: -0.9138982, z: -0.15606375, w: 0.3584726}
  m_LocalPosition: {x: -6.9138823, y: 2.1536498, z: -3.9082172}
  m_LocalScale: {x: 1.0000005, y: 1.0000002, z: 1.0000007}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4984024956374414}
  - {fileID: 4601518289705304}
  m_Father: {fileID: 4977961836617372}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1198802723363956
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4480655159773972}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4480655159773972
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1198802723363956}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4277132183547240}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1200621657721620
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4918264362442128}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4918264362442128
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1200621657721620}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4230287617088586}
  m_Father: {fileID: 4313060887433174}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1200852785659022
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4945638797137676}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4945638797137676
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1200852785659022}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4846589109640900}
  m_Father: {fileID: 4802156807059892}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1204585030498496
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4031826676103842}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4031826676103842
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1204585030498496}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4868606785142852}
  m_Father: {fileID: 4195136295709406}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1206480247025380
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4639739661995594}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4639739661995594
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1206480247025380}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4533458234848752}
  m_Father: {fileID: 4425983070017752}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1208104022658400
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4769110457820716}
  m_Layer: 0
  m_Name: Fur(Clone)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4769110457820716
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1208104022658400}
  serializedVersion: 2
  m_LocalRotation: {x: 0.20755598, y: 0.75641525, z: -0.15881714, w: -0.5996113}
  m_LocalPosition: {x: -6.80823, y: 2.1216354, z: -4.3345847}
  m_LocalScale: {x: 1.0000002, y: 1, z: 1.0000005}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4759809719001408}
  - {fileID: 4249090932954450}
  m_Father: {fileID: 4977961836617372}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1208448400096536
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4189374601071402}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4189374601071402
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1208448400096536}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4665251582377956}
  m_Father: {fileID: 4936066426117902}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1211946800016950
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4395400403226508}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4395400403226508
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1211946800016950}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4935704356867394}
  m_Father: {fileID: 4176602214343208}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1213044584428028
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4979244364572272}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4979244364572272
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1213044584428028}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4615692085858678}
  m_Father: {fileID: 4368120535833634}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1217768771835112
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4373823035342830}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4373823035342830
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1217768771835112}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4936066426117902}
  m_Father: {fileID: 4993651339134116}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1218800795568740
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4869433572613942}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4869433572613942
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1218800795568740}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4341806294985162}
  m_Father: {fileID: 4189245634933282}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1220460163592276
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4319768701508206}
  m_Layer: 0
  m_Name: Fur(Clone)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4319768701508206
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1220460163592276}
  serializedVersion: 2
  m_LocalRotation: {x: 0.000105090985, y: 0.88930535, z: 0.31127113, w: 0.33503172}
  m_LocalPosition: {x: -6.974192, y: 2.093048, z: -3.8131497}
  m_LocalScale: {x: 0.9999999, y: 0.9999999, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4603867063206794}
  - {fileID: 4927961426599292}
  m_Father: {fileID: 4977961836617372}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1221352809661982
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4069893404338898}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4069893404338898
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1221352809661982}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4688728242671482}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1224081567004088
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4367738376407622}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4367738376407622
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1224081567004088}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4317025439145908}
  m_Father: {fileID: 4203254112477852}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1228666642245464
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4342062008842472}
  - component: {fileID: 137060681219453230}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4342062008842472
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1228666642245464}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4599697830482060}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137060681219453230
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1228666642245464}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4503420414512394}
  - {fileID: 4392037079466006}
  - {fileID: 4703030257586666}
  - {fileID: 4272896805277716}
  - {fileID: 4449624579996280}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4503420414512394}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1233806613557784
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4913746822608142}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4913746822608142
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1233806613557784}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4959273635272118}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1242291277981746
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4921846498385518}
  m_Layer: 0
  m_Name: Fur(Clone)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4921846498385518
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1242291277981746}
  serializedVersion: 2
  m_LocalRotation: {x: 0.2235628, y: 0.6547911, z: 0.18733536, w: 0.6972616}
  m_LocalPosition: {x: -6.8542347, y: 2.1048627, z: -3.8114529}
  m_LocalScale: {x: 0.9999998, y: 0.9999998, z: 0.99999964}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4296962120943674}
  - {fileID: 4847772282989154}
  m_Father: {fileID: 4977961836617372}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1254569606819040
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4614034137141938}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4614034137141938
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1254569606819040}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4983265159176056}
  m_Father: {fileID: 4300497339241628}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1258733093403084
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4424588116255364}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4424588116255364
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1258733093403084}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4848106962410418}
  m_Father: {fileID: 4117431168131788}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1259943482778606
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4623017153353812}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4623017153353812
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1259943482778606}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4500105474886184}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1260581435930770
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4541661596719794}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4541661596719794
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1260581435930770}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4145468111388790}
  m_Father: {fileID: 4989920662108158}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1260656152789066
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4341806294985162}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4341806294985162
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1260656152789066}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4167610386639948}
  m_Father: {fileID: 4869433572613942}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1262863999740998
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4176602214343208}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4176602214343208
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1262863999740998}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4395400403226508}
  m_Father: {fileID: 4864227740807002}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1264225168206068
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4912527850978564}
  - component: {fileID: 137414285957677596}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4912527850978564
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1264225168206068}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4752875721389680}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137414285957677596
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1264225168206068}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4824459056264336}
  - {fileID: 4798276353987694}
  - {fileID: 4897583848602156}
  - {fileID: 4856218018701080}
  - {fileID: 4550657258160008}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4824459056264336}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1265060336631442
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4392071538200726}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4392071538200726
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1265060336631442}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4561738251446910}
  m_Father: {fileID: 4444047193085062}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1266974430059840
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4714836751408646}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4714836751408646
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1266974430059840}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4220394680458434}
  m_Father: {fileID: 4851493309731908}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1272192207663468
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4759809719001408}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4759809719001408
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1272192207663468}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4023791195339470}
  m_Father: {fileID: 4769110457820716}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1272896682011614
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4471016430163470}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4471016430163470
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1272896682011614}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4078181946602220}
  m_Father: {fileID: 4584989041995992}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1273920103954238
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4938663688755766}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4938663688755766
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1273920103954238}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4421920654009908}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1274459471661842
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4936955753479582}
  m_Layer: 0
  m_Name: Fur(Clone)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4936955753479582
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1274459471661842}
  serializedVersion: 2
  m_LocalRotation: {x: 0.014615847, y: -0.99528766, z: -0.032143965, w: -0.09030903}
  m_LocalPosition: {x: -6.8874497, y: 2.1886907, z: -4.0593276}
  m_LocalScale: {x: 1.0000011, y: 0.99999976, z: 1.000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4243965163282450}
  - {fileID: 4428464269997570}
  m_Father: {fileID: 4977961836617372}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1275064550060786
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4888743764280720}
  m_Layer: 0
  m_Name: Fur(Clone)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4888743764280720
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1275064550060786}
  serializedVersion: 2
  m_LocalRotation: {x: 0.03053418, y: 0.9726024, z: 0.08695057, w: 0.21342872}
  m_LocalPosition: {x: -6.85886, y: 2.1814451, z: -3.998915}
  m_LocalScale: {x: 0.9999999, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4392647646421458}
  - {fileID: 4581389076948222}
  m_Father: {fileID: 4977961836617372}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1279210513610970
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4702188306494432}
  - component: {fileID: 137911839658828590}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4702188306494432
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1279210513610970}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4376072352231028}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137911839658828590
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1279210513610970}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4255209885820608}
  - {fileID: 4386590654129050}
  - {fileID: 4892596039920884}
  - {fileID: 4890763884176514}
  - {fileID: 4605556085403904}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4255209885820608}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1281020679429810
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4601518289705304}
  - component: {fileID: 137493572379297940}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4601518289705304
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1281020679429810}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4677527877880628}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137493572379297940
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1281020679429810}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4984024956374414}
  - {fileID: 4224402311386574}
  - {fileID: 4006290093314506}
  - {fileID: 4620077261551636}
  - {fileID: 4367942849137652}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4984024956374414}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1289040916930070
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4313060887433174}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4313060887433174
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1289040916930070}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4918264362442128}
  m_Father: {fileID: 4052611642327718}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1290692435346536
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4093435145443704}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4093435145443704
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1290692435346536}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4620250118782988}
  m_Father: {fileID: 4582628293445100}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1292623087536656
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4433432817394978}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4433432817394978
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1292623087536656}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4442391927560176}
  m_Father: {fileID: 4334088815347686}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1296870373609468
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4962244202733878}
  m_Layer: 0
  m_Name: Fur(Clone)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4962244202733878
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1296870373609468}
  serializedVersion: 2
  m_LocalRotation: {x: 0.257508, y: -0.724363, z: -0.19059093, w: 0.6104613}
  m_LocalPosition: {x: -6.940181, y: 2.0873022, z: -3.7947445}
  m_LocalScale: {x: 1.0000002, y: 1.0000002, z: 1.0000004}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4334088815347686}
  - {fileID: 4005591801546938}
  m_Father: {fileID: 4977961836617372}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1297163751333860
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4903566107057864}
  - component: {fileID: 137981538101332012}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4903566107057864
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1297163751333860}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4724658083788706}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137981538101332012
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1297163751333860}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4487000767925862}
  - {fileID: 4691286733771098}
  - {fileID: 4883814444037064}
  - {fileID: 4566568186652270}
  - {fileID: 4701125099160858}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4487000767925862}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1297400700650758
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4986817217752682}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4986817217752682
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1297400700650758}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4238663227045868}
  m_Father: {fileID: 4147375165687756}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1301368080168272
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4032702940973426}
  - component: {fileID: 137817681636139676}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4032702940973426
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1301368080168272}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4409716045811712}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137817681636139676
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1301368080168272}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4644933580379040}
  - {fileID: 4645445171498760}
  - {fileID: 4710079006732550}
  - {fileID: 4277132183547240}
  - {fileID: 4480655159773972}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4644933580379040}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1302374732840760
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4353885735641544}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4353885735641544
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1302374732840760}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4040016471392240}
  m_Father: {fileID: 4355073563388744}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1306779170326072
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4167555383973238}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4167555383973238
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1306779170326072}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4718693015350470}
  m_Father: {fileID: 4587924813672038}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1308380186039368
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4243965163282450}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4243965163282450
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1308380186039368}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4948951483051014}
  m_Father: {fileID: 4936955753479582}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1310654240758670
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4974044196236162}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4974044196236162
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1310654240758670}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4902787726143872}
  m_Father: {fileID: 4026798870534402}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1313361218018502
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4085788322006672}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4085788322006672
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1313361218018502}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4254815655435546}
  m_Father: {fileID: 4546294073972830}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1313456280769370
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4963064634980566}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4963064634980566
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1313456280769370}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4494256637927940}
  m_Father: {fileID: 4220394680458434}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1313695233424302
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4804192041069872}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4804192041069872
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1313695233424302}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4963768608483880}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1315229319113064
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4834631175720770}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4834631175720770
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1315229319113064}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4920532339840920}
  m_Father: {fileID: 4261180413190798}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1316134822196406
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4667746719447994}
  m_Layer: 0
  m_Name: Fur(Clone)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4667746719447994
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1316134822196406}
  serializedVersion: 2
  m_LocalRotation: {x: -0.20826852, y: 0.66138774, z: -0.2741613, w: 0.6663528}
  m_LocalPosition: {x: -6.825058, y: 2.0713975, z: -4.4101076}
  m_LocalScale: {x: 1.0000001, y: 0.9999999, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4363388827427754}
  - {fileID: 4861914948284396}
  m_Father: {fileID: 4977961836617372}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1318119869159246
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4096698606900844}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4096698606900844
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1318119869159246}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4748562607798066}
  m_Father: {fileID: 4250435098304044}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1319256440185796
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4694969504235254}
  - component: {fileID: 137450357040954684}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4694969504235254
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1319256440185796}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4901293845650826}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137450357040954684
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1319256440185796}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4649251345835370}
  - {fileID: 4916152883094722}
  - {fileID: 4026798870534402}
  - {fileID: 4974044196236162}
  - {fileID: 4902787726143872}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4649251345835370}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1320484747908336
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4548107541833350}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4548107541833350
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1320484747908336}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4397762129535532}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1321094501986506
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4214952577954650}
  m_Layer: 0
  m_Name: Fur(Clone)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4214952577954650
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1321094501986506}
  serializedVersion: 2
  m_LocalRotation: {x: -0.10873462, y: 0.83316654, z: -0.13234612, w: 0.5258278}
  m_LocalPosition: {x: -6.8910027, y: 2.1605985, z: -4.257442}
  m_LocalScale: {x: 1.0000004, y: 1, z: 1.0000007}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4189245634933282}
  - {fileID: 4022143604929592}
  m_Father: {fileID: 4977961836617372}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1326005762534598
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4936066426117902}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4936066426117902
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1326005762534598}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4189374601071402}
  m_Father: {fileID: 4373823035342830}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1327565108310266
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4219978465878828}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4219978465878828
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1327565108310266}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4227246508873876}
  m_Father: {fileID: 4461644577713470}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1332665919565886
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4986021569262276}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4986021569262276
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1332665919565886}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4715441191537784}
  m_Father: {fileID: 4655043311984714}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1337278595105850
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4188299226200268}
  m_Layer: 0
  m_Name: Fur(Clone)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4188299226200268
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1337278595105850}
  serializedVersion: 2
  m_LocalRotation: {x: 0.11202253, y: 0.82049423, z: 0.23003575, w: 0.5111984}
  m_LocalPosition: {x: -6.89568, y: 2.1244726, z: -3.843991}
  m_LocalScale: {x: 0.9999998, y: 1, z: 0.9999997}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4725026542486276}
  - {fileID: 4353248957707084}
  m_Father: {fileID: 4977961836617372}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1337933919074684
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4563072722727396}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4563072722727396
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1337933919074684}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4182293280401814}
  m_Father: {fileID: 4615692085858678}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1351694740013034
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4006290093314506}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4006290093314506
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1351694740013034}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4620077261551636}
  m_Father: {fileID: 4224402311386574}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1353646663051816
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4134671816585310}
  m_Layer: 0
  m_Name: Fur(Clone)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4134671816585310
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1353646663051816}
  serializedVersion: 2
  m_LocalRotation: {x: 0.40186724, y: 0.5301848, z: 0.42410046, w: 0.6144474}
  m_LocalPosition: {x: -6.9175234, y: 1.848579, z: -3.6182222}
  m_LocalScale: {x: 1.0000002, y: 1.0000002, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4574519651251692}
  - {fileID: 4930714792307448}
  m_Father: {fileID: 4977961836617372}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1354293367682770
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4984024956374414}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4984024956374414
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1354293367682770}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4224402311386574}
  m_Father: {fileID: 4677527877880628}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1358040304400368
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4620077261551636}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4620077261551636
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1358040304400368}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4367942849137652}
  m_Father: {fileID: 4006290093314506}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1360475220836188
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4388266255378952}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4388266255378952
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1360475220836188}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4937691019764640}
  m_Father: {fileID: 4613222307439402}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1364762279164198
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4918694255455694}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4918694255455694
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1364762279164198}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4117431168131788}
  m_Father: {fileID: 4430308352352364}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1367034573771440
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4238663227045868}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4238663227045868
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1367034573771440}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4157678545001122}
  m_Father: {fileID: 4986817217752682}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1369147206763766
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4500105474886184}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4500105474886184
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1369147206763766}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4623017153353812}
  m_Father: {fileID: 4359196667285750}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1374136964263582
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4993651339134116}
  m_Layer: 0
  m_Name: Fur(Clone)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4993651339134116
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1374136964263582}
  serializedVersion: 2
  m_LocalRotation: {x: -0.42446023, y: -0.029503603, z: -0.061038498, w: 0.902905}
  m_LocalPosition: {x: -6.802365, y: 2.0060458, z: -4.471447}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4373823035342830}
  - {fileID: 4808603457345964}
  m_Father: {fileID: 4977961836617372}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1374928831256736
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4025098631932344}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4025098631932344
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1374928831256736}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4134525503075568}
  m_Father: {fileID: 4991779370478380}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1377244995147078
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4892596039920884}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4892596039920884
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1377244995147078}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4890763884176514}
  m_Father: {fileID: 4386590654129050}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1382571693881220
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4461644577713470}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4461644577713470
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1382571693881220}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4219978465878828}
  m_Father: {fileID: 4714875017111912}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1391907383525678
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4254815655435546}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4254815655435546
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1391907383525678}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4553626595404402}
  m_Father: {fileID: 4085788322006672}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1396037867531274
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4317025439145908}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4317025439145908
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1396037867531274}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4367738376407622}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1402136896408532
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4096408345934446}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4096408345934446
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1402136896408532}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4864227740807002}
  m_Father: {fileID: 4065542281751594}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1409873583813594
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4597697637988894}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4597697637988894
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1409873583813594}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4913953666140160}
  m_Father: {fileID: 4419046540371290}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1412804759444438
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4903818840579240}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4903818840579240
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1412804759444438}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4696930072443680}
  m_Father: {fileID: 4433307594048860}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1416401178687250
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4847153430429860}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4847153430429860
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1416401178687250}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4910196598776148}
  m_Father: {fileID: 4937691019764640}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1417937999778552
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4357541566505000}
  m_Layer: 0
  m_Name: Fur(Clone)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4357541566505000
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1417937999778552}
  serializedVersion: 2
  m_LocalRotation: {x: -0.29611704, y: 0.031544153, z: -0.013286961, w: 0.9545382}
  m_LocalPosition: {x: -6.8666577, y: 2.1020756, z: -4.373074}
  m_LocalScale: {x: 0.9999999, y: 1.0000002, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4696546182723534}
  - {fileID: 4377009971401804}
  m_Father: {fileID: 4977961836617372}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1430979412114776
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4442391927560176}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4442391927560176
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1430979412114776}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4101809373755890}
  m_Father: {fileID: 4433432817394978}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1431754938748000
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4496229664588006}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4496229664588006
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1431754938748000}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4655043311984714}
  m_Father: {fileID: 4361404020662066}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1432125679411196
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4609204755482258}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4609204755482258
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1432125679411196}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4566847082062092}
  m_Father: {fileID: 4412283709732870}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1433238897122590
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4471762172044324}
  - component: {fileID: 137537463716305766}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4471762172044324
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1433238897122590}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4865116496707714}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137537463716305766
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1433238897122590}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4304330772313052}
  - {fileID: 4072949263928410}
  - {fileID: 4412283709732870}
  - {fileID: 4609204755482258}
  - {fileID: 4566847082062092}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4304330772313052}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1437915954952750
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4277132183547240}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4277132183547240
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1437915954952750}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4480655159773972}
  m_Father: {fileID: 4710079006732550}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1440128894170010
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4808603457345964}
  - component: {fileID: 137176691865546042}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4808603457345964
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1440128894170010}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4993651339134116}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137176691865546042
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1440128894170010}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4373823035342830}
  - {fileID: 4936066426117902}
  - {fileID: 4189374601071402}
  - {fileID: 4665251582377956}
  - {fileID: 4674067540964586}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4373823035342830}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1440145634689568
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4022143604929592}
  - component: {fileID: 137877716191670550}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4022143604929592
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1440145634689568}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4214952577954650}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137877716191670550
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1440145634689568}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4189245634933282}
  - {fileID: 4869433572613942}
  - {fileID: 4341806294985162}
  - {fileID: 4167610386639948}
  - {fileID: 4587345039050988}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4189245634933282}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1446722874493004
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4799744229588196}
  - component: {fileID: 137319694894355222}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4799744229588196
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1446722874493004}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4848895636298698}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137319694894355222
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1446722874493004}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4567394065405726}
  - {fileID: 4503963407063252}
  - {fileID: 4203254112477852}
  - {fileID: 4367738376407622}
  - {fileID: 4317025439145908}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4567394065405726}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1455400755346556
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4620902779414840}
  - component: {fileID: 137833114668034534}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4620902779414840
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1455400755346556}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4190871456304996}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137833114668034534
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1455400755346556}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4425983070017752}
  - {fileID: 4639739661995594}
  - {fileID: 4533458234848752}
  - {fileID: 4234808820688060}
  - {fileID: 4479242454100818}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4425983070017752}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1456247783823396
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4094941458665390}
  - component: {fileID: 137744908032932258}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4094941458665390
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1456247783823396}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4802156807059892}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137744908032932258
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1456247783823396}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4945638797137676}
  - {fileID: 4846589109640900}
  - {fileID: 4248073578691954}
  - {fileID: 4191479781446082}
  - {fileID: 4790392879173234}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4945638797137676}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1458180509162134
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4255209885820608}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4255209885820608
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1458180509162134}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4386590654129050}
  m_Father: {fileID: 4376072352231028}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1460081892506030
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4834389742527184}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4834389742527184
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1460081892506030}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4893567241062206}
  m_Father: {fileID: 4343013350933866}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1462009158599434
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4145468111388790}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4145468111388790
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1462009158599434}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4640521537347706}
  m_Father: {fileID: 4541661596719794}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1462681851792074
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4328043985580676}
  - component: {fileID: 114062618198861446}
  - component: {fileID: 114684649229453740}
  - component: {fileID: 114538101282083008}
  m_Layer: 0
  m_Name: PR_FTail_Mohawk
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4328043985580676
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1462681851792074}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4724685065434272}
  - {fileID: 4977961836617372}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &114062618198861446
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1462681851792074}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc71bf79a039ae24894ef08c2171fb45, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  VibrationRate: 6
  BaseRange: 19
  SmoothTranslation: 0.5
  AxesMultiplier: {x: 1, y: 1, z: 1}
  ChangeObjectPosition: 1
--- !u!114 &114684649229453740
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1462681851792074}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ebc354317bbc5dd40b24b190775f892a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  RotationAxis: {x: 0, y: 1, z: 0}
  RotationRange: 180
  SinSpeed: 3
--- !u!114 &114538101282083008
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1462681851792074}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ebc354317bbc5dd40b24b190775f892a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  RotationAxis: {x: 0.3, y: 0, z: 0.25}
  RotationRange: 180
  SinSpeed: 4
--- !u!1 &1463367706880922
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4820038678222118}
  - component: {fileID: 137297985557074888}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4820038678222118
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1463367706880922}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4431445969422820}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137297985557074888
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1463367706880922}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4221795971942832}
  - {fileID: 4639868989751984}
  - {fileID: 4995391476415840}
  - {fileID: 4144806673296094}
  - {fileID: 4524649745880076}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4221795971942832}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1466069687530650
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4023791195339470}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4023791195339470
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1466069687530650}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4584989041995992}
  m_Father: {fileID: 4759809719001408}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1466919847189876
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4433307594048860}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4433307594048860
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1466919847189876}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4903818840579240}
  m_Father: {fileID: 4626570946792822}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1469893452532684
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4052611642327718}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4052611642327718
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1469893452532684}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4313060887433174}
  m_Father: {fileID: 4242606925366644}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1473862186135340
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4144806673296094}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4144806673296094
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1473862186135340}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4524649745880076}
  m_Father: {fileID: 4995391476415840}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1474390904159414
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4724658083788706}
  m_Layer: 0
  m_Name: Fur(Clone)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4724658083788706
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1474390904159414}
  serializedVersion: 2
  m_LocalRotation: {x: 0.09583813, y: 0.47056216, z: 0.06045928, w: 0.87506056}
  m_LocalPosition: {x: -6.8778076, y: 2.1770973, z: -3.9776862}
  m_LocalScale: {x: 0.99999976, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4487000767925862}
  - {fileID: 4903566107057864}
  m_Father: {fileID: 4977961836617372}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1474938672268822
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4960828013354232}
  m_Layer: 0
  m_Name: Fur(Clone)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4960828013354232
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1474938672268822}
  serializedVersion: 2
  m_LocalRotation: {x: -0.24980475, y: 0.36638048, z: -0.18571912, w: 0.8768531}
  m_LocalPosition: {x: -6.798675, y: 2.0930438, z: -4.377086}
  m_LocalScale: {x: 1, y: 1.0000002, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4638915420087780}
  - {fileID: 4450629636940304}
  m_Father: {fileID: 4977961836617372}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1477422358455312
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4499785479890192}
  - component: {fileID: 137342737339958752}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4499785479890192
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1477422358455312}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4773637574037170}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137342737339958752
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1477422358455312}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4587924813672038}
  - {fileID: 4167555383973238}
  - {fileID: 4718693015350470}
  - {fileID: 4130549224605982}
  - {fileID: 4707362654427690}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4587924813672038}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1479429187634984
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4248073578691954}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4248073578691954
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1479429187634984}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4191479781446082}
  m_Father: {fileID: 4846589109640900}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1488182655562072
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4050510668770460}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4050510668770460
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1488182655562072}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4083148178963320}
  m_Father: {fileID: 4196335805681500}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1488308306981904
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4657346393586062}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4657346393586062
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1488308306981904}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4578751123297142}
  m_Father: {fileID: 4515752805541442}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1488966745566536
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4116735580823194}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4116735580823194
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1488966745566536}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4086513863479698}
  m_Father: {fileID: 4574519651251692}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1489958592861080
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4185913783158184}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4185913783158184
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1489958592861080}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4989920662108158}
  m_Father: {fileID: 4597466805297008}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1492045638338904
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4553626595404402}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4553626595404402
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1492045638338904}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4063075524144764}
  m_Father: {fileID: 4254815655435546}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1493995476094622
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4546294073972830}
  m_Layer: 0
  m_Name: Fur(Clone)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4546294073972830
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1493995476094622}
  serializedVersion: 2
  m_LocalRotation: {x: 0.44614404, y: -0.52620536, z: -0.19736475, w: 0.6964988}
  m_LocalPosition: {x: -6.967299, y: 1.9519403, z: -3.675407}
  m_LocalScale: {x: 1, y: 1, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4085788322006672}
  - {fileID: 4232745432906730}
  m_Father: {fileID: 4977961836617372}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1494931473941620
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4959273635272118}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4959273635272118
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1494931473941620}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4913746822608142}
  m_Father: {fileID: 4920532339840920}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1500331829232630
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4688728242671482}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4688728242671482
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1500331829232630}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4069893404338898}
  m_Father: {fileID: 4086513863479698}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1507558072263782
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4786831487071688}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4786831487071688
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1507558072263782}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4963768608483880}
  m_Father: {fileID: 4620250118782988}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1509507770350320
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4927961426599292}
  - component: {fileID: 137242724687524656}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4927961426599292
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1509507770350320}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4319768701508206}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137242724687524656
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1509507770350320}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4603867063206794}
  - {fileID: 4612412811047328}
  - {fileID: 4444047193085062}
  - {fileID: 4392071538200726}
  - {fileID: 4561738251446910}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4603867063206794}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1515890589853514
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4342661123435952}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4342661123435952
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1515890589853514}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4710580022078200}
  m_Father: {fileID: 4748562607798066}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1527930341875176
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4149952384548004}
  - component: {fileID: 137741603772133136}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4149952384548004
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1527930341875176}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4361404020662066}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137741603772133136
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1527930341875176}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4496229664588006}
  - {fileID: 4655043311984714}
  - {fileID: 4986021569262276}
  - {fileID: 4715441191537784}
  - {fileID: 4638125490918254}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4496229664588006}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1531672667200130
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4478986039485242}
  m_Layer: 0
  m_Name: Fur(Clone)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4478986039485242
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1531672667200130}
  serializedVersion: 2
  m_LocalRotation: {x: 0.035686743, y: 0.91201884, z: 0.3484145, w: 0.21343729}
  m_LocalPosition: {x: -6.9118176, y: 2.0672715, z: -3.7646227}
  m_LocalScale: {x: 1.0000002, y: 1, z: 1.0000004}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4754619340078462}
  - {fileID: 4434867743158798}
  m_Father: {fileID: 4977961836617372}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1532218222123488
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4117431168131788}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4117431168131788
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1532218222123488}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4424588116255364}
  m_Father: {fileID: 4918694255455694}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1533066497327004
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4809469808739632}
  m_Layer: 0
  m_Name: Fur(Clone)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4809469808739632
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1533066497327004}
  serializedVersion: 2
  m_LocalRotation: {x: 0.18088378, y: -0.72739077, z: -0.10201625, w: 0.6540462}
  m_LocalPosition: {x: -6.9348497, y: 2.1468112, z: -3.897488}
  m_LocalScale: {x: 0.9999997, y: 0.99999994, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4851493309731908}
  - {fileID: 4541600953103936}
  m_Father: {fileID: 4977961836617372}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1536632186222482
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4639868989751984}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4639868989751984
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1536632186222482}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4995391476415840}
  m_Father: {fileID: 4221795971942832}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1545028043916460
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4195136295709406}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4195136295709406
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1545028043916460}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4031826676103842}
  m_Father: {fileID: 4290934278003306}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1547997639967202
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4185824689734182}
  - component: {fileID: 137274647536422274}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4185824689734182
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1547997639967202}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4601683942242640}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137274647536422274
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1547997639967202}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4247191628394108}
  - {fileID: 4837246579761702}
  - {fileID: 4359196667285750}
  - {fileID: 4500105474886184}
  - {fileID: 4623017153353812}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4247191628394108}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1558953497384000
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4487000767925862}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4487000767925862
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1558953497384000}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4691286733771098}
  m_Father: {fileID: 4724658083788706}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1562906611559594
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4431445969422820}
  m_Layer: 0
  m_Name: Fur(Clone)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4431445969422820
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1562906611559594}
  serializedVersion: 2
  m_LocalRotation: {x: 0.30972174, y: 0.6708877, z: 0.34659916, w: 0.5777986}
  m_LocalPosition: {x: -6.862476, y: 1.9738793, z: -3.678514}
  m_LocalScale: {x: 1.0000005, y: 1.0000002, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4221795971942832}
  - {fileID: 4820038678222118}
  m_Father: {fileID: 4977961836617372}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1564074533878370
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4137936656315706}
  m_Layer: 0
  m_Name: Fur(Clone)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4137936656315706
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1564074533878370}
  serializedVersion: 2
  m_LocalRotation: {x: -0.09783349, y: 0.16711225, z: -0.050239865, w: 0.9797847}
  m_LocalPosition: {x: -6.837125, y: 2.1778421, z: -4.1942515}
  m_LocalScale: {x: 1, y: 0.9999999, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4250435098304044}
  - {fileID: 4501361830274588}
  m_Father: {fileID: 4977961836617372}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1564626544906892
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4537663300902504}
  m_Layer: 0
  m_Name: Fur(Clone)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4537663300902504
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1564626544906892}
  serializedVersion: 2
  m_LocalRotation: {x: 0.17071743, y: -0.3911023, z: -0.005355734, w: 0.9043594}
  m_LocalPosition: {x: -6.9319243, y: 2.1607647, z: -3.9335155}
  m_LocalScale: {x: 1.0000001, y: 0.9999998, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4613222307439402}
  - {fileID: 4997042813539546}
  m_Father: {fileID: 4977961836617372}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1566432155164284
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4494256637927940}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4494256637927940
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1566432155164284}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4963064634980566}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1569951772202834
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4131767099609148}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4131767099609148
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1569951772202834}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4068294932989172}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1572771745783250
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4865116496707714}
  m_Layer: 0
  m_Name: Fur(Clone)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4865116496707714
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1572771745783250}
  serializedVersion: 2
  m_LocalRotation: {x: 0.15590791, y: -0.75811857, z: -0.62613887, w: 0.094335236}
  m_LocalPosition: {x: -6.9291296, y: 1.7735804, z: -3.600605}
  m_LocalScale: {x: 1.0000005, y: 0.9999999, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4304330772313052}
  - {fileID: 4471762172044324}
  m_Father: {fileID: 4977961836617372}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1573429247678564
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4550657258160008}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4550657258160008
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1573429247678564}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4856218018701080}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1574742531439518
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4864227740807002}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4864227740807002
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1574742531439518}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4176602214343208}
  m_Father: {fileID: 4096408345934446}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1577454588577946
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4536971664846306}
  - component: {fileID: 137695352246107848}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4536971664846306
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1577454588577946}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4147375165687756}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137695352246107848
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1577454588577946}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4986817217752682}
  - {fileID: 4238663227045868}
  - {fileID: 4157678545001122}
  - {fileID: 4421920654009908}
  - {fileID: 4938663688755766}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4986817217752682}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1579412173800606
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4934980662918152}
  - component: {fileID: 137109440963636098}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4934980662918152
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1579412173800606}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4597466805297008}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137109440963636098
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1579412173800606}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4185913783158184}
  - {fileID: 4989920662108158}
  - {fileID: 4541661596719794}
  - {fileID: 4145468111388790}
  - {fileID: 4640521537347706}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4185913783158184}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1582947804649198
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4392037079466006}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4392037079466006
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1582947804649198}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4703030257586666}
  m_Father: {fileID: 4503420414512394}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1584090335151952
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4221795971942832}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4221795971942832
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1584090335151952}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4639868989751984}
  m_Father: {fileID: 4431445969422820}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1584137096862178
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4856218018701080}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4856218018701080
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1584137096862178}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4550657258160008}
  m_Father: {fileID: 4897583848602156}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1587587419216150
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4225472753494694}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4225472753494694
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1587587419216150}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4101809373755890}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1589009509219032
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4701125099160858}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4701125099160858
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1589009509219032}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4566568186652270}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1593207078194430
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4963768608483880}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4963768608483880
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1593207078194430}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4804192041069872}
  m_Father: {fileID: 4786831487071688}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1595565532582362
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4101809373755890}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4101809373755890
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1595565532582362}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4225472753494694}
  m_Father: {fileID: 4442391927560176}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1598832349463622
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4640521537347706}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4640521537347706
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1598832349463622}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4145468111388790}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1601249673966900
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4566568186652270}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4566568186652270
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1601249673966900}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4701125099160858}
  m_Father: {fileID: 4883814444037064}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1602720473275484
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4157678545001122}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4157678545001122
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1602720473275484}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4421920654009908}
  m_Father: {fileID: 4238663227045868}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1604637899545776
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4922032548121034}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4922032548121034
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1604637899545776}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4412675744483826}
  m_Father: {fileID: 4394753740093694}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1607812227206984
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4412675744483826}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4412675744483826
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1607812227206984}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4922032548121034}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1617655417440644
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4554326689474886}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4554326689474886
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1617655417440644}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4397762129535532}
  m_Father: {fileID: 4072691009248966}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1625795313244684
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4530335046546304}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4530335046546304
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1625795313244684}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4227246508873876}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1630052231271430
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4566847082062092}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4566847082062092
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1630052231271430}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4609204755482258}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1633679539365730
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4890904808900904}
  m_Layer: 0
  m_Name: Fur(Clone)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4890904808900904
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1633679539365730}
  serializedVersion: 2
  m_LocalRotation: {x: 0.07250361, y: 0.7554566, z: 0.55426794, w: 0.34178293}
  m_LocalPosition: {x: -7.004666, y: 1.8774678, z: -3.6464944}
  m_LocalScale: {x: 1, y: 1, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4690225216439162}
  - {fileID: 4900454779698950}
  m_Father: {fileID: 4977961836617372}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1634118246617652
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4161054935575720}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4161054935575720
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1634118246617652}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4343013350933866}
  m_Father: {fileID: 4392647646421458}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1637634135714814
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4533458234848752}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4533458234848752
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1637634135714814}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4234808820688060}
  m_Father: {fileID: 4639739661995594}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1639865228924378
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4561738251446910}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4561738251446910
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1639865228924378}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4392071538200726}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1640662441115728
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4450629636940304}
  - component: {fileID: 137056601660860784}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4450629636940304
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1640662441115728}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4960828013354232}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137056601660860784
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1640662441115728}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4638915420087780}
  - {fileID: 4196335805681500}
  - {fileID: 4050510668770460}
  - {fileID: 4083148178963320}
  - {fileID: 4716547058762192}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4638915420087780}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1643282055632492
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4987700972223346}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4987700972223346
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1643282055632492}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4696930072443680}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1643599876575262
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4788977542518684}
  - component: {fileID: 137848745551415232}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4788977542518684
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1643599876575262}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4376175844078128}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137848745551415232
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1643599876575262}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4099749800158918}
  - {fileID: 4628289515667272}
  - {fileID: 4355073563388744}
  - {fileID: 4353885735641544}
  - {fileID: 4040016471392240}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4099749800158918}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1643725267478316
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4063075524144764}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4063075524144764
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1643725267478316}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4755461458901046}
  m_Father: {fileID: 4553626595404402}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1648211994021488
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4883814444037064}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4883814444037064
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1648211994021488}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4566568186652270}
  m_Father: {fileID: 4691286733771098}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1648322209647716
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4242606925366644}
  m_Layer: 0
  m_Name: Fur(Clone)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4242606925366644
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1648322209647716}
  serializedVersion: 2
  m_LocalRotation: {x: 0.18618312, y: 0.40339038, z: -0.038019743, w: -0.8950791}
  m_LocalPosition: {x: -6.828926, y: 2.1538277, z: -4.2719855}
  m_LocalScale: {x: 0.9999998, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4052611642327718}
  - {fileID: 4540374804730384}
  m_Father: {fileID: 4977961836617372}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1654260982665950
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4615692085858678}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4615692085858678
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1654260982665950}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4563072722727396}
  m_Father: {fileID: 4979244364572272}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1657696737855606
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4707362654427690}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4707362654427690
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1657696737855606}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4130549224605982}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1661683083115118
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4790392879173234}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4790392879173234
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1661683083115118}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4191479781446082}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1664000826525718
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4105069489818272}
  m_Layer: 0
  m_Name: Fur(Clone)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4105069489818272
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1664000826525718}
  serializedVersion: 2
  m_LocalRotation: {x: 0.014264269, y: 0.7502879, z: 0.003544928, w: -0.6609479}
  m_LocalPosition: {x: -6.8569546, y: 2.1897213, z: -4.0967684}
  m_LocalScale: {x: 1.0000005, y: 1, z: 1.0000005}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4556502358832764}
  - {fileID: 4947970512328624}
  m_Father: {fileID: 4977961836617372}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1665136796565778
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4748562607798066}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4748562607798066
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1665136796565778}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4342661123435952}
  m_Father: {fileID: 4096698606900844}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1668812221991276
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4503963407063252}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4503963407063252
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1668812221991276}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4203254112477852}
  m_Father: {fileID: 4567394065405726}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1669425720536772
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4673868401861434}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4673868401861434
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1669425720536772}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4669585959581036}
  m_Father: {fileID: 4868606785142852}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1674818594876242
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4855663554261602}
  - component: {fileID: 137406473627709870}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4855663554261602
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1674818594876242}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4065542281751594}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137406473627709870
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1674818594876242}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4096408345934446}
  - {fileID: 4864227740807002}
  - {fileID: 4176602214343208}
  - {fileID: 4395400403226508}
  - {fileID: 4935704356867394}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4096408345934446}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1675088468200334
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4196335805681500}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4196335805681500
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1675088468200334}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4050510668770460}
  m_Father: {fileID: 4638915420087780}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1677223690583628
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4691286733771098}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4691286733771098
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1677223690583628}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4883814444037064}
  m_Father: {fileID: 4487000767925862}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1678516071714920
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4406525262696272}
  m_Layer: 0
  m_Name: Fur(Clone)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4406525262696272
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1678516071714920}
  serializedVersion: 2
  m_LocalRotation: {x: 0.023350406, y: 0.97176844, z: -0.058729023, w: -0.22731434}
  m_LocalPosition: {x: -6.8606586, y: 2.1859431, z: -4.152379}
  m_LocalScale: {x: 1.0000002, y: 1, z: 1.0000006}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4430308352352364}
  - {fileID: 4174441436357838}
  m_Father: {fileID: 4977961836617372}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1678668061310954
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4377009971401804}
  - component: {fileID: 137714084733941882}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4377009971401804
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1678668061310954}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4357541566505000}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137714084733941882
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1678668061310954}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4696546182723534}
  - {fileID: 4086800233472960}
  - {fileID: 4991779370478380}
  - {fileID: 4025098631932344}
  - {fileID: 4134525503075568}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4696546182723534}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1689497035316932
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4716547058762192}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4716547058762192
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1689497035316932}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4083148178963320}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1693316646524216
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4072691009248966}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4072691009248966
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1693316646524216}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4554326689474886}
  m_Father: {fileID: 4582902832403578}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1695601137441902
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4247191628394108}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4247191628394108
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1695601137441902}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4837246579761702}
  m_Father: {fileID: 4601683942242640}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1697161448573164
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4599697830482060}
  m_Layer: 0
  m_Name: Fur(Clone)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4599697830482060
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1697161448573164}
  serializedVersion: 2
  m_LocalRotation: {x: 0.04074431, y: 0.9673987, z: 0.023078807, w: 0.24889183}
  m_LocalPosition: {x: -6.836328, y: 2.1877453, z: -4.057533}
  m_LocalScale: {x: 1.000001, y: 1, z: 1.0000012}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4503420414512394}
  - {fileID: 4342062008842472}
  m_Father: {fileID: 4977961836617372}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1700960593216152
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4249090932954450}
  - component: {fileID: 137458815441968440}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4249090932954450
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1700960593216152}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4769110457820716}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137458815441968440
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1700960593216152}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4759809719001408}
  - {fileID: 4023791195339470}
  - {fileID: 4584989041995992}
  - {fileID: 4471016430163470}
  - {fileID: 4078181946602220}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4759809719001408}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1701912590590648
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4300497339241628}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4300497339241628
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1701912590590648}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4614034137141938}
  m_Father: {fileID: 4613037116500886}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1706042025939782
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4567394065405726}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4567394065405726
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1706042025939782}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4503963407063252}
  m_Father: {fileID: 4848895636298698}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1710863342242476
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4245450905810606}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4245450905810606
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1710863342242476}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4863179927707052}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1712197916043260
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4890763884176514}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4890763884176514
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1712197916043260}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4605556085403904}
  m_Father: {fileID: 4892596039920884}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1716967711442300
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4655043311984714}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4655043311984714
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1716967711442300}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4986021569262276}
  m_Father: {fileID: 4496229664588006}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1717236769812696
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4603867063206794}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4603867063206794
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717236769812696}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4612412811047328}
  m_Father: {fileID: 4319768701508206}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1719191991340566
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4649251345835370}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4649251345835370
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1719191991340566}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4916152883094722}
  m_Father: {fileID: 4901293845650826}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1720377128116064
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4167610386639948}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4167610386639948
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1720377128116064}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4587345039050988}
  m_Father: {fileID: 4341806294985162}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1727782532594354
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4099749800158918}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4099749800158918
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1727782532594354}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4628289515667272}
  m_Father: {fileID: 4376175844078128}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1734099986511606
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4409716045811712}
  m_Layer: 0
  m_Name: Fur(Clone)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4409716045811712
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1734099986511606}
  serializedVersion: 2
  m_LocalRotation: {x: 0.029210296, y: -0.91349757, z: -0.00039653495, w: 0.40579414}
  m_LocalPosition: {x: -6.8965225, y: 2.1890845, z: -4.0777845}
  m_LocalScale: {x: 0.99999994, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4644933580379040}
  - {fileID: 4032702940973426}
  m_Father: {fileID: 4977961836617372}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1735596312230050
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4203254112477852}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4203254112477852
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1735596312230050}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4367738376407622}
  m_Father: {fileID: 4503963407063252}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1735605440964142
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4977961836617372}
  m_Layer: 0
  m_Name: Duplicated-Fur-Container
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4977961836617372
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1735605440964142}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: 1, z: -0, w: 0.00005541741}
  m_LocalPosition: {x: -6.8695464, y: 0.29006243, z: -4.0907617}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4597466805297008}
  - {fileID: 4993651339134116}
  - {fileID: 4667746719447994}
  - {fileID: 4001657965531710}
  - {fileID: 4960828013354232}
  - {fileID: 4769110457820716}
  - {fileID: 4317401655048226}
  - {fileID: 4357541566505000}
  - {fileID: 4242606925366644}
  - {fileID: 4214952577954650}
  - {fileID: 4065542281751594}
  - {fileID: 4901293845650826}
  - {fileID: 4752875721389680}
  - {fileID: 4773637574037170}
  - {fileID: 4406525262696272}
  - {fileID: 4137936656315706}
  - {fileID: 4105069489818272}
  - {fileID: 4409716045811712}
  - {fileID: 4599697830482060}
  - {fileID: 4936955753479582}
  - {fileID: 4724658083788706}
  - {fileID: 4848895636298698}
  - {fileID: 4888743764280720}
  - {fileID: 4376175844078128}
  - {fileID: 4582628293445100}
  - {fileID: 4537663300902504}
  - {fileID: 4809469808739632}
  - {fileID: 4677527877880628}
  - {fileID: 4962244202733878}
  - {fileID: 4188299226200268}
  - {fileID: 4319768701508206}
  - {fileID: 4921846498385518}
  - {fileID: 4809769831409178}
  - {fileID: 4478986039485242}
  - {fileID: 4361404020662066}
  - {fileID: 4222688486545566}
  - {fileID: 4190871456304996}
  - {fileID: 4431445969422820}
  - {fileID: 4546294073972830}
  - {fileID: 4601683942242640}
  - {fileID: 4890904808900904}
  - {fileID: 4802156807059892}
  - {fileID: 4134671816585310}
  - {fileID: 4376072352231028}
  - {fileID: 4147375165687756}
  - {fileID: 4865116496707714}
  - {fileID: 4263517636708870}
  - {fileID: 4290934278003306}
  m_Father: {fileID: 4328043985580676}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1737665668411302
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4040016471392240}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4040016471392240
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1737665668411302}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4353885735641544}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1740383982553972
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4368120535833634}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4368120535833634
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1740383982553972}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4979244364572272}
  m_Father: {fileID: 4809769831409178}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1746047592144470
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4444047193085062}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4444047193085062
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1746047592144470}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4392071538200726}
  m_Father: {fileID: 4612412811047328}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1747735508537128
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4913953666140160}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4913953666140160
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1747735508537128}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4597697637988894}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1750610907354590
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4581389076948222}
  - component: {fileID: 137412349687940656}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4581389076948222
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1750610907354590}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4888743764280720}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137412349687940656
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1750610907354590}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4392647646421458}
  - {fileID: 4161054935575720}
  - {fileID: 4343013350933866}
  - {fileID: 4834389742527184}
  - {fileID: 4893567241062206}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4392647646421458}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1751013098971008
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4425983070017752}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4425983070017752
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1751013098971008}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4639739661995594}
  m_Father: {fileID: 4190871456304996}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1752042312592102
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4837246579761702}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4837246579761702
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1752042312592102}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4359196667285750}
  m_Father: {fileID: 4247191628394108}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1753159221744938
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4290934278003306}
  m_Layer: 0
  m_Name: Fur(Clone)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4290934278003306
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1753159221744938}
  serializedVersion: 2
  m_LocalRotation: {x: 0.6672475, y: 0.0919036, z: 0.103558466, w: 0.731854}
  m_LocalPosition: {x: -6.884467, y: 1.7339941, z: -3.592155}
  m_LocalScale: {x: 0.9999999, y: 1.0000005, z: 1.0000005}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4195136295709406}
  - {fileID: 4588577475580552}
  m_Father: {fileID: 4977961836617372}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1759871210394728
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4710079006732550}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4710079006732550
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1759871210394728}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4277132183547240}
  m_Father: {fileID: 4645445171498760}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1761923151386740
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4645445171498760}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4645445171498760
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1761923151386740}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4710079006732550}
  m_Father: {fileID: 4644933580379040}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1763103573172984
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4343013350933866}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4343013350933866
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1763103573172984}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4834389742527184}
  m_Father: {fileID: 4161054935575720}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1764569409044732
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4893567241062206}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4893567241062206
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1764569409044732}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4834389742527184}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1773887763231630
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4755461458901046}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4755461458901046
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1773887763231630}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4063075524144764}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1774974314379450
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4355073563388744}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4355073563388744
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1774974314379450}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4353885735641544}
  m_Father: {fileID: 4628289515667272}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1779083555844176
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4900454779698950}
  - component: {fileID: 137395392660675704}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4900454779698950
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1779083555844176}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4890904808900904}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137395392660675704
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1779083555844176}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4690225216439162}
  - {fileID: 4533768059356236}
  - {fileID: 4515752805541442}
  - {fileID: 4657346393586062}
  - {fileID: 4578751123297142}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4690225216439162}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1781286868579612
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4588577475580552}
  - component: {fileID: 137335496403004876}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4588577475580552
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1781286868579612}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4290934278003306}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137335496403004876
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1781286868579612}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4195136295709406}
  - {fileID: 4031826676103842}
  - {fileID: 4868606785142852}
  - {fileID: 4673868401861434}
  - {fileID: 4669585959581036}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4195136295709406}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1781323829892298
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4902787726143872}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4902787726143872
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1781323829892298}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4974044196236162}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1783702941303560
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4068294932989172}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4068294932989172
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1783702941303560}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4131767099609148}
  m_Father: {fileID: 4121012289966634}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1783729650279170
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4612412811047328}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4612412811047328
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1783729650279170}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4444047193085062}
  m_Father: {fileID: 4603867063206794}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1783822464861540
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4005591801546938}
  - component: {fileID: 137179025305054216}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4005591801546938
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1783822464861540}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4962244202733878}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137179025305054216
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1783822464861540}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4334088815347686}
  - {fileID: 4433432817394978}
  - {fileID: 4442391927560176}
  - {fileID: 4101809373755890}
  - {fileID: 4225472753494694}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4334088815347686}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1787591268942616
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4182293280401814}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4182293280401814
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1787591268942616}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4563072722727396}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1794122335585066
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4441489369223116}
  - component: {fileID: 137947758420937912}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4441489369223116
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1794122335585066}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4582628293445100}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137947758420937912
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1794122335585066}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4093435145443704}
  - {fileID: 4620250118782988}
  - {fileID: 4786831487071688}
  - {fileID: 4963768608483880}
  - {fileID: 4804192041069872}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4093435145443704}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1795900790210632
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4189245634933282}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4189245634933282
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1795900790210632}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4869433572613942}
  m_Father: {fileID: 4214952577954650}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1796159083103142
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4183220221287532}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4183220221287532
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1796159083103142}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4539077765570672}
  m_Father: {fileID: 4948951483051014}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1802320956395956
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4863179927707052}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4863179927707052
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1802320956395956}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4245450905810606}
  m_Father: {fileID: 4539323340940212}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1802511855885348
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4230287617088586}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4230287617088586
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1802511855885348}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4551806082897238}
  m_Father: {fileID: 4918264362442128}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1804349560283686
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4434867743158798}
  - component: {fileID: 137375016443269112}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4434867743158798
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1804349560283686}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4478986039485242}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137375016443269112
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1804349560283686}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4754619340078462}
  - {fileID: 4627399967623150}
  - {fileID: 4121012289966634}
  - {fileID: 4068294932989172}
  - {fileID: 4131767099609148}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4754619340078462}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1810032216647542
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4065542281751594}
  m_Layer: 0
  m_Name: Fur(Clone)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4065542281751594
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1810032216647542}
  serializedVersion: 2
  m_LocalRotation: {x: 0.1819296, y: 0.4190021, z: -0.016555753, w: -0.88941824}
  m_LocalPosition: {x: -6.808496, y: 2.1565652, z: -4.2587485}
  m_LocalScale: {x: 0.9999999, y: 1.0000001, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4096408345934446}
  - {fileID: 4855663554261602}
  m_Father: {fileID: 4977961836617372}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1810423933962466
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4613222307439402}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4613222307439402
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1810423933962466}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4388266255378952}
  m_Father: {fileID: 4537663300902504}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1819010321908616
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4669585959581036}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4669585959581036
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1819010321908616}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4673868401861434}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1825890021202126
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4989920662108158}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4989920662108158
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1825890021202126}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4541661596719794}
  m_Father: {fileID: 4185913783158184}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1828633946372568
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4794916087554770}
  - component: {fileID: 137842786128837358}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4794916087554770
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1828633946372568}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4317401655048226}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137842786128837358
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1828633946372568}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4261180413190798}
  - {fileID: 4834631175720770}
  - {fileID: 4920532339840920}
  - {fileID: 4959273635272118}
  - {fileID: 4913746822608142}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4261180413190798}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1830699500808466
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4620250118782988}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4620250118782988
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1830699500808466}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4786831487071688}
  m_Father: {fileID: 4093435145443704}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1831411370050052
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4851493309731908}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4851493309731908
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1831411370050052}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4714836751408646}
  m_Father: {fileID: 4809469808739632}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1832456467364350
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4428464269997570}
  - component: {fileID: 137119164064769314}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4428464269997570
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1832456467364350}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4936955753479582}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137119164064769314
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1832456467364350}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4243965163282450}
  - {fileID: 4948951483051014}
  - {fileID: 4183220221287532}
  - {fileID: 4539077765570672}
  - {fileID: 4736993046457010}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4243965163282450}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1834795029720520
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4083148178963320}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4083148178963320
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1834795029720520}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4716547058762192}
  m_Father: {fileID: 4050510668770460}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1839686248912498
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4317401655048226}
  m_Layer: 0
  m_Name: Fur(Clone)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4317401655048226
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1839686248912498}
  serializedVersion: 2
  m_LocalRotation: {x: 0.25225103, y: 0.5892881, z: -0.10329102, w: -0.7605524}
  m_LocalPosition: {x: -6.7999096, y: 2.1156383, z: -4.3427186}
  m_LocalScale: {x: 1.0000002, y: 0.9999999, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4261180413190798}
  - {fileID: 4794916087554770}
  m_Father: {fileID: 4977961836617372}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1844325289317644
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4605556085403904}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4605556085403904
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1844325289317644}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4890763884176514}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1845874516550948
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4200513286318596}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4200513286318596
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1845874516550948}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4394753740093694}
  m_Father: {fileID: 4556502358832764}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1851767367697146
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4696930072443680}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4696930072443680
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1851767367697146}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4987700972223346}
  m_Father: {fileID: 4903818840579240}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1852365030865904
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4539323340940212}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4539323340940212
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1852365030865904}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4863179927707052}
  m_Father: {fileID: 4127068397799744}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1854665919997212
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4983265159176056}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4983265159176056
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1854665919997212}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4614034137141938}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1855287955074008
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4710580022078200}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4710580022078200
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1855287955074008}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4342661123435952}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1855946248412772
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4353248957707084}
  - component: {fileID: 137751535353392128}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4353248957707084
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1855946248412772}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4188299226200268}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137751535353392128
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1855946248412772}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4725026542486276}
  - {fileID: 4613037116500886}
  - {fileID: 4300497339241628}
  - {fileID: 4614034137141938}
  - {fileID: 4983265159176056}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4725026542486276}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1857327944982142
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4935704356867394}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4935704356867394
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1857327944982142}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4395400403226508}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1864043418042826
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4628289515667272}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4628289515667272
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1864043418042826}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4355073563388744}
  m_Father: {fileID: 4099749800158918}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1868138266281214
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4582902832403578}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4582902832403578
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1868138266281214}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4072691009248966}
  m_Father: {fileID: 4222688486545566}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1868543455446206
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4584989041995992}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4584989041995992
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1868543455446206}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4471016430163470}
  m_Father: {fileID: 4023791195339470}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1869603120497352
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4991779370478380}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4991779370478380
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1869603120497352}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4025098631932344}
  m_Father: {fileID: 4086800233472960}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1870029489581356
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4930714792307448}
  - component: {fileID: 137885617256163560}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4930714792307448
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1870029489581356}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4134671816585310}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137885617256163560
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1870029489581356}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4574519651251692}
  - {fileID: 4116735580823194}
  - {fileID: 4086513863479698}
  - {fileID: 4688728242671482}
  - {fileID: 4069893404338898}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4574519651251692}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1872653057224760
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4363388827427754}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4363388827427754
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1872653057224760}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4910638746150252}
  m_Father: {fileID: 4667746719447994}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1876409433756974
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4334088815347686}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4334088815347686
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1876409433756974}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4433432817394978}
  m_Father: {fileID: 4962244202733878}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1878862150434994
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4134525503075568}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4134525503075568
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1878862150434994}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4025098631932344}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1879858821317728
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4574519651251692}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4574519651251692
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1879858821317728}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4116735580823194}
  m_Father: {fileID: 4134671816585310}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1880211851776262
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4376175844078128}
  m_Layer: 0
  m_Name: Fur(Clone)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4376175844078128
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1880211851776262}
  serializedVersion: 2
  m_LocalRotation: {x: 0.16696845, y: -0.12806024, z: -0.017931702, w: 0.97744596}
  m_LocalPosition: {x: -6.8738546, y: 2.1617377, z: -3.9245012}
  m_LocalScale: {x: 0.9999999, y: 1.0000002, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4099749800158918}
  - {fileID: 4788977542518684}
  m_Father: {fileID: 4977961836617372}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1882833567714138
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4386590654129050}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4386590654129050
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1882833567714138}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4892596039920884}
  m_Father: {fileID: 4255209885820608}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1883636349880600
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4501361830274588}
  - component: {fileID: 137558801912113826}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4501361830274588
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1883636349880600}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4137936656315706}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137558801912113826
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1883636349880600}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4250435098304044}
  - {fileID: 4096698606900844}
  - {fileID: 4748562607798066}
  - {fileID: 4342661123435952}
  - {fileID: 4710580022078200}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4250435098304044}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1883756599700476
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4086513863479698}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4086513863479698
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1883756599700476}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4688728242671482}
  m_Father: {fileID: 4116735580823194}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1892171813131030
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4868606785142852}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4868606785142852
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1892171813131030}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4673868401861434}
  m_Father: {fileID: 4031826676103842}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1897912009181366
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4479242454100818}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4479242454100818
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1897912009181366}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4234808820688060}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1898427921726296
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4582628293445100}
  m_Layer: 0
  m_Name: Fur(Clone)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4582628293445100
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1898427921726296}
  serializedVersion: 2
  m_LocalRotation: {x: 0.11105192, y: -0.91521704, z: -0.14114895, w: 0.36072475}
  m_LocalPosition: {x: -6.9207206, y: 2.1576824, z: -3.920759}
  m_LocalScale: {x: 0.9999999, y: 1, z: 0.99999976}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4093435145443704}
  - {fileID: 4441489369223116}
  m_Father: {fileID: 4977961836617372}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1904096192555510
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4725026542486276}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4725026542486276
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1904096192555510}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4613037116500886}
  m_Father: {fileID: 4188299226200268}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1907420602473708
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4995391476415840}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4995391476415840
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1907420602473708}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4144806673296094}
  m_Father: {fileID: 4639868989751984}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1908246839934880
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4127068397799744}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4127068397799744
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1908246839934880}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4539323340940212}
  m_Father: {fileID: 4296962120943674}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1913120200494826
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4597466805297008}
  m_Layer: 0
  m_Name: Fur(Clone)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4597466805297008
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1913120200494826}
  serializedVersion: 2
  m_LocalRotation: {x: 0.123862445, y: 0.90682954, z: -0.34514272, w: -0.2078338}
  m_LocalPosition: {x: -6.82941, y: 2.0554724, z: -4.4287286}
  m_LocalScale: {x: 1.000001, y: 1.0000002, z: 1.0000007}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4185913783158184}
  - {fileID: 4934980662918152}
  m_Father: {fileID: 4977961836617372}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1919834264748132
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4227246508873876}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4227246508873876
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1919834264748132}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4530335046546304}
  m_Father: {fileID: 4219978465878828}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1925280805705526
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4910638746150252}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4910638746150252
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1925280805705526}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4419046540371290}
  m_Father: {fileID: 4363388827427754}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1926577195518376
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4948951483051014}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4948951483051014
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1926577195518376}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4183220221287532}
  m_Father: {fileID: 4243965163282450}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1928823461284290
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4503420414512394}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4503420414512394
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1928823461284290}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4392037079466006}
  m_Father: {fileID: 4599697830482060}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1938742285922384
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4752875721389680}
  m_Layer: 0
  m_Name: Fur(Clone)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4752875721389680
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1938742285922384}
  serializedVersion: 2
  m_LocalRotation: {x: 0.08380998, y: 0.8633446, z: -0.068268515, w: -0.4929011}
  m_LocalPosition: {x: -6.8312926, y: 2.1782532, z: -4.1902494}
  m_LocalScale: {x: 1.0000005, y: 1, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4824459056264336}
  - {fileID: 4912527850978564}
  m_Father: {fileID: 4977961836617372}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1943162579788900
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4736993046457010}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4736993046457010
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1943162579788900}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4539077765570672}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1945738448772246
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4897583848602156}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4897583848602156
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1945738448772246}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4856218018701080}
  m_Father: {fileID: 4798276353987694}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1947056736940484
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4449624579996280}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4449624579996280
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1947056736940484}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4272896805277716}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1953840104278324
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4601683942242640}
  m_Layer: 0
  m_Name: Fur(Clone)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4601683942242640
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1953840104278324}
  serializedVersion: 2
  m_LocalRotation: {x: 0.3676816, y: 0.6045552, z: 0.40379718, w: 0.579889}
  m_LocalPosition: {x: -6.8818736, y: 1.8916957, z: -3.632668}
  m_LocalScale: {x: 0.99999994, y: 0.9999998, z: 0.99999964}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4247191628394108}
  - {fileID: 4185824689734182}
  m_Father: {fileID: 4977961836617372}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1955421692738724
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4715441191537784}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4715441191537784
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1955421692738724}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4638125490918254}
  m_Father: {fileID: 4986021569262276}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1957213971913142
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4703030257586666}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4703030257586666
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1957213971913142}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4272896805277716}
  m_Father: {fileID: 4392037079466006}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1957794710249638
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4222688486545566}
  m_Layer: 0
  m_Name: Fur(Clone)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4222688486545566
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1957794710249638}
  serializedVersion: 2
  m_LocalRotation: {x: 0.069169305, y: -0.9074338, z: -0.41444543, w: 0.0038129177}
  m_LocalPosition: {x: -6.931186, y: 2.0133884, z: -3.7136543}
  m_LocalScale: {x: 1.0000007, y: 1.0000002, z: 1.0000005}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4582902832403578}
  - {fileID: 4608367468215096}
  m_Father: {fileID: 4977961836617372}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1961367016673970
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4809769831409178}
  m_Layer: 0
  m_Name: Fur(Clone)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4809769831409178
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1961367016673970}
  serializedVersion: 2
  m_LocalRotation: {x: 0.35275182, y: -0.5151208, z: -0.14727698, w: 0.76715463}
  m_LocalPosition: {x: -6.9387255, y: 2.0438132, z: -3.7435195}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4368120535833634}
  - {fileID: 4933250456257874}
  m_Father: {fileID: 4977961836617372}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1964393297145280
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4367942849137652}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4367942849137652
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1964393297145280}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4620077261551636}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1967745345889462
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4665251582377956}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4665251582377956
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1967745345889462}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4674067540964586}
  m_Father: {fileID: 4189374601071402}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1968328972387838
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4220394680458434}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4220394680458434
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1968328972387838}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4963064634980566}
  m_Father: {fileID: 4714836751408646}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1969672991348330
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4541600953103936}
  - component: {fileID: 137346978265804680}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4541600953103936
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1969672991348330}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4809469808739632}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137346978265804680
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1969672991348330}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4851493309731908}
  - {fileID: 4714836751408646}
  - {fileID: 4220394680458434}
  - {fileID: 4963064634980566}
  - {fileID: 4494256637927940}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4851493309731908}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1973368667079922
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4937691019764640}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4937691019764640
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1973368667079922}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4847153430429860}
  m_Father: {fileID: 4388266255378952}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1981665019671270
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4910196598776148}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4910196598776148
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1981665019671270}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4847153430429860}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1984607844404554
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4587924813672038}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4587924813672038
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1984607844404554}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4167555383973238}
  m_Father: {fileID: 4773637574037170}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1986979126772088
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4587345039050988}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4587345039050988
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1986979126772088}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4167610386639948}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1996122107068986
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4419046540371290}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4419046540371290
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1996122107068986}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4597697637988894}
  m_Father: {fileID: 4910638746150252}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
