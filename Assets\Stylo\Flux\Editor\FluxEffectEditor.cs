#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using Stylo.Flux;
#if URP_INSTALLED
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
#endif

namespace Stylo.Flux.Editor
{
    // Note: This editor is designed to work with any FluxEffect component via reflection
    // The actual target type will be determined at runtime
    public class FluxEffectEditor : UnityEditor.Editor
    {
        private FluxPreset? selectedPreset;
        private int selectedPresetIndex = 0;
        private string[]? presetNames;
        private string newPresetName = "My Flux Preset";
        private string newPresetCategory = "Custom";

        private void OnEnable()
        {
            RefreshPresetList();
        }

        private void RefreshPresetList()
        {
            FluxPresetManager.RefreshPresetCache();
            var presets = FluxPresetManager.GetAllPresets();
            presetNames = new string[presets.Count + 1];
            presetNames[0] = "None";

            for (int i = 0; i < presets.Count; i++)
            {
                presetNames[i + 1] = presets[i].GetCategorizedName();
            }

            selectedPresetIndex = 0;
            selectedPreset = null;
        }

        public override void OnInspectorGUI()
        {
            var fluxEffect = target; // Use object type for flexibility

            // Preset section at the top
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("Flux Preset", EditorStyles.boldLabel);

            EditorGUILayout.BeginHorizontal();
            selectedPreset = (FluxPreset)EditorGUILayout.ObjectField("Select Preset", selectedPreset, typeof(FluxPreset), false);

            // Apply button
            GUI.enabled = selectedPreset != null;
            if (GUILayout.Button("Apply", GUILayout.Width(60)))
            {
                if (selectedPreset != null)
                {
                    Undo.RecordObject(fluxEffect, "Apply Flux Preset");
                    ApplyPresetToFluxEffect(selectedPreset, fluxEffect);
                    EditorUtility.SetDirty(fluxEffect);
                }
            }
            GUI.enabled = true;
            EditorGUILayout.EndHorizontal();

            // Show preset description
            if (selectedPreset != null && !string.IsNullOrEmpty(selectedPreset.description))
            {
                EditorGUILayout.HelpBox(selectedPreset.description, MessageType.Info);
            }

            // Quick actions
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("Save as Preset", GUILayout.Height(20)))
            {
                SaveCurrentAsPreset(fluxEffect);
            }
            if (GUILayout.Button("Create Defaults", GUILayout.Height(20)))
            {
                FluxPresetManager.CreateDefaultPresets();
                RefreshPresetList();
            }
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.EndVertical();

            EditorGUILayout.Space(10);

            // Draw default inspector for all Flux parameters
            DrawDefaultInspector();
        }

        private void ApplyPresetToFluxEffect(FluxPreset preset, object fluxEffect)
        {
            if (preset == null || fluxEffect == null) return;

            // Use reflection to apply preset values
            var type = fluxEffect.GetType();

            // Core parameters
            SetProperty(type, fluxEffect, "EffectIntensity", preset.effectIntensity);
            SetProperty(type, fluxEffect, "ColorCrunch", preset.colorCrunch);
            SetProperty(type, fluxEffect, "Downscaling", preset.downscaling);
            SetEnumProperty(type, fluxEffect, "BlockSize", (int)preset.blockSize);
            SetProperty(type, fluxEffect, "Oversharpening", preset.oversharpening);
            SetProperty(type, fluxEffect, "DontCrunchSkybox", preset.dontCrunchSkybox);
            SetProperty(type, fluxEffect, "OnlyStenciled", preset.onlyStenciled);

            // Basic datamoshing
            SetProperty(type, fluxEffect, "ReprojectBaseNoise", preset.reprojectBaseNoise);
            SetProperty(type, fluxEffect, "ReprojectBaseRerollSpeed", preset.reprojectBaseRerollSpeed);
            SetProperty(type, fluxEffect, "ReprojectLengthInfluence", preset.reprojectLengthInfluence);

            // Advanced datamoshing
            SetProperty(type, fluxEffect, "KeyframeResetRate", preset.keyframeResetRate);
            SetProperty(type, fluxEffect, "MotionVectorCorruption", preset.motionVectorCorruption);
            SetProperty(type, fluxEffect, "ErrorAccumulation", preset.errorAccumulation);
            SetProperty(type, fluxEffect, "DCTCorruption", preset.dctCorruption);

            // Enhanced corruption
            SetProperty(type, fluxEffect, "CorruptionMask", preset.corruptionMask);
            SetProperty(type, fluxEffect, "ChromaCorruption", preset.chromaCorruption);
            SetProperty(type, fluxEffect, "GlitchTransition", preset.glitchTransition);
            SetProperty(type, fluxEffect, "FeedbackIntensity", preset.feedbackIntensity);
            SetProperty(type, fluxEffect, "MultiScaleCorruption", preset.multiScaleCorruption);

            // Debug
            SetProperty(type, fluxEffect, "PreviewMotionVectors", preset.visualizeMotionVectors);
        }

        private void SetProperty(System.Type type, object obj, string propertyName, object value)
        {
            var prop = type.GetProperty(propertyName);
            if (prop != null && prop.CanWrite) prop.SetValue(obj, value);
        }

        private void SetEnumProperty(System.Type type, object obj, string propertyName, int value)
        {
            var prop = type.GetProperty(propertyName);
            if (prop != null && prop.CanWrite)
            {
                var enumValue = System.Enum.ToObject(prop.PropertyType, value);
                prop.SetValue(obj, enumValue);
            }
        }

        private void SaveCurrentAsPreset(object fluxEffect)
        {
            if (string.IsNullOrEmpty(newPresetName))
            {
                EditorUtility.DisplayDialog("Invalid Name", "Please enter a valid preset name.", "OK");
                return;
            }

            // Create the preset asset
            FluxPreset newPreset = FluxPresetManager.CreatePresetAsset(newPresetName);

            if (newPreset != null)
            {
                // Set preset info
                newPreset.presetName = newPresetName;
                newPreset.category = string.IsNullOrEmpty(newPresetCategory) ? "Custom" : newPresetCategory;
                newPreset.description = $"Custom preset created from {((UnityEngine.Object)fluxEffect).name}";

                // Capture current parameters
                newPreset.CaptureFromRuntime(fluxEffect);

                // Save the asset
                EditorUtility.SetDirty(newPreset);
                AssetDatabase.SaveAssets();

                // Refresh the list
                RefreshPresetList();

                // Select the new preset in the dropdown
                var presets = FluxPresetManager.GetAllPresets();
                for (int i = 0; i < presets.Count; i++)
                {
                    if (presets[i] == newPreset)
                    {
                        selectedPresetIndex = i + 1;
                        selectedPreset = newPreset;
                        break;
                    }
                }

                EditorUtility.DisplayDialog("Preset Saved",
                    $"Preset '{newPresetName}' has been saved successfully!", "OK");
            }
            else
            {
                EditorUtility.DisplayDialog("Save Failed",
                    "Failed to create preset asset. Please check the console for errors.", "OK");
            }
        }
    }
}
#endif
