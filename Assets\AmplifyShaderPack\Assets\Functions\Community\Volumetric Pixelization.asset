%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: Volumetric Pixelization
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor v1.9.3.3\n// Available at the
    Unity Asset Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=19303\nNode;AmplifyShaderEditor.CommentaryNode;81;339.3724,-1640.206;Inherit;False;1782.987;547.9377;Output
    data;20;165;47;48;46;49;50;51;92;82;52;62;166;167;168;169;170;171;172;173;179;;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;80;-2281.119,318.3503;Inherit;False;2880.367;605.2032;Intersection
    points;23;152;129;131;38;163;119;154;164;157;158;162;39;42;45;139;127;130;40;43;41;132;128;44;;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;79;-1019.591,-335.2646;Inherit;False;1624.394;403.3865;Projection;11;36;25;37;31;29;27;24;30;26;22;28;;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;78;-638.533,-694.0873;Inherit;False;825.0687;260.7555;Vectors
    similarity;4;20;21;19;18;;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;77;-644.6196,-1083.475;Inherit;False;773;293;Sphere
    to cam vector;4;15;17;14;16;;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;76;832,-944;Inherit;False;1049.548;3064.427;;39;178;177;176;175;174;97;95;94;96;93;68;75;53;88;0;57;84;71;74;69;61;60;33;86;87;58;66;59;70;73;85;65;32;72;63;35;67;83;64;Outputs;1,0.4,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;13;-2361.763,-523;Inherit;False;1185.763;690.3141;;14;122;3;114;115;12;8;9;10;2;1;5;4;11;151;Inputs;1,0.3981043,0,1;0;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;25;-98.35941,-176.705;Float;False;Ratio;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;128;-335.3994,527.3321;Inherit;False;12;ViewDirection;1;0;OBJECT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;132;169.9795,589.5518;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;28;-683.5907,-47.26453;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;1;-1668,-89;Inherit;False;Sphere
    Position;3;1;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;11;-1463,-458;Float;False;CamPosition;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;16;-596.6196,-907.4757;Inherit;False;4;SphereCenter;1;0;OBJECT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WorldSpaceCameraPos;8;-2164,-458;Inherit;False;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.SimpleAddOpNode;40;-1769.238,404.0628;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;41;-1841.634,603.265;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;162;-574.5894,513.3831;Float;False;RejectionValue;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;37;363.1449,-178.5192;Float;False;SqrtRatio;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;17;-116.6198,-987.4757;Float;False;SphereToCam;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;15;-580.6196,-1035.475;Inherit;False;11;CamPosition;1;0;OBJECT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;167;1697.206,-1487.543;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleDivideOpNode;49;1315.856,-1227.529;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;30;-523.5906,-271.2646;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;48;356.6727,-1531.507;Inherit;False;44;ExitPoint;1;0;OBJECT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;9;-1844,-346;Inherit;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;45;374.2427,378.7978;Float;False;EntryPoint;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;12;-1435,-348;Float;False;ViewDirection;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;4;-1436,-83;Float;False;SphereCenter;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;26;-491.5907,-111.2645;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;22;-971.5907,-143.2646;Inherit;False;17;SphereToCam;1;0;OBJECT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;129;-82.63494,624.0209;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;29;-811.5907,-287.2646;Inherit;False;21;DirectionsSimilarity;1;0;OBJECT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.DotProductOpNode;24;-651.5907,-159.2646;Inherit;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;51;616.3732,-1199.407;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;50;362.3724,-1261.407;Inherit;False;5;SphereRadius;1;0;OBJECT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;82;920.6613,-1254.378;Float;False;DistanceBetweenIntersectionPoints;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;31;-316.689,-178.2348;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;62;1491.856,-1211.529;Float;False;VolumetricMask;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;52;399.3727,-1182.407;Float;False;Constant;_Float0;Float
    0;0;0;Create;True;0;0;0;False;0;False;2;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.DistanceOpNode;46;668.0748,-1306.916;Inherit;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;179;644.2776,-1394.355;Float;False;Constant;_Float6;Float
    6;0;0;Create;True;0;0;0;False;0;False;0.5;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;169;1073.534,-1420.958;Inherit;False;5;SphereRadius;1;0;OBJECT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;173;1722.95,-1322.717;Float;False;LinearDensity;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;166;742.9351,-1575.76;Inherit;False;4;SphereCenter;1;0;OBJECT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;172;838.5157,-1479.097;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT;0.5;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;171;600.5157,-1497.097;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.NormalizeNode;10;-1624,-345;Inherit;False;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RangedFloatNode;59;988.6047,-773.9053;Float;False;Constant;_Float0;Float
    0;0;0;Create;True;0;0;0;False;0;False;0;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.NegateNode;39;-2004.238,524.0624;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;139;-418.6124,409.0154;Float;False;DistanceToEntryPoint;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.DotProductOpNode;20;-286.5321,-598.0873;Inherit;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SqrtOpNode;36;215.1448,-171.5192;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;44;368.0173,653.8575;Float;False;ExitPoint;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;18;-574.533,-646.0873;Inherit;False;17;SphereToCam;1;0;OBJECT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;42;-2020.238,412.0628;Inherit;False;37;SqrtRatio;1;0;OBJECT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;19;-590.533,-518.0873;Inherit;False;12;ViewDirection;1;0;OBJECT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;130;-97.63494,421.0207;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;27;-971.5907,-63.26454;Inherit;False;5;SphereRadius;1;0;OBJECT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;21;-94.53228,-598.0873;Float;False;DirectionsSimilarity;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;127;-65.63493,533.0208;Inherit;False;11;CamPosition;1;0;OBJECT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.IntNode;61;984.9449,-615.8052;Float;False;Constant;_Int1;Int
    1;0;0;Create;True;0;0;0;False;0;False;1;0;False;0;1;INT;0\nNode;AmplifyShaderEditor.RangedFloatNode;157;-1446.451,542.5869;Float;False;Constant;_Float0;Float
    0;0;0;Create;True;0;0;0;False;0;False;-1;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;131;158.365,439.0207;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.NegateNode;151;-1563.522,-227.1225;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.Compare;60;1247.965,-745.2391;Inherit;False;4;4;0;FLOAT;0;False;1;FLOAT;0;False;2;INT;0;False;3;INT;0;False;1;INT;0\nNode;AmplifyShaderEditor.FunctionInput;2;-1668,39;Inherit;False;Sphere
    Radius;1;2;False;1;0;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;158;-1447.948,622.7039;Inherit;False;25;Ratio;1;0;OBJECT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;177;991.0483,389.7305;Inherit;False;162;RejectionValue;1;0;OBJECT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;93;1019.556,93.20818;Float;False;Constant;_Float4;Float
    4;0;0;Create;True;0;0;0;False;0;False;0;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;69;954.0185,929.9778;Inherit;False;4;SphereCenter;1;0;OBJECT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;38;-2260.24,524.0624;Inherit;False;21;DirectionsSimilarity;1;0;OBJECT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.Compare;83;1268.499,1729.963;Inherit;False;4;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.Compare;176;1263.048,405.7305;Inherit;False;4;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;58;956.6047,-853.9056;Inherit;False;162;RejectionValue;1;0;OBJECT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;14;-308.6198,-987.4757;Inherit;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;122;-2088.843,-301.4263;Float;False;WorldPosition;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleDivideOpNode;168;1371.037,-1489.159;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.IntNode;57;988.6047,-693.9056;Float;False;Constant;_Int0;Int
    0;0;0;Create;True;0;0;0;False;0;False;0;0;False;0;1;INT;0\nNode;AmplifyShaderEditor.Compare;163;-1242.889,485.5717;Inherit;False;4;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;119;-1830.512,509.304;Inherit;False;114;DistanceToPixel;1;0;OBJECT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;170;1518.516,-1494.097;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;164;-972.4601,779.2328;Inherit;False;Use
    Inner Occlusion;True;1;2;-1;In 0;In 1;Object;-1;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.LengthOpNode;115;-1697.985,-229.0745;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;3;-2313.547,-301.8655;Inherit;False;World
    Position;3;0;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;92;1847.626,-1479.028;Float;False;SquareDensity;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.DistanceOpNode;165;1060.281,-1539.363;Inherit;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;154;-868.666,515.9661;Inherit;False;Use
    Outer Occlusion;True;1;2;-1;In 0;In 1;Object;-1;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;114;-1430.725,-227.1559;Float;False;DistanceToPixel;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMaxOpNode;152;-1486.302,811.816;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;43;-2092.234,625.6461;Inherit;False;37;SqrtRatio;1;0;OBJECT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;86;958.8233,1777.963;Float;False;Constant;_Float3;Float
    3;0;0;Create;True;0;0;0;False;0;False;0;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;47;345.8727,-1433.807;Inherit;False;45;EntryPoint;1;0;OBJECT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;5;-1436,45;Float;False;SphereRadius;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.Compare;64;1274.018,785.9779;Inherit;False;4;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT3;0,0,0;False;3;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RangedFloatNode;65;1018.018,833.9778;Float;False;Constant;_Float1;Float
    1;0;0;Create;True;0;0;0;False;0;False;0;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;66;986.0185,753.9779;Inherit;False;162;RejectionValue;1;0;OBJECT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;74;975.8593,1233.015;Inherit;False;162;RejectionValue;1;0;OBJECT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;71;943.8594,1409.015;Inherit;False;4;SphereCenter;1;0;OBJECT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RangedFloatNode;73;1007.859,1313.015;Float;False;Constant;_Float2;Float
    2;0;0;Create;True;0;0;0;False;0;False;0;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;67;970.0185,1025.978;Inherit;False;45;EntryPoint;1;0;OBJECT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;87;927.8233,1697.963;Inherit;False;162;RejectionValue;1;0;OBJECT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.Compare;32;1254.28,-379.5264;Inherit;False;4;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;33;982.2796,-395.5264;Inherit;False;162;RejectionValue;1;0;OBJECT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;96;939.556,189.2082;Inherit;False;173;LinearDensity;1;0;OBJECT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;174;1023.049,469.7306;Float;False;Constant;_Float5;Float
    5;0;0;Create;True;0;0;0;False;0;False;0;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;175;943.0482,565.7306;Inherit;False;92;SquareDensity;1;0;OBJECT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.Compare;94;1259.555,29.20817;Inherit;False;4;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;95;987.556,13.20817;Inherit;False;162;RejectionValue;1;0;OBJECT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.Compare;70;1263.859,1265.015;Inherit;False;4;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT3;0,0,0;False;3;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;72;959.8594,1505.014;Inherit;False;44;ExitPoint;1;0;OBJECT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;85;894.7207,1978.91;Inherit;False;82;DistanceBetweenIntersectionPoints;1;0;OBJECT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;35;1014.28,-315.5264;Float;False;Constant;_Float1;Float
    1;0;0;Create;True;0;0;0;False;0;False;0;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;63;934.2796,-219.5264;Inherit;False;62;VolumetricMask;1;0;OBJECT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;84;894.8233,1873.963;Inherit;False;4;SphereCenter;1;0;OBJECT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionOutput;0;1478.28,-379.5264;Inherit;False;True;-1;Normalized
    Depth;3;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;88;1511.872,1747.855;Inherit;False;False;-1;Linear
    Depth;2;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;178;1487.048,405.7305;Inherit;False;False;-1;Square
    Density;1;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;75;1535.859,1265.015;Inherit;False;False;-1;Exit
    Point;5;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionOutput;97;1483.555,29.20817;Inherit;False;False;-1;Linear
    Density;0;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;68;1530.018,785.9779;Inherit;False;False;-1;Entry
    Point;4;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionOutput;53;1540.078,-734.0724;Inherit;False;False;-1;Does
    Intersect ?;6;False;1;0;INT;0;False;1;INT;0\nWireConnection;25;0;31;0\nWireConnection;132;0;127;0\nWireConnection;132;1;129;0\nWireConnection;28;0;27;0\nWireConnection;28;1;27;0\nWireConnection;11;0;8;0\nWireConnection;40;0;42;0\nWireConnection;40;1;39;0\nWireConnection;41;0;39;0\nWireConnection;41;1;43;0\nWireConnection;162;0;154;0\nWireConnection;37;0;36;0\nWireConnection;17;0;14;0\nWireConnection;167;0;170;0\nWireConnection;167;1;170;0\nWireConnection;49;0;82;0\nWireConnection;49;1;51;0\nWireConnection;30;0;29;0\nWireConnection;30;1;29;0\nWireConnection;9;0;8;0\nWireConnection;9;1;122;0\nWireConnection;45;0;131;0\nWireConnection;12;0;10;0\nWireConnection;4;0;1;0\nWireConnection;26;0;24;0\nWireConnection;26;1;28;0\nWireConnection;129;0;128;0\nWireConnection;129;1;164;0\nWireConnection;24;0;22;0\nWireConnection;24;1;22;0\nWireConnection;51;0;50;0\nWireConnection;51;1;52;0\nWireConnection;82;0;46;0\nWireConnection;31;0;30;0\nWireConnection;31;1;26;0\nWireConnection;62;0;49;0\nWireConnection;46;0;48;0\nWireConnection;46;1;47;0\nWireConnection;173;0;170;0\nWireConnection;172;0;171;0\nWireConnection;172;1;179;0\nWireConnection;171;0;48;0\nWireConnection;171;1;47;0\nWireConnection;10;0;9;0\nWireConnection;39;0;38;0\nWireConnection;139;0;40;0\nWireConnection;20;0;18;0\nWireConnection;20;1;19;0\nWireConnection;36;0;25;0\nWireConnection;44;0;132;0\nWireConnection;130;0;139;0\nWireConnection;130;1;128;0\nWireConnection;21;0;20;0\nWireConnection;131;0;130;0\nWireConnection;131;1;127;0\nWireConnection;151;0;115;0\nWireConnection;60;0;58;0\nWireConnection;60;1;59;0\nWireConnection;60;2;57;0\nWireConnection;60;3;61;0\nWireConnection;83;0;87;0\nWireConnection;83;1;86;0\nWireConnection;83;2;86;0\nWireConnection;83;3;85;0\nWireConnection;176;0;177;0\nWireConnection;176;1;174;0\nWireConnection;176;2;174;0\nWireConnection;176;3;175;0\nWireConnection;14;0;15;0\nWireConnection;14;1;16;0\nWireConnection;122;0;3;0\nWireConnection;168;0;165;0\nWireConnection;168;1;169;0\nWireConnection;163;0;40;0\nWireConnection;163;1;119;0\nWireConnection;163;2;157;0\nWireConnection;163;3;158;0\nWireConnection;170;0;168;0\nWireConnection;164;0;41;0\nWireConnection;164;1;152;0\nWireConnection;115;0;9;0\nWireConnection;92;0;167;0\nWireConnection;165;0;166;0\nWireConnection;165;1;172;0\nWireConnection;154;0;158;0\nWireConnection;154;1;163;0\nWireConnection;114;0;151;0\nWireConnection;152;0;41;0\nWireConnection;152;1;119;0\nWireConnection;5;0;2;0\nWireConnection;64;0;66;0\nWireConnection;64;1;65;0\nWireConnection;64;2;69;0\nWireConnection;64;3;67;0\nWireConnection;32;0;33;0\nWireConnection;32;1;35;0\nWireConnection;32;2;35;0\nWireConnection;32;3;63;0\nWireConnection;94;0;95;0\nWireConnection;94;1;93;0\nWireConnection;94;2;93;0\nWireConnection;94;3;96;0\nWireConnection;70;0;74;0\nWireConnection;70;1;73;0\nWireConnection;70;2;71;0\nWireConnection;70;3;72;0\nWireConnection;0;0;32;0\nWireConnection;88;0;83;0\nWireConnection;178;0;176;0\nWireConnection;75;0;70;0\nWireConnection;97;0;94;0\nWireConnection;68;0;64;0\nWireConnection;53;0;60;0\nASEEND*/\n//CHKSM=135223A3C6FC0ED83F3A513E8F4B8BEA8005F87C"
  m_functionName: 
  m_description: 'Amplify Shader Pack Example


    Computes a volumetric sphere
    and outputs several masks and data.'
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 0
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_customEdited: 0
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 0
  m_headerStyle: 0
  m_headerColor: {r: 1, g: 0.4, b: 0, a: 1}
  m_customNodeCategory: Amplify Shader Pack
  m_previewPosition: 0
  m_hidden: 0
  m_url: 
