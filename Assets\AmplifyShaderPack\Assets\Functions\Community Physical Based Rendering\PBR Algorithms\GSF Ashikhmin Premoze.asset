%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: GSF Ashikhmin Premoze
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor v1.9.4.4\n// Available at the
    Unity Asset Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=19404\nNode;AmplifyShaderEditor.CommentaryNode;36;-1664,1536;Inherit;False;1021.874;428.4466;Math;7;108;107;101;106;216;408;409;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;336;-2817.802,1536;Inherit;False;1095.249;522.9104;Dot
    Products;9;373;339;340;342;341;251;397;413;414;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;409;-1625,1760;Inherit;False;219;138.6666;NdotL;1;105;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;408;-1624,1584;Inherit;False;214;145.6666;NdotV;1;103;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;265;-1664,1232;Inherit;False;677.443;256.8369;GSF
    Ashikhmin Premoze Node;1;45;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;45;-1360,1280;Inherit;False;331.9489;164.641;;;0,0,0,1;The
    Ashikhmin-Premoze GSF is designed for use with Isotropic NDF, unlike the Ashikhmin-Shirley
    approach. As with the Ashikhmin-Shirley, this is a very subtle GSF$$;0;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;106;-1344,1744;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;101;-1344,1632;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;107;-1184,1696;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleDivideOpNode;108;-1024,1632;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.StickyNoteNode;410;-1664,1056;Inherit;False;575.2275;149.5013;Credits;;0,0,0,0.5019608;Method
    by Jordan Stevens$Link: https://www.jordanstevenstechart.com/physically-based-rendering$$Node
    Made by Tiber Legacy$Special Thanks to Dawie;0;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;397;-2016,1600;Inherit;True;NdotV;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;251;-2016,1824;Inherit;True;NdotL;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;341;-2448,1680;Inherit;False;NdotV;1;2;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;342;-2448,1904;Inherit;False;NdotL;1;1;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;340;-2288,1824;Inherit;False;Option;False;0;2;1;In
    0;In 1;Instance;339;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;339;-2288,1600;Inherit;False;Custom
    Dot Products;True;0;2;1;In 0;In 1;Object;-1;10;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;103;-1600,1632;Inherit;False;397;NdotV;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;105;-1600,1808;Inherit;False;251;NdotL;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;373;-2704,1600;Inherit;False;Normal;3;0;False;1;0;FLOAT3;0,0,1;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionNode;413;-2512,1600;Inherit;False;NdotV;-1;;4;65be4ec269a534f4e8867af2f999aaa9;4,27,1,377,0,452,0,451,0;3;230;FLOAT3;0,0,1;False;443;FLOAT3;0,0,0;False;415;FLOAT;1E-37;False;1;FLOAT;23\nNode;AmplifyShaderEditor.FunctionNode;414;-2512,1824;Inherit;False;NdotL;-1;;5;6d7ee1aa8c79587448475fa2b37affc1;5,27,1,381,0,460,0,461,0,463,0;4;230;FLOAT3;0,0,1;False;462;FLOAT3;0,0,0;False;454;FLOAT3;0,0,0;False;416;FLOAT;1E-37;False;1;FLOAT;111\nNode;AmplifyShaderEditor.FunctionOutput;216;-864,1632;Inherit;True;True;-1;Out;0;False;1;0;FLOAT;0;False;1;FLOAT;0\nWireConnection;106;0;105;0\nWireConnection;106;1;103;0\nWireConnection;101;0;103;0\nWireConnection;101;1;105;0\nWireConnection;107;0;106;0\nWireConnection;107;1;101;0\nWireConnection;108;0;101;0\nWireConnection;108;1;107;0\nWireConnection;397;0;339;0\nWireConnection;251;0;340;0\nWireConnection;340;0;414;111\nWireConnection;340;1;342;0\nWireConnection;339;0;413;23\nWireConnection;339;1;341;0\nWireConnection;413;230;373;0\nWireConnection;414;230;373;0\nWireConnection;216;0;108;0\nASEEND*/\n//CHKSM=5F8B22F5772FBC38837B0C61F389FA6BB0576424"
  m_functionName: 
  m_description: 'GSF Ashikhmin Premoze Node

'
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 0
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_customEdited: 0
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 0
  m_headerStyle: 2
  m_headerColor: {r: 0.7529412, g: 0.6784314, b: 0, a: 1}
  m_customNodeCategory: Amplify Shader Pack
  m_previewPosition: 0
  m_hidden: 0
  m_url: 
