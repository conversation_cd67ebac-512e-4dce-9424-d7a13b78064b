using UnityEngine;
using UnityEditor;

namespace Stylo.Cadance
{
    /// <summary>
    /// Simple test for waveform generation to verify the fixes are working.
    /// </summary>
    public static class SimpleWaveformTest
    {
        [MenuItem("Stylo/Cadance/Tools/🔧 Simple Waveform Test")]
        public static void RunSimpleWaveformTest()
        {
            Debug.Log("=== 🔧 SIMPLE WAVEFORM TEST ===");
            
            // Find AudioClips
            string[] audioGuids = AssetDatabase.FindAssets("t:AudioClip");
            
            if (audioGuids.Length == 0)
            {
                Debug.LogError("❌ No AudioClips found in project");
                EditorUtility.DisplayDialog("Waveform Test", "No AudioClips found.\n\nImport some audio files first.", "OK");
                return;
            }
            
            Debug.Log($"📁 Found {audioGuids.Length} AudioClips");
            
            // Test with first AudioClip
            string path = AssetDatabase.GUIDToAssetPath(audioGuids[0]);
            var testClip = AssetDatabase.LoadAssetAtPath<AudioClip>(path);
            
            if (testClip == null)
            {
                Debug.LogError("❌ Failed to load test AudioClip");
                return;
            }
            
            Debug.Log($"🧪 Testing with: {testClip.name}");
            Debug.Log($"   📊 Duration: {testClip.length:F2}s");
            Debug.Log($"   📊 Samples: {testClip.samples}");
            Debug.Log($"   📊 Channels: {testClip.channels}");
            Debug.Log($"   📊 Frequency: {testClip.frequency}Hz");
            
            // Test direct Unity method
            bool success = TestDirectUnityMethod(testClip);
            
            if (success)
            {
                Debug.Log("✅ WAVEFORM GENERATION WORKING!");
                EditorUtility.DisplayDialog("Waveform Test", 
                    $"✅ SUCCESS!\n\nWaveform generation is working for:\n{testClip.name}\n\nYou can now use the Cadance Editor with proper waveform display!", 
                    "Great!");
            }
            else
            {
                Debug.LogError("❌ WAVEFORM GENERATION FAILED");
                EditorUtility.DisplayDialog("Waveform Test", 
                    $"❌ FAILED\n\nWaveform generation failed for:\n{testClip.name}\n\nCheck the console for details.", 
                    "OK");
            }
        }
        
        private static bool TestDirectUnityMethod(AudioClip clip)
        {
            try
            {
                Debug.Log("🔧 Testing direct Unity AudioClip.GetData() method...");
                
                int totalSamples = clip.samples * clip.channels;
                Debug.Log($"   📊 Total samples to read: {totalSamples}");
                
                if (totalSamples > 50000000)
                {
                    Debug.LogWarning("   ⚠️ AudioClip too large for test");
                    return false;
                }
                
                float[] audioData = new float[totalSamples];
                
                bool getDataSuccess = clip.GetData(audioData, 0);
                Debug.Log($"   📊 GetData() success: {getDataSuccess}");
                
                if (!getDataSuccess)
                {
                    Debug.LogWarning("   ⚠️ GetData() returned false");
                    return false;
                }
                
                // Check for actual audio data
                bool hasData = false;
                float maxAmplitude = 0f;
                int nonZeroSamples = 0;
                
                for (int i = 0; i < audioData.Length; i++)
                {
                    float abs = Mathf.Abs(audioData[i]);
                    if (abs > 0.0001f)
                    {
                        hasData = true;
                        nonZeroSamples++;
                        maxAmplitude = Mathf.Max(maxAmplitude, abs);
                    }
                }
                
                Debug.Log($"   📊 Non-zero samples: {nonZeroSamples}/{audioData.Length}");
                Debug.Log($"   📊 Max amplitude: {maxAmplitude:F4}");
                
                if (!hasData)
                {
                    Debug.LogWarning("   ⚠️ No audio data found (silent or empty)");
                    return false;
                }
                
                // Generate test waveform
                int waveformSamples = 1024;
                float[] waveform = new float[waveformSamples];
                int samplesPerPoint = Mathf.Max(1, totalSamples / waveformSamples);
                
                for (int i = 0; i < waveformSamples; i++)
                {
                    float maxInSegment = 0f;
                    int startIdx = i * samplesPerPoint;
                    int endIdx = Mathf.Min(startIdx + samplesPerPoint, totalSamples);
                    
                    for (int j = startIdx; j < endIdx; j += clip.channels)
                    {
                        float channelSum = 0f;
                        for (int ch = 0; ch < clip.channels && j + ch < totalSamples; ch++)
                        {
                            channelSum += audioData[j + ch];
                        }
                        float avgSample = channelSum / clip.channels;
                        maxInSegment = Mathf.Max(maxInSegment, Mathf.Abs(avgSample));
                    }
                    
                    waveform[i] = maxInSegment;
                }
                
                // Validate waveform
                bool waveformHasData = false;
                float waveformMax = 0f;
                int waveformNonZero = 0;
                
                for (int i = 0; i < waveform.Length; i++)
                {
                    if (waveform[i] > 0.0001f)
                    {
                        waveformHasData = true;
                        waveformNonZero++;
                        waveformMax = Mathf.Max(waveformMax, waveform[i]);
                    }
                }
                
                Debug.Log($"   📊 Waveform points with data: {waveformNonZero}/{waveform.Length}");
                Debug.Log($"   📊 Waveform max amplitude: {waveformMax:F4}");
                
                if (waveformHasData)
                {
                    Debug.Log("   ✅ Waveform generation successful!");
                    return true;
                }
                else
                {
                    Debug.LogWarning("   ⚠️ Generated waveform is empty");
                    return false;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"   ❌ Test failed: {ex.Message}");
                return false;
            }
        }
        
        [MenuItem("Stylo/Cadance/Tools/📊 Test All AudioClips")]
        public static void TestAllAudioClips()
        {
            Debug.Log("=== 📊 TESTING ALL AUDIOCLIPS ===");
            
            string[] audioGuids = AssetDatabase.FindAssets("t:AudioClip");
            
            if (audioGuids.Length == 0)
            {
                Debug.LogError("❌ No AudioClips found");
                return;
            }
            
            int successCount = 0;
            int failCount = 0;
            
            for (int i = 0; i < audioGuids.Length; i++)
            {
                string path = AssetDatabase.GUIDToAssetPath(audioGuids[i]);
                var clip = AssetDatabase.LoadAssetAtPath<AudioClip>(path);
                
                if (clip != null)
                {
                    Debug.Log($"🧪 Testing {i + 1}/{audioGuids.Length}: {clip.name}");
                    
                    if (TestDirectUnityMethod(clip))
                    {
                        successCount++;
                        Debug.Log($"   ✅ SUCCESS");
                    }
                    else
                    {
                        failCount++;
                        Debug.Log($"   ❌ FAILED");
                    }
                }
            }
            
            Debug.Log($"=== RESULTS: {successCount} SUCCESS, {failCount} FAILED ===");
            
            string message = $"AudioClip Waveform Test Results:\n\n" +
                           $"✅ Successful: {successCount}\n" +
                           $"❌ Failed: {failCount}\n" +
                           $"📁 Total: {audioGuids.Length}\n\n" +
                           $"Success Rate: {(float)successCount / audioGuids.Length * 100:F1}%";
            
            EditorUtility.DisplayDialog("AudioClip Test Results", message, "OK");
        }
        
        [MenuItem("Stylo/Cadance/Tools/🎵 Open Cadance Editor (Test Ready)")]
        public static void OpenCadanceEditorTestReady()
        {
            Debug.Log("=== 🎵 OPENING CADANCE EDITOR (TEST READY) ===");
            
            try
            {
                var editorType = System.Type.GetType("Stylo.Cadance.CadanceEditorWindow");
                if (editorType != null)
                {
                    var window = EditorWindow.GetWindow(editorType, false, "Cadance Editor - Waveform Fixed");
                    if (window != null)
                    {
                        Debug.Log("✅ Cadance Editor opened");
                        
                        string[] cadanceGuids = AssetDatabase.FindAssets("t:CadanceAsset");
                        string[] audioGuids = AssetDatabase.FindAssets("t:AudioClip");
                        
                        string instructions = $"🎵 Cadance Editor Ready for Testing!\n\n" +
                                            $"Waveform fixes have been applied:\n" +
                                            $"✅ Improved generation methods\n" +
                                            $"✅ Better error handling\n" +
                                            $"✅ Enhanced Unity fallback\n" +
                                            $"✅ Proper timeline scaling\n\n" +
                                            $"Assets found:\n" +
                                            $"📁 {cadanceGuids.Length} CadanceAssets\n" +
                                            $"🎵 {audioGuids.Length} AudioClips\n\n" +
                                            $"Test Instructions:\n" +
                                            $"1. Open a CadanceAsset\n" +
                                            $"2. Check waveform display\n" +
                                            $"3. Test zoom functionality\n" +
                                            $"4. Verify timeline scaling";
                        
                        EditorUtility.DisplayDialog("Cadance Editor Ready", instructions, "Start Testing");
                    }
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"❌ Failed to open editor: {ex.Message}");
            }
        }
    }
}
