%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12001, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: BuildReportWindowDark
  m_EditorClassIdentifier: 
  m_Font: {fileID: 10102, guid: 0000000000000000e000000000000000, type: 0}
  m_box:
    m_Name: box
    m_Normal:
      m_Background: {fileID: 2575269581626704993, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 3
      m_Right: 3
      m_Top: 2
      m_Bottom: 2
    m_Margin:
      m_Left: 4
      m_Right: 4
      m_Top: 4
      m_Bottom: 4
    m_Padding:
      m_Left: 3
      m_Right: 3
      m_Top: 3
      m_Bottom: 3
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 1
    m_WordWrap: 1
    m_RichText: 0
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  m_button:
    m_Name: button
    m_Normal:
      m_Background: {fileID: 2800000, guid: 9e644f611032dfe438662bf693b63531, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 2800000, guid: 4e3a67ec9e85b1a4394acede318d9039, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_OnNormal:
      m_Background: {fileID: 2800000, guid: bd5b2b78eb67c9f4098b0cad7d330df3, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_OnActive:
      m_Background: {fileID: 2800000, guid: 5e2c2fc9f318f504d8486e23c2996945, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_Border:
      m_Left: 6
      m_Right: 6
      m_Top: 4
      m_Bottom: 4
    m_Margin:
      m_Left: 4
      m_Right: 4
      m_Top: 4
      m_Bottom: 4
    m_Padding:
      m_Left: 6
      m_Right: 6
      m_Top: 2
      m_Bottom: 3
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 1
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 0
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  m_toggle:
    m_Name: toggle
    m_Normal:
      m_Background: {fileID: -5335821736339326178, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: -5440736679484030619, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_Focused:
      m_Background: {fileID: 8488476462167042113, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_OnNormal:
      m_Background: {fileID: -6861823045970851139, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: -6998870504307324776, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_OnFocused:
      m_Background: {fileID: -2043049995710802033, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_Border:
      m_Left: 15
      m_Right: 0
      m_Top: 13
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 2
      m_Bottom: 2
    m_Padding:
      m_Left: 5
      m_Right: 5
      m_Top: 2
      m_Bottom: 3
    m_Overflow:
      m_Left: -3
      m_Right: 0
      m_Top: -3
      m_Bottom: 1
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 0
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 15, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  m_label:
    m_Name: label
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 4
      m_Right: 4
      m_Top: 2
      m_Bottom: 2
    m_Padding:
      m_Left: 2
      m_Right: 2
      m_Top: 1
      m_Bottom: 2
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 1
    m_RichText: 1
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  m_textField:
    m_Name: textfield
    m_Normal:
      m_Background: {fileID: 2800000, guid: b6e9a3400cb80844b9b86c56a020568e, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.9019608, g: 0.9019608, b: 0.9019608, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 2800000, guid: 80003ef802d6179429eed4d66bf577d0, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.9019608, g: 0.9019608, b: 0.9019608, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 3
      m_Right: 3
      m_Top: 3
      m_Bottom: 3
    m_Margin:
      m_Left: 4
      m_Right: 4
      m_Top: 2
      m_Bottom: 2
    m_Padding:
      m_Left: 3
      m_Right: 3
      m_Top: 2
      m_Bottom: 3
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 0
    m_TextClipping: 1
    m_ImagePosition: 3
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  m_textArea:
    m_Name: textarea
    m_Normal:
      m_Background: {fileID: 2800000, guid: b6e9a3400cb80844b9b86c56a020568e, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.9019608, g: 0.9019608, b: 0.9019608, a: 1}
    m_Hover:
      m_Background: {fileID: 11026, guid: 0000000000000000e000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.79999995, g: 0.79999995, b: 0.79999995, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 11025, guid: 0000000000000000e000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 4
      m_Right: 4
      m_Top: 4
      m_Bottom: 4
    m_Margin:
      m_Left: 4
      m_Right: 4
      m_Top: 4
      m_Bottom: 4
    m_Padding:
      m_Left: 3
      m_Right: 3
      m_Top: 3
      m_Bottom: 3
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 1
    m_RichText: 1
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  m_window:
    m_Name: window
    m_Normal:
      m_Background: {fileID: 11023, guid: 0000000000000000e000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 11022, guid: 0000000000000000e000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 8
      m_Right: 8
      m_Top: 18
      m_Bottom: 8
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 10
      m_Right: 10
      m_Top: 20
      m_Bottom: 10
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 1
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: -18}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  m_horizontalSlider:
    m_Name: horizontalslider
    m_Normal:
      m_Background: {fileID: 11009, guid: 0000000000000000e000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 3
      m_Right: 3
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 4
      m_Right: 4
      m_Top: 4
      m_Bottom: 4
    m_Padding:
      m_Left: -1
      m_Right: -1
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: -2
      m_Bottom: -3
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 1
    m_ImagePosition: 2
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 12
    m_StretchWidth: 1
    m_StretchHeight: 0
  m_horizontalSliderThumb:
    m_Name: horizontalsliderthumb
    m_Normal:
      m_Background: {fileID: 11011, guid: 0000000000000000e000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 11012, guid: 0000000000000000e000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 11010, guid: 0000000000000000e000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 4
      m_Right: 4
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 7
      m_Right: 7
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: -1
      m_Right: -1
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 1
    m_ImagePosition: 2
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 12
    m_StretchWidth: 1
    m_StretchHeight: 0
  m_verticalSlider:
    m_Name: verticalslider
    m_Normal:
      m_Background: {fileID: 11021, guid: 0000000000000000e000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 3
      m_Bottom: 3
    m_Margin:
      m_Left: 4
      m_Right: 4
      m_Top: 4
      m_Bottom: 4
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: -1
      m_Bottom: -1
    m_Overflow:
      m_Left: -2
      m_Right: -3
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 12
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 1
  m_verticalSliderThumb:
    m_Name: verticalsliderthumb
    m_Normal:
      m_Background: {fileID: 11011, guid: 0000000000000000e000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 11012, guid: 0000000000000000e000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 11010, guid: 0000000000000000e000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 7
      m_Bottom: 7
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: -1
      m_Bottom: -1
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 12
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 1
  m_horizontalScrollbar:
    m_Name: horizontalscrollbar
    m_Normal:
      m_Background: {fileID: 2800000, guid: 154606301c8355545a7251945b2070cd, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 25
      m_Right: 25
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: -1
      m_Right: -1
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 1
      m_Right: 1
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 0
    m_TextClipping: 1
    m_ImagePosition: 2
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 15
    m_StretchWidth: 1
    m_StretchHeight: 0
  m_horizontalScrollbarThumb:
    m_Name: horizontalscrollbarthumb
    m_Normal:
      m_Background: {fileID: 2800000, guid: e333933629c354d41a6c29fc5b52832f, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 8
      m_Right: 8
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 8
      m_Right: 8
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 0
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 15
    m_StretchWidth: 1
    m_StretchHeight: 0
  m_horizontalScrollbarLeftButton:
    m_Name: horizontalscrollbarleftbutton
    m_Normal:
      m_Background: {fileID: 2800000, guid: 54a117b0d87a73743870ba5697db4c7e, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 2800000, guid: 9ad4bd4ead4232f4f9ff61d6a68d0093, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 8
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 0
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 17
    m_FixedHeight: 15
    m_StretchWidth: 1
    m_StretchHeight: 0
  m_horizontalScrollbarRightButton:
    m_Name: horizontalscrollbarrightbutton
    m_Normal:
      m_Background: {fileID: 2800000, guid: cfff64841883793418cdced67a06666b, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 2800000, guid: 5e29d420126aad846b65c11c40e02a54, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 8
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 0
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 17.24739
    m_FixedHeight: 15
    m_StretchWidth: 1
    m_StretchHeight: 0
  m_verticalScrollbar:
    m_Name: verticalscrollbar
    m_Normal:
      m_Background: {fileID: 2800000, guid: 38f65eab78de46e49b1f04de17a4f5cc, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 9
      m_Bottom: 9
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: -1
      m_Bottom: -1
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 1
      m_Bottom: 1
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 0
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 15
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  m_verticalScrollbarThumb:
    m_Name: verticalscrollbarthumb
    m_Normal:
      m_Background: {fileID: 2800000, guid: ad72ae6b5fa68ba4ab16f2b560b97287, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 8
      m_Bottom: 8
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 10
      m_Bottom: 10
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 0
    m_TextClipping: 1
    m_ImagePosition: 2
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 15
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 1
  m_verticalScrollbarUpButton:
    m_Name: verticalscrollbarupbutton
    m_Normal:
      m_Background: {fileID: 2800000, guid: cb67d961c9719794399051c8da95f39b, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 2800000, guid: 04165b0ffe255ce47a434b3e2815daf2, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 8
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 0
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 15
    m_FixedHeight: 17
    m_StretchWidth: 1
    m_StretchHeight: 0
  m_verticalScrollbarDownButton:
    m_Name: verticalscrollbardownbutton
    m_Normal:
      m_Background: {fileID: 2800000, guid: 378141fc61556bb49b1eb4738da928be, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 2800000, guid: 6177c9f88d9604a45a0fececf270b89c, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 8
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 0
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 15
    m_FixedHeight: 17
    m_StretchWidth: 1
    m_StretchHeight: 0
  m_ScrollView:
    m_Name: scrollview
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  m_CustomStyles:
  - m_Name: MiniButton
    m_Normal:
      m_Background: {fileID: 2800000, guid: 9e644f611032dfe438662bf693b63531, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.8113208, g: 0.8113208, b: 0.8113208, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.8117647, g: 0.8117647, b: 0.8117647, a: 1}
    m_Active:
      m_Background: {fileID: 2800000, guid: 4e3a67ec9e85b1a4394acede318d9039, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 2800000, guid: bd5b2b78eb67c9f4098b0cad7d330df3, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.8117647, g: 0.8117647, b: 0.8117647, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 2800000, guid: 5e2c2fc9f318f504d8486e23c2996945, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 4
      m_Right: 4
      m_Top: 4
      m_Bottom: 4
    m_Margin:
      m_Left: 2
      m_Right: 2
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 12
      m_Right: 12
      m_Top: 1
      m_Bottom: 2
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 1
      m_Bottom: 1
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: -1}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: DebugOverlay
    m_Normal:
      m_Background: {fileID: 2800000, guid: c26d7a4f0daeb3b48973a2a5e2ccdcee, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 4
      m_Right: 4
      m_Top: 4
      m_Bottom: 4
    m_Margin:
      m_Left: 2
      m_Right: 2
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 4
      m_Right: 4
      m_Top: 3
      m_Bottom: 3
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: LabelSingleLine
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 4
      m_Right: 4
      m_Top: 4
      m_Bottom: 2
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 1
      m_Bottom: 2
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 3
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: Big1
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.8507463, g: 0.8507463, b: 0.8507463, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 10
      m_Bottom: 4
    m_Padding:
      m_Left: 0
      m_Right: 20
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 24
    m_FontStyle: 1
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: -3, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: TextInfo
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 20
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 16
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: -3, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: TinyHelp
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.5943396, g: 0.5943396, b: 0.5943396, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 3
      m_Bottom: 3
    m_Padding:
      m_Left: 2
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 11
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 1
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: Big2
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 32
    m_FontStyle: 1
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 3, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: Big3
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 17
    m_FontStyle: 1
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 3, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: Title
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 5
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 32
    m_FontStyle: 1
    m_Alignment: 1
    m_WordWrap: 1
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: Subtitle
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 11
    m_FontStyle: 0
    m_Alignment: 1
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: Header3
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 4
      m_Right: 4
      m_Top: 2
      m_Bottom: 2
    m_Padding:
      m_Left: 2
      m_Right: 2
      m_Top: 1
      m_Bottom: 2
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 1
    m_Alignment: 6
    m_WordWrap: 1
    m_RichText: 1
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: Header2
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 4
      m_Right: 4
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 14
    m_FontStyle: 0
    m_Alignment: 6
    m_WordWrap: 1
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: Header2Bold
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 4
      m_Right: 4
      m_Top: 4
      m_Bottom: 4
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 3
      m_Bottom: 3
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 6
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: Text
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 12
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 1
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: TextSelected
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 12
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 1
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: TextNoWrap
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.8207547, g: 0.8207547, b: 0.8207547, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 12
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: Text-Boxed
    m_Normal:
      m_Background: {fileID: 2800000, guid: c26d7a4f0daeb3b48973a2a5e2ccdcee, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 3
      m_Right: 3
      m_Top: 3
      m_Bottom: 3
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 5
      m_Right: 5
      m_Top: 5
      m_Bottom: 5
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 1
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: Tooltip
    m_Normal:
      m_Background: {fileID: 2800000, guid: 828d9b22190dfe746ae229893d0ed85e, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 5
      m_Right: 9
      m_Top: 5
      m_Bottom: 9
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 5
      m_Right: 5
      m_Top: 5
      m_Bottom: 5
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 1
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: TooltipText
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 2
      m_Right: 2
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 1
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: TextField-Search
    m_Normal:
      m_Background: {fileID: 2800000, guid: ec577dc4a2bb0f24b9b056ed15024f75, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.35294116, g: 0.35294116, b: 0.35294116, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 17
      m_Right: 0
      m_Top: 4
      m_Bottom: 4
    m_Margin:
      m_Left: 3
      m_Right: 0
      m_Top: 3
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 1
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 11
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 0
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 17, y: 1}
    m_FixedWidth: 0
    m_FixedHeight: 18
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: TextField-Search-Text
    m_Normal:
      m_Background: {fileID: 2800000, guid: 7bab1b503eb28b7498b376aaef0c4f90, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.11372549, g: 0.11372549, b: 0.11372549, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 4
      m_Bottom: 4
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 3
      m_Bottom: 0
    m_Padding:
      m_Left: 2
      m_Right: 0
      m_Top: 1
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 11
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 0
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 1}
    m_FixedWidth: 0
    m_FixedHeight: 18
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: TextField-Search-DropDown
    m_Normal:
      m_Background: {fileID: 2800000, guid: bdfae28b47e3a5444b4490d930fd7a6f, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.35294116, g: 0.35294116, b: 0.35294116, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 2800000, guid: 8a51a690a4ea0ef4aa9394c0498e35b0, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 2800000, guid: 8a51a690a4ea0ef4aa9394c0498e35b0, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 24
      m_Right: 0
      m_Top: 4
      m_Bottom: 4
    m_Margin:
      m_Left: 3
      m_Right: 0
      m_Top: 3
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 1
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 11
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 0
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 1}
    m_FixedWidth: 24
    m_FixedHeight: 18
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: TextField-Search-ClearButton
    m_Normal:
      m_Background: {fileID: 2800000, guid: 052aa522a7b5fb646897414b72655233, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.35294116, g: 0.35294116, b: 0.35294116, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 2800000, guid: 6f3ea1a6420b7b54ea10732fdfe9eeb5, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 3
      m_Top: 3
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 1
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 11
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 0
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 16
    m_FixedHeight: 18
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: Version
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 5
      m_Top: 5
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 10102, guid: 0000000000000000e000000000000000, type: 0}
    m_FontSize: 11
    m_FontStyle: 0
    m_Alignment: 2
    m_WordWrap: 1
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: TextBold
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.5377358, g: 0.5377358, b: 0.5377358, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 12
    m_FontStyle: 1
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: ToolbarLeft
    m_Normal:
      m_Background: {fileID: 2800000, guid: 5564fea23991ca341baaa53f06cd6137, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 2800000, guid: 9389f88a278c7f54a9af8c869d752946, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 2800000, guid: 3e3c42e12414604469ee077869eaa861, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 2800000, guid: 1f5b00268884dac4881ce0809bfb03fd, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 3
      m_Right: 2
      m_Top: 2
      m_Bottom: 3
    m_Margin:
      m_Left: 3
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 5
      m_Right: 4
      m_Top: 4
      m_Bottom: 4
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 9
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 1
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: ToolbarMiddle
    m_Normal:
      m_Background: {fileID: 2800000, guid: dcf615e454a68004c80c6311b6af330c, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 2800000, guid: 40a207e7a3c635e4ea8088d995280a63, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 2800000, guid: 4fb754b002829ea4eb221879e0ff7222, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 2800000, guid: 54c653dd33cfabc43a14818a9e3941e0, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 2
      m_Right: 2
      m_Top: 2
      m_Bottom: 3
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 4
      m_Right: 6
      m_Top: 4
      m_Bottom: 4
    m_Overflow:
      m_Left: 1
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 9
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 1
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: ToolbarRight
    m_Normal:
      m_Background: {fileID: 2800000, guid: a8aaf08f368747c41ab163ddecb2f42f, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 2800000, guid: 16405c6a4c5289a4888accae2ff48a1a, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 2800000, guid: 82719a8ac678bd041a2749241b274bd7, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 2800000, guid: df298b4295d08e74da0aae66ebc52eca, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 2
      m_Right: 3
      m_Top: 2
      m_Bottom: 3
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 6
      m_Right: 8
      m_Top: 4
      m_Bottom: 4
    m_Overflow:
      m_Left: 2
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 9
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 1
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: RadioLeft
    m_Normal:
      m_Background: {fileID: 2800000, guid: 5564fea23991ca341baaa53f06cd6137, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 2800000, guid: 9389f88a278c7f54a9af8c869d752946, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 2800000, guid: 3e3c42e12414604469ee077869eaa861, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 2800000, guid: 1f5b00268884dac4881ce0809bfb03fd, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 5
      m_Right: 2
      m_Top: 2
      m_Bottom: 2
    m_Margin:
      m_Left: 3
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 5
      m_Right: 4
      m_Top: 4
      m_Bottom: 4
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 11
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 1
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: RadioMiddle
    m_Normal:
      m_Background: {fileID: 2800000, guid: dcf615e454a68004c80c6311b6af330c, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 2800000, guid: 40a207e7a3c635e4ea8088d995280a63, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 2800000, guid: 4fb754b002829ea4eb221879e0ff7222, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 2800000, guid: 54c653dd33cfabc43a14818a9e3941e0, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 2
      m_Right: 2
      m_Top: 2
      m_Bottom: 2
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 4
      m_Right: 6
      m_Top: 4
      m_Bottom: 4
    m_Overflow:
      m_Left: 1
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 11
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 1
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: RadioRight
    m_Normal:
      m_Background: {fileID: 2800000, guid: a8aaf08f368747c41ab163ddecb2f42f, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 2800000, guid: 82719a8ac678bd041a2749241b274bd7, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 2800000, guid: 16405c6a4c5289a4888accae2ff48a1a, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 2800000, guid: df298b4295d08e74da0aae66ebc52eca, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 2
      m_Right: 5
      m_Top: 2
      m_Bottom: 2
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 4
      m_Right: 8
      m_Top: 4
      m_Bottom: 4
    m_Overflow:
      m_Left: 2
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 11
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 1
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: TabLeft
    m_Normal:
      m_Background: {fileID: 2800000, guid: c503b913cf5eeb941b6afd666dc92652, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 2800000, guid: 345e479402e005844bd9ead400648933, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 2800000, guid: 7e99c3d9cea55a44a997e2080c9f5c64, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 2800000, guid: 39c440be84b648d45b27205440fb9df1, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 5
      m_Right: 2
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 3
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 4
      m_Right: 5
      m_Top: 6
      m_Bottom: 2
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: TabMiddle
    m_Normal:
      m_Background: {fileID: 2800000, guid: 06fdbb959fdc16a429a60fdc1b891d6a, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 2800000, guid: dd2206eb6339fb84490319fede1250fa, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 2800000, guid: a386b30514769604280a79563c307e67, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 2800000, guid: 950c8f6862de1894985c9deeccb44b1c, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 5
      m_Right: 2
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 6
      m_Bottom: 2
    m_Overflow:
      m_Left: 1
      m_Right: 1
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: TabRight
    m_Normal:
      m_Background: {fileID: 2800000, guid: dd49c0bbf3671e547ab1813b9c2e2d03, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 2800000, guid: c41881b7118ba9c449d4ded5d0a33c60, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 2800000, guid: 23ff8206be541ce4b95aabb591360f64, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 2800000, guid: 8f965e743ad46af4881be1810e3ee6f8, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 2
      m_Right: 5
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 3
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 4
      m_Right: 7
      m_Top: 6
      m_Bottom: 2
    m_Overflow:
      m_Left: 2
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: StatusBarBg
    m_Normal:
      m_Background: {fileID: 2800000, guid: 1ca5533c69eb2f54aa409c5fcf41e1ca, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 2
      m_Right: 2
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 7
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 10102, guid: 0000000000000000e000000000000000, type: 0}
    m_FontSize: 12
    m_FontStyle: 0
    m_Alignment: 3
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 4, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 1
  - m_Name: StatusBarLabel
    m_Normal:
      m_Background: {fileID: 2800000, guid: 1ca5533c69eb2f54aa409c5fcf41e1ca, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 2
      m_Right: 2
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 5
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 10102, guid: 0000000000000000e000000000000000, type: 0}
    m_FontSize: 12
    m_FontStyle: 0
    m_Alignment: 3
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 4, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 1
  - m_Name: TopBarBg
    m_Normal:
      m_Background: {fileID: 2800000, guid: 781d5fa17ba67b343a0f1f5fc4b890d7, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 9999
      m_Right: 9999
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 10
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 23
    m_StretchWidth: 1
    m_StretchHeight: 1
  - m_Name: TopBarLabel
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 4
      m_Right: 4
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 5
      m_Right: 3
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 10
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 1
  - m_Name: TopBarButton
    m_Normal:
      m_Background: {fileID: 2800000, guid: f0e3c8e28e40b5249870c3e3db685051, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 2800000, guid: e6e477e69bde1144b823fe621ddc1090, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 2800000, guid: a386b30514769604280a79563c307e67, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 2800000, guid: 950c8f6862de1894985c9deeccb44b1c, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 4
      m_Right: 4
      m_Top: 4
      m_Bottom: 4
    m_Margin:
      m_Left: -1
      m_Right: -1
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 5
      m_Right: 5
      m_Top: 3
      m_Bottom: 3
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 11
    m_FontStyle: 0
    m_Alignment: 3
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 1
  - m_Name: TopBarPopup
    m_Normal:
      m_Background: {fileID: 2800000, guid: 3d4ab15594af81345a2559291a937b8b, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 2800000, guid: a95cb67892caf7f48ad04ba26484fb9f, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 4
      m_Right: 10
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 5
      m_Right: 5
      m_Top: 3
      m_Bottom: 3
    m_Overflow:
      m_Left: 0
      m_Right: 5
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 10
    m_FontStyle: 0
    m_Alignment: 3
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 1
  - m_Name: ListColumnHeader
    m_Normal:
      m_Background: {fileID: 2800000, guid: d991b78680057d642bd61c8f7601baf4, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 2800000, guid: f322b44ed25d78045bed6966ac9c997b, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 4
      m_Right: 4
      m_Top: 2
      m_Bottom: 2
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 5
      m_Right: 0
      m_Top: 3
      m_Bottom: 3
    m_Overflow:
      m_Left: 0
      m_Right: 2
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 11
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: ListColumnHeader-Asc
    m_Normal:
      m_Background: {fileID: 2800000, guid: 9309bddb79ba8544eb740260186ab1ff, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 2800000, guid: a6d9bf1d86df4f444a040eb60dd38422, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 2800000, guid: 59fd6b4691d8a154ca5f24122b2183d3, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 2800000, guid: 163d65b61ac37404f9bea771fe477056, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 2
      m_Right: 15
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 5
      m_Right: 0
      m_Top: 3
      m_Bottom: 3
    m_Overflow:
      m_Left: 0
      m_Right: 2
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 11
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 1
    m_RichText: 1
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: ListColumnHeader-Desc
    m_Normal:
      m_Background: {fileID: 2800000, guid: 6afa125853d8a0d4abce452dfdcb9e27, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 2800000, guid: 44fa44c27de6a1d4c9a08c8aa141ffb2, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 2800000, guid: 98e9c8080c5a37048be6f3f4b95f0ff8, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 2800000, guid: 2ee7fd8754b16f24fb5755637e60f925, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 2
      m_Right: 15
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 5
      m_Right: 0
      m_Top: 3
      m_Bottom: 3
    m_Overflow:
      m_Left: 0
      m_Right: 2
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 11
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 1
    m_RichText: 1
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: List
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.46323532, g: 0.46323532, b: 0.46323532, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 2
      m_Right: 2
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 11
    m_FontStyle: 0
    m_Alignment: 3
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: ListNormal
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 5
      m_Right: 5
      m_Top: 5
      m_Bottom: 5
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 3
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: ListIcon
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: ListAlt
    m_Normal:
      m_Background: {fileID: 2800000, guid: b14e1cc735bc0462e9ce75f75a0626da, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.46323532, g: 0.46323532, b: 0.46323532, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 2
      m_Right: 2
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 11
    m_FontStyle: 0
    m_Alignment: 3
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: ListAltNormal
    m_Normal:
      m_Background: {fileID: 2800000, guid: b14e1cc735bc0462e9ce75f75a0626da, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 5
      m_Right: 5
      m_Top: 5
      m_Bottom: 5
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 3
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: ListAltNormalSelected
    m_Normal:
      m_Background: {fileID: 2800000, guid: 572c0c87dd48b624bad4d6289300c765, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 5
      m_Right: 5
      m_Top: 5
      m_Bottom: 5
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 3
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: ListAltIcon
    m_Normal:
      m_Background: {fileID: 2800000, guid: b14e1cc735bc0462e9ce75f75a0626da, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: ListAltSelected
    m_Normal:
      m_Background: {fileID: 2800000, guid: 572c0c87dd48b624bad4d6289300c765, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.88235295, g: 0.88235295, b: 0.88235295, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 2
      m_Right: 2
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 11
    m_FontStyle: 0
    m_Alignment: 3
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: ListButton
    m_Normal:
      m_Background: {fileID: 2800000, guid: f0e3c8e28e40b5249870c3e3db685051, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 2800000, guid: e6e477e69bde1144b823fe621ddc1090, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.88235295, g: 0.88235295, b: 0.88235295, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 2
      m_Right: 2
      m_Top: 3
      m_Bottom: 3
    m_Margin:
      m_Left: 1
      m_Right: 1
      m_Top: 1
      m_Bottom: 1
    m_Padding:
      m_Left: 3
      m_Right: 3
      m_Top: 2
      m_Bottom: 3
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: ListButtonRadio
    m_Normal:
      m_Background: {fileID: 2800000, guid: f0e3c8e28e40b5249870c3e3db685051, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 2800000, guid: e6e477e69bde1144b823fe621ddc1090, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 2800000, guid: f322b44ed25d78045bed6966ac9c997b, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.88235295, g: 0.88235295, b: 0.88235295, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 2
      m_Right: 2
      m_Top: 3
      m_Bottom: 3
    m_Margin:
      m_Left: 1
      m_Right: 1
      m_Top: 2
      m_Bottom: 0
    m_Padding:
      m_Left: 3
      m_Right: 3
      m_Top: 1
      m_Bottom: 2
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 10
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: ExpandButton
    m_Normal:
      m_Background: {fileID: 2800000, guid: 09d370cb92bf7ca4f985a950cbf7acfa, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: 2800000, guid: e4e412fdf0bbf8647bc7175f88e05bb2, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 2800000, guid: 2fba41542a4c0e440b7fae310b98e6b7, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 2800000, guid: 8dcaefb813272534f925c2cc1be37b69, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 12
      m_Right: 12
      m_Top: 2
      m_Bottom: 2
    m_Margin:
      m_Left: 1
      m_Right: 1
      m_Top: 2
      m_Bottom: 0
    m_Padding:
      m_Left: 14
      m_Right: 14
      m_Top: 0
      m_Bottom: 2
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 10
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 15
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: ButtonAlreadyPressed
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 4
      m_Right: 4
      m_Top: 4
      m_Bottom: 4
    m_Margin:
      m_Left: 2
      m_Right: 2
      m_Top: 2
      m_Bottom: 2
    m_Padding:
      m_Left: 8
      m_Right: 8
      m_Top: 4
      m_Bottom: 4
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: Popup
    m_Normal:
      m_Background: {fileID: 2800000, guid: 6afa125853d8a0d4abce452dfdcb9e27, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Hover:
      m_Background: {fileID: 2800000, guid: 6afa125853d8a0d4abce452dfdcb9e27, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 2800000, guid: 44fa44c27de6a1d4c9a08c8aa141ffb2, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.46323532, g: 0.46323532, b: 0.46323532, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 4
      m_Right: 15
      m_Top: 4
      m_Bottom: 4
    m_Margin:
      m_Left: 2
      m_Right: 2
      m_Top: 5
      m_Bottom: 5
    m_Padding:
      m_Left: 3
      m_Right: 3
      m_Top: 4
      m_Bottom: 4
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 4
      m_Bottom: -2
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: -4}
    m_FixedWidth: 0
    m_FixedHeight: 18
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: PopupPanel
    m_Normal:
      m_Background: {fileID: 2800000, guid: 398adf16755d0d34682292b57a94786e, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.46323532, g: 0.46323532, b: 0.46323532, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 9
      m_Right: 9
      m_Top: 4
      m_Bottom: 11
    m_Margin:
      m_Left: 5
      m_Right: 5
      m_Top: 5
      m_Bottom: 5
    m_Padding:
      m_Left: 5
      m_Right: 5
      m_Top: 5
      m_Bottom: 5
    m_Overflow:
      m_Left: 3
      m_Right: 0
      m_Top: 0
      m_Bottom: 5
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: ButtonHasContents
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 4
      m_Right: 4
      m_Top: 4
      m_Bottom: 4
    m_Margin:
      m_Left: 2
      m_Right: 2
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 4
      m_Right: 4
      m_Top: 3
      m_Bottom: 3
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: ButtonNoContents
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 4
      m_Right: 4
      m_Top: 4
      m_Bottom: 4
    m_Margin:
      m_Left: 2
      m_Right: 2
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 4
      m_Right: 4
      m_Top: 3
      m_Bottom: 3
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: AssetInfoPanel
    m_Normal:
      m_Background: {fileID: 2800000, guid: d0285fedba859c74689e8eba27e5297b, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 4
      m_Right: 4
      m_Top: 4
      m_Bottom: 4
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 2
      m_Right: 2
      m_Top: 2
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 3
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: AssetInfoPanelToolbarTop
    m_Normal:
      m_Background: {fileID: 2800000, guid: d0285fedba859c74689e8eba27e5297b, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 4
      m_Right: 4
      m_Top: 4
      m_Bottom: 4
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 2
      m_Right: 2
      m_Top: 2
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: AssetInfoPanelToolbarTopAllList
    m_Normal:
      m_Background: {fileID: 2800000, guid: d0285fedba859c74689e8eba27e5297b, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 4
      m_Right: 4
      m_Top: 4
      m_Bottom: 4
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 2
      m_Right: 2
      m_Top: 4
      m_Bottom: 2
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: AssetInfoPanelNoList
    m_Normal:
      m_Background: {fileID: 2800000, guid: d0285fedba859c74689e8eba27e5297b, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 4
      m_Right: 4
      m_Top: 4
      m_Bottom: 4
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 2
      m_Right: 2
      m_Top: 5
      m_Bottom: 5
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: IndentStyle1
    m_Normal:
      m_Background: {fileID: 2800000, guid: a68bd5f397e1f2a4ab605aa35026ca0d, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 2
      m_Right: 5
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 3
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 4
      m_Right: 7
      m_Top: 6
      m_Bottom: 2
    m_Overflow:
      m_Left: 2
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: ProjectSettingsGroup
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 0
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: Asset
    m_Normal:
      m_Background: {fileID: 2800000, guid: 1d35c6f19492f7147a4fb89911480ab2, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 2800000, guid: c26d7a4f0daeb3b48973a2a5e2ccdcee, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 4
      m_Right: 4
      m_Top: 4
      m_Bottom: 4
    m_Margin:
      m_Left: 2
      m_Right: 2
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 4
      m_Right: 6
      m_Top: 3
      m_Bottom: 3
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: AssetHovered
    m_Normal:
      m_Background: {fileID: 2800000, guid: 862200d03ab47bb4fbe67878d074f7eb, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Hover:
      m_Background: {fileID: 2800000, guid: 862200d03ab47bb4fbe67878d074f7eb, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 2800000, guid: c26d7a4f0daeb3b48973a2a5e2ccdcee, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 4
      m_Right: 4
      m_Top: 4
      m_Bottom: 4
    m_Margin:
      m_Left: 2
      m_Right: 2
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 4
      m_Right: 6
      m_Top: 3
      m_Bottom: 3
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: AssetUsageArrow
    m_Normal:
      m_Background: {fileID: 2800000, guid: 356851a1da2c40c4682114040e12128f, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Hover:
      m_Background: {fileID: 2800000, guid: 862200d03ab47bb4fbe67878d074f7eb, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 2800000, guid: c26d7a4f0daeb3b48973a2a5e2ccdcee, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 9
    m_FixedHeight: 11
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: IconValidity
    m_Normal:
      m_Background: {fileID: 2800000, guid: 8c49083ee1fe30f498dd7298cd26d01d, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Hover:
      m_Background: {fileID: 2800000, guid: b900a745cfa24cb48b7e50457028a2f8, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 0
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: IconHovered
    m_Normal:
      m_Background: {fileID: 2800000, guid: 4e634b8baf8e5f64f8a3026d6443f2f3, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 2800000, guid: c26d7a4f0daeb3b48973a2a5e2ccdcee, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 3
      m_Right: 3
      m_Top: 3
      m_Bottom: 3
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: HiddenScrollbar
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 0}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: HiddenScrollbarthumb
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 0}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: HiddenScrollbarupbutton
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 0}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: HiddenScrollbardownbutton
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 0}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: HiddenScrollbarleftbutton
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 0}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: HiddenScrollbarrightbutton
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 0}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: DrawTexture
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 0}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: Icon-ArrowBig-Left
    m_Normal:
      m_Background: {fileID: 2800000, guid: 8d9e22fc216ec7040951b3fc639fee65, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 0}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: Icon-ArrowBig-Right
    m_Normal:
      m_Background: {fileID: 2800000, guid: 356851a1da2c40c4682114040e12128f, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 0}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: Icon-Toolbar-Log
    m_Normal:
      m_Background: {fileID: 2800000, guid: 315f41d9a48042544ad8ee904a85489a, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 0}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: Icon-Toolbar-Open
    m_Normal:
      m_Background: {fileID: 2800000, guid: 4f37f55e356d36541b6b6c98cd6f9552, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 0}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: Icon-Toolbar-Save
    m_Normal:
      m_Background: {fileID: 2800000, guid: 544efce9a6412b842bdc5f70c3b8c6e5, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 0}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: Icon-Toolbar-Options
    m_Normal:
      m_Background: {fileID: 2800000, guid: 45696119a0f085748a79d353c96bf81e, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 0}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: Icon-Toolbar-Help
    m_Normal:
      m_Background: {fileID: 2800000, guid: dce2f5f611fdaaa48aa3c56b70fede18, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 0}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: Icon-Toolbar-Column
    m_Normal:
      m_Background: {fileID: 2800000, guid: b1ff0ab72e5d25f41be81b7470928d76, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 0}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: Icon-Warning
    m_Normal:
      m_Background: {fileID: 2800000, guid: 78babf560b3543043bce62a6d7d5edfc, type: 3}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 0}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 0
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 30
    m_FixedHeight: 30
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: GUIEditor.BreadcrumbLeft
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.705, g: 0.705, b: 0.705, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.705, g: 0.705, b: 0.705, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.705, g: 0.705, b: 0.705, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.705, g: 0.705, b: 0.705, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.705, g: 0.705, b: 0.705, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.705, g: 0.705, b: 0.705, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.705, g: 0.705, b: 0.705, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.705, g: 0.705, b: 0.705, a: 1}
    m_Border:
      m_Left: 3
      m_Right: 10
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 5
      m_Right: 11
      m_Top: 2
      m_Bottom: 2
    m_Overflow:
      m_Left: 6
      m_Right: 7
      m_Top: 1
      m_Bottom: 1
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 19
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: GUIEditor.BreadcrumbMid
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.705, g: 0.705, b: 0.705, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.705, g: 0.705, b: 0.705, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.705, g: 0.705, b: 0.705, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.705, g: 0.705, b: 0.705, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.705, g: 0.705, b: 0.705, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.705, g: 0.705, b: 0.705, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.705, g: 0.705, b: 0.705, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.705, g: 0.705, b: 0.705, a: 1}
    m_Border:
      m_Left: 10
      m_Right: 10
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 10
      m_Right: 10
      m_Top: 2
      m_Bottom: 2
    m_Overflow:
      m_Left: 5
      m_Right: 7
      m_Top: 1
      m_Bottom: 1
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 19
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: RL DragHandle
    m_Normal:
      m_Background: {fileID: -6754115436749502611, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds:
      - {fileID: 0}
      m_TextColor: {r: 0.043137256, g: 0.043137256, b: 0.043137256, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.705, g: 0.705, b: 0.705, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.705, g: 0.705, b: 0.705, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 6
      m_Right: 6
      m_Top: 6
      m_Bottom: 6
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 1
    m_Alignment: 7
    m_WordWrap: 0
    m_RichText: 0
    m_TextClipping: 1
    m_ImagePosition: 3
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 6
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: RL Header
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.8235294, g: 0.8235294, b: 0.8235294, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.8235294, g: 0.8235294, b: 0.8235294, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.8235294, g: 0.8235294, b: 0.8235294, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.8235294, g: 0.8235294, b: 0.8235294, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.8235294, g: 0.8235294, b: 0.8235294, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.8235294, g: 0.8235294, b: 0.8235294, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.705, g: 0.705, b: 0.705, a: 1}
    m_Border:
      m_Left: 4
      m_Right: 4
      m_Top: 3
      m_Bottom: 2
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 1
    m_WordWrap: 0
    m_RichText: 0
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: -1}
    m_FixedWidth: 0
    m_FixedHeight: 20
    m_StretchWidth: 0
    m_StretchHeight: 0
  - m_Name: RL Footer
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.8235294, g: 0.8235294, b: 0.8235294, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.8235294, g: 0.8235294, b: 0.8235294, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.8235294, g: 0.8235294, b: 0.8235294, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.8235294, g: 0.8235294, b: 0.8235294, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.8235294, g: 0.8235294, b: 0.8235294, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.8235294, g: 0.8235294, b: 0.8235294, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.1019608, g: 0.1019608, b: 0.1019608, a: 1}
    m_Border:
      m_Left: 4
      m_Right: 4
      m_Top: 3
      m_Bottom: 4
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 3
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 1
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 0
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 20
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: RL Background
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.8235294, g: 0.8235294, b: 0.8235294, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.8235294, g: 0.8235294, b: 0.8235294, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.8235294, g: 0.8235294, b: 0.8235294, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.705, g: 0.705, b: 0.705, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.8235294, g: 0.8235294, b: 0.8235294, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.8235294, g: 0.8235294, b: 0.8235294, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.8235294, g: 0.8235294, b: 0.8235294, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.705, g: 0.705, b: 0.705, a: 1}
    m_Border:
      m_Left: 6
      m_Right: 3
      m_Top: 0
      m_Bottom: 6
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 0
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 1
  - m_Name: RL FooterButton
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.8235294, g: 0.8235294, b: 0.8235294, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.8235294, g: 0.8235294, b: 0.8235294, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.8235294, g: 0.8235294, b: 0.8235294, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.8235294, g: 0.8235294, b: 0.8235294, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.8235294, g: 0.8235294, b: 0.8235294, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.8235294, g: 0.8235294, b: 0.8235294, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.1019608, g: 0.1019608, b: 0.1019608, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: -3
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 1
    m_Alignment: 4
    m_WordWrap: 0
    m_RichText: 0
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 16
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: RL Element
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.8235294, g: 0.8235294, b: 0.8235294, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.8235294, g: 0.8235294, b: 0.8235294, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.8235294, g: 0.8235294, b: 0.8235294, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 1, g: 1, b: 1, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.8235294, g: 0.8235294, b: 0.8235294, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.8235294, g: 0.8235294, b: 0.8235294, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.8235294, g: 0.8235294, b: 0.8235294, a: 1}
    m_OnFocused:
      m_Background: {fileID: 7516798493847391269, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 2
      m_Right: 3
      m_Top: 0
      m_Bottom: 3
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 0
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 1
  - m_Name: LogMessageIcons
    m_Normal:
      m_Background: {fileID: 5425037494185492166, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Hover:
      m_Background: {fileID: -5763820162405496800, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Active:
      m_Background: {fileID: -2005373149481181617, guid: 0000000000000000d000000000000000, type: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_Border:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 0
    m_FontStyle: 0
    m_Alignment: 0
    m_WordWrap: 0
    m_RichText: 1
    m_TextClipping: 0
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: 0}
    m_FixedWidth: 0
    m_FixedHeight: 0
    m_StretchWidth: 1
    m_StretchHeight: 0
  - m_Name: RL Empty Header
    m_Normal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.8235294, g: 0.8235294, b: 0.8235294, a: 1}
    m_Hover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.8235294, g: 0.8235294, b: 0.8235294, a: 1}
    m_Active:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.8235294, g: 0.8235294, b: 0.8235294, a: 1}
    m_Focused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0, g: 0, b: 0, a: 1}
    m_OnNormal:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.8235294, g: 0.8235294, b: 0.8235294, a: 1}
    m_OnHover:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.8235294, g: 0.8235294, b: 0.8235294, a: 1}
    m_OnActive:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.8235294, g: 0.8235294, b: 0.8235294, a: 1}
    m_OnFocused:
      m_Background: {fileID: 0}
      m_ScaledBackgrounds: []
      m_TextColor: {r: 0.7058824, g: 0.7058824, b: 0.7058824, a: 1}
    m_Border:
      m_Left: 4
      m_Right: 4
      m_Top: 3
      m_Bottom: 2
    m_Margin:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Padding:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Overflow:
      m_Left: 0
      m_Right: 0
      m_Top: 0
      m_Bottom: 0
    m_Font: {fileID: 0}
    m_FontSize: 10
    m_FontStyle: 0
    m_Alignment: 1
    m_WordWrap: 0
    m_RichText: 0
    m_TextClipping: 1
    m_ImagePosition: 0
    m_ContentOffset: {x: 0, y: -1}
    m_FixedWidth: 0
    m_FixedHeight: 20
    m_StretchWidth: 0
    m_StretchHeight: 0
  m_Settings:
    m_DoubleClickSelectsWord: 1
    m_TripleClickSelectsLine: 1
    m_CursorColor: {r: 0.34117648, g: 0.34117648, b: 0.34117648, a: 1}
    m_CursorFlashSpeed: -1
    m_SelectionColor: {r: 0.23921569, g: 0.49803922, b: 0.87058824, a: 0.69803923}
