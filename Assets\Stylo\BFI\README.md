# Stylo BFI (Black Frame Insertion)

## Overview

Black Frame Insertion (BFI) is a motion blur reduction technique that improves motion clarity by inserting black frames between rendered frames. This creates a strobing effect similar to CRT displays, significantly reducing motion blur at the cost of reduced brightness.

## How BFI Works

1. **Frame Rate Multiplication**: The display refresh rate is increased (e.g., 60Hz → 120Hz)
2. **Black Frame Insertion**: Black frames are inserted between actual rendered frames
3. **Brightness Compensation**: Visible frames are brightened to compensate for the darkness
4. **Motion Clarity**: The strobing effect reduces persistence blur, creating sharper motion

## BFI Modes

- **Off**: No BFI effect
- **2x Frame Rate**: 60Hz → 120Hz (1 black frame per rendered frame)
- **3x Frame Rate**: 60Hz → 180Hz (2 black frames per rendered frame)  
- **4x Frame Rate**: 60Hz → 240Hz (3 black frames per rendered frame)

## Key Parameters

### Master Controls
- **Mode**: BFI frame rate multiplier
- **Intensity**: Overall effect strength (0-1)

### Brightness & Compensation
- **Brightness Compensation**: Automatic brightness boost (1-3x)
- **Manual Brightness**: Additional brightness control (0.5-2x)
- **Gamma Correction**: Gamma adjustment for brightness compensation

### Timing & Synchronization
- **Black Frame Duration**: How long black frames are displayed
- **Phase Offset**: Timing offset for BFI cycle
- **Sync To Refresh Rate**: Synchronize with display refresh rate

### Flicker Reduction
- **Flicker Reduction Mode**: None/Low/Medium/High
- **Temporal Smoothing**: Reduces flicker through temporal filtering
- **Edge Preservation**: Maintains sharp edges during smoothing

### Motion Adaptive
- **Motion Adaptive**: Enable motion-based BFI intensity
- **Motion Sensitivity**: How responsive to motion changes
- **Motion Threshold**: Minimum motion to trigger BFI
- **Adaptation Speed**: How quickly BFI adapts to motion

### Advanced Settings
- **Dithering Strength**: Reduces banding artifacts
- **Subpixel Precision**: Frame timing precision
- **Debug Visualization**: Shows BFI timing and motion data

## Display Requirements

For optimal results, your display should support the target refresh rate:
- **2x Mode**: 120Hz display
- **3x Mode**: 180Hz display  
- **4x Mode**: 240Hz display

BFI will work on lower refresh rate displays but may not provide the full motion clarity benefits.

## Performance Considerations

- **GPU Load**: Minimal additional GPU overhead
- **Frame Timing**: Requires precise frame timing control
- **Brightness**: Inherent brightness reduction (compensated automatically)
- **Flicker**: May cause eye strain for sensitive users

## Integration

BFI integrates with the Stylo framework and URP render pipeline:

1. Add `BFIRendererFeature` to your URP Renderer Data
2. Configure BFI parameters via Volume Profile
3. Ensure your display supports the target refresh rate
4. Adjust brightness compensation as needed

## Best Practices

1. **Start with 2x Mode**: Begin with double frame rate for best compatibility
2. **Adjust Brightness**: Fine-tune compensation for your display
3. **Enable Flicker Reduction**: Use Medium setting for most users
4. **Motion Adaptive**: Enable for dynamic scenes with varying motion
5. **Test Performance**: Monitor frame timing stability

## Troubleshooting

### No Visible Effect
- Check that BFI Mode is not set to "Off"
- Verify Intensity is greater than 0
- Ensure your display supports the target refresh rate

### Too Much Flicker
- Increase Flicker Reduction Mode
- Adjust Temporal Smoothing
- Lower the BFI Mode (e.g., 4x → 2x)

### Too Dark
- Increase Brightness Compensation
- Adjust Manual Brightness
- Modify Gamma Correction

### Performance Issues
- Lower BFI Mode
- Disable Motion Adaptive features
- Reduce Subpixel Precision

## Technical Implementation

The BFI system uses Unity 6's Render Graph to:
- Calculate frame timing and BFI phase
- Determine when to render black frames vs. compensated frames
- Apply brightness compensation with gamma correction
- Implement motion-adaptive intensity control
- Provide debug visualization for timing analysis

## Compatibility

- **Unity Version**: Unity 6.0+
- **Render Pipeline**: Universal Render Pipeline (URP)
- **Platform**: All platforms supporting URP
- **VR**: Compatible with VR rendering
