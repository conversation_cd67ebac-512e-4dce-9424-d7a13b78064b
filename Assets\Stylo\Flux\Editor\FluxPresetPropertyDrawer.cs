#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using Stylo.Flux;
#if URP_INSTALLED
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
#endif

namespace Stylo.Flux.Editor
{
    /// <summary>
    /// Custom property drawer for FluxPreset that enables drag-and-drop application
    /// </summary>
    [CustomPropertyDrawer(typeof(FluxPreset))]
    public class FluxPresetPropertyDrawer : PropertyDrawer
    {
        public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
        {
            EditorGUI.BeginProperty(position, label, property);

            // Draw the object field
            Rect objectFieldRect = new Rect(position.x, position.y, position.width - 60, position.height);
            FluxPreset preset = (FluxPreset)EditorGUI.ObjectField(objectFieldRect, label, property.objectReferenceValue, typeof(FluxPreset), false);

            if (preset != property.objectReferenceValue)
            {
                property.objectReferenceValue = preset;
            }

            // Draw apply button if preset is assigned
            if (preset != null)
            {
                Rect buttonRect = new Rect(position.x + position.width - 55, position.y, 55, position.height);
                if (GUI.Button(buttonRect, "Apply"))
                {
                    ApplyPresetToTarget(preset, property);
                }
            }

            EditorGUI.EndProperty();
        }

        private void ApplyPresetToTarget(FluxPreset preset, SerializedProperty property)
        {
            // Get the target object
            Object targetObject = property.serializedObject.targetObject;

            // Try to apply to different target types
#if URP_INSTALLED
            if (targetObject is VolumeProfile volumeProfile)
            {
                ApplyPresetToVolumeProfile(preset, volumeProfile);
                EditorUtility.DisplayDialog("Preset Applied",
                    $"Flux preset '{preset.GetDisplayName()}' applied to Volume Profile.", "OK");
            }
            else if (targetObject is Volume volume && volume.profile != null)
            {
                ApplyPresetToVolumeProfile(preset, volume.profile);
                EditorUtility.DisplayDialog("Preset Applied",
                    $"Flux preset '{preset.GetDisplayName()}' applied to Volume's profile.", "OK");
            }
            else
#endif
            {
                EditorUtility.DisplayDialog("Cannot Apply Preset",
                    "Preset can only be applied to Volume Profiles or Volumes with URP.", "OK");
            }
        }

#if URP_INSTALLED
        private void ApplyPresetToVolumeProfile(FluxPreset preset, VolumeProfile volumeProfile)
        {
            if (volumeProfile == null) return;

            // Get or add Flux effect to the volume profile
            if (volumeProfile.TryGet<Stylo.Flux.Universal.FluxEffect>(out var existingFlux))
            {
                // Apply to existing effect
                Undo.RecordObject(volumeProfile, "Apply Flux Preset");
                preset.ApplyToURP(existingFlux);
                EditorUtility.SetDirty(volumeProfile);
            }
            else
            {
                // Add new Flux effect and apply preset
                Undo.RecordObject(volumeProfile, "Add Flux Effect and Apply Preset");
                var newFlux = volumeProfile.Add<Stylo.Flux.Universal.FluxEffect>();
                preset.ApplyToURP(newFlux);
                EditorUtility.SetDirty(volumeProfile);
            }
        }
#endif
    }

    /// <summary>
    /// Custom editor for Volume Profile that shows Flux preset application
    /// </summary>
#if URP_INSTALLED
    [CustomEditor(typeof(VolumeProfile))]
    public class VolumeProfileFluxEditor : UnityEditor.Editor
    {
        private FluxPreset? selectedPreset;

        public override void OnInspectorGUI()
        {
            // Draw default inspector
            DrawDefaultInspector();

            VolumeProfile volumeProfile = (VolumeProfile)target;

            EditorGUILayout.Space(10);
            EditorGUILayout.LabelField("Flux Preset Application", EditorStyles.boldLabel);

            EditorGUILayout.BeginHorizontal("box");

            // Preset selection field
            selectedPreset = (FluxPreset)EditorGUILayout.ObjectField("Flux Preset", selectedPreset, typeof(FluxPreset), false);

            // Apply button
            GUI.enabled = selectedPreset != null;
            if (GUILayout.Button("Apply", GUILayout.Width(60)) && selectedPreset != null)
            {
                ApplyPresetToVolumeProfile(selectedPreset, volumeProfile);
                EditorUtility.DisplayDialog("Preset Applied",
                    $"Flux preset '{selectedPreset.GetDisplayName()}' applied to Volume Profile.", "OK");
            }
            GUI.enabled = true;

            EditorGUILayout.EndHorizontal();

            // Show current Flux effect status
            if (volumeProfile.TryGet<Stylo.Flux.Universal.FluxEffect>(out var fluxEffect))
            {
                EditorGUILayout.Space(5);
                EditorGUILayout.HelpBox($"Flux Effect is present in this Volume Profile.\nEffect Intensity: {fluxEffect.EffectIntensity.value:F2}", MessageType.Info);

                // Quick create preset from current settings
                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button("Create Preset from Current Settings"))
                {
                    CreatePresetFromCurrentSettings(volumeProfile);
                }
                EditorGUILayout.EndHorizontal();
            }
            else
            {
                EditorGUILayout.Space(5);
                EditorGUILayout.HelpBox("No Flux Effect in this Volume Profile. Applying a preset will add one.", MessageType.Info);
            }
        }

        private void ApplyPresetToVolumeProfile(FluxPreset preset, VolumeProfile volumeProfile)
        {
            if (volumeProfile == null || preset == null) return;

            // Get or add Flux effect to the volume profile
            if (volumeProfile.TryGet<Stylo.Flux.Universal.FluxEffect>(out var existingFlux))
            {
                // Apply to existing effect
                Undo.RecordObject(volumeProfile, "Apply Flux Preset");
                preset.ApplyToURP(existingFlux);
                EditorUtility.SetDirty(volumeProfile);
            }
            else
            {
                // Add new Flux effect and apply preset
                Undo.RecordObject(volumeProfile, "Add Flux Effect and Apply Preset");
                var newFlux = volumeProfile.Add<Stylo.Flux.Universal.FluxEffect>();
                preset.ApplyToURP(newFlux);
                EditorUtility.SetDirty(volumeProfile);
            }
        }

        private void CreatePresetFromCurrentSettings(VolumeProfile volumeProfile)
        {
            string path = EditorUtility.SaveFilePanelInProject(
                "Save Flux Preset",
                "New Flux Preset",
                "asset",
                "Choose where to save the Flux preset");

            if (!string.IsNullOrEmpty(path))
            {
                var preset = FluxPreset.CreateFromVolumeProfile(volumeProfile, System.IO.Path.GetFileNameWithoutExtension(path));
                if (preset != null)
                {
                    AssetDatabase.CreateAsset(preset, path);
                    AssetDatabase.SaveAssets();
                    AssetDatabase.Refresh();

                    // Select the new preset
                    Selection.activeObject = preset;
                    EditorGUIUtility.PingObject(preset);

                    EditorUtility.DisplayDialog("Preset Created",
                        $"Flux preset saved as '{path}'", "OK");
                }
            }
        }
    }
#endif

    /// <summary>
    /// Menu items for quick preset operations
    /// </summary>
    public static class FluxPresetMenuItems
    {
        [MenuItem("Assets/Create Flux Preset from Selection", true)]
        public static bool ValidateCreatePresetFromSelection()
        {
#if URP_INSTALLED
            if (Selection.activeObject is VolumeProfile) return true;
#endif
            // Check for any FluxEffect component using reflection
            if (Selection.activeObject != null)
            {
                var type = Selection.activeObject.GetType();
                return type.Name.Contains("FluxEffect");
            }
            return false;
        }

        [MenuItem("Assets/Create Flux Preset from Selection")]
        public static void CreatePresetFromSelection()
        {
            Object selected = Selection.activeObject;
            FluxPreset? preset = null;

#if URP_INSTALLED
            if (selected is VolumeProfile volumeProfile)
            {
                preset = FluxPreset.CreateFromVolumeProfile(volumeProfile, volumeProfile.name + " Preset");
            }
            else
#endif
            if (selected != null && selected.GetType().Name.Contains("FluxEffect"))
            {
                preset = FluxPreset.CreateFromFluxComponent(selected, selected.name + " Preset");
            }

            if (preset != null)
            {
                string path = AssetDatabase.GetAssetPath(selected);
                string? directory = System.IO.Path.GetDirectoryName(path);
                if (directory == null) directory = "Assets";
                string fileName = selected.name + " Flux Preset.asset";
                string fullPath = System.IO.Path.Combine(directory, fileName);
                fullPath = AssetDatabase.GenerateUniqueAssetPath(fullPath);

                AssetDatabase.CreateAsset(preset, fullPath);
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();

                Selection.activeObject = preset;
                EditorGUIUtility.PingObject(preset);

                EditorUtility.DisplayDialog("Preset Created",
                    $"Flux preset created at '{fullPath}'", "OK");
            }
        }
    }
}
#endif
