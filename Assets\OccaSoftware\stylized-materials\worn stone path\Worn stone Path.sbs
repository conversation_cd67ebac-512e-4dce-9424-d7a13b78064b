<?xml version="1.0" encoding="UTF-8"?><package><identifier v="Unsaved Package"/><formatVersion v="1.1.0.202302"/><updaterVersion v="1.1.0.202302"/><fileUID v="{6db21c63-9315-4f4a-b0b5-b9e28a462084}"/><versionUID v="0"/><dependencies><dependency><filename v="sbs://hbao_2.sbs"/><uid v="1297162667"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://waveform_1.sbs"/><uid v="1416542616"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://bevel.sbs"/><uid v="1417332159"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://auto_levels.sbs"/><uid v="1383786570"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://invert.sbs"/><uid v="1368115694"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://gradient_linear_1.sbs"/><uid v="1357065436"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://pattern_tile_sampler.sbs"/><uid v="1449399972"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_perlin_noise.sbs"/><uid v="1407332030"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_plasma.sbs"/><uid v="1416576551"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://non_uniform_blur.sbs"/><uid v="1290776959"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://slope_blur.sbs"/><uid v="1449402040"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://blur_hq.sbs"/><uid v="1299236171"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://histogram_scan.sbs"/><uid v="1149349207"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://height_blend.sbs"/><uid v="1449623608"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://flood_fill_2.sbs"/><uid v="1323881949"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://edge_detect.sbs"/><uid v="1407202483"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://pattern_polygon_2.sbs"/><uid v="1487918781"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_gaussian_noise.sbs"/><uid v="1357066167"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_fractal_sum_base.sbs"/><uid v="1417357149"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://vector_warp.sbs"/><uid v="1417486190"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://histogram_select.sbs"/><uid v="1416686315"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://quantize.sbs"/><uid v="1417466547"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://gradient_linear_2.sbs"/><uid v="1416701314"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://curvature_smooth.sbs"/><uid v="1449590280"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://gradient_linear_3.sbs"/><uid v="1416593955"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency></dependencies><content><graph><identifier v="Worn_stone_path"/><uid v="1416526733"/><attributes><category v="Stylized, Grass, Rock"/><author v="3dex"/><authorURL v="3dex.net"/><tags v="stylized, rock, stones, path, grass, flowers"/></attributes><paraminputs><paraminput><identifier v="mask_random"/><uid v="1416701425"/><attributes><label v="Flowers_Mask Random"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.850000024"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.85"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Flowers"/></paraminput><paraminput><identifier v="mask_random_1"/><uid v="1416703653"/><attributes><label v="Flowers_mask random (color)"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.850000024"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.85"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Flowers"/></paraminput><paraminput><identifier v="disorder"/><uid v="1416710083"/><attributes><label v="Grass_disorder"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Grass"/></paraminput><paraminput><identifier v="disorder_1"/><uid v="1416713550"/><attributes><label v="Grass_disorder_2"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="0"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Grass"/></paraminput><paraminput><identifier v="mask_random_2"/><uid v="1416717042"/><attributes><label v="Grass_mask_random"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.899999976"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.9"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Grass"/></paraminput><paraminput><identifier v="position_random"/><uid v="1416720082"/><attributes><label v="Rocks_Position Random"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.25"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="0.25"/></option><option><name v="max"/><value v="10"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Rocks"/></paraminput><paraminput><identifier v="Position"/><uid v="1416725109"/><attributes><label v="Rocks_mask"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.409999996"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.41"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Rocks"/></paraminput><paraminput><identifier v="position_random_1"/><uid v="1416739983"/><attributes><label v="Rocks_details_Position Random"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.959999979"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="0.96"/></option><option><name v="max"/><value v="10"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Rocks"/></paraminput><paraminput><identifier v="scale"/><uid v="1488159747"/><attributes><label v="Scale"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="11"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="11"/></option><option><name v="max"/><value v="10"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Grass"/></paraminput><paraminput><identifier v="outputcolor"/><uid v="1488176665"/><attributes><label v="Grass"/><description v="The &lt;b&gt;Color Output&lt;/b&gt; parameter creates a uniform color output image"/></attributes><type v="2048"/><defaultValue><constantValueFloat4 v="0.360783994 0.403921992 0.149020001 1"/></defaultValue><defaultWidget><name v="color"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.360784;0.403922;0.14902;1"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="valueInterpretation"/><value v="color"/></option></options></defaultWidget><group v="Color"/></paraminput><paraminput><identifier v="outputcolor_1"/><uid v="1488176794"/><attributes><label v="Grass Tip"/><description v="The &lt;b&gt;Color Output&lt;/b&gt; parameter creates a uniform color output image"/></attributes><type v="2048"/><defaultValue><constantValueFloat4 v="0.592157006 0.584313989 0.243137002 1"/></defaultValue><defaultWidget><name v="color"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.592157;0.584314;0.243137;1"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="valueInterpretation"/><value v="color"/></option></options></defaultWidget><group v="Color"/></paraminput><paraminput><identifier v="opacitymult"/><uid v="1488176818"/><attributes><label v="Grass Tip Opacity"/><description v="The &lt;b&gt;Opacity&lt;/b&gt; parameter sets the transparency of the blend"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="1"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="1"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Color"/></paraminput><paraminput><identifier v="outputcolor_2"/><uid v="1488176907"/><attributes><label v="Rocks"/><description v="The &lt;b&gt;Color Output&lt;/b&gt; parameter creates a uniform color output image"/></attributes><type v="2048"/><defaultValue><constantValueFloat4 v="0.400000006 0.396077991 0.396077991 1"/></defaultValue><defaultWidget><name v="color"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.4;0.396078;0.396078;1"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="valueInterpretation"/><value v="color"/></option></options></defaultWidget><group v="Color"/></paraminput><paraminput><identifier v="opacitymult_1"/><uid v="1488176929"/><attributes><label v="Rocks Value"/><description v="The &lt;b&gt;Opacity&lt;/b&gt; parameter sets the transparency of the blend"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.180000007"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="0.18"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Color"/></paraminput><paraminput><identifier v="outputcolor_3"/><uid v="1488177001"/><attributes><label v="Rock Top"/><description v="The &lt;b&gt;Color Output&lt;/b&gt; parameter creates a uniform color output image"/></attributes><type v="2048"/><defaultValue><constantValueFloat4 v="0.415686011 0.423528999 0.435294002 1"/></defaultValue><defaultWidget><name v="color"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.415686;0.423529;0.435294;1"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="valueInterpretation"/><value v="color"/></option></options></defaultWidget><group v="Color"/></paraminput><paraminput><identifier v="opacitymult_2"/><uid v="1488177040"/><attributes><label v="Rock Surface"/><description v="The &lt;b&gt;Opacity&lt;/b&gt; parameter sets the transparency of the blend"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.0500000007"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="0.05"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Color"/></paraminput><paraminput><identifier v="opacitymult_3"/><uid v="1488177068"/><attributes><label v="Rock Surface 2"/><description v="The &lt;b&gt;Opacity&lt;/b&gt; parameter sets the transparency of the blend"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.0299999993"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="0.03"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Color"/></paraminput><paraminput><identifier v="opacitymult_4"/><uid v="1488177102"/><attributes><label v="Rock Edge"/><description v="The &lt;b&gt;Opacity&lt;/b&gt; parameter sets the transparency of the blend"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.0799999982"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="0.08"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Color"/></paraminput><paraminput><identifier v="outputcolor_4"/><uid v="1488177243"/><attributes><label v="Cavity"/><description v="The &lt;b&gt;Color Output&lt;/b&gt; parameter creates a uniform color output image"/></attributes><type v="2048"/><defaultValue><constantValueFloat4 v="0.427451015 0.290196002 0.125489995 1"/></defaultValue><defaultWidget><name v="color"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.427451;0.290196;0.12549;1"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="valueInterpretation"/><value v="color"/></option></options></defaultWidget><group v="Color"/></paraminput><paraminput><identifier v="outputcolor_5"/><uid v="1488177284"/><attributes><label v="Flowers"/><description v="The &lt;b&gt;Color Output&lt;/b&gt; parameter creates a uniform color output image"/></attributes><type v="2048"/><defaultValue><constantValueFloat4 v="0.93333298 0.917647004 0.843137026 1"/></defaultValue><defaultWidget><name v="color"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.933333;0.917647;0.843137;1"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="valueInterpretation"/><value v="color"/></option></options></defaultWidget><group v="Color"/></paraminput><paraminput><identifier v="outputcolor_6"/><uid v="1488177537"/><attributes><label v="Grass"/><description v="The &lt;b&gt;Color Output&lt;/b&gt; parameter creates a uniform color output image"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.690195978"/></defaultValue><defaultWidget><name v="color"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.690196"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="valueInterpretation"/><value v="color"/></option></options></defaultWidget><group v="Roughness"/></paraminput><paraminput><identifier v="outputcolor_7"/><uid v="1488177559"/><attributes><label v="Rock"/><description v="The &lt;b&gt;Color Output&lt;/b&gt; parameter creates a uniform color output image"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.564706028"/></defaultValue><defaultWidget><name v="color"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.564706"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="valueInterpretation"/><value v="color"/></option></options></defaultWidget><group v="Roughness"/></paraminput><paraminput><identifier v="outputcolor_8"/><uid v="1488177631"/><attributes><label v="Flowers"/><description v="The &lt;b&gt;Color Output&lt;/b&gt; parameter creates a uniform color output image"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.529411972"/></defaultValue><defaultWidget><name v="color"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.529412"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="valueInterpretation"/><value v="color"/></option></options></defaultWidget><group v="Roughness"/></paraminput><paraminput><identifier v="outputcolor_9"/><uid v="1488177709"/><attributes><label v="Cavity"/><description v="The &lt;b&gt;Color Output&lt;/b&gt; parameter creates a uniform color output image"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.811765015"/></defaultValue><defaultWidget><name v="color"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.811765"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="valueInterpretation"/><value v="color"/></option></options></defaultWidget><group v="Roughness"/></paraminput><paraminput><identifier v="intensity"/><uid v="1488177737"/><attributes><label v="Normal Intensity"/><description v="The &lt;b&gt;Intensity&lt;/b&gt; parameter modifies the intensity of height map "/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="10"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="10"/></option><option><name v="max"/><value v="50"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget></paraminput><paraminput><identifier v="inversedy"/><uid v="1488177743"/><attributes><label v="Normal Format"/><description v="The &lt;b&gt;Normal Format&lt;/b&gt; parameter inverts y coordinates of height map (Open-GL)"/></attributes><type v="4"/><defaultValue><constantValueBool v="1"/></defaultValue><defaultWidget><name v="buttons"/><options><option><name v="booleditortype"/><value v="enumbuttons"/></option><option><name v="default"/><value v="1"/></option><option><name v="label0"/><value v="DirectX"/></option><option><name v="label1"/><value v="OpenGL"/></option></options></defaultWidget></paraminput><paraminput><identifier v="height_depth"/><uid v="1488177752"/><attributes><label v="Height Depth"/><description v="Defines the height map scale compared to the image size.&#10;A value of 1 means the height map depth is the same as its the largest border."/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.0399999991"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.04"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="AO"/></paraminput><paraminput><identifier v="radius"/><uid v="1488178654"/><attributes><label v="Radius"/><description v="Adjusts the Ambient Occlusion radius. Corresponds to the &quot;Max Occluder Distance&quot; parameter in the bakers.&#10;A value of 1 equals to the largest border of the image."/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.0799999982"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.08"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="AO"/></paraminput></paraminputs><graphOutputs><graphoutput><identifier v="basecolor"/><uid v="1416540817"/><attributes><label v="Base Color"/></attributes><usages><usage><components v="RGBA"/><name v="baseColor"/></usage></usages><group v="Material"/></graphoutput><graphoutput><identifier v="normal"/><uid v="1416540819"/><attributes><label v="Normal"/></attributes><usages><usage><components v="RGBA"/><name v="normal"/></usage></usages><group v="Material"/></graphoutput><graphoutput><identifier v="roughness"/><uid v="1416540821"/><attributes><label v="Roughness"/></attributes><usages><usage><components v="RGBA"/><name v="roughness"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput><graphoutput><identifier v="metallic"/><uid v="1416540823"/><attributes><label v="Metallic"/></attributes><usages><usage><components v="RGBA"/><name v="metallic"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput><graphoutput><identifier v="height"/><uid v="1416540825"/><attributes><label v="Height"/></attributes><usages><usage><components v="RGBA"/><name v="height"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput><graphoutput><identifier v="AO"/><uid v="1416540827"/><usages><usage><components v="RGBA"/><name v="ambientOcclusion"/></usage></usages><channels v="2"/></graphoutput></graphOutputs><compNodes><compNode><uid v="1416540810"/><connections><connection><identifier v="destination"/><connRef v="1416623853"/><connRefOutput v="1416623854"/></connection></connections><GUILayout><gpos v="8496 272 0"/></GUILayout><compOutputs><compOutput><uid v="1410296043"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1416540813"/><GUILayout><gpos v="8688 592 0"/></GUILayout><compOutputs><compOutput><uid v="1360419830"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="uniform"/><parameters><parameter><name v="colorswitch"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1416540814"/><connections><connection><identifier v="input"/><connRef v="1416540810"/><connRefOutput v="1410296043"/></connection></connections><GUILayout><gpos v="8692.16602 422.833313 0"/></GUILayout><compOutputs><compOutput><uid v="1410296137"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///hbao?dependency=1297162667"/><parameters><parameter><name v="radius"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1488178655"/><paramNodes><paramNode><uid v="1488178655"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="radius"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter><parameter><name v="height_depth"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1488177753"/><paramNodes><paramNode><uid v="1488177753"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="height_depth"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1410296137"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416540815"/><connections><connection><identifier v="input1"/><connRef v="1416540810"/><connRefOutput v="1410296043"/></connection></connections><GUILayout><gpos v="8688 111.999985 0"/></GUILayout><compOutputs><compOutput><uid v="1410297936"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="normal"/><parameters><parameter><name v="input2alpha"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="inversedy"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1488177744"/><paramNodes><paramNode><uid v="1488177744"/><function v="get_bool"/><type v="4"/><funcDatas><funcData><name v="get_bool"/><constantValue><constantValueString v="inversedy"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1488177738"/><paramNodes><paramNode><uid v="1488177738"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="intensity"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1416540816"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1416737824"/><connRefOutput v="1416737825"/></connection></connections><GUILayout><gpos v="8848 -208.000015 0"/></GUILayout><compImplementation><compOutputBridge><output v="1416540817"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1416540818"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1416540815"/><connRefOutput v="1410297936"/></connection></connections><GUILayout><gpos v="8848 111.999985 0"/></GUILayout><compImplementation><compOutputBridge><output v="1416540819"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1416540820"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1416755796"/><connRefOutput v="1416755797"/></connection></connections><GUILayout><gpos v="8848 -48.0000153 0"/></GUILayout><compImplementation><compOutputBridge><output v="1416540821"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1416540822"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1416540813"/><connRefOutput v="1360419830"/></connection></connections><GUILayout><gpos v="8848 592 0"/></GUILayout><compImplementation><compOutputBridge><output v="1416540823"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1416540824"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1416540810"/><connRefOutput v="1410296043"/></connection></connections><GUILayout><gpos v="8848 272 0"/></GUILayout><compImplementation><compOutputBridge><output v="1416540825"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1416540826"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1416540814"/><connRefOutput v="1410296137"/></connection></connections><GUILayout><gpos v="8848 432 0"/></GUILayout><compImplementation><compOutputBridge><output v="1416540827"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1416542672"/><GUILayout><gpos v="-2864 -2432 0"/></GUILayout><compOutputs><compOutput><uid v="1416542673"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///waveform_1?dependency=1416542616"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1024"/></paramValue></parameter><parameter><name v="SizeMax"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0799999982"/></paramValue></parameter><parameter><name v="WaveNumber"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="Noise"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="Pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="9"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1416542673"/><identifier v="Waveform"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416542738"/><connections><connection><identifier v="input1"/><connRef v="1416542672"/><connRefOutput v="1416542673"/></connection></connections><GUILayout><gpos v="-2704 -2432 0"/></GUILayout><compOutputs><compOutput><uid v="1416542739"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="transformation"/><parameters><parameter><name v="tiling"/><relativeTo v="0"/><paramValue><constantValueInt32 v="0"/></paramValue></parameter><parameter><name v="matrix22"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0 0.644500017 -0.644500017 0"/></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.211700007 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1416545035"/><connections><connection><identifier v="input"/><connRef v="1416542738"/><connRefOutput v="1416542739"/></connection></connections><GUILayout><gpos v="-2544 -2432 0"/></GUILayout><compOutputs><compOutput><uid v="1416545036"/><comptype v="2"/></compOutput><compOutput><uid v="1416545037"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///bevel?dependency=1417332159"/><parameters><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="-0.209999993"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1416545036"/><identifier v="height"/></outputBridging><outputBridging><uid v="1416545037"/><identifier v="normal"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416557424"/><connections><connection><identifier v="Input"/><connRef v="1416545035"/><connRefOutput v="1416545036"/></connection></connections><GUILayout><gpos v="-2384 -2432 0"/></GUILayout><compOutputs><compOutput><uid v="1416557425"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1383786570"/><parameters/><outputBridgings><outputBridging><uid v="1416557425"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416558408"/><connections><connection><identifier v="Source"/><connRef v="1416557424"/><connRefOutput v="1416557425"/></connection></connections><GUILayout><gpos v="-2224 -2432 0"/></GUILayout><compOutputs><compOutput><uid v="1416558409"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///invert_grayscale?dependency=1368115694"/><parameters/><outputBridgings><outputBridging><uid v="1416558409"/><identifier v="Invert_Grayscale"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416558419"/><connections><connection><identifier v="destination"/><connRef v="1416559345"/><connRefOutput v="1416559346"/></connection><connection><identifier v="source"/><connRef v="1416563517"/><connRefOutput v="1416563518"/></connection></connections><GUILayout><gpos v="-1872 -2336 0"/></GUILayout><compOutputs><compOutput><uid v="1416558420"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1416559345"/><connections><connection><identifier v="input"/><connRef v="1416542738"/><connRefOutput v="1416542739"/></connection></connections><GUILayout><gpos v="-2032 -2272 0"/></GUILayout><compOutputs><compOutput><uid v="1416559346"/><comptype v="2"/></compOutput><compOutput><uid v="1416559347"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///bevel?dependency=1417332159"/><parameters><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="-0.0199999996"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1416559346"/><identifier v="height"/></outputBridging><outputBridging><uid v="1416559347"/><identifier v="normal"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416563517"/><connections><connection><identifier v="input1"/><connRef v="1416558408"/><connRefOutput v="1416558409"/></connection></connections><GUILayout><gpos v="-2032 -2432 0"/></GUILayout><compOutputs><compOutput><uid v="1416563518"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="curve"/><parameters><parameter><name v="exposecurve"/><paramValue><constantValueBool v="0"/></paramValue></parameter></parameters><paramsArrays><paramsArray><name v="curveluminance"/><uid v="1416564430"/><paramsArrayCells><paramsArrayCell><uid v="1416564431"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat2 v="0 0.764999986"/></paramValue></parameter><parameter><name v="left"/><paramValue><constantValueFloat2 v="0 0.764999986"/></paramValue></parameter><parameter><name v="isLeftBroken"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="right"/><paramValue><constantValueFloat2 v="0.100000001 0.764999986"/></paramValue></parameter><parameter><name v="isRightBroken"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="isLocked"/><paramValue><constantValueBool v="1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1416564432"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat2 v="1 1"/></paramValue></parameter><parameter><name v="left"/><paramValue><constantValueFloat2 v="0.899999976 1"/></paramValue></parameter><parameter><name v="isLeftBroken"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="right"/><paramValue><constantValueFloat2 v="1 1"/></paramValue></parameter><parameter><name v="isRightBroken"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="isLocked"/><paramValue><constantValueBool v="1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1416564433"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat2 v="0.460999995 0.860000014"/></paramValue></parameter><parameter><name v="left"/><paramValue><constantValueFloat2 v="0.197999999 0.782000005"/></paramValue></parameter><parameter><name v="isLeftBroken"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="right"/><paramValue><constantValueFloat2 v="0.723999977 0.938000023"/></paramValue></parameter><parameter><name v="isRightBroken"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="isLocked"/><paramValue><constantValueBool v="1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1416565464"/><connections><connection><identifier v="inputintensity"/><connRef v="1416566307"/><connRefOutput v="1416566308"/></connection><connection><identifier v="input1"/><connRef v="1416569759"/><connRefOutput v="1416569760"/></connection></connections><GUILayout><gpos v="-1232 -2432 0"/></GUILayout><compOutputs><compOutput><uid v="1416565465"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="directionalwarp"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="56.8999977"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1416566288"/><GUILayout><gpos v="-1808 -2880 0"/></GUILayout><compOutputs><compOutput><uid v="1416566289"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///gradient_linear_1?dependency=1357065436"/><parameters/><outputBridgings><outputBridging><uid v="1416566289"/><identifier v="Simple_Gradient"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416566307"/><connections><connection><identifier v="input1"/><connRef v="1416566288"/><connRefOutput v="1416566289"/></connection></connections><GUILayout><gpos v="-1488 -2880 0"/></GUILayout><compOutputs><compOutput><uid v="1416566308"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="curve"/><parameters><parameter><name v="exposecurve"/><paramValue><constantValueBool v="0"/></paramValue></parameter></parameters><paramsArrays><paramsArray><name v="curveluminance"/><uid v="1416566317"/><paramsArrayCells><paramsArrayCell><uid v="1416566318"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat2 v="0 0.214000002"/></paramValue></parameter><parameter><name v="left"/><paramValue><constantValueFloat2 v="0 0.214000002"/></paramValue></parameter><parameter><name v="isLeftBroken"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="right"/><paramValue><constantValueFloat2 v="0.100000001 0.214000002"/></paramValue></parameter><parameter><name v="isRightBroken"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="isLocked"/><paramValue><constantValueBool v="1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1416566319"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat2 v="0.971000016 0.402999997"/></paramValue></parameter><parameter><name v="left"/><paramValue><constantValueFloat2 v="0.870999992 0.402999997"/></paramValue></parameter><parameter><name v="isLeftBroken"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="right"/><paramValue><constantValueFloat2 v="0.971000016 0.402999997"/></paramValue></parameter><parameter><name v="isRightBroken"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="isLocked"/><paramValue><constantValueBool v="1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1416566320"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat2 v="0.319999993 0.644999981"/></paramValue></parameter><parameter><name v="left"/><paramValue><constantValueFloat2 v="0.0649999976 0.172000006"/></paramValue></parameter><parameter><name v="isLeftBroken"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="right"/><paramValue><constantValueFloat2 v="0.574999988 1.11800003"/></paramValue></parameter><parameter><name v="isRightBroken"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="isLocked"/><paramValue><constantValueBool v="1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1416568330"/><connections><connection><identifier v="inputintensity"/><connRef v="1416568386"/><connRefOutput v="1416566308"/></connection><connection><identifier v="input1"/><connRef v="1416569759"/><connRefOutput v="1416569760"/></connection></connections><GUILayout><gpos v="-1232 -2304 0"/></GUILayout><compOutputs><compOutput><uid v="1416565465"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="directionalwarp"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="31.9200001"/></paramValue></parameter><parameter><name v="warpangle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="-0.302611113"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1416568386"/><connections><connection><identifier v="input1"/><connRef v="1416566288"/><connRefOutput v="1416566289"/></connection></connections><GUILayout><gpos v="-1488 -2720 0"/></GUILayout><compOutputs><compOutput><uid v="1416566308"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="curve"/><parameters><parameter><name v="exposecurve"/><paramValue><constantValueBool v="0"/></paramValue></parameter></parameters><paramsArrays><paramsArray><name v="curveluminance"/><uid v="1416566317"/><paramsArrayCells><paramsArrayCell><uid v="1416566318"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat2 v="0 0"/></paramValue></parameter><parameter><name v="left"/><paramValue><constantValueFloat2 v="0 0"/></paramValue></parameter><parameter><name v="isLeftBroken"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="right"/><paramValue><constantValueFloat2 v="0.100000001 0"/></paramValue></parameter><parameter><name v="isRightBroken"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="isLocked"/><paramValue><constantValueBool v="1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1416566319"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat2 v="1 0"/></paramValue></parameter><parameter><name v="left"/><paramValue><constantValueFloat2 v="0.899999976 0"/></paramValue></parameter><parameter><name v="isLeftBroken"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="right"/><paramValue><constantValueFloat2 v="1 0"/></paramValue></parameter><parameter><name v="isRightBroken"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="isLocked"/><paramValue><constantValueBool v="1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1416566320"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat2 v="0.49000001 0.720000029"/></paramValue></parameter><parameter><name v="left"/><paramValue><constantValueFloat2 v="0 1.28100002"/></paramValue></parameter><parameter><name v="isLeftBroken"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="right"/><paramValue><constantValueFloat2 v="1 0.137999997"/></paramValue></parameter><parameter><name v="isRightBroken"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="isLocked"/><paramValue><constantValueBool v="1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1416569484"/><connections><connection><identifier v="pattern_input"/><connRef v="1416584703"/><connRefOutput v="1416584704"/></connection><connection><identifier v="pattern_input_2"/><connRef v="1416584764"/><connRefOutput v="1416584704"/></connection><connection><identifier v="mask_map_input"/><connRef v="1416595124"/><connRefOutput v="1416595125"/></connection><connection><identifier v="rotation_map_input"/><connRef v="1416584602"/><connRefOutput v="1416584603"/></connection></connections><GUILayout><gpos v="-80 -2613.33325 0"/></GUILayout><compOutputs><compOutput><uid v="1416569485"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1449399972"/><parameters><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="pattern_input_number"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.5"/></paramValue></parameter><parameter><name v="rotation_map_multiplier"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="34"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="35"/></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.299999982"/></paramValue></parameter><parameter><name v="color_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.839999974"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="9.35999966"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1416569485"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416569519"/><connections><connection><identifier v="destination"/><connRef v="1416569525"/><connRefOutput v="1416569526"/></connection><connection><identifier v="source"/><connRef v="1416569586"/><connRefOutput v="1416569587"/></connection></connections><GUILayout><gpos v="-1537.80957 -2560 0"/></GUILayout><compOutputs><compOutput><uid v="1416569520"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1416569525"/><connections><connection><identifier v="input1"/><connRef v="1416558419"/><connRefOutput v="1416558420"/></connection></connections><GUILayout><gpos v="-1680 -2560 0"/></GUILayout><compOutputs><compOutput><uid v="1416569526"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelouthigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.300000012 0.300000012 0.300000012 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1416569586"/><connections><connection><identifier v="input1"/><connRef v="1416566288"/><connRefOutput v="1416566289"/></connection></connections><GUILayout><gpos v="-1680 -2720 0"/></GUILayout><compOutputs><compOutput><uid v="1416569587"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="curve"/><parameters><parameter><name v="exposecurve"/><paramValue><constantValueBool v="0"/></paramValue></parameter></parameters><paramsArrays><paramsArray><name v="curveluminance"/><uid v="1416569600"/><paramsArrayCells><paramsArrayCell><uid v="1416569601"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat2 v="0 0"/></paramValue></parameter><parameter><name v="left"/><paramValue><constantValueFloat2 v="0 0"/></paramValue></parameter><parameter><name v="isLeftBroken"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="right"/><paramValue><constantValueFloat2 v="0.100000001 0"/></paramValue></parameter><parameter><name v="isRightBroken"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="isLocked"/><paramValue><constantValueBool v="1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1416569602"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat2 v="1 1"/></paramValue></parameter><parameter><name v="left"/><paramValue><constantValueFloat2 v="0.899999976 1"/></paramValue></parameter><parameter><name v="isLeftBroken"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="right"/><paramValue><constantValueFloat2 v="1 1"/></paramValue></parameter><parameter><name v="isRightBroken"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="isLocked"/><paramValue><constantValueBool v="1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1416569603"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat2 v="0.259000003 0.73299998"/></paramValue></parameter><parameter><name v="left"/><paramValue><constantValueFloat2 v="0 0.477999985"/></paramValue></parameter><parameter><name v="isLeftBroken"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="right"/><paramValue><constantValueFloat2 v="0.531000018 1.00100005"/></paramValue></parameter><parameter><name v="isRightBroken"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="isLocked"/><paramValue><constantValueBool v="1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1416569759"/><connections><connection><identifier v="Input"/><connRef v="1416569519"/><connRefOutput v="1416569520"/></connection></connections><GUILayout><gpos v="-1377.80957 -2560 0"/></GUILayout><compOutputs><compOutput><uid v="1416569760"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1383786570"/><parameters/><outputBridgings><outputBridging><uid v="1416569760"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416570313"/><GUILayout><gpos v="-1488 -3200 0"/></GUILayout><compOutputs><compOutput><uid v="1416570314"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///perlin_noise?dependency=1407332030"/><parameters><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueInt32 v="5"/></paramValue></parameter><parameter><name v="disorder"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1416710084"/><paramNodes><paramNode><uid v="1416710084"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="disorder"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1416570314"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416570340"/><connections><connection><identifier v="Input"/><connRef v="1416570313"/><connRefOutput v="1416570314"/></connection></connections><GUILayout><gpos v="-1328 -3200 0"/></GUILayout><compOutputs><compOutput><uid v="1416570341"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1383786570"/><parameters/><outputBridgings><outputBridging><uid v="1416570341"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416577215"/><GUILayout><gpos v="-1136 -3328 0"/></GUILayout><compOutputs><compOutput><uid v="1416577216"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///plasma?dependency=1416576551"/><parameters><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueInt32 v="12"/></paramValue></parameter><parameter><name v="disorder"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1416713551"/><paramNodes><paramNode><uid v="1416713551"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="disorder_1"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1416577216"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416577251"/><connections><connection><identifier v="Source"/><connRef v="1416577215"/><connRefOutput v="1416577216"/></connection><connection><identifier v="Effect"/><connRef v="1416596448"/><connRefOutput v="1416596449"/></connection></connections><GUILayout><gpos v="-976 -3328 0"/></GUILayout><compOutputs><compOutput><uid v="1416577252"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///non_uniform_blur_grayscale?dependency=1290776959"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="16"/></paramValue></parameter><parameter><name v="Blades"/><relativeTo v="0"/><paramValue><constantValueInt32 v="9"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="13.3800001"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1416577252"/><identifier v="Non_Uniform_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416584602"/><connections><connection><identifier v="Input"/><connRef v="1416577251"/><connRefOutput v="1416577252"/></connection></connections><GUILayout><gpos v="-816 -3328 0"/></GUILayout><compOutputs><compOutput><uid v="1416584603"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1383786570"/><parameters/><outputBridgings><outputBridging><uid v="1416584603"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416584703"/><connections><connection><identifier v="input1"/><connRef v="1416565464"/><connRefOutput v="1416565465"/></connection></connections><GUILayout><gpos v="-1072 -2432 0"/></GUILayout><compOutputs><compOutput><uid v="1416584704"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="transformation"/><parameters><parameter><name v="matrix22"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="1.727 0 0 1.727"/></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="-0.123899996 0.222100005"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1416584764"/><connections><connection><identifier v="input1"/><connRef v="1416568330"/><connRefOutput v="1416565465"/></connection></connections><GUILayout><gpos v="-1072 -2304 0"/></GUILayout><compOutputs><compOutput><uid v="1416584704"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="transformation"/><parameters><parameter><name v="matrix22"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="1.68400002 0 0 1.68400002"/></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="-0.0584000014 0.381700009"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1416596448"/><connections><connection><identifier v="Source"/><connRef v="1416570340"/><connRefOutput v="1416570341"/></connection><connection><identifier v="Effect"/><connRef v="1416570340"/><connRefOutput v="1416570341"/></connection></connections><GUILayout><gpos v="-1136 -3200 0"/></GUILayout><compOutputs><compOutput><uid v="1416596449"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///slope_blur_grayscale_2?dependency=1449402040"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="32"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="16"/></paramValue></parameter><parameter><name v="mode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1416596449"/><identifier v="Slope_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416609605"/><connections><connection><identifier v="rotation_map_input"/><connRef v="1416584602"/><connRefOutput v="1416584603"/></connection><connection><identifier v="pattern_input"/><connRef v="1416584703"/><connRefOutput v="1416584704"/></connection><connection><identifier v="pattern_input_2"/><connRef v="1416584764"/><connRefOutput v="1416584704"/></connection><connection><identifier v="displacement_map_input"/><connRef v="1416650703"/><connRefOutput v="1416650704"/></connection><connection><identifier v="mask_map_input"/><connRef v="1416927247"/><connRefOutput v="1416927248"/></connection></connections><GUILayout><gpos v="-80 -2933.3335 0"/></GUILayout><compOutputs><compOutput><uid v="1416569485"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1449399972"/><parameters><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="pattern_input_number"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="4.8499999"/></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.5"/></paramValue></parameter><parameter><name v="rotation_map_multiplier"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="44"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="46"/></paramValue></parameter><parameter><name v="rotation_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.049999997"/></paramValue></parameter><parameter><name v="mask_random"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1416717043"/><paramNodes><paramNode><uid v="1416717043"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="mask_random_2"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter><parameter><name v="displacement_map_intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.649999976"/></paramValue></parameter><parameter><name v="mask_map_threshold"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0199999996"/></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="2.42999983"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1416569485"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416615158"/><connections><connection><identifier v="Input"/><connRef v="1416569484"/><connRefOutput v="1416569485"/></connection></connections><GUILayout><gpos v="80 -2608 0"/></GUILayout><compOutputs><compOutput><uid v="1416615159"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1383786570"/><parameters/><outputBridgings><outputBridging><uid v="1416615159"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416623125"/><connections><connection><identifier v="destination"/><connRef v="1416623129"/><connRefOutput v="1416623130"/></connection><connection><identifier v="source"/><connRef v="1416670452"/><connRefOutput v="1416670453"/></connection></connections><GUILayout><gpos v="528 -2624 0"/></GUILayout><compOutputs><compOutput><uid v="1416623126"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="5"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1416623129"/><connections><connection><identifier v="input1"/><connRef v="1416615158"/><connRefOutput v="1416615159"/></connection></connections><GUILayout><gpos v="240 -2624 0"/></GUILayout><compOutputs><compOutput><uid v="1416623130"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelouthigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.417883217 0.417883217 0.417883217 1"/></paramValue></parameter><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.366788328 0.366788328 0.366788328 0.5"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1416627613"/><connections><connection><identifier v="height_bottom"/><connRef v="1416623129"/><connRefOutput v="1416623130"/></connection><connection><identifier v="height_top"/><connRef v="1416670452"/><connRefOutput v="1416670453"/></connection></connections><GUILayout><gpos v="3092.16602 -1977.16663 0"/></GUILayout><compOutputs><compOutput><uid v="1416627614"/><comptype v="2"/></compOutput><compOutput><uid v="1416627615"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///height_blend?dependency=1449623608"/><parameters><parameter><name v="contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1416627614"/><identifier v="blended_height"/></outputBridging><outputBridging><uid v="1416627615"/><identifier v="height_mask"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416627654"/><connections><connection><identifier v="destination"/><connRef v="1416627613"/><connRefOutput v="1416627615"/></connection><connection><identifier v="source"/><connRef v="1416670452"/><connRefOutput v="1416670453"/></connection></connections><GUILayout><gpos v="3188.16602 -2073.16675 0"/></GUILayout><compOutputs><compOutput><uid v="1416627655"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1416627763"/><connections><connection><identifier v="opacity"/><connRef v="1416614901"/><connRefOutput v="1416614902"/></connection><connection><identifier v="destination"/><connRef v="1488176562"/><connRefOutput v="1488176563"/></connection><connection><identifier v="source"/><connRef v="1488176698"/><connRefOutput v="1488176563"/></connection></connections><GUILayout><gpos v="3700.16602 -1835.203 0"/></GUILayout><compOutputs><compOutput><uid v="1416627764"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1488176819"/><paramNodes><paramNode><uid v="1488176819"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="opacitymult"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1416592808"/><GUILayout><gpos v="-3628 -385.516174 0"/></GUILayout><compOutputs><compOutput><uid v="1416592809"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1449399972"/><parameters><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="7"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.049999997"/></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1416720083"/><paramNodes><paramNode><uid v="1416720083"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="position_random"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.5"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1416592809"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416593561"/><connections><connection><identifier v="mask"/><connRef v="1416592808"/><connRefOutput v="1416592809"/></connection></connections><GUILayout><gpos v="-3468 -385.516174 0"/></GUILayout><compOutputs><compOutput><uid v="1416593562"/><comptype v="1"/></compOutput><compOutput><uid v="1416593563"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill?dependency=1323881949"/><parameters/><outputBridgings><outputBridging><uid v="1416593562"/><identifier v="output"/></outputBridging><outputBridging><uid v="1416593563"/><identifier v="referenceMap"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416593674"/><connections><connection><identifier v="input"/><connRef v="1416593561"/><connRefOutput v="1416593562"/></connection></connections><GUILayout><gpos v="-3308 -385.516174 0"/></GUILayout><compOutputs><compOutput><uid v="1416593675"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_random_grayscale?dependency=1323881949"/><parameters/><outputBridgings><outputBridging><uid v="1416593675"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416593684"/><connections><connection><identifier v="source"/><connRef v="1416593674"/><connRefOutput v="1416593675"/></connection><connection><identifier v="mask"/><connRef v="1416592808"/><connRefOutput v="1416592809"/></connection></connections><GUILayout><gpos v="-3212 -225.516174 0"/></GUILayout><compOutputs><compOutput><uid v="1416593685"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters><parameter><name v="combinedistance"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="256"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1416593772"/><connections><connection><identifier v="input"/><connRef v="1416593684"/><connRefOutput v="1416593685"/></connection></connections><GUILayout><gpos v="-3052 -449.516174 0"/></GUILayout><compOutputs><compOutput><uid v="1416593773"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///edge_detect?dependency=1407202483"/><parameters><parameter><name v="edge_width"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="2.20000005"/></paramValue></parameter><parameter><name v="edge_roundness"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="6.46000004"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1416593773"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416593796"/><connections><connection><identifier v="mask"/><connRef v="1416593772"/><connRefOutput v="1416593773"/></connection></connections><GUILayout><gpos v="-2860 -449.516174 0"/></GUILayout><compOutputs><compOutput><uid v="1416593797"/><comptype v="1"/></compOutput><compOutput><uid v="1416593798"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill?dependency=1323881949"/><parameters/><outputBridgings><outputBridging><uid v="1416593797"/><identifier v="output"/></outputBridging><outputBridging><uid v="1416593798"/><identifier v="referenceMap"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416593946"/><connections><connection><identifier v="input"/><connRef v="1416593796"/><connRefOutput v="1416593797"/></connection><connection><identifier v="grayscale_input"/><connRef v="1416593975"/><connRefOutput v="1416593976"/></connection></connections><GUILayout><gpos v="-2668 -353.516174 0"/></GUILayout><compOutputs><compOutput><uid v="1416593947"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_grayscale?dependency=1323881949"/><parameters><parameter><name v="luminance_adjustement"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.329999983"/></paramValue></parameter><parameter><name v="luminance_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="-0.399999976"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1416593947"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416593975"/><GUILayout><gpos v="-2796 -193.516174 0"/></GUILayout><compOutputs><compOutput><uid v="1416593976"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///gradient_linear_3?dependency=1416593955"/><parameters/><outputBridgings><outputBridging><uid v="1416593976"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416593991"/><connections><connection><identifier v="Input_1"/><connRef v="1416593946"/><connRefOutput v="1416593947"/></connection></connections><GUILayout><gpos v="-2380 -385.516174 0"/></GUILayout><compOutputs><compOutput><uid v="1416593992"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1149349207"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1416725110"/><paramNodes><paramNode><uid v="1416725110"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="Position"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1416593992"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416595124"/><connections><connection><identifier v="Source"/><connRef v="1416702022"/><connRefOutput v="1416593992"/></connection></connections><GUILayout><gpos v="-624 -2304 0"/></GUILayout><compOutputs><compOutput><uid v="1416595125"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///invert_grayscale?dependency=1368115694"/><parameters/><outputBridgings><outputBridging><uid v="1416595125"/><identifier v="Invert_Grayscale"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416596968"/><connections><connection><identifier v="source"/><connRef v="1416597050"/><connRefOutput v="1416597051"/></connection><connection><identifier v="destination"/><connRef v="1416623129"/><connRefOutput v="1416623130"/></connection></connections><GUILayout><gpos v="-924.888916 -449.516174 0"/></GUILayout><compOutputs><compOutput><uid v="1416596969"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="5"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1416597050"/><connections><connection><identifier v="input1"/><connRef v="1416607677"/><connRefOutput v="1416607678"/></connection></connections><GUILayout><gpos v="-1182.52686 -400 0"/></GUILayout><compOutputs><compOutput><uid v="1416597051"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelouthigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.944444418 0.944444418 0.944444418 1"/></paramValue></parameter><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.403284669 0.403284669 0.403284669 0.5"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1416599483"/><connections><connection><identifier v="height_top"/><connRef v="1416623125"/><connRefOutput v="1416623126"/></connection><connection><identifier v="height_bottom"/><connRef v="1416597050"/><connRefOutput v="1416597051"/></connection></connections><GUILayout><gpos v="3604.16602 -1593.16663 0"/></GUILayout><compOutputs><compOutput><uid v="1416599484"/><comptype v="2"/></compOutput><compOutput><uid v="1416599485"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///height_blend?dependency=1449623608"/><parameters><parameter><name v="contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1416599484"/><identifier v="blended_height"/></outputBridging><outputBridging><uid v="1416599485"/><identifier v="height_mask"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416599508"/><connections><connection><identifier v="destination"/><connRef v="1416627763"/><connRefOutput v="1416627764"/></connection><connection><identifier v="opacity"/><connRef v="1416599598"/><connRefOutput v="1416599599"/></connection><connection><identifier v="source"/><connRef v="1488176872"/><connRefOutput v="1488176508"/></connection></connections><GUILayout><gpos v="3956.16602 -1849.16663 0"/></GUILayout><compOutputs><compOutput><uid v="1416599509"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1416599598"/><connections><connection><identifier v="Source"/><connRef v="1416599483"/><connRefOutput v="1416599485"/></connection></connections><GUILayout><gpos v="3732.16602 -1593.16663 0"/></GUILayout><compOutputs><compOutput><uid v="1416599599"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///invert_grayscale?dependency=1368115694"/><parameters/><outputBridgings><outputBridging><uid v="1416599599"/><identifier v="Invert_Grayscale"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416599690"/><connections><connection><identifier v="Source"/><connRef v="1416593991"/><connRefOutput v="1416593992"/></connection><connection><identifier v="Effect"/><connRef v="1416593991"/><connRefOutput v="1416593992"/></connection></connections><GUILayout><gpos v="-2188 -385.516174 0"/></GUILayout><compOutputs><compOutput><uid v="1416599691"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///non_uniform_blur_grayscale?dependency=1290776959"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="16"/></paramValue></parameter><parameter><name v="Blades"/><relativeTo v="0"/><paramValue><constantValueInt32 v="9"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1416599691"/><identifier v="Non_Uniform_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416607677"/><connections><connection><identifier v="source"/><connRef v="1416607789"/><connRefOutput v="1416607790"/></connection><connection><identifier v="destination"/><connRef v="1416607925"/><connRefOutput v="1416607926"/></connection></connections><GUILayout><gpos v="-1356 -449.516174 0"/></GUILayout><compOutputs><compOutput><uid v="1416607678"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.629999995"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1416607789"/><connections><connection><identifier v="input"/><connRef v="1416593796"/><connRefOutput v="1416593797"/></connection></connections><GUILayout><gpos v="-2668 -481.516174 0"/></GUILayout><compOutputs><compOutput><uid v="1416607790"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.74000001"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1416607790"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416607925"/><connections><connection><identifier v="source"/><connRef v="1416607940"/><connRefOutput v="1416607941"/></connection><connection><identifier v="destination"/><connRef v="1416677942"/><connRefOutput v="1416677943"/></connection></connections><GUILayout><gpos v="-1548 -353.516174 0"/></GUILayout><compOutputs><compOutput><uid v="1416607926"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.829999983"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1416607940"/><connections><connection><identifier v="input"/><connRef v="1416593796"/><connRefOutput v="1416593797"/></connection></connections><GUILayout><gpos v="-2668 -609.516174 0"/></GUILayout><compOutputs><compOutput><uid v="1416607941"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_random_grayscale?dependency=1323881949"/><parameters/><outputBridgings><outputBridging><uid v="1416607941"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416614901"/><connections><connection><identifier v="Input_1"/><connRef v="1416623873"/><connRefOutput v="1416623874"/></connection></connections><GUILayout><gpos v="848 -2912 0"/></GUILayout><compOutputs><compOutput><uid v="1416614902"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1149349207"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1416614902"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416614948"/><connections><connection><identifier v="Effect"/><connRef v="1416614901"/><connRefOutput v="1416614902"/></connection><connection><identifier v="Source"/><connRef v="1416740780"/><connRefOutput v="1416740781"/></connection></connections><GUILayout><gpos v="134.668579 -449.516174 0"/></GUILayout><compOutputs><compOutput><uid v="1416614949"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///non_uniform_blur_grayscale?dependency=1290776959"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="16"/></paramValue></parameter><parameter><name v="Blades"/><relativeTo v="0"/><paramValue><constantValueInt32 v="9"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.40999997"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1416614949"/><identifier v="Non_Uniform_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416623853"/><connections><connection><identifier v="destination"/><connRef v="1416614948"/><connRefOutput v="1416614949"/></connection><connection><identifier v="source"/><connRef v="1416623873"/><connRefOutput v="1416623874"/></connection></connections><GUILayout><gpos v="358.668579 -449.516174 0"/></GUILayout><compOutputs><compOutput><uid v="1416623854"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1416623873"/><connections><connection><identifier v="input1"/><connRef v="1416670452"/><connRefOutput v="1416670453"/></connection></connections><GUILayout><gpos v="528 -2912 0"/><dockDistance v="384 128"/></GUILayout><compOutputs><compOutput><uid v="1416623874"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelouthigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.348540157 0.348540157 0.348540157 1"/></paramValue></parameter><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.0711678863 0.0711678863 0.0711678863 0.5"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1416650650"/><connections><connection><identifier v="Source"/><connRef v="1416599690"/><connRefOutput v="1416599691"/></connection></connections><GUILayout><gpos v="-1040 -2797.80615 0"/></GUILayout><compOutputs><compOutput><uid v="1416650651"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1299236171"/><parameters/><outputBridgings><outputBridging><uid v="1416650651"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416650703"/><connections><connection><identifier v="source"/><connRef v="1416650650"/><connRefOutput v="1416650651"/></connection><connection><identifier v="destination"/><connRef v="1416584602"/><connRefOutput v="1416584603"/></connection></connections><GUILayout><gpos v="-880 -3040 0"/></GUILayout><compOutputs><compOutput><uid v="1416650704"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1416659684"/><connections><connection><identifier v="input1"/><connRef v="1416661525"/><connRefOutput v="1416661526"/></connection></connections><GUILayout><gpos v="-688 -2720 0"/></GUILayout><compOutputs><compOutput><uid v="1416659685"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.239463598 0.239463598 0.239463598 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1416659702"/><connections><connection><identifier v="destination"/><connRef v="1416595124"/><connRefOutput v="1416595125"/></connection><connection><identifier v="source"/><connRef v="1416659684"/><connRefOutput v="1416659685"/></connection></connections><GUILayout><gpos v="-381.987549 -2816 0"/></GUILayout><compOutputs><compOutput><uid v="1416659703"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1416661525"/><connections><connection><identifier v="destination"/><connRef v="1416650650"/><connRefOutput v="1416650651"/></connection><connection><identifier v="source"/><connRef v="1416584602"/><connRefOutput v="1416584603"/></connection></connections><GUILayout><gpos v="-816 -2720 0"/></GUILayout><compOutputs><compOutput><uid v="1416661526"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.579999983"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1416667732"/><connections><connection><identifier v="Input"/><connRef v="1416596968"/><connRefOutput v="1416596969"/></connection></connections><GUILayout><gpos v="-684 -449.516174 0"/></GUILayout><compOutputs><compOutput><uid v="1416667733"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1383786570"/><parameters/><outputBridgings><outputBridging><uid v="1416667733"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416670452"/><connections><connection><identifier v="Input"/><connRef v="1416609605"/><connRefOutput v="1416569485"/></connection></connections><GUILayout><gpos v="80 -2933.33325 0"/></GUILayout><compOutputs><compOutput><uid v="1416670453"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1383786570"/><parameters/><outputBridgings><outputBridging><uid v="1416670453"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416675440"/><GUILayout><gpos v="-4112 592 0"/></GUILayout><compOutputs><compOutput><uid v="1416675441"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///polygon_2?dependency=1487918781"/><parameters><parameter><name v="Sides"/><relativeTo v="0"/><paramValue><constantValueInt32 v="8"/></paramValue></parameter><parameter><name v="Gradient"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.189999998"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1416675441"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416675496"/><connections><connection><identifier v="input1"/><connRef v="1416675440"/><connRefOutput v="1416675441"/></connection></connections><GUILayout><gpos v="-3952 592 0"/></GUILayout><compOutputs><compOutput><uid v="1416675497"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="transformation"/><parameters><parameter><name v="matrix22"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="1 0 0 1.90690005"/></paramValue></parameter><parameter><name v="tiling"/><relativeTo v="0"/><paramValue><constantValueInt32 v="0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1416675539"/><connections><connection><identifier v="pattern_input"/><connRef v="1416675568"/><connRefOutput v="1416675569"/></connection></connections><GUILayout><gpos v="-3248 592 0"/></GUILayout><compOutputs><compOutput><uid v="1416675540"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1449399972"/><parameters><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="8"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="11"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="5.19999981"/></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1416739984"/><paramNodes><paramNode><uid v="1416739984"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="position_random_1"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.5"/></paramValue></parameter><parameter><name v="rotation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.125"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.459999979"/></paramValue></parameter><parameter><name v="color_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.50999999"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1416675540"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416675568"/><connections><connection><identifier v="destination"/><connRef v="1416675496"/><connRefOutput v="1416675497"/></connection><connection><identifier v="source"/><connRef v="1416675581"/><connRefOutput v="1416675582"/></connection></connections><GUILayout><gpos v="-3824 432 0"/></GUILayout><compOutputs><compOutput><uid v="1416675569"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1416675581"/><GUILayout><gpos v="-3984 432 0"/></GUILayout><compOutputs><compOutput><uid v="1416675582"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///gradient_linear_1?dependency=1357065436"/><parameters><parameter><name v="rotation"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="Tiling"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1416675582"/><identifier v="Simple_Gradient"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416675718"/><connections><connection><identifier v="Source"/><connRef v="1416675539"/><connRefOutput v="1416675540"/></connection><connection><identifier v="Effect"/><connRef v="1416675841"/><connRefOutput v="1416675842"/></connection></connections><GUILayout><gpos v="-2992 592 0"/></GUILayout><compOutputs><compOutput><uid v="1416675719"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///slope_blur_grayscale_2?dependency=1449402040"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="32"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="2.3499999"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1416675719"/><identifier v="Slope_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416675841"/><GUILayout><gpos v="-3216 880 0"/></GUILayout><compOutputs><compOutput><uid v="1416675842"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///gaussian_noise?dependency=1357066167"/><parameters><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueInt32 v="26"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1416675842"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416675966"/><connections><connection><identifier v="Source"/><connRef v="1416675718"/><connRefOutput v="1416675719"/></connection><connection><identifier v="Effect"/><connRef v="1416677688"/><connRefOutput v="1416677689"/></connection></connections><GUILayout><gpos v="-2704 592 0"/></GUILayout><compOutputs><compOutput><uid v="1416675967"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///slope_blur_grayscale_2?dependency=1449402040"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="32"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.829999983"/></paramValue></parameter><parameter><name v="mode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="5"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1416675967"/><identifier v="Slope_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416677688"/><GUILayout><gpos v="-2896 752 0"/></GUILayout><compOutputs><compOutput><uid v="1416677689"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///fractal_sum_base_2?dependency=1417357149"/><parameters><parameter><name v="Roughness"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.289999992"/></paramValue></parameter><parameter><name v="MinLevel"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter><parameter><name v="MaxLevel"/><relativeTo v="0"/><paramValue><constantValueInt32 v="10"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1416677689"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416677908"/><connections><connection><identifier v="input"/><connRef v="1416675966"/><connRefOutput v="1416675967"/></connection></connections><GUILayout><gpos v="-2480 592 0"/></GUILayout><compOutputs><compOutput><uid v="1416677909"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///vector_warp_grayscale?dependency=1417486190"/><parameters/><outputBridgings><outputBridging><uid v="1416677909"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416677942"/><connections><connection><identifier v="source"/><connRef v="1416689045"/><connRefOutput v="1416688917"/></connection><connection><identifier v="destination"/><connRef v="1416700671"/><connRefOutput v="1416700672"/></connection></connections><GUILayout><gpos v="-1804 -385.516174 0"/></GUILayout><compOutputs><compOutput><uid v="1416677943"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.550000012"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1416677959"/><connections><connection><identifier v="input1"/><connRef v="1416677970"/><connRefOutput v="1416677971"/></connection></connections><GUILayout><gpos v="-2192 592 0"/></GUILayout><compOutputs><compOutput><uid v="1416677960"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1416677970"/><connections><connection><identifier v="Input"/><connRef v="1416677908"/><connRefOutput v="1416677909"/></connection></connections><GUILayout><gpos v="-2320 592 0"/></GUILayout><compOutputs><compOutput><uid v="1416677971"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1383786570"/><parameters/><outputBridgings><outputBridging><uid v="1416677971"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416686354"/><connections><connection><identifier v="input"/><connRef v="1416607940"/><connRefOutput v="1416607941"/></connection></connections><GUILayout><gpos v="-2192 720 0"/></GUILayout><compOutputs><compOutput><uid v="1416686355"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_select?dependency=1416686315"/><parameters><parameter><name v="constrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1416686355"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416686365"/><connections><connection><identifier v="input"/><connRef v="1416688960"/><connRefOutput v="1416688961"/></connection></connections><GUILayout><gpos v="-2480 880 0"/></GUILayout><compOutputs><compOutput><uid v="1416677909"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///vector_warp_grayscale?dependency=1417486190"/><parameters/><outputBridgings><outputBridging><uid v="1416677909"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416686366"/><connections><connection><identifier v="Input"/><connRef v="1416686365"/><connRefOutput v="1416677909"/></connection></connections><GUILayout><gpos v="-2320 880 0"/></GUILayout><compOutputs><compOutput><uid v="1416677971"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1383786570"/><parameters/><outputBridgings><outputBridging><uid v="1416677971"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416686368"/><connections><connection><identifier v="input1"/><connRef v="1416686366"/><connRefOutput v="1416677971"/></connection></connections><GUILayout><gpos v="-2192 880 0"/></GUILayout><compOutputs><compOutput><uid v="1416677960"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1416686383"/><connections><connection><identifier v="input1"/><connRef v="1416686384"/><connRefOutput v="1416677971"/></connection></connections><GUILayout><gpos v="-2192 1200 0"/></GUILayout><compOutputs><compOutput><uid v="1416677960"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1416686384"/><connections><connection><identifier v="Input"/><connRef v="1416686385"/><connRefOutput v="1416677909"/></connection></connections><GUILayout><gpos v="-2320 1200 0"/></GUILayout><compOutputs><compOutput><uid v="1416677971"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1383786570"/><parameters/><outputBridgings><outputBridging><uid v="1416677971"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416686385"/><connections><connection><identifier v="input"/><connRef v="1416675966"/><connRefOutput v="1416675967"/></connection></connections><GUILayout><gpos v="-2480 1200 0"/></GUILayout><compOutputs><compOutput><uid v="1416677909"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///vector_warp_grayscale?dependency=1417486190"/><parameters/><outputBridgings><outputBridging><uid v="1416677909"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416688916"/><connections><connection><identifier v="opacity"/><connRef v="1416686354"/><connRefOutput v="1416686355"/></connection><connection><identifier v="destination"/><connRef v="1416677959"/><connRefOutput v="1416677960"/></connection><connection><identifier v="source"/><connRef v="1416686368"/><connRefOutput v="1416677960"/></connection></connections><GUILayout><gpos v="-1968 688 0"/></GUILayout><compOutputs><compOutput><uid v="1416688917"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1416688960"/><connections><connection><identifier v="input1"/><connRef v="1416675966"/><connRefOutput v="1416675967"/></connection></connections><GUILayout><gpos v="-2558 869.333313 0"/><docked v="1"/><dockDistance v="112 144"/></GUILayout><compOutputs><compOutput><uid v="1416688961"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="transformation"/><parameters><parameter><name v="matrix22"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0 -1 1 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1416688999"/><connections><connection><identifier v="input"/><connRef v="1416607940"/><connRefOutput v="1416607941"/></connection></connections><GUILayout><gpos v="-2192 1040 0"/></GUILayout><compOutputs><compOutput><uid v="1416686355"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_select?dependency=1416686315"/><parameters><parameter><name v="constrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.799999952"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1416686355"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416689045"/><connections><connection><identifier v="destination"/><connRef v="1416688916"/><connRefOutput v="1416688917"/></connection><connection><identifier v="source"/><connRef v="1416686383"/><connRefOutput v="1416677960"/></connection><connection><identifier v="opacity"/><connRef v="1416688999"/><connRefOutput v="1416686355"/></connection></connections><GUILayout><gpos v="-1776 688 0"/></GUILayout><compOutputs><compOutput><uid v="1416688917"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1416700671"/><connections><connection><identifier v="Source"/><connRef v="1416599690"/><connRefOutput v="1416599691"/></connection><connection><identifier v="Effect"/><connRef v="1416700684"/><connRefOutput v="1416700685"/></connection></connections><GUILayout><gpos v="-1979.52222 -385.516174 0"/></GUILayout><compOutputs><compOutput><uid v="1416700672"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///slope_blur_grayscale_2?dependency=1449402040"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="32"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="9.53999996"/></paramValue></parameter><parameter><name v="mode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1416700672"/><identifier v="Slope_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416700684"/><GUILayout><gpos v="-2092 -225.516174 0"/></GUILayout><compOutputs><compOutput><uid v="1416700685"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///fractal_sum_base_2?dependency=1417357149"/><parameters><parameter><name v="Roughness"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="MinLevel"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1416700685"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416702022"/><connections><connection><identifier v="Input_1"/><connRef v="1416700671"/><connRefOutput v="1416700672"/></connection></connections><GUILayout><gpos v="-1804 -577.516174 0"/></GUILayout><compOutputs><compOutput><uid v="1416593992"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1149349207"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.709999979"/></paramValue></parameter><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1416593992"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416712421"/><connections><connection><identifier v="Input_1"/><connRef v="1416623853"/><connRefOutput v="1416623854"/></connection></connections><GUILayout><gpos v="4418.73486 -505.166687 0"/></GUILayout><compOutputs><compOutput><uid v="1416712422"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1149349207"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1416712422"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416712452"/><connections><connection><identifier v="opacity"/><connRef v="1416714638"/><connRefOutput v="1416714639"/></connection><connection><identifier v="destination"/><connRef v="1416702587"/><connRefOutput v="1416702588"/></connection><connection><identifier v="source"/><connRef v="1488177132"/><connRefOutput v="1488176508"/></connection></connections><GUILayout><gpos v="6068.16602 -1849.16663 0"/></GUILayout><compOutputs><compOutput><uid v="1416712453"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1416714638"/><connections><connection><identifier v="Source"/><connRef v="1416712421"/><connRefOutput v="1416712422"/></connection></connections><GUILayout><gpos v="4578.73486 -505.166687 0"/></GUILayout><compOutputs><compOutput><uid v="1416714639"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///invert_grayscale?dependency=1368115694"/><parameters/><outputBridgings><outputBridging><uid v="1416714639"/><identifier v="Invert_Grayscale"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416740780"/><connections><connection><identifier v="Source"/><connRef v="1416667732"/><connRefOutput v="1416667733"/></connection></connections><GUILayout><gpos v="-492 -449.516174 0"/></GUILayout><compOutputs><compOutput><uid v="1416740781"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///non_uniform_blur_grayscale?dependency=1290776959"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="16"/></paramValue></parameter><parameter><name v="Blades"/><relativeTo v="0"/><paramValue><constantValueInt32 v="9"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.63999999"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1416740781"/><identifier v="Non_Uniform_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416743959"/><connections><connection><identifier v="destination"/><connRef v="1416712452"/><connRefOutput v="1416712453"/></connection><connection><identifier v="opacity"/><connRef v="1416927155"/><connRefOutput v="1416927156"/></connection></connections><GUILayout><gpos v="6307.79297 -1849.16663 0"/></GUILayout><compOutputs><compOutput><uid v="1416743960"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1416748831"/><connections><connection><identifier v="destination"/><connRef v="1416743959"/><connRefOutput v="1416743960"/></connection><connection><identifier v="source"/><connRef v="1416752777"/><connRefOutput v="1416752778"/></connection><connection><identifier v="opacity"/><connRef v="1416927220"/><connRefOutput v="1416927156"/></connection></connections><GUILayout><gpos v="6563.79297 -1849.16663 0"/></GUILayout><compOutputs><compOutput><uid v="1416748832"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1416752777"/><GUILayout><gpos v="6485.79297 -1870.5 0"/><docked v="1"/><dockDistance v="3648 304"/></GUILayout><compOutputs><compOutput><uid v="1416752778"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1416752785"/><paramsArrayCells><paramsArrayCell><uid v="1416752786"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0 0 0 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1416752787"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.934782624 0.82211715 0.125 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1416773180"/><connections><connection><identifier v="destination"/><connRef v="1416599508"/><connRefOutput v="1416599509"/></connection><connection><identifier v="opacity"/><connRef v="1416599598"/><connRefOutput v="1416599599"/></connection><connection><identifier v="source"/><connRef v="1416773224"/><connRefOutput v="1416773225"/></connection></connections><GUILayout><gpos v="4212.16602 -1849.16663 0"/></GUILayout><compOutputs><compOutput><uid v="1416773181"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1488176930"/><paramNodes><paramNode><uid v="1488176930"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="opacitymult_1"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1416773224"/><connections><connection><identifier v="input1"/><connRef v="1416607940"/><connRefOutput v="1416607941"/></connection></connections><GUILayout><gpos v="4134.16602 -1870.5 0"/><docked v="1"/><dockDistance v="5872 -1159.85742"/></GUILayout><compOutputs><compOutput><uid v="1416773225"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1416701398"/><GUILayout><gpos v="2499.00195 -1113.16663 0"/></GUILayout><compOutputs><compOutput><uid v="1416701399"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///gradient_linear_2?dependency=1416701314"/><parameters><parameter><name v="rotation"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="Tiling"/><relativeTo v="0"/><paramValue><constantValueInt32 v="16"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1416701399"/><identifier v="Simple_Gradient_2"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416701479"/><connections><connection><identifier v="input"/><connRef v="1416607677"/><connRefOutput v="1416607678"/></connection></connections><GUILayout><gpos v="2499.00195 -857.166687 0"/></GUILayout><compOutputs><compOutput><uid v="1416701480"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///hbao?dependency=1297162667"/><parameters><parameter><name v="height_depth"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.769999981"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1416701480"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416707766"/><connections><connection><identifier v="Source"/><connRef v="1416701479"/><connRefOutput v="1416701480"/></connection></connections><GUILayout><gpos v="2659.00195 -857.166687 0"/></GUILayout><compOutputs><compOutput><uid v="1416707767"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1299236171"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="4.80999994"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1416707767"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416707840"/><connections><connection><identifier v="inputgradient"/><connRef v="1416707766"/><connRefOutput v="1416707767"/></connection><connection><identifier v="input1"/><connRef v="1416712508"/><connRefOutput v="1416712509"/></connection></connections><GUILayout><gpos v="3235.00195 -1145.16663 0"/></GUILayout><compOutputs><compOutput><uid v="1416707841"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="warp"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1416707949"/><connections><connection><identifier v="Input"/><connRef v="1416709641"/><connRefOutput v="1416709642"/></connection></connections><GUILayout><gpos v="3523.00195 -1145.16663 0"/></GUILayout><compOutputs><compOutput><uid v="1416707950"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///quantize_grayscale?dependency=1417466547"/><parameters/><outputBridgings><outputBridging><uid v="1416707950"/><identifier v="Quantize"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416709641"/><connections><connection><identifier v="input1"/><connRef v="1416707840"/><connRefOutput v="1416707841"/></connection><connection><identifier v="inputgradient"/><connRef v="1416709678"/><connRefOutput v="1416709679"/></connection></connections><GUILayout><gpos v="3395.00195 -1145.16663 0"/></GUILayout><compOutputs><compOutput><uid v="1416709642"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="warp"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0199999996"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1416709678"/><connections><connection><identifier v="Source"/><connRef v="1416701479"/><connRefOutput v="1416701480"/></connection></connections><GUILayout><gpos v="2819.00195 -857.166687 0"/></GUILayout><compOutputs><compOutput><uid v="1416709679"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///blur_hq_grayscale?dependency=1299236171"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.829999983"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1416709679"/><identifier v="Blur_HQ"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416709796"/><connections><connection><identifier v="source"/><connRef v="1416709810"/><connRefOutput v="1416709811"/></connection><connection><identifier v="opacity"/><connRef v="1416599598"/><connRefOutput v="1416599599"/></connection><connection><identifier v="destination"/><connRef v="1416691545"/><connRefOutput v="1416691546"/></connection></connections><GUILayout><gpos v="5268.16602 -1849.16663 0"/></GUILayout><compOutputs><compOutput><uid v="1416709797"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1488177069"/><paramNodes><paramNode><uid v="1488177069"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="opacitymult_3"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1416709810"/><connections><connection><identifier v="input1"/><connRef v="1416710171"/><connRefOutput v="1416710172"/></connection></connections><GUILayout><gpos v="5190.16602 -1870.5 0"/><docked v="1"/><dockDistance v="96 -192"/></GUILayout><compOutputs><compOutput><uid v="1416709811"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1416709869"/><connections><connection><identifier v="input"/><connRef v="1416707949"/><connRefOutput v="1416707950"/></connection></connections><GUILayout><gpos v="3668.04492 -1145.16663 0"/></GUILayout><compOutputs><compOutput><uid v="1416709870"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///edge_detect?dependency=1407202483"/><parameters><parameter><name v="edge_width"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.19000006"/></paramValue></parameter><parameter><name v="edge_roundness"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1416709870"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416709895"/><connections><connection><identifier v="mask"/><connRef v="1416709869"/><connRefOutput v="1416709870"/></connection></connections><GUILayout><gpos v="3828.04492 -1145.16663 0"/></GUILayout><compOutputs><compOutput><uid v="1416709896"/><comptype v="1"/></compOutput><compOutput><uid v="1416709897"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill?dependency=1323881949"/><parameters><parameter><name v="profile"/><relativeTo v="0"/><paramValue><constantValueInt32 v="100"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1416709896"/><identifier v="output"/></outputBridging><outputBridging><uid v="1416709897"/><identifier v="referenceMap"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416709978"/><connections><connection><identifier v="input"/><connRef v="1416709895"/><connRefOutput v="1416709896"/></connection></connections><GUILayout><gpos v="3988.04492 -1145.16663 0"/></GUILayout><compOutputs><compOutput><uid v="1416709979"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_random_grayscale?dependency=1323881949"/><parameters/><outputBridgings><outputBridging><uid v="1416709979"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416710171"/><connections><connection><identifier v="source"/><connRef v="1416709978"/><connRefOutput v="1416709979"/></connection><connection><identifier v="mask"/><connRef v="1416709869"/><connRefOutput v="1416709870"/></connection></connections><GUILayout><gpos v="4148.04492 -1145.16663 0"/></GUILayout><compOutputs><compOutput><uid v="1416710172"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters><parameter><name v="combinedistance"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="256"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1416712508"/><connections><connection><identifier v="Effect"/><connRef v="1416714267"/><connRefOutput v="1416714268"/></connection><connection><identifier v="Source"/><connRef v="1416701398"/><connRefOutput v="1416701399"/></connection></connections><GUILayout><gpos v="3075.00195 -1145.16663 0"/></GUILayout><compOutputs><compOutput><uid v="1416712509"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///slope_blur_grayscale_2?dependency=1449402040"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="32"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.379999995"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1416712509"/><identifier v="Slope_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416714267"/><GUILayout><gpos v="2499.00195 -985.166687 0"/></GUILayout><compOutputs><compOutput><uid v="1416714268"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///fractal_sum_base_2?dependency=1417357149"/><parameters><parameter><name v="Roughness"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.370000005"/></paramValue></parameter><parameter><name v="MinLevel"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1416714268"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416691400"/><connections><connection><identifier v="Input"/><connRef v="1416701479"/><connRefOutput v="1416701480"/></connection></connections><GUILayout><gpos v="2644.16602 -729.166687 0"/></GUILayout><compOutputs><compOutput><uid v="1416691401"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///quantize_grayscale?dependency=1417466547"/><parameters><parameter><name v="Quantize"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1416691401"/><identifier v="Quantize"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416691430"/><connections><connection><identifier v="Input_1"/><connRef v="1416701479"/><connRefOutput v="1416701480"/></connection></connections><GUILayout><gpos v="2644.16602 -601.166687 0"/></GUILayout><compOutputs><compOutput><uid v="1416691431"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1149349207"/><parameters><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.979999959"/></paramValue></parameter><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.189999998"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1416691431"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416691545"/><connections><connection><identifier v="opacity"/><connRef v="1416599598"/><connRefOutput v="1416599599"/></connection><connection><identifier v="source"/><connRef v="1416691568"/><connRefOutput v="1416691569"/></connection><connection><identifier v="destination"/><connRef v="1416694351"/><connRefOutput v="1416693910"/></connection></connections><GUILayout><gpos v="5076.16602 -1849.16663 0"/></GUILayout><compOutputs><compOutput><uid v="1416691546"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1488177041"/><paramNodes><paramNode><uid v="1488177041"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="opacitymult_2"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1416691568"/><connections><connection><identifier v="input1"/><connRef v="1416691430"/><connRefOutput v="1416691431"/></connection></connections><GUILayout><gpos v="4998.16602 -1870.5 0"/><docked v="1"/><dockDistance v="640 -464"/></GUILayout><compOutputs><compOutput><uid v="1416691569"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1416693764"/><connections><connection><identifier v="input"/><connRef v="1416693766"/><connRefOutput v="1416709896"/></connection></connections><GUILayout><gpos v="3988.16602 -985.166687 0"/></GUILayout><compOutputs><compOutput><uid v="1416709979"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_random_grayscale?dependency=1323881949"/><parameters><parameter><name v="randomseed"/><relativeTo v="2"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1416709979"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416693765"/><connections><connection><identifier v="input"/><connRef v="1416691400"/><connRefOutput v="1416691401"/></connection></connections><GUILayout><gpos v="3668.16602 -985.166687 0"/></GUILayout><compOutputs><compOutput><uid v="1416709870"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///edge_detect?dependency=1407202483"/><parameters><parameter><name v="edge_width"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.19000006"/></paramValue></parameter><parameter><name v="edge_roundness"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1416709870"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416693766"/><connections><connection><identifier v="mask"/><connRef v="1416693765"/><connRefOutput v="1416709870"/></connection></connections><GUILayout><gpos v="3828.16602 -985.166687 0"/></GUILayout><compOutputs><compOutput><uid v="1416709896"/><comptype v="1"/></compOutput><compOutput><uid v="1416709897"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill?dependency=1323881949"/><parameters><parameter><name v="profile"/><relativeTo v="0"/><paramValue><constantValueInt32 v="100"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1416709896"/><identifier v="output"/></outputBridging><outputBridging><uid v="1416709897"/><identifier v="referenceMap"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416693767"/><connections><connection><identifier v="source"/><connRef v="1416693764"/><connRefOutput v="1416709979"/></connection><connection><identifier v="mask"/><connRef v="1416693765"/><connRefOutput v="1416709870"/></connection></connections><GUILayout><gpos v="4148.16602 -985.166687 0"/></GUILayout><compOutputs><compOutput><uid v="1416710172"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters><parameter><name v="combinedistance"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="256"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1416693909"/><connections><connection><identifier v="opacity"/><connRef v="1416694149"/><connRefOutput v="1416694150"/></connection><connection><identifier v="destination"/><connRef v="1416773180"/><connRefOutput v="1416773181"/></connection><connection><identifier v="source"/><connRef v="1488176949"/><connRefOutput v="1488176508"/></connection></connections><GUILayout><gpos v="4500.16602 -1849.16663 0"/></GUILayout><compOutputs><compOutput><uid v="1416693910"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.569999993"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1416693936"/><connections><connection><identifier v="Input_1"/><connRef v="1416693767"/><connRefOutput v="1416710172"/></connection></connections><GUILayout><gpos v="4308.16602 -1273.16663 0"/></GUILayout><compOutputs><compOutput><uid v="1416693937"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1149349207"/><parameters><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.199999988"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1416693937"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416694149"/><connections><connection><identifier v="source"/><connRef v="1416693936"/><connRefOutput v="1416693937"/></connection><connection><identifier v="destination"/><connRef v="1416599598"/><connRefOutput v="1416599599"/></connection></connections><GUILayout><gpos v="4468.16602 -1273.16663 0"/></GUILayout><compOutputs><compOutput><uid v="1416694150"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1416694213"/><connections><connection><identifier v="input"/><connRef v="1416691400"/><connRefOutput v="1416691401"/></connection></connections><GUILayout><gpos v="3668.16602 -825.166687 0"/></GUILayout><compOutputs><compOutput><uid v="1416709870"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///edge_detect?dependency=1407202483"/><parameters><parameter><name v="edge_width"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.19000006"/></paramValue></parameter><parameter><name v="edge_roundness"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1416709870"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416694214"/><connections><connection><identifier v="mask"/><connRef v="1416694213"/><connRefOutput v="1416709870"/></connection></connections><GUILayout><gpos v="3828.16602 -825.166687 0"/></GUILayout><compOutputs><compOutput><uid v="1416709896"/><comptype v="1"/></compOutput><compOutput><uid v="1416709897"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill?dependency=1323881949"/><parameters><parameter><name v="profile"/><relativeTo v="0"/><paramValue><constantValueInt32 v="100"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1416709896"/><identifier v="output"/></outputBridging><outputBridging><uid v="1416709897"/><identifier v="referenceMap"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416694215"/><connections><connection><identifier v="input"/><connRef v="1416694214"/><connRefOutput v="1416709896"/></connection></connections><GUILayout><gpos v="3988.16602 -825.166687 0"/></GUILayout><compOutputs><compOutput><uid v="1416709979"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_random_grayscale?dependency=1323881949"/><parameters><parameter><name v="randomseed"/><relativeTo v="2"/><paramValue><constantValueInt32 v="19"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1416709979"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416694216"/><connections><connection><identifier v="source"/><connRef v="1416694215"/><connRefOutput v="1416709979"/></connection><connection><identifier v="mask"/><connRef v="1416694213"/><connRefOutput v="1416709870"/></connection></connections><GUILayout><gpos v="4148.16602 -825.166687 0"/></GUILayout><compOutputs><compOutput><uid v="1416710172"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters><parameter><name v="combinedistance"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="256"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1416694333"/><connections><connection><identifier v="Input_1"/><connRef v="1416694216"/><connRefOutput v="1416710172"/></connection></connections><GUILayout><gpos v="4308.16602 -825.166687 0"/></GUILayout><compOutputs><compOutput><uid v="1416693937"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1149349207"/><parameters><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0899999961"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1416693937"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416694334"/><connections><connection><identifier v="source"/><connRef v="1416694333"/><connRefOutput v="1416693937"/></connection><connection><identifier v="destination"/><connRef v="1416599598"/><connRefOutput v="1416599599"/></connection></connections><GUILayout><gpos v="4468.16602 -825.166687 0"/></GUILayout><compOutputs><compOutput><uid v="1416694150"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1416694350"/><connections><connection><identifier v="input1"/><connRef v="1416694334"/><connRefOutput v="1416694150"/></connection></connections><GUILayout><gpos v="4742.16602 -1870.5 0"/><docked v="1"/><dockDistance v="192 -288"/></GUILayout><compOutputs><compOutput><uid v="1416693926"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1416694009"/><paramsArrayCells><paramsArrayCell><uid v="1416694010"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0 0 0 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1416694011"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.415686309 0.411717266 0.399384886 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1416694351"/><connections><connection><identifier v="source"/><connRef v="1416694350"/><connRefOutput v="1416693926"/></connection><connection><identifier v="opacity"/><connRef v="1416694334"/><connRefOutput v="1416694150"/></connection><connection><identifier v="destination"/><connRef v="1416693909"/><connRefOutput v="1416693910"/></connection></connections><GUILayout><gpos v="4820.16602 -1849.16663 0"/></GUILayout><compOutputs><compOutput><uid v="1416693910"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1416701639"/><connections><connection><identifier v="input1"/><connRef v="1416607677"/><connRefOutput v="1416607678"/></connection></connections><GUILayout><gpos v="4436.16602 -2169.16675 0"/></GUILayout><compOutputs><compOutput><uid v="1416701640"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="normal"/><parameters><parameter><name v="input2alpha"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="inversedy"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="10"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1416702442"/><connections><connection><identifier v="input"/><connRef v="1416701639"/><connRefOutput v="1416701640"/></connection></connections><GUILayout><gpos v="4596.16602 -2169.16675 0"/></GUILayout><compOutputs><compOutput><uid v="1416702443"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///curvature_smooth?dependency=1449590280"/><parameters><parameter><name v="normal_format"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1416702443"/><identifier v="height"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416702470"/><connections><connection><identifier v="Input_1"/><connRef v="1416702442"/><connRefOutput v="1416702443"/></connection></connections><GUILayout><gpos v="4756.16602 -2169.16675 0"/></GUILayout><compOutputs><compOutput><uid v="1416702471"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1149349207"/><parameters><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.379999995"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1416702471"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416702587"/><connections><connection><identifier v="destination"/><connRef v="1416709796"/><connRefOutput v="1416709797"/></connection><connection><identifier v="opacity"/><connRef v="1416599598"/><connRefOutput v="1416599599"/></connection><connection><identifier v="source"/><connRef v="1416706214"/><connRefOutput v="1416706215"/></connection></connections><GUILayout><gpos v="5524.16602 -1849.16663 0"/></GUILayout><compOutputs><compOutput><uid v="1416702588"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1488177103"/><paramNodes><paramNode><uid v="1488177103"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="opacitymult_4"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1416706214"/><connections><connection><identifier v="input1"/><connRef v="1416702470"/><connRefOutput v="1416702471"/></connection></connections><GUILayout><gpos v="5446.16602 -1870.5 0"/><docked v="1"/><dockDistance v="112.885254 -96"/></GUILayout><compOutputs><compOutput><uid v="1416706215"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1416709987"/><GUILayout><gpos v="6548.16602 -2041.16663 0"/></GUILayout><compOutputs><compOutput><uid v="1416709988"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///hbao?dependency=1297162667"/><parameters><parameter><name v="height_depth"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0599999987"/></paramValue></parameter><parameter><name v="radius"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.00999999978"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1416709988"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416737824"/><connections><connection><identifier v="destination"/><connRef v="1416748831"/><connRefOutput v="1416748832"/></connection><connection><identifier v="source"/><connRef v="1416737845"/><connRefOutput v="1416737846"/></connection><connection><identifier v="opacity"/><connRef v="1416927155"/><connRefOutput v="1416927156"/></connection></connections><GUILayout><gpos v="6868.16602 -1881.16663 0"/></GUILayout><compOutputs><compOutput><uid v="1416737825"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.269999981"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1416737832"/><connections><connection><identifier v="Source"/><connRef v="1416709987"/><connRefOutput v="1416709988"/></connection></connections><GUILayout><gpos v="6708.16602 -2041.16663 0"/></GUILayout><compOutputs><compOutput><uid v="1416737833"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///invert_grayscale?dependency=1368115694"/><parameters/><outputBridgings><outputBridging><uid v="1416737833"/><identifier v="Invert_Grayscale"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416737845"/><connections><connection><identifier v="input1"/><connRef v="1416737832"/><connRefOutput v="1416737833"/></connection></connections><GUILayout><gpos v="6790.16602 -1902.5 0"/><docked v="1"/><dockDistance v="-160 -96"/></GUILayout><compOutputs><compOutput><uid v="1416737846"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1416755226"/><connections><connection><identifier v="source"/><connRef v="1416615158"/><connRefOutput v="1416615159"/></connection><connection><identifier v="opacity"/><connRef v="1416755257"/><connRefOutput v="1416755258"/></connection><connection><identifier v="destination"/><connRef v="1488177354"/><connRefOutput v="1488177355"/></connection></connections><GUILayout><gpos v="4756.16602 70.833313 0"/></GUILayout><compOutputs><compOutput><uid v="1416755227"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0899999961"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1416755257"/><connections><connection><identifier v="Source"/><connRef v="1416599598"/><connRefOutput v="1416599599"/></connection></connections><GUILayout><gpos v="4116.16602 326.833313 0"/></GUILayout><compOutputs><compOutput><uid v="1416755258"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///invert_grayscale?dependency=1368115694"/><parameters/><outputBridgings><outputBridging><uid v="1416755258"/><identifier v="Invert_Grayscale"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1416755348"/><connections><connection><identifier v="destination"/><connRef v="1416755226"/><connRefOutput v="1416755227"/></connection><connection><identifier v="source"/><connRef v="1416627654"/><connRefOutput v="1416627655"/></connection></connections><GUILayout><gpos v="4916.16602 70.833313 0"/></GUILayout><compOutputs><compOutput><uid v="1416755349"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.049999997"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1416755421"/><connections><connection><identifier v="destination"/><connRef v="1416755348"/><connRefOutput v="1416755349"/></connection><connection><identifier v="source"/><connRef v="1416694149"/><connRefOutput v="1416694150"/></connection><connection><identifier v="opacity"/><connRef v="1416599598"/><connRefOutput v="1416599599"/></connection></connections><GUILayout><gpos v="5140.16602 70.833313 0"/></GUILayout><compOutputs><compOutput><uid v="1416755422"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.049999997"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1416755482"/><connections><connection><identifier v="opacity"/><connRef v="1416599598"/><connRefOutput v="1416599599"/></connection><connection><identifier v="destination"/><connRef v="1416755421"/><connRefOutput v="1416755422"/></connection><connection><identifier v="source"/><connRef v="1416694334"/><connRefOutput v="1416694150"/></connection></connections><GUILayout><gpos v="5332.16602 70.833313 0"/></GUILayout><compOutputs><compOutput><uid v="1416755422"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0299999993"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1416755532"/><connections><connection><identifier v="opacity"/><connRef v="1416599598"/><connRefOutput v="1416599599"/></connection><connection><identifier v="destination"/><connRef v="1416755482"/><connRefOutput v="1416755422"/></connection><connection><identifier v="source"/><connRef v="1416693767"/><connRefOutput v="1416710172"/></connection></connections><GUILayout><gpos v="5556.16602 70.833313 0"/></GUILayout><compOutputs><compOutput><uid v="1416755422"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.099999994"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1416755617"/><connections><connection><identifier v="destination"/><connRef v="1416755532"/><connRefOutput v="1416755422"/></connection><connection><identifier v="opacity"/><connRef v="1416714638"/><connRefOutput v="1416714639"/></connection><connection><identifier v="source"/><connRef v="1488177342"/><connRefOutput v="1488176508"/></connection></connections><GUILayout><gpos v="5716.16602 70.833313 0"/></GUILayout><compOutputs><compOutput><uid v="1416755618"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1416755669"/><connections><connection><identifier v="destination"/><connRef v="1416755617"/><connRefOutput v="1416755618"/></connection><connection><identifier v="source"/><connRef v="1488177599"/><connRefOutput v="1488176508"/></connection></connections><GUILayout><gpos v="5972.16602 70.833313 0"/></GUILayout><compOutputs><compOutput><uid v="1416755670"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1416755796"/><connections><connection><identifier v="destination"/><connRef v="1416755669"/><connRefOutput v="1416755670"/></connection></connections><GUILayout><gpos v="6160 80 0"/></GUILayout><compOutputs><compOutput><uid v="1416755797"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1416927155"/><connections><connection><identifier v="source"/><connRef v="1416614901"/><connRefOutput v="1416614902"/></connection></connections><GUILayout><gpos v="6100.16602 -1689.16663 0"/></GUILayout><compOutputs><compOutput><uid v="1416927156"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1416927220"/><connections><connection><identifier v="source"/><connRef v="1416614901"/><connRefOutput v="1416614902"/></connection></connections><GUILayout><gpos v="6376.66602 -1681.66663 0"/></GUILayout><compOutputs><compOutput><uid v="1416927156"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1416927247"/><connections><connection><identifier v="destination"/><connRef v="1416659702"/><connRefOutput v="1416659703"/></connection></connections><GUILayout><gpos v="-208 -2831.94946 0"/></GUILayout><compOutputs><compOutput><uid v="1416927248"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488176523"/><GUILayout><gpos v="3092.16602 -1608.30579 0"/></GUILayout><compOutputs><compOutput><uid v="1488176508"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="uniform"/><parameters><parameter><name v="outputsize"/><relativeTo v="0"/><paramValue><constantValueInt2 v="4 4"/></paramValue></parameter><parameter><name v="colorswitch"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="outputcolor"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.370854795 0.580645144 0.0351906158 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488176526"/><GUILayout><gpos v="4276.16602 198.833313 0"/></GUILayout><compOutputs><compOutput><uid v="1488176508"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="uniform"/><parameters><parameter><name v="outputsize"/><relativeTo v="0"/><paramValue><constantValueInt2 v="4 4"/></paramValue></parameter><parameter><name v="colorswitch"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="outputcolor"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1488177560"/><paramNodes><paramNode><uid v="1488177560"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="outputcolor_7"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488176546"/><connections><connection><identifier v="input1"/><connRef v="1416615158"/><connRefOutput v="1416615159"/></connection></connections><GUILayout><gpos v="3060.16602 -1817.16663 0"/></GUILayout><compOutputs><compOutput><uid v="1488176547"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1488176562"/><connections><connection><identifier v="source"/><connRef v="1488176546"/><connRefOutput v="1488176547"/></connection><connection><identifier v="destination"/><connRef v="1488176523"/><connRefOutput v="1488176508"/></connection></connections><GUILayout><gpos v="3366.79834 -1704.44812 0"/></GUILayout><compOutputs><compOutput><uid v="1488176563"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="outputsize"/><relativeTo v="1"/><paramValue><constantValueInt2 v="0 0"/></paramValue></parameter><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.479999989"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488176689"/><GUILayout><gpos v="3348.16602 -1832.02368 0"/></GUILayout><compOutputs><compOutput><uid v="1488176508"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="uniform"/><parameters><parameter><name v="outputsize"/><relativeTo v="0"/><paramValue><constantValueInt2 v="4 4"/></paramValue></parameter><parameter><name v="outputcolor"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.282352954 0.43921569 0.0274509806 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488176698"/><connections><connection><identifier v="destination"/><connRef v="1488176689"/><connRefOutput v="1488176508"/></connection><connection><identifier v="source"/><connRef v="1488176707"/><connRefOutput v="1488176708"/></connection></connections><GUILayout><gpos v="3558.16504 -1945.16663 0"/></GUILayout><compOutputs><compOutput><uid v="1488176563"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="outputsize"/><relativeTo v="1"/><paramValue><constantValueInt2 v="0 0"/></paramValue></parameter><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488176707"/><connections><connection><identifier v="input1"/><connRef v="1416627654"/><connRefOutput v="1416627655"/></connection></connections><GUILayout><gpos v="3364.94434 -2041.16663 0"/></GUILayout><compOutputs><compOutput><uid v="1488176708"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1488176872"/><GUILayout><gpos v="3717.25098 -2041.16663 0"/></GUILayout><compOutputs><compOutput><uid v="1488176508"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="uniform"/><parameters><parameter><name v="outputsize"/><relativeTo v="0"/><paramValue><constantValueInt2 v="4 4"/></paramValue></parameter><parameter><name v="outputcolor"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.376344085 0.356110513 0.319322258 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488176949"/><GUILayout><gpos v="4180.16602 -1657.16663 0"/></GUILayout><compOutputs><compOutput><uid v="1488176508"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="uniform"/><parameters><parameter><name v="outputsize"/><relativeTo v="0"/><paramValue><constantValueInt2 v="4 4"/></paramValue></parameter><parameter><name v="outputcolor"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.403225809 0.35356164 0.29325515 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488177132"/><GUILayout><gpos v="5556.16602 -1561.16663 0"/></GUILayout><compOutputs><compOutput><uid v="1488176508"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="uniform"/><parameters><parameter><name v="outputsize"/><relativeTo v="0"/><paramValue><constantValueInt2 v="4 4"/></paramValue></parameter><parameter><name v="outputcolor"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1488177244"/><paramNodes><paramNode><uid v="1488177244"/><function v="get_float4"/><type v="2048"/><funcDatas><funcData><name v="get_float4"/><constantValue><constantValueString v="outputcolor_4"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488177339"/><GUILayout><gpos v="4226.80518 -89.166687 0"/></GUILayout><compOutputs><compOutput><uid v="1488176508"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="uniform"/><parameters><parameter><name v="outputsize"/><relativeTo v="0"/><paramValue><constantValueInt2 v="4 4"/></paramValue></parameter><parameter><name v="colorswitch"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="outputcolor"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1488177538"/><paramNodes><paramNode><uid v="1488177538"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="outputcolor_6"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488177342"/><GUILayout><gpos v="5492.16602 230.833313 0"/></GUILayout><compOutputs><compOutput><uid v="1488176508"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="uniform"/><parameters><parameter><name v="outputsize"/><relativeTo v="0"/><paramValue><constantValueInt2 v="4 4"/></paramValue></parameter><parameter><name v="colorswitch"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="outputcolor"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1488177710"/><paramNodes><paramNode><uid v="1488177710"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="outputcolor_9"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488177354"/><connections><connection><identifier v="opacity"/><connRef v="1416755257"/><connRefOutput v="1416755258"/></connection><connection><identifier v="source"/><connRef v="1488177339"/><connRefOutput v="1488176508"/></connection><connection><identifier v="destination"/><connRef v="1488176526"/><connRefOutput v="1488176508"/></connection></connections><GUILayout><gpos v="4628.16602 116.162827 0"/></GUILayout><compOutputs><compOutput><uid v="1488177355"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="outputsize"/><relativeTo v="1"/><paramValue><constantValueInt2 v="0 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488177599"/><GUILayout><gpos v="5748.16602 262.833313 0"/></GUILayout><compOutputs><compOutput><uid v="1488176508"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="uniform"/><parameters><parameter><name v="outputsize"/><relativeTo v="0"/><paramValue><constantValueInt2 v="4 4"/></paramValue></parameter><parameter><name v="colorswitch"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="outputcolor"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1488177632"/><paramNodes><paramNode><uid v="1488177632"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="outputcolor_8"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter></parameters></compFilter></compImplementation></compNode></compNodes><baseParameters/><options><option><name v="defaultParentSize"/><value v="11x11"/></option><option><name v="export/fromGraph/autoExport"/><value v="false"/></option><option><name v="export/fromGraph/destination"/><value v="D:/work/Annery/stylized material/stylized material/images/worn stone path"/></option><option><name v="export/fromGraph/extension"/><value v="png"/></option><option><name v="export/fromGraph/outputs/AO"/><value v="true"/></option><option><name v="export/fromGraph/outputs/basecolor"/><value v="false"/></option><option><name v="export/fromGraph/outputs/height"/><value v="false"/></option><option><name v="export/fromGraph/outputs/metallic"/><value v="false"/></option><option><name v="export/fromGraph/outputs/normal"/><value v="false"/></option><option><name v="export/fromGraph/outputs/roughness"/><value v="false"/></option><option><name v="export/fromGraph/outputsColorspace/AO"/><value v="Raw"/></option><option><name v="export/fromGraph/outputsColorspace/basecolor"/><value v="sRGB"/></option><option><name v="export/fromGraph/outputsColorspace/height"/><value v="Raw"/></option><option><name v="export/fromGraph/outputsColorspace/metallic"/><value v="Raw"/></option><option><name v="export/fromGraph/outputsColorspace/normal"/><value v="Raw"/></option><option><name v="export/fromGraph/outputsColorspace/roughness"/><value v="Raw"/></option><option><name v="export/fromGraph/pattern"/><value v="$(graph)_$(identifier)"/></option></options><root><rootOutputs><rootOutput><output v="1416540817"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1416540819"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1416540821"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1416540823"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1416540825"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1416540827"/><format v="0"/><usertag v=""/></rootOutput></rootOutputs></root></graph></content></package>
