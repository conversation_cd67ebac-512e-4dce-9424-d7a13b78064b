%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -2076007791, guid: aa81bbec85a3f423193272a7c6154986, type: 3}
  m_Name: 172 Flux Drums - Scene 2
  m_EditorClassIdentifier: 
  mSourceClip: {fileID: 0}
  mAudioFilePath: ../FMOD Studio Projects/Beat Traveller Reload/Assets/GS_FLUX_172_Drum_Loop_07_V1.wav
  mSampleRate: 44100
  mIgnoreLatencyOffset: 0
  mTempoSections:
  - sectionName: New Section
    startSample: 0
    samplesPerBeat: 15383.************
    beatsPerMeasure: 4
    bStartNewMeasure: 1
  mTracks:
  - {fileID: 11400000, guid: 4202bed90bcc6794586a3281fe904c88, type: 2}
  - {fileID: 11400000, guid: 45b541391a61c82488e0cb39d4108fb3, type: 2}
  - {fileID: 11400000, guid: 7e463318aa3f5ca44ad84f78e461089f, type: 2}
  - {fileID: 11400000, guid: 688058058c168c74187018699424421b, type: 2}
  - {fileID: 11400000, guid: e136e913109bf574289f3dfbd8dbc536, type: 2}
