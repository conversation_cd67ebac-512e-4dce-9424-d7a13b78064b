fileFormatVersion: 2
guid: 9bd9265533309304db171aa9f916554a
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 21300000
    second: loading-animation_0
  - first:
      213: 21300002
    second: loading-animation_1
  - first:
      213: 21300004
    second: loading-animation_2
  - first:
      213: 21300006
    second: loading-animation_3
  - first:
      213: 21300008
    second: loading-animation_4
  - first:
      213: 21300010
    second: loading-animation_5
  - first:
      213: 21300012
    second: loading-animation_6
  - first:
      213: 21300014
    second: loading-animation_7
  - first:
      213: 21300016
    second: loading-animation_8
  - first:
      213: 21300018
    second: loading-animation_9
  - first:
      213: 21300020
    second: loading-animation_10
  - first:
      213: 21300022
    second: loading-animation_11
  - first:
      213: 21300024
    second: loading-animation_12
  - first:
      213: 21300026
    second: loading-animation_13
  - first:
      213: 21300028
    second: loading-animation_14
  - first:
      213: 21300030
    second: loading-animation_15
  - first:
      213: 21300032
    second: loading-animation_16
  - first:
      213: 21300034
    second: loading-animation_17
  - first:
      213: 21300036
    second: loading-animation_18
  - first:
      213: 21300038
    second: loading-animation_19
  - first:
      213: 21300040
    second: loading-animation_20
  - first:
      213: 21300042
    second: loading-animation_21
  - first:
      213: 21300044
    second: loading-animation_22
  - first:
      213: 21300046
    second: loading-animation_23
  - first:
      213: 21300048
    second: loading-animation_24
  - first:
      213: 21300050
    second: loading-animation_25
  - first:
      213: 21300052
    second: loading-animation_26
  - first:
      213: 21300054
    second: loading-animation_27
  - first:
      213: 21300056
    second: loading-animation_28
  - first:
      213: 21300058
    second: loading-animation_29
  - first:
      213: 21300060
    second: loading-animation_30
  - first:
      213: 21300062
    second: loading-animation_31
  - first:
      213: 21300064
    second: loading-animation_32
  - first:
      213: 21300066
    second: loading-animation_33
  - first:
      213: 21300068
    second: loading-animation_34
  - first:
      213: 21300070
    second: loading-animation_35
  - first:
      213: 21300072
    second: loading-animation_36
  - first:
      213: 21300074
    second: loading-animation_37
  - first:
      213: 21300076
    second: loading-animation_38
  - first:
      213: 21300078
    second: loading-animation_39
  - first:
      213: 21300080
    second: loading-animation_40
  - first:
      213: 21300082
    second: loading-animation_41
  - first:
      213: 21300084
    second: loading-animation_42
  - first:
      213: 21300086
    second: loading-animation_43
  - first:
      213: 21300088
    second: loading-animation_44
  - first:
      213: 21300090
    second: loading-animation_45
  - first:
      213: 21300092
    second: loading-animation_46
  - first:
      213: 21300094
    second: loading-animation_47
  - first:
      213: 21300096
    second: loading-animation_48
  - first:
      213: 21300098
    second: loading-animation_49
  - first:
      213: 21300100
    second: loading-animation_50
  - first:
      213: 21300102
    second: loading-animation_51
  - first:
      213: 21300104
    second: loading-animation_52
  - first:
      213: 21300106
    second: loading-animation_53
  - first:
      213: 21300108
    second: loading-animation_54
  - first:
      213: 21300110
    second: loading-animation_55
  - first:
      213: 21300112
    second: loading-animation_56
  - first:
      213: 21300114
    second: loading-animation_57
  - first:
      213: 21300116
    second: loading-animation_58
  - first:
      213: 21300118
    second: loading-animation_59
  - first:
      213: 21300120
    second: loading-animation_60
  - first:
      213: 21300122
    second: loading-animation_61
  - first:
      213: 21300124
    second: loading-animation_62
  - first:
      213: 21300126
    second: loading-animation_63
  - first:
      213: 21300128
    second: loading-animation_64
  - first:
      213: 21300130
    second: loading-animation_65
  - first:
      213: 21300132
    second: loading-animation_66
  - first:
      213: 21300134
    second: loading-animation_67
  - first:
      213: 21300136
    second: loading-animation_68
  - first:
      213: 21300138
    second: loading-animation_69
  - first:
      213: 21300140
    second: loading-animation_70
  - first:
      213: 21300142
    second: loading-animation_71
  - first:
      213: 21300144
    second: loading-animation_72
  - first:
      213: 21300146
    second: loading-animation_73
  - first:
      213: 21300148
    second: loading-animation_74
  - first:
      213: 21300150
    second: loading-animation_75
  - first:
      213: 21300152
    second: loading-animation_76
  - first:
      213: 21300154
    second: loading-animation_77
  - first:
      213: 21300156
    second: loading-animation_78
  - first:
      213: 21300158
    second: loading-animation_79
  - first:
      213: 21300160
    second: loading-animation_80
  - first:
      213: 21300162
    second: loading-animation_81
  - first:
      213: 21300164
    second: loading-animation_82
  - first:
      213: 21300166
    second: loading-animation_83
  - first:
      213: 21300168
    second: loading-animation_84
  - first:
      213: 21300170
    second: loading-animation_85
  - first:
      213: 21300172
    second: loading-animation_86
  - first:
      213: 21300174
    second: loading-animation_87
  - first:
      213: 21300176
    second: loading-animation_88
  - first:
      213: 21300178
    second: loading-animation_89
  - first:
      213: 21300180
    second: loading-animation_90
  - first:
      213: 21300182
    second: loading-animation_91
  - first:
      213: 21300184
    second: loading-animation_92
  - first:
      213: 21300186
    second: loading-animation_93
  externalObjects: {}
  serializedVersion: 12
  mipmaps:
    mipMapMode: 0
    enableMipMap: 1
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: -1
  maxTextureSize: 1024
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 16
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 1
  swizzle: 50462976
  cookieLightType: 1
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 1024
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 1024
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: WebGL
    maxTextureSize: 1024
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Android
    maxTextureSize: 1024
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: loading-animation_0
      rect:
        serializedVersion: 2
        x: 0
        y: 960
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 02305410000000000800000000000000
      internalID: 21300000
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_1
      rect:
        serializedVersion: 2
        x: 64
        y: 960
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 22305410000000000800000000000000
      internalID: 21300002
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_2
      rect:
        serializedVersion: 2
        x: 128
        y: 960
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 42305410000000000800000000000000
      internalID: 21300004
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_3
      rect:
        serializedVersion: 2
        x: 192
        y: 960
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 62305410000000000800000000000000
      internalID: 21300006
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_4
      rect:
        serializedVersion: 2
        x: 256
        y: 960
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 82305410000000000800000000000000
      internalID: 21300008
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_5
      rect:
        serializedVersion: 2
        x: 320
        y: 960
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a2305410000000000800000000000000
      internalID: 21300010
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_6
      rect:
        serializedVersion: 2
        x: 384
        y: 960
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c2305410000000000800000000000000
      internalID: 21300012
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_7
      rect:
        serializedVersion: 2
        x: 448
        y: 960
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e2305410000000000800000000000000
      internalID: 21300014
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_8
      rect:
        serializedVersion: 2
        x: 512
        y: 960
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 03305410000000000800000000000000
      internalID: 21300016
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_9
      rect:
        serializedVersion: 2
        x: 576
        y: 960
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 23305410000000000800000000000000
      internalID: 21300018
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_10
      rect:
        serializedVersion: 2
        x: 0
        y: 896
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 43305410000000000800000000000000
      internalID: 21300020
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_11
      rect:
        serializedVersion: 2
        x: 64
        y: 896
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 63305410000000000800000000000000
      internalID: 21300022
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_12
      rect:
        serializedVersion: 2
        x: 128
        y: 896
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 83305410000000000800000000000000
      internalID: 21300024
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_13
      rect:
        serializedVersion: 2
        x: 192
        y: 896
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a3305410000000000800000000000000
      internalID: 21300026
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_14
      rect:
        serializedVersion: 2
        x: 256
        y: 896
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c3305410000000000800000000000000
      internalID: 21300028
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_15
      rect:
        serializedVersion: 2
        x: 320
        y: 896
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e3305410000000000800000000000000
      internalID: 21300030
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_16
      rect:
        serializedVersion: 2
        x: 384
        y: 896
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 04305410000000000800000000000000
      internalID: 21300032
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_17
      rect:
        serializedVersion: 2
        x: 448
        y: 896
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 24305410000000000800000000000000
      internalID: 21300034
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_18
      rect:
        serializedVersion: 2
        x: 512
        y: 896
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 44305410000000000800000000000000
      internalID: 21300036
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_19
      rect:
        serializedVersion: 2
        x: 576
        y: 896
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 64305410000000000800000000000000
      internalID: 21300038
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_20
      rect:
        serializedVersion: 2
        x: 0
        y: 832
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 84305410000000000800000000000000
      internalID: 21300040
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_21
      rect:
        serializedVersion: 2
        x: 64
        y: 832
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a4305410000000000800000000000000
      internalID: 21300042
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_22
      rect:
        serializedVersion: 2
        x: 128
        y: 832
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c4305410000000000800000000000000
      internalID: 21300044
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_23
      rect:
        serializedVersion: 2
        x: 192
        y: 832
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e4305410000000000800000000000000
      internalID: 21300046
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_24
      rect:
        serializedVersion: 2
        x: 256
        y: 832
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 05305410000000000800000000000000
      internalID: 21300048
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_25
      rect:
        serializedVersion: 2
        x: 320
        y: 832
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 25305410000000000800000000000000
      internalID: 21300050
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_26
      rect:
        serializedVersion: 2
        x: 384
        y: 832
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 45305410000000000800000000000000
      internalID: 21300052
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_27
      rect:
        serializedVersion: 2
        x: 448
        y: 832
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 65305410000000000800000000000000
      internalID: 21300054
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_28
      rect:
        serializedVersion: 2
        x: 512
        y: 832
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 85305410000000000800000000000000
      internalID: 21300056
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_29
      rect:
        serializedVersion: 2
        x: 576
        y: 832
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a5305410000000000800000000000000
      internalID: 21300058
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_30
      rect:
        serializedVersion: 2
        x: 0
        y: 768
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c5305410000000000800000000000000
      internalID: 21300060
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_31
      rect:
        serializedVersion: 2
        x: 64
        y: 768
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e5305410000000000800000000000000
      internalID: 21300062
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_32
      rect:
        serializedVersion: 2
        x: 128
        y: 768
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 06305410000000000800000000000000
      internalID: 21300064
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_33
      rect:
        serializedVersion: 2
        x: 192
        y: 768
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 26305410000000000800000000000000
      internalID: 21300066
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_34
      rect:
        serializedVersion: 2
        x: 256
        y: 768
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 46305410000000000800000000000000
      internalID: 21300068
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_35
      rect:
        serializedVersion: 2
        x: 320
        y: 768
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 66305410000000000800000000000000
      internalID: 21300070
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_36
      rect:
        serializedVersion: 2
        x: 384
        y: 768
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 86305410000000000800000000000000
      internalID: 21300072
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_37
      rect:
        serializedVersion: 2
        x: 448
        y: 768
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a6305410000000000800000000000000
      internalID: 21300074
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_38
      rect:
        serializedVersion: 2
        x: 512
        y: 768
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c6305410000000000800000000000000
      internalID: 21300076
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_39
      rect:
        serializedVersion: 2
        x: 576
        y: 768
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e6305410000000000800000000000000
      internalID: 21300078
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_40
      rect:
        serializedVersion: 2
        x: 0
        y: 704
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 07305410000000000800000000000000
      internalID: 21300080
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_41
      rect:
        serializedVersion: 2
        x: 64
        y: 704
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 27305410000000000800000000000000
      internalID: 21300082
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_42
      rect:
        serializedVersion: 2
        x: 128
        y: 704
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 47305410000000000800000000000000
      internalID: 21300084
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_43
      rect:
        serializedVersion: 2
        x: 192
        y: 704
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 67305410000000000800000000000000
      internalID: 21300086
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_44
      rect:
        serializedVersion: 2
        x: 256
        y: 704
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 87305410000000000800000000000000
      internalID: 21300088
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_45
      rect:
        serializedVersion: 2
        x: 320
        y: 704
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a7305410000000000800000000000000
      internalID: 21300090
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_46
      rect:
        serializedVersion: 2
        x: 512
        y: 704
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c7305410000000000800000000000000
      internalID: 21300092
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_47
      rect:
        serializedVersion: 2
        x: 576
        y: 704
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e7305410000000000800000000000000
      internalID: 21300094
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_48
      rect:
        serializedVersion: 2
        x: 0
        y: 640
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 08305410000000000800000000000000
      internalID: 21300096
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_49
      rect:
        serializedVersion: 2
        x: 64
        y: 640
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 28305410000000000800000000000000
      internalID: 21300098
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_50
      rect:
        serializedVersion: 2
        x: 128
        y: 640
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 48305410000000000800000000000000
      internalID: 21300100
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_51
      rect:
        serializedVersion: 2
        x: 192
        y: 640
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 68305410000000000800000000000000
      internalID: 21300102
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_52
      rect:
        serializedVersion: 2
        x: 256
        y: 640
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 88305410000000000800000000000000
      internalID: 21300104
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_53
      rect:
        serializedVersion: 2
        x: 320
        y: 640
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a8305410000000000800000000000000
      internalID: 21300106
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_54
      rect:
        serializedVersion: 2
        x: 384
        y: 640
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c8305410000000000800000000000000
      internalID: 21300108
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_55
      rect:
        serializedVersion: 2
        x: 448
        y: 640
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e8305410000000000800000000000000
      internalID: 21300110
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_56
      rect:
        serializedVersion: 2
        x: 512
        y: 640
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 09305410000000000800000000000000
      internalID: 21300112
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_57
      rect:
        serializedVersion: 2
        x: 576
        y: 640
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 29305410000000000800000000000000
      internalID: 21300114
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_58
      rect:
        serializedVersion: 2
        x: 0
        y: 576
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 49305410000000000800000000000000
      internalID: 21300116
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_59
      rect:
        serializedVersion: 2
        x: 64
        y: 576
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 69305410000000000800000000000000
      internalID: 21300118
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_60
      rect:
        serializedVersion: 2
        x: 128
        y: 576
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 89305410000000000800000000000000
      internalID: 21300120
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_61
      rect:
        serializedVersion: 2
        x: 192
        y: 576
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a9305410000000000800000000000000
      internalID: 21300122
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_62
      rect:
        serializedVersion: 2
        x: 256
        y: 576
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c9305410000000000800000000000000
      internalID: 21300124
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_63
      rect:
        serializedVersion: 2
        x: 320
        y: 576
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e9305410000000000800000000000000
      internalID: 21300126
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_64
      rect:
        serializedVersion: 2
        x: 384
        y: 576
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0a305410000000000800000000000000
      internalID: 21300128
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_65
      rect:
        serializedVersion: 2
        x: 448
        y: 576
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2a305410000000000800000000000000
      internalID: 21300130
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_66
      rect:
        serializedVersion: 2
        x: 512
        y: 576
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4a305410000000000800000000000000
      internalID: 21300132
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_67
      rect:
        serializedVersion: 2
        x: 576
        y: 576
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6a305410000000000800000000000000
      internalID: 21300134
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_68
      rect:
        serializedVersion: 2
        x: 0
        y: 512
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8a305410000000000800000000000000
      internalID: 21300136
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_69
      rect:
        serializedVersion: 2
        x: 64
        y: 512
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: aa305410000000000800000000000000
      internalID: 21300138
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_70
      rect:
        serializedVersion: 2
        x: 128
        y: 512
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ca305410000000000800000000000000
      internalID: 21300140
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_71
      rect:
        serializedVersion: 2
        x: 192
        y: 512
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ea305410000000000800000000000000
      internalID: 21300142
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_72
      rect:
        serializedVersion: 2
        x: 256
        y: 512
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0b305410000000000800000000000000
      internalID: 21300144
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_73
      rect:
        serializedVersion: 2
        x: 320
        y: 512
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2b305410000000000800000000000000
      internalID: 21300146
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_74
      rect:
        serializedVersion: 2
        x: 384
        y: 512
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4b305410000000000800000000000000
      internalID: 21300148
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_75
      rect:
        serializedVersion: 2
        x: 448
        y: 512
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6b305410000000000800000000000000
      internalID: 21300150
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_76
      rect:
        serializedVersion: 2
        x: 512
        y: 512
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8b305410000000000800000000000000
      internalID: 21300152
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_77
      rect:
        serializedVersion: 2
        x: 576
        y: 512
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ab305410000000000800000000000000
      internalID: 21300154
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_78
      rect:
        serializedVersion: 2
        x: 0
        y: 448
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cb305410000000000800000000000000
      internalID: 21300156
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_79
      rect:
        serializedVersion: 2
        x: 64
        y: 448
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: eb305410000000000800000000000000
      internalID: 21300158
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_80
      rect:
        serializedVersion: 2
        x: 128
        y: 448
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0c305410000000000800000000000000
      internalID: 21300160
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_81
      rect:
        serializedVersion: 2
        x: 192
        y: 448
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2c305410000000000800000000000000
      internalID: 21300162
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_82
      rect:
        serializedVersion: 2
        x: 256
        y: 448
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4c305410000000000800000000000000
      internalID: 21300164
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_83
      rect:
        serializedVersion: 2
        x: 320
        y: 448
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6c305410000000000800000000000000
      internalID: 21300166
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_84
      rect:
        serializedVersion: 2
        x: 384
        y: 448
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8c305410000000000800000000000000
      internalID: 21300168
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_85
      rect:
        serializedVersion: 2
        x: 448
        y: 448
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ac305410000000000800000000000000
      internalID: 21300170
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_86
      rect:
        serializedVersion: 2
        x: 512
        y: 448
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cc305410000000000800000000000000
      internalID: 21300172
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_87
      rect:
        serializedVersion: 2
        x: 576
        y: 448
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ec305410000000000800000000000000
      internalID: 21300174
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_88
      rect:
        serializedVersion: 2
        x: 0
        y: 384
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0d305410000000000800000000000000
      internalID: 21300176
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_89
      rect:
        serializedVersion: 2
        x: 64
        y: 384
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2d305410000000000800000000000000
      internalID: 21300178
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_90
      rect:
        serializedVersion: 2
        x: 128
        y: 384
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4d305410000000000800000000000000
      internalID: 21300180
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_91
      rect:
        serializedVersion: 2
        x: 192
        y: 384
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6d305410000000000800000000000000
      internalID: 21300182
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_92
      rect:
        serializedVersion: 2
        x: 256
        y: 384
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8d305410000000000800000000000000
      internalID: 21300184
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: loading-animation_93
      rect:
        serializedVersion: 2
        x: 320
        y: 384
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ad305410000000000800000000000000
      internalID: 21300186
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      loading-animation_0: 21300000
      loading-animation_1: 21300002
      loading-animation_10: 21300020
      loading-animation_11: 21300022
      loading-animation_12: 21300024
      loading-animation_13: 21300026
      loading-animation_14: 21300028
      loading-animation_15: 21300030
      loading-animation_16: 21300032
      loading-animation_17: 21300034
      loading-animation_18: 21300036
      loading-animation_19: 21300038
      loading-animation_2: 21300004
      loading-animation_20: 21300040
      loading-animation_21: 21300042
      loading-animation_22: 21300044
      loading-animation_23: 21300046
      loading-animation_24: 21300048
      loading-animation_25: 21300050
      loading-animation_26: 21300052
      loading-animation_27: 21300054
      loading-animation_28: 21300056
      loading-animation_29: 21300058
      loading-animation_3: 21300006
      loading-animation_30: 21300060
      loading-animation_31: 21300062
      loading-animation_32: 21300064
      loading-animation_33: 21300066
      loading-animation_34: 21300068
      loading-animation_35: 21300070
      loading-animation_36: 21300072
      loading-animation_37: 21300074
      loading-animation_38: 21300076
      loading-animation_39: 21300078
      loading-animation_4: 21300008
      loading-animation_40: 21300080
      loading-animation_41: 21300082
      loading-animation_42: 21300084
      loading-animation_43: 21300086
      loading-animation_44: 21300088
      loading-animation_45: 21300090
      loading-animation_46: 21300092
      loading-animation_47: 21300094
      loading-animation_48: 21300096
      loading-animation_49: 21300098
      loading-animation_5: 21300010
      loading-animation_50: 21300100
      loading-animation_51: 21300102
      loading-animation_52: 21300104
      loading-animation_53: 21300106
      loading-animation_54: 21300108
      loading-animation_55: 21300110
      loading-animation_56: 21300112
      loading-animation_57: 21300114
      loading-animation_58: 21300116
      loading-animation_59: 21300118
      loading-animation_6: 21300012
      loading-animation_60: 21300120
      loading-animation_61: 21300122
      loading-animation_62: 21300124
      loading-animation_63: 21300126
      loading-animation_64: 21300128
      loading-animation_65: 21300130
      loading-animation_66: 21300132
      loading-animation_67: 21300134
      loading-animation_68: 21300136
      loading-animation_69: 21300138
      loading-animation_7: 21300014
      loading-animation_70: 21300140
      loading-animation_71: 21300142
      loading-animation_72: 21300144
      loading-animation_73: 21300146
      loading-animation_74: 21300148
      loading-animation_75: 21300150
      loading-animation_76: 21300152
      loading-animation_77: 21300154
      loading-animation_78: 21300156
      loading-animation_79: 21300158
      loading-animation_8: 21300016
      loading-animation_80: 21300160
      loading-animation_81: 21300162
      loading-animation_82: 21300164
      loading-animation_83: 21300166
      loading-animation_84: 21300168
      loading-animation_85: 21300170
      loading-animation_86: 21300172
      loading-animation_87: 21300174
      loading-animation_88: 21300176
      loading-animation_89: 21300178
      loading-animation_9: 21300018
      loading-animation_90: 21300180
      loading-animation_91: 21300182
      loading-animation_92: 21300184
      loading-animation_93: 21300186
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
AssetOrigin:
  serializedVersion: 1
  productId: 183370
  packageName: Feel
  packageVersion: 5.5
  assetPath: Assets/Feel/MMTools/Core/MMSceneLoading/Sprites/LoadingAnimation.png
  uploadId: 759320
