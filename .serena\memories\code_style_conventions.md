# BTR Code Style & Conventions

## Namespace Organization
- **BTR**: Legacy namespace for older systems
- **BTR.{SystemName}**: Modern namespace structure (e.g., BTR.Projectiles, BTR.EnemySystem)
- **Stylo.{ModuleName}**: Custom framework modules (e.g., Stylo.Epoch, Stylo.MenUI)
- **BTRU6.Management**: Management systems namespace

## Naming Conventions
- **Classes**: PascalCase (e.g., `ProjectileManager`, `EnemyCore`)
- **Methods**: PascalCase (e.g., `InitializeSystem()`, `HandleWaveEvent()`)
- **Fields/Properties**: PascalCase for public, camelCase with underscore for private (e.g., `_isInitialized`)
- **Constants**: UPPER_SNAKE_CASE
- **ScriptableObjects**: Descriptive names ending with purpose (e.g., `ProjectileConfiguration`, `EnemyBehaviorSettings`)

## File Organization
- **Assets/_Scripts/**: Main scripts organized by system
- **Assets/Stylo/**: Custom framework modules
- **Documentation/**: Comprehensive docs for each system
- **Editor/**: Custom editor tools and inspectors
- **Runtime/**: Runtime-only code separated from editor code

## Architecture Patterns
- **Component-based design** with strategy patterns
- **Manager classes** for system coordination (e.g., `ProjectileManager`, `EnemyManager`)
- **ScriptableObject configurations** for data-driven design
- **Event-driven architecture** using UnityEvents and custom event systems
- **Async/await patterns** with UniTask for performance

## Documentation Standards
- **XML documentation** for public APIs
- **Comprehensive README files** for each major system
- **Architecture diagrams** using Mermaid
- **Usage guides** with code examples
- **Migration guides** for system updates

## Performance Guidelines
- **Object pooling** for frequently instantiated objects
- **Job System** for CPU-intensive operations
- **Burst compilation** for performance-critical code
- **ZLinq** for LINQ optimizations
- **Memory allocation awareness** in hot paths