﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class Math
 | Flexalon 3D Layouts </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class Math
 | Flexalon 3D Layouts ">
    <meta name="generator" content="docfx ">
  
    <link rel="shortcut icon" href="images/favicon.svg">
    <link rel="stylesheet" href="styles/docfx.vendor.css">
    <link rel="stylesheet" href="styles/docfx.css">
    <link rel="stylesheet" href="styles/main.css">
    <meta property="docfx:navrel" content="tochead.html">
    <meta property="docfx:tocrel" content="toc.html">
  
  <meta property="docfx:rel" content="">
  
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="https://www.flexalon.com/">
                <img id="logo" class="svg" src="images/logo_32x32.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first="First" data-prev="Previous" data-next="Next" data-last="Last"></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">

        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="Flexalon.Math">


  <h1 id="Flexalon_Math" data-uid="Flexalon.Math" class="text-break">Class Math
</h1>
  <div class="markdown level0 summary"><p>Common math help functions.</p>
</div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">object</span></div>
    <div class="level1"><span class="xref">Math</span></div>
  </div>
  <h5 id="Flexalon_Math_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static class Math</code></pre>
  </div>
  <h3 id="fields">Fields
</h3>
  <h4 id="Flexalon_Math_MaxValue" data-uid="Flexalon.Math.MaxValue">MaxValue</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly float MaxValue</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">float</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h4 id="Flexalon_Math_MaxVector" data-uid="Flexalon.Math.MaxVector">MaxVector</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static readonly Vector3 MaxVector</code></pre>
  </div>
  <h5 class="fieldValue">Field Value</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h3 id="methods">Methods
</h3>
  <a id="Flexalon_Math_Abs_" data-uid="Flexalon.Math.Abs*"></a>
  <h4 id="Flexalon_Math_Abs_UnityEngine_Vector3_" data-uid="Flexalon.Math.Abs(UnityEngine.Vector3)">Abs(Vector3)</h4>
  <div class="markdown level1 summary"><p>Applies absolute value of to each vector component.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 Abs(Vector3 v)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><span class="parametername">v</span></td>
        <td><p>The vector to apply absolute value to.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><p>The vector with absolute value applied.</p>
</td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_Math_Align_" data-uid="Flexalon.Math.Align*"></a>
  <h4 id="Flexalon_Math_Align_System_Single_Flexalon_Align_" data-uid="Flexalon.Math.Align(System.Single,Flexalon.Align)">Align(float, Align)</h4>
  <div class="markdown level1 summary"><p>Determines the aligned position in a size.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static float Align(float size, Align align)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">float</span></td>
        <td><span class="parametername">size</span></td>
        <td><p>The size to align to.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="Flexalon.Align.html">Align</a></td>
        <td><span class="parametername">align</span></td>
        <td><p>The alignment.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">float</span></td>
        <td><p>The aligned position.</p>
</td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_Math_Align_" data-uid="Flexalon.Math.Align*"></a>
  <h4 id="Flexalon_Math_Align_System_Single_System_Single_Flexalon_Align_Flexalon_Align_" data-uid="Flexalon.Math.Align(System.Single,System.Single,Flexalon.Align,Flexalon.Align)">Align(float, float, Align, Align)</h4>
  <div class="markdown level1 summary"><p>Aligns a child size to a parent size.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static float Align(float childSize, float parentSize, Align parentAlign, Align childAlign)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">float</span></td>
        <td><span class="parametername">childSize</span></td>
        <td><p>The size of the child.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">float</span></td>
        <td><span class="parametername">parentSize</span></td>
        <td><p>The size of the parent.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="Flexalon.Align.html">Align</a></td>
        <td><span class="parametername">parentAlign</span></td>
        <td><p>The alignment of the parent.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="Flexalon.Align.html">Align</a></td>
        <td><span class="parametername">childAlign</span></td>
        <td><p>The pivot of the child.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">float</span></td>
        <td><p>The aligned position of the child.</p>
</td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_Math_Align_" data-uid="Flexalon.Math.Align*"></a>
  <h4 id="Flexalon_Math_Align_System_Single_System_Single_Flexalon_Align_" data-uid="Flexalon.Math.Align(System.Single,System.Single,Flexalon.Align)">Align(float, float, Align)</h4>
  <div class="markdown level1 summary"><p>Aligns a child size to a parent size.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static float Align(float childSize, float parentSize, Align align)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">float</span></td>
        <td><span class="parametername">childSize</span></td>
        <td><p>The size of the child.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">float</span></td>
        <td><span class="parametername">parentSize</span></td>
        <td><p>The size of the parent.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="Flexalon.Align.html">Align</a></td>
        <td><span class="parametername">align</span></td>
        <td><p>The alignment of the parent and child.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">float</span></td>
        <td><p>The aligned position of the child.</p>
</td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_Math_Align_" data-uid="Flexalon.Math.Align*"></a>
  <h4 id="Flexalon_Math_Align_UnityEngine_Vector3_Flexalon_Align_Flexalon_Align_Flexalon_Align_" data-uid="Flexalon.Math.Align(UnityEngine.Vector3,Flexalon.Align,Flexalon.Align,Flexalon.Align)">Align(Vector3, Align, Align, Align)</h4>
  <div class="markdown level1 summary"><p>Determines the aligned position in a size.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 Align(Vector3 size, Align horizontal, Align vertical, Align depth)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><span class="parametername">size</span></td>
        <td><p>The size to align to.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="Flexalon.Align.html">Align</a></td>
        <td><span class="parametername">horizontal</span></td>
        <td><p>The horizontal alignment.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="Flexalon.Align.html">Align</a></td>
        <td><span class="parametername">vertical</span></td>
        <td><p>The vertical alignment.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="Flexalon.Align.html">Align</a></td>
        <td><span class="parametername">depth</span></td>
        <td><p>The depth alignment.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><p>The aligned position.</p>
</td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_Math_Align_" data-uid="Flexalon.Math.Align*"></a>
  <h4 id="Flexalon_Math_Align_UnityEngine_Vector3_System_Int32_Flexalon_Align_" data-uid="Flexalon.Math.Align(UnityEngine.Vector3,System.Int32,Flexalon.Align)">Align(Vector3, int, Align)</h4>
  <div class="markdown level1 summary"><p>Determines the aligned position in a size for an axis.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static float Align(Vector3 size, int axis, Align align)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><span class="parametername">size</span></td>
        <td><p>The size to align to.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">int</span></td>
        <td><span class="parametername">axis</span></td>
        <td><p>The axis to align to.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="Flexalon.Align.html">Align</a></td>
        <td><span class="parametername">align</span></td>
        <td><p>The alignment.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">float</span></td>
        <td><p>The aligned position.</p>
</td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_Math_Align_" data-uid="Flexalon.Math.Align*"></a>
  <h4 id="Flexalon_Math_Align_UnityEngine_Vector3_UnityEngine_Vector3_Flexalon_Align_Flexalon_Align_Flexalon_Align_Flexalon_Align_Flexalon_Align_Flexalon_Align_" data-uid="Flexalon.Math.Align(UnityEngine.Vector3,UnityEngine.Vector3,Flexalon.Align,Flexalon.Align,Flexalon.Align,Flexalon.Align,Flexalon.Align,Flexalon.Align)">Align(Vector3, Vector3, Align, Align, Align, Align, Align, Align)</h4>
  <div class="markdown level1 summary"><p>Aligns a child size to a parent size on all axes.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 Align(Vector3 childSize, Vector3 parentSize, Align parentHorizontal, Align parentVertical, Align parentDepth, Align childHorizontal, Align childVertical, Align childDepth)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><span class="parametername">childSize</span></td>
        <td><p>The size of the child.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><span class="parametername">parentSize</span></td>
        <td><p>The size of the parent.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="Flexalon.Align.html">Align</a></td>
        <td><span class="parametername">parentHorizontal</span></td>
        <td><p>The horizontal alignment of the parent.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="Flexalon.Align.html">Align</a></td>
        <td><span class="parametername">parentVertical</span></td>
        <td><p>The vertical alignment of the parent.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="Flexalon.Align.html">Align</a></td>
        <td><span class="parametername">parentDepth</span></td>
        <td><p>The depth alignment of the parent.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="Flexalon.Align.html">Align</a></td>
        <td><span class="parametername">childHorizontal</span></td>
        <td><p>The horizontal pivot of the child.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="Flexalon.Align.html">Align</a></td>
        <td><span class="parametername">childVertical</span></td>
        <td><p>The vertical pivot of the child.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="Flexalon.Align.html">Align</a></td>
        <td><span class="parametername">childDepth</span></td>
        <td><p>The depth pivot of the child.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><p>The aligned position of the child.</p>
</td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_Math_Align_" data-uid="Flexalon.Math.Align*"></a>
  <h4 id="Flexalon_Math_Align_UnityEngine_Vector3_UnityEngine_Vector3_Flexalon_Align_Flexalon_Align_Flexalon_Align_" data-uid="Flexalon.Math.Align(UnityEngine.Vector3,UnityEngine.Vector3,Flexalon.Align,Flexalon.Align,Flexalon.Align)">Align(Vector3, Vector3, Align, Align, Align)</h4>
  <div class="markdown level1 summary"><p>Aligns a child size to a parent size on all axes.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 Align(Vector3 childSize, Vector3 parentSize, Align horizontal, Align vertical, Align depth)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><span class="parametername">childSize</span></td>
        <td><p>The size of the child.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><span class="parametername">parentSize</span></td>
        <td><p>The size of the parent.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="Flexalon.Align.html">Align</a></td>
        <td><span class="parametername">horizontal</span></td>
        <td><p>The horizontal alignment of the parent and child.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="Flexalon.Align.html">Align</a></td>
        <td><span class="parametername">vertical</span></td>
        <td><p>The vertical alignment of the parent and child.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="Flexalon.Align.html">Align</a></td>
        <td><span class="parametername">depth</span></td>
        <td><p>The depth alignment of the parent and child.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><p>The aligned position of the child.</p>
</td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_Math_Align_" data-uid="Flexalon.Math.Align*"></a>
  <h4 id="Flexalon_Math_Align_UnityEngine_Vector3_UnityEngine_Vector3_Flexalon_Axis_Flexalon_Align_Flexalon_Align_" data-uid="Flexalon.Math.Align(UnityEngine.Vector3,UnityEngine.Vector3,Flexalon.Axis,Flexalon.Align,Flexalon.Align)">Align(Vector3, Vector3, Axis, Align, Align)</h4>
  <div class="markdown level1 summary"><p>Aligns a child size to a parent size on an axis.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static float Align(Vector3 childSize, Vector3 parentSize, Axis axis, Align parentAlign, Align childAlign)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><span class="parametername">childSize</span></td>
        <td><p>The size of the child.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><span class="parametername">parentSize</span></td>
        <td><p>The size of the parent.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="Flexalon.Axis.html">Axis</a></td>
        <td><span class="parametername">axis</span></td>
        <td><p>The axis to align on.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="Flexalon.Align.html">Align</a></td>
        <td><span class="parametername">parentAlign</span></td>
        <td><p>The alignment of the parent.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="Flexalon.Align.html">Align</a></td>
        <td><span class="parametername">childAlign</span></td>
        <td><p>The pivot of the child.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">float</span></td>
        <td><p>The aligned position of the child.</p>
</td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_Math_Align_" data-uid="Flexalon.Math.Align*"></a>
  <h4 id="Flexalon_Math_Align_UnityEngine_Vector3_UnityEngine_Vector3_Flexalon_Axis_Flexalon_Align_" data-uid="Flexalon.Math.Align(UnityEngine.Vector3,UnityEngine.Vector3,Flexalon.Axis,Flexalon.Align)">Align(Vector3, Vector3, Axis, Align)</h4>
  <div class="markdown level1 summary"><p>Aligns a child size to a parent size on an axis.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static float Align(Vector3 childSize, Vector3 parentSize, Axis axis, Align align)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><span class="parametername">childSize</span></td>
        <td><p>The size of the child.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><span class="parametername">parentSize</span></td>
        <td><p>The size of the parent.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="Flexalon.Axis.html">Axis</a></td>
        <td><span class="parametername">axis</span></td>
        <td><p>The axis to align on.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="Flexalon.Align.html">Align</a></td>
        <td><span class="parametername">align</span></td>
        <td><p>The alignment of the parent and child.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">float</span></td>
        <td><p>The aligned position of the child.</p>
</td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_Math_Align_" data-uid="Flexalon.Math.Align*"></a>
  <h4 id="Flexalon_Math_Align_UnityEngine_Vector3_UnityEngine_Vector3_System_Int32_Flexalon_Align_Flexalon_Align_" data-uid="Flexalon.Math.Align(UnityEngine.Vector3,UnityEngine.Vector3,System.Int32,Flexalon.Align,Flexalon.Align)">Align(Vector3, Vector3, int, Align, Align)</h4>
  <div class="markdown level1 summary"><p>Aligns a child size to a parent size on an axis.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static float Align(Vector3 childSize, Vector3 parentSize, int axis, Align parentAlign, Align childAlign)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><span class="parametername">childSize</span></td>
        <td><p>The size of the child.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><span class="parametername">parentSize</span></td>
        <td><p>The size of the parent.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">int</span></td>
        <td><span class="parametername">axis</span></td>
        <td><p>The axis to align on.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="Flexalon.Align.html">Align</a></td>
        <td><span class="parametername">parentAlign</span></td>
        <td><p>The alignment of the parent.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="Flexalon.Align.html">Align</a></td>
        <td><span class="parametername">childAlign</span></td>
        <td><p>The pivot of the child.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">float</span></td>
        <td><p>The aligned position of the child.</p>
</td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_Math_Align_" data-uid="Flexalon.Math.Align*"></a>
  <h4 id="Flexalon_Math_Align_UnityEngine_Vector3_UnityEngine_Vector3_System_Int32_Flexalon_Align_" data-uid="Flexalon.Math.Align(UnityEngine.Vector3,UnityEngine.Vector3,System.Int32,Flexalon.Align)">Align(Vector3, Vector3, int, Align)</h4>
  <div class="markdown level1 summary"><p>Aligns a child size to a parent size on an axis.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static float Align(Vector3 childSize, Vector3 parentSize, int axis, Align align)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><span class="parametername">childSize</span></td>
        <td><p>The size of the child.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><span class="parametername">parentSize</span></td>
        <td><p>The size of the parent.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">int</span></td>
        <td><span class="parametername">axis</span></td>
        <td><p>The axis to align on.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="Flexalon.Align.html">Align</a></td>
        <td><span class="parametername">align</span></td>
        <td><p>The alignment of the parent and child.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">float</span></td>
        <td><p>The aligned position of the child.</p>
</td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_Math_Clamp_" data-uid="Flexalon.Math.Clamp*"></a>
  <h4 id="Flexalon_Math_Clamp_UnityEngine_Vector3_UnityEngine_Vector3_UnityEngine_Vector3_" data-uid="Flexalon.Math.Clamp(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3)">Clamp(Vector3, Vector3, Vector3)</h4>
  <div class="markdown level1 summary"><p>Clamps value of to each vector component between min and max.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 Clamp(Vector3 v, Vector3 min, Vector3 max)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><span class="parametername">v</span></td>
        <td><p>The vector to clamp.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><span class="parametername">min</span></td>
        <td><p>The minimum value.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><span class="parametername">max</span></td>
        <td><p>The maximum value.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><p>The clamped vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_Math_CreateRotatedBounds_" data-uid="Flexalon.Math.CreateRotatedBounds*"></a>
  <h4 id="Flexalon_Math_CreateRotatedBounds_UnityEngine_Vector3_UnityEngine_Vector3_UnityEngine_Quaternion_" data-uid="Flexalon.Math.CreateRotatedBounds(UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Quaternion)">CreateRotatedBounds(Vector3, Vector3, Quaternion)</h4>
  <div class="markdown level1 summary"><p>Creates rotated and scaled bounds at center.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Bounds CreateRotatedBounds(Vector3 center, Vector3 size, Quaternion rotation)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><span class="parametername">center</span></td>
        <td><p>The center of the bounds.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><span class="parametername">size</span></td>
        <td><p>The size of the bound before rotation.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">UnityEngine.Quaternion</span></td>
        <td><span class="parametername">rotation</span></td>
        <td><p>The rotation to apply to the size.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">UnityEngine.Bounds</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_Math_Div_" data-uid="Flexalon.Math.Div*"></a>
  <h4 id="Flexalon_Math_Div_UnityEngine_Vector3_UnityEngine_Vector3_" data-uid="Flexalon.Math.Div(UnityEngine.Vector3,UnityEngine.Vector3)">Div(Vector3, Vector3)</h4>
  <div class="markdown level1 summary"><p>Divides each component of two vectors.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 Div(Vector3 a, Vector3 b)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><span class="parametername">a</span></td>
        <td><p>The divided vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><span class="parametername">b</span></td>
        <td><p>The divisor vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><p>The divided vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_Math_GetAxisFromDirection_" data-uid="Flexalon.Math.GetAxisFromDirection*"></a>
  <h4 id="Flexalon_Math_GetAxisFromDirection_Flexalon_Direction_" data-uid="Flexalon.Math.GetAxisFromDirection(Flexalon.Direction)">GetAxisFromDirection(Direction)</h4>
  <div class="markdown level1 summary"><p>Returns the axis of a direction.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Axis GetAxisFromDirection(Direction direction)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.Direction.html">Direction</a></td>
        <td><span class="parametername">direction</span></td>
        <td><p>The direction to get the axis of.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.Axis.html">Axis</a></td>
        <td><p>The axis of the direction.</p>
</td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_Math_GetAxisFromDirection_" data-uid="Flexalon.Math.GetAxisFromDirection*"></a>
  <h4 id="Flexalon_Math_GetAxisFromDirection_System_Int32_" data-uid="Flexalon.Math.GetAxisFromDirection(System.Int32)">GetAxisFromDirection(int)</h4>
  <div class="markdown level1 summary"><p>Returns the axis of a direction.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Axis GetAxisFromDirection(int direction)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">int</span></td>
        <td><span class="parametername">direction</span></td>
        <td><p>The direction to get the axis of.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.Axis.html">Axis</a></td>
        <td><p>The axis of the direction.</p>
</td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_Math_GetDirectionsFromAxis_" data-uid="Flexalon.Math.GetDirectionsFromAxis*"></a>
  <h4 id="Flexalon_Math_GetDirectionsFromAxis_Flexalon_Axis_" data-uid="Flexalon.Math.GetDirectionsFromAxis(Flexalon.Axis)">GetDirectionsFromAxis(Axis)</h4>
  <div class="markdown level1 summary"><p>Returns the positive and negative directions of an axis.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static (Direction, Direction) GetDirectionsFromAxis(Axis axis)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.Axis.html">Axis</a></td>
        <td><span class="parametername">axis</span></td>
        <td><p>The axis to get the directions of.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>(<a class="xref" href="Flexalon.Direction.html">Direction</a>, <a class="xref" href="Flexalon.Direction.html">Direction</a>)</td>
        <td><p>The positive and negative directions of the axis.</p>
</td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_Math_GetDirectionsFromAxis_" data-uid="Flexalon.Math.GetDirectionsFromAxis*"></a>
  <h4 id="Flexalon_Math_GetDirectionsFromAxis_System_Int32_" data-uid="Flexalon.Math.GetDirectionsFromAxis(System.Int32)">GetDirectionsFromAxis(int)</h4>
  <div class="markdown level1 summary"><p>Returns the positive and negative directions of an axis.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static (Direction, Direction) GetDirectionsFromAxis(int axis)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">int</span></td>
        <td><span class="parametername">axis</span></td>
        <td><p>The axis to get the directions of.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>(<a class="xref" href="Flexalon.Direction.html">Direction</a>, <a class="xref" href="Flexalon.Direction.html">Direction</a>)</td>
        <td><p>The positive and negative directions of the axis.</p>
</td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_Math_GetOppositeDirection_" data-uid="Flexalon.Math.GetOppositeDirection*"></a>
  <h4 id="Flexalon_Math_GetOppositeDirection_Flexalon_Direction_" data-uid="Flexalon.Math.GetOppositeDirection(Flexalon.Direction)">GetOppositeDirection(Direction)</h4>
  <div class="markdown level1 summary"><p>Returns the opposite direction.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Direction GetOppositeDirection(Direction direction)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.Direction.html">Direction</a></td>
        <td><span class="parametername">direction</span></td>
        <td><p>The direction to get the opposite of.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.Direction.html">Direction</a></td>
        <td><p>The opposite direction.</p>
</td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_Math_GetOppositeDirection_" data-uid="Flexalon.Math.GetOppositeDirection*"></a>
  <h4 id="Flexalon_Math_GetOppositeDirection_System_Int32_" data-uid="Flexalon.Math.GetOppositeDirection(System.Int32)">GetOppositeDirection(int)</h4>
  <div class="markdown level1 summary"><p>Returns the opposite direction.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Direction GetOppositeDirection(int direction)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">int</span></td>
        <td><span class="parametername">direction</span></td>
        <td><p>The direction to get the opposite of.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.Direction.html">Direction</a></td>
        <td><p>The opposite direction.</p>
</td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_Math_GetOtherAxes_" data-uid="Flexalon.Math.GetOtherAxes*"></a>
  <h4 id="Flexalon_Math_GetOtherAxes_Flexalon_Axis_" data-uid="Flexalon.Math.GetOtherAxes(Flexalon.Axis)">GetOtherAxes(Axis)</h4>
  <div class="markdown level1 summary"><p>Returns the other two axes.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static (Axis, Axis) GetOtherAxes(Axis axis)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.Axis.html">Axis</a></td>
        <td><span class="parametername">axis</span></td>
        <td><p>The axis to get the other two axes of.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>(<a class="xref" href="Flexalon.Axis.html">Axis</a>, <a class="xref" href="Flexalon.Axis.html">Axis</a>)</td>
        <td><p>The other two axes.</p>
</td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_Math_GetOtherAxes_" data-uid="Flexalon.Math.GetOtherAxes*"></a>
  <h4 id="Flexalon_Math_GetOtherAxes_System_Int32_" data-uid="Flexalon.Math.GetOtherAxes(System.Int32)">GetOtherAxes(int)</h4>
  <div class="markdown level1 summary"><p>Returns the other two axes.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static (int, int) GetOtherAxes(int axis)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">int</span></td>
        <td><span class="parametername">axis</span></td>
        <td><p>The axis to get the other two axes of.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>(<span class="xref">int</span>, <span class="xref">int</span>)</td>
        <td><p>The other two axes.</p>
</td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_Math_GetPlaneAxes_" data-uid="Flexalon.Math.GetPlaneAxes*"></a>
  <h4 id="Flexalon_Math_GetPlaneAxes_Flexalon_Plane_" data-uid="Flexalon.Math.GetPlaneAxes(Flexalon.Plane)">GetPlaneAxes(Plane)</h4>
  <div class="markdown level1 summary"><p>Returns the axes of a plane.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static (Axis, Axis) GetPlaneAxes(Plane plane)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.Plane.html">Plane</a></td>
        <td><span class="parametername">plane</span></td>
        <td><p>The plane to get the axes of.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>(<a class="xref" href="Flexalon.Axis.html">Axis</a>, <a class="xref" href="Flexalon.Axis.html">Axis</a>)</td>
        <td><p>The axes of the plane.</p>
</td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_Math_GetPlaneAxesInt_" data-uid="Flexalon.Math.GetPlaneAxesInt*"></a>
  <h4 id="Flexalon_Math_GetPlaneAxesInt_Flexalon_Plane_" data-uid="Flexalon.Math.GetPlaneAxesInt(Flexalon.Plane)">GetPlaneAxesInt(Plane)</h4>
  <div class="markdown level1 summary"><p>Returns the axes of a plane.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static (int, int) GetPlaneAxesInt(Plane plane)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.Plane.html">Plane</a></td>
        <td><span class="parametername">plane</span></td>
        <td><p>The plane to get the axes of.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>(<span class="xref">int</span>, <span class="xref">int</span>)</td>
        <td><p>The axes of the plane.</p>
</td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_Math_GetPositiveFromDirection_" data-uid="Flexalon.Math.GetPositiveFromDirection*"></a>
  <h4 id="Flexalon_Math_GetPositiveFromDirection_Flexalon_Direction_" data-uid="Flexalon.Math.GetPositiveFromDirection(Flexalon.Direction)">GetPositiveFromDirection(Direction)</h4>
  <div class="markdown level1 summary"><p>Returns the positive direction of an axis.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static float GetPositiveFromDirection(Direction direction)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.Direction.html">Direction</a></td>
        <td><span class="parametername">direction</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">float</span></td>
        <td><p>The positive direction of the axis.</p>
</td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_Math_GetPositiveFromDirection_" data-uid="Flexalon.Math.GetPositiveFromDirection*"></a>
  <h4 id="Flexalon_Math_GetPositiveFromDirection_System_Int32_" data-uid="Flexalon.Math.GetPositiveFromDirection(System.Int32)">GetPositiveFromDirection(int)</h4>
  <div class="markdown level1 summary"><p>Returns the positive direction of an axis.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static float GetPositiveFromDirection(int direction)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">int</span></td>
        <td><span class="parametername">direction</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">float</span></td>
        <td><p>The positive direction of the axis.</p>
</td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_Math_GetThirdAxis_" data-uid="Flexalon.Math.GetThirdAxis*"></a>
  <h4 id="Flexalon_Math_GetThirdAxis_Flexalon_Axis_Flexalon_Axis_" data-uid="Flexalon.Math.GetThirdAxis(Flexalon.Axis,Flexalon.Axis)">GetThirdAxis(Axis, Axis)</h4>
  <div class="markdown level1 summary"><p>Given two axes, returns the third axis.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Axis GetThirdAxis(Axis axis1, Axis axis2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.Axis.html">Axis</a></td>
        <td><span class="parametername">axis1</span></td>
        <td><p>The first axis.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="Flexalon.Axis.html">Axis</a></td>
        <td><span class="parametername">axis2</span></td>
        <td><p>The second axis.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.Axis.html">Axis</a></td>
        <td><p>The third axis.</p>
</td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_Math_GetThirdAxis_" data-uid="Flexalon.Math.GetThirdAxis*"></a>
  <h4 id="Flexalon_Math_GetThirdAxis_System_Int32_System_Int32_" data-uid="Flexalon.Math.GetThirdAxis(System.Int32,System.Int32)">GetThirdAxis(int, int)</h4>
  <div class="markdown level1 summary"><p>Given two axes, returns the third axis.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static int GetThirdAxis(int axis1, int axis2)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">int</span></td>
        <td><span class="parametername">axis1</span></td>
        <td><p>The first axis.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">int</span></td>
        <td><span class="parametername">axis2</span></td>
        <td><p>The second axis.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">int</span></td>
        <td><p>The third axis.</p>
</td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_Math_GetVectorFromAxis_" data-uid="Flexalon.Math.GetVectorFromAxis*"></a>
  <h4 id="Flexalon_Math_GetVectorFromAxis_Flexalon_Axis_" data-uid="Flexalon.Math.GetVectorFromAxis(Flexalon.Axis)">GetVectorFromAxis(Axis)</h4>
  <div class="markdown level1 summary"><p>Returns a unit vector in the positive direction of axis.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 GetVectorFromAxis(Axis axis)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.Axis.html">Axis</a></td>
        <td><span class="parametername">axis</span></td>
        <td><p>The axis to get the vector of.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><p>A unit vector in the axis.</p>
</td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_Math_GetVectorFromAxis_" data-uid="Flexalon.Math.GetVectorFromAxis*"></a>
  <h4 id="Flexalon_Math_GetVectorFromAxis_System_Int32_" data-uid="Flexalon.Math.GetVectorFromAxis(System.Int32)">GetVectorFromAxis(int)</h4>
  <div class="markdown level1 summary"><p>Returns a unit vector in the positive direction of axis.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 GetVectorFromAxis(int axis)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">int</span></td>
        <td><span class="parametername">axis</span></td>
        <td><p>The axis to get the vector of.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><p>A unit vector in the axis.</p>
</td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_Math_GetVectorFromDirection_" data-uid="Flexalon.Math.GetVectorFromDirection*"></a>
  <h4 id="Flexalon_Math_GetVectorFromDirection_Flexalon_Direction_" data-uid="Flexalon.Math.GetVectorFromDirection(Flexalon.Direction)">GetVectorFromDirection(Direction)</h4>
  <div class="markdown level1 summary"><p>Returns a unit vector in the direction.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 GetVectorFromDirection(Direction direction)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.Direction.html">Direction</a></td>
        <td><span class="parametername">direction</span></td>
        <td><p>The direction to get the vector of.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><p>A unit vector in the direction.</p>
</td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_Math_GetVectorFromDirection_" data-uid="Flexalon.Math.GetVectorFromDirection*"></a>
  <h4 id="Flexalon_Math_GetVectorFromDirection_System_Int32_" data-uid="Flexalon.Math.GetVectorFromDirection(System.Int32)">GetVectorFromDirection(int)</h4>
  <div class="markdown level1 summary"><p>Returns a unit vector in the direction.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 GetVectorFromDirection(int direction)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">int</span></td>
        <td><span class="parametername">direction</span></td>
        <td><p>The direction to get the vector of.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><p>A unit vector in the direction.</p>
</td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_Math_MeasureComponentBounds_" data-uid="Flexalon.Math.MeasureComponentBounds*"></a>
  <h4 id="Flexalon_Math_MeasureComponentBounds_UnityEngine_Bounds_Flexalon_FlexalonNode_UnityEngine_Vector3_UnityEngine_Vector3_UnityEngine_Vector3_" data-uid="Flexalon.Math.MeasureComponentBounds(UnityEngine.Bounds,Flexalon.FlexalonNode,UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3)">MeasureComponentBounds(Bounds, FlexalonNode, Vector3, Vector3, Vector3)</h4>
  <div class="markdown level1 summary"><p>Given the bounds of a component, creates a bounds for the node respecting the
size types. Aspect ratio is preserved when possible.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Bounds MeasureComponentBounds(Bounds componentBounds, FlexalonNode node, Vector3 size, Vector3 min, Vector3 max)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">UnityEngine.Bounds</span></td>
        <td><span class="parametername">componentBounds</span></td>
        <td><p>The bounds of the component.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="Flexalon.FlexalonNode.html">FlexalonNode</a></td>
        <td><span class="parametername">node</span></td>
        <td><p>The node to measure the bounds for.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><span class="parametername">size</span></td>
        <td><p>The size of the node.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><span class="parametername">min</span></td>
        <td><p>The minimum size of the node.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><span class="parametername">max</span></td>
        <td><p>The maximum size of the node.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">UnityEngine.Bounds</span></td>
        <td><p>The bounds of the node.</p>
</td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_Math_MeasureComponentBounds2D_" data-uid="Flexalon.Math.MeasureComponentBounds2D*"></a>
  <h4 id="Flexalon_Math_MeasureComponentBounds2D_UnityEngine_Bounds_Flexalon_FlexalonNode_UnityEngine_Vector3_UnityEngine_Vector3_UnityEngine_Vector3_" data-uid="Flexalon.Math.MeasureComponentBounds2D(UnityEngine.Bounds,Flexalon.FlexalonNode,UnityEngine.Vector3,UnityEngine.Vector3,UnityEngine.Vector3)">MeasureComponentBounds2D(Bounds, FlexalonNode, Vector3, Vector3, Vector3)</h4>
  <div class="markdown level1 summary"><p>Given the bounds of a component, creates a bounds for the node respecting the
size types. Aspect ratio is preserved for X and Y when possible.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Bounds MeasureComponentBounds2D(Bounds componentBounds, FlexalonNode node, Vector3 size, Vector3 min, Vector3 max)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">UnityEngine.Bounds</span></td>
        <td><span class="parametername">componentBounds</span></td>
        <td><p>The bounds of the component.</p>
</td>
      </tr>
      <tr>
        <td><a class="xref" href="Flexalon.FlexalonNode.html">FlexalonNode</a></td>
        <td><span class="parametername">node</span></td>
        <td><p>The node to measure the bounds for.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><span class="parametername">size</span></td>
        <td><p>The size of the node.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><span class="parametername">min</span></td>
        <td><p>The minimum size of the node.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><span class="parametername">max</span></td>
        <td><p>The maximum size of the node.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">UnityEngine.Bounds</span></td>
        <td><p>The bounds of the node.</p>
</td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_Math_Mul_" data-uid="Flexalon.Math.Mul*"></a>
  <h4 id="Flexalon_Math_Mul_UnityEngine_Vector3_UnityEngine_Vector3_" data-uid="Flexalon.Math.Mul(UnityEngine.Vector3,UnityEngine.Vector3)">Mul(Vector3, Vector3)</h4>
  <div class="markdown level1 summary"><p>Multiplies each component of two vectors.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Vector3 Mul(Vector3 a, Vector3 b)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><span class="parametername">a</span></td>
        <td><p>The first vector.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><span class="parametername">b</span></td>
        <td><p>The second vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><p>The multiplied vector.</p>
</td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_Math_RotateBounds_" data-uid="Flexalon.Math.RotateBounds*"></a>
  <h4 id="Flexalon_Math_RotateBounds_UnityEngine_Bounds_UnityEngine_Quaternion_" data-uid="Flexalon.Math.RotateBounds(UnityEngine.Bounds,UnityEngine.Quaternion)">RotateBounds(Bounds, Quaternion)</h4>
  <div class="markdown level1 summary"><p>Rotates a bounds around the origin and returns a new bounds
that encapsulates all of the rotated corners.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Bounds RotateBounds(Bounds bounds, Quaternion rotation)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">UnityEngine.Bounds</span></td>
        <td><span class="parametername">bounds</span></td>
        <td><p>The bounds to rotate.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">UnityEngine.Quaternion</span></td>
        <td><span class="parametername">rotation</span></td>
        <td><p>The rotation to rotate the bounds by.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">UnityEngine.Bounds</span></td>
        <td><p>The new bounds.</p>
</td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_Math_ScaleBounds_" data-uid="Flexalon.Math.ScaleBounds*"></a>
  <h4 id="Flexalon_Math_ScaleBounds_UnityEngine_Bounds_UnityEngine_Vector3_" data-uid="Flexalon.Math.ScaleBounds(UnityEngine.Bounds,UnityEngine.Vector3)">ScaleBounds(Bounds, Vector3)</h4>
  <div class="markdown level1 summary"><p>Scales a bounds by multiplying the center and size by 'scale'.</p>
</div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static Bounds ScaleBounds(Bounds bounds, Vector3 scale)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">UnityEngine.Bounds</span></td>
        <td><span class="parametername">bounds</span></td>
        <td><p>The bounds to scale.</p>
</td>
      </tr>
      <tr>
        <td><span class="xref">UnityEngine.Vector3</span></td>
        <td><span class="parametername">scale</span></td>
        <td><p>The scale to scale the bounds by.</p>
</td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">UnityEngine.Bounds</span></td>
        <td><p>The scaled bounds.</p>
</td>
      </tr>
    </tbody>
  </table>
</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In This Article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      Flexalon 3D Layouts
      
          </div>
        </div>
      </footer>
    </div>

    
    
    
  </body>
</html>
