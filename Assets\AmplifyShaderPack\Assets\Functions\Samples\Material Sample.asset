%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: Material Sample
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor v1.9.5.1\n// Available at the
    Unity Asset Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=19501\nNode;AmplifyShaderEditor.CommentaryNode;127;-490.5173,-1014.033;Inherit;False;1390.685;883.4981;ALPHA
    CLIPPING;22;118;117;125;8;71;126;122;116;121;156;120;160;110;115;155;124;159;100;119;200;199;259;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;259;-0.1821432,-407.0315;Inherit;False;857.2662;240.2057;Alpha
    Clip Threshold Shadow;4;72;128;129;130;;0,0,0,1;0;0\nNode;AmplifyShaderEditor.SamplerNode;21;-889.8948,249.1555;Inherit;True;Property;_TextureSample13;Texture
    Sample 13;33;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;6;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT3;5\nNode;AmplifyShaderEditor.WireNode;75;-944.7512,311.7962;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;77;-941.7512,336.7963;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;78;-944.7512,313.7962;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;79;-944.7512,315.7962;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;80;-941.7512,337.7962;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;83;-941.7512,339.7962;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;82;-946.7512,312.7962;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.SwizzleNode;73;-572.8774,248.0809;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SamplerNode;22;-890.024,469.5127;Inherit;True;Property;_TextureSample14;Texture
    Sample 14;34;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;6;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT3;5\nNode;AmplifyShaderEditor.SamplerNode;24;-887.4442,791.1992;Inherit;True;Property;_TextureSample16;Texture
    Sample 16;36;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;6;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT3;5\nNode;AmplifyShaderEditor.WireNode;76;-933.2678,853.9809;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.SwizzleNode;27;-565.8189,789.9145;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RangedFloatNode;97;-585.9936,544.7042;Half;False;Property;_NormalStrength;Normal
    Strength;9;0;Create;True;1;;0;0;False;0;False;1;1;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;150;-935.0566,164.5478;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;151;-933.2719,296.5571;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;146;-935.8542,162.799;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;152;-933.2719,501.2923;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;153;-925.9224,804.7205;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;154;-926.9723,1030.454;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;149;-936.2913,164.626;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;148;-935.6287,164.9634;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;147;-934.8757,166.8398;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.UnpackScaleNormalNode;98;-379.3992,473.2933;Inherit;False;Tangent;2;0;FLOAT4;0,0,0,0;False;1;FLOAT;1;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3\nNode;AmplifyShaderEditor.WireNode;81;-929.3772,1114.176;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.SwizzleNode;28;-563.8383,1047.512;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;166;-947.3667,313.1952;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;167;-940.3667,342.1952;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;172;-935.4297,168.8707;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;175;134.8828,1023.933;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;89;132.3722,225.1724;Inherit;False;3;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RangedFloatNode;90;-178.7889,316.7153;Half;False;Property;_Brightness;Brightness;5;0;Create;False;1;;0;0;False;0;False;1;1.12;0;2;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SwizzleNode;88;-67.31689,117.2819;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SamplerNode;25;-881.525,1049.121;Inherit;True;Property;_TextureSample17;Texture
    Sample 17;37;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;6;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT3;5\nNode;AmplifyShaderEditor.RegisterLocalVarNode;107;-573.5995,341.3481;Inherit;False;MapBaseColorAlpha;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.StickyNoteNode;199;608.4469,-564.8409;Inherit;False;240;123;HDRP
    Hidden _Cutoff;;0,0,0,1;_Cutoff must be set on both Master Node Properties Additional
    Options and Material Inspector;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;200;396.2227,-795.7266;Inherit;False;351.7355;100;Alpha;;0,0,0,1;If
    Alpha value  is lower than Alpha Clip Threshold then Alpha Clip happens;0;0\nNode;AmplifyShaderEditor.WireNode;213;-942.2749,340.095;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;214;-945.2749,316.095;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.SwizzleNode;62;-1346.32,28.58244;Inherit;False;FLOAT2;0;1;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SwizzleNode;61;-1346.408,98.75526;Inherit;False;FLOAT2;2;3;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;132;-1200.516,-68.01764;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;225;-945.3745,317.6628;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;226;-941.2078,343.7038;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.StickyNoteNode;250;800.675,2775.06;Inherit;False;251.7784;100;Color
    Double Space;;0,0,0,1;UNITY_COLORSPACE_GAMMA;0;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;253;449.9873,314.1446;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;255;407.1302,283.0376;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;254;386.9874,259.5376;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitch;251;597.2244,221.9676;Inherit;False;Enable
    Detail Map;True;0;2;0;In 0;In 1;Object;-1;10;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.StickyNoteNode;256;-864.7835,2405.52;Inherit;False;248.6713;109.1362;Detail
    Mapping;;0,0,0,1;Adds Detail Abledo X2 map, similar to Unity Standard Material;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;257;443.0549,835.5297;Inherit;False;178.6;100;;;0,0,0,1;Saturate
    Occclusion to Prevent Nan in Unity;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;258;-887.972,653.0121;Inherit;False;271.6077;121.433;SS
    Sampler States Normal Map;;0,0,0,1;Keep Normal SS separated to prevent possible
    GPU crossing in fragment stages of the Mip stream;0;0\nNode;AmplifyShaderEditor.ColorNode;87;-282.5479,116.7919;Half;False;Property;_BaseColor;Base
    Color;4;1;[Header];Create;False;1;SURFACE INPUTS;0;0;False;0;False;1,1,1,0;0,0,0,0;True;True;0;6;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT3;5\nNode;AmplifyShaderEditor.WireNode;119;-79.65927,-928.6104;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitchByPipeline;100;391.4799,-935.8264;Inherit;False;4;0;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;159;280.0515,-916.4954;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;124;280.9279,-915.1696;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;155;-22.15655,-928.6992;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.ClipNode;110;44.12825,-810.968;Inherit;False;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;160;-12.10296,-801.3776;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;120;-62.08387,-912.9841;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;156;-8.574277,-907.7388;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;121;-55.69407,-783.1738;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.OneMinusNode;116;-142.7097,-611.6492;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitchByPipeline;122;395.6505,-632.004;Inherit;False;4;0;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;125;236.383,-523.4164;Inherit;False;Property;_Cutoff;Cutoff;0;1;[HideInInspector];Create;False;1;;0;0;False;0;False;0;0.9;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitchByPipeline;130;341.7911,-345.8258;Inherit;False;4;0;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;117;-427.1242,-586.106;Half;False;Property;_AlphaCutoffBias;Alpha
    Cutoff Bias;1;1;[Header];Create;False;1;ALPHA CLIPPING;0;0;False;0;False;0.45;0.9;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;129;49.81786,-282.5333;Half;False;Property;_AlphaCutoffShadow;Alpha
    Cutoff Shadow;2;1;[HideInInspector];Create;False;0;0;0;False;0;False;1;0.851;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;128;51.15886,-357.0315;Half;False;Property;_AlphaCutoffShadowBias;Alpha
    Cutoff Shadow;3;1;[Header];Create;False;1;SHADOW;0;0;False;0;False;0.5;0.9;0.01;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.TexCoordVertexDataNode;131;-1570.266,-69.86572;Inherit;False;0;2;0;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.Vector4Node;63;-1546.033,98.95057;Inherit;False;Property;_MainUVs;Main
    UVs;7;0;Create;False;0;0;0;False;0;False;1,1,0,0;0,0,0,0;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SamplerNode;161;-880.037,1495.895;Inherit;True;Property;_TextureSample19;Texture
    Sample 17;37;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;6;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT3;5\nNode;AmplifyShaderEditor.SwizzleNode;163;-562.3504,1494.286;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;168;-921.5031,1553.615;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.SimpleSubtractOpNode;266;-377.2045,1348.398;Inherit;False;2;0;FLOAT;1;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;267;-375.6719,1436.923;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;1;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleDivideOpNode;265;-235.3692,1416.055;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;264;-86.97916,1404.596;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;84;-923.4906,1763.217;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.SamplerNode;26;-876.7107,1706.291;Inherit;True;Property;_TextureSample18;Texture
    Sample 18;38;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;6;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT3;5\nNode;AmplifyShaderEditor.SwizzleNode;29;-558.9517,1705.552;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;145;-923.2487,1716.914;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;135;130.4601,1685.978;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RangedFloatNode;134;-162.0386,1663.624;Inherit;False;Property;_MetallicStrength;Metallic
    Strength;11;0;Create;False;0;0;0;False;0;False;0;0;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;268;-587.093,1398.494;Inherit;False;Property;_SpecularColorIOR;Specular
    Color IOR;16;0;Create;True;0;0;0;False;0;False;0;1.51;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;261;12.00085,1300.465;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SwizzleNode;202;-174.484,1225.177;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.StickyNoteNode;269;261.9316,1187.466;Inherit;False;270.9963;145.2141;Enable
    Specular IOR;;0,0,0,1;;0;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;262;144.3333,1379.547;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;165;512.7896,1471.796;Inherit;False;3;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RangedFloatNode;141;213.6325,1567.85;Half;False;Property;_SpecularStrength;Specular
    Strength;14;0;Create;False;0;0;0;False;0;False;0.04;0;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.ColorNode;201;-587.5212,1225.079;Inherit;False;Property;_SpecularColor;Specular
    Color;13;0;Create;True;0;0;0;False;0;False;0,0,0,0;1,0.661858,0.1650943,0;True;True;0;6;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT3;5\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;176;300.7295,766.9329;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SaturateNode;144;451.9179,768.0354;Inherit;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RangedFloatNode;93;-172.9534,975.9557;Half;False;Property;_SmoothnessStrength;Smoothness
    Strength;20;0;Create;False;0;0;0;False;0;False;0;0;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;260;-241.0127,1313.953;Inherit;False;Property;_SpecularColorWeight;Specular
    Color Weight;15;0;Create;True;0;0;0;False;0;False;1;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionSwitch;263;283.1537,1222.275;Inherit;False;Enable
    Specular IOR;True;0;2;2;In 0;In 1;Object;-1;10;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.StickyNoteNode;272;-1381.016,252.4198;Inherit;False;150;100;BaseColor
    Map;;0,0,0,1;_MainTex;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;273;-1386.016,468.4198;Inherit;False;150;100;Normal
    Map;;0,0,0,1;_BumpMap;0;0\nNode;AmplifyShaderEditor.TexturePropertyNode;20;-1221.175,470.7509;Inherit;True;Property;_BumpMap;Normal
    Map;8;2;[Normal];[SingleLineTexture];Create;False;0;0;0;False;0;False;None;None;False;bump;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.StickyNoteNode;274;-1383.016,793.4198;Inherit;False;150;100;Occlusion
    Map;;0,0,0,1;_OcclusionMap;0;0\nNode;AmplifyShaderEditor.TexturePropertyNode;42;-1220.496,793.3965;Inherit;True;Property;_OcclusionMap;Occlusion
    Map;17;1;[SingleLineTexture];Create;False;1;;0;0;False;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.StickyNoteNode;275;-1381.016,1055.42;Inherit;False;150;100;Smoothness
    Map;;0,0,0,1;_SmoothnessMap;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;276;-1375.35,1496.616;Inherit;False;150;100;Specular
    Map;;0,0,0,1;_SpecularMap;0;0\nNode;AmplifyShaderEditor.TexturePropertyNode;164;-1216.522,1496.296;Inherit;True;Property;_SpecularMap;Specular
    Map;12;1;[SingleLineTexture];Create;False;0;0;0;False;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.StickyNoteNode;277;-1390.35,1709.616;Inherit;False;170;100;Metallic
    Map;;0,0,0,1;_MetallicGlossMap;0;0\nNode;AmplifyShaderEditor.TexturePropertyNode;40;-1209.397,1707.485;Inherit;True;Property;_MetallicGlossMap;Metallic
    Map;10;1;[SingleLineTexture];Create;False;1;;0;0;False;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.StickyNoteNode;278;-1400.24,2623.271;Inherit;False;150;100;Detail
    Map;;0.0471698,0.0471698,0.0471698,1;_DetailColorMap;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;279;-1445.24,3105.271;Inherit;False;186;100;Detail
    Mask Map;;0,0,0,1;_DetailColorMaskMap;0;0\nNode;AmplifyShaderEditor.StickyNoteNode;338;-864,3376;Inherit;False;150;100;Emissive
    Color;;0,0,0,1;_EmissiveColor;0;0\nNode;AmplifyShaderEditor.TexturePropertyNode;17;-1229.66,248.597;Inherit;True;Property;_MainTex;BaseColor
    Map;6;1;[SingleLineTexture];Create;False;1;;0;0;False;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.WireNode;342;-946.9427,318.1741;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.WireNode;343;-941.9427,343.1741;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.StickyNoteNode;345;-1431.749,3848.851;Inherit;False;181.6185;101.1292;Emission
    Map;;0,0,0,1;_EmissiveColorMap;0;0\nNode;AmplifyShaderEditor.GetLocalVarNode;252;248.0298,333.3841;Inherit;False;197;Detail;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;348;862.3754,138.1517;Inherit;False;BaseColor;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.OneMinusNode;271;107.6529,722.4769;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;138;-183.1943,718.1416;Half;False;Property;_OcclusionStrengthAO;Occlusion
    Strength;18;0;Create;False;0;0;0;False;0;False;0;0;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.StickyNoteNode;373;-1407.415,1982.231;Inherit;False;181.6185;101.1292;Displacement
    Map;;0,0,0,1;_ParallaxMap;0;0\nNode;AmplifyShaderEditor.WireNode;215;-930.6605,2570.821;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.SwizzleNode;206;-562.8495,2556.477;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SwizzleNode;204;-76.51767,2402.426;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;207;111.9247,2533.749;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.DynamicAppendNode;189;425.6454,2740.345;Inherit;False;FLOAT3;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.OneMinusNode;191;263.9453,2771.977;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;190;279.4809,2604.007;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;192;607.1795,2600.801;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;220;-1032.712,2439.43;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SwizzleNode;211;-1310.076,2388.958;Inherit;False;FLOAT2;0;1;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SwizzleNode;212;-1314.076,2458.958;Inherit;False;FLOAT2;2;3;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;219;-1157.486,2291.437;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SamplerNode;224;-877.8821,3037.052;Inherit;True;Property;_TextureSample0;Texture
    Sample 0;24;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;6;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT3;5\nNode;AmplifyShaderEditor.WireNode;227;-930.1886,3048.853;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.SwizzleNode;232;-1294.416,2927.589;Inherit;False;FLOAT2;2;3;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;243;-528.774,3105.996;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;244;-526.4912,3121.976;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;245;-455.7229,2813.792;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;241;-517.3595,2829.771;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;242;-489.9655,2672.253;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.WireNode;240;-515.0767,2711.062;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SwizzleNode;247;825.9965,2699.044;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.ColorSpaceDouble;248;600.6909,2703.713;Inherit;False;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SamplerNode;208;-879.6667,2557.56;Inherit;True;Property;_Texture0;Texture
    0;5;0;Create;True;1;;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;6;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT3;5\nNode;AmplifyShaderEditor.ColorNode;205;-309.3716,2403.474;Half;False;Property;_DetailColor;Detail
    Color;26;2;[HDR];[Header];Create;False;1;DETAIL MAPPING;0;0;False;0;False;1,1,1,0;1,1,1,0;True;True;0;6;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT3;5\nNode;AmplifyShaderEditor.SwizzleNode;231;-1293.956,2851.845;Inherit;False;FLOAT2;0;1;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.TexCoordVertexDataNode;221;-1528.382,2291.868;Inherit;False;0;2;0;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.Vector4Node;210;-1501.417,2460.125;Inherit;False;Property;_DetailUVs;Detail
    UVs;28;0;Create;False;0;0;0;False;0;False;1,1,0,0;0,0,0,0;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.Vector4Node;233;-1508.182,2928.964;Inherit;False;Property;_DetailMaskUVs;Detail
    Mask UVs;30;0;Create;False;0;0;0;False;0;False;1,1,0,0;0,0,0,0;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.TexturePropertyNode;209;-1237.464,2556.231;Inherit;True;Property;_DetailColorMap;Detail
    Map;27;1;[SingleLineTexture];Create;False;0;0;0;False;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.TexturePropertyNode;222;-1242.135,3039.132;Inherit;True;Property;_DetailColorMaskMap;Detail
    Mask Map;29;1;[SingleLineTexture];Create;False;0;0;0;False;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.SimpleAddOpNode;229;-1026.515,2908.063;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;228;-1148.159,2749.47;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.TexCoordVertexDataNode;230;-1479.125,2749.265;Inherit;False;0;2;0;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.WireNode;344;-928.9661,3766.156;Inherit;False;1;0;SAMPLERSTATE;;False;1;SAMPLERSTATE;0\nNode;AmplifyShaderEditor.SamplerNode;310;-876.3686,3769.354;Inherit;True;Property;_TextureSample1;Texture
    Sample 0;51;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;6;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT3;5\nNode;AmplifyShaderEditor.Vector4Node;306;-1522.679,3684.753;Inherit;False;Property;_EmissiveColorMapUVs;Emissive
    UVs;23;0;Create;False;0;0;0;False;0;False;1,1,0,0;0,0,0,0;0;5;FLOAT4;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SwizzleNode;308;-1297.255,3683.296;Inherit;False;FLOAT2;2;3;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;339;-1030.17,3663.459;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.TexCoordVertexDataNode;341;-1540.451,3538.034;Inherit;False;0;2;0;5;FLOAT2;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;340;-1148.238,3537.049;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SwizzleNode;307;-1296.311,3606.75;Inherit;False;FLOAT2;0;1;2;3;1;0;FLOAT4;0,0,0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SwizzleNode;281;-566.6566,3767.664;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.HDEmissionNode;336;-412.0588,3591.719;Inherit;False;Luminance;False;3;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;2;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;350;-411.5123,3496.246;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionSwitchByPipeline;351;-137.2124,3497.546;Inherit;False;4;0;FLOAT3;0,0,0;False;3;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;349;-96.71767,3718.88;Inherit;False;348;BaseColor;1;0;OBJECT;;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.LerpOp;347;104.5245,3722.526;Inherit;False;3;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RangedFloatNode;315;-709.6938,3682.206;Half;False;Property;_EmissiveExposureWeight;Emissive
    Exposure Weight;25;0;Create;False;0;0;0;False;0;False;1;0;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;313;-708.3562,3611.328;Half;False;Property;_EmissiveIntensity;Emissive
    Intensity;24;0;Create;False;1;;0;0;False;0;False;1;86.9;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SwizzleNode;337;-609.3882,3491.397;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.ColorNode;311;-900.616,3492.086;Half;False;Property;_EmissiveColor;Emissive
    Color;21;2;[HDR];[Header];Create;False;1;EMISSION;0;0;False;0;False;0,0,0,0;1,0,0,0;True;True;0;6;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT3;5\nNode;AmplifyShaderEditor.FunctionSwitch;346;288.9194,3492.453;Inherit;False;Enable
    Emission Map;True;1;2;3;In 0;In 1;Object;-1;10;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.TexturePropertyNode;334;-1242.987,3770.778;Inherit;True;Property;_EmissiveColorMap;Emission
    Map;22;2;[HDR];[SingleLineTexture];Create;False;0;0;0;False;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.SimpleAddOpNode;133;-1057.071,82.13205;Inherit;False;2;2;0;FLOAT2;0,0;False;1;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.SamplerNode;367;-886.3585,1979.682;Inherit;True;Property;_TextureSample3;Texture
    Sample 3;2;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;6;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4;FLOAT3;5\nNode;AmplifyShaderEditor.SwizzleNode;375;-570.895,1978.973;Inherit;False;FLOAT3;0;1;2;3;1;0;COLOR;0,0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;376;-932.2631,1958.415;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.WireNode;377;-936.4933,164.7034;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.TexturePropertyNode;41;-1218.572,1047.668;Inherit;True;Property;_SmoothnessMap;Smoothness
    Map;19;1;[SingleLineTexture];Create;False;0;0;0;False;0;False;None;None;False;white;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.RangedFloatNode;368;-573.0748,2070.854;Half;False;Property;_DisplacementStrength;Displacement
    Strength;32;0;Create;False;1;;0;0;False;0;False;0;0;0;1;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;369;-250.7418,1984.011;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.NormalVertexDataNode;370;-93.2364,1852.19;Inherit;False;0;5;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;371;131.242,1960.088;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.WireNode;173;-926.7717,1534.308;Inherit;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.TexturePropertyNode;366;-1210.762,1979.812;Inherit;True;Property;_ParallaxMap;Displacement
    Map;31;2;[Header];[SingleLineTexture];Create;False;1;DISPLACEMENT;0;0;False;0;False;None;None;False;black;Auto;Texture2D;-1;0;2;SAMPLER2D;0;SAMPLERSTATE;1\nNode;AmplifyShaderEditor.FunctionSwitch;216;742.2255,2529.247;Inherit;False;Enable
    Detail Mask;True;0;2;1;In 0;In 1;Object;-1;10;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;6;FLOAT;0;False;7;FLOAT;0;False;8;FLOAT;0;False;9;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;246;1024,2528;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;197;1184,2528;Inherit;False;Detail;-1;True;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.SaturateNode;115;224,-816;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;118;-400,-976;Inherit;False;107;MapBaseColorAlpha;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;126;192,-624;Inherit;False;Constant;_Float0;Float
    0;13;0;Create;True;0;0;0;False;0;False;0.45;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;71;610.3669,-632.0208;Inherit;False;False;-1;Alpha
    Clip Threshold;8;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;8;610.8042,-936.1411;Inherit;False;False;-1;Alpha;7;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;72;558.4174,-346.287;Inherit;False;False;-1;Alpha
    Clip Threshold Shadow;9;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;3;640.1386,1683.504;Inherit;False;False;-1;Metallic;3;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionOutput;2;640.2928,1025.881;Inherit;False;False;-1;Smoothness;5;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionOutput;4;643.2393,765.5605;Inherit;False;False;-1;Occlusion;6;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionOutput;5;651.6837,1472.174;Inherit;False;False;-1;Specular;4;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionOutput;1;853.9293,226.2791;Inherit;False;True;-1;BaseColor;0;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionOutput;328;557.5551,3497.498;Inherit;False;False;-1;Emission;2;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionOutput;6;6.897927,474.5773;Inherit;False;False;-1;Normal;1;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.FunctionOutput;378;319.8468,1961.713;Inherit;False;False;-1;Vertex
    Position;10;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nWireConnection;21;0;17;0\nWireConnection;21;1;151;0\nWireConnection;21;7;78;0\nWireConnection;75;0;17;1\nWireConnection;77;0;75;0\nWireConnection;78;0;17;1\nWireConnection;79;0;17;1\nWireConnection;80;0;79;0\nWireConnection;83;0;82;0\nWireConnection;82;0;17;1\nWireConnection;73;0;21;0\nWireConnection;22;0;20;0\nWireConnection;22;1;152;0\nWireConnection;22;7;20;1\nWireConnection;24;0;42;0\nWireConnection;24;1;153;0\nWireConnection;24;7;76;0\nWireConnection;76;0;77;0\nWireConnection;27;0;24;0\nWireConnection;150;0;133;0\nWireConnection;151;0;146;0\nWireConnection;146;0;133;0\nWireConnection;152;0;147;0\nWireConnection;153;0;148;0\nWireConnection;154;0;149;0\nWireConnection;149;0;133;0\nWireConnection;148;0;133;0\nWireConnection;147;0;133;0\nWireConnection;98;0;22;0\nWireConnection;98;1;97;0\nWireConnection;81;0;80;0\nWireConnection;28;0;25;0\nWireConnection;166;0;17;1\nWireConnection;167;0;166;0\nWireConnection;172;0;133;0\nWireConnection;175;0;93;0\nWireConnection;175;1;28;0\nWireConnection;89;0;88;0\nWireConnection;89;1;73;0\nWireConnection;89;2;90;0\nWireConnection;88;0;87;0\nWireConnection;25;0;41;0\nWireConnection;25;1;154;0\nWireConnection;25;7;81;0\nWireConnection;107;0;21;4\nWireConnection;213;0;214;0\nWireConnection;214;0;17;1\nWireConnection;62;0;63;0\nWireConnection;61;0;63;0\nWireConnection;132;0;131;0\nWireConnection;132;1;62;0\nWireConnection;225;0;17;1\nWireConnection;226;0;225;0\nWireConnection;253;0;255;0\nWireConnection;253;1;252;0\nWireConnection;255;0;254;0\nWireConnection;254;0;89;0\nWireConnection;251;0;89;0\nWireConnection;251;1;253;0\nWireConnection;119;0;118;0\nWireConnection;100;0;115;0\nWireConnection;100;3;115;0\nWireConnection;100;1;159;0\nWireConnection;100;2;124;0\nWireConnection;159;0;118;0\nWireConnection;124;0;118;0\nWireConnection;155;0;118;0\nWireConnection;110;0;160;0\nWireConnection;110;1;121;0\nWireConnection;110;2;116;0\nWireConnection;160;0;156;0\nWireConnection;120;0;119;0\nWireConnection;156;0;155;0\nWireConnection;121;0;120;0\nWireConnection;116;0;117;0\nWireConnection;122;0;126;0\nWireConnection;122;3;126;0\nWireConnection;122;1;117;0\nWireConnection;122;2;125;0\nWireConnection;130;0;128;0\nWireConnection;130;3;128;0\nWireConnection;130;1;128;0\nWireConnection;130;2;129;0\nWireConnection;161;0;164;0\nWireConnection;161;1;173;0\nWireConnection;161;7;168;0\nWireConnection;163;0;161;0\nWireConnection;168;0;167;0\nWireConnection;266;1;268;0\nWireConnection;267;0;268;0\nWireConnection;265;0;266;0\nWireConnection;265;1;267;0\nWireConnection;264;0;265;0\nWireConnection;264;1;265;0\nWireConnection;84;0;83;0\nWireConnection;26;0;40;0\nWireConnection;26;1;145;0\nWireConnection;26;7;84;0\nWireConnection;29;0;26;0\nWireConnection;145;0;150;0\nWireConnection;135;0;134;0\nWireConnection;135;1;29;0\nWireConnection;261;0;202;0\nWireConnection;261;1;260;0\nWireConnection;202;0;201;0\nWireConnection;262;0;261;0\nWireConnection;262;1;264;0\nWireConnection;165;0;263;0\nWireConnection;165;1;163;0\nWireConnection;165;2;141;0\nWireConnection;176;0;271;0\nWireConnection;176;1;27;0\nWireConnection;144;0;176;0\nWireConnection;263;0;202;0\nWireConnection;263;1;262;0\nWireConnection;342;0;17;1\nWireConnection;343;0;342;0\nWireConnection;348;0;251;0\nWireConnection;271;0;138;0\nWireConnection;215;0;213;0\nWireConnection;206;0;208;0\nWireConnection;204;0;205;0\nWireConnection;207;0;204;0\nWireConnection;207;1;206;0\nWireConnection;189;0;191;0\nWireConnection;189;1;191;0\nWireConnection;189;2;191;0\nWireConnection;191;0;245;0\nWireConnection;190;0;207;0\nWireConnection;190;1;242;0\nWireConnection;192;0;190;0\nWireConnection;192;1;189;0\nWireConnection;220;0;219;0\nWireConnection;220;1;212;0\nWireConnection;211;0;210;0\nWireConnection;212;0;210;0\nWireConnection;219;0;221;0\nWireConnection;219;1;211;0\nWireConnection;224;0;222;0\nWireConnection;224;1;229;0\nWireConnection;224;7;227;0\nWireConnection;227;0;226;0\nWireConnection;232;0;233;0\nWireConnection;243;0;224;4\nWireConnection;244;0;224;4\nWireConnection;245;0;241;0\nWireConnection;241;0;243;0\nWireConnection;242;0;240;0\nWireConnection;240;0;244;0\nWireConnection;247;0;248;0\nWireConnection;208;0;209;0\nWireConnection;208;1;220;0\nWireConnection;208;7;215;0\nWireConnection;231;0;233;0\nWireConnection;229;0;228;0\nWireConnection;229;1;232;0\nWireConnection;228;0;230;0\nWireConnection;228;1;231;0\nWireConnection;344;0;343;0\nWireConnection;310;0;334;0\nWireConnection;310;1;339;0\nWireConnection;310;7;344;0\nWireConnection;308;0;306;0\nWireConnection;339;0;340;0\nWireConnection;339;1;308;0\nWireConnection;340;0;341;0\nWireConnection;340;1;307;0\nWireConnection;307;0;306;0\nWireConnection;281;0;310;0\nWireConnection;336;0;337;0\nWireConnection;336;1;313;0\nWireConnection;336;2;315;0\nWireConnection;350;0;337;0\nWireConnection;350;1;313;0\nWireConnection;351;0;350;0\nWireConnection;351;3;350;0\nWireConnection;351;1;350;0\nWireConnection;351;2;336;0\nWireConnection;347;0;349;0\nWireConnection;347;1;351;0\nWireConnection;347;2;281;0\nWireConnection;337;0;311;0\nWireConnection;346;0;351;0\nWireConnection;346;1;347;0\nWireConnection;133;0;132;0\nWireConnection;133;1;61;0\nWireConnection;367;0;366;0\nWireConnection;367;1;376;0\nWireConnection;367;7;366;1\nWireConnection;375;0;367;0\nWireConnection;376;0;377;0\nWireConnection;377;0;133;0\nWireConnection;369;0;375;0\nWireConnection;369;1;368;0\nWireConnection;371;0;370;0\nWireConnection;371;1;369;0\nWireConnection;173;0;172;0\nWireConnection;216;0;207;0\nWireConnection;216;1;192;0\nWireConnection;246;0;216;0\nWireConnection;246;1;247;0\nWireConnection;197;0;246;0\nWireConnection;115;0;110;0\nWireConnection;71;0;122;0\nWireConnection;8;0;100;0\nWireConnection;72;0;130;0\nWireConnection;3;0;135;0\nWireConnection;2;0;175;0\nWireConnection;4;0;144;0\nWireConnection;5;0;165;0\nWireConnection;1;0;251;0\nWireConnection;328;0;346;0\nWireConnection;6;0;98;0\nWireConnection;378;0;371;0\nASEEND*/\n//CHKSM=CDE0E7808627D5482AA8964330CDA8C730EF901A"
  m_functionName: 
  m_description: Amplify Shader Pack Example
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 0
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_customEdited: 0
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 0
  m_headerStyle: 2
  m_headerColor: {r: 1, g: 0.4, b: 0, a: 1}
  m_customNodeCategory: Amplify Shader Pack
  m_previewPosition: 0
  m_hidden: 0
  m_url: 
