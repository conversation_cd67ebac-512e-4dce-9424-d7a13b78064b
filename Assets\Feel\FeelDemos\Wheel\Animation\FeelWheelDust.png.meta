fileFormatVersion: 2
guid: bad6c0f0bf6c3aa4690d400a757fa284
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 21300000
    second: Dust_0
  - first:
      213: 21300002
    second: Dust_1
  - first:
      213: 21300004
    second: Dust_2
  - first:
      213: 21300006
    second: Dust_3
  - first:
      213: 21300008
    second: Dust_4
  - first:
      213: 21300010
    second: Dust_5
  - first:
      213: 21300012
    second: Dust_6
  - first:
      213: 21300014
    second: Dust_7
  - first:
      213: 21300016
    second: Dust_8
  - first:
      213: 21300018
    second: Dust_9
  - first:
      213: 21300020
    second: Dust_10
  - first:
      213: 21300022
    second: Dust_11
  - first:
      213: 21300024
    second: Dust_12
  - first:
      213: 21300026
    second: Dust_13
  - first:
      213: 21300028
    second: Dust_14
  - first:
      213: 21300030
    second: Dust_15
  - first:
      213: 21300032
    second: Dust_16
  - first:
      213: 21300034
    second: Dust_17
  - first:
      213: 21300036
    second: Dust_18
  - first:
      213: 21300038
    second: Dust_19
  - first:
      213: 21300040
    second: Dust_20
  - first:
      213: 21300042
    second: Dust_21
  - first:
      213: 21300044
    second: Dust_22
  - first:
      213: 21300046
    second: Dust_23
  - first:
      213: 21300048
    second: Dust_24
  externalObjects: {}
  serializedVersion: 12
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMasterTextureLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 0
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 1
  cookieLightType: 1
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 1
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 1
  - serializedVersion: 3
    buildTarget: iPhone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 1
  - serializedVersion: 3
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 1
  - serializedVersion: 3
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 1
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Dust_0
      rect:
        serializedVersion: 2
        x: 0
        y: 1792
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 02305410000000000800000000000000
      internalID: 21300000
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Dust_1
      rect:
        serializedVersion: 2
        x: 256
        y: 1792
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 22305410000000000800000000000000
      internalID: 21300002
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Dust_2
      rect:
        serializedVersion: 2
        x: 512
        y: 1792
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 42305410000000000800000000000000
      internalID: 21300004
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Dust_3
      rect:
        serializedVersion: 2
        x: 768
        y: 1792
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 62305410000000000800000000000000
      internalID: 21300006
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Dust_4
      rect:
        serializedVersion: 2
        x: 1024
        y: 1792
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 82305410000000000800000000000000
      internalID: 21300008
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Dust_5
      rect:
        serializedVersion: 2
        x: 0
        y: 1536
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a2305410000000000800000000000000
      internalID: 21300010
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Dust_6
      rect:
        serializedVersion: 2
        x: 256
        y: 1536
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c2305410000000000800000000000000
      internalID: 21300012
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Dust_7
      rect:
        serializedVersion: 2
        x: 512
        y: 1536
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e2305410000000000800000000000000
      internalID: 21300014
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Dust_8
      rect:
        serializedVersion: 2
        x: 768
        y: 1536
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 03305410000000000800000000000000
      internalID: 21300016
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Dust_9
      rect:
        serializedVersion: 2
        x: 1024
        y: 1536
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 23305410000000000800000000000000
      internalID: 21300018
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Dust_10
      rect:
        serializedVersion: 2
        x: 0
        y: 1280
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 43305410000000000800000000000000
      internalID: 21300020
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Dust_11
      rect:
        serializedVersion: 2
        x: 256
        y: 1280
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 63305410000000000800000000000000
      internalID: 21300022
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Dust_12
      rect:
        serializedVersion: 2
        x: 512
        y: 1280
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 83305410000000000800000000000000
      internalID: 21300024
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Dust_13
      rect:
        serializedVersion: 2
        x: 768
        y: 1280
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a3305410000000000800000000000000
      internalID: 21300026
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Dust_14
      rect:
        serializedVersion: 2
        x: 1024
        y: 1280
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c3305410000000000800000000000000
      internalID: 21300028
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Dust_15
      rect:
        serializedVersion: 2
        x: 0
        y: 1024
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e3305410000000000800000000000000
      internalID: 21300030
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Dust_16
      rect:
        serializedVersion: 2
        x: 256
        y: 1024
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 04305410000000000800000000000000
      internalID: 21300032
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Dust_17
      rect:
        serializedVersion: 2
        x: 512
        y: 1024
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 24305410000000000800000000000000
      internalID: 21300034
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Dust_18
      rect:
        serializedVersion: 2
        x: 768
        y: 1024
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 44305410000000000800000000000000
      internalID: 21300036
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Dust_19
      rect:
        serializedVersion: 2
        x: 1024
        y: 1024
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 64305410000000000800000000000000
      internalID: 21300038
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Dust_20
      rect:
        serializedVersion: 2
        x: 0
        y: 768
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 84305410000000000800000000000000
      internalID: 21300040
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Dust_21
      rect:
        serializedVersion: 2
        x: 256
        y: 768
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a4305410000000000800000000000000
      internalID: 21300042
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Dust_22
      rect:
        serializedVersion: 2
        x: 512
        y: 768
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c4305410000000000800000000000000
      internalID: 21300044
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Dust_23
      rect:
        serializedVersion: 2
        x: 768
        y: 768
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e4305410000000000800000000000000
      internalID: 21300046
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Dust_24
      rect:
        serializedVersion: 2
        x: 1024
        y: 768
        width: 256
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 05305410000000000800000000000000
      internalID: 21300048
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      Dust_0: 21300000
      Dust_1: 21300002
      Dust_10: 21300020
      Dust_11: 21300022
      Dust_12: 21300024
      Dust_13: 21300026
      Dust_14: 21300028
      Dust_15: 21300030
      Dust_16: 21300032
      Dust_17: 21300034
      Dust_18: 21300036
      Dust_19: 21300038
      Dust_2: 21300004
      Dust_20: 21300040
      Dust_21: 21300042
      Dust_22: 21300044
      Dust_23: 21300046
      Dust_24: 21300048
      Dust_3: 21300006
      Dust_4: 21300008
      Dust_5: 21300010
      Dust_6: 21300012
      Dust_7: 21300014
      Dust_8: 21300016
      Dust_9: 21300018
  spritePackingTag: 
  pSDRemoveMatte: 0
  pSDShowRemoveMatteOption: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
AssetOrigin:
  serializedVersion: 1
  productId: 183370
  packageName: Feel
  packageVersion: 5.5
  assetPath: Assets/Feel/FeelDemos/Wheel/Animation/FeelWheelDust.png
  uploadId: 759320
