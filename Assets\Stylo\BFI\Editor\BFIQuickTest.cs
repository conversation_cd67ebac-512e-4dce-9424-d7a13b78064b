#if UNITY_EDITOR && URP_INSTALLED
using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using UnityEditor;
using Stylo.BFI.Universal;

namespace Stylo.BFI.Editor
{
    public static class BFIQuickTest
    {
        [MenuItem("Stylo/BFI/Quick Test Setup")]
        public static void SetupQuickTest()
        {
            Debug.Log("=== BFI Quick Test Setup ===");

            // Step 1: Check if BFI is in renderer
            if (!CheckBFIInRenderer())
            {
                Debug.Log("Adding BFI to renderer...");
                BFIIntegration.AddBFIToCurrentRenderer();
            }

            // Step 2: Create test volume profile
            var volumeProfile = CreateTestVolumeProfile();
            
            // Step 3: Create global volume in scene
            CreateGlobalVolume(volumeProfile);
            
            // Step 4: Create test objects
            CreateTestObjects();
            
            Debug.Log("=== BFI Quick Test Setup Complete ===");
            Debug.Log("1. Enter Play Mode");
            Debug.Log("2. Look for debug visualization (timing bars at bottom)");
            Debug.Log("3. Try different BFI modes in the Volume Profile");
            Debug.Log("4. Observe motion clarity on moving objects");
        }

        private static bool CheckBFIInRenderer()
        {
            var urpAsset = UnityEngine.Rendering.GraphicsSettings.currentRenderPipeline as UniversalRenderPipelineAsset;
            if (urpAsset == null) return false;

            var rendererDataListField = typeof(UniversalRenderPipelineAsset).GetField("m_RendererDataList", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            
            if (rendererDataListField == null) return false;
            
            var rendererDataArray = rendererDataListField.GetValue(urpAsset) as ScriptableRendererData[];
            if (rendererDataArray == null || rendererDataArray.Length == 0) return false;
            
            var rendererData = rendererDataArray[0] as UniversalRendererData;
            if (rendererData == null) return false;

            foreach (var feature in rendererData.rendererFeatures)
            {
                if (feature is BFIRendererFeature && feature.isActive)
                {
                    Debug.Log($"✅ BFI Renderer Feature found and active in {rendererData.name}");
                    return true;
                }
            }

            return false;
        }

        private static VolumeProfile CreateTestVolumeProfile()
        {
            // Create volume profile
            var volumeProfile = ScriptableObject.CreateInstance<VolumeProfile>();
            
            // Add BFI Effect
            var bfiEffect = volumeProfile.Add<BFIEffect>();
            
            // Configure for testing with debug visualization
            bfiEffect.Mode.value = BFIEffect.BFIMode.DoubleFrameRate;
            bfiEffect.Mode.overrideState = true;
            
            bfiEffect.Intensity.value = 1f;
            bfiEffect.Intensity.overrideState = true;
            
            bfiEffect.BrightnessCompensation.value = 1.8f;
            bfiEffect.BrightnessCompensation.overrideState = true;
            
            bfiEffect.DebugVisualization.value = true;
            bfiEffect.DebugVisualization.overrideState = true;
            
            // Save the profile
            string path = "Assets/Stylo/BFI/BFI_QuickTest_Profile.asset";
            AssetDatabase.CreateAsset(volumeProfile, path);
            AssetDatabase.SaveAssets();
            
            Debug.Log($"✅ Created test volume profile at: {path}");
            return volumeProfile;
        }

        private static void CreateGlobalVolume(VolumeProfile profile)
        {
            // Remove existing test volume if it exists
            var existingVolume = GameObject.Find("BFI Test Volume");
            if (existingVolume != null)
            {
                Object.DestroyImmediate(existingVolume);
            }

            // Create new global volume
            var volumeGO = new GameObject("BFI Test Volume");
            var volume = volumeGO.AddComponent<Volume>();
            volume.isGlobal = true;
            volume.profile = profile;
            
            Debug.Log("✅ Created Global Volume with BFI profile");
        }

        private static void CreateTestObjects()
        {
            // Remove existing test objects
            var existingTest = GameObject.Find("BFI Test Objects");
            if (existingTest != null)
            {
                Object.DestroyImmediate(existingTest);
            }

            // Create parent object
            var testParent = new GameObject("BFI Test Objects");
            
            // Create moving cube
            var cube = GameObject.CreatePrimitive(PrimitiveType.Cube);
            cube.name = "Moving Cube";
            cube.transform.parent = testParent.transform;
            cube.transform.position = new Vector3(-2, 0, 5);
            
            // Make it bright colored for better visibility
            var cubeRenderer = cube.GetComponent<Renderer>();
            var cubeMaterial = new Material(Shader.Find("Universal Render Pipeline/Lit"));
            cubeMaterial.color = Color.red;
            cubeRenderer.material = cubeMaterial;
            
            // Add movement
            var cubeMover = cube.AddComponent<BFITestMover>();
            cubeMover.speed = 3f;
            cubeMover.range = 4f;
            
            // Create moving sphere
            var sphere = GameObject.CreatePrimitive(PrimitiveType.Sphere);
            sphere.name = "Moving Sphere";
            sphere.transform.parent = testParent.transform;
            sphere.transform.position = new Vector3(2, 0, 5);
            
            var sphereRenderer = sphere.GetComponent<Renderer>();
            var sphereMaterial = new Material(Shader.Find("Universal Render Pipeline/Lit"));
            sphereMaterial.color = Color.blue;
            sphereRenderer.material = sphereMaterial;
            
            var sphereMover = sphere.AddComponent<BFITestMover>();
            sphereMover.speed = 2f;
            sphereMover.range = 3f;
            sphereMover.axis = Vector3.up;
            
            // Create text instructions
            var instructionsGO = new GameObject("Instructions");
            instructionsGO.transform.parent = testParent.transform;
            instructionsGO.transform.position = new Vector3(0, 3, 5);
            
            var textMesh = instructionsGO.AddComponent<TextMesh>();
            textMesh.text = "BFI Test Scene\nLook for timing bars at bottom\nCompare motion clarity with BFI on/off";
            textMesh.fontSize = 20;
            textMesh.color = Color.white;
            textMesh.anchor = TextAnchor.MiddleCenter;
            
            Debug.Log("✅ Created test objects with movement");
        }

        [MenuItem("Stylo/BFI/Test - Enable Debug Visualization")]
        public static void EnableDebugVisualization()
        {
            var stack = VolumeManager.instance?.stack;
            if (stack != null)
            {
                var bfiEffect = stack.GetComponent<BFIEffect>();
                if (bfiEffect != null)
                {
                    bfiEffect.DebugVisualization.value = true;
                    Debug.Log("✅ Debug visualization enabled");
                }
                else
                {
                    Debug.LogWarning("❌ No BFI Effect found in volume stack");
                }
            }
            else
            {
                Debug.LogWarning("❌ Volume Manager not available");
            }
        }

        [MenuItem("Stylo/BFI/Test - Force BFI Active")]
        public static void ForceBFIActive()
        {
            var stack = VolumeManager.instance?.stack;
            if (stack != null)
            {
                var bfiEffect = stack.GetComponent<BFIEffect>();
                if (bfiEffect != null)
                {
                    bfiEffect.Mode.value = BFIEffect.BFIMode.DoubleFrameRate;
                    bfiEffect.Mode.overrideState = true;
                    bfiEffect.Intensity.value = 1f;
                    bfiEffect.Intensity.overrideState = true;
                    bfiEffect.DebugVisualization.value = true;
                    bfiEffect.DebugVisualization.overrideState = true;
                    
                    Debug.Log($"✅ BFI forced active - IsActive: {bfiEffect.IsActive()}");
                }
                else
                {
                    Debug.LogWarning("❌ No BFI Effect found in volume stack");
                }
            }
        }
    }

    // Simple test movement component
    public class BFITestMover : MonoBehaviour
    {
        public float speed = 2f;
        public float range = 3f;
        public Vector3 axis = Vector3.right;
        private Vector3 startPos;

        void Start()
        {
            startPos = transform.position;
        }

        void Update()
        {
            float offset = Mathf.Sin(Time.time * speed) * range;
            transform.position = startPos + axis * offset;
        }
    }
}
#endif
