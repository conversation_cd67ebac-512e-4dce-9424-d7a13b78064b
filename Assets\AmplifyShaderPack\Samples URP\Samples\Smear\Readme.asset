%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e78934177bebe0545ab566bb787b0282, type: 3}
  m_Name: Readme
  m_EditorClassIdentifier: 
  Icon: {fileID: 2800000, guid: 31491d604b3b7c14fb7db96cb0d490a3, type: 3}
  Title: Smear
  Description:
    Heading: 
    Text: This samples creates a smear effect by checking current and past object
      position and creating a new offset vertex position according to them.
    LinkText: 
    Url: 
  PropertiesHeader:
    Heading: Properties
    Text: 
    LinkText: 
    Url: 
  Properties:
  - Heading: Albedo
    Text: Albedo texture to be applied to surface.
    LinkText: 
    Url: 
  - Heading: Tint
    Text: Color tint to be applied over albedo channel.
    LinkText: 
    Url: 
  - Heading: Normal
    Text: Normals texture to be applied to surface.
    LinkText: 
    Url: 
  - Heading: Phong Tess Strength
    Text: Phong strength to be set over distance based tessellation.
    LinkText: 
    Url: 
  - Heading: Smoothness
    Text: Overall smoothness value for surface.
    LinkText: 
    Url: 
  - Heading: Metallic
    Text: Overall metallic value for surface.
    LinkText: 
    Url: 
  - Heading: Noise Influence
    Text: Value that sets the amount of influence generated noise has on smear effect.
    LinkText: 
    Url: 
  - Heading: Noise Height Scale
    Text: Scale value for generated noise.
    LinkText: 
    Url: 
  - Heading: Noise Tilling
    Text: Amount of tiling on generated noise.
    LinkText: 
    Url: 
  AdditionalProperties: []
  AdditionalScripts:
  - BlockHeader:
      Heading: Scripts
      Text: 
      LinkText: 
      Url: 
    BlockContent:
    - Heading: Simple Move Example
      Text: Moves object on a random way with specific speed over specified boundaries.
      LinkText: 
      Url: 
    - Heading: Smear
      Text: Stores object old positions and feed both old and new positions to shader.
      LinkText: 
      Url: 
  LoadedLayout: 0
  RPType: 3
