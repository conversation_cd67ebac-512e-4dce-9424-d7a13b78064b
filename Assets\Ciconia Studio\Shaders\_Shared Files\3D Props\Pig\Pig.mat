%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Pig
  m_Shader: {fileID: 4800000, guid: 933532a4fcc9baf4fa0491de14d08ed7, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords:
  - _EMISSION
  - _NORMALMAP
  m_InvalidKeywords:
  - _FALLOFFOPACITY_ON
  - _FRESNELONOFF_ON
  - _USECUBEMAP_OFF
  m_LightmapFlags: 0
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap:
    RenderType: Opaque
  disabledShaderPasses:
  - MOTIONVECTORS
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BaseMap:
        m_Texture: {fileID: 2800000, guid: 5cd2ac68c9dc5ae46aca6a5938207db9, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 2800000, guid: 9251973286db4b14db73f55db66d934d, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _CubeMap:
        m_Texture: {fileID: 8900000, guid: 5bd3dde6ef6012d4fabbbeddb40d6a4b, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: 5cd2ac68c9dc5ae46aca6a5938207db9, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap1:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _texcoord:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AddPrecomputedVelocity: 0
    - _AlphaClip: 0
    - _AlphaToMask: 0
    - _Ambientlight: 1
    - _AoIntensity: 0
    - _Blend: 0
    - _BlendModePreserveSpecular: 1
    - _BlurReflection: 0
    - _Brightness: 1
    - _BumpScale: 1
    - _BumpScaleDecal: 0.1
    - _BumpScaleDecal1: 0
    - _ChromaticAberration: 0
    - _ClearCoatMask: 0
    - _ClearCoatSmoothness: 0
    - _Cull: 2
    - _Cutoff: 0.5
    - _DetailAlbedoMapScale: 1
    - _DetailNormalMapScale: 1
    - _DstBlend: 0
    - _DstBlendAlpha: 0
    - _EmissionIntensity: 1
    - _EmissiveIntensity: 1
    - _EnvironmentReflections: 1
    - _Fade: 0
    - _FalloffFade: 0
    - _FalloffFade1: 0
    - _FalloffOpacity: 1
    - _FalloffOpacityIntensity: 0.497
    - _FresnelStrength: 0.5
    - _GlossMapScale: 1
    - _Glossiness: 0
    - _GlossinessDecal: 0.5
    - _GlossyReflections: 1
    - _Gradient1: 0.724
    - _GradientFade: 1
    - _IndexofRefraction: 1
    - _Intensity: 1
    - _Invert: 0
    - _InvertAlbedoA: 0
    - _InvertAlbedoA1: 0
    - _InvertFresnelFade: 0
    - _Metallic: 0
    - _MetallicDecal: 0.2
    - _Mode: 0
    - _OcclusionStrength: 1
    - _OffsetX: 0
    - _OffsetY: 0
    - _Opacity: 0.252
    - _Parallax: 0.02
    - _Power1: 2
    - _PowerFalloffFade: 0.5
    - _PowerFalloffOpacity: 2.29
    - _PowerFresnel: 0.81
    - _QueueOffset: 0
    - _ReceiveShadows: 1
    - _ReflectionDecal: 0
    - _ReflectionIntensity: 1
    - _Refraction: 1.2
    - _Rotation: 0
    - _Saturation: 0
    - _SaturationDecal: 0
    - _Smoothness: 0
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _SrcBlendAlpha: 1
    - _Surface: 0
    - _TilingX: 0
    - _TilingY: 0
    - _UVSec: 0
    - _UseAlbedoA: 0
    - _UseAlbedoA1: 0
    - _UseCubemap: 0
    - _UseSmoothness: 0
    - _Weight: 1
    - _WorkflowMode: 1
    - _ZWrite: 1
    - __dirty: 1
    m_Colors:
    - _BaseColor: {r: 1, g: 1, b: 1, a: 0}
    - _Color: {r: 1, g: 1, b: 1, a: 0}
    - _ColorCubemap: {r: 1, g: 1, b: 1, a: 1}
    - _ColorDecal: {r: 1, g: 1, b: 1, a: 1}
    - _ColorFresnel: {r: 0, g: 0, b: 0, a: 0}
    - _ColorFresnel1: {r: 0.8962264, g: 0, b: 0.7196971, a: 0}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 0}
    - _SecondaryColor: {r: 1, g: 1, b: 1, a: 0}
    - _SpecColor: {r: 0.19999996, g: 0.19999996, b: 0.19999996, a: 1}
  m_BuildTextureStacks: []
  m_AllowLocking: 1
--- !u!114 &7953481233189883035
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0353a89b1f911e48b9e16bdc9f2e058, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 9
