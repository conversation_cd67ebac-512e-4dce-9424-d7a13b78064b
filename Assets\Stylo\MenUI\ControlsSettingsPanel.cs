using UnityEngine;
using UnityEngine.UI;
using UnityEngine.InputSystem;
using TMPro;
using System.Collections.Generic;

namespace Stylo.MenUI
{
    /// <summary>
    /// Controls settings panel that provides controls for input sensitivity,
    /// key bindings, controller options, and other input-related settings.
    /// </summary>
    public class ControlsSettingsPanel : MonoBehaviour
    {
        [Header("Sensitivity Settings")]
        [SerializeField] private Slider mouseSensitivitySlider;
        [SerializeField] private TextMeshProUGUI mouseSensitivityValueText;
        [SerializeField] private Slider controllerSensitivitySlider;
        [SerializeField] private TextMeshProUGUI controllerSensitivityValueText;
        [SerializeField] private Slider scrollSensitivitySlider;
        [SerializeField] private TextMeshProUG<PERSON> scrollSensitivityValueText;

        [Header("Mouse Settings")]
        [SerializeField] private Toggle invertMouseYToggle;
        [SerializeField] private Toggle rawMouseInputToggle;
        [SerializeField] private Slider mouseAccelerationSlider;
        [SerializeField] private TextMeshProUGUI mouseAccelerationValueText;
        [SerializeField] private TMP_Dropdown mouseCursorModeDropdown;

        [Header("Controller Settings")]
        [SerializeField] private Toggle invertControllerYToggle;
        [SerializeField] private Slider controllerDeadzoneSlider;
        [SerializeField] private TextMeshProUGUI controllerDeadzoneValueText;
        [SerializeField] private Toggle controllerVibrationToggle;
        [SerializeField] private TMP_Dropdown controllerTypeDropdown;

        [Header("Key Binding Display")]
        [SerializeField] private Transform keyBindingContainer;
        [SerializeField] private GameObject keyBindingItemPrefab;

        [Header("Input Actions")]
        [SerializeField] private InputActionAsset inputActions;

        [Header("Accessibility")]
        [SerializeField] private Toggle colorBlindAssistToggle;
        [SerializeField] private Toggle subtitlesToggle;
        [SerializeField] private Slider uiScaleSlider;
        [SerializeField] private TextMeshProUGUI uiScaleValueText;
        [SerializeField] private Toggle highContrastToggle;
        [SerializeField] private Toggle reducedMotionToggle;
        [SerializeField] private Toggle screenReaderToggle;

        [Header("Advanced Input")]
        [SerializeField] private Toggle holdToRunToggle;
        [SerializeField] private Toggle autoAimToggle;
        [SerializeField] private Slider inputBufferSlider;
        [SerializeField] private TextMeshProUGUI inputBufferValueText;
        [SerializeField] private Toggle doubleClickToggle;
        [SerializeField] private Slider doubleClickSpeedSlider;
        [SerializeField] private TextMeshProUGUI doubleClickSpeedValueText;

        // Data
        private ControlsSettingsData _currentSettings;
        private List<KeyBindingItem> _keyBindingItems = new List<KeyBindingItem>();

        // Events
        public System.Action<ControlsSettingsData> OnSettingsChanged;

        [System.Serializable]
        public class ControlsSettingsData
        {
            [Range(0.1f, 5f)] public float mouseSensitivity = 1.0f;
            [Range(0.1f, 5f)] public float controllerSensitivity = 1.0f;
            [Range(0.1f, 3f)] public float scrollSensitivity = 1.0f;
            public bool invertMouseY = false;
            public bool rawMouseInput = true;
            [Range(0f, 2f)] public float mouseAcceleration = 0f;
            public int mouseCursorMode = 0; // 0=Locked, 1=Confined, 2=Free
            public bool invertControllerY = false;
            [Range(0f, 0.9f)] public float controllerDeadzone = 0.2f;
            public bool controllerVibrationEnabled = true;
            public int controllerType = 0; // 0=Xbox, 1=PlayStation, 2=Generic

            // Accessibility Settings
            public bool colorBlindAssistEnabled = false;
            public bool subtitlesEnabled = false;
            [Range(0.5f, 2f)] public float uiScale = 1.0f;
            public bool highContrastEnabled = false;
            public bool reducedMotionEnabled = false;
            public bool screenReaderEnabled = false;

            // Advanced Input Settings
            public bool holdToRunEnabled = false;
            public bool autoAimEnabled = false;
            [Range(0f, 500f)] public float inputBufferTime = 100f; // milliseconds
            public bool doubleClickEnabled = true;
            [Range(100f, 1000f)] public float doubleClickSpeed = 300f; // milliseconds
            public Dictionary<string, string> keyBindings = new Dictionary<string, string>();
        }

        [System.Serializable]
        public class KeyBindingItem
        {
            public string actionName;
            public TextMeshProUGUI actionLabel;
            public Button bindingButton;
            public TextMeshProUGUI bindingText;
            public InputAction inputAction;
        }

        #region Unity Lifecycle

        private void Awake()
        {
            InitializeComponents();
            LoadCurrentSettings();
        }

        private void Start()
        {
            SetupSliders();
            SetupDropdowns();
            SetupToggles();
            SetupKeyBindings();
            RefreshUI();
        }

        private void OnEnable()
        {
            RefreshUI();
        }

        #endregion

        #region Initialization

        private void InitializeComponents()
        {
            _currentSettings = new ControlsSettingsData();

            // Find input actions if not assigned
            if (inputActions == null)
            {
                // Try to find the default controls asset
                var defaultControls = Resources.Load<InputActionAsset>("DefaultControls");
                if (defaultControls != null)
                    inputActions = defaultControls;
            }
        }

        private void SetupSliders()
        {
            if (mouseSensitivitySlider != null)
            {
                mouseSensitivitySlider.minValue = 0.1f;
                mouseSensitivitySlider.maxValue = 5f;
                mouseSensitivitySlider.onValueChanged.AddListener(OnMouseSensitivityChanged);
            }

            if (controllerSensitivitySlider != null)
            {
                controllerSensitivitySlider.minValue = 0.1f;
                controllerSensitivitySlider.maxValue = 5f;
                controllerSensitivitySlider.onValueChanged.AddListener(OnControllerSensitivityChanged);
            }

            if (scrollSensitivitySlider != null)
            {
                scrollSensitivitySlider.minValue = 0.1f;
                scrollSensitivitySlider.maxValue = 3f;
                scrollSensitivitySlider.onValueChanged.AddListener(OnScrollSensitivityChanged);
            }

            if (mouseAccelerationSlider != null)
            {
                mouseAccelerationSlider.minValue = 0f;
                mouseAccelerationSlider.maxValue = 2f;
                mouseAccelerationSlider.onValueChanged.AddListener(OnMouseAccelerationChanged);
            }

            if (controllerDeadzoneSlider != null)
            {
                controllerDeadzoneSlider.minValue = 0f;
                controllerDeadzoneSlider.maxValue = 0.9f;
                controllerDeadzoneSlider.onValueChanged.AddListener(OnControllerDeadzoneChanged);
            }

            // Accessibility sliders
            if (uiScaleSlider != null)
            {
                uiScaleSlider.minValue = 0.5f;
                uiScaleSlider.maxValue = 2f;
                uiScaleSlider.onValueChanged.AddListener(OnUIScaleChanged);
            }

            // Advanced Input sliders
            if (inputBufferSlider != null)
            {
                inputBufferSlider.minValue = 0f;
                inputBufferSlider.maxValue = 500f;
                inputBufferSlider.wholeNumbers = true;
                inputBufferSlider.onValueChanged.AddListener(OnInputBufferChanged);
            }

            if (doubleClickSpeedSlider != null)
            {
                doubleClickSpeedSlider.minValue = 100f;
                doubleClickSpeedSlider.maxValue = 1000f;
                doubleClickSpeedSlider.wholeNumbers = true;
                doubleClickSpeedSlider.onValueChanged.AddListener(OnDoubleClickSpeedChanged);
            }
        }

        private void SetupDropdowns()
        {
            SetupMouseCursorModeDropdown();
            SetupControllerTypeDropdown();
        }

        private void SetupMouseCursorModeDropdown()
        {
            if (mouseCursorModeDropdown == null) return;

            mouseCursorModeDropdown.ClearOptions();
            mouseCursorModeDropdown.AddOptions(new List<string>
            {
                "Locked (FPS Mode)",
                "Confined to Window",
                "Free Movement"
            });
            mouseCursorModeDropdown.onValueChanged.AddListener(OnMouseCursorModeChanged);
        }

        private void SetupControllerTypeDropdown()
        {
            if (controllerTypeDropdown == null) return;

            controllerTypeDropdown.ClearOptions();
            controllerTypeDropdown.AddOptions(new List<string>
            {
                "Xbox Controller",
                "PlayStation Controller",
                "Generic Controller"
            });
            controllerTypeDropdown.onValueChanged.AddListener(OnControllerTypeChanged);
        }

        private void SetupToggles()
        {
            if (invertMouseYToggle != null)
                invertMouseYToggle.onValueChanged.AddListener(OnInvertMouseYChanged);

            if (rawMouseInputToggle != null)
                rawMouseInputToggle.onValueChanged.AddListener(OnRawMouseInputChanged);

            if (invertControllerYToggle != null)
                invertControllerYToggle.onValueChanged.AddListener(OnInvertControllerYChanged);

            if (controllerVibrationToggle != null)
                controllerVibrationToggle.onValueChanged.AddListener(OnControllerVibrationChanged);

            // Accessibility toggles
            if (colorBlindAssistToggle != null)
                colorBlindAssistToggle.onValueChanged.AddListener(OnColorBlindAssistChanged);

            if (subtitlesToggle != null)
                subtitlesToggle.onValueChanged.AddListener(OnSubtitlesChanged);

            if (highContrastToggle != null)
                highContrastToggle.onValueChanged.AddListener(OnHighContrastChanged);

            if (reducedMotionToggle != null)
                reducedMotionToggle.onValueChanged.AddListener(OnReducedMotionChanged);

            if (screenReaderToggle != null)
                screenReaderToggle.onValueChanged.AddListener(OnScreenReaderChanged);

            // Advanced Input toggles
            if (holdToRunToggle != null)
                holdToRunToggle.onValueChanged.AddListener(OnHoldToRunChanged);

            if (autoAimToggle != null)
                autoAimToggle.onValueChanged.AddListener(OnAutoAimChanged);

            if (doubleClickToggle != null)
                doubleClickToggle.onValueChanged.AddListener(OnDoubleClickChanged);
        }

        private void SetupKeyBindings()
        {
            if (keyBindingContainer == null || keyBindingItemPrefab == null || inputActions == null)
                return;

            // Clear existing items
            foreach (Transform child in keyBindingContainer)
            {
                if (Application.isPlaying)
                    Destroy(child.gameObject);
                else
                    DestroyImmediate(child.gameObject);
            }
            _keyBindingItems.Clear();

            // Create key binding items for important actions
            var importantActions = new List<string>
            {
                "Move",
                "Look",
                "Jump",
                "Run",
                "Crouch",
                "Interact",
                "Fire",
                "Aim",
                "Reload",
                "Pause"
            };

            foreach (var actionMap in inputActions.actionMaps)
            {
                foreach (var action in actionMap.actions)
                {
                    if (importantActions.Contains(action.name))
                    {
                        CreateKeyBindingItem(action);
                    }
                }
            }
        }

        private void CreateKeyBindingItem(InputAction action)
        {
            GameObject itemObj = Instantiate(keyBindingItemPrefab, keyBindingContainer);

            var item = new KeyBindingItem
            {
                actionName = action.name,
                inputAction = action
            };

            // Find UI components
            item.actionLabel = itemObj.transform.Find("ActionLabel")?.GetComponent<TextMeshProUGUI>();
            item.bindingButton = itemObj.transform.Find("BindingButton")?.GetComponent<Button>();
            item.bindingText = item.bindingButton?.GetComponentInChildren<TextMeshProUGUI>();

            if (item.actionLabel != null)
                item.actionLabel.text = action.name;

            if (item.bindingText != null)
                item.bindingText.text = GetBindingDisplayString(action);

            if (item.bindingButton != null)
            {
                item.bindingButton.onClick.AddListener(() => StartRebinding(item));
            }

            _keyBindingItems.Add(item);
        }

        private string GetBindingDisplayString(InputAction action)
        {
            if (action.bindings.Count > 0)
            {
                return InputControlPath.ToHumanReadableString(
                    action.bindings[0].effectivePath,
                    InputControlPath.HumanReadableStringOptions.OmitDevice);
            }
            return "Unbound";
        }

        #endregion

        #region Settings Management

        private void LoadCurrentSettings()
        {
            // Load from PlayerPrefs or use defaults
            _currentSettings.mouseSensitivity = PlayerPrefs.GetFloat("MouseSensitivity", 1.0f);
            _currentSettings.controllerSensitivity = PlayerPrefs.GetFloat("ControllerSensitivity", 1.0f);
            _currentSettings.scrollSensitivity = PlayerPrefs.GetFloat("ScrollSensitivity", 1.0f);
            _currentSettings.invertMouseY = PlayerPrefs.GetInt("InvertMouseY", 0) == 1;
            _currentSettings.rawMouseInput = PlayerPrefs.GetInt("RawMouseInput", 1) == 1;
            _currentSettings.mouseAcceleration = PlayerPrefs.GetFloat("MouseAcceleration", 0f);
            _currentSettings.mouseCursorMode = PlayerPrefs.GetInt("MouseCursorMode", 0);
            _currentSettings.invertControllerY = PlayerPrefs.GetInt("InvertControllerY", 0) == 1;
            _currentSettings.controllerDeadzone = PlayerPrefs.GetFloat("ControllerDeadzone", 0.2f);
            _currentSettings.controllerVibrationEnabled = PlayerPrefs.GetInt("ControllerVibration", 1) == 1;
            _currentSettings.controllerType = PlayerPrefs.GetInt("ControllerType", 0);
        }

        public void ApplySettings()
        {
            // Apply cursor lock mode
            Cursor.lockState = _currentSettings.mouseCursorMode switch
            {
                0 => CursorLockMode.Locked,
                1 => CursorLockMode.Confined,
                _ => CursorLockMode.None
            };

            // Save to PlayerPrefs
            PlayerPrefs.SetFloat("MouseSensitivity", _currentSettings.mouseSensitivity);
            PlayerPrefs.SetFloat("ControllerSensitivity", _currentSettings.controllerSensitivity);
            PlayerPrefs.SetFloat("ScrollSensitivity", _currentSettings.scrollSensitivity);
            PlayerPrefs.SetInt("InvertMouseY", _currentSettings.invertMouseY ? 1 : 0);
            PlayerPrefs.SetInt("RawMouseInput", _currentSettings.rawMouseInput ? 1 : 0);
            PlayerPrefs.SetFloat("MouseAcceleration", _currentSettings.mouseAcceleration);
            PlayerPrefs.SetInt("MouseCursorMode", _currentSettings.mouseCursorMode);
            PlayerPrefs.SetInt("InvertControllerY", _currentSettings.invertControllerY ? 1 : 0);
            PlayerPrefs.SetFloat("ControllerDeadzone", _currentSettings.controllerDeadzone);
            PlayerPrefs.SetInt("ControllerVibration", _currentSettings.controllerVibrationEnabled ? 1 : 0);
            PlayerPrefs.SetInt("ControllerType", _currentSettings.controllerType);
            PlayerPrefs.Save();

            // Trigger settings changed event
            OnSettingsChanged?.Invoke(_currentSettings);
        }

        public void ResetToDefaults()
        {
            _currentSettings = new ControlsSettingsData();
            RefreshUI();
        }

        private void RefreshUI()
        {
            // Update sliders
            if (mouseSensitivitySlider != null)
            {
                mouseSensitivitySlider.SetValueWithoutNotify(_currentSettings.mouseSensitivity);
                UpdateMouseSensitivityText(_currentSettings.mouseSensitivity);
            }

            if (controllerSensitivitySlider != null)
            {
                controllerSensitivitySlider.SetValueWithoutNotify(_currentSettings.controllerSensitivity);
                UpdateControllerSensitivityText(_currentSettings.controllerSensitivity);
            }

            if (scrollSensitivitySlider != null)
            {
                scrollSensitivitySlider.SetValueWithoutNotify(_currentSettings.scrollSensitivity);
                UpdateScrollSensitivityText(_currentSettings.scrollSensitivity);
            }

            if (mouseAccelerationSlider != null)
            {
                mouseAccelerationSlider.SetValueWithoutNotify(_currentSettings.mouseAcceleration);
                UpdateMouseAccelerationText(_currentSettings.mouseAcceleration);
            }

            if (controllerDeadzoneSlider != null)
            {
                controllerDeadzoneSlider.SetValueWithoutNotify(_currentSettings.controllerDeadzone);
                UpdateControllerDeadzoneText(_currentSettings.controllerDeadzone);
            }

            // Update dropdowns
            if (mouseCursorModeDropdown != null)
                mouseCursorModeDropdown.SetValueWithoutNotify(_currentSettings.mouseCursorMode);

            if (controllerTypeDropdown != null)
                controllerTypeDropdown.SetValueWithoutNotify(_currentSettings.controllerType);

            // Update toggles
            if (invertMouseYToggle != null)
                invertMouseYToggle.SetIsOnWithoutNotify(_currentSettings.invertMouseY);

            if (rawMouseInputToggle != null)
                rawMouseInputToggle.SetIsOnWithoutNotify(_currentSettings.rawMouseInput);

            if (invertControllerYToggle != null)
                invertControllerYToggle.SetIsOnWithoutNotify(_currentSettings.invertControllerY);

            if (controllerVibrationToggle != null)
                controllerVibrationToggle.SetIsOnWithoutNotify(_currentSettings.controllerVibrationEnabled);

            // Update accessibility settings
            if (colorBlindAssistToggle != null)
                colorBlindAssistToggle.SetIsOnWithoutNotify(_currentSettings.colorBlindAssistEnabled);

            if (subtitlesToggle != null)
                subtitlesToggle.SetIsOnWithoutNotify(_currentSettings.subtitlesEnabled);

            if (uiScaleSlider != null)
            {
                uiScaleSlider.SetValueWithoutNotify(_currentSettings.uiScale);
                UpdateUIScaleText(_currentSettings.uiScale);
            }

            if (highContrastToggle != null)
                highContrastToggle.SetIsOnWithoutNotify(_currentSettings.highContrastEnabled);

            if (reducedMotionToggle != null)
                reducedMotionToggle.SetIsOnWithoutNotify(_currentSettings.reducedMotionEnabled);

            if (screenReaderToggle != null)
                screenReaderToggle.SetIsOnWithoutNotify(_currentSettings.screenReaderEnabled);

            // Update advanced input settings
            if (holdToRunToggle != null)
                holdToRunToggle.SetIsOnWithoutNotify(_currentSettings.holdToRunEnabled);

            if (autoAimToggle != null)
                autoAimToggle.SetIsOnWithoutNotify(_currentSettings.autoAimEnabled);

            if (inputBufferSlider != null)
            {
                inputBufferSlider.SetValueWithoutNotify(_currentSettings.inputBufferTime);
                UpdateInputBufferText(_currentSettings.inputBufferTime);
            }

            if (doubleClickToggle != null)
                doubleClickToggle.SetIsOnWithoutNotify(_currentSettings.doubleClickEnabled);

            if (doubleClickSpeedSlider != null)
            {
                doubleClickSpeedSlider.SetValueWithoutNotify(_currentSettings.doubleClickSpeed);
                UpdateDoubleClickSpeedText(_currentSettings.doubleClickSpeed);
            }
        }

        #endregion

        #region UI Callbacks

        private void OnMouseSensitivityChanged(float value)
        {
            _currentSettings.mouseSensitivity = value;
            UpdateMouseSensitivityText(value);
        }

        private void OnControllerSensitivityChanged(float value)
        {
            _currentSettings.controllerSensitivity = value;
            UpdateControllerSensitivityText(value);
        }

        private void OnScrollSensitivityChanged(float value)
        {
            _currentSettings.scrollSensitivity = value;
            UpdateScrollSensitivityText(value);
        }

        private void OnInvertMouseYChanged(bool value)
        {
            _currentSettings.invertMouseY = value;
        }

        private void OnRawMouseInputChanged(bool value)
        {
            _currentSettings.rawMouseInput = value;
        }

        private void OnMouseAccelerationChanged(float value)
        {
            _currentSettings.mouseAcceleration = value;
            UpdateMouseAccelerationText(value);
        }

        private void OnMouseCursorModeChanged(int value)
        {
            _currentSettings.mouseCursorMode = value;
        }

        private void OnInvertControllerYChanged(bool value)
        {
            _currentSettings.invertControllerY = value;
        }

        private void OnControllerDeadzoneChanged(float value)
        {
            _currentSettings.controllerDeadzone = value;
            UpdateControllerDeadzoneText(value);
        }

        private void OnControllerVibrationChanged(bool value)
        {
            _currentSettings.controllerVibrationEnabled = value;
        }

        private void OnControllerTypeChanged(int value)
        {
            _currentSettings.controllerType = value;
        }

        // Accessibility Callbacks
        private void OnColorBlindAssistChanged(bool value)
        {
            _currentSettings.colorBlindAssistEnabled = value;

            // Apply color blind assistance filters
            ApplyColorBlindFilters(value);
            Debug.Log($"ControlsSettingsPanel: Color blind assistance {(value ? "enabled" : "disabled")}");
        }

        private void OnSubtitlesChanged(bool value)
        {
            _currentSettings.subtitlesEnabled = value;

            // Enable/disable subtitle system
            ApplySubtitleSettings(value);
            Debug.Log($"ControlsSettingsPanel: Subtitles {(value ? "enabled" : "disabled")}");
        }

        private void OnUIScaleChanged(float value)
        {
            _currentSettings.uiScale = value;
            UpdateUIScaleText(value);

            // Apply UI scaling to all Canvas components
            ApplyUIScaling(value);
            Debug.Log($"ControlsSettingsPanel: UI scale set to {value:F2}");
        }

        private void OnHighContrastChanged(bool value)
        {
            _currentSettings.highContrastEnabled = value;

            // Apply high contrast theme
            ApplyHighContrastTheme(value);
            Debug.Log($"ControlsSettingsPanel: High contrast {(value ? "enabled" : "disabled")}");
        }

        private void OnReducedMotionChanged(bool value)
        {
            _currentSettings.reducedMotionEnabled = value;

            // Reduce animations and motion effects
            ApplyReducedMotionSettings(value);
            Debug.Log($"ControlsSettingsPanel: Reduced motion {(value ? "enabled" : "disabled")}");
        }

        private void OnScreenReaderChanged(bool value)
        {
            _currentSettings.screenReaderEnabled = value;

            // Enable screen reader compatibility
            ApplyScreenReaderSettings(value);
            Debug.Log($"ControlsSettingsPanel: Screen reader compatibility {(value ? "enabled" : "disabled")}");
        }

        // Advanced Input Callbacks
        private void OnHoldToRunChanged(bool value)
        {
            _currentSettings.holdToRunEnabled = value;
            // TODO: Configure run input behavior
        }

        private void OnAutoAimChanged(bool value)
        {
            _currentSettings.autoAimEnabled = value;
            // TODO: Configure auto-aim system
        }

        private void OnInputBufferChanged(float value)
        {
            _currentSettings.inputBufferTime = value;
            UpdateInputBufferText(value);
            // TODO: Configure input buffer system
        }

        private void OnDoubleClickChanged(bool value)
        {
            _currentSettings.doubleClickEnabled = value;
            // TODO: Configure double-click detection
        }

        private void OnDoubleClickSpeedChanged(float value)
        {
            _currentSettings.doubleClickSpeed = value;
            UpdateDoubleClickSpeedText(value);
            // TODO: Configure double-click timing
        }

        #endregion

        #region UI Text Updates

        private void UpdateMouseSensitivityText(float value)
        {
            if (mouseSensitivityValueText != null)
                mouseSensitivityValueText.text = $"{value:F1}x";
        }

        private void UpdateControllerSensitivityText(float value)
        {
            if (controllerSensitivityValueText != null)
                controllerSensitivityValueText.text = $"{value:F1}x";
        }

        private void UpdateScrollSensitivityText(float value)
        {
            if (scrollSensitivityValueText != null)
                scrollSensitivityValueText.text = $"{value:F1}x";
        }

        private void UpdateMouseAccelerationText(float value)
        {
            if (mouseAccelerationValueText != null)
                mouseAccelerationValueText.text = $"{value:F1}x";
        }

        private void UpdateControllerDeadzoneText(float value)
        {
            if (controllerDeadzoneValueText != null)
                controllerDeadzoneValueText.text = $"{(value * 100):F0}%";
        }

        private void UpdateUIScaleText(float value)
        {
            if (uiScaleValueText != null)
                uiScaleValueText.text = $"{value:F1}x";
        }

        private void UpdateInputBufferText(float value)
        {
            if (inputBufferValueText != null)
                inputBufferValueText.text = $"{value:F0}ms";
        }

        private void UpdateDoubleClickSpeedText(float value)
        {
            if (doubleClickSpeedValueText != null)
                doubleClickSpeedValueText.text = $"{value:F0}ms";
        }

        #endregion

        #region Key Binding

        private void StartRebinding(KeyBindingItem item)
        {
            if (item.inputAction == null) return;

            // Disable the action temporarily
            item.inputAction.Disable();

            // Start interactive rebinding
            var rebindOperation = item.inputAction.PerformInteractiveRebinding()
                .WithControlsExcluding("Mouse")
                .OnMatchWaitForAnother(0.1f)
                .OnComplete(operation => OnRebindComplete(item, operation))
                .OnCancel(operation => OnRebindCancel(item, operation));

            // Update button text to show waiting state
            if (item.bindingText != null)
                item.bindingText.text = "Press any key...";

            rebindOperation.Start();
        }

        private void OnRebindComplete(KeyBindingItem item, InputActionRebindingExtensions.RebindingOperation operation)
        {
            // Update the binding display
            if (item.bindingText != null)
                item.bindingText.text = GetBindingDisplayString(item.inputAction);

            // Store the binding
            _currentSettings.keyBindings[item.actionName] = item.inputAction.bindings[0].effectivePath;

            // Re-enable the action
            item.inputAction.Enable();

            operation.Dispose();
        }

        private void OnRebindCancel(KeyBindingItem item, InputActionRebindingExtensions.RebindingOperation operation)
        {
            // Restore original binding display
            if (item.bindingText != null)
                item.bindingText.text = GetBindingDisplayString(item.inputAction);

            // Re-enable the action
            item.inputAction.Enable();

            operation.Dispose();
        }

        #endregion

        #region Public API

        /// <summary>
        /// Get current controls settings data
        /// </summary>
        public ControlsSettingsData GetCurrentSettings()
        {
            return _currentSettings;
        }

        /// <summary>
        /// Set controls settings data
        /// </summary>
        public void SetSettings(ControlsSettingsData settings)
        {
            _currentSettings = settings;
            RefreshUI();
        }

        /// <summary>
        /// Reset all key bindings to defaults
        /// </summary>
        public void ResetKeyBindings()
        {
            if (inputActions == null) return;

            foreach (var actionMap in inputActions.actionMaps)
            {
                actionMap.RemoveAllBindingOverrides();
            }

            // Update UI
            foreach (var item in _keyBindingItems)
            {
                if (item.bindingText != null)
                    item.bindingText.text = GetBindingDisplayString(item.inputAction);
            }

            _currentSettings.keyBindings.Clear();
        }

        #endregion
    }
}
