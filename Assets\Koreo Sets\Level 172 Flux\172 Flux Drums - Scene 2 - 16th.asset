%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -1223223985, guid: aa81bbec85a3f423193272a7c6154986, type: 3}
  m_Name: 172 Flux Drums - Scene 2 - 16th
  m_EditorClassIdentifier: 
  mEventID: 16th
  mEventList:
  - mStartSample: 0
    mEndSample: 0
  - mStartSample: 7691
    mEndSample: 7691
  - mStartSample: 15383
    mEndSample: 15383
  - mStartSample: 23074
    mEndSample: 23074
  - mStartSample: 30767
    mEndSample: 30767
  - mStartSample: 38458
    mEndSample: 38458
  - mStartSample: 46151
    mEndSample: 46151
  - mStartSample: 53842
    mEndSample: 53842
  - mStartSample: 61534
    mEndSample: 61534
  - mStartSample: 69225
    mEndSample: 69225
  - mStartSample: 76918
    mEndSample: 76918
  - mStartSample: 84609
    mEndSample: 84609
  - mStartSample: 92302
    mEndSample: 92302
  - mStartSample: 99993
    mEndSample: 99993
  - mStartSample: 107686
    mEndSample: 107686
  - mStartSample: 115377
    mEndSample: 115377
  - mStartSample: 123069
    mEndSample: 123069
  - mStartSample: 130760
    mEndSample: 130760
  - mStartSample: 138453
    mEndSample: 138453
  - mStartSample: 146144
    mEndSample: 146144
  - mStartSample: 153837
    mEndSample: 153837
  - mStartSample: 161528
    mEndSample: 161528
  - mStartSample: 169220
    mEndSample: 169220
  - mStartSample: 176911
    mEndSample: 176911
  - mStartSample: 184604
    mEndSample: 184604
  - mStartSample: 192295
    mEndSample: 192295
  - mStartSample: 199988
    mEndSample: 199988
  - mStartSample: 207679
    mEndSample: 207679
  - mStartSample: 215372
    mEndSample: 215372
  - mStartSample: 223063
    mEndSample: 223063
  - mStartSample: 230755
    mEndSample: 230755
  - mStartSample: 238446
    mEndSample: 238446
  - mStartSample: 246139
    mEndSample: 246139
  - mStartSample: 253830
    mEndSample: 253830
  - mStartSample: 261523
    mEndSample: 261523
  - mStartSample: 269214
    mEndSample: 269214
  - mStartSample: 276906
    mEndSample: 276906
  - mStartSample: 284597
    mEndSample: 284597
  - mStartSample: 292290
    mEndSample: 292290
  - mStartSample: 299981
    mEndSample: 299981
  - mStartSample: 307674
    mEndSample: 307674
  - mStartSample: 315365
    mEndSample: 315365
  - mStartSample: 323058
    mEndSample: 323058
  - mStartSample: 330749
    mEndSample: 330749
  - mStartSample: 338441
    mEndSample: 338441
  - mStartSample: 346132
    mEndSample: 346132
  - mStartSample: 353825
    mEndSample: 353825
  - mStartSample: 361516
    mEndSample: 361516
  - mStartSample: 369209
    mEndSample: 369209
  - mStartSample: 376900
    mEndSample: 376900
  - mStartSample: 384593
    mEndSample: 384593
  - mStartSample: 392284
    mEndSample: 392284
  - mStartSample: 399976
    mEndSample: 399976
  - mStartSample: 407667
    mEndSample: 407667
  - mStartSample: 415360
    mEndSample: 415360
  - mStartSample: 423051
    mEndSample: 423051
  - mStartSample: 430744
    mEndSample: 430744
  - mStartSample: 438435
    mEndSample: 438435
  - mStartSample: 446127
    mEndSample: 446127
  - mStartSample: 453818
    mEndSample: 453818
  - mStartSample: 461511
    mEndSample: 461511
  - mStartSample: 469202
    mEndSample: 469202
  - mStartSample: 476895
    mEndSample: 476895
  - mStartSample: 484586
    mEndSample: 484586
  _SerializedPayloadTypes: []
  _AssetPayloads: []
  _AssetPayloadIdxs: 
  _ColorPayloads: []
  _ColorPayloadIdxs: 
  _CurvePayloads: []
  _CurvePayloadIdxs: 
  _FloatPayloads: []
  _FloatPayloadIdxs: 
  _GradientPayloads: []
  _GradientPayloadIdxs: 
  _IntPayloads: []
  _IntPayloadIdxs: 
  _SpectrumPayloads: []
  _SpectrumPayloadIdxs: 
  _TextPayloads: []
  _TextPayloadIdxs: 
