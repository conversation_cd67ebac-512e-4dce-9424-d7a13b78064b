<?xml version="1.0" encoding="UTF-8"?><package><identifier v="Unsaved Package"/><formatVersion v="1.1.0.202302"/><updaterVersion v="1.1.0.202302"/><fileUID v="{4b1bfe15-995e-46b4-bfcf-9cc1e948aac0}"/><versionUID v="0"/><dependencies><dependency><filename v="sbs://pattern_polygon_2.sbs"/><uid v="1488273990"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://slope_blur.sbs"/><uid v="1488274307"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_perlin_noise.sbs"/><uid v="1488274972"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://histogram_scan.sbs"/><uid v="1488275507"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://flood_fill_2.sbs"/><uid v="1323881949"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://bevel.sbs"/><uid v="1488277589"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_crystal_1.sbs"/><uid v="1488309461"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://pattern_tile_sampler.sbs"/><uid v="1488309968"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://hbao_2.sbs"/><uid v="1297162667"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://gradient_linear_1.sbs"/><uid v="1335474237"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_gaussian_noise.sbs"/><uid v="1486875707"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://pattern_shape.sbs"/><uid v="1311189026"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://trapezoid_transform.sbs"/><uid v="1486830014"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://curvature_smooth.sbs"/><uid v="1488387240"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency></dependencies><content><graph><identifier v="Grass_with_rock"/><uid v="1488273663"/><graphOutputs><graphoutput><identifier v="basecolor"/><uid v="1488273758"/><attributes><label v="Base Color"/></attributes><usages><usage><components v="RGBA"/><name v="baseColor"/></usage></usages><group v="Material"/></graphoutput><graphoutput><identifier v="normal"/><uid v="1488273760"/><attributes><label v="Normal"/></attributes><usages><usage><components v="RGBA"/><name v="normal"/></usage></usages><group v="Material"/></graphoutput><graphoutput><identifier v="roughness"/><uid v="1488273762"/><attributes><label v="Roughness"/></attributes><usages><usage><components v="RGBA"/><name v="roughness"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput><graphoutput><identifier v="height"/><uid v="1488273764"/><attributes><label v="Height"/></attributes><usages><usage><components v="RGBA"/><name v="height"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput><graphoutput><identifier v="ambientOcclusion"/><uid v="1488273766"/><attributes><label v="Ambient Occlusion"/></attributes><usages><usage><components v="RGBA"/><name v="ambientOcclusion"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput></graphOutputs><compNodes><compNode><uid v="1488273757"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1488405738"/><connRefOutput v="1488405739"/></connection></connections><GUILayout><gpos v="619.715088 -11.5 1"/></GUILayout><compImplementation><compOutputBridge><output v="1488273758"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1488273759"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1488317078"/><connRefOutput v="1488317079"/></connection></connections><GUILayout><gpos v="619.715088 103.242737 1"/></GUILayout><compImplementation><compOutputBridge><output v="1488273760"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1488273761"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1488409051"/><connRefOutput v="1488409052"/></connection></connections><GUILayout><gpos v="619.715088 213.985535 1"/></GUILayout><compImplementation><compOutputBridge><output v="1488273762"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1488273763"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1488382361"/><connRefOutput v="1488382362"/></connection></connections><GUILayout><gpos v="621.284912 327.872314 1"/></GUILayout><compImplementation><compOutputBridge><output v="1488273764"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1488273765"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1488315263"/><connRefOutput v="1488315264"/></connection></connections><GUILayout><gpos v="619.715088 436.5 1"/></GUILayout><compImplementation><compOutputBridge><output v="1488273766"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1488274198"/><GUILayout><gpos v="-2627.45313 -112 0"/></GUILayout><compOutputs><compOutput><uid v="1488274199"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///polygon_2?dependency=1488273990"/><parameters><parameter><name v="Tiling"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="Scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.439999998"/></paramValue></parameter><parameter><name v="Gradient"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0599999987"/></paramValue></parameter><parameter><name v="Curve"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.349999994"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1488274199"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1488274937"/><connections><connection><identifier v="Source"/><connRef v="1488274198"/><connRefOutput v="1488274199"/></connection><connection><identifier v="Effect"/><connRef v="1488275281"/><connRefOutput v="1488275282"/></connection></connections><GUILayout><gpos v="-2483.36084 -112 0"/></GUILayout><compOutputs><compOutput><uid v="1488274938"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///slope_blur_grayscale_2?dependency=1488274307"/><parameters><parameter><name v="mode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="2.74000001"/></paramValue></parameter><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="32"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1488274938"/><identifier v="Slope_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1488275281"/><GUILayout><gpos v="-2624.71533 16 0"/></GUILayout><compOutputs><compOutput><uid v="1488275282"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///perlin_noise?dependency=1488274972"/><parameters><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueInt32 v="7"/></paramValue></parameter><parameter><name v="disorder"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.469999999"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1488275282"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1488275558"/><connections><connection><identifier v="Input_1"/><connRef v="1488274937"/><connRefOutput v="1488274938"/></connection></connections><GUILayout><gpos v="-2355.36084 -112 0"/></GUILayout><compOutputs><compOutput><uid v="1488275559"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1488275507"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0199999996"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1488275559"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1488277063"/><connections><connection><identifier v="mask"/><connRef v="1488275558"/><connRefOutput v="1488275559"/></connection></connections><GUILayout><gpos v="-2195.36084 -240 0"/></GUILayout><compOutputs><compOutput><uid v="1488277064"/><comptype v="1"/></compOutput><compOutput><uid v="1488277065"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill?dependency=1323881949"/><parameters/><outputBridgings><outputBridging><uid v="1488277064"/><identifier v="output"/></outputBridging><outputBridging><uid v="1488277065"/><identifier v="referenceMap"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1488277273"/><connections><connection><identifier v="input"/><connRef v="1488277063"/><connRefOutput v="1488277064"/></connection></connections><GUILayout><gpos v="-2067.36084 -240 0"/></GUILayout><compOutputs><compOutput><uid v="1488277274"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0468611121"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1488277274"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1488277312"/><connections><connection><identifier v="source"/><connRef v="1488277273"/><connRefOutput v="1488277274"/></connection><connection><identifier v="mask"/><connRef v="1488275558"/><connRefOutput v="1488275559"/></connection></connections><GUILayout><gpos v="-1939.36096 -240 0"/></GUILayout><compOutputs><compOutput><uid v="1488277313"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="14.1399994"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488277405"/><connections><connection><identifier v="input1"/><connRef v="1488277312"/></connection></connections><GUILayout><gpos v="-1811.36096 -240 0"/></GUILayout><compOutputs><compOutput><uid v="1488277406"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.475765318 0.475765318 0.475765318 0.5"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488277418"/><connections><connection><identifier v="input1"/><connRef v="1488277419"/></connection></connections><GUILayout><gpos v="-1811.36096 -354.109924 0"/></GUILayout><compOutputs><compOutput><uid v="1488277406"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.463010192 0.463010192 0.463010192 0.5"/></paramValue></parameter><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.218112245 0.218112245 0.218112245 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488277419"/><connections><connection><identifier v="source"/><connRef v="1488277420"/><connRefOutput v="1488277274"/></connection><connection><identifier v="mask"/><connRef v="1488275558"/><connRefOutput v="1488275559"/></connection></connections><GUILayout><gpos v="-1939.36096 -354.109924 0"/></GUILayout><compOutputs><compOutput><uid v="1488277313"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="14.1399994"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488277420"/><connections><connection><identifier v="input"/><connRef v="1488277063"/><connRefOutput v="1488277064"/></connection></connections><GUILayout><gpos v="-2067.36084 -354.109924 0"/></GUILayout><compOutputs><compOutput><uid v="1488277274"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="angle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.31936112"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1488277274"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1488277430"/><connections><connection><identifier v="input1"/><connRef v="1488277432"/></connection></connections><GUILayout><gpos v="-1811.36096 -464 0"/></GUILayout><compOutputs><compOutput><uid v="1488277406"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.24872449 0.24872449 0.24872449 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488277431"/><connections><connection><identifier v="input"/><connRef v="1488277063"/><connRefOutput v="1488277064"/></connection></connections><GUILayout><gpos v="-2067.36084 -464 0"/></GUILayout><compOutputs><compOutput><uid v="1488277274"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.5"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1488277274"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1488277432"/><connections><connection><identifier v="source"/><connRef v="1488277431"/><connRefOutput v="1488277274"/></connection><connection><identifier v="mask"/><connRef v="1488275558"/><connRefOutput v="1488275559"/></connection></connections><GUILayout><gpos v="-1939.36096 -464 0"/></GUILayout><compOutputs><compOutput><uid v="1488277313"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="distance"/><parameters><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="14.1399994"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488278826"/><connections><connection><identifier v="input"/><connRef v="1488275558"/><connRefOutput v="1488275559"/></connection></connections><GUILayout><gpos v="-1811.36096 -65.7439575 0"/></GUILayout><compOutputs><compOutput><uid v="1488278827"/><comptype v="2"/></compOutput><compOutput><uid v="1488278828"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///bevel?dependency=1488277589"/><parameters><parameter><name v="distance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.099999994"/></paramValue></parameter><parameter><name v="smoothing"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="3.89999986"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1488278827"/><identifier v="height"/></outputBridging><outputBridging><uid v="1488278828"/><identifier v="normal"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1488294018"/><connections><connection><identifier v="destination"/><connRef v="1488278826"/><connRefOutput v="1488278827"/></connection><connection><identifier v="source"/><connRef v="1488277405"/><connRefOutput v="1488277406"/></connection></connections><GUILayout><gpos v="-1665.77051 -65.7439575 0"/></GUILayout><compOutputs><compOutput><uid v="1488294019"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488296187"/><connections><connection><identifier v="destination"/><connRef v="1488294018"/></connection><connection><identifier v="source"/><connRef v="1488277418"/><connRefOutput v="1488277406"/></connection></connections><GUILayout><gpos v="-1523.36096 -65.7439575 0"/></GUILayout><compOutputs><compOutput><uid v="1488296188"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488296241"/><connections><connection><identifier v="destination"/><connRef v="1488296187"/></connection><connection><identifier v="source"/><connRef v="1488277430"/><connRefOutput v="1488277406"/></connection></connections><GUILayout><gpos v="-1395.36096 -65.7439575 0"/></GUILayout><compOutputs><compOutput><uid v="1488296242"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488308838"/><connections><connection><identifier v="input1"/><connRef v="1488309429"/><connRefOutput v="1488309430"/></connection><connection><identifier v="inputintensity"/><connRef v="1488309429"/><connRefOutput v="1488309430"/></connection></connections><GUILayout><gpos v="-1139.36096 -65.7439575 0"/></GUILayout><compOutputs><compOutput><uid v="1488308839"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="directionalwarp"/><parameters><parameter><name v="warpangle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="2.48711109"/></paramValue></parameter><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="4.92999983"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488309429"/><connections><connection><identifier v="input1"/><connRef v="1488296241"/><connRefOutput v="1488296242"/></connection><connection><identifier v="inputintensity"/><connRef v="1488309687"/><connRefOutput v="1488309688"/></connection></connections><GUILayout><gpos v="-1267.36096 -65.7439575 0"/></GUILayout><compOutputs><compOutput><uid v="1488309430"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="directionalwarp"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1488309687"/><GUILayout><gpos v="-1395.36096 62.2560425 0"/></GUILayout><compOutputs><compOutput><uid v="1488309688"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///crystal_1?dependency=1488309461"/><parameters><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueInt32 v="8"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1488309688"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1488310730"/><connections><connection><identifier v="pattern_input"/><connRef v="1488310742"/><connRefOutput v="1488310743"/></connection><connection><identifier v="pattern_input_2"/><connRef v="1488310878"/><connRefOutput v="1488310879"/></connection><connection><identifier v="pattern_input_3"/><connRef v="1488310990"/><connRefOutput v="1488310991"/></connection><connection><identifier v="pattern_input_4"/><connRef v="1488311093"/><connRefOutput v="1488311094"/></connection></connections><GUILayout><gpos v="-765.891052 112 0"/></GUILayout><compOutputs><compOutput><uid v="1488310731"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1488309968"/><parameters><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="6.51999998"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.669999957"/></paramValue></parameter><parameter><name v="scale_vector_map_multiplier"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.620000005"/></paramValue></parameter><parameter><name v="mask_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.969999969"/></paramValue></parameter><parameter><name v="mask_map_sampling_technique"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="15"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="15"/></paramValue></parameter><parameter><name v="vector_map_displacement"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.829999983"/></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="69.3899994"/></paramValue></parameter><parameter><name v="pattern_input_number"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter><parameter><name v="pattern_rotation_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="pattern_symmetry_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.569999993"/></paramValue></parameter><parameter><name v="size_mode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.75999999"/></paramValue></parameter><parameter><name v="global_offset"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.550000012 8.35999966"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1488310731"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1488310742"/><connections><connection><identifier v="input1"/><connRef v="1488308838"/></connection></connections><GUILayout><gpos v="-1139.36096 48 0"/></GUILayout><compOutputs><compOutput><uid v="1488310743"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="transformation"/><parameters><parameter><name v="matrix22"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.513199985 0 0 0.513199985"/></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.25 0.25"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488310878"/><connections><connection><identifier v="input1"/><connRef v="1488308838"/></connection></connections><GUILayout><gpos v="-1139.36096 157.931915 0"/></GUILayout><compOutputs><compOutput><uid v="1488310879"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="transformation"/><parameters><parameter><name v="matrix22"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.497799993 0 0 0.497799993"/></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="-0.25 -0.25"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488310990"/><connections><connection><identifier v="input1"/><connRef v="1488308838"/></connection></connections><GUILayout><gpos v="-1139.36096 271.04184 0"/></GUILayout><compOutputs><compOutput><uid v="1488310991"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="transformation"/><parameters><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.25 -0.25"/></paramValue></parameter><parameter><name v="matrix22"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.477899998 0 0 0.477899998"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488311093"/><connections><connection><identifier v="input1"/><connRef v="1488308838"/></connection></connections><GUILayout><gpos v="-1139.36096 382.828613 0"/></GUILayout><compOutputs><compOutput><uid v="1488311094"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="transformation"/><parameters><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="-0.266499996 0.266499996"/></paramValue></parameter><parameter><name v="matrix22"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.481299996 0 0 0.481299996"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488315263"/><connections><connection><identifier v="input"/><connRef v="1488382361"/><connRefOutput v="1488382362"/></connection></connections><GUILayout><gpos v="452.036072 437.981964 0"/></GUILayout><compOutputs><compOutput><uid v="1488315264"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///hbao?dependency=1297162667"/><parameters><parameter><name v="height_depth"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.159999996"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1488315264"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1488317078"/><connections><connection><identifier v="input1"/><connRef v="1488382361"/><connRefOutput v="1488382362"/></connection></connections><GUILayout><gpos v="-176 176 0"/></GUILayout><compOutputs><compOutput><uid v="1488317079"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="normal"/><parameters><parameter><name v="input2alpha"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="10"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488379265"/><GUILayout><gpos v="-1327.4895 773.333313 0"/></GUILayout><compOutputs><compOutput><uid v="1482591041"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///gradient_linear_1?dependency=1335474237"/><parameters><parameter><name v="rotation"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1482591041"/><identifier v="Simple_Gradient"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1488379266"/><GUILayout><gpos v="-857.723145 784.577087 0"/></GUILayout><compOutputs><compOutput><uid v="1482609943"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///gaussian_noise?dependency=1486875707"/><parameters><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueInt32 v="9"/></paramValue></parameter><parameter><name v="disorder"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.099999994"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1482609943"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1488379267"/><connections><connection><identifier v="destination"/><connRef v="1488379269"/><connRefOutput v="1482590705"/></connection><connection><identifier v="source"/><connRef v="1488379271"/><connRefOutput v="1482590975"/></connection></connections><GUILayout><gpos v="-1058.16724 655.484863 0"/></GUILayout><compOutputs><compOutput><uid v="1482591088"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="maskrectangle"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0 1 0 0"/></paramValue></parameter><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488379268"/><connections><connection><identifier v="pattern_input"/><connRef v="1488379275"/><connRefOutput v="1482608790"/></connection><connection><identifier v="rotation_map_input"/><connRef v="1488379266"/><connRefOutput v="1482609943"/></connection><connection><identifier v="mask_map_input"/><connRef v="1488379416"/><connRefOutput v="1488379417"/></connection></connections><GUILayout><gpos v="-705.558105 773.333313 0"/></GUILayout><compOutputs><compOutput><uid v="1482609709"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1488309968"/><parameters><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="149"/></paramValue></parameter><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="size"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="8.14999962 5"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="6.46000004"/></paramValue></parameter><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="149"/></paramValue></parameter><parameter><name v="size_random"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.329999983 0.319999993"/></paramValue></parameter><parameter><name v="rotation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.920000017"/></paramValue></parameter><parameter><name v="non_square_expansion"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="pattern_input_number"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.799999952"/></paramValue></parameter><parameter><name v="rotation_map_multiplier"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.569999993"/></paramValue></parameter><parameter><name v="rotation_vector_map_multiplier"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.340000004"/></paramValue></parameter><parameter><name v="color_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="color_parametrization_mode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="scale_map_multiplier"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0599999987"/></paramValue></parameter><parameter><name v="scale_vector_map_multiplier"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.519999981"/></paramValue></parameter><parameter><name v="mask_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.449999988"/></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="10"/></paramValue></parameter><parameter><name v="vector_map_displacement"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.969999969"/></paramValue></parameter><parameter><name v="mask_map_invert"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="mask_map_threshold"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1482609709"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1488379269"/><connections><connection><identifier v="source"/><connRef v="1488379277"/><connRefOutput v="1482590232"/></connection></connections><GUILayout><gpos v="-1200 656 0"/></GUILayout><compOutputs><compOutput><uid v="1482590705"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="maskrectangle"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.199999988 0.779999971 0 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488379270"/><GUILayout><gpos v="-1601.81665 655.921875 0"/></GUILayout><compOutputs><compOutput><uid v="1482576333"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///shape?dependency=1311189026"/><parameters><parameter><name v="Pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="16"/></paramValue></parameter><parameter><name v="Size"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.74000001"/></paramValue></parameter><parameter><name v="Pattern_Specific"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.829999983"/></paramValue></parameter><parameter><name v="Size_xy"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.199999988 1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1482576333"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1488379271"/><connections><connection><identifier v="Input_1"/><connRef v="1488379265"/><connRefOutput v="1482591041"/></connection></connections><GUILayout><gpos v="-1199.4895 773.333313 0"/></GUILayout><compOutputs><compOutput><uid v="1482590975"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1488275507"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.610000014"/></paramValue></parameter><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0700000003"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1482590975"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1488379275"/><connections><connection><identifier v="input1"/><connRef v="1488379267"/></connection></connections><GUILayout><gpos v="-930.166992 655.484863 0"/></GUILayout><compOutputs><compOutput><uid v="1482608790"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blur"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.91999996"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488379277"/><connections><connection><identifier v="input1"/><connRef v="1488379279"/><connRefOutput v="1482591428"/></connection></connections><GUILayout><gpos v="-1347.53296 655.484741 0"/></GUILayout><compOutputs><compOutput><uid v="1482590232"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.149234697 0.149234697 0.149234697 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488379278"/><connections><connection><identifier v="input1"/><connRef v="1488406032"/><connRefOutput v="1488406033"/></connection></connections><GUILayout><gpos v="-176 -208 0"/></GUILayout><compOutputs><compOutput><uid v="1482625016"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1482625039"/><paramsArrayCells><paramsArrayCell><uid v="1482625052"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.278260857 0.210313439 0.108392313 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1482625053"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.875527442"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.464808404 0.843478262 0.255005091 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1482625213"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.0354818478"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.168359771 0.352173924 0.114661269 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1482625257"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.386075944"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.482503086 0.586956501 0.211577341 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1482625330"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.630801678"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.616117656 0.786956549 0.2699444 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1487693664"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.0126582272"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.0372851379 0.256521732 0.0391915254 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1488379279"/><connections><connection><identifier v="input"/><connRef v="1488379270"/><connRefOutput v="1482576333"/></connection></connections><GUILayout><gpos v="-1476.33081 655.484741 0"/></GUILayout><compOutputs><compOutput><uid v="1482591428"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///trapezoid_transform_grayscale?dependency=1486830014"/><parameters><parameter><name v="top_stretch"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.620000005"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1482591428"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1488379355"/><connections><connection><identifier v="opacity"/><connRef v="1488379416"/><connRefOutput v="1488379417"/></connection><connection><identifier v="destination"/><connRef v="1488379278"/><connRefOutput v="1482625016"/></connection><connection><identifier v="source"/><connRef v="1488387083"/><connRefOutput v="1488387084"/></connection></connections><GUILayout><gpos v="16 -112 0"/></GUILayout><compOutputs><compOutput><uid v="1488379356"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1488379416"/><connections><connection><identifier v="input1"/><connRef v="1488419366"/><connRefOutput v="1488419367"/></connection></connections><GUILayout><gpos v="-176 48 501"/></GUILayout><compOutputs><compOutput><uid v="1488379417"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.108418368 0.108418368 0.108418368 0.5"/></paramValue></parameter><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.0854591802 0.0854591802 0.0854591802 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488382361"/><connections><connection><identifier v="destination"/><connRef v="1488383338"/><connRefOutput v="1488383339"/></connection><connection><identifier v="source"/><connRef v="1488419366"/><connRefOutput v="1488419367"/></connection></connections><GUILayout><gpos v="-257.504761 336 501"/></GUILayout><compOutputs><compOutput><uid v="1488382362"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="5"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488383338"/><connections><connection><identifier v="input1"/><connRef v="1488379268"/><connRefOutput v="1482609709"/></connection></connections><GUILayout><gpos v="-368 496 501"/></GUILayout><compOutputs><compOutput><uid v="1488383339"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelouthigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.378826529 0.378826529 0.378826529 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488385168"/><connections><connection><identifier v="input1"/><connRef v="1488310730"/><connRefOutput v="1488310731"/></connection></connections><GUILayout><gpos v="-592 144 501"/></GUILayout><compOutputs><compOutput><uid v="1488385169"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.460459173 0.460459173 0.460459173 0.5"/></paramValue></parameter><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.911989808 0.911989808 0.911989808 1"/></paramValue></parameter><parameter><name v="levelinlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.0599489808 0.0599489808 0.0599489808 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488387083"/><connections><connection><identifier v="input1"/><connRef v="1488419366"/><connRefOutput v="1488419367"/></connection></connections><GUILayout><gpos v="-176 -80 501"/></GUILayout><compOutputs><compOutput><uid v="1488387084"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1488387111"/><paramsArrayCells><paramsArrayCell><uid v="1488400175"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.0208543967 0.0652173907 0.0617455058 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1488400176"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.685653985"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.469160736 0.489661038 0.504347801 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1488388014"/><connections><connection><identifier v="input"/><connRef v="1488317078"/></connection></connections><GUILayout><gpos v="16 80 0"/></GUILayout><compOutputs><compOutput><uid v="1488388015"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///curvature_smooth?dependency=1488387240"/><parameters/><outputBridgings><outputBridging><uid v="1488388015"/><identifier v="height"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1488388481"/><connections><connection><identifier v="input1"/><connRef v="1488388014"/><connRefOutput v="1488388015"/></connection></connections><GUILayout><gpos v="126.178581 80 49"/></GUILayout><compOutputs><compOutput><uid v="1488388482"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters><parameter><name v="colorswitch"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter></parameters><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1488388487"/><paramsArrayCells><paramsArrayCell><uid v="1488388503"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.627118647"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0 0 0 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1488388504"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.864406765"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="1 1 1 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1488388679"/><connections><connection><identifier v="opacity"/><connRef v="1488388481"/><connRefOutput v="1488388482"/></connection><connection><identifier v="source"/><connRef v="1488389172"/><connRefOutput v="1488387084"/></connection><connection><identifier v="destination"/><connRef v="1488379355"/><connRefOutput v="1488379356"/></connection></connections><GUILayout><gpos v="304 -48 0"/></GUILayout><compOutputs><compOutput><uid v="1488388680"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.319999993"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488389172"/><GUILayout><gpos v="144 -112 0"/></GUILayout><compOutputs><compOutput><uid v="1488387084"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1488387111"/><paramsArrayCells><paramsArrayCell><uid v="1488387120"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.991304338 0.991304338 0.991304338 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1488387121"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.997640014"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.886956513 0.868930399 0.856016159 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1488405738"/><connections><connection><identifier v="destination"/><connRef v="1488388679"/><connRefOutput v="1488388680"/></connection><connection><identifier v="source"/><connRef v="1488406626"/><connRefOutput v="1488406627"/></connection><connection><identifier v="opacity"/><connRef v="1488407351"/><connRefOutput v="1488407352"/></connection></connections><GUILayout><gpos v="450.349243 -240 0"/></GUILayout><compOutputs><compOutput><uid v="1488405739"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.399999976"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488406032"/><connections><connection><identifier v="input1"/><connRef v="1488379268"/><connRefOutput v="1482609709"/></connection></connections><GUILayout><gpos v="-384.975189 -208 0"/></GUILayout><compOutputs><compOutput><uid v="1488406033"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters><parameter><name v="colorswitch"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter></parameters><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1488406204"/><paramsArrayCells><paramsArrayCell><uid v="1488406209"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0 0 0 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1488406210"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="1 1 1 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1488406614"/><GUILayout><gpos v="32.4672546 -368 502"/></GUILayout><compOutputs><compOutput><uid v="1482609943"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///gaussian_noise?dependency=1486875707"/><parameters><parameter><name v="disorder"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.329999983"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueInt32 v="54"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1482609943"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1488406626"/><connections><connection><identifier v="input1"/><connRef v="1488406614"/><connRefOutput v="1482609943"/></connection></connections><GUILayout><gpos v="176 -368 0"/></GUILayout><compOutputs><compOutput><uid v="1488406627"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1488406663"/><paramsArrayCells><paramsArrayCell><uid v="1488408185"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.234866828"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0 0 0 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1488408186"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.33414042"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.0711808652 0.282608688 0.0345045477 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1488407237"/><connections><connection><identifier v="source"/><connRef v="1488383338"/><connRefOutput v="1488383339"/></connection><connection><identifier v="destination"/><connRef v="1488419366"/><connRefOutput v="1488419367"/></connection></connections><GUILayout><gpos v="-528 -432 0"/></GUILayout><compOutputs><compOutput><uid v="1488407238"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="5"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488407322"/><connections><connection><identifier v="input1"/><connRef v="1488407237"/></connection></connections><GUILayout><gpos v="-368 -432 0"/></GUILayout><compOutputs><compOutput><uid v="1488407323"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="1 1 1 0"/></paramValue></parameter><parameter><name v="levelouthigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0 0 0 1"/></paramValue></parameter><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.133928567 0.133928567 0.133928567 0.5"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488407351"/><connections><connection><identifier v="input1"/><connRef v="1488407322"/></connection></connections><GUILayout><gpos v="-208 -432 0"/></GUILayout><compOutputs><compOutput><uid v="1488407352"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinmid"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.490018189 0.490018189 0.490018189 0.5"/></paramValue></parameter><parameter><name v="levelinlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.733418345 0.733418345 0.733418345 0"/></paramValue></parameter><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.779336751 0.779336751 0.779336751 1"/></paramValue></parameter><parameter><name v="levelouthigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.960459173 0.960459173 0.960459173 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488409051"/><connections><connection><identifier v="input1"/><connRef v="1488382361"/><connRefOutput v="1488382362"/></connection></connections><GUILayout><gpos v="432 208 0"/></GUILayout><compOutputs><compOutput><uid v="1488409052"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="leveloutlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.442602038 0.442602038 0.442602038 0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1488419366"/><connections><connection><identifier v="input1"/><connRef v="1488385168"/><connRefOutput v="1488385169"/></connection></connections><GUILayout><gpos v="-464 144 0"/></GUILayout><compOutputs><compOutput><uid v="1488419367"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="transformation"/><parameters><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.0837000012 0.0886000022"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode></compNodes><baseParameters/><options><option><name v="defaultParentSize"/><value v="11x11"/></option><option><name v="export/fromGraph/autoExport"/><value v="false"/></option><option><name v="export/fromGraph/destination"/><value v="C:/Users/<USER>/Desktop"/></option><option><name v="export/fromGraph/extension"/><value v="png"/></option><option><name v="export/fromGraph/outputs/ambientOcclusion"/><value v="true"/></option><option><name v="export/fromGraph/outputs/basecolor"/><value v="true"/></option><option><name v="export/fromGraph/outputs/height"/><value v="true"/></option><option><name v="export/fromGraph/outputs/normal"/><value v="true"/></option><option><name v="export/fromGraph/outputs/roughness"/><value v="true"/></option><option><name v="export/fromGraph/outputsColorspace/ambientOcclusion"/><value v="Raw"/></option><option><name v="export/fromGraph/outputsColorspace/basecolor"/><value v="sRGB"/></option><option><name v="export/fromGraph/outputsColorspace/height"/><value v="Raw"/></option><option><name v="export/fromGraph/outputsColorspace/normal"/><value v="Raw"/></option><option><name v="export/fromGraph/outputsColorspace/roughness"/><value v="Raw"/></option><option><name v="export/fromGraph/pattern"/><value v="$(graph)_$(identifier)"/></option></options><root><rootOutputs><rootOutput><output v="1488273758"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1488273760"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1488273762"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1488273764"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1488273766"/><format v="0"/><usertag v=""/></rootOutput></rootOutputs></root></graph></content></package>
