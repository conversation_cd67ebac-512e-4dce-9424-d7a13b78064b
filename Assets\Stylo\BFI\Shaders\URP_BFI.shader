Shader "Hidden/Universal Render Pipeline/BFI"
{
    HLSLINCLUDE

    #define HLSL 1
    #pragma target 3.0
    #pragma editor_sync_compilation

    #pragma multi_compile_local _ BFI_MOTION_ADAPTIVE
    #pragma multi_compile_local _ BFI_FLICKER_REDUCTION
    #pragma multi_compile_local _ BFI_DEBUG_VISUALIZATION
    #pragma multi_compile_local BFI_MODE_OFF BFI_MODE_2X BFI_MODE_3X BFI_MODE_4X
    
    #include "Packages/com.unity.render-pipelines.core/ShaderLibrary/Common.hlsl"
    #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"

    TEXTURE2D_X(_Input);
    TEXTURE2D_X(_MotionVectorTexture);
    TEXTURE2D_X(_CameraDepthTexture);
    // sampler_LinearClamp and sampler_PointClamp are already defined in Unity's Core library

    CBUFFER_START(BFIUniforms)
    int _BFIMode;
    float _Intensity;
    float _BrightnessCompensation;
    float _ManualBrightness;
    float _GammaCorrection;
    float _BlackFrameDuration;
    float _PhaseOffset;
    int _SyncToRefreshRate;
    int _FlickerReductionMode;
    float _TemporalSmoothing;
    float _EdgePreservation;
    int _MotionAdaptive;
    float _MotionSensitivity;
    float _MotionThreshold;
    float _AdaptationSpeed;
    float _DitheringStrength;
    float _SubpixelPrecision;
    int _DebugVisualization;
    float _CurrentTime;
    float _DeltaTime;
    int _FrameCount;
    CBUFFER_END

    struct Attributes
    {
        float4 positionOS : POSITION;
        float2 uv : TEXCOORD0;
    };

    struct Varyings
    {
        float4 positionCS : SV_POSITION;
        float2 uv : TEXCOORD0;
    };

    Varyings vert(Attributes input)
    {
        Varyings output;
        output.positionCS = TransformObjectToHClip(input.positionOS.xyz);
        output.uv = input.uv;
        return output;
    }

    // Utility functions for BFI calculations
    float GetFrameMultiplier()
    {
        #if defined(BFI_MODE_2X)
            return 2.0;
        #elif defined(BFI_MODE_3X)
            return 3.0;
        #elif defined(BFI_MODE_4X)
            return 4.0;
        #else
            return 1.0;
        #endif
    }

    float CalculateBFIPhase(float time, float frameMultiplier)
    {
        float cycleTime = time * frameMultiplier;
        return frac(cycleTime + _PhaseOffset);
    }

    bool ShouldRenderBlackFrame(float phase, float frameMultiplier)
    {
        float blackFrameThreshold = _BlackFrameDuration / frameMultiplier;
        return phase < blackFrameThreshold;
    }

    float3 ApplyGammaCorrection(float3 color, float gamma)
    {
        return pow(abs(color), 1.0 / gamma);
    }

    float3 ApplyBrightnessCompensation(float3 color, float compensation, float manual)
    {
        // Apply brightness compensation with gamma correction
        float3 compensated = color * compensation * manual;
        return ApplyGammaCorrection(compensated, _GammaCorrection);
    }

    float CalculateMotionMagnitude(float2 uv)
    {
        #ifdef BFI_MOTION_ADAPTIVE
            float2 motion = SAMPLE_TEXTURE2D_X(_MotionVectorTexture, sampler_LinearClamp, uv).xy;
            return length(motion);
        #else
            return 0.0;
        #endif
    }

    float CalculateMotionAdaptiveIntensity(float2 uv)
    {
        #ifdef BFI_MOTION_ADAPTIVE
            float motionMagnitude = CalculateMotionMagnitude(uv);
            float motionFactor = saturate((motionMagnitude - _MotionThreshold) * _MotionSensitivity);
            return lerp(0.5, 1.0, motionFactor);
        #else
            return 1.0;
        #endif
    }

    float3 ApplyFlickerReduction(float3 color, float2 uv, float intensity)
    {
        #ifdef BFI_FLICKER_REDUCTION
            // Temporal smoothing based on flicker reduction mode
            float smoothingFactor = _TemporalSmoothing;
            
            // Edge-preserving smoothing
            float depth = SAMPLE_TEXTURE2D_X(_CameraDepthTexture, sampler_PointClamp, uv).r;
            float edgeFactor = saturate(1.0 - _EdgePreservation * abs(ddx(depth)) * abs(ddy(depth)));
            
            smoothingFactor *= edgeFactor;
            
            // Apply smoothing based on flicker reduction mode
            if (_FlickerReductionMode == 1) // Low
                smoothingFactor *= 0.3;
            else if (_FlickerReductionMode == 2) // Medium
                smoothingFactor *= 0.6;
            else if (_FlickerReductionMode == 3) // High
                smoothingFactor *= 1.0;
            
            return lerp(color, color * intensity, 1.0 - smoothingFactor);
        #else
            return color;
        #endif
    }

    float3 ApplyDithering(float3 color, float2 uv)
    {
        if (_DitheringStrength > 0.0)
        {
            // Simple dithering pattern to reduce banding
            float2 ditherCoord = uv * _ScreenParams.xy;
            float dither = frac(sin(dot(ditherCoord, float2(12.9898, 78.233))) * 43758.5453);
            dither = (dither - 0.5) * _DitheringStrength * 0.01;
            return color + dither;
        }
        return color;
    }

    float3 CreateDebugVisualization(float2 uv, float phase, float frameMultiplier, float motionIntensity)
    {
        #ifdef BFI_DEBUG_VISUALIZATION
            if (_DebugVisualization)
            {
                float3 debugColor = float3(0, 0, 0);
                
                // Show BFI phase as color gradient
                debugColor.r = phase;
                
                // Show frame multiplier as green intensity
                debugColor.g = frameMultiplier / 4.0;
                
                // Show motion intensity as blue
                debugColor.b = motionIntensity;
                
                // Add timing bars
                float barHeight = 0.05;
                if (uv.y < barHeight)
                {
                    float barPhase = frac(uv.x * frameMultiplier);
                    bool isBlackFrame = ShouldRenderBlackFrame(barPhase, frameMultiplier);
                    debugColor = isBlackFrame ? float3(0, 0, 0) : float3(1, 1, 1);
                }
                
                return debugColor;
            }
        #endif
        return float3(0, 0, 0);
    }

    // Black Frame Pass
    float4 fragBlackFrame(Varyings input) : SV_Target
    {
        float2 uv = input.uv;
        
        #ifdef BFI_DEBUG_VISUALIZATION
            if (_DebugVisualization)
            {
                float frameMultiplier = GetFrameMultiplier();
                float phase = CalculateBFIPhase(_CurrentTime, frameMultiplier);
                float motionIntensity = CalculateMotionAdaptiveIntensity(uv);
                return float4(CreateDebugVisualization(uv, phase, frameMultiplier, motionIntensity), 1.0);
            }
        #endif
        
        // Pure black frame with optional subtle dithering
        float3 blackColor = float3(0, 0, 0);
        blackColor = ApplyDithering(blackColor, uv);
        
        return float4(blackColor, 1.0);
    }

    // Brightness Compensation Pass
    float4 fragBrightnessCompensation(Varyings input) : SV_Target
    {
        float2 uv = input.uv;
        float3 color = SAMPLE_TEXTURE2D_X(_Input, sampler_LinearClamp, uv).rgb;

        // Calculate motion-adaptive intensity if enabled
        float motionIntensity = CalculateMotionAdaptiveIntensity(uv);

        // Apply brightness compensation
        float totalCompensation = _BrightnessCompensation * motionIntensity;
        color = ApplyBrightnessCompensation(color, totalCompensation, _ManualBrightness);

        // Apply flicker reduction
        color = ApplyFlickerReduction(color, uv, _Intensity);

        // Apply dithering
        color = ApplyDithering(color, uv);

        // Debug visualization overlay
        #ifdef BFI_DEBUG_VISUALIZATION
            if (_DebugVisualization)
            {
                float frameMultiplier = GetFrameMultiplier();
                float phase = CalculateBFIPhase(_CurrentTime, frameMultiplier);
                float3 debugVis = CreateDebugVisualization(uv, phase, frameMultiplier, motionIntensity);
                color = lerp(color, debugVis, 0.5);
            }
        #endif

        return float4(color, 1.0);
    }

    // Simple Copy Pass
    float4 fragCopy(Varyings input) : SV_Target
    {
        float2 uv = input.uv;
        return SAMPLE_TEXTURE2D_X(_Input, sampler_LinearClamp, uv);
    }

    ENDHLSL

    SubShader
    {
        Tags { "RenderType" = "Opaque" "RenderPipeline" = "UniversalPipeline" }
        LOD 100
        ZTest Always ZWrite Off Cull Off

        // Pass 0: Black Frame
        Pass
        {
            Name "BFI Black Frame"

            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment fragBlackFrame
            ENDHLSL
        }

        // Pass 1: Brightness Compensation
        Pass
        {
            Name "BFI Brightness Compensation"

            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment fragBrightnessCompensation
            ENDHLSL
        }

        // Pass 2: Copy
        Pass
        {
            Name "BFI Copy"

            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment fragCopy
            ENDHLSL
        }
    }

    Fallback Off
}
