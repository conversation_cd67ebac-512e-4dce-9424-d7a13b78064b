# Flux Datamosh Visual Parity Fixes

## Summary
This document outlines the critical fixes applied to achieve visual parity between Flux and JPG Bitcrunch datamosh effects.

## Issues Identified

### 1. **CRITICAL: Zero Datamosh Parameters in Presets**
- **Problem**: The "JPG Bitcrunch Exact" preset had `reprojectBaseNoise: 0` and `reprojectLengthInfluence: 0`, completely disabling datamoshing
- **Root Cause**: Preset copied values from JPG Bitcrunch demo scene which had datamoshing disabled
- **Fix**: Updated preset values to working parameters that enable visible datamosh effects

### 2. **CRITICAL: Incorrect Block ID Calculation in Pure Datamosh Mode**
- **Problem**: Flux was using smoothness-affected block ID calculation even in Pure Datamosh Mode
- **JPG Bitcrunch**: `int2 blockID = floor(uv / (_Downscaled_TexelSize.xy * BLOCK_SIZE));`
- **Flux (Before)**: `int2 blockID = floor(lerp(snappedUV, pixelUV, smoothnessFactor * 0.5) / (_Downscaled_TexelSize.xy * BLOCK_SIZE));`
- **Fix**: Use simple block ID calculation in Pure Datamosh Mode, matching JPG Bitcrunch exactly

### 3. **CRITICAL: Inconsistent Motion Vector Usage**
- **Problem**: Flux was using `enhancedMotionVector` for previous frame sampling but original `motionVector` for reprojection calculation
- **Fix**: In Pure Datamosh Mode, consistently use original `motionVector` for both operations, matching JPG Bitcrunch behavior

### 4. **Motion Vector Enhancement in Pure Datamosh Mode**
- **Problem**: Even in Pure Datamosh Mode, motion vectors were being processed through enhancement logic
- **Fix**: Preserve original motion vector in Pure Datamosh Mode, only apply enhancements in Enhanced Mode

## Fixes Applied

### Shader Changes (Assets\Stylo\Flux\Shaders\Shared.cginc)

#### 1. Block ID and Motion Sampling (Lines 229-243)
```hlsl
#ifdef PURE_DATAMOSH_MODE
    // Pure Datamosh Mode: EXACT JPG Bitcrunch behavior - simple block-based sampling only
    float2 motionSampleUV = snappedUV;
    int2 blockID = floor(uv / (_Downscaled_TexelSize.xy * BLOCK_SIZE));
#else
    // Enhanced Mode: Blend between block-based and per-pixel sampling
    float2 pixelUV = uv;
    float smoothnessFactor = _TrailSmoothness;
    float2 motionSampleUV = lerp(snappedUV, pixelUV, smoothnessFactor);
    int2 blockID = floor(lerp(snappedUV, pixelUV, smoothnessFactor * 0.5) / (_Downscaled_TexelSize.xy * BLOCK_SIZE));
#endif
```

#### 2. Motion Vector Preservation (Lines 362-367)
```hlsl
#ifndef PURE_DATAMOSH_MODE
    // Enhanced Mode: Use enhanced motion vector for additional features
    motionVector = enhancedMotionVector;
#endif
// Pure Datamosh Mode: Keep original motion vector unchanged
```

#### 3. Previous Frame Sampling (Lines 400-408)
```hlsl
#ifdef PURE_DATAMOSH_MODE
    // Pure Datamosh Mode: Use original motion vector like JPG Bitcrunch
    float3 pull = SAMPLE(_PrevScreen, sampler_LinearClamp, uv - motionVector).rgb;
#else
    // Enhanced Mode: Use enhanced motion vector for additional features
    float3 pull = SAMPLE(_PrevScreen, sampler_LinearClamp, uv - enhancedMotionVector).rgb;
#endif
```

### Preset Updates

#### JPG Bitcrunch Exact.asset
- `reprojectBaseNoise: 0` → `reprojectBaseNoise: 0.15`
- `reprojectLengthInfluence: 0` → `reprojectLengthInfluence: 1.2`

#### VHS Datamosh.asset
- `reprojectBaseNoise: 0` → `reprojectBaseNoise: 0.25`
- `reprojectLengthInfluence: 0` → `reprojectLengthInfluence: 2.5`

#### Subtle Compression.asset
- `reprojectBaseNoise: 0` → `reprojectBaseNoise: 0.05`
- `reprojectLengthInfluence: 0` → `reprojectLengthInfluence: 0.3`

#### New Preset: Strong Datamosh.asset
- Created new preset with strong datamosh values for demonstration
- `reprojectBaseNoise: 0.35`, `reprojectLengthInfluence: 3.5`

## Validation Tools

### FluxDatamoshComparisonTool.cs
- Side-by-side parameter comparison between Flux and JPG Bitcrunch
- Real-time validation of datamosh enablement
- Quick preset application

### FluxDatamoshValidationTool.cs
- Comprehensive validation of Flux datamosh implementation
- Shader keyword verification
- Quick fix buttons for common issues

## Expected Results

With these fixes, Flux in Pure Datamosh Mode should now produce:
1. **Identical block-based artifacts** to JPG Bitcrunch
2. **Same motion sensitivity and responsiveness**
3. **Authentic trailing/smearing effects** with proper intensity
4. **Square-shaped datamosh blocks** matching JPG Bitcrunch appearance

## Testing Instructions

1. Open Flux demo scene
2. Apply "JPG Bitcrunch Exact" preset to Flux Volume
3. Ensure Pure Datamosh Mode is enabled
4. Compare with JPG Bitcrunch effect using identical parameters
5. Verify motion-based trailing effects are visible and match JPG Bitcrunch behavior

## Technical Notes

- Pure Datamosh Mode now uses exact JPG Bitcrunch shader logic
- Enhanced Mode retains all additional Flux features
- Motion vector handling is now consistent within each mode
- Block ID calculation matches JPG Bitcrunch exactly in Pure Datamosh Mode
