%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -1223223985, guid: aa81bbec85a3f423193272a7c6154986, type: 3}
  m_Name: 172 Flux Drums - Scene 2 - 8th
  m_EditorClassIdentifier: 
  mEventID: 8th
  mEventList:
  - mStartSample: 0
    mEndSample: 0
  - mStartSample: 15383
    mEndSample: 15383
  - mStartSample: 30767
    mEndSample: 30767
  - mStartSample: 46151
    mEndSample: 46151
  - mStartSample: 61534
    mEndSample: 61534
  - mStartSample: 76918
    mEndSample: 76918
  - mStartSample: 92302
    mEndSample: 92302
  - mStartSample: 107686
    mEndSample: 107686
  - mStartSample: 123069
    mEndSample: 123069
  - mStartSample: 138453
    mEndSample: 138453
  - mStartSample: 153837
    mEndSample: 153837
  - mStartSample: 169220
    mEndSample: 169220
  - mStartSample: 184604
    mEndSample: 184604
  - mStartSample: 199988
    mEndSample: 199988
  - mStartSample: 215372
    mEndSample: 215372
  - mStartSample: 230755
    mEndSample: 230755
  - mStartSample: 246139
    mEndSample: 246139
  - mStartSample: 261523
    mEndSample: 261523
  - mStartSample: 276906
    mEndSample: 276906
  - mStartSample: 292290
    mEndSample: 292290
  - mStartSample: 307674
    mEndSample: 307674
  - mStartSample: 323058
    mEndSample: 323058
  - mStartSample: 338441
    mEndSample: 338441
  - mStartSample: 353825
    mEndSample: 353825
  - mStartSample: 369209
    mEndSample: 369209
  - mStartSample: 384593
    mEndSample: 384593
  - mStartSample: 399976
    mEndSample: 399976
  - mStartSample: 415360
    mEndSample: 415360
  - mStartSample: 430744
    mEndSample: 430744
  - mStartSample: 446127
    mEndSample: 446127
  - mStartSample: 461511
    mEndSample: 461511
  - mStartSample: 476895
    mEndSample: 476895
  _SerializedPayloadTypes: []
  _AssetPayloads: []
  _AssetPayloadIdxs: 
  _ColorPayloads: []
  _ColorPayloadIdxs: 
  _CurvePayloads: []
  _CurvePayloadIdxs: 
  _FloatPayloads: []
  _FloatPayloadIdxs: 
  _GradientPayloads: []
  _GradientPayloadIdxs: 
  _IntPayloads: []
  _IntPayloadIdxs: 
  _SpectrumPayloads: []
  _SpectrumPayloadIdxs: 
  _TextPayloads: []
  _TextPayloadIdxs: 
