﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Class Flex
 | Flexalon 3D Layouts </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Class Flex
 | Flexalon 3D Layouts ">
    <meta name="generator" content="docfx ">
  
    <link rel="shortcut icon" href="images/favicon.svg">
    <link rel="stylesheet" href="styles/docfx.vendor.css">
    <link rel="stylesheet" href="styles/docfx.css">
    <link rel="stylesheet" href="styles/main.css">
    <meta property="docfx:navrel" content="tochead.html">
    <meta property="docfx:tocrel" content="toc.html">
  
  <meta property="docfx:rel" content="">
  
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="https://www.flexalon.com/">
                <img id="logo" class="svg" src="images/logo_32x32.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first="First" data-prev="Previous" data-next="Next" data-last="Last"></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">

        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="Flexalon.Flex">


  <h1 id="Flexalon_Flex" data-uid="Flexalon.Flex" class="text-break">Class Flex
</h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="inheritance">
    <h5>Inheritance</h5>
    <div class="level0"><span class="xref">object</span></div>
    <div class="level1"><span class="xref">Flex</span></div>
  </div>
  <h5 id="Flexalon_Flex_syntax">Syntax</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static class Flex</code></pre>
  </div>
  <h3 id="methods">Methods
</h3>
  <a id="Flexalon_Flex_CreateFlexItem_" data-uid="Flexalon.Flex.CreateFlexItem*"></a>
  <h4 id="Flexalon_Flex_CreateFlexItem_Flexalon_FlexalonNode_System_Int32_System_Single_System_Single_System_Single_" data-uid="Flexalon.Flex.CreateFlexItem(Flexalon.FlexalonNode,System.Int32,System.Single,System.Single,System.Single)">CreateFlexItem(FlexalonNode, int, float, float, float)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static FlexItem CreateFlexItem(FlexalonNode node, int axis, float childSize, float usedSize, float layoutSize)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.FlexalonNode.html">FlexalonNode</a></td>
        <td><span class="parametername">node</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">int</span></td>
        <td><span class="parametername">axis</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">float</span></td>
        <td><span class="parametername">childSize</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">float</span></td>
        <td><span class="parametername">usedSize</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">float</span></td>
        <td><span class="parametername">layoutSize</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <h5 class="returns">Returns</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><a class="xref" href="Flexalon.FlexItem.html">FlexItem</a></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_Flex_Grow_" data-uid="Flexalon.Flex.Grow*"></a>
  <h4 id="Flexalon_Flex_Grow_System_Collections_Generic_List_Flexalon_FlexItem__System_Single_System_Single_" data-uid="Flexalon.Flex.Grow(System.Collections.Generic.List{Flexalon.FlexItem},System.Single,System.Single)">Grow(List&lt;FlexItem&gt;, float, float)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Grow(List&lt;FlexItem&gt; items, float space, float gap)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Collections.Generic.List&lt;T&gt;</span>&lt;<a class="xref" href="Flexalon.FlexItem.html">FlexItem</a>&gt;</td>
        <td><span class="parametername">items</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">float</span></td>
        <td><span class="parametername">space</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">float</span></td>
        <td><span class="parametername">gap</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_Flex_GrowOrShrink_" data-uid="Flexalon.Flex.GrowOrShrink*"></a>
  <h4 id="Flexalon_Flex_GrowOrShrink_System_Collections_Generic_List_Flexalon_FlexItem__System_Single_System_Single_System_Single_" data-uid="Flexalon.Flex.GrowOrShrink(System.Collections.Generic.List{Flexalon.FlexItem},System.Single,System.Single,System.Single)">GrowOrShrink(List&lt;FlexItem&gt;, float, float, float)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void GrowOrShrink(List&lt;FlexItem&gt; items, float usedSpace, float totalSpace, float gap)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Collections.Generic.List&lt;T&gt;</span>&lt;<a class="xref" href="Flexalon.FlexItem.html">FlexItem</a>&gt;</td>
        <td><span class="parametername">items</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">float</span></td>
        <td><span class="parametername">usedSpace</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">float</span></td>
        <td><span class="parametername">totalSpace</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">float</span></td>
        <td><span class="parametername">gap</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
  <a id="Flexalon_Flex_Shrink_" data-uid="Flexalon.Flex.Shrink*"></a>
  <h4 id="Flexalon_Flex_Shrink_System_Collections_Generic_List_Flexalon_FlexItem__System_Single_System_Single_" data-uid="Flexalon.Flex.Shrink(System.Collections.Generic.List{Flexalon.FlexItem},System.Single,System.Single)">Shrink(List&lt;FlexItem&gt;, float, float)</h4>
  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>
  <h5 class="decalaration">Declaration</h5>
  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static void Shrink(List&lt;FlexItem&gt; items, float space, float gap)</code></pre>
  </div>
  <h5 class="parameters">Parameters</h5>
  <table class="table table-bordered table-striped table-condensed">
    <thead>
      <tr>
        <th>Type</th>
        <th>Name</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td><span class="xref">System.Collections.Generic.List&lt;T&gt;</span>&lt;<a class="xref" href="Flexalon.FlexItem.html">FlexItem</a>&gt;</td>
        <td><span class="parametername">items</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">float</span></td>
        <td><span class="parametername">space</span></td>
        <td></td>
      </tr>
      <tr>
        <td><span class="xref">float</span></td>
        <td><span class="parametername">gap</span></td>
        <td></td>
      </tr>
    </tbody>
  </table>
</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In This Article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      Flexalon 3D Layouts
      
          </div>
        </div>
      </footer>
    </div>

    
    
    
  </body>
</html>
