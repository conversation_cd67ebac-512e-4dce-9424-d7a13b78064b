%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &1007884926392850
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4414919988423118}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4414919988423118
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1007884926392850}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4649400960625530}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1008336766233542
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4273086064559108}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4273086064559108
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1008336766233542}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4261696009526220}
  m_Father: {fileID: 4533832410405600}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1009521092257128
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4181870408078312}
  m_Layer: 0
  m_Name: BSpineC
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4181870408078312
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1009521092257128}
  serializedVersion: 2
  m_LocalRotation: {x: -0.017976914, y: -1.0429271e-10, z: -0.000000040790603, w: 0.9998384}
  m_LocalPosition: {x: -0.0000000021907018, y: -0.02189353, z: -0.180428}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4633520252529680}
  - {fileID: 4084536281306498}
  - {fileID: 4564450943390816}
  - {fileID: 4985034967616254}
  - {fileID: 4292628634092992}
  - {fileID: 4774099960004502}
  - {fileID: 4821857296403344}
  - {fileID: 4567636475282416}
  - {fileID: 4670689883812756}
  - {fileID: 4892270367390320}
  m_Father: {fileID: 4526575612360388}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1017060798909946
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4108088491198552}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4108088491198552
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1017060798909946}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4405081626734872}
  m_Father: {fileID: 4011830325223328}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1020980685679820
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4378804833134752}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4378804833134752
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1020980685679820}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4215628041141408}
  m_Father: {fileID: 4035498698195584}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1025197815221460
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4485170664719034}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4485170664719034
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1025197815221460}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4394964925119168}
  m_Father: {fileID: 4068957565616730}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1036533170680250
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4341363269006082}
  - component: {fileID: 137956507482895520}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4341363269006082
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1036533170680250}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4146047430284390}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137956507482895520
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1036533170680250}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4215891986620012}
  - {fileID: 4680961590319614}
  - {fileID: 4086673973257362}
  - {fileID: 4702461193410900}
  - {fileID: 4586992301261024}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4215891986620012}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1036686994952296
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4874597137190332}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4874597137190332
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1036686994952296}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4560366291790108}
  m_Father: {fileID: 4168341642836460}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1045002895272122
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4478419107316534}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4478419107316534
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1045002895272122}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4680923526756138}
  m_Father: {fileID: 4894495637402798}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1049892924268802
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4428934940575620}
  m_Layer: 0
  m_Name: BArmL2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4428934940575620
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1049892924268802}
  serializedVersion: 2
  m_LocalRotation: {x: -0.029011615, y: -0.09358945, z: -0.8583484, w: 0.5036242}
  m_LocalPosition: {x: -0.0068614194, y: 0.0002758804, z: -0.0861839}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4749842592694542}
  m_Father: {fileID: 4856184045208054}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1053789672582352
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4528454017576386}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4528454017576386
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1053789672582352}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4599550948850218}
  m_Father: {fileID: 4336261565076342}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1056123295893178
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4567636475282416}
  m_Layer: 0
  m_Name: Fur (19)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4567636475282416
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1056123295893178}
  serializedVersion: 2
  m_LocalRotation: {x: 0.11435158, y: 0.044951953, z: -0.99240935, w: -0.005179321}
  m_LocalPosition: {x: -0, y: -0.153, z: -0.193}
  m_LocalScale: {x: 0.8275738, y: 0.736285, z: 1.0295831}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4168341642836460}
  - {fileID: 4236131051592128}
  m_Father: {fileID: 4181870408078312}
  m_LocalEulerAnglesHint: {x: 5.051, y: -13.198001, z: -181.183}
--- !u!1 &1058970133017348
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4595456102885146}
  - component: {fileID: 33682143233196688}
  - component: {fileID: 23984133750844700}
  m_Layer: 0
  m_Name: Holders
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4595456102885146
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1058970133017348}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.000000029802322, w: 1}
  m_LocalPosition: {x: -0.00009566011, y: -0.009760188, z: -5.7020564e-12}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4342970549100840}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &33682143233196688
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1058970133017348}
  m_Mesh: {fileID: 0}
--- !u!23 &23984133750844700
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1058970133017348}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &1062879257126730
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4892270367390320}
  m_Layer: 0
  m_Name: Fur (21)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4892270367390320
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1062879257126730}
  serializedVersion: 2
  m_LocalRotation: {x: 0.11291038, y: 0.2139723, z: -0.96997255, w: -0.024907794}
  m_LocalPosition: {x: 0, y: -0.123, z: -0.822}
  m_LocalScale: {x: 0.9999993, y: 0.9999999, z: 0.99999976}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4996393466360508}
  - {fileID: 4324097220994694}
  m_Father: {fileID: 4181870408078312}
  m_LocalEulerAnglesHint: {x: 24.172, y: -14.583001, z: -186.081}
--- !u!1 &1070528449835104
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4025355731299354}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4025355731299354
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1070528449835104}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4885188462864284}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1073648159361122
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4053762796047906}
  - component: {fileID: 95476367744974874}
  - component: {fileID: 54161756515638356}
  - component: {fileID: 136313978636084896}
  m_Layer: 0
  m_Name: PR_FTail_Fheelek Hairy
  m_TagString: Player
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4053762796047906
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1073648159361122}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4327838943989390}
  - {fileID: 4853758766688338}
  - {fileID: 4983228665807638}
  - {fileID: 4618220085747470}
  - {fileID: 4342970549100840}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!95 &95476367744974874
Animator:
  serializedVersion: 7
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1073648159361122}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 0}
  m_CullingMode: 1
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_AnimatePhysics: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!54 &54161756515638356
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1073648159361122}
  serializedVersion: 4
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 1000
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 0
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 112
  m_CollisionDetection: 0
--- !u!136 &136313978636084896
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1073648159361122}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.5
  m_Height: 2.7323802
  m_Direction: 1
  m_Center: {x: 0, y: 1.4844695, z: 0}
--- !u!1 &1074599267384106
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4975267610096312}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4975267610096312
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1074599267384106}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4885188462864284}
  m_Father: {fileID: 4773278208983928}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1079492280033410
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4687003068740068}
  m_Layer: 0
  m_Name: BSkeleton
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4687003068740068
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1079492280033410}
  serializedVersion: 2
  m_LocalRotation: {x: 0.70049936, y: -0.000000028847625, z: 0.000000028829723, w: 0.713653}
  m_LocalPosition: {x: 0.010177791, y: 1.202489, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4526575612360388}
  m_Father: {fileID: 4327838943989390}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1082252115837498
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4251146063190398}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4251146063190398
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1082252115837498}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4666171133201978}
  m_Father: {fileID: 4774099960004502}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1085085912520924
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4087729404986920}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4087729404986920
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1085085912520924}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4188517000829842}
  m_Father: {fileID: 4405081626734872}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1108652119090832
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4286066156482418}
  m_Layer: 0
  m_Name: Fur (5)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4286066156482418
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1108652119090832}
  serializedVersion: 2
  m_LocalRotation: {x: 0.021554934, y: -0.41846257, z: 0.009934679, w: 0.9079239}
  m_LocalPosition: {x: -0, y: 0, z: 0.126}
  m_LocalScale: {x: 1.0000017, y: 1.0000002, z: 1.0000015}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4028621993437382}
  - {fileID: 4051562232917774}
  m_Father: {fileID: 4042174603628926}
  m_LocalEulerAnglesHint: {x: 2.72, y: -49.49, z: 0}
--- !u!1 &1121495592416432
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4505141303637378}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4505141303637378
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1121495592416432}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4203414142884658}
  m_Father: {fileID: 4960120799512624}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1122181618760870
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4664705006320276}
  - component: {fileID: 137341500533036924}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4664705006320276
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1122181618760870}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4396765125847402}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137341500533036924
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1122181618760870}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4861287597963080}
  - {fileID: 4664729736593348}
  - {fileID: 4428496267986074}
  - {fileID: 4002762585685290}
  - {fileID: 4405510196472626}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4861287597963080}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1129466004892816
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4497358842511356}
  - component: {fileID: 137353628557576102}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4497358842511356
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1129466004892816}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4653096483216170}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137353628557576102
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1129466004892816}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4737859875262580}
  - {fileID: 4033321250539968}
  - {fileID: 4521574549823736}
  - {fileID: 4850721406318936}
  - {fileID: 4872750053369334}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4737859875262580}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1135498761844530
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4680923526756138}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4680923526756138
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1135498761844530}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4775575267650068}
  m_Father: {fileID: 4478419107316534}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1139197157470298
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4260181140283212}
  - component: {fileID: 137915083052026530}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4260181140283212
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1139197157470298}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4296495019836184}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137915083052026530
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1139197157470298}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4523420869360922}
  - {fileID: 4744188912455028}
  - {fileID: 4502208971878302}
  - {fileID: 4748188627754220}
  - {fileID: 4927437926029020}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4523420869360922}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1145981344907322
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4775575267650068}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4775575267650068
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1145981344907322}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4680923526756138}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1146362534953932
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4500763252442198}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4500763252442198
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1146362534953932}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4314984982105186}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1150745334321714
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4708979716387818}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4708979716387818
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1150745334321714}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4105173551035238}
  m_Father: {fileID: 4689688334605344}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1158822360110102
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4851599626611798}
  m_Layer: 0
  m_Name: Fur (10)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4851599626611798
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1158822360110102}
  serializedVersion: 2
  m_LocalRotation: {x: -0.4067362, y: 0.3969914, z: 0.79747516, w: -0.20247705}
  m_LocalPosition: {x: 0, y: 0.023954168, z: 0.12877136}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4187539100677642}
  - {fileID: 4334919485110382}
  m_Father: {fileID: 4339650069157304}
  m_LocalEulerAnglesHint: {x: -27.935, y: -66.384, z: -133.024}
--- !u!1 &1161603850572560
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4415730523496064}
  - component: {fileID: 137025652627099312}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4415730523496064
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1161603850572560}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4047537788991022}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137025652627099312
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1161603850572560}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4250497048755668}
  - {fileID: 4774993003118418}
  - {fileID: 4797175212013222}
  - {fileID: 4818605159667876}
  - {fileID: 4991597019885528}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4250497048755668}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1163435396988172
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4215891986620012}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4215891986620012
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1163435396988172}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4680961590319614}
  m_Father: {fileID: 4146047430284390}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1164460940515092
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4603316711219592}
  - component: {fileID: 137657591755872130}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4603316711219592
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1164460940515092}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4225051517241952}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137657591755872130
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1164460940515092}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4780643320262092}
  - {fileID: 4128175958769604}
  - {fileID: 4533832410405600}
  - {fileID: 4273086064559108}
  - {fileID: 4261696009526220}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4780643320262092}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1171580786539454
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4533832410405600}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4533832410405600
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1171580786539454}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4273086064559108}
  m_Father: {fileID: 4128175958769604}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1174871892131162
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4353468934613960}
  - component: {fileID: 137838129822330212}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4353468934613960
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1174871892131162}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4144516006307354}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137838129822330212
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1174871892131162}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4974066538998472}
  - {fileID: 4019553886114204}
  - {fileID: 4610367542880568}
  - {fileID: 4019633597365154}
  - {fileID: 4267271160151408}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4974066538998472}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1183732510931766
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4660225685297262}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4660225685297262
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1183732510931766}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4739797413204974}
  m_Father: {fileID: 4348207991646820}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1188637076641710
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4880024886022224}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4880024886022224
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1188637076641710}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4451211804789614}
  m_Father: {fileID: 4979023462863312}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1188877788911730
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4560366291790108}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4560366291790108
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1188877788911730}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4716354833891844}
  m_Father: {fileID: 4874597137190332}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1190673130189654
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4348207991646820}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4348207991646820
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1190673130189654}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4660225685297262}
  m_Father: {fileID: 4135377254520718}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1193008233840788
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4954473801884080}
  m_Layer: 0
  m_Name: BSpine_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4954473801884080
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1193008233840788}
  serializedVersion: 2
  m_LocalRotation: {x: 9.471605e-16, y: -1.3756501e-13, z: 0.000000010536712, w: 1}
  m_LocalPosition: {x: 1.47915e-13, y: -5.684342e-16, z: -0.5372595}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4042174603628926}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1194346516418426
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4044845356684678}
  m_Layer: 0
  m_Name: Fur (6)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4044845356684678
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1194346516418426}
  serializedVersion: 2
  m_LocalRotation: {x: 0.12464058, y: 0, z: 0, w: 0.992202}
  m_LocalPosition: {x: -0.00000005758932, y: 0.00000038091093, z: 0.12600034}
  m_LocalScale: {x: 0.9999995, y: 1.0000002, z: 0.9999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4695065046644840}
  - {fileID: 4579897501458906}
  m_Father: {fileID: 4042174603628926}
  m_LocalEulerAnglesHint: {x: 14.32, y: 0, z: 0}
--- !u!1 &1195729326700022
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4564450943390816}
  m_Layer: 0
  m_Name: Fur (15)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4564450943390816
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1195729326700022}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0000008527696, y: 0.09833818, z: -0.99515307, w: 0.00000032382383}
  m_LocalPosition: {x: 0, y: -0.1464977, z: -0.6387736}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4376262951846720}
  - {fileID: 4568611939921112}
  m_Father: {fileID: 4181870408078312}
  m_LocalEulerAnglesHint: {x: -78.5, y: 0, z: -179.99998}
--- !u!1 &1208661840915632
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4315595056922964}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4315595056922964
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1208661840915632}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4997621953761668}
  m_Father: {fileID: 4207457071001780}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1218019876342154
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4653096483216170}
  m_Layer: 0
  m_Name: Fur (3)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4653096483216170
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1218019876342154}
  serializedVersion: 2
  m_LocalRotation: {x: -0.000000065192566, y: -1.8651742e-14, z: -5.1583724e-14, w: 1}
  m_LocalPosition: {x: -0, y: -0, z: -0.305}
  m_LocalScale: {x: 1.0000017, y: 1.0000002, z: 1.0000015}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4737859875262580}
  - {fileID: 4497358842511356}
  m_Father: {fileID: 4042174603628926}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1218180651490142
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4280974406627108}
  m_Layer: 0
  m_Name: Fur (7)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4280974406627108
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1218180651490142}
  serializedVersion: 2
  m_LocalRotation: {x: -0.04422964, y: 0, z: 0, w: 0.9990214}
  m_LocalPosition: {x: -0, y: 0.001, z: -0.481}
  m_LocalScale: {x: 1.000001, y: 1.0000002, z: 1.0000008}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4656393384509608}
  - {fileID: 4991960695084132}
  m_Father: {fileID: 4042174603628926}
  m_LocalEulerAnglesHint: {x: -5.07, y: 0, z: 0}
--- !u!1 &1219080652906098
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4506234842885344}
  - component: {fileID: 137678187417724238}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4506234842885344
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1219080652906098}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4838571176460744}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137678187417724238
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1219080652906098}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4487077738355358}
  - {fileID: 4894495637402798}
  - {fileID: 4478419107316534}
  - {fileID: 4680923526756138}
  - {fileID: 4775575267650068}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4487077738355358}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1227171613212898
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4564079268094022}
  m_Layer: 0
  m_Name: Fur (4)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4564079268094022
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1227171613212898}
  serializedVersion: 2
  m_LocalRotation: {x: -0.000000065192566, y: -1.8651742e-14, z: -5.1583724e-14, w: 1}
  m_LocalPosition: {x: -0, y: -0, z: -0.416}
  m_LocalScale: {x: 1.0000017, y: 1.0000002, z: 1.0000015}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4040496962701982}
  - {fileID: 4305346378418236}
  m_Father: {fileID: 4042174603628926}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1252953376230004
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4608861758549540}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4608861758549540
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1252953376230004}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4155222149510626}
  m_Father: {fileID: 4367958959596264}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1253370753816964
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4334919485110382}
  - component: {fileID: 137930020297672002}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4334919485110382
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1253370753816964}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4851599626611798}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137930020297672002
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1253370753816964}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4187539100677642}
  - {fileID: 4433452175046342}
  - {fileID: 4823636708418956}
  - {fileID: 4518305856084032}
  - {fileID: 4879806383952630}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4187539100677642}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1259684579438272
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4749490994129548}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4749490994129548
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1259684579438272}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4491584567774784}
  m_Father: {fileID: 4046688474860290}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1261624565346632
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4573669960452038}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4573669960452038
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1261624565346632}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4269581396158818}
  m_Father: {fileID: 4499389407567348}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1262106621602374
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4155222149510626}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4155222149510626
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1262106621602374}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4649400960625530}
  m_Father: {fileID: 4608861758549540}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1268465917655234
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4576078118765600}
  m_Layer: 0
  m_Name: Fur (9)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4576078118765600
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1268465917655234}
  serializedVersion: 2
  m_LocalRotation: {x: 0.72072643, y: 0.22830771, z: 0.59276515, w: 0.2775942}
  m_LocalPosition: {x: 0, y: 0.023954168, z: 0.12877136}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4191461060998378}
  - {fileID: 4233861771209736}
  m_Father: {fileID: 4339650069157304}
  m_LocalEulerAnglesHint: {x: 7.439, y: 98.3, z: 138.41101}
--- !u!1 &1285124945342618
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4652276841428066}
  - component: {fileID: 137745388589440356}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4652276841428066
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1285124945342618}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4163549216908622}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137745388589440356
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1285124945342618}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4251496273052258}
  - {fileID: 4035498698195584}
  - {fileID: 4378804833134752}
  - {fileID: 4215628041141408}
  - {fileID: 4973464452936210}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4251496273052258}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1288877217477638
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4985034967616254}
  m_Layer: 0
  m_Name: Fur (16)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4985034967616254
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1288877217477638}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0000008390462, y: 0.04524929, z: -0.99897575, w: 0.00000028093064}
  m_LocalPosition: {x: 0, y: -0.12308095, z: -0.5268595}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4722338068230498}
  - {fileID: 4023531735124426}
  m_Father: {fileID: 4181870408078312}
  m_LocalEulerAnglesHint: {x: -84.6, y: 0, z: -179.99998}
--- !u!1 &1292315006587140
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4396765125847402}
  m_Layer: 0
  m_Name: Fur (22)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4396765125847402
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1292315006587140}
  serializedVersion: 2
  m_LocalRotation: {x: 0.4962059, y: -0.5121265, z: 0.48760033, w: 0.50373816}
  m_LocalPosition: {x: -0.091, y: 0.319, z: 0}
  m_LocalScale: {x: 0.56631005, y: 0.4096101, z: 0.68385994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4861287597963080}
  - {fileID: 4664705006320276}
  m_Father: {fileID: 4336793997347296}
  m_LocalEulerAnglesHint: {x: 92.07898, y: 62.078995, z: 152.066}
--- !u!1 &1294967843055094
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4861540218523750}
  m_Layer: 0
  m_Name: BHandR_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4861540218523750
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1294967843055094}
  serializedVersion: 2
  m_LocalRotation: {x: -0.03887727, y: -0.028507745, z: -0.0011095941, w: 0.99883664}
  m_LocalPosition: {x: 0.01644409, y: -0.022477856, z: -0.28784427}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4580696547446326}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1295295051381674
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4670689883812756}
  m_Layer: 0
  m_Name: Fur (20)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4670689883812756
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1295295051381674}
  serializedVersion: 2
  m_LocalRotation: {x: -0.66141814, y: 0.03391049, z: -0.7486512, w: 0.029959641}
  m_LocalPosition: {x: -0, y: -0.164, z: -0.073}
  m_LocalScale: {x: 0.6808877, y: 0.5854586, z: 0.93660367}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4068957565616730}
  - {fileID: 4148583181229762}
  m_Father: {fileID: 4181870408078312}
  m_LocalEulerAnglesHint: {x: 0.638, y: 82.949005, z: -174.852}
--- !u!1 &1307607716575662
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4991960695084132}
  - component: {fileID: 137337941357423792}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4991960695084132
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1307607716575662}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4280974406627108}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137337941357423792
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1307607716575662}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4656393384509608}
  - {fileID: 4468503701823024}
  - {fileID: 4563062003610390}
  - {fileID: 4966228392956610}
  - {fileID: 4114358581704958}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4656393384509608}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1309085994471702
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4225051517241952}
  m_Layer: 0
  m_Name: Fur (23)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4225051517241952
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1309085994471702}
  serializedVersion: 2
  m_LocalRotation: {x: 0.496206, y: -0.51212645, z: 0.48760036, w: 0.5037381}
  m_LocalPosition: {x: -0.092, y: 0.293, z: -0}
  m_LocalScale: {x: 0.56631005, y: 0.40961003, z: 0.68385994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4780643320262092}
  - {fileID: 4603316711219592}
  m_Father: {fileID: 4336793997347296}
  m_LocalEulerAnglesHint: {x: 92.07898, y: 62.078995, z: 152.066}
--- !u!1 &1309669586382260
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4047537788991022}
  m_Layer: 0
  m_Name: Fur (8)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4047537788991022
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1309669586382260}
  serializedVersion: 2
  m_LocalRotation: {x: -0.09402135, y: 0, z: 0, w: 0.9955702}
  m_LocalPosition: {x: -0, y: 0.002, z: -0.523}
  m_LocalScale: {x: 1.0000024, y: 1.0000001, z: 1.0000018}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4250497048755668}
  - {fileID: 4415730523496064}
  m_Father: {fileID: 4042174603628926}
  m_LocalEulerAnglesHint: {x: -10.79, y: 0, z: 0}
--- !u!1 &1323220775088306
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4128175958769604}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4128175958769604
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1323220775088306}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4533832410405600}
  m_Father: {fileID: 4780643320262092}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1344370120480220
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4028621993437382}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4028621993437382
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1344370120480220}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4960120799512624}
  m_Father: {fileID: 4286066156482418}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1347829875917236
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4046688474860290}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4046688474860290
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1347829875917236}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4749490994129548}
  m_Father: {fileID: 4722338068230498}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1351227561303730
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4526575612360388}
  m_Layer: 0
  m_Name: BSpine_1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4526575612360388
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1351227561303730}
  serializedVersion: 2
  m_LocalRotation: {x: 0.02727573, y: -0.0000000017828584, z: 4.8646864e-11, w: 0.99962795}
  m_LocalPosition: {x: -4.440892e-18, y: -2.1316282e-16, z: -0.5397692}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4795775428874804}
  - {fileID: 4856184045208054}
  - {fileID: 4181870408078312}
  m_Father: {fileID: 4687003068740068}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1356736130294208
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4773278208983928}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4773278208983928
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1356736130294208}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4975267610096312}
  m_Father: {fileID: 4376262951846720}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1361140215147372
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4011830325223328}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4011830325223328
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1361140215147372}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4108088491198552}
  m_Father: {fileID: 4457460715478288}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1363359132826340
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4143547789183638}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4143547789183638
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1363359132826340}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4314984982105186}
  m_Father: {fileID: 4532298462012058}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1368035619862398
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4114358581704958}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4114358581704958
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1368035619862398}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4966228392956610}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1368561819662380
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4828861340469682}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4828861340469682
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1368561819662380}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4207457071001780}
  m_Father: {fileID: 4996393466360508}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1369095780356464
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4973464452936210}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4973464452936210
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1369095780356464}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4215628041141408}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1370857031090480
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4568611939921112}
  - component: {fileID: 137720353420922424}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4568611939921112
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1370857031090480}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4564450943390816}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137720353420922424
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1370857031090480}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4376262951846720}
  - {fileID: 4773278208983928}
  - {fileID: 4975267610096312}
  - {fileID: 4885188462864284}
  - {fileID: 4025355731299354}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4376262951846720}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1373482739314618
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4897594588910752}
  m_Layer: 0
  m_Name: Fur
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4897594588910752
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1373482739314618}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0.9044923, z: 0, w: 0.42649}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4367958959596264}
  - {fileID: 4958423197816966}
  m_Father: {fileID: 4042174603628926}
  m_LocalEulerAnglesHint: {x: 0, y: 129.51, z: 0}
--- !u!1 &1373887910578988
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4304227809504874}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4304227809504874
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1373887910578988}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4203414142884658}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1374318123289652
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4188517000829842}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4188517000829842
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1374318123289652}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4087729404986920}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1376586214863200
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4974066538998472}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4974066538998472
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1376586214863200}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4019553886114204}
  m_Father: {fileID: 4144516006307354}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1383009688785058
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4347795491291248}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4347795491291248
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1383009688785058}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4873544723011614}
  m_Father: {fileID: 4252265682688120}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1385700414288300
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4281697796309706}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4281697796309706
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1385700414288300}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4855033002331920}
  m_Father: {fileID: 4394964925119168}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1386125892520062
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4081799783749526}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4081799783749526
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1386125892520062}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4773512029215718}
  m_Father: {fileID: 4666171133201978}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1391838359975180
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4823636708418956}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4823636708418956
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1391838359975180}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4518305856084032}
  m_Father: {fileID: 4433452175046342}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1394998528903226
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4664729736593348}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4664729736593348
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1394998528903226}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4428496267986074}
  m_Father: {fileID: 4861287597963080}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1395448176524440
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4287901234303708}
  - component: {fileID: 137783928474110844}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4287901234303708
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1395448176524440}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4111863530387508}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137783928474110844
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1395448176524440}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4934212092515022}
  - {fileID: 4532298462012058}
  - {fileID: 4143547789183638}
  - {fileID: 4314984982105186}
  - {fileID: 4500763252442198}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4934212092515022}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1398163713459426
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4376262951846720}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4376262951846720
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1398163713459426}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4773278208983928}
  m_Father: {fileID: 4564450943390816}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1398604348225926
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4773512029215718}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4773512029215718
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1398604348225926}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4200946280068458}
  m_Father: {fileID: 4081799783749526}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1400562607389072
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4296495019836184}
  m_Layer: 0
  m_Name: Fur (8)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4296495019836184
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1400562607389072}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000025953238, y: 0.28927544, z: 0.95724595, w: 0.000000783301}
  m_LocalPosition: {x: 0, y: 0.010236949, z: 0.11351205}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4523420869360922}
  - {fileID: 4260181140283212}
  m_Father: {fileID: 4339650069157304}
  m_LocalEulerAnglesHint: {x: 34.7, y: 0, z: 0}
--- !u!1 &1400681040550934
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4518305856084032}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4518305856084032
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1400681040550934}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4879806383952630}
  m_Father: {fileID: 4823636708418956}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1404224245288020
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4523161272351254}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4523161272351254
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1404224245288020}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4979023462863312}
  m_Father: {fileID: 4695065046644840}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1407320045590494
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4649400960625530}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4649400960625530
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1407320045590494}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4414919988423118}
  m_Father: {fileID: 4155222149510626}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1412507483944468
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4633520252529680}
  m_Layer: 0
  m_Name: Fur (13)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4633520252529680
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1412507483944468}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0000008639107, y: 0.15028553, z: -0.9886427, w: 0.0000003651192}
  m_LocalPosition: {x: 0, y: -0.1091889, z: -0.82491326}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4135377254520718}
  - {fileID: 4773010653878124}
  m_Father: {fileID: 4181870408078312}
  m_LocalEulerAnglesHint: {x: -72.5, y: 0, z: -179.99998}
--- !u!1 &1415238204654534
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4737859875262580}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4737859875262580
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1415238204654534}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4033321250539968}
  m_Father: {fileID: 4653096483216170}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1419267455148694
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4233861771209736}
  - component: {fileID: 137572546579510484}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4233861771209736
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1419267455148694}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4576078118765600}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137572546579510484
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1419267455148694}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4191461060998378}
  - {fileID: 4511003725944658}
  - {fileID: 4844982883260422}
  - {fileID: 4575649307495020}
  - {fileID: 4431622895221308}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4191461060998378}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1425454430654192
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4838571176460744}
  m_Layer: 0
  m_Name: Fur (12)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4838571176460744
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1425454430654192}
  serializedVersion: 2
  m_LocalRotation: {x: -0.27700058, y: 0.4615411, z: 0.8293756, w: -0.14962177}
  m_LocalPosition: {x: -0.000000012909023, y: 0.023954168, z: 0.12877136}
  m_LocalScale: {x: 1.0000019, y: 1.0000006, z: 1.0000019}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4487077738355358}
  - {fileID: 4506234842885344}
  m_Father: {fileID: 4339650069157304}
  m_LocalEulerAnglesHint: {x: -43.054, y: -54.867004, z: -136.403}
--- !u!1 &1428404094554182
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4563062003610390}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4563062003610390
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1428404094554182}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4966228392956610}
  m_Father: {fileID: 4468503701823024}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1431949638377214
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4581799003241716}
  m_Layer: 0
  m_Name: BHandL_2_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4581799003241716
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1431949638377214}
  serializedVersion: 2
  m_LocalRotation: {x: 0.06105151, y: -0.003096379, z: -0.18137237, w: 0.9815127}
  m_LocalPosition: {x: 0.1332512, y: -0.23274574, z: -0.05619811}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4553423104053020}
  m_Father: {fileID: 4346254202617646}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1434351795935856
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4405081626734872}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4405081626734872
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1434351795935856}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4087729404986920}
  m_Father: {fileID: 4108088491198552}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1435460909411416
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4294487224796136}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4294487224796136
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1435460909411416}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4934403202769354}
  m_Father: {fileID: 4090209565877424}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1437002324497098
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4748188627754220}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4748188627754220
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1437002324497098}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4927437926029020}
  m_Father: {fileID: 4502208971878302}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1447255468939530
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4090209565877424}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4090209565877424
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1447255468939530}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4294487224796136}
  m_Father: {fileID: 4873544723011614}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1461200679822732
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4324097220994694}
  - component: {fileID: 137811158370872832}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4324097220994694
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1461200679822732}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4892270367390320}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137811158370872832
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1461200679822732}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4996393466360508}
  - {fileID: 4828861340469682}
  - {fileID: 4207457071001780}
  - {fileID: 4315595056922964}
  - {fileID: 4997621953761668}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4996393466360508}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1464066631303742
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4956526269267272}
  m_Layer: 0
  m_Name: BHandR_1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4956526269267272
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1464066631303742}
  serializedVersion: 2
  m_LocalRotation: {x: 0.03132498, y: -0.58241296, z: 0.021153957, w: 0.8120138}
  m_LocalPosition: {x: -0.009338676, y: 0.0005073076, z: -0.22795919}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4000191470879700}
  - {fileID: 4580696547446326}
  m_Father: {fileID: 4930379291246824}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1479103118859234
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4267271160151408}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4267271160151408
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1479103118859234}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4019633597365154}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1479172839695916
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4261696009526220}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4261696009526220
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1479172839695916}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4273086064559108}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1483890381276890
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4821857296403344}
  m_Layer: 0
  m_Name: Fur (18)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4821857296403344
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1483890381276890}
  serializedVersion: 2
  m_LocalRotation: {x: -0.2556116, y: 0.043742917, z: -0.9657201, w: 0.011578409}
  m_LocalPosition: {x: -0, y: -0.143, z: -0.306}
  m_LocalScale: {x: 0.9058581, y: 0.847756, z: 0.9523945}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4336261565076342}
  - {fileID: 4290619602454556}
  m_Father: {fileID: 4181870408078312}
  m_LocalEulerAnglesHint: {x: 4.506, y: 29.752, z: -177.429}
--- !u!1 &1486198532799960
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4702461193410900}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4702461193410900
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1486198532799960}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4586992301261024}
  m_Father: {fileID: 4086673973257362}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1487933512971800
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4532298462012058}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4532298462012058
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1487933512971800}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4143547789183638}
  m_Father: {fileID: 4934212092515022}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1489887344059330
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4773010653878124}
  - component: {fileID: 137097156762572906}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4773010653878124
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1489887344059330}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4633520252529680}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137097156762572906
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1489887344059330}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4135377254520718}
  - {fileID: 4348207991646820}
  - {fileID: 4660225685297262}
  - {fileID: 4739797413204974}
  - {fileID: 4424136776440118}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4135377254520718}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1493534041552150
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4468503701823024}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4468503701823024
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1493534041552150}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4563062003610390}
  m_Father: {fileID: 4656393384509608}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1505982702559236
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4135377254520718}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4135377254520718
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1505982702559236}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4348207991646820}
  m_Father: {fileID: 4633520252529680}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1507168891578030
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4105173551035238}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4105173551035238
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1507168891578030}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4379371078639878}
  m_Father: {fileID: 4708979716387818}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1509484946569612
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4996393466360508}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4996393466360508
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1509484946569612}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4828861340469682}
  m_Father: {fileID: 4892270367390320}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1512027660130814
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4991597019885528}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4991597019885528
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1512027660130814}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4818605159667876}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1519244834694432
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4618220085747470}
  - component: {fileID: 137039756896014848}
  m_Layer: 0
  m_Name: FBody
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4618220085747470
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1519244834694432}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4053762796047906}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137039756896014848
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1519244834694432}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4687003068740068}
  - {fileID: 4526575612360388}
  - {fileID: 4181870408078312}
  - {fileID: 4292628634092992}
  - {fileID: 4336793997347296}
  - {fileID: 4548960234035166}
  - {fileID: 4339650069157304}
  - {fileID: 4042174603628926}
  - {fileID: 4954473801884080}
  - {fileID: 4795775428874804}
  - {fileID: 4171612398917912}
  - {fileID: 4930379291246824}
  - {fileID: 4956526269267272}
  - {fileID: 4000191470879700}
  - {fileID: 4094751896365414}
  - {fileID: 4580696547446326}
  - {fileID: 4861540218523750}
  - {fileID: 4856184045208054}
  - {fileID: 4428934940575620}
  - {fileID: 4749842592694542}
  - {fileID: 4346254202617646}
  - {fileID: 4535141322402038}
  - {fileID: 4759688349202108}
  - {fileID: 4581799003241716}
  - {fileID: 4553423104053020}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4687003068740068}
  m_AABB:
    m_Center: {x: 0.0022855103, y: 0.2697451, z: -0.83763385}
    m_Extent: {x: 0.51987207, y: 0.46960157, z: 0.91600394}
  m_DirtyAABB: 0
--- !u!1 &1523517195658588
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4487077738355358}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4487077738355358
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1523517195658588}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4894495637402798}
  m_Father: {fileID: 4838571176460744}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1527065880970768
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4236131051592128}
  - component: {fileID: 137299666474647374}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4236131051592128
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1527065880970768}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4567636475282416}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137299666474647374
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1527065880970768}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4168341642836460}
  - {fileID: 4874597137190332}
  - {fileID: 4560366291790108}
  - {fileID: 4716354833891844}
  - {fileID: 4675879760924856}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4168341642836460}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1531548102198318
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4023531735124426}
  - component: {fileID: 137568338187569438}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4023531735124426
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1531548102198318}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4985034967616254}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137568338187569438
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1531548102198318}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4722338068230498}
  - {fileID: 4046688474860290}
  - {fileID: 4749490994129548}
  - {fileID: 4491584567774784}
  - {fileID: 4640079639001080}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4722338068230498}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1540674609637904
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4305346378418236}
  - component: {fileID: 137944288707878734}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4305346378418236
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1540674609637904}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4564079268094022}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137944288707878734
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1540674609637904}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4040496962701982}
  - {fileID: 4689688334605344}
  - {fileID: 4708979716387818}
  - {fileID: 4105173551035238}
  - {fileID: 4379371078639878}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4040496962701982}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1542858005584500
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4269581396158818}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4269581396158818
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1542858005584500}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4583133167190564}
  m_Father: {fileID: 4573669960452038}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1543004780271544
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4695065046644840}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4695065046644840
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1543004780271544}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4523161272351254}
  m_Father: {fileID: 4044845356684678}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1547285883251988
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4342970549100840}
  - component: {fileID: 33410810449612972}
  - component: {fileID: 23448328912441078}
  m_Layer: 0
  m_Name: Wheel
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4342970549100840
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1547285883251988}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0.000000029802322, w: 1}
  m_LocalPosition: {x: 8.2052174e-17, y: 0.62024987, z: -3.7979346e-17}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4595456102885146}
  - {fileID: 4300056620940062}
  m_Father: {fileID: 4053762796047906}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &33410810449612972
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1547285883251988}
  m_Mesh: {fileID: 0}
--- !u!23 &23448328912441078
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1547285883251988}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &1548316614195676
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4252265682688120}
  m_Layer: 0
  m_Name: Fur (7)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4252265682688120
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1548316614195676}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0000001556367, y: 0.16254403, z: 0.98670137, w: 0.00000081036694}
  m_LocalPosition: {x: 0, y: 0.0014813766, z: 0.10034632}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4347795491291248}
  - {fileID: 4704320618191648}
  m_Father: {fileID: 4339650069157304}
  m_LocalEulerAnglesHint: {x: 19.78, y: 0, z: 0}
--- !u!1 &1552157430070980
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4499389407567348}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4499389407567348
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1552157430070980}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4573669960452038}
  m_Father: {fileID: 4853274235671810}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1557619490937810
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4873544723011614}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4873544723011614
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1557619490937810}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4090209565877424}
  m_Father: {fileID: 4347795491291248}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1558022742310804
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4749842592694542}
  m_Layer: 0
  m_Name: BHandL
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4749842592694542
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1558022742310804}
  serializedVersion: 2
  m_LocalRotation: {x: 0.13123077, y: -0.03048124, z: -0.22956292, w: 0.9639244}
  m_LocalPosition: {x: -0.00046985343, y: -0.0031713133, z: -0.21814069}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4346254202617646}
  m_Father: {fileID: 4428934940575620}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1560896425034336
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4583133167190564}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4583133167190564
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1560896425034336}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4269581396158818}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1564778700630106
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4960120799512624}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4960120799512624
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1564778700630106}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4505141303637378}
  m_Father: {fileID: 4028621993437382}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1570143885720604
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4879806383952630}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4879806383952630
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1570143885720604}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4518305856084032}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1579232577844078
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4680961590319614}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4680961590319614
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1579232577844078}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4086673973257362}
  m_Father: {fileID: 4215891986620012}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1594904196903090
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4722338068230498}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4722338068230498
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1594904196903090}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4046688474860290}
  m_Father: {fileID: 4985034967616254}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1595748485878830
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4523420869360922}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4523420869360922
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1595748485878830}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4744188912455028}
  m_Father: {fileID: 4296495019836184}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1597250021934774
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4163549216908622}
  m_Layer: 0
  m_Name: Fur (24)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4163549216908622
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1597250021934774}
  serializedVersion: 2
  m_LocalRotation: {x: 0.49620605, y: -0.5121264, z: 0.4876004, w: 0.50373805}
  m_LocalPosition: {x: -0.088, y: 0.408, z: 0.002}
  m_LocalScale: {x: 0.56631005, y: 0.40961057, z: 0.683861}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4251496273052258}
  - {fileID: 4652276841428066}
  m_Father: {fileID: 4336793997347296}
  m_LocalEulerAnglesHint: {x: 92.07898, y: 62.078995, z: 152.066}
--- !u!1 &1600091169807250
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4000191470879700}
  m_Layer: 0
  m_Name: BFingerR
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4000191470879700
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1600091169807250}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0025181437, y: 0.029360652, z: -0.21391517, w: 0.9764077}
  m_LocalPosition: {x: 0.0012323513, y: 0.0018262705, z: -0.038143877}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4094751896365414}
  m_Father: {fileID: 4956526269267272}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1604810753183082
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4983228665807638}
  - component: {fileID: 33856545958485556}
  - component: {fileID: 23749408331069400}
  m_Layer: 0
  m_Name: F_Inner
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4983228665807638
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1604810753183082}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000002235174, y: -0.00000004156808, z: 0.000000021073426, w: 1}
  m_LocalPosition: {x: 0.0000000012840802, y: 0.61774373, z: 0.010076396}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4053762796047906}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &33856545958485556
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1604810753183082}
  m_Mesh: {fileID: 0}
--- !u!23 &23749408331069400
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1604810753183082}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &1605363525278916
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4502208971878302}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4502208971878302
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1605363525278916}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4748188627754220}
  m_Father: {fileID: 4744188912455028}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1608062719441052
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4885188462864284}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4885188462864284
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1608062719441052}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4025355731299354}
  m_Father: {fileID: 4975267610096312}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1609790164565052
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4966228392956610}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4966228392956610
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1609790164565052}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4114358581704958}
  m_Father: {fileID: 4563062003610390}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1610519996796366
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4379371078639878}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4379371078639878
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1610519996796366}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4105173551035238}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1611822740172598
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4759688349202108}
  m_Layer: 0
  m_Name: BHandL_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4759688349202108
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1611822740172598}
  serializedVersion: 2
  m_LocalRotation: {x: -0.010790057, y: -0.01010924, z: 0.056062296, w: 0.99831784}
  m_LocalPosition: {x: -1.1368684e-15, y: -8.5265126e-16, z: -0.23128319}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4535141322402038}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1619900748295502
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4610367542880568}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4610367542880568
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1619900748295502}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4019633597365154}
  m_Father: {fileID: 4019553886114204}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1624383697642776
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4580696547446326}
  m_Layer: 0
  m_Name: BHandR_2_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4580696547446326
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1624383697642776}
  serializedVersion: 2
  m_LocalRotation: {x: 0.052603662, y: -0.03114049, z: -0.15537804, w: 0.98596185}
  m_LocalPosition: {x: -0.2660441, y: -0.033867348, z: -0.05619811}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4861540218523750}
  m_Father: {fileID: 4956526269267272}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1625685403823384
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4171612398917912}
  m_Layer: 0
  m_Name: BArm2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4171612398917912
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1625685403823384}
  serializedVersion: 2
  m_LocalRotation: {x: -0.07225967, y: -0.06617552, z: -0.007853134, w: 0.9951571}
  m_LocalPosition: {x: 0.0036883303, y: 0.005792358, z: -0.0861839}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4930379291246824}
  m_Father: {fileID: 4795775428874804}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1630455806544874
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4339650069157304}
  m_Layer: 0
  m_Name: BSpine_3_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4339650069157304
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1630455806544874}
  serializedVersion: 2
  m_LocalRotation: {x: 0.6898483, y: -0.00000059715836, z: 0.0000005685298, w: 0.723954}
  m_LocalPosition: {x: 4.440892e-18, y: -1.7763567e-16, z: -0.5097769}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4042174603628926}
  - {fileID: 4252265682688120}
  - {fileID: 4457460715478288}
  - {fileID: 4296495019836184}
  - {fileID: 4576078118765600}
  - {fileID: 4851599626611798}
  - {fileID: 4838571176460744}
  m_Father: {fileID: 4292628634092992}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1634837637617802
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4042174603628926}
  m_Layer: 0
  m_Name: BHeadHigher
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4042174603628926
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1634837637617802}
  serializedVersion: 2
  m_LocalRotation: {x: 0.000000014137453, y: -0.009344571, z: 0.99995637, w: 0.0000008250561}
  m_LocalPosition: {x: -0.00000000488013, y: -0.002788742, z: -0.04575914}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4954473801884080}
  - {fileID: 4897594588910752}
  - {fileID: 4111863530387508}
  - {fileID: 4146047430284390}
  - {fileID: 4653096483216170}
  - {fileID: 4564079268094022}
  - {fileID: 4286066156482418}
  - {fileID: 4044845356684678}
  - {fileID: 4280974406627108}
  - {fileID: 4047537788991022}
  m_Father: {fileID: 4339650069157304}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1638276691193554
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4207457071001780}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4207457071001780
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1638276691193554}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4315595056922964}
  m_Father: {fileID: 4828861340469682}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1640608943809948
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4861287597963080}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4861287597963080
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1640608943809948}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4664729736593348}
  m_Father: {fileID: 4396765125847402}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1646985749869578
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4084536281306498}
  m_Layer: 0
  m_Name: Fur (14)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4084536281306498
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1646985749869578}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0000008527694, y: 0.098338135, z: -0.99515307, w: 0.00000032382368}
  m_LocalPosition: {x: 0, y: -0.1229285, z: -0.75486165}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4853274235671810}
  - {fileID: 4000386371625086}
  m_Father: {fileID: 4181870408078312}
  m_LocalEulerAnglesHint: {x: -78.5, y: 0, z: -179.99998}
--- !u!1 &1650122654262424
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4853758766688338}
  - component: {fileID: 33318636852879566}
  - component: {fileID: 23973134262759396}
  m_Layer: 0
  m_Name: Connector
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4853758766688338
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1650122654262424}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000002235174, y: -0.00000004156808, z: 0.000000021073426, w: 1}
  m_LocalPosition: {x: -7.252152e-10, y: 1.1306208, z: -0.0017889336}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4053762796047906}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &33318636852879566
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1650122654262424}
  m_Mesh: {fileID: 0}
--- !u!23 &23973134262759396
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1650122654262424}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &1651454807709566
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4068957565616730}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4068957565616730
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1651454807709566}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4485170664719034}
  m_Father: {fileID: 4670689883812756}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1657105418848262
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4457460715478288}
  m_Layer: 0
  m_Name: Fur (11)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4457460715478288
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1657105418848262}
  serializedVersion: 2
  m_LocalRotation: {x: -0.107868366, y: -0.21062708, z: -0.9713154, w: -0.02339068}
  m_LocalPosition: {x: -0.00000005232025, y: 0.0014813766, z: 0.10034632}
  m_LocalScale: {x: 1.000001, y: 1.0000004, z: 1.0000012}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4011830325223328}
  - {fileID: 4818860062953090}
  m_Father: {fileID: 4339650069157304}
  m_LocalEulerAnglesHint: {x: -23.836, y: 13.878, z: -185.702}
--- !u!1 &1662473033786902
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4599550948850218}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4599550948850218
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1662473033786902}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4014445583392948}
  m_Father: {fileID: 4528454017576386}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1667273197899182
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4872750053369334}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4872750053369334
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1667273197899182}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4850721406318936}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1673834343770268
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4979023462863312}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4979023462863312
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1673834343770268}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4880024886022224}
  m_Father: {fileID: 4523161272351254}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1675789146754424
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4553423104053020}
  m_Layer: 0
  m_Name: BHandL_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4553423104053020
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1675789146754424}
  serializedVersion: 2
  m_LocalRotation: {x: -0.030392632, y: 0.03742221, z: 0.010928568, w: 0.9987775}
  m_LocalPosition: {x: -0.021425575, y: -0.017793447, z: -0.28784427}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4581799003241716}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1678748489185812
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4818605159667876}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4818605159667876
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1678748489185812}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4991597019885528}
  m_Father: {fileID: 4797175212013222}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1696488269751266
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4535141322402038}
  m_Layer: 0
  m_Name: BFingerL
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4535141322402038
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1696488269751266}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0355539, y: 0.007480397, z: -0.25942007, w: 0.9650809}
  m_LocalPosition: {x: 0.004475086, y: 0.0051815724, z: -0.031380042}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4759688349202108}
  m_Father: {fileID: 4346254202617646}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1699829702198990
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4666171133201978}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4666171133201978
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1699829702198990}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4081799783749526}
  m_Father: {fileID: 4251146063190398}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1700043708054816
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4405510196472626}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4405510196472626
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1700043708054816}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4002762585685290}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1700319459348414
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4934212092515022}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4934212092515022
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1700319459348414}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4532298462012058}
  m_Father: {fileID: 4111863530387508}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1700686541291400
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4111863530387508}
  m_Layer: 0
  m_Name: Fur (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4111863530387508
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1700686541291400}
  serializedVersion: 2
  m_LocalRotation: {x: -0.000000065192566, y: 3.5527128e-15, z: -5.1583724e-14, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: -0.097}
  m_LocalScale: {x: 1.0000017, y: 1.0000002, z: 1.0000015}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4934212092515022}
  - {fileID: 4287901234303708}
  m_Father: {fileID: 4042174603628926}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1700959182940232
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4586992301261024}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4586992301261024
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1700959182940232}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4702461193410900}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1707880395946324
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4251496273052258}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4251496273052258
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1707880395946324}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4035498698195584}
  m_Father: {fileID: 4163549216908622}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1722327032448926
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4336793997347296}
  m_Layer: 0
  m_Name: BHeadLower
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4336793997347296
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1722327032448926}
  serializedVersion: 2
  m_LocalRotation: {x: -0.010446419, y: 0.70702964, z: -0.010446446, w: 0.7070296}
  m_LocalPosition: {x: 0.0000000031398641, y: 0.043178335, z: -0.003986188}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4548960234035166}
  - {fileID: 4144516006307354}
  - {fileID: 4396765125847402}
  - {fileID: 4225051517241952}
  - {fileID: 4163549216908622}
  m_Father: {fileID: 4292628634092992}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1733542456820048
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4774993003118418}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4774993003118418
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1733542456820048}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4797175212013222}
  m_Father: {fileID: 4250497048755668}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1737366306262448
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4927437926029020}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4927437926029020
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1737366306262448}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4748188627754220}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1739307576729686
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4850721406318936}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4850721406318936
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1739307576729686}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4872750053369334}
  m_Father: {fileID: 4521574549823736}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1748205007423858
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4818860062953090}
  - component: {fileID: 137429125442439822}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4818860062953090
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1748205007423858}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4457460715478288}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137429125442439822
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1748205007423858}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4011830325223328}
  - {fileID: 4108088491198552}
  - {fileID: 4405081626734872}
  - {fileID: 4087729404986920}
  - {fileID: 4188517000829842}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4011830325223328}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1751764841043462
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4292628634092992}
  m_Layer: 0
  m_Name: BSpine_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4292628634092992
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1751764841043462}
  serializedVersion: 2
  m_LocalRotation: {x: 0.02225773, y: -0.0000000018232175, z: 4.0590736e-11, w: 0.9997523}
  m_LocalPosition: {x: -6.730172e-14, y: -6.661338e-17, z: -0.34913233}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4339650069157304}
  - {fileID: 4336793997347296}
  m_Father: {fileID: 4181870408078312}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1755830092317472
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4856184045208054}
  m_Layer: 0
  m_Name: BArmL
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4856184045208054
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1755830092317472}
  serializedVersion: 2
  m_LocalRotation: {x: 0.90997034, y: -0.25429386, z: 0.29384634, w: -0.1447168}
  m_LocalPosition: {x: -0.109501295, y: -0.01467076, z: -0.26328593}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4428934940575620}
  m_Father: {fileID: 4526575612360388}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1757785264345962
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4579897501458906}
  - component: {fileID: 137833791706882476}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4579897501458906
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1757785264345962}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4044845356684678}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137833791706882476
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1757785264345962}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4695065046644840}
  - {fileID: 4523161272351254}
  - {fileID: 4979023462863312}
  - {fileID: 4880024886022224}
  - {fileID: 4451211804789614}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4695065046644840}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1764781298960918
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4187539100677642}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4187539100677642
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1764781298960918}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4433452175046342}
  m_Father: {fileID: 4851599626611798}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1772766115300344
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4433452175046342}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4433452175046342
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1772766115300344}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4823636708418956}
  m_Father: {fileID: 4187539100677642}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1778904714055420
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4346254202617646}
  m_Layer: 0
  m_Name: BHandL_1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4346254202617646
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1778904714055420}
  serializedVersion: 2
  m_LocalRotation: {x: -0.5544269, y: 0.18109925, z: 0.65338135, w: -0.4826041}
  m_LocalPosition: {x: -0.008402936, y: -0.0041059586, z: -0.22795919}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4535141322402038}
  - {fileID: 4581799003241716}
  m_Father: {fileID: 4749842592694542}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1780075551911554
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4019553886114204}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4019553886114204
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1780075551911554}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4610367542880568}
  m_Father: {fileID: 4974066538998472}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1783265908268476
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4548960234035166}
  m_Layer: 0
  m_Name: BSpine_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4548960234035166
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1783265908268476}
  serializedVersion: 2
  m_LocalRotation: {x: 0.50733215, y: -0.49255872, z: 0.5073322, w: -0.49255866}
  m_LocalPosition: {x: 0.00000003473502, y: 0.43242973, z: -0.06802793}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4336793997347296}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1793639918796652
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4086673973257362}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4086673973257362
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1793639918796652}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4702461193410900}
  m_Father: {fileID: 4680961590319614}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1796336862451664
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4615065295786294}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4615065295786294
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1796336862451664}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4014445583392948}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1796529179273648
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4656393384509608}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4656393384509608
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1796529179273648}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4468503701823024}
  m_Father: {fileID: 4280974406627108}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1800803205903706
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4428496267986074}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4428496267986074
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1800803205903706}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4002762585685290}
  m_Father: {fileID: 4664729736593348}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1807998519535662
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4168341642836460}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4168341642836460
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1807998519535662}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4874597137190332}
  m_Father: {fileID: 4567636475282416}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1812124366453702
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4191461060998378}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4191461060998378
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1812124366453702}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4511003725944658}
  m_Father: {fileID: 4576078118765600}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1816884890224526
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4774099960004502}
  m_Layer: 0
  m_Name: Fur (17)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4774099960004502
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1816884890224526}
  serializedVersion: 2
  m_LocalRotation: {x: 0.65293133, y: 0.03424662, z: -0.7560643, w: -0.02957474}
  m_LocalPosition: {x: -0, y: -0.133, z: -0.418}
  m_LocalScale: {x: 0.9600002, y: 0.92905414, z: 1.0716712}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4251146063190398}
  - {fileID: 4392488785181980}
  m_Father: {fileID: 4181870408078312}
  m_LocalEulerAnglesHint: {x: 0.754, y: -81.661, z: -185.132}
--- !u!1 &1817117540687570
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4300056620940062}
  - component: {fileID: 33614764630195712}
  - component: {fileID: 23793345177001516}
  m_Layer: 0
  m_Name: Shield_Inner
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4300056620940062
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1817117540687570}
  serializedVersion: 2
  m_LocalRotation: {x: -0.000000021855698, y: -0.00000002980232, z: 0.000000010536711, w: 1}
  m_LocalPosition: {x: -4.748108e-17, y: 0, z: -2.830093e-24}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4342970549100840}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &33614764630195712
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1817117540687570}
  m_Mesh: {fileID: 0}
--- !u!23 &23793345177001516
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1817117540687570}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &1817354394045568
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4250497048755668}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4250497048755668
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1817354394045568}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4774993003118418}
  m_Father: {fileID: 4047537788991022}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1819783924436352
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4002762585685290}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4002762585685290
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1819783924436352}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4405510196472626}
  m_Father: {fileID: 4428496267986074}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1834155362885864
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4930379291246824}
  m_Layer: 0
  m_Name: BHandR
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4930379291246824
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1834155362885864}
  serializedVersion: 2
  m_LocalRotation: {x: -0.12473896, y: -0.05090003, z: -0.0071745683, w: 0.9908572}
  m_LocalPosition: {x: -0.00020904189, y: 0.003199108, z: -0.21814069}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4956526269267272}
  m_Father: {fileID: 4171612398917912}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1834267693003442
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4451211804789614}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4451211804789614
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1834267693003442}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4880024886022224}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1858482793027450
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4521574549823736}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4521574549823736
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1858482793027450}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4850721406318936}
  m_Father: {fileID: 4033321250539968}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1859486598485088
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4367958959596264}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4367958959596264
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1859486598485088}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4608861758549540}
  m_Father: {fileID: 4897594588910752}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1865201152847992
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4392488785181980}
  - component: {fileID: 137049108032649474}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4392488785181980
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1865201152847992}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4774099960004502}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137049108032649474
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1865201152847992}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4251146063190398}
  - {fileID: 4666171133201978}
  - {fileID: 4081799783749526}
  - {fileID: 4773512029215718}
  - {fileID: 4200946280068458}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4251146063190398}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1867275047691260
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4934403202769354}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4934403202769354
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1867275047691260}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4294487224796136}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1875434889706954
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4051562232917774}
  - component: {fileID: 137619686313568284}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4051562232917774
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1875434889706954}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4286066156482418}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137619686313568284
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1875434889706954}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4028621993437382}
  - {fileID: 4960120799512624}
  - {fileID: 4505141303637378}
  - {fileID: 4203414142884658}
  - {fileID: 4304227809504874}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4028621993437382}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1875940159217996
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4511003725944658}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4511003725944658
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1875940159217996}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4844982883260422}
  m_Father: {fileID: 4191461060998378}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1876500344757110
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4014445583392948}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4014445583392948
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1876500344757110}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4615065295786294}
  m_Father: {fileID: 4599550948850218}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1879888523460886
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4780643320262092}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4780643320262092
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1879888523460886}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4128175958769604}
  m_Father: {fileID: 4225051517241952}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1884781302547392
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4200946280068458}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4200946280068458
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1884781302547392}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4773512029215718}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1889522965547622
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4290619602454556}
  - component: {fileID: 137849509367581648}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4290619602454556
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1889522965547622}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4821857296403344}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137849509367581648
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1889522965547622}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4336261565076342}
  - {fileID: 4528454017576386}
  - {fileID: 4599550948850218}
  - {fileID: 4014445583392948}
  - {fileID: 4615065295786294}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4336261565076342}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1894170404564980
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4795775428874804}
  m_Layer: 0
  m_Name: BArm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4795775428874804
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1894170404564980}
  serializedVersion: 2
  m_LocalRotation: {x: 0.68740386, y: 0.6450697, z: -0.19784003, w: -0.26873845}
  m_LocalPosition: {x: 0.109501295, y: -0.014670778, z: -0.26328593}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4171612398917912}
  m_Father: {fileID: 4526575612360388}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1895395605479556
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4336261565076342}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4336261565076342
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1895395605479556}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4528454017576386}
  m_Father: {fileID: 4821857296403344}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1898046813014798
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4716354833891844}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4716354833891844
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1898046813014798}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4675879760924856}
  m_Father: {fileID: 4560366291790108}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1898391709963424
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4215628041141408}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4215628041141408
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1898391709963424}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4973464452936210}
  m_Father: {fileID: 4378804833134752}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1902360107192730
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4094751896365414}
  m_Layer: 0
  m_Name: BHandR_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4094751896365414
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1902360107192730}
  serializedVersion: 2
  m_LocalRotation: {x: 0.003172286, y: 0.01444156, z: 0.053852014, w: 0.9984395}
  m_LocalPosition: {x: 1.4210854e-16, y: -7.105427e-16, z: -0.23128319}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4000191470879700}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1906920772825302
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4019633597365154}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4019633597365154
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1906920772825302}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4267271160151408}
  m_Father: {fileID: 4610367542880568}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1907291385170576
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4314984982105186}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4314984982105186
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1907291385170576}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4500763252442198}
  m_Father: {fileID: 4143547789183638}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1910774201949230
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4704320618191648}
  - component: {fileID: 137817760091999242}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4704320618191648
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1910774201949230}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4252265682688120}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137817760091999242
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1910774201949230}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4347795491291248}
  - {fileID: 4873544723011614}
  - {fileID: 4090209565877424}
  - {fileID: 4294487224796136}
  - {fileID: 4934403202769354}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4347795491291248}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1915273190359572
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4575649307495020}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4575649307495020
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1915273190359572}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4431622895221308}
  m_Father: {fileID: 4844982883260422}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1922911309140398
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4146047430284390}
  m_Layer: 0
  m_Name: Fur (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4146047430284390
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1922911309140398}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0.31672534, z: 0, w: 0.9485173}
  m_LocalPosition: {x: -0, y: -0, z: -0.194}
  m_LocalScale: {x: 1.0000017, y: 1.0000002, z: 1.0000015}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4215891986620012}
  - {fileID: 4341363269006082}
  m_Father: {fileID: 4042174603628926}
  m_LocalEulerAnglesHint: {x: 0, y: -36.93, z: 0}
--- !u!1 &1925692075654338
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4148583181229762}
  - component: {fileID: 137349650967636646}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4148583181229762
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1925692075654338}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4670689883812756}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137349650967636646
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1925692075654338}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4068957565616730}
  - {fileID: 4485170664719034}
  - {fileID: 4394964925119168}
  - {fileID: 4281697796309706}
  - {fileID: 4855033002331920}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4068957565616730}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1927640776799356
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4997621953761668}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4997621953761668
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1927640776799356}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4315595056922964}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1929767358456138
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4431622895221308}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4431622895221308
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1929767358456138}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4575649307495020}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1930687792328432
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4144516006307354}
  m_Layer: 0
  m_Name: Fur (21)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4144516006307354
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1930687792328432}
  serializedVersion: 2
  m_LocalRotation: {x: 0.4962059, y: -0.5121265, z: 0.48760036, w: 0.50373816}
  m_LocalPosition: {x: -0.09, y: 0.361, z: 0.001}
  m_LocalScale: {x: 0.5663072, y: 0.40960887, z: 0.68386465}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4974066538998472}
  - {fileID: 4353468934613960}
  m_Father: {fileID: 4336793997347296}
  m_LocalEulerAnglesHint: {x: 92.07898, y: 62.078995, z: 152.066}
--- !u!1 &1931461171776120
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4855033002331920}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4855033002331920
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1931461171776120}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4281697796309706}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1934629329013092
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4894495637402798}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4894495637402798
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1934629329013092}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4478419107316534}
  m_Father: {fileID: 4487077738355358}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1939657753378350
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4394964925119168}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4394964925119168
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1939657753378350}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4281697796309706}
  m_Father: {fileID: 4485170664719034}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1940513403463626
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4033321250539968}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4033321250539968
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1940513403463626}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4521574549823736}
  m_Father: {fileID: 4737859875262580}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1947101460141084
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4853274235671810}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4853274235671810
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1947101460141084}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4499389407567348}
  m_Father: {fileID: 4084536281306498}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1954459913857032
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4689688334605344}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4689688334605344
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1954459913857032}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4708979716387818}
  m_Father: {fileID: 4040496962701982}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1955756111537376
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4640079639001080}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4640079639001080
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1955756111537376}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4491584567774784}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1956524846912698
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4744188912455028}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4744188912455028
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1956524846912698}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4502208971878302}
  m_Father: {fileID: 4523420869360922}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1962110268910418
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4491584567774784}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4491584567774784
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1962110268910418}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4640079639001080}
  m_Father: {fileID: 4749490994129548}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1967750299519116
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4958423197816966}
  - component: {fileID: 137473639919790130}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4958423197816966
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1967750299519116}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4897594588910752}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137473639919790130
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1967750299519116}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4367958959596264}
  - {fileID: 4608861758549540}
  - {fileID: 4155222149510626}
  - {fileID: 4649400960625530}
  - {fileID: 4414919988423118}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4367958959596264}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1972588853503924
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4675879760924856}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4675879760924856
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1972588853503924}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4716354833891844}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1974459323249546
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4203414142884658}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4203414142884658
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1974459323249546}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4304227809504874}
  m_Father: {fileID: 4505141303637378}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1976224192700744
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4000386371625086}
  - component: {fileID: 137417930149729574}
  m_Layer: 0
  m_Name: Fur_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4000386371625086
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1976224192700744}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -2.795446e-17, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4084536281306498}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &137417930149729574
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1976224192700744}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 0}
  m_Bones:
  - {fileID: 4853274235671810}
  - {fileID: 4499389407567348}
  - {fileID: 4573669960452038}
  - {fileID: 4269581396158818}
  - {fileID: 4583133167190564}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4853274235671810}
  m_AABB:
    m_Center: {x: 0.000000018626451, y: -0.00069778785, z: -0.46830836}
    m_Extent: {x: 0.09854455, y: 0.103648186, z: 0.4710107}
  m_DirtyAABB: 0
--- !u!1 &1981916436872524
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4040496962701982}
  m_Layer: 0
  m_Name: BFurRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4040496962701982
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1981916436872524}
  serializedVersion: 2
  m_LocalRotation: {x: 0.71094024, y: -0.000000024844745, z: 0.000000024576087, w: 0.7032525}
  m_LocalPosition: {x: -2.795446e-17, y: -0.0117572425, z: -0.0043936903}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4689688334605344}
  m_Father: {fileID: 4564079268094022}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1985633035086744
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4327838943989390}
  m_Layer: 0
  m_Name: Skeleton
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4327838943989390
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1985633035086744}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4687003068740068}
  m_Father: {fileID: 4053762796047906}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1986190318600298
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4844982883260422}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4844982883260422
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1986190318600298}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4575649307495020}
  m_Father: {fileID: 4511003725944658}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1990020237190058
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4424136776440118}
  m_Layer: 0
  m_Name: BFur_6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4424136776440118
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1990020237190058}
  serializedVersion: 2
  m_LocalRotation: {x: -8.6736174e-19, y: -1.6595311e-23, z: -6.617445e-24, w: 1}
  m_LocalPosition: {x: 3.0440245e-24, y: -1.8318679e-17, z: -0.23912726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4739797413204974}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1991966229899602
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4797175212013222}
  m_Layer: 0
  m_Name: BFur_4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4797175212013222
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1991966229899602}
  serializedVersion: 2
  m_LocalRotation: {x: 0.011109054, y: -8.2799556e-10, z: 9.1988145e-12, w: 0.9999383}
  m_LocalPosition: {x: 8.838326e-17, y: -6.5503156e-17, z: -0.2339866}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4818605159667876}
  m_Father: {fileID: 4774993003118418}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1995585185246166
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4739797413204974}
  m_Layer: 0
  m_Name: BFur_5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4739797413204974
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1995585185246166}
  serializedVersion: 2
  m_LocalRotation: {x: -4.440892e-16, y: 6.7208425e-24, z: 6.617445e-24, w: 1}
  m_LocalPosition: {x: 4.5455485e-15, y: -9.992007e-18, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4424136776440118}
  m_Father: {fileID: 4660225685297262}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1996785813268274
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4035498698195584}
  m_Layer: 0
  m_Name: BFur_3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4035498698195584
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1996785813268274}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011109054, y: 8.277911e-10, z: 9.196544e-12, w: 0.9999383}
  m_LocalPosition: {x: 3.7959624e-10, y: 0.005198419, z: -0.23392884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4378804833134752}
  m_Father: {fileID: 4251496273052258}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
