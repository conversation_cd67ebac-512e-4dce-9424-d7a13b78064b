#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using UnityEngine.Rendering;
using Stylo.Flux.Universal;

namespace Stylo.Flux.Editor
{
    /// <summary>
    /// Quick test menu items for Flux motion vector system
    /// </summary>
    public static class FluxQuickTest
    {
        [MenuItem("Tools/Flux/Quick Test - Check Motion Vector System", false, 500)]
        public static void QuickTestMotionVectors()
        {
            Debug.Log("=== FLUX QUICK TEST ===");

            // Check URP
            var renderPipelineAsset = UnityEngine.Rendering.GraphicsSettings.defaultRenderPipeline;
            if (renderPipelineAsset == null)
            {
                Debug.LogError("❌ No URP Render Pipeline Asset! Go to Edit → Project Settings → Graphics");
                return;
            }
            Debug.Log($"✅ URP Asset: {renderPipelineAsset.name}");

            // Find FluxEffect
            var volumes = Object.FindObjectsByType<Volume>(FindObjectsSortMode.None);
            FluxEffect? fluxEffect = null;

            foreach (var volume in volumes)
            {
                if (volume.profile != null && volume.profile.TryGet<FluxEffect>(out fluxEffect))
                {
                    Debug.Log($"✅ Found FluxEffect in Volume: {volume.name}");
                    break;
                }
            }

            if (fluxEffect == null)
            {
                Debug.LogError("❌ No FluxEffect found! Create a Volume with Flux Effect component.");
                return;
            }

            // Check key values
            Debug.Log($"📊 Key Values:");
            Debug.Log($"   Effect Intensity: {fluxEffect.EffectIntensity.value}");
            Debug.Log($"   Base Noise: {fluxEffect.ReprojectBaseNoise.value}");
            Debug.Log($"   Length Influence: {fluxEffect.ReprojectLengthInfluence.value}");
            Debug.Log($"   Motion Amplification: {fluxEffect.MotionAmplification.value}");
            Debug.Log($"   Visualize Motion Vectors: {fluxEffect.VisualizeMotionVectors.value}");

            // Calculate DoReprojection
            float reprojectPercent = fluxEffect.ReprojectBaseNoise.value * fluxEffect.EffectIntensity.value;
            float reprojectLengthInfluence = fluxEffect.ReprojectLengthInfluence.value * fluxEffect.EffectIntensity.value;
            bool doReprojection = (reprojectPercent > 0f || reprojectLengthInfluence > 0f ||
                                 fluxEffect.MotionAmplification.value > 0f ||
                                 fluxEffect.VisualizeMotionVectors.value);

            Debug.Log($"📊 DoReprojection = {doReprojection}");

            if (!doReprojection)
            {
                Debug.LogError("❌ Motion vectors DISABLED! Need at least one parameter > 0");
            }

            // Check cameras
            var cameras = Object.FindObjectsByType<Camera>(FindObjectsSortMode.None);
            Debug.Log($"📊 Found {cameras.Length} cameras:");

            foreach (var camera in cameras)
            {
                bool isSceneView = camera.cameraType == CameraType.SceneView;
                Debug.Log($"   {camera.name}: {camera.cameraType} {(isSceneView ? "❌ EXCLUDED" : "✅ OK")}");
            }

            Debug.Log($"📊 Play Mode: {Application.isPlaying}");

            Debug.Log("=== QUICK TEST COMPLETE ===");
            Debug.Log("💡 If DoReprojection = false, use 'Force Enable Motion Vectors' below");
        }

        [MenuItem("Tools/Flux/Quick Test - Force Enable Motion Vectors", false, 501)]
        public static void ForceEnableMotionVectors()
        {
            var volumes = Object.FindObjectsByType<Volume>(FindObjectsSortMode.None);

            foreach (var volume in volumes)
            {
                if (volume.profile != null && volume.profile.TryGet<FluxEffect>(out var fluxEffect))
                {
                    // Force enable by setting Base Noise
                    fluxEffect.ReprojectBaseNoise.value = 0.1f;
                    fluxEffect.ReprojectBaseNoise.overrideState = true;

                    // Also enable visualization
                    fluxEffect.VisualizeMotionVectors.value = true;
                    fluxEffect.VisualizeMotionVectors.overrideState = true;

                    EditorUtility.SetDirty(volume.profile);

                    Debug.Log("✅ FORCED MOTION VECTORS ON:");
                    Debug.Log("   - Base Noise = 0.1");
                    Debug.Log("   - Motion Vector Visualization = true");
                    Debug.Log("💡 Enter Play Mode and test in Game View!");
                    return;
                }
            }

            Debug.LogError("❌ No FluxEffect found to modify");
        }

        [MenuItem("Tools/Flux/Quick Test - Load Enhanced Preset", false, 502)]
        public static void LoadEnhancedPreset()
        {
            // Find the Enhanced Preset
            string[] guids = AssetDatabase.FindAssets("Enhanced Pixel Trailing Demo t:FluxPreset");

            if (guids.Length == 0)
            {
                Debug.LogError("❌ Enhanced Pixel Trailing Demo preset not found!");
                return;
            }

            string path = AssetDatabase.GUIDToAssetPath(guids[0]);
            var preset = AssetDatabase.LoadAssetAtPath<FluxPreset>(path);

            if (preset == null)
            {
                Debug.LogError("❌ Could not load Enhanced preset!");
                return;
            }

            // Find Volume and apply preset
            var volumes = Object.FindObjectsByType<Volume>(FindObjectsSortMode.None);

            foreach (var volume in volumes)
            {
                if (volume.profile != null && volume.profile.TryGet<FluxEffect>(out var fluxEffect))
                {
                    preset.ApplyToURP(fluxEffect);
                    EditorUtility.SetDirty(volume.profile);

                    Debug.Log($"✅ Applied Enhanced Preset to Volume: {volume.name}");
                    Debug.Log("💡 Enter Play Mode and test in Game View!");
                    return;
                }
            }

            Debug.LogError("❌ No Volume with FluxEffect found to apply preset");
        }
    }
}
#endif
