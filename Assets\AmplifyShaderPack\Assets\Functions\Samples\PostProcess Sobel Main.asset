%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 78b2425a2284af743826c689403a4924, type: 3}
  m_Name: PostProcess Sobel Main
  m_EditorClassIdentifier: 
  m_functionInfo: "// Made with Amplify Shader Editor v1.9.1.5\n// Available at the
    Unity Asset Store - http://u3d.as/y3X \n/*ASEBEGIN\nVersion=19105\nNode;AmplifyShaderEditor.CommentaryNode;114;-1194.796,-573.1996;Inherit;False;873.0001;357.2;Top
    Left;5;172;148;139;166;157;Top Left;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;119;-1193.293,932.6716;Inherit;False;879.4;340.4001;Bottom;5;176;152;143;169;33;Bottom;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;124;-263.1313,-537.6378;Inherit;False;1548.802;735.6005;X;14;134;133;81;180;183;83;84;85;80;182;190;189;187;107;X;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;192;-2272.307,758.2275;Inherit;False;774.647;975.6824;Setup
    Getters;12;3;2;22;1;21;165;147;138;156;164;160;4;Setup Getters;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;123;-222.4309,264.9632;Inherit;False;1485.5;696.699;Y;14;109;137;135;191;100;186;184;188;185;181;97;96;101;99;Y;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;115;-1188.797,-196.9014;Inherit;False;858.9;344.0001;Left;5;173;149;140;60;158;Left;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;120;-1202.489,1299.398;Inherit;False;898.9001;340.5999;Top
    Right;5;177;153;144;170;161;Top Right;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;121;-1200.811,1666.522;Inherit;False;901.6;339.8;Right;5;178;154;145;70;162;Right;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;122;-1193.299,2024;Inherit;False;888.0201;357.5313;Bottom
    Right;5;179;155;146;171;163;Bottom Right;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;117;-1190.868,160.2888;Inherit;False;862.1239;372.4885;Bottom
    Left;5;174;150;141;167;159;Bottom Left;1,1,1,1;0;0\nNode;AmplifyShaderEditor.CommentaryNode;118;-1189.687,558.0334;Inherit;False;869.3;340.3002;Top;5;175;151;142;168;63;Top;1,1,1,1;0;0\nNode;AmplifyShaderEditor.FunctionInput;2;-2222.307,1203.203;Inherit;False;StepX;1;0;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;3;-2125.295,1514.9;Inherit;False;StepY;1;1;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;1;-2143.105,931.3004;Inherit;False;Screen;9;3;False;1;0;SAMPLER2D;0.0;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.NegateNode;21;-2086.81,1144.513;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionInput;4;-2158.508,856.7979;Inherit;False;Center;2;2;False;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.NegateNode;22;-1966.801,1623.91;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;138;-1986.016,808.2275;Float;False;localCenter;-1;True;1;0;FLOAT2;0,0;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;164;-1970.059,1445.131;Float;False;localStepY;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;156;-1928.615,1061.832;Float;False;localNegStepX;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;165;-1756.662,1579.93;Float;False;localNegStepY;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;147;-1959.017,893.5283;Float;False;localScreen;-1;True;1;0;SAMPLER2D;0.0;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;160;-2068.912,1239.834;Float;False;localStepX;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;157;-1166.814,-515.7673;Inherit;False;156;localNegStepX;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;182;-116.7548,-352.1664;Inherit;False;173;localLeft;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;181;-177,329.5998;Inherit;False;172;localTopLeft;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;188;-190.5513,573.0296;Inherit;False;177;localTopRight;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;101;481.8685,576.0619;Inherit;False;6;6;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;189;-115.7997,-52.50034;Inherit;False;178;localRight;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;190;-115.7997,75.49931;Inherit;False;179;localBottomRight;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;185;-161,441.5998;Inherit;False;175;localTop;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;187;-115.7997,-148.5002;Inherit;False;177;localTopRight;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;186;-165.0006,764.8002;Inherit;False;176;localBottom;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleDivideOpNode;135;684.2972,612.6976;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;180;-115.7997,-452.5008;Inherit;False;172;localTopLeft;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.ScaleNode;80;188.2014,-356.5002;Inherit;False;2;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;183;-115.7997,-244.5;Inherit;False;174;localBottomLeft;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.NegateNode;84;172.2014,75.49931;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.NegateNode;83;172.2014,-148.5002;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.NegateNode;96;105.5693,327.9631;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;184;-178.4518,670.734;Inherit;False;174;localBottomLeft;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.ScaleNode;97;108.9686,421.5625;Inherit;False;-2;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;191;-147.8483,863.5322;Inherit;False;179;localBottomRight;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.NegateNode;99;127.3681,589.3631;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleDivideOpNode;133;940.9847,-386.1715;Inherit;False;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SqrtOpNode;111;1478.465,163.9632;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;134;720.8864,-243.1718;Float;False;Constant;_Float4;Float
    4;8;0;Create;True;0;0;0;False;0;False;6;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;137;469.7968,788.3988;Float;False;Constant;_Float5;Float
    5;8;0;Create;True;0;0;0;False;0;False;6;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;81;731.8671,-442.3373;Inherit;False;6;6;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;4;FLOAT;0;False;5;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.DynamicAppendNode;113;1651.865,130.2647;Inherit;False;FLOAT3;4;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.ScaleNode;100;119.7679,753.0599;Inherit;False;2;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.ScaleNode;85;172.2014,-52.50034;Inherit;False;-2;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;107;1106.871,-401.8369;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleAddOpNode;110;1333.865,125.6628;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.SimpleMultiplyOpNode;109;859.9699,593.1629;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionOutput;0;1822.804,132.4501;Inherit;False;True;-1;Output;0;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0\nNode;AmplifyShaderEditor.GetLocalVarNode;166;-1144.261,-446.667;Inherit;False;164;localStepY;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;139;-1150.013,-373.8709;Inherit;False;138;localCenter;1;0;OBJECT;;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.GetLocalVarNode;148;-1150.615,-301.171;Inherit;False;147;localScreen;1;0;OBJECT;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;172;-569.7526,-518.3664;Float;False;localTopLeft;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;158;-1163.806,-151.3671;Inherit;False;156;localNegStepX;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;60;-1101.654,-76.29;Float;False;Constant;_Float0;Float
    0;0;0;Create;True;0;0;0;False;0;False;0;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;140;-1143.009,-5.070602;Inherit;False;138;localCenter;1;0;OBJECT;;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.GetLocalVarNode;149;-1144.611,66.1292;Inherit;False;147;localScreen;1;0;OBJECT;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.GetLocalVarNode;159;-1167.502,225.3013;Inherit;False;156;localNegStepX;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;167;-1170.66,298.0331;Inherit;False;165;localNegStepY;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;141;-1151.402,370.5008;Inherit;False;138;localCenter;1;0;OBJECT;;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.GetLocalVarNode;150;-1150.518,444.5295;Inherit;False;147;localScreen;1;0;OBJECT;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.RangedFloatNode;63;-1104.618,600.5145;Float;False;Constant;_Float1;Float
    1;0;0;Create;True;0;0;0;False;0;False;0;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;168;-1141.151,671.6809;Inherit;False;164;localStepY;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;142;-1143.805,744.074;Inherit;False;138;localCenter;1;0;OBJECT;;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.GetLocalVarNode;151;-1143.707,815.7739;Inherit;False;147;localScreen;1;0;OBJECT;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.RangedFloatNode;33;-1094.522,977.9577;Float;False;Constant;_Float2;Float
    2;0;0;Create;True;0;0;0;False;0;False;0;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;169;-1153.387,1047.266;Inherit;False;165;localNegStepY;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;143;-1132.44,1118.363;Inherit;False;138;localCenter;1;0;OBJECT;;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.GetLocalVarNode;152;-1133.442,1191.063;Inherit;False;147;localScreen;1;0;OBJECT;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;176;-613.6802,979.063;Float;False;localBottom;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;161;-1134.335,1347.394;Inherit;False;160;localStepX;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;170;-1137.367,1415.49;Inherit;False;164;localStepY;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;144;-1141.434,1484.689;Inherit;False;138;localCenter;1;0;OBJECT;;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.GetLocalVarNode;153;-1144.536,1553.688;Inherit;False;147;localScreen;1;0;OBJECT;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;177;-599.6743,1342.195;Float;False;localTopRight;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;162;-1118.244,1713.988;Inherit;False;160;localStepX;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RangedFloatNode;70;-1083.244,1783.988;Float;False;Constant;_Float3;Float
    3;0;0;Create;True;0;0;0;False;0;False;0;0;0;0;0;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;145;-1127.244,1854.988;Inherit;False;138;localCenter;1;0;OBJECT;;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.GetLocalVarNode;154;-1126.244,1926.988;Inherit;False;147;localScreen;1;0;OBJECT;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.GetLocalVarNode;163;-1107.51,2077.726;Inherit;False;160;localStepX;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;171;-1130.299,2150;Inherit;False;165;localNegStepY;1;0;OBJECT;;False;1;FLOAT;0\nNode;AmplifyShaderEditor.GetLocalVarNode;146;-1111.3,2222;Inherit;False;138;localCenter;1;0;OBJECT;;False;1;FLOAT2;0\nNode;AmplifyShaderEditor.GetLocalVarNode;155;-1111.3,2294;Inherit;False;147;localScreen;1;0;OBJECT;;False;1;SAMPLER2D;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;179;-563.1534,2076.531;Float;False;localBottomRight;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;178;-586.0325,1712.921;Float;False;localRight;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;174;-580.7166,224.6464;Float;False;localBottomLeft;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;175;-619.4457,606.88;Float;False;localTop;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.RegisterLocalVarNode;173;-572.0659,-145.0668;Float;False;localLeft;-1;True;1;0;FLOAT;0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionNode;201;-944.5988,-514.2001;Inherit;False;PostProcess
    Sobel Intensity;0;;95;d269437fd7f8c6247b9aa1f04d3520ad;0;4;12;FLOAT;0;False;13;FLOAT;0;False;11;FLOAT2;0,0;False;1;SAMPLER2D;0,0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionNode;202;-907.5983,-149.5013;Inherit;False;PostProcess
    Sobel Intensity;0;;96;d269437fd7f8c6247b9aa1f04d3520ad;0;4;12;FLOAT;0;False;13;FLOAT;0;False;11;FLOAT2;0,0;False;1;SAMPLER2D;0,0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionNode;203;-892.1691,225.3885;Inherit;False;PostProcess
    Sobel Intensity;0;;97;d269437fd7f8c6247b9aa1f04d3520ad;0;4;12;FLOAT;0;False;13;FLOAT;0;False;11;FLOAT2;0,0;False;1;SAMPLER2D;0,0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionNode;204;-925.9859,607.3329;Inherit;False;PostProcess
    Sobel Intensity;0;;98;d269437fd7f8c6247b9aa1f04d3520ad;0;4;12;FLOAT;0;False;13;FLOAT;0;False;11;FLOAT2;0,0;False;1;SAMPLER2D;0,0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionNode;205;-900.0921,983.072;Inherit;False;PostProcess
    Sobel Intensity;0;;99;d269437fd7f8c6247b9aa1f04d3520ad;0;4;12;FLOAT;0;False;13;FLOAT;0;False;11;FLOAT2;0,0;False;1;SAMPLER2D;0,0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionNode;206;-895.8897,1347.496;Inherit;False;PostProcess
    Sobel Intensity;0;;100;d269437fd7f8c6247b9aa1f04d3520ad;0;4;12;FLOAT;0;False;13;FLOAT;0;False;11;FLOAT2;0,0;False;1;SAMPLER2D;0,0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionNode;207;-895.2446,1715.988;Inherit;False;PostProcess
    Sobel Intensity;0;;101;d269437fd7f8c6247b9aa1f04d3520ad;0;4;12;FLOAT;0;False;13;FLOAT;0;False;11;FLOAT2;0,0;False;1;SAMPLER2D;0,0,0,0;False;1;FLOAT;0\nNode;AmplifyShaderEditor.FunctionNode;208;-872.2999,2079;Inherit;False;PostProcess
    Sobel Intensity;0;;102;d269437fd7f8c6247b9aa1f04d3520ad;0;4;12;FLOAT;0;False;13;FLOAT;0;False;11;FLOAT2;0,0;False;1;SAMPLER2D;0,0,0,0;False;1;FLOAT;0\nWireConnection;21;0;2;0\nWireConnection;22;0;3;0\nWireConnection;138;0;4;0\nWireConnection;164;0;3;0\nWireConnection;156;0;21;0\nWireConnection;165;0;22;0\nWireConnection;147;0;1;0\nWireConnection;160;0;2;0\nWireConnection;101;0;96;0\nWireConnection;101;1;97;0\nWireConnection;101;2;99;0\nWireConnection;101;3;184;0\nWireConnection;101;4;100;0\nWireConnection;101;5;191;0\nWireConnection;135;0;101;0\nWireConnection;135;1;137;0\nWireConnection;80;0;182;0\nWireConnection;84;0;190;0\nWireConnection;83;0;187;0\nWireConnection;96;0;181;0\nWireConnection;97;0;185;0\nWireConnection;99;0;188;0\nWireConnection;133;0;81;0\nWireConnection;133;1;134;0\nWireConnection;111;0;110;0\nWireConnection;81;0;180;0\nWireConnection;81;1;80;0\nWireConnection;81;2;183;0\nWireConnection;81;3;83;0\nWireConnection;81;4;85;0\nWireConnection;81;5;84;0\nWireConnection;113;0;111;0\nWireConnection;113;1;111;0\nWireConnection;113;2;111;0\nWireConnection;100;0;186;0\nWireConnection;85;0;189;0\nWireConnection;107;0;133;0\nWireConnection;107;1;133;0\nWireConnection;110;0;107;0\nWireConnection;110;1;109;0\nWireConnection;109;0;135;0\nWireConnection;109;1;135;0\nWireConnection;0;0;113;0\nWireConnection;172;0;201;0\nWireConnection;176;0;205;0\nWireConnection;177;0;206;0\nWireConnection;179;0;208;0\nWireConnection;178;0;207;0\nWireConnection;174;0;203;0\nWireConnection;175;0;204;0\nWireConnection;173;0;202;0\nWireConnection;201;12;157;0\nWireConnection;201;13;166;0\nWireConnection;201;11;139;0\nWireConnection;201;1;148;0\nWireConnection;202;12;158;0\nWireConnection;202;13;60;0\nWireConnection;202;11;140;0\nWireConnection;202;1;149;0\nWireConnection;203;12;159;0\nWireConnection;203;13;167;0\nWireConnection;203;11;141;0\nWireConnection;203;1;150;0\nWireConnection;204;12;63;0\nWireConnection;204;13;168;0\nWireConnection;204;11;142;0\nWireConnection;204;1;151;0\nWireConnection;205;12;33;0\nWireConnection;205;13;169;0\nWireConnection;205;11;143;0\nWireConnection;205;1;152;0\nWireConnection;206;12;161;0\nWireConnection;206;13;170;0\nWireConnection;206;11;144;0\nWireConnection;206;1;153;0\nWireConnection;207;12;162;0\nWireConnection;207;13;70;0\nWireConnection;207;11;145;0\nWireConnection;207;1;154;0\nWireConnection;208;12;163;0\nWireConnection;208;13;171;0\nWireConnection;208;11;146;0\nWireConnection;208;1;155;0\nASEEND*/\n//CHKSM=FC7C6683C8745700BB58E38B2F2B57259E2B1C57"
  m_functionName: 
  m_description: Amplify Shader Pack Example
  m_additionalIncludes:
    m_additionalIncludes: []
    m_outsideIncludes: []
  m_additionalPragmas:
    m_additionalPragmas: []
    m_outsidePragmas: []
  m_additionalDirectives:
    m_validData: 0
    m_isDirty: 0
    m_moduleName: ' Additional Directives'
    m_independentModule: 1
    m_customEdited: 0
    m_additionalDirectives: []
    m_shaderFunctionDirectives: []
    m_nativeDirectives: []
    m_nativeDirectivesIndex: -1
    m_nativeDirectivesFoldout: 0
    m_directivesSaveItems: []
  m_nodeCategory: 0
  m_headerStyle: 0
  m_headerColor: {r: 1, g: 0.4, b: 0, a: 1}
  m_customNodeCategory: Amplify Shader Pack
  m_previewPosition: 0
  m_hidden: 0
  m_url: 
