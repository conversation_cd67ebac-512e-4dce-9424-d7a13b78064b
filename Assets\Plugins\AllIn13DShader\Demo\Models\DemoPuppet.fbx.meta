fileFormatVersion: 2
guid: b857f63a7556d4e48b6a61a5ffd4696f
ModelImporter:
  serializedVersion: 21300
  internalIDToNameTable: []
  externalObjects:
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: skin
    second: {fileID: 2100000, guid: 1f012968808652d458b836693082c597, type: 2}
  materials:
    materialImportMode: 2
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    removeConstantScaleCurves: 1
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 0.4
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    nodeNameCollisionStrategy: 1
    fileIdsGeneration: 2
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    bakeAxisConversion: 0
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    optimizeBones: 0
    meshOptimizationFlags: -1
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVMarginMethod: 1
    secondaryUVMinLightmapResolution: 40
    secondaryUVMinObjectScale: 1
    secondaryUVPackMargin: 4
    useFileScale: 1
    strictVertexDataChecks: 0
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human: []
    skeleton:
    - name: DemoPuppet(Clone)
      parentName: 
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Root
      parentName: DemoPuppet(Clone)
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0.7071068, y: 0, z: 0, w: 0.7071067}
      scale: {x: 100, y: 100, z: 100}
    - name: LeftFootCtrl
      parentName: Root
      position: {x: 0.0008168352, y: 0.0005952398, z: -0.0007728191}
      rotation: {x: 0.49999988, y: 0.50000006, z: 0.5000002, w: 0.49999988}
      scale: {x: 0.99999994, y: 0.99999976, z: 0.9999999}
    - name: LeftHeelRoll
      parentName: LeftFootCtrl
      position: {x: 0.00028341025, y: 0.0007196055, z: 0.000088961715}
      rotation: {x: -0.50751567, y: 0.5082331, z: 0.49190146, w: 0.4920976}
      scale: {x: 1, y: 0.99999994, z: 0.9999998}
    - name: LeftToeRoll
      parentName: LeftHeelRoll
      position: {x: -0.00011465684, y: 0.0012277318, z: -0.000004172113}
      rotation: {x: -0.015744422, y: 0.0020793797, z: 0.9946246, w: -0.10232149}
      scale: {x: 0.99999875, y: 0.99999994, z: 1.0000001}
    - name: LeftFootIK
      parentName: LeftToeRoll
      position: {x: -0.0000072857033, y: 0.0009660869, z: -0.0007202}
      rotation: {x: 0.9461289, y: -0.021870876, z: -0.07550268, w: 0.31410384}
      scale: {x: 0.9999999, y: 0.9999997, z: 0.99999964}
    - name: LeftFootIK_end
      parentName: LeftFootIK
      position: {x: -2.7474011e-11, y: 0.0012050173, z: -4.0978197e-11}
      rotation: {x: -0.00000004400499, y: 0.000000014901161, z: 0.000000063329935,
        w: 1}
      scale: {x: 0.99999994, y: 1, z: 1}
    - name: LeftFootIK_end_end
      parentName: LeftFootIK_end
      position: {x: 0, y: 0.001205017, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftFootRollCtrl
      parentName: LeftFootCtrl
      position: {x: 6.473559e-11, y: -5.810507e-10, z: 9.0855466e-12}
      rotation: {x: -0.50000006, y: -0.49999994, z: -0.49999985, w: 0.5000002}
      scale: {x: 1, y: 1, z: 0.9999999}
    - name: LeftFootRollCtrl_end
      parentName: LeftFootRollCtrl
      position: {x: 3.7418457e-11, y: 0.0011710668, z: -6.122029e-11}
      rotation: {x: 0.000000012309316, y: -0.00000005960471, z: 4.895104e-15, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftFootRollCtrl_end_end
      parentName: LeftFootRollCtrl_end
      position: {x: 0, y: 0.0011710665, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftKneeCtrl
      parentName: LeftFootCtrl
      position: {x: -0.0024718319, y: -0.0023468034, z: -0.00010354817}
      rotation: {x: 0.70710695, y: -0.000000030908613, z: 0.7071066, w: 0.00000003090863}
      scale: {x: 1, y: 0.9999999, z: 1}
    - name: LeftKneeCtrl_end
      parentName: LeftKneeCtrl
      position: {x: -4.222034e-11, y: 0.0013472625, z: -9.125362e-11}
      rotation: {x: -0.00000006403722, y: -6.7428735e-15, z: -0.00000008848258, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftKneeCtrl_end_end
      parentName: LeftKneeCtrl_end
      position: {x: 0, y: 0.0013472626, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightFootCtrl
      parentName: Root
      position: {x: -0.0008300819, y: 0.00059524016, z: -0.00077281904}
      rotation: {x: 0.49999988, y: 0.50000006, z: 0.5000002, w: 0.49999988}
      scale: {x: 0.99999994, y: 0.99999976, z: 0.9999999}
    - name: RightHeelRoll
      parentName: RightFootCtrl
      position: {x: 0.00028341013, y: 0.0007196055, z: -0.00008896161}
      rotation: {x: -0.5076816, y: 0.5078985, z: 0.49173155, w: 0.49244145}
      scale: {x: 0.99999976, y: 0.9999998, z: 0.9999998}
    - name: RightToeRoll
      parentName: RightHeelRoll
      position: {x: 0.00011469157, y: 0.0012277318, z: 0.0000030730612}
      rotation: {x: -0.015667995, y: -0.0011518252, z: 0.99462587, w: 0.10233566}
      scale: {x: 1.0000061, y: 1.0000002, z: 1.0000001}
    - name: RightFootIK
      parentName: RightToeRoll
      position: {x: 0.0000072855173, y: 0.0009660871, z: -0.0007202}
      rotation: {x: 0.948112, y: 0.011444996, z: 0.044093173, w: 0.3146561}
      scale: {x: 1.0000001, y: 0.99999994, z: 1.0000007}
    - name: RightFootIK_end
      parentName: RightFootIK
      position: {x: -5.681068e-11, y: 0.0012050173, z: -5.215406e-11}
      rotation: {x: 0.0000000033760443, y: -0.00000023376195, z: -0.0000000074505797,
        w: 1}
      scale: {x: 1.0000001, y: 1, z: 1}
    - name: RightFootIK_end_end
      parentName: RightFootIK_end
      position: {x: 0, y: 0.0012050171, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightFootRollCtrl
      parentName: RightFootCtrl
      position: {x: -1.0874668e-10, y: -6.1908334e-10, z: 9.085581e-12}
      rotation: {x: -0.50000006, y: -0.49999994, z: -0.49999985, w: 0.5000002}
      scale: {x: 1, y: 1, z: 0.9999999}
    - name: RightFootRollCtrl_end
      parentName: RightFootRollCtrl
      position: {x: -8.1790914e-11, y: 0.0011710667, z: -5.8135472e-11}
      rotation: {x: -0.00000003858188, y: -0.0000000596047, z: -1.8590001e-14, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightFootRollCtrl_end_end
      parentName: RightFootRollCtrl_end
      position: {x: 0, y: 0.0011710665, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightKneeCtrl
      parentName: RightFootCtrl
      position: {x: -0.0024718319, y: -0.0023468034, z: 0.0001167929}
      rotation: {x: 0.70710695, y: -0.000000030908613, z: 0.7071066, w: 0.00000003090863}
      scale: {x: 1, y: 0.9999999, z: 1}
    - name: RightKneeCtrl_end
      parentName: RightKneeCtrl
      position: {x: -4.2220224e-11, y: 0.0013472626, z: -1.6121744e-10}
      rotation: {x: -0.00000006403067, y: -6.7417944e-15, z: -0.00000008848258, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightKneeCtrl_end_end
      parentName: RightKneeCtrl_end
      position: {x: 0, y: 0.0013472626, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: HipsCtrl
      parentName: Root
      position: {x: -0.00000089363726, y: -0.000124961, z: -0.0062566884}
      rotation: {x: 0.0009546562, y: 0.7071062, z: 0.70710593, w: -0.0010862004}
      scale: {x: 0.99999994, y: 1, z: 1}
    - name: Hips
      parentName: HipsCtrl
      position: {x: 0.0000018583984, y: 0.0012878035, z: 1.9212464e-10}
      rotation: {x: 0.999999, y: -0.0014431037, z: 0.00009301445, w: -0.000000089406974}
      scale: {x: 1.0000042, y: 1, z: 0.99999994}
    - name: Spine
      parentName: Hips
      position: {x: -6.1091865e-14, y: 0.0012878064, z: -4.8388187e-13}
      rotation: {x: 0.061042573, y: 0.000000010545148, z: -0.000000010471791, w: 0.9981352}
      scale: {x: 1.000004, y: 0.9999998, z: 1.0000001}
    - name: Chest
      parentName: Spine
      position: {x: 1.0000425e-13, y: 0.001148195, z: -1.9276551e-10}
      rotation: {x: -0.011894621, y: -0.000000010011934, z: 0.000000011054812, w: 0.99992925}
      scale: {x: 1, y: 1, z: 0.99999994}
    - name: UpperChest
      parentName: Chest
      position: {x: 8.53892e-15, y: 0.0012211064, z: -1.490213e-11}
      rotation: {x: -0.049165756, y: 3.780111e-11, z: -4.0974526e-11, w: 0.9987906}
      scale: {x: 1, y: 1, z: 1.0000002}
    - name: Neck
      parentName: UpperChest
      position: {x: -7.8743605e-14, y: 0.0010834581, z: 1.5662506e-11}
      rotation: {x: -0.07661581, y: -3.5714324e-11, z: 2.6769916e-11, w: 0.9970607}
      scale: {x: 1, y: 1, z: 0.9999999}
    - name: Head
      parentName: Neck
      position: {x: 3.8465528e-14, y: 0.0011120371, z: -1.1178477e-11}
      rotation: {x: 0.07413529, y: -9.174157e-13, z: 1.2248472e-12, w: 0.99724823}
      scale: {x: 1.000004, y: 1, z: 1.0000001}
    - name: Head_end
      parentName: Head
      position: {x: -1.9436445e-14, y: 0.004017641, z: -1.8642956e-12}
      rotation: {x: -0.00000000698492, y: 4.7271218e-17, z: -1.1969592e-16, w: 1}
      scale: {x: 1, y: 0.99999994, z: 0.99999994}
    - name: Head_end_end
      parentName: Head_end
      position: {x: 0, y: 0.00401764, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftShoulder
      parentName: UpperChest
      position: {x: 0.00035311872, y: 0.0011243275, z: -0.00025984424}
      rotation: {x: 0.58688205, y: 0.41065297, z: -0.5482951, w: 0.43163195}
      scale: {x: 0.9999998, y: 0.99999994, z: 1.0000001}
    - name: LeftArm
      parentName: LeftShoulder
      position: {x: 0, y: 0.0010352023, z: 8.525094e-10}
      rotation: {x: -0.08820811, y: -0.7104929, z: 0.063604034, w: 0.6952508}
      scale: {x: 1.0000007, y: 0.9999958, z: 1.0000069}
    - name: LeftForeArm
      parentName: LeftArm
      position: {x: 3.7172865e-10, y: 0.0018830921, z: 3.72529e-12}
      rotation: {x: -0.040832393, y: -0.012276289, z: 0.011181881, w: 0.9990281}
      scale: {x: 0.9999998, y: 1.0000001, z: 1.0000002}
    - name: LeftHand
      parentName: LeftForeArm
      position: {x: -6.146729e-10, y: 0.0021089038, z: 2.980232e-11}
      rotation: {x: 0.02654673, y: -0.72473973, z: -0.021308554, w: 0.68818134}
      scale: {x: 1, y: 0.99999994, z: 1}
    - name: LeftHandIndex1
      parentName: LeftHand
      position: {x: -5.960464e-11, y: 0.00055439665, z: -1.285225e-10}
      rotation: {x: -0.05672772, y: 0.07746566, z: 0.00014867124, w: 0.99537987}
      scale: {x: 0.9999999, y: 0.99999994, z: 1.0000001}
    - name: LeftHandIndex2
      parentName: LeftHandIndex1
      position: {x: -7.4505804e-11, y: 0.00045128496, z: 3.483146e-10}
      rotation: {x: -0.03954869, y: -0.06709265, z: -0.00031015842, w: 0.99696255}
      scale: {x: 1, y: 1.0000001, z: 0.9999999}
    - name: LeftHandIndex3
      parentName: LeftHandIndex2
      position: {x: 2.980232e-11, y: 0.00040852083, z: 0.0000000012088566}
      rotation: {x: -0.030971484, y: 0.030523857, z: 0.000892146, w: 0.9990537}
      scale: {x: 0.99999946, y: 1.0000002, z: 1}
    - name: LeftHandIndex3_end
      parentName: LeftHandIndex3
      position: {x: 2.3283064e-12, y: 0.00052199373, z: 2.994493e-10}
      rotation: {x: -0.000000886728, y: 0.00000086088414, z: 0.000000026532827, w: 1}
      scale: {x: 0.99999994, y: 0.99999994, z: 0.99999994}
    - name: LeftHandIndex3_end_end
      parentName: LeftHandIndex3_end
      position: {x: 0, y: 0.0005219957, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandThumb1
      parentName: LeftHand
      position: {x: -0.00026059343, y: 0.00020392294, z: -0.0000097815055}
      rotation: {x: 0.3180126, y: -0.65091157, z: 0.30346236, w: 0.6189448}
      scale: {x: 1.0000002, y: 0.99999976, z: 0.9999999}
    - name: LeftHandThumb2
      parentName: LeftHandThumb1
      position: {x: 3.1292435e-10, y: 0.00042346996, z: -1.7136335e-10}
      rotation: {x: -0.033362515, y: 0.013763305, z: 0.0006512849, w: 0.9993484}
      scale: {x: 0.99999976, y: 0.99999994, z: 1.0000001}
    - name: LeftHandThumb2_end
      parentName: LeftHandThumb2
      position: {x: -0.0000000011324882, y: 0.0005290373, z: -1.1920928e-10}
      rotation: {x: -0.00000066589564, y: 0.0000032726678, z: 0.0000018551947, w: 1}
      scale: {x: 0.99999994, y: 1, z: 1}
    - name: LeftHandThumb2_end_end
      parentName: LeftHandThumb2_end
      position: {x: 0, y: 0.0005290389, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightShoulder
      parentName: UpperChest
      position: {x: -0.00035311884, y: 0.0011243275, z: -0.00025984424}
      rotation: {x: 0.6583801, y: -0.50761104, z: -0.45999324, w: -0.31188613}
      scale: {x: 0.9999997, y: 0.9999996, z: 0.9999999}
    - name: RightArm
      parentName: RightShoulder
      position: {x: 3.3900138e-10, y: 0.0010352023, z: -8.4936624e-10}
      rotation: {x: -0.0002838075, y: 0.96439177, z: 0.10874745, w: 0.24108621}
      scale: {x: 1.0000069, y: 0.9999954, z: 1.0000012}
    - name: RightForeArm
      parentName: RightArm
      position: {x: -8.940696e-11, y: 0.0018830933, z: -8.231727e-10}
      rotation: {x: -0.00703423, y: 0.0044677993, z: 0.04174695, w: 0.99909353}
      scale: {x: 1.0000001, y: 1, z: 0.9999998}
    - name: RightHand
      parentName: RightForeArm
      position: {x: -7.4505804e-11, y: 0.002108905, z: -8.177012e-10}
      rotation: {x: -0.0082064085, y: -0.00033079306, z: -0.033037357, w: 0.9994204}
      scale: {x: 0.99999994, y: 1, z: 1.0000001}
    - name: RightHandIndex1
      parentName: RightHand
      position: {x: 2.1606682e-10, y: 0.00055439735, z: 0.0000000010402872}
      rotation: {x: 0.05672966, y: 0.07746147, z: -0.00014820698, w: 0.9953801}
      scale: {x: 1, y: 0.99999976, z: 1.0000002}
    - name: RightHandIndex2
      parentName: RightHandIndex1
      position: {x: -5.215406e-11, y: 0.00045128464, z: -2.561137e-10}
      rotation: {x: 0.039546575, y: -0.06709022, z: 0.0003111869, w: 0.9969629}
      scale: {x: 1, y: 0.9999997, z: 0.9999999}
    - name: RightHandIndex3
      parentName: RightHandIndex2
      position: {x: 7.4505804e-11, y: 0.0004085223, z: -0.0000000017145648}
      rotation: {x: 0.030975439, y: 0.030521514, z: -0.0008921877, w: 0.99905366}
      scale: {x: 0.9999997, y: 1, z: 0.99999994}
    - name: RightHandIndex3_end
      parentName: RightHandIndex3
      position: {x: -3.72529e-12, y: 0.00052199455, z: -8.9945384e-10}
      rotation: {x: 0.000000029930266, y: 0.00000000383443, z: 0.0000000022785116,
        w: 1}
      scale: {x: 0.99999994, y: 1, z: 0.99999994}
    - name: RightHandIndex3_end_end
      parentName: RightHandIndex3_end
      position: {x: 0, y: 0.0005219957, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandThumb1
      parentName: RightHand
      position: {x: -0.0002556773, y: 0.0002039236, z: 0.000051317256}
      rotation: {x: -0.3330397, y: 0.76632255, z: 0.28689477, w: 0.46853572}
      scale: {x: 0.9999998, y: 0.9999999, z: 0.99999976}
    - name: RightHandThumb2
      parentName: RightHandThumb1
      position: {x: 3.5762784e-10, y: 0.00042347013, z: 6.854534e-10}
      rotation: {x: 0.031656384, y: 0.026198523, z: 0.010555859, w: 0.9990996}
      scale: {x: 0.9999999, y: 0.9999999, z: 0.99999976}
    - name: RightHandThumb2_end
      parentName: RightHandThumb2
      position: {x: -1.1920928e-10, y: 0.0005290379, z: 1.1920928e-10}
      rotation: {x: -0.0000013653189, y: -0.0000045597553, z: 0.0000014938414, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandThumb2_end_end
      parentName: RightHandThumb2_end
      position: {x: 0, y: 0.00052903884, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftUpLeg
      parentName: Hips
      position: {x: 0.00080611324, y: 0.0002338532, z: -3.0396752e-10}
      rotation: {x: 0.9867035, y: 0.0010598269, z: -0.16252585, w: -0.00067070505}
      scale: {x: 1.0000278, y: 0.9999997, z: 1.0000001}
    - name: LeftLeg
      parentName: LeftUpLeg
      position: {x: -3.7252902e-11, y: 0.0021046966, z: 2.095476e-12}
      rotation: {x: -0.14157447, y: -0.032443766, z: 0.052854557, w: 0.98798305}
      scale: {x: 0.9999997, y: 0.9999992, z: 1.0000024}
    - name: LeftFoot
      parentName: LeftLeg
      position: {x: 8.195639e-11, y: 0.0024352474, z: -1.6391279e-10}
      rotation: {x: 0.5438075, y: 0.19418126, z: -0.18754381, w: 0.7946033}
      scale: {x: 1.0000203, y: 0.99998355, z: 0.99999565}
    - name: LeftToes
      parentName: LeftFoot
      position: {x: -7.217749e-11, y: 0.0012050166, z: 5.5879346e-11}
      rotation: {x: -0.019817604, y: 0.95228684, z: 0.29825512, w: -0.06165168}
      scale: {x: 0.9999998, y: 0.99999934, z: 0.9999998}
    - name: LeftToes_end
      parentName: LeftToes
      position: {x: -7.477058e-11, y: 0.00085294666, z: -4.190952e-12}
      rotation: {x: 0.000000015788828, y: 0.0000015796886, z: -0.000000011788357,
        w: 1}
      scale: {x: 1.0000001, y: 1.0000001, z: 0.99999994}
    - name: LeftToes_end_end
      parentName: LeftToes_end
      position: {x: 0, y: 0.0008529456, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightUpLeg
      parentName: Hips
      position: {x: -0.00080611306, y: 0.00023385324, z: -3.0125674e-10}
      rotation: {x: 0.9765873, y: -0.0013587022, z: 0.21511672, w: -0.0004839266}
      scale: {x: 0.99999505, y: 0.99999964, z: 0.9999992}
    - name: RightLeg
      parentName: RightUpLeg
      position: {x: 6.426125e-11, y: 0.0021046966, z: 4.074536e-11}
      rotation: {x: -0.13894758, y: -0.011511234, z: -0.059173066, w: 0.9884633}
      scale: {x: 0.99999917, y: 1.0000002, z: 1}
    - name: RightFoot
      parentName: RightLeg
      position: {x: -6.705523e-11, y: 0.0024352465, z: -1.1920928e-10}
      rotation: {x: 0.54849505, y: -0.2288286, z: 0.17182066, w: 0.78566426}
      scale: {x: 0.9999948, y: 1.0000035, z: 1.0000011}
    - name: RightToes
      parentName: RightFoot
      position: {x: 1.1920928e-10, y: 0.0012050177, z: 1.2293458e-10}
      rotation: {x: 0.028260684, y: 0.9539411, z: 0.29757404, w: 0.025445573}
      scale: {x: 0.99999994, y: 0.99999964, z: 1.0000001}
    - name: RightToes_end
      parentName: RightToes
      position: {x: 1.1996598e-10, y: 0.0008529444, z: 1.3969838e-12}
      rotation: {x: -0.00000004905451, y: 0.00000034912955, z: -0.0000000046347854,
        w: 1}
      scale: {x: 1, y: 0.9999999, z: 1}
    - name: RightToes_end_end
      parentName: RightToes_end
      position: {x: 0, y: 0.000852945, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: characterMedium
      parentName: DemoPuppet(Clone)
      position: {x: -0.0000000034533438, y: -5.9604666e-10, z: 0.0000000016093251}
      rotation: {x: 0.00000008069665, y: 9.965151e-10, z: -1.4901154e-10, w: 1}
      scale: {x: 1, y: 1, z: 1}
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 0.4
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 1
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 3
  humanoidOversampling: 1
  avatarSetup: 1
  addHumanoidExtraRootOnlyWhenUsingAvatar: 1
  remapMaterialsIfMaterialImportModeIsNone: 0
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
AssetOrigin:
  serializedVersion: 1
  productId: 316173
  packageName: All In 1 3D-Shader
  packageVersion: 1.6
  assetPath: Assets/Plugins/AllIn13DShader/Demo/Models/DemoPuppet.fbx
  uploadId: 760663
