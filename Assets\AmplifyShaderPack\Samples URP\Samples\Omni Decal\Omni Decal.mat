%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Omni Decal
  m_Shader: {fileID: 4800000, guid: 102be843f3f4412a87d31b2ed415f253, type: 3}
  m_ValidKeywords: []
  m_InvalidKeywords:
  - _ADD_PRECOMPUTED_VELOCITY
  - _BLENDMODE_ALPHA
  - _DISABLE_SSR
  - _DISABLE_SSR_TRANSPARENT
  - _SURFACE_TYPE_TRANSPARENT
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: 3000
  stringTagMap:
    MotionVector: User
  disabledShaderPasses:
  - DistortionVectors
  - TransparentBackface
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _Ramp:
        m_Texture: {fileID: 2800000, guid: 60d1a2f814864f548b6b60406c360c51, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AddPrecomputedVelocity: 1
    - _AlphaCutoff: 0.5
    - _AlphaCutoffEnable: 0
    - _AlphaDstBlend: 10
    - _AlphaSrcBlend: 0
    - _BlendMode: 0
    - _CullMode: 1
    - _CullModeForward: 1
    - _DistortionEnable: 0
    - _DistortionOnly: 0
    - _DoubleSidedEnable: 0
    - _DoubleSidedGIMode: 0
    - _DoubleSidedNormalMode: 1
    - _DstBlend: 10
    - _EnableFogOnTransparent: 0
    - _OpaqueCullMode: 2
    - _QueueControl: 0
    - _QueueOffset: 0
    - _Rampfalloff: 5
    - _ReceivesSSR: 0
    - _ReceivesSSRTransparent: 0
    - _RenderQueueType: 5
    - _RequireSplitLighting: 0
    - _Size: 1
    - _SrcBlend: 1
    - _StencilRef: 0
    - _StencilRefDepth: 0
    - _StencilRefDistortionVec: 4
    - _StencilRefGBuffer: 2
    - _StencilRefMV: 32
    - _StencilWriteMask: 6
    - _StencilWriteMaskDepth: 9
    - _StencilWriteMaskDistortionVec: 4
    - _StencilWriteMaskGBuffer: 15
    - _StencilWriteMaskMV: 41
    - _SupportDecals: 1
    - _SurfaceType: 1
    - _TransparentBackfaceEnable: 0
    - _TransparentCullMode: 1
    - _TransparentSortPriority: 0
    - _TransparentWritingMotionVec: 0
    - _TransparentZWrite: 0
    - _UseShadowThreshold: 0
    - _ZTestDepthEqualForOpaque: 4
    - _ZTestGBuffer: 4
    - _ZTestTransparent: 4
    - _ZWrite: 0
    - __dirty: 0
    m_Colors:
    - _DoubleSidedConstants: {r: 1, g: 1, b: -1, a: 0}
    - _EmissionColor: {r: 1, g: 1, b: 1, a: 1}
    - _UnlitColorMap_MipInfo: {r: 0, g: 0, b: 0, a: 0}
  m_BuildTextureStacks: []
