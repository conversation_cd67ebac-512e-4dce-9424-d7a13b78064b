%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 94ddca8ef013926408780e6e292bad16, type: 3}
  m_Name: DemoElement_DepthColor
  m_EditorClassIdentifier: 
  demoName: Stylized Fog
  prefab: {fileID: 2481555142680936367, guid: 6a0266dc98652204dbe3621e86ba0f88, type: 3}
  environment: {fileID: 11400000, guid: 301d58fcd223fbb44a36be03b70d52d8, type: 2}
  info: The Depth Color effect takes the depth of the object and remaps that depth
    to a gradient set in a scriptable object and used by a component in the scene.
    Take a look at the Depth Coloring section of the Documentation.
  directionalLightEnabled: 1
  mainLightIntensity: 1
  skyboxEnabled: 1
  postProcessEnabled: 1
