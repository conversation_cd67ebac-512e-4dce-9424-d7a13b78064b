﻿<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Namespace Flexalon.Editor
 | Flexalon 3D Layouts </title>
    <meta name="viewport" content="width=device-width">
    <meta name="title" content="Namespace Flexalon.Editor
 | Flexalon 3D Layouts ">
    <meta name="generator" content="docfx ">
  
    <link rel="shortcut icon" href="images/favicon.svg">
    <link rel="stylesheet" href="styles/docfx.vendor.css">
    <link rel="stylesheet" href="styles/docfx.css">
    <link rel="stylesheet" href="styles/main.css">
    <meta property="docfx:navrel" content="tochead.html">
    <meta property="docfx:tocrel" content="toc.html">
  
  <meta property="docfx:rel" content="">
  
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="https://www.flexalon.com/">
                <img id="logo" class="svg" src="images/logo_32x32.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first="First" data-prev="Previous" data-next="Next" data-last="Last"></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">

        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="Flexalon.Editor">

  <h1 id="Flexalon_Editor" data-uid="Flexalon.Editor" class="text-break">Namespace Flexalon.Editor
</h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="markdown level0 remarks"></div>
    <h3 id="classes">Classes
</h3>
      <h4><a class="xref" href="Flexalon.Editor.FlexalonAlignLayoutEditor.html">FlexalonAlignLayoutEditor</a></h4>
      <section></section>
      <h4><a class="xref" href="Flexalon.Editor.FlexalonCircleLayoutEditor.html">FlexalonCircleLayoutEditor</a></h4>
      <section></section>
      <h4><a class="xref" href="Flexalon.Editor.FlexalonClonerEditor.html">FlexalonClonerEditor</a></h4>
      <section></section>
      <h4><a class="xref" href="Flexalon.Editor.FlexalonComponentEditor.html">FlexalonComponentEditor</a></h4>
      <section></section>
      <h4><a class="xref" href="Flexalon.Editor.FlexalonConstraintEditor.html">FlexalonConstraintEditor</a></h4>
      <section></section>
      <h4><a class="xref" href="Flexalon.Editor.FlexalonCurveLayoutEditor.html">FlexalonCurveLayoutEditor</a></h4>
      <section></section>
      <h4><a class="xref" href="Flexalon.Editor.FlexalonDragTargetEditor.html">FlexalonDragTargetEditor</a></h4>
      <section></section>
      <h4><a class="xref" href="Flexalon.Editor.FlexalonEditor.html">FlexalonEditor</a></h4>
      <section></section>
      <h4><a class="xref" href="Flexalon.Editor.FlexalonFlexibleLayoutEditor.html">FlexalonFlexibleLayoutEditor</a></h4>
      <section></section>
      <h4><a class="xref" href="Flexalon.Editor.FlexalonGridCellEditor.html">FlexalonGridCellEditor</a></h4>
      <section></section>
      <h4><a class="xref" href="Flexalon.Editor.FlexalonGridLayoutEditor.html">FlexalonGridLayoutEditor</a></h4>
      <section></section>
      <h4><a class="xref" href="Flexalon.Editor.FlexalonInteractableEditor.html">FlexalonInteractableEditor</a></h4>
      <section></section>
      <h4><a class="xref" href="Flexalon.Editor.FlexalonObjectEditor.html">FlexalonObjectEditor</a></h4>
      <section></section>
      <h4><a class="xref" href="Flexalon.Editor.FlexalonRandomLayoutEditor.html">FlexalonRandomLayoutEditor</a></h4>
      <section></section>
      <h4><a class="xref" href="Flexalon.Editor.FlexalonRandomModifierEditor.html">FlexalonRandomModifierEditor</a></h4>
      <section></section>
      <h4><a class="xref" href="Flexalon.Editor.FlexalonResultEditor.html">FlexalonResultEditor</a></h4>
      <section></section>
      <h4><a class="xref" href="Flexalon.Editor.FlexalonShapeLayoutEditor.html">FlexalonShapeLayoutEditor</a></h4>
      <section></section>
</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In This Article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      Flexalon 3D Layouts
      
          </div>
        </div>
      </footer>
    </div>

    
    
    
  </body>
</html>
