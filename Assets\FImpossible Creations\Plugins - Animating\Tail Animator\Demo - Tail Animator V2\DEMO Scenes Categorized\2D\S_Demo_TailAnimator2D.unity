%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 10
  m_Fog: 1
  m_FogColor: {r: 0.14117648, g: 0.15294118, b: 0.16078432, a: 1}
  m_FogMode: 1
  m_FogDensity: 0.09
  m_LinearFogStart: 5
  m_LinearFogEnd: 25
  m_AmbientSkyColor: {r: 0.14751694, g: 0.15180375, b: 0.16037738, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 3
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 10304, guid: 0000000000000000f000000000000000, type: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &3
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 13
  m_BakeOnSceneLoad: 0
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 1
    m_EnableRealtimeLightmaps: 0
  m_LightmapEditorSettings:
    serializedVersion: 12
    m_Resolution: 2
    m_BakeResolution: 40
    m_AtlasSize: 1024
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_ExtractAmbientOcclusion: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_ReflectionCompression: 2
    m_MixedBakeMode: 2
    m_BakeBackend: 1
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 512
    m_PVRBounces: 2
    m_PVREnvironmentSampleCount: 512
    m_PVREnvironmentReferencePointCount: 2048
    m_PVRFilteringMode: 2
    m_PVRDenoiserTypeDirect: 0
    m_PVRDenoiserTypeIndirect: 0
    m_PVRDenoiserTypeAO: 0
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVREnvironmentMIS: 0
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ExportTrainingData: 0
    m_TrainingDataDestination: TrainingData
    m_LightProbeSampleCountMultiplier: 4
  m_LightingDataAsset: {fileID: 0}
  m_LightingSettings: {fileID: 4890085278179872738, guid: db6c73c251469504aa46a096ba4db16c, type: 2}
--- !u!196 &4
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 3
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    buildHeightMesh: 0
    maxJobWorkers: 0
    preserveTilesOutsideBounds: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!1 &69809304
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 69809309}
  - component: {fileID: 69809308}
  - component: {fileID: 69809307}
  m_Layer: 0
  m_Name: Main Camera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!81 &69809307
AudioListener:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 69809304}
  m_Enabled: 1
--- !u!20 &69809308
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 69809304}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 2
  m_BackGroundColor: {r: 0.13843897, g: 0.15185618, b: 0.16037738, a: 0}
  m_projectionMatrixMode: 1
  m_GateFitMode: 2
  m_FOVAxisMode: 0
  m_Iso: 200
  m_ShutterSpeed: 0.005
  m_Aperture: 16
  m_FocusDistance: 10
  m_FocalLength: 50
  m_BladeCount: 5
  m_Curvature: {x: 2, y: 11}
  m_BarrelClipping: 0.25
  m_Anamorphism: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.3
  far clip plane: 1000
  field of view: 60
  orthographic: 1
  orthographic size: 7.14
  m_Depth: -1
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 1
  m_AllowMSAA: 1
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 0
  m_OcclusionCulling: 1
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!4 &69809309
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 69809304}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 1, z: -10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &232771907
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 232771911}
  - component: {fileID: 232771910}
  - component: {fileID: 232771909}
  - component: {fileID: 232771908}
  m_Layer: 0
  m_Name: Cube (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!61 &232771908
BoxCollider2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 232771907}
  m_Enabled: 1
  serializedVersion: 3
  m_Density: 1
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_ForceSendLayers:
    serializedVersion: 2
    m_Bits: 4294967295
  m_ForceReceiveLayers:
    serializedVersion: 2
    m_Bits: 4294967295
  m_ContactCaptureLayers:
    serializedVersion: 2
    m_Bits: 4294967295
  m_CallbackLayers:
    serializedVersion: 2
    m_Bits: 4294967295
  m_IsTrigger: 0
  m_UsedByEffector: 0
  m_CompositeOperation: 0
  m_CompositeOrder: 0
  m_Offset: {x: 0, y: 0}
  m_SpriteTilingProperty:
    border: {x: 0, y: 0, z: 0, w: 0}
    pivot: {x: 0, y: 0}
    oldSize: {x: 0, y: 0}
    newSize: {x: 0, y: 0}
    adaptiveTilingThreshold: 0
    drawMode: 0
    adaptiveTiling: 0
  m_AutoTiling: 0
  m_Size: {x: 0.99999994, y: 0.99999994}
  m_EdgeRadius: 0
--- !u!23 &232771909
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 232771907}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10303, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &232771910
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 232771907}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &232771911
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 232771907}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -6.04, y: 0.3300001, z: 0}
  m_LocalScale: {x: 6.5046024, y: 0.4048735, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &236733677
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 236733681}
  - component: {fileID: 236733680}
  - component: {fileID: 236733679}
  - component: {fileID: 236733678}
  m_Layer: 0
  m_Name: Cube (6)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!61 &236733678
BoxCollider2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 236733677}
  m_Enabled: 1
  serializedVersion: 3
  m_Density: 1
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_ForceSendLayers:
    serializedVersion: 2
    m_Bits: 4294967295
  m_ForceReceiveLayers:
    serializedVersion: 2
    m_Bits: 4294967295
  m_ContactCaptureLayers:
    serializedVersion: 2
    m_Bits: 4294967295
  m_CallbackLayers:
    serializedVersion: 2
    m_Bits: 4294967295
  m_IsTrigger: 0
  m_UsedByEffector: 0
  m_CompositeOperation: 0
  m_CompositeOrder: 0
  m_Offset: {x: 0, y: 0}
  m_SpriteTilingProperty:
    border: {x: 0, y: 0, z: 0, w: 0}
    pivot: {x: 0, y: 0}
    oldSize: {x: 0, y: 0}
    newSize: {x: 0, y: 0}
    adaptiveTilingThreshold: 0
    drawMode: 0
    adaptiveTiling: 0
  m_AutoTiling: 0
  m_Size: {x: 0.99999994, y: 0.99999994}
  m_EdgeRadius: 0
--- !u!23 &236733679
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 236733677}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10303, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &236733680
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 236733677}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &236733681
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 236733677}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -12.91, y: 3.79, z: 0}
  m_LocalScale: {x: 1.3612086, y: 14.398442, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &380034316
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 4618275177477432, guid: 64092093629618949b0a323192fa63d4, type: 3}
      propertyPath: m_RootOrder
      value: 16
      objectReference: {fileID: 0}
    - target: {fileID: 4618275177477432, guid: 64092093629618949b0a323192fa63d4, type: 3}
      propertyPath: m_LocalScale.x
      value: 1.9
      objectReference: {fileID: 0}
    - target: {fileID: 4618275177477432, guid: 64092093629618949b0a323192fa63d4, type: 3}
      propertyPath: m_LocalScale.y
      value: 1.9
      objectReference: {fileID: 0}
    - target: {fileID: 4618275177477432, guid: 64092093629618949b0a323192fa63d4, type: 3}
      propertyPath: m_LocalScale.z
      value: 1.9
      objectReference: {fileID: 0}
    - target: {fileID: 4618275177477432, guid: 64092093629618949b0a323192fa63d4, type: 3}
      propertyPath: m_LocalPosition.x
      value: -7.82
      objectReference: {fileID: 0}
    - target: {fileID: 4618275177477432, guid: 64092093629618949b0a323192fa63d4, type: 3}
      propertyPath: m_LocalPosition.y
      value: 1.148
      objectReference: {fileID: 0}
    - target: {fileID: 4618275177477432, guid: 64092093629618949b0a323192fa63d4, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4618275177477432, guid: 64092093629618949b0a323192fa63d4, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4618275177477432, guid: 64092093629618949b0a323192fa63d4, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4618275177477432, guid: 64092093629618949b0a323192fa63d4, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4618275177477432, guid: 64092093629618949b0a323192fa63d4, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 114570257999828066, guid: 64092093629618949b0a323192fa63d4, type: 3}
      propertyPath: CollisionMode
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114570257999828066, guid: 64092093629618949b0a323192fa63d4, type: 3}
      propertyPath: _Editor_Category
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 114570257999828066, guid: 64092093629618949b0a323192fa63d4, type: 3}
      propertyPath: _Editor_FeaturesCategory
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114570257999828066, guid: 64092093629618949b0a323192fa63d4, type: 3}
      propertyPath: _editor_IsInspectorViewingColliders
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114570257999828066, guid: 64092093629618949b0a323192fa63d4, type: 3}
      propertyPath: _editor_IsInspectorViewingIncludedColliders
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 120381544249335968, guid: 64092093629618949b0a323192fa63d4, type: 3}
      propertyPath: m_Positions.Array.data[0].x
      value: -7.825971
      objectReference: {fileID: 0}
    - target: {fileID: 120381544249335968, guid: 64092093629618949b0a323192fa63d4, type: 3}
      propertyPath: m_Positions.Array.data[0].y
      value: 1.7237
      objectReference: {fileID: 0}
    - target: {fileID: 120381544249335968, guid: 64092093629618949b0a323192fa63d4, type: 3}
      propertyPath: m_Positions.Array.data[0].z
      value: -0.0494
      objectReference: {fileID: 0}
    - target: {fileID: 120381544249335968, guid: 64092093629618949b0a323192fa63d4, type: 3}
      propertyPath: m_Positions.Array.data[1].x
      value: -7.825971
      objectReference: {fileID: 0}
    - target: {fileID: 120381544249335968, guid: 64092093629618949b0a323192fa63d4, type: 3}
      propertyPath: m_Positions.Array.data[1].y
      value: 2.096893
      objectReference: {fileID: 0}
    - target: {fileID: 120381544249335968, guid: 64092093629618949b0a323192fa63d4, type: 3}
      propertyPath: m_Positions.Array.data[1].z
      value: -0.0494
      objectReference: {fileID: 0}
    - target: {fileID: 120381544249335968, guid: 64092093629618949b0a323192fa63d4, type: 3}
      propertyPath: m_Positions.Array.data[2].x
      value: -7.825971
      objectReference: {fileID: 0}
    - target: {fileID: 120381544249335968, guid: 64092093629618949b0a323192fa63d4, type: 3}
      propertyPath: m_Positions.Array.data[2].y
      value: 2.470086
      objectReference: {fileID: 0}
    - target: {fileID: 120381544249335968, guid: 64092093629618949b0a323192fa63d4, type: 3}
      propertyPath: m_Positions.Array.data[2].z
      value: -0.0494
      objectReference: {fileID: 0}
    - target: {fileID: 120381544249335968, guid: 64092093629618949b0a323192fa63d4, type: 3}
      propertyPath: m_Positions.Array.data[3].x
      value: -7.825971
      objectReference: {fileID: 0}
    - target: {fileID: 120381544249335968, guid: 64092093629618949b0a323192fa63d4, type: 3}
      propertyPath: m_Positions.Array.data[3].y
      value: 2.843279
      objectReference: {fileID: 0}
    - target: {fileID: 120381544249335968, guid: 64092093629618949b0a323192fa63d4, type: 3}
      propertyPath: m_Positions.Array.data[3].z
      value: -0.0494
      objectReference: {fileID: 0}
    - target: {fileID: 120381544249335968, guid: 64092093629618949b0a323192fa63d4, type: 3}
      propertyPath: m_Positions.Array.data[4].x
      value: -7.825971
      objectReference: {fileID: 0}
    - target: {fileID: 120381544249335968, guid: 64092093629618949b0a323192fa63d4, type: 3}
      propertyPath: m_Positions.Array.data[4].y
      value: 3.216472
      objectReference: {fileID: 0}
    - target: {fileID: 120381544249335968, guid: 64092093629618949b0a323192fa63d4, type: 3}
      propertyPath: m_Positions.Array.data[4].z
      value: -0.0494
      objectReference: {fileID: 0}
    - target: {fileID: 120381544249335968, guid: 64092093629618949b0a323192fa63d4, type: 3}
      propertyPath: m_Positions.Array.data[5].x
      value: -7.825971
      objectReference: {fileID: 0}
    - target: {fileID: 120381544249335968, guid: 64092093629618949b0a323192fa63d4, type: 3}
      propertyPath: m_Positions.Array.data[5].y
      value: 3.5896647
      objectReference: {fileID: 0}
    - target: {fileID: 120381544249335968, guid: 64092093629618949b0a323192fa63d4, type: 3}
      propertyPath: m_Positions.Array.data[5].z
      value: -0.0494
      objectReference: {fileID: 0}
    - target: {fileID: 120381544249335968, guid: 64092093629618949b0a323192fa63d4, type: 3}
      propertyPath: m_Positions.Array.data[6].x
      value: -7.825971
      objectReference: {fileID: 0}
    - target: {fileID: 120381544249335968, guid: 64092093629618949b0a323192fa63d4, type: 3}
      propertyPath: m_Positions.Array.data[6].y
      value: 3.962858
      objectReference: {fileID: 0}
    - target: {fileID: 120381544249335968, guid: 64092093629618949b0a323192fa63d4, type: 3}
      propertyPath: m_Positions.Array.data[6].z
      value: -0.0494
      objectReference: {fileID: 0}
    - target: {fileID: 120381544249335968, guid: 64092093629618949b0a323192fa63d4, type: 3}
      propertyPath: m_Positions.Array.data[7].x
      value: -7.825971
      objectReference: {fileID: 0}
    - target: {fileID: 120381544249335968, guid: 64092093629618949b0a323192fa63d4, type: 3}
      propertyPath: m_Positions.Array.data[7].y
      value: 4.336051
      objectReference: {fileID: 0}
    - target: {fileID: 120381544249335968, guid: 64092093629618949b0a323192fa63d4, type: 3}
      propertyPath: m_Positions.Array.data[7].z
      value: -0.0494
      objectReference: {fileID: 0}
    - target: {fileID: 120381544249335968, guid: 64092093629618949b0a323192fa63d4, type: 3}
      propertyPath: m_Positions.Array.data[8].x
      value: -7.825971
      objectReference: {fileID: 0}
    - target: {fileID: 120381544249335968, guid: 64092093629618949b0a323192fa63d4, type: 3}
      propertyPath: m_Positions.Array.data[8].y
      value: 4.709244
      objectReference: {fileID: 0}
    - target: {fileID: 120381544249335968, guid: 64092093629618949b0a323192fa63d4, type: 3}
      propertyPath: m_Positions.Array.data[8].z
      value: -0.0494
      objectReference: {fileID: 0}
    - target: {fileID: 120381544249335968, guid: 64092093629618949b0a323192fa63d4, type: 3}
      propertyPath: m_Positions.Array.data[9].x
      value: -7.825971
      objectReference: {fileID: 0}
    - target: {fileID: 120381544249335968, guid: 64092093629618949b0a323192fa63d4, type: 3}
      propertyPath: m_Positions.Array.data[9].y
      value: 5.0824366
      objectReference: {fileID: 0}
    - target: {fileID: 120381544249335968, guid: 64092093629618949b0a323192fa63d4, type: 3}
      propertyPath: m_Positions.Array.data[9].z
      value: -0.0494
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 64092093629618949b0a323192fa63d4, type: 3}
--- !u!1 &395217917
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 395217918}
  - component: {fileID: 395217921}
  - component: {fileID: 395217920}
  - component: {fileID: 395217919}
  m_Layer: 5
  m_Name: Example Info
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &395217918
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 395217917}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1710261408}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 113, y: -62.299927}
  m_SizeDelta: {x: 1200, y: 600}
  m_Pivot: {x: 0, y: 1}
--- !u!114 &395217919
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 395217917}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e19747de3f5aca642ab2be37e372fb86, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_EffectColor: {r: 0, g: 0, b: 0, a: 1}
  m_EffectDistance: {x: 1, y: -1}
  m_UseGraphicAlpha: 1
--- !u!114 &395217920
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 395217917}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5f7201a12d95ffc409449d95f23cf332, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_FontData:
    m_Font: {fileID: 10102, guid: 0000000000000000e000000000000000, type: 0}
    m_FontSize: 46
    m_FontStyle: 1
    m_BestFit: 0
    m_MinSize: 4
    m_MaxSize: 46
    m_Alignment: 0
    m_AlignByGeometry: 0
    m_RichText: 1
    m_HorizontalOverflow: 0
    m_VerticalOverflow: 0
    m_LineSpacing: 1
  m_Text: Example of Tail Animator usage in 2D game space
--- !u!222 &395217921
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 395217917}
  m_CullTransparentMesh: 0
--- !u!1 &518919835
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 518919839}
  - component: {fileID: 518919838}
  - component: {fileID: 518919837}
  - component: {fileID: 518919836}
  m_Layer: 0
  m_Name: Cube (5)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!61 &518919836
BoxCollider2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 518919835}
  m_Enabled: 1
  serializedVersion: 3
  m_Density: 1
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_ForceSendLayers:
    serializedVersion: 2
    m_Bits: 4294967295
  m_ForceReceiveLayers:
    serializedVersion: 2
    m_Bits: 4294967295
  m_ContactCaptureLayers:
    serializedVersion: 2
    m_Bits: 4294967295
  m_CallbackLayers:
    serializedVersion: 2
    m_Bits: 4294967295
  m_IsTrigger: 0
  m_UsedByEffector: 0
  m_CompositeOperation: 0
  m_CompositeOrder: 0
  m_Offset: {x: 0, y: 0}
  m_SpriteTilingProperty:
    border: {x: 0, y: 0, z: 0, w: 0}
    pivot: {x: 0, y: 0}
    oldSize: {x: 0, y: 0}
    newSize: {x: 0, y: 0}
    adaptiveTilingThreshold: 0
    drawMode: 0
    adaptiveTiling: 0
  m_AutoTiling: 0
  m_Size: {x: 0.99999994, y: 0.99999994}
  m_EdgeRadius: 0
--- !u!23 &518919837
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 518919835}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10303, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &518919838
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 518919835}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &518919839
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 518919835}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0.35730854, w: 0.9339864}
  m_LocalPosition: {x: 11.199, y: -3.482, z: 0}
  m_LocalScale: {x: 4.922172, y: 0.49626648, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 41.87}
--- !u!1 &633614819
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 633614821}
  - component: {fileID: 633614820}
  m_Layer: 0
  m_Name: Directional Light
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!108 &633614820
Light:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 633614819}
  m_Enabled: 1
  serializedVersion: 11
  m_Type: 1
  m_Color: {r: 0.8820755, g: 0.9759214, b: 1, a: 1}
  m_Intensity: 0.2
  m_Range: 10
  m_SpotAngle: 30
  m_InnerSpotAngle: 21.80208
  m_CookieSize: 10
  m_Shadows:
    m_Type: 2
    m_Resolution: -1
    m_CustomResolution: -1
    m_Strength: 1
    m_Bias: 0.05
    m_NormalBias: 0.4
    m_NearPlane: 0.2
    m_CullingMatrixOverride:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    m_UseCullingMatrixOverride: 0
  m_Cookie: {fileID: 0}
  m_DrawHalo: 0
  m_Flare: {fileID: 0}
  m_RenderMode: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingLayerMask: 1
  m_Lightmapping: 4
  m_LightShadowCasterMode: 0
  m_AreaSize: {x: 1, y: 1}
  m_BounceIntensity: 1
  m_ColorTemperature: 6570
  m_UseColorTemperature: 0
  m_BoundingSphereOverride: {x: 0, y: 0, z: 0, w: 0}
  m_UseBoundingSphereOverride: 0
  m_UseViewFrustumForShadowCasterCull: 1
  m_ForceVisible: 0
  m_ShadowRadius: 0
  m_ShadowAngle: 0
  m_LightUnit: 1
  m_LuxAtDistance: 1
  m_EnableSpotReflector: 1
--- !u!4 &633614821
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 633614819}
  serializedVersion: 2
  m_LocalRotation: {x: 0.40821788, y: -0.23456968, z: 0.10938163, w: 0.8754261}
  m_LocalPosition: {x: 0, y: 3, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 50, y: -30, z: 0}
--- !u!1 &636626401
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 636626405}
  - component: {fileID: 636626404}
  - component: {fileID: 636626403}
  - component: {fileID: 636626402}
  m_Layer: 0
  m_Name: Cube
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!61 &636626402
BoxCollider2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 636626401}
  m_Enabled: 1
  serializedVersion: 3
  m_Density: 1
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_ForceSendLayers:
    serializedVersion: 2
    m_Bits: 4294967295
  m_ForceReceiveLayers:
    serializedVersion: 2
    m_Bits: 4294967295
  m_ContactCaptureLayers:
    serializedVersion: 2
    m_Bits: 4294967295
  m_CallbackLayers:
    serializedVersion: 2
    m_Bits: 4294967295
  m_IsTrigger: 0
  m_UsedByEffector: 0
  m_CompositeOperation: 0
  m_CompositeOrder: 0
  m_Offset: {x: 0, y: 0}
  m_SpriteTilingProperty:
    border: {x: 0, y: 0, z: 0, w: 0}
    pivot: {x: 0, y: 0}
    oldSize: {x: 0, y: 0}
    newSize: {x: 0, y: 0}
    adaptiveTilingThreshold: 0
    drawMode: 0
    adaptiveTiling: 0
  m_AutoTiling: 0
  m_Size: {x: 0.99999994, y: 0.99999994}
  m_EdgeRadius: 0
--- !u!23 &636626403
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 636626401}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10303, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &636626404
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 636626401}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &636626405
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 636626401}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0.02, y: -5.15, z: 0}
  m_LocalScale: {x: 23.146233, y: 0.52459735, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &655947739
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 655947740}
  m_Layer: 0
  m_Name: --------------------
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &655947740
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 655947739}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 2.2099159, y: 29.37389, z: -4.015108}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &770490215
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 4350619869302382, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_RootOrder
      value: 15
      objectReference: {fileID: 0}
    - target: {fileID: 4350619869302382, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_LocalScale.x
      value: 1.9
      objectReference: {fileID: 0}
    - target: {fileID: 4350619869302382, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_LocalScale.y
      value: 1.9
      objectReference: {fileID: 0}
    - target: {fileID: 4350619869302382, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_LocalScale.z
      value: 1.9
      objectReference: {fileID: 0}
    - target: {fileID: 4350619869302382, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_LocalPosition.x
      value: 3.806
      objectReference: {fileID: 0}
    - target: {fileID: 4350619869302382, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_LocalPosition.y
      value: -4.29
      objectReference: {fileID: 0}
    - target: {fileID: 4350619869302382, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4350619869302382, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4350619869302382, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4350619869302382, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4350619869302382, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 114671653712389970, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: CollisionMode
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114671653712389970, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: _Editor_Category
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 114671653712389970, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: _Editor_FeaturesCategory
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114671653712389970, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: _editor_IsInspectorViewingColliders
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114671653712389970, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: _editor_IsInspectorViewingIncludedColliders
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 120838229684978756, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_Positions.Array.data[0].x
      value: 3.8000288
      objectReference: {fileID: 0}
    - target: {fileID: 120838229684978756, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_Positions.Array.data[0].y
      value: -3.7143
      objectReference: {fileID: 0}
    - target: {fileID: 120838229684978756, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_Positions.Array.data[0].z
      value: -0.0494
      objectReference: {fileID: 0}
    - target: {fileID: 120838229684978756, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_Positions.Array.data[1].x
      value: 3.8000288
      objectReference: {fileID: 0}
    - target: {fileID: 120838229684978756, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_Positions.Array.data[1].y
      value: -3.341107
      objectReference: {fileID: 0}
    - target: {fileID: 120838229684978756, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_Positions.Array.data[1].z
      value: -0.0494
      objectReference: {fileID: 0}
    - target: {fileID: 120838229684978756, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_Positions.Array.data[2].x
      value: 3.8000288
      objectReference: {fileID: 0}
    - target: {fileID: 120838229684978756, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_Positions.Array.data[2].y
      value: -2.967914
      objectReference: {fileID: 0}
    - target: {fileID: 120838229684978756, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_Positions.Array.data[2].z
      value: -0.0494
      objectReference: {fileID: 0}
    - target: {fileID: 120838229684978756, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_Positions.Array.data[3].x
      value: 3.8000288
      objectReference: {fileID: 0}
    - target: {fileID: 120838229684978756, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_Positions.Array.data[3].y
      value: -2.5947208
      objectReference: {fileID: 0}
    - target: {fileID: 120838229684978756, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_Positions.Array.data[3].z
      value: -0.0494
      objectReference: {fileID: 0}
    - target: {fileID: 120838229684978756, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_Positions.Array.data[4].x
      value: 3.8000288
      objectReference: {fileID: 0}
    - target: {fileID: 120838229684978756, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_Positions.Array.data[4].y
      value: -2.221528
      objectReference: {fileID: 0}
    - target: {fileID: 120838229684978756, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_Positions.Array.data[4].z
      value: -0.0494
      objectReference: {fileID: 0}
    - target: {fileID: 120838229684978756, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_Positions.Array.data[5].x
      value: 3.8000288
      objectReference: {fileID: 0}
    - target: {fileID: 120838229684978756, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_Positions.Array.data[5].y
      value: -1.8483353
      objectReference: {fileID: 0}
    - target: {fileID: 120838229684978756, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_Positions.Array.data[5].z
      value: -0.0494
      objectReference: {fileID: 0}
    - target: {fileID: 120838229684978756, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_Positions.Array.data[6].x
      value: 3.8000288
      objectReference: {fileID: 0}
    - target: {fileID: 120838229684978756, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_Positions.Array.data[6].y
      value: -1.475142
      objectReference: {fileID: 0}
    - target: {fileID: 120838229684978756, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_Positions.Array.data[6].z
      value: -0.0494
      objectReference: {fileID: 0}
    - target: {fileID: 120838229684978756, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_Positions.Array.data[7].x
      value: 3.8000288
      objectReference: {fileID: 0}
    - target: {fileID: 120838229684978756, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_Positions.Array.data[7].y
      value: -1.101949
      objectReference: {fileID: 0}
    - target: {fileID: 120838229684978756, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_Positions.Array.data[7].z
      value: -0.0494
      objectReference: {fileID: 0}
    - target: {fileID: 120838229684978756, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_Positions.Array.data[8].x
      value: 3.8000288
      objectReference: {fileID: 0}
    - target: {fileID: 120838229684978756, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_Positions.Array.data[8].y
      value: -0.7287562
      objectReference: {fileID: 0}
    - target: {fileID: 120838229684978756, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_Positions.Array.data[8].z
      value: -0.0494
      objectReference: {fileID: 0}
    - target: {fileID: 120838229684978756, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_Positions.Array.data[9].x
      value: 3.8000288
      objectReference: {fileID: 0}
    - target: {fileID: 120838229684978756, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_Positions.Array.data[9].y
      value: -0.35556316
      objectReference: {fileID: 0}
    - target: {fileID: 120838229684978756, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_Positions.Array.data[9].z
      value: -0.0494
      objectReference: {fileID: 0}
    - target: {fileID: 120838229684978756, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_Positions.Array.data[10].x
      value: 3.8000288
      objectReference: {fileID: 0}
    - target: {fileID: 120838229684978756, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_Positions.Array.data[10].y
      value: 0.017629623
      objectReference: {fileID: 0}
    - target: {fileID: 120838229684978756, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_Positions.Array.data[10].z
      value: -0.0494
      objectReference: {fileID: 0}
    - target: {fileID: 120838229684978756, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_Positions.Array.data[11].x
      value: 3.8000288
      objectReference: {fileID: 0}
    - target: {fileID: 120838229684978756, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_Positions.Array.data[11].y
      value: 0.3908224
      objectReference: {fileID: 0}
    - target: {fileID: 120838229684978756, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_Positions.Array.data[11].z
      value: -0.0494
      objectReference: {fileID: 0}
    - target: {fileID: 120838229684978756, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_Positions.Array.data[12].x
      value: 3.8000288
      objectReference: {fileID: 0}
    - target: {fileID: 120838229684978756, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_Positions.Array.data[12].y
      value: 0.76401615
      objectReference: {fileID: 0}
    - target: {fileID: 120838229684978756, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_Positions.Array.data[12].z
      value: -0.0494
      objectReference: {fileID: 0}
    - target: {fileID: 120838229684978756, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_Positions.Array.data[13].x
      value: 3.8000288
      objectReference: {fileID: 0}
    - target: {fileID: 120838229684978756, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_Positions.Array.data[13].y
      value: 1.1372089
      objectReference: {fileID: 0}
    - target: {fileID: 120838229684978756, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_Positions.Array.data[13].z
      value: -0.0494
      objectReference: {fileID: 0}
    - target: {fileID: 120838229684978756, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_Positions.Array.data[14].x
      value: 3.8000288
      objectReference: {fileID: 0}
    - target: {fileID: 120838229684978756, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_Positions.Array.data[14].y
      value: 1.5104017
      objectReference: {fileID: 0}
    - target: {fileID: 120838229684978756, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
      propertyPath: m_Positions.Array.data[14].z
      value: -0.0494
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 4032aa9ee41392f409331f25fea0dc78, type: 3}
--- !u!1 &1036714688
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1036714692}
  - component: {fileID: 1036714691}
  - component: {fileID: 1036714690}
  - component: {fileID: 1036714689}
  m_Layer: 0
  m_Name: Cube (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!61 &1036714689
BoxCollider2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1036714688}
  m_Enabled: 1
  serializedVersion: 3
  m_Density: 1
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_ForceSendLayers:
    serializedVersion: 2
    m_Bits: 4294967295
  m_ForceReceiveLayers:
    serializedVersion: 2
    m_Bits: 4294967295
  m_ContactCaptureLayers:
    serializedVersion: 2
    m_Bits: 4294967295
  m_CallbackLayers:
    serializedVersion: 2
    m_Bits: 4294967295
  m_IsTrigger: 0
  m_UsedByEffector: 0
  m_CompositeOperation: 0
  m_CompositeOrder: 0
  m_Offset: {x: 0, y: 0}
  m_SpriteTilingProperty:
    border: {x: 0, y: 0, z: 0, w: 0}
    pivot: {x: 0, y: 0}
    oldSize: {x: 0, y: 0}
    newSize: {x: 0, y: 0}
    adaptiveTilingThreshold: 0
    drawMode: 0
    adaptiveTiling: 0
  m_AutoTiling: 0
  m_Size: {x: 0.99999994, y: 0.99999994}
  m_EdgeRadius: 0
--- !u!23 &1036714690
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1036714688}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10303, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1036714691
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1036714688}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &1036714692
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1036714688}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 6.98, y: 0.86, z: 0}
  m_LocalScale: {x: 4.922172, y: 1.0928873, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1044675526
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1044675530}
  - component: {fileID: 1044675529}
  - component: {fileID: 1044675528}
  - component: {fileID: 1044675527}
  m_Layer: 0
  m_Name: Cube (3)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!61 &1044675527
BoxCollider2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1044675526}
  m_Enabled: 1
  serializedVersion: 3
  m_Density: 1
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_ForceSendLayers:
    serializedVersion: 2
    m_Bits: 4294967295
  m_ForceReceiveLayers:
    serializedVersion: 2
    m_Bits: 4294967295
  m_ContactCaptureLayers:
    serializedVersion: 2
    m_Bits: 4294967295
  m_CallbackLayers:
    serializedVersion: 2
    m_Bits: 4294967295
  m_IsTrigger: 0
  m_UsedByEffector: 0
  m_CompositeOperation: 0
  m_CompositeOrder: 0
  m_Offset: {x: 0, y: 0}
  m_SpriteTilingProperty:
    border: {x: 0, y: 0, z: 0, w: 0}
    pivot: {x: 0, y: 0}
    oldSize: {x: 0, y: 0}
    newSize: {x: 0, y: 0}
    adaptiveTilingThreshold: 0
    drawMode: 0
    adaptiveTiling: 0
  m_AutoTiling: 0
  m_Size: {x: 0.99999994, y: 0.99999994}
  m_EdgeRadius: 0
--- !u!23 &1044675528
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1044675526}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10303, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1044675529
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1044675526}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &1044675530
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1044675526}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -12.095, y: -3.981, z: 0}
  m_LocalScale: {x: 3.0430884, y: 2.885853, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &1195774367
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 4242931100932202, guid: f795c82b89d5d994eb38a6217d465022, type: 3}
      propertyPath: m_RootOrder
      value: 14
      objectReference: {fileID: 0}
    - target: {fileID: 4242931100932202, guid: f795c82b89d5d994eb38a6217d465022, type: 3}
      propertyPath: m_LocalScale.x
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 4242931100932202, guid: f795c82b89d5d994eb38a6217d465022, type: 3}
      propertyPath: m_LocalScale.y
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 4242931100932202, guid: f795c82b89d5d994eb38a6217d465022, type: 3}
      propertyPath: m_LocalScale.z
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 4242931100932202, guid: f795c82b89d5d994eb38a6217d465022, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4242931100932202, guid: f795c82b89d5d994eb38a6217d465022, type: 3}
      propertyPath: m_LocalPosition.y
      value: -2.76
      objectReference: {fileID: 0}
    - target: {fileID: 4242931100932202, guid: f795c82b89d5d994eb38a6217d465022, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4242931100932202, guid: f795c82b89d5d994eb38a6217d465022, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4242931100932202, guid: f795c82b89d5d994eb38a6217d465022, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4242931100932202, guid: f795c82b89d5d994eb38a6217d465022, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4242931100932202, guid: f795c82b89d5d994eb38a6217d465022, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: f795c82b89d5d994eb38a6217d465022, type: 3}
--- !u!1 &1198396411
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1198396412}
  m_Layer: 0
  m_Name: --------------------
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &1198396412
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1198396411}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 2.2099159, y: 29.37389, z: -4.015108}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1421660175 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 1692231914730114, guid: dcb0194d2a6edb74d9bb0051e4a4b702, type: 3}
  m_PrefabInstance: {fileID: 1778347945}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1710261404
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1710261408}
  - component: {fileID: 1710261407}
  - component: {fileID: 1710261405}
  m_Layer: 5
  m_Name: Canvas
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1710261405
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1710261404}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0cd44c1031e13a943bb63640046fad76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UiScaleMode: 1
  m_ReferencePixelsPerUnit: 100
  m_ScaleFactor: 1
  m_ReferenceResolution: {x: 1920, y: 1080}
  m_ScreenMatchMode: 0
  m_MatchWidthOrHeight: 0
  m_PhysicalUnit: 3
  m_FallbackScreenDPI: 96
  m_DefaultSpriteDPI: 96
  m_DynamicPixelsPerUnit: 1
  m_PresetInfoIsWorld: 0
--- !u!223 &1710261407
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1710261404}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 0
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_VertexColorAlwaysGammaSpace: 0
  m_AdditionalShaderChannelsFlag: 0
  m_UpdateRectTransformForStandalone: 0
  m_SortingLayerID: 0
  m_SortingOrder: 0
  m_TargetDisplay: 0
--- !u!224 &1710261408
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1710261404}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0, y: 0, z: 0}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 395217918}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0, y: 0}
--- !u!1001 &1778347945
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 4855695790697426, guid: dcb0194d2a6edb74d9bb0051e4a4b702, type: 3}
      propertyPath: m_RootOrder
      value: 12
      objectReference: {fileID: 0}
    - target: {fileID: 4855695790697426, guid: dcb0194d2a6edb74d9bb0051e4a4b702, type: 3}
      propertyPath: m_LocalPosition.x
      value: -3.31
      objectReference: {fileID: 0}
    - target: {fileID: 4855695790697426, guid: dcb0194d2a6edb74d9bb0051e4a4b702, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.16000003
      objectReference: {fileID: 0}
    - target: {fileID: 4855695790697426, guid: dcb0194d2a6edb74d9bb0051e4a4b702, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4855695790697426, guid: dcb0194d2a6edb74d9bb0051e4a4b702, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4855695790697426, guid: dcb0194d2a6edb74d9bb0051e4a4b702, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4855695790697426, guid: dcb0194d2a6edb74d9bb0051e4a4b702, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4855695790697426, guid: dcb0194d2a6edb74d9bb0051e4a4b702, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 114962761562329234, guid: dcb0194d2a6edb74d9bb0051e4a4b702, type: 3}
      propertyPath: CollisionMode
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114962761562329234, guid: dcb0194d2a6edb74d9bb0051e4a4b702, type: 3}
      propertyPath: _Editor_Category
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 114962761562329234, guid: dcb0194d2a6edb74d9bb0051e4a4b702, type: 3}
      propertyPath: _Editor_FeaturesCategory
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114962761562329234, guid: dcb0194d2a6edb74d9bb0051e4a4b702, type: 3}
      propertyPath: _editor_IsInspectorViewingColliders
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114962761562329234, guid: dcb0194d2a6edb74d9bb0051e4a4b702, type: 3}
      propertyPath: _editor_IsInspectorViewingIncludedColliders
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: dcb0194d2a6edb74d9bb0051e4a4b702, type: 3}
--- !u!1 &1782490128
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1782490132}
  - component: {fileID: 1782490131}
  - component: {fileID: 1782490130}
  - component: {fileID: 1782490129}
  m_Layer: 0
  m_Name: Cube (4)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!61 &1782490129
BoxCollider2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1782490128}
  m_Enabled: 1
  serializedVersion: 3
  m_Density: 1
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_ForceSendLayers:
    serializedVersion: 2
    m_Bits: 4294967295
  m_ForceReceiveLayers:
    serializedVersion: 2
    m_Bits: 4294967295
  m_ContactCaptureLayers:
    serializedVersion: 2
    m_Bits: 4294967295
  m_CallbackLayers:
    serializedVersion: 2
    m_Bits: 4294967295
  m_IsTrigger: 0
  m_UsedByEffector: 0
  m_CompositeOperation: 0
  m_CompositeOrder: 0
  m_Offset: {x: 0, y: 0}
  m_SpriteTilingProperty:
    border: {x: 0, y: 0, z: 0, w: 0}
    pivot: {x: 0, y: 0}
    oldSize: {x: 0, y: 0}
    newSize: {x: 0, y: 0}
    adaptiveTilingThreshold: 0
    drawMode: 0
    adaptiveTiling: 0
  m_AutoTiling: 0
  m_Size: {x: 0.99999994, y: 0.99999994}
  m_EdgeRadius: 0
--- !u!23 &1782490130
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1782490128}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10303, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1782490131
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1782490128}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &1782490132
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1782490128}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 12.51, y: 0.561, z: 0}
  m_LocalScale: {x: 1.8586663, y: 11.967626, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1801063778
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1801063780}
  - component: {fileID: 1801063779}
  m_Layer: 0
  m_Name: Hanging Duplicate
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1801063779
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1801063778}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3dfe6c6e407a69b4e8a0e2d57d3835d3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  ToDuplicate: {fileID: 1421660175}
  DuplicatesCount: {x: 4.25, y: 1, z: 1}
  Offsets: {x: 1.08, y: 0, z: 3}
  Randomize: {x: 0, y: 0, z: 0}
  RandomRotate: {x: 0, y: 0, z: 0}
  RandomScale: {x: 0, y: 0, z: 0}
  Seed: -1553065832
  PlaceOnGround: 0
  DuplicateAtStart: 1
  GizmosSize: 1
  DuplicationType: 0
  DuplicationOrigin: 1
--- !u!4 &1801063780
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1801063778}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -8.52, y: 0.18, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &1916855448
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 4424248143717148, guid: b77d4950086e717448f213774e627618, type: 3}
      propertyPath: m_RootOrder
      value: 17
      objectReference: {fileID: 0}
    - target: {fileID: 4424248143717148, guid: b77d4950086e717448f213774e627618, type: 3}
      propertyPath: m_LocalScale.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 4424248143717148, guid: b77d4950086e717448f213774e627618, type: 3}
      propertyPath: m_LocalScale.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 4424248143717148, guid: b77d4950086e717448f213774e627618, type: 3}
      propertyPath: m_LocalScale.z
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 4424248143717148, guid: b77d4950086e717448f213774e627618, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0.24
      objectReference: {fileID: 0}
    - target: {fileID: 4424248143717148, guid: b77d4950086e717448f213774e627618, type: 3}
      propertyPath: m_LocalPosition.y
      value: 4.67
      objectReference: {fileID: 0}
    - target: {fileID: 4424248143717148, guid: b77d4950086e717448f213774e627618, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4424248143717148, guid: b77d4950086e717448f213774e627618, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4424248143717148, guid: b77d4950086e717448f213774e627618, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4424248143717148, guid: b77d4950086e717448f213774e627618, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4424248143717148, guid: b77d4950086e717448f213774e627618, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: b77d4950086e717448f213774e627618, type: 3}
--- !u!1660057539 &9223372036854775807
SceneRoots:
  m_ObjectHideFlags: 0
  m_Roots:
  - {fileID: 770490215}
  - {fileID: 1195774367}
  - {fileID: 380034316}
  - {fileID: 1778347945}
  - {fileID: 1916855448}
  - {fileID: 69809309}
  - {fileID: 633614821}
  - {fileID: 1710261408}
  - {fileID: 1198396412}
  - {fileID: 636626405}
  - {fileID: 232771911}
  - {fileID: 1036714692}
  - {fileID: 1044675530}
  - {fileID: 1782490132}
  - {fileID: 518919839}
  - {fileID: 236733681}
  - {fileID: 1801063780}
  - {fileID: 655947740}
