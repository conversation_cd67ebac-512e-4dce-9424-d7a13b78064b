%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &1276994386544440
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4355996818756374}
  - component: {fileID: 33221430460128480}
  - component: {fileID: 23367631545971552}
  - component: {fileID: 114338421657796410}
  m_Layer: 0
  m_Name: PR_ShapeC
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4355996818756374
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1276994386544440}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &33221430460128480
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1276994386544440}
  m_Mesh: {fileID: 4300002, guid: 83bf86f790e89d3499d7877c8d4a370a, type: 3}
--- !u!23 &23367631545971552
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1276994386544440}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 007af56d1ea720046b82b918a580276f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!114 &114338421657796410
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1276994386544440}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 488b95f6070988f45b11ba45372d0c65, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  AddToContainer: 1
  _editor_DrawSetup: 1
  _editor_DrawOptimizeList: 1
  _editor_DrawAddFeatures: 0
  _editor_DrawLODLevelsSetup: 1
  _editor_DrawExtra: 0
  _editor_horizontal: 1
  _editor_category: 0
  Editor_InIsolatedScene: 0
  Editor_JustCreated: 0
  UseDOTS: 0
  DOTSObstacleType: 2
  DOTSDetection: 0
  DOTSMeshData: []
  DOTSOffset: {x: 0, y: 0, z: 0}
  DOTSSize: {x: 1, y: 1, z: 1}
  DOTSRadius: 1
  UseMultiShape: 1
  AutoPrecision: 0.25
  AutoReferenceMesh: {fileID: 4300002, guid: 83bf86f790e89d3499d7877c8d4a370a, type: 3}
  DrawPositionHandles: 1
  ScalingHandles: 1
  Shapes:
  - position: {x: 0.08619839, y: 17.702183, z: -0.115288034}
    radius: 1.1235
    transform: {fileID: 0}
  - position: {x: 0.0002131852, y: 14.027794, z: 0.45319417}
    radius: 1
    transform: {fileID: 0}
  - position: {x: 0.000613282, y: 11.606506, z: 1.1981006}
    radius: 0.5417158
    transform: {fileID: 0}
  - position: {x: 0.044726253, y: 9.552132, z: 0.8271183}
    radius: 0.6600001
    transform: {fileID: 0}
  - position: {x: -0.0847437, y: 6.9084735, z: -0.10686806}
    radius: 0.75
    transform: {fileID: 0}
  - position: {x: -0.33760267, y: 3.538907, z: 0.013735311}
    radius: 0.8258579
    transform: {fileID: 0}
  - position: {x: 0.0510103, y: 0.43461108, z: -0.08067466}
    radius: 1
    transform: {fileID: 0}
  - position: {x: -5.3286066, y: 0.9346111, z: -3.6765}
    radius: 1.7709296
    transform: {fileID: 0}
  - position: {x: -11.155368, y: -0.32534122, z: -6.87644}
    radius: 1.4782841
    transform: {fileID: 0}
  ShapePos: []
  ShapeRadius: []
  UseObstacleDetection: 0
  CoveragePrecision: 1
  CoverageScale: 1
  CoverageMask:
    serializedVersion: 2
    m_Bits: 1
  CustomCoveragePoints: 0
  CoverageOffsets: []
  ignoredObstacleColliders: []
  OnlyCamCollLayer:
    serializedVersion: 2
    m_Bits: 0
  LODLevels: 3
  preLODLevels: 3
  MaxDistance: 333
  OptimizingMethod: 0
  CullIfNotSee: 1
  DetectionRadius: 2.64
  DetectionBounds: {x: 4.864055, y: 21.107992, z: 4.488134}
  Hideable: 1
  DetectionOffset: {x: 0.00006049127, y: 9.220398, z: 0.054772623}
  GizmosAlpha: 1
  DrawGizmos: 1
  FadeDuration: 0
  FadeViewVisibility: 0
  DeactivateObject: 0
  LODPercent:
  - 0.11347811
  - 0.48027572
  - 1
  AutoDistance: 0
  AutoDistanceFactor: 0
  DrawAutoDistanceToggle: 1
  HiddenCullAt: -1
  LimitLODLevels: 0
  UnlockFirstLOD: 0
  DrawGeneratedPrefabInfo: 0
  DrawDeactivateToggle: 1
  OptimizationTypes: {fileID: 0}
  ToOptimize:
  - constructed: 1
    ToOptimizeIndex: 1
    optimizer: {fileID: 114338421657796410}
    Component: {fileID: 23367631545971552}
    Version: 0
    lockFirstLOD: 1
    editorHeader: Optim
    drawProperties: 1
    LODs_Particle: []
    LODs_Light: []
    LODs_Mono: []
    LODs_Renderer:
    - SetDisabled: 0
      _version: 1
      _Locked: 0
      cmp: {fileID: 0}
      UseShadows: 1
      MotionVectors: 1
      SkinnedQuality: 0
      shaderParam: _Transparency
      ColorParameter: 0
      targetParamValue: 1
    - SetDisabled: 0
      _version: 1
      _Locked: 0
      cmp: {fileID: 0}
      UseShadows: 1
      MotionVectors: 0
      SkinnedQuality: 0
      shaderParam: _Transparency
      ColorParameter: 0
      targetParamValue: 1
    - SetDisabled: 0
      _version: 1
      _Locked: 0
      cmp: {fileID: 0}
      UseShadows: 0
      MotionVectors: 2
      SkinnedQuality: 1
      shaderParam: _Transparency
      ColorParameter: 0
      targetParamValue: 1
    - SetDisabled: 1
      _version: 1
      _Locked: 0
      cmp: {fileID: 0}
      UseShadows: 0
      MotionVectors: 2
      SkinnedQuality: 1
      shaderParam: _Transparency
      ColorParameter: 0
      targetParamValue: 1
    - SetDisabled: 1
      _version: 1
      _Locked: 0
      cmp: {fileID: 0}
      UseShadows: 0
      MotionVectors: 2
      SkinnedQuality: 1
      shaderParam: _Transparency
      ColorParameter: 0
      targetParamValue: 1
    LODs_NavMesh: []
    LODs_Audio: []
    LODs_Rigidbody: []
    LODs_LODGroup: []
    Ini_Particle:
      SetDisabled: 0
      QLowerer: 1
      _Locked: 0
      cmp: {fileID: 0}
      EmmissionAmount: 1
      BurstsAmount: 1
      ParticleSizeMul: 1
      MaxParticlAmount: 1
      OverDistanceMul: 1
      LifetimeAlpha: 1
      ChangeBursts: 1
      ChangeGradients: 1
      ColorOverLifetime:
        m_Mode: 0
        m_GradientMin:
          serializedVersion: 2
          key0: {r: 1, g: 1, b: 1, a: 1}
          key1: {r: 1, g: 1, b: 1, a: 1}
          key2: {r: 0, g: 0, b: 0, a: 0}
          key3: {r: 0, g: 0, b: 0, a: 0}
          key4: {r: 0, g: 0, b: 0, a: 0}
          key5: {r: 0, g: 0, b: 0, a: 0}
          key6: {r: 0, g: 0, b: 0, a: 0}
          key7: {r: 0, g: 0, b: 0, a: 0}
          ctime0: 0
          ctime1: 65535
          ctime2: 0
          ctime3: 0
          ctime4: 0
          ctime5: 0
          ctime6: 0
          ctime7: 0
          atime0: 0
          atime1: 65535
          atime2: 0
          atime3: 0
          atime4: 0
          atime5: 0
          atime6: 0
          atime7: 0
          m_Mode: 0
          m_ColorSpace: -1
          m_NumColorKeys: 2
          m_NumAlphaKeys: 2
        m_GradientMax:
          serializedVersion: 2
          key0: {r: 1, g: 1, b: 1, a: 1}
          key1: {r: 1, g: 1, b: 1, a: 1}
          key2: {r: 0, g: 0, b: 0, a: 0}
          key3: {r: 0, g: 0, b: 0, a: 0}
          key4: {r: 0, g: 0, b: 0, a: 0}
          key5: {r: 0, g: 0, b: 0, a: 0}
          key6: {r: 0, g: 0, b: 0, a: 0}
          key7: {r: 0, g: 0, b: 0, a: 0}
          ctime0: 0
          ctime1: 65535
          ctime2: 0
          ctime3: 0
          ctime4: 0
          ctime5: 0
          ctime6: 0
          ctime7: 0
          atime0: 0
          atime1: 65535
          atime2: 0
          atime3: 0
          atime4: 0
          atime5: 0
          atime6: 0
          atime7: 0
          m_Mode: 0
          m_ColorSpace: -1
          m_NumColorKeys: 2
          m_NumAlphaKeys: 2
        m_ColorMin: {r: 0, g: 0, b: 0, a: 0}
        m_ColorMax: {r: 0, g: 0, b: 0, a: 0}
    Ini_Light:
      SetDisabled: 0
      _Locked: 0
      cmp: {fileID: 0}
      IntensityMul: 1
      RangeMul: 1
      ShadowsMode: 2
      ShadowsStrength: 1
      RenderMode: 0
      ChangeIntensity: 1
    Ini_Mono:
      SetDisabled: 0
      ver: 0
      _Locked: 0
      cmp: {fileID: 0}
      BaseLOD: 0
      Event:
        m_PersistentCalls:
          m_Calls: []
      Parameters: []
      NotSupported: []
    Ini_Rend:
      SetDisabled: 0
      _version: 1
      _Locked: 0
      cmp: {fileID: 0}
      UseShadows: 1
      MotionVectors: 1
      SkinnedQuality: 0
      shaderParam: _Transparency
      ColorParameter: 0
      targetParamValue: 1
    Ini_Nav:
      SetDisabled: 0
      _Locked: 0
      cmp: {fileID: 0}
      Priority: 1
      Quality: 4
    Ini_Audio:
      SetDisabled: 0
      _Locked: 0
      cmp: {fileID: 0}
      PriorityFactor: 1
      Volume: 1
    Ini_Rigidbody:
      SetDisabled: 0
      _Locked: 0
      cmp: {fileID: 0}
      DetectCollisions: 0
      IsKinematic: 0
      Interpolation: 0
      CollisionMode: 0
      TryTriggerSleep: 0
      TriggerWakeup: 0
      ChangeIsKinematic: 1
    Ini_LODGroup:
      SetDisabled: 0
      _version: 1
      _Locked: 0
      cmp: {fileID: 0}
      UnityLODLevel: 0
      shaderParam: _Transparency
      ColorParameter: 0
      shaderVisibleValue: 1
      shaderInvisibleValue: 0
      crossfadeHelper: 0
    eOptimizer: {fileID: 114338421657796410}
    ControlerType: 4
