<?xml version="1.0" encoding="UTF-8"?><package><identifier v="Unsaved Package"/><formatVersion v="1.1.0.202302"/><updaterVersion v="1.1.0.202302"/><fileUID v="{ccaddfab-26a5-47b3-be31-c4adea564934}"/><versionUID v="0"/><dependencies><dependency><filename v="sbs://pattern_tile_sampler.sbs"/><uid v="1449399972"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://hbao_2.sbs"/><uid v="1297162667"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://flood_fill_2.sbs"/><uid v="1323881949"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://histogram_scan.sbs"/><uid v="1149349207"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://slope_blur.sbs"/><uid v="1449402040"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_perlin_noise.sbs"/><uid v="1407332030"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://non_uniform_blur.sbs"/><uid v="1290776959"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://invert.sbs"/><uid v="1368115694"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://floodfill_mapper.sbs"/><uid v="1383758355"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://curvature_smooth.sbs"/><uid v="1449590280"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://gradient_radial.sbs"/><uid v="1383765856"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://height_blend.sbs"/><uid v="1449623608"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://histogram_range.sbs"/><uid v="1383033101"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://auto_levels.sbs"/><uid v="1383786570"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_grunge_map_014.sbs"/><uid v="1383791918"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://contrast_luminosity.sbs"/><uid v="1146607502"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://gradient_linear_1.sbs"/><uid v="1357065436"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://waveform_1.sbs"/><uid v="1416542616"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency><dependency><filename v="sbs://noise_grunge_map_013.sbs"/><uid v="1383771917"/><type v="package"/><fileUID v="0"/><versionUID v="0"/></dependency></dependencies><content><graph><identifier v="stone_path"/><uid v="1383686873"/><attributes><category v="Stylized, Tiles, Path, Floor"/><author v="3dex"/><authorURL v="3dex.net"/><tags v="tiles, floor, stone path, terrain, grass"/></attributes><paraminputs><paraminput><identifier v="x_amount"/><uid v="1489346256"/><attributes><label v="X Amount"/></attributes><type v="16"/><defaultValue><constantValueInt1 v="4"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="4"/></option><option><name v="isRandomizable"/><value v="0"/></option><option><name v="max"/><value v="64"/></option><option><name v="min"/><value v="1"/></option><option><name v="step"/><value v="1"/></option></options></defaultWidget><group v="Tiles"/></paraminput><paraminput><identifier v="y_amount"/><uid v="1489347853"/><attributes><label v="Y Amount"/></attributes><type v="16"/><defaultValue><constantValueInt1 v="4"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="4"/></option><option><name v="isRandomizable"/><value v="0"/></option><option><name v="max"/><value v="64"/></option><option><name v="min"/><value v="1"/></option><option><name v="step"/><value v="1"/></option></options></defaultWidget><group v="Tiles"/></paraminput><paraminput><identifier v="offset"/><uid v="1489366977"/><attributes><label v="Offset"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Tiles"/></paraminput><paraminput><identifier v="mask_random"/><uid v="1489368575"/><attributes><label v="Mask Random"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Tiles"/></paraminput><paraminput><identifier v="opacitymult"/><uid v="1489372969"/><attributes><label v="Opacity"/><description v="The &lt;b&gt;Opacity&lt;/b&gt; parameter sets the transparency of the blend"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="0"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Foliage"/></paraminput><paraminput><identifier v="x_amount_1"/><uid v="1489373907"/><attributes><label v="X Amount"/></attributes><type v="16"/><defaultValue><constantValueInt1 v="177"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="177"/></option><option><name v="isRandomizable"/><value v="0"/></option><option><name v="max"/><value v="250"/></option><option><name v="min"/><value v="1"/></option><option><name v="step"/><value v="1"/></option></options></defaultWidget><group v="Foliage"/></paraminput><paraminput><identifier v="y_amount_1"/><uid v="1489374830"/><attributes><label v="Y Amount"/></attributes><type v="16"/><defaultValue><constantValueInt1 v="185"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="185"/></option><option><name v="isRandomizable"/><value v="0"/></option><option><name v="max"/><value v="250"/></option><option><name v="min"/><value v="1"/></option><option><name v="step"/><value v="1"/></option></options></defaultWidget><group v="Foliage"/></paraminput><paraminput><identifier v="scale"/><uid v="1489375753"/><attributes><label v="Scale"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="16.1000004"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="16.1"/></option><option><name v="max"/><value v="10"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Foliage"/></paraminput><paraminput><identifier v="scale_random"/><uid v="1489376675"/><attributes><label v="Scale Random"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.300000012"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.3"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Foliage"/></paraminput><paraminput><identifier v="position_random"/><uid v="1489380351"/><attributes><label v="Position Random"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.129999995"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="0.13"/></option><option><name v="max"/><value v="10"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Foliage"/></paraminput><paraminput><identifier v="mask_map_threshold"/><uid v="1489385860"/><attributes><label v="Mask Map Threshold"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.790000021"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.79"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Foliage"/></paraminput><paraminput><identifier v="mask_random_1"/><uid v="1489386782"/><attributes><label v="Mask Random"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.629999995"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.63"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Foliage"/></paraminput><paraminput><identifier v="opacitymult_1"/><uid v="1489390567"/><attributes><label v="Opacity"/><description v="The &lt;b&gt;Opacity&lt;/b&gt; parameter sets the transparency of the blend"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="0"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Pebbles"/></paraminput><paraminput><identifier v="x_amount_2"/><uid v="1489391526"/><attributes><label v="X Amount"/></attributes><type v="16"/><defaultValue><constantValueInt1 v="64"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="64"/></option><option><name v="isRandomizable"/><value v="0"/></option><option><name v="max"/><value v="128"/></option><option><name v="min"/><value v="1"/></option><option><name v="step"/><value v="1"/></option></options></defaultWidget><group v="Pebbles"/></paraminput><paraminput><identifier v="y_amount_2"/><uid v="1489392492"/><attributes><label v="Y Amount"/></attributes><type v="16"/><defaultValue><constantValueInt1 v="64"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="64"/></option><option><name v="isRandomizable"/><value v="0"/></option><option><name v="max"/><value v="128"/></option><option><name v="min"/><value v="1"/></option><option><name v="step"/><value v="1"/></option></options></defaultWidget><group v="Pebbles"/></paraminput><paraminput><identifier v="scale_1"/><uid v="1489393459"/><attributes><label v="Scale"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="2.48000002"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="2.48"/></option><option><name v="max"/><value v="10"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Pebbles"/></paraminput><paraminput><identifier v="mask_map_threshold_1"/><uid v="1489398271"/><attributes><label v="Mask Map Threshold"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="1"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="1"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Pebbles"/></paraminput><paraminput><identifier v="mask_random_2"/><uid v="1489399237"/><attributes><label v="Mask Random"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.400000006"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.4"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Pebbles"/></paraminput><paraminput><identifier v="scale_2"/><uid v="1489400218"/><attributes><label v="Scale"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.910000026"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="0.91"/></option><option><name v="max"/><value v="10"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Tiles"/></paraminput><paraminput><identifier v="outputcolor"/><uid v="1489402201"/><attributes><label v="Tiles"/><description v="The &lt;b&gt;Color Output&lt;/b&gt; parameter creates a uniform color output image"/></attributes><type v="2048"/><defaultValue><constantValueFloat4 v="0.431373 0.427451015 0.36470601 1"/></defaultValue><defaultWidget><name v="color"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.431373;0.427451;0.364706;1"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="valueInterpretation"/><value v="color"/></option></options></defaultWidget><group v="Color"/></paraminput><paraminput><identifier v="outputcolor_1"/><uid v="1489402228"/><attributes><label v="Dirt"/><description v="The &lt;b&gt;Color Output&lt;/b&gt; parameter creates a uniform color output image"/></attributes><type v="2048"/><defaultValue><constantValueFloat4 v="0.407842994 0.305882007 0.211765006 1"/></defaultValue><defaultWidget><name v="color"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.407843;0.305882;0.211765;1"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="valueInterpretation"/><value v="color"/></option></options></defaultWidget><group v="Color"/></paraminput><paraminput><identifier v="outputcolor_2"/><uid v="1489402326"/><attributes><label v="Foliage"/><description v="The &lt;b&gt;Color Output&lt;/b&gt; parameter creates a uniform color output image"/></attributes><type v="2048"/><defaultValue><constantValueFloat4 v="0.494118005 0.549019992 0.337255001 1"/></defaultValue><defaultWidget><name v="color"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.494118;0.54902;0.337255;1"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="valueInterpretation"/><value v="color"/></option></options></defaultWidget><group v="Color"/></paraminput><paraminput><identifier v="outputcolor_3"/><uid v="1489402440"/><attributes><label v="Pebbles"/><description v="The &lt;b&gt;Color Output&lt;/b&gt; parameter creates a uniform color output image"/></attributes><type v="2048"/><defaultValue><constantValueFloat4 v="0.529411972 0.49019599 0.431373 1"/></defaultValue><defaultWidget><name v="color"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.529412;0.490196;0.431373;1"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="valueInterpretation"/><value v="color"/></option></options></defaultWidget><group v="Color"/></paraminput><paraminput><identifier v="Luminosity"/><uid v="1489402597"/><attributes><label v="Tiles"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="-1"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="Roughness"/></paraminput><paraminput><identifier v="outputcolor_4"/><uid v="1489402669"/><attributes><label v="Pebbles"/><description v="The &lt;b&gt;Color Output&lt;/b&gt; parameter creates a uniform color output image"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.887097001"/></defaultValue><defaultWidget><name v="color"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.887097"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="valueInterpretation"/><value v="color"/></option></options></defaultWidget><group v="Roughness"/></paraminput><paraminput><identifier v="outputcolor_5"/><uid v="1489402708"/><attributes><label v="Foliage"/><description v="The &lt;b&gt;Color Output&lt;/b&gt; parameter creates a uniform color output image"/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.774194002"/></defaultValue><defaultWidget><name v="color"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.774194"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="valueInterpretation"/><value v="color"/></option></options></defaultWidget><group v="Roughness"/></paraminput><paraminput><identifier v="intensity"/><uid v="1489402747"/><attributes><label v="Normal Intensity"/><description v="The &lt;b&gt;Intensity&lt;/b&gt; parameter modifies the intensity of height map "/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="10"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="0"/></option><option><name v="default"/><value v="10"/></option><option><name v="max"/><value v="50"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget></paraminput><paraminput><identifier v="inversedy"/><uid v="1489402753"/><attributes><label v="Normal Format"/><description v="The &lt;b&gt;Normal Format&lt;/b&gt; parameter inverts y coordinates of height map (Open-GL)"/></attributes><type v="4"/><defaultValue><constantValueBool v="0"/></defaultValue><defaultWidget><name v="buttons"/><options><option><name v="booleditortype"/><value v="enumbuttons"/></option><option><name v="default"/><value v="0"/></option><option><name v="label0"/><value v="DirectX"/></option><option><name v="label1"/><value v="OpenGL"/></option></options></defaultWidget></paraminput><paraminput><identifier v="height_depth"/><uid v="1489402770"/><attributes><label v="Height Depth"/><description v="Defines the height map scale compared to the image size.&#10;A value of 1 means the height map depth is the same as its the largest border."/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="0.0700000003"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="0.07"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="AO"/></paraminput><paraminput><identifier v="radius"/><uid v="1489403673"/><attributes><label v="Radius"/><description v="Adjusts the Ambient Occlusion radius. Corresponds to the &quot;Max Occluder Distance&quot; parameter in the bakers.&#10;A value of 1 equals to the largest border of the image."/></attributes><type v="256"/><defaultValue><constantValueFloat1 v="1"/></defaultValue><defaultWidget><name v="slider"/><options><option><name v="clamp"/><value v="1"/></option><option><name v="default"/><value v="1"/></option><option><name v="max"/><value v="1"/></option><option><name v="min"/><value v="0"/></option><option><name v="step"/><value v="0.01"/></option></options></defaultWidget><group v="AO"/></paraminput></paraminputs><graphOutputs><graphoutput><identifier v="basecolor"/><uid v="1213284336"/><attributes><label v="Base Color"/></attributes><usages><usage><components v="RGBA"/><name v="baseColor"/></usage></usages><group v="Material"/></graphoutput><graphoutput><identifier v="normal"/><uid v="1213284338"/><attributes><label v="Normal"/></attributes><usages><usage><components v="RGBA"/><name v="normal"/></usage></usages><group v="Material"/></graphoutput><graphoutput><identifier v="roughness"/><uid v="1213284340"/><attributes><label v="Roughness"/></attributes><usages><usage><components v="RGBA"/><name v="roughness"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput><graphoutput><identifier v="metallic"/><uid v="1213284342"/><attributes><label v="Metallic"/></attributes><usages><usage><components v="RGBA"/><name v="metallic"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput><graphoutput><identifier v="height"/><uid v="1279137031"/><attributes><label v="Height"/></attributes><usages><usage><components v="RGBA"/><name v="height"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput><graphoutput><identifier v="AO"/><uid v="1359211721"/><attributes><label v="AO"/></attributes><usages><usage><components v="RGBA"/><name v="ambientOcclusion"/></usage></usages><channels v="2"/><group v="Material"/></graphoutput></graphOutputs><compNodes><compNode><uid v="1213284337"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1383770821"/><connRefOutput v="1383770822"/></connection></connections><GUILayout><gpos v="1776 -31.5667725 0"/></GUILayout><compImplementation><compOutputBridge><output v="1213284336"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1213284339"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1489336508"/><connRefOutput v="1359211384"/></connection></connections><GUILayout><gpos v="1776 640.433228 0"/></GUILayout><compImplementation><compOutputBridge><output v="1213284338"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1213284341"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1383798306"/><connRefOutput v="1383798307"/></connection></connections><GUILayout><gpos v="1776 800.433228 0"/></GUILayout><compImplementation><compOutputBridge><output v="1213284340"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1213284343"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1359211407"/><connRefOutput v="1359211408"/></connection></connections><GUILayout><gpos v="1776 960.433228 0"/></GUILayout><compImplementation><compOutputBridge><output v="1213284342"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1279137030"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1383885185"/><connRefOutput v="1383767942"/></connection></connections><GUILayout><gpos v="1776 1648 0"/></GUILayout><compImplementation><compOutputBridge><output v="1279137031"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1359211383"/><connections><connection><identifier v="input1"/><connRef v="1383762934"/><connRefOutput v="1383762935"/></connection></connections><GUILayout><gpos v="-624 -400 0"/></GUILayout><compOutputs><compOutput><uid v="1359211384"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="normal"/><parameters><parameter><name v="input2alpha"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="10"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1359211391"/><GUILayout><gpos v="848 848 0"/></GUILayout><compOutputs><compOutput><uid v="1359211392"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="uniform"/><parameters><parameter><name v="colorswitch"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="outputcolor"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1489402709"/><paramNodes><paramNode><uid v="1489402709"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="outputcolor_5"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1359211407"/><GUILayout><gpos v="1648 976 0"/></GUILayout><compOutputs><compOutput><uid v="1359211408"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="uniform"/><parameters><parameter><name v="colorswitch"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1359211720"/><connections><connection><identifier v="inputNodeOutput"/><connRef v="1383692389"/><connRefOutput v="1383692390"/></connection></connections><GUILayout><gpos v="1776 1280.43323 0"/></GUILayout><compImplementation><compOutputBridge><output v="1359211721"/></compOutputBridge></compImplementation></compNode><compNode><uid v="1383690730"/><GUILayout><gpos v="-4304 -240 0"/></GUILayout><compOutputs><compOutput><uid v="1383690731"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1449399972"/><parameters><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1489347854"/><paramNodes><paramNode><uid v="1489347854"/><function v="get_integer1"/><type v="16"/><funcDatas><funcData><name v="get_integer1"/><constantValue><constantValueString v="y_amount"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0299999993"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1489400219"/><paramNodes><paramNode><uid v="1489400219"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="scale_2"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0199999996"/></paramValue></parameter><parameter><name v="color_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.329999983"/></paramValue></parameter><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1489346257"/><paramNodes><paramNode><uid v="1489346257"/><function v="get_integer1"/><type v="16"/><funcDatas><funcData><name v="get_integer1"/><constantValue><constantValueString v="x_amount"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1489366978"/><paramNodes><paramNode><uid v="1489366978"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="offset"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter><parameter><name v="mask_random"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1489368576"/><paramNodes><paramNode><uid v="1489368576"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="mask_random"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1383690731"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1383692389"/><connections><connection><identifier v="input"/><connRef v="1383767941"/><connRefOutput v="1383767942"/></connection></connections><GUILayout><gpos v="336 1280.53552 0"/></GUILayout><compOutputs><compOutput><uid v="1383692390"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///hbao?dependency=1297162667"/><parameters><parameter><name v="height_depth"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1489402771"/><paramNodes><paramNode><uid v="1489402771"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="height_depth"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter><parameter><name v="radius"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1489403674"/><paramNodes><paramNode><uid v="1489403674"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="radius"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1383692390"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1383697560"/><connections><connection><identifier v="mask"/><connRef v="1383690730"/><connRefOutput v="1383690731"/></connection></connections><GUILayout><gpos v="-4050.16382 -304 0"/></GUILayout><compOutputs><compOutput><uid v="1383697561"/><comptype v="1"/></compOutput><compOutput><uid v="1383697562"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill?dependency=1323881949"/><parameters/><outputBridgings><outputBridging><uid v="1383697561"/><identifier v="output"/></outputBridging><outputBridging><uid v="1383697562"/><identifier v="referenceMap"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1383697826"/><connections><connection><identifier v="input"/><connRef v="1383697560"/><connRefOutput v="1383697561"/></connection></connections><GUILayout><gpos v="-3890.16382 -304 0"/></GUILayout><compOutputs><compOutput><uid v="1383697827"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.589999974"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1383697827"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1383697930"/><connections><connection><identifier v="destination"/><connRef v="1383690730"/><connRefOutput v="1383690731"/></connection><connection><identifier v="source"/><connRef v="1383697826"/><connRefOutput v="1383697827"/></connection></connections><GUILayout><gpos v="-3890.16382 -112 0"/></GUILayout><compOutputs><compOutput><uid v="1383697931"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.379999995"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1383701073"/><connections><connection><identifier v="Input_1"/><connRef v="1383697826"/><connRefOutput v="1383697827"/></connection></connections><GUILayout><gpos v="-3744.07617 -286.957031 0"/></GUILayout><compOutputs><compOutput><uid v="1383701074"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1149349207"/><parameters><parameter><name v="Invert_Position"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.449999988"/></paramValue></parameter><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.239999995"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1383701074"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1383701380"/><connections><connection><identifier v="destination"/><connRef v="1383697930"/><connRefOutput v="1383697931"/></connection><connection><identifier v="source"/><connRef v="1383701073"/><connRefOutput v="1383701074"/></connection></connections><GUILayout><gpos v="-3730.16382 -112 0"/></GUILayout><compOutputs><compOutput><uid v="1383701381"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1383704204"/><connections><connection><identifier v="Input_1"/><connRef v="1383704205"/><connRefOutput v="1383697827"/></connection></connections><GUILayout><gpos v="-3730.16382 -416.414185 0"/></GUILayout><compOutputs><compOutput><uid v="1383701074"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1149349207"/><parameters><parameter><name v="Invert_Position"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.449999988"/></paramValue></parameter><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.229999989"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1383701074"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1383704205"/><connections><connection><identifier v="input"/><connRef v="1383697560"/><connRefOutput v="1383697561"/></connection></connections><GUILayout><gpos v="-3876.25122 -433.457153 0"/></GUILayout><compOutputs><compOutput><uid v="1383697827"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.939999998"/></paramValue></parameter><parameter><name v="angle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.185972229"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1383697827"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1383704284"/><connections><connection><identifier v="destination"/><connRef v="1383701380"/><connRefOutput v="1383701381"/></connection><connection><identifier v="source"/><connRef v="1383704204"/><connRefOutput v="1383701074"/></connection></connections><GUILayout><gpos v="-3570.16382 -112 0"/></GUILayout><compOutputs><compOutput><uid v="1383704285"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1383707127"/><connections><connection><identifier v="Input_1"/><connRef v="1383704205"/><connRefOutput v="1383697827"/></connection></connections><GUILayout><gpos v="-3569.74536 -578.488159 0"/></GUILayout><compOutputs><compOutput><uid v="1383701074"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1149349207"/><parameters><parameter><name v="Invert_Position"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.449999988"/></paramValue></parameter><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.289999992"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1383701074"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1383707144"/><connections><connection><identifier v="destination"/><connRef v="1383704284"/><connRefOutput v="1383704285"/></connection><connection><identifier v="source"/><connRef v="1383709718"/><connRefOutput v="1383709719"/></connection></connections><GUILayout><gpos v="-3408 -112 0"/></GUILayout><compOutputs><compOutput><uid v="1383707145"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.099999994"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1383709718"/><connections><connection><identifier v="Source"/><connRef v="1383707127"/><connRefOutput v="1383701074"/></connection><connection><identifier v="Effect"/><connRef v="1383711106"/><connRefOutput v="1383711107"/></connection></connections><GUILayout><gpos v="-3440 -592 0"/></GUILayout><compOutputs><compOutput><uid v="1383709719"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///slope_blur_grayscale_2?dependency=1449402040"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="32"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.03999996"/></paramValue></parameter><parameter><name v="mode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1383709719"/><identifier v="Slope_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1383711106"/><GUILayout><gpos v="-3504 -368 0"/></GUILayout><compOutputs><compOutput><uid v="1383711107"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///perlin_noise?dependency=1407332030"/><parameters><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueInt32 v="9"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1383711107"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1383713934"/><connections><connection><identifier v="input"/><connRef v="1383697560"/><connRefOutput v="1383697561"/></connection></connections><GUILayout><gpos v="-3648.10864 -770.488159 0"/></GUILayout><compOutputs><compOutput><uid v="1383697827"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.610000014"/></paramValue></parameter><parameter><name v="angle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.541361094"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1383697827"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1383713995"/><connections><connection><identifier v="Input_1"/><connRef v="1383713934"/><connRefOutput v="1383697827"/></connection></connections><GUILayout><gpos v="-3488.10864 -770.488159 0"/></GUILayout><compOutputs><compOutput><uid v="1383701074"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1149349207"/><parameters><parameter><name v="Invert_Position"/><relativeTo v="0"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="Contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.229999989"/></paramValue></parameter><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.170000002"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1383701074"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1383713996"/><connections><connection><identifier v="Source"/><connRef v="1383713995"/><connRefOutput v="1383701074"/></connection><connection><identifier v="Effect"/><connRef v="1383711106"/><connRefOutput v="1383711107"/></connection></connections><GUILayout><gpos v="-3167.74805 -688 0"/></GUILayout><compOutputs><compOutput><uid v="1383709719"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///slope_blur_grayscale_2?dependency=1449402040"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="32"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.03999996"/></paramValue></parameter><parameter><name v="mode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1383709719"/><identifier v="Slope_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1383714010"/><connections><connection><identifier v="destination"/><connRef v="1383707144"/><connRefOutput v="1383707145"/></connection><connection><identifier v="source"/><connRef v="1383713996"/><connRefOutput v="1383709719"/></connection></connections><GUILayout><gpos v="-3216 -144 0"/></GUILayout><compOutputs><compOutput><uid v="1383714011"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.340000004"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1383721627"/><connections><connection><identifier v="inputgradient"/><connRef v="1383722679"/><connRefOutput v="1383711107"/></connection><connection><identifier v="input1"/><connRef v="1383781402"/><connRefOutput v="1383781403"/></connection></connections><GUILayout><gpos v="-2928 -144 0"/></GUILayout><compOutputs><compOutput><uid v="1383721628"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="warp"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.109999999"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1383722679"/><GUILayout><gpos v="-3248 16 0"/></GUILayout><compOutputs><compOutput><uid v="1383711107"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///perlin_noise?dependency=1407332030"/><parameters><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueInt32 v="20"/></paramValue></parameter><parameter><name v="disorder"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0899999961"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1383711107"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1383729244"/><connections><connection><identifier v="Source"/><connRef v="1383748735"/><connRefOutput v="1383748736"/></connection><connection><identifier v="Effect"/><connRef v="1383748735"/><connRefOutput v="1383748736"/></connection></connections><GUILayout><gpos v="-1872 464 0"/></GUILayout><compOutputs><compOutput><uid v="1383729245"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///non_uniform_blur_grayscale?dependency=1290776959"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="2.98000002"/></paramValue></parameter><parameter><name v="Blades"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1383729245"/><identifier v="Non_Uniform_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1383735548"/><connections><connection><identifier v="Source"/><connRef v="1383729244"/><connRefOutput v="1383729245"/></connection></connections><GUILayout><gpos v="-1696.34863 432 0"/></GUILayout><compOutputs><compOutput><uid v="1383735549"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///invert_grayscale?dependency=1368115694"/><parameters/><outputBridgings><outputBridging><uid v="1383735549"/><identifier v="Invert_Grayscale"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1383735558"/><connections><connection><identifier v="destination"/><connRef v="1383721627"/><connRefOutput v="1383721628"/></connection><connection><identifier v="opacity"/><connRef v="1383737614"/><connRefOutput v="1383721628"/></connection></connections><GUILayout><gpos v="-1247.23914 208 0"/></GUILayout><compOutputs><compOutput><uid v="1383735559"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.179999992"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1383737614"/><connections><connection><identifier v="inputgradient"/><connRef v="1383722679"/><connRefOutput v="1383711107"/></connection><connection><identifier v="input1"/><connRef v="1383735548"/><connRefOutput v="1383735549"/></connection></connections><GUILayout><gpos v="-1473.85352 400 501"/></GUILayout><compOutputs><compOutput><uid v="1383721628"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="warp"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.109999999"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1383748735"/><connections><connection><identifier v="Input_1"/><connRef v="1383697930"/><connRefOutput v="1383697931"/></connection></connections><GUILayout><gpos v="-2032 432 0"/></GUILayout><compOutputs><compOutput><uid v="1383748736"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1149349207"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1383748736"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1383759332"/><connections><connection><identifier v="bbox"/><connRef v="1383697560"/><connRefOutput v="1383697561"/></connection><connection><identifier v="pattern_input"/><connRef v="1383775998"/><connRefOutput v="1383775999"/></connection></connections><GUILayout><gpos v="-1040 -448.005188 0"/></GUILayout><compOutputs><compOutput><uid v="1383759333"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///floodfill_mapper_grayscale?dependency=1383758355"/><parameters><parameter><name v="tiling_mode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.229999989"/></paramValue></parameter><parameter><name v="rotation_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.729999959"/></paramValue></parameter><parameter><name v="position_offset"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.899999976 0.429999977"/></paramValue></parameter><parameter><name v="luminance_range_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.459999979"/></paramValue></parameter><parameter><name v="position_offset_random"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.810000002 0.340000004"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="6.92000008"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1383759333"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1383762863"/><connections><connection><identifier v="inputgradient"/><connRef v="1383722679"/><connRefOutput v="1383711107"/></connection><connection><identifier v="input1"/><connRef v="1383759332"/><connRefOutput v="1383759333"/></connection></connections><GUILayout><gpos v="-768.026001 -349.824249 0"/></GUILayout><compOutputs><compOutput><uid v="1383721628"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="warp"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.109999999"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1383762934"/><connections><connection><identifier v="destination"/><connRef v="1383767941"/><connRefOutput v="1383767942"/></connection><connection><identifier v="source"/><connRef v="1383889762"/><connRefOutput v="1383889763"/></connection></connections><GUILayout><gpos v="-288.026001 -336 0"/></GUILayout><compOutputs><compOutput><uid v="1383762935"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.629999995"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1383764271"/><connections><connection><identifier v="input"/><connRef v="1359211383"/><connRefOutput v="1359211384"/></connection></connections><GUILayout><gpos v="-592 -624 0"/></GUILayout><compOutputs><compOutput><uid v="1383764272"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///curvature_smooth?dependency=1449590280"/><parameters/><outputBridgings><outputBridging><uid v="1383764272"/><identifier v="height"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1383766939"/><connections><connection><identifier v="height_top"/><connRef v="1383735558"/><connRefOutput v="1383735559"/></connection></connections><GUILayout><gpos v="-784 573.686096 0"/></GUILayout><compOutputs><compOutput><uid v="1383766940"/><comptype v="2"/></compOutput><compOutput><uid v="1383766941"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///height_blend?dependency=1449623608"/><parameters/><outputBridgings><outputBridging><uid v="1383766940"/><identifier v="blended_height"/></outputBridging><outputBridging><uid v="1383766941"/><identifier v="height_mask"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1383767941"/><connections><connection><identifier v="height_top"/><connRef v="1383787525"/><connRefOutput v="1383787526"/></connection><connection><identifier v="height_bottom"/><connRef v="1494471548"/><connRefOutput v="1372395846"/></connection></connections><GUILayout><gpos v="-496 656 0"/></GUILayout><compOutputs><compOutput><uid v="1383767942"/><comptype v="2"/></compOutput><compOutput><uid v="1383767943"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///height_blend?dependency=1449623608"/><parameters/><outputBridgings><outputBridging><uid v="1383767942"/><identifier v="blended_height"/></outputBridging><outputBridging><uid v="1383767943"/><identifier v="height_mask"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1383769624"/><connections><connection><identifier v="opacity"/><connRef v="1383788662"/><connRefOutput v="1383788663"/></connection><connection><identifier v="destination"/><connRef v="1383794797"/><connRefOutput v="1383794798"/></connection><connection><identifier v="source"/><connRef v="1489402130"/><connRefOutput v="1489401883"/></connection></connections><GUILayout><gpos v="383.973999 -784 0"/></GUILayout><compOutputs><compOutput><uid v="1383769625"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1383770804"/><connections><connection><identifier v="Source"/><connRef v="1383767941"/><connRefOutput v="1383767943"/></connection></connections><GUILayout><gpos v="-80 720 0"/></GUILayout><compOutputs><compOutput><uid v="1383770704"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///invert_grayscale?dependency=1368115694"/><parameters/><outputBridgings><outputBridging><uid v="1383770704"/><identifier v="Invert_Grayscale"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1383770821"/><connections><connection><identifier v="opacity"/><connRef v="1383770804"/><connRefOutput v="1383770704"/></connection><connection><identifier v="destination"/><connRef v="1383790596"/><connRefOutput v="1383790597"/></connection><connection><identifier v="source"/><connRef v="1489402268"/><connRefOutput v="1489401883"/></connection></connections><GUILayout><gpos v="1296 -384.251251 0"/></GUILayout><compOutputs><compOutput><uid v="1383770822"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1383775998"/><GUILayout><gpos v="-1264 -400 0"/></GUILayout><compOutputs><compOutput><uid v="1383775999"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///grunge_map_013?dependency=1383771917"/><parameters><parameter><name v="contrast"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="balance"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.389999986"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1383775999"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1383778178"/><connections><connection><identifier v="input1"/><connRef v="1489401978"/><connRefOutput v="1489401979"/></connection></connections><GUILayout><gpos v="-240 -592 0"/></GUILayout><compOutputs><compOutput><uid v="1383778179"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="hsl"/><parameters><parameter><name v="saturation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.48999998"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1383781376"/><connections><connection><identifier v="Source"/><connRef v="1383713995"/><connRefOutput v="1383701074"/></connection><connection><identifier v="Effect"/><connRef v="1383781381"/><connRefOutput v="1383711107"/></connection></connections><GUILayout><gpos v="-3041.5 -448.5 0"/></GUILayout><compOutputs><compOutput><uid v="1383709719"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///slope_blur_grayscale_2?dependency=1449402040"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="32"/></paramValue></parameter><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1.03999996"/></paramValue></parameter><parameter><name v="mode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1383709719"/><identifier v="Slope_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1383781381"/><GUILayout><gpos v="-3280 -432 0"/></GUILayout><compOutputs><compOutput><uid v="1383711107"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///perlin_noise?dependency=1407332030"/><parameters><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1383711107"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1383781402"/><connections><connection><identifier v="destination"/><connRef v="1383714010"/><connRefOutput v="1383714011"/></connection><connection><identifier v="source"/><connRef v="1383781376"/><connRefOutput v="1383709719"/></connection></connections><GUILayout><gpos v="-3056 -144 0"/></GUILayout><compOutputs><compOutput><uid v="1383781403"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0899999961"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1383785615"/><GUILayout><gpos v="-2099.80566 1863.86475 0"/></GUILayout><compOutputs><compOutput><uid v="1383785616"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///gradient_radial?dependency=1383765856"/><parameters/><outputBridgings><outputBridging><uid v="1383785616"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1383785622"/><connections><connection><identifier v="mask"/><connRef v="1383785875"/><connRefOutput v="1383785876"/></connection></connections><GUILayout><gpos v="-1907.80566 1607.86475 0"/></GUILayout><compOutputs><compOutput><uid v="1383785623"/><comptype v="1"/></compOutput><compOutput><uid v="1383785624"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill?dependency=1323881949"/><parameters/><outputBridgings><outputBridging><uid v="1383785623"/><identifier v="output"/></outputBridging><outputBridging><uid v="1383785624"/><identifier v="referenceMap"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1383785784"/><connections><connection><identifier v="input"/><connRef v="1383785622"/><connRefOutput v="1383785623"/></connection></connections><GUILayout><gpos v="-1747.80566 1607.86475 0"/></GUILayout><compOutputs><compOutput><uid v="1383785785"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.359999985"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1383785785"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1383785856"/><connections><connection><identifier v="input"/><connRef v="1383785622"/><connRefOutput v="1383785623"/></connection></connections><GUILayout><gpos v="-1747.80566 1479.86475 501"/></GUILayout><compOutputs><compOutput><uid v="1383785785"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.779999971"/></paramValue></parameter><parameter><name v="angle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.152666673"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1383785785"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1383785875"/><connections><connection><identifier v="Input_1"/><connRef v="1383785615"/><connRefOutput v="1383785616"/></connection></connections><GUILayout><gpos v="-2003.80566 1719.86475 0"/></GUILayout><compOutputs><compOutput><uid v="1383785876"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1149349207"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.979999959"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1383785876"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1383786141"/><connections><connection><identifier v="destination"/><connRef v="1383785615"/><connRefOutput v="1383785616"/></connection><connection><identifier v="source"/><connRef v="1383785784"/><connRefOutput v="1383785785"/></connection></connections><GUILayout><gpos v="-1747.80566 1863.86475 0"/></GUILayout><compOutputs><compOutput><uid v="1383786142"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1383786188"/><connections><connection><identifier v="destination"/><connRef v="1383786141"/><connRefOutput v="1383786142"/></connection><connection><identifier v="source"/><connRef v="1383785856"/><connRefOutput v="1383785785"/></connection></connections><GUILayout><gpos v="-1523.80566 1799.86475 0"/></GUILayout><compOutputs><compOutput><uid v="1383786189"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1383786244"/><connections><connection><identifier v="input"/><connRef v="1383785622"/><connRefOutput v="1383785623"/></connection></connections><GUILayout><gpos v="-1508.896 1607.86475 0"/></GUILayout><compOutputs><compOutput><uid v="1383785785"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_gradient_2?dependency=1323881949"/><parameters><parameter><name v="angle_variation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.419999987"/></paramValue></parameter><parameter><name v="angle"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.419277787"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1383785785"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1383786252"/><connections><connection><identifier v="destination"/><connRef v="1383786188"/><connRefOutput v="1383786189"/></connection><connection><identifier v="source"/><connRef v="1383786244"/><connRefOutput v="1383785785"/></connection></connections><GUILayout><gpos v="-1363.80566 1799.86475 0"/></GUILayout><compOutputs><compOutput><uid v="1383786253"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="6"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1383786286"/><connections><connection><identifier v="input1"/><connRef v="1383786252"/><connRefOutput v="1383786253"/></connection><connection><identifier v="inputgradient"/><connRef v="1383786295"/><connRefOutput v="1383786296"/></connection></connections><GUILayout><gpos v="-1171.80566 1831.86475 0"/></GUILayout><compOutputs><compOutput><uid v="1383786287"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="warp"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="3.64999986"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1383786295"/><GUILayout><gpos v="-1491.80566 2023.86475 0"/></GUILayout><compOutputs><compOutput><uid v="1383786296"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///perlin_noise?dependency=1407332030"/><parameters><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueInt32 v="4"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1383786296"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1383786547"/><connections><connection><identifier v="destination"/><connRef v="1383786286"/><connRefOutput v="1383786287"/></connection><connection><identifier v="source"/><connRef v="1383786244"/><connRefOutput v="1383785785"/></connection></connections><GUILayout><gpos v="-997.333252 1831.86475 0"/></GUILayout><compOutputs><compOutput><uid v="1383786548"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1383787120"/><connections><connection><identifier v="Input"/><connRef v="1383786547"/><connRefOutput v="1383786548"/></connection></connections><GUILayout><gpos v="-837.333252 1831.86475 0"/></GUILayout><compOutputs><compOutput><uid v="1383787121"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1383786570"/><parameters/><outputBridgings><outputBridging><uid v="1383787121"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1383787134"/><connections><connection><identifier v="pattern_input"/><connRef v="1383787120"/><connRefOutput v="1383787121"/></connection><connection><identifier v="mask_map_input"/><connRef v="1383737614"/><connRefOutput v="1383721628"/></connection></connections><GUILayout><gpos v="-656 1840 0"/></GUILayout><compOutputs><compOutput><uid v="1383787135"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1449399972"/><parameters><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1489391527"/><paramNodes><paramNode><uid v="1489391527"/><function v="get_integer1"/><type v="16"/><funcDatas><funcData><name v="get_integer1"/><constantValue><constantValueString v="x_amount_2"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1489392493"/><paramNodes><paramNode><uid v="1489392493"/><function v="get_integer1"/><type v="16"/><funcDatas><funcData><name v="get_integer1"/><constantValue><constantValueString v="y_amount_2"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.709999979"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1489393460"/><paramNodes><paramNode><uid v="1489393460"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="scale_1"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.189999998"/></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.689999998"/></paramValue></parameter><parameter><name v="color_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.550000012"/></paramValue></parameter><parameter><name v="mask_random"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1489399238"/><paramNodes><paramNode><uid v="1489399238"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="mask_random_2"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter><parameter><name v="mask_map_threshold"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1489398272"/><paramNodes><paramNode><uid v="1489398272"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="mask_map_threshold_1"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter><parameter><name v="rotation_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.639999986"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1383787135"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1383787525"/><connections><connection><identifier v="height_top"/><connRef v="1383766939"/><connRefOutput v="1383766940"/></connection><connection><identifier v="height_bottom"/><connRef v="1489387723"/><connRefOutput v="1489387724"/></connection></connections><GUILayout><gpos v="-560 1008 0"/></GUILayout><compOutputs><compOutput><uid v="1383787526"/><comptype v="2"/></compOutput><compOutput><uid v="1383787527"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///height_blend?dependency=1449623608"/><parameters><parameter><name v="height_position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.459999979"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1383787526"/><identifier v="blended_height"/></outputBridging><outputBridging><uid v="1383787527"/><identifier v="height_mask"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1383788142"/><connections><connection><identifier v="destination"/><connRef v="1383737614"/><connRefOutput v="1383721628"/></connection><connection><identifier v="source"/><connRef v="1383788196"/><connRefOutput v="1383788197"/></connection></connections><GUILayout><gpos v="-1040 624 0"/></GUILayout><compOutputs><compOutput><uid v="1383788143"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1383788196"/><connections><connection><identifier v="Input_1"/><connRef v="1489387723"/><connRefOutput v="1489387724"/></connection></connections><GUILayout><gpos v="-912 896.396851 0"/></GUILayout><compOutputs><compOutput><uid v="1383788197"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1149349207"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.949999988"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1383788197"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1383788662"/><connections><connection><identifier v="source"/><connRef v="1383787525"/><connRefOutput v="1383787527"/></connection></connections><GUILayout><gpos v="-80 1058.66235 0"/></GUILayout><compOutputs><compOutput><uid v="1383788663"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1383790596"/><connections><connection><identifier v="destination"/><connRef v="1383769624"/><connRefOutput v="1383769625"/></connection><connection><identifier v="opacity"/><connRef v="1383791394"/><connRefOutput v="1383791395"/></connection><connection><identifier v="source"/><connRef v="1489402363"/><connRefOutput v="1489401883"/></connection></connections><GUILayout><gpos v="1053.07874 -880 0"/></GUILayout><compOutputs><compOutput><uid v="1383790597"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1383791394"/><connections><connection><identifier v="Source"/><connRef v="1383787525"/><connRefOutput v="1383787527"/></connection></connections><GUILayout><gpos v="-80 880 0"/></GUILayout><compOutputs><compOutput><uid v="1383791395"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///invert_grayscale?dependency=1368115694"/><parameters/><outputBridgings><outputBridging><uid v="1383791395"/><identifier v="Invert_Grayscale"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1383791583"/><connections><connection><identifier v="input1"/><connRef v="1383764271"/><connRefOutput v="1383764272"/></connection></connections><GUILayout><gpos v="-464 -573.931824 0"/></GUILayout><compOutputs><compOutput><uid v="1383777271"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1383777282"/><paramsArrayCells><paramsArrayCell><uid v="1494659836"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.405063301"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.371900827 0.163898528 0.06497062 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1494659837"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="1 1 1 1"/></paramValue></parameter></parameters></paramsArrayCell><paramsArrayCell><uid v="1494659838"/><parameters><parameter><name v="position"/><paramValue><constantValueFloat1 v="0.514767945"/></paramValue></parameter><parameter><name v="midpoint"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="value"/><paramValue><constantValueFloat4 v="0.513742089 0.513742089 0.513742089 1"/></paramValue></parameter></parameters></paramsArrayCell></paramsArrayCells></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1383791838"/><connections><connection><identifier v="inputgradient"/><connRef v="1383722679"/><connRefOutput v="1383711107"/></connection><connection><identifier v="input1"/><connRef v="1383791840"/><connRefOutput v="1383759333"/></connection></connections><GUILayout><gpos v="-512.026001 -1264 0"/></GUILayout><compOutputs><compOutput><uid v="1383721628"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="warp"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.109999999"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1383791840"/><connections><connection><identifier v="bbox"/><connRef v="1383697560"/><connRefOutput v="1383697561"/></connection><connection><identifier v="pattern_input"/><connRef v="1383793023"/><connRefOutput v="1383793024"/></connection></connections><GUILayout><gpos v="-832.026062 -1375.05994 0"/></GUILayout><compOutputs><compOutput><uid v="1383759333"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///floodfill_mapper_grayscale?dependency=1383758355"/><parameters><parameter><name v="tiling_mode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="scale_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.229999989"/></paramValue></parameter><parameter><name v="rotation_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.729999959"/></paramValue></parameter><parameter><name v="position_offset"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.899999976 0.429999977"/></paramValue></parameter><parameter><name v="luminance_range_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.459999979"/></paramValue></parameter><parameter><name v="position_offset_random"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="0.810000002 0.340000004"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="6.92000008"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1383759333"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1383793023"/><GUILayout><gpos v="-1040.23987 -1310.17871 0"/></GUILayout><compOutputs><compOutput><uid v="1383793024"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///grunge_map_014?dependency=1383791918"/><parameters/><outputBridgings><outputBridging><uid v="1383793024"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1383793511"/><connections><connection><identifier v="input1"/><connRef v="1383791838"/><connRefOutput v="1383721628"/></connection></connections><GUILayout><gpos v="-304 -1136 0"/></GUILayout><compOutputs><compOutput><uid v="1383793512"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/><paramsArrays><paramsArray><name v="gradientrgba"/><uid v="1383793522"/><paramsArrayCells/></paramsArray></paramsArrays></compFilter></compImplementation></compNode><compNode><uid v="1383793555"/><connections><connection><identifier v="destination"/><connRef v="1383778178"/><connRefOutput v="1383778179"/></connection><connection><identifier v="source"/><connRef v="1383793511"/><connRefOutput v="1383793512"/></connection></connections><GUILayout><gpos v="-96.026001 -656 0"/></GUILayout><compOutputs><compOutput><uid v="1383793556"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="5"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.349999994"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1383794757"/><connections><connection><identifier v="input"/><connRef v="1383697560"/><connRefOutput v="1383697561"/></connection></connections><GUILayout><gpos v="-1040 -784 0"/></GUILayout><compOutputs><compOutput><uid v="1383794758"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_random_color?dependency=1323881949"/><parameters/><outputBridgings><outputBridging><uid v="1383794758"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1383794771"/><connections><connection><identifier v="inputgradient"/><connRef v="1383722679"/><connRefOutput v="1383711107"/></connection><connection><identifier v="input1"/><connRef v="1383794757"/><connRefOutput v="1383794758"/></connection></connections><GUILayout><gpos v="-672.026001 -752 0"/></GUILayout><compOutputs><compOutput><uid v="1383721628"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="warp"/><parameters><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.109999999"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1383794797"/><connections><connection><identifier v="source"/><connRef v="1383794771"/><connRefOutput v="1383721628"/></connection><connection><identifier v="destination"/><connRef v="1383794997"/><connRefOutput v="1383794998"/></connection></connections><GUILayout><gpos v="255.973999 -752 0"/></GUILayout><compOutputs><compOutput><uid v="1383794798"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0799999982"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1383794962"/><connections><connection><identifier v="input"/><connRef v="1383697560"/><connRefOutput v="1383697561"/></connection></connections><GUILayout><gpos v="-576.026001 -993.574829 0"/></GUILayout><compOutputs><compOutput><uid v="1383794963"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///flood_fill_to_random_grayscale?dependency=1323881949"/><parameters/><outputBridgings><outputBridging><uid v="1383794963"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1383794989"/><connections><connection><identifier v="input1"/><connRef v="1383794962"/><connRefOutput v="1383794963"/></connection></connections><GUILayout><gpos v="-416.026001 -993.574829 0"/></GUILayout><compOutputs><compOutput><uid v="1383794990"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1383794997"/><connections><connection><identifier v="source"/><connRef v="1383794989"/><connRefOutput v="1383794990"/></connection><connection><identifier v="destination"/><connRef v="1383795753"/><connRefOutput v="1383795754"/></connection></connections><GUILayout><gpos v="127.973999 -766.094482 0"/></GUILayout><compOutputs><compOutput><uid v="1383794998"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.199999988"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1383795510"/><connections><connection><identifier v="input1"/><connRef v="1383764271"/><connRefOutput v="1383764272"/></connection></connections><GUILayout><gpos v="-96.026001 -480.278442 0"/></GUILayout><compOutputs><compOutput><uid v="1383795511"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters><parameter><name v="levelinlow"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.406488538 0.406488538 0.406488538 0"/></paramValue></parameter><parameter><name v="levelinhigh"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.78435117 0.78435117 0.78435117 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1383795742"/><connections><connection><identifier v="input1"/><connRef v="1383795510"/><connRefOutput v="1383795511"/></connection></connections><GUILayout><gpos v="63.973999 -480.278442 0"/></GUILayout><compOutputs><compOutput><uid v="1383795743"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1383795753"/><connections><connection><identifier v="destination"/><connRef v="1383793555"/><connRefOutput v="1383793556"/></connection><connection><identifier v="source"/><connRef v="1383795742"/><connRefOutput v="1383795743"/></connection></connections><GUILayout><gpos v="63.973999 -624 0"/></GUILayout><compOutputs><compOutput><uid v="1383795754"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="5"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.560000002"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1383796721"/><connections><connection><identifier v="input1"/><connRef v="1383770821"/><connRefOutput v="1383770822"/></connection></connections><GUILayout><gpos v="798.858276 1021.07874 0"/></GUILayout><compOutputs><compOutput><uid v="1383796722"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="hsl"/><parameters><parameter><name v="saturation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="luminosity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.719999969"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1383798306"/><connections><connection><identifier v="source"/><connRef v="1383801553"/><connRefOutput v="1383801554"/></connection><connection><identifier v="opacity"/><connRef v="1383801616"/><connRefOutput v="1383801617"/></connection><connection><identifier v="destination"/><connRef v="1489402562"/><connRefOutput v="1489402563"/></connection></connections><GUILayout><gpos v="1328 848 0"/></GUILayout><compOutputs><compOutput><uid v="1383798307"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1383798336"/><connections><connection><identifier v="input1"/><connRef v="1383796721"/><connRefOutput v="1383796722"/></connection></connections><GUILayout><gpos v="976 1008 0"/></GUILayout><compOutputs><compOutput><uid v="1383798337"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="grayscaleconversion"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1383800032"/><GUILayout><gpos v="863.0625 656 0"/></GUILayout><compOutputs><compOutput><uid v="1383800033"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="uniform"/><parameters><parameter><name v="colorswitch"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="outputcolor"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1489402670"/><paramNodes><paramNode><uid v="1489402670"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="outputcolor_4"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1383801553"/><connections><connection><identifier v="destination"/><connRef v="1359211391"/><connRefOutput v="1359211392"/></connection><connection><identifier v="source"/><connRef v="1383800032"/><connRefOutput v="1383800033"/></connection><connection><identifier v="opacity"/><connRef v="1383791394"/><connRefOutput v="1383791395"/></connection></connections><GUILayout><gpos v="1023.06256 752 0"/></GUILayout><compOutputs><compOutput><uid v="1383801554"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1383801616"/><connections><connection><identifier v="destination"/><connRef v="1383791394"/><connRefOutput v="1383791395"/></connection><connection><identifier v="source"/><connRef v="1383770804"/><connRefOutput v="1383770704"/></connection></connections><GUILayout><gpos v="528 976 0"/></GUILayout><compOutputs><compOutput><uid v="1383801617"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1383884454"/><connections><connection><identifier v="height_top"/><connRef v="1383766939"/><connRefOutput v="1383766940"/></connection><connection><identifier v="height_bottom"/><connRef v="1383885124"/><connRefOutput v="1383885125"/></connection></connections><GUILayout><gpos v="368 1757.56641 0"/></GUILayout><compOutputs><compOutput><uid v="1383787526"/><comptype v="2"/></compOutput><compOutput><uid v="1383787527"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///height_blend?dependency=1449623608"/><parameters><parameter><name v="height_position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.459999979"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1383787526"/><identifier v="blended_height"/></outputBridging><outputBridging><uid v="1383787527"/><identifier v="height_mask"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1383884541"/><connections><connection><identifier v="input1"/><connRef v="1489387723"/><connRefOutput v="1489387724"/></connection></connections><GUILayout><gpos v="-208 1968 0"/></GUILayout><compOutputs><compOutput><uid v="1383884542"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="levels"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1383885124"/><connections><connection><identifier v="input"/><connRef v="1383884541"/><connRefOutput v="1383884542"/></connection></connections><GUILayout><gpos v="-108.25 1749.25 0"/></GUILayout><compOutputs><compOutput><uid v="1383885125"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_range?dependency=1383033101"/><parameters><parameter><name v="range"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.419999987"/></paramValue></parameter><parameter><name v="position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.189999998"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1383885125"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1383885185"/><connections><connection><identifier v="height_top"/><connRef v="1383884454"/><connRefOutput v="1383787526"/></connection><connection><identifier v="height_bottom"/><connRef v="1383885221"/><connRefOutput v="1383885222"/></connection></connections><GUILayout><gpos v="624 1584 0"/></GUILayout><compOutputs><compOutput><uid v="1383767942"/><comptype v="2"/></compOutput><compOutput><uid v="1383767943"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///height_blend?dependency=1449623608"/><parameters/><outputBridgings><outputBridging><uid v="1383767942"/><identifier v="blended_height"/></outputBridging><outputBridging><uid v="1383767943"/><identifier v="height_mask"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1383885221"/><connections><connection><identifier v="input"/><connRef v="1494471548"/><connRefOutput v="1372395846"/></connection></connections><GUILayout><gpos v="272 1680 0"/></GUILayout><compOutputs><compOutput><uid v="1383885222"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_range?dependency=1383033101"/><parameters><parameter><name v="position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.189999998"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1383885222"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1383889762"/><connections><connection><identifier v="source"/><connRef v="1383762863"/><connRefOutput v="1383721628"/></connection><connection><identifier v="destination"/><connRef v="1383890531"/><connRefOutput v="1383890532"/></connection></connections><GUILayout><gpos v="-464 -304 0"/></GUILayout><compOutputs><compOutput><uid v="1383889763"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1383890531"/><connections><connection><identifier v="Source"/><connRef v="1383801616"/><connRefOutput v="1383801617"/></connection></connections><GUILayout><gpos v="-674.267822 -16 0"/></GUILayout><compOutputs><compOutput><uid v="1383890532"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///invert_grayscale?dependency=1368115694"/><parameters/><outputBridgings><outputBridging><uid v="1383890532"/><identifier v="Invert_Grayscale"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1489336508"/><connections><connection><identifier v="input1"/><connRef v="1383762934"/><connRefOutput v="1383762935"/></connection></connections><GUILayout><gpos v="1424 368 0"/></GUILayout><compOutputs><compOutput><uid v="1359211384"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="normal"/><parameters><parameter><name v="input2alpha"/><relativeTo v="0"/><paramValue><constantValueBool v="0"/></paramValue></parameter><parameter><name v="intensity"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1489402748"/><paramNodes><paramNode><uid v="1489402748"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="intensity"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter><parameter><name v="inversedy"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1489402754"/><paramNodes><paramNode><uid v="1489402754"/><function v="get_bool"/><type v="4"/><funcDatas><funcData><name v="get_bool"/><constantValue><constantValueString v="inversedy"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1489387723"/><connections><connection><identifier v="destination"/><connRef v="1383787134"/><connRefOutput v="1383787135"/></connection></connections><GUILayout><gpos v="-474.666504 1854.27295 0"/></GUILayout><compOutputs><compOutput><uid v="1489387724"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1489390568"/><paramNodes><paramNode><uid v="1489390568"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="opacitymult_1"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1489401840"/><GUILayout><gpos v="-784 -482.372742 0"/></GUILayout><compOutputs><compOutput><uid v="1489401841"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="uniform"/><parameters><parameter><name v="outputsize"/><relativeTo v="0"/><paramValue><constantValueInt2 v="4 4"/></paramValue></parameter><parameter><name v="outputcolor"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.90322578 0.67278403 0.426979482 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1489401882"/><connections><connection><identifier v="destination"/><connRef v="1489401840"/><connRefOutput v="1489401841"/></connection><connection><identifier v="source"/><connRef v="1489401892"/><connRefOutput v="1489401893"/></connection></connections><GUILayout><gpos v="-464 -464 0"/></GUILayout><compOutputs><compOutput><uid v="1489401883"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="outputsize"/><relativeTo v="1"/><paramValue><constantValueInt2 v="0 0"/></paramValue></parameter><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.140000001"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1489401892"/><connections><connection><identifier v="input1"/><connRef v="1383764271"/><connRefOutput v="1383764272"/></connection></connections><GUILayout><gpos v="-784 -656 0"/></GUILayout><compOutputs><compOutput><uid v="1489401893"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="gradient"/><parameters/></compFilter></compImplementation></compNode><compNode><uid v="1489401978"/><connections><connection><identifier v="destination"/><connRef v="1489401882"/><connRefOutput v="1489401883"/></connection><connection><identifier v="source"/><connRef v="1383791583"/><connRefOutput v="1383777271"/></connection></connections><GUILayout><gpos v="-336 -496 0"/></GUILayout><compOutputs><compOutput><uid v="1489401979"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.569999993"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1489402130"/><connections><connection><identifier v="source"/><connRef v="1489401892"/><connRefOutput v="1489401893"/></connection><connection><identifier v="destination"/><connRef v="1489402137"/><connRefOutput v="1489401841"/></connection></connections><GUILayout><gpos v="144 -944 0"/></GUILayout><compOutputs><compOutput><uid v="1489401883"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="outputsize"/><relativeTo v="1"/><paramValue><constantValueInt2 v="0 0"/></paramValue></parameter><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.140000001"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1489402137"/><GUILayout><gpos v="-58.6666718 -1018.66663 0"/></GUILayout><compOutputs><compOutput><uid v="1489401841"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="uniform"/><parameters><parameter><name v="outputsize"/><relativeTo v="0"/><paramValue><constantValueInt2 v="4 4"/></paramValue></parameter><parameter><name v="outputcolor"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1489402229"/><paramNodes><paramNode><uid v="1489402229"/><function v="get_float4"/><type v="2048"/><funcDatas><funcData><name v="get_float4"/><constantValue><constantValueString v="outputcolor_1"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1489402216"/><GUILayout><gpos v="416.586487 -400 0"/></GUILayout><compOutputs><compOutput><uid v="1489401841"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="uniform"/><parameters><parameter><name v="outputsize"/><relativeTo v="0"/><paramValue><constantValueInt2 v="4 4"/></paramValue></parameter><parameter><name v="colorswitch"/><paramValue><constantValueBool v="1"/></paramValue></parameter><parameter><name v="outputcolor"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.414025813 0.537634432 0.263929635 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1489402268"/><connections><connection><identifier v="source"/><connRef v="1489401892"/><connRefOutput v="1489401893"/></connection><connection><identifier v="destination"/><connRef v="1489402216"/><connRefOutput v="1489401841"/></connection></connections><GUILayout><gpos v="656 -496 0"/></GUILayout><compOutputs><compOutput><uid v="1489401883"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="outputsize"/><relativeTo v="1"/><paramValue><constantValueInt2 v="0 0"/></paramValue></parameter><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.149999991"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1489402355"/><GUILayout><gpos v="672.028015 -656 0"/></GUILayout><compOutputs><compOutput><uid v="1489401841"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="uniform"/><parameters><parameter><name v="outputsize"/><relativeTo v="0"/><paramValue><constantValueInt2 v="4 4"/></paramValue></parameter><parameter><name v="outputcolor"/><relativeTo v="0"/><paramValue><constantValueFloat4 v="0.688172042 0.573824227 0.492147297 1"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1489402363"/><connections><connection><identifier v="source"/><connRef v="1489401892"/><connRefOutput v="1489401893"/></connection><connection><identifier v="destination"/><connRef v="1489402355"/><connRefOutput v="1489401841"/></connection></connections><GUILayout><gpos v="880 -688 0"/></GUILayout><compOutputs><compOutput><uid v="1489401883"/><comptype v="1"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="outputsize"/><relativeTo v="1"/><paramValue><constantValueInt2 v="0 0"/></paramValue></parameter><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.149999991"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1489402562"/><connections><connection><identifier v="Source"/><connRef v="1383798336"/><connRefOutput v="1383798337"/></connection></connections><GUILayout><gpos v="1136 1008 0"/></GUILayout><compOutputs><compOutput><uid v="1489402563"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///contrast_luminosity_grayscale?dependency=1146607502"/><parameters><parameter><name v="Luminosity"/><relativeTo v="0"/><paramValue><dynamicValue><rootnode v="1489402598"/><paramNodes><paramNode><uid v="1489402598"/><function v="get_float1"/><type v="256"/><funcDatas><funcData><name v="get_float1"/><constantValue><constantValueString v="Luminosity"/></constantValue></funcData></funcDatas></paramNode></paramNodes></dynamicValue></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1489402563"/><identifier v="Contrast_Luminosity"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1494471545"/><GUILayout><gpos v="-3068.20776 893.794922 0"/></GUILayout><compOutputs><compOutput><uid v="1372392121"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///gradient_linear_1?dependency=1357065436"/><parameters><parameter><name v="rotation"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1372392121"/><identifier v="Simple_Gradient"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1494471546"/><connections><connection><identifier v="Source"/><connRef v="1494471552"/><connRefOutput v="1372349116"/></connection><connection><identifier v="Effect"/><connRef v="1494471552"/><connRefOutput v="1372349116"/></connection></connections><GUILayout><gpos v="-3088.21655 1104 0"/></GUILayout><compOutputs><compOutput><uid v="1372349558"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///non_uniform_blur_grayscale?dependency=1290776959"/><parameters><parameter><name v="Intensity"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="10.3499994"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1372349558"/><identifier v="Non_Uniform_Blur"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1494471547"/><connections><connection><identifier v="Input"/><connRef v="1494471550"/><connRefOutput v="1372392666"/></connection></connections><GUILayout><gpos v="-2497.02734 1040 0"/></GUILayout><compOutputs><compOutput><uid v="1372394006"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///auto_levels?dependency=1383786570"/><parameters/><outputBridgings><outputBridging><uid v="1372394006"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1494471548"/><connections><connection><identifier v="pattern_input"/><connRef v="1494471547"/><connRefOutput v="1372394006"/></connection><connection><identifier v="mask_map_input"/><connRef v="1383788142"/><connRefOutput v="1383788143"/></connection></connections><GUILayout><gpos v="-2288 1008 0"/></GUILayout><compOutputs><compOutput><uid v="1372395846"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///tile_sampler?dependency=1449399972"/><parameters><parameter><name v="pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter><parameter><name v="rotation"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="-0.21727778"/></paramValue></parameter><parameter><name v="offset"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.48999998"/></paramValue></parameter><parameter><name v="position_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.98999995"/></paramValue></parameter><parameter><name v="scale"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="8.2699995"/></paramValue></parameter><parameter><name v="y_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="59"/></paramValue></parameter><parameter><name v="x_amount"/><relativeTo v="0"/><paramValue><constantValueInt32 v="58"/></paramValue></parameter><parameter><name v="vector_map_displacement"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.48999998"/></paramValue></parameter><parameter><name v="color_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.579999983"/></paramValue></parameter><parameter><name v="size"/><relativeTo v="0"/><paramValue><constantValueFloat2 v="1 0.5"/></paramValue></parameter><parameter><name v="rotation_random"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.170000002"/></paramValue></parameter><parameter><name v="scale_vector_map_multiplier"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="1"/></paramValue></parameter><parameter><name v="mask_map_threshold"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.0700000003"/></paramValue></parameter><parameter><name v="scale_map_multiplier"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.419999987"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1372395846"/><identifier v="output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1494471549"/><connections><connection><identifier v="destination"/><connRef v="1494471546"/><connRefOutput v="1372349558"/></connection><connection><identifier v="source"/><connRef v="1494471545"/><connRefOutput v="1372392121"/></connection></connections><GUILayout><gpos v="-2848.92725 1053.79492 0"/></GUILayout><compOutputs><compOutput><uid v="1372392130"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="opacitymult"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.870000005"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1494471550"/><connections><connection><identifier v="destination"/><connRef v="1494471549"/><connRefOutput v="1372392130"/></connection><connection><identifier v="source"/><connRef v="1494471551"/><connRefOutput v="1372392624"/></connection></connections><GUILayout><gpos v="-2670.04932 1053.79492 0"/></GUILayout><compOutputs><compOutput><uid v="1372392666"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compFilter><filter v="blend"/><parameters><parameter><name v="blendingmode"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter></parameters></compFilter></compImplementation></compNode><compNode><uid v="1494471551"/><connections><connection><identifier v="Input_1"/><connRef v="1494471545"/><connRefOutput v="1372392121"/></connection></connections><GUILayout><gpos v="-2830.04932 893.794922 0"/></GUILayout><compOutputs><compOutput><uid v="1372392624"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///histogram_scan?dependency=1149349207"/><parameters><parameter><name v="Position"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.429999977"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1372392624"/><identifier v="Output"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode><compNode><uid v="1494471552"/><GUILayout><gpos v="-3278.04907 1098.83203 0"/></GUILayout><compOutputs><compOutput><uid v="1372349116"/><comptype v="2"/></compOutput></compOutputs><compImplementation><compInstance><path v="pkg:///waveform_1?dependency=1416542616"/><parameters><parameter><name v="Samples"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1024"/></paramValue></parameter><parameter><name v="SizeMax"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0.280000001"/></paramValue></parameter><parameter><name v="Pattern"/><relativeTo v="0"/><paramValue><constantValueInt32 v="2"/></paramValue></parameter><parameter><name v="Noise"/><relativeTo v="0"/><paramValue><constantValueFloat1 v="0"/></paramValue></parameter><parameter><name v="WaveNumber"/><relativeTo v="0"/><paramValue><constantValueInt32 v="1"/></paramValue></parameter></parameters><outputBridgings><outputBridging><uid v="1372349116"/><identifier v="Waveform"/></outputBridging></outputBridgings></compInstance></compImplementation></compNode></compNodes><baseParameters><parameter><name v="outputsize"/><relativeTo v="0"/><paramValue><constantValueInt2 v="11 11"/></paramValue></parameter><parameter><name v="format"/><relativeTo v="0"/><paramValue><constantValueInt32 v="3"/></paramValue></parameter></baseParameters><GUIObjects><GUIObject><type v="COMMENT"/><GUILayout><gpos v="-4384 -384 0"/><size v="160 288"/></GUILayout><GUIName v=""/><uid v="1489370171"/><title v="Tiles"/><frameColor v="0.197424889 1 0 0.250980407"/><isTitleVisible v="1"/><isFrameVisible v="1"/></GUIObject></GUIObjects><options><option><name v="export/fromGraph/autoExport"/><value v="false"/></option><option><name v="export/fromGraph/destination"/><value v="D:/work/Annery/stylized material/stylized material/images/stone path"/></option><option><name v="export/fromGraph/extension"/><value v="png"/></option><option><name v="export/fromGraph/outputs/AO"/><value v="true"/></option><option><name v="export/fromGraph/outputs/basecolor"/><value v="true"/></option><option><name v="export/fromGraph/outputs/height"/><value v="true"/></option><option><name v="export/fromGraph/outputs/metallic"/><value v="true"/></option><option><name v="export/fromGraph/outputs/normal"/><value v="true"/></option><option><name v="export/fromGraph/outputs/roughness"/><value v="true"/></option><option><name v="export/fromGraph/outputsColorspace/AO"/><value v="Raw"/></option><option><name v="export/fromGraph/outputsColorspace/basecolor"/><value v="sRGB"/></option><option><name v="export/fromGraph/outputsColorspace/height"/><value v="Raw"/></option><option><name v="export/fromGraph/outputsColorspace/metallic"/><value v="Raw"/></option><option><name v="export/fromGraph/outputsColorspace/normal"/><value v="Raw"/></option><option><name v="export/fromGraph/outputsColorspace/roughness"/><value v="Raw"/></option><option><name v="export/fromGraph/pattern"/><value v="$(graph)_$(identifier)"/></option></options><root><rootOutputs><rootOutput><output v="1213284336"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1213284338"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1213284340"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1213284342"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1279137031"/><format v="0"/><usertag v=""/></rootOutput><rootOutput><output v="1359211721"/><format v="0"/><usertag v=""/></rootOutput></rootOutputs></root></graph></content></package>
